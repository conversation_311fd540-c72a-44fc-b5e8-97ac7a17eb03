<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Grpc.Net.Client</name>
    </assembly>
    <members>
        <member name="T:Grpc.Net.Client.Balancer.BalancerAddress">
            <summary>
            Represents a balancer address.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.BalancerAddress.#ctor(System.Net.DnsEndPoint)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.BalancerAddress"/> class with the specified <see cref="T:System.Net.DnsEndPoint"/>.
            </summary>
            <param name="endPoint">The end point.</param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.BalancerAddress.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.BalancerAddress"/> class with the specified host and port.
            </summary>
            <param name="host">The host.</param>
            <param name="port">The port.</param>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.BalancerAddress.EndPoint">
            <summary>
            Gets the address <see cref="T:System.Net.DnsEndPoint"/>.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.BalancerAddress.Attributes">
            <summary>
            Gets the address attributes.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.BalancerAddress.ToString">
            <summary>
            Returns a string that reprsents the address.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.BalancerAttributes">
            <summary>
            Collection of load balancing metadata attributes.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="F:Grpc.Net.Client.Balancer.BalancerAttributes.Empty">
            <summary>
            Gets a read-only collection of metadata attributes.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.BalancerAttributes.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.BalancerAttributes"/> class.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.BalancerAttributes.TryGetValue``1(Grpc.Net.Client.Balancer.BalancerAttributesKey{``0},``0@)">
            <summary>
            Gets the value associated with the specified key.
            </summary>
            <typeparam name="TValue">The value type.</typeparam>
            <param name="key">The key of the <see cref="T:Grpc.Net.Client.Balancer.BalancerAttributesKey`1"/> to get.</param>
            <param name="value">
            When this method returns, contains the value associated with the specified key, if the key is found
            and the value type matches the specified type. Otherwise, contains the default value for the type of
            the <c>value</c> parameter.
            </param>
            <returns>
            <c>true</c> if the <see cref="T:Grpc.Net.Client.Balancer.BalancerAttributes"/> contains an element with the specified key and value type; otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.BalancerAttributes.Set``1(Grpc.Net.Client.Balancer.BalancerAttributesKey{``0},``0)">
            <summary>
            Sets the value associated with the specified key.
            </summary>
            <typeparam name="TValue">The value type.</typeparam>
            <param name="key">The key of the value to set.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.BalancerAttributes.Remove``1(Grpc.Net.Client.Balancer.BalancerAttributesKey{``0})">
            <summary>
            Removes the value associated with the specified key.
            </summary>
            <typeparam name="TValue">The value type.</typeparam>
            <param name="key">The key of the value to set.</param>
            <returns>
            <c>true</c> if the element is successfully removed; otherwise, <c>false</c>.
            This method also returns <c>false</c> if key was not found.
            </returns>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.BalancerAttributesKey`1">
            <summary>
            Represents the key used to get and set <see cref="T:Grpc.Net.Client.Balancer.BalancerAttributes"/> values.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
            <typeparam name="TValue">The value type.</typeparam>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.BalancerAttributesKey`1.Key">
            <summary>
            Gets the key.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.BalancerAttributesKey`1.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.BalancerAttributesKey`1"/> struct with the specified key.
            </summary>
            <param name="key">The key.</param>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.BalancerState">
            <summary>
            Represents the balancer state.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.BalancerState.#ctor(Grpc.Core.ConnectivityState,Grpc.Net.Client.Balancer.SubchannelPicker)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.BalancerState"/> class with the specified state.
            </summary>
            <param name="connectivityState">The connectivity state.</param>
            <param name="picker">The subchannel picker.</param>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.BalancerState.ConnectivityState">
            <summary>
            Gets the connectivity state.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.BalancerState.Picker">
            <summary>
            Gets the subchannel picker.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.ChannelState">
            <summary>
            Represents the state for a channel. This is created from results returned by a <see cref="T:Grpc.Net.Client.Balancer.Resolver"/>.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ChannelState.Addresses">
            <summary>
            Gets a collection of addresses. Will be <c>null</c> if <see cref="P:Grpc.Net.Client.Balancer.ChannelState.Status"/> has a non-OK value.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ChannelState.LoadBalancingConfig">
            <summary>
            Gets an optional load balancing config.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ChannelState.Status">
            <summary>
            Gets the status. Successful results has an <see cref="F:Grpc.Core.StatusCode.OK"/> status.
            A resolver error creates results with non-OK status. The status has details about the resolver error.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ChannelState.Attributes">
            <summary>
            Gets a collection of metadata attributes.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.CompletionContext">
            <summary>
            Context used to signal a call is complete.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.CompletionContext.Address">
            <summary>
            Gets or sets the <see cref="T:Grpc.Net.Client.Balancer.BalancerAddress"/> a call was made with. Required.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.CompletionContext.Error">
            <summary>
            Gets or sets the <see cref="T:System.Exception"/> thrown when making the call.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.DnsResolverFactory">
            <summary>
            A <see cref="T:Grpc.Net.Client.Balancer.ResolverFactory"/> that matches the URI scheme <c>dns</c>
            and creates <see cref="T:Grpc.Net.Client.Balancer.DnsResolver"/> instances.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.DnsResolverFactory.#ctor(System.TimeSpan)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.DnsResolverFactory"/> class with a refresh interval.
            </summary>
            <param name="refreshInterval">An interval for automatically refreshing the DNS hostname.</param>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.DnsResolverFactory.Name">
            <inheritdoc />
        </member>
        <member name="M:Grpc.Net.Client.Balancer.DnsResolverFactory.Create(Grpc.Net.Client.Balancer.ResolverOptions)">
            <inheritdoc />
        </member>
        <member name="T:Grpc.Net.Client.Balancer.IBackoffPolicy">
            <summary>
            An interface for specifying backoff duration.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.IBackoffPolicy.NextBackoff">
            <summary>
            Returns the next backoff duration.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.IBackoffPolicyFactory">
            <summary>
            A factory for creating <see cref="T:Grpc.Net.Client.Balancer.IBackoffPolicy"/> instances.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.IBackoffPolicyFactory.Create">
            <summary>
            Creates a backoff policy.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.IChannelControlHelper">
            <summary>
            Provides essentials for <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> implementations.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.IChannelControlHelper.CreateSubchannel(Grpc.Net.Client.Balancer.SubchannelOptions)">
            <summary>
            Creates a <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/>, which is a logical connection to the specified addresses.
            The <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> is responsible for closing unused subchannels, and closing
            all subchannels on shutdown.
            </summary>
            <param name="options">The options for the new <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/>.</param>
            <returns>A new <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/>.</returns>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.IChannelControlHelper.UpdateState(Grpc.Net.Client.Balancer.BalancerState)">
            <summary>
            Update the balancing state. This includes a new <see cref="T:Grpc.Core.ConnectivityState"/> and
            <see cref="T:Grpc.Net.Client.Balancer.SubchannelPicker"/>. The state is used by currently queued and future calls.
            </summary>
            <param name="state">The balancer state.</param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.IChannelControlHelper.RefreshResolver">
            <summary>
            Request the configured <see cref="T:Grpc.Net.Client.Balancer.Resolver"/> to refresh.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.Internal.ChildHandlerLoadBalancer">
            <summary>
            The load balancer is responsible for creating the real load balancer, and changing
            it if the resolver returns a service config with a new policy name.
            <para>
            This load balancer has a reference to both a current and a pending load balancer.
            
            If there is an update that prompts the load balancer to change then:
            1. New load balancer is created and set to pending. It will begin connecting.
            2. Current load balancer will continue to serve new gRPC calls.
            3. Once pending load balancer has a READY state then it is prompted to current.
            4. Old current load balancer is disposed.
            
            This is designed so that there is a smooth transistion when the load balancer changes.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Internal.ChildHandlerLoadBalancer.TryGetValidServiceConfigFactory(System.Collections.Generic.IList{Grpc.Net.Client.Configuration.LoadBalancingConfig},Grpc.Net.Client.Balancer.LoadBalancerFactory[],Grpc.Net.Client.Configuration.LoadBalancingConfig@,Grpc.Net.Client.Balancer.LoadBalancerFactory@)">
            <summary>
            Iterate through the load balancing configs and find the first config that has a factory.
            That means it is supported by the client.
            https://github.com/grpc/proposal/blob/master/A24-lb-policy-config.md
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.Internal.ISubchannelTransport">
            <summary>
            An abstraction for subchannels to create a transport and connect to the server.
            This abstraction allows the connection to be customized. Used in unit tests.
            Might be made public in the future to support using load balancing with non-socket transports.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.Internal.PassiveSubchannelTransport">
            <summary>
            Subchannel transport used when SocketsHttpHandler isn't configured.
            This transport will only be used when there is one address.
            It isn't able to correctly determine connectivity state.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.Internal.SocketConnectivitySubchannelTransport">
            <summary>
            Transport that makes it possible to monitor connectivity state while using HttpClient.
            
            Features:
            1. When a connection is requested the transport creates a Socket and connects to the server.
               The socket is used with the first stream created by SocketsHttpHandler.ConnectCallback.
               The transport keeps track of the socket or the streams in use to determine if the connection
               is ready. Connectivity API features require knowing whether there is a connection available.
               A limitation of the .NET support is only socket connectivity to the server is tracked.
               This transport is unable to check whether TLS and HTTP is succcessfully negotiated.
            2. Transport supports multiple addresses. When connecting it will iterate through the addresses,
               attempting to connect to each one.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Internal.SocketConnectivitySubchannelTransport.ShouldCloseSocket(System.Net.Sockets.Socket,System.Net.DnsEndPoint,System.Collections.Generic.List{System.ReadOnlyMemory{System.Byte}}@,System.Exception@)">
            <summary>
            Checks whether the socket is healthy. May read available data into the passed in buffer.
            Returns true if the socket should be closed.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Internal.SocketConnectivitySubchannelTransport.PollSocket(System.Net.Sockets.Socket,System.Net.DnsEndPoint)">
            <summary>
            Poll the socket to check for health and available data.
            Shouldn't be used by itself as data needs to be consumed to accurately report the socket health.
            <see cref="M:Grpc.Net.Client.Balancer.Internal.SocketConnectivitySubchannelTransport.ShouldCloseSocket(System.Net.Sockets.Socket,System.Net.DnsEndPoint,System.Collections.Generic.List{System.ReadOnlyMemory{System.Byte}}@,System.Exception@)"/> handles consuming data and getting the socket health.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.ISubchannelCallTracker">
            <summary>
            An interface for tracking subchannel calls.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.ISubchannelCallTracker.Start">
            <summary>
            Called when a subchannel call is started after a load balance pick.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.ISubchannelCallTracker.Complete(Grpc.Net.Client.Balancer.CompletionContext)">
            <summary>
            Called when a subchannel call is completed.
            </summary>
            <param name="context">The complete context.</param>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.LoadBalancer">
            <summary>
            A configurable component that receives resolved addresses from <see cref="T:Grpc.Net.Client.Balancer.Resolver"/> and provides a usable
            <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/> when asked.
            <para>
            A new load balancer implements:
            <list type="bullet">
              <item>
                <description>
                  <see cref="T:Grpc.Net.Client.Balancer.LoadBalancerFactory"/> creates new <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> instances.
                </description>
              </item>
              <item>
                <description>
                  <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> receives results from the <see cref="T:Grpc.Net.Client.Balancer.Resolver"/>, subchannels'
                  connectivity states, and requests to create a connection and shutdown.
                  <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> creates <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/> instances from resolve results using
                  <see cref="T:Grpc.Net.Client.Balancer.IChannelControlHelper"/>, and updates the channel state with a <see cref="T:Grpc.Core.ConnectivityState"/>
                  and <see cref="T:Grpc.Net.Client.Balancer.SubchannelPicker"/>.
                </description>
              </item>
              <item>
                <description>
                  <see cref="T:Grpc.Net.Client.Balancer.SubchannelPicker"/> makes a load balancing decision.
                </description>
              </item>
            </list>
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.LoadBalancer.UpdateChannelState(Grpc.Net.Client.Balancer.ChannelState)">
            <summary>
            Updates the <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> with state from the <see cref="T:Grpc.Net.Client.Balancer.Resolver"/>.
            </summary>
            <param name="state">State from the <see cref="T:Grpc.Net.Client.Balancer.Resolver"/>.</param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.LoadBalancer.RequestConnection">
            <summary>
            Request the <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> to establish connections now (if applicable) so that
            future calls can use a ready connection without waiting for a connection.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.LoadBalancer.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> and optionally releases
            the managed resources.
            </summary>
            <param name="disposing">
            <c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.
            </param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.LoadBalancer.Dispose">
            <summary>
            Disposes the <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/>.
            The load balancer state is updated to <see cref="F:Grpc.Core.ConnectivityState.Shutdown"/>.
            state 
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.LoadBalancerFactory">
            <summary>
            Factory for creating new <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> instances. A factory is used when the load balancer config name
            matches the factory name.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.LoadBalancerFactory.Name">
            <summary>
            Gets the load balancer factory name. A factory is used when the load balancer config name
            matches the factory name.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.LoadBalancerFactory.Create(Grpc.Net.Client.Balancer.LoadBalancerOptions)">
            <summary>
            Creates a new <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> with the specified options.
            </summary>
            <param name="options">Options for creating a <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/>.</param>
            <returns>A new <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/>.</returns>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.LoadBalancerOptions">
            <summary>
            Options for creating a <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/>.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.LoadBalancerOptions.#ctor(Grpc.Net.Client.Balancer.IChannelControlHelper,Microsoft.Extensions.Logging.ILoggerFactory,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.LoadBalancerOptions"/> class.
            </summary>
            <param name="controller">The controller.</param>
            <param name="loggerFactory">The logger factory.</param>
            <param name="configuration">The load balancer configuration.</param>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.LoadBalancerOptions.Controller">
            <summary>
            Gets the <see cref="T:Grpc.Net.Client.Balancer.IChannelControlHelper"/>.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.LoadBalancerOptions.LoggerFactory">
            <summary>
            Gets the logger factory.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.LoadBalancerOptions.Configuration">
            <summary>
            Gets the load balancer configuration.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.PickContext">
            <summary>
            Context used to pick a <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/>.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.PickContext.Request">
            <summary>
            Gets or sets the <see cref="T:System.Net.Http.HttpRequestMessage"/>.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.PickFirstBalancer">
            <summary>
            A <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> that attempts to connect to addresses until a connection
            is successfully made. gRPC calls are all made to the first successful connection.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PickFirstBalancer.#ctor(Grpc.Net.Client.Balancer.IChannelControlHelper,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.PickFirstBalancer"/> class.
            </summary>
            <param name="controller">The controller.</param>
            <param name="loggerFactory">The logger factory.</param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PickFirstBalancer.UpdateChannelState(Grpc.Net.Client.Balancer.ChannelState)">
            <inheritdoc />
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PickFirstBalancer.Dispose(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PickFirstBalancer.RequestConnection">
            <inheritdoc />
        </member>
        <member name="T:Grpc.Net.Client.Balancer.PickFirstBalancerFactory">
            <summary>
            A <see cref="T:Grpc.Net.Client.Balancer.LoadBalancerFactory"/> that matches the name <c>pick_first</c>
            and creates <see cref="T:Grpc.Net.Client.Balancer.PickFirstBalancer"/> instances.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.PickFirstBalancerFactory.Name">
            <inheritdoc />
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PickFirstBalancerFactory.Create(Grpc.Net.Client.Balancer.LoadBalancerOptions)">
            <inheritdoc />
        </member>
        <member name="T:Grpc.Net.Client.Balancer.PickResult">
            <summary>
            A balancing decision made by a <see cref="T:Grpc.Net.Client.Balancer.SubchannelPicker"/> for a gRPC call.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.PickResult.Type">
            <summary>
            The pick result type.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.PickResult.Subchannel">
            <summary>
            The <see cref="P:Grpc.Net.Client.Balancer.PickResult.Subchannel"/> provided by <see cref="M:Grpc.Net.Client.Balancer.PickResult.ForSubchannel(Grpc.Net.Client.Balancer.Subchannel,Grpc.Net.Client.Balancer.ISubchannelCallTracker)"/>.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.PickResult.Status">
            <summary>
            The <see cref="T:Grpc.Core.Status"/> provided by <see cref="M:Grpc.Net.Client.Balancer.PickResult.ForFailure(Grpc.Core.Status)"/> or <see cref="M:Grpc.Net.Client.Balancer.PickResult.ForDrop(Grpc.Core.Status)"/>.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.PickResult.SubchannelCallTracker">
            <summary>
            The optional <see cref="P:Grpc.Net.Client.Balancer.PickResult.SubchannelCallTracker"/> provided by <see cref="M:Grpc.Net.Client.Balancer.PickResult.ForSubchannel(Grpc.Net.Client.Balancer.Subchannel,Grpc.Net.Client.Balancer.ISubchannelCallTracker)"/>.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PickResult.ForSubchannel(Grpc.Net.Client.Balancer.Subchannel,Grpc.Net.Client.Balancer.ISubchannelCallTracker)">
            <summary>
            Create a <see cref="T:Grpc.Net.Client.Balancer.PickResult"/> that provides a <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/> to gRPC calls.
            <para>
            A result created with a <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/> won't necessarily be used by a gRPC call.
            The subchannel's state may change at the same time the picker is making a decision. That means the
            decision may be made with outdated information. For example, a picker may return a subchannel
            with a state that is <see cref="F:Grpc.Core.ConnectivityState.Ready"/>, but
            becomes <see cref="F:Grpc.Core.ConnectivityState.Idle"/> when the subchannel is about to be used. In that situation
            the gRPC call waits for the load balancer to react to the new state and create a new picker.
            </para>
            </summary>
            <param name="subchannel">The picked subchannel.</param>
            <param name="subchannelCallTracker">An optional interface to track the subchannel call.</param>
            <returns>The pick result.</returns>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PickResult.ForFailure(Grpc.Core.Status)">
            <summary>
            Creates a <see cref="T:Grpc.Net.Client.Balancer.PickResult"/> to report a connectivity error to calls. If the call has
            a <see cref="P:Grpc.Core.CallOptions.IsWaitForReady"/> value of <c>true</c> then the call will wait.
            </summary>
            <param name="status">The error status. Must not be <see cref="F:Grpc.Core.StatusCode.OK"/>.</param>
            <returns>The pick result.</returns>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PickResult.ForDrop(Grpc.Core.Status)">
            <summary>
            Creates a <see cref="T:Grpc.Net.Client.Balancer.PickResult"/> to fail a gRPC call immediately. A result with a type of 
            <see cref="F:Grpc.Net.Client.Balancer.PickResultType.Drop"/> causes calls to ignore <see cref="P:Grpc.Core.CallOptions.IsWaitForReady"/> and retry.
            </summary>
            <param name="status">The error status. Must not be <see cref="F:Grpc.Core.StatusCode.OK"/>.</param>
            <returns>The pick result.</returns>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PickResult.ForQueue">
            <summary>
            Creates a <see cref="T:Grpc.Net.Client.Balancer.PickResult"/> to queue gRPC calls.
            </summary>
            <returns>The pick result.</returns>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.PickResultType">
            <summary>
            The <see cref="T:Grpc.Net.Client.Balancer.PickResult"/> type.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="F:Grpc.Net.Client.Balancer.PickResultType.Complete">
            <summary>
            <see cref="T:Grpc.Net.Client.Balancer.PickResult"/> with a <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/>.
            </summary>
        </member>
        <member name="F:Grpc.Net.Client.Balancer.PickResultType.Queue">
            <summary>
            <see cref="T:Grpc.Net.Client.Balancer.PickResult"/> that was unable to resolve success or failure.
            This result will queue gRPC calls until a non-queue result is available.
            </summary>
        </member>
        <member name="F:Grpc.Net.Client.Balancer.PickResultType.Fail">
            <summary>
            <see cref="T:Grpc.Net.Client.Balancer.PickResult"/> with a connectivity error. gRPC calls fail
            unless <see cref="P:Grpc.Core.CallOptions.IsWaitForReady"/> is set to <c>true</c>.
            If <see cref="P:Grpc.Core.CallOptions.IsWaitForReady"/> is set to <c>true</c> then gRPC calls
            will queue.
            </summary>
        </member>
        <member name="F:Grpc.Net.Client.Balancer.PickResultType.Drop">
            <summary>
            <see cref="T:Grpc.Net.Client.Balancer.PickResult"/> with an immediate failure. All gRPC calls will fail,
            regardless of what <see cref="P:Grpc.Core.CallOptions.IsWaitForReady"/> is set to,
            and retry logic is bypassed.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.PollingResolver">
            <summary>
            An abstract base type for <see cref="T:Grpc.Net.Client.Balancer.Resolver"/> implementations that use asynchronous polling logic to resolve the <see cref="T:System.Uri"/>.
            <para>
            <see cref="T:Grpc.Net.Client.Balancer.PollingResolver"/> adds a virtual <see cref="M:Grpc.Net.Client.Balancer.PollingResolver.ResolveAsync(System.Threading.CancellationToken)"/> method. The resolver runs one asynchronous
            resolve task at a time. Calling <see cref="M:Grpc.Net.Client.Balancer.PollingResolver.Refresh"/> on the resolver when a resolve task is already running has
            no effect.
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.PollingResolver.Listener">
            <summary>
            Gets the listener.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PollingResolver.#ctor(Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.PollingResolver"/>.
            </summary>
            <param name="loggerFactory">The logger factory.</param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PollingResolver.#ctor(Microsoft.Extensions.Logging.ILoggerFactory,Grpc.Net.Client.Balancer.IBackoffPolicyFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.PollingResolver"/>.
            </summary>
            <param name="loggerFactory">The logger factory.</param>
            <param name="backoffPolicyFactory">The backoff policy factory.</param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PollingResolver.Start(System.Action{Grpc.Net.Client.Balancer.ResolverResult})">
            <summary>
            Starts listening to resolver for results with the specified callback. Can only be called once.
            <para>
            The <see cref="T:Grpc.Net.Client.Balancer.ResolverResult"/> passed to the callback has addresses when successful,
            otherwise a <see cref="T:Grpc.Core.Status"/> details the resolution error.
            </para>
            </summary>
            <param name="listener">The callback used to receive updates on the target.</param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PollingResolver.OnStarted">
            <summary>
            Executes after the resolver starts.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PollingResolver.Refresh">
            <summary>
            Refresh resolution. Can only be called after <see cref="M:Grpc.Net.Client.Balancer.PollingResolver.Start(System.Action{Grpc.Net.Client.Balancer.ResolverResult})"/>.
            <para>
            The resolver runs one asynchronous resolve task at a time. Calling <see cref="M:Grpc.Net.Client.Balancer.PollingResolver.Refresh"/> on the resolver when a
            resolve task is already running has no effect.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PollingResolver.ResolveAsync(System.Threading.CancellationToken)">
            <summary>
            Resolve the target <see cref="T:System.Uri"/>. Updated results are passed to the callback
            registered by <see cref="M:Grpc.Net.Client.Balancer.PollingResolver.Start(System.Action{Grpc.Net.Client.Balancer.ResolverResult})"/>. Can only be called
            after the resolver has started.
            </summary>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>A task.</returns>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.PollingResolver.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> and optionally releases
            the managed resources.
            </summary>
            <param name="disposing">
            <c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.
            </param>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.Resolver">
            <summary>
            A configurable component that resolves a target <see cref="T:System.Uri"/> and returns them to the caller.
            The targets URI's scheme is used to select the <see cref="T:Grpc.Net.Client.Balancer.Resolver"/> implementation, and uses the
            URI parts after the scheme for actual resolution.
            <para>
            The addresses of a target may change over time, thus the caller registers a callback to receive
            continuous updates as <see cref="T:Grpc.Net.Client.Balancer.ResolverResult"/>.
            </para>
            <para>
            A <see cref="T:Grpc.Net.Client.Balancer.Resolver"/> doesn't need to automatically re-resolve on failure. Instead, the callback
            is responsible for eventually invoking <see cref="M:Grpc.Net.Client.Balancer.Resolver.Refresh"/>.
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Resolver.Start(System.Action{Grpc.Net.Client.Balancer.ResolverResult})">
            <summary>
            Starts listening to resolver for results with the specified callback. Can only be called once.
            <para>
            The <see cref="T:Grpc.Net.Client.Balancer.ResolverResult"/> passed to the callback has addresses when successful,
            otherwise a <see cref="T:Grpc.Core.Status"/> details the resolution error.
            </para>
            </summary>
            <param name="listener">The callback used to receive updates on the target.</param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Resolver.Refresh">
            <summary>
            Refresh resolution. Can only be called after <see cref="M:Grpc.Net.Client.Balancer.Resolver.Start(System.Action{Grpc.Net.Client.Balancer.ResolverResult})"/>.
            The default implementation is no-op.
            <para>
            This is only a hint. Implementation takes it as a signal but may not start resolution.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Resolver.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> and optionally releases
            the managed resources.
            </summary>
            <param name="disposing">
            <c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.
            </param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Resolver.Dispose">
            <summary>
            Disposes the <see cref="T:Grpc.Net.Client.Balancer.Resolver"/>. Stops resolution.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.ResolverResult">
            <summary>
            Represents the results from a <see cref="T:Grpc.Net.Client.Balancer.Resolver"/>.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ResolverResult.Status">
            <summary>
            Gets the status. A status other than <see cref="F:Grpc.Core.StatusCode.OK"/> indicates failure.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ResolverResult.Addresses">
            <summary>
            Gets a collection of resolved addresses.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ResolverResult.ServiceConfig">
            <summary>
            Gets an optional service config.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ResolverResult.ServiceConfigStatus">
            <summary>
            Gets an optional service config status.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ResolverResult.Attributes">
            <summary>
            Gets metadata attributes.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.ResolverResult.ForFailure(Grpc.Core.Status)">
            <summary>
            Create <see cref="T:Grpc.Net.Client.Balancer.ResolverResult"/> for failure.
            </summary>
            <param name="status">The error status. Must not be <see cref="F:Grpc.Core.StatusCode.OK"/>.</param>
            <returns>A resolver result.</returns>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.ResolverResult.ForResult(System.Collections.Generic.IReadOnlyList{Grpc.Net.Client.Balancer.BalancerAddress})">
            <summary>
            Create <see cref="T:Grpc.Net.Client.Balancer.ResolverResult"/> for the specified addresses.
            </summary>
            <param name="addresses">The resolved addresses.</param>
            <returns>A resolver result.</returns>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.ResolverResult.ForResult(System.Collections.Generic.IReadOnlyList{Grpc.Net.Client.Balancer.BalancerAddress},Grpc.Net.Client.Configuration.ServiceConfig,System.Nullable{Grpc.Core.Status})">
            <summary>
            Create <see cref="T:Grpc.Net.Client.Balancer.ResolverResult"/> for the specified addresses and service config.
            </summary>
            <param name="addresses">The resolved addresses.</param>
            <param name="serviceConfig">An optional service config. A <c>null</c> value indicates that the resolver either didn't retreive a service config or an error occurred. The error must be specified using <paramref name="serviceConfigStatus"/>.</param>
            <param name="serviceConfigStatus">A service config status. The status indicates an error retreiveing or parsing the config. The status must not be <see cref="F:Grpc.Core.StatusCode.OK"/> if no service config is specified.</param>
            <returns>A resolver result.</returns>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.ResolverFactory">
            <summary>
            Factory for creating new <see cref="T:Grpc.Net.Client.Balancer.Resolver"/> instances. A factory is used when the
            target address <see cref="T:System.Uri"/> scheme matches the factory name.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ResolverFactory.Name">
            <summary>
            Gets the resolver factory name. A factory is used when the target <see cref="T:System.Uri"/> scheme
            matches the factory name.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.ResolverFactory.Create(Grpc.Net.Client.Balancer.ResolverOptions)">
            <summary>
            Creates a new <see cref="T:Grpc.Net.Client.Balancer.Resolver"/> with the specified options.
            </summary>
            <param name="options">Options for creating a <see cref="T:Grpc.Net.Client.Balancer.Resolver"/>.</param>
            <returns>A new <see cref="T:Grpc.Net.Client.Balancer.Resolver"/>.</returns>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.ResolverOptions">
            <summary>
            Options for creating a resolver.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.ResolverOptions.#ctor(System.Uri,System.Int32,Microsoft.Extensions.Logging.ILoggerFactory,Grpc.Net.Client.GrpcChannelOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.ResolverOptions"/> class.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ResolverOptions.Address">
            <summary>
            Gets the address.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ResolverOptions.DefaultPort">
            <summary>
            Gets the default port. This port is used when the resolver address doesn't specify a port.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ResolverOptions.DisableServiceConfig">
            <summary>
            Gets a flag indicating whether the resolver should disable resolving a service config.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ResolverOptions.LoggerFactory">
            <summary>
            Gets the logger factory.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.ResolverOptions.ChannelOptions">
            <summary>
            Gets the channel options.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.RoundRobinBalancer">
            <summary>
            A <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> that attempts to connect to all addresses. gRPC calls are distributed
            across all successful connections using round-robin logic.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.RoundRobinBalancer.#ctor(Grpc.Net.Client.Balancer.IChannelControlHelper,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.RoundRobinBalancer"/> class.
            </summary>
            <param name="controller">The controller.</param>
            <param name="loggerFactory">The logger factory.</param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.RoundRobinBalancer.CreatePicker(System.Collections.Generic.IReadOnlyList{Grpc.Net.Client.Balancer.Subchannel})">
            <inheritdoc />
        </member>
        <member name="T:Grpc.Net.Client.Balancer.RoundRobinBalancerFactory">
            <summary>
            A <see cref="T:Grpc.Net.Client.Balancer.LoadBalancerFactory"/> that matches the name <c>round_robin</c>
            and creates <see cref="T:Grpc.Net.Client.Balancer.RoundRobinBalancer"/> instances.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.RoundRobinBalancerFactory.Name">
            <inheritdoc />
        </member>
        <member name="M:Grpc.Net.Client.Balancer.RoundRobinBalancerFactory.Create(Grpc.Net.Client.Balancer.LoadBalancerOptions)">
            <inheritdoc />
        </member>
        <member name="T:Grpc.Net.Client.Balancer.StaticResolver">
            <summary>
            A <see cref="T:Grpc.Net.Client.Balancer.Resolver"/> that returns a static collection of addresses.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.StaticResolver.#ctor(System.Collections.Generic.IEnumerable{Grpc.Net.Client.Balancer.BalancerAddress})">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.StaticResolver"/> class with the specified addresses.
            </summary>
            <param name="addresses">The resolved addresses.</param>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.StaticResolverFactory">
            <summary>
            A <see cref="T:Grpc.Net.Client.Balancer.ResolverFactory"/> that matches the URI scheme <c>static</c>
            and creates <see cref="T:Grpc.Net.Client.Balancer.StaticResolver"/> instances.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.StaticResolverFactory.#ctor(System.Func{System.Uri,System.Collections.Generic.IEnumerable{Grpc.Net.Client.Balancer.BalancerAddress}})">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.StaticResolverFactory"/> class with a callback
            that returns a collection of addresses for a target <see cref="T:System.Uri"/>.
            </summary>
            <param name="addressesCallback">
            A callback that returns a collection of addresses for a target <see cref="T:System.Uri"/>.
            </param>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.StaticResolverFactory.Name">
            <inheritdoc />
        </member>
        <member name="M:Grpc.Net.Client.Balancer.StaticResolverFactory.Create(Grpc.Net.Client.Balancer.ResolverOptions)">
            <inheritdoc />
        </member>
        <member name="T:Grpc.Net.Client.Balancer.Subchannel">
            <summary>
            Represents a logical connection. A subchannel is created with one or more addresses to equivalent servers.
            <para>
            A subchannel maintains at most one physical connection (aka transport) for sending new gRPC calls.
            If there isn't an active transport, and a call is assigned to the subchannel, it will create
            a new transport. A transport won't be created otherwise unless <see cref="M:Grpc.Net.Client.Balancer.Subchannel.RequestConnection"/>
            is called to create a transport if there isn't any.
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.Subchannel.State">
            <summary>
            Connectivity state is internal rather than public because it can be updated by multiple threads while
            a load balancer is building the picker.
            Load balancers that care about multiple subchannels should track state by subscribing to
            Subchannel.OnStateChanged and storing results.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.Subchannel.CurrentAddress">
            <summary>
            Gets the current connected address.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.Subchannel.Attributes">
            <summary>
            Gets the metadata attributes.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Subchannel.OnStateChanged(System.Action{Grpc.Net.Client.Balancer.SubchannelState})">
            <summary>
            Registers a callback that will be invoked this subchannel's state changes.
            </summary>
            <param name="callback">The callback that will be invoked when the subchannel's state changes.</param>
            <returns>A subscription that can be disposed to unsubscribe from state changes.</returns>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Subchannel.UpdateAddresses(System.Collections.Generic.IReadOnlyList{Grpc.Net.Client.Balancer.BalancerAddress})">
            <summary>
            Replaces the existing addresses used with this <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/>.
            <para>
            If the subchannel has an active connection and the new addresses contain the connected address
            then the connection is reused. Otherwise the subchannel will reconnect.
            </para>
            </summary>
            <param name="addresses"></param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Subchannel.RequestConnection">
            <summary>
            Creates a connection (aka transport), if there isn't an active one.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Subchannel.ToString">
            <inheritdocs />
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Subchannel.GetAddresses">
            <summary>
            Returns the addresses that this subchannel is bound to.
            </summary>
            <returns>The addresses that this subchannel is bound to.</returns>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.Subchannel.Dispose">
            <summary>
            Disposes the <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/>.
            The subchannel state is updated to <see cref="F:Grpc.Core.ConnectivityState.Shutdown"/>.
            After dispose the subchannel should no longer be returned by the latest <see cref="T:Grpc.Net.Client.Balancer.SubchannelPicker"/>.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.SubchannelOptions">
            <summary>
            Represents options used to create <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/>.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.SubchannelOptions.#ctor(System.Collections.Generic.IReadOnlyList{Grpc.Net.Client.Balancer.BalancerAddress})">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.SubchannelOptions"/> class.
            </summary>
            <param name="addresses">A collection of addresses.</param>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.SubchannelOptions.Addresses">
            <summary>
            Gets a collection of addresses.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.SubchannelPicker">
            <summary>
            Base type for picking a subchannel. A <see cref="T:Grpc.Net.Client.Balancer.SubchannelPicker"/> is responsible for picking
            a ready <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/> that gRPC calls will use.
            <para>
            Load balancers implement <see cref="T:Grpc.Net.Client.Balancer.SubchannelPicker"/> with their own balancing logic to
            determine which subchannel is returned for a call.
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.SubchannelPicker.Pick(Grpc.Net.Client.Balancer.PickContext)">
            <summary>
            Picks a ready <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/> for the specified context.
            </summary>
            <param name="context">The pick content.</param>
            <returns>A ready <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/>.</returns>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.SubchannelsLoadBalancer">
            <summary>
            An abstract <see cref="T:Grpc.Net.Client.Balancer.LoadBalancer"/> that manages creating <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/> instances
            from addresses. It is designed to make it easy to implement a custom picking policy by overriding
            <see cref="M:Grpc.Net.Client.Balancer.SubchannelsLoadBalancer.CreatePicker(System.Collections.Generic.IReadOnlyList{Grpc.Net.Client.Balancer.Subchannel})"/> and returning a custom <see cref="T:Grpc.Net.Client.Balancer.SubchannelPicker"/>.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.SubchannelsLoadBalancer.Controller">
            <summary>
            Gets the controller.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.SubchannelsLoadBalancer.State">
            <summary>
            Gets the connectivity state.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.SubchannelsLoadBalancer.#ctor(Grpc.Net.Client.Balancer.IChannelControlHelper,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.SubchannelsLoadBalancer"/> class.
            </summary>
            <param name="controller">The controller.</param>
            <param name="loggerFactory">The logger factory.</param>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.SubchannelsLoadBalancer.UpdateChannelState(Grpc.Net.Client.Balancer.ChannelState)">
            <inheritdoc />
        </member>
        <member name="M:Grpc.Net.Client.Balancer.SubchannelsLoadBalancer.RequestConnection">
            <inheritdoc />
        </member>
        <member name="M:Grpc.Net.Client.Balancer.SubchannelsLoadBalancer.Dispose(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:Grpc.Net.Client.Balancer.SubchannelsLoadBalancer.CreatePicker(System.Collections.Generic.IReadOnlyList{Grpc.Net.Client.Balancer.Subchannel})">
            <summary>
            Creates a <see cref="T:Grpc.Net.Client.Balancer.SubchannelPicker"/> for the specified <see cref="T:Grpc.Net.Client.Balancer.Subchannel"/> instances.
            This method can be overriden to return new a <see cref="T:Grpc.Net.Client.Balancer.SubchannelPicker"/> implementation
            with custom load balancing logic.
            </summary>
            <param name="readySubchannels">A collection of ready subchannels.</param>
            <returns>A subchannel picker.</returns>
        </member>
        <member name="T:Grpc.Net.Client.Balancer.SubchannelState">
            <summary>
            Represents subchannel state.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Balancer.SubchannelState.#ctor(Grpc.Core.ConnectivityState,Grpc.Core.Status)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Balancer.SubchannelState"/> class.
            </summary>
            <param name="state">The connectivity state.</param>
            <param name="status">The status.</param>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.SubchannelState.State">
            <summary>
            Gets the connectivity state.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Balancer.SubchannelState.Status">
            <summary>
            Gets the status.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Configuration.ConfigObject">
            <summary>
            Represents a configuration object. Implementations provide strongly typed wrappers over
            collections of untyped values.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.ConfigObject.Inner">
            <summary>
            Gets the underlying configuration values.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Configuration.HedgingPolicy">
            <summary>
            The hedging policy for outgoing calls. Hedged calls may execute more than
            once on the server, so only idempotent methods should specify a hedging
            policy.
            </summary>
            <remarks>
            <para>
            Represents the <c>HedgingPolicy</c> message in <see href="https://github.com/grpc/grpc-proto/blob/master/grpc/service_config/service_config.proto"/>.
            </para>
            </remarks>
        </member>
        <member name="M:Grpc.Net.Client.Configuration.HedgingPolicy.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Configuration.HedgingPolicy"/> class.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.HedgingPolicy.MaxAttempts">
             <summary>
             Gets or sets the maximum number of call attempts. This value includes the original attempt.
             The hedging policy will send up to this number of calls.
            
             This property is required and must be 2 or greater.
             This value is limited by <see cref="P:Grpc.Net.Client.GrpcChannelOptions.MaxRetryAttempts"/>.
             </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.HedgingPolicy.HedgingDelay">
            <summary>
            Gets or sets the hedging delay.
            The first call will be sent immediately, but the subsequent
            hedged call will be sent at intervals of the specified delay.
            Set this to 0 or <c>null</c> to immediately send all hedged calls.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.HedgingPolicy.NonFatalStatusCodes">
             <summary>
             Gets a collection of status codes which indicate other hedged calls may still
             succeed. If a non-fatal status code is returned by the server, hedged
             calls will continue. Otherwise, outstanding requests will be canceled and
             the error returned to the client application layer.
            
             Specifying status codes is optional.
             </summary>
        </member>
        <member name="T:Grpc.Net.Client.Configuration.LoadBalancingConfig">
            <summary>
            Base type for load balancer policy configuration.
            </summary>
        </member>
        <member name="F:Grpc.Net.Client.Configuration.LoadBalancingConfig.PickFirstPolicyName">
            <summary>
            <c>pick_first</c> policy name.
            </summary>
        </member>
        <member name="F:Grpc.Net.Client.Configuration.LoadBalancingConfig.RoundRobinPolicyName">
            <summary>
            <c>round_robin</c> policy name.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Configuration.LoadBalancingConfig.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Configuration.LoadBalancingConfig"/> class.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.LoadBalancingConfig.PolicyName">
            <summary>
            Gets the load balancer policy name.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Configuration.MethodConfig">
            <summary>
            Configuration for a method.
            The <see cref="P:Grpc.Net.Client.Configuration.MethodConfig.Names"/> collection is used to determine which methods this configuration applies to.
            </summary>
            <remarks>
            <para>
            Represents the <c>MethodConfig</c> message in <see href="https://github.com/grpc/grpc-proto/blob/master/grpc/service_config/service_config.proto"/>.
            </para>
            </remarks>
        </member>
        <member name="M:Grpc.Net.Client.Configuration.MethodConfig.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Configuration.MethodConfig"/> class.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.MethodConfig.RetryPolicy">
            <summary>
            Gets or sets the retry policy for outgoing calls.
            A retry policy can't be combined with <see cref="P:Grpc.Net.Client.Configuration.MethodConfig.HedgingPolicy"/>.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.MethodConfig.HedgingPolicy">
            <summary>
            Gets or sets the hedging policy for outgoing calls. Hedged calls may execute
            more than once on the server, so only idempotent methods should specify a hedging
            policy. A hedging policy can't be combined with <see cref="P:Grpc.Net.Client.Configuration.MethodConfig.RetryPolicy"/>.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.MethodConfig.Names">
            <summary>
            Gets a collection of names which determine the calls the method config will apply to.
            A <see cref="T:Grpc.Net.Client.Configuration.MethodConfig"/> without names won't be used. Each name must be unique
            across an entire <see cref="T:Grpc.Net.Client.Configuration.ServiceConfig"/>.
            </summary>
            <remarks>
            <para>
            If a name's <see cref="P:Grpc.Net.Client.Configuration.MethodName.Method"/> property isn't set then the method config is the default
            for all methods for the specified service.
            </para>
            <para>
            If a name's <see cref="P:Grpc.Net.Client.Configuration.MethodName.Service"/> property isn't set then <see cref="P:Grpc.Net.Client.Configuration.MethodName.Method"/> must also be unset,
            and the method config is the default for all methods on all services.
            <see cref="F:Grpc.Net.Client.Configuration.MethodName.Default"/> represents this global default name.
            </para>
            <para>
            When determining which method config to use for a given RPC, the most specific match wins. A method config
            with a configured <see cref="T:Grpc.Net.Client.Configuration.MethodName"/> that exactly matches a call's method and service will be used
            instead of a service or global default method config.
            </para>
            </remarks>
        </member>
        <member name="T:Grpc.Net.Client.Configuration.MethodName">
            <summary>
            The name of a method. Used to configure what calls a <see cref="T:Grpc.Net.Client.Configuration.MethodConfig"/> applies to using
            the <see cref="P:Grpc.Net.Client.Configuration.MethodConfig.Names"/> collection.
            </summary>
            <remarks>
            <para>
            Represents the <c>Name</c> message in <see href="https://github.com/grpc/grpc-proto/blob/master/grpc/service_config/service_config.proto"/>.
            </para>
            <para>
            If a name's <see cref="P:Grpc.Net.Client.Configuration.MethodName.Method"/> property isn't set then the method config is the default
            for all methods for the specified service.
            </para>
            <para>
            If a name's <see cref="P:Grpc.Net.Client.Configuration.MethodName.Service"/> property isn't set then <see cref="P:Grpc.Net.Client.Configuration.MethodName.Method"/> must also be unset,
            and the method config is the default for all methods on all services.
            <see cref="F:Grpc.Net.Client.Configuration.MethodName.Default"/> represents this global default name.
            </para>
            <para>
            When determining which method config to use for a given RPC, the most specific match wins. A method config
            with a configured <see cref="T:Grpc.Net.Client.Configuration.MethodName"/> that exactly matches a call's method and service will be used
            instead of a service or global default method config.
            </para>
            </remarks>
        </member>
        <member name="F:Grpc.Net.Client.Configuration.MethodName.Default">
            <summary>
            A global default name.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Configuration.MethodName.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Configuration.MethodName"/> class.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.MethodName.Service">
            <summary>
            Gets or sets the service name.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.MethodName.Method">
            <summary>
            Gets or sets the method name.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Configuration.PickFirstConfig">
            <summary>
            Configuration for pick_first load balancer policy.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Configuration.PickFirstConfig.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Configuration.PickFirstConfig"/> class.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Configuration.RetryPolicy">
            <summary>
            The retry policy for outgoing calls.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Configuration.RetryPolicy.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Configuration.RetryPolicy"/> class.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.RetryPolicy.MaxAttempts">
            <summary>
            Gets or sets the maximum number of call attempts. This value includes the original attempt.
            This property is required and must be greater than 1.
            This value is limited by <see cref="P:Grpc.Net.Client.GrpcChannelOptions.MaxRetryAttempts"/>.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.RetryPolicy.InitialBackoff">
            <summary>
            Gets or sets the initial backoff.
            A randomized delay between 0 and the current backoff value will determine when the next
            retry attempt is made.
            This property is required and must be greater than zero.
            <para>
            The backoff will be multiplied by <see cref="P:Grpc.Net.Client.Configuration.RetryPolicy.BackoffMultiplier"/> after each retry
            attempt and will increase exponentially when the multiplier is greater than 1.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.RetryPolicy.MaxBackoff">
            <summary>
            Gets or sets the maximum backoff.
            The maximum backoff places an upper limit on exponential backoff growth.
            This property is required and must be greater than zero.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.RetryPolicy.BackoffMultiplier">
            <summary>
            Gets or sets the backoff multiplier.
            The backoff will be multiplied by <see cref="P:Grpc.Net.Client.Configuration.RetryPolicy.BackoffMultiplier"/> after each retry
            attempt and will increase exponentially when the multiplier is greater than 1.
            This property is required and must be greater than 0.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.RetryPolicy.RetryableStatusCodes">
            <summary>
            Gets a collection of status codes which may be retried.
            At least one status code is required.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Configuration.RetryThrottlingPolicy">
            <summary>
            The retry throttling policy for a server.
            <para>
            For more information about configuring throttling, see <see href="https://github.com/grpc/proposal/blob/master/A6-client-retries.md#throttling-retry-attempts-and-hedged-rpcs"/>.
            </para>
            </summary>
            <remarks>
            <para>
            Represents the <c>RetryThrottlingPolicy</c> message in <see href="https://github.com/grpc/grpc-proto/blob/master/grpc/service_config/service_config.proto"/>.
            </para>
            </remarks>
        </member>
        <member name="M:Grpc.Net.Client.Configuration.RetryThrottlingPolicy.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Configuration.RetryThrottlingPolicy"/> class.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.RetryThrottlingPolicy.MaxTokens">
            <summary>
            Gets or sets the maximum number of tokens.
            The number of tokens starts at <see cref="P:Grpc.Net.Client.Configuration.RetryThrottlingPolicy.MaxTokens"/> and the token count will
            always be between 0 and <see cref="P:Grpc.Net.Client.Configuration.RetryThrottlingPolicy.MaxTokens"/>.
            This property is required and must be greater than zero.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.RetryThrottlingPolicy.TokenRatio">
            <summary>
            Gets or sets the amount of tokens to add on each successful call. Typically this will
            be some number between 0 and 1, e.g., 0.1.
            This property is required and must be greater than zero. Up to 3 decimal places are supported.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Configuration.RoundRobinConfig">
            <summary>
            Configuration for pick_first load balancer policy.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Configuration.RoundRobinConfig.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Configuration.RoundRobinConfig"/> class.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Configuration.ServiceConfig">
            <summary>
            A <see cref="T:Grpc.Net.Client.Configuration.ServiceConfig"/> represents information about a service.
            </summary>
            <remarks>
            <para>
            Represents the <c>ServiceConfig</c> message in <see href="https://github.com/grpc/grpc-proto/blob/master/grpc/service_config/service_config.proto"/>.
            </para>
            </remarks>
        </member>
        <member name="M:Grpc.Net.Client.Configuration.ServiceConfig.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.Configuration.ServiceConfig"/> class.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.ServiceConfig.LoadBalancingConfigs">
            <summary>
            Gets a collection of <see cref="T:Grpc.Net.Client.Configuration.LoadBalancingConfig"/> instances. The client will iterate
            through the configured policies in order and use the first policy that is supported.
            If none are supported by the client then a configuration error is thrown.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.ServiceConfig.MethodConfigs">
            <summary>
            Gets a collection of <see cref="T:Grpc.Net.Client.Configuration.MethodConfig"/> instances. This collection is used to specify
            configuration on a per-method basis. <see cref="P:Grpc.Net.Client.Configuration.MethodConfig.Names"/> determines which calls
            a method config applies to.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Configuration.ServiceConfig.RetryThrottling">
            <summary>
            Gets or sets the retry throttling policy.
            If a <see cref="T:Grpc.Net.Client.Configuration.RetryThrottlingPolicy"/> is provided, gRPC will automatically throttle
            retry attempts and hedged RPCs when the client's ratio of failures to
            successes exceeds a threshold.
            <para>
            For more information about configuring throttling, see <see href="https://github.com/grpc/proposal/blob/master/A6-client-retries.md#throttling-retry-attempts-and-hedged-rpcs"/>.
            </para>
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.GrpcChannel">
            <summary>
            Represents a gRPC channel. Channels are an abstraction of long-lived connections to remote servers.
            Client objects can reuse the same channel. Creating a channel is an expensive operation compared to invoking
            a remote call so in general you should reuse a single channel for as many calls as possible.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.GrpcChannel.CreateCallInvoker">
            <summary>
            Create a new <see cref="T:Grpc.Core.CallInvoker"/> for the channel.
            </summary>
            <returns>A new <see cref="T:Grpc.Core.CallInvoker"/>.</returns>
        </member>
        <member name="M:Grpc.Net.Client.GrpcChannel.ForAddress(System.String)">
            <summary>
            Creates a <see cref="T:Grpc.Net.Client.GrpcChannel"/> for the specified address.
            </summary>
            <param name="address">The address the channel will use.</param>
            <returns>A new instance of <see cref="T:Grpc.Net.Client.GrpcChannel"/>.</returns>
        </member>
        <member name="M:Grpc.Net.Client.GrpcChannel.ForAddress(System.String,Grpc.Net.Client.GrpcChannelOptions)">
            <summary>
            Creates a <see cref="T:Grpc.Net.Client.GrpcChannel"/> for the specified address and configuration options.
            </summary>
            <param name="address">The address the channel will use.</param>
            <param name="channelOptions">The channel configuration options.</param>
            <returns>A new instance of <see cref="T:Grpc.Net.Client.GrpcChannel"/>.</returns>
        </member>
        <member name="M:Grpc.Net.Client.GrpcChannel.ForAddress(System.Uri)">
            <summary>
            Creates a <see cref="T:Grpc.Net.Client.GrpcChannel"/> for the specified address.
            </summary>
            <param name="address">The address the channel will use.</param>
            <returns>A new instance of <see cref="T:Grpc.Net.Client.GrpcChannel"/>.</returns>
        </member>
        <member name="M:Grpc.Net.Client.GrpcChannel.ForAddress(System.Uri,Grpc.Net.Client.GrpcChannelOptions)">
            <summary>
            Creates a <see cref="T:Grpc.Net.Client.GrpcChannel"/> for the specified address and configuration options.
            </summary>
            <param name="address">The address the channel will use.</param>
            <param name="channelOptions">The channel configuration options.</param>
            <returns>A new instance of <see cref="T:Grpc.Net.Client.GrpcChannel"/>.</returns>
        </member>
        <member name="M:Grpc.Net.Client.GrpcChannel.ConnectAsync(System.Threading.CancellationToken)">
            <summary>
            Allows explicitly requesting channel to connect without starting an RPC.
            Returned task completes once <see cref="P:Grpc.Net.Client.GrpcChannel.State"/> Ready was seen.
            There is no need to call this explicitly unless your use case requires that.
            Starting an RPC on a new channel will request connection implicitly.
            <para>
            This API is only supported when the channel is configured with a
            <see cref="T:System.Net.Http.SocketsHttpHandler"/> HTTP transport.
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
            <param name="cancellationToken">The cancellation token.</param>
            <returns></returns>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannel.State">
            <summary>
            Gets current connectivity state of this channel.
            After the channel has been shutdown, <see cref="F:Grpc.Core.ConnectivityState.Shutdown"/> is returned.
            <para>
            This API is only supported when the channel is configured with a
            <see cref="T:System.Net.Http.SocketsHttpHandler"/> HTTP transport.
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.GrpcChannel.WaitForStateChangedAsync(Grpc.Core.ConnectivityState,System.Threading.CancellationToken)">
            <summary>
            Wait for channel's state to change. The task completes when <see cref="P:Grpc.Net.Client.GrpcChannel.State"/> becomes
            different from <paramref name="lastObservedState"/>.
            <para>
            This API is only supported when the channel is configured with a
            <see cref="T:System.Net.Http.SocketsHttpHandler"/> HTTP transport.
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
            <param name="lastObservedState">The last observed state. The task completes when <see cref="P:Grpc.Net.Client.GrpcChannel.State"/> becomes different from this value.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Grpc.Net.Client.GrpcChannel.Dispose">
            <summary>
            Releases the resources used by the <see cref="T:Grpc.Net.Client.GrpcChannel"/> class.
            Clients created with the channel can't be used after the channel is disposed.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.GrpcChannelOptions">
            <summary>
            An options class for configuring a <see cref="T:Grpc.Net.Client.GrpcChannel"/>.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.Credentials">
            <summary>
            Gets or sets the credentials for the channel. This setting is used to set <see cref="T:Grpc.Core.ChannelCredentials"/> for
            a channel. Connection transport layer security (TLS) is determined by the address used to create the channel.
            </summary>
            <remarks>
            <para>
            The channel credentials you use must match the address TLS setting. Use <see cref="P:Grpc.Core.ChannelCredentials.Insecure"/>
            for an "http" address and <see cref="P:Grpc.Core.ChannelCredentials.SecureSsl"/> for "https".
            </para>
            <para>
            The underlying <see cref="T:System.Net.Http.HttpClient"/> used by the channel automatically loads root certificates
            from the operating system certificate store.
            Client certificates should be configured on HttpClient. See <see href="https://aka.ms/aspnet/grpc/certauth"/> for details.
            </para>
            </remarks>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.MaxSendMessageSize">
            <summary>
            Gets or sets the maximum message size in bytes that can be sent from the client. Attempting to send a message
            that exceeds the configured maximum message size results in an exception.
            <para>
            A <c>null</c> value removes the maximum message size limit. Defaults to <c>null</c>.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.MaxReceiveMessageSize">
            <summary>
            Gets or sets the maximum message size in bytes that can be received by the client. If the client receives a
            message that exceeds this limit, it throws an exception.
            <para>
            A <c>null</c> value removes the maximum message size limit. Defaults to 4,194,304 (4 MB).
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.MaxRetryAttempts">
            <summary>
            Gets or sets the maximum retry attempts. This value limits any retry and hedging attempt values specified in
            the service config.
            <para>
            Setting this value alone doesn't enable retries. Retries are enabled in the service config, which can be done
            using <see cref="P:Grpc.Net.Client.GrpcChannelOptions.ServiceConfig"/>.
            </para>
            <para>
            A <c>null</c> value removes the maximum retry attempts limit. Defaults to 5.
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.MaxRetryBufferSize">
            <summary>
            Gets or sets the maximum buffer size in bytes that can be used to store sent messages when retrying
            or hedging calls. If the buffer limit is exceeded, then no more retry attempts are made and all
            hedging calls but one will be canceled. This limit is applied across all calls made using the channel.
            <para>
            Setting this value alone doesn't enable retries. Retries are enabled in the service config, which can be done
            using <see cref="P:Grpc.Net.Client.GrpcChannelOptions.ServiceConfig"/>.
            </para>
            <para>
            A <c>null</c> value removes the maximum retry buffer size limit. Defaults to 16,777,216 (16 MB).
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.MaxRetryBufferPerCallSize">
            <summary>
            Gets or sets the maximum buffer size in bytes that can be used to store sent messages when retrying
            or hedging calls. If the buffer limit is exceeded, then no more retry attempts are made and all
            hedging calls but one will be canceled. This limit is applied to one call.
            <para>
            Setting this value alone doesn't enable retries. Retries are enabled in the service config, which can be done
            using <see cref="P:Grpc.Net.Client.GrpcChannelOptions.ServiceConfig"/>.
            </para>
            <para>
            A <c>null</c> value removes the maximum retry buffer size limit per call. Defaults to 1,048,576 (1 MB).
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.CompressionProviders">
            <summary>
            Gets or sets a collection of compression providers.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.LoggerFactory">
            <summary>
            Gets or sets the logger factory used by the channel. If no value is specified then the channel
            attempts to resolve an <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> from the <see cref="P:Grpc.Net.Client.GrpcChannelOptions.ServiceProvider"/>.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.HttpClient">
            <summary>
            Gets or sets the <see cref="T:System.Net.Http.HttpClient"/> used by the channel to make HTTP calls.
            </summary>
            <remarks>
            <para>
            By default a <see cref="T:System.Net.Http.HttpClient"/> specified here will not be disposed with the channel.
            To dispose the <see cref="T:System.Net.Http.HttpClient"/> with the channel you must set <see cref="P:Grpc.Net.Client.GrpcChannelOptions.DisposeHttpClient"/>
            to <c>true</c>.
            </para>
            <para>
            Only one HTTP caller can be specified for a channel. An error will be thrown if this is configured
            together with <see cref="P:Grpc.Net.Client.GrpcChannelOptions.HttpHandler"/>.
            </para>
            </remarks>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.HttpHandler">
            <summary>
            Gets or sets the <see cref="T:System.Net.Http.HttpMessageHandler"/> used by the channel to make HTTP calls.
            </summary>
            <remarks>
            <para>
            By default a <see cref="T:System.Net.Http.HttpMessageHandler"/> specified here will not be disposed with the channel.
            To dispose the <see cref="T:System.Net.Http.HttpMessageHandler"/> with the channel you must set <see cref="P:Grpc.Net.Client.GrpcChannelOptions.DisposeHttpClient"/>
            to <c>true</c>.
            </para>
            <para>
            Only one HTTP caller can be specified for a channel. An error will be thrown if this is configured
            together with <see cref="P:Grpc.Net.Client.GrpcChannelOptions.HttpClient"/>.
            </para>
            </remarks>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.DisposeHttpClient">
            <summary>
            Gets or sets a value indicating whether the underlying <see cref="T:System.Net.Http.HttpClient"/> or 
            <see cref="T:System.Net.Http.HttpMessageHandler"/> should be disposed when the <see cref="T:Grpc.Net.Client.GrpcChannel"/> instance is disposed.
            The default value is <c>false</c>.
            </summary>
            <remarks>
            This setting is used when a <see cref="P:Grpc.Net.Client.GrpcChannelOptions.HttpClient"/> or <see cref="P:Grpc.Net.Client.GrpcChannelOptions.HttpHandler"/> value is specified.
            If they are not specified then the channel will create an internal HTTP caller that is always disposed
            when the channel is disposed.
            </remarks>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.ThrowOperationCanceledOnCancellation">
            <summary>
            Gets or sets a value indicating whether clients will throw <see cref="T:System.OperationCanceledException"/> for a call when its
            <see cref="P:Grpc.Core.CallOptions.CancellationToken"/> is triggered or its <see cref="P:Grpc.Core.CallOptions.Deadline"/> is exceeded.
            The default value is <c>false</c>.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.UnsafeUseInsecureChannelCallCredentials">
            <summary>
            Gets or sets a value indicating whether a gRPC call's <see cref="T:Grpc.Core.CallCredentials"/> are used by an insecure channel.
            The default value is <c>false</c>.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
            <remarks>
            <para>
            The default value for this property is <c>false</c>, which causes an insecure channel to ignore a gRPC call's <see cref="T:Grpc.Core.CallCredentials"/>.
            Sending authentication headers over an insecure connection has security implications and shouldn't be done in production environments.
            </para>
            <para>
            If this property is set to <c>true</c>, call credentials are always used by a channel.
            </para>
            </remarks>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.ServiceConfig">
            <summary>
            Gets or sets the service config for a gRPC channel. A service config allows service owners to publish parameters
            to be automatically used by all clients of their service. A service config can also be specified by a client
            using this property.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.DisableResolverServiceConfig">
            <summary>
            Gets or sets a value indicating whether resolving a service config from the <see cref="T:Grpc.Net.Client.Balancer.Resolver"/>
            is disabled.
            The default value is <c>false</c>.
            <para>
            A hint is provided to the resolver that it shouldn't fetch a service config.
            If a service config is returned by then resolver then it is ignored.
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.MaxReconnectBackoff">
            <summary>
            Gets or sets the the maximum time between subsequent connection attempts.
            <para>
            The reconnect backoff starts at an initial backoff and then exponentially increases between attempts, up to the maximum reconnect backoff.
            Reconnect backoff adds a jitter to randomize the backoff. This is done to avoid spikes of connection attempts.
            </para>
            <para>
            A <c>null</c> value removes the maximum reconnect backoff limit. The default value is 120 seconds.
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.InitialReconnectBackoff">
            <summary>
            Gets or sets the time between the first and second connection attempts.
            <para>
            The reconnect backoff starts at an initial backoff and then exponentially increases between attempts, up to the maximum reconnect backoff.
            Reconnect backoff adds a jitter to randomize the backoff. This is done to avoid spikes of connection attempts.
            </para>
            <para>
            Defaults to 1 second.
            </para>
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.GrpcChannelOptions.ServiceProvider">
            <summary>
            Gets or sets the <see cref="T:System.IServiceProvider"/> the channel uses to resolve types.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.GrpcChannelOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Client.GrpcChannelOptions"/> class.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Internal.ClientStreamWriterBase`1.IsWriteInProgressUnsynchronized">
            <summary>
            A value indicating whether there is an async write already in progress.
            Should only check this property when holding the write lock.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Internal.GrpcCall`2.Cleanup(Grpc.Core.Status)">
            <summary>
            Clean up can be called by:
            1. The user. AsyncUnaryCall.Dispose et al will call this on Dispose
            2. <see cref="M:Grpc.Net.Client.Internal.GrpcCall.ValidateHeaders(System.Net.Http.HttpResponseMessage,Grpc.Core.Metadata@)"/> will call dispose if errors fail validation
            3. <see cref="M:Grpc.Net.Client.Internal.GrpcCall`2.FinishResponseAndCleanUp(Grpc.Core.Status)"/> will call dispose
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Internal.GrpcCall`2.ResponseStreamEnded(Grpc.Core.Status,System.Boolean)">
            <summary>
            Used by response stream reader to report it is finished.
            </summary>
            <param name="status">The completed response status code.</param>
            <param name="finishedGracefully">true when the end of the response stream was read, otherwise false.</param>
        </member>
        <member name="M:Grpc.Net.Client.Internal.GrpcCall`2.ResolveException(System.String,System.Exception,System.Nullable{Grpc.Core.Status}@,System.Exception@)">
            <summary>
            Resolve the specified exception to an end-user exception that will be thrown from the client.
            The resolved exception is normally a RpcException. Returns true when the resolved exception is changed.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Internal.GrpcCallSerializationContext.GetWrittenPayload">
            <summary>
            Obtains the payload from this operation. Error is thrown if complete hasn't been called.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Internal.GrpcMethodInfo">
            <summary>
            Cached log scope and URI for a gRPC <see cref="T:Grpc.Core.IMethod"/>.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Internal.GrpcProtocolConstants.GetDebugEnumerator(Grpc.Core.ChannelBase,Grpc.Core.IMethod,System.Object)">
            <summary>
            Gets key value pairs used by debugging. These are provided as an enumerator instead of a dictionary
            because it's one method to implement an enumerator on gRPC calls compared to a dozen members for a dictionary.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Internal.GrpcProtocolHelpers.ResolveRpcExceptionStatusCode(System.Exception)">
            <summary>
            Resolve the exception from HttpClient to a gRPC status code.
            <param name="ex">The <see cref="T:System.Exception"/> to resolve a <see cref="T:Grpc.Core.StatusCode"/> from.</param>
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Internal.Http.WinHttpUnaryContent`2">
            <summary>
            WinHttp doesn't support streaming request data so a length needs to be specified.
            This HttpContent pre-serializes the payload so it has a length available.
            The payload is then written directly to the request using specialized context
            and serializer method.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Internal.HttpClientCallInvoker">
            <summary>
            A client-side RPC invocation using HttpClient.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Internal.HttpClientCallInvoker.AsyncClientStreamingCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions)">
            <summary>
            Invokes a client streaming call asynchronously.
            In client streaming scenario, client sends a stream of requests and server responds with a single response.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Internal.HttpClientCallInvoker.AsyncDuplexStreamingCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions)">
            <summary>
            Invokes a duplex streaming call asynchronously.
            In duplex streaming scenario, client sends a stream of requests and server responds with a stream of responses.
            The response stream is completely independent and both side can be sending messages at the same time.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Internal.HttpClientCallInvoker.AsyncServerStreamingCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)">
            <summary>
            Invokes a server streaming call asynchronously.
            In server streaming scenario, client sends on request and server responds with a stream of responses.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Internal.HttpClientCallInvoker.AsyncUnaryCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)">
            <summary>
            Invokes a simple remote call asynchronously.
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Internal.HttpClientCallInvoker.BlockingUnaryCall``2(Grpc.Core.Method{``0,``1},System.String,Grpc.Core.CallOptions,``0)">
            <summary>
            Invokes a simple remote call in a blocking fashion.
            </summary>
        </member>
        <member name="P:Grpc.Net.Client.Internal.HttpContentClientStreamReader`2.IsMoveNextInProgressUnsynchronized">
            <summary>
            A value indicating whether there is an async move next already in progress.
            Should only check this property when holding the move next lock.
            </summary>
        </member>
        <member name="T:Grpc.Net.Client.Internal.Native">
            <summary>
            Types for calling RtlGetVersion. See https://www.pinvoke.net/default.aspx/ntdll/RtlGetVersion.html
            </summary>
        </member>
        <member name="F:Grpc.Net.Client.Internal.Native.NTSTATUS.STATUS_SUCCESS">
            <summary>
            The operation completed successfully. 
            </summary>
        </member>
        <member name="M:Grpc.Net.Client.Internal.TaskExtensions.ObserveException(System.Threading.Tasks.Task)">
            <summary>
            Observes and ignores a potential exception on a given Task.
            If a Task fails and throws an exception which is never observed, it will be caught by the .NET finalizer thread.
            This function awaits the given task and if the exception is thrown, it observes this exception and simply ignores it.
            This will prevent the escalation of this exception to the .NET finalizer thread.
            </summary>
            <param name="task">The task to be ignored.</param>
        </member>
        <member name="M:Grpc.Net.Client.Internal.UserAgentGenerator.GetUserAgentString">
            <summary>
            Generates a user agent string to be transported in headers.
            <example>
              grpc-dotnet/2.41.0-dev (.NET 6.0.0-preview.7.21377.19; CLR 6.0.0; net6.0; osx; x64)
              grpc-dotnet/2.41.0-dev (Mono 6.12.0.140; CLR 4.0.30319; netstandard2.0; osx; x64)
              grpc-dotnet/2.41.0-dev (.NET 6.0.0-rc.1.21380.1; CLR 6.0.0; net6.0; linux; arm64)
              grpc-dotnet/2.41.0-dev (.NET 5.0.8; CLR 5.0.8; net5.0; linux; arm64)
              grpc-dotnet/2.41.0-dev (.NET Core; CLR 3.1.4; netstandard2.1; linux; arm64)
              grpc-dotnet/2.41.0-dev (.NET Framework; CLR 4.0.30319.42000; netstandard2.0; windows; x86)
              grpc-dotnet/2.41.0-dev (.NET 6.0.0-rc.1.21380.1; CLR 6.0.0; net6.0; windows; x64)
            </example>
            </summary>
        </member>
        <member name="M:Grpc.Shared.ObjectDisposedThrowHelper.ThrowIf(System.Boolean,System.Object)">
            <summary>Throws an <see cref="T:System.ObjectDisposedException"/> if the specified <paramref name="condition"/> is <see langword="true"/>.</summary>
            <param name="condition">The condition to evaluate.</param>
            <param name="instance">The object whose type's full name should be included in any resulting <see cref="T:System.ObjectDisposedException"/>.</param>
            <exception cref="T:System.ObjectDisposedException">The <paramref name="condition"/> is <see langword="true"/>.</exception>
        </member>
        <member name="M:Grpc.Shared.ObjectDisposedThrowHelper.ThrowIf(System.Boolean,System.Type)">
            <summary>Throws an <see cref="T:System.ObjectDisposedException"/> if the specified <paramref name="condition"/> is <see langword="true"/>.</summary>
            <param name="condition">The condition to evaluate.</param>
            <param name="type">The type whose full name should be included in any resulting <see cref="T:System.ObjectDisposedException"/>.</param>
            <exception cref="T:System.ObjectDisposedException">The <paramref name="condition"/> is <see langword="true"/>.</exception>
        </member>
        <member name="M:Grpc.Shared.ArgumentNullThrowHelper.ThrowIfNull(System.Object,System.String)">
            <summary>Throws an <see cref="T:System.ArgumentNullException"/> if <paramref name="argument"/> is null.</summary>
            <param name="argument">The reference type argument to validate as non-null.</param>
            <param name="paramName">The name of the parameter with which <paramref name="argument"/> corresponds.</param>
        </member>
    </members>
</doc>
