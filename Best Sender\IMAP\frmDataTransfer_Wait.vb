﻿Imports System.ComponentModel
Imports System.IO
Imports System.Net
Imports System.Net.Mail
Imports System.Reflection
Imports System.Reflection.Emit
Imports System.Security.Cryptography
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Windows.Forms.VisualStyles.VisualStyleElement
Imports MailKit
Imports MailKit.Net.Imap
Imports MailKit.Search
Imports MailKit.Security
Imports MimeKit
Public Class frmDataTransfer_Wait
    Private Sub frmSMTPTester_Details_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        CenterForm(Me, frmIMAP)
        emailList.Clear()
        lblCount.Text = "found 0 out of " & IMAP_AccountNO
        BackgroundWorker1.WorkerSupportsCancellation = True
        If BackgroundWorker1.CancellationPending Then
            BackgroundWorker1.CancelAsync()
        End If
        If BackgroundWorker1.CancellationPending = True Then BackgroundWorker1.CancelAsync()
        If BackgroundWorker1.IsBusy = True Then BackgroundWorker1.CancelAsync()
        BackgroundWorker1.RunWorkerAsync()
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Try
            ' Connect to the email server
            Using client As New ImapClient()
                client.Connect(IMaphost_, IMapport_, SecureSocketOptions.SslOnConnect)
                client.Authenticate(IMapusername_, IMappassword_)
                ' Select the inbox folder
                Dim inbox As ImapFolder = client.Inbox
                inbox.Open(FolderAccess.ReadOnly)
                ' Get the list of email
                Dim messages As IList(Of UniqueId) = inbox.Search(SearchQuery.All)
                ' Check for the emails that you are interested in
                For Each uid As UniqueId In messages
                    Dim message As MimeMessage = inbox.GetMessage(uid)
                    ' Extract email address from the "From" field
                    Dim email As String = message.From.Mailboxes(0).Address
                    If BackgroundWorker1.CancellationPending Then
                        e.Cancel = True
                        Return
                    End If
                    'Check if email is already in the list
                    If Not emailList.Contains(email) Then
                        emailList.Add(email)
                        If emailList.Count = CInt(IMAP_AccountNO) Then ' هنا الكود الى بيوقف الفورم كولو
                            Exit For
                        End If
                        lblCount.Invoke(New MethodInvoker(Sub() lblCount.Text = "found " & emailList.Count & " out of " & IMAP_AccountNO))
                        Dim result As String = String.Join(Environment.NewLine, emailList)
                        frmIMAP.txtMailList.Invoke(Sub()
                                                       frmIMAP.txtMailList.Text = ""
                                                       frmIMAP.txtMailList.Text = result
                                                       frmIMAP.txtMailList.Refresh()
                                                   End Sub)
                    End If
                Next
                inbox.Close()
                client.Disconnect(True)
            End Using
            IMAP_Accountno_Status = "OK"
        Catch ex As Exception
            IMAP_Accountno_Status = ex.Message
        End Try
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        Me.Dispose()
    End Sub
    Private Sub frmIMAP_Wait_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        If BackgroundWorker1.IsBusy Then
            If BackgroundWorker1.WorkerSupportsCancellation Then
                BackgroundWorker1.CancelAsync()
            End If
        End If
    End Sub
End Class