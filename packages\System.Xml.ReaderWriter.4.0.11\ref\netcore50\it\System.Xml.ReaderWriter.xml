﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.ReaderWriter</name>
  </assembly>
  <members>
    <member name="T:System.Xml.ConformanceLevel">
      <summary>Specifica il livello di controllo dell'input o dell'output eseguito dagli oggetti <see cref="T:System.Xml.XmlReader" /> e <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Auto">
      <summary>L'oggetto <see cref="T:System.Xml.XmlReader" /> o <see cref="T:System.Xml.XmlWriter" /> rileva automaticamente se il controllo deve essere eseguito a livello di documento o di frammento e procede nel modo appropriato.Se viene eseguito il wrapping di un altro oggetto <see cref="T:System.Xml.XmlReader" /> o <see cref="T:System.Xml.XmlWriter" />, l'oggetto esterno non esegue altri controlli di conformità.Il controllo di conformità viene eseguito fino al livello dell'oggetto sottostante.Per informazioni su come viene determinato il livello di conformità, vedere le proprietà <see cref="P:System.Xml.XmlReaderSettings.ConformanceLevel" /> e <see cref="P:System.Xml.XmlWriterSettings.ConformanceLevel" />.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Document">
      <summary>I dati XML sono conformi alle regole per un documento XML 1.0 ben formato, in base alla definizione di W3C.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Fragment">
      <summary>I dati XML sono un frammento XML ben formato, in base alla definizione di W3C.</summary>
    </member>
    <member name="T:System.Xml.DtdProcessing">
      <summary>Specifica le opzioni per l'elaborazione dei DTD.L'enumerazione <see cref="T:System.Xml.DtdProcessing" /> viene utilizzata dalla classe <see cref="T:System.Xml.XmlReaderSettings" />.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Ignore">
      <summary>Fa in modo che venga ignorato l'elemento DOCTYPE.L'operazione di elaborazione dei DTD non ha luogo.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Prohibit">
      <summary>Specifica che quando viene rilevato un DTD, viene generato un oggetto <see cref="T:System.Xml.XmlException" /> con un messaggio indicante che i DTD non sono consentiti.Questo è il comportamento predefinito.</summary>
    </member>
    <member name="T:System.Xml.IXmlLineInfo">
      <summary>Fornisce un'interfaccia che consente ad una classe di restituire informazioni sulla riga e sulla posizione.</summary>
    </member>
    <member name="M:System.Xml.IXmlLineInfo.HasLineInfo">
      <summary>Ottiene un valore che indica se la classe può restituire informazioni sulla riga.</summary>
      <returns>true se è possibile specificare la <see cref="P:System.Xml.IXmlLineInfo.LineNumber" /> e <see cref="P:System.Xml.IXmlLineInfo.LinePosition" />; in caso contrario false.</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LineNumber">
      <summary>Ottiene il numero corrente di riga.</summary>
      <returns>Numero della riga corrente o 0 se non sono disponibili informazioni sulla riga: <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" />, ad esempio, restituisce false.</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LinePosition">
      <summary>Ottiene la posizione corrente di riga.</summary>
      <returns>Posizione della riga corrente o 0 se non sono disponibili informazioni sulla riga: <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" />, ad esempio, restituisce false.</returns>
    </member>
    <member name="T:System.Xml.IXmlNamespaceResolver">
      <summary>Fornisce l'accesso in sola lettura a un set di mapping di prefissi e spazi dei nomi.</summary>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Ottiene una raccolta di mapping definiti di prefissi-spazi dei nomi attualmente inclusi nell'ambito.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> contenente tutti gli spazi dei nomi attualmente inclusi nell'ambito.</returns>
      <param name="scope">Valore di <see cref="T:System.Xml.XmlNamespaceScope" /> che specifica il tipo di nodi spazio dei nomi da restituire.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupNamespace(System.String)">
      <summary>Ottiene l'URI dello spazio dei nomi mappato al prefisso specificato.</summary>
      <returns>L'URI dello spazio dei nomi mappato al prefisso; null se il prefisso non è mappato all'URI dello spazio dei nomi.</returns>
      <param name="prefix">Prefisso del quale si desidera individuare l'URI dello spazio dei nomi.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupPrefix(System.String)">
      <summary>Ottiene il prefisso mappato all'URI dello spazio dei nomi specificato.</summary>
      <returns>Il prefisso mappato all'URI dello spazio dei nomi; null se l'URI dello spazio dei nomi non è mappato al prefisso.</returns>
      <param name="namespaceName">URI dello spazio dei nomi di cui si desidera individuare il prefisso.</param>
    </member>
    <member name="T:System.Xml.NamespaceHandling">
      <summary>Specifica se rimuovere le dichiarazioni di spazio dei nomi duplicate nell'oggetto <see cref="T:System.Xml.XmlWriter" />. </summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.Default">
      <summary>Specifica che le dichiarazioni dello spazio dei nomi duplicate non verranno rimosse.</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.OmitDuplicates">
      <summary>Specifica che le dichiarazioni dello spazio dei nomi duplicate verranno rimosse.Affinché lo spazio dei nomi duplicato venga rimosso, il prefisso e lo spazio dei nomi devono corrispondere.</summary>
    </member>
    <member name="T:System.Xml.NameTable">
      <summary>Implementa una classe <see cref="T:System.Xml.XmlNameTable" /> a thread singolo.</summary>
    </member>
    <member name="M:System.Xml.NameTable.#ctor">
      <summary>Inizializza una nuova istanza della classe NameTable.</summary>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>Suddivide in elementi di base la stringa specificata e la aggiunge alla classe NameTable.</summary>
      <returns>Stringa suddivisa in elementi di base o stringa esistente se già presente nella classe NameTable.Se <paramref name="len" /> è zero, verrà restituito il valore String.Empty.</returns>
      <param name="key">Matrice di caratteri contenente la stringa da aggiungere. </param>
      <param name="start">Indice in base zero nella matrice che specifica il primo carattere della stringa. </param>
      <param name="len">Numero di caratteri nella stringa. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />- oppure - <paramref name="start" /> &gt;= <paramref name="key" />.Length - oppure - <paramref name="len" /> &gt;= <paramref name="key" />.Length Queste condizioni non provocano la generazione di un'eccezione se <paramref name="len" /> = 0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.String)">
      <summary>Suddivide in elementi di base la stringa specificata e la aggiunge alla classe NameTable.</summary>
      <returns>Stringa suddivisa in elementi di base o stringa esistente se già presente nella classe NameTable.</returns>
      <param name="key">Stringa da aggiungere. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>Ottiene la stringa suddivisa in elementi di base che contiene gli stessi caratteri dell'intervallo di caratteri specificato nella matrice indicata.</summary>
      <returns>Stringa suddivisa in elementi di base o null se la stringa non è già stata suddivisa.Se <paramref name="len" /> è zero, verrà restituito il valore String.Empty.</returns>
      <param name="key">Matrice di caratteri contenente il nome da trovare. </param>
      <param name="start">Indice in base zero nella matrice che specifica il primo carattere del nome. </param>
      <param name="len">Numero di caratteri nel nome. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />- oppure - <paramref name="start" /> &gt;= <paramref name="key" />.Length - oppure - <paramref name="len" /> &gt;= <paramref name="key" />.Length Queste condizioni non provocano la generazione di un'eccezione se <paramref name="len" /> = 0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.String)">
      <summary>Ottiene la stringa suddivisa in elementi di base con il valore specificato.</summary>
      <returns>Oggetto della stringa suddivisa in elementi di base o null se la stringa non è stata ancora suddivisa.</returns>
      <param name="value">Nome da trovare. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> è null. </exception>
    </member>
    <member name="T:System.Xml.NewLineHandling">
      <summary>Specifica in che modo gestire le interruzioni di riga.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Entitize">
      <summary>I caratteri della nuova riga diventano entità.Questa impostazione mantiene tutti i caratteri quando l'output viene letto da una classe <see cref="T:System.Xml.XmlReader" /> di normalizzazione.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.None">
      <summary>I caratteri della nuova riga restano invariati.L'output è uguale all'input.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Replace">
      <summary>I caratteri della nuova riga vengono sostituiti in modo che corrispondano al carattere specificato nella proprietà <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" />.</summary>
    </member>
    <member name="T:System.Xml.ReadState">
      <summary>Specifica lo stato del lettore.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Closed">
      <summary>È stato chiamato il metodo <see cref="M:System.Xml.XmlReader.Close" />.</summary>
    </member>
    <member name="F:System.Xml.ReadState.EndOfFile">
      <summary>È stata raggiunta correttamente la fine del file.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Error">
      <summary>Si è verificato un errore che non consente di continuare l'operazione di lettura.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Initial">
      <summary>Il metodo Read non è stato chiamato.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Interactive">
      <summary>È stato chiamato il metodo Read.È possibile chiamare altri metodi sul lettore.</summary>
    </member>
    <member name="T:System.Xml.WriteState">
      <summary>Specifica lo stato della classe <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Attribute">
      <summary>Indica che viene scritto il valore di un attributo.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Closed">
      <summary>Indica che il metodo <see cref="M:System.Xml.XmlWriter.Close" /> è già stato chiamato.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Content">
      <summary>Indica che viene scritto il contenuto dell'elemento.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Element">
      <summary>Indica che viene scritto il tag di inizio di un elemento.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Error">
      <summary>È stata generata un'eccezione che ha lasciato la classe <see cref="T:System.Xml.XmlWriter" /> in uno stato non valido.È possibile chiamare il metodo <see cref="M:System.Xml.XmlWriter.Close" /> per porre <see cref="T:System.Xml.XmlWriter" /> in stato <see cref="F:System.Xml.WriteState.Closed" />.Qualsiasi altra chiamata al metodo <see cref="T:System.Xml.XmlWriter" /> ha come conseguenza un'eccezione <see cref="T:System.InvalidOperationException" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Prolog">
      <summary>Indica che viene scritto il prologo.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Start">
      <summary>Indica che un metodo Write non è ancora stato chiamato.</summary>
    </member>
    <member name="T:System.Xml.XmlConvert">
      <summary>Codifica e decodifica i nomi XML e fornisce metodi per la conversione tra tipi Common Language Runtime e tipi XSD (XML Schema Definition Language).Quando si convertono i tipi di dati, i valori restituiti sono indipendenti dalle impostazioni locali.</summary>
    </member>
    <member name="M:System.Xml.XmlConvert.DecodeName(System.String)">
      <summary>Decodifica un nome.Questo metodo produce effetti opposti rispetto ai metodi <see cref="M:System.Xml.XmlConvert.EncodeName(System.String)" /> e <see cref="M:System.Xml.XmlConvert.EncodeLocalName(System.String)" />.</summary>
      <returns>Nome decodificato.</returns>
      <param name="name">Nome da trasformare. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeLocalName(System.String)">
      <summary>Converte il nome in un nome XML locale valido.</summary>
      <returns>Nome codificato.</returns>
      <param name="name">Nome da codificare. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeName(System.String)">
      <summary>Converte il nome in un nome XML valido.</summary>
      <returns>Restituisce il nome con gli eventuali caratteri non validi sostituiti da una stringa di escape.</returns>
      <param name="name">Nome da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeNmToken(System.String)">
      <summary>Verifica che il nome sia valido secondo le specifiche XML.</summary>
      <returns>Nome codificato.</returns>
      <param name="name">Nome da codificare. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToBoolean(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.Boolean" /> equivalente.</summary>
      <returns>Valore Boolean, ossia true o false.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> does not represent a Boolean value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToByte(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.Byte" /> equivalente.</summary>
      <returns>Equivalente Byte della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToChar(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.Char" /> equivalente.</summary>
      <returns>Oggetto Char che rappresenta il carattere singolo.</returns>
      <param name="s">Stringa contenente un singolo carattere da convertire. </param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="s" /> parameter is null. </exception>
      <exception cref="T:System.FormatException">The <paramref name="s" /> parameter contains more than one character. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTime(System.String,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Converte <see cref="T:System.String" /> in un oggetto <see cref="T:System.DateTime" /> usando l'oggetto <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> specificato.</summary>
      <returns>Equivalente <see cref="T:System.DateTime" /> di <see cref="T:System.String" />.</returns>
      <param name="s">Valore <see cref="T:System.String" /> da convertire.</param>
      <param name="dateTimeOption">Uno dei valori di <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> che specifica se la data deve essere convertita nell'ora locale o mantenuta nel formato UTC (Coordinated Universal Time), se si tratta di una data UTC.</param>
      <exception cref="T:System.NullReferenceException">
        <paramref name="s" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="dateTimeOption" /> value is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is an empty string or is not in a valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> fornito in un oggetto <see cref="T:System.DateTimeOffset" /> equivalente.</summary>
      <returns>Equivalente <see cref="T:System.DateTimeOffset" /> della stringa specificata.</returns>
      <param name="s">Stringa da convertire.Nota   La stringa deve essere conforme a un sottoinsieme della raccomandazione W3C per il tipo XML dateTime.Per altre informazioni, vedere http://www.w3.org/TR/xmlschema-2/#dateTime.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The argument passed to this method is outside the range of allowable values.For information about allowable values, see <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.FormatException">The argument passed to this method does not conform to a subset of the W3C Recommendations for the XML dateTime type.For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> fornito in un oggetto <see cref="T:System.DateTimeOffset" /> equivalente.</summary>
      <returns>Equivalente <see cref="T:System.DateTimeOffset" /> della stringa specificata.</returns>
      <param name="s">Stringa da convertire.</param>
      <param name="format">Formato da cui viene convertito <paramref name="s" />.Il parametro del formato può essere qualsiasi sottoinsieme della raccomandazione W3C per il tipo XML dateTime.Per altre informazioni, vedere http://www.w3.org/TR/xmlschema-2/#dateTime. La stringa <paramref name="s" /> viene convalidata sulla base di questo formato.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> or <paramref name="format" /> is an empty string or is not in the specified format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String[])">
      <summary>Converte l'oggetto <see cref="T:System.String" /> fornito in un oggetto <see cref="T:System.DateTimeOffset" /> equivalente.</summary>
      <returns>Equivalente <see cref="T:System.DateTimeOffset" /> della stringa specificata.</returns>
      <param name="s">Stringa da convertire.</param>
      <param name="formats">Matrice di formati dalla quale è possibile convertire <paramref name="s" />.Ogni formato in <paramref name="formats" /> può essere qualsiasi sottoinsieme della raccomandazione W3C per il tipo XML dateTime.Per altre informazioni, vedere http://www.w3.org/TR/xmlschema-2/#dateTime. La stringa <paramref name="s" /> viene convalidata sulla base di uno di questi formati.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDecimal(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.Decimal" /> equivalente.</summary>
      <returns>Equivalente Decimal della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDouble(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.Double" /> equivalente.</summary>
      <returns>Equivalente Double della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToGuid(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.Guid" /> equivalente.</summary>
      <returns>Equivalente Guid della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt16(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.Int16" /> equivalente.</summary>
      <returns>Equivalente Int16 della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt32(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.Int32" /> equivalente.</summary>
      <returns>Equivalente Int32 della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt64(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.Int64" /> equivalente.</summary>
      <returns>Equivalente Int64 della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSByte(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.SByte" /> equivalente.</summary>
      <returns>Equivalente SByte della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSingle(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.Single" /> equivalente.</summary>
      <returns>Equivalente Single della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Boolean)">
      <summary>Converte <see cref="T:System.Boolean" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa del valore Boolean, ossia "true" o "false".</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Byte)">
      <summary>Converte <see cref="T:System.Byte" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di Byte.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Char)">
      <summary>Converte <see cref="T:System.Char" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di Char.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTime,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Converte <see cref="T:System.DateTime" /> in un oggetto <see cref="T:System.String" /> usando l'oggetto <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> specificato.</summary>
      <returns>Equivalente <see cref="T:System.String" /> di <see cref="T:System.DateTime" />.</returns>
      <param name="value">Valore <see cref="T:System.DateTime" /> da convertire.</param>
      <param name="dateTimeOption">Uno dei valori di <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> che specifica la modalità di gestione del valore <see cref="T:System.DateTime" />.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="dateTimeOption" /> value is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> or <paramref name="dateTimeOption" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset)">
      <summary>Converte l'oggetto <see cref="T:System.DateTimeOffset" /> fornito in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione <see cref="T:System.String" /> dell'oggetto <see cref="T:System.DateTimeOffset" /> specificato.</returns>
      <param name="value">Elemento <see cref="T:System.DateTimeOffset" /> da convertire.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset,System.String)">
      <summary>Converte l'oggetto <see cref="T:System.DateTimeOffset" /> fornito in un oggetto <see cref="T:System.String" /> nel formato specificato.</summary>
      <returns>Rappresentazione <see cref="T:System.String" /> nel formato specificato dell'oggetto <see cref="T:System.DateTimeOffset" /> fornito.</returns>
      <param name="value">Elemento <see cref="T:System.DateTimeOffset" /> da convertire.</param>
      <param name="format">Formato in cui viene convertito <paramref name="s" />.Il parametro del formato può essere qualsiasi sottoinsieme della raccomandazione W3C per il tipo XML dateTime.Per altre informazioni, vedere http://www.w3.org/TR/xmlschema-2/#dateTime.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Decimal)">
      <summary>Converte <see cref="T:System.Decimal" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di Decimal.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Double)">
      <summary>Converte <see cref="T:System.Double" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di Double.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Guid)">
      <summary>Converte <see cref="T:System.Guid" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di Guid.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int16)">
      <summary>Converte <see cref="T:System.Int16" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di Int16.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int32)">
      <summary>Converte <see cref="T:System.Int32" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di Int32.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int64)">
      <summary>Converte <see cref="T:System.Int64" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di Int64.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.SByte)">
      <summary>Converte <see cref="T:System.SByte" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di SByte.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Single)">
      <summary>Converte <see cref="T:System.Single" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di Single.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.TimeSpan)">
      <summary>Converte <see cref="T:System.TimeSpan" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di TimeSpan.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt16)">
      <summary>Converte <see cref="T:System.UInt16" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di UInt16.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt32)">
      <summary>Converte <see cref="T:System.UInt32" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di UInt32.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt64)">
      <summary>Converte <see cref="T:System.UInt64" /> in un oggetto <see cref="T:System.String" />.</summary>
      <returns>Rappresentazione di stringa di UInt64.</returns>
      <param name="value">Valore da convertire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToTimeSpan(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.TimeSpan" /> equivalente.</summary>
      <returns>Equivalente TimeSpan della stringa.</returns>
      <param name="s">Stringa da convertire.Il formato della stringa deve essere conforme alla raccomandazione W3C XML Schema Part 2: Datatypes per la durata.</param>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in correct format to represent a TimeSpan value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt16(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.UInt16" /> equivalente.</summary>
      <returns>Equivalente UInt16 della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt32(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.UInt32" /> equivalente.</summary>
      <returns>Equivalente UInt32 della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt64(System.String)">
      <summary>Converte l'oggetto <see cref="T:System.String" /> in un oggetto <see cref="T:System.UInt64" /> equivalente.</summary>
      <returns>Equivalente UInt64 della stringa.</returns>
      <param name="s">Stringa da convertire. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyName(System.String)">
      <summary>Verifica che il nome sia valido in base alle specifiche del linguaggio XML (Extended Markup Language) di W3C.</summary>
      <returns>Nome, se è un nome XML valido.</returns>
      <param name="name">Nome da verificare. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid XML name. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNCName(System.String)">
      <summary>Verifica che il nome sia un NCName valido in base alle specifiche del linguaggio XML (Extended Markup Language) di W3C.Un NCName è un nome che non può contenere i due punti (:).</summary>
      <returns>Nome, se è un nome NCName valido.</returns>
      <param name="name">Nome da verificare. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid non-colon name. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNMTOKEN(System.String)">
      <summary>Verifica che la stringa sia un tipo NMTOKEN valido in base alla raccomandazione W3C XML Schema Part2: Datatypes.</summary>
      <returns>Token del nome, se è un NMTOKEN valido.</returns>
      <param name="name">Stringa da verificare.</param>
      <exception cref="T:System.Xml.XmlException">The string is not a valid name token.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyPublicId(System.String)">
      <summary>Restituisce l'istanza di stringa passata se tutti i caratteri nell'argomento di tipo stringa sono caratteri dell'ID pubblico validi.</summary>
      <returns>Restituisce la stringa passata se tutti i caratteri nell'argomento sono caratteri dell'ID pubblico validi.</returns>
      <param name="publicId">
        <see cref="T:System.String" /> che contiene l'ID da convalidare.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyWhitespace(System.String)">
      <summary>Restituisce l'istanza di stringa passata se tutti i caratteri nell'argomento di stringa sono spazi vuoti validi. </summary>
      <returns>Restituisce l'istanza di stringa passata se tutti i caratteri nell'argomento di stringa sono spazi vuoti validi; in caso contrario null.</returns>
      <param name="content">
        <see cref="T:System.String" /> da verificare.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyXmlChars(System.String)">
      <summary>Restituisce la stringa passata se tutti i caratteri e i caratteri delle coppie di surrogati nell'argomento stringa sono caratteri XML validi, in caso contrario viene generata un'eccezione XmlException con le informazioni relative al primo carattere non valido rilevato. </summary>
      <returns>Restituisce la stringa passata se tutti i caratteri e i caratteri delle coppie di surrogati nell'argomento stringa sono caratteri XML validi, in caso contrario viene generata un'eccezione XmlException con le informazioni relative al primo carattere non valido rilevato.</returns>
      <param name="content">
        <see cref="T:System.String" /> che contiene i caratteri da verificare.</param>
    </member>
    <member name="T:System.Xml.XmlDateTimeSerializationMode">
      <summary>Specifica in che modo deve essere considerato il valore dell'ora nelle conversioni tra una stringa e <see cref="T:System.DateTime" />.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Local">
      <summary>Viene considerato come ora locale.Se l'oggetto <see cref="T:System.DateTime" /> rappresenta un'ora UTC (Coordinated Universal Time), viene convertito nell'ora locale.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.RoundtripKind">
      <summary>Durante la conversione devono essere mantenute le informazioni sul fuso orario.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Unspecified">
      <summary>Viene considerato come ora locale se <see cref="T:System.DateTime" /> viene convertito in una stringa.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Utc">
      <summary>Viene considerato come UTC.Se l'oggetto <see cref="T:System.DateTime" /> rappresenta un'ora locale, viene convertito in un valore UTC.</summary>
    </member>
    <member name="T:System.Xml.XmlException">
      <summary>Restituisce informazioni dettagliate sull'ultima eccezione.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor">
      <summary>Inizializza una nuova istanza della classe XmlException.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String)">
      <summary>Consente l'inizializzazione di una nuova istanza della classe XmlException con un messaggio di errore specificato.</summary>
      <param name="message">Descrizione dell'errore. </param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe XmlException.</summary>
      <param name="message">Descrizione della condizione di errore. </param>
      <param name="innerException">
        <see cref="T:System.Exception" /> che ha generato l'eccezione XmlException, se presente.Il valore può essere null.</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception,System.Int32,System.Int32)">
      <summary>Inizializza una nuova istanza della classe XmlException con l'eccezione interna, il numero di riga, la posizione nella riga e il messaggio specificati.</summary>
      <param name="message">Descrizione dell'errore. </param>
      <param name="innerException">Eccezione causa dell'eccezione corrente.Il valore può essere null.</param>
      <param name="lineNumber">Numero di riga che indica dove si è verificato l'errore. </param>
      <param name="linePosition">Posizione che indica in che punto della riga si è verificato l'errore. </param>
    </member>
    <member name="P:System.Xml.XmlException.LineNumber">
      <summary>Ottiene il numero di riga che indica dove si è verificato l'errore.</summary>
      <returns>Numero di riga che indica dove si è verificato l'errore.</returns>
    </member>
    <member name="P:System.Xml.XmlException.LinePosition">
      <summary>Ottiene la posizione di riga in cui si è verificato l'errore.</summary>
      <returns>Posizione che indica in che punto della riga si è verificato l'errore.</returns>
    </member>
    <member name="P:System.Xml.XmlException.Message">
      <summary>Ottiene un messaggio in cui viene descritta l'eccezione corrente.</summary>
      <returns>Messaggio di errore nel quale viene indicato il motivo dell’eccezione</returns>
    </member>
    <member name="T:System.Xml.XmlNamespaceManager">
      <summary>Risolve, aggiunge e rimuove spazi dei nomi in una raccolta e ne consente la gestione dell'ambito. </summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.#ctor(System.Xml.XmlNameTable)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Xml.XmlNamespaceManager" /> con l'oggetto <see cref="T:System.Xml.XmlNameTable" /> specificato.</summary>
      <param name="nameTable">Oggetto <see cref="T:System.Xml.XmlNameTable" /> da usare. </param>
      <exception cref="T:System.NullReferenceException">null is passed to the constructor </exception>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.AddNamespace(System.String,System.String)">
      <summary>Aggiunge alla raccolta lo spazio dei nomi specificato.</summary>
      <param name="prefix">Prefisso da associare allo spazio dei nomi aggiunto.Usare String.Empty per aggiungere uno spazio dei nomi predefinito.NotaSe si usa l'oggetto <see cref="T:System.Xml.XmlNamespaceManager" /> per risolvere gli spazi dei nomi in un'espressione XML Path Language (XPath), è necessario specificare un prefisso.Se in un'espressione XPath non è incluso un prefisso, si presuppone che l'URI (Uniform Resource Identifier) dello spazio dei nomi sia lo spazio dei nomi vuoto.Per altre informazioni sulle espressioni XPath e su <see cref="T:System.Xml.XmlNamespaceManager" />, fare riferimento ai metodi <see cref="M:System.Xml.XmlNode.SelectNodes(System.String)" /> e <see cref="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.XmlNamespaceManager)" />.</param>
      <param name="uri">Spazio dei nomi da aggiungere. </param>
      <exception cref="T:System.ArgumentException">The value for <paramref name="prefix" /> is "xml" or "xmlns". </exception>
      <exception cref="T:System.ArgumentNullException">The value for <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.DefaultNamespace">
      <summary>Ottiene l'URI dello spazio dei nomi per lo spazio dei nomi predefinito.</summary>
      <returns>Restituisce l'URI dello spazio dei nomi per lo spazio dei nomi predefinito o String.Empty se non è presente uno spazio dei nomi predefinito.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetEnumerator">
      <summary>Restituisce un enumeratore usato per scorrere gli spazi dei nomi nell'oggetto <see cref="T:System.Xml.XmlNamespaceManager" />.</summary>
      <returns>Oggetto <see cref="T:System.Collections.IEnumerator" /> contenente i prefissi archiviati da <see cref="T:System.Xml.XmlNamespaceManager" />.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Ottiene una raccolta di nomi di spazi dei nomi con chiave in base al prefisso, che può essere usata per enumerare gli spazi dei nomi attualmente nell'ambito.</summary>
      <returns>Raccolta delle coppie di spazio dei nomi e prefisso attualmente nell'ambito.</returns>
      <param name="scope">Valore di enumerazione che specifica il tipo di nodi spazio dei nomi da restituire.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.HasNamespace(System.String)">
      <summary>Ottiene un valore che indica se il prefisso fornito dispone di uno spazio dei nomi definito per l'ambito inserito attualmente.</summary>
      <returns>true se è presente uno spazio dei nomi definito; in caso contrario, false.</returns>
      <param name="prefix">Prefisso dello spazio dei nomi da trovare. </param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupNamespace(System.String)">
      <summary>Ottiene l'URI dello spazio dei nomi per il prefisso specificato.</summary>
      <returns>Restituisce l'URI dello spazio dei nomi per <paramref name="prefix" /> o null se non è disponibile uno spazio dei nomi mappato.La stringa restituita è atomizzata.Per altre informazioni sulle stringhe atomizzate, vedere la classe <see cref="T:System.Xml.XmlNameTable" />.</returns>
      <param name="prefix">Prefisso di cui risolvere l'URI dello spazio dei nomi.Per trovare la corrispondenza con lo spazio dei nomi predefinito, passare String.Empty.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupPrefix(System.String)">
      <summary>Trova il prefisso dichiarato per l'URI dello spazio dei nomi specificato.</summary>
      <returns>Prefisso corrispondente.Se non è presente un prefisso mappato, il metodo restituisce String.Empty. Se viene specificato un valore Null, viene restituito null.</returns>
      <param name="uri">Spazio dei nomi da risolvere per il prefisso. </param>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.NameTable">
      <summary>Ottiene l'oggetto <see cref="T:System.Xml.XmlNameTable" /> associato a questo oggetto.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlNameTable" /> usato da questo oggetto.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PopScope">
      <summary>Estrae un ambito dello spazio dei nomi dallo stack.</summary>
      <returns>true se sono rimasti ambiti dello spazio dei nomi nello stack; false se non sono più disponibili spazi dei nomi da prelevare.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PushScope">
      <summary>Inserisce un ambito dello spazio dei nomi nello stack.</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.RemoveNamespace(System.String,System.String)">
      <summary>Rimuove lo spazio dei nomi specificato per il prefisso specificato.</summary>
      <param name="prefix">Prefisso per lo spazio dei nomi </param>
      <param name="uri">Spazio dei nomi da rimuovere per il prefisso specificato.Lo spazio dei nomi rimosso deriva dall'ambito dello spazio dei nomi corrente.Gli spazi dei nomi non compresi nell'ambito corrente vengono ignorati.</param>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="T:System.Xml.XmlNamespaceScope">
      <summary>Definisce l'ambito dello spazio dei nomi.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.All">
      <summary>tutti gli spazi dei nomi definiti nell'ambito del nodo corrente,compreso lo spazio dei nomi xmlns:xml, che viene sempre dichiarato in modo implicito.L'ordine degli spazi dei nomi restituiti non è definito.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.ExcludeXml">
      <summary>tutti gli spazi dei nomi definiti nell'ambito del nodo corrente, escluso lo spazio dei nomi xmlns:xml, che viene sempre dichiarato in modo implicito.L'ordine degli spazi dei nomi restituiti non è definito.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.Local">
      <summary>tutti gli spazi dei nomi definiti localmente nel nodo corrente.</summary>
    </member>
    <member name="T:System.Xml.XmlNameTable">
      <summary>Tabella degli oggetti stringa suddivisi in elementi di base.</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Xml.XmlNameTable" />. </summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>Quando sottoposto a override in una classe derivata, suddivide in elementi di base la stringa specificata e la aggiunge alla tabella XmlNameTable.</summary>
      <returns>Nuova stringa suddivisa in elementi di base o stringa disponibile, se già presente.Se la lunghezza equivale a zero, verrà restituito String.Empty.</returns>
      <param name="array">Matrice di caratteri contenente il nome da aggiungere. </param>
      <param name="offset">Indice in base zero nella matrice che specifica il primo carattere del nome. </param>
      <param name="length">Numero di caratteri nel nome. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />- oppure - <paramref name="offset" /> &gt;= <paramref name="array" />.Length - oppure - <paramref name="length" /> &gt; <paramref name="array" />.Length Queste condizioni non provocano la generazione di un'eccezione se <paramref name="length" /> = 0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.String)">
      <summary>Quando sottoposto a override in una classe derivata, suddivide in elementi di base la stringa specificata e la aggiunge alla tabella XmlNameTable.</summary>
      <returns>Nuova stringa suddivisa in elementi di base o stringa disponibile, se già presente.</returns>
      <param name="array">Nome da aggiungere. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>Quando sottoposto a override in una classe derivata, ottiene la stringa suddivisa in elementi di base contenente gli stessi caratteri dell'intervallo di caratteri specificato nella matrice indicata.</summary>
      <returns>Stringa suddivisa in elementi di base o null se la stringa non è già stata suddivisa.Se <paramref name="length" /> è zero, verrà restituito il valore String.Empty.</returns>
      <param name="array">Matrice di caratteri contenente il nome da cercare. </param>
      <param name="offset">Indice in base zero nella matrice che specifica il primo carattere del nome. </param>
      <param name="length">Numero di caratteri nel nome. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />- oppure - <paramref name="offset" /> &gt;= <paramref name="array" />.Length - oppure - <paramref name="length" /> &gt; <paramref name="array" />.Length Queste condizioni non provocano la generazione di un'eccezione se <paramref name="length" /> = 0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.String)">
      <summary>Quando sottoposto a override in una classe derivata, ottiene la stringa suddivisa in elementi di base contenente lo stesso valore della stringa specificata.</summary>
      <returns>Stringa suddivisa in elementi di base o null se la stringa non è già stata suddivisa.</returns>
      <param name="array">Nome da cercare. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null. </exception>
    </member>
    <member name="T:System.Xml.XmlNodeType">
      <summary>Specifica il tipo di nodo.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Attribute">
      <summary>Attributo (ad esempio, id='123' ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.CDATA">
      <summary>Sezione CDATA (ad esempio, &lt;![CDATA[my escaped text]]&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Comment">
      <summary>Commento (ad esempio, &lt;!-- my comment --&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Document">
      <summary>Oggetto documento che, come radice della struttura ad albero del documento, fornisce accesso all'intero documento XML.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentFragment">
      <summary>Frammento di documento.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentType">
      <summary>Dichiarazione del tipo di documento, indicata dal tag seguente (ad esempio, &lt;!DOCTYPE...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Element">
      <summary>Elemento (ad esempio, &lt;item&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndElement">
      <summary>Tag di fine dell'elemento (ad esempio, &lt;/item&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndEntity">
      <summary>Viene restituito quando XmlReader completa l'analisi del testo sostitutivo dell'entità in seguito a una chiamata al metodo <see cref="M:System.Xml.XmlReader.ResolveEntity" />.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Entity">
      <summary>Dichiarazione di entità (ad esempio, &lt;!ENTITY...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EntityReference">
      <summary>Riferimento a un'entità (ad esempio, &amp;num;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.None">
      <summary>Viene restituito dall'oggetto <see cref="T:System.Xml.XmlReader" /> se non è stato chiamato un metodo Read.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Notation">
      <summary>Notazione nella dichiarazione del tipo del documento (ad esempio &lt;!NOTATION...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.ProcessingInstruction">
      <summary>Istruzione di elaborazione (ad esempio, &lt;?pi test?&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.SignificantWhitespace">
      <summary>Spazio vuoto all'interno di markup in un modello a contenuto misto oppure spazio vuoto all'interno dell'ambito xml:space="preserve".</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Text">
      <summary>Contenuto di un nodo.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Whitespace">
      <summary>Spazio vuoto all'interno di markup.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.XmlDeclaration">
      <summary>Dichiarazione XML (ad esempio, &lt;?xml version='1.0'?&gt;).</summary>
    </member>
    <member name="T:System.Xml.XmlParserContext">
      <summary>Fornisce tutte le informazioni sul contesto richieste dalla classe <see cref="T:System.Xml.XmlReader" /> per analizzare un frammento XML.</summary>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace)">
      <summary>Inizializza una nuova istanza della classe XmlParserContext con l'oggetto <see cref="T:System.Xml.XmlNameTable" />, l'oggetto <see cref="T:System.Xml.XmlNamespaceManager" /> , l'URI di base, l'xml:lang, l'xml:space e il tipo di documento specificati.</summary>
      <param name="nt">Oggetto <see cref="T:System.Xml.XmlNameTable" /> da utilizzare per suddividere le stringhe in elementi di base.Se il valore di questo oggetto è null, verrà utilizzata la tabella dei nomi impiegata per creare <paramref name="nsMgr" />.Per ulteriori informazioni sulle stringhe suddivise in elementi di base, vedere <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Oggetto <see cref="T:System.Xml.XmlNamespaceManager" /> da utilizzare per cercare informazioni sugli spazi dei nomi oppure null. </param>
      <param name="docTypeName">Nome della dichiarazione del tipo di documento. </param>
      <param name="pubId">Identificatore pubblico. </param>
      <param name="sysId">Identificatore di sistema. </param>
      <param name="internalSubset">Sottoinsieme DTD interno.Il sottoinsieme DTD viene utilizzato per la risoluzione dell'entità, non per la convalida del documento.</param>
      <param name="baseURI">URI di base per il frammento XML, ovvero percorso da cui è stato caricato il frammento. </param>
      <param name="xmlLang">Ambito xml:lang. </param>
      <param name="xmlSpace">Valore di <see cref="T:System.Xml.XmlSpace" /> che indica l'ambito xml:space. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> non è lo stesso XmlNameTable utilizzato per la costruzione di <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Inizializza una nuova istanza della classe XmlParserContext con l'oggetto <see cref="T:System.Xml.XmlNameTable" />, l'oggetto <see cref="T:System.Xml.XmlNamespaceManager" />, l'URI di base, l'xml:lang, l'xml:space e il tipo di documento specificati.</summary>
      <param name="nt">Oggetto <see cref="T:System.Xml.XmlNameTable" /> da utilizzare per suddividere le stringhe in elementi di base.Se il valore di questo oggetto è null, verrà utilizzata la tabella dei nomi impiegata per creare <paramref name="nsMgr" />.Per ulteriori informazioni sulle stringhe suddivise in elementi di base, vedere <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Oggetto <see cref="T:System.Xml.XmlNamespaceManager" /> da utilizzare per cercare informazioni sugli spazi dei nomi oppure null. </param>
      <param name="docTypeName">Nome della dichiarazione del tipo di documento. </param>
      <param name="pubId">Identificatore pubblico. </param>
      <param name="sysId">Identificatore di sistema. </param>
      <param name="internalSubset">Sottoinsieme DTD interno.La definizione DTD viene utilizzata per la risoluzione dell'entità, non per la convalida del documento.</param>
      <param name="baseURI">URI di base per il frammento XML, ovvero percorso da cui è stato caricato il frammento. </param>
      <param name="xmlLang">Ambito xml:lang. </param>
      <param name="xmlSpace">Valore di <see cref="T:System.Xml.XmlSpace" /> che indica l'ambito xml:space. </param>
      <param name="enc">Oggetto <see cref="T:System.Text.Encoding" /> che indica l'impostazione della codifica. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> non è lo stesso XmlNameTable utilizzato per la costruzione di <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace)">
      <summary>Inizializza una nuova istanza della classe XmlParserContext con i valori di <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang e xml:space specificati.</summary>
      <param name="nt">Oggetto <see cref="T:System.Xml.XmlNameTable" /> da utilizzare per suddividere le stringhe in elementi di base.Se il valore di questo oggetto è null, verrà utilizzata la tabella dei nomi impiegata per creare <paramref name="nsMgr" />.Per ulteriori informazioni sulle stringhe suddivise in elementi di base, vedere <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Oggetto <see cref="T:System.Xml.XmlNamespaceManager" /> da utilizzare per cercare informazioni sugli spazi dei nomi oppure null. </param>
      <param name="xmlLang">Ambito xml:lang. </param>
      <param name="xmlSpace">Valore di <see cref="T:System.Xml.XmlSpace" /> che indica l'ambito xml:space. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> non è lo stesso XmlNameTable utilizzato per la costruzione di <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Inizializza una nuova istanza della classe XmlParserContext con la codifica, l'oggetto <see cref="T:System.Xml.XmlNameTable" />, l'oggetto <see cref="T:System.Xml.XmlNamespaceManager" />, l'xml:lang e l'xml:space specificati.</summary>
      <param name="nt">Oggetto <see cref="T:System.Xml.XmlNameTable" /> da utilizzare per suddividere le stringhe in elementi di base.Se il valore di questo oggetto è null, verrà utilizzata la tabella dei nomi impiegata per creare <paramref name="nsMgr" />.Per ulteriori informazioni sulle stringhe suddivise in elementi di base, vedere <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Oggetto <see cref="T:System.Xml.XmlNamespaceManager" /> da utilizzare per cercare informazioni sugli spazi dei nomi oppure null. </param>
      <param name="xmlLang">Ambito xml:lang. </param>
      <param name="xmlSpace">Valore di <see cref="T:System.Xml.XmlSpace" /> che indica l'ambito xml:space. </param>
      <param name="enc">Oggetto <see cref="T:System.Text.Encoding" /> che indica l'impostazione della codifica. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> non è lo stesso XmlNameTable utilizzato per la costruzione di <paramref name="nsMgr" />. </exception>
    </member>
    <member name="P:System.Xml.XmlParserContext.BaseURI">
      <summary>Ottiene o imposta l'URI di base.</summary>
      <returns>URI di base da utilizzare per risolvere il file DTD.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.DocTypeName">
      <summary>Ottiene o imposta il nome della dichiarazione del tipo di documento.</summary>
      <returns>Nome della dichiarazione del tipo di documento.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.Encoding">
      <summary>Ottiene o imposta il tipo di codifica.</summary>
      <returns>Oggetto <see cref="T:System.Text.Encoding" /> che indica il tipo di codifica.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.InternalSubset">
      <summary>Ottiene o imposta il sottoinsieme DTD interno.</summary>
      <returns>Sottoinsieme DTD interno.Questa proprietà restituisce ad esempio tutte le informazioni racchiuse tra parentesi quadre &lt;!DOCTYPE doc [...]&gt;.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NamespaceManager">
      <summary>Ottiene o imposta <see cref="T:System.Xml.XmlNamespaceManager" />.</summary>
      <returns>Campo XmlNamespaceManager.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NameTable">
      <summary>Ottiene l'<see cref="T:System.Xml.XmlNameTable" /> utilizzata per suddividere le stringhe in elementi di base.Per ulteriori informazioni sulle stringhe suddivise in elementi di base, vedere <see cref="T:System.Xml.XmlNameTable" />.</summary>
      <returns>Campo XmlNameTable.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.PublicId">
      <summary>Ottiene o imposta l'identificatore pubblico.</summary>
      <returns>Identificatore pubblico.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.SystemId">
      <summary>Ottiene o imposta l'identificatore di sistema.</summary>
      <returns>Identificatore di sistema.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlLang">
      <summary>Ottiene o imposta l'ambito xml:lang corrente.</summary>
      <returns>Ambito xml:lang corrente.Se nell'ambito non è disponibile alcun valore xml:lang, viene restituito String.Empty.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlSpace">
      <summary>Ottiene o imposta l'ambito xml:space corrente.</summary>
      <returns>Valore di <see cref="T:System.Xml.XmlSpace" /> che indica l'ambito xml:space.</returns>
    </member>
    <member name="T:System.Xml.XmlQualifiedName">
      <summary>Rappresenta un nome XML completo.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String)">
      <summary>Consente l'inizializzazione di una nuova istanza della classe <see cref="T:System.Xml.XmlQualifiedName" /> con il nome specificato.</summary>
      <param name="name">Nome locale da utilizzare come nome dell'oggetto <see cref="T:System.Xml.XmlQualifiedName" />. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Xml.XmlQualifiedName" /> con il nome e lo spazio dei nomi specificati.</summary>
      <param name="name">Nome locale da utilizzare come nome dell'oggetto <see cref="T:System.Xml.XmlQualifiedName" />. </param>
      <param name="ns">Spazio dei nomi per l'oggetto <see cref="T:System.Xml.XmlQualifiedName" />. </param>
    </member>
    <member name="F:System.Xml.XmlQualifiedName.Empty">
      <summary>Fornisce un oggetto <see cref="T:System.Xml.XmlQualifiedName" /> vuoto.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.Equals(System.Object)">
      <summary>Determina se l'oggetto <see cref="T:System.Xml.XmlQualifiedName" /> specificato equivale all'oggetto <see cref="T:System.Xml.XmlQualifiedName" /> corrente. </summary>
      <returns>true se i due oggetti rappresentano lo stesso oggetto di istanza; in caso contrario, false.</returns>
      <param name="other">Oggetto <see cref="T:System.Xml.XmlQualifiedName" /> da confrontare. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.GetHashCode">
      <summary>Restituisce il codice hash per l'oggetto <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Codice hash per l'oggetto.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.IsEmpty">
      <summary>Ottiene un valore che indica se l'oggetto <see cref="T:System.Xml.XmlQualifiedName" /> è vuoto.</summary>
      <returns>true se il nome e lo spazio dei nomi sono stringhe vuote; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Name">
      <summary>Ottiene una rappresentazione in forma di stringa del nome completo dell'oggetto <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Rappresentazione in forma di stringa del nome completo, oppure String.Empty se un nome non è definito per l'oggetto.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Namespace">
      <summary>Ottiene una rappresentazione in forma di stringa dello spazio dei nomi dell'oggetto <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Rappresentazione in forma di stringa dello spazio dei nomi, oppure String.Empty se uno spazio dei nomi non è definito per l'oggetto.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Equality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Confronta due oggetti <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>true se i due oggetti hanno lo stesso nome e gli stessi valori di spazio dei nomi; in caso contrario, false.</returns>
      <param name="a">Oggetto <see cref="T:System.Xml.XmlQualifiedName" /> da confrontare. </param>
      <param name="b">Oggetto <see cref="T:System.Xml.XmlQualifiedName" /> da confrontare. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Inequality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Confronta due oggetti <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>true se il nome e i valori di spazio dei nomi dei due oggetti sono diversi; in caso contrario, false.</returns>
      <param name="a">Oggetto <see cref="T:System.Xml.XmlQualifiedName" /> da confrontare. </param>
      <param name="b">Oggetto <see cref="T:System.Xml.XmlQualifiedName" /> da confrontare. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString">
      <summary>Restituisce il valore di stringa dell'oggetto <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Valore di stringa dell'oggetto <see cref="T:System.Xml.XmlQualifiedName" /> nel formato namespace:localname.Se per l'oggetto non è definito alcuno spazio dei nomi, questo metodo restituisce solo il nome locale.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString(System.String,System.String)">
      <summary>Restituisce il valore di stringa dell'oggetto <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Valore di stringa dell'oggetto <see cref="T:System.Xml.XmlQualifiedName" /> nel formato namespace:localname.Se per l'oggetto non è definito alcuno spazio dei nomi, questo metodo restituisce solo il nome locale.</returns>
      <param name="name">Nome dell'oggetto. </param>
      <param name="ns">Spazio dei nomi dell'oggetto. </param>
    </member>
    <member name="T:System.Xml.XmlReader">
      <summary>Rappresenta un lettore che fornisce accesso veloce, non in cache e di tipo forward-only ai dati XML.Per esaminare il codice sorgente .NET Framework per questo tipo, vedere Origine riferimento.</summary>
    </member>
    <member name="M:System.Xml.XmlReader.#ctor">
      <summary>Inizializza una nuova istanza della classe XmlReader.</summary>
    </member>
    <member name="P:System.Xml.XmlReader.AttributeCount">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il numero di attributi sul nodo corrente.</summary>
      <returns>Numero di attributi sul nodo corrente.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.BaseURI">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene l'URI di base del nodo corrente.</summary>
      <returns>URI di base del nodo corrente.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadBinaryContent">
      <summary>Ottiene un valore che indica se <see cref="T:System.Xml.XmlReader" /> implementa metodi di lettura del contenuto binario.</summary>
      <returns>true se i metodi di lettura del contenuto binario vengono implementati; in caso contrario, false.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadValueChunk">
      <summary>Ottiene un valore che indica se <see cref="T:System.Xml.XmlReader" /> implementa il metodo <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" />.</summary>
      <returns>true se <see cref="T:System.Xml.XmlReader" /> implementa il metodo <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" />; in caso contrario, false.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanResolveEntity">
      <summary>Ottiene un valore che indica se il lettore può analizzare e risolvere le entità.</summary>
      <returns>true se il lettore può analizzare e risolvere le entità; in caso contrario, false.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlReader" /> con il flusso specificato e le impostazioni predefinite.</summary>
      <returns>Oggetto usato per leggere i dati XML nel flusso.</returns>
      <param name="input">Flusso che contiene i dati XML.L'oggetto <see cref="T:System.Xml.XmlReader" /> analizza i primi byte del flusso cercando un indicatore per l'ordine dei byte o un altro segno di codifica.Quando viene determinata, la codifica viene usata per continuare la lettura del flusso, mentre l'elaborazione continua ad analizzare l'input come flusso di caratteri (Unicode).</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="input" /> è null.</exception>
      <exception cref="T:System.Security.SecurityException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non dispone di autorizzazioni sufficienti per accedere al percorso dei dati XML.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlReader" /> con il flusso e le impostazioni specificati.</summary>
      <returns>Oggetto usato per leggere i dati XML nel flusso.</returns>
      <param name="input">Flusso che contiene i dati XML.L'oggetto <see cref="T:System.Xml.XmlReader" /> analizza i primi byte del flusso cercando un indicatore per l'ordine dei byte o un altro segno di codifica.Quando viene determinata, la codifica viene usata per continuare la lettura del flusso, mentre l'elaborazione continua ad analizzare l'input come flusso di caratteri (Unicode).</param>
      <param name="settings">Impostazioni per la nuova istanza di <see cref="T:Microsoft.VisualStudio.Utilities.IContentType" />.Il valore può essere null.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="input" /> è null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlReader" /> con il flusso, le impostazioni e le informazioni di contesto specificati per l'analisi.</summary>
      <returns>Oggetto usato per leggere i dati XML nel flusso.</returns>
      <param name="input">Flusso che contiene i dati XML. L'oggetto <see cref="T:System.Xml.XmlReader" /> analizza i primi byte del flusso cercando un indicatore per l'ordine dei byte o un altro segno di codifica.Quando viene determinata, la codifica viene usata per continuare la lettura del flusso, mentre l'elaborazione continua ad analizzare l'input come flusso di caratteri (Unicode).</param>
      <param name="settings">Impostazioni per la nuova istanza di <see cref="T:Microsoft.VisualStudio.Utilities.IContentType" />.Il valore può essere null.</param>
      <param name="inputContext">Informazioni sul contesto necessarie per analizzare il frammento XML.Le informazioni sul contesto possono includere l'oggetto <see cref="T:System.Xml.XmlNameTable" /> da usare, la codifica, l'ambito dello spazio dei nomi, gli ambiti xml:lang e xml:space correnti, l'URI di base e la definizione DTD.Il valore può essere null.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="input" /> è null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlReader" /> con il lettore di testo specificato.</summary>
      <returns>Oggetto usato per leggere i dati XML nel flusso.</returns>
      <param name="input">Lettore di testo da cui leggere i dati XML.Poiché un lettore di testo restituisce un flusso di caratteri Unicode, la codifica specificata nella dichiarazione XML non viene usata dal lettore XML per decodificare il flusso di dati.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="input" /> è null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlReader" /> con il lettore di testo e le impostazioni specificati.</summary>
      <returns>Oggetto usato per leggere i dati XML nel flusso.</returns>
      <param name="input">Lettore di testo da cui leggere i dati XML.Poiché un lettore di testo restituisce un flusso di caratteri Unicode, la codifica specificata nella dichiarazione XML non viene usata dal lettore XML per decodificare il flusso di dati.</param>
      <param name="settings">Impostazioni del nuovo oggetto <see cref="T:System.Xml.XmlReader" />.Il valore può essere null.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="input" /> è null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlReader" /> con il lettore di testo, le impostazioni e le informazioni di contesto specificati per l'analisi.</summary>
      <returns>Oggetto usato per leggere i dati XML nel flusso.</returns>
      <param name="input">Lettore di testo da cui leggere i dati XML.Poiché un lettore di testo restituisce un flusso di caratteri Unicode, la codifica specificata nella dichiarazione XML non viene usata dal lettore XML per decodificare il flusso di dati.</param>
      <param name="settings">Impostazioni per la nuova istanza di <see cref="T:Microsoft.VisualStudio.Utilities.IContentType" />.Il valore può essere null.</param>
      <param name="inputContext">Informazioni sul contesto necessarie per analizzare il frammento XML.Le informazioni sul contesto possono includere l'oggetto <see cref="T:System.Xml.XmlNameTable" /> da usare, la codifica, l'ambito dello spazio dei nomi, gli ambiti xml:lang e xml:space correnti, l'URI di base e la definizione DTD.Il valore può essere null.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="input" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Le proprietà <see cref="P:System.Xml.XmlReaderSettings.NameTable" /> e <see cref="P:System.Xml.XmlParserContext.NameTable" /> contengono entrambe valori.È possibile impostare e utilizzare una sola delle proprietà NameTable.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlReader" /> con l'URI specificato.</summary>
      <returns>Oggetto usato per leggere i dati XML nel flusso.</returns>
      <param name="inputUri">URI del file che contiene i dati XML.La classe <see cref="T:System.Xml.XmlUrlResolver" /> viene usata per convertire il percorso in una rappresentazione canonica dei dati.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="inputUri" /> è null.</exception>
      <exception cref="T:System.Security.SecurityException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non dispone di autorizzazioni sufficienti per accedere al percorso dei dati XML.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file identificato dall'URI non esiste.</exception>
      <exception cref="T:System.UriFormatException">Nell'API.NET per le applicazioni Windows o nella Libreria di classi portabile, rilevare piuttosto l'eccezione della classe di base <see cref="T:System.FormatException" />.Il formato dell'URI non è corretto.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String,System.Xml.XmlReaderSettings)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlReader" /> con l'URI e le impostazioni specificati.</summary>
      <returns>Oggetto usato per leggere i dati XML nel flusso.</returns>
      <param name="inputUri">URI del file che contiene i dati XML.L'oggetto <see cref="T:System.Xml.XmlResolver" /> nell'oggetto <see cref="T:System.Xml.XmlReaderSettings" /> viene usato per eseguire la conversione del percorso a una rappresentazione canonica dei dati.Se <see cref="P:System.Xml.XmlReaderSettings.XmlResolver" /> è null, viene usato un nuovo oggetto <see cref="T:System.Xml.XmlUrlResolver" />.</param>
      <param name="settings">Impostazioni per la nuova istanza di <see cref="T:Microsoft.VisualStudio.Utilities.IContentType" />.Il valore può essere null.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="inputUri" /> è null.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Il file specificato dall'URI non è stato trovato.</exception>
      <exception cref="T:System.UriFormatException">Nell'API.NET per le applicazioni Windows o nella Libreria di classi portabile, rilevare piuttosto l'eccezione della classe di base <see cref="T:System.FormatException" />.Il formato dell'URI non è corretto.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.Xml.XmlReader,System.Xml.XmlReaderSettings)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlReader" /> con il lettore XML e le impostazioni specificate.</summary>
      <returns>Oggetto di cui è stato eseguito il wrapping intorno all'oggetto <see cref="T:System.Xml.XmlReader" /> specificato.</returns>
      <param name="reader">Oggetto da usare come lettore XML sottostante.</param>
      <param name="settings">Impostazioni per la nuova istanza di <see cref="T:Microsoft.VisualStudio.Utilities.IContentType" />.Il livello di conformità dell'oggetto <see cref="T:System.Xml.XmlReaderSettings" /> deve corrispondere a quello del lettore sottostante o deve essere impostato su <see cref="F:System.Xml.ConformanceLevel.Auto" />.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="reader" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Se l'oggetto <see cref="T:System.Xml.XmlReaderSettings" /> specifica un livello di conformità che non corrisponde al livello di conformità del lettore sottostante.-oppure-Lo stato dell'oggetto <see cref="T:System.Xml.XmlReader" /> sottostante è <see cref="F:System.Xml.ReadState.Error" /> o <see cref="F:System.Xml.ReadState.Closed" />.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Depth">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene la profondità del nodo corrente nel documento XML.</summary>
      <returns>Profondità del nodo corrente nel documento XML.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose">
      <summary>Rilascia tutte le risorse usate dall'istanza corrente della classe <see cref="T:System.Xml.XmlReader" />.</summary>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite usate da <see cref="T:System.Xml.XmlReader" /> e, facoltativamente, le risorse gestite.</summary>
      <param name="disposing">true per rilasciare sia le risorse gestite sia quelle non gestite; false per rilasciare solo le risorse non gestite.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.EOF">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene un valore che indica se il lettore è posizionato alla fine del flusso.</summary>
      <returns>true se il lettore è posizionato alla fine del flusso; in caso contrario, false.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.Int32)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il valore dell'attributo con l'indice specificato.</summary>
      <returns>Valore dell'attributo specificato.Questo metodo non determina lo spostamento del lettore.</returns>
      <param name="i">Indice dell'attributo.L'indice è in base zero.Il primo attributo ha indice 0.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" /> non è compreso nell'intervallo.Richiesto valore non negativo e minore della dimensione dell'insieme di attributi.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il valore dell'attributo con la proprietà <see cref="P:System.Xml.XmlReader.Name" /> specificata.</summary>
      <returns>Valore dell'attributo specificato.Se l'attributo non viene trovato o se il valore è String.Empty, verrà restituito null.</returns>
      <param name="name">Nome completo dell'attributo.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il valore dell'attributo con le proprietà <see cref="P:System.Xml.XmlReader.LocalName" /> e <see cref="P:System.Xml.XmlReader.NamespaceURI" /> specificate.</summary>
      <returns>Valore dell'attributo specificato.Se l'attributo non viene trovato o se il valore è String.Empty, verrà restituito null.Questo metodo non determina lo spostamento del lettore.</returns>
      <param name="name">Nome locale dell'attributo.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'attributo.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetValueAsync">
      <summary>Ottiene in modo asincrono il valore del nodo corrente.</summary>
      <returns>Valore del nodo corrente.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasAttributes">
      <summary>Ottiene un valore che indica se il nodo corrente dispone di attributi.</summary>
      <returns>true se il nodo corrente contiene attributi; in caso contrario, false.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasValue">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene un valore che indica se il nodo corrente può avere <see cref="P:System.Xml.XmlReader.Value" />.</summary>
      <returns>true se il nodo sul quale il lettore è attualmente posizionato può contenere Value; in caso contrario, false.Se false, il valore del nodo è String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsDefault">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene un valore che indica se il nodo corrente è un attributo generato dal valore predefinito configurato nella definizione DTD o nello schema.</summary>
      <returns>true se il nodo corrente è un attributo il cui valore è stato generato in base al valore predefinito configurato nella definizione DTD o nello schema; false se il valore dell'attributo è stato impostato in modo esplicito.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsEmptyElement">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene un valore che indica se il nodo corrente è un elemento vuoto, ad esempio &lt;MyElement/&gt;.</summary>
      <returns>true se il nodo corrente rappresenta un elemento (<see cref="P:System.Xml.XmlReader.NodeType" /> uguale a XmlNodeType.Element) che termina con /&gt;; in caso contrario, false.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsName(System.String)">
      <summary>Restituisce un valore che indica se l'argomento della stringa è un nome XML valido.</summary>
      <returns>true se il nome è valido; in caso contrario, false.</returns>
      <param name="str">Nome da convalidare.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="str" /> è null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsNameToken(System.String)">
      <summary>Restituisce un valore che indica se l'argomento della stringa è un token di un nome XML valido o meno.</summary>
      <returns>true se è un token del nome valido; in caso contrario, false.</returns>
      <param name="str">Token del nome da convalidare.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="str" /> è null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement">
      <summary>Chiama <see cref="M:System.Xml.XmlReader.MoveToContent" /> e verifica se il nodo di contenuto corrente è un tag di inizio o un tag di elemento vuoto.</summary>
      <returns>true se <see cref="M:System.Xml.XmlReader.MoveToContent" /> trova un tag di inizio o un tag di elemento vuoto; false se viene trovato un tipo di nodo diverso da XmlNodeType.Element.</returns>
      <exception cref="T:System.Xml.XmlException">È stata trovata una stringa XML non corretta nel flusso di input.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String)">
      <summary>Chiama <see cref="M:System.Xml.XmlReader.MoveToContent" /> e verifica se il nodo corrente è un tag di inizio o un tag di elemento vuoto e se la proprietà <see cref="P:System.Xml.XmlReader.Name" /> dell'elemento trovato corrisponde all'argomento specificato.</summary>
      <returns>true se il nodo risultante è un elemento e la proprietà Name corrisponde alla stringa specificata.false se è stato trovato un tipo di nodo diverso da XmlNodeType.Element oppure se la proprietà Name dell'elemento non corrisponde alla stringa specificata.</returns>
      <param name="name">Stringa confrontata con la proprietà Name dell'elemento trovato.</param>
      <exception cref="T:System.Xml.XmlException">È stata trovata una stringa XML non corretta nel flusso di input.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String,System.String)">
      <summary>Chiama <see cref="M:System.Xml.XmlReader.MoveToContent" /> e verifica se il nodo di contenuto è un tag di inizio o un tag di elemento vuoto e se le proprietà <see cref="P:System.Xml.XmlReader.LocalName" /> e <see cref="P:System.Xml.XmlReader.NamespaceURI" /> dell'elemento trovato corrispondono alle stringhe specificate.</summary>
      <returns>true, se il nodo risultante è un elemento.false se è stato trovato un tipo di nodo diverso da XmlNodeType.Element oppure se le proprietà LocalName e NamespaceURI dell'elemento non corrispondono alle stringhe specificate.</returns>
      <param name="localname">Stringa da confrontare con la proprietà LocalName dell'elemento trovato.</param>
      <param name="ns">Stringa da confrontare con la proprietà NamespaceURI dell'elemento trovato.</param>
      <exception cref="T:System.Xml.XmlException">È stata trovata una stringa XML non corretta nel flusso di input.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.Int32)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il valore dell'attributo con l'indice specificato.</summary>
      <returns>Valore dell'attributo specificato.</returns>
      <param name="i">Indice dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il valore dell'attributo con la proprietà <see cref="P:System.Xml.XmlReader.Name" /> specificata.</summary>
      <returns>Valore dell'attributo specificato.Se l'attributo non viene trovato, verrà restituito null.</returns>
      <param name="name">Nome completo dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il valore dell'attributo con le proprietà <see cref="P:System.Xml.XmlReader.LocalName" /> e <see cref="P:System.Xml.XmlReader.NamespaceURI" /> specificate.</summary>
      <returns>Valore dell'attributo specificato.Se l'attributo non viene trovato, verrà restituito null.</returns>
      <param name="name">Nome locale dell'attributo.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.LocalName">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il nome locale del nodo corrente.</summary>
      <returns>Nome del nodo corrente senza il prefisso.Ad esempio, LocalName è book per l'elemento &lt;bk:book&gt;.Per i tipi di nodo privi di nome, quali Text, Comment e così via, questa proprietà restituisce String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.LookupNamespace(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, risolve il prefisso di uno spazio dei nomi nell'ambito dell'elemento corrente.</summary>
      <returns>URI dello spazio dei nomi a cui viene mappato il prefisso oppure null se non viene trovato alcun prefisso corrispondente.</returns>
      <param name="prefix">Prefisso di cui risolvere l'URI dello spazio dei nomi.Per ottenere lo spazio dei nomi predefinito corrispondente, passare una stringa vuota.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.Int32)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, passa all'attributo con l'indice specificato.</summary>
      <param name="i">Indice dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il parametro ha un valore negativo.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, passa all'attributo con la proprietà <see cref="P:System.Xml.XmlReader.Name" /> specificata.</summary>
      <returns>true se l'attributo viene trovato; in caso contrario, false.Se viene restituito il valore false, la posizione del lettore non subirà alcuna modifica.</returns>
      <param name="name">Nome completo dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.ArgumentException">Il parametro è una stringa vuota.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, passa all'attributo con le proprietà <see cref="P:System.Xml.XmlReader.LocalName" /> e <see cref="P:System.Xml.XmlReader.NamespaceURI" /> specificate.</summary>
      <returns>true se l'attributo viene trovato; in caso contrario, false.Se viene restituito il valore false, la posizione del lettore non subirà alcuna modifica.</returns>
      <param name="name">Nome locale dell'attributo.</param>
      <param name="ns">URI dello spazio dei nomi dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.ArgumentNullException">I valori di entrambi i parametri sono null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContent">
      <summary>Controlla se il nodo corrente è un nodo di contenuto (testo diverso da spazi vuoti, CDATA, Element, EndElement, EntityReference o EndEntity).Se il nodo non è un nodo di contenuto, il lettore passa al nodo di contenuto successivo oppure alla fine del file.Ignora i nodi del tipo seguente: ProcessingInstruction, DocumentType, Comment, Whitespace o SignificantWhitespace.</summary>
      <returns>Proprietà <see cref="P:System.Xml.XmlReader.NodeType" /> del nodo corrente trovato dal metodo o XmlNodeType.None se il lettore ha raggiunto la fine del flusso di input.</returns>
      <exception cref="T:System.Xml.XmlException">È stata trovata una stringa XML non corretta nel flusso di input.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContentAsync">
      <summary>Verifica in modo asincrono se il nodo corrente è un nodo di contenuto.Se il nodo non è un nodo di contenuto, il lettore passa al nodo di contenuto successivo oppure alla fine del file.</summary>
      <returns>Proprietà <see cref="P:System.Xml.XmlReader.NodeType" /> del nodo corrente trovato dal metodo o XmlNodeType.None se il lettore ha raggiunto la fine del flusso di input.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToElement">
      <summary>Quando ne viene eseguito l'override in una classe derivata, passa all'elemento che contiene il nodo attributo corrente.</summary>
      <returns>true se il lettore è posizionato in corrispondenza di un attributo, ovvero il lettore si sposta in corrispondenza dell'elemento che possiede l'attributo; false se il lettore non è posizionato in corrispondenza di un attributo, ovvero la posizione del lettore non subisce alcuna modifica.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToFirstAttribute">
      <summary>Quando ne viene eseguito l'override in una classe derivata, passa al primo attributo.</summary>
      <returns>true se esiste un attributo, ovvero il lettore si sposta in corrispondenza del primo attributo; in caso contrario, false, ovvero la posizione del lettore non subisce alcuna modifica.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToNextAttribute">
      <summary>Quando ne viene eseguito l'override in una classe derivata, passa all'attributo successivo.</summary>
      <returns>true se esiste un attributo successivo; false se non esistono altri attributi.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Name">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il nome completo del nodo corrente.</summary>
      <returns>Nome completo del nodo corrente.Ad esempio, Name è bk:book per l'elemento &lt;bk:book&gt;.Il nome restituito dipende dalla proprietà <see cref="P:System.Xml.XmlReader.NodeType" /> del nodo.I seguenti tipi di nodo restituiscono i valori inclusi nell'elenco.Tutti gli altri tipi di nodo restituiscono una stringa vuota.Tipo di nodo Nome AttributeNome dell'attributo. DocumentTypeNome del tipo di documento. ElementNome del tag. EntityReferenceNome dell'entità a cui si fa riferimento. ProcessingInstructionDestinazione dell'istruzione di elaborazione. XmlDeclarationStringa letterale xml. </returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NamespaceURI">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene l'URI dello spazio dei nomi, definito nella specifica W3C Namespace, del nodo su cui è posizionato il lettore.</summary>
      <returns>URI dello spazio dei nomi del nodo corrente; in caso contrario, una stringa vuota.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NameTable">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene l'oggetto <see cref="T:System.Xml.XmlNameTable" /> associato a questa implementazione.</summary>
      <returns>Oggetto XmlNameTable che consente di ottenere la versione atomizzata di una stringa all'interno del nodo.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NodeType">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il tipo del nodo corrente.</summary>
      <returns>Uno dei valori di enumerazione che specifica il tipo del nodo corrente.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Prefix">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il prefisso dello spazio dei nomi associato al nodo corrente.</summary>
      <returns>Prefisso dello spazio dei nomi associato al nodo corrente.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Read">
      <summary>Quando ne viene eseguito l'override in una classe derivata, visualizza il nodo successivo nel flusso.</summary>
      <returns>true se il nodo successivo è stato letto correttamente; in caso contrario, false.</returns>
      <exception cref="T:System.Xml.XmlException">Si è verificato un errore durante l'analisi dell'XML.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAsync">
      <summary>Legge in modo asincrono il nodo successivo del flusso.</summary>
      <returns>true se è stata completata la lettura del nodo successivo; false se non esistono altri nodi da leggere.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAttributeValue">
      <summary>Quando ne viene eseguito l'override in una classe derivata, analizza il valore dell'attributo incluso in uno o più nodi Text, EntityReference o EndEntity.</summary>
      <returns>true se sono presenti nodi da restituire.false se il lettore non è posizionato in corrispondenza del nodo attributo quando viene effettuata la chiamata iniziale oppure se è stato letto il valore di tutti gli attributi.Un attributo vuoto, quale misc="", restituisce true con un singolo nodo il cui valore è String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Legge il contenuto come oggetto del tipo specificato.</summary>
      <returns>Contenuto di testo concatenato o valore dell'attributo convertito nel tipo specificato.</returns>
      <param name="returnType">Tipo di valore da restituire.Nota   In .NET Framework 3.5 il valore del parametro <paramref name="returnType" /> può essere il tipo <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Oggetto <see cref="T:System.Xml.IXmlNamespaceResolver" /> usato per risolvere qualsiasi prefisso di spazio dei nomi correlato al tipo di conversione.Può essere usato ad esempio per la conversione di un oggetto <see cref="T:System.Xml.XmlQualifiedName" /> in xs:string.Il valore può essere null.</param>
      <exception cref="T:System.FormatException">Il contenuto non presenta il formato corretto per il tipo di destinazione.</exception>
      <exception cref="T:System.InvalidCastException">Il tentativo di cast non è valido.</exception>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="returnType" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Il nodo corrente non è un tipo di nodo supportato.Per ulteriori informazioni vedere la tabella riportata di seguito.</exception>
      <exception cref="T:System.OverflowException">Leggere Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Legge in modo asincrono il contenuto come oggetto del tipo specificato.</summary>
      <returns>Contenuto di testo concatenato o valore dell'attributo convertito nel tipo specificato.</returns>
      <param name="returnType">Tipo di valore da restituire.</param>
      <param name="namespaceResolver">Oggetto <see cref="T:System.Xml.IXmlNamespaceResolver" /> usato per risolvere qualsiasi prefisso di spazio dei nomi correlato al tipo di conversione.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Legge il contenuto e restituisce byte binari decodificati Base64.</summary>
      <returns>Numero di byte scritti nel buffer.</returns>
      <param name="buffer">Buffer in cui copiare il testo risultante.Questo valore non può essere null.</param>
      <param name="index">Offset nel buffer a partire da cui iniziare a copiare il risultato.</param>
      <param name="count">Numero massimo di byte da copiare nel buffer.Il numero effettivo di byte copiati viene restituito da questo metodo.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="buffer" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)" /> non è supportato nel nodo corrente.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'indice nel buffer oppure la somma di indice e numero è superiore alla dimensione del buffer allocato.</exception>
      <exception cref="T:System.NotSupportedException">L'implementazione di <see cref="T:System.Xml.XmlReader" /> non supporta questo metodo.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Legge in modo asincrono il contenuto e restituisce byte binari con decodifica Base64.</summary>
      <returns>Numero di byte scritti nel buffer.</returns>
      <param name="buffer">Buffer in cui copiare il testo risultante.Questo valore non può essere null.</param>
      <param name="index">Offset nel buffer a partire da cui iniziare a copiare il risultato.</param>
      <param name="count">Numero massimo di byte da copiare nel buffer.Il numero effettivo di byte copiati viene restituito da questo metodo.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Legge il contenuto e restituisce i byte binari decodificati BinHex.</summary>
      <returns>Numero di byte scritti nel buffer.</returns>
      <param name="buffer">Buffer in cui copiare il testo risultante.Questo valore non può essere null.</param>
      <param name="index">Offset nel buffer a partire da cui iniziare a copiare il risultato.</param>
      <param name="count">Numero massimo di byte da copiare nel buffer.Il numero effettivo di byte copiati viene restituito da questo metodo.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="buffer" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)" /> non è supportato nel nodo corrente.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'indice nel buffer oppure la somma di indice e numero è superiore alla dimensione del buffer allocato.</exception>
      <exception cref="T:System.NotSupportedException">L'implementazione di <see cref="T:System.Xml.XmlReader" /> non supporta questo metodo.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Legge in modo asincrono il contenuto e restituisce dati binari con decodifica BinHex.</summary>
      <returns>Numero di byte scritti nel buffer.</returns>
      <param name="buffer">Buffer in cui copiare il testo risultante.Questo valore non può essere null.</param>
      <param name="index">Offset nel buffer a partire da cui iniziare a copiare il risultato.</param>
      <param name="count">Numero massimo di byte da copiare nel buffer.Il numero effettivo di byte copiati viene restituito da questo metodo.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBoolean">
      <summary>Legge il contenuto di testo nella posizione corrente come Boolean.</summary>
      <returns>Contenuto di testo come oggetto <see cref="T:System.Boolean" />.</returns>
      <exception cref="T:System.InvalidCastException">Il tentativo di cast non è valido.</exception>
      <exception cref="T:System.FormatException">Il formato della stringa non è valido.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDateTimeOffset">
      <summary>Legge il contenuto di testo nella posizione corrente come oggetto <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Contenuto di testo come oggetto <see cref="T:System.DateTimeOffset" />.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDecimal">
      <summary>Legge il contenuto di testo nella posizione corrente come oggetto <see cref="T:System.Decimal" />.</summary>
      <returns>Contenuto di testo nella posizione corrente come oggetto <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.InvalidCastException">Il tentativo di cast non è valido.</exception>
      <exception cref="T:System.FormatException">Il formato della stringa non è valido.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDouble">
      <summary>Legge il contenuto di testo nella posizione corrente come numero a virgola mobile a precisione doppia.</summary>
      <returns>Contenuto di testo come numero a virgola mobile a precisione doppia.</returns>
      <exception cref="T:System.InvalidCastException">Il tentativo di cast non è valido.</exception>
      <exception cref="T:System.FormatException">Il formato della stringa non è valido.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsFloat">
      <summary>Legge il contenuto di testo nella posizione corrente come numero a virgola mobile a precisione singola.</summary>
      <returns>Contenuto di testo nella posizione corrente come numero a virgola mobile a precisione singola.</returns>
      <exception cref="T:System.InvalidCastException">Il tentativo di cast non è valido.</exception>
      <exception cref="T:System.FormatException">Il formato della stringa non è valido.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsInt">
      <summary>Legge il contenuto di testo nella posizione corrente come valore intero con segno a 32 bit.</summary>
      <returns>Contenuto di testo come valore intero con segno a 32 bit.</returns>
      <exception cref="T:System.InvalidCastException">Il tentativo di cast non è valido.</exception>
      <exception cref="T:System.FormatException">Il formato della stringa non è valido.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsLong">
      <summary>Legge il contenuto di testo nella posizione corrente come valore intero con segno a 64 bit.</summary>
      <returns>Contenuto di testo come valore intero con segno a 64 bit.</returns>
      <exception cref="T:System.InvalidCastException">Il tentativo di cast non è valido.</exception>
      <exception cref="T:System.FormatException">Il formato della stringa non è valido.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObject">
      <summary>Legge il contenuto di testo nella posizione corrente come <see cref="T:System.Object" />.</summary>
      <returns>Contenuto di testo come oggetto CLR (Common Language Runtime) più appropriato.</returns>
      <exception cref="T:System.InvalidCastException">Il tentativo di cast non è valido.</exception>
      <exception cref="T:System.FormatException">Il formato della stringa non è valido.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObjectAsync">
      <summary>Legge in modo asincrono il contenuto di testo nella posizione corrente come oggetto <see cref="T:System.Object" />.</summary>
      <returns>Contenuto di testo come oggetto CLR (Common Language Runtime) più appropriato.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsString">
      <summary>Legge il contenuto di testo nella posizione corrente come oggetto <see cref="T:System.String" />.</summary>
      <returns>Contenuto di testo come oggetto <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidCastException">Il tentativo di cast non è valido.</exception>
      <exception cref="T:System.FormatException">Il formato della stringa non è valido.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsStringAsync">
      <summary>Legge in modo asincrono il contenuto di testo nella posizione corrente come oggetto <see cref="T:System.String" />.</summary>
      <returns>Contenuto di testo come oggetto <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Legge il contenuto dell'elemento come il tipo richiesto.</summary>
      <returns>Contenuto dell'elemento convertito nell'oggetto tipizzato richiesto.</returns>
      <param name="returnType">Tipo di valore da restituire.Nota   In .NET Framework 3.5 il valore del parametro <paramref name="returnType" /> può essere il tipo <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Oggetto <see cref="T:System.Xml.IXmlNamespaceResolver" /> usato per risolvere qualsiasi prefisso di spazio dei nomi correlato al tipo di conversione.</param>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito nel tipo richiesto.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.OverflowException">Leggere Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver,System.String,System.String)">
      <summary>Verifica che il nome locale e l'URI dello spazio dei nomi specificati corrispondano a quelli dell'elemento corrente, quindi legge il contenuto dell'elemento come il tipo richiesto.</summary>
      <returns>Contenuto dell'elemento convertito nell'oggetto tipizzato richiesto.</returns>
      <param name="returnType">Tipo di valore da restituire.Nota   In .NET Framework 3.5 il valore del parametro <paramref name="returnType" /> può essere il tipo <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Oggetto <see cref="T:System.Xml.IXmlNamespaceResolver" /> usato per risolvere qualsiasi prefisso di spazio dei nomi correlato al tipo di conversione.</param>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito nel tipo richiesto.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.ArgumentException">Il nome locale e l'URI dello spazio dei nomi specificati non corrispondono a quelli dell'elemento corrente da leggere.</exception>
      <exception cref="T:System.OverflowException">Leggere Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Legge in modo asincrono il contenuto dell'elemento come il tipo richiesto.</summary>
      <returns>Contenuto dell'elemento convertito nell'oggetto tipizzato richiesto.</returns>
      <param name="returnType">Tipo di valore da restituire.</param>
      <param name="namespaceResolver">Oggetto <see cref="T:System.Xml.IXmlNamespaceResolver" /> usato per risolvere qualsiasi prefisso di spazio dei nomi correlato al tipo di conversione.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Legge l'elemento e decodifica il contenuto Base64.</summary>
      <returns>Numero di byte scritti nel buffer.</returns>
      <param name="buffer">Buffer in cui copiare il testo risultante.Questo valore non può essere null.</param>
      <param name="index">Offset nel buffer a partire da cui iniziare a copiare il risultato.</param>
      <param name="count">Numero massimo di byte da copiare nel buffer.Il numero effettivo di byte copiati viene restituito da questo metodo.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="buffer" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Il nodo corrente non è un nodo elemento.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'indice nel buffer oppure la somma di indice e numero è superiore alla dimensione del buffer allocato.</exception>
      <exception cref="T:System.NotSupportedException">L'implementazione di <see cref="T:System.Xml.XmlReader" /> non supporta questo metodo.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento include contenuto misto.</exception>
      <exception cref="T:System.FormatException">Il contenuto non può essere convertito nel tipo richiesto.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Legge in modo asincrono l'elemento e decodifica il contenuto Base64.</summary>
      <returns>Numero di byte scritti nel buffer.</returns>
      <param name="buffer">Buffer in cui copiare il testo risultante.Questo valore non può essere null.</param>
      <param name="index">Offset nel buffer a partire da cui iniziare a copiare il risultato.</param>
      <param name="count">Numero massimo di byte da copiare nel buffer.Il numero effettivo di byte copiati viene restituito da questo metodo.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Legge l'elemento e decodifica il contenuto BinHex.</summary>
      <returns>Numero di byte scritti nel buffer.</returns>
      <param name="buffer">Buffer in cui copiare il testo risultante.Questo valore non può essere null.</param>
      <param name="index">Offset nel buffer a partire da cui iniziare a copiare il risultato.</param>
      <param name="count">Numero massimo di byte da copiare nel buffer.Il numero effettivo di byte copiati viene restituito da questo metodo.</param>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="buffer" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Il nodo corrente non è un nodo elemento.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'indice nel buffer oppure la somma di indice e numero è superiore alla dimensione del buffer allocato.</exception>
      <exception cref="T:System.NotSupportedException">L'implementazione di <see cref="T:System.Xml.XmlReader" /> non supporta questo metodo.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento include contenuto misto.</exception>
      <exception cref="T:System.FormatException">Il contenuto non può essere convertito nel tipo richiesto.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Legge in modo asincrono l'elemento e decodifica il contenuto BinHex.</summary>
      <returns>Numero di byte scritti nel buffer.</returns>
      <param name="buffer">Buffer in cui copiare il testo risultante.Questo valore non può essere null.</param>
      <param name="index">Offset nel buffer a partire da cui iniziare a copiare il risultato.</param>
      <param name="count">Numero massimo di byte da copiare nel buffer.Il numero effettivo di byte copiati viene restituito da questo metodo.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean">
      <summary>Legge l'elemento corrente e restituisce il contenuto come oggetto <see cref="T:System.Boolean" />.</summary>
      <returns>Contenuto dell'elemento come oggetto <see cref="T:System.Boolean" />.</returns>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito in un oggetto <see cref="T:System.Boolean" />.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean(System.String,System.String)">
      <summary>Verifica che il nome locale e l'URI dello spazio dei nomi specificati corrispondano a quelli dell'elemento corrente, quindi legge l'elemento e restituisce il contenuto come oggetto <see cref="T:System.Boolean" />.</summary>
      <returns>Contenuto dell'elemento come oggetto <see cref="T:System.Boolean" />.</returns>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito nel tipo richiesto.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.ArgumentException">Il nome locale e l'URI dello spazio dei nomi specificati non corrispondono a quelli dell'elemento corrente da leggere.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal">
      <summary>Legge l'elemento corrente e restituisce il contenuto come oggetto <see cref="T:System.Decimal" />.</summary>
      <returns>Contenuto dell'elemento come oggetto <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito in un oggetto <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal(System.String,System.String)">
      <summary>Verifica che il nome locale e l'URI dello spazio dei nomi specificati corrispondano a quelli dell'elemento corrente, quindi legge l'elemento e restituisce il contenuto come oggetto <see cref="T:System.Decimal" />.</summary>
      <returns>Contenuto dell'elemento come oggetto <see cref="T:System.Decimal" />.</returns>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito in un oggetto <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.ArgumentException">Il nome locale e l'URI dello spazio dei nomi specificati non corrispondono a quelli dell'elemento corrente da leggere.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble">
      <summary>Legge l'elemento corrente e restituisce il contenuto come numero a virgola mobile a precisione doppia.</summary>
      <returns>Contenuto dell'elemento come numero a virgola mobile a precisione doppia.</returns>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito come numero a virgola mobile e precisione doppia.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble(System.String,System.String)">
      <summary>Verifica che il nome locale e l'URI dello spazio dei nomi specificati corrispondano a quelli dell'elemento corrente, quindi legge l'elemento e restituisce il contenuto come numero a virgola mobile a precisione doppia.</summary>
      <returns>Contenuto dell'elemento come numero a virgola mobile a precisione doppia.</returns>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito nel tipo richiesto.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.ArgumentException">Il nome locale e l'URI dello spazio dei nomi specificati non corrispondono a quelli dell'elemento corrente da leggere.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat">
      <summary>Legge l'elemento corrente e restituisce il contenuto come numero a virgola mobile a precisione singola.</summary>
      <returns>Contenuto dell'elemento come numero a virgola mobile a precisione singola.</returns>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito come numero a virgola mobile e precisione singola.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat(System.String,System.String)">
      <summary>Verifica che il nome locale e l'URI dello spazio dei nomi specificati corrispondano a quelli dell'elemento corrente, quindi legge l'elemento e restituisce il contenuto come numero a virgola mobile a precisione singola.</summary>
      <returns>Contenuto dell'elemento come numero a virgola mobile a precisione singola.</returns>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito come numero a virgola mobile e precisione singola.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.ArgumentException">Il nome locale e l'URI dello spazio dei nomi specificati non corrispondono a quelli dell'elemento corrente da leggere.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt">
      <summary>Legge l'elemento corrente e restituisce il contenuto come valore intero con segno a 32 bit.</summary>
      <returns>Contenuto dell'elemento come valore intero con segno a 32 bit.</returns>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito in un valore intero con segno a 32 bit.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt(System.String,System.String)">
      <summary>Verifica che il nome locale e l'URI dello spazio dei nomi specificati corrispondano a quelli dell'elemento corrente, quindi legge l'elemento e restituisce il contenuto come valore intero con segno a 32 bit.</summary>
      <returns>Contenuto dell'elemento come valore intero con segno a 32 bit.</returns>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito in un valore intero con segno a 32 bit.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.ArgumentException">Il nome locale e l'URI dello spazio dei nomi specificati non corrispondono a quelli dell'elemento corrente da leggere.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong">
      <summary>Legge l'elemento corrente e restituisce il contenuto come valore intero con segno a 64 bit.</summary>
      <returns>Contenuto dell'elemento come valore intero con segno a 64 bit.</returns>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito in un valore intero con segno a 64 bit.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong(System.String,System.String)">
      <summary>Verifica che il nome locale e l'URI dello spazio dei nomi specificati corrispondano a quelli dell'elemento corrente, quindi legge l'elemento e restituisce il contenuto come valore intero con segno a 64 bit.</summary>
      <returns>Contenuto dell'elemento come valore intero con segno a 64 bit.</returns>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito in un valore intero con segno a 64 bit.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.ArgumentException">Il nome locale e l'URI dello spazio dei nomi specificati non corrispondono a quelli dell'elemento corrente da leggere.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject">
      <summary>Legge l'elemento corrente e restituisce il contenuto come <see cref="T:System.Object" />.</summary>
      <returns>Oggetto CLR (Common Language Runtime) boxed del tipo più appropriato.La proprietà <see cref="P:System.Xml.XmlReader.ValueType" /> determina il tipo CLR appropriato.Se il contenuto è tipizzato come tipo di elenco, il metodo restituisce una matrice di oggetti boxed del tipo appropriato.</returns>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito nel tipo richiesto.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject(System.String,System.String)">
      <summary>Verifica che il nome locale e l'URI dello spazio dei nomi corrispondano a quelli dell'elemento corrente, quindi legge l'elemento corrente e restituisce il contenuto come <see cref="T:System.Object" />.</summary>
      <returns>Oggetto CLR (Common Language Runtime) boxed del tipo più appropriato.La proprietà <see cref="P:System.Xml.XmlReader.ValueType" /> determina il tipo CLR appropriato.Se il contenuto è tipizzato come tipo di elenco, il metodo restituisce una matrice di oggetti boxed del tipo appropriato.</returns>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito nel tipo richiesto.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.ArgumentException">Il nome locale e l'URI dello spazio dei nomi specificati non corrispondono a quelli dell'elemento corrente da leggere.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObjectAsync">
      <summary>Legge in modo asincrono l'elemento corrente e restituisce il contenuto come oggetto <see cref="T:System.Object" />.</summary>
      <returns>Oggetto CLR (Common Language Runtime) boxed del tipo più appropriato.La proprietà <see cref="P:System.Xml.XmlReader.ValueType" /> determina il tipo CLR appropriato.Se il contenuto è tipizzato come tipo di elenco, il metodo restituisce una matrice di oggetti boxed del tipo appropriato.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString">
      <summary>Legge l'elemento corrente e restituisce il contenuto come oggetto <see cref="T:System.String" />.</summary>
      <returns>Contenuto dell'elemento come oggetto <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito in un oggetto <see cref="T:System.String" />.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString(System.String,System.String)">
      <summary>Verifica che il nome locale e l'URI dello spazio dei nomi specificati corrispondano a quelli dell'elemento corrente, quindi legge l'elemento e restituisce il contenuto come oggetto <see cref="T:System.String" />.</summary>
      <returns>Contenuto dell'elemento come oggetto <see cref="T:System.String" />.</returns>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Xml.XmlReader" /> non è posizionato in corrispondenza di un elemento.</exception>
      <exception cref="T:System.Xml.XmlException">L'elemento corrente contiene elementi figlio.-oppure-Il contenuto dell'elemento non può essere convertito in un oggetto <see cref="T:System.String" />.</exception>
      <exception cref="T:System.ArgumentNullException">Il metodo è stato chiamato con argomenti null.</exception>
      <exception cref="T:System.ArgumentException">Il nome locale e l'URI dello spazio dei nomi specificati non corrispondono a quelli dell'elemento corrente da leggere.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsStringAsync">
      <summary>Legge in modo asincrono l'elemento corrente e restituisce il contenuto come oggetto <see cref="T:System.String" />.</summary>
      <returns>Contenuto dell'elemento come oggetto <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadEndElement">
      <summary>Verifica che il nodo di contenuto corrente sia un tag di fine e sposta il lettore al nodo successivo.</summary>
      <exception cref="T:System.Xml.XmlException">Il nodo corrente non è un tag di fine oppure è stata rilevata una stringa di codice XML non corretta nel flusso di input.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXml">
      <summary>Quando ne viene eseguito l'override in una classe derivata, legge tutto il contenuto come stringa, incluso il markup.</summary>
      <returns>Tutto il contenuto XML del nodo corrente, incluso il markup.Se il nodo corrente non ha elementi figlio, viene restituita una stringa vuota.Se il nodo corrente non è né un elemento né un attributo, verrà restituita una stringa vuota.</returns>
      <exception cref="T:System.Xml.XmlException">L'XML non è in formato corretto oppure si è verificato un errore durante l'analisi dell'XML.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXmlAsync">
      <summary>Legge in modo asincrono tutto il contenuto, incluso il markup, come stringa.</summary>
      <returns>Tutto il contenuto XML del nodo corrente, incluso il markup.Se il nodo corrente non ha elementi figlio, viene restituita una stringa vuota.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXml">
      <summary>Quando ne viene eseguito l'override in una classe derivata, legge il contenuto, incluso il markup, che rappresenta questo nodo e tutti i relativi nodi figlio.</summary>
      <returns>Se il lettore è posizionato su un nodo elemento o attributo, il metodo restituisce tutto il contenuto XML, incluso il markup, del nodo corrente e di tutti i relativi nodi figlio; in caso contrario, restituisce una stringa vuota.</returns>
      <exception cref="T:System.Xml.XmlException">L'XML non è in formato corretto oppure si è verificato un errore durante l'analisi dell'XML.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXmlAsync">
      <summary>Legge in modo asincrono il contenuto, incluso il markup, che rappresenta il nodo e tutti i relativi nodi figlio.</summary>
      <returns>Se il lettore è posizionato su un nodo elemento o attributo, il metodo restituisce tutto il contenuto XML, incluso il markup, del nodo corrente e di tutti i relativi nodi figlio; in caso contrario, restituisce una stringa vuota.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement">
      <summary>Verifica se il nodo corrente è un elemento e sposta il lettore al nodo successivo.</summary>
      <exception cref="T:System.Xml.XmlException">È stata rilevata una stringa XML non corretta nel flusso di input.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String)">
      <summary>Verifica che il nodo di contenuto corrente sia un elemento con la proprietà <see cref="P:System.Xml.XmlReader.Name" /> specificata e passa il lettore al nodo successivo.</summary>
      <param name="name">Nome completo dell'elemento.</param>
      <exception cref="T:System.Xml.XmlException">È stata rilevata una stringa XML non corretta nel flusso di input. -oppure- Il <see cref="P:System.Xml.XmlReader.Name" /> dell'elemento non corrisponde al <paramref name="name" /> specificato.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String,System.String)">
      <summary>Verifica che il nodo di contenuto corrente sia un elemento con le proprietà <see cref="P:System.Xml.XmlReader.LocalName" /> e <see cref="P:System.Xml.XmlReader.NamespaceURI" /> specificate e passa il lettore al nodo successivo.</summary>
      <param name="localname">Nome locale dell'elemento.</param>
      <param name="ns">URI dello spazio dei nomi dell'elemento.</param>
      <exception cref="T:System.Xml.XmlException">È stata rilevata una stringa XML non corretta nel flusso di input.-oppure-Le proprietà <see cref="P:System.Xml.XmlReader.LocalName" /> e <see cref="P:System.Xml.XmlReader.NamespaceURI" /> dell'elemento trovato non corrispondono agli argomenti specificati.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ReadState">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene lo stato del lettore.</summary>
      <returns>Uno dei valori di enumerazione che specifica lo stato del lettore.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadSubtree">
      <summary>Restituisce una nuova istanza di XmlReader che può essere usata per leggere il nodo corrente e tutti i relativi discendenti.</summary>
      <returns>Nuova istanza del lettore XML impostata su <see cref="F:System.Xml.ReadState.Initial" />.La chiamata al metodo <see cref="M:System.Xml.XmlReader.Read" /> posiziona il nuovo lettore sul nodo che era il nodo corrente prima della chiamata al metodo <see cref="M:System.Xml.XmlReader.ReadSubtree" />.</returns>
      <exception cref="T:System.InvalidOperationException">Il lettore XML non è posizionato su un elemento quando viene chiamato questo metodo.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String)">
      <summary>Sposta l'oggetto <see cref="T:System.Xml.XmlReader" /> al successivo elemento discendente con il nome completo specificato.</summary>
      <returns>true se viene trovato un elemento discendente corrispondente; in caso contrario, false.Se non viene trovato un elemento figlio corrispondente, l'oggetto <see cref="T:System.Xml.XmlReader" /> viene posizionato in corrispondenza del tag di fine (<see cref="P:System.Xml.XmlReader.NodeType" /> è XmlNodeType.EndElement) dell'elemento.Se <see cref="T:System.Xml.XmlReader" /> non viene posizionato in corrispondenza di un elemento quando viene chiamato <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String)" />, questo metodo restituisce false e la posizione di <see cref="T:System.Xml.XmlReader" /> non viene modificata.</returns>
      <param name="name">Nome completo dell'elemento a cui spostarsi.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.ArgumentException">Il parametro è una stringa vuota.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)">
      <summary>Sposta l'oggetto <see cref="T:System.Xml.XmlReader" /> al successivo elemento discendente con il nome locale e l'URI dello spazio dei nomi specificati.</summary>
      <returns>true se viene trovato un elemento discendente corrispondente; in caso contrario, false.Se non viene trovato un elemento figlio corrispondente, l'oggetto <see cref="T:System.Xml.XmlReader" /> viene posizionato in corrispondenza del tag di fine (<see cref="P:System.Xml.XmlReader.NodeType" /> è XmlNodeType.EndElement) dell'elemento.Se <see cref="T:System.Xml.XmlReader" /> non viene posizionato in corrispondenza di un elemento quando viene chiamato <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)" />, questo metodo restituisce false e la posizione di <see cref="T:System.Xml.XmlReader" /> non viene modificata.</returns>
      <param name="localName">Nome locale dell'elemento a cui spostarsi.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento a cui spostarsi.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.ArgumentNullException">I valori di entrambi i parametri sono null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String)">
      <summary>Legge fino a trovare un elemento con il nome completo specificato.</summary>
      <returns>true se viene trovato un elemento corrispondente; in caso contrario, false e l'oggetto <see cref="T:System.Xml.XmlReader" /> si trova nello stato fine del file.</returns>
      <param name="name">Nome completo dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.ArgumentException">Il parametro è una stringa vuota.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String,System.String)">
      <summary>Legge fino a trovare un elemento con il nome locale e l'URI dello spazio dei nomi specificati.</summary>
      <returns>true se viene trovato un elemento corrispondente; in caso contrario, false e l'oggetto <see cref="T:System.Xml.XmlReader" /> si trova nello stato fine del file.</returns>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.ArgumentNullException">I valori di entrambi i parametri sono null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String)">
      <summary>Sposta l'oggetto XmlReader al successivo elemento di pari livello con il nome completo specificato.</summary>
      <returns>true se viene trovato un elemento di pari livello corrispondente; in caso contrario, false.Se non viene trovato un elemento corrispondente di pari livello, l'oggetto XmlReader viene posizionato in corrispondenza del tag di fine (<see cref="P:System.Xml.XmlReader.NodeType" /> è XmlNodeType.EndElement) dell'elemento padre.</returns>
      <param name="name">Nome completo dell'elemento di pari livello a cui spostarsi.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.ArgumentException">Il parametro è una stringa vuota.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String,System.String)">
      <summary>Sposta l'oggetto XmlReader al successivo elemento di pari livello con il nome locale e l'URI dello spazio dei nomi specificati.</summary>
      <returns>true se viene trovato un elemento di pari livello corrispondente; in caso contrario, false.Se non viene trovato un elemento corrispondente di pari livello, l'oggetto XmlReader viene posizionato in corrispondenza del tag di fine (<see cref="P:System.Xml.XmlReader.NodeType" /> è XmlNodeType.EndElement) dell'elemento padre.</returns>
      <param name="localName">Nome locale dell'elemento di pari livello a cui spostarsi.</param>
      <param name="namespaceURI">URI dello spazio dei nomi dell'elemento di pari livello a cui spostarsi.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.ArgumentNullException">I valori di entrambi i parametri sono null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)">
      <summary>Legge flussi di testo di grandi dimensioni incorporati in un documento XML.</summary>
      <returns>Numero di caratteri letti nel buffer.Quando non è più disponibile contenuto di testo, viene restituito il valore zero.</returns>
      <param name="buffer">Matrice di caratteri che funge da buffer in cui viene scritto il contenuto di testo.Questo valore non può essere null.</param>
      <param name="index">Offset all'interno del buffer in cui il <see cref="T:System.Xml.XmlReader" /> può iniziare a copiare i risultati.</param>
      <param name="count">Numero massimo di caratteri da copiare nel buffer.Il numero effettivo di caratteri copiati viene restituito da questo metodo.</param>
      <exception cref="T:System.InvalidOperationException">Il nodo corrente non ha un valore (<see cref="P:System.Xml.XmlReader.HasValue" /> è false).</exception>
      <exception cref="T:System.ArgumentNullException">Il valore <paramref name="buffer" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'indice nel buffer oppure la somma di indice e numero è superiore alla dimensione del buffer allocato.</exception>
      <exception cref="T:System.NotSupportedException">L'implementazione di <see cref="T:System.Xml.XmlReader" /> non supporta questo metodo.</exception>
      <exception cref="T:System.Xml.XmlException">Il formato dei dati XML non è corretto.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunkAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Legge in modo asincrono flussi di testo di grandi dimensioni incorporati in un documento XML.</summary>
      <returns>Numero di caratteri letti nel buffer.Quando non è più disponibile contenuto di testo, viene restituito il valore zero.</returns>
      <param name="buffer">Matrice di caratteri che funge da buffer in cui viene scritto il contenuto di testo.Questo valore non può essere null.</param>
      <param name="index">Offset all'interno del buffer in cui il <see cref="T:System.Xml.XmlReader" /> può iniziare a copiare i risultati.</param>
      <param name="count">Numero massimo di caratteri da copiare nel buffer.Il numero effettivo di caratteri copiati viene restituito da questo metodo.</param>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ResolveEntity">
      <summary>Quando ne viene eseguito l'override in una classe derivata, risolve il riferimento a entità per i nodi EntityReference.</summary>
      <exception cref="T:System.InvalidOperationException">Il lettore non è posizionato in corrispondenza di un nodo EntityReference; questa implementazione del lettore non consente di risolvere le entità, ovvero la proprietà <see cref="P:System.Xml.XmlReader.CanResolveEntity" /> restituisce il valore false.</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Settings">
      <summary>Ottiene l'oggetto <see cref="T:System.Xml.XmlReaderSettings" /> usato per creare questa istanza di <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlReaderSettings" /> usato per creare questa istanza del lettore.Se il lettore non è stato creato con il metodo <see cref="Overload:System.Xml.XmlReader.Create" />, la proprietà restituisce null.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Skip">
      <summary>Ignora gli elementi figlio del nodo corrente.</summary>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.SkipAsync">
      <summary>Ignora in modo asincrono gli elementi figlio del nodo corrente.</summary>
      <returns>Nodo corrente.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
      <exception cref="T:System.InvalidOperationException">Un metodo asincrono di <see cref="T:System.Xml.XmlReader" /> è stato chiamato senza impostare il flag di <see cref="P:System.Xml.XmlReaderSettings.Async" /> a true.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Impostare XmlReaderSettings.Async su true se si desidera utilizzare i metodi di Async."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Value">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene il valore del testo del nodo corrente.</summary>
      <returns>Il valore restituito dipende dalla proprietà <see cref="P:System.Xml.XmlReader.NodeType" /> del nodo.La tabella seguente elenca i tipi di nodo che hanno un valore da restituire.Tutti gli altri tipi di nodo restituiscono String.Empty.Tipo di nodo Valore AttributeValore dell'attributo. CDATAContenuto della sezione CDATA. CommentContenuto del commento. DocumentTypeSottoinsieme interno. ProcessingInstructionIntero contenuto, esclusa la destinazione. SignificantWhitespaceSpazio vuoto tra markup in un modello con contenuto misto. TextContenuto del nodo di testo. WhitespaceSpazio vuoto tra markup. XmlDeclarationContenuto della dichiarazione. </returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ValueType">
      <summary>Ottiene il tipo CLR (Common Language Runtime) per il nodo corrente.</summary>
      <returns>Tipo CLR che corrisponde al valore tipizzato del nodo.Il valore predefinito è System.String.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlLang">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene l'ambito xml:lang corrente.</summary>
      <returns>Ambito xml:lang corrente.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlSpace">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene l'ambito xml:space corrente.</summary>
      <returns>Uno dei valori di <see cref="T:System.Xml.XmlSpace" />.Se non esiste alcun ambito xml:space, alla proprietà viene applicata l'impostazione predefinita XmlSpace.None.</returns>
      <exception cref="T:System.InvalidOperationException">Un metodo di <see cref="T:System.Xml.XmlReader" /> è stato chiamato prima del completamento di un'operazione asincrona precedente.In questo caso, viene generata un'eccezione <see cref="T:System.InvalidOperationException" /> con il messaggio "Un'operazione asincrona è già in corso".</exception>
    </member>
    <member name="T:System.Xml.XmlReaderSettings">
      <summary>Specifica un set di funzionalità da supportare nell'oggetto <see cref="T:System.Xml.XmlReader" /> creato dal metodo <see cref="Overload:System.Xml.XmlReader.Create" />. </summary>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Xml.XmlReaderSettings" />.</summary>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.Async">
      <summary>Ottiene o imposta un valore che indica se è possibile usare metodi <see cref="T:System.Xml.XmlReader" /> asincroni in una determinata istanza di <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>true se i metodi asincroni possono essere usati; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CheckCharacters">
      <summary>Ottiene o imposta un valore che indica se eseguire il controllo dei caratteri.</summary>
      <returns>true per eseguire il controllo dei caratteri; in caso contrario, false.Il valore predefinito è true.NotaSe l'oggetto <see cref="T:System.Xml.XmlReader" /> elabora dati di testo, controlla sempre che i nomi XML e il contenuto del testo siano validi, indipendentemente dall'impostazione della proprietà.Se si imposta la proprietà <see cref="P:System.Xml.XmlReaderSettings.CheckCharacters" /> su false, il controllo dei caratteri per i riferimenti a entità carattere viene disattivato.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Clone">
      <summary>Crea una copia dell'istanza di <see cref="T:System.Xml.XmlReaderSettings" />.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlReaderSettings" /> clonato.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CloseInput">
      <summary>Ottiene o imposta un valore che indica se il flusso o la classe <see cref="T:System.IO.TextReader" /> sottostante devono essere chiusi alla chiusura del lettore.</summary>
      <returns>true per chiudere il flusso o l'oggetto <see cref="T:System.IO.TextReader" /> sottostante alla chiusura del lettore; in caso contrario, false.Il valore predefinito è false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.ConformanceLevel">
      <summary> Ottiene o imposta il livello di conformità dell'oggetto <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Uno dei valori di enumerazione che specifica il livello di conformità che verrà applicato dal lettore XML.Il valore predefinito è <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.DtdProcessing">
      <summary>Ottiene o imposta un valore che determina l'elaborazione di DTD.</summary>
      <returns>Uno dei valori di enumerazione che determina l'elaborazione di DTD.Il valore predefinito è <see cref="F:System.Xml.DtdProcessing.Prohibit" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreComments">
      <summary>Ottiene o imposta un valore che indica se ignorare i commenti.</summary>
      <returns>true per ignorare i commenti; in caso contrario, false.Il valore predefinito è false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreProcessingInstructions">
      <summary>Ottiene o imposta un valore che indica se ignorare le istruzioni di elaborazione.</summary>
      <returns>true per ignorare le istruzioni di elaborazione; in caso contrario, false.Il valore predefinito è false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreWhitespace">
      <summary>Ottiene o imposta un valore che indica se ignorare gli spazi vuoti non significativi.</summary>
      <returns>true per ignorare gli spazi vuoti; in caso contrario, false.Il valore predefinito è false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LineNumberOffset">
      <summary>Ottiene o imposta l'offset del numero di riga dell'oggetto <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Offset del numero di riga.Il valore predefinito è 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LinePositionOffset">
      <summary> Ottiene o imposta l'offset della posizione della riga dell'oggetto <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Offset della posizione della riga.Il valore predefinito è 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersFromEntities">
      <summary>Ottiene o imposta un valore che indica il numero massimo di caratteri consentito in un documento generato dall'espansione delle entità.</summary>
      <returns>Numero massimo consentito per i caratteri generati dalle entità espanse.Il valore predefinito è 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersInDocument">
      <summary>Ottiene o imposta un valore che indica il numero massimo di caratteri consentito in un documento XML.Un valore zero (0) indica che non è previsto alcun limite alla dimensione del documento XML.Un valore diverso da zero specifica la dimensione massima in caratteri.</summary>
      <returns>Numero massimo di caratteri consentito in un documento XML.Il valore predefinito è 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.NameTable">
      <summary>Ottiene o imposta l'oggetto <see cref="T:System.Xml.XmlNameTable" /> usato per il confronto delle stringhe atomizzate.</summary>
      <returns>Classe <see cref="T:System.Xml.XmlNameTable" /> che archivia tutte le stringhe atomizzate usate da tutte le istanze di <see cref="T:System.Xml.XmlReader" /> create tramite l'oggetto <see cref="T:System.Xml.XmlReaderSettings" />.Il valore predefinito è null.Se questo valore è null, l'istanza di <see cref="T:System.Xml.XmlReader" /> creata userà una nuova classe <see cref="T:System.Xml.NameTable" /> vuota.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Reset">
      <summary>Ripristina i valori predefiniti dei membri della classe delle impostazioni.</summary>
    </member>
    <member name="T:System.Xml.XmlSpace">
      <summary>Specifica l'ambito xml:space corrente.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Default">
      <summary>L'ambito xml:space è uguale a default.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.None">
      <summary>Nessun ambito xml:space.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Preserve">
      <summary>L'ambito xml:space è uguale a preserve.</summary>
    </member>
    <member name="T:System.Xml.XmlWriter">
      <summary>Rappresenta un writer che fornisce un modo veloce, non in cache e di tipo forward-only per generare flussi o i file contenenti dati XML.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlWriter" /> con il flusso specificato.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Flusso in cui scrivere.L'oggetto <see cref="T:System.Xml.XmlWriter" /> scrive la sintassi del testo di XML 1.0 e la aggiunge al flusso specificato.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream,System.Xml.XmlWriterSettings)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlWriter" /> con i flusso e l'oggetto <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Flusso in cui scrivere.L'oggetto <see cref="T:System.Xml.XmlWriter" /> scrive la sintassi del testo di XML 1.0 e la aggiunge al flusso specificato.</param>
      <param name="settings">Oggetto <see cref="T:System.Xml.XmlWriterSettings" /> usato per configurare la nuova istanza di <see cref="T:System.Xml.XmlWriter" />.Se è null, viene usato un oggetto <see cref="T:System.Xml.XmlWriterSettings" /> con le impostazioni predefinite.Se si usa l'oggetto <see cref="T:System.Xml.XmlWriter" /> con il metodo <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, è necessario usare la proprietà <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> per ottenere un oggetto <see cref="T:System.Xml.XmlWriterSettings" /> con le impostazioni corrette.In questo modo viene assicurato che le impostazioni di output dell'oggetto <see cref="T:System.Xml.XmlWriter" /> creato siano corrette.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlWriter" /> usando l'oggetto <see cref="T:System.IO.TextWriter" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Oggetto <see cref="T:System.IO.TextWriter" /> in cui scrivere.L'oggetto <see cref="T:System.Xml.XmlWriter" /> scrive la sintassi del testo di XML 1.0 e la aggiunge all'oggetto <see cref="T:System.IO.TextWriter" /> specificato.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter,System.Xml.XmlWriterSettings)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlWriter" /> usando gli oggetti <see cref="T:System.IO.TextWriter" /> e <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Oggetto <see cref="T:System.IO.TextWriter" /> in cui scrivere.L'oggetto <see cref="T:System.Xml.XmlWriter" /> scrive la sintassi del testo di XML 1.0 e la aggiunge all'oggetto <see cref="T:System.IO.TextWriter" /> specificato.</param>
      <param name="settings">Oggetto <see cref="T:System.Xml.XmlWriterSettings" /> usato per configurare la nuova istanza di <see cref="T:System.Xml.XmlWriter" />.Se è null, viene usato un oggetto <see cref="T:System.Xml.XmlWriterSettings" /> con le impostazioni predefinite.Se si usa l'oggetto <see cref="T:System.Xml.XmlWriter" /> con il metodo <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, è necessario usare la proprietà <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> per ottenere un oggetto <see cref="T:System.Xml.XmlWriterSettings" /> con le impostazioni corrette.In questo modo viene assicurato che le impostazioni di output dell'oggetto <see cref="T:System.Xml.XmlWriter" /> creato siano corrette.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlWriter" /> usando l'oggetto <see cref="T:System.Text.StringBuilder" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Oggetto <see cref="T:System.Text.StringBuilder" /> in cui scrivere.Il contenuto scritto dall'oggetto <see cref="T:System.Xml.XmlWriter" /> viene aggiunto all'oggetto <see cref="T:System.Text.StringBuilder" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder,System.Xml.XmlWriterSettings)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlWriter" /> usando gli oggetti <see cref="T:System.Text.StringBuilder" /> e <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Oggetto <see cref="T:System.Text.StringBuilder" /> in cui scrivere.Il contenuto scritto dall'oggetto <see cref="T:System.Xml.XmlWriter" /> viene aggiunto all'oggetto <see cref="T:System.Text.StringBuilder" />.</param>
      <param name="settings">Oggetto <see cref="T:System.Xml.XmlWriterSettings" /> usato per configurare la nuova istanza di <see cref="T:System.Xml.XmlWriter" />.Se è null, viene usato un oggetto <see cref="T:System.Xml.XmlWriterSettings" /> con le impostazioni predefinite.Se si usa l'oggetto <see cref="T:System.Xml.XmlWriter" /> con il metodo <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, è necessario usare la proprietà <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> per ottenere un oggetto <see cref="T:System.Xml.XmlWriterSettings" /> con le impostazioni corrette.In questo modo viene assicurato che le impostazioni di output dell'oggetto <see cref="T:System.Xml.XmlWriter" /> creato siano corrette.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlWriter" /> con l'oggetto <see cref="T:System.Xml.XmlWriter" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlWriter" /> che ha eseguito il wrapping dell'oggetto <see cref="T:System.Xml.XmlWriter" /> specificato.</returns>
      <param name="output">Oggetto <see cref="T:System.Xml.XmlWriter" /> da usare come writer sottostante.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter,System.Xml.XmlWriterSettings)">
      <summary>Crea una nuova istanza di <see cref="T:System.Xml.XmlWriter" /> con gli oggetti <see cref="T:System.Xml.XmlWriter" /> e <see cref="T:System.Xml.XmlWriterSettings" /> specificati.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlWriter" /> che ha eseguito il wrapping dell'oggetto <see cref="T:System.Xml.XmlWriter" /> specificato.</returns>
      <param name="output">Oggetto <see cref="T:System.Xml.XmlWriter" /> da usare come writer sottostante.</param>
      <param name="settings">Oggetto <see cref="T:System.Xml.XmlWriterSettings" /> usato per configurare la nuova istanza di <see cref="T:System.Xml.XmlWriter" />.Se è null, viene usato un oggetto <see cref="T:System.Xml.XmlWriterSettings" /> con le impostazioni predefinite.Se si usa l'oggetto <see cref="T:System.Xml.XmlWriter" /> con il metodo <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, è necessario usare la proprietà <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> per ottenere un oggetto <see cref="T:System.Xml.XmlWriterSettings" /> con le impostazioni corrette.In questo modo viene assicurato che le impostazioni di output dell'oggetto <see cref="T:System.Xml.XmlWriter" /> creato siano corrette.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose">
      <summary>Rilascia tutte le risorse usate dall'istanza corrente della classe <see cref="T:System.Xml.XmlWriter" />.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite usate da <see cref="T:System.Xml.XmlWriter" /> e, facoltativamente, le risorse gestite.</summary>
      <param name="disposing">true per rilasciare sia le risorse gestite sia quelle non gestite; false per rilasciare solo le risorse non gestite.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Flush">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scarica il contenuto del buffer nei flussi sottostanti e scarica anche il flusso sottostante.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.FlushAsync">
      <summary>Scarica in modo asincrono il contenuto del buffer nei flussi sottostanti e scarica anche il flusso sottostante.</summary>
      <returns>Attività che rappresenta l'operazione asincrona Flush.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.LookupPrefix(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, restituisce il prefisso più vicino definito nell'ambito dello spazio dei nomi corrente per l'URI dello spazio dei nomi.</summary>
      <returns>Prefisso corrispondente o null se nell'ambito corrente non viene trovato nessun URI dello spazio dei nomi corrispondente.</returns>
      <param name="ns">URI dello spazio dei nomi di cui trovare il prefisso.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ns" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.Settings">
      <summary>Ottiene l'oggetto <see cref="T:System.Xml.XmlWriterSettings" /> usato per creare questa istanza di <see cref="T:System.Xml.XmlWriter" />.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlWriterSettings" /> usato per creare questa istanza del writer.Se il writer non è stato creato usando il metodo <see cref="Overload:System.Xml.XmlWriter.Create" />, questa proprietà restituisce null.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributes(System.Xml.XmlReader,System.Boolean)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive tutti gli attributi che si trovano nella posizione corrente in <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="reader">Oggetto XmlReader dal quale copiare gli attributi.</param>
      <param name="defattr">true per copiare gli attributi predefiniti dall'oggetto XmlReader; in caso contrario, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null. </exception>
      <exception cref="T:System.Xml.XmlException">The reader is not positioned on an element, attribute or XmlDeclaration node. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributesAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>Scrive in modo asincrono tutti gli attributi che si trovano nella posizione corrente in <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteAttributes.</returns>
      <param name="reader">Oggetto XmlReader dal quale copiare gli attributi.</param>
      <param name="defattr">true per copiare gli attributi predefiniti dall'oggetto XmlReader; in caso contrario, false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive l'attributo con il nome locale e il valore specificati.</summary>
      <param name="localName">Nome locale dell'attributo.</param>
      <param name="value">Valore dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive un attributo con il nome locale, l'URI dello spazio dei nomi e il valore specificati.</summary>
      <param name="localName">Nome locale dell'attributo.</param>
      <param name="ns">URI dello spazio dei nomi da associare all'attributo.</param>
      <param name="value">Valore dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive l'attributo con il prefisso, il nome locale, l'URI dello spazio dei nomi e il valore specificati.</summary>
      <param name="prefix">Prefisso dello spazio dei nomi dell'attributo.</param>
      <param name="localName">Nome locale dell'attributo.</param>
      <param name="ns">URI dello spazio dei nomi dell'attributo.</param>
      <param name="value">Valore dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.Xml.XmlException">The <paramref name="localName" /> or <paramref name="ns" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Scrive in modo asincrono un attributo con il prefisso, il nome locale, l'URI dello spazio dei nomi e il valore specificati.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteAttributeString.</returns>
      <param name="prefix">Prefisso dello spazio dei nomi dell'attributo.</param>
      <param name="localName">Nome locale dell'attributo.</param>
      <param name="ns">URI dello spazio dei nomi dell'attributo.</param>
      <param name="value">Valore dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, codifica i byte binari specificati come valori Base64 e scrive il testo risultante.</summary>
      <param name="buffer">Matrice di byte da codificare.</param>
      <param name="index">Posizione nel buffer che indica l'inizio dei byte da scrivere.</param>
      <param name="count">Numero di byte da scrivere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Codifica in modo asincrono i byte binari specificati come valori Base64 e scrive il testo risultante.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteBase64.</returns>
      <param name="buffer">Matrice di byte da codificare.</param>
      <param name="index">Posizione nel buffer che indica l'inizio dei byte da scrivere.</param>
      <param name="count">Numero di byte da scrivere.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Quando ne viene eseguito l'override in una classe derivata,codifica i byte binari specificati come BinHex e scrive il testo risultante.</summary>
      <param name="buffer">Matrice di byte da codificare.</param>
      <param name="index">Posizione nel buffer che indica l'inizio dei byte da scrivere.</param>
      <param name="count">Numero di byte da scrivere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Codifica in modo asincrono i byte binari specificati come BinHex e scrive il testo risultante.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteBinHex.</returns>
      <param name="buffer">Matrice di byte da codificare.</param>
      <param name="index">Posizione nel buffer che indica l'inizio dei byte da scrivere.</param>
      <param name="count">Numero di byte da scrivere.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCData(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive un blocco &lt;![CDATA[...]]&gt; che contiene il testo specificato.</summary>
      <param name="text">Testo da inserire all'interno del blocco CDATA.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCDataAsync(System.String)">
      <summary>Scrive in modo asincrono un blocco &lt;![CDATA[...]]&gt; che contiene il testo specificato.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteCData.</returns>
      <param name="text">Testo da inserire all'interno del blocco CDATA.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntity(System.Char)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, forza la generazione di un'entità carattere per il valore del carattere Unicode specificato.</summary>
      <param name="ch">Carattere Unicode per cui generare l'entità carattere.</param>
      <exception cref="T:System.ArgumentException">The character is in the surrogate pair character range, 0xd800 - 0xdfff.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntityAsync(System.Char)">
      <summary>Forza in modo asincrono la generazione di un'entità carattere per il valore del carattere Unicode specificato.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteCharEntity.</returns>
      <param name="ch">Carattere Unicode per cui generare l'entità carattere.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteChars(System.Char[],System.Int32,System.Int32)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive il testo in un buffer alla volta.</summary>
      <param name="buffer">Matrice di caratteri che contiene il testo da scrivere.</param>
      <param name="index">Posizione nel buffer che indica l'inizio del testo da scrivere.</param>
      <param name="count">Numero di caratteri da scrivere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />; the call results in surrogate pair characters being split or an invalid surrogate pair being written.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="buffer" /> parameter value is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharsAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Scrive in modo asincrono il testo in un buffer alla volta.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteChars.</returns>
      <param name="buffer">Matrice di caratteri che contiene il testo da scrivere.</param>
      <param name="index">Posizione nel buffer che indica l'inizio del testo da scrivere.</param>
      <param name="count">Numero di caratteri da scrivere.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteComment(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive un commento &lt;!--...--&gt; che contiene il testo specificato.</summary>
      <param name="text">Testo da inserire nel commento.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well-formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCommentAsync(System.String)">
      <summary>Scrive in modo asincrono un commento &lt;!--...--&gt; che contiene il testo specificato.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteComment.</returns>
      <param name="text">Testo da inserire nel commento.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocType(System.String,System.String,System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive la dichiarazione DOCTYPE con il nome e gli attributi facoltativi specificati.</summary>
      <param name="name">Nome per la dichiarazione DOCTYPE.Questo parametro non deve essere vuoto.</param>
      <param name="pubid">Se diverso da Null, scrive anche PUBLIC "pubid" "sysid", dove <paramref name="pubid" /> e <paramref name="sysid" /> vengono sostituiti con il valore degli argomenti specificati.</param>
      <param name="sysid">Se <paramref name="pubid" /> è null e <paramref name="sysid" /> è diverso da Null, scrive SYSTEM "sysid", dove <paramref name="sysid" /> viene sostituito dal valore di questo argomento.</param>
      <param name="subset">Se diverso da Null, scrive [subset], che viene sostituito dal valore di questo argomento.</param>
      <exception cref="T:System.InvalidOperationException">This method was called outside the prolog (after the root element). </exception>
      <exception cref="T:System.ArgumentException">The value for <paramref name="name" /> would result in invalid XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocTypeAsync(System.String,System.String,System.String,System.String)">
      <summary>Scrive in modo asincrono la dichiarazione DOCTYPE con il nome e gli attributi facoltativi specificati.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteDocType.</returns>
      <param name="name">Nome per la dichiarazione DOCTYPE.Questo parametro non deve essere vuoto.</param>
      <param name="pubid">Se diverso da Null, scrive anche PUBLIC "pubid" "sysid", dove <paramref name="pubid" /> e <paramref name="sysid" /> vengono sostituiti con il valore degli argomenti specificati.</param>
      <param name="sysid">Se <paramref name="pubid" /> è null e <paramref name="sysid" /> è diverso da Null, scrive SYSTEM "sysid", dove <paramref name="sysid" /> viene sostituito dal valore di questo argomento.</param>
      <param name="subset">Se diverso da Null, scrive [subset], che viene sostituito dal valore di questo argomento.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String)">
      <summary>Scrive un elemento con il nome locale e il valore specificati.</summary>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="value">Valore dell'elemento.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String)">
      <summary>Scrive un elemento con il nome locale, l'URI dello spazio dei nomi e il valore specificati.</summary>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="ns">URI dello spazio dei nomi da associare all'elemento.</param>
      <param name="value">Valore dell'elemento.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String,System.String)">
      <summary>Scrive un elemento con il prefisso, il nome locale, l'URI dello spazio dei nomi e il valore specificati.</summary>
      <param name="prefix">Prefisso dell'elemento.</param>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="ns">URI dello spazio dei nomi dell'elemento.</param>
      <param name="value">Valore dell'elemento.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Scrive in modo asincrono un elemento con il prefisso, il nome locale, l'URI dello spazio dei nomi e il valore specificati.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteElementString.</returns>
      <param name="prefix">Prefisso dell'elemento.</param>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="ns">URI dello spazio dei nomi dell'elemento.</param>
      <param name="value">Valore dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttribute">
      <summary>Quando ne viene eseguito l'override in una classe derivata, chiude la chiamata a <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> precedente.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttributeAsync">
      <summary>Chiude in modo asincrono la chiamata a <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> precedente.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteEndAttribute.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocument">
      <summary>Quando se ne esegue l'override in una classe derivata, chiude qualsiasi elemento o attributo aperto e riporta il writer allo stato di avvio.</summary>
      <exception cref="T:System.ArgumentException">The XML document is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocumentAsync">
      <summary>Chiude in modo asincrono qualsiasi elemento o attributo aperto e riporta il writer allo stato di avvio.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteEndDocument.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElement">
      <summary>Quando ne viene eseguito l'override in una classe derivata, chiude un elemento e visualizza l'ambito dello spazio dei nomi corrispondente.</summary>
      <exception cref="T:System.InvalidOperationException">This results in an invalid XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElementAsync">
      <summary>Chiude in modo asincrono un elemento e visualizza l'ambito dello spazio dei nomi corrispondente.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteEndElement.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRef(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive un riferimento a entità come &amp;name;.</summary>
      <param name="name">Nome del riferimento a entità.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRefAsync(System.String)">
      <summary>Scrive in modo asincrono un riferimento a entità come &amp;name;.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteEntityRef.</returns>
      <param name="name">Nome del riferimento a entità.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElement">
      <summary>Quando ne viene eseguito l'override in una classe derivata, chiude un elemento e visualizza l'ambito dello spazio dei nomi corrispondente.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElementAsync">
      <summary>Chiude in modo asincrono un elemento e visualizza l'ambito dello spazio dei nomi corrispondente.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteFullEndElement.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteName(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, inserisce il nome specificato, verificando che si tratti di un nome valido in base alla raccomandazione W3C XML 1.0, disponibile all'indirizzo http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name.</summary>
      <param name="name">Nome da scrivere.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid XML name; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNameAsync(System.String)">
      <summary>Inserisce in modo asincrono il nome specificato, verificando che si tratti di un nome valido in base alla raccomandazione W3C XML 1.0, disponibile all'indirizzo http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteName.</returns>
      <param name="name">Nome da scrivere.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmToken(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, inserisce il nome specificato, verificando che si tratti di un oggetto NmToken valido in base alla raccomandazione W3C XML 1.0, disponibile all'indirizzo http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name.</summary>
      <param name="name">Nome da scrivere.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid NmToken; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmTokenAsync(System.String)">
      <summary>Inserisce in modo asincrono il nome specificato, verificando che si tratti di un NmToken valido in base alla raccomandazione W3C XML 1.0, disponibile all'indirizzo http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteNmToken.</returns>
      <param name="name">Nome da scrivere.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNode(System.Xml.XmlReader,System.Boolean)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, copia tutto il contenuto del lettore nel writer e sposta il lettore all'inizio del successivo elemento di pari livello.</summary>
      <param name="reader">Oggetto <see cref="T:System.Xml.XmlReader" /> da cui leggere.</param>
      <param name="defattr">true per copiare gli attributi predefiniti dall'oggetto XmlReader; in caso contrario, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains invalid characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNodeAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>Copia in modo asincrono tutto il contenuto del lettore nel writer e sposta il lettore sul successivo elemento di pari livello.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteNode.</returns>
      <param name="reader">Oggetto <see cref="T:System.Xml.XmlReader" /> da cui leggere.</param>
      <param name="defattr">true per copiare gli attributi predefiniti dall'oggetto XmlReader; in caso contrario, false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstruction(System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive un'istruzione di elaborazione con uno spazio tra il nome e il testo, come segue: &lt;?nome testo?&gt;.</summary>
      <param name="name">Nome dell'istruzione di elaborazione.</param>
      <param name="text">Testo da includere nell'istruzione di elaborazione.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.<paramref name="name" /> is either null or String.Empty.This method is being used to create an XML declaration after <see cref="M:System.Xml.XmlWriter.WriteStartDocument" /> has already been called. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstructionAsync(System.String,System.String)">
      <summary>Scrive in modo asincrono un'istruzione di elaborazione con uno spazio tra il nome e il testo, come segue: &lt;?nome testo?&gt;.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteProcessingInstruction.</returns>
      <param name="name">Nome dell'istruzione di elaborazione.</param>
      <param name="text">Testo da includere nell'istruzione di elaborazione.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedName(System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive il nome completo dello spazio dei nomi.Questo metodo esegue la ricerca del prefisso incluso nell'ambito dello spazio dei nomi specificato.</summary>
      <param name="localName">Nome locale da scrivere.</param>
      <param name="ns">URI dello spazio dei nomi del nome.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="localName" /> is either null or String.Empty.<paramref name="localName" /> is not a valid name. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedNameAsync(System.String,System.String)">
      <summary>Scrive in modo asincrono il nome completo dello spazio dei nomi.Questo metodo esegue la ricerca del prefisso incluso nell'ambito dello spazio dei nomi specificato.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteQualifiedName.</returns>
      <param name="localName">Nome locale da scrivere.</param>
      <param name="ns">URI dello spazio dei nomi del nome.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.Char[],System.Int32,System.Int32)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive manualmente markup non elaborato in base a un buffer di caratteri.</summary>
      <param name="buffer">Matrice di caratteri che contiene il testo da scrivere.</param>
      <param name="index">Posizione all'interno del buffer che indica l'inizio del testo da scrivere.</param>
      <param name="count">Numero di caratteri da scrivere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive manualmente markup non elaborato in base a una stringa.</summary>
      <param name="data">Stringa contenente il testo da scrivere.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Scrive manualmente in modo asincrono markup non elaborato in base a un buffer di caratteri.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteRaw.</returns>
      <param name="buffer">Matrice di caratteri che contiene il testo da scrivere.</param>
      <param name="index">Posizione all'interno del buffer che indica l'inizio del testo da scrivere.</param>
      <param name="count">Numero di caratteri da scrivere.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.String)">
      <summary>Scrive manualmente in modo asincrono markup non elaborato in base a una stringa.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteRaw.</returns>
      <param name="data">Stringa contenente il testo da scrivere.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String)">
      <summary>Scrive l'inizio di un attributo con il nome locale specificato.</summary>
      <param name="localName">Nome locale dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)">
      <summary>Scrive l'inizio di un attributo con il nome locale e l'URI dello spazio dei nomi specificati.</summary>
      <param name="localName">Nome locale dell'attributo.</param>
      <param name="ns">URI dello spazio dei nomi dell'attributo.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive l'inizio di un attributo con il prefisso, il nome locale e l'URI dello spazio dei nomi specificati.</summary>
      <param name="prefix">Prefisso dello spazio dei nomi dell'attributo.</param>
      <param name="localName">Nome locale dell'attributo.</param>
      <param name="ns">URI dello spazio dei nomi dell'attributo.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttributeAsync(System.String,System.String,System.String)">
      <summary>Scrive in modo asincrono l'inizio di un attributo con il prefisso, il nome locale e l'URI dello spazio dei nomi specificati.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteStartAttribute.</returns>
      <param name="prefix">Prefisso dello spazio dei nomi dell'attributo.</param>
      <param name="localName">Nome locale dell'attributo.</param>
      <param name="ns">URI dello spazio dei nomi dell'attributo.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive la dichiarazione XML in base alla versione "1.0".</summary>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument(System.Boolean)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive la dichiarazione XML in base alla versione "1.0" e all'attributo standalone.</summary>
      <param name="standalone">Se true, scrive "standalone=yes"; se false, scrive "standalone=no".</param>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync">
      <summary>Scrive in modo asincrono la dichiarazione XML con la versione "1.0".</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteStartDocument.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync(System.Boolean)">
      <summary>Scrive in modo asincrono la dichiarazione XML con la versione "1.0" e l'attributo standalone.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteStartDocument.</returns>
      <param name="standalone">Se true, scrive "standalone=yes"; se false, scrive "standalone=no".</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive un tag di inizio con il nome locale specificato.</summary>
      <param name="localName">Nome locale dell'elemento.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive il tag di inizio specificato e lo associa allo spazio dei nomi indicato.</summary>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="ns">URI dello spazio dei nomi da associare all'elemento.Se questo spazio dei nomi si trova già all'interno dell'ambito ed è associato a un prefisso, il writer scriverà automaticamente anche tale prefisso.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String,System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive il tag di inizio specificato e lo associa allo spazio dei nomi e al prefisso specificati.</summary>
      <param name="prefix">Prefisso dello spazio dei nomi dell'elemento.</param>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="ns">URI dello spazio dei nomi da associare all'elemento.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElementAsync(System.String,System.String,System.String)">
      <summary>Scrive in modo asincrono il tag di inizio specificato e lo associa allo spazio dei nomi e al prefisso specificati.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteStartElement.</returns>
      <param name="prefix">Prefisso dello spazio dei nomi dell'elemento.</param>
      <param name="localName">Nome locale dell'elemento.</param>
      <param name="ns">URI dello spazio dei nomi da associare all'elemento.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.WriteState">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene lo stato del writer.</summary>
      <returns>Uno dei valori di <see cref="T:System.Xml.WriteState" />.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteString(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive il contenuto di testo specificato.</summary>
      <param name="text">Testo da scrivere.</param>
      <exception cref="T:System.ArgumentException">The text string contains an invalid surrogate pair.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStringAsync(System.String)">
      <summary>Scrive in modo asincrono il contenuto di testo specificato.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteString.</returns>
      <param name="text">Testo da scrivere.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntity(System.Char,System.Char)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, genera e scrive l'entità carattere surrogata per la coppia di caratteri surrogati.</summary>
      <param name="lowChar">Surrogato basso.Deve essere un valore compreso tra 0xDC00 e 0xDFFF.</param>
      <param name="highChar">Surrogato alto.Deve essere un valore compreso tra 0xD800 e 0xDBFF.</param>
      <exception cref="T:System.ArgumentException">An invalid surrogate character pair was passed.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntityAsync(System.Char,System.Char)">
      <summary>Genera in modo asincrono e scrive l'entità carattere surrogata per la coppia di caratteri surrogati.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteSurrogateCharEntity.</returns>
      <param name="lowChar">Surrogato basso.Deve essere un valore compreso tra 0xDC00 e 0xDFFF.</param>
      <param name="highChar">Surrogato alto.Deve essere un valore compreso tra 0xD800 e 0xDBFF.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Boolean)">
      <summary>Scrive un valore <see cref="T:System.Boolean" />.</summary>
      <param name="value">Valore <see cref="T:System.Boolean" /> da scrivere.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.DateTimeOffset)">
      <summary>Scrive un valore <see cref="T:System.DateTimeOffset" />.</summary>
      <param name="value">Valore <see cref="T:System.DateTimeOffset" /> da scrivere.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Decimal)">
      <summary>Scrive un valore <see cref="T:System.Decimal" />.</summary>
      <param name="value">Valore <see cref="T:System.Decimal" /> da scrivere.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Double)">
      <summary>Scrive un valore <see cref="T:System.Double" />.</summary>
      <param name="value">Valore <see cref="T:System.Double" /> da scrivere.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int32)">
      <summary>Scrive un valore <see cref="T:System.Int32" />.</summary>
      <param name="value">Valore <see cref="T:System.Int32" /> da scrivere.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int64)">
      <summary>Scrive un valore <see cref="T:System.Int64" />.</summary>
      <param name="value">Valore <see cref="T:System.Int64" /> da scrivere.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Object)">
      <summary>Scrive il valore dell'oggetto.</summary>
      <param name="value">Valore dell'oggetto da scrivere.Nota   In .NET Framework 3.5 questo metodo accetta <see cref="T:System.DateTimeOffset" /> come parametro.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Single)">
      <summary>Scrive un numero a virgola mobile e precisione singola.</summary>
      <param name="value">Numero a virgola mobile e precisione singola da scrivere.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.String)">
      <summary>Scrive un valore <see cref="T:System.String" />.</summary>
      <param name="value">Valore <see cref="T:System.String" /> da scrivere.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespace(System.String)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, scrive lo spazio specificato.</summary>
      <param name="ws">Stringa di caratteri spazio vuoto.</param>
      <exception cref="T:System.ArgumentException">The string contains non-white space characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespaceAsync(System.String)">
      <summary>Scrive in modo asincrono lo spazio vuoto specificato.</summary>
      <returns>Attività che rappresenta l'operazione asincrona WriteWhitespace.</returns>
      <param name="ws">Stringa di caratteri spazio vuoto.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlLang">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene l'ambito xml:lang corrente.</summary>
      <returns>Ambito xml:lang corrente.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlSpace">
      <summary>Quando ne viene eseguito l'override in una classe derivata, ottiene un oggetto <see cref="T:System.Xml.XmlSpace" /> che rappresenta l'ambito xml:space corrente.</summary>
      <returns>Oggetto XmlSpace che rappresenta l'ambito xml:space corrente.Valore Significato NoneValore predefinito se non esistono ambiti xml:space.DefaultL'ambito corrente è xml:space="default".PreserveL'ambito corrente è xml:space="preserve".</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="T:System.Xml.XmlWriterSettings">
      <summary>Specifica un set di funzionalità da supportare nell'oggetto <see cref="T:System.Xml.XmlWriter" /> creato dal metodo <see cref="Overload:System.Xml.XmlWriter.Create" />.</summary>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Async">
      <summary>Ottiene o imposta un valore che indica se è possibile usare i metodi <see cref="T:System.Xml.XmlWriter" /> asincroni in una specifica istanza di <see cref="T:System.Xml.XmlWriter" />.</summary>
      <returns>true se i metodi asincroni possono essere usati; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CheckCharacters">
      <summary>Ottiene o imposta un valore che indica se il writer XML deve verificare la conformità di tutti i caratteri nel documento alla sezione "2.2 Characters" della specifica W3C XML 1.0 Recommendation.</summary>
      <returns>true per eseguire il controllo dei caratteri; in caso contrario, false.Il valore predefinito è true.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Clone">
      <summary>Crea una copia dell'istanza di <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Oggetto <see cref="T:System.Xml.XmlWriterSettings" /> clonato.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CloseOutput">
      <summary>Ottiene o imposta un valore che indica se l'oggetto <see cref="T:System.Xml.XmlWriter" /> deve anche chiudere il flusso sottostante o <see cref="T:System.IO.TextWriter" /> quando viene chiamato il metodo <see cref="M:System.Xml.XmlWriter.Close" />.</summary>
      <returns>true per chiudere anche il flusso sottostante o <see cref="T:System.IO.TextWriter" />; in caso contrario, false.Il valore predefinito è false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.ConformanceLevel">
      <summary>Ottiene o imposta il livello di conformità per cui il writer XML controlla l'output XML.</summary>
      <returns>Uno dei valori di enumerazione che specifica il livello di conformità (documento, frammento o rilevamento automatico).Il valore predefinito è <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Encoding">
      <summary>Ottiene o imposta il tipo di codifica testo da usare.</summary>
      <returns>Codifica testo da usare.Il valore predefinito è Encoding.UTF8.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Indent">
      <summary>Ottiene o imposta un valore che indica se impostare il rientro di elementi.</summary>
      <returns>true per scrivere singoli elementi su nuove righe e applicare il rientro; in caso contrario, false.Il valore predefinito è false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.IndentChars">
      <summary>Ottiene o imposta la stringa di caratteri da usare per il rientro.Questa impostazione viene usata quando la proprietà <see cref="P:System.Xml.XmlWriterSettings.Indent" /> è impostata su true.</summary>
      <returns>Stringa di caratteri da usare per il rientro.Può essere impostata su qualsiasi valore stringa.Tuttavia, per essere sicuri che l'XML sia valido, specificare solo spazi vuoti validi, ad esempio spazi, tabulazioni, ritorni a capo o avanzamenti riga.Il valore predefinito è due spazi.</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.IndentChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NamespaceHandling">
      <summary>Ottiene o imposta un valore che indica se <see cref="T:System.Xml.XmlWriter" /> deve rimuovere le dichiarazioni dello spazio dei nomi duplicati quando viene scritto contenuto XML.Il comportamento predefinito del writer è restituire tutte le dichiarazioni dello spazio dei nomi presenti nel resolver dello spazio dei nomi del writer.</summary>
      <returns>Enumerazione <see cref="T:System.Xml.NamespaceHandling" /> usata per specificare se rimuovere le dichiarazioni dello spazio dei nomi duplicate in <see cref="T:System.Xml.XmlWriter" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineChars">
      <summary>Ottiene o imposta la stringa di caratteri da usare per le interruzioni di riga.</summary>
      <returns>Stringa di caratteri da usare per le interruzioni di riga.Può essere impostata su qualsiasi valore stringa.Tuttavia, per essere sicuri che l'XML sia valido, specificare solo spazi vuoti validi, ad esempio spazi, tabulazioni, ritorni a capo o avanzamenti riga.Il valore predefinito è \r\n (ritorno a capo, nuova riga).</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineHandling">
      <summary>Ottiene o imposta un valore che indica se le interruzioni di riga devono essere normalizzate nell'output.</summary>
      <returns>Uno dei valori di <see cref="T:System.Xml.NewLineHandling" />.Il valore predefinito è <see cref="F:System.Xml.NewLineHandling.Replace" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineOnAttributes">
      <summary>Ottiene o imposta un valore che indica se scrivere gli attributi su una nuova riga.</summary>
      <returns>true per scrivere gli attributi su una nuova riga; in caso contrario, false.Il valore predefinito è false.NotaQuesta impostazione non ha effetto se il valore della proprietà <see cref="P:System.Xml.XmlWriterSettings.Indent" /> è false.Se il valore di <see cref="P:System.Xml.XmlWriterSettings.NewLineOnAttributes" /> è impostato su true, ogni attributo viene preceduto da una nuova riga e da un livello aggiuntivo di rientro.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.OmitXmlDeclaration">
      <summary>Ottiene o imposta un valore che indica se omettere una dichiarazione XML.</summary>
      <returns>true per omettere la dichiarazione XML; in caso contrario, false.Il valore predefinito false significa che viene scritta una dichiarazione XML.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Reset">
      <summary>Ripristina i valori predefiniti dei membri della classe delle impostazioni.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.WriteEndDocumentOnClose">
      <summary>Ottiene o imposta un valore che indica se <see cref="T:System.Xml.XmlWriter" /> aggiungerà tag di chiusura a tutti i tag di elemento senza chiusura quando viene chiamato metodo <see cref="M:System.Xml.XmlWriter.Close" />.</summary>
      <returns>true se tutti i tag di elemento senza chiusura verranno chiusi; in caso contrario, false.Il valore predefinito è true.</returns>
    </member>
    <member name="T:System.Xml.Schema.XmlSchema">
      <summary>Rappresentazione in memoria di un XML Schema, come descritto nelle specifiche di World Wide Web Consortium (W3C) XML Schema Part 1: Structures e XML Schema Part 2: Datatypes.</summary>
    </member>
    <member name="T:System.Xml.Schema.XmlSchemaForm">
      <summary>Indica se gli attributi o gli elementi devono essere qualificati con un prefisso di uno spazio dei nomi.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.None">
      <summary>La forma dell'attributo e dell'elemento non è specificata nello schema.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Qualified">
      <summary>Gli elementi e gli attributi devono essere qualificati con un prefisso di uno spazio dei nomi.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Unqualified">
      <summary>Non occorre che gli attributi e gli elementi siano qualificati con un prefisso di uno spazio dei nomi.</summary>
    </member>
    <member name="T:System.Xml.Serialization.IXmlSerializable">
      <summary>Fornisce una formattazione personalizzata per la serializzazione e la deserializzazione XML.</summary>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.GetSchema">
      <summary>Il metodo è riservato e non deve essere utilizzato.Quando si implementa l'interfaccia IXmlSerializable, è necessario restituire null (Nothing in Visual Basic) da questo metodo. Se invece è richiesta la specifica di uno schema personalizzato, applicare <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> alla classe.</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchema" /> che descrive la rappresentazione XML dell'oggetto generato dal metodo <see cref="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)" /> e utilizzato dal metodo <see cref="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)">
      <summary>Genera un oggetto dalla relativa rappresentazione XML.</summary>
      <param name="reader">Flusso di <see cref="T:System.Xml.XmlReader" /> da cui viene deserializzato l'oggetto. </param>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)">
      <summary>Converte un oggetto nella relativa rappresentazione XML.</summary>
      <param name="writer">Flusso di <see cref="T:System.Xml.XmlWriter" /> nel quale viene serializzato l'oggetto. </param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaProviderAttribute">
      <summary>Quando viene applicata a un tipo, archivia il nome di un metodo statico del tipo che restituisce uno schema XML e una classe <see cref="T:System.Xml.XmlQualifiedName" /> (o <see cref="T:System.Xml.Schema.XmlSchemaType" /> per i tipi anonimi) che controlla la serializzazione del tipo.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaProviderAttribute.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> utilizzando il nome del metodo statico che fornisce lo schema XML del tipo.</summary>
      <param name="methodName">Nome del metodo statico che deve essere implementato.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.IsAny">
      <summary>Ottiene o imposta un valore che determina se la classe di destinazione è un carattere jolly o lo schema della classe contiene solo un elemento xs:any.</summary>
      <returns>true se la classe è un carattere jolly o se lo schema contiene solo l'elemento xs:any; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.MethodName">
      <summary>Ottiene il nome del metodo statico che fornisce lo schema XML del tipo e il nome del relativo tipo di dati XML Schema.</summary>
      <returns>Nome del metodo che viene richiamato dall'infrastruttura XML per restituire uno schema XML.</returns>
    </member>
  </members>
</doc>