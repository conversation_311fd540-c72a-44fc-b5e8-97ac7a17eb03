﻿Imports System.ComponentModel
Imports System.Data.SqlClient
Imports System.IO
Imports System.Reflection
Imports Chilkat
Imports DevExpress.XtraEditors
Imports MicrovisionActivation
Public Class frmActivationInfo
    Dim chgOnLineActiveConn As New SqlConnection
    Dim oldActivaitionModule_Vaule As String = ""
    Dim ChngFlg As String = ""
    Sub calRemainTrailPeriod()
        Dim r1 = DateTime.Parse(Now)
        Dim r2 = DateTime.Parse(ActivationExpireDate)
        Dim result = r2 - r1
        Dim Remain_ = MicrovisionActivation.Sec_Ref.FormatTimeSpan(result)
        If Remain_ = "" Then
            LBLREMAINDAYS.Text = "Trail Lincense is Expired "
            Me.Hide()
            'StoreDataSet.Delete()
            XtraMessageBox.Show("Trail License is Expired..Closing in...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Process.GetCurrentProcess().Kill()
        Else
            LBLREMAINDAYS.Text = Remain_
        End If
    End Sub

    Private Sub frmActivationInfo_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Opacity = 0
        TrmFadeIn.Enabled = True
        Dim exePath As String = Application.StartupPath & "\Best Sender.exe"
        Dim exeDate As DateTime = File.GetLastWriteTime(exePath)
        lblVersion.Tag = "Version: " & Application.ProductVersion & " (" & exeDate.ToString("dd/MM/yyyy") & ") "
        lblVersion.Text = lblVersion.Tag.ToString()
        LBLACTIVATIONOWNER.Text = GlobalVariables.ActivationKeyOwner
        LBLPCSERIAL.Text = GlobalVariables.MachineSerial
        LBLACTIVATIONCODE.Text = GlobalVariables.ActivationKeyCode
        oldActivaitionModule_Vaule = ActivationModule
        lbllic.Text = GET_LICENSE_PLAN_STRING(ActivationModule)
        ProgressPanel1.Visible = False
        If ActivateionType = "Full Version" Then
            lblVersion.Text = lblVersion.Tag.ToString() & " Premium "
            LBLACTIVATIONTYPE.Text = "Full Version"
            Label6.ForeColor = Color.FromArgb(78, 78, 78)
            Label3.ForeColor = Color.FromArgb(78, 78, 78)
            LBLEXPIREDATE.ForeColor = Color.FromArgb(78, 78, 78)
            LBLREMAINDAYS.ForeColor = Color.FromArgb(78, 78, 78)
        ElseIf ActivateionType = "Trail Version" Then
            lblVersion.Text = lblVersion.Tag.ToString() & " Trail Version "
            LBLACTIVATIONTYPE.Text = "Trail Version"
            Label6.ForeColor = Color.FromArgb(224, 224, 224)
            Label3.ForeColor = Color.FromArgb(224, 224, 224)
            LBLEXPIREDATE.ForeColor = Color.FromArgb(224, 224, 224)
            LBLREMAINDAYS.ForeColor = Color.FromArgb(224, 224, 224)
            calRemainTrailPeriod()
            LBLEXPIREDATE.Text = String.Format("{0:dd/MM/yyyy}", GlobalVariables.ActivationExpireDate)
            Timer1.Enabled = True
        End If
        If ActivationStatus = True Then
            LBLACTIVATIONSTATUS.Text = "Valid"
        ElseIf ActivationStatus = False Then
            LBLACTIVATIONSTATUS.Text = "Invalid"
        End If

        ' إضافة معالج حدث لتغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmActivationInfo_Resize

        ' توسيط المجموعة عند التحميل
        CenterGroupControl()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmActivationInfo_Resize(sender As Object, e As EventArgs)
        ' توسيط المجموعة عند تغيير حجم النموذج
        CenterGroupControl()
    End Sub

    ''' <summary>
    ''' توسيط المجموعة في النموذج
    ''' </summary>
    Private Sub CenterGroupControl()
        Try
            ' التحقق من أن النموذج مهيأ بشكل كامل
            If Not Me.IsHandleCreated OrElse Me.IsDisposed Then
                Return
            End If

            ' توسيط GroupControl6 في النموذج
            If GroupControl6 IsNot Nothing Then
                ' حساب الموقع الجديد لتوسيط المجموعة
                Dim newX As Integer = (Me.ClientSize.Width - GroupControl6.Width) \ 2
                Dim newY As Integer = (Me.ClientSize.Height - GroupControl6.Height) \ 2

                ' تعيين الموقع الجديد
                GroupControl6.Location = New Point(newX, newY)
            End If

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in CenterGroupControl: {ex.Message}")
        End Try
    End Sub
    Private Sub frmActivationInfo_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        Timer1.Enabled = False
    End Sub
    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        calRemainTrailPeriod()
    End Sub
    Private Sub HyperLinkEdit1_OpenLink(sender As Object, e As Controls.OpenLinkEventArgs)
        Process.Start(HyperLinkEdit1.Text)
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        If CheckForInternetConnection() = False Then
            Exit Sub
        End If
        If chgOnLineActiveConn.State = 1 Then chgOnLineActiveConn.Close()
        Try
            chgOnLineActiveConn.ConnectionString = My.Settings.appver
            chgOnLineActiveConn.Open()
            Dim Adp As New SqlClient.SqlDataAdapter
            Dim DT As New DataTable
            Dim SqlCmd As New SqlClient.SqlCommand("Select * from ActivationCodes where ActivationCode=N'" & ActivationKeyCode & "'", chgOnLineActiveConn)
            Adp = New SqlClient.SqlDataAdapter(SqlCmd)
            DT = New DataTable
            Adp.Fill(DT)
            If DT.Rows.Count > 0 Then
                If DT.Rows(0).Item("ActivationModule") = oldActivaitionModule_Vaule Then
                    ChngFlg = "SAME"
                    chgOnLineActiveConn.Close()
                    Exit Try
                End If
                ActivationModule = DT.Rows(0).Item("ActivationModule")
                If IsDBNull(ActivationModule) Or ActivationModule = "" Or ActivationModule = String.Empty Then ActivationModule = "None"
                StoreDataSet.UpdateActivationModule(ActivationModule)
                '======================== SystemModulePermission
                DT.Rows(0).Item("PermissionUpdated") = True
                Dim cmd As New SqlClient.SqlCommandBuilder(Adp)
                Adp.Update(DT)
                DT.Dispose()
                Adp.Dispose()
                cmd.Dispose()
                '===============================================
            End If
            ChngFlg = ""
            chgOnLineActiveConn.Close()
        Catch ex As Exception
            chgOnLineActiveConn.Close()
            ChngFlg = ex.Message
        End Try
    End Sub

    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        ProgressPanel1.Visible = False
        BntUpdate.Enabled = True
        lbllic.Text = GET_LICENSE_PLAN_STRING(ActivationModule)
        If ChngFlg = "" Then
            Dim args As New XtraMessageBoxArgs()
            args.AutoCloseOptions.Delay = 5000
            args.AutoCloseOptions.ShowTimerOnDefaultButton = True
            args.DefaultButtonIndex = 0
            args.Icon = System.Drawing.SystemIcons.Information
            args.Caption = "License plan"
            args.Text = "  Your license plan updated Successfully. Restarting in... "
            args.Buttons = New DialogResult() {DialogResult.OK}
            XtraMessageBox.Show(args).ToString()
            System.Diagnostics.Process.Start(Application.ExecutablePath)
            Process.GetCurrentProcess().Kill()
        ElseIf ChngFlg = "SAME" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("There is no new plan for this license...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            DevExpress.XtraEditors.XtraMessageBox.Show("Update failed, please try again...!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub lbllic_Click(sender As Object, e As EventArgs)
    End Sub
    Private Sub lbllic_MouseHover(sender As Object, e As EventArgs)
        ToolTip1.SetToolTip(lbllic, ActivationModule)
    End Sub

    Private Sub BntUpdate_Click(sender As Object, e As EventArgs) Handles BntUpdate.Click
        ProgressPanel1.Visible = True
        BntUpdate.Enabled = False
        If BackgroundWorker1.IsBusy = False Then
            BackgroundWorker1.RunWorkerAsync()
        End If
    End Sub
End Class