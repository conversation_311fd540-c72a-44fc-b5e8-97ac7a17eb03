﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmEncodedLinkWolf
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.SeparatorControl2 = New DevExpress.XtraEditors.SeparatorControl()
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.LogInCheckBox1 = New System.Windows.Forms.CheckBox()
        Me.LogInNormalTextBox3 = New System.Windows.Forms.TextBox()
        Me.ProgressBarControl1 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.BntClear = New DevExpress.XtraEditors.SimpleButton()
        Me.LogInButtonWithProgress1 = New DevExpress.XtraEditors.SimpleButton()
        Me.BntSave = New DevExpress.XtraEditors.SimpleButton()
        Me.bntCopy = New DevExpress.XtraEditors.SimpleButton()
        Me.txtLinkEncodedWolf = New DevExpress.XtraEditors.TextEdit()
        Me.cmbEncryptionMode = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.txtmyLinkURL = New DevExpress.XtraEditors.TextEdit()
        Me.txtHttps = New DevExpress.XtraEditors.TextEdit()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtLinkEncodedWolf.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbEncryptionMode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtmyLinkURL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHttps.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'SeparatorControl2
        '
        Me.SeparatorControl2.LineThickness = 1
        Me.SeparatorControl2.Location = New System.Drawing.Point(295, 135)
        Me.SeparatorControl2.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl2.Name = "SeparatorControl2"
        Me.SeparatorControl2.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl2.Size = New System.Drawing.Size(363, 26)
        Me.SeparatorControl2.TabIndex = 483
        '
        'Timer1
        '
        '
        'LogInCheckBox1
        '
        Me.LogInCheckBox1.AutoSize = True
        Me.LogInCheckBox1.ForeColor = System.Drawing.Color.White
        Me.LogInCheckBox1.Location = New System.Drawing.Point(1334, 17)
        Me.LogInCheckBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.LogInCheckBox1.Name = "LogInCheckBox1"
        Me.LogInCheckBox1.Size = New System.Drawing.Size(86, 22)
        Me.LogInCheckBox1.TabIndex = 485
        Me.LogInCheckBox1.Text = "CheckBox1"
        Me.LogInCheckBox1.UseVisualStyleBackColor = True
        Me.LogInCheckBox1.Visible = False
        '
        'LogInNormalTextBox3
        '
        Me.LogInNormalTextBox3.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.0!)
        Me.LogInNormalTextBox3.ForeColor = System.Drawing.Color.FromArgb(CType(CType(8, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(81, Byte), Integer))
        Me.LogInNormalTextBox3.Location = New System.Drawing.Point(1395, 108)
        Me.LogInNormalTextBox3.Margin = New System.Windows.Forms.Padding(4)
        Me.LogInNormalTextBox3.Multiline = True
        Me.LogInNormalTextBox3.Name = "LogInNormalTextBox3"
        Me.LogInNormalTextBox3.Size = New System.Drawing.Size(11, 50)
        Me.LogInNormalTextBox3.TabIndex = 486
        Me.LogInNormalTextBox3.Visible = False
        '
        'ProgressBarControl1
        '
        Me.ProgressBarControl1.Location = New System.Drawing.Point(1350, 48)
        Me.ProgressBarControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.ProgressBarControl1.Name = "ProgressBarControl1"
        Me.ProgressBarControl1.Size = New System.Drawing.Size(117, 25)
        Me.ProgressBarControl1.TabIndex = 487
        Me.ProgressBarControl1.Visible = False
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label4.Location = New System.Drawing.Point(153, 313)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(94, 21)
        Me.Label4.TabIndex = 513
        Me.Label4.Text = "Link Encode"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label3.Location = New System.Drawing.Point(153, 256)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(76, 21)
        Me.Label3.TabIndex = 514
        Me.Label3.Text = "Page Link"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(153, 199)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(97, 21)
        Me.Label2.TabIndex = 515
        Me.Label2.Text = "Redirect Link"
        '
        'BntClear
        '
        Me.BntClear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntClear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntClear.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntClear.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntClear.Appearance.Options.UseBackColor = True
        Me.BntClear.Appearance.Options.UseBorderColor = True
        Me.BntClear.Appearance.Options.UseFont = True
        Me.BntClear.Appearance.Options.UseForeColor = True
        Me.BntClear.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntClear.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntClear.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntClear.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntClear.AppearanceDisabled.Options.UseBackColor = True
        Me.BntClear.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntClear.AppearanceDisabled.Options.UseFont = True
        Me.BntClear.AppearanceDisabled.Options.UseForeColor = True
        Me.BntClear.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntClear.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntClear.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntClear.AppearanceHovered.Options.UseBackColor = True
        Me.BntClear.AppearanceHovered.Options.UseBorderColor = True
        Me.BntClear.AppearanceHovered.Options.UseForeColor = True
        Me.BntClear.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntClear.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntClear.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntClear.AppearancePressed.Options.UseBackColor = True
        Me.BntClear.AppearancePressed.Options.UseBorderColor = True
        Me.BntClear.AppearancePressed.Options.UseForeColor = True
        Me.BntClear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntClear.Location = New System.Drawing.Point(640, 389)
        Me.BntClear.Margin = New System.Windows.Forms.Padding(4)
        Me.BntClear.Name = "BntClear"
        Me.BntClear.Size = New System.Drawing.Size(148, 38)
        Me.BntClear.TabIndex = 512
        Me.BntClear.Text = "Reset All"
        '
        'LogInButtonWithProgress1
        '
        Me.LogInButtonWithProgress1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LogInButtonWithProgress1.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LogInButtonWithProgress1.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.999999!, System.Drawing.FontStyle.Bold)
        Me.LogInButtonWithProgress1.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LogInButtonWithProgress1.Appearance.Options.UseBackColor = True
        Me.LogInButtonWithProgress1.Appearance.Options.UseBorderColor = True
        Me.LogInButtonWithProgress1.Appearance.Options.UseFont = True
        Me.LogInButtonWithProgress1.Appearance.Options.UseForeColor = True
        Me.LogInButtonWithProgress1.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.LogInButtonWithProgress1.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.LogInButtonWithProgress1.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.LogInButtonWithProgress1.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.LogInButtonWithProgress1.AppearanceDisabled.Options.UseBackColor = True
        Me.LogInButtonWithProgress1.AppearanceDisabled.Options.UseBorderColor = True
        Me.LogInButtonWithProgress1.AppearanceDisabled.Options.UseFont = True
        Me.LogInButtonWithProgress1.AppearanceDisabled.Options.UseForeColor = True
        Me.LogInButtonWithProgress1.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.LogInButtonWithProgress1.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LogInButtonWithProgress1.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.LogInButtonWithProgress1.AppearanceHovered.Options.UseBackColor = True
        Me.LogInButtonWithProgress1.AppearanceHovered.Options.UseBorderColor = True
        Me.LogInButtonWithProgress1.AppearanceHovered.Options.UseForeColor = True
        Me.LogInButtonWithProgress1.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LogInButtonWithProgress1.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LogInButtonWithProgress1.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.LogInButtonWithProgress1.AppearancePressed.Options.UseBackColor = True
        Me.LogInButtonWithProgress1.AppearancePressed.Options.UseBorderColor = True
        Me.LogInButtonWithProgress1.AppearancePressed.Options.UseForeColor = True
        Me.LogInButtonWithProgress1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.LinkEN
        Me.LogInButtonWithProgress1.Location = New System.Drawing.Point(157, 389)
        Me.LogInButtonWithProgress1.Margin = New System.Windows.Forms.Padding(4)
        Me.LogInButtonWithProgress1.Name = "LogInButtonWithProgress1"
        Me.LogInButtonWithProgress1.Size = New System.Drawing.Size(148, 38)
        Me.LogInButtonWithProgress1.TabIndex = 509
        Me.LogInButtonWithProgress1.Text = "Encoded"
        '
        'BntSave
        '
        Me.BntSave.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.999999!, System.Drawing.FontStyle.Bold)
        Me.BntSave.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.Appearance.Options.UseBackColor = True
        Me.BntSave.Appearance.Options.UseBorderColor = True
        Me.BntSave.Appearance.Options.UseFont = True
        Me.BntSave.Appearance.Options.UseForeColor = True
        Me.BntSave.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntSave.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntSave.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntSave.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntSave.AppearanceDisabled.Options.UseBackColor = True
        Me.BntSave.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntSave.AppearanceDisabled.Options.UseFont = True
        Me.BntSave.AppearanceDisabled.Options.UseForeColor = True
        Me.BntSave.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntSave.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntSave.AppearanceHovered.Options.UseBackColor = True
        Me.BntSave.AppearanceHovered.Options.UseBorderColor = True
        Me.BntSave.AppearanceHovered.Options.UseForeColor = True
        Me.BntSave.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntSave.AppearancePressed.Options.UseBackColor = True
        Me.BntSave.AppearancePressed.Options.UseBorderColor = True
        Me.BntSave.AppearancePressed.Options.UseForeColor = True
        Me.BntSave.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.BntSave.Location = New System.Drawing.Point(479, 389)
        Me.BntSave.Margin = New System.Windows.Forms.Padding(4)
        Me.BntSave.Name = "BntSave"
        Me.BntSave.Size = New System.Drawing.Size(148, 38)
        Me.BntSave.TabIndex = 511
        Me.BntSave.Text = "Save"
        '
        'bntCopy
        '
        Me.bntCopy.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntCopy.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntCopy.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.999999!, System.Drawing.FontStyle.Bold)
        Me.bntCopy.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntCopy.Appearance.Options.UseBackColor = True
        Me.bntCopy.Appearance.Options.UseBorderColor = True
        Me.bntCopy.Appearance.Options.UseFont = True
        Me.bntCopy.Appearance.Options.UseForeColor = True
        Me.bntCopy.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntCopy.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntCopy.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntCopy.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntCopy.AppearanceDisabled.Options.UseBackColor = True
        Me.bntCopy.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntCopy.AppearanceDisabled.Options.UseFont = True
        Me.bntCopy.AppearanceDisabled.Options.UseForeColor = True
        Me.bntCopy.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntCopy.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntCopy.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntCopy.AppearanceHovered.Options.UseBackColor = True
        Me.bntCopy.AppearanceHovered.Options.UseBorderColor = True
        Me.bntCopy.AppearanceHovered.Options.UseForeColor = True
        Me.bntCopy.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntCopy.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntCopy.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntCopy.AppearancePressed.Options.UseBackColor = True
        Me.bntCopy.AppearancePressed.Options.UseBorderColor = True
        Me.bntCopy.AppearancePressed.Options.UseForeColor = True
        Me.bntCopy.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy32x32
        Me.bntCopy.Location = New System.Drawing.Point(318, 389)
        Me.bntCopy.Margin = New System.Windows.Forms.Padding(4)
        Me.bntCopy.Name = "bntCopy"
        Me.bntCopy.Size = New System.Drawing.Size(148, 38)
        Me.bntCopy.TabIndex = 510
        Me.bntCopy.Text = "Copy"
        '
        'txtLinkEncodedWolf
        '
        Me.txtLinkEncodedWolf.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtLinkEncodedWolf.EditValue = ""
        Me.txtLinkEncodedWolf.Location = New System.Drawing.Point(153, 337)
        Me.txtLinkEncodedWolf.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtLinkEncodedWolf.Name = "txtLinkEncodedWolf"
        Me.txtLinkEncodedWolf.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtLinkEncodedWolf.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtLinkEncodedWolf.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtLinkEncodedWolf.Properties.Appearance.Options.UseBackColor = True
        Me.txtLinkEncodedWolf.Properties.Appearance.Options.UseFont = True
        Me.txtLinkEncodedWolf.Properties.Appearance.Options.UseForeColor = True
        Me.txtLinkEncodedWolf.Properties.NullValuePrompt = "Your Encrypted link will appear here"
        Me.txtLinkEncodedWolf.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtLinkEncodedWolf.Size = New System.Drawing.Size(636, 30)
        Me.txtLinkEncodedWolf.TabIndex = 506
        Me.txtLinkEncodedWolf.ToolTipTitle = "Your Encrypted link will appear here"
        '
        'cmbEncryptionMode
        '
        Me.cmbEncryptionMode.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cmbEncryptionMode.EditValue = "Encryption Method 1"
        Me.cmbEncryptionMode.Location = New System.Drawing.Point(614, 173)
        Me.cmbEncryptionMode.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cmbEncryptionMode.Name = "cmbEncryptionMode"
        Me.cmbEncryptionMode.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cmbEncryptionMode.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cmbEncryptionMode.Properties.Appearance.Options.UseBackColor = True
        Me.cmbEncryptionMode.Properties.Appearance.Options.UseFont = True
        Me.cmbEncryptionMode.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cmbEncryptionMode.Properties.Items.AddRange(New Object() {"Encryption Method 1", "Encryption Method 2"})
        Me.cmbEncryptionMode.Size = New System.Drawing.Size(175, 38)
        Me.cmbEncryptionMode.TabIndex = 505
        '
        'txtmyLinkURL
        '
        Me.txtmyLinkURL.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtmyLinkURL.EditValue = ""
        Me.txtmyLinkURL.Location = New System.Drawing.Point(153, 280)
        Me.txtmyLinkURL.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtmyLinkURL.Name = "txtmyLinkURL"
        Me.txtmyLinkURL.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtmyLinkURL.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtmyLinkURL.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtmyLinkURL.Properties.Appearance.Options.UseBackColor = True
        Me.txtmyLinkURL.Properties.Appearance.Options.UseFont = True
        Me.txtmyLinkURL.Properties.Appearance.Options.UseForeColor = True
        Me.txtmyLinkURL.Properties.NullValuePrompt = "www.Example.com Your link without (  / )"
        Me.txtmyLinkURL.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtmyLinkURL.Size = New System.Drawing.Size(636, 30)
        Me.txtmyLinkURL.TabIndex = 507
        Me.txtmyLinkURL.ToolTipTitle = "www.Example.com Your link without (  / )"
        '
        'txtHttps
        '
        Me.txtHttps.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtHttps.EditValue = ""
        Me.txtHttps.Location = New System.Drawing.Point(153, 223)
        Me.txtHttps.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtHttps.Name = "txtHttps"
        Me.txtHttps.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtHttps.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtHttps.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtHttps.Properties.Appearance.Options.UseBackColor = True
        Me.txtHttps.Properties.Appearance.Options.UseFont = True
        Me.txtHttps.Properties.Appearance.Options.UseForeColor = True
        Me.txtHttps.Properties.NullValuePrompt = "https:// or http://"
        Me.txtHttps.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtHttps.Size = New System.Drawing.Size(636, 30)
        Me.txtHttps.TabIndex = 508
        Me.txtHttps.ToolTipTitle = "https:// or http://"
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.EncodedLinkWolfImage
        Me.PictureBox1.Location = New System.Drawing.Point(278, 81)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(393, 75)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 516
        Me.PictureBox1.TabStop = False
        '
        'frmEncodedLinkWolf
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(980, 604)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.BntClear)
        Me.Controls.Add(Me.LogInButtonWithProgress1)
        Me.Controls.Add(Me.BntSave)
        Me.Controls.Add(Me.bntCopy)
        Me.Controls.Add(Me.txtLinkEncodedWolf)
        Me.Controls.Add(Me.cmbEncryptionMode)
        Me.Controls.Add(Me.txtmyLinkURL)
        Me.Controls.Add(Me.txtHttps)
        Me.Controls.Add(Me.ProgressBarControl1)
        Me.Controls.Add(Me.LogInNormalTextBox3)
        Me.Controls.Add(Me.LogInCheckBox1)
        Me.Controls.Add(Me.SeparatorControl2)
        Me.Controls.Add(Me.PictureBox1)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Wolf_EN
        Me.IconOptions.ShowIcon = False
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmEncodedLinkWolf"
        Me.ShowInTaskbar = False
        Me.Text = "Encoded Link Wolf"
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtLinkEncodedWolf.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbEncryptionMode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtmyLinkURL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHttps.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents SeparatorControl2 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents Timer1 As Timer
    Friend WithEvents LogInCheckBox1 As CheckBox
    Friend WithEvents LogInNormalTextBox3 As TextBox
    Friend WithEvents ProgressBarControl1 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents Label4 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents BntClear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LogInButtonWithProgress1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents bntCopy As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtLinkEncodedWolf As DevExpress.XtraEditors.TextEdit
    Friend WithEvents cmbEncryptionMode As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents txtmyLinkURL As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtHttps As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureBox1 As PictureBox
End Class
