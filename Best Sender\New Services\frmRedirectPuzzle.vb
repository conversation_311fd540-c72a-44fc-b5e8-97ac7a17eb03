﻿Imports System.IO
Imports DevExpress.XtraEditors.Controls
Imports System.Windows.Controls
Imports DevExpress.XtraEditors
Imports System.Text.RegularExpressions
Imports System.Text
Imports System.Diagnostics ' For Process.Start
Public Class frmRedirectPuzzle
    Private Const VBQuote As String = """"
    ' تعديل دالة ConvertFile لتأخذ النص من RichTextBox1 وتكتب في ملف
    Private Sub ConvertFile(ByVal SourceText As String, ByVal DestFile As String, ByVal Title As String, Optional ByVal DisableClick As Boolean = False)
        Dim sb As New StringBuilder()
        Dim Buffer As String = SourceText
        ' استبدال النصوص بالحقول المدخلة
        If cb_txt_Time.SelectedItem IsNot Nothing AndAlso Not String.IsNullOrEmpty(cb_txt_Time.SelectedItem.ToString()) Then
            Buffer = Buffer.Replace("[-Time-]", cb_txt_Time.SelectedItem.ToString())
        End If
        If Not String.IsNullOrEmpty(txt_Link_Puzzle.Text) Then
            Buffer = Buffer.Replace("[-Link-]", txt_Link_Puzzle.Text)
        End If
        If Not String.IsNullOrEmpty(txt_type_Puzzle.Text) Then
            Buffer = Buffer.Replace("Please solve the puzzle to prove you're not a robot", txt_type_Puzzle.Text)
        End If
        If Not String.IsNullOrEmpty(txt_type_Puzzle.Text) Then
            Buffer = Buffer.Replace("[-type-]", txt_type_Puzzle.Text)
        Else
            Buffer = Buffer.Replace("[-type-]", "")
        End If
        ' تحقق من النقر بزر الماوس الأيمن
        If DisableClick Then
            Buffer &= "<script>document.oncontextmenu=new Function(" & VBQuote & "return false" & VBQuote & ")</script>"
        End If
        ' بناء الملف
        sb.AppendLine("<html>")
        sb.AppendLine("<head>")
        sb.AppendLine("<title>" & Title & "</title>")
        sb.AppendLine("<body>")
        sb.AppendLine("<script language=" & VBQuote & "JavaScript" & VBQuote & " type=" & VBQuote & "text/javascript" & VBQuote & ">")
        sb.Append("document.write(unescape('")
        For Each c As Char In Buffer
            sb.Append("%" & Asc(c).ToString("X2"))
        Next c
        sb.Append("'));")
        sb.AppendLine("</script>")
        sb.AppendLine("</body>")
        sb.AppendLine("</html>")
        Dim sw As New StreamWriter(DestFile)
        sw.Write(sb.ToString())
        sw.Close()
        Console.WriteLine("File Successfully written to: " & DestFile)
    End Sub


    'Private Sub BntNew_Click(sender As Object, e As EventArgs)

    'End Sub

    Private Sub frmRedirectPuzzle_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If My.Settings.Link <> "" Then
            txt_Link_Puzzle.Text = My.Settings.txt_Link_Puzzle
        End If
        If My.Settings.Tag <> "" Then
            txt_type_Puzzle.Text = My.Settings.txt_type_Puzzle
        End If
        ToolTip1.SetToolTip(txt_Link_Puzzle, "(Example) https://www.Example.com")
        ToolTip1.SetToolTip(txt_type_Puzzle, "(Example) Please solve the puzzle to prove you're not a robot")
        ToolTip1.SetToolTip(btn_Save, "Save")
        ToolTip1.SetToolTip(BntCLear, "Clear All")
        ToolTip1.SetToolTip(cb_txt_Time, "Here if you choose time as an example: 1000 = 1 second")

        ' إضافة معالج حدث تغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmRedirectPuzzle_Resize

        ' توسيط العناصر عند التحميل
        CenterControlsInForm()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmRedirectPuzzle_Resize(sender As Object, e As EventArgs)
        ' توسيط العناصر عند تغيير حجم النموذج
        CenterControlsInForm()
    End Sub

    ''' <summary>
    ''' توسيط جميع العناصر في النموذج دون تغيير حجمها
    ''' </summary>
    Private Sub CenterControlsInForm()
        ' توسيط الصورة والفاصل في الأعلى
        PictureBox1.Left = (Me.ClientSize.Width - PictureBox1.Width) \ 2
        SeparatorControl2.Left = (Me.ClientSize.Width - SeparatorControl2.Width) \ 2

        ' توسيط مجموعة الأدوات الرئيسية
        ' نحسب العرض الإجمالي للمجموعة الرئيسية (أكبر عنصر هو txt_Link_Puzzle)
        Dim mainGroupWidth As Integer = txt_Link_Puzzle.Width
        Dim centerX As Integer = Me.ClientSize.Width \ 2
        Dim leftMargin As Integer = centerX - (mainGroupWidth \ 2)

        ' توسيط حقول الإدخال مع الحفاظ على أحجامها الأصلية
        txt_Link_Puzzle.Left = leftMargin
        txt_type_Puzzle.Left = leftMargin

        ' توسيط أزرار المسح
        SimpleButton1.Left = txt_Link_Puzzle.Right + 5
        SimpleButton2.Left = txt_type_Puzzle.Right + 5

        ' توسيط التسميات بنفس محاذاة حقول الإدخال
        Label2.Left = leftMargin
        Label3.Left = leftMargin

        ' توسيط عناصر الوقت
        Dim timeControlsWidth As Integer = Label6.Width + cb_txt_Time.Width + 10
        Dim timeStartX As Integer = centerX + (mainGroupWidth \ 2) - timeControlsWidth

        Label6.Left = timeStartX
        cb_txt_Time.Left = Label6.Right + 10

        ' توسيط مجموعة الأزرار
        Dim buttonWidth As Integer = BntBuild.Width + btn_Save.Width + BntCLear.Width + 15 * 2 ' المسافة بين الأزرار
        Dim startX As Integer = (Me.ClientSize.Width - buttonWidth) \ 2

        BntBuild.Left = startX
        btn_Save.Left = BntBuild.Right + 15
        BntCLear.Left = btn_Save.Right + 15
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        txt_Link_Puzzle.Clear()
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        txt_type_Puzzle.Clear()
    End Sub

    Private Sub btn_Save_Click(sender As Object, e As EventArgs) Handles btn_Save.Click

        My.Settings.txt_Link_Puzzle = txt_Link_Puzzle.Text
        My.Settings.txt_type_Puzzle = txt_type_Puzzle.Text
        My.Settings.Save()
        ' Show a success message using DevExpress MessageBox
        DevExpress.XtraEditors.XtraMessageBox.Show("Settings have been saved Successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    Dim linkPattern As String = "^(https?)://[^\s/$.?#].[^\s]*$"
    Private Sub BntBuild_Click(sender As Object, e As EventArgs) Handles BntBuild.Click
        ' التحقق من صحة الرابط
        If String.IsNullOrWhiteSpace(txt_Link_Puzzle.Text) OrElse Not Regex.IsMatch(txt_Link_Puzzle.Text, linkPattern) Then
            XtraMessageBox.Show("Please enter a valid link starting with http or https.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txt_Link_Puzzle.Focus()
            Exit Sub
        End If
        If txt_type_Puzzle.Text.Trim = "" Then
            XtraMessageBox.Show("Please Enter Text (Example) Please solve the puzzle to prove you're not a robot ...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        ' التحقق من وجود نص في RichTextBox1
        If Not String.IsNullOrEmpty(RichTextBox1.Text) Then
            ' إنشاء مجلد "Redirect Puzzle" على سطح المكتب إذا لم يكن موجودًا
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim folderPath As String = Path.Combine(desktopPath, "Redirect Puzzle")

            ' التحقق من وجود المجلد وإنشائه إذا لم يكن موجودًا
            If Not Directory.Exists(folderPath) Then
                Directory.CreateDirectory(folderPath)
            End If

            ' إنشاء اسم ملف فريد بناءً على التاريخ والوقت
            Dim fileName As String = "Puzzle_" & DateTime.Now.ToString("yyyyMMdd_HHmmss") & ".html"
            Dim filePath As String = Path.Combine(folderPath, fileName)

            ' تخزين النص الأصلي
            Dim originalText As String = RichTextBox1.Text

            ' تعديل النصوص
            If Not String.IsNullOrEmpty(txt_Link_Puzzle.Text) Then
                RichTextBox1.Text = RichTextBox1.Text.Replace("[-Link-]", txt_Link_Puzzle.Text)
            End If
            If Not String.IsNullOrEmpty(txt_type_Puzzle.Text) Then
                RichTextBox1.Text = RichTextBox1.Text.Replace("[-type-]", txt_type_Puzzle.Text)
            End If

            ' استدعاء دالة ConvertFile لتحويل النص
            ConvertFile(RichTextBox1.Text, filePath, "Puzzle Verification")

            ' استرجاع النص الأصلي
            RichTextBox1.Text = originalText

            '' فتح الملف بعد الحفظ
            'Process.Start(filePath)

            ' فتح المجلد بعد الحفظ
            Process.Start("explorer.exe", folderPath)

            ' عرض رسالة بنجاح العملية
            DevExpress.XtraEditors.XtraMessageBox.Show($"File Successfully saved to: {folderPath}", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            ' عرض رسالة في حالة عدم وجود نص
            DevExpress.XtraEditors.XtraMessageBox.Show("Source text is empty!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub BntCLear_Click(sender As Object, e As EventArgs) Handles BntCLear.Click
        txt_type_Puzzle.Clear()
        txt_Link_Puzzle.Clear()
    End Sub
End Class
