<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Grpc.Net.Common</name>
    </assembly>
    <members>
        <member name="T:Grpc.Core.AsyncStreamReaderExtensions">
            <summary>
            Extension methods for <see cref="T:Grpc.Core.IAsyncStreamReader`1"/>.
            </summary>
        </member>
        <member name="M:Grpc.Core.AsyncStreamReaderExtensions.ReadAllAsync``1(Grpc.Core.IAsyncStreamReader{``0},System.Threading.CancellationToken)">
            <summary>
            Creates an <see cref="T:System.Collections.Generic.IAsyncEnumerable`1"/> that enables reading all of the data from the stream reader.
            </summary>
            <typeparam name="T">The message type.</typeparam>
            <param name="streamReader">The stream reader.</param>
            <param name="cancellationToken">The cancellation token to use to cancel the enumeration.</param>
            <returns>The created async enumerable.</returns>
        </member>
        <member name="T:Grpc.Core.ConnectivityState">
            <summary>
            The connectivity state.
            <para>
            Note: Experimental API that can change or be removed without any prior notice.
            </para>
            </summary>
        </member>
        <member name="F:Grpc.Core.ConnectivityState.Idle">
            <summary>
            Not trying to create a connection.
            </summary>
        </member>
        <member name="F:Grpc.Core.ConnectivityState.Connecting">
            <summary>
            Establishing a connection.
            </summary>
        </member>
        <member name="F:Grpc.Core.ConnectivityState.Ready">
            <summary>
            Connection ready.
            </summary>
        </member>
        <member name="F:Grpc.Core.ConnectivityState.TransientFailure">
            <summary>
            A transient failure on connection.
            </summary>
        </member>
        <member name="F:Grpc.Core.ConnectivityState.Shutdown">
            <summary>
            Connection shutdown.
            </summary>
        </member>
        <member name="T:Grpc.Net.Compression.DeflateCompressionProvider">
            <summary>
            Deflate compression provider.
            </summary>
        </member>
        <member name="M:Grpc.Net.Compression.DeflateCompressionProvider.#ctor(System.IO.Compression.CompressionLevel)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Compression.DeflateCompressionProvider"/> class with the specified <see cref="T:System.IO.Compression.CompressionLevel"/>.
            </summary>
            <param name="defaultCompressionLevel">The default compression level to use when compressing data.</param>
        </member>
        <member name="P:Grpc.Net.Compression.DeflateCompressionProvider.EncodingName">
            <summary>
            The encoding name used in the 'grpc-encoding' and 'grpc-accept-encoding' request and response headers.
            </summary>
        </member>
        <member name="M:Grpc.Net.Compression.DeflateCompressionProvider.CreateCompressionStream(System.IO.Stream,System.Nullable{System.IO.Compression.CompressionLevel})">
            <summary>
            Create a new compression stream.
            </summary>
            <param name="stream">The stream that compressed data is written to.</param>
            <param name="compressionLevel">The compression level.</param>
            <returns>A stream used to compress data.</returns>
        </member>
        <member name="M:Grpc.Net.Compression.DeflateCompressionProvider.CreateDecompressionStream(System.IO.Stream)">
            <summary>
            Create a new decompression stream.
            </summary>
            <param name="stream">The stream that compressed data is copied from.</param>
            <returns>A stream used to decompress data.</returns>
        </member>
        <member name="T:Grpc.Net.Compression.GzipCompressionProvider">
            <summary>
            GZIP compression provider.
            </summary>
        </member>
        <member name="M:Grpc.Net.Compression.GzipCompressionProvider.#ctor(System.IO.Compression.CompressionLevel)">
            <summary>
            Initializes a new instance of the <see cref="T:Grpc.Net.Compression.GzipCompressionProvider"/> class with the specified <see cref="T:System.IO.Compression.CompressionLevel"/>.
            </summary>
            <param name="defaultCompressionLevel">The default compression level to use when compressing data.</param>
        </member>
        <member name="P:Grpc.Net.Compression.GzipCompressionProvider.EncodingName">
            <summary>
            The encoding name used in the 'grpc-encoding' and 'grpc-accept-encoding' request and response headers.
            </summary>
        </member>
        <member name="M:Grpc.Net.Compression.GzipCompressionProvider.CreateCompressionStream(System.IO.Stream,System.Nullable{System.IO.Compression.CompressionLevel})">
            <summary>
            Create a new compression stream.
            </summary>
            <param name="stream">The stream that compressed data is written to.</param>
            <param name="compressionLevel">The compression level.</param>
            <returns>A stream used to compress data.</returns>
        </member>
        <member name="M:Grpc.Net.Compression.GzipCompressionProvider.CreateDecompressionStream(System.IO.Stream)">
            <summary>
            Create a new decompression stream.
            </summary>
            <param name="stream">The stream that compressed data is copied from.</param>
            <returns>A stream used to decompress data.</returns>
        </member>
        <member name="T:Grpc.Net.Compression.ICompressionProvider">
            <summary>
            Provides a specific compression implementation to compress gRPC messages.
            </summary>
        </member>
        <member name="P:Grpc.Net.Compression.ICompressionProvider.EncodingName">
            <summary>
            The encoding name used in the 'grpc-encoding' and 'grpc-accept-encoding' request and response headers.
            </summary>
        </member>
        <member name="M:Grpc.Net.Compression.ICompressionProvider.CreateCompressionStream(System.IO.Stream,System.Nullable{System.IO.Compression.CompressionLevel})">
            <summary>
            Create a new compression stream.
            </summary>
            <param name="stream">The stream that compressed data is written to.</param>
            <param name="compressionLevel">The compression level.</param>
            <returns>A stream used to compress data.</returns>
        </member>
        <member name="M:Grpc.Net.Compression.ICompressionProvider.CreateDecompressionStream(System.IO.Stream)">
            <summary>
            Create a new decompression stream.
            </summary>
            <param name="stream">The stream that compressed data is copied from.</param>
            <returns>A stream used to decompress data.</returns>
        </member>
        <member name="M:Grpc.Shared.ArgumentNullThrowHelper.ThrowIfNull(System.Object,System.String)">
            <summary>Throws an <see cref="T:System.ArgumentNullException"/> if <paramref name="argument"/> is null.</summary>
            <param name="argument">The reference type argument to validate as non-null.</param>
            <param name="paramName">The name of the parameter with which <paramref name="argument"/> corresponds.</param>
        </member>
    </members>
</doc>
