﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmRedirectSpider
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmRedirectSpider))
        Me.RichTextBox2 = New System.Windows.Forms.RichTextBox()
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.Bnt_Download = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.Clear3 = New DevExpress.XtraEditors.SimpleButton()
        Me.txtUploadeCpanel = New DevExpress.XtraEditors.TextEdit()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.GroupControl3 = New DevExpress.XtraEditors.GroupControl()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        Me.txtLinkPageCpanel = New DevExpress.XtraEditors.TextEdit()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.GroupControl4 = New DevExpress.XtraEditors.GroupControl()
        Me.SimpleButton3 = New DevExpress.XtraEditors.SimpleButton()
        Me.LinkEncodeCpanel = New DevExpress.XtraEditors.TextEdit()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.BTN_Redirect_Link_Microsoft = New DevExpress.XtraEditors.SimpleButton()
        Me.btn_Reset = New DevExpress.XtraEditors.SimpleButton()
        Me.btn_Save = New DevExpress.XtraEditors.SimpleButton()
        Me.BntURLCOpy = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupControl5 = New DevExpress.XtraEditors.GroupControl()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.txtUploadeCpanel.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl3.SuspendLayout()
        CType(Me.txtLinkPageCpanel.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl4.SuspendLayout()
        CType(Me.LinkEncodeCpanel.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl5.SuspendLayout()
        Me.SuspendLayout()
        '
        'RichTextBox2
        '
        Me.RichTextBox2.Location = New System.Drawing.Point(1226, 35)
        Me.RichTextBox2.Margin = New System.Windows.Forms.Padding(4)
        Me.RichTextBox2.Name = "RichTextBox2"
        Me.RichTextBox2.Size = New System.Drawing.Size(125, 66)
        Me.RichTextBox2.TabIndex = 518
        Me.RichTextBox2.Text = ""
        Me.RichTextBox2.Visible = False
        '
        'RichTextBox1
        '
        Me.RichTextBox1.Location = New System.Drawing.Point(1359, 35)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(118, 66)
        Me.RichTextBox1.TabIndex = 519
        Me.RichTextBox1.Text = resources.GetString("RichTextBox1.Text")
        Me.RichTextBox1.Visible = False
        '
        'GroupControl2
        '
        Me.GroupControl2.AppearanceCaption.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.GroupControl2.AppearanceCaption.Options.UseFont = True
        Me.GroupControl2.Controls.Add(Me.Bnt_Download)
        Me.GroupControl2.Location = New System.Drawing.Point(29, 149)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.Size = New System.Drawing.Size(572, 122)
        Me.GroupControl2.TabIndex = 578
        Me.GroupControl2.Text = "Step 1"
        '
        'Bnt_Download
        '
        Me.Bnt_Download.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Bnt_Download.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Download.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.Bnt_Download.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Download.Appearance.Options.UseBackColor = True
        Me.Bnt_Download.Appearance.Options.UseBorderColor = True
        Me.Bnt_Download.Appearance.Options.UseFont = True
        Me.Bnt_Download.Appearance.Options.UseForeColor = True
        Me.Bnt_Download.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.Bnt_Download.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.Bnt_Download.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.Bnt_Download.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Bnt_Download.AppearanceDisabled.Options.UseBackColor = True
        Me.Bnt_Download.AppearanceDisabled.Options.UseBorderColor = True
        Me.Bnt_Download.AppearanceDisabled.Options.UseFont = True
        Me.Bnt_Download.AppearanceDisabled.Options.UseForeColor = True
        Me.Bnt_Download.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.Bnt_Download.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Bnt_Download.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.Bnt_Download.AppearanceHovered.Options.UseBackColor = True
        Me.Bnt_Download.AppearanceHovered.Options.UseBorderColor = True
        Me.Bnt_Download.AppearanceHovered.Options.UseForeColor = True
        Me.Bnt_Download.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Download.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Bnt_Download.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.Bnt_Download.AppearancePressed.Options.UseBackColor = True
        Me.Bnt_Download.AppearancePressed.Options.UseBorderColor = True
        Me.Bnt_Download.AppearancePressed.Options.UseForeColor = True
        Me.Bnt_Download.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.Bnt_Download.Location = New System.Drawing.Point(208, 51)
        Me.Bnt_Download.Margin = New System.Windows.Forms.Padding(4)
        Me.Bnt_Download.Name = "Bnt_Download"
        Me.Bnt_Download.Size = New System.Drawing.Size(152, 38)
        Me.Bnt_Download.TabIndex = 591
        Me.Bnt_Download.Text = "Download"
        '
        'GroupControl1
        '
        Me.GroupControl1.AppearanceCaption.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.GroupControl1.AppearanceCaption.Options.UseFont = True
        Me.GroupControl1.Controls.Add(Me.Clear3)
        Me.GroupControl1.Controls.Add(Me.txtUploadeCpanel)
        Me.GroupControl1.Controls.Add(Me.Label9)
        Me.GroupControl1.Location = New System.Drawing.Point(29, 295)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(572, 122)
        Me.GroupControl1.TabIndex = 578
        Me.GroupControl1.Text = "Step 2"
        '
        'Clear3
        '
        Me.Clear3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear3.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear3.Appearance.Options.UseBackColor = True
        Me.Clear3.Appearance.Options.UseBorderColor = True
        Me.Clear3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.Clear3.Location = New System.Drawing.Point(525, 80)
        Me.Clear3.Name = "Clear3"
        Me.Clear3.Size = New System.Drawing.Size(25, 23)
        Me.Clear3.TabIndex = 593
        '
        'txtUploadeCpanel
        '
        Me.txtUploadeCpanel.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtUploadeCpanel.EditValue = ""
        Me.txtUploadeCpanel.Location = New System.Drawing.Point(47, 77)
        Me.txtUploadeCpanel.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtUploadeCpanel.Name = "txtUploadeCpanel"
        Me.txtUploadeCpanel.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtUploadeCpanel.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtUploadeCpanel.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtUploadeCpanel.Properties.Appearance.Options.UseBackColor = True
        Me.txtUploadeCpanel.Properties.Appearance.Options.UseFont = True
        Me.txtUploadeCpanel.Properties.Appearance.Options.UseForeColor = True
        Me.txtUploadeCpanel.Properties.NullValuePrompt = "Enter Your Link With file name> https://Example.com/FileName.html"
        Me.txtUploadeCpanel.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtUploadeCpanel.Size = New System.Drawing.Size(503, 30)
        Me.txtUploadeCpanel.TabIndex = 592
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label9.Location = New System.Drawing.Point(47, 51)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(79, 21)
        Me.Label9.TabIndex = 591
        Me.Label9.Text = "Enter Link"
        '
        'GroupControl3
        '
        Me.GroupControl3.AppearanceCaption.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.GroupControl3.AppearanceCaption.Options.UseFont = True
        Me.GroupControl3.Controls.Add(Me.SimpleButton2)
        Me.GroupControl3.Controls.Add(Me.txtLinkPageCpanel)
        Me.GroupControl3.Controls.Add(Me.Label1)
        Me.GroupControl3.Location = New System.Drawing.Point(625, 149)
        Me.GroupControl3.Name = "GroupControl3"
        Me.GroupControl3.Size = New System.Drawing.Size(572, 122)
        Me.GroupControl3.TabIndex = 578
        Me.GroupControl3.Text = "Step 3"
        '
        'SimpleButton2
        '
        Me.SimpleButton2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton2.Appearance.Options.UseBackColor = True
        Me.SimpleButton2.Appearance.Options.UseBorderColor = True
        Me.SimpleButton2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.SimpleButton2.Location = New System.Drawing.Point(501, 80)
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton2.TabIndex = 593
        '
        'txtLinkPageCpanel
        '
        Me.txtLinkPageCpanel.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtLinkPageCpanel.EditValue = ""
        Me.txtLinkPageCpanel.Location = New System.Drawing.Point(65, 77)
        Me.txtLinkPageCpanel.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtLinkPageCpanel.Name = "txtLinkPageCpanel"
        Me.txtLinkPageCpanel.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtLinkPageCpanel.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtLinkPageCpanel.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtLinkPageCpanel.Properties.Appearance.Options.UseBackColor = True
        Me.txtLinkPageCpanel.Properties.Appearance.Options.UseFont = True
        Me.txtLinkPageCpanel.Properties.Appearance.Options.UseForeColor = True
        Me.txtLinkPageCpanel.Properties.NullValuePrompt = "Enter Your Link Page"
        Me.txtLinkPageCpanel.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtLinkPageCpanel.Size = New System.Drawing.Size(461, 30)
        Me.txtLinkPageCpanel.TabIndex = 592
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label1.Location = New System.Drawing.Point(65, 51)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(262, 21)
        Me.Label1.TabIndex = 591
        Me.Label1.Text = "Enter Your page link to be Encrypted"
        '
        'GroupControl4
        '
        Me.GroupControl4.AppearanceCaption.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.GroupControl4.AppearanceCaption.Options.UseFont = True
        Me.GroupControl4.Controls.Add(Me.SimpleButton3)
        Me.GroupControl4.Controls.Add(Me.LinkEncodeCpanel)
        Me.GroupControl4.Controls.Add(Me.Label4)
        Me.GroupControl4.Location = New System.Drawing.Point(625, 295)
        Me.GroupControl4.Name = "GroupControl4"
        Me.GroupControl4.Size = New System.Drawing.Size(572, 122)
        Me.GroupControl4.TabIndex = 578
        Me.GroupControl4.Text = "Step 4"
        '
        'SimpleButton3
        '
        Me.SimpleButton3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton3.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton3.Appearance.Options.UseBackColor = True
        Me.SimpleButton3.Appearance.Options.UseBorderColor = True
        Me.SimpleButton3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.SimpleButton3.Location = New System.Drawing.Point(497, 80)
        Me.SimpleButton3.Name = "SimpleButton3"
        Me.SimpleButton3.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton3.TabIndex = 593
        '
        'LinkEncodeCpanel
        '
        Me.LinkEncodeCpanel.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.LinkEncodeCpanel.EditValue = ""
        Me.LinkEncodeCpanel.Location = New System.Drawing.Point(61, 77)
        Me.LinkEncodeCpanel.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LinkEncodeCpanel.Name = "LinkEncodeCpanel"
        Me.LinkEncodeCpanel.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.LinkEncodeCpanel.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LinkEncodeCpanel.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LinkEncodeCpanel.Properties.Appearance.Options.UseBackColor = True
        Me.LinkEncodeCpanel.Properties.Appearance.Options.UseFont = True
        Me.LinkEncodeCpanel.Properties.Appearance.Options.UseForeColor = True
        Me.LinkEncodeCpanel.Properties.NullValuePrompt = "Encrypted link with Redirection"
        Me.LinkEncodeCpanel.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.LinkEncodeCpanel.Size = New System.Drawing.Size(461, 30)
        Me.LinkEncodeCpanel.TabIndex = 592
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label4.Location = New System.Drawing.Point(61, 51)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(79, 21)
        Me.Label4.TabIndex = 591
        Me.Label4.Text = "Enter Link"
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.SPiderPanel
        Me.PictureBox1.Location = New System.Drawing.Point(327, 12)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(574, 158)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 580
        Me.PictureBox1.TabStop = False
        '
        'BTN_Redirect_Link_Microsoft
        '
        Me.BTN_Redirect_Link_Microsoft.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_Redirect_Link_Microsoft.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.Appearance.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Microsoft.Appearance.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Microsoft.Appearance.Options.UseFont = True
        Me.BTN_Redirect_Link_Microsoft.Appearance.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.Options.UseFont = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Microsoft.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.BTN_Redirect_Link_Microsoft.Location = New System.Drawing.Point(65, 59)
        Me.BTN_Redirect_Link_Microsoft.Margin = New System.Windows.Forms.Padding(4)
        Me.BTN_Redirect_Link_Microsoft.Name = "BTN_Redirect_Link_Microsoft"
        Me.BTN_Redirect_Link_Microsoft.Size = New System.Drawing.Size(152, 38)
        Me.BTN_Redirect_Link_Microsoft.TabIndex = 594
        Me.BTN_Redirect_Link_Microsoft.Text = "Encrypter"
        '
        'btn_Reset
        '
        Me.btn_Reset.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Reset.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Reset.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Reset.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Reset.Appearance.Options.UseBackColor = True
        Me.btn_Reset.Appearance.Options.UseBorderColor = True
        Me.btn_Reset.Appearance.Options.UseFont = True
        Me.btn_Reset.Appearance.Options.UseForeColor = True
        Me.btn_Reset.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn_Reset.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn_Reset.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Reset.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn_Reset.AppearanceDisabled.Options.UseBackColor = True
        Me.btn_Reset.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn_Reset.AppearanceDisabled.Options.UseFont = True
        Me.btn_Reset.AppearanceDisabled.Options.UseForeColor = True
        Me.btn_Reset.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn_Reset.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Reset.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn_Reset.AppearanceHovered.Options.UseBackColor = True
        Me.btn_Reset.AppearanceHovered.Options.UseBorderColor = True
        Me.btn_Reset.AppearanceHovered.Options.UseForeColor = True
        Me.btn_Reset.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Reset.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Reset.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn_Reset.AppearancePressed.Options.UseBackColor = True
        Me.btn_Reset.AppearancePressed.Options.UseBorderColor = True
        Me.btn_Reset.AppearancePressed.Options.UseForeColor = True
        Me.btn_Reset.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.btn_Reset.Location = New System.Drawing.Point(563, 59)
        Me.btn_Reset.Margin = New System.Windows.Forms.Padding(4)
        Me.btn_Reset.Name = "btn_Reset"
        Me.btn_Reset.Size = New System.Drawing.Size(152, 38)
        Me.btn_Reset.TabIndex = 595
        Me.btn_Reset.Text = "Reset All"
        '
        'btn_Save
        '
        Me.btn_Save.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Options.UseBackColor = True
        Me.btn_Save.Appearance.Options.UseBorderColor = True
        Me.btn_Save.Appearance.Options.UseFont = True
        Me.btn_Save.Appearance.Options.UseForeColor = True
        Me.btn_Save.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn_Save.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn_Save.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn_Save.AppearanceDisabled.Options.UseBackColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseFont = True
        Me.btn_Save.AppearanceDisabled.Options.UseForeColor = True
        Me.btn_Save.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn_Save.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn_Save.AppearanceHovered.Options.UseBackColor = True
        Me.btn_Save.AppearanceHovered.Options.UseBorderColor = True
        Me.btn_Save.AppearanceHovered.Options.UseForeColor = True
        Me.btn_Save.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn_Save.AppearancePressed.Options.UseBackColor = True
        Me.btn_Save.AppearancePressed.Options.UseBorderColor = True
        Me.btn_Save.AppearancePressed.Options.UseForeColor = True
        Me.btn_Save.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.settings32x32
        Me.btn_Save.Location = New System.Drawing.Point(231, 59)
        Me.btn_Save.Margin = New System.Windows.Forms.Padding(4)
        Me.btn_Save.Name = "btn_Save"
        Me.btn_Save.Size = New System.Drawing.Size(152, 38)
        Me.btn_Save.TabIndex = 596
        Me.btn_Save.Text = "Save Settings"
        '
        'BntURLCOpy
        '
        Me.BntURLCOpy.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntURLCOpy.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntURLCOpy.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntURLCOpy.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntURLCOpy.Appearance.Options.UseBackColor = True
        Me.BntURLCOpy.Appearance.Options.UseBorderColor = True
        Me.BntURLCOpy.Appearance.Options.UseFont = True
        Me.BntURLCOpy.Appearance.Options.UseForeColor = True
        Me.BntURLCOpy.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntURLCOpy.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntURLCOpy.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntURLCOpy.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntURLCOpy.AppearanceDisabled.Options.UseBackColor = True
        Me.BntURLCOpy.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntURLCOpy.AppearanceDisabled.Options.UseFont = True
        Me.BntURLCOpy.AppearanceDisabled.Options.UseForeColor = True
        Me.BntURLCOpy.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntURLCOpy.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntURLCOpy.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntURLCOpy.AppearanceHovered.Options.UseBackColor = True
        Me.BntURLCOpy.AppearanceHovered.Options.UseBorderColor = True
        Me.BntURLCOpy.AppearanceHovered.Options.UseForeColor = True
        Me.BntURLCOpy.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntURLCOpy.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntURLCOpy.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntURLCOpy.AppearancePressed.Options.UseBackColor = True
        Me.BntURLCOpy.AppearancePressed.Options.UseBorderColor = True
        Me.BntURLCOpy.AppearancePressed.Options.UseForeColor = True
        Me.BntURLCOpy.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy32x32
        Me.BntURLCOpy.Location = New System.Drawing.Point(397, 59)
        Me.BntURLCOpy.Margin = New System.Windows.Forms.Padding(4)
        Me.BntURLCOpy.Name = "BntURLCOpy"
        Me.BntURLCOpy.Size = New System.Drawing.Size(152, 38)
        Me.BntURLCOpy.TabIndex = 597
        Me.BntURLCOpy.Text = "Copy URL"
        '
        'GroupControl5
        '
        Me.GroupControl5.Controls.Add(Me.BntURLCOpy)
        Me.GroupControl5.Controls.Add(Me.btn_Save)
        Me.GroupControl5.Controls.Add(Me.btn_Reset)
        Me.GroupControl5.Controls.Add(Me.BTN_Redirect_Link_Microsoft)
        Me.GroupControl5.Location = New System.Drawing.Point(189, 437)
        Me.GroupControl5.Name = "GroupControl5"
        Me.GroupControl5.Size = New System.Drawing.Size(763, 139)
        Me.GroupControl5.TabIndex = 578
        Me.GroupControl5.Text = "Step 5 Build"
        '
        'frmRedirectSpider
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1223, 611)
        Me.Controls.Add(Me.GroupControl5)
        Me.Controls.Add(Me.GroupControl4)
        Me.Controls.Add(Me.GroupControl3)
        Me.Controls.Add(Me.GroupControl1)
        Me.Controls.Add(Me.GroupControl2)
        Me.Controls.Add(Me.RichTextBox2)
        Me.Controls.Add(Me.RichTextBox1)
        Me.Controls.Add(Me.PictureBox1)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.delete_hyperlink_16x16
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmRedirectSpider"
        Me.ShowInTaskbar = False
        Me.Text = "Redirect With Encoded Link"
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        CType(Me.txtUploadeCpanel.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl3.ResumeLayout(False)
        Me.GroupControl3.PerformLayout()
        CType(Me.txtLinkPageCpanel.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl4.ResumeLayout(False)
        Me.GroupControl4.PerformLayout()
        CType(Me.LinkEncodeCpanel.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl5.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents RichTextBox2 As RichTextBox
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Bnt_Download As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Clear3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtUploadeCpanel As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label9 As Label
    Friend WithEvents GroupControl3 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtLinkPageCpanel As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label1 As Label
    Friend WithEvents GroupControl4 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents SimpleButton3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LinkEncodeCpanel As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label4 As Label
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents BTN_Redirect_Link_Microsoft As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btn_Reset As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btn_Save As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntURLCOpy As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupControl5 As DevExpress.XtraEditors.GroupControl
End Class
