﻿<?xml version='1.0' encoding='UTF-8'?>
<svg x="0px" y="0px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" id="Layer_1" style="enable-background:new 0 0 32 32">
  <style type="text/css">
	.Red{fill:#D11C1C;}
	.Black{fill:#727272;}
	.Blue{fill:#1177D7;}
	.Green{fill:#039C23;}
	.Yellow{fill:#FFB115;}
	.White{fill:#FFFFFF;}
	.st0{opacity:0.5;}
	.st1{opacity:0.75;}
	.st2{opacity:0.25;}
</style>
  <g id="OpenCalendar_1_">
    <g class="st0">
      <path d="M10,26H6v-4h4V26z M16,22h-4v4h4V22z M22,22h-4v4h4V22z M28,22h-4v4h4V22z M22,10h-4v4h4V10z M28,10h-4v4h4    V10z M28,16h-4v4h4V16z M16,16h-4v4h4V16z M10,16H6v4h4V16z M16,10h-4v4h4V10z" class="Black" />
    </g>
    <path d="M31,4H16v4h14v20H4V14v-4H2v19c0,0.6,0.4,1,1,1h28c0.6,0,1-0.4,1-1V5C32,4.4,31.6,4,31,4z" class="Black" />
    <polygon points="14,4 10,4 10,0 6,0 6,4 2,4 2,8 6,8 6,12 10,12 10,8 14,8  " class="Green" />
    <rect x="18" y="16" width="4" height="4" class="Red" />
  </g>
</svg>