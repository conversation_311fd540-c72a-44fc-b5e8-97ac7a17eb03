﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections</name>
  </assembly>
  <members>
    <member name="T:System.Collections.BitArray">
      <summary>管理位值的压缩数组，该值表示为布尔值，其中 true 表示位是打开的 (1)，false 表示位是关闭的 (0)。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Boolean[])">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 类的新实例，该实例包含从布尔值指定数组复制的位值。</summary>
      <param name="values">要复制的布尔值数组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Byte[])">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 类的新实例，该实例包含从指定的字节数组复制的位值。</summary>
      <param name="bytes">字节数组包含要复制的值，在这里每个字节代表八个连续位。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="bytes" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Collections.BitArray)">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 类的新实例，该实例包含从指定 <see cref="T:System.Collections.BitArray" /> 复制的位值。</summary>
      <param name="bits">要复制的 <see cref="T:System.Collections.BitArray" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bits" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 类的新实例，该实例可拥有指定数目的位值，位值最初设置为 false。</summary>
      <param name="length">新 <see cref="T:System.Collections.BitArray" /> 中位值的数目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32,System.Boolean)">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 类的新实例，该实例可拥有指定数目的位值，位值最初设置为指定值。</summary>
      <param name="length">新 <see cref="T:System.Collections.BitArray" /> 中位值的数目。</param>
      <param name="defaultValue">要分配给每个比特位的布尔值。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32[])">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 类的新实例，该实例包含从指定的 32 位整数数组复制的位值。</summary>
      <param name="values">整数数组包含要复制的值，在这里每个整数代表 32 个连续位。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="values" /> is greater than <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Collections.BitArray.And(System.Collections.BitArray)">
      <summary>针对指定的 <see cref="T:System.Collections.BitArray" /> 中的相应元素对当前 <see cref="T:System.Collections.BitArray" /> 中的元素执行按位“与”运算。</summary>
      <returns>当前实例包含针对指定的 <see cref="T:System.Collections.BitArray" /> 中的相应元素对当前 <see cref="T:System.Collections.BitArray" /> 中的元素执行按位“与”运算的结果。</returns>
      <param name="value">用其执行按位“与”运算的 <see cref="T:System.Collections.BitArray" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Get(System.Int32)">
      <summary>获取 <see cref="T:System.Collections.BitArray" /> 中特定位置处的位值。</summary>
      <returns>在 <paramref name="index" /> 位置处的位的值。</returns>
      <param name="index">要获取的值的从零开始索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.BitArray" /> 的枚举数。</summary>
      <returns>一个用于整个 <see cref="T:System.Collections.BitArray" /> 的 <see cref="T:System.Collections.IEnumerator" />。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Item(System.Int32)">
      <summary>获取或设置 <see cref="T:System.Collections.BitArray" /> 中特定位置处的位值。</summary>
      <returns>在 <paramref name="index" /> 位置处的位的值。</returns>
      <param name="index">要获取或设置的值的从零开始索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.BitArray.Count" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Length">
      <summary>获取或设置 <see cref="T:System.Collections.BitArray" /> 中的元素数。</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" /> 中元素的数目。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is set to a value that is less than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Not">
      <summary>反转当前 <see cref="T:System.Collections.BitArray" /> 中的所有位值，以便将设置为 true 的元素更改为 false；将设置为 false 的元素更改为 true。</summary>
      <returns>具有已反转的位值的当前实例。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Or(System.Collections.BitArray)">
      <summary>针对指定的 <see cref="T:System.Collections.BitArray" /> 中的相应元素对当前 <see cref="T:System.Collections.BitArray" /> 中的元素执行按位“或”运算。</summary>
      <returns>当前实例包含针对指定的 <see cref="T:System.Collections.BitArray" /> 中的相应元素对当前 <see cref="T:System.Collections.BitArray" /> 中的元素执行按位“或”运算的结果。</returns>
      <param name="value">用其执行按位“或”运算的 <see cref="T:System.Collections.BitArray" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Set(System.Int32,System.Boolean)">
      <summary>将 <see cref="T:System.Collections.BitArray" /> 中特定位置处的位设置为指定值。</summary>
      <param name="index">要设置的位的从零开始索引。</param>
      <param name="value">要分配给比特位的布尔值。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.SetAll(System.Boolean)">
      <summary>将 <see cref="T:System.Collections.BitArray" /> 中所有位设置为指定值。</summary>
      <param name="value">要分配给所有位的布尔值。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的 <see cref="T:System.Array" /> 索引处开始，将 <see cref="T:System.Collections.BitArray" /> 的元素复制到一个 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">一维 <see cref="T:System.Array" />，它是从 <see cref="T:System.Collections.BitArray" /> 复制的元素的目标。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or- The number of elements in the source <see cref="T:System.Collections.BitArray" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.BitArray" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#Count">
      <summary>获取 <see cref="T:System.Collections.BitArray" /> 中的元素数。</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" /> 中元素的数目。</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.BitArray" /> 的访问（线程安全）。</summary>
      <returns>如果对 <see cref="T:System.Collections.BitArray" /> 的访问是同步的（线程安全），则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.BitArray" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.BitArray" /> 的访问的对象。</returns>
    </member>
    <member name="M:System.Collections.BitArray.Xor(System.Collections.BitArray)">
      <summary>针对指定的 <see cref="T:System.Collections.BitArray" /> 中的相应元素对当前 <see cref="T:System.Collections.BitArray" /> 中的元素执行按位“异或”运算。</summary>
      <returns>当前实例包含针对指定的 <see cref="T:System.Collections.BitArray" /> 中的相应元素对当前 <see cref="T:System.Collections.BitArray" /> 中的元素执行按位“异或”运算的结果。</returns>
      <param name="value">用其执行按位“异或”运算的 <see cref="T:System.Collections.BitArray" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Collections.StructuralComparisons">
      <summary>提供用于对两个集合对象执行结构比较的对象。</summary>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralComparer">
      <summary>获取可执行两个对象的结构比较的预定义对象。</summary>
      <returns>一个用于执行两个集合对象的结构比较的预定义对象。</returns>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralEqualityComparer">
      <summary>获取一个可比较两个对象的结构是否相等的预定义对象。</summary>
      <returns>一个用于比较两个集合对象的结构是否相等的预定义对象。</returns>
    </member>
    <member name="T:System.Collections.Generic.Comparer`1">
      <summary>为 <see cref="T:System.Collections.Generic.IComparer`1" /> 泛型接口的实现提供基类。</summary>
      <typeparam name="T">要比较的对象的类型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.Comparer`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Compare(`0,`0)">
      <summary>在派生类中重写时，对同一类型的两个对象执行比较并返回一个值，指示一个对象是小于、等于还是大于另一个对象。</summary>
      <returns>一个有符号整数，指示 <paramref name="x" /> 与 <paramref name="y" /> 的相对值，如下表所示。值含义小于零<paramref name="x" /> 小于 <paramref name="y" />。零<paramref name="x" /> 等于 <paramref name="y" />。大于零<paramref name="x" /> 大于 <paramref name="y" />。</returns>
      <param name="x">要比较的第一个对象。</param>
      <param name="y">要比较的第二个对象。</param>
      <exception cref="T:System.ArgumentException">类型 <paramref name="T" /> 没有实现 <see cref="T:System.IComparable`1" /> 泛型接口或 <see cref="T:System.IComparable" /> 接口。</exception>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Create(System.Comparison{`0})">
      <summary>用指定的比较创建一个比较器。 </summary>
      <returns>新的比较器。</returns>
      <param name="comparison">要使用的比较。</param>
    </member>
    <member name="P:System.Collections.Generic.Comparer`1.Default">
      <summary>返回由泛型参数指定的类型的默认排序顺序比较器。</summary>
      <returns>继承 <see cref="T:System.Collections.Generic.Comparer`1" /> 并作为 <paramref name="T" /> 类型的排序顺序比较器的对象。</returns>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>比较两个对象并返回一个值，该值指示一个对象小于、等于还是大于另一个对象。</summary>
      <returns>一个有符号整数，指示 <paramref name="x" /> 与 <paramref name="y" /> 的相对值，如下表所示。值含义小于零<paramref name="x" /> 小于 <paramref name="y" />。零<paramref name="x" /> 等于 <paramref name="y" />。大于零<paramref name="x" /> 大于 <paramref name="y" />。</returns>
      <param name="x">要比较的第一个对象。</param>
      <param name="y">要比较的第二个对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> 或 <paramref name="y" /> 所属的类型无法被强制转换为类型 <paramref name="T" />。- 或 -<paramref name="x" /> 和 <paramref name="y" /> 不实现 <see cref="T:System.IComparable`1" /> 泛型接口或 <see cref="T:System.IComparable" /> 接口。</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2">
      <summary>表示键和值的集合。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <typeparam name="TKey">字典中的键的类型。</typeparam>
      <typeparam name="TValue">字典中的值的类型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 类的新实例，该实例为空，具有默认的初始容量并为键类型使用默认的相等比较器。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 类的新实例，该实例包含从指定的 <see cref="T:System.Collections.Generic.IDictionary`2" /> 复制的元素并为键类型使用默认的相等比较器。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" />，它的元素被复制到新 <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一个或多个重复键。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 类的新实例，该实例包含从指定的 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中复制的元素并使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" />，它的元素被复制到新 <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <param name="comparer">比较键时要使用的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 实现，或者为 null，以便为键类型使用默认的 <see cref="T:System.Collections.Generic.EqualityComparer`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一个或多个重复键。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 类的新实例，该实例为空，具有默认的初始容量并使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</summary>
      <param name="comparer">比较键时要使用的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 实现，或者为 null，以便为键类型使用默认的 <see cref="T:System.Collections.Generic.EqualityComparer`1" />。</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 类的新实例，该实例为空，具有指定的初始容量并为键类型使用默认的相等比较器。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 可包含的初始元素数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> 小于 0。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 类的新实例，该实例为空，具有指定的初始容量并使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 可包含的初始元素数。</param>
      <param name="comparer">比较键时要使用的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 实现，或者为 null，以便为键类型使用默认的 <see cref="T:System.Collections.Generic.EqualityComparer`1" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> 小于 0。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Add(`0,`1)">
      <summary>将指定的键和值添加到字典中。</summary>
      <param name="key">要添加的元素的键。</param>
      <param name="value">要添加的元素的值。对于引用类型，该值可以为 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 中已存在具有相同键的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Clear">
      <summary>将所有键和值从 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中移除。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Comparer">
      <summary>获取用于确定字典中的键是否相等的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 泛型接口实现，它用于确定当前 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的键是否相等并为键提供哈希值。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsKey(`0)">
      <summary>确定是否 <see cref="T:System.Collections.Generic.Dictionary`2" /> 包含指定键。</summary>
      <returns>如果 true 包含具有指定键的元素，则为 <see cref="T:System.Collections.Generic.Dictionary`2" />；否则为 false。</returns>
      <param name="key">要在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中定位的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsValue(`1)">
      <summary>确定 <see cref="T:System.Collections.Generic.Dictionary`2" /> 是否包含特定值。</summary>
      <returns>如果 true 包含具有指定值的元素，则为 <see cref="T:System.Collections.Generic.Dictionary`2" />；否则为 false。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中定位的值。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Count">
      <summary>获取包含在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的键/值对的数目。</summary>
      <returns>包含在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的键/值对的数目。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的枚举数。</summary>
      <returns>用于 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的 <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> 结构。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Item(`0)">
      <summary>获取或设置与指定的键关联的值。</summary>
      <returns>与指定的键相关联的值。如果指定键未找到，则 Get 操作引发 <see cref="T:System.Collections.Generic.KeyNotFoundException" />，而 Set 操作创建一个带指定键的新元素。</returns>
      <param name="key">要获取或设置的值的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">已检索该属性，并且集合中不存在 <paramref name="key" />。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Keys">
      <summary>获得一个包含 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的键的集合。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />，包含 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的键。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Remove(`0)">
      <summary>将带有指定键的值从 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中移除。</summary>
      <returns>如果成功找到并移除该元素，则为 true；否则为 false。如果在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中没有找到 <paramref name="key" />，则此方法返回 false。</returns>
      <param name="key">要移除的元素的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>添加指定值到带有指定键的 <see cref="T:System.Collections.Generic.ICollection`1" />。</summary>
      <param name="keyValuePair">表示要添加到 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的键和值的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 结构。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" /> 的键为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 中已存在具有相同键的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>确定是否 <see cref="T:System.Collections.Generic.ICollection`1" /> 包含一个指定键和值。</summary>
      <returns>如果在 true 中找到 <paramref name="keyValuePair" />，则为 <see cref="T:System.Collections.Generic.ICollection`1" />；否则为 false。</returns>
      <param name="keyValuePair">要在 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 中定位的 <see cref="T:System.Collections.Generic.ICollection`1" /> 结构。</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>从特定的数组索引处开始，将 <see cref="T:System.Collections.Generic.ICollection`1" /> 的元素复制到类型 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 的一个数组。</summary>
      <param name="array">类型 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 的一维数组，它是从 <see cref="T:System.Collections.Generic.ICollection`1" /> 复制的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 元素的目标。该数组的索引必须从零开始。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此处开始复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">源 <see cref="T:System.Collections.Generic.ICollection`1" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示字典是否为只读。</summary>
      <returns>如果 true 是只读的，则为 <see cref="T:System.Collections.Generic.ICollection`1" />；否则为 false。在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>从字典中移除键和值。</summary>
      <returns>如果成功找到并移除 <paramref name="keyValuePair" /> 所表示的键和值，则为 true；否则为 false。如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中没有找到 <paramref name="keyValuePair" />，则此方法返回 false。</returns>
      <param name="keyValuePair">表示要从 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中移除的键和值的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 结构。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>获取包含 <see cref="T:System.Collections.Generic.ICollection`1" /> 的键的 <see cref="T:System.Collections.Generic.IDictionary`2" />。</summary>
      <returns>包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的键的类型 <paramref name="TKey" /> 的 <see cref="T:System.Collections.Generic.ICollection`1" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>获取一个 <see cref="T:System.Collections.Generic.ICollection`1" />，它包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值。</summary>
      <returns>包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值的类型 <paramref name="TValue" /> 的 <see cref="T:System.Collections.Generic.ICollection`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>获得一个包含 <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> 的键的集合。</summary>
      <returns>一个包含 <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> 的键的集合。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>获得一个包含 <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> 的值的集合。</summary>
      <returns>一个包含 <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> 的值的集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的数组索引处开始，将 <see cref="T:System.Collections.Generic.ICollection`1" /> 的元素复制到一个数组中。</summary>
      <param name="array">一维数组，用作从 <see cref="T:System.Collections.Generic.ICollection`1" /> 复制的元素的目标位置。该数组的索引必须从零开始。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此处开始复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多维的。- 或 -<paramref name="array" /> 没有从零开始的索引。- 或 -源 <see cref="T:System.Collections.Generic.ICollection`1" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。- 或 -源 <see cref="T:System.Collections.Generic.ICollection`1" /> 的类型无法自动转换为目标 <paramref name="array" /> 的类型。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>如果对 true 的访问是同步的（线程安全），则为 <see cref="T:System.Collections.ICollection" />；否则为 false。在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>将指定的键和值添加到字典中。</summary>
      <param name="key">要用作键的对象。</param>
      <param name="value">要用作值的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> 属于不能分配给 <paramref name="TKey" /> 的键类型 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的类型。- 或 -<paramref name="value" /> 属于不能分配给 <paramref name="TValue" />（<see cref="T:System.Collections.Generic.Dictionary`2" /> 中的值的类型）的类型。- 或 -<see cref="T:System.Collections.Generic.Dictionary`2" /> 中已存在相同键的值。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>确定是否 <see cref="T:System.Collections.IDictionary" /> 包含带有指定键的元素。</summary>
      <returns>如果 true 包含具有指定键的元素，则为 <see cref="T:System.Collections.IDictionary" />；否则为 false。</returns>
      <param name="key">要在 <see cref="T:System.Collections.IDictionary" /> 中定位的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>返回 <see cref="T:System.Collections.IDictionaryEnumerator" /> 的 <see cref="T:System.Collections.IDictionary" />。</summary>
      <returns>用于 <see cref="T:System.Collections.IDictionaryEnumerator" /> 的 <see cref="T:System.Collections.IDictionary" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.IDictionary" /> 是否具有固定大小。</summary>
      <returns>如果 true 具有固定大小，则为 <see cref="T:System.Collections.IDictionary" />；否则为 false。在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.IDictionary" /> 是否为只读。</summary>
      <returns>如果 true 是只读的，则为 <see cref="T:System.Collections.IDictionary" />；否则为 false。在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>获取或设置具有指定键的值。</summary>
      <returns>如果 <paramref name="key" /> 不在字典中或 <paramref name="key" /> 的类型属于不可分配给 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的键类型 <paramref name="TKey" />，则为与指定的键关联的值或为 null。</returns>
      <param name="key">要获取的值的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">正在分配值，并且 <paramref name="key" /> 属于不能分配给 <paramref name="TKey" /> 的键类型 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的类型。- 或 -正在分配值，并且 <paramref name="value" /> 属于不能分配给 <paramref name="TValue" /> 的值类型 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的类型。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Keys">
      <summary>获取包含 <see cref="T:System.Collections.ICollection" /> 的键的 <see cref="T:System.Collections.IDictionary" />。</summary>
      <returns>一个 <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 的键。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>从 <see cref="T:System.Collections.IDictionary" /> 中移除带有指定键的元素。</summary>
      <param name="key">要移除的元素的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Values">
      <summary>获取一个 <see cref="T:System.Collections.ICollection" />，它包含 <see cref="T:System.Collections.IDictionary" /> 中的值。</summary>
      <returns>一个 <see cref="T:System.Collections.ICollection" />，它包含 <see cref="T:System.Collections.IDictionary" /> 中的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.TryGetValue(`0,`1@)">
      <summary>获取与指定键关联的值。</summary>
      <returns>如果 true 包含具有指定键的元素，则为 <see cref="T:System.Collections.Generic.Dictionary`2" />；否则为 false。</returns>
      <param name="key">要获取的值的键。</param>
      <param name="value">当此方法返回时，如果找到指定键，则包含与该键相关的值；否则包含 <paramref name="value" /> 参数类型的默认值。此参数未经初始化即被传递。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Values">
      <summary>获得一个包含 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的值的集合。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />，包含 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的值。</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 中位于枚举数当前位置的元素。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.Dispose">
      <summary>释放由 <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.MoveNext">
      <summary>使枚举数前进到 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>字典中位于枚举数当前位置的元素，如 <see cref="T:System.Collections.DictionaryEntry" />。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>获取位于枚举数当前位置的元素的键。</summary>
      <returns>字典中位于枚举数当前位置的元素的键。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>获取位于枚举数当前位置的元素的值。</summary>
      <returns>字典中位于枚举数当前位置的元素的值。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素，如 <see cref="T:System.Object" />。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection">
      <summary>表示 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中键的集合。此类不能被继承。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 类的新实例，该实例反映指定的 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的键。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.Dictionary`2" />，其键反映在新的 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>从指定数组索引开始将 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 元素复制到现有一维 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">作为从 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 复制的元素的目标位置的一维 <see cref="T:System.Array" />。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。</exception>
      <exception cref="T:System.ArgumentException">源 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Count">
      <summary>获取 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 中包含的元素数。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 中包含的元素个数。检索此属性的值的运算复杂度为 O(1)。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的枚举数。</summary>
      <returns>用于 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>将某项添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 中。此实现总是引发 <see cref="T:System.NotSupportedException" />。</summary>
      <param name="item">要添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 的对象。</param>
      <exception cref="T:System.NotSupportedException">总是引发。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除所有项。此实现总是引发 <see cref="T:System.NotSupportedException" />。</summary>
      <exception cref="T:System.NotSupportedException">总是引发。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>确定 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含特定值。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找到 <paramref name="item" />，则为 true；否则为 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中定位的对象。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否为只读。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 为只读，则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的默认实现中，此属性始终返回 true。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除特定对象的第一个匹配项。此实现总是引发 <see cref="T:System.NotSupportedException" />。</summary>
      <returns>如果已从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中成功移除 <paramref name="item" />，则为 true；否则为 false。如果在原始 <see cref="T:System.Collections.Generic.ICollection`1" /> 中没有找到 <paramref name="item" />，此方法也会返回 false。</returns>
      <param name="item">要从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除的对象。</param>
      <exception cref="T:System.NotSupportedException">总是引发。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的 <see cref="T:System.Array" /> 索引处开始，将 <see cref="T:System.Collections.ICollection" /> 的元素复制到一个 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">作为从 <see cref="T:System.Collections.ICollection" /> 复制的元素的目标的一维 <see cref="T:System.Array" />。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多维的。- 或 -<paramref name="array" /> 没有从零开始的索引。- 或 -源 <see cref="T:System.Collections.ICollection" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。- 或 -源 <see cref="T:System.Collections.ICollection" /> 的类型无法自动转换为目标 <paramref name="array" /> 的类型。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>如果对 <see cref="T:System.Collections.ICollection" /> 的访问是同步的（线程安全），则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。在 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的默认实现中，此属性始终返回当前实例。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 中位于该枚举数当前位置的元素。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>释放由 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>使枚举数前进到 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的下一个元素。</summary>
      <returns>如果枚举数已成功地推进到下一个元素，则为 true；如果枚举数传递到集合的末尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection">
      <summary>表示 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中值的集合。此类不能被继承。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 类的新实例，该实例反映指定的 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的值。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.Dictionary`2" />，其值反映在新的 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>从指定数组索引开始将 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 元素复制到现有一维 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">作为从 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 复制的元素的目标位置的一维 <see cref="T:System.Array" />。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。</exception>
      <exception cref="T:System.ArgumentException">源 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Count">
      <summary>获取 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 中包含的元素数。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 中包含的元素个数。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的枚举数。</summary>
      <returns>用于 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>将某项添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 中。此实现总是引发 <see cref="T:System.NotSupportedException" />。</summary>
      <param name="item">要添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 的对象。</param>
      <exception cref="T:System.NotSupportedException">总是引发。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除所有项。此实现总是引发 <see cref="T:System.NotSupportedException" />。</summary>
      <exception cref="T:System.NotSupportedException">总是引发。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>确定 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含特定值。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找到 <paramref name="item" />，则为 true；否则为 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中定位的对象。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否为只读。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 为只读，则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的默认实现中，此属性始终返回 true。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除特定对象的第一个匹配项。此实现总是引发 <see cref="T:System.NotSupportedException" />。</summary>
      <returns>如果已从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中成功移除 <paramref name="item" />，则为 true；否则为 false。如果在原始 <see cref="T:System.Collections.Generic.ICollection`1" /> 中没有找到 <paramref name="item" />，此方法也会返回 false。</returns>
      <param name="item">要从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除的对象。</param>
      <exception cref="T:System.NotSupportedException">总是引发。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的 <see cref="T:System.Array" /> 索引处开始，将 <see cref="T:System.Collections.ICollection" /> 的元素复制到一个 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">作为从 <see cref="T:System.Collections.ICollection" /> 复制的元素的目标的一维 <see cref="T:System.Array" />。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多维的。- 或 -<paramref name="array" /> 没有从零开始的索引。- 或 -源 <see cref="T:System.Collections.ICollection" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。- 或 -源 <see cref="T:System.Collections.ICollection" /> 的类型无法自动转换为目标 <paramref name="array" /> 的类型。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>如果对 <see cref="T:System.Collections.ICollection" /> 的访问是同步的（线程安全），则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。在 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的默认实现中，此属性始终返回当前实例。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 中位于枚举数当前位置的元素。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>释放由 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>使枚举器前进到 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Collections.Generic.EqualityComparer`1">
      <summary>为 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 泛型接口的实现提供基类。</summary>
      <typeparam name="T">要比较的对象的类型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Collections.Generic.EqualityComparer`1.Default">
      <summary>返回一个默认的相等比较器，用于比较此泛型参数指定的类型。</summary>
      <returns>用于类型 <paramref name="T" /> 的 <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 类的默认实例。</returns>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.Equals(`0,`0)">
      <summary>在派生类中重写时，确定类型 <paramref name="T" /> 的两个对象是否相等。</summary>
      <returns>如果指定的对象相等，则为 true；否则为 false。</returns>
      <param name="x">要比较的第一个对象。</param>
      <param name="y">要比较的第二个对象。</param>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.GetHashCode(`0)">
      <summary>在派生类中重写时，用作指定对象的哈希算法和数据结构（如哈希表）的哈希函数。</summary>
      <returns>指定对象的哈希代码。</returns>
      <param name="obj">要为其获取哈希代码的对象。</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>确定指定的对象是否相等。</summary>
      <returns>如果指定的对象相等，则为 true；否则为 false。</returns>
      <param name="x">要比较的第一个对象。</param>
      <param name="y">要比较的第二个对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> or <paramref name="y" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>返回指定对象的哈希代码。</summary>
      <returns>指定对象的哈希代码。</returns>
      <param name="obj">将为其返回哈希代码的 <see cref="T:System.Object" />。</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.-or-<paramref name="obj" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1">
      <summary>表示值的集。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <typeparam name="T">哈希集中的元素类型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.HashSet`1" /> 类的一个新实例，该实例为空并使用集类型的默认相等比较器。</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.HashSet`1" /> 类的一个新实例，该实例使用集类型的默认相等比较器，包含从指定的集合复制的元素，并且有足够的容量容纳所复制的这些元素。</summary>
      <param name="collection">其元素被复制到新集中的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.HashSet`1" /> 类的一个新实例，该实例使用集类型的指定相等比较器，包含从指定的集合复制的元素，并且有足够的容量容纳所复制的这些元素。</summary>
      <param name="collection">其元素被复制到新集中的集合。</param>
      <param name="comparer">在比较集中的值时使用的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 实现，或为 null 以使用集类型的默认 <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 实现。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.HashSet`1" /> 类的一个新实例，该实例为空并使用集类型的指定相等比较器。</summary>
      <param name="comparer">在比较集中的值时使用的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 实现，或为 null 以使用集类型的默认 <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 实现。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Add(`0)">
      <summary>将指定的元素添加到集中。</summary>
      <returns>如果该元素添加到 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象中则为 true；如果该元素已存在则为 false。</returns>
      <param name="item">要添加到集中的元素。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Clear">
      <summary>从 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象中移除所有元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Comparer">
      <summary>获取用于确定集中的值是否相等的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对象。</summary>
      <returns>用于确定集中的值是否相等的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对象。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Contains(`0)">
      <summary>确定 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象是否包含指定的元素。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象包含指定的元素，则为 true；否则为 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象中查找的元素。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[])">
      <summary>将 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象的元素复制到数组中。</summary>
      <param name="array">作为从 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象复制的元素的目标的一维数组。该数组的索引必须从零开始。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32)">
      <summary>从指定数组索引处开始，将 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象的元素复制到数组中。</summary>
      <param name="array">作为从 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象复制的元素的目标的一维数组。该数组的索引必须从零开始。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，从此处开始复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> 大于目标 <paramref name="array" /> 的长度。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>从指定数组索引处开始，将 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象的指定数目的元素复制到数组中。</summary>
      <param name="array">作为从 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象复制的元素的目标的一维数组。该数组的索引必须从零开始。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，从此处开始复制。</param>
      <param name="count">要复制到 <paramref name="array" /> 的元素的数目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小于 0。- 或 -<paramref name="count" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> 大于目标 <paramref name="array" /> 的长度。- 或 -<paramref name="count" /> 大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾的可用空间。</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Count">
      <summary>获取集中包含的元素数。</summary>
      <returns>集中包含的元素数。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>从当前 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象中移除指定集合中的所有元素。</summary>
      <param name="other">要从 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象中移除的项的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象的枚举器。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 对象的 <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> 对象。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改当前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象，以仅包含该对象和指定集合中存在的元素。</summary>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象是否为指定集合的真子集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象是 <paramref name="other" /> 的真子集，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象是否为指定集合的真超集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象是 <paramref name="other" /> 的真超集，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象是否为指定集合的子集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象是 <paramref name="other" /> 的子集，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象是否为指定集合的超集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象是 <paramref name="other" /> 的超集，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定是否当前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象和指定的集合共享通用元素。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象和 <paramref name="other" /> 共享至少一个公共元素，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Remove(`0)">
      <summary>从 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象移除指定元素。</summary>
      <returns>如果成功找到并移除该元素，则为 true；否则为 false。如果未在 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象中找到 <paramref name="item" />，则此方法返回 false。</returns>
      <param name="item">要移除的元素。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>从 <see cref="T:System.Collections.Generic.HashSet`1" /> 集合中移除与指定的谓词所定义的条件相匹配的所有元素。</summary>
      <returns>从 <see cref="T:System.Collections.Generic.HashSet`1" /> 集合中移除的元素数。</returns>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要移除的元素应满足的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定是否 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象和指定集合包含相同的元素。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象与 <paramref name="other" /> 相等，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改当前 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象以仅包含存在于该对象中或存在于指定集合中的元素（但并非两者）。</summary>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>向 <see cref="T:System.Collections.Generic.ICollection`1" /> 对象添加一个项。</summary>
      <param name="item">要添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 对象中的对象。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 为只读。</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示集合是否为只读。</summary>
      <returns>如果该集合为只读，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>一个可用于循环访问集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" /> 对象。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>一个可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" /> 对象。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.TrimExcess">
      <summary>将 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象的容量设置为它包含的实际元素数，向上舍入为接近的特定于实现的值。</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改当前 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象以包含存在于该对象中、指定集合中或两者中的所有元素。</summary>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.HashSet`1" /> 对象的元素。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 集合中位于枚举器当前位置的元素。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.Dispose">
      <summary>释放 <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> 对象使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.MoveNext">
      <summary>将枚举器前进到 <see cref="T:System.Collections.Generic.HashSet`1" /> 集合的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素，如 <see cref="T:System.Object" />。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1">
      <summary>表示双向链接列表。</summary>
      <typeparam name="T">指定链表的元素类型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor">
      <summary>初始化为空的 <see cref="T:System.Collections.Generic.LinkedList`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.LinkedList`1" /> 类的新实例，该实例包含从指定的 <see cref="T:System.Collections.IEnumerable" /> 中复制的元素并且其容量足以容纳所复制的元素数。</summary>
      <param name="collection">
        <see cref="T:System.Collections.IEnumerable" />，其元素被复制到新的 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中指定的现有节点后添加指定的新节点。</summary>
      <param name="node">要在其后插入 <paramref name="newNode" /> 的 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="newNode">要添加到 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 为 null。- 或 -<paramref name="newNode" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 不在当前 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。- 或 -<paramref name="newNode" /> 属于另一个 <see cref="T:System.Collections.Generic.LinkedList`1" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中指定的现有节点后添加包含指定值的新节点。</summary>
      <returns>包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="node">要在其后插入包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 的 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="value">要添加到 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 不在当前 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中指定的现有节点前添加指定的新节点。</summary>
      <param name="node">要在其前插入 <paramref name="newNode" /> 的 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="newNode">要添加到 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 为 null。- 或 -<paramref name="newNode" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 不在当前 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。- 或 -<paramref name="newNode" /> 属于另一个 <see cref="T:System.Collections.Generic.LinkedList`1" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中指定的现有节点前添加包含指定值的新节点。</summary>
      <returns>包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="node">要在其前插入包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 的 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="value">要添加到 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 不在当前 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(System.Collections.Generic.LinkedListNode{`0})">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的开头处添加指定的新节点。</summary>
      <param name="node">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的开头处添加的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 属于另一个 <see cref="T:System.Collections.Generic.LinkedList`1" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(`0)">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的开头处添加包含指定值的新节点。</summary>
      <returns>包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的开头处添加的值。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(System.Collections.Generic.LinkedListNode{`0})">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的结尾处添加指定的新节点。</summary>
      <param name="node">要添加至 <see cref="T:System.Collections.Generic.LinkedList`1" /> 结尾的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 属于另一个 <see cref="T:System.Collections.Generic.LinkedList`1" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(`0)">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的结尾处添加包含指定值的新节点。</summary>
      <returns>包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="value">要添加到 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的末尾的值。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Clear">
      <summary>从 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中移除所有节点。</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Contains(`0)">
      <summary>确定某值是否在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中找到 <paramref name="value" />，则为 true；否则为 false。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中定位的值。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.CopyTo(`0[],System.Int32)">
      <summary>从目标数组的指定索引处开始将整个 <see cref="T:System.Collections.Generic.LinkedList`1" /> 复制到兼容的一维 <see cref="T:System.Array" />。</summary>
      <param name="array">作为从 <see cref="T:System.Collections.Generic.LinkedList`1" /> 复制的元素的目标位置的一维 <see cref="T:System.Array" />。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。</exception>
      <exception cref="T:System.ArgumentException">源 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Count">
      <summary>获取 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中实际包含的节点数。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 中实际包含的节点数。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Find(`0)">
      <summary>查找包含指定值的第一个节点。</summary>
      <returns>如果找到，则为包含指定值的第一个 <see cref="T:System.Collections.Generic.LinkedListNode`1" />；否则为 null。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中定位的值。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.FindLast(`0)">
      <summary>查找包含指定值的最后一个节点。</summary>
      <returns>如果找到，则为包含指定值的最后一个 <see cref="T:System.Collections.Generic.LinkedListNode`1" />；否则为 null。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中定位的值。</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.First">
      <summary>获取 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的第一个节点。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 的第一个 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的枚举数。</summary>
      <returns>用于 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的 <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Last">
      <summary>获取 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的最后一个节点。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 的最后一个 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(System.Collections.Generic.LinkedListNode{`0})">
      <summary>从 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中移除指定的节点。</summary>
      <param name="node">要从 <see cref="T:System.Collections.Generic.LinkedList`1" /> 移除的 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 不在当前 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(`0)">
      <summary>从 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中移除指定值的第一个匹配项。</summary>
      <returns>如果成功移除包含 <paramref name="value" /> 的元素，则为 true；否则为 false。如果在原始 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中没有找到 <paramref name="value" />，此方法也会返回 false。</returns>
      <param name="value">要从 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中移除的值。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveFirst">
      <summary>移除位于 <see cref="T:System.Collections.Generic.LinkedList`1" /> 开头处的节点。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 为空。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveLast">
      <summary>移除位于 <see cref="T:System.Collections.Generic.LinkedList`1" /> 结尾处的节点。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 为空。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>将项添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 的结尾处。</summary>
      <param name="value">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 的结尾处添加的值。</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否为只读。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 为只读，则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的 <see cref="T:System.Array" /> 索引处开始，将 <see cref="T:System.Collections.ICollection" /> 的元素复制到一个 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">作为从 <see cref="T:System.Collections.ICollection" /> 复制的元素的目标的一维 <see cref="T:System.Array" />。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多维的。- 或 -<paramref name="array" /> 没有从零开始的索引。- 或 -源 <see cref="T:System.Collections.ICollection" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。- 或 -源 <see cref="T:System.Collections.ICollection" /> 的类型无法自动转换为目标 <paramref name="array" /> 的类型。</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>如果对 <see cref="T:System.Collections.ICollection" /> 的访问是同步的（线程安全），则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的默认实现中，此属性始终返回当前实例。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个将链表作为集合进行循环访问的枚举数。</summary>
      <returns>可用于将链表作为集合循环访问的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 中位于该枚举数当前位置的元素。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.Dispose">
      <summary>释放由 <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.MoveNext">
      <summary>使枚举数前进到 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。此类不能被继承。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedListNode`1">
      <summary>表示 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中的节点。此类不能被继承。</summary>
      <typeparam name="T">指定链表的元素类型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedListNode`1.#ctor(`0)">
      <summary>初始化 <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 类的新实例，该实例包含指定的值。</summary>
      <param name="value">要在 <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 中包含的值。</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.List">
      <summary>获取 <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 所属的 <see cref="T:System.Collections.Generic.LinkedList`1" />。</summary>
      <returns>对 <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 所属的 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的引用；或者如果 <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 未链接，则为 null。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Next">
      <summary>获取 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中的下一个节点。</summary>
      <returns>对 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中的下一个节点的引用；或者如果当前节点是 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的最后一个元素 (<see cref="P:System.Collections.Generic.LinkedList`1.Last" />)，则为 null。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Previous">
      <summary>获取 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中的上一个节点。</summary>
      <returns>对 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中的上一个节点的引用；或者如果当前节点是 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的第一个元素 (<see cref="P:System.Collections.Generic.LinkedList`1.First" />)，则为 null。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Value">
      <summary>获取节点中包含的值。</summary>
      <returns>节点中包含的值。</returns>
    </member>
    <member name="T:System.Collections.Generic.List`1">
      <summary>表示可通过索引访问的对象的强类型列表。提供用于对列表进行搜索、排序和操作的方法。若要浏览此类型的.NET Framework 源代码，请参阅参考源。</summary>
      <typeparam name="T">列表中元素的类型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.List`1" /> 类的新实例，该实例为空并且具有默认初始容量。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.List`1" /> 类的新实例，该实例包含从指定集合复制的元素并且具有足够的容量来容纳所复制的元素。</summary>
      <param name="collection">一个集合，其元素被复制到新列表中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Collections.Generic.List`1" /> 类的新实例，该实例为空并且具有指定的初始容量。</summary>
      <param name="capacity">新列表最初可以存储的元素数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> 小于 0。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Add(`0)">
      <summary>将对象添加到 <see cref="T:System.Collections.Generic.List`1" /> 的结尾处。</summary>
      <param name="item">要添加到 <see cref="T:System.Collections.Generic.List`1" /> 的末尾处的对象。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>将指定集合的元素添加到 <see cref="T:System.Collections.Generic.List`1" /> 的末尾。</summary>
      <param name="collection">一个集合，其元素应被添加到 <see cref="T:System.Collections.Generic.List`1" /> 的末尾。集合自身不能为 null，但它可以包含为 null 的元素（如果类型 <paramref name="T" /> 为引用类型）。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.AsReadOnly">
      <summary>返回当前集合的只读 <see cref="T:System.Collections.Generic.IList`1" /> 包装。</summary>
      <returns>作为当前 <see cref="T:System.Collections.Generic.List`1" /> 周围的只读包装的 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>使用指定的比较器在已排序 <see cref="T:System.Collections.Generic.List`1" /> 的某个元素范围中搜索元素，并返回该元素从零开始的索引。</summary>
      <returns>如果找到 <paramref name="item" />，则为已排序的 <see cref="T:System.Collections.Generic.List`1" /> 中 <paramref name="item" /> 的从零开始的索引；否则为一个负数，该负数是大于 <paramref name="item" /> 的第一个元素的索引的按位求补。如果没有更大的元素，则为 <see cref="P:System.Collections.Generic.List`1.Count" /> 的按位求补。</returns>
      <param name="index">要搜索范围的从零开始的起始索引。</param>
      <param name="count">要搜索的范围的长度。</param>
      <param name="item">要定位的对象。对于引用类型，该值可以为 null。</param>
      <param name="comparer">比较元素时要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现，或者为 null，表示使用默认比较器 <see cref="P:System.Collections.Generic.Comparer`1.Default" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。- 或 -<paramref name="count" /> 小于 0。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 和 <paramref name="count" /> 不表示 <see cref="T:System.Collections.Generic.List`1" /> 中的有效范围。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> 为 null，且默认比较器 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <paramref name="T" /> 类型的 <see cref="T:System.IComparable`1" /> 泛型接口或 <see cref="T:System.IComparable" /> 接口的实现。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0)">
      <summary>使用默认的比较器在整个已排序的 <see cref="T:System.Collections.Generic.List`1" /> 中搜索元素，并返回该元素从零开始的索引。</summary>
      <returns>如果找到 <paramref name="item" />，则为已排序的 <see cref="T:System.Collections.Generic.List`1" /> 中 <paramref name="item" /> 的从零开始的索引；否则为一个负数，该负数是大于 <paramref name="item" /> 的第一个元素的索引的按位求补。如果没有更大的元素，则为 <see cref="P:System.Collections.Generic.List`1.Count" /> 的按位求补。</returns>
      <param name="item">要定位的对象。对于引用类型，该值可以为 null。</param>
      <exception cref="T:System.InvalidOperationException">默认比较器 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <paramref name="T" /> 类型的 <see cref="T:System.IComparable`1" /> 泛型接口或 <see cref="T:System.IComparable" /> 接口的实现。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>使用指定的比较器在整个已排序的 <see cref="T:System.Collections.Generic.List`1" /> 中搜索元素，并返回该元素从零开始的索引。</summary>
      <returns>如果找到 <paramref name="item" />，则为已排序的 <see cref="T:System.Collections.Generic.List`1" /> 中 <paramref name="item" /> 的从零开始的索引；否则为一个负数，该负数是大于 <paramref name="item" /> 的第一个元素的索引的按位求补。如果没有更大的元素，则为 <see cref="P:System.Collections.Generic.List`1.Count" /> 的按位求补。</returns>
      <param name="item">要定位的对象。对于引用类型，该值可以为 null。</param>
      <param name="comparer">比较元素时要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现。- 或 -为 null 以使用默认比较器 <see cref="P:System.Collections.Generic.Comparer`1.Default" />。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> 为 null，且默认比较器 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <paramref name="T" /> 类型的 <see cref="T:System.IComparable`1" /> 泛型接口或 <see cref="T:System.IComparable" /> 接口的实现。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Capacity">
      <summary>获取或设置该内部数据结构在不调整大小的情况下能够容纳的元素总数。</summary>
      <returns>在需要调整大小之前 <see cref="T:System.Collections.Generic.List`1" /> 能够容纳的元素的数目。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.List`1.Capacity" /> 设置为小于 <see cref="P:System.Collections.Generic.List`1.Count" /> 的值。</exception>
      <exception cref="T:System.OutOfMemoryException">系统中没有足够的可用内存。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Clear">
      <summary>从 <see cref="T:System.Collections.Generic.List`1" /> 中移除所有元素。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Contains(`0)">
      <summary>确定某元素是否在 <see cref="T:System.Collections.Generic.List`1" /> 中。</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.List`1" />; otherwise, false.</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中定位的对象。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>将一定范围的元素从 <see cref="T:System.Collections.Generic.List`1" /> 复制到兼容的一维数组中，从目标数组的指定索引位置开始放置。</summary>
      <param name="index">源 <see cref="T:System.Collections.Generic.List`1" /> 中复制开始位置的从零开始的索引。</param>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <param name="count">要复制的元素数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。- 或 -<paramref name="arrayIndex" /> 小于 0。- 或 -<paramref name="count" /> 小于 0。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 等于或大于源 <see cref="T:System.Collections.Generic.List`1" /> 的 <see cref="P:System.Collections.Generic.List`1.Count" />。- 或 -从 <paramref name="index" /> 到源 <see cref="T:System.Collections.Generic.List`1" /> 的末尾的元素数大于从 <paramref name="arrayIndex" /> 到目标 <paramref name="array" /> 的末尾的可用空间。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[])">
      <summary>将整个 <see cref="T:System.Collections.Generic.List`1" /> 复制到兼容的一维数组中，从目标数组的开头开始放置。</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">源 <see cref="T:System.Collections.Generic.List`1" /> 中的元素数大于目标 <paramref name="array" /> 可包含的元素数。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[],System.Int32)">
      <summary>将整个 <see cref="T:System.Collections.Generic.List`1" /> 复制到兼容的一维数组中，从目标数组的指定索引位置开始放置。</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">源 <see cref="T:System.Collections.Generic.List`1" /> 中的元素数目大于从 <paramref name="arrayIndex" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Count">
      <summary>获取 <see cref="T:System.Collections.Generic.List`1" /> 中包含的元素数。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> 中包含的元素个数。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Exists(System.Predicate{`0})">
      <summary>确定 <see cref="T:System.Collections.Generic.List`1" /> 是否包含与指定谓词所定义的条件相匹配的元素。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.List`1" /> 包含一个或多个与指定谓词所定义的条件相匹配的元素，则为 true；否则为 false。</returns>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要搜索的元素应满足的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Find(System.Predicate{`0})">
      <summary>搜索与指定谓词所定义的条件相匹配的元素，并返回整个 <see cref="T:System.Collections.Generic.List`1" /> 中的第一个匹配元素。</summary>
      <returns>如果找到与指定谓词定义的条件匹配的第一个元素，则为该元素；否则为类型 <paramref name="T" /> 的默认值。</returns>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要搜索的元素的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindAll(System.Predicate{`0})">
      <summary>检索与指定谓词定义的条件匹配的所有元素。</summary>
      <returns>如果找到，则为一个 <see cref="T:System.Collections.Generic.List`1" />，其中包含与指定谓词所定义的条件相匹配的所有元素；否则为一个空 <see cref="T:System.Collections.Generic.List`1" />。</returns>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要搜索的元素应满足的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>搜索与指定谓词所定义的条件相匹配的一个元素，并返回 <see cref="T:System.Collections.Generic.List`1" /> 中从指定的索引开始、包含指定元素个数的元素范围内第一个匹配项的从零开始的索引。</summary>
      <returns>如果找到与 <paramref name="match" /> 定义的条件相匹配的第一个元素，则为该元素的从零开始的索引；否则为 -1。</returns>
      <param name="startIndex">从零开始的搜索的起始索引。</param>
      <param name="count">要搜索的部分中的元素数。</param>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要搜索的元素的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 不在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引范围内。- 或 -<paramref name="count" /> 小于 0。- 或 -<paramref name="startIndex" /> 和 <paramref name="count" /> 未指定 <see cref="T:System.Collections.Generic.List`1" /> 中的有效部分。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>搜索与指定谓词所定义的条件相匹配的元素，并返回 <see cref="T:System.Collections.Generic.List`1" /> 中从指定索引到最后一个元素的元素范围内第一个匹配项的从零开始的索引。</summary>
      <returns>如果找到与 <paramref name="match" /> 定义的条件相匹配的第一个元素，则为该元素的从零开始的索引；否则为 -1。</returns>
      <param name="startIndex">从零开始的搜索的起始索引。</param>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要搜索的元素的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 不在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引范围内。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Predicate{`0})">
      <summary>搜索与指定谓词所定义的条件相匹配的元素，并返回整个 <see cref="T:System.Collections.Generic.List`1" /> 中第一个匹配元素的从零开始的索引。</summary>
      <returns>如果找到与 <paramref name="match" /> 定义的条件相匹配的第一个元素，则为该元素的从零开始的索引；否则为 -1。</returns>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要搜索的元素的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLast(System.Predicate{`0})">
      <summary>搜索与指定谓词所定义的条件相匹配的元素，并返回整个 <see cref="T:System.Collections.Generic.List`1" /> 中的最后一个匹配元素。</summary>
      <returns>如果找到，则为与指定谓词所定义的条件相匹配的最后一个元素；否则为类型 <paramref name="T" /> 的默认值。</returns>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要搜索的元素的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>搜索与指定谓词所定义的条件相匹配的元素，并返回 <see cref="T:System.Collections.Generic.List`1" /> 中包含指定元素个数、到指定索引结束的元素范围内最后一个匹配项的从零开始的索引。</summary>
      <returns>如果找到与 <paramref name="match" /> 定义的条件相匹配的最后一个元素，则为该元素的从零开始的索引；否则为 -1。</returns>
      <param name="startIndex">向后搜索的从零开始的起始索引。</param>
      <param name="count">要搜索的部分中的元素数。</param>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要搜索的元素的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 不在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引范围内。- 或 -<paramref name="count" /> 小于 0。- 或 -<paramref name="startIndex" /> 和 <paramref name="count" /> 未指定 <see cref="T:System.Collections.Generic.List`1" /> 中的有效部分。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>搜索与由指定谓词定义的条件相匹配的元素，并返回 <see cref="T:System.Collections.Generic.List`1" /> 中从第一个元素到指定索引的元素范围内最后一个匹配项的从零开始的索引。</summary>
      <returns>如果找到与 <paramref name="match" /> 定义的条件相匹配的最后一个元素，则为该元素的从零开始的索引；否则为 -1。</returns>
      <param name="startIndex">向后搜索的从零开始的起始索引。</param>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要搜索的元素的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 不在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引范围内。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Predicate{`0})">
      <summary>搜索与指定谓词所定义的条件相匹配的元素，并返回整个 <see cref="T:System.Collections.Generic.List`1" /> 中最后一个匹配元素的从零开始的索引。</summary>
      <returns>如果找到与 <paramref name="match" /> 定义的条件相匹配的最后一个元素，则为该元素的从零开始的索引；否则为 -1。</returns>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要搜索的元素的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ForEach(System.Action{`0})">
      <summary>对 <see cref="T:System.Collections.Generic.List`1" /> 的每个元素执行指定操作。</summary>
      <param name="action">要对 <see cref="T:System.Collections.Generic.List`1" /> 的每个元素执行的 <see cref="T:System.Action`1" /> 委托。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.Generic.List`1" /> 的枚举数。</summary>
      <returns>用于 <see cref="T:System.Collections.Generic.List`1" /> 的 <see cref="T:System.Collections.Generic.List`1.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetRange(System.Int32,System.Int32)">
      <summary>创建源 <see cref="T:System.Collections.Generic.List`1" /> 中的元素范围的浅表副本。</summary>
      <returns>源 <see cref="T:System.Collections.Generic.List`1" /> 中的元素范围的浅表副本。</returns>
      <param name="index">范围开始处的从零开始的 <see cref="T:System.Collections.Generic.List`1" /> 索引。</param>
      <param name="count">范围中的元素数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。- 或 -<paramref name="count" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 和 <paramref name="count" /> 不表示 <see cref="T:System.Collections.Generic.List`1" /> 中元素的有效范围。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0)">
      <summary>搜索指定的对象，并返回整个 <see cref="T:System.Collections.Generic.List`1" /> 中第一个匹配项的从零开始的索引。</summary>
      <returns>The zero-based index of the first occurrence of <paramref name="item" /> within the entire <see cref="T:System.Collections.Generic.List`1" />, if found; otherwise, –1.</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中定位的对象。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32)">
      <summary>搜索指定的对象，并返回 <see cref="T:System.Collections.Generic.List`1" /> 中从指定索引到最后一个元素的元素范围内第一个匹配项的从零开始的索引。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.List`1" /> 中从 <paramref name="index" /> 到最后一个元素的元素范围内找到 <paramref name="item" /> 的第一个匹配项，则为该项的从零开始的索引；否则为 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中定位的对象。对于引用类型，该值可以为 null。</param>
      <param name="index">从零开始的搜索的起始索引。空列表中 0（零）为有效值。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引范围内。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32,System.Int32)">
      <summary>搜索指定的对象，并返回 <see cref="T:System.Collections.Generic.List`1" /> 中从指定的索引开始并包含指定的元素数的元素范围内第一个匹配项的从零开始的索引。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.List`1" /> 中从 <paramref name="index" /> 开始并包含 <paramref name="count" /> 个元素的元素范围内找到 <paramref name="item" /> 的第一个匹配项，则为该项的从零开始的索引；否则为 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中定位的对象。对于引用类型，该值可以为 null。</param>
      <param name="index">从零开始的搜索的起始索引。空列表中 0（零）为有效值。</param>
      <param name="count">要搜索的部分中的元素数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引范围内。- 或 -<paramref name="count" /> 小于 0。- 或 -<paramref name="index" /> 和 <paramref name="count" /> 未指定 <see cref="T:System.Collections.Generic.List`1" /> 中的有效部分。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Insert(System.Int32,`0)">
      <summary>将元素插入 <see cref="T:System.Collections.Generic.List`1" /> 的指定索引处。</summary>
      <param name="index">从零开始的索引，应在该位置插入 <paramref name="item" />。</param>
      <param name="item">要插入的对象。对于引用类型，该值可以为 null。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。- 或 -<paramref name="index" /> 大于 <see cref="P:System.Collections.Generic.List`1.Count" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>将集合中的某个元素插入 <see cref="T:System.Collections.Generic.List`1" /> 的指定索引处。</summary>
      <param name="index">应在此处插入新元素的从零开始的索引。</param>
      <param name="collection">一个集合，应将其元素插入到 <see cref="T:System.Collections.Generic.List`1" /> 中。集合自身不能为 null，但它可以包含为 null 的元素（如果类型 <paramref name="T" /> 为引用类型）。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。- 或 -<paramref name="index" /> 大于 <see cref="P:System.Collections.Generic.List`1.Count" />。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Item(System.Int32)">
      <summary>获取或设置指定索引处的元素。</summary>
      <returns>指定索引处的元素。</returns>
      <param name="index">要获取或设置的元素的从零开始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。- 或 -<paramref name="index" /> 等于或大于 <see cref="P:System.Collections.Generic.List`1.Count" />。 </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0)">
      <summary>搜索指定的对象，并返回整个 <see cref="T:System.Collections.Generic.List`1" /> 中最后一个匹配项的从零开始的索引。</summary>
      <returns>如果在整个 <see cref="T:System.Collections.Generic.List`1" /> 中找到 <paramref name="item" /> 的最后一个匹配项，则为该项的从零开始的索引；否则为 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中定位的对象。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32)">
      <summary>搜索指定的对象，并返回 <see cref="T:System.Collections.Generic.List`1" /> 中从第一个元素到指定索引的元素范围内最后一个匹配项的从零开始的索引。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.List`1" /> 中从第一个元素到 <paramref name="index" /> 的元素范围内找到 <paramref name="item" /> 的最后一个匹配项，则为该项的从零开始的索引；否则为 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中定位的对象。对于引用类型，该值可以为 null。</param>
      <param name="index">向后搜索的从零开始的起始索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引范围内。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>搜索指定的对象，并返回 <see cref="T:System.Collections.Generic.List`1" /> 中包含指定的元素数并在指定索引处结束的元素范围内最后一个匹配项的从零开始的索引。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.List`1" /> 中包含 <paramref name="count" /> 个元素、在 <paramref name="index" /> 处结尾的元素范围内找到 <paramref name="item" /> 的最后一个匹配项，则为该项的从零开始的索引；否则为 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中定位的对象。对于引用类型，该值可以为 null。</param>
      <param name="index">向后搜索的从零开始的起始索引。</param>
      <param name="count">要搜索的部分中的元素数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引范围内。- 或 -<paramref name="count" /> 小于 0。- 或 -<paramref name="index" /> 和 <paramref name="count" /> 未指定 <see cref="T:System.Collections.Generic.List`1" /> 中的有效部分。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Remove(`0)">
      <summary>从 <see cref="T:System.Collections.Generic.List`1" /> 中移除特定对象的第一个匹配项。</summary>
      <returns>如果成功移除 <paramref name="item" />，则为 true；否则为 false。This method also returns false if <paramref name="item" /> was not found in the <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="item">要从 <see cref="T:System.Collections.Generic.List`1" /> 中移除的对象。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAll(System.Predicate{`0})">
      <summary>移除与指定的谓词所定义的条件相匹配的所有元素。</summary>
      <returns>从 <see cref="T:System.Collections.Generic.List`1" /> 中移除的元素的数目。</returns>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，用于定义要移除的元素应满足的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAt(System.Int32)">
      <summary>移除 <see cref="T:System.Collections.Generic.List`1" /> 的指定索引处的元素。</summary>
      <param name="index">要移除的元素的从零开始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。- 或 -<paramref name="index" /> 等于或大于 <see cref="P:System.Collections.Generic.List`1.Count" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveRange(System.Int32,System.Int32)">
      <summary>从 <see cref="T:System.Collections.Generic.List`1" /> 中移除一定范围的元素。</summary>
      <param name="index">要移除的元素范围的从零开始的起始索引。</param>
      <param name="count">要移除的元素数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。- 或 -<paramref name="count" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 和 <paramref name="count" /> 不表示 <see cref="T:System.Collections.Generic.List`1" /> 中元素的有效范围。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse">
      <summary>将整个 <see cref="T:System.Collections.Generic.List`1" /> 中元素的顺序反转。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse(System.Int32,System.Int32)">
      <summary>将指定范围中元素的顺序反转。</summary>
      <param name="index">要反转的范围的从零开始的起始索引。</param>
      <param name="count">要反转的范围内的元素数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。- 或 -<paramref name="count" /> 小于 0。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 和 <paramref name="count" /> 不表示 <see cref="T:System.Collections.Generic.List`1" /> 中元素的有效范围。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort">
      <summary>使用默认比较器对整个 <see cref="T:System.Collections.Generic.List`1" /> 中的元素进行排序。</summary>
      <exception cref="T:System.InvalidOperationException">默认比较器 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <paramref name="T" /> 类型的 <see cref="T:System.IComparable`1" /> 泛型接口或 <see cref="T:System.IComparable" /> 接口的实现。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>使用指定的比较器对整个 <see cref="T:System.Collections.Generic.List`1" /> 中的元素进行排序。</summary>
      <param name="comparer">比较元素时要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现，或者为 null，表示使用默认比较器 <see cref="P:System.Collections.Generic.Comparer`1.Default" />。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> 为 null，且默认比较器 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <paramref name="T" /> 类型的 <see cref="T:System.IComparable`1" /> 泛型接口或 <see cref="T:System.IComparable" /> 接口的实现。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="comparer" /> 的实现导致排序时出现错误。例如，将某个项与其自身进行比较时，<paramref name="comparer" /> 可能不返回 0。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Comparison{`0})">
      <summary>使用指定的 <see cref="T:System.Comparison`1" /> 对整个 <see cref="T:System.Collections.Generic.List`1" /> 中的元素进行排序。</summary>
      <param name="comparison">比较元素时要使用的 <see cref="T:System.Comparison`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="comparison" /> 的实现导致排序时出现错误。例如，将某个项与其自身进行比较时，<paramref name="comparison" /> 可能不返回 0。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>使用指定的比较器对 <see cref="T:System.Collections.Generic.List`1" /> 中某个范围内的元素进行排序。</summary>
      <param name="index">要排序范围的从零开始的起始索引。</param>
      <param name="count">要排序的范围的长度。</param>
      <param name="comparer">比较元素时要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现，或者为 null，表示使用默认比较器 <see cref="P:System.Collections.Generic.Comparer`1.Default" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。- 或 -<paramref name="count" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 和 <paramref name="count" /> 未指定 <see cref="T:System.Collections.Generic.List`1" /> 中的有效范围。- 或 -<paramref name="comparer" /> 的实现导致排序时出现错误。例如，将某个项与其自身进行比较时，<paramref name="comparer" /> 可能不返回 0。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> 为 null，且默认比较器 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <paramref name="T" /> 类型的 <see cref="T:System.IComparable`1" /> 泛型接口或 <see cref="T:System.IComparable" /> 接口的实现。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否为只读。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.在 <see cref="T:System.Collections.Generic.List`1" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的 <see cref="T:System.Array" /> 索引处开始，将 <see cref="T:System.Collections.ICollection" /> 的元素复制到一个 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多维的。- 或 -<paramref name="array" /> 没有从零开始的索引。- 或 -源 <see cref="T:System.Collections.ICollection" /> 中的元素数目大于从 <paramref name="arrayIndex" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。- 或 -源 <see cref="T:System.Collections.ICollection" /> 的类型无法自动转换为目标 <paramref name="array" /> 的类型。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.在 <see cref="T:System.Collections.Generic.List`1" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。在 <see cref="T:System.Collections.Generic.List`1" /> 的默认实现中，此属性始终返回当前实例。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Add(System.Object)">
      <summary>将某项添加到 <see cref="T:System.Collections.IList" /> 中。</summary>
      <returns>新元素的插入位置。</returns>
      <param name="item">要添加到 <see cref="T:System.Collections.IList" /> 的 <see cref="T:System.Object" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> 属于不能分配给 <see cref="T:System.Collections.IList" /> 的类型。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Contains(System.Object)">
      <summary>确定 <see cref="T:System.Collections.IList" /> 是否包含特定值。</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.IList" />; otherwise, false.</returns>
      <param name="item">要在 <see cref="T:System.Collections.IList" /> 中查找的 <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>确定 <see cref="T:System.Collections.IList" /> 中特定项的索引。</summary>
      <returns>如果在列表中找到，则为 <paramref name="item" /> 的索引；否则为 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.IList" /> 中定位的对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> 属于不能分配给 <see cref="T:System.Collections.IList" /> 的类型。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>将一个项插入指定索引处的 <see cref="T:System.Collections.IList" />。</summary>
      <param name="index">从零开始的索引，应在该位置插入 <paramref name="item" />。</param>
      <param name="item">要插入 <see cref="T:System.Collections.IList" /> 的对象。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Collections.IList" /> 中的有效索引。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> 属于不能分配给 <see cref="T:System.Collections.IList" /> 的类型。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsFixedSize">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.IList" /> 是否具有固定大小。</summary>
      <returns>如果 <see cref="T:System.Collections.IList" /> 具有固定大小，则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.List`1" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.IList" /> 是否为只读。</summary>
      <returns>true if the <see cref="T:System.Collections.IList" /> is read-only; otherwise, false.在 <see cref="T:System.Collections.Generic.List`1" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#Item(System.Int32)">
      <summary>获取或设置指定索引处的元素。</summary>
      <returns>指定索引处的元素。</returns>
      <param name="index">要获取或设置的元素的从零开始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Collections.IList" /> 中的有效索引。</exception>
      <exception cref="T:System.ArgumentException">已设置属性，且 <paramref name="value" /> 属于不能对 <see cref="T:System.Collections.IList" /> 赋值的类型。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Remove(System.Object)">
      <summary>从 <see cref="T:System.Collections.IList" /> 中移除特定对象的第一个匹配项。</summary>
      <param name="item">要从 <see cref="T:System.Collections.IList" /> 中移除的对象。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> 属于不能分配给 <see cref="T:System.Collections.IList" /> 的类型。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ToArray">
      <summary>将 <see cref="T:System.Collections.Generic.List`1" /> 的元素复制到新数组中。</summary>
      <returns>一个数组，它包含 <see cref="T:System.Collections.Generic.List`1" /> 的元素的副本。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrimExcess">
      <summary>将容量设置为 <see cref="T:System.Collections.Generic.List`1" /> 中的实际元素数目（如果该数目小于某个阈值）。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrueForAll(System.Predicate{`0})">
      <summary>确定是否 <see cref="T:System.Collections.Generic.List`1" /> 中的每个元素都与指定的谓词所定义的条件相匹配。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.List`1" /> 中的每个元素都与指定的谓词所定义的条件相匹配，则为 true；否则为 false。如果列表不包含任何元素，则返回值为 true。</returns>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委托，定义要据以检查元素的条件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
    </member>
    <member name="T:System.Collections.Generic.List`1.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.List`1" /> 的元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> 中位于该枚举数当前位置的元素。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.Dispose">
      <summary>释放由 <see cref="T:System.Collections.Generic.List`1.Enumerator" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.MoveNext">
      <summary>使枚举数前进到 <see cref="T:System.Collections.Generic.List`1" /> 的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> 中位于该枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Collections.Generic.Queue`1">
      <summary>表示对象的先进先出集合。</summary>
      <typeparam name="T">指定队列中元素的类型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.Queue`1" /> 类的新实例，该实例为空并且具有默认初始容量。</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Queue`1" /> 类的新实例，该实例包含从指定集合复制的元素并且具有足够的容量来容纳所复制的元素。</summary>
      <param name="collection">其元素被复制到新的 <see cref="T:System.Collections.Generic.Queue`1" /> 中的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Collections.Generic.Queue`1" /> 类的新实例，该实例为空并且具有指定的初始容量。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Queue`1" /> 可包含的初始元素数目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Clear">
      <summary>从 <see cref="T:System.Collections.Generic.Queue`1" /> 中移除所有对象。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Contains(`0)">
      <summary>确定某元素是否在 <see cref="T:System.Collections.Generic.Queue`1" /> 中。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.Queue`1" /> 中找到了 <paramref name="item" />，则为 true；否则为 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.Queue`1" /> 中定位的对象。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.CopyTo(`0[],System.Int32)">
      <summary>从指定数组索引开始将 <see cref="T:System.Collections.Generic.Queue`1" /> 元素复制到现有一维 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">一维 <see cref="T:System.Array" />，它是从 <see cref="T:System.Collections.Generic.Queue`1" /> 复制的元素的目标。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Queue`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Count">
      <summary>获取 <see cref="T:System.Collections.Generic.Queue`1" /> 中包含的元素数。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" /> 中包含的元素数。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Dequeue">
      <summary>移除并返回位于 <see cref="T:System.Collections.Generic.Queue`1" /> 开始处的对象。</summary>
      <returns>从 <see cref="T:System.Collections.Generic.Queue`1" /> 的开头移除的对象。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enqueue(`0)">
      <summary>将对象添加到 <see cref="T:System.Collections.Generic.Queue`1" /> 的结尾处。</summary>
      <param name="item">要添加到 <see cref="T:System.Collections.Generic.Queue`1" /> 的对象。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.Generic.Queue`1" /> 的枚举数。</summary>
      <returns>用于 <see cref="T:System.Collections.Generic.Queue`1" /> 的 <see cref="T:System.Collections.Generic.Queue`1.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Peek">
      <summary>返回位于 <see cref="T:System.Collections.Generic.Queue`1" /> 开始处的对象但不将其移除。</summary>
      <returns>位于 <see cref="T:System.Collections.Generic.Queue`1" /> 的开头的对象。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的 <see cref="T:System.Array" /> 索引处开始，将 <see cref="T:System.Collections.ICollection" /> 的元素复制到一个 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">一维 <see cref="T:System.Array" />，它是从 <see cref="T:System.Collections.ICollection" /> 复制的元素的目标。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>如果对 <see cref="T:System.Collections.ICollection" /> 的访问是同步的（线程安全），则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.Queue`1" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。在 <see cref="T:System.Collections.Generic.Queue`1" /> 的默认实现中，此属性始终返回当前实例。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.ToArray">
      <summary>将 <see cref="T:System.Collections.Generic.Queue`1" /> 元素复制到新数组。</summary>
      <returns>包含从 <see cref="T:System.Collections.Generic.Queue`1" /> 复制的元素的新数组。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.TrimExcess">
      <summary>如果元素数小于当前容量的 90%，将容量设置为 <see cref="T:System.Collections.Generic.Queue`1" /> 中的实际元素数。</summary>
    </member>
    <member name="T:System.Collections.Generic.Queue`1.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.Queue`1" /> 的元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" /> 中位于该枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.Dispose">
      <summary>释放由 <see cref="T:System.Collections.Generic.Queue`1.Enumerator" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.MoveNext">
      <summary>使枚举数前进到 <see cref="T:System.Collections.Generic.Queue`1" /> 的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2">
      <summary>表示根据键进行排序的键/值对的集合。</summary>
      <typeparam name="TKey">字典中的键的类型。</typeparam>
      <typeparam name="TValue">字典中的值的类型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 类的一个新实例，该实例为空并对键类型使用默认 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 类的一个新实例，该实例为空并使用指定的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现来比较键。</summary>
      <param name="comparer">比较键时要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现，或者为 null，以便为键类型使用默认的 <see cref="T:System.Collections.Generic.Comparer`1" />。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 类的新实例，该实例包含从指定的 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中复制的元素，并使用键类型的默认 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" />，其元素被复制到新的 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一个或多个重复键。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 类的新实例，该实例包含从指定的 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中复制的元素，并使用指定的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现来比较键。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" />，其元素被复制到新的 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中。</param>
      <param name="comparer">比较键时要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现，或者为 null，以便为键类型使用默认的 <see cref="T:System.Collections.Generic.Comparer`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一个或多个重复键。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Add(`0,`1)">
      <summary>将带有指定键和值的元素添加到 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中。</summary>
      <param name="key">要添加的元素的键。</param>
      <param name="value">要添加的元素的值。对于引用类型，该值可以为 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中已存在具有相同键的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Clear">
      <summary>从 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中移除所有元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Comparer">
      <summary>获取用于对 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的元素进行排序的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <returns>用于对 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的元素进行排序的 <see cref="T:System.Collections.Generic.IComparer`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsKey(`0)">
      <summary>确定 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 是否包含具有指定键的元素。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">要在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中定位的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsValue(`1)">
      <summary>确定 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 是否包含具有指定值的元素。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified value; otherwise, false.</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中定位的值。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>从指定的索引处开始，将 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的元素复制到指定的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 结构的数组中。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 结构的一维数组，它是从当前 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中复制的元素的目标。该数组必须具有从零开始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，将在此处开始复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">源 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Count">
      <summary>获取包含在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的键/值对的数目。</summary>
      <returns>包含在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的键/值对的数目。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.GetEnumerator">
      <summary>返回一个循环访问 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的枚举数。</summary>
      <returns>用于 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的 <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Item(`0)">
      <summary>获取或设置与指定的键关联的值。</summary>
      <returns>与指定的键相关联的值。如果找不到指定的键，get 操作便会引发 <see cref="T:System.Collections.Generic.KeyNotFoundException" />，而 set 操作会创建一个具有指定键的新元素。</returns>
      <param name="key">要获取或设置的值的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">已检索该属性，并且集合中不存在 <paramref name="key" />。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Keys">
      <summary>获取包含 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的键的集合。</summary>
      <returns>包含 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的键的 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Remove(`0)">
      <summary>从 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中移除包含指定键的元素。</summary>
      <returns>如果该元素已成功移除，则为 true；否则为 false。如果在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中没有找到 <paramref name="key" />，此方法也会返回 false。</returns>
      <param name="key">要移除的元素的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>向 <see cref="T:System.Collections.Generic.ICollection`1" /> 添加一项。</summary>
      <param name="keyValuePair">要添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 中的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 结构。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中已存在具有相同键的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>确定 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含特定的键和值。</summary>
      <returns>true if <paramref name="keyValuePair" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="keyValuePair">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中查找的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 结构。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否为只读。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 为只读，则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除指定元素的第一个匹配项。</summary>
      <returns>如果已从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中成功移除 <paramref name="keyValuePair" />，则为 true；否则为 false。This method also returns false if <paramref name="keyValuePair" /> was not found in the <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">要从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 结构。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>获取包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的键的 <see cref="T:System.Collections.Generic.ICollection`1" />。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.ICollection`1" />，它包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的键。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>获取包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值的 <see cref="T:System.Collections.Generic.ICollection`1" />。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.ICollection`1" />，它包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>一个 <see cref="T:System.Collections.IEnumerator" />，可用于循环访问集合。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>获取包含在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的键的集合</summary>
      <returns>包含在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的键的集合</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>获取包含在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的值的集合</summary>
      <returns>包含在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的值的集合</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从指定的数组索引开始，将 <see cref="T:System.Collections.Generic.ICollection`1" /> 中的元素复制到一个数组中。</summary>
      <param name="array">一维数组，它是从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中复制的元素的目标。该数组的索引必须从零开始。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，将在此处开始复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多维的。- 或 -<paramref name="array" /> 没有从零开始的索引。- 或 -源 <see cref="T:System.Collections.Generic.ICollection`1" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。- 或 -源 <see cref="T:System.Collections.Generic.ICollection`1" /> 的类型无法自动转换为目标 <paramref name="array" /> 的类型。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>获取一个可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>一个可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>使用所提供的键和值向 <see cref="T:System.Collections.IDictionary" /> 中添加一个元素。</summary>
      <param name="key">用作要添加的元素的键的对象。</param>
      <param name="value">用作要添加的元素的值的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.IDictionary" />.- 或 -<paramref name="value" /> 属于不能分配给 <see cref="T:System.Collections.IDictionary" /> 的值类型 <paramref name="TValue" /> 的类型。- 或 -<see cref="T:System.Collections.IDictionary" /> 中已存在具有相同键的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>确定 <see cref="T:System.Collections.IDictionary" /> 是否包含具有指定键的元素。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.</returns>
      <param name="key">要在 <see cref="T:System.Collections.IDictionary" /> 中定位的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>返回 <see cref="T:System.Collections.IDictionary" /> 的 <see cref="T:System.Collections.IDictionaryEnumerator" />。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> 的一个 <see cref="T:System.Collections.IDictionaryEnumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.IDictionary" /> 是否具有固定大小。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> has a fixed size; otherwise, false.在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.IDictionary" /> 是否为只读。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> is read-only; otherwise, false.在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>获取或设置具有指定键的元素。</summary>
      <returns>具有指定键的元素，如果 <paramref name="key" /> 不在词典中或者 <paramref name="key" /> 属于不能分配给 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的键类型 <paramref name="TKey" /> 的类型，则为 null。</returns>
      <param name="key">要获取的元素的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">A value is being assigned, and <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.- 或 -A value is being assigned, and <paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Keys">
      <summary>获取包含 <see cref="T:System.Collections.IDictionary" /> 的键的 <see cref="T:System.Collections.ICollection" />。</summary>
      <returns>一个 <see cref="T:System.Collections.ICollection" />，它包含 <see cref="T:System.Collections.IDictionary" /> 的键。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>从 <see cref="T:System.Collections.IDictionary" /> 中移除包含指定键的元素。</summary>
      <param name="key">要移除的元素的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Values">
      <summary>获取包含 <see cref="T:System.Collections.IDictionary" /> 中的值的 <see cref="T:System.Collections.ICollection" />。</summary>
      <returns>一个 <see cref="T:System.Collections.ICollection" />，它包含 <see cref="T:System.Collections.IDictionary" /> 中的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerator`1" />，可用于循环访问集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.TryGetValue(`0,`1@)">
      <summary>获取与指定键关联的值。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">要获取的值的键。</param>
      <param name="value">如果找到指定的键，此方法将返回与该键关联的值；否则返回 <paramref name="value" /> 参数类型的默认值。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Values">
      <summary>获取包含 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的值的集合。</summary>
      <returns>包含 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的值的 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中位于该枚举数当前位置的元素。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.Dispose">
      <summary>释放由 <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.MoveNext">
      <summary>使枚举数前进到 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>以 <see cref="T:System.Collections.DictionaryEntry" /> 结构的形式获取枚举数当前位置的元素。</summary>
      <returns>集合中位于字典当前位置的元素，形式为<see cref="T:System.Collections.DictionaryEntry" /> 结构。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>获取位于枚举数当前位置的元素的键。</summary>
      <returns>集合中位于该枚举数当前位置的元素的键。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>获取位于枚举数当前位置的元素的值。</summary>
      <returns>集合中位于该枚举数当前位置的元素的值。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection">
      <summary>表示 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中键的集合。此类不能被继承。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 类的新实例，该实例反映指定的 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的键。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />，其键反映在新的 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>从指定数组索引位置开始，将 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 元素复制到某个现有的一维数组。</summary>
      <param name="array">一维数组，它是从 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中复制的元素的目标。该数组的索引必须从零开始。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">源 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Count">
      <summary>获取 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中包含的元素数。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中包含的元素个数。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的枚举数。</summary>
      <returns>用于 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> 结构。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>将某项添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 中。此实现始终引发 <see cref="T:System.NotSupportedException" />。</summary>
      <param name="item">要添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 的对象。</param>
      <exception cref="T:System.NotSupportedException">始终被引发；该集合为只读。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除所有项。此实现始终引发 <see cref="T:System.NotSupportedException" />。</summary>
      <exception cref="T:System.NotSupportedException">始终被引发；该集合为只读。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>确定 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含指定值。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找到 <paramref name="item" />，则为 true；否则为 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中定位的对象。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否为只读。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 为只读，则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除特定对象的第一个匹配项。此实现始终引发 <see cref="T:System.NotSupportedException" />。</summary>
      <returns>如果已从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中成功移除了 <paramref name="item" />，则为 true；否则为 false。如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中没有找到 <paramref name="item" />，此方法也会返回 false。</returns>
      <param name="item">要从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除的对象。</param>
      <exception cref="T:System.NotSupportedException">始终被引发；该集合为只读。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的数组索引开始，将 <see cref="T:System.Collections.ICollection" /> 的元素复制到一个数组中。</summary>
      <param name="array">一维数组，它是从 <see cref="T:System.Collections.ICollection" /> 中复制的元素的目标。该数组的索引必须从零开始。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多维的。- 或 -<paramref name="array" /> 没有从零开始的索引。- 或 -源 <see cref="T:System.Collections.ICollection" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。- 或 -源 <see cref="T:System.Collections.ICollection" /> 的类型无法自动转换为目标 <paramref name="array" /> 的类型。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>如果对 <see cref="T:System.Collections.ICollection" /> 的访问是同步的（线程安全），则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的默认实现中，此属性始终返回当前实例。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中位于该枚举数当前位置的元素。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>释放由 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>使枚举数前进到 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection">
      <summary>表示 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中值的集合。无法继承此类</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 类的新实例，该实例反映指定的 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的值。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />，其值反映在新的 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>从指定的数组索引开始，将 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 元素复制到现有的一维数组中。</summary>
      <param name="array">一维数组，它是从 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 中复制的元素的目标。该数组的索引必须从零开始。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">源 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Count">
      <summary>获取 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 中包含的元素数。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 中包含的元素个数。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的枚举数。</summary>
      <returns>用于 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> 结构。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>将某项添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 中。此实现始终引发 <see cref="T:System.NotSupportedException" />。</summary>
      <param name="item">要添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 的对象。</param>
      <exception cref="T:System.NotSupportedException">始终被引发；该集合为只读。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除所有项。此实现始终引发 <see cref="T:System.NotSupportedException" />。</summary>
      <exception cref="T:System.NotSupportedException">始终被引发；该集合为只读。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>确定 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含指定值。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找到 <paramref name="item" />，则为 true；否则为 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中定位的对象。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否为只读。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 为只读，则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除特定对象的第一个匹配项。此实现始终引发 <see cref="T:System.NotSupportedException" />。</summary>
      <returns>如果已从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中成功移除了 <paramref name="item" />，则为 true；否则为 false。如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中没有找到 <paramref name="item" />，此方法也会返回 false。</returns>
      <param name="item">要从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除的对象。</param>
      <exception cref="T:System.NotSupportedException">始终被引发；该集合为只读。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除特定对象的第一个匹配项。此实现始终引发 <see cref="T:System.NotSupportedException" />。</summary>
      <returns>如果已从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中成功移除了 <paramref name="item" />，则为 true；否则为 false。如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中没有找到 <paramref name="item" />，此方法也会返回 false。</returns>
      <exception cref="T:System.NotSupportedException">始终被引发；该集合为只读。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的数组索引开始，将 <see cref="T:System.Collections.ICollection" /> 的元素复制到一个数组中。</summary>
      <param name="array">一维数组，它是从 <see cref="T:System.Collections.ICollection" /> 中复制的元素的目标。该数组的索引必须从零开始。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多维的。- 或 -<paramref name="array" /> 没有从零开始的索引。- 或 -源 <see cref="T:System.Collections.ICollection" /> 中的元素数目大于从 <paramref name="index" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。- 或 -源 <see cref="T:System.Collections.ICollection" /> 的类型无法自动转换为目标 <paramref name="array" /> 的类型。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>如果对 <see cref="T:System.Collections.ICollection" /> 的访问是同步的（线程安全），则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的默认实现中，此属性始终返回当前实例。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 中位于该枚举数当前位置的元素。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>释放由 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>使枚举数前进到 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedList`2">
      <summary>表示根据键进行排序的键/值对的集合，而键基于的是相关的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现。</summary>
      <typeparam name="TKey">集合中的键的类型。</typeparam>
      <typeparam name="TValue">集合中值的类型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 类的新实例，该示例为空且具有默认的初始容量，并使用默认的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 类的新实例，该实例为空且具有默认的初始容量，并使用指定的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <param name="comparer">在比较键时要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现。- 或 -为 null，则为这类键使用默认的 <see cref="T:System.Collections.Generic.Comparer`1" />。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 类的新实例，该实例包含从指定的 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中复制的元素，其容量足以容纳所复制的元素数并使用默认的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" />，其元素被复制到新的 <see cref="T:System.Collections.Generic.SortedList`2" /> 中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一个或多个重复键。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 类的新实例，该实例包含从指定的 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中复制的元素，其容量足以容纳所复制的元素数并使用指定的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" />，其元素被复制到新的 <see cref="T:System.Collections.Generic.SortedList`2" /> 中。</param>
      <param name="comparer">在比较键时要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现。- 或 -为 null，则为这类键使用默认的 <see cref="T:System.Collections.Generic.Comparer`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一个或多个重复键。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 类的新实例，该实例为空且具有指定的初始容量，并使用默认的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.SortedList`2" /> 可包含的初始元素数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> 小于零。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 类的新实例，该实例为空且具有指定的初始容量，并使用指定的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.SortedList`2" /> 可包含的初始元素数。</param>
      <param name="comparer">在比较键时要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 实现。- 或 -为 null，则为这类键使用默认的 <see cref="T:System.Collections.Generic.Comparer`1" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> 小于零。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Add(`0,`1)">
      <summary>将带有指定键和值的元素添加到 <see cref="T:System.Collections.Generic.SortedList`2" /> 中。</summary>
      <param name="key">要添加的元素的键。</param>
      <param name="value">要添加的元素的值。对于引用类型，该值可以为 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Collections.Generic.SortedList`2" /> 中已存在具有相同键的元素。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Capacity">
      <summary>获取或设置 <see cref="T:System.Collections.Generic.SortedList`2" /> 可包含的元素数。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> 可包含的元素数。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.SortedList`2.Capacity" /> 设置为小于 <see cref="P:System.Collections.Generic.SortedList`2.Count" /> 的值。</exception>
      <exception cref="T:System.OutOfMemoryException">系统中没有足够的可用内存。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Clear">
      <summary>从 <see cref="T:System.Collections.Generic.SortedList`2" /> 中移除所有元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Comparer">
      <summary>获取该排序列表的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <returns>当前 <see cref="T:System.Collections.Generic.SortedList`2" /> 的 <see cref="T:System.IComparable`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsKey(`0)">
      <summary>确定 <see cref="T:System.Collections.Generic.SortedList`2" /> 是否包含特定的键。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">要在 <see cref="T:System.Collections.Generic.SortedList`2" /> 中定位的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsValue(`1)">
      <summary>确定 <see cref="T:System.Collections.Generic.SortedList`2" /> 是否包含特定值。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified value; otherwise, false.</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.SortedList`2" /> 中定位的值。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Count">
      <summary>获取包含在 <see cref="T:System.Collections.Generic.SortedList`2" /> 中的键/值对的数目。</summary>
      <returns>包含在 <see cref="T:System.Collections.Generic.SortedList`2" /> 中的键/值对的数目。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.GetEnumerator">
      <summary>返回一个循环访问 <see cref="T:System.Collections.Generic.SortedList`2" /> 的枚举数。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> 的类型为 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfKey(`0)">
      <summary>在整个 <see cref="T:System.Collections.Generic.SortedList`2" /> 中搜索指定键并返回从零开始的索引。</summary>
      <returns>如果找到，则为整个 <see cref="T:System.Collections.Generic.SortedList`2" /> 中 <paramref name="key" /> 的从零开始的索引；否则为 -1。</returns>
      <param name="key">要在 <see cref="T:System.Collections.Generic.SortedList`2" /> 中定位的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfValue(`1)">
      <summary>在整个 <see cref="T:System.Collections.Generic.SortedList`2" /> 中搜索指定的值，并返回第一个匹配项的从零开始的索引。</summary>
      <returns>如果在整个 <see cref="T:System.Collections.Generic.SortedList`2" /> 中找到 <paramref name="value" /> 的匹配项，则为第一个匹配项的从零开始的索引；否则为 -1。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.SortedList`2" /> 中定位的值。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Item(`0)">
      <summary>获取或设置与指定的键关联的值。</summary>
      <returns>与指定的键相关联的值。如果找不到指定的键，则 get 操作会引发 <see cref="T:System.Collections.Generic.KeyNotFoundException" />，而 set 操作会创建一个使用指定键的新元素。</returns>
      <param name="key">要获取或设置其值的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">已检索该属性，并且集合中不存在 <paramref name="key" />。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Keys">
      <summary>获取一个按排序顺序包含 <see cref="T:System.Collections.Generic.SortedList`2" /> 中的键的集合。</summary>
      <returns>包含 <see cref="T:System.Collections.Generic.SortedList`2" /> 中的键的 <see cref="T:System.Collections.Generic.IList`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Remove(`0)">
      <summary>从 <see cref="T:System.Collections.Generic.SortedList`2" /> 中移除包含指定键的元素。</summary>
      <returns>如果该元素已成功移除，则为 true；否则为 false。This method also returns false if <paramref name="key" /> was not found in the original <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">要移除的元素的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.RemoveAt(System.Int32)">
      <summary>移除 <see cref="T:System.Collections.Generic.SortedList`2" /> 的指定索引处的元素。</summary>
      <param name="index">要移除的元素的从零开始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。- 或 -<paramref name="index" /> 等于或大于 <see cref="P:System.Collections.Generic.SortedList`2.Count" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>向 <see cref="T:System.Collections.Generic.ICollection`1" /> 中添加键/值对。</summary>
      <param name="keyValuePair">要添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 的 <see cref="T:System.Collections.Generic.KeyValuePair`2" />。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>确定 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含特定元素。</summary>
      <returns>true if <paramref name="keyValuePair" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="keyValuePair">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中定位的 <see cref="T:System.Collections.Generic.KeyValuePair`2" />。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>从特定的 <see cref="T:System.Array" /> 索引开始，将 <see cref="T:System.Collections.Generic.ICollection`1" /> 的元素复制到一个 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">作为从 <see cref="T:System.Collections.Generic.ICollection`1" /> 复制的元素的目标位置的一维 <see cref="T:System.Array" />。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，将在此处开始复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小于零。</exception>
      <exception cref="T:System.ArgumentException">源 <see cref="T:System.Collections.Generic.ICollection`1" /> 中的元素数目大于从 <paramref name="arrayIndex" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否为只读。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 为只读，则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.SortedList`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除特定键/值对的第一个匹配项。</summary>
      <returns>如果已从 <see cref="T:System.Collections.Generic.ICollection`1" /> 中成功移除 <paramref name="keyValuePair" />，则为 true；否则为 false。This method also returns false if <paramref name="keyValuePair" /> was not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">要从 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除的 <see cref="T:System.Collections.Generic.KeyValuePair`2" />。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>获取包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的键的 <see cref="T:System.Collections.Generic.ICollection`1" />。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.ICollection`1" />，它包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的键。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>获取包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值的 <see cref="T:System.Collections.Generic.ICollection`1" />。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.ICollection`1" />，它包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.IEnumerator`1" />，可用于循环访问集合。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>获取包含只读字典中的键的可枚举集合。</summary>
      <returns>包含只读字典中的键的可枚举集合。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>获取包含只读字典中的值的可枚举集合。</summary>
      <returns>包含只读字典中的值的可枚举集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的 <see cref="T:System.Array" /> 索引开始，将 <see cref="T:System.Collections.ICollection" /> 的元素复制到一个 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，将在此处开始复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小于零。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多维的。- 或 -<paramref name="array" /> 没有从零开始的索引。- 或 -源 <see cref="T:System.Collections.ICollection" /> 中的元素数目大于从 <paramref name="arrayIndex" /> 到目标 <paramref name="array" /> 末尾之间的可用空间。- 或 -源 <see cref="T:System.Collections.ICollection" /> 的类型无法自动转换为目标 <paramref name="array" /> 的类型。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.在 <see cref="T:System.Collections.Generic.SortedList`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#SyncRoot">
      <summary>获取一个可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>一个可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。在 <see cref="T:System.Collections.Generic.SortedList`2" /> 的默认实现中，此属性始终返回当前实例。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>使用所提供的键和值向 <see cref="T:System.Collections.IDictionary" /> 中添加一个元素。</summary>
      <param name="key">用作要添加的元素的键的 <see cref="T:System.Object" />。</param>
      <param name="value">用作要添加的元素的值的 <see cref="T:System.Object" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.IDictionary" />.- 或 -<paramref name="value" /> 属于不能分配给 <see cref="T:System.Collections.IDictionary" /> 的值类型 <paramref name="TValue" /> 的类型。- 或 -<see cref="T:System.Collections.IDictionary" /> 中已存在具有相同键的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>确定 <see cref="T:System.Collections.IDictionary" /> 是否包含具有指定键的元素。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.</returns>
      <param name="key">要在 <see cref="T:System.Collections.IDictionary" /> 中定位的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#GetEnumerator">
      <summary>返回 <see cref="T:System.Collections.IDictionary" /> 的 <see cref="T:System.Collections.IDictionaryEnumerator" />。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> 的一个 <see cref="T:System.Collections.IDictionaryEnumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsFixedSize">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.IDictionary" /> 是否具有固定大小。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> has a fixed size; otherwise, false.在 <see cref="T:System.Collections.Generic.SortedList`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.IDictionary" /> 是否为只读。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> is read-only; otherwise, false.在 <see cref="T:System.Collections.Generic.SortedList`2" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>获取或设置具有指定键的元素。</summary>
      <returns>The element with the specified key, or null if <paramref name="key" /> is not in the dictionary or <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">要获取或设置的元素的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">A value is being assigned, and <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.- 或 -A value is being assigned, and <paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Keys">
      <summary>获取包含 <see cref="T:System.Collections.IDictionary" /> 的键的 <see cref="T:System.Collections.ICollection" />。</summary>
      <returns>一个 <see cref="T:System.Collections.ICollection" />，它包含 <see cref="T:System.Collections.IDictionary" /> 的键。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>从 <see cref="T:System.Collections.IDictionary" /> 中移除包含指定键的元素。</summary>
      <param name="key">要移除的元素的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Values">
      <summary>获取包含 <see cref="T:System.Collections.IDictionary" /> 中的值的 <see cref="T:System.Collections.ICollection" />。</summary>
      <returns>一个 <see cref="T:System.Collections.ICollection" />，它包含 <see cref="T:System.Collections.IDictionary" /> 中的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>一个 <see cref="T:System.Collections.IEnumerator" />，可用于循环访问集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TrimExcess">
      <summary>如果元素数小于当前容量的 90%，将容量设置为 <see cref="T:System.Collections.Generic.SortedList`2" /> 中的实际元素数。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TryGetValue(`0,`1@)">
      <summary>获取与指定键关联的值。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">要获取其值的键。</param>
      <param name="value">如果找到指定的键，此方法将返回与该键关联的值；否则返回 <paramref name="value" /> 参数类型的默认值。此参数未经初始化即被传递。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Values">
      <summary>获取包含 <see cref="T:System.Collections.Generic.SortedList`2" /> 中的值的集合。</summary>
      <returns>包含 <see cref="T:System.Collections.Generic.SortedList`2" /> 中的值的 <see cref="T:System.Collections.Generic.IList`1" />。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1">
      <summary>表示按排序顺序维护的对象的集合。</summary>
      <typeparam name="T">集内元素的类型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedSet`1" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>初始化使用指定比较器的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 类的新实例。</summary>
      <param name="comparer">用于比较对象的默认比较器。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedSet`1" /> 类的新实例，该实例包含从指定的可枚举集合中复制的元素。</summary>
      <param name="collection">要复制的可枚举集合。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedSet`1" /> 类的新实例，该实例包含从指定的可枚举集合中复制的元素并使用指定的比较器。</summary>
      <param name="collection">要复制的可枚举集合。</param>
      <param name="comparer">用于比较对象的默认比较器。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Add(`0)">
      <summary>向集内添加元素，并返回一个指示是否已成功添加元素的值。</summary>
      <returns>true if <paramref name="item" /> is added to the set; otherwise, false.</returns>
      <param name="item">要添加到集中的元素。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Clear">
      <summary>从集内移除所有元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Comparer">
      <summary>获取用于确定 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的值是否相等的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对象。</summary>
      <returns>用于确定 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的值是否相等的比较器。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Contains(`0)">
      <summary>确定集是否包含特定元素。</summary>
      <returns>如果集包含 <paramref name="item" />，则为 true；否则为 false。</returns>
      <param name="item">要在集内定位的元素。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[])">
      <summary>将整个 <see cref="T:System.Collections.Generic.SortedSet`1" /> 复制到兼容的一维数组中（从目标数组的开头开始复制）。</summary>
      <param name="array">一个一维数组，它是从 <see cref="T:System.Collections.Generic.SortedSet`1" /> 复制的元素的目标。</param>
      <exception cref="T:System.ArgumentException">源 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的元素数大于目标数组可包含的元素数。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32)">
      <summary>将整个 <see cref="T:System.Collections.Generic.SortedSet`1" /> 复制到兼容的一维目标数组（从指定的数组索引处开始复制）。</summary>
      <param name="array">一个一维数组，它是从 <see cref="T:System.Collections.Generic.SortedSet`1" /> 复制的元素的目标。该数组的索引必须从零开始。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentException">源数组中的元素数大于从 <paramref name="index" /> 到目标数组末尾处的可用空间。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>将指定数量的元素从 <see cref="T:System.Collections.Generic.SortedSet`1" /> 复制到兼容的一维数组中（从指定的数组索引处开始复制）。</summary>
      <param name="array">一个一维数组，它是从 <see cref="T:System.Collections.Generic.SortedSet`1" /> 复制的元素的目标。该数组的索引必须从零开始。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <param name="count">要复制的元素数。</param>
      <exception cref="T:System.ArgumentException">源数组中的元素数大于从 <paramref name="index" /> 到目标数组末尾处的可用空间。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。- 或 -<paramref name="count" /> 小于零。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Count">
      <summary>获取 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中元素的数目。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 中元素的数目。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>从当前 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象中移除指定集合中的所有元素。</summary>
      <param name="other">要从 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象中移除的项的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.Generic.SortedSet`1" /> 的枚举数。</summary>
      <returns>返回一个按顺序循环访问 <see cref="T:System.Collections.Generic.SortedSet`1" /> 的枚举器。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetViewBetween(`0,`0)">
      <summary>返回 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的子集的视图。</summary>
      <returns>仅包含指定范围内的值的子集视图。</returns>
      <param name="lowerValue">视图中所需的最小值。</param>
      <param name="upperValue">视图中所需的最大值。</param>
      <exception cref="T:System.ArgumentException">根据比较器，<paramref name="lowerValue" /> 大于 <paramref name="upperValue" />。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">在视图上尝试的操作超出了 <paramref name="lowerValue" /> 和 <paramref name="upperValue" /> 指定的范围。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象，使该对象仅包含指定集合中也存在的元素。</summary>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象是否为指定集合的真子集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象是 <paramref name="other" /> 的真子集，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象是否为指定集合的真超集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象是 <paramref name="other" /> 的真超集，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象进行比较的集合。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象是否为指定集合的子集。</summary>
      <returns>如果当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象为 <paramref name="other" /> 的子集，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象是否为指定集合的超集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象是 <paramref name="other" /> 的超集，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象进行比较的集合。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Max">
      <summary>按照比较器的定义，获取 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的最大值。</summary>
      <returns>集内的最大值。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Min">
      <summary>按照比较器的定义，获取 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的最小值。</summary>
      <returns>集内的最小值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象和指定的集合是否共享常见元素。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象与 <paramref name="other" /> 至少共享一个公共元素，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Remove(`0)">
      <summary>从 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中移除指定的项。</summary>
      <returns>true if the element is found and successfully removed; otherwise, false.</returns>
      <param name="item">要移除的元素。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>从 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中移除与指定的谓词所定义的条件相匹配的所有元素。</summary>
      <returns>已从 <see cref="T:System.Collections.Generic.SortedSet`1" /> 集合中移除的元素的数目。 </returns>
      <param name="match">用于定义要移除的元素应满足的条件的委托。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Reverse">
      <summary>返回一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，它按相反的顺序循环访问 <see cref="T:System.Collections.Generic.SortedSet`1" />。</summary>
      <returns>一个枚举器，它按相反的顺序循环访问 <see cref="T:System.Collections.Generic.SortedSet`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>确定当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象与指定的集合中是否包含相同的元素。</summary>
      <returns>如果当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象等于 <paramref name="other" />，则为 true；否则为 false。</returns>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象，使该对象仅包含当前对象或指定集合中存在的元素（但不可包含两者共有的元素）。</summary>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>向 <see cref="T:System.Collections.Generic.ICollection`1" /> 对象添加一个项。</summary>
      <param name="item">要添加到 <see cref="T:System.Collections.Generic.ICollection`1" /> 对象中的对象。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 为只读。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.ICollection" /> 是否为只读。</summary>
      <returns>如果该集合为只读，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>用于循环访问集合的枚举数。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>将整个 <see cref="T:System.Collections.Generic.SortedSet`1" /> 复制到兼容的一维目标数组（从指定的数组索引处开始复制）。</summary>
      <param name="array">一个一维数组，它是从 <see cref="T:System.Collections.Generic.SortedSet`1" /> 复制的元素的目标。该数组的索引必须从零开始。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentException">源数组中的元素数大于从 <paramref name="index" /> 到目标数组末尾处的可用空间。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>如果同步对 <see cref="T:System.Collections.ICollection" /> 的访问，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。在 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的默认实现中，此属性始终返回当前实例。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>用于循环访问集合的枚举数。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象，使该对象包含当前对象或指定集合中存在的所有元素。</summary>
      <param name="other">要与当前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象进行比较的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 为 null。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.SortedSet`1" /> 对象的元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.Dispose">
      <summary>释放由 <see cref="T:System.Collections.Generic.SortedSet`1.Enumerator" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.MoveNext">
      <summary>将枚举器前进到 <see cref="T:System.Collections.Generic.SortedSet`1" /> 集合的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="T:System.Collections.Generic.Stack`1">
      <summary>表示可变大小的后进先出 (LIFO) 集合（对于相同指定类型的实例）。</summary>
      <typeparam name="T">指定堆栈中元素的类型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.Stack`1" /> 类的新实例，该实例为空并且具有默认初始容量。</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Stack`1" /> 类的新实例，该实例包含从指定集合复制的元素并且具有足够的容量来容纳所复制的元素。</summary>
      <param name="collection">从中复制元素的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Collections.Generic.Stack`1" /> 类的新实例，该实例为空，具有指定的初始容量或默认的初始容量（其中较大的一个）。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Stack`1" /> 可包含的初始元素数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Clear">
      <summary>从 <see cref="T:System.Collections.Generic.Stack`1" /> 中移除所有对象。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Contains(`0)">
      <summary>确定某元素是否在 <see cref="T:System.Collections.Generic.Stack`1" /> 中。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.Stack`1" /> 中找到了 <paramref name="item" />，则为 true；否则为 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.Stack`1" /> 中定位的对象。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.CopyTo(`0[],System.Int32)">
      <summary>从特定的数组索引处开始，将 <see cref="T:System.Collections.Generic.Stack`1" /> 复制到现有一维 <see cref="T:System.Array" />。</summary>
      <param name="array">一维 <see cref="T:System.Array" />，它是从 <see cref="T:System.Collections.Generic.Stack`1" /> 复制的元素的目标。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Stack`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Count">
      <summary>获取 <see cref="T:System.Collections.Generic.Stack`1" /> 中包含的元素数。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" /> 中包含的元素个数。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.GetEnumerator">
      <summary>返回用于 <see cref="T:System.Collections.Generic.Stack`1" /> 的枚举数。</summary>
      <returns>用于 <see cref="T:System.Collections.Generic.Stack`1" /> 的 <see cref="T:System.Collections.Generic.Stack`1.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Peek">
      <summary>返回 <see cref="T:System.Collections.Generic.Stack`1" /> 顶部的对象而无需移除它。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" /> 顶部的对象。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Pop">
      <summary>移除并返回 <see cref="T:System.Collections.Generic.Stack`1" /> 顶部的对象。</summary>
      <returns>从 <see cref="T:System.Collections.Generic.Stack`1" /> 顶部移除的对象。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Push(`0)">
      <summary>将对象插入 <see cref="T:System.Collections.Generic.Stack`1" /> 的顶部。</summary>
      <param name="item">要推入到 <see cref="T:System.Collections.Generic.Stack`1" /> 中的对象。对于引用类型，该值可以为 null。</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的 <see cref="T:System.Array" /> 索引处开始，将 <see cref="T:System.Collections.ICollection" /> 的元素复制到一个 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">一维 <see cref="T:System.Array" />，它是从 <see cref="T:System.Collections.ICollection" /> 复制的元素的目标。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>如果对 <see cref="T:System.Collections.ICollection" /> 的访问是同步的（线程安全），则为 true；否则为 false。在 <see cref="T:System.Collections.Generic.Stack`1" /> 的默认实现中，此属性始终返回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。在 <see cref="T:System.Collections.Generic.Stack`1" /> 的默认实现中，此属性始终返回当前实例。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回循环访问集合的枚举数。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.ToArray">
      <summary>将 <see cref="T:System.Collections.Generic.Stack`1" /> 复制到新数组。</summary>
      <returns>一个新数组，包含 <see cref="T:System.Collections.Generic.Stack`1" /> 的元素的副本。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.TrimExcess">
      <summary>如果元素数小于当前容量的 90%，将容量设置为 <see cref="T:System.Collections.Generic.Stack`1" /> 中的实际元素数。</summary>
    </member>
    <member name="T:System.Collections.Generic.Stack`1.Enumerator">
      <summary>枚举 <see cref="T:System.Collections.Generic.Stack`1" /> 的元素。</summary>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" /> 中位于枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.Dispose">
      <summary>释放由 <see cref="T:System.Collections.Generic.Stack`1.Enumerator" /> 使用的所有资源。</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.MoveNext">
      <summary>使枚举数前进到 <see cref="T:System.Collections.Generic.Stack`1" /> 的下一个元素。</summary>
      <returns>如果枚举数成功地推进到下一个元素，则为 true；如果枚举数越过集合的结尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>获取枚举数当前位置的元素。</summary>
      <returns>集合中位于枚举数当前位置的元素。</returns>
      <exception cref="T:System.InvalidOperationException">枚举数定位在该集合的第一个元素之前或最后一个元素之后。</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>将枚举数设置为其初始位置，该位置位于集合中第一个元素之前。此类不能被继承。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
  </members>
</doc>