﻿Imports System.Net.Http
Imports System.Threading
Imports System.Threading.Tasks
Imports HtmlAgilityPack
Imports System.Text.RegularExpressions
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports System.Diagnostics
Imports System.Drawing
Imports DevExpress.XtraGrid.Views.Base
Imports System.Collections.Concurrent
Imports System.ComponentModel
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraWaitForm
Public Class MailCatcher
    Private Emails As New BindingList(Of ScrapedEmailResult)()
    Private processedUrls As New Concurrent.ConcurrentDictionary(Of String, Boolean)()
    Private cancellationTokenSource As CancellationTokenSource
    Private imageExtensions As String() = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff"}
    Private httpClient As HttpClient
    Private isProcessing As Boolean = False
    Sub New()
        InitializeComponent()
        httpClient = New HttpClient()
        httpClient.Timeout = TimeSpan.FromSeconds(15)
        httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    End Sub
    Private Sub MailCatcher_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ProgressPanel1.Visible = False
        SetupGrid()
        UpdateButtonStates()
    End Sub
    Private Sub SetupGrid()
        GridControl1.DataSource = Emails
        ' إعدادات تعطيل تفاصيل المستوى الأول
        GridView1.OptionsDetail.EnableMasterViewMode = False
        GridView1.OptionsDetail.ShowDetailTabs = False
        GridView1.Columns.Clear()
        Dim colEmail As New DevExpress.XtraGrid.Columns.GridColumn()
        colEmail.Caption = "Mail Results"
        colEmail.FieldName = "Email"
        colEmail.Visible = True
        colEmail.Width = 300
        GridView1.Columns.Add(colEmail)
        Dim colUrl As New DevExpress.XtraGrid.Columns.GridColumn()
        colUrl.Caption = "Link Results"
        colUrl.FieldName = "Url"
        colUrl.Visible = True
        colUrl.Width = 400
        GridView1.Columns.Add(colUrl)
        ' إزالة أيقونة ABC
        GridView1.OptionsView.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowOnlyInEditor
        GridView1.OptionsBehavior.EditorShowMode = DevExpress.Utils.EditorShowMode.Click
        GridView1.Appearance.HeaderPanel.Font = New Font(GridView1.Appearance.HeaderPanel.Font, FontStyle.Bold)
        GridView1.OptionsView.ShowAutoFilterRow = True
        GridView1.OptionsBehavior.Editable = False
        GridView1.OptionsView.ShowGroupPanel = False
        GridView1.OptionsView.ShowIndicator = False
        GridView1.BestFitColumns()
    End Sub
    Private Sub UpdateButtonStates()
        BTN_SaveEmail.Enabled = Not isProcessing AndAlso Emails.Count > 0
        BTN_SaveLink.Enabled = Not isProcessing AndAlso Emails.Count > 0
        btnClear.Enabled = Not isProcessing
    End Sub
    Private Sub bntStart_Click(sender As Object, e As EventArgs) Handles bntStart.Click
        If String.IsNullOrWhiteSpace(txtWord.Text) Then
            XtraMessageBox.Show("Please enter a keyword to search.")
            Return
        End If
        If cancellationTokenSource IsNot Nothing AndAlso Not cancellationTokenSource.IsCancellationRequested Then
            cancellationTokenSource.Cancel()
        End If
        cancellationTokenSource = New CancellationTokenSource()
        isProcessing = True
        btnStop.Enabled = True
        bntStart.Enabled = False
        ProgressPanel1.Visible = True
        ProgressPanel1.Description = "Starting search..."
        UpdateButtonStates()
        Task.Run(Sub() ProcessTextInParallel(cancellationTokenSource.Token))
    End Sub
    Private Async Sub ProcessTextInParallel(token As CancellationToken)
        Try
            Dim query As String = txtWord.Text.Trim()
            Dim searchUrls As New Concurrent.ConcurrentBag(Of String)()
            Dim queries As New List(Of String) From {
                query,
                $"{query} email",
                $"{query} contact",
                $"{query} info",
                $"{query} details",
                $"{query} directory"
            }
            Dim searchEngines As New List(Of String) From {
                "https://search.yahoo.com/search?p={0}",
                "https://duckduckgo.com/?q={0}",
                "https://www.google.com/search?q={0}&num=20",
                "https://www.bing.com/search?q={0}&count=20"
            }
            Parallel.ForEach(queries, Sub(q)
                                          Parallel.ForEach(searchEngines, Sub(engine)
                                                                              searchUrls.Add(String.Format(engine, Uri.EscapeDataString(q)))
                                                                          End Sub)
                                      End Sub)
            Dim links As New Concurrent.ConcurrentBag(Of String)()
            Dim options As New ParallelOptions With {
                .MaxDegreeOfParallelism = Environment.ProcessorCount * 2,
                .CancellationToken = token
            }
            Await Task.Run(Sub()
                               Parallel.ForEach(searchUrls, options, Sub(searchUrl)
                                                                         Try
                                                                             Dim results = GetSearchResults(searchUrl, token).Result
                                                                             For Each link In results
                                                                                 links.Add(link)
                                                                             Next
                                                                         Catch ex As Exception
                                                                             Debug.WriteLine($"Error processing search URL: {ex.Message}")
                                                                         End Try
                                                                     End Sub)
                           End Sub)
            ' التحقق من إلغاء العملية بعد جمع الروابط
            token.ThrowIfCancellationRequested()
            Dim processedCount As Integer = 0
            Dim totalLinks = links.Distinct().Where(Function(url) Not processedUrls.ContainsKey(url)).Take(250000).ToList()
            Parallel.For(0, totalLinks.Count, options, Sub(i)
                                                           If token.IsCancellationRequested Then
                                                               Exit Sub
                                                           End If
                                                           Dim url = totalLinks(i)
                                                           If Not processedUrls.ContainsKey(url) Then
                                                               processedUrls.TryAdd(url, True)
                                                               ProcessLink(url, token).Wait()
                                                               Interlocked.Increment(processedCount)
                                                               Me.Invoke(Sub()
                                                                             ProgressPanel1.Description = $"Processing {processedCount} of {totalLinks.Count} - Emails: {Emails.Count} - Links: {processedUrls.Count}"
                                                                         End Sub)
                                                           End If
                                                       End Sub)
            Me.Invoke(Sub()
                          XtraMessageBox.Show($"Found {Emails.Count} Emails and {processedUrls.Count} links!")
                          ProgressPanel1.Visible = False
                          isProcessing = False
                          UpdateButtonStates()
                      End Sub)
        Catch ex As OperationCanceledException
            Me.Invoke(Sub()
                          XtraMessageBox.Show($"Operation cancelled. Found {Emails.Count} emails and {processedUrls.Count} links.")
                          ProgressPanel1.Visible = False
                          isProcessing = False
                          UpdateButtonStates()
                      End Sub)
        Catch ex As Exception
            Me.Invoke(Sub()
                          XtraMessageBox.Show($"Error: {ex.Message}")
                          ProgressPanel1.Visible = False
                          isProcessing = False
                          UpdateButtonStates()
                      End Sub)
        Finally
            Me.Invoke(Sub()
                          btnStop.Enabled = False
                          bntStart.Enabled = True
                          GridView1.RefreshData()
                          UpdateButtonStates()
                      End Sub)
        End Try
    End Sub
    Private Async Function GetSearchResults(searchUrl As String, token As CancellationToken) As Task(Of List(Of String))
        Dim links As New List(Of String)
        Try
            Dim response As String = Await httpClient.GetStringAsync(searchUrl)
            token.ThrowIfCancellationRequested()
            Dim htmlDoc As New HtmlDocument()
            htmlDoc.LoadHtml(response)
            Dim linkPatterns As New Dictionary(Of String, String) From {
                {"google.com", "//a[contains(@href,'http') and not(contains(@href,'google.com'))]"},
                {"bing.com", "//a[contains(@href,'http')]"},
                {"yahoo.com", "//a[contains(@href,'http')]"},
                {"duckduckgo.com", "//a[contains(@href,'http')]"}
            }
            Dim pattern = linkPatterns.FirstOrDefault(Function(x) searchUrl.Contains(x.Key)).Value
            If String.IsNullOrEmpty(pattern) Then pattern = "//a[contains(@href,'http')]"
            Dim nodes = htmlDoc.DocumentNode.SelectNodes(pattern)
            If nodes IsNot Nothing Then
                For Each node In nodes
                    If token.IsCancellationRequested Then Exit For
                    Dim href = node.GetAttributeValue("href", "")
                    If Not String.IsNullOrEmpty(href) Then
                        Try
                            Dim cleanUrl = New Uri(href).GetLeftPart(UriPartial.Path)
                            links.Add(cleanUrl)
                        Catch ex As UriFormatException
                            Debug.WriteLine($"Invalid URL format: {href}")
                        End Try
                    End If
                Next
            End If
        Catch ex As Exception
            Debug.WriteLine($"Search Error: {ex.Message}")
        End Try
        Return links.Distinct().ToList()
    End Function
    Private Async Function ProcessLink(url As String, token As CancellationToken) As Task
        Try
            Dim pageContent As String = Await httpClient.GetStringAsync(url)
            token.ThrowIfCancellationRequested()
            Dim _emails = ExtractEmails(pageContent)
            Dim newEmails As New List(Of ScrapedEmailResult)
            For Each email In _emails.Distinct(StringComparer.OrdinalIgnoreCase)
                If token.IsCancellationRequested Then Exit For
                If imageExtensions.Any(Function(x) email.Contains(x)) Then Continue For
                newEmails.Add(New ScrapedEmailResult With {
                    .Email = email,
                    .Url = url
                })
            Next
            If newEmails.Any() Then
                Me.Invoke(Sub()
                              For Each email In newEmails
                                  Emails.Add(email)
                              Next
                              GridView1.RefreshData()
                          End Sub)
            End If
        Catch ex As Exception
            Debug.WriteLine($"Processing Error: {ex.Message}")
        End Try
    End Function
    Function ExtractEmails(content As String) As List(Of String)
        Dim emailRegex As New Regex("\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", RegexOptions.Compiled Or RegexOptions.IgnoreCase)
        Return emailRegex.Matches(content).Cast(Of Match)().Select(Function(m) m.Value).ToList()
    End Function
    Private Sub btnStop_Click(sender As Object, e As EventArgs) Handles btnStop.Click
        If cancellationTokenSource IsNot Nothing Then
            cancellationTokenSource.Cancel()
            isProcessing = False
            UpdateButtonStates()
        End If
    End Sub
    Private Sub btnClear_Click(sender As Object, e As EventArgs) Handles btnClear.Click
        If isProcessing Then
            XtraMessageBox.Show("Please stop the process first.")
            Return
        End If
        Emails.Clear()
        processedUrls.Clear()
        GridView1.RefreshData()
        UpdateButtonStates()
    End Sub
    Private Sub BTN_SaveEmail_Click(sender As Object, e As EventArgs) Handles BTN_SaveEmail.Click
        SaveToFile("emails.txt", Emails.Select(Function(x) x.Email))
    End Sub
    Private Sub BTN_SaveLink_Click(sender As Object, e As EventArgs) Handles BTN_SaveLink.Click
        SaveToFile("urls.txt", Emails.Select(Function(x) x.Url))
    End Sub
    Private Sub SaveToFile(defaultFilename As String, data As IEnumerable(Of String))
        If isProcessing Then
            XtraMessageBox.Show("Please wait until the process completes.")
            Return
        End If
        Dim desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        Dim folderPath = Path.Combine(desktopPath, "MailCatcher")
        If Not Directory.Exists(folderPath) Then
            Directory.CreateDirectory(folderPath)
        End If
        Using saveFileDialog As New SaveFileDialog()
            saveFileDialog.Filter = "Text Files (*.txt)|*.txt"
            saveFileDialog.DefaultExt = "txt"
            saveFileDialog.InitialDirectory = folderPath
            saveFileDialog.FileName = defaultFilename
            If saveFileDialog.ShowDialog() = DialogResult.OK Then
                Try
                    File.WriteAllLines(saveFileDialog.FileName, data.Distinct())
                    Process.Start("notepad.exe", saveFileDialog.FileName)
                Catch ex As Exception
                    XtraMessageBox.Show($"Error saving file: {ex.Message}")
                End Try
            End If
        End Using
    End Sub
    Private Sub MailCatcher_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        cancellationTokenSource?.Cancel()
        httpClient?.Dispose()
    End Sub
    Private Sub GridView1_CustomDrawCell(sender As Object, e As RowCellCustomDrawEventArgs)
        GridView1.OptionsSelection.EnableAppearanceFocusedRow = False
    End Sub
    Public Class ScrapedEmailResult
        Public Property Email As String
        Public Property Url As String
    End Class
End Class
