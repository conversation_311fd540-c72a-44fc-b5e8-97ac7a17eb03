﻿Imports System
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Windows.Forms
Imports System.Collections.Generic
Imports System.IO
Imports System.Net
Imports System.Net.Http
Imports System.Threading.Tasks
Imports HtmlAgilityPack
Imports DevExpress.XtraEditors
Imports System.ComponentModel
Imports System.Data
Imports DevExpress.XtraWaitForm
Imports System.Net.Sockets
Imports System.Drawing.Drawing2D
Imports DevExpress.XtraBars.Ribbon
Public Class frmLetterinorSP
    Public Property RelatedRibbonPage As RibbonPage
    Public Property originalHtml As String
    Private dt As DataTable ' تعريف DataTable لتخزين البيانات
    ' متغير لحفظ النص السابق في txtLetterSP
    Private previousText As String = ""
    Public Sub Uploade_Letter()
        txtLetterSP.ResetText()
        Dim openFileDialog As New OpenFileDialog With {
            .Filter = "HTML Files|*.html;*.htm",
            .Title = "Select an HTML File"
        }
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            Using reader As New StreamReader(openFileDialog.FileName, Encoding.Default, True)
                originalHtml = reader.ReadToEnd()
            End Using
            txtLetterSP.Text = originalHtml
        End If
    End Sub
    Public Sub Transfer_To_Sender()
        ' التحقق من وجود نص للإرسال
        If String.IsNullOrWhiteSpace(txtLetterSP.Text) Then
            XtraMessageBox.Show("There is no text to send!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        ' تأكيد الإرسال
        Dim result As DialogResult = XtraMessageBox.Show("Do you want to proceed with sending the letter?",
                                                   "Confirmation",
                                                   MessageBoxButtons.YesNo,
                                                   MessageBoxIcon.Question)
        If result <> DialogResult.Yes Then
            Return
        End If
        ' البحث عن نافذة الإرسال إذا كانت مفتوحة
        Dim emailForm As frmEmailSender = Nothing
        For Each form As Form In Application.OpenForms
            If TypeOf form Is frmEmailSender AndAlso Not form.IsDisposed Then
                emailForm = DirectCast(form, frmEmailSender)
                Exit For
            End If
        Next
        ' إذا لم تكن مفتوحة، نعرض رسالة بدلاً من فتحها
        If emailForm Is Nothing Then
            XtraMessageBox.Show("Please open the Sender control panel first.",
                           "Information",
                           MessageBoxButtons.OK,
                           MessageBoxIcon.Information)
            Return
        End If
        ' إذا كانت مفتوحة، ننقل النص
        If emailForm.IsHandleCreated Then
            If emailForm.txtLetter.InvokeRequired Then
                emailForm.Invoke(Sub()
                                     emailForm.txtLetter.Text = txtLetterSP.Text
                                     emailForm.txtLetter.SelectionStart = 0
                                     emailForm.txtLetter.SelectionLength = 0
                                     emailForm.Activate()
                                 End Sub)
            Else
                emailForm.txtLetter.Text = txtLetterSP.Text
                emailForm.txtLetter.SelectionStart = 0
                emailForm.txtLetter.SelectionLength = 0
                emailForm.Activate()
            End If
        End If
        ' إعلام المستخدم بنجاح النقل
        XtraMessageBox.Show("The letter has been transferred to the Sender panel.",
                       "Success",
                       MessageBoxButtons.OK,
                       MessageBoxIcon.Information)
    End Sub
    Public Sub Convert_Text()
        ' التحقق من الحقول الفارغة مع تحديد الحقل الفارغ
        If txtFind.Text.Trim = "" Then
            XtraMessageBox.Show("Please enter the word you want to search for", "Search Term Missing",
                          MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtFind.Focus()
            Return
        End If
        If txtReplace.Text.Trim = "" Then
            XtraMessageBox.Show("Please enter the replacement text", "Replacement Text Missing",
                          MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtReplace.Focus()
            Return
        End If
        If txtLetterSP.Text.Trim = "" Then
            XtraMessageBox.Show("Please add text to the letter first", "Letter Content Missing",
                          MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        Dim searchText As String = txtFind.Text
        Dim replaceText As String = txtReplace.Text
        Dim originalText As String = txtLetterSP.Text
        Dim comparisonType As StringComparison = StringComparison.OrdinalIgnoreCase ' تجاهل حالة الأحرف
        ' استخدام StringBuilder لتحسين الأداء مع النصوص الكبيرة
        Dim sb As New System.Text.StringBuilder(originalText)
        Dim replacementCount As Integer = 0
        Dim index As Integer = originalText.IndexOf(searchText, comparisonType)
        ' استبدال جميع التكرارات
        While index >= 0
            sb.Remove(index, searchText.Length)
            sb.Insert(index, replaceText)
            replacementCount += 1
            index = sb.ToString().IndexOf(searchText, index + replaceText.Length, comparisonType)
        End While
        ' تطبيق التغييرات إذا وجدت نتائج
        If replacementCount > 0 Then
            txtLetterSP.Text = sb.ToString()
            XtraMessageBox.Show($"Successfully replaced {replacementCount} occurrence(s)",
                          "Replacement Complete",
                          MessageBoxButtons.OK, MessageBoxIcon.Information)
            ' إعادة تطبيق التظليل بعد الاستبدال
            HighlightSearchText()
        Else
            XtraMessageBox.Show("The search term was not found in the text",
                          "Not Found",
                          MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub txtFind_TextChanged(sender As Object, e As EventArgs) Handles txtFind.TextChanged
        ' تأخير التنفيذ لتحسين الأداء أثناء الكتابة
        Static updateTimer As New Timer With {.Interval = 300}
        updateTimer.Stop()
        updateTimer = New Timer With {.Interval = 300}
        AddHandler updateTimer.Tick, Sub(s, args)
                                         updateTimer.Stop()
                                         HighlightSearchText()
                                     End Sub
        updateTimer.Start()
    End Sub
    Private Sub HighlightSearchText()
        ' إلغاء التظليل الحالي
        txtLetterSP.SelectAll()
        txtLetterSP.SelectionBackColor = txtLetterSP.BackColor
        txtLetterSP.SelectionColor = txtLetterSP.ForeColor
        txtLetterSP.DeselectAll()
        ' إذا كان حقل البحث فارغاً
        If String.IsNullOrWhiteSpace(txtFind.Text) Then Return
        Dim searchText As String = txtFind.Text
        Dim currentPosition As Integer = txtLetterSP.SelectionStart
        Dim comparisonType As StringComparison = StringComparison.OrdinalIgnoreCase
        Dim index As Integer = 0
        ' تطبيق التظليل على جميع التكرارات
        While index < txtLetterSP.Text.Length AndAlso index >= 0
            index = txtLetterSP.Text.IndexOf(searchText, index, comparisonType)
            If index < 0 Then Exit While
            txtLetterSP.Select(index, searchText.Length)
            txtLetterSP.SelectionBackColor = Color.FromArgb(255, 100, 100) ' لون أحمر فاتح
            txtLetterSP.SelectionColor = Color.White
            index += searchText.Length
        End While
        ' استعادة موضع المؤشر
        txtLetterSP.SelectionStart = currentPosition
        txtLetterSP.SelectionLength = 0
        txtLetterSP.ScrollToCaret()
    End Sub
    Private Sub frmLetterinorSP_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        dt = New DataTable()
        ' إنشاء الأعمدة مع تحديد نوع البيانات
        dt.Columns.Add("Property", GetType(String))
        dt.Columns.Add("Value", GetType(String))
        dt.Columns.Add("Details", GetType(String))
        ' تعيين مصدر البيانات مع إعادة تهيئة GridView
        GridControl1.DataSource = dt
        Dim gridView As DevExpress.XtraGrid.Views.Grid.GridView = CType(GridControl1.MainView, DevExpress.XtraGrid.Views.Grid.GridView)
        gridView.PopulateColumns()
        gridView.OptionsView.ShowGroupPanel = False
        gridView.OptionsView.RowAutoHeight = True
        frmMain.RibbonPageGroup26.Visible = False
        frmMain.RibbonPageGroup27.Visible = False
        frmMain.RibbonPageGroup28.Visible = False
        frmMain.RibbonPageGroup29.Visible = False
        frmMain.RibbonPageGroup30.Visible = False
        frmMain.RibbonPageGroup31.Visible = False
    End Sub
    Private Sub MakeRoundedPanel(panel As PanelControl, radius As Integer)
        Dim path As New GraphicsPath()
        path.AddArc(0, 0, radius, radius, 180, 90)
        path.AddArc(panel.Width - radius, 0, radius, radius, 270, 90)
        path.AddArc(panel.Width - radius, panel.Height - radius, radius, radius, 0, 90)
        path.AddArc(0, panel.Height - radius, radius, radius, 90, 90)
        path.CloseFigure()
        panel.Region = New Region(path)
    End Sub
    Public Sub Start_Check()
        PanelControl1.Show()
        progressPanel1.Visible = True
        If Not BackgroundWorker1.IsBusy Then
            BackgroundWorker1.RunWorkerAsync()
        End If
    End Sub
    ' دالة محدثة لعرض البيانات في GridControl1
    Private Sub UpdateGridControl(myIP As String, isBlacklisted As Boolean, foundBlacklists As List(Of String), suspiciousCheck As Tuple(Of String, Integer, String))
        Try
            dt.Rows.Clear()
            ' إضافة بيانات IP مع التحقق من الخطأ
            dt.Rows.Add("IP Address", If(String.IsNullOrEmpty(myIP) OrElse myIP = "Unknown", "Error", myIP), "")
            ' إضافة حالة القائمة السوداء
            dt.Rows.Add("Blacklisted", If(isBlacklisted, "Yes", "No"), "")
            ' إضافة القوائم السوداء
            If foundBlacklists?.Count > 0 Then
                For Each bl In foundBlacklists
                    dt.Rows.Add("Blacklist Found", "Yes", bl)
                Next
            Else
                dt.Rows.Add("Blacklist Found", "No", "None")
            End If
            ' إضافة الكلمات المشبوهة مع تصحيح اسم العمود
            If Not String.IsNullOrWhiteSpace(suspiciousCheck?.Item1) Then
                dt.Rows.Add("Suspicious Words", suspiciousCheck.Item1, $"Count: {suspiciousCheck.Item2}")
                If Not String.IsNullOrWhiteSpace(suspiciousCheck.Item3) Then
                    dt.Rows.Add("Context", "", suspiciousCheck.Item3)
                End If
            Else
                dt.Rows.Add("Suspicious Words", "None", "")
            End If
            ' إعادة تعيين مصدر البيانات مع التحديث القسري
            GridControl1.DataSource = Nothing
            GridControl1.DataSource = dt
            GridControl1.ForceInitialize()
            ' تحسين مظهر الجدول
            Dim gridView As DevExpress.XtraGrid.Views.Grid.GridView = CType(GridControl1.MainView, DevExpress.XtraGrid.Views.Grid.GridView)
            gridView.BestFitColumns()
            gridView.Columns("Property").Width = 150
            gridView.Columns("Value").Width = 100
            gridView.Columns("Details").Width = 300
        Catch ex As Exception
            XtraMessageBox.Show($"Error updating grid: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub ClearGridControl()
        dt.Clear()
        GridControl1.RefreshDataSource()
    End Sub
    Private Function CheckForSuspiciousWords(suspiciousWords As List(Of String)) As Tuple(Of String, Integer, String)
        Dim letterText As String = ""
        If txtLetterSP.InvokeRequired Then
            txtLetterSP.Invoke(Sub()
                                   letterText = txtLetterSP.Text
                               End Sub)
        Else
            letterText = txtLetterSP.Text
        End If
        If String.IsNullOrWhiteSpace(letterText) Then
            Return New Tuple(Of String, Integer, String)("", 0, "")
        End If
        Dim foundWords As New List(Of String)
        Dim matchCount As Integer = 0
        For Each word In suspiciousWords
            Dim occurrences As Integer = CountOccurrences(letterText, word)
            If occurrences > 0 Then
                foundWords.Add(word)
                matchCount += occurrences
            End If
        Next
        Dim context As String = If(foundWords.Any(), String.Join(", ", foundWords), "")
        Return New Tuple(Of String, Integer, String)(context, matchCount, letterText)
    End Function
    Private Function CountOccurrences(text As String, word As String) As Integer
        Dim count As Integer = 0
        Dim index As Integer = text.IndexOf(word, StringComparison.OrdinalIgnoreCase)
        While index <> -1
            count += 1
            index = text.IndexOf(word, index + word.Length, StringComparison.OrdinalIgnoreCase)
        End While
        Return count
    End Function
    Public Sub Reset_Aall()
        Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show("What do you want to delete?" & vbCrLf &
                                                                            "To clear the data in the columns, click Yes." & vbCrLf &
                                                                            "To clear Your Letter, click No.",
                                                                            "Confirmation",
                                                                            MessageBoxButtons.YesNoCancel,
                                                                            MessageBoxIcon.Question)
        If result = DialogResult.Yes Then
            ClearGridControl1()
        ElseIf result = DialogResult.No Then
            ClearTextLetterSP()
        End If
    End Sub
    Private Sub ClearGridControl1()
        If dt.Rows.Count = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("There is no data in the columns to clear.",
                                           "Alert",
                                           MessageBoxButtons.OK,
                                           MessageBoxIcon.Information)
        Else
            dt.Rows.Clear()
            GridControl1.RefreshDataSource()
            DevExpress.XtraEditors.XtraMessageBox.Show("All data has been cleared from the columns.",
                                           "Success",
                                           MessageBoxButtons.OK,
                                           MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub ClearTextLetterSP()
        If String.IsNullOrWhiteSpace(txtLetterSP.Text) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("There is no text to Delete.",
                                           "Warning",
                                           MessageBoxButtons.OK,
                                           MessageBoxIcon.Information)
        Else
            txtLetterSP.Clear()
            DevExpress.XtraEditors.XtraMessageBox.Show("The Letter has been Deleted.",
                                           "Success",
                                           MessageBoxButtons.OK,
                                           MessageBoxIcon.Information)
        End If
    End Sub
    Public Function GetExternalIPv4Address() As String
        Try
            Dim client As New WebClient()
            Dim externalIP As String = client.DownloadString("https://api64.ipify.org").Trim()
            Return externalIP
        Catch ex As Exception
            Debug.Print("❌ Error retrieving external IP: " & ex.Message)
            MsgBox("❌ Failed to retrieve external IP: " & ex.Message, MsgBoxStyle.Critical, "Error")
            Return "Unknown"
        End Try
    End Function
    Public Function GetBlacklists() As List(Of String)
        Return New List(Of String) From {
    "b.barracudacentral.org",
    "bl.spamcop.net",
    "zen.spamhaus.org",
    "dnsbl.sorbs.net",
    "bl.blocklist.de",
    "bl.mailspike.net",
    "ubl.unsubscore.com",
    "psbl.surriel.com",
    "dnsbl.dronebl.org",
    "spam.dnsbl.sorbs.net",
    "cbl.abuseat.org",
    "rbl.interserver.net",
    "dnsbl.inps.de",
    "dnsbl.spfbl.net",
    "noptr.spamrats.com",
    "dyna.spamrats.com",
    "spam.spamrats.com",
    "bl.spameatingmonkey.net",
    "bl.score.senderscore.com",
    "dnsrbl.swinog.ch",
    "rbl.0spam.org",
    "rbl.abuse.ro",
    "rbl.blockedservers.com",
    "rbl.emailbasura.org",
    "rbl.efnet.org",
    "rbl.iprange.net",
    "rbl.tornevall.org",
    "rbl.suresupport.com",
    "dnsbl.justspam.org",
    "dnsbl.kempt.net",
    "dnsbl.njabl.org",
    "dnsbl.rv-soft.info",
    "dnsbl.rymsho.ru",
    "dnsbl.ahbl.org",
    "dnsbl.msu.ru",
    "dnsbl.zapbl.net",
    "spam.dnsbl.anonmails.de",
    "spam.dnsbl.abuse.ch",
    "spam.dnsbl.sorbs.net",
    "dnsbl-1.uceprotect.net",
    "dnsbl-2.uceprotect.net",
    "dnsbl-3.uceprotect.net",
    "dnsbl.proxybl.org",
    "dnsbl.tornevall.org",
    "dnsbl.dronebl.org",
    "rbl.efnet.org",
    "rbl.interserver.net",
    "rbl.spamlab.com",
    "rbl.suresupport.com",
    "dnsbl.sorbs.net",
    "spam.dnsbl.sorbs.net",
    "z.mailspike.net",
    "bl.mailspike.net",
    "dnsbl.spamhaus.org",
    "pbl.spamhaus.org",
    "sbl.spamhaus.org",
    "xbl.spamhaus.org",
    "zen.spamhaus.org",
    "ubl.unsubscore.com",
    "ix.dnsbl.manitu.net",
    "bl.spamcop.net",
    "truncate.gbudb.net",
    "all.s5h.net",
    "bl.blocklist.de",
    "dnsbl.dronebl.org",
    "dnsbl.proxybl.org",
    "dnsbl.inps.de",
    "psbl.surriel.com",
    "dnsbl.cobion.com",
    "dnsbl.kempt.net",
    "dnsbl.njabl.org",
    "dnsbl.rv-soft.info",
    "dnsbl.rymsho.ru",
    "rbl.abuse.ro",
    "rbl.blockedservers.com",
    "rbl.emailbasura.org",
    "rbl.efnet.org",
    "rbl.iprange.net",
    "rbl.megarbl.net",
    "rbl.spamlab.com",
    "rbl.suresupport.com",
    "b.barracudacentral.org",
    "dnsbl.justspam.org",
    "bl.emailbasura.org",
    "sbl.nszones.com",
    "dnsbl.ahbl.org",
    "rbl.tornevall.org",
    "rbl.orbitrbl.com",
    "dbl.spamhaus.org",
    "bl.suomispam.net",
    "spamguard.leadmon.net",
    "rbl.octopuscloud.com",
    "dnsbl.scamcop.net"
}
    End Function
    Public Function CheckIPBlacklisted(ip As String, blacklists As List(Of String)) As List(Of String)
        Dim foundBlacklists As New List(Of String)()
        Try
            If String.IsNullOrEmpty(ip) OrElse ip = "Unknown" Then Return foundBlacklists
            Dim reversedIP As String = String.Join(".", ip.Split("."c).Reverse())
            Dim tasks As New List(Of Task)()
            For Each blacklist In blacklists
                tasks.Add(Task.Run(Sub()
                                       Dim query As String = $"{reversedIP}.{blacklist}"
                                       Debug.Print("🔍 Checking: " & query)
                                       Dim nslookupResult As String = RunNslookup(query)
                                       If nslookupResult.Contains("127.0.0.") Then
                                           SyncLock foundBlacklists
                                               foundBlacklists.Add(blacklist)
                                           End SyncLock
                                       End If
                                   End Sub))
            Next
            Task.WhenAll(tasks).Wait()
        Catch ex As Exception
            Debug.Print("Error in CheckIPBlacklisted: " & ex.Message)
        End Try
        Return foundBlacklists
    End Function
    Public Function RunNslookup(domain As String) As String
        Try
            Dim process As New Process()
            process.StartInfo.FileName = "cmd.exe"
            process.StartInfo.Arguments = "/c nslookup " & domain
            process.StartInfo.RedirectStandardOutput = True
            process.StartInfo.UseShellExecute = False
            process.StartInfo.CreateNoWindow = True
            process.Start()
            Dim output As String = process.StandardOutput.ReadToEnd()
            process.WaitForExit()
            Debug.Print("🔍 Nslookup Output for " & domain & ": " & output)
            Return output
        Catch ex As Exception
            Debug.Print("❌ Error running nslookup: " & ex.Message)
            Return ""
        End Try
    End Function
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        Try
            PanelControl1.Hide()
            progressPanel1.Visible = False
            If e.Error IsNot Nothing Then
                XtraMessageBox.Show($"Error during check: {e.Error.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If
            Dim result = TryCast(e.Result, Tuple(Of String, Boolean, List(Of String), Tuple(Of String, Integer, String)))
            If result IsNot Nothing Then
                If GridControl1.InvokeRequired Then
                    GridControl1.Invoke(Sub() UpdateGridControl(result.Item1, result.Item2, result.Item3, result.Item4))
                Else
                    UpdateGridControl(result.Item1, result.Item2, result.Item3, result.Item4)
                End If
            End If
        Catch ex As Exception
            XtraMessageBox.Show($"Error in RunWorkerCompleted: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Public Function GetSuspiciousWords() As List(Of String)
        Dim suspiciousWords As New List(Of String)
        Dim client As New WebClient()
        Try
            Dim rawData As String = client.DownloadString("https://textbin.net/raw/aqcwikjukf")
            suspiciousWords = rawData.Split(New String() {vbCrLf, vbLf}, StringSplitOptions.RemoveEmptyEntries).ToList()
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("An error occurred while loading suspicious words: " & ex.Message,
                                       "Error",
                                       MessageBoxButtons.OK,
                                       MessageBoxIcon.Error)
        End Try
        Return suspiciousWords
    End Function
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Try
            ' الحصول على IP
            Dim myIP As String = GetExternalIPv4Address()
            If String.IsNullOrEmpty(myIP) OrElse myIP = "Unknown" Then
                e.Result = New Tuple(Of String, Boolean, List(Of String), Tuple(Of String, Integer, String))(
                "Error", False, New List(Of String), New Tuple(Of String, Integer, String)("", 0, ""))
                Return
            End If
            ' التحقق من القوائم السوداء
            Dim blacklists As List(Of String) = GetBlacklists()
            Dim foundBlacklists As List(Of String) = CheckIPBlacklisted(myIP, blacklists)
            Dim isBlacklisted As Boolean = foundBlacklists?.Count > 0
            ' التحقق من الكلمات المشبوهة
            Dim suspiciousWords As List(Of String) = GetSuspiciousWords()
            Dim suspiciousCheck As Tuple(Of String, Integer, String) = CheckForSuspiciousWords(suspiciousWords)
            ' إرجاع النتائج
            e.Result = New Tuple(Of String, Boolean, List(Of String), Tuple(Of String, Integer, String))(
            myIP, isBlacklisted, foundBlacklists, suspiciousCheck)
        Catch ex As Exception
            e.Result = New Tuple(Of String, Boolean, List(Of String), Tuple(Of String, Integer, String))(
            "Error", False, New List(Of String), New Tuple(Of String, Integer, String)("", 0, ""))
        End Try
    End Sub
    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        If progressPanel1.InvokeRequired Then
            progressPanel1.Invoke(Sub()
                                      progressPanel1.Caption = e.UserState.ToString()
                                      progressPanel1.Description = $"Progress: {e.ProgressPercentage}%"
                                  End Sub)
        Else
            progressPanel1.Caption = e.UserState.ToString()
            progressPanel1.Description = $"Progress: {e.ProgressPercentage}%"
        End If
    End Sub
    Private Sub frmLetterinorSP_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        frmMain.RibbonPageGroup26.Visible = False
        frmMain.RibbonPageGroup27.Visible = False
        frmMain.RibbonPageGroup28.Visible = False
        frmMain.RibbonPageGroup29.Visible = False
        frmMain.RibbonPageGroup30.Visible = False
        frmMain.RibbonPageGroup31.Visible = False
    End Sub
End Class