﻿Imports System.IO
Imports System.Text
Imports DevExpress.XtraBars
Imports DevExpress.XtraBars.Ribbon
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Public Class frmAddHostToSMTP
    Dim Lines As New List(Of String)
    Public Property RelatedRibbonPage As RibbonPage
    Private Sub BtnSave_Click(sender As Object, e As EventArgs)
        ' Check if txtMailList is empty
        If String.IsNullOrWhiteSpace(txtMailList.Text) Then
            ' Show a message to the user
            DevExpress.XtraEditors.XtraMessageBox.Show("Please click on Start first to start the ordering process",
                                                        "Warning",
                                                        MessageBoxButtons.OK,
                                                        MessageBoxIcon.Warning)
            Return ' Exit the method
        End If
        ' Create and configure the DevExpress Save File Dialog
        Dim saveFileDialog As New SaveFileDialog()
        saveFileDialog.Filter = "Text Files (*.txt)|*.txt"
        saveFileDialog.Title = "Choose Save Location"
        saveFileDialog.FileName = "MailList.txt" ' Default file name
        ' Show the dialog and handle the user's selection
        If saveFileDialog.ShowDialog() = DialogResult.OK Then
            Dim filePath As String = saveFileDialog.FileName
            ' Write the contents of txtMailList to the selected file
            Try
                System.IO.File.WriteAllText(filePath, txtMailList.Text)
                ' Use DevExpress notification for success
                DevExpress.XtraEditors.XtraMessageBox.Show("File saved Successfully at: " & filePath,
                                                            "Success",
                                                            MessageBoxButtons.OK,
                                                            MessageBoxIcon.Information)
            Catch ex As Exception
                ' Use DevExpress notification for error
                DevExpress.XtraEditors.XtraMessageBox.Show("An error occurred while saving the file: " & ex.Message,
                                                            "Error",
                                                            MessageBoxButtons.OK,
                                                            MessageBoxIcon.Error)
            End Try
        End If
    End Sub
    Public Sub Start_Older()
        If MemoEdit1.Text.Trim = "" Then
            XtraMessageBox.Show("Please Uploade List...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Information)
            MemoEdit1.Focus()
            Exit Sub
        End If
        If txt_Seperate.Text.Trim = "" Then
            XtraMessageBox.Show("Please Enter | in Format Info", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Information)
            txt_Seperate.Focus()
            Exit Sub
        End If
        If TextHost.Text.Trim = "" Then
            XtraMessageBox.Show("Please Enter Host", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Information)
            TextHost.Focus()
            Exit Sub
        End If
        If TextPort.Text.Trim = "" Then
            XtraMessageBox.Show("Please Enter Port", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Information)
            TextPort.Focus()
            Exit Sub
        End If
        lbl_total_con.Text = "0"
        txtMailList.EditValue = ""
        Lines.ForEach(Sub(x)
                          ' تنظيف السطر
                          x = x.Trim().Replace(":", "|")
                          ' تقسيم بناءً على الفاصل الجديد "|"
                          Dim parts As String() = x.Split("|"c)
                          If parts.Length >= 2 Then
                              Dim email As String = parts(0).Trim()
                              Dim password As String = parts(1).Trim()
                              Dim fullLine As String = String.Format("{0}|{1}|{2}|{3}|{1}",
                                                                 TextHost.Text.Trim(), email, password, TextPort.Text.Trim())
                              txtMailList.AppendLine(fullLine)
                              lbl_total_con.Text = Val(lbl_total_con.Text) + 1
                          End If
                      End Sub)
        DevExpress.XtraEditors.XtraMessageBox.Show("Done!!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    Public Sub Save_Mail_List()
        ' افحص ما إذا كانت النصوص المراد حفظها فعلاً موجودة قبل الحفظ
        If String.IsNullOrEmpty(txtMailList.Text) Then
            XtraMessageBox.Show("There is nothing to List.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If
        Try
            ' احفظ النص الموجود في txtMailList إلى ملف .txt على سطح المكتب
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim filePath As String = Path.Combine(desktopPath, "mail_list_SMTP.txt")
            ' اكتب النص إلى الملف
            File.WriteAllText(filePath, txtMailList.Text)
            ' أعلم المستخدم بنجاح الحفظ
            XtraMessageBox.Show("Saved Successfully")
            MemoEdit1.Clear()
            txtMailList.Clear()
            ' فتح الملف بعد الحفظ
            System.Diagnostics.Process.Start(filePath)
        Catch ex As Exception
            XtraMessageBox.Show("There is nothing to List For Save", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            XtraMessageBox.Show("An error occurred while saving: " & ex.Message)
        End Try
    End Sub
    Public Sub Copy_Mail_Results()
        If Not String.IsNullOrEmpty(txtMailList.Text) Then
            ' نسخ النص إلى الحافظة
            Clipboard.SetText(txtMailList.Text)
            XtraMessageBox.Show("Copied.", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            XtraMessageBox.Show("There is nothing to copy.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Public Sub Add_Mail_List()
        lbl_total_add.Text = "0"
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            Try
                lbl_total_add.Text = "0"
                MemoEdit1.EditValue = ""
                Dim filePath As String = openFileDialog.FileName
                Lines = New HashSet(Of String)(File.ReadAllLines(filePath)).ToList()
                Lines.ForEach(Sub(x) MemoEdit1.AppendLine(x))
                lbl_total_add.Text = Lines.Count()
            Catch ex As Exception
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
    Public Sub Clear_Mail_List()
        lbl_total_add.Text = ""
        lbl_total_con.Text = ""
        MemoEdit1.Clear()
        txtMailList.Clear()
        TextHost.Clear()
        TextPort.Clear()
        txt_Seperate.Clear()
    End Sub
    Public Sub Get_example()
        TextHost.Text = "smtp.office365.com"
        MemoEdit1.Text = "<EMAIL>:123456"
        TextPort.Text = "587"
        txt_Seperate.Text = "|"
    End Sub
    Private Sub frmAddHostToSMTP_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        frmMain.BarButtonItem2.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        frmMain.BntStartOlderHost.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        frmMain.BntClear.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        frmMain.Bnt_get_Example.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        frmMain.Bnt_Copy_List.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        frmMain.BntSaveSMTPList.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        frmMain.RibbonPageGroup11.Visible = False
        frmMain.RibbonPageGroup19.Visible = False
        frmMain.RibbonPageGroup20.Visible = False
        frmMain.RibbonPageGroup21.Visible = False
        frmMain.RibbonPageGroup22.Visible = False
        frmMain.RibbonPageGroup23.Visible = False
    End Sub


End Class