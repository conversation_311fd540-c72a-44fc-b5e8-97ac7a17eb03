﻿Imports System
Imports System.Collections.Generic
Imports System.IO
Imports System.Linq
Imports System.Text
Imports System.Threading.Tasks
Imports YamlDotNet.Serialization
Public NotInheritable Class Config
    Public Shared rnd As Random = New Random()
    Public Shared ProxyType As Integer = 0
    Public Shared Timeout As Integer = &H2710
    Public Shared Threads As Integer = 100
    Public Shared SSL As Boolean = True
    Public Shared Function SetSettings(ByVal file As String) As Boolean
        Try
            Dim dictionary As Dictionary(Of Object, Object) = TryCast(New Deserializer().Deserialize(New StringReader(My.Computer.FileSystem.ReadAllText(file))), Dictionary(Of Object, Object))
            Dim dictionary2 As Dictionary(Of Object, Object) = TryCast(dictionary("proxies"), Dictionary(Of Object, Object))
            Dim dictionary3 As Dictionary(Of Object, Object) = TryCast(dictionary("settings"), Dictionary(Of Object, Object))
            Integer.TryParse(TryCast(dictionary3("Threads"), String), Threads)
            Integer.TryParse(TryCast(dictionary3("Timeout"), String), Timeout)
            SSL = CBool(dictionary3("SSL"))
            Dim Str As String = dictionary2("protocol")
            If (Str.ToLower() = "http") Then
                ProxyType = 0
            ElseIf (Str.ToLower() = "socks4") Then
                ProxyType = 1
            ElseIf (Str.ToLower() = "socks5") Then
                ProxyType = 2
            End If
            Return True
        Catch ex As Exception
            Return False
        End Try
    End Function
    Public isRunning As Boolean
End Class
