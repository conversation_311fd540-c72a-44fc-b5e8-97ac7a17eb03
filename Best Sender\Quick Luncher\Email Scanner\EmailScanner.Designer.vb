﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class EmailScanner
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim ColumnDefinition1 As DevExpress.XtraLayout.ColumnDefinition = New DevExpress.XtraLayout.ColumnDefinition()
        Dim RowDefinition1 As DevExpress.XtraLayout.RowDefinition = New DevExpress.XtraLayout.RowDefinition()
        Dim RowDefinition2 As DevExpress.XtraLayout.RowDefinition = New DevExpress.XtraLayout.RowDefinition()
        Dim EditorButtonImageOptions1 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(EmailScanner))
        Dim SerializableAppearanceObject1 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject2 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject3 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject4 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim EditorButtonImageOptions2 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject5 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject6 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject7 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject8 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.XtraTabPage1 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.SimpleButton4 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton3 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        Me.txtmailfrom = New DevExpress.XtraEditors.TextEdit()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemTextEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
        Me.GridColumn7 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Bnt_Remove = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.XtraTabPage2 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.SimpleButton5 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton6 = New DevExpress.XtraEditors.SimpleButton()
        Me.GridControl2 = New DevExpress.XtraGrid.GridControl()
        Me.GridView2 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn6 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.BntDelete = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.SimpleButton7 = New DevExpress.XtraEditors.SimpleButton()
        Me.txtmailto = New DevExpress.XtraEditors.TextEdit()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.pnlSmtp = New System.Windows.Forms.Panel()
        Me.SimpleButton8 = New DevExpress.XtraEditors.SimpleButton()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.ResultLabel = New System.Windows.Forms.Label()
        Me.CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
        Me.BntCheckSmtp = New DevExpress.XtraEditors.SimpleButton()
        Me.BntNew = New DevExpress.XtraEditors.SimpleButton()
        Me.lblattachfile = New System.Windows.Forms.Label()
        Me.LinkLabel1 = New DevExpress.XtraEditors.HyperlinkLabelControl()
        Me.txtSubject = New DevExpress.XtraEditors.MemoEdit()
        Me.txtFromName = New DevExpress.XtraEditors.TextEdit()
        Me.lbl5 = New System.Windows.Forms.Label()
        Me.txtpassword = New DevExpress.XtraEditors.TextEdit()
        Me.lbl4 = New System.Windows.Forms.Label()
        Me.txtEmail = New DevExpress.XtraEditors.TextEdit()
        Me.lbl3 = New System.Windows.Forms.Label()
        Me.CheckSSL = New DevExpress.XtraEditors.CheckEdit()
        Me.txtPort = New DevExpress.XtraEditors.TextEdit()
        Me.lbl2 = New System.Windows.Forms.Label()
        Me.txtHost = New DevExpress.XtraEditors.TextEdit()
        Me.lbl1 = New System.Windows.Forms.Label()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.trmfadein = New System.Windows.Forms.Timer(Me.components)
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.DxErrorProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(Me.components)
        Me.Timer2 = New System.Windows.Forms.Timer(Me.components)
        Me.BackgroundWorker2 = New System.ComponentModel.BackgroundWorker()
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.trmNotify = New System.Windows.Forms.Timer(Me.components)
        Me.ToolTipController1 = New DevExpress.Utils.ToolTipController(Me.components)
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.XtraTabPage1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.txtmailfrom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Bnt_Remove, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage2.SuspendLayout()
        Me.Panel2.SuspendLayout()
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BntDelete, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtmailto.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        Me.pnlSmtp.SuspendLayout()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtSubject.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtFromName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtpassword.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtEmail.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckSSL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtPort.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHost.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem2, Me.LayoutControlItem1})
        Me.Root.LayoutMode = DevExpress.XtraLayout.Utils.LayoutMode.Table
        Me.Root.Name = "Root"
        ColumnDefinition1.SizeType = System.Windows.Forms.SizeType.Percent
        ColumnDefinition1.Width = 100.0R
        Me.Root.OptionsTableLayoutGroup.ColumnDefinitions.AddRange(New DevExpress.XtraLayout.ColumnDefinition() {ColumnDefinition1})
        RowDefinition1.Height = 80.0R
        RowDefinition1.SizeType = System.Windows.Forms.SizeType.Percent
        RowDefinition2.Height = 100.0R
        RowDefinition2.SizeType = System.Windows.Forms.SizeType.Percent
        Me.Root.OptionsTableLayoutGroup.RowDefinitions.AddRange(New DevExpress.XtraLayout.RowDefinition() {RowDefinition1, RowDefinition2})
        Me.Root.Size = New System.Drawing.Size(1134, 834)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.XtraTabControl1
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 359)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.OptionsTableLayoutItem.RowIndex = 1
        Me.LayoutControlItem2.Size = New System.Drawing.Size(1108, 449)
        Me.LayoutControlItem2.TextVisible = False
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.XtraTabControl1.Appearance.Options.UseFont = True
        Me.XtraTabControl1.AppearancePage.Header.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.AppearancePage.Header.Options.UseFont = True
        Me.XtraTabControl1.AppearancePage.HeaderActive.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.AppearancePage.HeaderActive.Options.UseFont = True
        Me.XtraTabControl1.Location = New System.Drawing.Point(16, 375)
        Me.XtraTabControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.XtraTabPage1
        Me.XtraTabControl1.Size = New System.Drawing.Size(1102, 443)
        Me.XtraTabControl1.TabIndex = 83
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.XtraTabPage1, Me.XtraTabPage2})
        '
        'XtraTabPage1
        '
        Me.XtraTabPage1.Appearance.PageClient.BackColor = System.Drawing.Color.FromArgb(CType(CType(207, Byte), Integer), CType(CType(221, Byte), Integer), CType(CType(238, Byte), Integer))
        Me.XtraTabPage1.Appearance.PageClient.Options.UseBackColor = True
        Me.XtraTabPage1.Controls.Add(Me.Panel1)
        Me.XtraTabPage1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage1.Name = "XtraTabPage1"
        Me.XtraTabPage1.Size = New System.Drawing.Size(1100, 412)
        Me.XtraTabPage1.Text = "   My Mail List   "
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.SimpleButton4)
        Me.Panel1.Controls.Add(Me.SimpleButton3)
        Me.Panel1.Controls.Add(Me.SimpleButton2)
        Me.Panel1.Controls.Add(Me.txtmailfrom)
        Me.Panel1.Controls.Add(Me.GridControl1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1100, 412)
        Me.Panel1.TabIndex = 0
        '
        'SimpleButton4
        '
        Me.SimpleButton4.AllowFocus = False
        Me.SimpleButton4.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton4.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton4.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton4.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton4.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton4.Appearance.Options.UseBackColor = True
        Me.SimpleButton4.Appearance.Options.UseBorderColor = True
        Me.SimpleButton4.Appearance.Options.UseFont = True
        Me.SimpleButton4.Appearance.Options.UseForeColor = True
        Me.SimpleButton4.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton4.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton4.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.SimpleButton4.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton4.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton4.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton4.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton4.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton4.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton4.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton4.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.SimpleButton4.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton4.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton4.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton4.AppearanceHovered.Options.UseFont = True
        Me.SimpleButton4.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton4.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton4.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton4.AppearancePressed.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.SimpleButton4.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton4.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton4.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton4.AppearancePressed.Options.UseFont = True
        Me.SimpleButton4.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton4.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.addheader_32x322
        Me.SimpleButton4.Location = New System.Drawing.Point(938, 347)
        Me.SimpleButton4.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SimpleButton4.Name = "SimpleButton4"
        Me.SimpleButton4.Size = New System.Drawing.Size(134, 47)
        Me.SimpleButton4.TabIndex = 315
        Me.SimpleButton4.Text = "&Add List"
        '
        'SimpleButton3
        '
        Me.SimpleButton3.AllowFocus = False
        Me.SimpleButton3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton3.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton3.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton3.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton3.Appearance.Options.UseBackColor = True
        Me.SimpleButton3.Appearance.Options.UseBorderColor = True
        Me.SimpleButton3.Appearance.Options.UseFont = True
        Me.SimpleButton3.Appearance.Options.UseForeColor = True
        Me.SimpleButton3.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton3.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton3.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.SimpleButton3.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton3.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton3.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton3.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton3.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton3.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton3.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton3.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.SimpleButton3.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton3.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton3.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton3.AppearanceHovered.Options.UseFont = True
        Me.SimpleButton3.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton3.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton3.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton3.AppearancePressed.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.SimpleButton3.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton3.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton3.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton3.AppearancePressed.Options.UseFont = True
        Me.SimpleButton3.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.SimpleButton3.Location = New System.Drawing.Point(798, 347)
        Me.SimpleButton3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SimpleButton3.Name = "SimpleButton3"
        Me.SimpleButton3.Size = New System.Drawing.Size(134, 47)
        Me.SimpleButton3.TabIndex = 314
        Me.SimpleButton3.Text = "&Clear List"
        '
        'SimpleButton2
        '
        Me.SimpleButton2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton2.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton2.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton2.Appearance.Options.UseBackColor = True
        Me.SimpleButton2.Appearance.Options.UseBorderColor = True
        Me.SimpleButton2.Appearance.Options.UseFont = True
        Me.SimpleButton2.Appearance.Options.UseForeColor = True
        Me.SimpleButton2.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton2.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton2.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton2.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton2.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton2.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton2.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton2.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton2.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton2.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton2.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton2.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton2.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton2.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton2.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton2.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton2.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton2.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton2.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton2.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.SimpleButton2.Location = New System.Drawing.Point(327, 19)
        Me.SimpleButton2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(128, 42)
        Me.SimpleButton2.TabIndex = 313
        Me.SimpleButton2.Text = "Add Email"
        '
        'txtmailfrom
        '
        Me.txtmailfrom.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.txtmailfrom.EditValue = ""
        Me.txtmailfrom.Location = New System.Drawing.Point(24, 25)
        Me.txtmailfrom.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtmailfrom.Name = "txtmailfrom"
        Me.txtmailfrom.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtmailfrom.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtmailfrom.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.txtmailfrom.Properties.Appearance.Options.UseBackColor = True
        Me.txtmailfrom.Properties.Appearance.Options.UseFont = True
        Me.txtmailfrom.Properties.Appearance.Options.UseForeColor = True
        Me.txtmailfrom.Properties.NullValuePrompt = "Enter a vaild email address..."
        Me.txtmailfrom.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtmailfrom.Size = New System.Drawing.Size(296, 30)
        Me.txtmailfrom.TabIndex = 312
        '
        'GridControl1
        '
        Me.GridControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl1.EmbeddedNavigator.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GridControl1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.GridControl1.Location = New System.Drawing.Point(24, 79)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemTextEdit1, Me.Bnt_Remove})
        Me.GridControl1.Size = New System.Drawing.Size(1051, 253)
        Me.GridControl1.TabIndex = 311
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Appearance.Empty.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.Empty.Options.UseBackColor = True
        Me.GridView1.Appearance.Row.BackColor2 = System.Drawing.Color.WhiteSmoke
        Me.GridView1.ColumnPanelRowHeight = 39
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn3, Me.GridColumn7})
        Me.GridView1.DetailHeight = 393
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.GroupRowHeight = 0
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsEditForm.PopupEditFormWidth = 933
        Me.GridView1.OptionsFind.AllowFindPanel = False
        Me.GridView1.OptionsFind.AllowMruItems = False
        Me.GridView1.OptionsView.BestFitMode = DevExpress.XtraGrid.Views.Grid.GridBestFitMode.Full
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView1.OptionsView.ShowIndicator = False
        Me.GridView1.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView1.RowHeight = 28
        '
        'GridColumn1
        '
        Me.GridColumn1.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn1.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn1.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn1.AppearanceCell.Options.UseFont = True
        Me.GridColumn1.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn1.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn1.AppearanceHeader.Options.UseFont = True
        Me.GridColumn1.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn1.Caption = "#"
        Me.GridColumn1.FieldName = "ID"
        Me.GridColumn1.MinWidth = 12
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.OptionsColumn.AllowEdit = False
        Me.GridColumn1.OptionsColumn.AllowFocus = False
        Me.GridColumn1.OptionsColumn.AllowMove = False
        Me.GridColumn1.OptionsColumn.AllowShowHide = False
        Me.GridColumn1.OptionsColumn.AllowSize = False
        Me.GridColumn1.OptionsColumn.FixedWidth = True
        Me.GridColumn1.OptionsColumn.ReadOnly = True
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        Me.GridColumn1.Width = 50
        '
        'GridColumn3
        '
        Me.GridColumn3.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn3.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn3.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.GridColumn3.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn3.AppearanceCell.Options.UseFont = True
        Me.GridColumn3.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn3.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn3.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn3.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn3.AppearanceHeader.Options.UseFont = True
        Me.GridColumn3.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn3.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn3.Caption = "Email Address"
        Me.GridColumn3.ColumnEdit = Me.RepositoryItemTextEdit1
        Me.GridColumn3.FieldName = "EmailFrom"
        Me.GridColumn3.MinWidth = 12
        Me.GridColumn3.Name = "GridColumn3"
        Me.GridColumn3.OptionsColumn.AllowEdit = False
        Me.GridColumn3.OptionsColumn.AllowFocus = False
        Me.GridColumn3.OptionsColumn.AllowMove = False
        Me.GridColumn3.OptionsColumn.AllowShowHide = False
        Me.GridColumn3.OptionsColumn.AllowSize = False
        Me.GridColumn3.OptionsColumn.FixedWidth = True
        Me.GridColumn3.OptionsColumn.ReadOnly = True
        Me.GridColumn3.Visible = True
        Me.GridColumn3.VisibleIndex = 1
        Me.GridColumn3.Width = 300
        '
        'RepositoryItemTextEdit1
        '
        Me.RepositoryItemTextEdit1.AutoHeight = False
        Me.RepositoryItemTextEdit1.Name = "RepositoryItemTextEdit1"
        Me.RepositoryItemTextEdit1.Padding = New System.Windows.Forms.Padding(8, 0, 8, 0)
        '
        'GridColumn7
        '
        Me.GridColumn7.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn7.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn7.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn7.AppearanceCell.Options.UseFont = True
        Me.GridColumn7.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn7.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn7.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn7.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn7.AppearanceHeader.Options.UseFont = True
        Me.GridColumn7.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn7.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn7.Caption = "Delete"
        Me.GridColumn7.ColumnEdit = Me.Bnt_Remove
        Me.GridColumn7.MinWidth = 24
        Me.GridColumn7.Name = "GridColumn7"
        Me.GridColumn7.OptionsColumn.AllowMove = False
        Me.GridColumn7.OptionsColumn.AllowShowHide = False
        Me.GridColumn7.OptionsColumn.AllowSize = False
        Me.GridColumn7.OptionsColumn.FixedWidth = True
        Me.GridColumn7.OptionsColumn.ReadOnly = True
        Me.GridColumn7.Visible = True
        Me.GridColumn7.VisibleIndex = 2
        Me.GridColumn7.Width = 50
        '
        'Bnt_Remove
        '
        Me.Bnt_Remove.AutoHeight = False
        EditorButtonImageOptions1.Image = CType(resources.GetObject("EditorButtonImageOptions1.Image"), System.Drawing.Image)
        Me.Bnt_Remove.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, False, EditorButtonImageOptions1, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject1, SerializableAppearanceObject2, SerializableAppearanceObject3, SerializableAppearanceObject4, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.Bnt_Remove.ButtonsStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.Bnt_Remove.Name = "Bnt_Remove"
        Me.Bnt_Remove.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'XtraTabPage2
        '
        Me.XtraTabPage2.Controls.Add(Me.Panel2)
        Me.XtraTabPage2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage2.Name = "XtraTabPage2"
        Me.XtraTabPage2.Size = New System.Drawing.Size(1100, 412)
        Me.XtraTabPage2.Text = "   Send from Mail List   "
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.SimpleButton5)
        Me.Panel2.Controls.Add(Me.SimpleButton6)
        Me.Panel2.Controls.Add(Me.GridControl2)
        Me.Panel2.Controls.Add(Me.SimpleButton7)
        Me.Panel2.Controls.Add(Me.txtmailto)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel2.Location = New System.Drawing.Point(0, 0)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1100, 412)
        Me.Panel2.TabIndex = 0
        '
        'SimpleButton5
        '
        Me.SimpleButton5.AllowFocus = False
        Me.SimpleButton5.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton5.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton5.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton5.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton5.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton5.Appearance.Options.UseBackColor = True
        Me.SimpleButton5.Appearance.Options.UseBorderColor = True
        Me.SimpleButton5.Appearance.Options.UseFont = True
        Me.SimpleButton5.Appearance.Options.UseForeColor = True
        Me.SimpleButton5.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton5.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton5.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton5.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton5.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton5.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton5.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton5.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton5.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton5.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton5.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton5.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton5.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton5.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton5.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton5.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton5.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton5.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton5.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton5.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton5.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.addheader_16x16
        Me.SimpleButton5.Location = New System.Drawing.Point(939, 343)
        Me.SimpleButton5.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SimpleButton5.Name = "SimpleButton5"
        Me.SimpleButton5.Size = New System.Drawing.Size(134, 47)
        Me.SimpleButton5.TabIndex = 321
        Me.SimpleButton5.Text = "&Add List"
        '
        'SimpleButton6
        '
        Me.SimpleButton6.AllowFocus = False
        Me.SimpleButton6.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton6.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton6.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton6.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.SimpleButton6.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton6.Appearance.Options.UseBackColor = True
        Me.SimpleButton6.Appearance.Options.UseBorderColor = True
        Me.SimpleButton6.Appearance.Options.UseFont = True
        Me.SimpleButton6.Appearance.Options.UseForeColor = True
        Me.SimpleButton6.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton6.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton6.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.SimpleButton6.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton6.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton6.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton6.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton6.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton6.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton6.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton6.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.SimpleButton6.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton6.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton6.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton6.AppearanceHovered.Options.UseFont = True
        Me.SimpleButton6.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton6.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton6.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton6.AppearancePressed.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.SimpleButton6.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton6.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton6.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton6.AppearancePressed.Options.UseFont = True
        Me.SimpleButton6.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton6.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_16x163
        Me.SimpleButton6.Location = New System.Drawing.Point(799, 343)
        Me.SimpleButton6.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SimpleButton6.Name = "SimpleButton6"
        Me.SimpleButton6.Size = New System.Drawing.Size(134, 47)
        Me.SimpleButton6.TabIndex = 320
        Me.SimpleButton6.Text = "&Clear List"
        '
        'GridControl2
        '
        Me.GridControl2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl2.EmbeddedNavigator.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GridControl2.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!)
        Me.GridControl2.Location = New System.Drawing.Point(24, 78)
        Me.GridControl2.MainView = Me.GridView2
        Me.GridControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GridControl2.Name = "GridControl2"
        Me.GridControl2.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.BntDelete})
        Me.GridControl2.Size = New System.Drawing.Size(1051, 253)
        Me.GridControl2.TabIndex = 319
        Me.GridControl2.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView2})
        '
        'GridView2
        '
        Me.GridView2.Appearance.Empty.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.Empty.Options.UseBackColor = True
        Me.GridView2.Appearance.FooterPanel.Options.UseTextOptions = True
        Me.GridView2.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridView2.Appearance.FooterPanel.TextOptions.HotkeyPrefix = DevExpress.Utils.HKeyPrefix.Hide
        Me.GridView2.Appearance.FooterPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None
        Me.GridView2.Appearance.FooterPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridView2.Appearance.GroupFooter.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridView2.Appearance.GroupFooter.Options.UseFont = True
        Me.GridView2.Appearance.Row.BackColor2 = System.Drawing.Color.WhiteSmoke
        Me.GridView2.ColumnPanelRowHeight = 39
        Me.GridView2.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn2, Me.GridColumn4, Me.GridColumn6, Me.GridColumn5})
        Me.GridView2.DetailHeight = 393
        Me.GridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        Me.GridView2.GridControl = Me.GridControl2
        Me.GridView2.GroupRowHeight = 0
        Me.GridView2.Name = "GridView2"
        Me.GridView2.OptionsEditForm.PopupEditFormWidth = 933
        Me.GridView2.OptionsFind.AllowFindPanel = False
        Me.GridView2.OptionsFind.AllowMruItems = False
        Me.GridView2.OptionsView.BestFitMode = DevExpress.XtraGrid.Views.Grid.GridBestFitMode.Full
        Me.GridView2.OptionsView.ShowGroupPanel = False
        Me.GridView2.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView2.OptionsView.ShowIndicator = False
        Me.GridView2.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView2.RowHeight = 28
        '
        'GridColumn2
        '
        Me.GridColumn2.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn2.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn2.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn2.AppearanceCell.Options.UseFont = True
        Me.GridColumn2.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn2.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn2.AppearanceHeader.Options.UseFont = True
        Me.GridColumn2.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn2.Caption = "#"
        Me.GridColumn2.FieldName = "ID"
        Me.GridColumn2.MinWidth = 12
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.OptionsColumn.AllowEdit = False
        Me.GridColumn2.OptionsColumn.AllowFocus = False
        Me.GridColumn2.OptionsColumn.AllowMove = False
        Me.GridColumn2.OptionsColumn.AllowShowHide = False
        Me.GridColumn2.OptionsColumn.AllowSize = False
        Me.GridColumn2.OptionsColumn.FixedWidth = True
        Me.GridColumn2.OptionsColumn.ReadOnly = True
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 0
        Me.GridColumn2.Width = 50
        '
        'GridColumn4
        '
        Me.GridColumn4.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn4.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn4.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.GridColumn4.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn4.AppearanceCell.Options.UseFont = True
        Me.GridColumn4.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn4.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn4.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn4.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn4.AppearanceHeader.Options.UseFont = True
        Me.GridColumn4.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn4.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn4.Caption = "Email Address"
        Me.GridColumn4.FieldName = "EmailTo"
        Me.GridColumn4.MinWidth = 12
        Me.GridColumn4.Name = "GridColumn4"
        Me.GridColumn4.OptionsColumn.AllowEdit = False
        Me.GridColumn4.OptionsColumn.AllowFocus = False
        Me.GridColumn4.OptionsColumn.AllowMove = False
        Me.GridColumn4.OptionsColumn.AllowShowHide = False
        Me.GridColumn4.OptionsColumn.AllowSize = False
        Me.GridColumn4.OptionsColumn.FixedWidth = True
        Me.GridColumn4.OptionsColumn.ReadOnly = True
        Me.GridColumn4.Visible = True
        Me.GridColumn4.VisibleIndex = 1
        Me.GridColumn4.Width = 300
        '
        'GridColumn6
        '
        Me.GridColumn6.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn6.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn6.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn6.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn6.AppearanceCell.Options.UseFont = True
        Me.GridColumn6.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn6.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn6.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn6.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn6.AppearanceHeader.Options.UseFont = True
        Me.GridColumn6.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn6.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn6.Caption = "Status"
        Me.GridColumn6.FieldName = "status"
        Me.GridColumn6.MinWidth = 12
        Me.GridColumn6.Name = "GridColumn6"
        Me.GridColumn6.OptionsColumn.AllowEdit = False
        Me.GridColumn6.OptionsColumn.AllowFocus = False
        Me.GridColumn6.OptionsColumn.AllowMove = False
        Me.GridColumn6.OptionsColumn.AllowShowHide = False
        Me.GridColumn6.OptionsColumn.AllowSize = False
        Me.GridColumn6.OptionsColumn.FixedWidth = True
        Me.GridColumn6.OptionsColumn.ReadOnly = True
        Me.GridColumn6.Visible = True
        Me.GridColumn6.VisibleIndex = 2
        Me.GridColumn6.Width = 94
        '
        'GridColumn5
        '
        Me.GridColumn5.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn5.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn5.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(128, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.GridColumn5.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn5.AppearanceCell.Options.UseFont = True
        Me.GridColumn5.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn5.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn5.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn5.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn5.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn5.AppearanceHeader.Options.UseFont = True
        Me.GridColumn5.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn5.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn5.Caption = "Delete"
        Me.GridColumn5.ColumnEdit = Me.BntDelete
        Me.GridColumn5.MinWidth = 24
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.OptionsColumn.AllowMove = False
        Me.GridColumn5.OptionsColumn.AllowShowHide = False
        Me.GridColumn5.OptionsColumn.AllowSize = False
        Me.GridColumn5.OptionsColumn.FixedWidth = True
        Me.GridColumn5.OptionsColumn.ReadOnly = True
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 3
        Me.GridColumn5.Width = 50
        '
        'BntDelete
        '
        Me.BntDelete.AutoHeight = False
        Me.BntDelete.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        EditorButtonImageOptions2.Image = CType(resources.GetObject("EditorButtonImageOptions2.Image"), System.Drawing.Image)
        Me.BntDelete.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, False, EditorButtonImageOptions2, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject5, SerializableAppearanceObject6, SerializableAppearanceObject7, SerializableAppearanceObject8, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.BntDelete.Name = "BntDelete"
        Me.BntDelete.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'SimpleButton7
        '
        Me.SimpleButton7.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton7.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton7.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton7.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton7.Appearance.Options.UseBackColor = True
        Me.SimpleButton7.Appearance.Options.UseBorderColor = True
        Me.SimpleButton7.Appearance.Options.UseFont = True
        Me.SimpleButton7.Appearance.Options.UseForeColor = True
        Me.SimpleButton7.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton7.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton7.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton7.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton7.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton7.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton7.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton7.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton7.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton7.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton7.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton7.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton7.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton7.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton7.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton7.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton7.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton7.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton7.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton7.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton7.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.SimpleButton7.Location = New System.Drawing.Point(328, 24)
        Me.SimpleButton7.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SimpleButton7.Name = "SimpleButton7"
        Me.SimpleButton7.Size = New System.Drawing.Size(131, 42)
        Me.SimpleButton7.TabIndex = 318
        Me.SimpleButton7.Text = "Add Email"
        '
        'txtmailto
        '
        Me.txtmailto.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.txtmailto.EditValue = ""
        Me.txtmailto.Location = New System.Drawing.Point(24, 24)
        Me.txtmailto.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtmailto.Name = "txtmailto"
        Me.txtmailto.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtmailto.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtmailto.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.txtmailto.Properties.Appearance.Options.UseBackColor = True
        Me.txtmailto.Properties.Appearance.Options.UseFont = True
        Me.txtmailto.Properties.Appearance.Options.UseForeColor = True
        Me.txtmailto.Properties.NullValuePrompt = "Enter a vaild email address..."
        Me.txtmailto.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtmailto.Size = New System.Drawing.Size(296, 30)
        Me.txtmailto.TabIndex = 317
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.GroupControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(1108, 359)
        Me.LayoutControlItem1.TextVisible = False
        '
        'GroupControl1
        '
        Me.GroupControl1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControl1.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.GroupControl1.AppearanceCaption.Options.UseFont = True
        Me.GroupControl1.Controls.Add(Me.pnlSmtp)
        Me.GroupControl1.Controls.Add(Me.ResultLabel)
        Me.GroupControl1.Controls.Add(Me.CheckEdit1)
        Me.GroupControl1.Controls.Add(Me.BntCheckSmtp)
        Me.GroupControl1.Controls.Add(Me.BntNew)
        Me.GroupControl1.Controls.Add(Me.lblattachfile)
        Me.GroupControl1.Controls.Add(Me.LinkLabel1)
        Me.GroupControl1.Controls.Add(Me.txtSubject)
        Me.GroupControl1.Controls.Add(Me.txtFromName)
        Me.GroupControl1.Controls.Add(Me.lbl5)
        Me.GroupControl1.Controls.Add(Me.txtpassword)
        Me.GroupControl1.Controls.Add(Me.lbl4)
        Me.GroupControl1.Controls.Add(Me.txtEmail)
        Me.GroupControl1.Controls.Add(Me.lbl3)
        Me.GroupControl1.Controls.Add(Me.CheckSSL)
        Me.GroupControl1.Controls.Add(Me.txtPort)
        Me.GroupControl1.Controls.Add(Me.lbl2)
        Me.GroupControl1.Controls.Add(Me.txtHost)
        Me.GroupControl1.Controls.Add(Me.lbl1)
        Me.GroupControl1.Location = New System.Drawing.Point(16, 16)
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(1102, 353)
        Me.GroupControl1.TabIndex = 84
        Me.GroupControl1.Text = " Smtp Account Information"
        '
        'pnlSmtp
        '
        Me.pnlSmtp.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlSmtp.Controls.Add(Me.SimpleButton8)
        Me.pnlSmtp.Controls.Add(Me.ProgressPanel1)
        Me.pnlSmtp.Location = New System.Drawing.Point(625, 197)
        Me.pnlSmtp.Margin = New System.Windows.Forms.Padding(4)
        Me.pnlSmtp.Name = "pnlSmtp"
        Me.pnlSmtp.Size = New System.Drawing.Size(448, 140)
        Me.pnlSmtp.TabIndex = 402
        Me.pnlSmtp.Visible = False
        '
        'SimpleButton8
        '
        Me.SimpleButton8.AllowFocus = False
        Me.SimpleButton8.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.1!, System.Drawing.FontStyle.Bold)
        Me.SimpleButton8.Appearance.Options.UseFont = True
        Me.SimpleButton8.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!)
        Me.SimpleButton8.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.SimpleButton8.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton8.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton8.AppearanceHovered.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!)
        Me.SimpleButton8.AppearanceHovered.Options.UseFont = True
        Me.SimpleButton8.Location = New System.Drawing.Point(173, 79)
        Me.SimpleButton8.LookAndFeel.SkinName = "McSkin"
        Me.SimpleButton8.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton8.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SimpleButton8.Name = "SimpleButton8"
        Me.SimpleButton8.Size = New System.Drawing.Size(115, 46)
        Me.SimpleButton8.TabIndex = 394
        Me.SimpleButton8.Text = "&Cancel"
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.AnimationToTextDistance = 10
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.Appearance.Options.UseFont = True
        Me.ProgressPanel1.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel1.AppearanceCaption.Options.UseFont = True
        Me.ProgressPanel1.AppearanceDescription.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel1.AppearanceDescription.Options.UseFont = True
        Me.ProgressPanel1.BarAnimationElementThickness = 5
        Me.ProgressPanel1.Caption = "Please Wait..."
        Me.ProgressPanel1.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
        Me.ProgressPanel1.Description = "verifiying smtp account"
        Me.ProgressPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.ProgressPanel1.Location = New System.Drawing.Point(0, 0)
        Me.ProgressPanel1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(448, 96)
        Me.ProgressPanel1.TabIndex = 393
        Me.ProgressPanel1.Text = "ProgressPanel2"
        '
        'ResultLabel
        '
        Me.ResultLabel.BackColor = System.Drawing.Color.Transparent
        Me.ResultLabel.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.25!, System.Drawing.FontStyle.Bold)
        Me.ResultLabel.Location = New System.Drawing.Point(340, 0)
        Me.ResultLabel.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.ResultLabel.Name = "ResultLabel"
        Me.ResultLabel.Size = New System.Drawing.Size(100, 25)
        Me.ResultLabel.TabIndex = 404
        Me.ResultLabel.Text = "VERIFIED"
        Me.ResultLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'CheckEdit1
        '
        Me.CheckEdit1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CheckEdit1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.CheckEdit1.Location = New System.Drawing.Point(563, 237)
        Me.CheckEdit1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.CheckEdit1.Name = "CheckEdit1"
        Me.CheckEdit1.Properties.AllowFocused = False
        Me.CheckEdit1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.CheckEdit1.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.CheckEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.CheckEdit1.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEdit1.Properties.AutoHeight = False
        Me.CheckEdit1.Properties.Caption = "View password"
        Me.CheckEdit1.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style15
        Me.CheckEdit1.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.CheckEdit1.Size = New System.Drawing.Size(18, 26)
        Me.CheckEdit1.TabIndex = 403
        '
        'BntCheckSmtp
        '
        Me.BntCheckSmtp.AllowFocus = False
        Me.BntCheckSmtp.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntCheckSmtp.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCheckSmtp.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntCheckSmtp.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCheckSmtp.Appearance.Options.UseBackColor = True
        Me.BntCheckSmtp.Appearance.Options.UseBorderColor = True
        Me.BntCheckSmtp.Appearance.Options.UseFont = True
        Me.BntCheckSmtp.Appearance.Options.UseForeColor = True
        Me.BntCheckSmtp.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntCheckSmtp.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntCheckSmtp.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.BntCheckSmtp.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntCheckSmtp.AppearanceDisabled.Options.UseBackColor = True
        Me.BntCheckSmtp.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntCheckSmtp.AppearanceDisabled.Options.UseFont = True
        Me.BntCheckSmtp.AppearanceDisabled.Options.UseForeColor = True
        Me.BntCheckSmtp.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntCheckSmtp.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntCheckSmtp.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.BntCheckSmtp.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntCheckSmtp.AppearanceHovered.Options.UseBackColor = True
        Me.BntCheckSmtp.AppearanceHovered.Options.UseBorderColor = True
        Me.BntCheckSmtp.AppearanceHovered.Options.UseFont = True
        Me.BntCheckSmtp.AppearanceHovered.Options.UseForeColor = True
        Me.BntCheckSmtp.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCheckSmtp.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntCheckSmtp.AppearancePressed.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.BntCheckSmtp.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntCheckSmtp.AppearancePressed.Options.UseBackColor = True
        Me.BntCheckSmtp.AppearancePressed.Options.UseBorderColor = True
        Me.BntCheckSmtp.AppearancePressed.Options.UseFont = True
        Me.BntCheckSmtp.AppearancePressed.Options.UseForeColor = True
        Me.BntCheckSmtp.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.BntCheckSmtp.Location = New System.Drawing.Point(842, 263)
        Me.BntCheckSmtp.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntCheckSmtp.Name = "BntCheckSmtp"
        Me.BntCheckSmtp.Size = New System.Drawing.Size(142, 50)
        Me.BntCheckSmtp.TabIndex = 401
        Me.BntCheckSmtp.Text = "Check &Smtp"
        '
        'BntNew
        '
        Me.BntNew.AllowFocus = False
        Me.BntNew.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntNew.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntNew.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.Appearance.Options.UseBackColor = True
        Me.BntNew.Appearance.Options.UseBorderColor = True
        Me.BntNew.Appearance.Options.UseFont = True
        Me.BntNew.Appearance.Options.UseForeColor = True
        Me.BntNew.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntNew.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntNew.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.BntNew.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntNew.AppearanceDisabled.Options.UseBackColor = True
        Me.BntNew.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntNew.AppearanceDisabled.Options.UseFont = True
        Me.BntNew.AppearanceDisabled.Options.UseForeColor = True
        Me.BntNew.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntNew.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntNew.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.BntNew.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntNew.AppearanceHovered.Options.UseBackColor = True
        Me.BntNew.AppearanceHovered.Options.UseBorderColor = True
        Me.BntNew.AppearanceHovered.Options.UseFont = True
        Me.BntNew.AppearanceHovered.Options.UseForeColor = True
        Me.BntNew.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntNew.AppearancePressed.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.BntNew.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntNew.AppearancePressed.Options.UseBackColor = True
        Me.BntNew.AppearancePressed.Options.UseBorderColor = True
        Me.BntNew.AppearancePressed.Options.UseFont = True
        Me.BntNew.AppearancePressed.Options.UseForeColor = True
        Me.BntNew.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntNew.Location = New System.Drawing.Point(692, 263)
        Me.BntNew.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntNew.Name = "BntNew"
        Me.BntNew.Size = New System.Drawing.Size(142, 50)
        Me.BntNew.TabIndex = 400
        Me.BntNew.Text = "&Reset Smtp"
        '
        'lblattachfile
        '
        Me.lblattachfile.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblattachfile.AutoSize = True
        Me.lblattachfile.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblattachfile.ForeColor = System.Drawing.Color.FromArgb(CType(CType(30, Byte), Integer), CType(CType(30, Byte), Integer), CType(CType(30, Byte), Integer))
        Me.lblattachfile.Location = New System.Drawing.Point(803, 204)
        Me.lblattachfile.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblattachfile.Name = "lblattachfile"
        Me.lblattachfile.Size = New System.Drawing.Size(96, 13)
        Me.lblattachfile.TabIndex = 399
        Me.lblattachfile.Text = "no file attached"
        '
        'LinkLabel1
        '
        Me.LinkLabel1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LinkLabel1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.LinkLabel1.Appearance.Options.UseFont = True
        Me.LinkLabel1.Location = New System.Drawing.Point(626, 204)
        Me.LinkLabel1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LinkLabel1.Name = "LinkLabel1"
        Me.LinkLabel1.Size = New System.Drawing.Size(146, 13)
        Me.LinkLabel1.TabIndex = 398
        Me.LinkLabel1.Text = "Click here to attach file ..."
        '
        'txtSubject
        '
        Me.txtSubject.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSubject.EditValue = ""
        Me.txtSubject.Location = New System.Drawing.Point(626, 60)
        Me.txtSubject.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtSubject.Name = "txtSubject"
        Me.txtSubject.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtSubject.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtSubject.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.txtSubject.Properties.Appearance.Options.UseBackColor = True
        Me.txtSubject.Properties.Appearance.Options.UseFont = True
        Me.txtSubject.Properties.Appearance.Options.UseForeColor = True
        Me.txtSubject.Properties.NullValuePrompt = "Enter a vaild email subject..."
        Me.txtSubject.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtSubject.Properties.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtSubject.Size = New System.Drawing.Size(458, 134)
        Me.txtSubject.TabIndex = 396
        '
        'txtFromName
        '
        Me.txtFromName.EditValue = ""
        Me.txtFromName.Location = New System.Drawing.Point(178, 280)
        Me.txtFromName.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtFromName.Name = "txtFromName"
        Me.txtFromName.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtFromName.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtFromName.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.txtFromName.Properties.Appearance.Options.UseBackColor = True
        Me.txtFromName.Properties.Appearance.Options.UseFont = True
        Me.txtFromName.Properties.Appearance.Options.UseForeColor = True
        Me.txtFromName.Properties.NullValuePrompt = "Enter a vaild from name..."
        Me.txtFromName.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtFromName.Size = New System.Drawing.Size(406, 30)
        Me.txtFromName.TabIndex = 394
        '
        'lbl5
        '
        Me.lbl5.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lbl5.AutoSize = True
        Me.lbl5.BackColor = System.Drawing.Color.Transparent
        Me.lbl5.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.25!, System.Drawing.FontStyle.Bold)
        Me.lbl5.ForeColor = System.Drawing.Color.White
        Me.lbl5.Location = New System.Drawing.Point(38, 287)
        Me.lbl5.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbl5.Name = "lbl5"
        Me.lbl5.Size = New System.Drawing.Size(99, 16)
        Me.lbl5.TabIndex = 395
        Me.lbl5.Text = "From Name : "
        '
        'txtpassword
        '
        Me.txtpassword.EditValue = ""
        Me.txtpassword.Location = New System.Drawing.Point(178, 235)
        Me.txtpassword.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtpassword.Name = "txtpassword"
        Me.txtpassword.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtpassword.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtpassword.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.txtpassword.Properties.Appearance.Options.UseBackColor = True
        Me.txtpassword.Properties.Appearance.Options.UseFont = True
        Me.txtpassword.Properties.Appearance.Options.UseForeColor = True
        Me.txtpassword.Properties.NullValuePrompt = "Enter a vaild email password..."
        Me.txtpassword.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtpassword.Size = New System.Drawing.Size(406, 30)
        Me.txtpassword.TabIndex = 391
        '
        'lbl4
        '
        Me.lbl4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lbl4.AutoSize = True
        Me.lbl4.BackColor = System.Drawing.Color.Transparent
        Me.lbl4.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.25!, System.Drawing.FontStyle.Bold)
        Me.lbl4.ForeColor = System.Drawing.Color.White
        Me.lbl4.Location = New System.Drawing.Point(38, 245)
        Me.lbl4.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbl4.Name = "lbl4"
        Me.lbl4.Size = New System.Drawing.Size(87, 16)
        Me.lbl4.TabIndex = 392
        Me.lbl4.Text = "Password : "
        '
        'txtEmail
        '
        Me.txtEmail.EditValue = ""
        Me.txtEmail.Location = New System.Drawing.Point(178, 194)
        Me.txtEmail.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtEmail.Name = "txtEmail"
        Me.txtEmail.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtEmail.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtEmail.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.txtEmail.Properties.Appearance.Options.UseBackColor = True
        Me.txtEmail.Properties.Appearance.Options.UseFont = True
        Me.txtEmail.Properties.Appearance.Options.UseForeColor = True
        Me.txtEmail.Properties.NullValuePrompt = "Enter a vaild email address..."
        Me.txtEmail.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtEmail.Size = New System.Drawing.Size(406, 30)
        Me.txtEmail.TabIndex = 316
        '
        'lbl3
        '
        Me.lbl3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lbl3.AutoSize = True
        Me.lbl3.BackColor = System.Drawing.Color.Transparent
        Me.lbl3.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.25!, System.Drawing.FontStyle.Bold)
        Me.lbl3.ForeColor = System.Drawing.Color.White
        Me.lbl3.Location = New System.Drawing.Point(38, 201)
        Me.lbl3.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbl3.Name = "lbl3"
        Me.lbl3.Size = New System.Drawing.Size(120, 16)
        Me.lbl3.TabIndex = 317
        Me.lbl3.Text = "Email Address : "
        '
        'CheckSSL
        '
        Me.CheckSSL.Location = New System.Drawing.Point(357, 151)
        Me.CheckSSL.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.CheckSSL.Name = "CheckSSL"
        Me.CheckSSL.Properties.AllowFocused = False
        Me.CheckSSL.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.CheckSSL.Properties.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.CheckSSL.Properties.Appearance.Options.UseFont = True
        Me.CheckSSL.Properties.Appearance.Options.UseForeColor = True
        Me.CheckSSL.Properties.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.CheckSSL.Properties.AppearanceDisabled.Options.UseForeColor = True
        Me.CheckSSL.Properties.AppearanceFocused.ForeColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.CheckSSL.Properties.AppearanceFocused.Options.UseForeColor = True
        Me.CheckSSL.Properties.AppearanceReadOnly.ForeColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.CheckSSL.Properties.AppearanceReadOnly.Options.UseForeColor = True
        Me.CheckSSL.Properties.Caption = " Enable SSL Connection"
        Me.CheckSSL.Size = New System.Drawing.Size(224, 25)
        Me.CheckSSL.TabIndex = 315
        '
        'txtPort
        '
        Me.txtPort.EditValue = ""
        Me.txtPort.Location = New System.Drawing.Point(178, 100)
        Me.txtPort.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtPort.Name = "txtPort"
        Me.txtPort.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtPort.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtPort.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.txtPort.Properties.Appearance.Options.UseBackColor = True
        Me.txtPort.Properties.Appearance.Options.UseFont = True
        Me.txtPort.Properties.Appearance.Options.UseForeColor = True
        Me.txtPort.Properties.NullValuePrompt = "Enter a vaild port number..."
        Me.txtPort.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtPort.Size = New System.Drawing.Size(406, 30)
        Me.txtPort.TabIndex = 71
        '
        'lbl2
        '
        Me.lbl2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lbl2.AutoSize = True
        Me.lbl2.BackColor = System.Drawing.Color.Transparent
        Me.lbl2.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.25!, System.Drawing.FontStyle.Bold)
        Me.lbl2.ForeColor = System.Drawing.Color.White
        Me.lbl2.Location = New System.Drawing.Point(38, 107)
        Me.lbl2.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbl2.Name = "lbl2"
        Me.lbl2.Size = New System.Drawing.Size(105, 16)
        Me.lbl2.TabIndex = 72
        Me.lbl2.Text = "Port Number : "
        '
        'txtHost
        '
        Me.txtHost.EditValue = ""
        Me.txtHost.Location = New System.Drawing.Point(178, 60)
        Me.txtHost.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtHost.Name = "txtHost"
        Me.txtHost.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtHost.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtHost.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.txtHost.Properties.Appearance.Options.UseBackColor = True
        Me.txtHost.Properties.Appearance.Options.UseFont = True
        Me.txtHost.Properties.Appearance.Options.UseForeColor = True
        Me.txtHost.Properties.NullValuePrompt = "Enter a vaild host name..."
        Me.txtHost.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtHost.Size = New System.Drawing.Size(406, 30)
        Me.txtHost.TabIndex = 63
        '
        'lbl1
        '
        Me.lbl1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lbl1.AutoSize = True
        Me.lbl1.BackColor = System.Drawing.Color.Transparent
        Me.lbl1.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.25!, System.Drawing.FontStyle.Bold)
        Me.lbl1.ForeColor = System.Drawing.Color.White
        Me.lbl1.Location = New System.Drawing.Point(38, 66)
        Me.lbl1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbl1.Name = "lbl1"
        Me.lbl1.Size = New System.Drawing.Size(96, 16)
        Me.lbl1.TabIndex = 64
        Me.lbl1.Text = "Host Name : "
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.GroupControl1)
        Me.LayoutControl1.Controls.Add(Me.XtraTabControl1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(1134, 834)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'trmfadein
        '
        Me.trmfadein.Enabled = True
        Me.trmfadein.Interval = 1
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'DxErrorProvider1
        '
        Me.DxErrorProvider1.ContainerControl = Me
        '
        'Timer2
        '
        Me.Timer2.Interval = 1000
        '
        'BackgroundWorker2
        '
        Me.BackgroundWorker2.WorkerReportsProgress = True
        Me.BackgroundWorker2.WorkerSupportsCancellation = True
        '
        'Timer1
        '
        Me.Timer1.Enabled = True
        Me.Timer1.Interval = 5000
        '
        'trmNotify
        '
        Me.trmNotify.Interval = 1000
        '
        'EmailScanner
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1134, 834)
        Me.Controls.Add(Me.LayoutControl1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Icon = CType(resources.GetObject("EmailScanner.IconOptions.Icon"), System.Drawing.Icon)
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Name = "EmailScanner"
        Me.Opacity = 0.99R
        Me.Text = "  Email Scanner  "
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.XtraTabPage1.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        CType(Me.txtmailfrom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Bnt_Remove, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage2.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BntDelete, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtmailto.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        Me.pnlSmtp.ResumeLayout(False)
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtSubject.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtFromName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtpassword.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtEmail.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckSSL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtPort.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHost.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents XtraTabPage1 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents XtraTabPage2 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents pnlSmtp As Panel
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents BntCheckSmtp As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntNew As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lblattachfile As Label
    Friend WithEvents LinkLabel1 As DevExpress.XtraEditors.HyperlinkLabelControl
    Friend WithEvents txtFromName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lbl5 As Label
    Friend WithEvents txtpassword As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lbl4 As Label
    Friend WithEvents txtEmail As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lbl3 As Label
    Friend WithEvents CheckSSL As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents txtPort As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lbl2 As Label
    Friend WithEvents txtHost As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lbl1 As Label
    Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents txtSubject As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents trmfadein As Timer
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents DxErrorProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider
    Friend WithEvents Timer2 As Timer
    Friend WithEvents BackgroundWorker2 As System.ComponentModel.BackgroundWorker
    Public WithEvents Timer1 As Timer
    Public WithEvents trmNotify As Timer
    Friend WithEvents ToolTipController1 As DevExpress.Utils.ToolTipController
    Friend WithEvents ResultLabel As Label
    Friend WithEvents Panel2 As Panel
    Friend WithEvents SimpleButton5 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton6 As DevExpress.XtraEditors.SimpleButton
    Public WithEvents GridControl2 As DevExpress.XtraGrid.GridControl
    Public WithEvents GridView2 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn4 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn6 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn5 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents BntDelete As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents SimpleButton7 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtmailto As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Panel1 As Panel
    Friend WithEvents SimpleButton4 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtmailfrom As DevExpress.XtraEditors.TextEdit
    Public WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Public WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemTextEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents GridColumn7 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Bnt_Remove As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents SimpleButton8 As DevExpress.XtraEditors.SimpleButton
End Class
