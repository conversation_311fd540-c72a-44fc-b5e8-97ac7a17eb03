﻿Public Class frmNew
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Process.Start("https://www.bestsendervip.com/video-gallery")
    End Sub

    Private Sub Label6_Click(sender As Object, e As EventArgs)
        Process.Start("https://www.bestsendervip.com/shop")
    End Sub

    Private Sub Label10_Click(sender As Object, e As EventArgs)
        Process.Start("https://www.Bestsendervip.com")
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        Process.Start("https://www.bestsendervip.com/product/vip-gold-card/")
    End Sub

    Private Sub frmNew_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' إضافة النصوص إلى الـ Labels
        SetupLabels()

        ' إضافة معالج حدث لتغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmNew_Resize

        ' توسيط العناصر كمجموعة واحدة
        CenterControlsAsGroup()
    End Sub

    Private Sub frmNew_Resize(sender As Object, e As EventArgs)
        ' توسيط العناصر كمجموعة واحدة عند تغيير حجم النموذج
        CenterControlsAsGroup()
    End Sub

    Private Sub SetupLabels()
        ' إضافة عنوان للنموذج
        Me.Text = "What's New in Best Sender VIP"

        ' إضافة النصوص إلى الـ Labels
        Label1.Text = "1- Fixed All Known bugs"
        Label2.Text = "2- Best Sender VIP is designed and updated"
        Label3.Text = "3- Update All Tools"
        Label4.Text = "4- Best Sender VIP Now fully compatible with all SMTP types for maximum reliability and performance"
        Label5.Text = "5- Checks if your IP is blacklisted or not"
        Label6.Text = "6- Extracts words from Your Letter if it is classified as Spam"
        Label7.Text = "7- All types of Encoded Letter have been improved and modified"
        Label8.Text = "8- Sender auto-rotates on SMTP"
        Label9.Text = "9- PHP Encryption has been improved"
        Label10.Text = "10- Email Sorter has been improved and works with all email types"
        Label11.Text = "11- Mail Catcher added"
        Label12.Text = "12- Office 365 Verification"

        ' تنسيق الـ Labels
        FormatLabels()
    End Sub

    Private Sub FormatLabels()
        ' قائمة بجميع الـ Labels
        Dim labels As Label() = {Label1, Label2, Label3, Label4, Label5, Label6, Label7, Label8, Label9, Label10, Label11, Label12}

        ' تنسيق جميع الـ Labels
        For Each lbl As Label In labels
            ' تعيين خصائص الـ Label
            lbl.AutoSize = True
            lbl.Font = New System.Drawing.Font("Comfortaa", 10.0!)

            ' تنسيق رقم الترتيب باللون الذهبي
            Dim index As Integer = lbl.Text.IndexOf("-")
            If index > 0 Then
                ' إنشاء نص جديد بدون الرقم والشرطة
                Dim newText As String = lbl.Text.Substring(index + 1).Trim()

                ' تعيين النص الجديد
                lbl.Text = lbl.Text.Substring(0, index + 1) + " " + newText

                ' تعيين لون النص الأبيض
                lbl.ForeColor = System.Drawing.Color.White

                ' تعديل موقع الـ Label ليكون بعد الرقم والشرطة
                If lbl.Name <> "Label1" Then
                    Dim prevLabel As Label = labels(Array.IndexOf(labels, lbl) - 1)
                    lbl.Location = New System.Drawing.Point(50, prevLabel.Location.Y + 37)
                End If

                ' تعديل عرض الـ Label ليناسب النص
                lbl.Width = TextRenderer.MeasureText(lbl.Text, lbl.Font).Width + 100
            End If
        Next

        ' تنسيق أرقام الترتيب باللون الذهبي
        FormatNumbersWithGoldColor()
    End Sub

    Private Sub FormatNumbersWithGoldColor()
        ' قائمة بجميع الـ Labels
        Dim labels As Label() = {Label1, Label2, Label3, Label4, Label5, Label6, Label7, Label8, Label9, Label10, Label11, Label12}

        ' إنشاء الـ Labels الإضافية للأرقام
        For i As Integer = 0 To labels.Length - 1
            Dim lbl As Label = labels(i)

            ' إنشاء Label جديد للرقم
            Dim numberLabel As New Label()
            numberLabel.Name = "NumberLabel" & (i + 1)
            numberLabel.Text = (i + 1).ToString() & "-"
            numberLabel.AutoSize = True
            numberLabel.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Bold)
            numberLabel.ForeColor = System.Drawing.Color.FromArgb(254, 219, 65) ' اللون الذهبي
            numberLabel.Location = New System.Drawing.Point(21, lbl.Location.Y)
            numberLabel.BringToFront()

            ' إضافة الـ Label الجديد إلى النموذج
            Me.Controls.Add(numberLabel)

            ' تعديل نص الـ Label الأصلي ليكون بدون الرقم
            Dim index As Integer = lbl.Text.IndexOf("-")
            If index > 0 Then
                lbl.Text = lbl.Text.Substring(index + 1).Trim()
            End If
        Next
    End Sub

    ''' <summary>
    ''' توسيط العناصر كمجموعة واحدة
    ''' </summary>
    Private Sub CenterControlsAsGroup()
        Try
            ' التحقق من أن النموذج مهيأ بشكل كامل
            If Not Me.IsHandleCreated OrElse Me.IsDisposed Then
                Return
            End If

            ' قائمة بجميع الـ Labels
            Dim contentLabels As Label() = {Label1, Label2, Label3, Label4, Label5, Label6, Label7, Label8, Label9, Label10, Label11, Label12}

            ' قائمة بجميع الـ Labels الرقمية
            Dim numberLabels As New List(Of Label)()
            For i As Integer = 1 To 12
                Dim numberLabel As Label = Me.Controls.Find("NumberLabel" & i, True).FirstOrDefault()
                If numberLabel IsNot Nothing Then
                    numberLabels.Add(numberLabel)
                End If
            Next

            ' حساب أقصى عرض للنص في الـ Labels
            Dim maxTextWidth As Integer = 0
            For Each lbl As Label In contentLabels
                Dim textWidth As Integer = TextRenderer.MeasureText(lbl.Text, lbl.Font).Width
                If textWidth > maxTextWidth Then
                    maxTextWidth = textWidth
                End If
            Next

            ' حساب العرض الإجمالي للمجموعة
            Dim totalWidth As Integer = maxTextWidth + 50 ' إضافة هامش

            ' حساب الارتفاع الإجمالي للمجموعة
            Dim totalHeight As Integer = 0
            If contentLabels.Length > 0 AndAlso numberLabels.Count > 0 Then
                Dim lastContentLabel As Label = contentLabels(contentLabels.Length - 1)
                Dim lastNumberLabel As Label = numberLabels(numberLabels.Count - 1)
                totalHeight = Math.Max(lastContentLabel.Location.Y + lastContentLabel.Height,
                                      lastNumberLabel.Location.Y + lastNumberLabel.Height)
            End If

            ' حساب نقطة البداية لتوسيط المجموعة
            Dim startX As Integer = (Me.ClientSize.Width - totalWidth) \ 2
            Dim startY As Integer = (Me.ClientSize.Height - totalHeight) \ 2

            ' تعديل موقع الـ Labels
            For i As Integer = 0 To contentLabels.Length - 1
                Dim contentLabel As Label = contentLabels(i)

                ' تعديل موقع الـ Label
                contentLabel.Location = New System.Drawing.Point(startX + 50, startY + i * 37)

                ' تعديل موقع الـ Label الرقمي المقابل
                If i < numberLabels.Count Then
                    Dim numberLabel As Label = numberLabels(i)
                    numberLabel.Location = New System.Drawing.Point(startX, contentLabel.Location.Y)
                End If
            Next

            ' تعديل موقع الأزرار
            If SimpleButton1 IsNot Nothing Then
                SimpleButton1.Location = New System.Drawing.Point(startX, startY + totalHeight + 20)
            End If

            If SimpleButton3 IsNot Nothing Then
                SimpleButton3.Location = New System.Drawing.Point(Me.ClientSize.Width - SimpleButton3.Width - 20, Me.ClientSize.Height - SimpleButton3.Height - 20)
            End If

            ' تعديل موقع الصورة
            If PictureBox1 IsNot Nothing Then
                PictureBox1.Location = New System.Drawing.Point(Me.ClientSize.Width - PictureBox1.Width - 20, startY)
            End If
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in CenterControlsAsGroup: {ex.Message}")
        End Try
    End Sub
End Class