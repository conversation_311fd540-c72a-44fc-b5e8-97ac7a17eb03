﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Thread</name>
  </assembly>
  <members>
    <member name="T:System.Threading.ParameterizedThreadStart">
      <summary>表示在 <see cref="T:System.Threading.Thread" /> 上执行的方法。</summary>
      <param name="obj">包含该线程过程的数据的对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.Thread">
      <summary>创建和控制线程，设置其优先级并获取其状态。若要浏览此类型的 .NET Framework 源代码，请参阅引用源。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.#ctor(System.Threading.ParameterizedThreadStart)">
      <summary>初始化 <see cref="T:System.Threading.Thread" /> 类的新实例，指定允许对象在线程启动时传递给线程的委托。</summary>
      <param name="start">一个委托，它表示此线程开始执行时要调用的方法。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="start" /> is null. </exception>
    </member>
    <member name="M:System.Threading.Thread.#ctor(System.Threading.ThreadStart)">
      <summary>初始化 <see cref="T:System.Threading.Thread" /> 类的新实例。</summary>
      <param name="start">表示开始执行此线程时要调用的方法的 <see cref="T:System.Threading.ThreadStart" /> 委托。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="start" /> parameter is null. </exception>
    </member>
    <member name="P:System.Threading.Thread.CurrentThread">
      <summary>获取当前正在运行的线程。</summary>
      <returns>
        <see cref="T:System.Threading.Thread" />，表示当前正在运行的线程。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.IsAlive">
      <summary>获取指示当前线程的执行状态的值。</summary>
      <returns>如果此线程已经开始但尚未正常终止或中止，则为 true，否则为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.IsBackground">
      <summary>获取或设置一个值，该值指示某个线程是否为后台线程。</summary>
      <returns>如果此线程为或将成为后台线程，则为 true；否则为 false。</returns>
      <exception cref="T:System.Threading.ThreadStateException">The thread is dead. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Join">
      <summary>阻止调用线程直到线程终止，同时继续执行标准的 COM 和 SendMessage 传送。</summary>
      <exception cref="T:System.Threading.ThreadStateException">The caller attempted to join a thread that is in the <see cref="F:System.Threading.ThreadState.Unstarted" /> state. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">The thread is interrupted while waiting. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Join(System.Int32)">
      <summary>在继续执行标准的 COM 和 SendMessage 消息泵处理期间，阻止调用线程，直到某个线程终止或经过了指定时间为止。</summary>
      <returns>如果线程已终止，则为 true；如果 false 参数指定的时间量已过之后还未终止线程，则为 <paramref name="millisecondsTimeout" />。</returns>
      <param name="millisecondsTimeout">等待线程终止的毫秒数。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> in milliseconds. </exception>
      <exception cref="T:System.Threading.ThreadStateException">The thread has not been started. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.ManagedThreadId">
      <summary>获取当前托管线程的唯一标识符。</summary>
      <returns>一个整数，表示此托管线程的唯一标识符。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.Name">
      <summary>获取或设置线程的名称。</summary>
      <returns>包含线程名称的字符串或 null（如果未设置名称）。</returns>
      <exception cref="T:System.InvalidOperationException">A set operation was requested, but the Name property has already been set. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Sleep(System.Int32)">
      <summary>将当前线程挂起指定的毫秒数。</summary>
      <param name="millisecondsTimeout">挂起线程的毫秒数。如果 <paramref name="millisecondsTimeout" /> 参数的值为零，则该线程会将其时间片的剩余部分让给任何已经准备好运行的、具有同等优先级的线程。如果没有其他已经准备好运行的、具有同等优先级的线程，则不会挂起当前线程的执行。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The time-out value is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Sleep(System.TimeSpan)">
      <summary>将当前线程挂起指定的时间。</summary>
      <param name="timeout">挂起线程的时间量。如果 <paramref name="millisecondsTimeout" /> 参数的值为 <see cref="F:System.TimeSpan.Zero" />，则该线程会将其时间片的剩余部分让给任何已经准备好运行的、具有同等优先级的线程。如果没有其他已经准备好运行的、具有同等优先级的线程，则不会挂起当前线程的执行。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> in milliseconds, or is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Start">
      <summary>导致操作系统将当前实例的状态更改为 <see cref="F:System.Threading.ThreadState.Running" />。</summary>
      <exception cref="T:System.Threading.ThreadStateException">The thread has already been started. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available to start this thread. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Start(System.Object)">
      <summary>导致操作系统将当前实例的状态更改为 <see cref="F:System.Threading.ThreadState.Running" />，并选择提供包含线程执行的方法要使用的数据的对象。</summary>
      <param name="parameter">一个对象，包含线程执行的方法要使用的数据。</param>
      <exception cref="T:System.Threading.ThreadStateException">The thread has already been started. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available to start this thread. </exception>
      <exception cref="T:System.InvalidOperationException">This thread was created using a <see cref="T:System.Threading.ThreadStart" /> delegate instead of a <see cref="T:System.Threading.ParameterizedThreadStart" /> delegate.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.ThreadState">
      <summary>获取一个值，该值包含当前线程的状态。</summary>
      <returns>其中一个表示当前线程的状态的 <see cref="T:System.Threading.ThreadState" /> 值。初始值为 Unstarted。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.ThreadStart">
      <summary>表示在 <see cref="T:System.Threading.Thread" /> 上执行的方法。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.ThreadStartException">
      <summary>当基础操作系统线程已启动但该线程尚未准备好执行用户代码前，托管线程中出现错误，则会引发异常。</summary>
    </member>
    <member name="T:System.Threading.ThreadState">
      <summary>指定 <see cref="T:System.Threading.Thread" /> 的执行状态。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Threading.ThreadState.Aborted">
      <summary>线程状态包括 <see cref="F:System.Threading.ThreadState.AbortRequested" /> 并且该线程现在已死，但其状态尚未更改为 <see cref="F:System.Threading.ThreadState.Stopped" />。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.AbortRequested">
      <summary>已对线程调用了 <see cref="M:System.Threading.Thread.Abort(System.Object)" /> 方法，但线程尚未收到试图终止它的挂起的 <see cref="T:System.Threading.ThreadAbortException" />。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Background">
      <summary>线程正作为后台线程执行（相对于前台线程而言）。此状态可以通过设置 <see cref="P:System.Threading.Thread.IsBackground" /> 属性来控制。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Running">
      <summary>线程已启动，它未被阻塞，并且没有挂起的 <see cref="T:System.Threading.ThreadAbortException" />。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Stopped">
      <summary>线程已停止。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.StopRequested">
      <summary>正在请求线程停止。这仅用于内部。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Suspended">
      <summary>线程已挂起。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.SuspendRequested">
      <summary>正在请求线程挂起。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Unstarted">
      <summary>尚未对线程调用 <see cref="M:System.Threading.Thread.Start" /> 方法。</summary>
    </member>
    <member name="F:System.Threading.ThreadState.WaitSleepJoin">
      <summary>线程已被阻止。这可能是因为：调用 <see cref="M:System.Threading.Thread.Sleep(System.Int32)" /> 或 <see cref="M:System.Threading.Thread.Join" />、请求锁定（例如通过调用 <see cref="M:System.Threading.Monitor.Enter(System.Object)" /> 或 <see cref="M:System.Threading.Monitor.Wait(System.Object,System.Int32,System.Boolean)" />）或等待线程同步对象（例如 <see cref="T:System.Threading.ManualResetEvent" />）。</summary>
    </member>
    <member name="T:System.Threading.ThreadStateException">
      <summary>当 <see cref="T:System.Threading.Thread" /> 处于对方法调用无效的 <see cref="P:System.Threading.Thread.ThreadState" /> 时引发的异常。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor">
      <summary>使用默认属性初始化 <see cref="T:System.Threading.ThreadStateException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor(System.String)">
      <summary>使用指定的错误消息初始化 <see cref="T:System.Threading.ThreadStateException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor(System.String,System.Exception)">
      <summary>使用指定错误信息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.Threading.ThreadStateException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="innerException">导致当前异常的异常。如果 <paramref name="innerException" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
  </members>
</doc>