﻿Imports System.Text
Imports System.Runtime.InteropServices
Imports System.IO
Imports System.Text.RegularExpressions
Public Class frmAddAttachment
    Dim tt As New ToolTip()
    Function ReturnShortString(ByVal str As String,
                                       ByVal maxLength As Integer) _
                                       As String
        If str.Length <= maxLength Then
            Return str
        Else
            Dim leftEndPosition As Integer = CInt((maxLength / 2) - 2.5)
            Dim rightStartPosition As Integer = str.Length - (CInt((maxLength / 2) - 2.5))
            Dim sb As New System.Text.StringBuilder
            sb.Append(str.Substring(0, leftEndPosition) & " ... " & str.Substring(rightStartPosition))
            Return sb.ToString
        End If
    End Function
    Private Sub Form3_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        CenterForm(Me, EmailScanner)
        If EmailScanner.LinkLabel1.Tag = "" Then
            Label2.Text = "No file chosen"
        Else
            Label2.Text = EmailScanner.LinkLabel1.Tag
        End If
    End Sub
    Function CompactString(ByVal MyString As String, ByVal Width As Integer,
    ByVal Font As Drawing.Font,
    ByVal FormatFlags As Windows.Forms.TextFormatFlags) As String
        Dim Result As String = String.Copy(MyString)
        TextRenderer.MeasureText(Result, Font, New Drawing.Size(Width, 0),
    FormatFlags Or TextFormatFlags.ModifyString)
        Return Result
    End Function
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Dim OFD As New OpenFileDialog
        OFD.FileName = ""
        OFD.Filter = "All Graphics Types|*.bmp;*.jpg;*.png;*.tif;*.tiff|Html files|*.html|MS Word Files|*.doc;*.docx|MS Excel Files|*.xls;*.xlsx|PDF Document|*.pdf|All Files|*.*"
        If OFD.ShowDialog = DialogResult.OK Then
            Dim shortPath As String = ReturnShortString(OFD.FileName, 35)
            Label2.Text = shortPath
            AttachmentFilePath = OFD.FileName
        Else
            Label2.Text = "No file chosen"
            AttachmentFilePath = ""
        End If
    End Sub
    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        If Label2.Text = "No file chosen" Then
            EmailScanner.LinkLabel1.Tag = ""
        Else
            Dim myFile As New FileInfo(AttachmentFilePath)
            Dim sizeInBytes As Long = myFile.Length
            If Val(sizeInBytes) > 25000000 Then
                Dim msg = "Attachment size exceeds the allowable limit (25 MB)."
                DevExpress.XtraEditors.XtraMessageBox.Show(msg, "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If
            EmailScanner.LinkLabel1.Tag = Label2.Text
        End If
        Me.Dispose()
    End Sub
    Private Sub Label2_MouseEnter(sender As Object, e As EventArgs) Handles Label2.MouseEnter
        tt.SetToolTip(Label2, "")
        If AttachmentFilePath <> "" Then tt.SetToolTip(Label2, AttachmentFilePath)
    End Sub
End Class