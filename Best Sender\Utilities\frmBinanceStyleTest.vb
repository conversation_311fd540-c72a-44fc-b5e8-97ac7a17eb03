Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid
Imports System.Data
Imports DevExpress.XtraBars.Ribbon

Public Class frmBinanceStyleTest
    Inherits XtraForm

    ' إضافة خاصية RelatedRibbonPage للتوافق مع بقية النماذج
    Public Property RelatedRibbonPage As RibbonPage
    Private Sub frmBinanceStyleTest_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' إنشاء بيانات اختبار
        CreateTestData()

        ' تطبيق تصميم Binance على GridControl
        BinanceGridStyle.ApplyBinanceStyle(GridControl1, True)
    End Sub

    Private Sub CreateTestData()
        ' إنشاء جدول بيانات للاختبار
        Dim dt As New DataTable()

        ' إضافة الأعمدة
        dt.Columns.Add("ID", GetType(Integer))
        dt.Columns.Add("Name", GetType(String))
        dt.Columns.Add("Email", GetType(String))
        dt.Columns.Add("Status", GetType(String))
        dt.Columns.Add("Balance", GetType(Decimal))
        dt.Columns.Add("Change", GetType(Decimal))

        ' إضافة بيانات اختبار
        dt.Rows.Add(1, "John Doe", "<EMAIL>", "Active", 1500.50, 120.75)
        dt.Rows.Add(2, "Jane Smith", "<EMAIL>", "Inactive", 750.25, -45.50)
        dt.Rows.Add(3, "Robert Johnson", "<EMAIL>", "Pending", 2200.00, 0)
        dt.Rows.Add(4, "Emily Davis", "<EMAIL>", "Active", 3100.75, 250.25)
        dt.Rows.Add(5, "Michael Brown", "<EMAIL>", "Failed", 500.00, -120.00)
        dt.Rows.Add(6, "Sarah Wilson", "<EMAIL>", "Active", 1800.50, 75.25)
        dt.Rows.Add(7, "David Taylor", "<EMAIL>", "Inactive", 950.25, -25.50)
        dt.Rows.Add(8, "Jennifer Martinez", "<EMAIL>", "Pending", 2500.00, 0)
        dt.Rows.Add(9, "James Anderson", "<EMAIL>", "Active", 3300.75, 300.25)
        dt.Rows.Add(10, "Lisa Thomas", "<EMAIL>", "Failed", 600.00, -90.00)

        ' تعيين مصدر البيانات للـ GridControl
        GridControl1.DataSource = dt

        ' تكوين GridView
        Dim gridView As GridView = TryCast(GridControl1.MainView, GridView)
        If gridView IsNot Nothing Then
            ' تعيين عناوين الأعمدة
            gridView.Columns("ID").Caption = "#"
            gridView.Columns("Name").Caption = "الاسم"
            gridView.Columns("Email").Caption = "البريد الإلكتروني"
            gridView.Columns("Status").Caption = "الحالة"
            gridView.Columns("Balance").Caption = "الرصيد"
            gridView.Columns("Change").Caption = "التغيير"

            ' تعيين تنسيق الأعمدة الرقمية
            gridView.Columns("Balance").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            gridView.Columns("Balance").DisplayFormat.FormatString = "n2"
            gridView.Columns("Change").DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
            gridView.Columns("Change").DisplayFormat.FormatString = "n2"

            ' تعيين عرض الأعمدة
            gridView.Columns("ID").Width = 50
            gridView.Columns("Name").Width = 150
            gridView.Columns("Email").Width = 200
            gridView.Columns("Status").Width = 100
            gridView.Columns("Balance").Width = 120
            gridView.Columns("Change").Width = 120

            ' تمكين خاصية BestFit
            gridView.BestFitColumns()
        End If
    End Sub
End Class
