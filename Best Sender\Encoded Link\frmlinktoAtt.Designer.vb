﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmlinktoAtt
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmlinktoAtt))
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.TextBox8 = New DevExpress.XtraEditors.TextEdit()
        Me.TextBox4 = New DevExpress.XtraEditors.TextEdit()
        Me.TextBox3 = New DevExpress.XtraEditors.TextEdit()
        Me.ComboBox_SleepTime = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.bntPaste = New DevExpress.XtraEditors.SimpleButton()
        Me.BntCopy = New DevExpress.XtraEditors.SimpleButton()
        Me.bntAddLogo = New DevExpress.XtraEditors.SimpleButton()
        Me.BntReset = New DevExpress.XtraEditors.SimpleButton()
        Me.BNT_Backup = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonSaveHTML = New DevExpress.XtraEditors.SimpleButton()
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.TextBox1 = New DevExpress.XtraEditors.TextEdit()
        Me.TextBox2 = New System.Windows.Forms.TextBox()
        Me.TextBox7 = New System.Windows.Forms.TextBox()
        Me.Button4 = New System.Windows.Forms.Button()
        Me.Button2 = New System.Windows.Forms.Button()
        Me.SeparatorControl1 = New DevExpress.XtraEditors.SeparatorControl()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox8.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBox_SleepTime.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.PictureBox2)
        Me.GroupControl1.Controls.Add(Me.Label14)
        Me.GroupControl1.Controls.Add(Me.Label13)
        Me.GroupControl1.Controls.Add(Me.Label12)
        Me.GroupControl1.Controls.Add(Me.Label11)
        Me.GroupControl1.Controls.Add(Me.TextBox8)
        Me.GroupControl1.Controls.Add(Me.TextBox4)
        Me.GroupControl1.Controls.Add(Me.TextBox3)
        Me.GroupControl1.Controls.Add(Me.ComboBox_SleepTime)
        Me.GroupControl1.Controls.Add(Me.bntPaste)
        Me.GroupControl1.Controls.Add(Me.BntCopy)
        Me.GroupControl1.Controls.Add(Me.bntAddLogo)
        Me.GroupControl1.Controls.Add(Me.BntReset)
        Me.GroupControl1.Controls.Add(Me.BNT_Backup)
        Me.GroupControl1.Controls.Add(Me.ButtonSaveHTML)
        Me.GroupControl1.Controls.Add(Me.RichTextBox1)
        Me.GroupControl1.Controls.Add(Me.Label4)
        Me.GroupControl1.Controls.Add(Me.TextBox1)
        Me.GroupControl1.Location = New System.Drawing.Point(13, 127)
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(1167, 471)
        Me.GroupControl1.TabIndex = 518
        Me.GroupControl1.Text = "Settings Convert Link to Attachment"
        '
        'PictureBox2
        '
        Me.PictureBox2.BackColor = System.Drawing.Color.Transparent
        Me.PictureBox2.Location = New System.Drawing.Point(893, 78)
        Me.PictureBox2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(239, 112)
        Me.PictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox2.TabIndex = 531
        Me.PictureBox2.TabStop = False
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label14.Location = New System.Drawing.Point(893, 394)
        Me.Label14.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(102, 21)
        Me.Label14.TabIndex = 527
        Me.Label14.Text = "Time Redirect"
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label13.Location = New System.Drawing.Point(893, 327)
        Me.Label13.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(88, 21)
        Me.Label13.TabIndex = 528
        Me.Label13.Text = "Width Logo"
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label12.Location = New System.Drawing.Point(893, 260)
        Me.Label12.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(93, 21)
        Me.Label12.TabIndex = 529
        Me.Label12.Text = "Length logo"
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label11.Location = New System.Drawing.Point(893, 193)
        Me.Label11.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(102, 21)
        Me.Label11.TabIndex = 530
        Me.Label11.Text = "Time Redirect"
        '
        'TextBox8
        '
        Me.TextBox8.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBox8.EditValue = "[-Email64-]"
        Me.TextBox8.Location = New System.Drawing.Point(893, 423)
        Me.TextBox8.Margin = New System.Windows.Forms.Padding(4)
        Me.TextBox8.Name = "TextBox8"
        Me.TextBox8.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBox8.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox8.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBox8.Properties.Appearance.Options.UseBackColor = True
        Me.TextBox8.Properties.Appearance.Options.UseFont = True
        Me.TextBox8.Properties.Appearance.Options.UseForeColor = True
        Me.TextBox8.Properties.ContextImageOptions.Image = CType(resources.GetObject("TextBox8.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.TextBox8.Properties.NullValuePrompt = "[-Email64-]"
        Me.TextBox8.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBox8.Size = New System.Drawing.Size(239, 30)
        Me.TextBox8.TabIndex = 523
        '
        'TextBox4
        '
        Me.TextBox4.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBox4.EditValue = ""
        Me.TextBox4.Location = New System.Drawing.Point(893, 356)
        Me.TextBox4.Margin = New System.Windows.Forms.Padding(4)
        Me.TextBox4.Name = "TextBox4"
        Me.TextBox4.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBox4.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox4.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBox4.Properties.Appearance.Options.UseBackColor = True
        Me.TextBox4.Properties.Appearance.Options.UseFont = True
        Me.TextBox4.Properties.Appearance.Options.UseForeColor = True
        Me.TextBox4.Properties.ContextImageOptions.Image = CType(resources.GetObject("TextBox4.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.TextBox4.Properties.NullValuePrompt = "Width Logo"
        Me.TextBox4.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBox4.Size = New System.Drawing.Size(239, 30)
        Me.TextBox4.TabIndex = 524
        '
        'TextBox3
        '
        Me.TextBox3.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBox3.EditValue = ""
        Me.TextBox3.Location = New System.Drawing.Point(893, 289)
        Me.TextBox3.Margin = New System.Windows.Forms.Padding(4)
        Me.TextBox3.Name = "TextBox3"
        Me.TextBox3.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBox3.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox3.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBox3.Properties.Appearance.Options.UseBackColor = True
        Me.TextBox3.Properties.Appearance.Options.UseFont = True
        Me.TextBox3.Properties.Appearance.Options.UseForeColor = True
        Me.TextBox3.Properties.ContextImageOptions.Image = CType(resources.GetObject("TextBox3.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.TextBox3.Properties.NullValuePrompt = "Length logo"
        Me.TextBox3.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBox3.Size = New System.Drawing.Size(239, 30)
        Me.TextBox3.TabIndex = 525
        '
        'ComboBox_SleepTime
        '
        Me.ComboBox_SleepTime.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboBox_SleepTime.EditValue = "5000"
        Me.ComboBox_SleepTime.Location = New System.Drawing.Point(893, 222)
        Me.ComboBox_SleepTime.Margin = New System.Windows.Forms.Padding(4)
        Me.ComboBox_SleepTime.Name = "ComboBox_SleepTime"
        Me.ComboBox_SleepTime.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.ComboBox_SleepTime.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ComboBox_SleepTime.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.ComboBox_SleepTime.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBox_SleepTime.Properties.Appearance.Options.UseFont = True
        Me.ComboBox_SleepTime.Properties.Appearance.Options.UseForeColor = True
        Me.ComboBox_SleepTime.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBox_SleepTime.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.time16x16
        Me.ComboBox_SleepTime.Properties.Items.AddRange(New Object() {"1000", "2000", "3000", "4000", "5000", "6000", "7000", "8000", "9000"})
        Me.ComboBox_SleepTime.Properties.NullValuePrompt = "Select Time (1000 = 1 second)"
        Me.ComboBox_SleepTime.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.ComboBox_SleepTime.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ComboBox_SleepTime.Size = New System.Drawing.Size(239, 30)
        Me.ComboBox_SleepTime.TabIndex = 526
        '
        'bntPaste
        '
        Me.bntPaste.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntPaste.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntPaste.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bntPaste.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntPaste.Appearance.Options.UseBackColor = True
        Me.bntPaste.Appearance.Options.UseBorderColor = True
        Me.bntPaste.Appearance.Options.UseFont = True
        Me.bntPaste.Appearance.Options.UseForeColor = True
        Me.bntPaste.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntPaste.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntPaste.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bntPaste.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntPaste.AppearanceDisabled.Options.UseBackColor = True
        Me.bntPaste.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntPaste.AppearanceDisabled.Options.UseFont = True
        Me.bntPaste.AppearanceDisabled.Options.UseForeColor = True
        Me.bntPaste.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntPaste.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntPaste.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntPaste.AppearanceHovered.Options.UseBackColor = True
        Me.bntPaste.AppearanceHovered.Options.UseBorderColor = True
        Me.bntPaste.AppearanceHovered.Options.UseForeColor = True
        Me.bntPaste.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntPaste.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntPaste.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntPaste.AppearancePressed.Options.UseBackColor = True
        Me.bntPaste.AppearancePressed.Options.UseBorderColor = True
        Me.bntPaste.AppearancePressed.Options.UseForeColor = True
        Me.bntPaste.Cursor = System.Windows.Forms.Cursors.Hand
        Me.bntPaste.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.paste_16x16
        Me.bntPaste.Location = New System.Drawing.Point(747, 71)
        Me.bntPaste.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.bntPaste.Name = "bntPaste"
        Me.bntPaste.Size = New System.Drawing.Size(122, 31)
        Me.bntPaste.TabIndex = 522
        Me.bntPaste.Text = "Paste"
        '
        'BntCopy
        '
        Me.BntCopy.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCopy.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCopy.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntCopy.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCopy.Appearance.Options.UseBackColor = True
        Me.BntCopy.Appearance.Options.UseBorderColor = True
        Me.BntCopy.Appearance.Options.UseFont = True
        Me.BntCopy.Appearance.Options.UseForeColor = True
        Me.BntCopy.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntCopy.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntCopy.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntCopy.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntCopy.AppearanceDisabled.Options.UseBackColor = True
        Me.BntCopy.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntCopy.AppearanceDisabled.Options.UseFont = True
        Me.BntCopy.AppearanceDisabled.Options.UseForeColor = True
        Me.BntCopy.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntCopy.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCopy.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntCopy.AppearanceHovered.Options.UseBackColor = True
        Me.BntCopy.AppearanceHovered.Options.UseBorderColor = True
        Me.BntCopy.AppearanceHovered.Options.UseForeColor = True
        Me.BntCopy.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCopy.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCopy.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntCopy.AppearancePressed.Options.UseBackColor = True
        Me.BntCopy.AppearancePressed.Options.UseBorderColor = True
        Me.BntCopy.AppearancePressed.Options.UseForeColor = True
        Me.BntCopy.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntCopy.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy32x32
        Me.BntCopy.Location = New System.Drawing.Point(529, 409)
        Me.BntCopy.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntCopy.Name = "BntCopy"
        Me.BntCopy.Size = New System.Drawing.Size(166, 44)
        Me.BntCopy.TabIndex = 521
        Me.BntCopy.Text = "Copy"
        '
        'bntAddLogo
        '
        Me.bntAddLogo.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntAddLogo.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntAddLogo.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bntAddLogo.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntAddLogo.Appearance.Options.UseBackColor = True
        Me.bntAddLogo.Appearance.Options.UseBorderColor = True
        Me.bntAddLogo.Appearance.Options.UseFont = True
        Me.bntAddLogo.Appearance.Options.UseForeColor = True
        Me.bntAddLogo.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntAddLogo.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntAddLogo.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bntAddLogo.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntAddLogo.AppearanceDisabled.Options.UseBackColor = True
        Me.bntAddLogo.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntAddLogo.AppearanceDisabled.Options.UseFont = True
        Me.bntAddLogo.AppearanceDisabled.Options.UseForeColor = True
        Me.bntAddLogo.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntAddLogo.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntAddLogo.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntAddLogo.AppearanceHovered.Options.UseBackColor = True
        Me.bntAddLogo.AppearanceHovered.Options.UseBorderColor = True
        Me.bntAddLogo.AppearanceHovered.Options.UseForeColor = True
        Me.bntAddLogo.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntAddLogo.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntAddLogo.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntAddLogo.AppearancePressed.Options.UseBackColor = True
        Me.bntAddLogo.AppearancePressed.Options.UseBorderColor = True
        Me.bntAddLogo.AppearancePressed.Options.UseForeColor = True
        Me.bntAddLogo.Cursor = System.Windows.Forms.Cursors.Hand
        Me.bntAddLogo.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.imageimport
        Me.bntAddLogo.Location = New System.Drawing.Point(355, 409)
        Me.bntAddLogo.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.bntAddLogo.Name = "bntAddLogo"
        Me.bntAddLogo.Size = New System.Drawing.Size(166, 44)
        Me.bntAddLogo.TabIndex = 520
        Me.bntAddLogo.Text = "Select Logo"
        '
        'BntReset
        '
        Me.BntReset.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntReset.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntReset.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntReset.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntReset.Appearance.Options.UseBackColor = True
        Me.BntReset.Appearance.Options.UseBorderColor = True
        Me.BntReset.Appearance.Options.UseFont = True
        Me.BntReset.Appearance.Options.UseForeColor = True
        Me.BntReset.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntReset.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntReset.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntReset.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntReset.AppearanceDisabled.Options.UseBackColor = True
        Me.BntReset.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntReset.AppearanceDisabled.Options.UseFont = True
        Me.BntReset.AppearanceDisabled.Options.UseForeColor = True
        Me.BntReset.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntReset.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntReset.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntReset.AppearanceHovered.Options.UseBackColor = True
        Me.BntReset.AppearanceHovered.Options.UseBorderColor = True
        Me.BntReset.AppearanceHovered.Options.UseForeColor = True
        Me.BntReset.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntReset.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntReset.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntReset.AppearancePressed.Options.UseBackColor = True
        Me.BntReset.AppearancePressed.Options.UseBorderColor = True
        Me.BntReset.AppearancePressed.Options.UseForeColor = True
        Me.BntReset.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntReset.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntReset.Location = New System.Drawing.Point(703, 409)
        Me.BntReset.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntReset.Name = "BntReset"
        Me.BntReset.Size = New System.Drawing.Size(166, 44)
        Me.BntReset.TabIndex = 519
        Me.BntReset.Text = "Reset All"
        '
        'BNT_Backup
        '
        Me.BNT_Backup.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BNT_Backup.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BNT_Backup.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BNT_Backup.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BNT_Backup.Appearance.Options.UseBackColor = True
        Me.BNT_Backup.Appearance.Options.UseBorderColor = True
        Me.BNT_Backup.Appearance.Options.UseFont = True
        Me.BNT_Backup.Appearance.Options.UseForeColor = True
        Me.BNT_Backup.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BNT_Backup.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BNT_Backup.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BNT_Backup.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BNT_Backup.AppearanceDisabled.Options.UseBackColor = True
        Me.BNT_Backup.AppearanceDisabled.Options.UseBorderColor = True
        Me.BNT_Backup.AppearanceDisabled.Options.UseFont = True
        Me.BNT_Backup.AppearanceDisabled.Options.UseForeColor = True
        Me.BNT_Backup.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BNT_Backup.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BNT_Backup.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BNT_Backup.AppearanceHovered.Options.UseBackColor = True
        Me.BNT_Backup.AppearanceHovered.Options.UseBorderColor = True
        Me.BNT_Backup.AppearanceHovered.Options.UseForeColor = True
        Me.BNT_Backup.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BNT_Backup.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BNT_Backup.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BNT_Backup.AppearancePressed.Options.UseBackColor = True
        Me.BNT_Backup.AppearancePressed.Options.UseBorderColor = True
        Me.BNT_Backup.AppearancePressed.Options.UseForeColor = True
        Me.BNT_Backup.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BNT_Backup.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.BNT_Backup.Location = New System.Drawing.Point(181, 409)
        Me.BNT_Backup.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BNT_Backup.Name = "BNT_Backup"
        Me.BNT_Backup.Size = New System.Drawing.Size(166, 44)
        Me.BNT_Backup.TabIndex = 518
        Me.BNT_Backup.Text = "Save"
        '
        'ButtonSaveHTML
        '
        Me.ButtonSaveHTML.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ButtonSaveHTML.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.ButtonSaveHTML.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ButtonSaveHTML.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.ButtonSaveHTML.Appearance.Options.UseBackColor = True
        Me.ButtonSaveHTML.Appearance.Options.UseBorderColor = True
        Me.ButtonSaveHTML.Appearance.Options.UseFont = True
        Me.ButtonSaveHTML.Appearance.Options.UseForeColor = True
        Me.ButtonSaveHTML.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.ButtonSaveHTML.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.ButtonSaveHTML.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.ButtonSaveHTML.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseBackColor = True
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseBorderColor = True
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseFont = True
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseForeColor = True
        Me.ButtonSaveHTML.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.ButtonSaveHTML.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ButtonSaveHTML.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.ButtonSaveHTML.AppearanceHovered.Options.UseBackColor = True
        Me.ButtonSaveHTML.AppearanceHovered.Options.UseBorderColor = True
        Me.ButtonSaveHTML.AppearanceHovered.Options.UseForeColor = True
        Me.ButtonSaveHTML.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.ButtonSaveHTML.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ButtonSaveHTML.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.ButtonSaveHTML.AppearancePressed.Options.UseBackColor = True
        Me.ButtonSaveHTML.AppearancePressed.Options.UseBorderColor = True
        Me.ButtonSaveHTML.AppearancePressed.Options.UseForeColor = True
        Me.ButtonSaveHTML.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ButtonSaveHTML.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.ButtonSaveHTML.Location = New System.Drawing.Point(7, 409)
        Me.ButtonSaveHTML.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ButtonSaveHTML.Name = "ButtonSaveHTML"
        Me.ButtonSaveHTML.Size = New System.Drawing.Size(166, 44)
        Me.ButtonSaveHTML.TabIndex = 517
        Me.ButtonSaveHTML.Text = "Start"
        '
        'RichTextBox1
        '
        Me.RichTextBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.RichTextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox1.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.RichTextBox1.ForeColor = System.Drawing.Color.White
        Me.RichTextBox1.Location = New System.Drawing.Point(6, 109)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(863, 294)
        Me.RichTextBox1.TabIndex = 516
        Me.RichTextBox1.Text = ""
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label4.Location = New System.Drawing.Point(14, 39)
        Me.Label4.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(114, 21)
        Me.Label4.TabIndex = 515
        Me.Label4.Text = "Enter Your Link"
        '
        'TextBox1
        '
        Me.TextBox1.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBox1.EditValue = ""
        Me.TextBox1.Location = New System.Drawing.Point(7, 71)
        Me.TextBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.TextBox1.Name = "TextBox1"
        Me.TextBox1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBox1.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox1.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBox1.Properties.Appearance.Options.UseBackColor = True
        Me.TextBox1.Properties.Appearance.Options.UseFont = True
        Me.TextBox1.Properties.Appearance.Options.UseForeColor = True
        Me.TextBox1.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.LinkDomain16x16
        Me.TextBox1.Properties.NullValuePrompt = "Enter Your Link"
        Me.TextBox1.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBox1.Size = New System.Drawing.Size(732, 30)
        Me.TextBox1.TabIndex = 514
        '
        'TextBox2
        '
        Me.TextBox2.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.TextBox2.Font = New System.Drawing.Font("Tahoma", 10.0!)
        Me.TextBox2.ForeColor = System.Drawing.Color.White
        Me.TextBox2.Location = New System.Drawing.Point(1226, -11)
        Me.TextBox2.Margin = New System.Windows.Forms.Padding(4)
        Me.TextBox2.Name = "TextBox2"
        Me.TextBox2.Size = New System.Drawing.Size(36, 24)
        Me.TextBox2.TabIndex = 521
        Me.TextBox2.Visible = False
        '
        'TextBox7
        '
        Me.TextBox7.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.TextBox7.Font = New System.Drawing.Font("Tahoma", 10.0!)
        Me.TextBox7.ForeColor = System.Drawing.Color.White
        Me.TextBox7.Location = New System.Drawing.Point(1184, -11)
        Me.TextBox7.Margin = New System.Windows.Forms.Padding(4)
        Me.TextBox7.Name = "TextBox7"
        Me.TextBox7.Size = New System.Drawing.Size(35, 24)
        Me.TextBox7.TabIndex = 522
        Me.TextBox7.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.TextBox7.Visible = False
        '
        'Button4
        '
        Me.Button4.Location = New System.Drawing.Point(1103, 68)
        Me.Button4.Margin = New System.Windows.Forms.Padding(4)
        Me.Button4.Name = "Button4"
        Me.Button4.Size = New System.Drawing.Size(77, 32)
        Me.Button4.TabIndex = 520
        Me.Button4.Text = "Button4"
        Me.Button4.UseVisualStyleBackColor = True
        Me.Button4.Visible = False
        '
        'Button2
        '
        Me.Button2.Location = New System.Drawing.Point(1214, 68)
        Me.Button2.Margin = New System.Windows.Forms.Padding(4)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(77, 32)
        Me.Button2.TabIndex = 519
        Me.Button2.Text = "Button2"
        Me.Button2.UseVisualStyleBackColor = True
        Me.Button2.Visible = False
        '
        'SeparatorControl1
        '
        Me.SeparatorControl1.LineThickness = 1
        Me.SeparatorControl1.Location = New System.Drawing.Point(367, 94)
        Me.SeparatorControl1.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl1.Name = "SeparatorControl1"
        Me.SeparatorControl1.Padding = New System.Windows.Forms.Padding(10, 9, 10, 9)
        Me.SeparatorControl1.Size = New System.Drawing.Size(420, 27)
        Me.SeparatorControl1.TabIndex = 594
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.LinktoAttachmentPaner
        Me.PictureBox1.Location = New System.Drawing.Point(259, 3)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(647, 117)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 595
        Me.PictureBox1.TabStop = False
        '
        'frmlinktoAtt
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1223, 611)
        Me.Controls.Add(Me.SeparatorControl1)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.TextBox2)
        Me.Controls.Add(Me.TextBox7)
        Me.Controls.Add(Me.Button4)
        Me.Controls.Add(Me.Button2)
        Me.Controls.Add(Me.GroupControl1)
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmlinktoAtt"
        Me.ShowInTaskbar = False
        Me.Text = "Convert Link To Attachment"
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox8.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBox_SleepTime.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Label4 As Label
    Friend WithEvents TextBox1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents BntReset As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BNT_Backup As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonSaveHTML As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntCopy As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents bntAddLogo As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents bntPaste As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextBox2 As TextBox
    Friend WithEvents TextBox7 As TextBox
    Friend WithEvents Button4 As Button
    Friend WithEvents Button2 As Button
    Friend WithEvents SeparatorControl1 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents Label14 As Label
    Friend WithEvents Label13 As Label
    Friend WithEvents Label12 As Label
    Friend WithEvents Label11 As Label
    Friend WithEvents TextBox8 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextBox4 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextBox3 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ComboBox_SleepTime As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents PictureBox2 As PictureBox
End Class
