﻿Imports System.IO
Imports System.Text.RegularExpressions
Imports MailKit.Net.Imap
Imports MailKit.Search
Imports MailKit
Imports MimeKit
Imports DevExpress.XtraEditors
Imports System.ComponentModel
Imports DevExpress.XtraGrid
Public Class IMAP_BSV
    Public OpenFileDialog As New OpenFileDialog With {.Filter = "Text Files (*.txt)|*.txt"}
    ''' <summary>
    ''' عند تحميل الفورم، تفعيل خاصية التقدم والإلغاء للـ BackgroundWorker
    ''' </summary>
    Private Sub IMAP_BSV_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        BackgroundWorker1.WorkerReportsProgress = True
        BackgroundWorker1.WorkerSupportsCancellation = True
        AddHandler BackgroundWorker1.DoWork, AddressOf BackgroundWorker1_DoWork
        AddHandler BackgroundWorker1.ProgressChanged, AddressOf BackgroundWorker1_ProgressChanged
        AddHandler BackgroundWorker1.RunWorkerCompleted, AddressOf BackgroundWorker1_RunWorkerCompleted
    End Sub
    ''' <summary>
    ''' زر رفع الملف النصي
    ''' </summary>
    Private Sub bntUploade_Click(sender As Object, e As EventArgs) Handles bntUploade.Click
        If OpenFileDialog1.ShowDialog() = DialogResult.OK Then
            ' تخزين مسار الملف في المتغير
            BackgroundWorker1.RunWorkerAsync(OpenFileDialog1.FileName)
        End If
    End Sub
    ''' <summary>
    ''' زر بدء العملية (Login and Fetch Emails)
    ''' </summary>
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        If Not BackgroundWorker1.IsBusy Then
            BackgroundWorker1.RunWorkerAsync("single_account")
        End If
    End Sub
    ''' <summary>
    ''' العمليات الخلفية: قراءة الملف أو معالجة حساب واحد
    ''' </summary>
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Dim worker = CType(sender, BackgroundWorker)
        ' --- حالة حساب واحد فقط ---
        If e.Argument.ToString() = "single_account" Then
            ProcessSingleAccount(worker, e)
            Return
        End If
        ' --- حالة معالجة ملف نصي ---
        Dim filePath = CType(e.Argument, String)
        Dim lines = File.ReadAllLines(filePath)
        Dim totalAccounts = lines.Length
        Dim validCount = 0
        For i As Integer = 0 To lines.Length - 1
            If worker.CancellationPending Then
                e.Cancel = True
                Return
            End If
            Dim line = lines(i).Trim()
            If String.IsNullOrEmpty(line) Then Continue For
            ' --- استخراج البريد وكلمة المرور ---
            Dim email = ExtractEmailFromLine(line)
            Dim password = ExtractPasswordFromLine(line)
            If String.IsNullOrEmpty(email) OrElse String.IsNullOrEmpty(password) Then
                worker.ReportProgress(CInt((i / totalAccounts) * 100), $"Skipped line {i + 1}: Invalid format")
                Continue For
            End If
            ' --- المنفذ والـ SSL تلقائيًا ---
            Dim port As Integer = 993
            Dim useSsl As Boolean = True
            Try
                Using client As New ImapClient()
                    client.Connect("imap." & GetDomainFromEmail(email), port, useSsl)
                    client.Authenticate(email, password)
                    ' --- جلب البيانات ---
                    Dim folderNames As String() = {"INBOX", "[Gmail]/Spam", "[Gmail]/Trash", "Spam", "Trash"}
                    Dim dataTable As New DataTable()
                    InitializeDataTable(dataTable)
                    Dim counter = 1
                    For Each folderName In folderNames
                        Dim folder = TryCast(client.GetFolder(folderName), IMailFolder)
                        If folder IsNot Nothing AndAlso folder.Exists Then
                            folder.Open(FolderAccess.ReadOnly)
                            Dim uids = folder.Search(SearchQuery.All)
                            For Each uid In uids
                                Dim message = folder.GetMessage(uid)
                                Dim fromEmails = ExtractEmails(message.From.ToString())
                                Dim toEmails = ExtractEmails(message.To.ToString())
                                Dim bodyEmails = ExtractEmails(message.TextBody & " " & message.HtmlBody)
                                Dim allEmails = fromEmails.Union(toEmails).Union(bodyEmails).ToList()
                                For Each eml In allEmails
                                    dataTable.Rows.Add(
                                        counter,
                                        eml,
                                        If(message.Date.UtcDateTime = DateTime.MinValue, DateTime.Now, message.Date.UtcDateTime),
                                        If(fromEmails.Contains(eml), eml, ""),
                                        If(toEmails.Contains(eml), eml, "")
                                    )
                                    counter += 1
                                Next
                            Next
                            folder.Close()
                        End If
                    Next
                    If dataTable.Rows.Count > 0 Then
                        SaveEmailDataToFile(email, dataTable)
                        validCount += 1
                    End If
                    client.Disconnect(True)
                End Using
            Catch ex As Exception
                ' يمكنك تسجيل الخطأ هنا إذا أردت
            End Try
            Dim progress = CInt((i / totalAccounts) * 100)
            worker.ReportProgress(progress, $"Processed account {i + 1}/{totalAccounts}")
        Next
        e.Result = $"Done. Valid accounts: {validCount}/{totalAccounts}"
    End Sub
    ''' <summary>
    ''' معالجة حساب واحد فقط
    ''' </summary>
    Private Sub ProcessSingleAccount(worker As BackgroundWorker, e As DoWorkEventArgs)
        Dim email = txtEmailImap.Text.Trim()
        Dim password = txtPasswordImap.Text.Trim()
        If String.IsNullOrEmpty(email) OrElse String.IsNullOrEmpty(password) Then
            XtraMessageBox.Show("Please enter your email and password.")
            e.Cancel = True
            Return
        End If
        Dim port = If(String.IsNullOrEmpty(txtPortImap.Text), 993, CInt(txtPortImap.Text))
        Dim useSsl = CheckEdit1.Checked
        Try
            Using client As New ImapClient()
                client.Connect("imap." & GetDomainFromEmail(email), port, useSsl)
                client.Authenticate(email, password)
                Dim folderNames As String() = {"INBOX", "[Gmail]/Spam", "[Gmail]/Trash", "Spam", "Trash"}
                Dim dataTable As New DataTable()
                InitializeDataTable(dataTable)
                Dim counter = 1
                For Each folderName In folderNames
                    Dim folder = TryCast(client.GetFolder(folderName), IMailFolder)
                    If folder IsNot Nothing AndAlso folder.Exists Then
                        folder.Open(FolderAccess.ReadOnly)
                        Dim uids = folder.Search(SearchQuery.All)
                        For Each uid In uids
                            Dim message = folder.GetMessage(uid)
                            Dim fromEmails = ExtractEmails(message.From.ToString())
                            Dim toEmails = ExtractEmails(message.To.ToString())
                            Dim bodyEmails = ExtractEmails(message.TextBody & " " & message.HtmlBody)
                            Dim allEmails = fromEmails.Union(toEmails).Union(bodyEmails).ToList()
                            For Each eml In allEmails
                                dataTable.Rows.Add(
                                    counter,
                                    eml,
                                    If(message.Date.UtcDateTime = DateTime.MinValue, DateTime.Now, message.Date.UtcDateTime),
                                    If(fromEmails.Contains(eml), eml, ""),
                                    If(toEmails.Contains(eml), eml, "")
                                )
                                counter += 1
                            Next
                        Next
                        folder.Close()
                    End If
                Next
                SaveEmailDataToFile(email, dataTable)
                e.Result = dataTable
                client.Disconnect(True)
            End Using
        Catch ex As Exception
            If ex.Message.ToLower.Contains("application-specific password") OrElse
               ex.Message.ToLower.Contains("two-factor") OrElse
               ex.Message.ToLower.Contains("verify it's you") Then
                worker.ReportProgress(-1, "Authentication failed: Use App Password for 2FA accounts.")
            Else
                worker.ReportProgress(-1, "Login failed: " & ex.Message)
            End If
        End Try
    End Sub
    ''' <summary>
    ''' تحديث شريط التقدم والرسائل
    ''' </summary>
    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        If e.ProgressPercentage >= 0 Then
            ProgressBarControl1.EditValue = e.ProgressPercentage
        End If
        If Not String.IsNullOrEmpty(e.UserState?.ToString()) Then
            Label1.Text = e.UserState.ToString()
        End If
    End Sub
    ''' <summary>
    ''' بعد انتهاء العملية
    ''' </summary>
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        If e.Error IsNot Nothing Then
            XtraMessageBox.Show("Error: " & e.Error.Message)
        ElseIf e.Cancelled Then
            XtraMessageBox.Show("Operation cancelled.")
        Else
            If TypeOf e.Result Is DataTable Then
                Dim dt = CType(e.Result, DataTable)
                GridControl1.DataSource = dt
                Label1.Text = $"Total Emails Extracted: {dt.Rows.Count}"
                ProgressBarControl1.EditValue = 100
            Else
                XtraMessageBox.Show(e.Result.ToString(), "اكتملت العملية")
                Dim exportPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "Valid_Accounts")
                If Directory.Exists(exportPath) Then
                    Process.Start(exportPath)
                Else
                    XtraMessageBox.Show("No data was exported.")
                End If
            End If
        End If
    End Sub
    ''' <summary>
    ''' استخراج البريد من سطر في الملف
    ''' </summary>
    Private Function ExtractEmailFromLine(line As String) As String
        line = line.Replace("Email:", "").Replace("username:", "").Replace(" ", "")
        If line.Contains("=") Then
            Return line.Split("="c)(0).Trim()
        ElseIf line.Contains(":") Then
            Return line.Split(":"c)(0).Trim()
        Else
            Return ""
        End If
    End Function
    ''' <summary>
    ''' استخراج كلمة المرور من سطر في الملف
    ''' </summary>
    Private Function ExtractPasswordFromLine(line As String) As String
        line = line.Replace("Pass:", "").Trim()
        If line.Contains("=") Then
            Return line.Split("="c)(1).Trim()
        ElseIf line.Contains(":") Then
            Return line.Split(":"c)(1).Trim()
        Else
            Return ""
        End If
    End Function
    ''' <summary>
    ''' استخراج اسم الدومين من البريد الإلكتروني
    ''' </summary>
    Private Function GetDomainFromEmail(email As String) As String
        If email.Contains("@") Then
            Return email.Split("@"c)(1)
        Else
            Throw New ArgumentException("Invalid Email Address")
        End If
    End Function
    ''' <summary>
    ''' استخراج عناوين البريد الإلكتروني باستخدام Regex
    ''' </summary>
    Private Function ExtractEmails(input As String) As List(Of String)
        Dim emails As New List(Of String)()
        If String.IsNullOrEmpty(input) Then Return emails
        Dim regexPattern = "\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        Dim matches = Regex.Matches(input, regexPattern, RegexOptions.IgnoreCase)
        For Each match As Match In matches
            emails.Add(match.Value.ToLower())
        Next
        Return emails.Distinct().ToList()
    End Function
    ''' <summary>
    ''' إنشاء هيكل الجدول
    ''' </summary>
    Private Sub InitializeDataTable(table As DataTable)
        table.Columns.Add("#", GetType(Integer))
        table.Columns.Add("Email Inbox", GetType(String))
        table.Columns.Add("Received Date", GetType(DateTime))
        table.Columns.Add("Email Spam", GetType(String))
        table.Columns.Add("Email Send", GetType(String))
    End Sub
    ''' <summary>
    ''' حفظ بيانات البريد إلى ملف TXT داخل مجلد باسم البريد
    ''' </summary>
    Private Sub SaveEmailDataToFile(email As String, dt As DataTable)
        Try
            Dim desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim safeEmail = Regex.Replace(email, "[\\/:*?""<>|]", "_")
            Dim exportPath = Path.Combine(desktopPath, "Valid_Accounts", safeEmail)
            Directory.CreateDirectory(exportPath)
            For i As Integer = 0 To dt.Rows.Count - 1
                Dim row = dt.Rows(i)
                Dim inbox = row("Email Inbox").ToString()
                Dim received = row("Received Date").ToString()
                Dim spam = row("Email Spam").ToString()
                Dim send = row("Email Send").ToString()
                If String.IsNullOrEmpty(inbox) Then Continue For
                Dim content = $"Inbox Email: {inbox}{Environment.NewLine}" &
                              $"Received Date: {received}{Environment.NewLine}" &
                              $"Sender (Spam): {spam}{Environment.NewLine}" &
                              $"Recipient (Send): {send}{Environment.NewLine}"
                Dim fileName = $"{inbox}_{i + 1}.txt"
                Dim invalidChars = Path.GetInvalidFileNameChars()
                fileName = String.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries)).TrimEnd(".")
                Dim filePath = Path.Combine(exportPath, fileName)
                File.WriteAllText(filePath, content)
            Next
        Catch ex As Exception
            ' يمكنك تسجيل الخطأ هنا إذا أردت
        End Try
    End Sub
    ''' <summary>
    ''' تعليمات لإنشاء Application Password حسب نوع الحساب
    ''' </summary>
    Private Sub ShowAppPasswordInstructions(email As String)
        If email.Contains("@gmail.com") Then
            XtraMessageBox.Show("This is a Gmail account with 2FA enabled." & vbCrLf &
                            "Please create an App Password from:" & vbCrLf &
                            "https://myaccount.google.com/apppasswords" & vbCrLf &
                            "Then paste the password here.")
        ElseIf email.Contains("@outlook.") OrElse email.Contains("@hotmail.") OrElse email.Contains("@live.") Then
            XtraMessageBox.Show("This is a Microsoft account with 2FA enabled." & vbCrLf &
                            "Please create an App Password from:" & vbCrLf &
                            "https://account.live.com/proofs/AppPassword" & vbCrLf &
                            "Then paste the password here.")
        Else
            XtraMessageBox.Show("Make sure your account supports external access (IMAP) and that your password is correct.")
        End If
    End Sub
End Class