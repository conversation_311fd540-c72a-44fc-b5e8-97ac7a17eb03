﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmEncodedAttachment
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.LogInButton2 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.LogInNormalTextBox1 = New System.Windows.Forms.TextBox()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.LogInButton3 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.LogInNormalTextBox2 = New System.Windows.Forms.TextBox()
        Me.bntClear = New DevExpress.XtraEditors.SimpleButton()
        Me.bntSave = New DevExpress.XtraEditors.SimpleButton()
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.ProgressBarControl1 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.txttitle = New System.Windows.Forms.TextBox()
        Me.LogInCheckBox1 = New System.Windows.Forms.CheckBox()
        Me.LogInNormalTextBox3 = New System.Windows.Forms.TextBox()
        Me.BehaviorManager1 = New DevExpress.Utils.Behaviors.BehaviorManager(Me.components)
        Me.GroupBox1.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BehaviorManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.LogInButton2)
        Me.GroupBox1.Controls.Add(Me.Label4)
        Me.GroupBox1.Controls.Add(Me.LogInNormalTextBox1)
        Me.GroupBox1.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.GroupBox1.Location = New System.Drawing.Point(28, 74)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(598, 103)
        Me.GroupBox1.TabIndex = 22
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "   Source Attachment file :   "
        '
        'LogInButton2
        '
        Me.LogInButton2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.LogInButton2.Appearance.Options.UseBackColor = True
        Me.LogInButton2.Location = New System.Drawing.Point(522, 51)
        Me.LogInButton2.LookAndFeel.SkinName = "Dark Side"
        Me.LogInButton2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.LogInButton2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LogInButton2.Name = "LogInButton2"
        Me.LogInButton2.Size = New System.Drawing.Size(61, 20)
        Me.LogInButton2.TabIndex = 10
        Me.LogInButton2.Text = "Browse"
        '
        'Label4
        '
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(203, Byte), Integer), CType(CType(121, Byte), Integer))
        Me.Label4.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label4.Location = New System.Drawing.Point(38, 26)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(257, 22)
        Me.Label4.TabIndex = 9
        Me.Label4.Text = "Please Select |html|htm"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'LogInNormalTextBox1
        '
        Me.LogInNormalTextBox1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.LogInNormalTextBox1.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LogInNormalTextBox1.ForeColor = System.Drawing.Color.Black
        Me.LogInNormalTextBox1.Location = New System.Drawing.Point(38, 51)
        Me.LogInNormalTextBox1.Name = "LogInNormalTextBox1"
        Me.LogInNormalTextBox1.ReadOnly = True
        Me.LogInNormalTextBox1.Size = New System.Drawing.Size(479, 21)
        Me.LogInNormalTextBox1.TabIndex = 0
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.LogInButton3)
        Me.GroupBox2.Controls.Add(Me.Label1)
        Me.GroupBox2.Controls.Add(Me.LogInNormalTextBox2)
        Me.GroupBox2.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.GroupBox2.Location = New System.Drawing.Point(28, 204)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(598, 103)
        Me.GroupBox2.TabIndex = 23
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "  Encoded Attachment file :   "
        '
        'LogInButton3
        '
        Me.LogInButton3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.LogInButton3.Appearance.Options.UseBackColor = True
        Me.LogInButton3.Location = New System.Drawing.Point(522, 51)
        Me.LogInButton3.LookAndFeel.SkinName = "Dark Side"
        Me.LogInButton3.LookAndFeel.UseDefaultLookAndFeel = False
        Me.LogInButton3.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LogInButton3.Name = "LogInButton3"
        Me.LogInButton3.Size = New System.Drawing.Size(61, 20)
        Me.LogInButton3.TabIndex = 10
        Me.LogInButton3.Text = "Save"
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(203, Byte), Integer), CType(CType(121, Byte), Integer))
        Me.Label1.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label1.Location = New System.Drawing.Point(41, 26)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(257, 22)
        Me.Label1.TabIndex = 9
        Me.Label1.Text = "Save File  As :"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'LogInNormalTextBox2
        '
        Me.LogInNormalTextBox2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.LogInNormalTextBox2.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LogInNormalTextBox2.ForeColor = System.Drawing.Color.Black
        Me.LogInNormalTextBox2.Location = New System.Drawing.Point(38, 51)
        Me.LogInNormalTextBox2.Name = "LogInNormalTextBox2"
        Me.LogInNormalTextBox2.ReadOnly = True
        Me.LogInNormalTextBox2.Size = New System.Drawing.Size(479, 21)
        Me.LogInNormalTextBox2.TabIndex = 0
        '
        'bntClear
        '
        Me.bntClear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.bntClear.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bntClear.Appearance.Options.UseBackColor = True
        Me.bntClear.Appearance.Options.UseFont = True
        Me.bntClear.Location = New System.Drawing.Point(180, 329)
        Me.bntClear.LookAndFeel.SkinName = "Dark Side"
        Me.bntClear.LookAndFeel.UseDefaultLookAndFeel = False
        Me.bntClear.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.bntClear.Name = "bntClear"
        Me.bntClear.Size = New System.Drawing.Size(142, 31)
        Me.bntClear.TabIndex = 25
        Me.bntClear.Text = "Clear"
        '
        'bntSave
        '
        Me.bntSave.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.bntSave.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bntSave.Appearance.Options.UseBackColor = True
        Me.bntSave.Appearance.Options.UseFont = True
        Me.bntSave.Location = New System.Drawing.Point(328, 329)
        Me.bntSave.LookAndFeel.SkinName = "Dark Side"
        Me.bntSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.bntSave.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.bntSave.Name = "bntSave"
        Me.bntSave.Size = New System.Drawing.Size(142, 31)
        Me.bntSave.TabIndex = 24
        Me.bntSave.Text = "Encrypt"
        '
        'Timer1
        '
        Me.Timer1.Interval = 30
        '
        'ProgressBarControl1
        '
        Me.ProgressBarControl1.EditValue = 50
        Me.ProgressBarControl1.Location = New System.Drawing.Point(85, 327)
        Me.ProgressBarControl1.Name = "ProgressBarControl1"
        Me.ProgressBarControl1.Properties.LookAndFeel.SkinName = "Darkroom"
        Me.ProgressBarControl1.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ProgressBarControl1.Properties.ShowTitle = True
        Me.ProgressBarControl1.Size = New System.Drawing.Size(493, 40)
        Me.ProgressBarControl1.TabIndex = 28
        '
        'txttitle
        '
        Me.txttitle.Font = New System.Drawing.Font("Segoe UI", 15.75!, System.Drawing.FontStyle.Bold)
        Me.txttitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(36, Byte), Integer))
        Me.txttitle.Location = New System.Drawing.Point(427, 45)
        Me.txttitle.Name = "txttitle"
        Me.txttitle.ReadOnly = True
        Me.txttitle.Size = New System.Drawing.Size(43, 35)
        Me.txttitle.TabIndex = 290
        Me.txttitle.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.txttitle.Visible = False
        '
        'LogInCheckBox1
        '
        Me.LogInCheckBox1.AutoSize = True
        Me.LogInCheckBox1.ForeColor = System.Drawing.Color.White
        Me.LogInCheckBox1.Location = New System.Drawing.Point(318, 51)
        Me.LogInCheckBox1.Name = "LogInCheckBox1"
        Me.LogInCheckBox1.Size = New System.Drawing.Size(79, 17)
        Me.LogInCheckBox1.TabIndex = 289
        Me.LogInCheckBox1.Text = "CheckBox1"
        Me.LogInCheckBox1.UseVisualStyleBackColor = True
        Me.LogInCheckBox1.Visible = False
        '
        'LogInNormalTextBox3
        '
        Me.LogInNormalTextBox3.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.0!)
        Me.LogInNormalTextBox3.ForeColor = System.Drawing.Color.FromArgb(CType(CType(8, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(81, Byte), Integer))
        Me.LogInNormalTextBox3.Location = New System.Drawing.Point(366, 43)
        Me.LogInNormalTextBox3.Multiline = True
        Me.LogInNormalTextBox3.Name = "LogInNormalTextBox3"
        Me.LogInNormalTextBox3.Size = New System.Drawing.Size(43, 37)
        Me.LogInNormalTextBox3.TabIndex = 288
        Me.LogInNormalTextBox3.Visible = False
        '
        'frmEncodedAttachment
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(657, 403)
        Me.Controls.Add(Me.ProgressBarControl1)
        Me.Controls.Add(Me.txttitle)
        Me.Controls.Add(Me.LogInCheckBox1)
        Me.Controls.Add(Me.LogInNormalTextBox3)
        Me.Controls.Add(Me.bntClear)
        Me.Controls.Add(Me.bntSave)
        Me.Controls.Add(Me.GroupBox2)
        Me.Controls.Add(Me.GroupBox1)
        Me.FormBorderEffect = DevExpress.XtraEditors.FormBorderEffect.Glow
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Logo_NewBestSender
        Me.LookAndFeel.SkinName = "The Bezier"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmEncodedAttachment"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "  Encoded Attachment ..."
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BehaviorManager1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()
    End Sub
    Friend WithEvents GroupBox1 As GroupBox
    Friend WithEvents LogInButton2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label4 As Label
    Friend WithEvents LogInNormalTextBox1 As TextBox
    Friend WithEvents GroupBox2 As GroupBox
    Friend WithEvents LogInButton3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label1 As Label
    Friend WithEvents LogInNormalTextBox2 As TextBox
    Friend WithEvents bntClear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents bntSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Timer1 As Timer
    Friend WithEvents ProgressBarControl1 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents txttitle As TextBox
    Friend WithEvents LogInCheckBox1 As CheckBox
    Friend WithEvents LogInNormalTextBox3 As TextBox
    Friend WithEvents BehaviorManager1 As DevExpress.Utils.Behaviors.BehaviorManager
End Class
