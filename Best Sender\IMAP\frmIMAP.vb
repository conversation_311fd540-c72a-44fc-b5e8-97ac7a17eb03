﻿Imports System.Net.Mail
Imports System.Net
Imports System.Security.Cryptography.X509Certificates
Imports System.Net.Security
Imports System.Text.RegularExpressions
Imports OpenPop.Pop3
Imports OpenPop.Pop3.Exceptions
Imports OpenPop.Common
Imports MailKit.Net.Imap
Imports MailKit.Security
Imports MailKit
Imports MailKit.Search
Imports MimeKit
Imports System.ComponentModel
Imports DevExpress.XtraEditors
Imports System.Threading
Imports System.IO
Public Class frmIMAP
    Private Sub trmfadein_Tick(sender As Object, e As EventArgs) Handles trmfadein.Tick
        Me.Opacity = Me.Opacity + 0.02
        If Me.Opacity = 1 Then
            Me.Opacity = 98 / 100
            trmfadein.Enabled = False
            trmfadein.Stop()
        End If
    End Sub
    Private Sub trmfadeout_Tick(sender As Object, e As EventArgs) Handles trmfadeout.Tick
        Me.Opacity = Me.Opacity - 0.02
        If Me.Opacity < 0.001 Then
            Me.Dispose()
        End If
    End Sub
    Private Sub CheckEdit1_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEdit1.CheckedChanged
        If CheckEdit1.Checked = True Then
            txtPassword.Properties.PasswordChar = ""
        ElseIf CheckEdit1.Checked = False Then
            txtPassword.Properties.PasswordChar = "•"
        End If
    End Sub
    Private Sub BNT_New_Click(sender As Object, e As EventArgs) Handles BNT_New.Click
        DxErrorProvider1.ClearErrors()
        'TextHost.Text = ""
        'TextEmail.Text = ""
        'TextPort.Text = ""
        'txtPassword.Text = ""
        txtMailList.Text = ""
        txtMailNumber.Text = "0"
        picStatus.Visible = False
        picStatus.Image = Nothing
        IMAP_Account_Status = ""
        IMAP_Accountno_Status = ""
        'CheckSSL.Checked = False
        'CheckSSL_CheckedChanged(Nothing, Nothing)
        Me.ActiveControl = TextHost
        Panel1.Visible = False
    End Sub
    Private Sub BNT_CheckEmail_Click(sender As Object, e As EventArgs) Handles BNT_CheckEmail.Click
        If TextHost.Text.Trim = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Invalid Host IMAP name!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            TextHost.Focus()
            Exit Sub
        End If
        If TextPort.Text.Trim = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Invaild Host IMAP port number...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            TextPort.Focus()
            Exit Sub
        End If
        If TextEmail.Text.Trim = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Invaild Host IMAP user name...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            TextEmail.Focus()
            Exit Sub
        End If
        If txtPassword.Text.Trim = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Invaild Host IMAP user password...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            txtPassword.Focus()
            Exit Sub
        End If
        '=============================================================================
        IMaphost_ = TextHost.Text.Trim
        IMapport_ = CInt(TextPort.Text.Trim)
        IMapusername_ = TextEmail.Text.Trim
        IMappassword_ = txtPassword.Text.Trim
        IMapSSL = CheckSSL.Checked
        '=============================================================================
        IMAP_Account_Status = ""
        picStatus.Visible = False
        picStatus.Image = Nothing
        frmIMAP_Wait.ShowDialog()
        Select Case IMAP_Account_Status
            Case "OK"
                picStatus.Image = My.Resources.IMAPOk
                picStatus.Visible = True
                DevExpress.XtraEditors.XtraMessageBox.Show("IMAP account Successfully connected", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Case ""
                picStatus.Image = Nothing
                picStatus.Visible = False
                DevExpress.XtraEditors.XtraMessageBox.Show("Verification was cancelled by user", "Message", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Case Else
                picStatus.Image = My.Resources.IMAPFail
                picStatus.Visible = True
                DevExpress.XtraEditors.XtraMessageBox.Show(IMAP_Account_Status, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Select
    End Sub
    Private Sub txtHostIMAP_EditValueChanged(sender As Object, e As EventArgs) Handles TextHost.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPort_EditValueChanged(sender As Object, e As EventArgs) Handles TextPort.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtUserName_EditValueChanged(sender As Object, e As EventArgs) Handles TextEmail.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPassword_EditValueChanged(sender As Object, e As EventArgs) Handles txtPassword.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    'If IMAP_Account_Status <> "OK" Then
    '    DevExpress.XtraEditors.XtraMessageBox.Show("IMAP account is not connected", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
    '    Exit Sub
    'End If
    'If Val(txtMailNumber.Text) <= 0 Then
    '    DxErrorProvider1.SetError(txtMailNumber, "Enter the number for mails you want to grab...!")
    '    txtMailNumber.Focus()
    '    Exit Sub
    'End If
    'txtMailList.Text = ""
    'IMAP_AccountNO = CInt(txtMailNumber.Text)
    'IMAP_Accountno_Status = ""
    'frmDataTransfer_Wait.ShowDialog()
    'Select Case IMAP_Accountno_Status
    'Case "OK"
    '        txtMailList.Text = ""
    '        If emailList.Count > 0 Then
    'Dim result As String = String.Join(Environment.NewLine, emailList)
    '            txtMailList.Text = result
    '            DevExpress.XtraEditors.XtraMessageBox.Show("Operation Successfully finished.", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
    '        Else
    '            DevExpress.XtraEditors.XtraMessageBox.Show("Operation Successfully finished, but no mails found.", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
    '        End If
    'Case ""
    '        txtMailList.Text = ""
    '        If emailList.Count > 0 Then
    'Dim result As String = String.Join(Environment.NewLine, emailList)
    '            txtMailList.Text = result
    '            DevExpress.XtraEditors.XtraMessageBox.Show("Operation was cancelled by user after finding " & emailList.Count & " mails.", "Message", MessageBoxButtons.OK, MessageBoxIcon.Warning)
    '        Else
    '            DevExpress.XtraEditors.XtraMessageBox.Show("Operation was cancelled by user.", "Message", MessageBoxButtons.OK, MessageBoxIcon.Warning)
    '        End If
    'Case Else
    '        txtMailList.Text = ""
    '        DevExpress.XtraEditors.XtraMessageBox.Show(IMAP_Accountno_Status, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
    'End Select
    'End Sub
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        If txtMailList.Text.Trim = "" Then Exit Sub
        ' Get the path to the Desktop directory.
        Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory)
        ' Define the path for the "Best Defender" folder.
        Dim bestDefenderPath As String = Path.Combine(desktopPath, "Best Sender")
        ' Check if the "Best Defender" folder exists. If not, create it.
        If Not Directory.Exists(bestDefenderPath) Then
            Directory.CreateDirectory(bestDefenderPath)
        End If
        ' Define the path for the "Email Sorter List" folder.
        Dim emailSorterListPath As String = Path.Combine(bestDefenderPath, "IMAP")
        ' Check if the "Email Sorter List" folder exists. If not, create it.
        If Not Directory.Exists(emailSorterListPath) Then
            Directory.CreateDirectory(emailSorterListPath)
        End If
        Dim filePath As String = Path.Combine(emailSorterListPath, "IMAP Mail List.txt")
        File.WriteAllText(filePath, txtMailList.Text())
        Dim argument As String = "/select," & filePath
        Process.Start("explorer.exe", argument)
        Process.Start(filePath)
        DevExpress.XtraEditors.XtraMessageBox.Show("IMAP Mail List Successfully Saved...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    Private Sub CheckSSL_CheckedChanged(sender As Object, e As EventArgs) Handles CheckSSL.CheckedChanged
        If CheckSSL.Checked = True Then
            CheckSSL.ForeColor = Color.White()
        Else
            CheckSSL.ForeColor = Color.Gray()
        End If
    End Sub
    Private Sub txtMailNumber_EditValueChanged(sender As Object, e As EventArgs) Handles txtMailNumber.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub BackgroundWorker2_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker2.DoWork
        Dim host As String = TextHost.Text
        Dim port As Integer = CInt(TextPort.Text)
        Dim username As String = TextEmail.Text
        Dim password As String = txtPassword.Text
        ' Connect to the email server
        Using client As New ImapClient()
            client.Connect(host, port, SecureSocketOptions.SslOnConnect)
            client.Authenticate(username, password)
            ' Select the inbox folder
            Dim inbox As ImapFolder = client.Inbox
            inbox.Open(FolderAccess.ReadOnly)
            ' Get the list of email
            Dim messages As IList(Of UniqueId) = inbox.Search(SearchQuery.All)
            Dim emailList As New List(Of String)
            ' Check for the emails that you are interested in
            For Each uid As UniqueId In messages
                Dim message As MimeMessage = inbox.GetMessage(uid)
                ' Extract email address from the "From" field
                Dim email As String = message.From.Mailboxes(0).Address
                If BackgroundWorker2.CancellationPending Then
                    e.Cancel = True
                    Return
                End If
                'Check if email is already in the list
                If Not emailList.Contains(email) Then
                    emailList.Add(email)
                    If emailList.Count = CInt(txtMailNumber.Text) Then ' هنا الكود الى بيوقف الفورم كولو
                        Exit For
                    End If
                    'DataGridView1.Invoke(New MethodInvoker(Sub() DataGridView1.Rows.Add(email)))
                    txtMailList.Invoke(Sub() txtMailList.Text &= email & Environment.NewLine)
                End If
            Next
            XtraMessageBox.Show("Done Successfully ", "IMAP", MessageBoxButtons.OK, MessageBoxIcon.Information)
            inbox.Close()
            client.Disconnect(True)
        End Using
    End Sub
    Private Sub BNT_Connect_Click(sender As Object, e As EventArgs) Handles BNT_Connect.Click
        If IMAP_Account_Status <> "OK" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("IMAP account is not connected", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If Val(txtMailNumber.Text) <= 0 Then
            DxErrorProvider1.SetError(txtMailNumber, "Enter the number for mails you want to grab...!")
            txtMailNumber.Focus()
            Exit Sub
        End If
        txtMailList.Text = ""
        IMAP_AccountNO = CInt(txtMailNumber.Text)
        IMAP_Accountno_Status = ""
        emailList.Clear()
        ProgressPanel1.Description = emailList.Count & " out of " & txtMailNumber.Text & " has been found."
        Panel1.Visible = True
        BackgroundWorker1.WorkerSupportsCancellation = True
        If BackgroundWorker1.CancellationPending Then
            BackgroundWorker1.CancelAsync()
        End If
        If BackgroundWorker1.CancellationPending = True Then BackgroundWorker1.CancelAsync()
        If BackgroundWorker1.IsBusy = True Then BackgroundWorker1.CancelAsync()
        BackgroundWorker1.RunWorkerAsync()
        'frmDataTransfer_Wait.ShowDialog()
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Try
            ' Connect to the email server
            Using client As New ImapClient()
                client.Connect(IMaphost_, IMapport_, SecureSocketOptions.SslOnConnect)
                client.Authenticate(IMapusername_, IMappassword_)
                ' Select the inbox folder
                Dim inbox As ImapFolder = client.Inbox
                inbox.Open(FolderAccess.ReadOnly)
                ' Get the list of email
                Dim messages As IList(Of UniqueId) = inbox.Search(SearchQuery.All)
                ' Check for the emails that you are interested in
                For Each uid As UniqueId In messages
                    Dim message As MimeMessage = inbox.GetMessage(uid)
                    ' Extract email address from the "From" field
                    Dim email As String = message.From.Mailboxes(0).Address
                    If BackgroundWorker1.CancellationPending Then
                        e.Cancel = True
                        Return
                    End If
                    'Check if email is already in the list
                    If Not emailList.Contains(email) Then
                        emailList.Add(email)
                        If emailList.Count = CInt(IMAP_AccountNO) Then ' هنا الكود الى بيوقف الفورم كولو
                            Exit For
                        End If
                        ProgressPanel1.Invoke(Sub()
                                                  ProgressPanel1.Description = emailList.Count & " out of " & txtMailNumber.Text & " has been found."
                                                  Refresh()
                                              End Sub)
                        txtMailList.Invoke(Sub()
                                               txtMailList.Text = ""
                                               txtMailList.Text = String.Join(Environment.NewLine, emailList)
                                               txtMailList.Refresh()
                                           End Sub)
                    End If
                Next
                inbox.Close()
                client.Disconnect(True)
            End Using
            IMAP_Accountno_Status = "OK"
        Catch ex As Exception
            IMAP_Accountno_Status = ex.Message
        End Try
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        Panel1.Visible = False
        Select Case IMAP_Accountno_Status
            Case "OK"
                txtMailList.Text = ""
                If emailList.Count > 0 Then
                    Dim result As String = String.Join(Environment.NewLine, emailList)
                    txtMailList.Text = result
                    DevExpress.XtraEditors.XtraMessageBox.Show("Operation Successfully finished.", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Else
                    DevExpress.XtraEditors.XtraMessageBox.Show("Operation Successfully finished, but no mails found.", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
            Case ""
                txtMailList.Text = ""
                If emailList.Count > 0 Then
                    Dim result As String = String.Join(Environment.NewLine, emailList)
                    txtMailList.Text = result
                    DevExpress.XtraEditors.XtraMessageBox.Show("Operation was cancelled by user after finding " & emailList.Count & " mails.", "Message", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Else
                    DevExpress.XtraEditors.XtraMessageBox.Show("Operation was cancelled by user.", "Message", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                End If
            Case Else
                txtMailList.Text = ""
                DevExpress.XtraEditors.XtraMessageBox.Show(IMAP_Accountno_Status, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Select
    End Sub
    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to stop the process now?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        IMAP_Accountno_Status = ""
        BackgroundWorker1.WorkerSupportsCancellation = True
        BackgroundWorker1.CancelAsync()
    End Sub
    'Private Sub CheckSSL_CheckedChanged(sender As Object, e As EventArgs)
    '    If CheckSSL.Checked = True Then
    '        CheckSSL.ForeColor = Color.White()
    '        IMapSSL = True
    '    Else
    '        CheckSSL.ForeColor = Color.Gray()
    '        IMapSSL = False
    '    End If
    'End Sub
End Class