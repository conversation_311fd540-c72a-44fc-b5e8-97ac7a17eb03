﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDragonLetter
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.LayoutControl2 = New DevExpress.XtraLayout.LayoutControl()
        Me.cmbEncryptionMode = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LetterEncoder_bntEncrypt = New DevExpress.XtraEditors.SimpleButton()
        Me.LetterEncoder_bntSave = New DevExpress.XtraEditors.SimpleButton()
        Me.LetterEncoder_Richtext2 = New System.Windows.Forms.RichTextBox()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.LayoutControl3 = New DevExpress.XtraLayout.LayoutControl()
        Me.LetterEncoder_bntClearAll = New DevExpress.XtraEditors.SimpleButton()
        Me.LetterEncoder_bntBrowse = New DevExpress.XtraEditors.SimpleButton()
        Me.LetterEncoder_Richtext = New System.Windows.Forms.RichTextBox()
        Me.LetterEncoder_txtFilepath = New DevExpress.XtraEditors.TextEdit()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.TimerWatcher = New System.Windows.Forms.Timer(Me.components)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl2.SuspendLayout()
        CType(Me.cmbEncryptionMode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl3.SuspendLayout()
        CType(Me.LetterEncoder_txtFilepath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.GroupControl2)
        Me.LayoutControl1.Controls.Add(Me.GroupControl1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(972, 602)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'GroupControl2
        '
        Me.GroupControl2.Controls.Add(Me.LayoutControl2)
        Me.GroupControl2.Location = New System.Drawing.Point(471, 16)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.Size = New System.Drawing.Size(485, 570)
        Me.GroupControl2.TabIndex = 521
        Me.GroupControl2.Text = "Encoded"
        '
        'LayoutControl2
        '
        Me.LayoutControl2.Controls.Add(Me.cmbEncryptionMode)
        Me.LayoutControl2.Controls.Add(Me.LetterEncoder_bntEncrypt)
        Me.LayoutControl2.Controls.Add(Me.LetterEncoder_bntSave)
        Me.LayoutControl2.Controls.Add(Me.LetterEncoder_Richtext2)
        Me.LayoutControl2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl2.Location = New System.Drawing.Point(2, 29)
        Me.LayoutControl2.Name = "LayoutControl2"
        Me.LayoutControl2.Root = Me.LayoutControlGroup1
        Me.LayoutControl2.Size = New System.Drawing.Size(481, 539)
        Me.LayoutControl2.TabIndex = 0
        Me.LayoutControl2.Text = "LayoutControl2"
        '
        'cmbEncryptionMode
        '
        Me.cmbEncryptionMode.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cmbEncryptionMode.EditValue = ""
        Me.cmbEncryptionMode.Location = New System.Drawing.Point(394, 16)
        Me.cmbEncryptionMode.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cmbEncryptionMode.Name = "cmbEncryptionMode"
        Me.cmbEncryptionMode.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cmbEncryptionMode.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cmbEncryptionMode.Properties.Appearance.Options.UseBackColor = True
        Me.cmbEncryptionMode.Properties.Appearance.Options.UseFont = True
        Me.cmbEncryptionMode.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cmbEncryptionMode.Properties.Items.AddRange(New Object() {"Encryption Method 1", "Encryption Method 2"})
        Me.cmbEncryptionMode.Size = New System.Drawing.Size(71, 38)
        Me.cmbEncryptionMode.TabIndex = 46
        '
        'LetterEncoder_bntEncrypt
        '
        Me.LetterEncoder_bntEncrypt.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntEncrypt.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntEncrypt.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.LetterEncoder_bntEncrypt.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntEncrypt.Appearance.Options.UseBackColor = True
        Me.LetterEncoder_bntEncrypt.Appearance.Options.UseBorderColor = True
        Me.LetterEncoder_bntEncrypt.Appearance.Options.UseFont = True
        Me.LetterEncoder_bntEncrypt.Appearance.Options.UseForeColor = True
        Me.LetterEncoder_bntEncrypt.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.LetterEncoder_bntEncrypt.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.LetterEncoder_bntEncrypt.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.LetterEncoder_bntEncrypt.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.LetterEncoder_bntEncrypt.AppearanceDisabled.Options.UseBackColor = True
        Me.LetterEncoder_bntEncrypt.AppearanceDisabled.Options.UseBorderColor = True
        Me.LetterEncoder_bntEncrypt.AppearanceDisabled.Options.UseFont = True
        Me.LetterEncoder_bntEncrypt.AppearanceDisabled.Options.UseForeColor = True
        Me.LetterEncoder_bntEncrypt.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.LetterEncoder_bntEncrypt.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntEncrypt.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.LetterEncoder_bntEncrypt.AppearanceHovered.Options.UseBackColor = True
        Me.LetterEncoder_bntEncrypt.AppearanceHovered.Options.UseBorderColor = True
        Me.LetterEncoder_bntEncrypt.AppearanceHovered.Options.UseForeColor = True
        Me.LetterEncoder_bntEncrypt.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntEncrypt.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntEncrypt.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.LetterEncoder_bntEncrypt.AppearancePressed.Options.UseBackColor = True
        Me.LetterEncoder_bntEncrypt.AppearancePressed.Options.UseBorderColor = True
        Me.LetterEncoder_bntEncrypt.AppearancePressed.Options.UseForeColor = True
        Me.LetterEncoder_bntEncrypt.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.LetterEncoder_bntEncrypt.Location = New System.Drawing.Point(16, 16)
        Me.LetterEncoder_bntEncrypt.Name = "LetterEncoder_bntEncrypt"
        Me.LetterEncoder_bntEncrypt.Size = New System.Drawing.Size(93, 38)
        Me.LetterEncoder_bntEncrypt.StyleController = Me.LayoutControl2
        Me.LetterEncoder_bntEncrypt.TabIndex = 521
        Me.LetterEncoder_bntEncrypt.Text = "Encrypt"
        '
        'LetterEncoder_bntSave
        '
        Me.LetterEncoder_bntSave.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntSave.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntSave.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.LetterEncoder_bntSave.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntSave.Appearance.Options.UseBackColor = True
        Me.LetterEncoder_bntSave.Appearance.Options.UseBorderColor = True
        Me.LetterEncoder_bntSave.Appearance.Options.UseFont = True
        Me.LetterEncoder_bntSave.Appearance.Options.UseForeColor = True
        Me.LetterEncoder_bntSave.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.LetterEncoder_bntSave.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.LetterEncoder_bntSave.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.LetterEncoder_bntSave.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.LetterEncoder_bntSave.AppearanceDisabled.Options.UseBackColor = True
        Me.LetterEncoder_bntSave.AppearanceDisabled.Options.UseBorderColor = True
        Me.LetterEncoder_bntSave.AppearanceDisabled.Options.UseFont = True
        Me.LetterEncoder_bntSave.AppearanceDisabled.Options.UseForeColor = True
        Me.LetterEncoder_bntSave.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.LetterEncoder_bntSave.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntSave.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.LetterEncoder_bntSave.AppearanceHovered.Options.UseBackColor = True
        Me.LetterEncoder_bntSave.AppearanceHovered.Options.UseBorderColor = True
        Me.LetterEncoder_bntSave.AppearanceHovered.Options.UseForeColor = True
        Me.LetterEncoder_bntSave.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntSave.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntSave.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.LetterEncoder_bntSave.AppearancePressed.Options.UseBackColor = True
        Me.LetterEncoder_bntSave.AppearancePressed.Options.UseBorderColor = True
        Me.LetterEncoder_bntSave.AppearancePressed.Options.UseForeColor = True
        Me.LetterEncoder_bntSave.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.html32x32
        Me.LetterEncoder_bntSave.Location = New System.Drawing.Point(115, 16)
        Me.LetterEncoder_bntSave.Name = "LetterEncoder_bntSave"
        Me.LetterEncoder_bntSave.Size = New System.Drawing.Size(110, 38)
        Me.LetterEncoder_bntSave.StyleController = Me.LayoutControl2
        Me.LetterEncoder_bntSave.TabIndex = 520
        Me.LetterEncoder_bntSave.Text = "Save"
        '
        'LetterEncoder_Richtext2
        '
        Me.LetterEncoder_Richtext2.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_Richtext2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.LetterEncoder_Richtext2.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.LetterEncoder_Richtext2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.LetterEncoder_Richtext2.Location = New System.Drawing.Point(16, 60)
        Me.LetterEncoder_Richtext2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LetterEncoder_Richtext2.Name = "LetterEncoder_Richtext2"
        Me.LetterEncoder_Richtext2.Size = New System.Drawing.Size(449, 463)
        Me.LetterEncoder_Richtext2.TabIndex = 68
        Me.LetterEncoder_Richtext2.Text = ""
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem7, Me.LayoutControlItem8, Me.LayoutControlItem6})
        Me.LayoutControlGroup1.Name = "Root"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(481, 539)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.LetterEncoder_Richtext2
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 44)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(455, 469)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.LetterEncoder_bntSave
        Me.LayoutControlItem7.Location = New System.Drawing.Point(99, 0)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(116, 44)
        Me.LayoutControlItem7.TextVisible = False
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.LetterEncoder_bntEncrypt
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(99, 44)
        Me.LayoutControlItem8.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.cmbEncryptionMode
        Me.LayoutControlItem6.Location = New System.Drawing.Point(215, 0)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(240, 44)
        Me.LayoutControlItem6.Text = "Select Algorithm Method : "
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(147, 18)
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.LayoutControl3)
        Me.GroupControl1.Location = New System.Drawing.Point(16, 16)
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(449, 570)
        Me.GroupControl1.TabIndex = 520
        Me.GroupControl1.Text = "Normall"
        '
        'LayoutControl3
        '
        Me.LayoutControl3.Controls.Add(Me.LetterEncoder_bntClearAll)
        Me.LayoutControl3.Controls.Add(Me.LetterEncoder_bntBrowse)
        Me.LayoutControl3.Controls.Add(Me.LetterEncoder_Richtext)
        Me.LayoutControl3.Controls.Add(Me.LetterEncoder_txtFilepath)
        Me.LayoutControl3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl3.Location = New System.Drawing.Point(2, 29)
        Me.LayoutControl3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LayoutControl3.Name = "LayoutControl3"
        Me.LayoutControl3.Root = Me.LayoutControlGroup2
        Me.LayoutControl3.Size = New System.Drawing.Size(445, 539)
        Me.LayoutControl3.TabIndex = 0
        Me.LayoutControl3.Text = "LayoutControl3"
        '
        'LetterEncoder_bntClearAll
        '
        Me.LetterEncoder_bntClearAll.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntClearAll.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntClearAll.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.LetterEncoder_bntClearAll.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntClearAll.Appearance.Options.UseBackColor = True
        Me.LetterEncoder_bntClearAll.Appearance.Options.UseBorderColor = True
        Me.LetterEncoder_bntClearAll.Appearance.Options.UseFont = True
        Me.LetterEncoder_bntClearAll.Appearance.Options.UseForeColor = True
        Me.LetterEncoder_bntClearAll.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.LetterEncoder_bntClearAll.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.LetterEncoder_bntClearAll.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.LetterEncoder_bntClearAll.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.LetterEncoder_bntClearAll.AppearanceDisabled.Options.UseBackColor = True
        Me.LetterEncoder_bntClearAll.AppearanceDisabled.Options.UseBorderColor = True
        Me.LetterEncoder_bntClearAll.AppearanceDisabled.Options.UseFont = True
        Me.LetterEncoder_bntClearAll.AppearanceDisabled.Options.UseForeColor = True
        Me.LetterEncoder_bntClearAll.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.LetterEncoder_bntClearAll.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntClearAll.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.LetterEncoder_bntClearAll.AppearanceHovered.Options.UseBackColor = True
        Me.LetterEncoder_bntClearAll.AppearanceHovered.Options.UseBorderColor = True
        Me.LetterEncoder_bntClearAll.AppearanceHovered.Options.UseForeColor = True
        Me.LetterEncoder_bntClearAll.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntClearAll.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntClearAll.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.LetterEncoder_bntClearAll.AppearancePressed.Options.UseBackColor = True
        Me.LetterEncoder_bntClearAll.AppearancePressed.Options.UseBorderColor = True
        Me.LetterEncoder_bntClearAll.AppearancePressed.Options.UseForeColor = True
        Me.LetterEncoder_bntClearAll.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.LetterEncoder_bntClearAll.Location = New System.Drawing.Point(329, 16)
        Me.LetterEncoder_bntClearAll.Name = "LetterEncoder_bntClearAll"
        Me.LetterEncoder_bntClearAll.Size = New System.Drawing.Size(100, 38)
        Me.LetterEncoder_bntClearAll.StyleController = Me.LayoutControl3
        Me.LetterEncoder_bntClearAll.TabIndex = 521
        Me.LetterEncoder_bntClearAll.Text = "Reset All"
        '
        'LetterEncoder_bntBrowse
        '
        Me.LetterEncoder_bntBrowse.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntBrowse.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntBrowse.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.LetterEncoder_bntBrowse.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntBrowse.Appearance.Options.UseBackColor = True
        Me.LetterEncoder_bntBrowse.Appearance.Options.UseBorderColor = True
        Me.LetterEncoder_bntBrowse.Appearance.Options.UseFont = True
        Me.LetterEncoder_bntBrowse.Appearance.Options.UseForeColor = True
        Me.LetterEncoder_bntBrowse.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.LetterEncoder_bntBrowse.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.LetterEncoder_bntBrowse.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.LetterEncoder_bntBrowse.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.LetterEncoder_bntBrowse.AppearanceDisabled.Options.UseBackColor = True
        Me.LetterEncoder_bntBrowse.AppearanceDisabled.Options.UseBorderColor = True
        Me.LetterEncoder_bntBrowse.AppearanceDisabled.Options.UseFont = True
        Me.LetterEncoder_bntBrowse.AppearanceDisabled.Options.UseForeColor = True
        Me.LetterEncoder_bntBrowse.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.LetterEncoder_bntBrowse.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntBrowse.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.LetterEncoder_bntBrowse.AppearanceHovered.Options.UseBackColor = True
        Me.LetterEncoder_bntBrowse.AppearanceHovered.Options.UseBorderColor = True
        Me.LetterEncoder_bntBrowse.AppearanceHovered.Options.UseForeColor = True
        Me.LetterEncoder_bntBrowse.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_bntBrowse.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_bntBrowse.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.LetterEncoder_bntBrowse.AppearancePressed.Options.UseBackColor = True
        Me.LetterEncoder_bntBrowse.AppearancePressed.Options.UseBorderColor = True
        Me.LetterEncoder_bntBrowse.AppearancePressed.Options.UseForeColor = True
        Me.LetterEncoder_bntBrowse.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.LetterEncoder_bntBrowse.Location = New System.Drawing.Point(221, 16)
        Me.LetterEncoder_bntBrowse.Name = "LetterEncoder_bntBrowse"
        Me.LetterEncoder_bntBrowse.Size = New System.Drawing.Size(102, 38)
        Me.LetterEncoder_bntBrowse.StyleController = Me.LayoutControl3
        Me.LetterEncoder_bntBrowse.TabIndex = 520
        Me.LetterEncoder_bntBrowse.Text = "Browse..."
        '
        'LetterEncoder_Richtext
        '
        Me.LetterEncoder_Richtext.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_Richtext.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.LetterEncoder_Richtext.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.LetterEncoder_Richtext.ForeColor = System.Drawing.Color.White
        Me.LetterEncoder_Richtext.Location = New System.Drawing.Point(16, 60)
        Me.LetterEncoder_Richtext.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LetterEncoder_Richtext.Name = "LetterEncoder_Richtext"
        Me.LetterEncoder_Richtext.Size = New System.Drawing.Size(413, 453)
        Me.LetterEncoder_Richtext.TabIndex = 67
        Me.LetterEncoder_Richtext.Text = ""
        '
        'LetterEncoder_txtFilepath
        '
        Me.LetterEncoder_txtFilepath.EditValue = ""
        Me.LetterEncoder_txtFilepath.Location = New System.Drawing.Point(130, 21)
        Me.LetterEncoder_txtFilepath.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LetterEncoder_txtFilepath.Name = "LetterEncoder_txtFilepath"
        Me.LetterEncoder_txtFilepath.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LetterEncoder_txtFilepath.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseBackColor = True
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseFont = True
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseForeColor = True
        Me.LetterEncoder_txtFilepath.Properties.NullValuePrompt = "Path File HTML..."
        Me.LetterEncoder_txtFilepath.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.LetterEncoder_txtFilepath.Size = New System.Drawing.Size(85, 30)
        Me.LetterEncoder_txtFilepath.StyleController = Me.LayoutControl3
        Me.LetterEncoder_txtFilepath.TabIndex = 66
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup2.GroupBordersVisible = False
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem4, Me.LayoutControlItem5, Me.LayoutControlItem2, Me.LayoutControlItem3, Me.EmptySpaceItem1})
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(445, 539)
        Me.LayoutControlGroup2.TextVisible = False
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.LetterEncoder_txtFilepath
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(205, 44)
        Me.LayoutControlItem4.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 5, 0)
        Me.LayoutControlItem4.Text = "File Path ( *.html ) :"
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(98, 18)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.LetterEncoder_Richtext
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 44)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(419, 459)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.LetterEncoder_bntBrowse
        Me.LayoutControlItem2.Location = New System.Drawing.Point(205, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(108, 44)
        Me.LayoutControlItem2.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.LetterEncoder_bntClearAll
        Me.LayoutControlItem3.Location = New System.Drawing.Point(313, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(106, 44)
        Me.LayoutControlItem3.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 503)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(419, 10)
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem9, Me.LayoutControlItem10})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(972, 602)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.GroupControl2
        Me.LayoutControlItem9.Location = New System.Drawing.Point(455, 0)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(491, 576)
        Me.LayoutControlItem9.TextVisible = False
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.GroupControl1
        Me.LayoutControlItem10.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(455, 576)
        Me.LayoutControlItem10.TextVisible = False
        '
        'TimerWatcher
        '
        Me.TimerWatcher.Enabled = True
        Me.TimerWatcher.Interval = 1000
        '
        'frmDragonLetter
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.LayoutControl1)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Dragon
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmDragonLetter"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Dragon Encoded Letter"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl2.ResumeLayout(False)
        CType(Me.cmbEncryptionMode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl3.ResumeLayout(False)
        CType(Me.LetterEncoder_txtFilepath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LayoutControl2 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LetterEncoder_bntEncrypt As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LetterEncoder_bntSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LetterEncoder_Richtext2 As RichTextBox
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LayoutControl3 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LetterEncoder_bntClearAll As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LetterEncoder_bntBrowse As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LetterEncoder_Richtext As RichTextBox
    Friend WithEvents LetterEncoder_txtFilepath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents TimerWatcher As Timer
    Friend WithEvents cmbEncryptionMode As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
End Class
