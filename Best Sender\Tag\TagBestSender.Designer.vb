﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class TagBestSender
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.SeparatorControl1 = New DevExpress.XtraEditors.SeparatorControl()
        Me.T1 = New DevExpress.XtraEditors.TextEdit()
        Me.btn28 = New DevExpress.XtraEditors.SimpleButton()
        Me.T15 = New DevExpress.XtraEditors.TextEdit()
        Me.btn14 = New DevExpress.XtraEditors.SimpleButton()
        Me.T2 = New DevExpress.XtraEditors.TextEdit()
        Me.btn27 = New DevExpress.XtraEditors.SimpleButton()
        Me.T16 = New DevExpress.XtraEditors.TextEdit()
        Me.btn13 = New DevExpress.XtraEditors.SimpleButton()
        Me.T3 = New DevExpress.XtraEditors.TextEdit()
        Me.btn23 = New DevExpress.XtraEditors.SimpleButton()
        Me.T17 = New DevExpress.XtraEditors.TextEdit()
        Me.btn9 = New DevExpress.XtraEditors.SimpleButton()
        Me.T4 = New DevExpress.XtraEditors.TextEdit()
        Me.btn26 = New DevExpress.XtraEditors.SimpleButton()
        Me.T18 = New DevExpress.XtraEditors.TextEdit()
        Me.btn12 = New DevExpress.XtraEditors.SimpleButton()
        Me.T5 = New DevExpress.XtraEditors.TextEdit()
        Me.btn22 = New DevExpress.XtraEditors.SimpleButton()
        Me.T19 = New DevExpress.XtraEditors.TextEdit()
        Me.btn8 = New DevExpress.XtraEditors.SimpleButton()
        Me.T6 = New DevExpress.XtraEditors.TextEdit()
        Me.btn25 = New DevExpress.XtraEditors.SimpleButton()
        Me.T12 = New DevExpress.XtraEditors.TextEdit()
        Me.btn11 = New DevExpress.XtraEditors.SimpleButton()
        Me.T20 = New DevExpress.XtraEditors.TextEdit()
        Me.btn21 = New DevExpress.XtraEditors.SimpleButton()
        Me.T28 = New DevExpress.XtraEditors.TextEdit()
        Me.btn7 = New DevExpress.XtraEditors.SimpleButton()
        Me.T26 = New DevExpress.XtraEditors.TextEdit()
        Me.btn24 = New DevExpress.XtraEditors.SimpleButton()
        Me.T21 = New DevExpress.XtraEditors.TextEdit()
        Me.btn10 = New DevExpress.XtraEditors.SimpleButton()
        Me.T7 = New DevExpress.XtraEditors.TextEdit()
        Me.btn20 = New DevExpress.XtraEditors.SimpleButton()
        Me.T25 = New DevExpress.XtraEditors.TextEdit()
        Me.btn6 = New DevExpress.XtraEditors.SimpleButton()
        Me.T22 = New DevExpress.XtraEditors.TextEdit()
        Me.btn19 = New DevExpress.XtraEditors.SimpleButton()
        Me.T8 = New DevExpress.XtraEditors.TextEdit()
        Me.btn5 = New DevExpress.XtraEditors.SimpleButton()
        Me.T13 = New DevExpress.XtraEditors.TextEdit()
        Me.btn18 = New DevExpress.XtraEditors.SimpleButton()
        Me.T23 = New DevExpress.XtraEditors.TextEdit()
        Me.btn4 = New DevExpress.XtraEditors.SimpleButton()
        Me.T9 = New DevExpress.XtraEditors.TextEdit()
        Me.btn17 = New DevExpress.XtraEditors.SimpleButton()
        Me.T14 = New DevExpress.XtraEditors.TextEdit()
        Me.btn3 = New DevExpress.XtraEditors.SimpleButton()
        Me.T24 = New DevExpress.XtraEditors.TextEdit()
        Me.btn16 = New DevExpress.XtraEditors.SimpleButton()
        Me.T10 = New DevExpress.XtraEditors.TextEdit()
        Me.btn2 = New DevExpress.XtraEditors.SimpleButton()
        Me.T27 = New DevExpress.XtraEditors.TextEdit()
        Me.btn15 = New DevExpress.XtraEditors.SimpleButton()
        Me.T11 = New DevExpress.XtraEditors.TextEdit()
        Me.btn1 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Label22 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label27 = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.Label21 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Label28 = New System.Windows.Forms.Label()
        Me.Label39 = New System.Windows.Forms.Label()
        Me.T38 = New DevExpress.XtraEditors.TextEdit()
        Me.T37 = New DevExpress.XtraEditors.TextEdit()
        Me.Label31 = New System.Windows.Forms.Label()
        Me.btn35 = New DevExpress.XtraEditors.SimpleButton()
        Me.T31 = New DevExpress.XtraEditors.TextEdit()
        Me.Label32 = New System.Windows.Forms.Label()
        Me.btn29 = New DevExpress.XtraEditors.SimpleButton()
        Me.btn36 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label33 = New System.Windows.Forms.Label()
        Me.Label34 = New System.Windows.Forms.Label()
        Me.Label35 = New System.Windows.Forms.Label()
        Me.Label36 = New System.Windows.Forms.Label()
        Me.Label37 = New System.Windows.Forms.Label()
        Me.Label38 = New System.Windows.Forms.Label()
        Me.btn37 = New DevExpress.XtraEditors.SimpleButton()
        Me.T39 = New DevExpress.XtraEditors.TextEdit()
        Me.btn34 = New DevExpress.XtraEditors.SimpleButton()
        Me.T36 = New DevExpress.XtraEditors.TextEdit()
        Me.btn33 = New DevExpress.XtraEditors.SimpleButton()
        Me.T35 = New DevExpress.XtraEditors.TextEdit()
        Me.btn32 = New DevExpress.XtraEditors.SimpleButton()
        Me.btn31 = New DevExpress.XtraEditors.SimpleButton()
        Me.btn30 = New DevExpress.XtraEditors.SimpleButton()
        Me.T34 = New DevExpress.XtraEditors.TextEdit()
        Me.T32 = New DevExpress.XtraEditors.TextEdit()
        Me.T33 = New DevExpress.XtraEditors.TextEdit()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.T29 = New DevExpress.XtraEditors.TextEdit()
        Me.Label30 = New System.Windows.Forms.Label()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.Label25 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label29 = New System.Windows.Forms.Label()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T15.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T16.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T17.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T18.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T5.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T19.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T6.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T12.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T20.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T28.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T26.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T21.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T7.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T25.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T22.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T8.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T13.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T23.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T9.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T14.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T24.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T10.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T27.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T11.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        CType(Me.T38.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T37.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T31.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T39.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T36.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T35.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T34.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T32.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T33.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T29.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'SeparatorControl1
        '
        Me.SeparatorControl1.LineOrientation = System.Windows.Forms.Orientation.Vertical
        Me.SeparatorControl1.LineThickness = 1
        Me.SeparatorControl1.Location = New System.Drawing.Point(306, 1)
        Me.SeparatorControl1.LookAndFeel.SkinName = "DevExpress Dark Style"
        Me.SeparatorControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.SeparatorControl1.Name = "SeparatorControl1"
        Me.SeparatorControl1.Padding = New System.Windows.Forms.Padding(10, 12, 10, 12)
        Me.SeparatorControl1.Size = New System.Drawing.Size(37, 803)
        Me.SeparatorControl1.TabIndex = 35
        '
        'T1
        '
        Me.T1.EditValue = "[-Email-]"
        Me.T1.Location = New System.Drawing.Point(72, 3)
        Me.T1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T1.Name = "T1"
        Me.T1.Properties.AllowFocused = False
        Me.T1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T1.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T1.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T1.Properties.Appearance.Options.UseBackColor = True
        Me.T1.Properties.Appearance.Options.UseFont = True
        Me.T1.Properties.Appearance.Options.UseForeColor = True
        Me.T1.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T1.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T1.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T1.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T1.Properties.ReadOnly = True
        Me.T1.Size = New System.Drawing.Size(182, 28)
        Me.T1.TabIndex = 24
        '
        'btn28
        '
        Me.btn28.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn28.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn28.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn28.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn28.Appearance.Options.UseBackColor = True
        Me.btn28.Appearance.Options.UseBorderColor = True
        Me.btn28.Appearance.Options.UseFont = True
        Me.btn28.Appearance.Options.UseForeColor = True
        Me.btn28.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn28.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn28.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn28.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn28.AppearanceDisabled.Options.UseBackColor = True
        Me.btn28.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn28.AppearanceDisabled.Options.UseFont = True
        Me.btn28.AppearanceDisabled.Options.UseForeColor = True
        Me.btn28.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn28.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn28.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn28.AppearanceHovered.Options.UseBackColor = True
        Me.btn28.AppearanceHovered.Options.UseBorderColor = True
        Me.btn28.AppearanceHovered.Options.UseForeColor = True
        Me.btn28.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn28.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn28.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn28.AppearancePressed.Options.UseBackColor = True
        Me.btn28.AppearancePressed.Options.UseBorderColor = True
        Me.btn28.AppearancePressed.Options.UseForeColor = True
        Me.btn28.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn28.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn28.Location = New System.Drawing.Point(870, 318)
        Me.btn28.Margin = New System.Windows.Forms.Padding(4)
        Me.btn28.Name = "btn28"
        Me.btn28.Size = New System.Drawing.Size(25, 26)
        Me.btn28.TabIndex = 34
        '
        'T15
        '
        Me.T15.EditValue = "[-RCh3-]"
        Me.T15.Location = New System.Drawing.Point(681, 81)
        Me.T15.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T15.Name = "T15"
        Me.T15.Properties.AllowFocused = False
        Me.T15.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T15.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T15.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T15.Properties.Appearance.Options.UseBackColor = True
        Me.T15.Properties.Appearance.Options.UseFont = True
        Me.T15.Properties.Appearance.Options.UseForeColor = True
        Me.T15.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T15.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T15.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T15.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T15.Properties.ReadOnly = True
        Me.T15.Size = New System.Drawing.Size(182, 28)
        Me.T15.TabIndex = 24
        '
        'btn14
        '
        Me.btn14.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn14.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn14.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn14.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn14.Appearance.Options.UseBackColor = True
        Me.btn14.Appearance.Options.UseBorderColor = True
        Me.btn14.Appearance.Options.UseFont = True
        Me.btn14.Appearance.Options.UseForeColor = True
        Me.btn14.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn14.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn14.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn14.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn14.AppearanceDisabled.Options.UseBackColor = True
        Me.btn14.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn14.AppearanceDisabled.Options.UseFont = True
        Me.btn14.AppearanceDisabled.Options.UseForeColor = True
        Me.btn14.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn14.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn14.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn14.AppearanceHovered.Options.UseBackColor = True
        Me.btn14.AppearanceHovered.Options.UseBorderColor = True
        Me.btn14.AppearanceHovered.Options.UseForeColor = True
        Me.btn14.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn14.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn14.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn14.AppearancePressed.Options.UseBackColor = True
        Me.btn14.AppearancePressed.Options.UseBorderColor = True
        Me.btn14.AppearancePressed.Options.UseForeColor = True
        Me.btn14.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn14.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn14.Location = New System.Drawing.Point(259, 511)
        Me.btn14.Margin = New System.Windows.Forms.Padding(4)
        Me.btn14.Name = "btn14"
        Me.btn14.Size = New System.Drawing.Size(25, 26)
        Me.btn14.TabIndex = 34
        '
        'T2
        '
        Me.T2.EditValue = "[-Email64-]"
        Me.T2.Location = New System.Drawing.Point(72, 42)
        Me.T2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T2.Name = "T2"
        Me.T2.Properties.AllowFocused = False
        Me.T2.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T2.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T2.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T2.Properties.Appearance.Options.UseBackColor = True
        Me.T2.Properties.Appearance.Options.UseFont = True
        Me.T2.Properties.Appearance.Options.UseForeColor = True
        Me.T2.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T2.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T2.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T2.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T2.Properties.ReadOnly = True
        Me.T2.Size = New System.Drawing.Size(182, 28)
        Me.T2.TabIndex = 24
        '
        'btn27
        '
        Me.btn27.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn27.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn27.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn27.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn27.Appearance.Options.UseBackColor = True
        Me.btn27.Appearance.Options.UseBorderColor = True
        Me.btn27.Appearance.Options.UseFont = True
        Me.btn27.Appearance.Options.UseForeColor = True
        Me.btn27.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn27.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn27.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn27.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn27.AppearanceDisabled.Options.UseBackColor = True
        Me.btn27.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn27.AppearanceDisabled.Options.UseFont = True
        Me.btn27.AppearanceDisabled.Options.UseForeColor = True
        Me.btn27.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn27.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn27.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn27.AppearanceHovered.Options.UseBackColor = True
        Me.btn27.AppearanceHovered.Options.UseBorderColor = True
        Me.btn27.AppearanceHovered.Options.UseForeColor = True
        Me.btn27.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn27.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn27.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn27.AppearancePressed.Options.UseBackColor = True
        Me.btn27.AppearancePressed.Options.UseBorderColor = True
        Me.btn27.AppearancePressed.Options.UseForeColor = True
        Me.btn27.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn27.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn27.Location = New System.Drawing.Point(577, 512)
        Me.btn27.Margin = New System.Windows.Forms.Padding(4)
        Me.btn27.Name = "btn27"
        Me.btn27.Size = New System.Drawing.Size(25, 26)
        Me.btn27.TabIndex = 33
        '
        'T16
        '
        Me.T16.EditValue = "[-RCh4-]"
        Me.T16.Location = New System.Drawing.Point(681, 120)
        Me.T16.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T16.Name = "T16"
        Me.T16.Properties.AllowFocused = False
        Me.T16.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T16.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T16.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T16.Properties.Appearance.Options.UseBackColor = True
        Me.T16.Properties.Appearance.Options.UseFont = True
        Me.T16.Properties.Appearance.Options.UseForeColor = True
        Me.T16.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T16.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T16.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T16.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T16.Properties.ReadOnly = True
        Me.T16.Size = New System.Drawing.Size(182, 28)
        Me.T16.TabIndex = 24
        '
        'btn13
        '
        Me.btn13.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn13.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn13.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn13.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn13.Appearance.Options.UseBackColor = True
        Me.btn13.Appearance.Options.UseBorderColor = True
        Me.btn13.Appearance.Options.UseFont = True
        Me.btn13.Appearance.Options.UseForeColor = True
        Me.btn13.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn13.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn13.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn13.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn13.AppearanceDisabled.Options.UseBackColor = True
        Me.btn13.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn13.AppearanceDisabled.Options.UseFont = True
        Me.btn13.AppearanceDisabled.Options.UseForeColor = True
        Me.btn13.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn13.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn13.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn13.AppearanceHovered.Options.UseBackColor = True
        Me.btn13.AppearanceHovered.Options.UseBorderColor = True
        Me.btn13.AppearanceHovered.Options.UseForeColor = True
        Me.btn13.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn13.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn13.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn13.AppearancePressed.Options.UseBackColor = True
        Me.btn13.AppearancePressed.Options.UseBorderColor = True
        Me.btn13.AppearancePressed.Options.UseForeColor = True
        Me.btn13.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn13.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn13.Location = New System.Drawing.Point(870, 359)
        Me.btn13.Margin = New System.Windows.Forms.Padding(4)
        Me.btn13.Name = "btn13"
        Me.btn13.Size = New System.Drawing.Size(25, 26)
        Me.btn13.TabIndex = 33
        '
        'T3
        '
        Me.T3.EditValue = "[-UCase-]"
        Me.T3.Location = New System.Drawing.Point(72, 81)
        Me.T3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T3.Name = "T3"
        Me.T3.Properties.AllowFocused = False
        Me.T3.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T3.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T3.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T3.Properties.Appearance.Options.UseBackColor = True
        Me.T3.Properties.Appearance.Options.UseFont = True
        Me.T3.Properties.Appearance.Options.UseForeColor = True
        Me.T3.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T3.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T3.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T3.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T3.Properties.ReadOnly = True
        Me.T3.Size = New System.Drawing.Size(182, 28)
        Me.T3.TabIndex = 24
        '
        'btn23
        '
        Me.btn23.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn23.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn23.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn23.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn23.Appearance.Options.UseBackColor = True
        Me.btn23.Appearance.Options.UseBorderColor = True
        Me.btn23.Appearance.Options.UseFont = True
        Me.btn23.Appearance.Options.UseForeColor = True
        Me.btn23.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn23.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn23.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn23.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn23.AppearanceDisabled.Options.UseBackColor = True
        Me.btn23.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn23.AppearanceDisabled.Options.UseFont = True
        Me.btn23.AppearanceDisabled.Options.UseForeColor = True
        Me.btn23.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn23.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn23.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn23.AppearanceHovered.Options.UseBackColor = True
        Me.btn23.AppearanceHovered.Options.UseBorderColor = True
        Me.btn23.AppearanceHovered.Options.UseForeColor = True
        Me.btn23.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn23.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn23.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn23.AppearancePressed.Options.UseBackColor = True
        Me.btn23.AppearancePressed.Options.UseBorderColor = True
        Me.btn23.AppearancePressed.Options.UseForeColor = True
        Me.btn23.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn23.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn23.Location = New System.Drawing.Point(576, 201)
        Me.btn23.Margin = New System.Windows.Forms.Padding(4)
        Me.btn23.Name = "btn23"
        Me.btn23.Size = New System.Drawing.Size(25, 26)
        Me.btn23.TabIndex = 34
        '
        'T17
        '
        Me.T17.EditValue = "[-RCh5-]"
        Me.T17.Location = New System.Drawing.Point(681, 156)
        Me.T17.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T17.Name = "T17"
        Me.T17.Properties.AllowFocused = False
        Me.T17.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T17.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T17.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T17.Properties.Appearance.Options.UseBackColor = True
        Me.T17.Properties.Appearance.Options.UseFont = True
        Me.T17.Properties.Appearance.Options.UseForeColor = True
        Me.T17.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T17.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T17.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T17.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T17.Properties.ReadOnly = True
        Me.T17.Size = New System.Drawing.Size(182, 28)
        Me.T17.TabIndex = 24
        '
        'btn9
        '
        Me.btn9.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn9.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn9.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn9.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn9.Appearance.Options.UseBackColor = True
        Me.btn9.Appearance.Options.UseBorderColor = True
        Me.btn9.Appearance.Options.UseFont = True
        Me.btn9.Appearance.Options.UseForeColor = True
        Me.btn9.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn9.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn9.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn9.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn9.AppearanceDisabled.Options.UseBackColor = True
        Me.btn9.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn9.AppearanceDisabled.Options.UseFont = True
        Me.btn9.AppearanceDisabled.Options.UseForeColor = True
        Me.btn9.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn9.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn9.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn9.AppearanceHovered.Options.UseBackColor = True
        Me.btn9.AppearanceHovered.Options.UseBorderColor = True
        Me.btn9.AppearanceHovered.Options.UseForeColor = True
        Me.btn9.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn9.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn9.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn9.AppearancePressed.Options.UseBackColor = True
        Me.btn9.AppearancePressed.Options.UseBorderColor = True
        Me.btn9.AppearancePressed.Options.UseForeColor = True
        Me.btn9.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn9.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn9.Location = New System.Drawing.Point(259, 316)
        Me.btn9.Margin = New System.Windows.Forms.Padding(4)
        Me.btn9.Name = "btn9"
        Me.btn9.Size = New System.Drawing.Size(25, 26)
        Me.btn9.TabIndex = 34
        '
        'T4
        '
        Me.T4.EditValue = "[-Link-]"
        Me.T4.Location = New System.Drawing.Point(72, 120)
        Me.T4.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T4.Name = "T4"
        Me.T4.Properties.AllowFocused = False
        Me.T4.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T4.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T4.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T4.Properties.Appearance.Options.UseBackColor = True
        Me.T4.Properties.Appearance.Options.UseFont = True
        Me.T4.Properties.Appearance.Options.UseForeColor = True
        Me.T4.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T4.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T4.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T4.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T4.Properties.ReadOnly = True
        Me.T4.Size = New System.Drawing.Size(182, 28)
        Me.T4.TabIndex = 24
        '
        'btn26
        '
        Me.btn26.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn26.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn26.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn26.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn26.Appearance.Options.UseBackColor = True
        Me.btn26.Appearance.Options.UseBorderColor = True
        Me.btn26.Appearance.Options.UseFont = True
        Me.btn26.Appearance.Options.UseForeColor = True
        Me.btn26.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn26.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn26.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn26.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn26.AppearanceDisabled.Options.UseBackColor = True
        Me.btn26.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn26.AppearanceDisabled.Options.UseFont = True
        Me.btn26.AppearanceDisabled.Options.UseForeColor = True
        Me.btn26.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn26.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn26.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn26.AppearanceHovered.Options.UseBackColor = True
        Me.btn26.AppearanceHovered.Options.UseBorderColor = True
        Me.btn26.AppearanceHovered.Options.UseForeColor = True
        Me.btn26.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn26.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn26.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn26.AppearancePressed.Options.UseBackColor = True
        Me.btn26.AppearancePressed.Options.UseBorderColor = True
        Me.btn26.AppearancePressed.Options.UseForeColor = True
        Me.btn26.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn26.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn26.Location = New System.Drawing.Point(576, 474)
        Me.btn26.Margin = New System.Windows.Forms.Padding(4)
        Me.btn26.Name = "btn26"
        Me.btn26.Size = New System.Drawing.Size(25, 26)
        Me.btn26.TabIndex = 32
        '
        'T18
        '
        Me.T18.EditValue = "[-RCh6-]"
        Me.T18.Location = New System.Drawing.Point(681, 198)
        Me.T18.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T18.Name = "T18"
        Me.T18.Properties.AllowFocused = False
        Me.T18.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T18.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T18.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T18.Properties.Appearance.Options.UseBackColor = True
        Me.T18.Properties.Appearance.Options.UseFont = True
        Me.T18.Properties.Appearance.Options.UseForeColor = True
        Me.T18.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T18.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T18.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T18.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T18.Properties.ReadOnly = True
        Me.T18.Size = New System.Drawing.Size(182, 28)
        Me.T18.TabIndex = 24
        '
        'btn12
        '
        Me.btn12.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn12.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn12.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn12.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn12.Appearance.Options.UseBackColor = True
        Me.btn12.Appearance.Options.UseBorderColor = True
        Me.btn12.Appearance.Options.UseFont = True
        Me.btn12.Appearance.Options.UseForeColor = True
        Me.btn12.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn12.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn12.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn12.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn12.AppearanceDisabled.Options.UseBackColor = True
        Me.btn12.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn12.AppearanceDisabled.Options.UseFont = True
        Me.btn12.AppearanceDisabled.Options.UseForeColor = True
        Me.btn12.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn12.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn12.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn12.AppearanceHovered.Options.UseBackColor = True
        Me.btn12.AppearanceHovered.Options.UseBorderColor = True
        Me.btn12.AppearanceHovered.Options.UseForeColor = True
        Me.btn12.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn12.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn12.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn12.AppearancePressed.Options.UseBackColor = True
        Me.btn12.AppearancePressed.Options.UseBorderColor = True
        Me.btn12.AppearancePressed.Options.UseForeColor = True
        Me.btn12.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn12.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn12.Location = New System.Drawing.Point(259, 433)
        Me.btn12.Margin = New System.Windows.Forms.Padding(4)
        Me.btn12.Name = "btn12"
        Me.btn12.Size = New System.Drawing.Size(25, 26)
        Me.btn12.TabIndex = 32
        '
        'T5
        '
        Me.T5.EditValue = "[-Logo-]"
        Me.T5.Location = New System.Drawing.Point(72, 159)
        Me.T5.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T5.Name = "T5"
        Me.T5.Properties.AllowFocused = False
        Me.T5.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T5.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T5.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T5.Properties.Appearance.Options.UseBackColor = True
        Me.T5.Properties.Appearance.Options.UseFont = True
        Me.T5.Properties.Appearance.Options.UseForeColor = True
        Me.T5.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T5.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T5.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T5.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T5.Properties.ReadOnly = True
        Me.T5.Size = New System.Drawing.Size(182, 28)
        Me.T5.TabIndex = 24
        '
        'btn22
        '
        Me.btn22.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn22.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn22.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn22.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn22.Appearance.Options.UseBackColor = True
        Me.btn22.Appearance.Options.UseBorderColor = True
        Me.btn22.Appearance.Options.UseFont = True
        Me.btn22.Appearance.Options.UseForeColor = True
        Me.btn22.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn22.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn22.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn22.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn22.AppearanceDisabled.Options.UseBackColor = True
        Me.btn22.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn22.AppearanceDisabled.Options.UseFont = True
        Me.btn22.AppearanceDisabled.Options.UseForeColor = True
        Me.btn22.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn22.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn22.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn22.AppearanceHovered.Options.UseBackColor = True
        Me.btn22.AppearanceHovered.Options.UseBorderColor = True
        Me.btn22.AppearanceHovered.Options.UseForeColor = True
        Me.btn22.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn22.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn22.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn22.AppearancePressed.Options.UseBackColor = True
        Me.btn22.AppearancePressed.Options.UseBorderColor = True
        Me.btn22.AppearancePressed.Options.UseForeColor = True
        Me.btn22.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn22.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn22.Location = New System.Drawing.Point(576, 162)
        Me.btn22.Margin = New System.Windows.Forms.Padding(4)
        Me.btn22.Name = "btn22"
        Me.btn22.Size = New System.Drawing.Size(25, 26)
        Me.btn22.TabIndex = 33
        '
        'T19
        '
        Me.T19.EditValue = "[-RCh7-]"
        Me.T19.Location = New System.Drawing.Point(681, 237)
        Me.T19.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T19.Name = "T19"
        Me.T19.Properties.AllowFocused = False
        Me.T19.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T19.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T19.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T19.Properties.Appearance.Options.UseBackColor = True
        Me.T19.Properties.Appearance.Options.UseFont = True
        Me.T19.Properties.Appearance.Options.UseForeColor = True
        Me.T19.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T19.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T19.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T19.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T19.Properties.ReadOnly = True
        Me.T19.Size = New System.Drawing.Size(182, 28)
        Me.T19.TabIndex = 24
        '
        'btn8
        '
        Me.btn8.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn8.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn8.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn8.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn8.Appearance.Options.UseBackColor = True
        Me.btn8.Appearance.Options.UseBorderColor = True
        Me.btn8.Appearance.Options.UseFont = True
        Me.btn8.Appearance.Options.UseForeColor = True
        Me.btn8.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn8.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn8.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn8.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn8.AppearanceDisabled.Options.UseBackColor = True
        Me.btn8.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn8.AppearanceDisabled.Options.UseFont = True
        Me.btn8.AppearanceDisabled.Options.UseForeColor = True
        Me.btn8.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn8.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn8.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn8.AppearanceHovered.Options.UseBackColor = True
        Me.btn8.AppearanceHovered.Options.UseBorderColor = True
        Me.btn8.AppearanceHovered.Options.UseForeColor = True
        Me.btn8.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn8.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn8.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn8.AppearancePressed.Options.UseBackColor = True
        Me.btn8.AppearancePressed.Options.UseBorderColor = True
        Me.btn8.AppearancePressed.Options.UseForeColor = True
        Me.btn8.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn8.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn8.Location = New System.Drawing.Point(259, 277)
        Me.btn8.Margin = New System.Windows.Forms.Padding(4)
        Me.btn8.Name = "btn8"
        Me.btn8.Size = New System.Drawing.Size(25, 26)
        Me.btn8.TabIndex = 33
        '
        'T6
        '
        Me.T6.EditValue = "[-IP-]"
        Me.T6.Location = New System.Drawing.Point(72, 198)
        Me.T6.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T6.Name = "T6"
        Me.T6.Properties.AllowFocused = False
        Me.T6.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T6.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T6.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T6.Properties.Appearance.Options.UseBackColor = True
        Me.T6.Properties.Appearance.Options.UseFont = True
        Me.T6.Properties.Appearance.Options.UseForeColor = True
        Me.T6.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T6.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T6.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T6.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T6.Properties.ReadOnly = True
        Me.T6.Size = New System.Drawing.Size(182, 28)
        Me.T6.TabIndex = 24
        '
        'btn25
        '
        Me.btn25.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn25.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn25.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn25.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn25.Appearance.Options.UseBackColor = True
        Me.btn25.Appearance.Options.UseBorderColor = True
        Me.btn25.Appearance.Options.UseFont = True
        Me.btn25.Appearance.Options.UseForeColor = True
        Me.btn25.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn25.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn25.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn25.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn25.AppearanceDisabled.Options.UseBackColor = True
        Me.btn25.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn25.AppearanceDisabled.Options.UseFont = True
        Me.btn25.AppearanceDisabled.Options.UseForeColor = True
        Me.btn25.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn25.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn25.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn25.AppearanceHovered.Options.UseBackColor = True
        Me.btn25.AppearanceHovered.Options.UseBorderColor = True
        Me.btn25.AppearanceHovered.Options.UseForeColor = True
        Me.btn25.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn25.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn25.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn25.AppearancePressed.Options.UseBackColor = True
        Me.btn25.AppearancePressed.Options.UseBorderColor = True
        Me.btn25.AppearancePressed.Options.UseForeColor = True
        Me.btn25.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn25.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn25.Location = New System.Drawing.Point(576, 435)
        Me.btn25.Margin = New System.Windows.Forms.Padding(4)
        Me.btn25.Name = "btn25"
        Me.btn25.Size = New System.Drawing.Size(25, 26)
        Me.btn25.TabIndex = 31
        '
        'T12
        '
        Me.T12.EditValue = "[-DateTomorrow-]"
        Me.T12.Location = New System.Drawing.Point(72, 432)
        Me.T12.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T12.Name = "T12"
        Me.T12.Properties.AllowFocused = False
        Me.T12.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T12.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T12.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T12.Properties.Appearance.Options.UseBackColor = True
        Me.T12.Properties.Appearance.Options.UseFont = True
        Me.T12.Properties.Appearance.Options.UseForeColor = True
        Me.T12.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T12.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T12.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T12.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T12.Properties.ReadOnly = True
        Me.T12.Size = New System.Drawing.Size(182, 28)
        Me.T12.TabIndex = 24
        '
        'btn11
        '
        Me.btn11.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn11.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn11.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn11.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn11.Appearance.Options.UseBackColor = True
        Me.btn11.Appearance.Options.UseBorderColor = True
        Me.btn11.Appearance.Options.UseFont = True
        Me.btn11.Appearance.Options.UseForeColor = True
        Me.btn11.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn11.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn11.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn11.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn11.AppearanceDisabled.Options.UseBackColor = True
        Me.btn11.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn11.AppearanceDisabled.Options.UseFont = True
        Me.btn11.AppearanceDisabled.Options.UseForeColor = True
        Me.btn11.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn11.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn11.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn11.AppearanceHovered.Options.UseBackColor = True
        Me.btn11.AppearanceHovered.Options.UseBorderColor = True
        Me.btn11.AppearanceHovered.Options.UseForeColor = True
        Me.btn11.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn11.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn11.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn11.AppearancePressed.Options.UseBackColor = True
        Me.btn11.AppearancePressed.Options.UseBorderColor = True
        Me.btn11.AppearancePressed.Options.UseForeColor = True
        Me.btn11.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn11.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn11.Location = New System.Drawing.Point(259, 394)
        Me.btn11.Margin = New System.Windows.Forms.Padding(4)
        Me.btn11.Name = "btn11"
        Me.btn11.Size = New System.Drawing.Size(25, 26)
        Me.btn11.TabIndex = 31
        '
        'T20
        '
        Me.T20.EditValue = "[-RN3-]"
        Me.T20.Location = New System.Drawing.Point(388, 83)
        Me.T20.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T20.Name = "T20"
        Me.T20.Properties.AllowFocused = False
        Me.T20.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T20.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T20.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T20.Properties.Appearance.Options.UseBackColor = True
        Me.T20.Properties.Appearance.Options.UseFont = True
        Me.T20.Properties.Appearance.Options.UseForeColor = True
        Me.T20.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T20.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T20.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T20.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T20.Properties.ReadOnly = True
        Me.T20.Size = New System.Drawing.Size(182, 28)
        Me.T20.TabIndex = 24
        '
        'btn21
        '
        Me.btn21.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn21.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn21.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn21.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn21.Appearance.Options.UseBackColor = True
        Me.btn21.Appearance.Options.UseBorderColor = True
        Me.btn21.Appearance.Options.UseFont = True
        Me.btn21.Appearance.Options.UseForeColor = True
        Me.btn21.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn21.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn21.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn21.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn21.AppearanceDisabled.Options.UseBackColor = True
        Me.btn21.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn21.AppearanceDisabled.Options.UseFont = True
        Me.btn21.AppearanceDisabled.Options.UseForeColor = True
        Me.btn21.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn21.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn21.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn21.AppearanceHovered.Options.UseBackColor = True
        Me.btn21.AppearanceHovered.Options.UseBorderColor = True
        Me.btn21.AppearanceHovered.Options.UseForeColor = True
        Me.btn21.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn21.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn21.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn21.AppearancePressed.Options.UseBackColor = True
        Me.btn21.AppearancePressed.Options.UseBorderColor = True
        Me.btn21.AppearancePressed.Options.UseForeColor = True
        Me.btn21.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn21.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn21.Location = New System.Drawing.Point(576, 123)
        Me.btn21.Margin = New System.Windows.Forms.Padding(4)
        Me.btn21.Name = "btn21"
        Me.btn21.Size = New System.Drawing.Size(25, 26)
        Me.btn21.TabIndex = 32
        '
        'T28
        '
        Me.T28.EditValue = "[-IPChina-]"
        Me.T28.Location = New System.Drawing.Point(681, 317)
        Me.T28.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T28.Name = "T28"
        Me.T28.Properties.AllowFocused = False
        Me.T28.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T28.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T28.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T28.Properties.Appearance.Options.UseBackColor = True
        Me.T28.Properties.Appearance.Options.UseFont = True
        Me.T28.Properties.Appearance.Options.UseForeColor = True
        Me.T28.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T28.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T28.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T28.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T28.Properties.ReadOnly = True
        Me.T28.Size = New System.Drawing.Size(182, 28)
        Me.T28.TabIndex = 24
        '
        'btn7
        '
        Me.btn7.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn7.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn7.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn7.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn7.Appearance.Options.UseBackColor = True
        Me.btn7.Appearance.Options.UseBorderColor = True
        Me.btn7.Appearance.Options.UseFont = True
        Me.btn7.Appearance.Options.UseForeColor = True
        Me.btn7.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn7.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn7.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn7.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn7.AppearanceDisabled.Options.UseBackColor = True
        Me.btn7.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn7.AppearanceDisabled.Options.UseFont = True
        Me.btn7.AppearanceDisabled.Options.UseForeColor = True
        Me.btn7.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn7.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn7.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn7.AppearanceHovered.Options.UseBackColor = True
        Me.btn7.AppearanceHovered.Options.UseBorderColor = True
        Me.btn7.AppearanceHovered.Options.UseForeColor = True
        Me.btn7.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn7.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn7.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn7.AppearancePressed.Options.UseBackColor = True
        Me.btn7.AppearancePressed.Options.UseBorderColor = True
        Me.btn7.AppearancePressed.Options.UseForeColor = True
        Me.btn7.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn7.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn7.Location = New System.Drawing.Point(259, 238)
        Me.btn7.Margin = New System.Windows.Forms.Padding(4)
        Me.btn7.Name = "btn7"
        Me.btn7.Size = New System.Drawing.Size(25, 26)
        Me.btn7.TabIndex = 32
        '
        'T26
        '
        Me.T26.EditValue = "[-RCountry-]"
        Me.T26.Location = New System.Drawing.Point(388, 473)
        Me.T26.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T26.Name = "T26"
        Me.T26.Properties.AllowFocused = False
        Me.T26.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T26.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T26.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T26.Properties.Appearance.Options.UseBackColor = True
        Me.T26.Properties.Appearance.Options.UseFont = True
        Me.T26.Properties.Appearance.Options.UseForeColor = True
        Me.T26.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T26.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T26.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T26.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T26.Properties.ReadOnly = True
        Me.T26.Size = New System.Drawing.Size(182, 28)
        Me.T26.TabIndex = 24
        '
        'btn24
        '
        Me.btn24.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn24.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn24.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn24.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn24.Appearance.Options.UseBackColor = True
        Me.btn24.Appearance.Options.UseBorderColor = True
        Me.btn24.Appearance.Options.UseFont = True
        Me.btn24.Appearance.Options.UseForeColor = True
        Me.btn24.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn24.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn24.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn24.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn24.AppearanceDisabled.Options.UseBackColor = True
        Me.btn24.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn24.AppearanceDisabled.Options.UseFont = True
        Me.btn24.AppearanceDisabled.Options.UseForeColor = True
        Me.btn24.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn24.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn24.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn24.AppearanceHovered.Options.UseBackColor = True
        Me.btn24.AppearanceHovered.Options.UseBorderColor = True
        Me.btn24.AppearanceHovered.Options.UseForeColor = True
        Me.btn24.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn24.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn24.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn24.AppearancePressed.Options.UseBackColor = True
        Me.btn24.AppearancePressed.Options.UseBorderColor = True
        Me.btn24.AppearancePressed.Options.UseForeColor = True
        Me.btn24.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn24.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn24.Location = New System.Drawing.Point(576, 240)
        Me.btn24.Margin = New System.Windows.Forms.Padding(4)
        Me.btn24.Name = "btn24"
        Me.btn24.Size = New System.Drawing.Size(25, 26)
        Me.btn24.TabIndex = 30
        '
        'T21
        '
        Me.T21.EditValue = "[-RN4-]"
        Me.T21.Location = New System.Drawing.Point(388, 122)
        Me.T21.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T21.Name = "T21"
        Me.T21.Properties.AllowFocused = False
        Me.T21.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T21.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T21.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T21.Properties.Appearance.Options.UseBackColor = True
        Me.T21.Properties.Appearance.Options.UseFont = True
        Me.T21.Properties.Appearance.Options.UseForeColor = True
        Me.T21.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T21.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T21.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T21.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T21.Properties.ReadOnly = True
        Me.T21.Size = New System.Drawing.Size(182, 28)
        Me.T21.TabIndex = 24
        '
        'btn10
        '
        Me.btn10.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn10.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn10.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn10.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn10.Appearance.Options.UseBackColor = True
        Me.btn10.Appearance.Options.UseBorderColor = True
        Me.btn10.Appearance.Options.UseFont = True
        Me.btn10.Appearance.Options.UseForeColor = True
        Me.btn10.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn10.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn10.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn10.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn10.AppearanceDisabled.Options.UseBackColor = True
        Me.btn10.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn10.AppearanceDisabled.Options.UseFont = True
        Me.btn10.AppearanceDisabled.Options.UseForeColor = True
        Me.btn10.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn10.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn10.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn10.AppearanceHovered.Options.UseBackColor = True
        Me.btn10.AppearanceHovered.Options.UseBorderColor = True
        Me.btn10.AppearanceHovered.Options.UseForeColor = True
        Me.btn10.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn10.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn10.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn10.AppearancePressed.Options.UseBackColor = True
        Me.btn10.AppearancePressed.Options.UseBorderColor = True
        Me.btn10.AppearancePressed.Options.UseForeColor = True
        Me.btn10.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn10.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn10.Location = New System.Drawing.Point(259, 355)
        Me.btn10.Margin = New System.Windows.Forms.Padding(4)
        Me.btn10.Name = "btn10"
        Me.btn10.Size = New System.Drawing.Size(25, 26)
        Me.btn10.TabIndex = 30
        '
        'T7
        '
        Me.T7.EditValue = "[-Domain-]"
        Me.T7.Location = New System.Drawing.Point(72, 237)
        Me.T7.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T7.Name = "T7"
        Me.T7.Properties.AllowFocused = False
        Me.T7.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T7.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T7.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T7.Properties.Appearance.Options.UseBackColor = True
        Me.T7.Properties.Appearance.Options.UseFont = True
        Me.T7.Properties.Appearance.Options.UseForeColor = True
        Me.T7.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T7.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T7.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T7.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T7.Properties.ReadOnly = True
        Me.T7.Size = New System.Drawing.Size(182, 28)
        Me.T7.TabIndex = 24
        '
        'btn20
        '
        Me.btn20.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn20.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn20.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn20.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn20.Appearance.Options.UseBackColor = True
        Me.btn20.Appearance.Options.UseBorderColor = True
        Me.btn20.Appearance.Options.UseFont = True
        Me.btn20.Appearance.Options.UseForeColor = True
        Me.btn20.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn20.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn20.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn20.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn20.AppearanceDisabled.Options.UseBackColor = True
        Me.btn20.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn20.AppearanceDisabled.Options.UseFont = True
        Me.btn20.AppearanceDisabled.Options.UseForeColor = True
        Me.btn20.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn20.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn20.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn20.AppearanceHovered.Options.UseBackColor = True
        Me.btn20.AppearanceHovered.Options.UseBorderColor = True
        Me.btn20.AppearanceHovered.Options.UseForeColor = True
        Me.btn20.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn20.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn20.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn20.AppearancePressed.Options.UseBackColor = True
        Me.btn20.AppearancePressed.Options.UseBorderColor = True
        Me.btn20.AppearancePressed.Options.UseForeColor = True
        Me.btn20.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn20.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn20.Location = New System.Drawing.Point(576, 84)
        Me.btn20.Margin = New System.Windows.Forms.Padding(4)
        Me.btn20.Name = "btn20"
        Me.btn20.Size = New System.Drawing.Size(25, 26)
        Me.btn20.TabIndex = 31
        '
        'T25
        '
        Me.T25.EditValue = "[-RandomBrowser-]"
        Me.T25.Location = New System.Drawing.Point(388, 434)
        Me.T25.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T25.Name = "T25"
        Me.T25.Properties.AllowFocused = False
        Me.T25.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T25.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T25.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T25.Properties.Appearance.Options.UseBackColor = True
        Me.T25.Properties.Appearance.Options.UseFont = True
        Me.T25.Properties.Appearance.Options.UseForeColor = True
        Me.T25.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T25.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T25.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T25.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T25.Properties.ReadOnly = True
        Me.T25.Size = New System.Drawing.Size(182, 28)
        Me.T25.TabIndex = 24
        '
        'btn6
        '
        Me.btn6.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn6.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn6.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn6.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn6.Appearance.Options.UseBackColor = True
        Me.btn6.Appearance.Options.UseBorderColor = True
        Me.btn6.Appearance.Options.UseFont = True
        Me.btn6.Appearance.Options.UseForeColor = True
        Me.btn6.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn6.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn6.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn6.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn6.AppearanceDisabled.Options.UseBackColor = True
        Me.btn6.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn6.AppearanceDisabled.Options.UseFont = True
        Me.btn6.AppearanceDisabled.Options.UseForeColor = True
        Me.btn6.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn6.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn6.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn6.AppearanceHovered.Options.UseBackColor = True
        Me.btn6.AppearanceHovered.Options.UseBorderColor = True
        Me.btn6.AppearanceHovered.Options.UseForeColor = True
        Me.btn6.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn6.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn6.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn6.AppearancePressed.Options.UseBackColor = True
        Me.btn6.AppearancePressed.Options.UseBorderColor = True
        Me.btn6.AppearancePressed.Options.UseForeColor = True
        Me.btn6.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn6.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn6.Location = New System.Drawing.Point(259, 199)
        Me.btn6.Margin = New System.Windows.Forms.Padding(4)
        Me.btn6.Name = "btn6"
        Me.btn6.Size = New System.Drawing.Size(25, 26)
        Me.btn6.TabIndex = 31
        '
        'T22
        '
        Me.T22.EditValue = "[-RN5-]"
        Me.T22.Location = New System.Drawing.Point(388, 161)
        Me.T22.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T22.Name = "T22"
        Me.T22.Properties.AllowFocused = False
        Me.T22.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T22.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T22.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T22.Properties.Appearance.Options.UseBackColor = True
        Me.T22.Properties.Appearance.Options.UseFont = True
        Me.T22.Properties.Appearance.Options.UseForeColor = True
        Me.T22.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T22.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T22.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T22.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T22.Properties.ReadOnly = True
        Me.T22.Size = New System.Drawing.Size(182, 28)
        Me.T22.TabIndex = 24
        '
        'btn19
        '
        Me.btn19.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn19.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn19.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn19.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn19.Appearance.Options.UseBackColor = True
        Me.btn19.Appearance.Options.UseBorderColor = True
        Me.btn19.Appearance.Options.UseFont = True
        Me.btn19.Appearance.Options.UseForeColor = True
        Me.btn19.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn19.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn19.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn19.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn19.AppearanceDisabled.Options.UseBackColor = True
        Me.btn19.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn19.AppearanceDisabled.Options.UseFont = True
        Me.btn19.AppearanceDisabled.Options.UseForeColor = True
        Me.btn19.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn19.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn19.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn19.AppearanceHovered.Options.UseBackColor = True
        Me.btn19.AppearanceHovered.Options.UseBorderColor = True
        Me.btn19.AppearanceHovered.Options.UseForeColor = True
        Me.btn19.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn19.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn19.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn19.AppearancePressed.Options.UseBackColor = True
        Me.btn19.AppearancePressed.Options.UseBorderColor = True
        Me.btn19.AppearancePressed.Options.UseForeColor = True
        Me.btn19.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn19.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn19.Location = New System.Drawing.Point(870, 238)
        Me.btn19.Margin = New System.Windows.Forms.Padding(4)
        Me.btn19.Name = "btn19"
        Me.btn19.Size = New System.Drawing.Size(25, 26)
        Me.btn19.TabIndex = 30
        '
        'T8
        '
        Me.T8.EditValue = "[-Name-]"
        Me.T8.Location = New System.Drawing.Point(72, 276)
        Me.T8.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T8.Name = "T8"
        Me.T8.Properties.AllowFocused = False
        Me.T8.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T8.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T8.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T8.Properties.Appearance.Options.UseBackColor = True
        Me.T8.Properties.Appearance.Options.UseFont = True
        Me.T8.Properties.Appearance.Options.UseForeColor = True
        Me.T8.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T8.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T8.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T8.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T8.Properties.ReadOnly = True
        Me.T8.Size = New System.Drawing.Size(182, 28)
        Me.T8.TabIndex = 24
        '
        'btn5
        '
        Me.btn5.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn5.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn5.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn5.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn5.Appearance.Options.UseBackColor = True
        Me.btn5.Appearance.Options.UseBorderColor = True
        Me.btn5.Appearance.Options.UseFont = True
        Me.btn5.Appearance.Options.UseForeColor = True
        Me.btn5.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn5.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn5.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn5.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn5.AppearanceDisabled.Options.UseBackColor = True
        Me.btn5.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn5.AppearanceDisabled.Options.UseFont = True
        Me.btn5.AppearanceDisabled.Options.UseForeColor = True
        Me.btn5.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn5.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn5.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn5.AppearanceHovered.Options.UseBackColor = True
        Me.btn5.AppearanceHovered.Options.UseBorderColor = True
        Me.btn5.AppearanceHovered.Options.UseForeColor = True
        Me.btn5.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn5.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn5.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn5.AppearancePressed.Options.UseBackColor = True
        Me.btn5.AppearancePressed.Options.UseBorderColor = True
        Me.btn5.AppearancePressed.Options.UseForeColor = True
        Me.btn5.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn5.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn5.Location = New System.Drawing.Point(259, 160)
        Me.btn5.Margin = New System.Windows.Forms.Padding(4)
        Me.btn5.Name = "btn5"
        Me.btn5.Size = New System.Drawing.Size(25, 26)
        Me.btn5.TabIndex = 30
        '
        'T13
        '
        Me.T13.EditValue = "[-FakePhone-]"
        Me.T13.Location = New System.Drawing.Point(681, 358)
        Me.T13.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T13.Name = "T13"
        Me.T13.Properties.AllowFocused = False
        Me.T13.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T13.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T13.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T13.Properties.Appearance.Options.UseBackColor = True
        Me.T13.Properties.Appearance.Options.UseFont = True
        Me.T13.Properties.Appearance.Options.UseForeColor = True
        Me.T13.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T13.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T13.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T13.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T13.Properties.ReadOnly = True
        Me.T13.Size = New System.Drawing.Size(182, 28)
        Me.T13.TabIndex = 24
        '
        'btn18
        '
        Me.btn18.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn18.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn18.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn18.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn18.Appearance.Options.UseBackColor = True
        Me.btn18.Appearance.Options.UseBorderColor = True
        Me.btn18.Appearance.Options.UseFont = True
        Me.btn18.Appearance.Options.UseForeColor = True
        Me.btn18.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn18.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn18.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn18.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn18.AppearanceDisabled.Options.UseBackColor = True
        Me.btn18.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn18.AppearanceDisabled.Options.UseFont = True
        Me.btn18.AppearanceDisabled.Options.UseForeColor = True
        Me.btn18.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn18.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn18.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn18.AppearanceHovered.Options.UseBackColor = True
        Me.btn18.AppearanceHovered.Options.UseBorderColor = True
        Me.btn18.AppearanceHovered.Options.UseForeColor = True
        Me.btn18.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn18.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn18.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn18.AppearancePressed.Options.UseBackColor = True
        Me.btn18.AppearancePressed.Options.UseBorderColor = True
        Me.btn18.AppearancePressed.Options.UseForeColor = True
        Me.btn18.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn18.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn18.Location = New System.Drawing.Point(870, 199)
        Me.btn18.Margin = New System.Windows.Forms.Padding(4)
        Me.btn18.Name = "btn18"
        Me.btn18.Size = New System.Drawing.Size(25, 26)
        Me.btn18.TabIndex = 29
        '
        'T23
        '
        Me.T23.EditValue = "[-RN6-]"
        Me.T23.Location = New System.Drawing.Point(388, 200)
        Me.T23.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T23.Name = "T23"
        Me.T23.Properties.AllowFocused = False
        Me.T23.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T23.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T23.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T23.Properties.Appearance.Options.UseBackColor = True
        Me.T23.Properties.Appearance.Options.UseFont = True
        Me.T23.Properties.Appearance.Options.UseForeColor = True
        Me.T23.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T23.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T23.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T23.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T23.Properties.ReadOnly = True
        Me.T23.Size = New System.Drawing.Size(182, 28)
        Me.T23.TabIndex = 24
        '
        'btn4
        '
        Me.btn4.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn4.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn4.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn4.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn4.Appearance.Options.UseBackColor = True
        Me.btn4.Appearance.Options.UseBorderColor = True
        Me.btn4.Appearance.Options.UseFont = True
        Me.btn4.Appearance.Options.UseForeColor = True
        Me.btn4.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn4.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn4.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn4.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn4.AppearanceDisabled.Options.UseBackColor = True
        Me.btn4.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn4.AppearanceDisabled.Options.UseFont = True
        Me.btn4.AppearanceDisabled.Options.UseForeColor = True
        Me.btn4.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn4.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn4.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn4.AppearanceHovered.Options.UseBackColor = True
        Me.btn4.AppearanceHovered.Options.UseBorderColor = True
        Me.btn4.AppearanceHovered.Options.UseForeColor = True
        Me.btn4.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn4.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn4.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn4.AppearancePressed.Options.UseBackColor = True
        Me.btn4.AppearancePressed.Options.UseBorderColor = True
        Me.btn4.AppearancePressed.Options.UseForeColor = True
        Me.btn4.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn4.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn4.Location = New System.Drawing.Point(259, 121)
        Me.btn4.Margin = New System.Windows.Forms.Padding(4)
        Me.btn4.Name = "btn4"
        Me.btn4.Size = New System.Drawing.Size(25, 26)
        Me.btn4.TabIndex = 29
        '
        'T9
        '
        Me.T9.EditValue = "[-CompanyName-]"
        Me.T9.Location = New System.Drawing.Point(72, 315)
        Me.T9.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T9.Name = "T9"
        Me.T9.Properties.AllowFocused = False
        Me.T9.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T9.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T9.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T9.Properties.Appearance.Options.UseBackColor = True
        Me.T9.Properties.Appearance.Options.UseFont = True
        Me.T9.Properties.Appearance.Options.UseForeColor = True
        Me.T9.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T9.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T9.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T9.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T9.Properties.ReadOnly = True
        Me.T9.Size = New System.Drawing.Size(182, 28)
        Me.T9.TabIndex = 24
        '
        'btn17
        '
        Me.btn17.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn17.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn17.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn17.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn17.Appearance.Options.UseBackColor = True
        Me.btn17.Appearance.Options.UseBorderColor = True
        Me.btn17.Appearance.Options.UseFont = True
        Me.btn17.Appearance.Options.UseForeColor = True
        Me.btn17.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn17.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn17.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn17.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn17.AppearanceDisabled.Options.UseBackColor = True
        Me.btn17.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn17.AppearanceDisabled.Options.UseFont = True
        Me.btn17.AppearanceDisabled.Options.UseForeColor = True
        Me.btn17.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn17.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn17.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn17.AppearanceHovered.Options.UseBackColor = True
        Me.btn17.AppearanceHovered.Options.UseBorderColor = True
        Me.btn17.AppearanceHovered.Options.UseForeColor = True
        Me.btn17.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn17.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn17.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn17.AppearancePressed.Options.UseBackColor = True
        Me.btn17.AppearancePressed.Options.UseBorderColor = True
        Me.btn17.AppearancePressed.Options.UseForeColor = True
        Me.btn17.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn17.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn17.Location = New System.Drawing.Point(870, 157)
        Me.btn17.Margin = New System.Windows.Forms.Padding(4)
        Me.btn17.Name = "btn17"
        Me.btn17.Size = New System.Drawing.Size(25, 26)
        Me.btn17.TabIndex = 28
        '
        'T14
        '
        Me.T14.EditValue = "[-FakeEmail-]"
        Me.T14.Location = New System.Drawing.Point(72, 510)
        Me.T14.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T14.Name = "T14"
        Me.T14.Properties.AllowFocused = False
        Me.T14.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T14.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T14.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T14.Properties.Appearance.Options.UseBackColor = True
        Me.T14.Properties.Appearance.Options.UseFont = True
        Me.T14.Properties.Appearance.Options.UseForeColor = True
        Me.T14.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T14.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T14.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T14.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T14.Properties.ReadOnly = True
        Me.T14.Size = New System.Drawing.Size(182, 28)
        Me.T14.TabIndex = 24
        '
        'btn3
        '
        Me.btn3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn3.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn3.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn3.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn3.Appearance.Options.UseBackColor = True
        Me.btn3.Appearance.Options.UseBorderColor = True
        Me.btn3.Appearance.Options.UseFont = True
        Me.btn3.Appearance.Options.UseForeColor = True
        Me.btn3.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn3.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn3.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn3.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn3.AppearanceDisabled.Options.UseBackColor = True
        Me.btn3.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn3.AppearanceDisabled.Options.UseFont = True
        Me.btn3.AppearanceDisabled.Options.UseForeColor = True
        Me.btn3.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn3.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn3.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn3.AppearanceHovered.Options.UseBackColor = True
        Me.btn3.AppearanceHovered.Options.UseBorderColor = True
        Me.btn3.AppearanceHovered.Options.UseForeColor = True
        Me.btn3.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn3.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn3.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn3.AppearancePressed.Options.UseBackColor = True
        Me.btn3.AppearancePressed.Options.UseBorderColor = True
        Me.btn3.AppearancePressed.Options.UseForeColor = True
        Me.btn3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn3.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn3.Location = New System.Drawing.Point(259, 82)
        Me.btn3.Margin = New System.Windows.Forms.Padding(4)
        Me.btn3.Name = "btn3"
        Me.btn3.Size = New System.Drawing.Size(25, 26)
        Me.btn3.TabIndex = 28
        '
        'T24
        '
        Me.T24.EditValue = "[-RN7-]"
        Me.T24.Location = New System.Drawing.Point(388, 239)
        Me.T24.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T24.Name = "T24"
        Me.T24.Properties.AllowFocused = False
        Me.T24.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T24.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T24.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T24.Properties.Appearance.Options.UseBackColor = True
        Me.T24.Properties.Appearance.Options.UseFont = True
        Me.T24.Properties.Appearance.Options.UseForeColor = True
        Me.T24.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T24.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T24.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T24.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T24.Properties.ReadOnly = True
        Me.T24.Size = New System.Drawing.Size(182, 28)
        Me.T24.TabIndex = 24
        '
        'btn16
        '
        Me.btn16.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn16.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn16.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn16.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn16.Appearance.Options.UseBackColor = True
        Me.btn16.Appearance.Options.UseBorderColor = True
        Me.btn16.Appearance.Options.UseFont = True
        Me.btn16.Appearance.Options.UseForeColor = True
        Me.btn16.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn16.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn16.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn16.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn16.AppearanceDisabled.Options.UseBackColor = True
        Me.btn16.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn16.AppearanceDisabled.Options.UseFont = True
        Me.btn16.AppearanceDisabled.Options.UseForeColor = True
        Me.btn16.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn16.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn16.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn16.AppearanceHovered.Options.UseBackColor = True
        Me.btn16.AppearanceHovered.Options.UseBorderColor = True
        Me.btn16.AppearanceHovered.Options.UseForeColor = True
        Me.btn16.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn16.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn16.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn16.AppearancePressed.Options.UseBackColor = True
        Me.btn16.AppearancePressed.Options.UseBorderColor = True
        Me.btn16.AppearancePressed.Options.UseForeColor = True
        Me.btn16.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn16.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn16.Location = New System.Drawing.Point(870, 121)
        Me.btn16.Margin = New System.Windows.Forms.Padding(4)
        Me.btn16.Name = "btn16"
        Me.btn16.Size = New System.Drawing.Size(25, 26)
        Me.btn16.TabIndex = 27
        '
        'T10
        '
        Me.T10.EditValue = "[-Date-]"
        Me.T10.Location = New System.Drawing.Point(72, 354)
        Me.T10.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T10.Name = "T10"
        Me.T10.Properties.AllowFocused = False
        Me.T10.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T10.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T10.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T10.Properties.Appearance.Options.UseBackColor = True
        Me.T10.Properties.Appearance.Options.UseFont = True
        Me.T10.Properties.Appearance.Options.UseForeColor = True
        Me.T10.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T10.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T10.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T10.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T10.Properties.ReadOnly = True
        Me.T10.Size = New System.Drawing.Size(182, 28)
        Me.T10.TabIndex = 24
        '
        'btn2
        '
        Me.btn2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn2.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn2.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn2.Appearance.Options.UseBackColor = True
        Me.btn2.Appearance.Options.UseBorderColor = True
        Me.btn2.Appearance.Options.UseFont = True
        Me.btn2.Appearance.Options.UseForeColor = True
        Me.btn2.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn2.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn2.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn2.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn2.AppearanceDisabled.Options.UseBackColor = True
        Me.btn2.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn2.AppearanceDisabled.Options.UseFont = True
        Me.btn2.AppearanceDisabled.Options.UseForeColor = True
        Me.btn2.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn2.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn2.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn2.AppearanceHovered.Options.UseBackColor = True
        Me.btn2.AppearanceHovered.Options.UseBorderColor = True
        Me.btn2.AppearanceHovered.Options.UseForeColor = True
        Me.btn2.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn2.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn2.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn2.AppearancePressed.Options.UseBackColor = True
        Me.btn2.AppearancePressed.Options.UseBorderColor = True
        Me.btn2.AppearancePressed.Options.UseForeColor = True
        Me.btn2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn2.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn2.Location = New System.Drawing.Point(259, 43)
        Me.btn2.Margin = New System.Windows.Forms.Padding(4)
        Me.btn2.Name = "btn2"
        Me.btn2.Size = New System.Drawing.Size(25, 26)
        Me.btn2.TabIndex = 27
        '
        'T27
        '
        Me.T27.EditValue = "[-NewYork-]"
        Me.T27.Location = New System.Drawing.Point(388, 511)
        Me.T27.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T27.Name = "T27"
        Me.T27.Properties.AllowFocused = False
        Me.T27.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T27.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T27.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T27.Properties.Appearance.Options.UseBackColor = True
        Me.T27.Properties.Appearance.Options.UseFont = True
        Me.T27.Properties.Appearance.Options.UseForeColor = True
        Me.T27.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T27.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T27.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T27.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T27.Properties.ReadOnly = True
        Me.T27.Size = New System.Drawing.Size(182, 28)
        Me.T27.TabIndex = 24
        '
        'btn15
        '
        Me.btn15.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn15.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn15.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn15.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn15.Appearance.Options.UseBackColor = True
        Me.btn15.Appearance.Options.UseBorderColor = True
        Me.btn15.Appearance.Options.UseFont = True
        Me.btn15.Appearance.Options.UseForeColor = True
        Me.btn15.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn15.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn15.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn15.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn15.AppearanceDisabled.Options.UseBackColor = True
        Me.btn15.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn15.AppearanceDisabled.Options.UseFont = True
        Me.btn15.AppearanceDisabled.Options.UseForeColor = True
        Me.btn15.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn15.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn15.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn15.AppearanceHovered.Options.UseBackColor = True
        Me.btn15.AppearanceHovered.Options.UseBorderColor = True
        Me.btn15.AppearanceHovered.Options.UseForeColor = True
        Me.btn15.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn15.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn15.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn15.AppearancePressed.Options.UseBackColor = True
        Me.btn15.AppearancePressed.Options.UseBorderColor = True
        Me.btn15.AppearancePressed.Options.UseForeColor = True
        Me.btn15.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn15.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn15.Location = New System.Drawing.Point(870, 82)
        Me.btn15.Margin = New System.Windows.Forms.Padding(4)
        Me.btn15.Name = "btn15"
        Me.btn15.Size = New System.Drawing.Size(25, 26)
        Me.btn15.TabIndex = 26
        '
        'T11
        '
        Me.T11.EditValue = "[-Time-]"
        Me.T11.Location = New System.Drawing.Point(72, 393)
        Me.T11.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T11.Name = "T11"
        Me.T11.Properties.AllowFocused = False
        Me.T11.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T11.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.T11.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T11.Properties.Appearance.Options.UseBackColor = True
        Me.T11.Properties.Appearance.Options.UseFont = True
        Me.T11.Properties.Appearance.Options.UseForeColor = True
        Me.T11.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T11.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T11.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T11.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T11.Properties.ReadOnly = True
        Me.T11.Size = New System.Drawing.Size(182, 28)
        Me.T11.TabIndex = 24
        '
        'btn1
        '
        Me.btn1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn1.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn1.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn1.Appearance.Options.UseBackColor = True
        Me.btn1.Appearance.Options.UseBorderColor = True
        Me.btn1.Appearance.Options.UseFont = True
        Me.btn1.Appearance.Options.UseForeColor = True
        Me.btn1.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn1.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn1.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn1.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn1.AppearanceDisabled.Options.UseBackColor = True
        Me.btn1.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn1.AppearanceDisabled.Options.UseFont = True
        Me.btn1.AppearanceDisabled.Options.UseForeColor = True
        Me.btn1.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn1.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn1.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn1.AppearanceHovered.Options.UseBackColor = True
        Me.btn1.AppearanceHovered.Options.UseBorderColor = True
        Me.btn1.AppearanceHovered.Options.UseForeColor = True
        Me.btn1.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn1.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn1.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn1.AppearancePressed.Options.UseBackColor = True
        Me.btn1.AppearancePressed.Options.UseBorderColor = True
        Me.btn1.AppearancePressed.Options.UseForeColor = True
        Me.btn1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn1.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn1.Location = New System.Drawing.Point(259, 4)
        Me.btn1.Margin = New System.Windows.Forms.Padding(4)
        Me.btn1.Name = "btn1"
        Me.btn1.Size = New System.Drawing.Size(25, 26)
        Me.btn1.TabIndex = 26
        Me.btn1.Text = "Copy"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(45, 7)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(19, 18)
        Me.Label1.TabIndex = 25
        Me.Label1.Text = "1 -"
        '
        'Label20
        '
        Me.Label20.AutoSize = True
        Me.Label20.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label20.Location = New System.Drawing.Point(355, 361)
        Me.Label20.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(28, 18)
        Me.Label20.TabIndex = 25
        Me.Label20.Text = "24 -"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label2.Location = New System.Drawing.Point(44, 46)
        Me.Label2.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(21, 18)
        Me.Label2.TabIndex = 25
        Me.Label2.Text = "2 -"
        '
        'Label19
        '
        Me.Label19.AutoSize = True
        Me.Label19.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label19.Location = New System.Drawing.Point(356, 322)
        Me.Label19.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(27, 18)
        Me.Label19.TabIndex = 25
        Me.Label19.Text = "23 -"
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label11.Location = New System.Drawing.Point(356, 10)
        Me.Label11.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(26, 18)
        Me.Label11.TabIndex = 25
        Me.Label11.Text = "15 -"
        '
        'Label22
        '
        Me.Label22.AutoSize = True
        Me.Label22.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label22.Location = New System.Drawing.Point(42, 436)
        Me.Label22.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(25, 18)
        Me.Label22.TabIndex = 25
        Me.Label22.Text = "12 -"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label3.Location = New System.Drawing.Point(44, 85)
        Me.Label3.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(21, 18)
        Me.Label3.TabIndex = 25
        Me.Label3.Text = "3 -"
        '
        'Label27
        '
        Me.Label27.AutoSize = True
        Me.Label27.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label27.Location = New System.Drawing.Point(355, 516)
        Me.Label27.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label27.Name = "Label27"
        Me.Label27.Size = New System.Drawing.Size(28, 18)
        Me.Label27.TabIndex = 25
        Me.Label27.Text = "28 -"
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label12.Location = New System.Drawing.Point(357, 49)
        Me.Label12.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(25, 18)
        Me.Label12.TabIndex = 25
        Me.Label12.Text = "16 -"
        '
        'Label21
        '
        Me.Label21.AutoSize = True
        Me.Label21.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label21.Location = New System.Drawing.Point(43, 397)
        Me.Label21.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(23, 18)
        Me.Label21.TabIndex = 25
        Me.Label21.Text = "11 -"
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label4.Location = New System.Drawing.Point(43, 124)
        Me.Label4.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(22, 18)
        Me.Label4.TabIndex = 25
        Me.Label4.Text = "4 -"
        '
        'Panel1
        '
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.Label28)
        Me.Panel1.Controls.Add(Me.Label39)
        Me.Panel1.Controls.Add(Me.T38)
        Me.Panel1.Controls.Add(Me.T37)
        Me.Panel1.Controls.Add(Me.T1)
        Me.Panel1.Controls.Add(Me.Label31)
        Me.Panel1.Controls.Add(Me.btn1)
        Me.Panel1.Controls.Add(Me.btn35)
        Me.Panel1.Controls.Add(Me.T31)
        Me.Panel1.Controls.Add(Me.Label32)
        Me.Panel1.Controls.Add(Me.btn29)
        Me.Panel1.Controls.Add(Me.btn36)
        Me.Panel1.Controls.Add(Me.Label33)
        Me.Panel1.Controls.Add(Me.Label34)
        Me.Panel1.Controls.Add(Me.Label35)
        Me.Panel1.Controls.Add(Me.Label36)
        Me.Panel1.Controls.Add(Me.Label37)
        Me.Panel1.Controls.Add(Me.Label38)
        Me.Panel1.Controls.Add(Me.btn37)
        Me.Panel1.Controls.Add(Me.T39)
        Me.Panel1.Controls.Add(Me.btn34)
        Me.Panel1.Controls.Add(Me.T36)
        Me.Panel1.Controls.Add(Me.btn33)
        Me.Panel1.Controls.Add(Me.T35)
        Me.Panel1.Controls.Add(Me.btn32)
        Me.Panel1.Controls.Add(Me.btn31)
        Me.Panel1.Controls.Add(Me.btn30)
        Me.Panel1.Controls.Add(Me.T34)
        Me.Panel1.Controls.Add(Me.T32)
        Me.Panel1.Controls.Add(Me.T33)
        Me.Panel1.Controls.Add(Me.SimpleButton1)
        Me.Panel1.Controls.Add(Me.T29)
        Me.Panel1.Controls.Add(Me.Label30)
        Me.Panel1.Controls.Add(Me.SeparatorControl1)
        Me.Panel1.Controls.Add(Me.btn28)
        Me.Panel1.Controls.Add(Me.T15)
        Me.Panel1.Controls.Add(Me.btn14)
        Me.Panel1.Controls.Add(Me.T2)
        Me.Panel1.Controls.Add(Me.btn27)
        Me.Panel1.Controls.Add(Me.T16)
        Me.Panel1.Controls.Add(Me.btn13)
        Me.Panel1.Controls.Add(Me.T3)
        Me.Panel1.Controls.Add(Me.btn23)
        Me.Panel1.Controls.Add(Me.T17)
        Me.Panel1.Controls.Add(Me.btn9)
        Me.Panel1.Controls.Add(Me.T4)
        Me.Panel1.Controls.Add(Me.btn26)
        Me.Panel1.Controls.Add(Me.T18)
        Me.Panel1.Controls.Add(Me.btn12)
        Me.Panel1.Controls.Add(Me.T5)
        Me.Panel1.Controls.Add(Me.btn22)
        Me.Panel1.Controls.Add(Me.T19)
        Me.Panel1.Controls.Add(Me.btn8)
        Me.Panel1.Controls.Add(Me.T6)
        Me.Panel1.Controls.Add(Me.btn25)
        Me.Panel1.Controls.Add(Me.T12)
        Me.Panel1.Controls.Add(Me.btn11)
        Me.Panel1.Controls.Add(Me.T20)
        Me.Panel1.Controls.Add(Me.btn21)
        Me.Panel1.Controls.Add(Me.T28)
        Me.Panel1.Controls.Add(Me.btn7)
        Me.Panel1.Controls.Add(Me.T26)
        Me.Panel1.Controls.Add(Me.btn24)
        Me.Panel1.Controls.Add(Me.T21)
        Me.Panel1.Controls.Add(Me.btn10)
        Me.Panel1.Controls.Add(Me.T7)
        Me.Panel1.Controls.Add(Me.btn20)
        Me.Panel1.Controls.Add(Me.T25)
        Me.Panel1.Controls.Add(Me.btn6)
        Me.Panel1.Controls.Add(Me.T22)
        Me.Panel1.Controls.Add(Me.btn19)
        Me.Panel1.Controls.Add(Me.T8)
        Me.Panel1.Controls.Add(Me.btn5)
        Me.Panel1.Controls.Add(Me.T13)
        Me.Panel1.Controls.Add(Me.btn18)
        Me.Panel1.Controls.Add(Me.T23)
        Me.Panel1.Controls.Add(Me.btn4)
        Me.Panel1.Controls.Add(Me.T9)
        Me.Panel1.Controls.Add(Me.btn17)
        Me.Panel1.Controls.Add(Me.T14)
        Me.Panel1.Controls.Add(Me.btn3)
        Me.Panel1.Controls.Add(Me.T24)
        Me.Panel1.Controls.Add(Me.btn16)
        Me.Panel1.Controls.Add(Me.T10)
        Me.Panel1.Controls.Add(Me.btn2)
        Me.Panel1.Controls.Add(Me.T27)
        Me.Panel1.Controls.Add(Me.btn15)
        Me.Panel1.Controls.Add(Me.T11)
        Me.Panel1.Controls.Add(Me.Label1)
        Me.Panel1.Controls.Add(Me.Label20)
        Me.Panel1.Controls.Add(Me.Label2)
        Me.Panel1.Controls.Add(Me.Label19)
        Me.Panel1.Controls.Add(Me.Label11)
        Me.Panel1.Controls.Add(Me.Label22)
        Me.Panel1.Controls.Add(Me.Label3)
        Me.Panel1.Controls.Add(Me.Label27)
        Me.Panel1.Controls.Add(Me.Label12)
        Me.Panel1.Controls.Add(Me.Label21)
        Me.Panel1.Controls.Add(Me.Label4)
        Me.Panel1.Controls.Add(Me.Label26)
        Me.Panel1.Controls.Add(Me.Label13)
        Me.Panel1.Controls.Add(Me.Label10)
        Me.Panel1.Controls.Add(Me.Label5)
        Me.Panel1.Controls.Add(Me.Label18)
        Me.Panel1.Controls.Add(Me.Label14)
        Me.Panel1.Controls.Add(Me.Label25)
        Me.Panel1.Controls.Add(Me.Label6)
        Me.Panel1.Controls.Add(Me.Label9)
        Me.Panel1.Controls.Add(Me.Label29)
        Me.Panel1.Controls.Add(Me.Label17)
        Me.Panel1.Controls.Add(Me.Label15)
        Me.Panel1.Controls.Add(Me.Label24)
        Me.Panel1.Controls.Add(Me.Label7)
        Me.Panel1.Controls.Add(Me.Label8)
        Me.Panel1.Controls.Add(Me.Label23)
        Me.Panel1.Controls.Add(Me.Label16)
        Me.Panel1.Location = New System.Drawing.Point(16, 16)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(940, 570)
        Me.Panel1.TabIndex = 301
        '
        'Label28
        '
        Me.Label28.AutoSize = True
        Me.Label28.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label28.Location = New System.Drawing.Point(628, 404)
        Me.Label28.Name = "Label28"
        Me.Label28.Size = New System.Drawing.Size(0, 21)
        Me.Label28.TabIndex = 186
        '
        'Label39
        '
        Me.Label39.AutoSize = True
        Me.Label39.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label39.Location = New System.Drawing.Point(644, 322)
        Me.Label39.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label39.Name = "Label39"
        Me.Label39.Size = New System.Drawing.Size(28, 18)
        Me.Label39.TabIndex = 184
        Me.Label39.Text = "38 -"
        '
        'T38
        '
        Me.T38.EditValue = "[-RCh2-]"
        Me.T38.Location = New System.Drawing.Point(681, 43)
        Me.T38.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T38.Name = "T38"
        Me.T38.Properties.AllowFocused = False
        Me.T38.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T38.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T38.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T38.Properties.Appearance.Options.UseBackColor = True
        Me.T38.Properties.Appearance.Options.UseFont = True
        Me.T38.Properties.Appearance.Options.UseForeColor = True
        Me.T38.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T38.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T38.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T38.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T38.Properties.ReadOnly = True
        Me.T38.Size = New System.Drawing.Size(182, 28)
        Me.T38.TabIndex = 172
        '
        'T37
        '
        Me.T37.EditValue = "[-RCh1-]"
        Me.T37.Location = New System.Drawing.Point(680, 9)
        Me.T37.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T37.Name = "T37"
        Me.T37.Properties.AllowFocused = False
        Me.T37.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T37.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T37.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T37.Properties.Appearance.Options.UseBackColor = True
        Me.T37.Properties.Appearance.Options.UseFont = True
        Me.T37.Properties.Appearance.Options.UseForeColor = True
        Me.T37.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T37.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T37.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T37.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T37.Properties.ReadOnly = True
        Me.T37.Size = New System.Drawing.Size(182, 28)
        Me.T37.TabIndex = 169
        '
        'Label31
        '
        Me.Label31.AutoSize = True
        Me.Label31.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label31.Location = New System.Drawing.Point(645, 12)
        Me.Label31.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label31.Name = "Label31"
        Me.Label31.Size = New System.Drawing.Size(27, 18)
        Me.Label31.TabIndex = 176
        Me.Label31.Text = "30 -"
        '
        'btn35
        '
        Me.btn35.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn35.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn35.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn35.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn35.Appearance.Options.UseBackColor = True
        Me.btn35.Appearance.Options.UseBorderColor = True
        Me.btn35.Appearance.Options.UseFont = True
        Me.btn35.Appearance.Options.UseForeColor = True
        Me.btn35.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn35.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn35.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn35.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn35.AppearanceDisabled.Options.UseBackColor = True
        Me.btn35.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn35.AppearanceDisabled.Options.UseFont = True
        Me.btn35.AppearanceDisabled.Options.UseForeColor = True
        Me.btn35.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn35.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn35.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn35.AppearanceHovered.Options.UseBackColor = True
        Me.btn35.AppearanceHovered.Options.UseBorderColor = True
        Me.btn35.AppearanceHovered.Options.UseForeColor = True
        Me.btn35.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn35.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn35.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn35.AppearancePressed.Options.UseBackColor = True
        Me.btn35.AppearancePressed.Options.UseBorderColor = True
        Me.btn35.AppearancePressed.Options.UseForeColor = True
        Me.btn35.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn35.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn35.Location = New System.Drawing.Point(869, 10)
        Me.btn35.Margin = New System.Windows.Forms.Padding(4)
        Me.btn35.Name = "btn35"
        Me.btn35.Size = New System.Drawing.Size(25, 26)
        Me.btn35.TabIndex = 171
        Me.btn35.Text = "Copy"
        '
        'T31
        '
        Me.T31.EditValue = "[-RN1-]"
        Me.T31.Location = New System.Drawing.Point(388, 4)
        Me.T31.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T31.Name = "T31"
        Me.T31.Properties.AllowFocused = False
        Me.T31.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T31.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T31.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T31.Properties.Appearance.Options.UseBackColor = True
        Me.T31.Properties.Appearance.Options.UseFont = True
        Me.T31.Properties.Appearance.Options.UseForeColor = True
        Me.T31.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T31.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T31.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T31.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T31.Properties.ReadOnly = True
        Me.T31.Size = New System.Drawing.Size(182, 28)
        Me.T31.TabIndex = 158
        '
        'Label32
        '
        Me.Label32.AutoSize = True
        Me.Label32.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label32.Location = New System.Drawing.Point(646, 48)
        Me.Label32.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label32.Name = "Label32"
        Me.Label32.Size = New System.Drawing.Size(25, 18)
        Me.Label32.TabIndex = 177
        Me.Label32.Text = "31 -"
        '
        'btn29
        '
        Me.btn29.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn29.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn29.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn29.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn29.Appearance.Options.UseBackColor = True
        Me.btn29.Appearance.Options.UseBorderColor = True
        Me.btn29.Appearance.Options.UseFont = True
        Me.btn29.Appearance.Options.UseForeColor = True
        Me.btn29.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn29.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn29.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn29.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn29.AppearanceDisabled.Options.UseBackColor = True
        Me.btn29.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn29.AppearanceDisabled.Options.UseFont = True
        Me.btn29.AppearanceDisabled.Options.UseForeColor = True
        Me.btn29.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn29.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn29.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn29.AppearanceHovered.Options.UseBackColor = True
        Me.btn29.AppearanceHovered.Options.UseBorderColor = True
        Me.btn29.AppearanceHovered.Options.UseForeColor = True
        Me.btn29.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn29.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn29.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn29.AppearancePressed.Options.UseBackColor = True
        Me.btn29.AppearancePressed.Options.UseBorderColor = True
        Me.btn29.AppearancePressed.Options.UseForeColor = True
        Me.btn29.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn29.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn29.Location = New System.Drawing.Point(578, 5)
        Me.btn29.Margin = New System.Windows.Forms.Padding(4)
        Me.btn29.Name = "btn29"
        Me.btn29.Size = New System.Drawing.Size(25, 26)
        Me.btn29.TabIndex = 162
        Me.btn29.Text = "Copy"
        '
        'btn36
        '
        Me.btn36.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn36.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn36.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn36.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn36.Appearance.Options.UseBackColor = True
        Me.btn36.Appearance.Options.UseBorderColor = True
        Me.btn36.Appearance.Options.UseFont = True
        Me.btn36.Appearance.Options.UseForeColor = True
        Me.btn36.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn36.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn36.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn36.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn36.AppearanceDisabled.Options.UseBackColor = True
        Me.btn36.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn36.AppearanceDisabled.Options.UseFont = True
        Me.btn36.AppearanceDisabled.Options.UseForeColor = True
        Me.btn36.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn36.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn36.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn36.AppearanceHovered.Options.UseBackColor = True
        Me.btn36.AppearanceHovered.Options.UseBorderColor = True
        Me.btn36.AppearanceHovered.Options.UseForeColor = True
        Me.btn36.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn36.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn36.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn36.AppearancePressed.Options.UseBackColor = True
        Me.btn36.AppearancePressed.Options.UseBorderColor = True
        Me.btn36.AppearancePressed.Options.UseForeColor = True
        Me.btn36.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn36.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn36.Location = New System.Drawing.Point(869, 44)
        Me.btn36.Margin = New System.Windows.Forms.Padding(4)
        Me.btn36.Name = "btn36"
        Me.btn36.Size = New System.Drawing.Size(25, 26)
        Me.btn36.TabIndex = 173
        '
        'Label33
        '
        Me.Label33.AutoSize = True
        Me.Label33.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label33.Location = New System.Drawing.Point(645, 86)
        Me.Label33.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label33.Name = "Label33"
        Me.Label33.Size = New System.Drawing.Size(27, 18)
        Me.Label33.TabIndex = 178
        Me.Label33.Text = "32 -"
        '
        'Label34
        '
        Me.Label34.AutoSize = True
        Me.Label34.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label34.Location = New System.Drawing.Point(645, 281)
        Me.Label34.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label34.Name = "Label34"
        Me.Label34.Size = New System.Drawing.Size(27, 18)
        Me.Label34.TabIndex = 179
        Me.Label34.Text = "37 -"
        '
        'Label35
        '
        Me.Label35.AutoSize = True
        Me.Label35.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label35.Location = New System.Drawing.Point(645, 125)
        Me.Label35.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label35.Name = "Label35"
        Me.Label35.Size = New System.Drawing.Size(27, 18)
        Me.Label35.TabIndex = 180
        Me.Label35.Text = "33 -"
        '
        'Label36
        '
        Me.Label36.AutoSize = True
        Me.Label36.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label36.Location = New System.Drawing.Point(645, 242)
        Me.Label36.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label36.Name = "Label36"
        Me.Label36.Size = New System.Drawing.Size(27, 18)
        Me.Label36.TabIndex = 181
        Me.Label36.Text = "36 -"
        '
        'Label37
        '
        Me.Label37.AutoSize = True
        Me.Label37.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label37.Location = New System.Drawing.Point(644, 161)
        Me.Label37.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label37.Name = "Label37"
        Me.Label37.Size = New System.Drawing.Size(28, 18)
        Me.Label37.TabIndex = 182
        Me.Label37.Text = "34 -"
        '
        'Label38
        '
        Me.Label38.AutoSize = True
        Me.Label38.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label38.Location = New System.Drawing.Point(644, 203)
        Me.Label38.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label38.Name = "Label38"
        Me.Label38.Size = New System.Drawing.Size(28, 18)
        Me.Label38.TabIndex = 183
        Me.Label38.Text = "35 -"
        '
        'btn37
        '
        Me.btn37.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn37.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn37.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn37.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn37.Appearance.Options.UseBackColor = True
        Me.btn37.Appearance.Options.UseBorderColor = True
        Me.btn37.Appearance.Options.UseFont = True
        Me.btn37.Appearance.Options.UseForeColor = True
        Me.btn37.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn37.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn37.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn37.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn37.AppearanceDisabled.Options.UseBackColor = True
        Me.btn37.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn37.AppearanceDisabled.Options.UseFont = True
        Me.btn37.AppearanceDisabled.Options.UseForeColor = True
        Me.btn37.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn37.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn37.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn37.AppearanceHovered.Options.UseBackColor = True
        Me.btn37.AppearanceHovered.Options.UseBorderColor = True
        Me.btn37.AppearanceHovered.Options.UseForeColor = True
        Me.btn37.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn37.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn37.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn37.AppearancePressed.Options.UseBackColor = True
        Me.btn37.AppearancePressed.Options.UseBorderColor = True
        Me.btn37.AppearancePressed.Options.UseForeColor = True
        Me.btn37.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn37.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn37.Location = New System.Drawing.Point(870, 277)
        Me.btn37.Margin = New System.Windows.Forms.Padding(4)
        Me.btn37.Name = "btn37"
        Me.btn37.Size = New System.Drawing.Size(25, 26)
        Me.btn37.TabIndex = 175
        '
        'T39
        '
        Me.T39.EditValue = "[-RCh8-]"
        Me.T39.Location = New System.Drawing.Point(681, 276)
        Me.T39.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T39.Name = "T39"
        Me.T39.Properties.AllowFocused = False
        Me.T39.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T39.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T39.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T39.Properties.Appearance.Options.UseBackColor = True
        Me.T39.Properties.Appearance.Options.UseFont = True
        Me.T39.Properties.Appearance.Options.UseForeColor = True
        Me.T39.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T39.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T39.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T39.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T39.Properties.ReadOnly = True
        Me.T39.Size = New System.Drawing.Size(182, 28)
        Me.T39.TabIndex = 174
        '
        'btn34
        '
        Me.btn34.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn34.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn34.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn34.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn34.Appearance.Options.UseBackColor = True
        Me.btn34.Appearance.Options.UseBorderColor = True
        Me.btn34.Appearance.Options.UseFont = True
        Me.btn34.Appearance.Options.UseForeColor = True
        Me.btn34.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn34.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn34.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn34.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn34.AppearanceDisabled.Options.UseBackColor = True
        Me.btn34.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn34.AppearanceDisabled.Options.UseFont = True
        Me.btn34.AppearanceDisabled.Options.UseForeColor = True
        Me.btn34.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn34.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn34.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn34.AppearanceHovered.Options.UseBackColor = True
        Me.btn34.AppearanceHovered.Options.UseBorderColor = True
        Me.btn34.AppearanceHovered.Options.UseForeColor = True
        Me.btn34.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn34.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn34.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn34.AppearancePressed.Options.UseBackColor = True
        Me.btn34.AppearancePressed.Options.UseBorderColor = True
        Me.btn34.AppearancePressed.Options.UseForeColor = True
        Me.btn34.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn34.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn34.Location = New System.Drawing.Point(576, 396)
        Me.btn34.Margin = New System.Windows.Forms.Padding(4)
        Me.btn34.Name = "btn34"
        Me.btn34.Size = New System.Drawing.Size(25, 26)
        Me.btn34.TabIndex = 170
        '
        'T36
        '
        Me.T36.EditValue = "[-RN12-]"
        Me.T36.Location = New System.Drawing.Point(388, 395)
        Me.T36.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T36.Name = "T36"
        Me.T36.Properties.AllowFocused = False
        Me.T36.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T36.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T36.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T36.Properties.Appearance.Options.UseBackColor = True
        Me.T36.Properties.Appearance.Options.UseFont = True
        Me.T36.Properties.Appearance.Options.UseForeColor = True
        Me.T36.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T36.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T36.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T36.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T36.Properties.ReadOnly = True
        Me.T36.Size = New System.Drawing.Size(182, 28)
        Me.T36.TabIndex = 168
        '
        'btn33
        '
        Me.btn33.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn33.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn33.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn33.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn33.Appearance.Options.UseBackColor = True
        Me.btn33.Appearance.Options.UseBorderColor = True
        Me.btn33.Appearance.Options.UseFont = True
        Me.btn33.Appearance.Options.UseForeColor = True
        Me.btn33.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn33.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn33.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn33.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn33.AppearanceDisabled.Options.UseBackColor = True
        Me.btn33.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn33.AppearanceDisabled.Options.UseFont = True
        Me.btn33.AppearanceDisabled.Options.UseForeColor = True
        Me.btn33.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn33.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn33.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn33.AppearanceHovered.Options.UseBackColor = True
        Me.btn33.AppearanceHovered.Options.UseBorderColor = True
        Me.btn33.AppearanceHovered.Options.UseForeColor = True
        Me.btn33.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn33.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn33.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn33.AppearancePressed.Options.UseBackColor = True
        Me.btn33.AppearancePressed.Options.UseBorderColor = True
        Me.btn33.AppearancePressed.Options.UseForeColor = True
        Me.btn33.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn33.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn33.Location = New System.Drawing.Point(576, 357)
        Me.btn33.Margin = New System.Windows.Forms.Padding(4)
        Me.btn33.Name = "btn33"
        Me.btn33.Size = New System.Drawing.Size(25, 26)
        Me.btn33.TabIndex = 167
        '
        'T35
        '
        Me.T35.EditValue = "[-RN10-]"
        Me.T35.Location = New System.Drawing.Point(388, 356)
        Me.T35.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T35.Name = "T35"
        Me.T35.Properties.AllowFocused = False
        Me.T35.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T35.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T35.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T35.Properties.Appearance.Options.UseBackColor = True
        Me.T35.Properties.Appearance.Options.UseFont = True
        Me.T35.Properties.Appearance.Options.UseForeColor = True
        Me.T35.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T35.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T35.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T35.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T35.Properties.ReadOnly = True
        Me.T35.Size = New System.Drawing.Size(182, 28)
        Me.T35.TabIndex = 166
        '
        'btn32
        '
        Me.btn32.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn32.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn32.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn32.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn32.Appearance.Options.UseBackColor = True
        Me.btn32.Appearance.Options.UseBorderColor = True
        Me.btn32.Appearance.Options.UseFont = True
        Me.btn32.Appearance.Options.UseForeColor = True
        Me.btn32.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn32.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn32.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn32.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn32.AppearanceDisabled.Options.UseBackColor = True
        Me.btn32.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn32.AppearanceDisabled.Options.UseFont = True
        Me.btn32.AppearanceDisabled.Options.UseForeColor = True
        Me.btn32.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn32.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn32.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn32.AppearanceHovered.Options.UseBackColor = True
        Me.btn32.AppearanceHovered.Options.UseBorderColor = True
        Me.btn32.AppearanceHovered.Options.UseForeColor = True
        Me.btn32.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn32.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn32.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn32.AppearancePressed.Options.UseBackColor = True
        Me.btn32.AppearancePressed.Options.UseBorderColor = True
        Me.btn32.AppearancePressed.Options.UseForeColor = True
        Me.btn32.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn32.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn32.Location = New System.Drawing.Point(576, 318)
        Me.btn32.Margin = New System.Windows.Forms.Padding(4)
        Me.btn32.Name = "btn32"
        Me.btn32.Size = New System.Drawing.Size(25, 26)
        Me.btn32.TabIndex = 165
        '
        'btn31
        '
        Me.btn31.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn31.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn31.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn31.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn31.Appearance.Options.UseBackColor = True
        Me.btn31.Appearance.Options.UseBorderColor = True
        Me.btn31.Appearance.Options.UseFont = True
        Me.btn31.Appearance.Options.UseForeColor = True
        Me.btn31.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn31.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn31.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn31.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn31.AppearanceDisabled.Options.UseBackColor = True
        Me.btn31.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn31.AppearanceDisabled.Options.UseFont = True
        Me.btn31.AppearanceDisabled.Options.UseForeColor = True
        Me.btn31.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn31.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn31.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn31.AppearanceHovered.Options.UseBackColor = True
        Me.btn31.AppearanceHovered.Options.UseBorderColor = True
        Me.btn31.AppearanceHovered.Options.UseForeColor = True
        Me.btn31.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn31.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn31.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn31.AppearancePressed.Options.UseBackColor = True
        Me.btn31.AppearancePressed.Options.UseBorderColor = True
        Me.btn31.AppearancePressed.Options.UseForeColor = True
        Me.btn31.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn31.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn31.Location = New System.Drawing.Point(576, 279)
        Me.btn31.Margin = New System.Windows.Forms.Padding(4)
        Me.btn31.Name = "btn31"
        Me.btn31.Size = New System.Drawing.Size(25, 26)
        Me.btn31.TabIndex = 164
        '
        'btn30
        '
        Me.btn30.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn30.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn30.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn30.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn30.Appearance.Options.UseBackColor = True
        Me.btn30.Appearance.Options.UseBorderColor = True
        Me.btn30.Appearance.Options.UseFont = True
        Me.btn30.Appearance.Options.UseForeColor = True
        Me.btn30.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn30.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn30.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn30.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn30.AppearanceDisabled.Options.UseBackColor = True
        Me.btn30.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn30.AppearanceDisabled.Options.UseFont = True
        Me.btn30.AppearanceDisabled.Options.UseForeColor = True
        Me.btn30.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn30.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn30.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn30.AppearanceHovered.Options.UseBackColor = True
        Me.btn30.AppearanceHovered.Options.UseBorderColor = True
        Me.btn30.AppearanceHovered.Options.UseForeColor = True
        Me.btn30.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn30.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn30.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn30.AppearancePressed.Options.UseBackColor = True
        Me.btn30.AppearancePressed.Options.UseBorderColor = True
        Me.btn30.AppearancePressed.Options.UseForeColor = True
        Me.btn30.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.btn30.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.btn30.Location = New System.Drawing.Point(577, 44)
        Me.btn30.Margin = New System.Windows.Forms.Padding(4)
        Me.btn30.Name = "btn30"
        Me.btn30.Size = New System.Drawing.Size(25, 26)
        Me.btn30.TabIndex = 163
        '
        'T34
        '
        Me.T34.EditValue = "[-RN9-]"
        Me.T34.Location = New System.Drawing.Point(388, 317)
        Me.T34.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T34.Name = "T34"
        Me.T34.Properties.AllowFocused = False
        Me.T34.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T34.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T34.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T34.Properties.Appearance.Options.UseBackColor = True
        Me.T34.Properties.Appearance.Options.UseFont = True
        Me.T34.Properties.Appearance.Options.UseForeColor = True
        Me.T34.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T34.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T34.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T34.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T34.Properties.ReadOnly = True
        Me.T34.Size = New System.Drawing.Size(182, 28)
        Me.T34.TabIndex = 156
        '
        'T32
        '
        Me.T32.EditValue = "[-RN2-]"
        Me.T32.Location = New System.Drawing.Point(388, 43)
        Me.T32.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T32.Name = "T32"
        Me.T32.Properties.AllowFocused = False
        Me.T32.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T32.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T32.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T32.Properties.Appearance.Options.UseBackColor = True
        Me.T32.Properties.Appearance.Options.UseFont = True
        Me.T32.Properties.Appearance.Options.UseForeColor = True
        Me.T32.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T32.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T32.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T32.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T32.Properties.ReadOnly = True
        Me.T32.Size = New System.Drawing.Size(182, 28)
        Me.T32.TabIndex = 157
        '
        'T33
        '
        Me.T33.EditValue = "[-RN8-]"
        Me.T33.Location = New System.Drawing.Point(388, 278)
        Me.T33.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T33.Name = "T33"
        Me.T33.Properties.AllowFocused = False
        Me.T33.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T33.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T33.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T33.Properties.Appearance.Options.UseBackColor = True
        Me.T33.Properties.Appearance.Options.UseFont = True
        Me.T33.Properties.Appearance.Options.UseForeColor = True
        Me.T33.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T33.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T33.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T33.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T33.Properties.ReadOnly = True
        Me.T33.Size = New System.Drawing.Size(182, 28)
        Me.T33.TabIndex = 159
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton1.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Options.UseBackColor = True
        Me.SimpleButton1.Appearance.Options.UseBorderColor = True
        Me.SimpleButton1.Appearance.Options.UseFont = True
        Me.SimpleButton1.Appearance.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton1.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton1.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton1.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton1.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton1.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy16x16
        Me.SimpleButton1.ImageOptions.SvgImageSize = New System.Drawing.Size(18, 18)
        Me.SimpleButton1.Location = New System.Drawing.Point(259, 472)
        Me.SimpleButton1.Margin = New System.Windows.Forms.Padding(4)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(25, 26)
        Me.SimpleButton1.TabIndex = 38
        '
        'T29
        '
        Me.T29.EditValue = "[-QRCode-]"
        Me.T29.Location = New System.Drawing.Point(72, 471)
        Me.T29.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.T29.Name = "T29"
        Me.T29.Properties.AllowFocused = False
        Me.T29.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.T29.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold)
        Me.T29.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.T29.Properties.Appearance.Options.UseBackColor = True
        Me.T29.Properties.Appearance.Options.UseFont = True
        Me.T29.Properties.Appearance.Options.UseForeColor = True
        Me.T29.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.T29.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.T29.Properties.NullValuePrompt = "Click browse to select the HTML file you want to Encrypt..."
        Me.T29.Properties.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.T29.Properties.ReadOnly = True
        Me.T29.Size = New System.Drawing.Size(182, 28)
        Me.T29.TabIndex = 36
        '
        'Label30
        '
        Me.Label30.AutoSize = True
        Me.Label30.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label30.Location = New System.Drawing.Point(645, 363)
        Me.Label30.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label30.Name = "Label30"
        Me.Label30.Size = New System.Drawing.Size(27, 18)
        Me.Label30.TabIndex = 37
        Me.Label30.Text = "39 -"
        '
        'Label26
        '
        Me.Label26.AutoSize = True
        Me.Label26.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label26.Location = New System.Drawing.Point(356, 478)
        Me.Label26.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label26.Name = "Label26"
        Me.Label26.Size = New System.Drawing.Size(27, 18)
        Me.Label26.TabIndex = 25
        Me.Label26.Text = "27 -"
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label13.Location = New System.Drawing.Point(357, 88)
        Me.Label13.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(25, 18)
        Me.Label13.TabIndex = 25
        Me.Label13.Text = "17 -"
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label10.Location = New System.Drawing.Point(42, 358)
        Me.Label10.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(25, 18)
        Me.Label10.TabIndex = 25
        Me.Label10.Text = "10 -"
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label5.Location = New System.Drawing.Point(43, 163)
        Me.Label5.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(22, 18)
        Me.Label5.TabIndex = 25
        Me.Label5.Text = "5 -"
        '
        'Label18
        '
        Me.Label18.AutoSize = True
        Me.Label18.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label18.Location = New System.Drawing.Point(356, 283)
        Me.Label18.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(27, 18)
        Me.Label18.TabIndex = 25
        Me.Label18.Text = "22 -"
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label14.Location = New System.Drawing.Point(356, 127)
        Me.Label14.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(26, 18)
        Me.Label14.TabIndex = 25
        Me.Label14.Text = "18 -"
        '
        'Label25
        '
        Me.Label25.AutoSize = True
        Me.Label25.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label25.Location = New System.Drawing.Point(41, 514)
        Me.Label25.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label25.Name = "Label25"
        Me.Label25.Size = New System.Drawing.Size(26, 18)
        Me.Label25.TabIndex = 25
        Me.Label25.Text = "14 -"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label6.Location = New System.Drawing.Point(44, 202)
        Me.Label6.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(21, 18)
        Me.Label6.TabIndex = 25
        Me.Label6.Text = "6 -"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label9.Location = New System.Drawing.Point(44, 319)
        Me.Label9.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(21, 18)
        Me.Label9.TabIndex = 25
        Me.Label9.Text = "9 -"
        '
        'Label29
        '
        Me.Label29.AutoSize = True
        Me.Label29.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label29.Location = New System.Drawing.Point(356, 439)
        Me.Label29.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label29.Name = "Label29"
        Me.Label29.Size = New System.Drawing.Size(27, 18)
        Me.Label29.TabIndex = 25
        Me.Label29.Text = "26 -"
        '
        'Label17
        '
        Me.Label17.AutoSize = True
        Me.Label17.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label17.Location = New System.Drawing.Point(357, 244)
        Me.Label17.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(25, 18)
        Me.Label17.TabIndex = 25
        Me.Label17.Text = "21 -"
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label15.Location = New System.Drawing.Point(357, 166)
        Me.Label15.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(25, 18)
        Me.Label15.TabIndex = 25
        Me.Label15.Text = "19 -"
        '
        'Label24
        '
        Me.Label24.AutoSize = True
        Me.Label24.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label24.Location = New System.Drawing.Point(42, 475)
        Me.Label24.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(25, 18)
        Me.Label24.TabIndex = 25
        Me.Label24.Text = "13 -"
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label7.Location = New System.Drawing.Point(44, 241)
        Me.Label7.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(21, 18)
        Me.Label7.TabIndex = 25
        Me.Label7.Text = "7 -"
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label8.Location = New System.Drawing.Point(43, 280)
        Me.Label8.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(22, 18)
        Me.Label8.TabIndex = 25
        Me.Label8.Text = "8 -"
        '
        'Label23
        '
        Me.Label23.AutoSize = True
        Me.Label23.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label23.Location = New System.Drawing.Point(355, 400)
        Me.Label23.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(28, 18)
        Me.Label23.TabIndex = 25
        Me.Label23.Text = "25 -"
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label16.Location = New System.Drawing.Point(356, 205)
        Me.Label16.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(27, 18)
        Me.Label16.TabIndex = 25
        Me.Label16.Text = "20 -"
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.Panel1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(972, 602)
        Me.LayoutControl1.TabIndex = 302
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(972, 602)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.Panel1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(946, 576)
        Me.LayoutControlItem1.TextVisible = False
        '
        'TagBestSender
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(96, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(96, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.LayoutControl1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Logo_NewBestSender
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "TagBestSender"
        Me.ShowInTaskbar = False
        Me.Text = "Tag Best Sender VIP"
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T15.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T16.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T17.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T18.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T5.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T19.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T6.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T12.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T20.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T28.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T26.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T21.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T7.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T25.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T22.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T8.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T13.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T23.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T9.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T14.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T24.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T10.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T27.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T11.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.T38.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T37.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T31.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T39.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T36.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T35.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T34.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T32.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T33.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T29.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents SeparatorControl1 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents T1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn28 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T15 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn14 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn27 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T16 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn13 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T3 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn23 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T17 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn9 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T4 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn26 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T18 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn12 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T5 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn22 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T19 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn8 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T6 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn25 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T12 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn11 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T20 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn21 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T28 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn7 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T26 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn24 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T21 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn10 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T7 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn20 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T25 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn6 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T22 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn19 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T8 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn5 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T13 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn18 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T23 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn4 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T9 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn17 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T14 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T24 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn16 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T10 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T27 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn15 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T11 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label1 As Label
    Friend WithEvents Label20 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents Label19 As Label
    Friend WithEvents Label11 As Label
    Friend WithEvents Label22 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label27 As Label
    Friend WithEvents Label12 As Label
    Friend WithEvents Label21 As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents Panel1 As Panel
    Friend WithEvents Label26 As Label
    Friend WithEvents Label13 As Label
    Friend WithEvents Label10 As Label
    Friend WithEvents Label5 As Label
    Friend WithEvents Label18 As Label
    Friend WithEvents Label14 As Label
    Friend WithEvents Label25 As Label
    Friend WithEvents Label6 As Label
    Friend WithEvents Label9 As Label
    Friend WithEvents Label29 As Label
    Friend WithEvents Label17 As Label
    Friend WithEvents Label15 As Label
    Friend WithEvents Label24 As Label
    Friend WithEvents Label7 As Label
    Friend WithEvents Label8 As Label
    Friend WithEvents Label23 As Label
    Friend WithEvents Label16 As Label
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T29 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label30 As Label
    Friend WithEvents btn37 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T39 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn36 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T38 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn35 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btn34 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T37 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents T36 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn33 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T35 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btn32 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btn31 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btn30 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btn29 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents T34 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents T32 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents T31 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents T33 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label39 As Label
    Friend WithEvents Label31 As Label
    Friend WithEvents Label32 As Label
    Friend WithEvents Label33 As Label
    Friend WithEvents Label34 As Label
    Friend WithEvents Label35 As Label
    Friend WithEvents Label36 As Label
    Friend WithEvents Label37 As Label
    Friend WithEvents Label38 As Label
    Friend WithEvents Label28 As Label
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
End Class
