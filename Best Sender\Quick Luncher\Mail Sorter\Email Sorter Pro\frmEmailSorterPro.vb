﻿Imports System.ComponentModel
Imports System.IO
Imports System.Net
Imports System.Text
Imports System.Text.RegularExpressions
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraEditors
Imports System.Threading.Tasks
Imports DevExpress.XtraGrid.Columns
Imports System.Collections.Concurrent
Public Class frmEmailSorterPro
    Private Shared ReadOnly MxCache As New ConcurrentDictionary(Of String, List(Of String))()
    Private Shared ReadOnly DomainCache As New ConcurrentDictionary(Of String, String)() ' تم تصحيح الخطأ هنا
    Private Shared ReadOnly PersonalDomains As New HashSet(Of String) From {
        "gmail.com", "yahoo.com", "outlook.com", "hotmail.com", "icloud.com",
        "aol.com", "mail.com", "protonmail.com", "zoho.com", "yandex.com"
    }
    Private emailList As New List(Of EmailInfo)()
    Private stopProcessing As Boolean = False
    Private Class EmailInfo
        Public Property Number As Integer
        Public Property Email As String
        Public Property MailType As String
        Public Property Icon As Image
        Public Property Status As String
        Public Property VerificationDate As String
        Public Property IsValid As Boolean
        Public Property Details As String
    End Class
    Private Sub frmEmailSorterPro_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InitializeBackgroundWorker()
        InitializeGridColumns()
        UpdateButtonStates("Initial")

        ' تطبيق تصميم Binance على GridControl
        ApplyBinanceGridStyling()
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على GridControl مع الحفاظ على ثيم WXI
    ''' </summary>
    Private Sub ApplyBinanceGridStyling()
        ' الحفاظ على ثيم WXI مع تطبيق تعديلات Binance

        ' تعيين إعدادات GridControl
        GridControl1.LookAndFeel.UseDefaultLookAndFeel = True
        GridControl1.LookAndFeel.SkinName = "WXI"

        ' تعيين إعدادات GridView
        With GridView1
            ' تعيين مظهر الصفوف المتناوبة
            .OptionsView.EnableAppearanceEvenRow = True
            .Appearance.EvenRow.BackColor = Color.FromArgb(30, 35, 41)
            .Appearance.EvenRow.Options.UseBackColor = True

            ' تعيين مظهر رأس الأعمدة
            .Appearance.HeaderPanel.Font = New Font(.Appearance.HeaderPanel.Font, FontStyle.Bold)
            .Appearance.HeaderPanel.ForeColor = Color.FromArgb(160, 174, 192)
            .Appearance.HeaderPanel.Options.UseFont = True
            .Appearance.HeaderPanel.Options.UseForeColor = True
            .Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            .Appearance.HeaderPanel.Options.UseTextOptions = True

            ' تعيين مظهر الصف المحدد
            .Appearance.FocusedRow.BackColor = Color.FromArgb(47, 52, 59)
            .Appearance.FocusedRow.Options.UseBackColor = True

            ' تعيين مظهر الصف عند المرور عليه
            .Appearance.HideSelectionRow.BackColor = Color.FromArgb(44, 47, 54)
            .Appearance.HideSelectionRow.Options.UseBackColor = True

            ' إزالة خطوط الشبكة
            .OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.False
            .OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.False

            ' تقليل ارتفاع الصفوف لعرض أكثر كثافة
            .RowHeight = 24

            ' تعيين الخط
            .Appearance.Row.Font = New Font("Segoe UI", 10)
            .Appearance.Row.Options.UseFont = True
        End With

        ' تعيين ألوان ProgressBarControl
        ProgressBarControl1.Properties.Appearance.ForeColor = Color.FromArgb(0, 194, 146) ' لون Binance الأخضر

        ' إضافة معالج حدث RowCellStyle لتطبيق ألوان مخصصة على القيم الموجبة والسالبة
        RemoveHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
        AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
    End Sub

    ''' <summary>
    ''' معالج حدث تنسيق خلايا الصفوف لتطبيق ألوان مخصصة على القيم الموجبة والسالبة
    ''' </summary>
    Private Sub GridView1_RowCellStyle(sender As Object, e As RowCellStyleEventArgs)
        ' تطبيق ألوان مخصصة على عمود الحالة
        If e.Column.FieldName = "Status" Then
            Dim statusValue As String = ""
            Try
                statusValue = GridView1.GetRowCellValue(e.RowHandle, "Status").ToString()
            Catch ex As Exception
                ' في حالة حدوث خطأ، استمر بدون قيمة
                Return
            End Try

            Select Case statusValue
                Case "Valid"
                    ' استخدام لون Binance الأخضر للقيم الموجبة
                    e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                Case "Invalid"
                    ' استخدام لون Binance الأحمر للقيم السالبة
                    e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                Case "Pending"
                    ' استخدام لون برتقالي للقيم غير المختبرة
                    e.Appearance.ForeColor = Color.FromArgb(255, 159, 67)
            End Select
        End If

        ' تطبيق ألوان مخصصة على عمود IsValid
        If e.Column.FieldName = "IsValid" Then
            Dim isValid As Boolean = False
            Try
                isValid = Convert.ToBoolean(GridView1.GetRowCellValue(e.RowHandle, "IsValid"))
            Catch ex As Exception
                ' في حالة حدوث خطأ، استمر بدون قيمة
                Return
            End Try

            If isValid Then
                ' استخدام لون Binance الأخضر للقيم الموجبة
                e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
            Else
                ' استخدام لون Binance الأحمر للقيم السالبة
                e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
            End If
        End If
    End Sub
    Private Sub InitializeBackgroundWorker()
        BackgroundWorker1.WorkerReportsProgress = True
        BackgroundWorker1.WorkerSupportsCancellation = True
    End Sub
    Private Sub InitializeGridColumns()
        GridView1.Columns.Clear()
        Dim columns As New List(Of GridColumn) From {
            New GridColumn() With {.FieldName = "Number", .Caption = "#", .VisibleIndex = 0, .Width = 40},
            New GridColumn() With {.FieldName = "Email", .Caption = "Email", .VisibleIndex = 1, .Width = 200},
            New GridColumn() With {.FieldName = "MailType", .Caption = "Mail Type", .VisibleIndex = 2, .Width = 120},
            New GridColumn() With {.FieldName = "Icon", .Caption = "Icon", .VisibleIndex = 3, .Width = 40},
            New GridColumn() With {.FieldName = "Status", .Caption = "Status", .VisibleIndex = 4, .Width = 80},
            New GridColumn() With {.FieldName = "VerificationDate", .Caption = "Verification Date", .VisibleIndex = 5, .Width = 120},
            New GridColumn() With {.FieldName = "IsValid", .Caption = "Is Valid", .VisibleIndex = 6, .Width = 60},
            New GridColumn() With {.FieldName = "Details", .Caption = "Details", .VisibleIndex = 7, .Width = 150}
        }
        For Each col In columns
            GridView1.Columns.Add(col)
        Next
        GridView1.OptionsView.ShowAutoFilterRow = False
        GridView1.OptionsView.ShowGroupPanel = False
        GridView1.OptionsBehavior.Editable = False
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Dim args = CType(e.Argument, Object)
        Dim action As String = args.Action
        If action = "Load" Then
            Dim filePath As String = args.FilePath
            Dim lines As String() = File.ReadAllLines(filePath)
            Dim tempList As New List(Of EmailInfo)()
            For i As Integer = 0 To lines.Length - 1
                If BackgroundWorker1.CancellationPending Then
                    e.Cancel = True
                    Return
                End If
                Dim email = lines(i).Trim()
                If IsValidEmail(email) Then
                    tempList.Add(New EmailInfo With {
                        .Number = i + 1,
                        .Email = email,
                        .MailType = "Not verified",
                        .Icon = My.Resources.pending16x16,
                        .Status = "Pending",
                        .VerificationDate = "",
                        .IsValid = False,
                        .Details = "Waiting for verification"
                    })
                End If
                If i Mod 100 = 0 Then
                    BackgroundWorker1.ReportProgress(CInt((i / lines.Length) * 100), New With {.ProgressType = "Load", .Count = tempList.Count})
                End If
            Next
            e.Result = New With {.Action = "Load", .Result = tempList}
        ElseIf action = "Verify" Then
            Dim emailsToVerify As List(Of EmailInfo) = args.EmailList
            For i As Integer = 0 To emailsToVerify.Count - 1
                If BackgroundWorker1.CancellationPending OrElse stopProcessing Then
                    e.Cancel = True
                    Return
                End If
                Dim result = QuickVerifyEmail(emailsToVerify(i).Email)
                emailsToVerify(i).MailType = GetEmailType(emailsToVerify(i).Email)
                emailsToVerify(i).Status = If(result.IsValid, "Valid", "Invalid")
                emailsToVerify(i).Icon = GetEmailIcon(emailsToVerify(i).MailType, result.IsValid)
                emailsToVerify(i).VerificationDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                emailsToVerify(i).IsValid = result.IsValid
                emailsToVerify(i).Details = result.Details
                BackgroundWorker1.ReportProgress(CInt((i / emailsToVerify.Count) * 100), New With {.ProgressType = "Verify", .Index = i})
            Next
            e.Result = New With {.Action = "Verify", .Result = emailsToVerify}
        End If
    End Sub
    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        Dim progressData = e.UserState
        If progressData.ProgressType = "Load" Then
            ProgressBarControl1.Position = e.ProgressPercentage
            ProgressBarControl1.Properties.Appearance.ForeColor = Color.FromArgb(0, 194, 146) ' لون Binance الأخضر
            Label1.Text = $"Loading emails... {e.ProgressPercentage}%"
            Label2.Text = $"Loaded: {progressData.Count}"
        ElseIf progressData.ProgressType = "Verify" Then
            ProgressBarControl1.Position = e.ProgressPercentage
            ProgressBarControl1.Properties.Appearance.ForeColor = Color.FromArgb(0, 194, 146) ' لون Binance الأخضر
            Label2.Text = $"Verified: {progressData.Index + 1}/{emailList.Count}"
            txtPath.Text = $"Verifying... {e.ProgressPercentage}%"
            GridView1.RefreshRow(progressData.Index)
        End If
    End Sub
    Private Sub BntResetAll_Click(sender As Object, e As EventArgs) Handles BntResetAll.Click
        emailList.Clear()
        GridControl1.DataSource = Nothing
        txtPath.Text = ""
        ProgressBarControl1.Position = 0
        Label1.Text = "Total Emails: 0"
        Label2.Text = "Verified: 0"
        UpdateButtonStates("Reset")
    End Sub
    Private Sub BntSave_Click(sender As Object, e As EventArgs) Handles BntSave.Click
        If emailList.Count = 0 Then
            XtraMessageBox.Show("No data to save", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        Dim desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        Dim folderPath = Path.Combine(desktopPath, "Email Sorter")
        Try
            Directory.CreateDirectory(folderPath)
            Dim groups = emailList.GroupBy(Function(x) x.MailType)
            For Each group In groups
                Dim fileName = group.Key.Replace(" ", "_") & "_emails.txt"
                Dim filePath = Path.Combine(folderPath, fileName)
                File.WriteAllLines(filePath, group.Select(Function(x) x.Email))
            Next
            XtraMessageBox.Show($"Saved {groups.Count} files to:{vbCrLf}{folderPath}", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            UpdateButtonStates("AfterSave")
        Catch ex As Exception
            XtraMessageBox.Show($"Error saving files: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub UpdateButtonStates(state As String)
        Select Case state
            Case "Initial"
                bntStart.Enabled = False
                BntStop.Enabled = False
                BntBrowse.Enabled = True
                BntSave.Enabled = False
                BntResetAll.Enabled = False
            Case "AfterBrowse"
                BntBrowse.Enabled = False
                BntSave.Enabled = False
                BntResetAll.Enabled = False
                BntStop.Enabled = True
            Case "LoadComplete"
                bntStart.Enabled = True
                BntStop.Enabled = False
                BntBrowse.Enabled = False
                BntSave.Enabled = False
                BntResetAll.Enabled = True
            Case "Start"
                bntStart.Enabled = False
                BntStop.Enabled = True
                BntBrowse.Enabled = False
                BntSave.Enabled = False
                BntResetAll.Enabled = False
            Case "Stop"
                bntStart.Enabled = True
                BntStop.Enabled = False
                BntBrowse.Enabled = True
                BntSave.Enabled = True
                BntResetAll.Enabled = True
            Case "VerifyComplete"
                bntStart.Enabled = True
                BntStop.Enabled = False
                BntBrowse.Enabled = True
                BntSave.Enabled = True
                BntResetAll.Enabled = True
            Case "Reset"
                bntStart.Enabled = False
                BntStop.Enabled = False
                BntBrowse.Enabled = True
                BntSave.Enabled = False
                BntResetAll.Enabled = False
            Case "AfterSave"
                ' No changes needed after save
        End Select
    End Sub
    ' باقي الدوال المساعدة (QuickVerifyEmail, GetEmailType, DetermineEmailType, IsBusinessDomain, ...)
    ' تبقى كما هي بدون تغيير
    Private Function QuickVerifyEmail(email As String) As (IsValid As Boolean, Details As String)
        If Not IsValidEmail(email) Then
            Return (False, "Invalid email format")
        End If
        Dim domain = email.Split("@"c)(1)
        Try
            Dns.GetHostEntry(domain)
            Return (True, "Valid email (quick verification)")
        Catch
            Return (False, "Domain does not exist")
        End Try
    End Function
    Private Function GetEmailType(email As String) As String
        If String.IsNullOrWhiteSpace(email) OrElse Not email.Contains("@") Then Return "Other"
        Return DomainCache.GetOrAdd(email, Function(e) DetermineEmailType(e))
    End Function
    Private Function DetermineEmailType(email As String) As String
        Dim domain = email.Split("@"c)(1).ToLower()
        ' Check personal domains first
        If PersonalDomains.Contains(domain) Then
            Return GetPersonalEmailType(domain)
        End If
        ' Check known Microsoft domains
        Dim microsoftType = GetMicrosoftEmailType(domain)
        If microsoftType <> "Other" Then Return microsoftType
        ' Check other known email types
        Dim knownType = GetKnownEmailType(domain)
        If knownType <> "Other" Then Return knownType
        ' Default to Office 365 for all other business domains
        If IsBusinessDomain(domain) Then
            Return "Office 365"
        End If
        Return "Other"
    End Function
    Private Function IsBusinessDomain(domain As String) As Boolean
        ' Skip common non-business domains
        If domain.EndsWith(".local") OrElse domain.EndsWith(".localhost") Then
            Return False
        End If
        ' Check for business-like domain patterns
        Return domain.EndsWith(".com") OrElse
               domain.EndsWith(".net") OrElse
               domain.EndsWith(".org") OrElse
               domain.EndsWith(".biz") OrElse
               domain.EndsWith(".co") OrElse
               domain.EndsWith(".io") OrElse
               domain.EndsWith(".ai")
    End Function
    Private Function GetPersonalEmailType(domain As String) As String
        Select Case domain
            Case "gmail.com", "googlemail.com" : Return "Gmail"
            Case "yahoo.com", "ymail.com", "rocketmail.com" : Return "Yahoo Mail"
            Case "outlook.com", "hotmail.com", "live.com", "msn.com", "att.net" : Return "Outlook"
            Case "icloud.com", "me.com", "mac.com" : Return "iCloud Mail"
            Case "aol.com", "aim.com" : Return "AOL Mail"
            Case "protonmail.com", "protonmail.ch" : Return "ProtonMail"
            Case "zoho.com" : Return "Zoho Mail"
            Case "yandex.ru", "yandex.com" : Return "Yandex Mail"
            Case Else : Return "Other"
        End Select
    End Function
    Private Function GetMicrosoftEmailType(domain As String) As String
        If domain.Contains(".onmicrosoft.com") OrElse
           domain.Contains(".sharepoint.com") OrElse
           domain.Contains(".exchangeonline.com") OrElse
           domain = "office365.com" OrElse
           domain = "microsoft.com" OrElse
           domain = "cn4e" Then
            Return "Office 365"
        End If
        Return "Other"
    End Function
    Private Function GetKnownEmailType(domain As String) As String
        ' List of known email services (simplified for brevity)
        Dim knownServices As New Dictionary(Of String, String) From {
            {"mail.ru", "Mail.ru"}, {"yandex.", "Yandex Mail"}, {"qq.com", "QQ Mail"},
            {"163.com", "163.com"}, {"126.com", "126.com"}, {"sina.com", "Sina Mail"},
            {"rediffmail.com", "Rediffmail"}, {"web.de", "Web.de"}, {"seznam.cz", "Seznam.cz"},
            {"orange.fr", "Orange.fr"}, {"laposte.net", "Laposte.net"}, {"walla.co.il", "Walla! Mail"},
            {"terra.com", "Terra Mail"}, {"ziggo.nl", "Ziggo Mail"}, {"comcast.net", "Comcast Email"},
            {"verizon.net", "Verizon Email"}, {"att.com", "AT&T Mail"}, {"btinternet.com", "BT Mail"},
            {"virgin.net", "Virgin Media Mail"}, {"t-online.de", "T-Online Mail"}, {"mailbox.org", "Mailbox.org"},
            {"fastmail.com", "FastMail"}, {"runbox.com", "Runbox"}, {"mimecast.com", "Mimecast"},
            {"bluehost.com", "Bluehost Email"}, {"rackspace.com", "Rackspace Email"}, {"godaddy.com", "GoDaddy Email"},
            {"namecheap.com", "Namecheap Private Email"}, {"dreamhost.com", "DreamHost Email"}
        }
        For Each kvp In knownServices
            If domain.Contains(kvp.Key) Then
                Return kvp.Value
            End If
        Next
        Return "Other"
    End Function
    Private Function GetEmailIcon(mailType As String, isValid As Boolean) As Image
        If Not isValid Then Return My.Resources.Error16x16
        ' Convert to lowercase for case-insensitive comparison
        Dim lowerType = mailType.ToLower()
        Select Case lowerType
        ' Microsoft/Office 365 variants
            Case "office 365", "microsoft 365", "office365", "cn4e", "exchange online", "edu", "me", "edu", "News", "org", "us", "fr", "uk", "academy", "net",
                 "cc", "ad", "ae", "af", "ag", "ai", "al", "am", "ao", "aq", "ar", "as", "at", "au", "aw", "ax", "az",
     "ba", "bb", "bd", "be", "bf", "bg", "bh", "bi", "bj", "bl", "bm", "bn", "bo", "bq", "br", "bs", "bt", "bv", "bw", "by", "bz",
     "ca", "cc", "cd", "cf", "cg", "ch", "ci", "ck", "cl", "cm", "cn", "co", "cr", "cu", "cv", "cw", "cx", "cy", "cz",
     "de", "dj", "dk", "dm", "do", "dz",
     "ec", "ee", "eg", "eh", "er", "es", "et",
     "fi", "fj", "fk", "fm", "fo", "fr",
     "ga", "gb", "gd", "ge", "gf", "gg", "gh", "gi", "gl", "gm", "gn", "gp", "gq", "gr", "gs", "gt", "gu", "gw", "gy",
     "hk", "hm", "hn", "hr", "ht", "hu",
     "id", "ie", "il", "im", "in", "io", "iq", "ir", "is", "it",
     "je", "jm", "jo", "jp",
     "ke", "kg", "kh", "ki", "km", "kn", "kp", "kr", "kw", "ky", "kz",
     "la", "lb", "lc", "li", "lk", "lr", "ls", "lt", "lu", "lv", "ly",
     "ma", "mc", "md", "me", "mf", "mg", "mh", "mk", "ml", "mm", "mn", "mo", "mp", "mq", "mr", "ms", "mt", "mu", "mv", "mw", "mx", "my", "mz",
     "na", "nc", "ne", "nf", "ng", "ni", "nl", "no", "np", "nr", "nu", "nz",
     "om",
     "pa", "pe", "pf", "pg", "ph", "pk", "pl", "pm", "pn", "pr", "ps", "pt", "pw", "py",
     "qa",
     "re", "ro", "rs", "ru", "rw",
     "sa", "sb", "sc", "sd", "se", "sg", "sh", "si", "sj", "sk", "sl", "sm", "sn", "so", "sr", "ss", "st", "sv", "sx", "sy", "sz",
     "tc", "td", "tf", "tg", "th", "tj", "tk", "tl", "tm", "tn", "to", "tr", "tt", "tv", "tw", "tz",
     "ua", "ug", "um", "us", "uy", "uz",
     "va", "vc", "ve", "vg", "vi", "vn", "vu",
     "wf", "ws",
     "ye", "yt",
     "za", "zm", "zw"
                Return My.Resources.Microsoft_36516x16
        ' Google services
            Case "gmail", "google workspace", "googlemail", "google apps"
                Return My.Resources.gmail16x16
        ' Microsoft personal emails
            Case "outlook", "outlook.com", "hotmail", "live.com", "msn.com", "att.net"
                Return My.Resources.Outlook16x16
        ' Yahoo
            Case "yahoo mail", "yahoo", "ymail", "rocketmail"
                Return My.Resources.yahoo16x16
        ' Secure email providers
            Case "protonmail", "proton mail", "protonmail.ch"
                Return My.Resources.ProtonMail16x16
            Case "tutanota", "tutanota.com"
                Return My.Resources.Tutanota16x16
            Case "hushmail", "hushmail.com"
                Return My.Resources.Hushmail16x16
            Case "mailfence", "mailfence.com"
                Return My.Resources.Mailfence16x16
            Case "countermail", "countermail.com"
                Return My.Resources.CounterMail16x16
        ' Business email services
            Case "zoho mail", "zoho", "zohomail"
                Return My.Resources.Zoho_Mail16x16
            Case "fastmail", "fastmail.com"
                Return My.Resources.FastMail16x16
            Case "runbox", "runbox.com"
                Return My.Resources.RunBox16x1616x16
            Case "kolab now", "kolabnow", "kolab.com"
                Return My.Resources.KolabNow16x16
            Case "mimecast", "mimecast.com"
                Return My.Resources.Mimecast16x16
        ' Apple/iCloud
            Case "icloud mail", "icloud", "me.com", "mac.com"
                Return My.Resources.iCloud_Mail16x16
        ' Traditional providers
            Case "aol mail", "aol", "aim.com"
                Return My.Resources.Aol16x16
            Case "gmx mail", "gmx.net", "gmx", "gmx.de"
                Return My.Resources.GMX16x16
            Case "mail.com", "email.com"
                Return My.Resources.Mail_com16x16
            Case "yandex mail", "yandex", "ya.ru"
                Return My.Resources.yandex16x16
        ' Regional providers
            Case "mail.ru", "bk.ru", "inbox.ru"
                Return My.Resources.Mail_ru16x16
            Case "naver mail", "naver", "naver.com"
                Return My.Resources.naver16x16
            Case "qq mail", "qq", "qq.com"
                Return My.Resources.QQ16x16
            Case "163.com", "netease", "126.com", "yeah.net"
                Return If(lowerType = "126.com", My.Resources._12616x16, My.Resources.NetEase16x16)
            Case "sina mail", "sina", "sina.com.cn"
                Return My.Resources.Sina16x16
            Case "rediffmail", "rediff.com"
                Return My.Resources.Rediff16x16
            Case "libero mail", "libero", "libero.it"
                Return My.Resources.Libero16x16
            Case "web.de", "web.de mail"
                Return My.Resources.web_de16x16
            Case "seznam.cz", "seznam email"
                Return My.Resources.Seznam16x16
            Case "orange.fr", "orange mail"
                Return My.Resources.Orange_fr16x16
            Case "laposte.net", "laposte mail"
                Return My.Resources.Laposte16x16
            Case "walla! mail", "walla", "walla.co.il"
                Return My.Resources.walla16x16
            Case "terra mail", "terra", "terra.com.br"
                Return My.Resources.Terra16x16
        ' ISPs
            Case "comcast email", "comcast", "xfinity"
                Return My.Resources.Comcast16x16
            Case "verizon email", "verizon", "verizon.net"
                Return My.Resources.Verizon16x16
            Case "at&t mail", "at&t", "att.com", "sbcglobal.net"
                Return My.Resources.AT_T16x16
            Case "bt mail", "bt", "btinternet.com"
                Return My.Resources.BT16x16
            Case "virgin media mail", "virgin media", "virgin.net"
                Return My.Resources.virgin16x16
            Case "t-online mail", "t-online", "t-online.de"
                Return My.Resources.T_Online16x16
            Case "ziggo mail", "ziggo", "ziggo.nl"
                Return My.Resources.Ziggo16x16
        ' Other services
            Case "amazon", "amazon.com"
                Return My.Resources.amazon16x16
            Case "ionos", "1and1", "1und1"
                Return My.Resources.ionos16x16
            Case "strato", "strato.de"
                Return My.Resources.Strato16x16
            Case "aliyun", "alibaba mail"
                Return My.Resources.Aliyun16x16
            Case "aruba", "aruba.it"
                Return My.Resources.aruba16x16
        ' Temporary email services
            Case "temp-mail", "10 minute mail", "guerrilla mail", "mailinator",
         "fake email generator", "temp mail", "throwaway email"
                Return My.Resources.Temp16x16
        ' Email clients
            Case "thunderbird", "mozilla thunderbird"
                Return My.Resources.Thunderbird16x16
            Case "airmail", "airmail app"
                Return My.Resources.Airmail16x16
            Case "spark mail", "spark", "readdle spark"
                Return My.Resources.Spark_mail16x16
            Case "hey.com", "hey", "hey email"
                Return My.Resources.Hey_com16x16
        ' Default cases
            Case "business mail", "corporate mail", "work email"
                Return My.Resources.Microsoft_36516x16
            Case "others", "other", "unknown"
                Return My.Resources.Other16x16
            Case Else
                Return My.Resources.default16x16
        End Select
    End Function
    Private Shared Function GetMxRecords(domain As String) As List(Of String)
        Return MxCache.GetOrAdd(domain, Function(d) FetchMxRecords(d))
    End Function
    Private Shared Function FetchMxRecords(domain As String) As List(Of String)
        Dim mxRecords As New List(Of String)()
        Try
            Dim queryResult As IPHostEntry = Dns.GetHostEntry(domain)
            If queryResult.Aliases.Length > 0 Then
                mxRecords.AddRange(queryResult.Aliases)
            End If
            If mxRecords.Count = 0 Then
                Dim startInfo As New ProcessStartInfo("nslookup", "-q=mx " & domain) With {
                    .RedirectStandardOutput = True,
                    .UseShellExecute = False,
                    .CreateNoWindow = True
                }
                Using process As Process = Process.Start(startInfo)
                    Dim output As String = process.StandardOutput.ReadToEnd()
                    process.WaitForExit()
                    For Each line In output.Split({Environment.NewLine}, StringSplitOptions.RemoveEmptyEntries)
                        If line.ToLower().Contains("mail exchanger") Then
                            Dim parts = line.Split("=").Last().Trim()
                            mxRecords.Add(parts)
                        End If
                    Next
                End Using
            End If
        Catch ex As Exception
            ' Log error if needed
        End Try
        Return mxRecords
    End Function
    Private Function IsValidEmail(email As String) As Boolean
        If String.IsNullOrWhiteSpace(email) Then Return False
        Return Regex.IsMatch(email, "^[^@\s]+@[^@\s]+\.[^@\s]+$")
    End Function
    Private Sub bntStart_Click(sender As Object, e As EventArgs) Handles bntStart.Click
        If emailList.Count = 0 Then
            XtraMessageBox.Show("No emails to verify", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        UpdateButtonStates("Start")
        stopProcessing = False
        If Not BackgroundWorker1.IsBusy Then
            BackgroundWorker1.RunWorkerAsync(New With {.Action = "Verify", .EmailList = emailList})
        End If
    End Sub
    Private Sub BntStop_Click(sender As Object, e As EventArgs) Handles BntStop.Click
        stopProcessing = True
        BackgroundWorker1.CancelAsync()
        UpdateButtonStates("Stop")
    End Sub
    Private Sub BntBrowse_Click(sender As Object, e As EventArgs) Handles BntBrowse.Click
        Dim openFileDialog As New OpenFileDialog() With {
         .Filter = "Text Files|*.txt|CSV Files|*.csv|All Files|*.*"
     }
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            txtPath.Text = openFileDialog.FileName
            UpdateButtonStates("AfterBrowse")
            If Not BackgroundWorker1.IsBusy Then
                BackgroundWorker1.RunWorkerAsync(New With {.Action = "Load", .FilePath = openFileDialog.FileName})
            End If
        End If
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        If e.Cancelled Then
            XtraMessageBox.Show("Operation cancelled", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
        ElseIf e.Error IsNot Nothing Then
            XtraMessageBox.Show($"Error: {e.Error.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Else
            Dim result = e.Result
            If result.Action = "Load" Then
                emailList = result.Result
                GridControl1.DataSource = emailList
                Label1.Text = $"Total Emails: {emailList.Count}"
                Label2.Text = "Verified: 0"
                UpdateButtonStates("LoadComplete")
            ElseIf result.Action = "Verify" Then
                XtraMessageBox.Show("Verification completed", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
                UpdateButtonStates("VerifyComplete")
            End If
        End If
        ProgressBarControl1.Position = 0
        txtPath.Text = "Ready"
    End Sub
End Class