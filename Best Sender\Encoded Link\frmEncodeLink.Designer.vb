﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmEncodeLink
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.SimpleButton5 = New DevExpress.XtraEditors.SimpleButton()
        Me.BntCLear = New DevExpress.XtraEditors.SimpleButton()
        Me.BntEncodedLink = New DevExpress.XtraEditors.SimpleButton()
        Me.textbox3 = New DevExpress.XtraEditors.TextEdit()
        Me.textbox1 = New DevExpress.XtraEditors.TextEdit()
        Me.txtRedirectLink = New DevExpress.XtraEditors.TextEdit()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.SeparatorControl2 = New DevExpress.XtraEditors.SeparatorControl()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        CType(Me.textbox3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.textbox1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtRedirectLink.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Timer1
        '
        '
        'SimpleButton5
        '
        Me.SimpleButton5.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton5.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton5.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton5.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton5.Appearance.Options.UseBackColor = True
        Me.SimpleButton5.Appearance.Options.UseBorderColor = True
        Me.SimpleButton5.Appearance.Options.UseFont = True
        Me.SimpleButton5.Appearance.Options.UseForeColor = True
        Me.SimpleButton5.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton5.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton5.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton5.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton5.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton5.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton5.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton5.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton5.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton5.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton5.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton5.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton5.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton5.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton5.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton5.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton5.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton5.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton5.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton5.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton5.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy32x32
        Me.SimpleButton5.Location = New System.Drawing.Point(399, 378)
        Me.SimpleButton5.Margin = New System.Windows.Forms.Padding(4)
        Me.SimpleButton5.Name = "SimpleButton5"
        Me.SimpleButton5.Size = New System.Drawing.Size(152, 38)
        Me.SimpleButton5.TabIndex = 519
        Me.SimpleButton5.Text = "Copy Link      "
        '
        'BntCLear
        '
        Me.BntCLear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Options.UseBackColor = True
        Me.BntCLear.Appearance.Options.UseBorderColor = True
        Me.BntCLear.Appearance.Options.UseFont = True
        Me.BntCLear.Appearance.Options.UseForeColor = True
        Me.BntCLear.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntCLear.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntCLear.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntCLear.AppearanceDisabled.Options.UseBackColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseFont = True
        Me.BntCLear.AppearanceDisabled.Options.UseForeColor = True
        Me.BntCLear.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntCLear.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntCLear.AppearanceHovered.Options.UseBackColor = True
        Me.BntCLear.AppearanceHovered.Options.UseBorderColor = True
        Me.BntCLear.AppearanceHovered.Options.UseForeColor = True
        Me.BntCLear.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntCLear.AppearancePressed.Options.UseBackColor = True
        Me.BntCLear.AppearancePressed.Options.UseBorderColor = True
        Me.BntCLear.AppearancePressed.Options.UseForeColor = True
        Me.BntCLear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntCLear.Location = New System.Drawing.Point(593, 378)
        Me.BntCLear.Margin = New System.Windows.Forms.Padding(4)
        Me.BntCLear.Name = "BntCLear"
        Me.BntCLear.Size = New System.Drawing.Size(152, 38)
        Me.BntCLear.TabIndex = 518
        Me.BntCLear.Text = "Reset All"
        '
        'BntEncodedLink
        '
        Me.BntEncodedLink.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntEncodedLink.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntEncodedLink.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntEncodedLink.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntEncodedLink.Appearance.Options.UseBackColor = True
        Me.BntEncodedLink.Appearance.Options.UseBorderColor = True
        Me.BntEncodedLink.Appearance.Options.UseFont = True
        Me.BntEncodedLink.Appearance.Options.UseForeColor = True
        Me.BntEncodedLink.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntEncodedLink.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntEncodedLink.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntEncodedLink.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntEncodedLink.AppearanceDisabled.Options.UseBackColor = True
        Me.BntEncodedLink.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntEncodedLink.AppearanceDisabled.Options.UseFont = True
        Me.BntEncodedLink.AppearanceDisabled.Options.UseForeColor = True
        Me.BntEncodedLink.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntEncodedLink.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntEncodedLink.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntEncodedLink.AppearanceHovered.Options.UseBackColor = True
        Me.BntEncodedLink.AppearanceHovered.Options.UseBorderColor = True
        Me.BntEncodedLink.AppearanceHovered.Options.UseForeColor = True
        Me.BntEncodedLink.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntEncodedLink.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntEncodedLink.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntEncodedLink.AppearancePressed.Options.UseBackColor = True
        Me.BntEncodedLink.AppearancePressed.Options.UseBorderColor = True
        Me.BntEncodedLink.AppearancePressed.Options.UseForeColor = True
        Me.BntEncodedLink.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.LinkEN
        Me.BntEncodedLink.Location = New System.Drawing.Point(205, 378)
        Me.BntEncodedLink.Margin = New System.Windows.Forms.Padding(4)
        Me.BntEncodedLink.Name = "BntEncodedLink"
        Me.BntEncodedLink.Size = New System.Drawing.Size(152, 38)
        Me.BntEncodedLink.TabIndex = 517
        Me.BntEncodedLink.Text = "Encoded Link"
        '
        'textbox3
        '
        Me.textbox3.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.textbox3.EditValue = ""
        Me.textbox3.Location = New System.Drawing.Point(201, 325)
        Me.textbox3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.textbox3.Name = "textbox3"
        Me.textbox3.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.textbox3.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.textbox3.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.textbox3.Properties.Appearance.Options.UseBackColor = True
        Me.textbox3.Properties.Appearance.Options.UseFont = True
        Me.textbox3.Properties.Appearance.Options.UseForeColor = True
        Me.textbox3.Properties.NullValuePrompt = "Your Encrypted link will appear here"
        Me.textbox3.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.textbox3.Size = New System.Drawing.Size(571, 30)
        Me.textbox3.TabIndex = 515
        '
        'textbox1
        '
        Me.textbox1.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.textbox1.EditValue = ""
        Me.textbox1.Location = New System.Drawing.Point(201, 268)
        Me.textbox1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.textbox1.Name = "textbox1"
        Me.textbox1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.textbox1.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.textbox1.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.textbox1.Properties.Appearance.Options.UseBackColor = True
        Me.textbox1.Properties.Appearance.Options.UseFont = True
        Me.textbox1.Properties.Appearance.Options.UseForeColor = True
        Me.textbox1.Properties.NullValuePrompt = "www.Example.com Your link without (  / )"
        Me.textbox1.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.textbox1.Size = New System.Drawing.Size(571, 30)
        Me.textbox1.TabIndex = 516
        '
        'txtRedirectLink
        '
        Me.txtRedirectLink.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtRedirectLink.EditValue = ""
        Me.txtRedirectLink.Location = New System.Drawing.Point(201, 211)
        Me.txtRedirectLink.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtRedirectLink.Name = "txtRedirectLink"
        Me.txtRedirectLink.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtRedirectLink.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtRedirectLink.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtRedirectLink.Properties.Appearance.Options.UseBackColor = True
        Me.txtRedirectLink.Properties.Appearance.Options.UseFont = True
        Me.txtRedirectLink.Properties.Appearance.Options.UseForeColor = True
        Me.txtRedirectLink.Properties.NullValuePrompt = "https:// or http://"
        Me.txtRedirectLink.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtRedirectLink.Size = New System.Drawing.Size(571, 30)
        Me.txtRedirectLink.TabIndex = 514
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label4.Location = New System.Drawing.Point(201, 301)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(94, 21)
        Me.Label4.TabIndex = 511
        Me.Label4.Text = "Link Encode"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label3.Location = New System.Drawing.Point(201, 244)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(76, 21)
        Me.Label3.TabIndex = 512
        Me.Label3.Text = "Page Link"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(201, 187)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(97, 21)
        Me.Label2.TabIndex = 513
        Me.Label2.Text = "Redirect Link"
        '
        'SeparatorControl2
        '
        Me.SeparatorControl2.LineThickness = 1
        Me.SeparatorControl2.Location = New System.Drawing.Point(307, 123)
        Me.SeparatorControl2.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl2.Name = "SeparatorControl2"
        Me.SeparatorControl2.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl2.Size = New System.Drawing.Size(363, 26)
        Me.SeparatorControl2.TabIndex = 520
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.EncodedLinkImageNew
        Me.PictureBox1.Location = New System.Drawing.Point(290, 69)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(393, 75)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 521
        Me.PictureBox1.TabStop = False
        '
        'frmEncodeLink
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.SeparatorControl2)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.SimpleButton5)
        Me.Controls.Add(Me.BntCLear)
        Me.Controls.Add(Me.BntEncodedLink)
        Me.Controls.Add(Me.textbox3)
        Me.Controls.Add(Me.textbox1)
        Me.Controls.Add(Me.txtRedirectLink)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label2)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmEncodeLink"
        Me.ShowInTaskbar = False
        Me.Text = "Encoded Link"
        CType(Me.textbox3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.textbox1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtRedirectLink.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Timer1 As Timer
    Friend WithEvents SimpleButton5 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntCLear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntEncodedLink As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents textbox3 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents textbox1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtRedirectLink As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label4 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents SeparatorControl2 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents PictureBox1 As PictureBox
End Class
