﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class Remove
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Remove))
        Me.TxtSMTPServer = New DevExpress.XtraEditors.TextEdit()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.PnlWait = New System.Windows.Forms.Panel()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.DxErrorProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(Me.components)
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.TxtSubject = New DevExpress.XtraEditors.MemoEdit()
        Me.TxtTag_2 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TxtTag_1 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.txt_limitSend = New DevExpress.XtraEditors.TextEdit()
        Me.TxtFromMail = New DevExpress.XtraEditors.TextEdit()
        Me.Bnt_Reset = New DevExpress.XtraEditors.SimpleButton()
        Me.BntTest = New DevExpress.XtraEditors.SimpleButton()
        Me.TxtFromName = New DevExpress.XtraEditors.TextEdit()
        Me.TxtPort = New DevExpress.XtraEditors.TextEdit()
        Me.CheckSSL = New DevExpress.XtraEditors.CheckEdit()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.TxtPassword = New DevExpress.XtraEditors.TextEdit()
        Me.BntAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.Panel13 = New System.Windows.Forms.Panel()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.PictureBox5 = New System.Windows.Forms.PictureBox()
        Me.PictureBox4 = New System.Windows.Forms.PictureBox()
        Me.ProgressBarControl2 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.lblfailed = New System.Windows.Forms.Label()
        Me.lblSuccess = New System.Windows.Forms.Label()
        Me.ProgressBarControl1 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.lblTotal = New System.Windows.Forms.Label()
        Me.lbltotalstmp = New System.Windows.Forms.Label()
        Me.SeparatorControl5 = New DevExpress.XtraEditors.SeparatorControl()
        Me.SeparatorControl2 = New DevExpress.XtraEditors.SeparatorControl()
        Me.pnlResult = New System.Windows.Forms.Panel()
        Me.SeparatorControl1 = New DevExpress.XtraEditors.SeparatorControl()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.lbl1 = New System.Windows.Forms.Label()
        Me.LogInNormalTextBox1 = New DevExpress.XtraEditors.TextEdit()
        Me.SeparatorControl6 = New DevExpress.XtraEditors.SeparatorControl()
        Me.SimpleButton5 = New DevExpress.XtraEditors.SimpleButton()
        Me.XtraTabPage6 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel8 = New System.Windows.Forms.Panel()
        Me.XtraTabControl2 = New DevExpress.XtraTab.XtraTabControl()
        Me.TabAddLogo = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel6 = New System.Windows.Forms.Panel()
        Me.Label29 = New System.Windows.Forms.Label()
        Me.TabSendAttachment = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.txtbody = New System.Windows.Forms.TextBox()
        Me.TabConvertLetterToPNG = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.ConvertLinkToQRCODE = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel10 = New System.Windows.Forms.Panel()
        Me.Label30 = New System.Windows.Forms.Label()
        Me.XtraTabPage3 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel9 = New System.Windows.Forms.Panel()
        Me.XtraTabPage4 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel11 = New System.Windows.Forms.Panel()
        Me.Label31 = New System.Windows.Forms.Label()
        Me.XtraTabPageSleepTime = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel12 = New System.Windows.Forms.Panel()
        Me.Label32 = New System.Windows.Forms.Label()
        Me.XtraTabPage5 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel15 = New System.Windows.Forms.Panel()
        Me.LayoutControl4 = New DevExpress.XtraLayout.LayoutControl()
        Me.GroupControl8 = New DevExpress.XtraEditors.GroupControl()
        Me.SeparatorControl3 = New DevExpress.XtraEditors.SeparatorControl()
        Me.ComboBoxSleepTime = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.chk_limitSend = New DevExpress.XtraEditors.CheckEdit()
        Me.Label22 = New System.Windows.Forms.Label()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.TextEdit1 = New DevExpress.XtraEditors.TextEdit()
        Me.SeparatorControl4 = New DevExpress.XtraEditors.SeparatorControl()
        Me.GroupControl7 = New DevExpress.XtraEditors.GroupControl()
        Me.Label25 = New System.Windows.Forms.Label()
        Me.PictureEdit4 = New DevExpress.XtraEditors.PictureEdit()
        Me.ComboFlagStatus = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.Label27 = New System.Windows.Forms.Label()
        Me.ComboSensitivity = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.GroupControl5 = New DevExpress.XtraEditors.GroupControl()
        Me.Pic_QRCode = New System.Windows.Forms.PictureBox()
        Me.lbl_success_QRcode = New System.Windows.Forms.Label()
        Me.txtWidth = New DevExpress.XtraEditors.TextEdit()
        Me.PictureEdit1 = New DevExpress.XtraEditors.PictureEdit()
        Me.txtHeight = New DevExpress.XtraEditors.TextEdit()
        Me.PictureEdit3 = New DevExpress.XtraEditors.PictureEdit()
        Me.txtCode = New DevExpress.XtraEditors.TextEdit()
        Me.PictureEdit2 = New DevExpress.XtraEditors.PictureEdit()
        Me.SimpleButton3 = New DevExpress.XtraEditors.SimpleButton()
        Me.btnGenerate = New DevExpress.XtraEditors.SimpleButton()
        Me.btnExport = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupControl3 = New DevExpress.XtraEditors.GroupControl()
        Me.ChkLogo = New DevExpress.XtraEditors.CheckEdit()
        Me.BntResetLetterLogo = New DevExpress.XtraEditors.SimpleButton()
        Me.lblLogoTitle = New System.Windows.Forms.Label()
        Me.bntLetterLogo = New DevExpress.XtraEditors.SimpleButton()
        Me.piclogo = New System.Windows.Forms.PictureBox()
        Me.GroupControl4 = New DevExpress.XtraEditors.GroupControl()
        Me.ComboBoxEdit1 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.bntResetLetterConvertor = New DevExpress.XtraEditors.SimpleButton()
        Me.Label28 = New System.Windows.Forms.Label()
        Me.trcThreads = New DevExpress.XtraEditors.TrackBarControl()
        Me.txtTitilOffice365 = New DevExpress.XtraEditors.TextEdit()
        Me.Switch_LetterConvertorLink = New DevExpress.XtraEditors.ToggleSwitch()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.BntRemoveAttachment = New DevExpress.XtraEditors.SimpleButton()
        Me.lblattacmentFileCount = New System.Windows.Forms.Label()
        Me.ToggleSwitch1 = New DevExpress.XtraEditors.ToggleSwitch()
        Me.bntAddAttachment = New DevExpress.XtraEditors.SimpleButton()
        Me.txtattach = New DevExpress.XtraEditors.TextEdit()
        Me.cbTags = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.cmbSites = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.XtraTabPage2 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel7 = New System.Windows.Forms.Panel()
        Me.LayoutControl2 = New DevExpress.XtraLayout.LayoutControl()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.LayoutControl3 = New DevExpress.XtraLayout.LayoutControl()
        Me.txtLetter = New System.Windows.Forms.RichTextBox()
        Me.txtLetterPath = New DevExpress.XtraEditors.TextEdit()
        Me.txtNewLink = New DevExpress.XtraEditors.TextEdit()
        Me.GroupControl9 = New DevExpress.XtraEditors.GroupControl()
        Me.Panel16 = New System.Windows.Forms.Panel()
        Me.LayoutControl5 = New DevExpress.XtraLayout.LayoutControl()
        Me.LetterEncoder_txtFilepath = New DevExpress.XtraEditors.TextEdit()
        Me.LetterEncoder_Richtext = New System.Windows.Forms.RichTextBox()
        Me.LayoutControlGroup5 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem2 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem13 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lblTrcThreads = New System.Windows.Forms.Label()
        Me.TabSmtp = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.ProgressBarControl3 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.Label21 = New System.Windows.Forms.Label()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn8 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn11 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn7 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn6 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn9 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn10 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn23 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn24 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn25 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn18 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn19 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.PnlWaits = New DevExpress.XtraEditors.GroupControl()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        Me.ProgressPanel2 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.XtraTabPage1 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.ProgressBarControl4 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.GridControl2 = New DevExpress.XtraGrid.GridControl()
        Me.GridView2 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn12 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn13 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemTextEdit2 = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
        Me.GridColumn14 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.col_status_icon = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn15 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn16 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn17 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn20 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemButtonEdit2 = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.GridColumn21 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemCheckEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit()
        Me.GridColumn22 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn26 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn27 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn28 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemImageEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemImageEdit()
        Me.RepositoryItemPictureEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemPictureEdit()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.NumericUpDown1 = New System.Windows.Forms.NumericUpDown()
        Me.SpinEdit1 = New DevExpress.XtraEditors.SpinEdit()
        Me.TextBoxTitle = New DevExpress.XtraEditors.TextEdit()
        Me.TextBoxLogoLM = New DevExpress.XtraEditors.TextEdit()
        Me.ComboBox_TitleColor = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ComboBox_TextColor = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ComboBox_ButtonColor = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.FontSizeMessage = New DevExpress.XtraEditors.SpinEdit()
        Me.fonttitel = New DevExpress.XtraEditors.SpinEdit()
        Me.Label33 = New System.Windows.Forms.Label()
        Me.Label34 = New System.Windows.Forms.Label()
        Me.Label35 = New System.Windows.Forms.Label()
        Me.Label36 = New System.Windows.Forms.Label()
        Me.Label37 = New System.Windows.Forms.Label()
        Me.Label38 = New System.Windows.Forms.Label()
        Me.Label39 = New System.Windows.Forms.Label()
        Me.RightAlignPictureBox = New DevExpress.XtraEditors.PictureEdit()
        Me.CenterAlignPictureBox = New DevExpress.XtraEditors.PictureEdit()
        Me.LeftAlignPictureBox = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonSaveHTML = New DevExpress.XtraEditors.SimpleButton()
        Me.BNT_Backup = New DevExpress.XtraEditors.SimpleButton()
        Me.BntReset = New DevExpress.XtraEditors.SimpleButton()
        Me.TextBoxSignature = New DevExpress.XtraEditors.TextEdit()
        Me.Label40 = New System.Windows.Forms.Label()
        Me.TextButtonname = New DevExpress.XtraEditors.TextEdit()
        Me.Label41 = New System.Windows.Forms.Label()
        Me.CheckBoxBoldText = New DevExpress.XtraEditors.CheckEdit()
        Me.GroupControl6 = New DevExpress.XtraEditors.GroupControl()
        Me.GroupControl10 = New DevExpress.XtraEditors.GroupControl()
        Me.LayoutControl6 = New DevExpress.XtraLayout.LayoutControl()
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.TextEdit2 = New DevExpress.XtraEditors.TextEdit()
        Me.TextEdit3 = New DevExpress.XtraEditors.TextEdit()
        Me.GroupControl11 = New DevExpress.XtraEditors.GroupControl()
        Me.Panel14 = New System.Windows.Forms.Panel()
        Me.LayoutControl7 = New DevExpress.XtraLayout.LayoutControl()
        Me.TextEdit4 = New DevExpress.XtraEditors.TextEdit()
        Me.RichTextBox2 = New System.Windows.Forms.RichTextBox()
        Me.LayoutControlGroup6 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem14 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem3 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup7 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem4 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem17 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem18 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ComboBoxEdit2 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.CheckFrench = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckRussian = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckJapanese = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckHindi = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckArabic = New DevExpress.XtraEditors.CheckEdit()
        CType(Me.TxtSMTPServer.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PnlWait.SuspendLayout()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.TxtSubject.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtTag_2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtTag_1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_limitSend.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtFromMail.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtFromName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtPort.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckSSL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtPassword.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel13.SuspendLayout()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ProgressBarControl2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlResult.SuspendLayout()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        CType(Me.LogInNormalTextBox1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage6.SuspendLayout()
        Me.Panel8.SuspendLayout()
        CType(Me.XtraTabControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl2.SuspendLayout()
        Me.TabAddLogo.SuspendLayout()
        Me.Panel6.SuspendLayout()
        Me.TabSendAttachment.SuspendLayout()
        Me.Panel4.SuspendLayout()
        Me.TabConvertLetterToPNG.SuspendLayout()
        Me.ConvertLinkToQRCODE.SuspendLayout()
        Me.Panel10.SuspendLayout()
        Me.XtraTabPage3.SuspendLayout()
        Me.XtraTabPage4.SuspendLayout()
        Me.Panel11.SuspendLayout()
        Me.XtraTabPageSleepTime.SuspendLayout()
        Me.Panel12.SuspendLayout()
        Me.XtraTabPage5.SuspendLayout()
        Me.Panel15.SuspendLayout()
        CType(Me.LayoutControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl4.SuspendLayout()
        CType(Me.GroupControl8, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl8.SuspendLayout()
        CType(Me.SeparatorControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBoxSleepTime.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chk_limitSend.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl7, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl7.SuspendLayout()
        CType(Me.PictureEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboFlagStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboSensitivity.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl5.SuspendLayout()
        CType(Me.Pic_QRCode, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtWidth.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHeight.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtCode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl3.SuspendLayout()
        CType(Me.ChkLogo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.piclogo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl4.SuspendLayout()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.trcThreads, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.trcThreads.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtTitilOffice365.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Switch_LetterConvertorLink.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        CType(Me.ToggleSwitch1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtattach.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbTags.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cmbSites.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage2.SuspendLayout()
        Me.Panel7.SuspendLayout()
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl2.SuspendLayout()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl3.SuspendLayout()
        CType(Me.txtLetterPath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtNewLink.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl9, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl9.SuspendLayout()
        Me.Panel16.SuspendLayout()
        CType(Me.LayoutControl5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl5.SuspendLayout()
        CType(Me.LetterEncoder_txtFilepath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabSmtp.SuspendLayout()
        Me.Panel2.SuspendLayout()
        CType(Me.ProgressBarControl3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PnlWaits, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PnlWaits.SuspendLayout()
        Me.XtraTabPage1.SuspendLayout()
        Me.Panel3.SuspendLayout()
        CType(Me.ProgressBarControl4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemTextEdit2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemButtonEdit2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemCheckEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemImageEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemPictureEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        CType(Me.NumericUpDown1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SpinEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxTitle.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxLogoLM.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBox_TitleColor.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBox_TextColor.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBox_ButtonColor.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FontSizeMessage.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.fonttitel.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RightAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CenterAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LeftAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxSignature.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextButtonname.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckBoxBoldText.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl10, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl10.SuspendLayout()
        CType(Me.LayoutControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl6.SuspendLayout()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl11, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl11.SuspendLayout()
        Me.Panel14.SuspendLayout()
        CType(Me.LayoutControl7, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl7.SuspendLayout()
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBoxEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckFrench.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckRussian.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckJapanese.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckHindi.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckArabic.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'TxtSMTPServer
        '
        Me.TxtSMTPServer.EditValue = ""
        Me.TxtSMTPServer.Location = New System.Drawing.Point(95, 27)
        Me.TxtSMTPServer.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtSMTPServer.Name = "TxtSMTPServer"
        Me.TxtSMTPServer.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtSMTPServer.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtSMTPServer.Properties.Appearance.Options.UseFont = True
        Me.TxtSMTPServer.Properties.Appearance.Options.UseForeColor = True
        Me.TxtSMTPServer.Properties.NullValuePrompt = "Enter a vaild host name..."
        Me.TxtSMTPServer.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtSMTPServer.Size = New System.Drawing.Size(292, 30)
        Me.TxtSMTPServer.TabIndex = 64
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.AnimationToTextDistance = 10
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.Appearance.Options.UseFont = True
        Me.ProgressPanel1.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel1.AppearanceCaption.Options.UseFont = True
        Me.ProgressPanel1.AppearanceDescription.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel1.AppearanceDescription.Options.UseFont = True
        Me.ProgressPanel1.BarAnimationElementThickness = 5
        Me.ProgressPanel1.Caption = "Please Wait..."
        Me.ProgressPanel1.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
        Me.ProgressPanel1.Description = "verifiying smtp account"
        Me.ProgressPanel1.Location = New System.Drawing.Point(9, 13)
        Me.ProgressPanel1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(229, 35)
        Me.ProgressPanel1.TabIndex = 394
        Me.ProgressPanel1.Text = "ProgressPanel2"
        '
        'PnlWait
        '
        Me.PnlWait.Controls.Add(Me.SimpleButton1)
        Me.PnlWait.Controls.Add(Me.ProgressPanel1)
        Me.PnlWait.Location = New System.Drawing.Point(39, 330)
        Me.PnlWait.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PnlWait.Name = "PnlWait"
        Me.PnlWait.Size = New System.Drawing.Size(359, 57)
        Me.PnlWait.TabIndex = 396
        Me.PnlWait.Visible = False
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton1.Location = New System.Drawing.Point(241, 20)
        Me.SimpleButton1.LookAndFeel.SkinName = "McSkin"
        Me.SimpleButton1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(77, 28)
        Me.SimpleButton1.TabIndex = 391
        Me.SimpleButton1.Text = "&Cancel"
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'DxErrorProvider1
        '
        Me.DxErrorProvider1.ContainerControl = Me
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.TxtSubject)
        Me.GroupBox1.Controls.Add(Me.PnlWait)
        Me.GroupBox1.Controls.Add(Me.TxtTag_2)
        Me.GroupBox1.Controls.Add(Me.TxtTag_1)
        Me.GroupBox1.Controls.Add(Me.txt_limitSend)
        Me.GroupBox1.Controls.Add(Me.TxtFromMail)
        Me.GroupBox1.Controls.Add(Me.Bnt_Reset)
        Me.GroupBox1.Controls.Add(Me.BntTest)
        Me.GroupBox1.Controls.Add(Me.TxtFromName)
        Me.GroupBox1.Controls.Add(Me.TxtPort)
        Me.GroupBox1.Controls.Add(Me.TxtSMTPServer)
        Me.GroupBox1.Controls.Add(Me.CheckSSL)
        Me.GroupBox1.Controls.Add(Me.Label18)
        Me.GroupBox1.Controls.Add(Me.Label17)
        Me.GroupBox1.Controls.Add(Me.Label16)
        Me.GroupBox1.Controls.Add(Me.Label15)
        Me.GroupBox1.Controls.Add(Me.Label14)
        Me.GroupBox1.Controls.Add(Me.Label13)
        Me.GroupBox1.Controls.Add(Me.Label12)
        Me.GroupBox1.Controls.Add(Me.Label11)
        Me.GroupBox1.Controls.Add(Me.Label10)
        Me.GroupBox1.Controls.Add(Me.Label9)
        Me.GroupBox1.Controls.Add(Me.Label8)
        Me.GroupBox1.Controls.Add(Me.Label6)
        Me.GroupBox1.Controls.Add(Me.Label7)
        Me.GroupBox1.Controls.Add(Me.Label5)
        Me.GroupBox1.Controls.Add(Me.CheckEdit1)
        Me.GroupBox1.Controls.Add(Me.Label4)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.TxtPassword)
        Me.GroupBox1.Controls.Add(Me.BntAdd)
        Me.GroupBox1.Location = New System.Drawing.Point(11, 1)
        Me.GroupBox1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Padding = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupBox1.Size = New System.Drawing.Size(100, 40)
        Me.GroupBox1.TabIndex = 397
        Me.GroupBox1.TabStop = False
        '
        'TxtSubject
        '
        Me.TxtSubject.Location = New System.Drawing.Point(95, 218)
        Me.TxtSubject.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtSubject.Name = "TxtSubject"
        Me.TxtSubject.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtSubject.Properties.Appearance.Options.UseForeColor = True
        Me.TxtSubject.Size = New System.Drawing.Size(292, 22)
        Me.TxtSubject.TabIndex = 399
        '
        'TxtTag_2
        '
        Me.TxtTag_2.Location = New System.Drawing.Point(95, 187)
        Me.TxtTag_2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtTag_2.Name = "TxtTag_2"
        Me.TxtTag_2.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtTag_2.Properties.Appearance.Options.UseForeColor = True
        Me.TxtTag_2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TxtTag_2.Properties.Items.AddRange(New Object() {"Add Link", "Encoded Email 64", "Random Browser", "Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Country", "Random IP", "Random IP China", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Send With Logo", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show Random Fake Email Address Office", "Show Random Fake Phone Numbers", "Show Random Street New York", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.TxtTag_2.Size = New System.Drawing.Size(292, 32)
        Me.TxtTag_2.TabIndex = 398
        '
        'TxtTag_1
        '
        Me.TxtTag_1.Location = New System.Drawing.Point(95, 253)
        Me.TxtTag_1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtTag_1.Name = "TxtTag_1"
        Me.TxtTag_1.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtTag_1.Properties.Appearance.Options.UseForeColor = True
        Me.TxtTag_1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TxtTag_1.Properties.Items.AddRange(New Object() {"Add Link", "Encoded Email 64", "Random Browser", "Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Country", "Random IP", "Random IP China", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Send With Logo", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show Random Fake Email Address Office", "Show Random Fake Phone Numbers", "Show Random Street New York", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.TxtTag_1.Size = New System.Drawing.Size(292, 32)
        Me.TxtTag_1.TabIndex = 398
        '
        'txt_limitSend
        '
        Me.txt_limitSend.EditValue = ""
        Me.txt_limitSend.Location = New System.Drawing.Point(95, 89)
        Me.txt_limitSend.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txt_limitSend.Name = "txt_limitSend"
        Me.txt_limitSend.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_limitSend.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_limitSend.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_limitSend.Properties.Appearance.Options.UseBackColor = True
        Me.txt_limitSend.Properties.Appearance.Options.UseFont = True
        Me.txt_limitSend.Properties.Appearance.Options.UseForeColor = True
        Me.txt_limitSend.Properties.NullValuePrompt = "Enter your limit Number Send"
        Me.txt_limitSend.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_limitSend.Size = New System.Drawing.Size(292, 30)
        Me.txt_limitSend.TabIndex = 64
        Me.txt_limitSend.ToolTipTitle = "Height"
        '
        'TxtFromMail
        '
        Me.TxtFromMail.EditValue = ""
        Me.TxtFromMail.Location = New System.Drawing.Point(95, 157)
        Me.TxtFromMail.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtFromMail.Name = "TxtFromMail"
        Me.TxtFromMail.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtFromMail.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtFromMail.Properties.Appearance.Options.UseFont = True
        Me.TxtFromMail.Properties.Appearance.Options.UseForeColor = True
        Me.TxtFromMail.Properties.NullValuePrompt = "Enter a vaild host name..."
        Me.TxtFromMail.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtFromMail.Size = New System.Drawing.Size(292, 30)
        Me.TxtFromMail.TabIndex = 64
        '
        'Bnt_Reset
        '
        Me.Bnt_Reset.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.Bnt_Reset.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Reset.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_Reset.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Reset.Appearance.Options.UseBackColor = True
        Me.Bnt_Reset.Appearance.Options.UseBorderColor = True
        Me.Bnt_Reset.Appearance.Options.UseFont = True
        Me.Bnt_Reset.Appearance.Options.UseForeColor = True
        Me.Bnt_Reset.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.Bnt_Reset.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.Bnt_Reset.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_Reset.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Bnt_Reset.AppearanceDisabled.Options.UseBackColor = True
        Me.Bnt_Reset.AppearanceDisabled.Options.UseBorderColor = True
        Me.Bnt_Reset.AppearanceDisabled.Options.UseFont = True
        Me.Bnt_Reset.AppearanceDisabled.Options.UseForeColor = True
        Me.Bnt_Reset.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.Bnt_Reset.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.Bnt_Reset.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.Bnt_Reset.AppearanceHovered.Options.UseBackColor = True
        Me.Bnt_Reset.AppearanceHovered.Options.UseBorderColor = True
        Me.Bnt_Reset.AppearanceHovered.Options.UseForeColor = True
        Me.Bnt_Reset.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Reset.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.Bnt_Reset.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.Bnt_Reset.AppearancePressed.Options.UseBackColor = True
        Me.Bnt_Reset.AppearancePressed.Options.UseBorderColor = True
        Me.Bnt_Reset.AppearancePressed.Options.UseForeColor = True
        Me.Bnt_Reset.Location = New System.Drawing.Point(47, 344)
        Me.Bnt_Reset.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Bnt_Reset.Name = "Bnt_Reset"
        Me.Bnt_Reset.Size = New System.Drawing.Size(104, 28)
        Me.Bnt_Reset.TabIndex = 376
        Me.Bnt_Reset.Text = "Reset"
        '
        'BntTest
        '
        Me.BntTest.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntTest.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTest.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntTest.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTest.Appearance.Options.UseBackColor = True
        Me.BntTest.Appearance.Options.UseBorderColor = True
        Me.BntTest.Appearance.Options.UseFont = True
        Me.BntTest.Appearance.Options.UseForeColor = True
        Me.BntTest.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntTest.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntTest.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntTest.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntTest.AppearanceDisabled.Options.UseBackColor = True
        Me.BntTest.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntTest.AppearanceDisabled.Options.UseFont = True
        Me.BntTest.AppearanceDisabled.Options.UseForeColor = True
        Me.BntTest.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntTest.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntTest.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntTest.AppearanceHovered.Options.UseBackColor = True
        Me.BntTest.AppearanceHovered.Options.UseBorderColor = True
        Me.BntTest.AppearanceHovered.Options.UseForeColor = True
        Me.BntTest.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTest.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntTest.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntTest.AppearancePressed.Options.UseBackColor = True
        Me.BntTest.AppearancePressed.Options.UseBorderColor = True
        Me.BntTest.AppearancePressed.Options.UseForeColor = True
        Me.BntTest.Location = New System.Drawing.Point(165, 344)
        Me.BntTest.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.BntTest.Name = "BntTest"
        Me.BntTest.Size = New System.Drawing.Size(104, 28)
        Me.BntTest.TabIndex = 377
        Me.BntTest.Text = "Start"
        '
        'TxtFromName
        '
        Me.TxtFromName.EditValue = ""
        Me.TxtFromName.Location = New System.Drawing.Point(95, 286)
        Me.TxtFromName.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtFromName.Name = "TxtFromName"
        Me.TxtFromName.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtFromName.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtFromName.Properties.Appearance.Options.UseFont = True
        Me.TxtFromName.Properties.Appearance.Options.UseForeColor = True
        Me.TxtFromName.Properties.NullValuePrompt = "Enter a vaild host name..."
        Me.TxtFromName.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtFromName.Size = New System.Drawing.Size(292, 30)
        Me.TxtFromName.TabIndex = 64
        '
        'TxtPort
        '
        Me.TxtPort.EditValue = "587"
        Me.TxtPort.Location = New System.Drawing.Point(95, 58)
        Me.TxtPort.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtPort.Name = "TxtPort"
        Me.TxtPort.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtPort.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtPort.Properties.Appearance.Options.UseFont = True
        Me.TxtPort.Properties.Appearance.Options.UseForeColor = True
        Me.TxtPort.Properties.NullValuePrompt = "Enter a vaild host name..."
        Me.TxtPort.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtPort.Size = New System.Drawing.Size(56, 30)
        Me.TxtPort.TabIndex = 64
        '
        'CheckSSL
        '
        Me.CheckSSL.Location = New System.Drawing.Point(280, 59)
        Me.CheckSSL.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CheckSSL.Name = "CheckSSL"
        Me.CheckSSL.Properties.AllowFocused = False
        Me.CheckSSL.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckSSL.Properties.Appearance.ForeColor = System.Drawing.Color.Transparent
        Me.CheckSSL.Properties.Appearance.Options.UseFont = True
        Me.CheckSSL.Properties.Appearance.Options.UseForeColor = True
        Me.CheckSSL.Properties.Caption = " Enable SSL"
        Me.CheckSSL.Properties.LookAndFeel.SkinName = "DevExpress Style"
        Me.CheckSSL.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckSSL.Size = New System.Drawing.Size(107, 25)
        Me.CheckSSL.TabIndex = 9
        '
        'Label18
        '
        Me.Label18.AutoSize = True
        Me.Label18.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label18.ForeColor = System.Drawing.Color.Crimson
        Me.Label18.Location = New System.Drawing.Point(5, 223)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(13, 15)
        Me.Label18.TabIndex = 375
        Me.Label18.Text = "*"
        '
        'Label17
        '
        Me.Label17.AutoSize = True
        Me.Label17.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label17.ForeColor = System.Drawing.Color.Crimson
        Me.Label17.Location = New System.Drawing.Point(5, 191)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(13, 15)
        Me.Label17.TabIndex = 374
        Me.Label17.Text = "*"
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label16.ForeColor = System.Drawing.Color.Crimson
        Me.Label16.Location = New System.Drawing.Point(5, 162)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(13, 15)
        Me.Label16.TabIndex = 373
        Me.Label16.Text = "*"
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.ForeColor = System.Drawing.Color.Crimson
        Me.Label15.Location = New System.Drawing.Point(5, 292)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(13, 15)
        Me.Label15.TabIndex = 372
        Me.Label15.Text = "*"
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label14.ForeColor = System.Drawing.Color.Crimson
        Me.Label14.Location = New System.Drawing.Point(5, 257)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(13, 15)
        Me.Label14.TabIndex = 371
        Me.Label14.Text = "*"
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.ForeColor = System.Drawing.Color.Crimson
        Me.Label13.Location = New System.Drawing.Point(5, 64)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(13, 15)
        Me.Label13.TabIndex = 370
        Me.Label13.Text = "*"
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label12.ForeColor = System.Drawing.Color.Crimson
        Me.Label12.Location = New System.Drawing.Point(5, 130)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(13, 15)
        Me.Label12.TabIndex = 369
        Me.Label12.Text = "*"
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label11.ForeColor = System.Drawing.Color.Crimson
        Me.Label11.Location = New System.Drawing.Point(5, 95)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(13, 15)
        Me.Label11.TabIndex = 368
        Me.Label11.Text = "*"
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.ForeColor = System.Drawing.Color.Crimson
        Me.Label10.Location = New System.Drawing.Point(5, 32)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(13, 15)
        Me.Label10.TabIndex = 367
        Me.Label10.Text = "*"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.ForeColor = System.Drawing.Color.White
        Me.Label9.Location = New System.Drawing.Point(17, 222)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(65, 19)
        Me.Label9.TabIndex = 366
        Me.Label9.Text = "Subject : "
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.ForeColor = System.Drawing.Color.White
        Me.Label8.Location = New System.Drawing.Point(17, 191)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(91, 19)
        Me.Label8.TabIndex = 364
        Me.Label8.Text = "Subject Tag : "
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.ForeColor = System.Drawing.Color.White
        Me.Label6.Location = New System.Drawing.Point(17, 160)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(84, 19)
        Me.Label6.TabIndex = 362
        Me.Label6.Text = "From Mail : "
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.ForeColor = System.Drawing.Color.White
        Me.Label7.Location = New System.Drawing.Point(17, 290)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(92, 19)
        Me.Label7.TabIndex = 360
        Me.Label7.Text = "From Name : "
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.White
        Me.Label5.Location = New System.Drawing.Point(17, 257)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(81, 19)
        Me.Label5.TabIndex = 358
        Me.Label5.Text = "Name Tag : "
        '
        'CheckEdit1
        '
        Me.CheckEdit1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.CheckEdit1.Location = New System.Drawing.Point(363, 130)
        Me.CheckEdit1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CheckEdit1.Name = "CheckEdit1"
        Me.CheckEdit1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(96, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(96, Byte), Integer))
        Me.CheckEdit1.Properties.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.CheckEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.CheckEdit1.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEdit1.Properties.Caption = "View password"
        Me.CheckEdit1.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style15
        Me.CheckEdit1.Size = New System.Drawing.Size(16, 22)
        Me.CheckEdit1.TabIndex = 10
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.White
        Me.Label4.Location = New System.Drawing.Point(17, 62)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(45, 19)
        Me.Label4.TabIndex = 66
        Me.Label4.Text = "Port : "
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.White
        Me.Label2.Location = New System.Drawing.Point(17, 130)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(79, 19)
        Me.Label2.TabIndex = 64
        Me.Label2.Text = "Password : "
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.White
        Me.Label1.Location = New System.Drawing.Point(17, 94)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(58, 19)
        Me.Label1.TabIndex = 62
        Me.Label1.Text = "Email  : "
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.Color.White
        Me.Label3.Location = New System.Drawing.Point(17, 31)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(88, 19)
        Me.Label3.TabIndex = 60
        Me.Label3.Text = "Host SMTP : "
        '
        'TxtPassword
        '
        Me.TxtPassword.EditValue = ""
        Me.TxtPassword.Location = New System.Drawing.Point(95, 127)
        Me.TxtPassword.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtPassword.Name = "TxtPassword"
        Me.TxtPassword.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtPassword.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtPassword.Properties.Appearance.Options.UseFont = True
        Me.TxtPassword.Properties.Appearance.Options.UseForeColor = True
        Me.TxtPassword.Properties.NullValuePrompt = "Enter a vaild host name..."
        Me.TxtPassword.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtPassword.Size = New System.Drawing.Size(292, 30)
        Me.TxtPassword.TabIndex = 64
        '
        'BntAdd
        '
        Me.BntAdd.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntAdd.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntAdd.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.Appearance.Options.UseBackColor = True
        Me.BntAdd.Appearance.Options.UseBorderColor = True
        Me.BntAdd.Appearance.Options.UseFont = True
        Me.BntAdd.Appearance.Options.UseForeColor = True
        Me.BntAdd.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntAdd.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntAdd.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntAdd.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntAdd.AppearanceDisabled.Options.UseBackColor = True
        Me.BntAdd.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntAdd.AppearanceDisabled.Options.UseFont = True
        Me.BntAdd.AppearanceDisabled.Options.UseForeColor = True
        Me.BntAdd.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntAdd.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntAdd.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntAdd.AppearanceHovered.Options.UseBackColor = True
        Me.BntAdd.AppearanceHovered.Options.UseBorderColor = True
        Me.BntAdd.AppearanceHovered.Options.UseForeColor = True
        Me.BntAdd.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntAdd.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntAdd.AppearancePressed.Options.UseBackColor = True
        Me.BntAdd.AppearancePressed.Options.UseBorderColor = True
        Me.BntAdd.AppearancePressed.Options.UseForeColor = True
        Me.BntAdd.Location = New System.Drawing.Point(284, 344)
        Me.BntAdd.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.BntAdd.Name = "BntAdd"
        Me.BntAdd.Size = New System.Drawing.Size(104, 28)
        Me.BntAdd.TabIndex = 378
        Me.BntAdd.Text = "Add"
        '
        'Panel13
        '
        Me.Panel13.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Panel13.Controls.Add(Me.Label19)
        Me.Panel13.Controls.Add(Me.PictureBox5)
        Me.Panel13.Controls.Add(Me.PictureBox4)
        Me.Panel13.Controls.Add(Me.ProgressBarControl2)
        Me.Panel13.Controls.Add(Me.Label20)
        Me.Panel13.Controls.Add(Me.lblfailed)
        Me.Panel13.Controls.Add(Me.lblSuccess)
        Me.Panel13.Controls.Add(Me.ProgressBarControl1)
        Me.Panel13.Controls.Add(Me.lblTotal)
        Me.Panel13.Controls.Add(Me.lbltotalstmp)
        Me.Panel13.Controls.Add(Me.SeparatorControl5)
        Me.Panel13.Controls.Add(Me.SeparatorControl2)
        Me.Panel13.Controls.Add(Me.pnlResult)
        Me.Panel13.Controls.Add(Me.Panel1)
        Me.Panel13.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel13.Location = New System.Drawing.Point(0, 0)
        Me.Panel13.Name = "Panel13"
        Me.Panel13.Size = New System.Drawing.Size(1210, 48)
        Me.Panel13.TabIndex = 398
        '
        'Label19
        '
        Me.Label19.AutoSize = True
        Me.Label19.Font = New System.Drawing.Font("Microsoft Sans Serif", 18.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label19.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(203, Byte), Integer), CType(CType(121, Byte), Integer))
        Me.Label19.Image = Global.Best_Sender.My.Resources.Resources.Send
        Me.Label19.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Label19.Location = New System.Drawing.Point(677, 12)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(48, 29)
        Me.Label19.TabIndex = 346
        Me.Label19.Text = "....."
        Me.Label19.Visible = False
        '
        'PictureBox5
        '
        Me.PictureBox5.Image = Global.Best_Sender.My.Resources.Resources.smtpserverOlder
        Me.PictureBox5.Location = New System.Drawing.Point(219, 9)
        Me.PictureBox5.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PictureBox5.Name = "PictureBox5"
        Me.PictureBox5.Size = New System.Drawing.Size(23, 17)
        Me.PictureBox5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox5.TabIndex = 343
        Me.PictureBox5.TabStop = False
        '
        'PictureBox4
        '
        Me.PictureBox4.Image = Global.Best_Sender.My.Resources.Resources.Emailimage
        Me.PictureBox4.Location = New System.Drawing.Point(10, 9)
        Me.PictureBox4.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PictureBox4.Name = "PictureBox4"
        Me.PictureBox4.Size = New System.Drawing.Size(23, 17)
        Me.PictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox4.TabIndex = 343
        Me.PictureBox4.TabStop = False
        '
        'ProgressBarControl2
        '
        Me.ProgressBarControl2.EditValue = 1
        Me.ProgressBarControl2.Location = New System.Drawing.Point(3, 34)
        Me.ProgressBarControl2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ProgressBarControl2.Name = "ProgressBarControl2"
        Me.ProgressBarControl2.Properties.ShowTitle = True
        Me.ProgressBarControl2.Size = New System.Drawing.Size(184, 13)
        Me.ProgressBarControl2.TabIndex = 339
        '
        'Label20
        '
        Me.Label20.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label20.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(203, Byte), Integer), CType(CType(121, Byte), Integer))
        Me.Label20.Location = New System.Drawing.Point(1011, 6)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(10, 12)
        Me.Label20.TabIndex = 342
        Me.Label20.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label20.Visible = False
        '
        'lblfailed
        '
        Me.lblfailed.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.lblfailed.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.lblfailed.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(128, Byte), Integer))
        Me.lblfailed.Location = New System.Drawing.Point(547, 8)
        Me.lblfailed.Name = "lblfailed"
        Me.lblfailed.Size = New System.Drawing.Size(82, 19)
        Me.lblfailed.TabIndex = 316
        Me.lblfailed.Text = "Failed : 0"
        Me.lblfailed.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblSuccess
        '
        Me.lblSuccess.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.lblSuccess.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.lblSuccess.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(203, Byte), Integer), CType(CType(121, Byte), Integer))
        Me.lblSuccess.Location = New System.Drawing.Point(394, 8)
        Me.lblSuccess.Name = "lblSuccess"
        Me.lblSuccess.Size = New System.Drawing.Size(82, 19)
        Me.lblSuccess.TabIndex = 315
        Me.lblSuccess.Text = "Sent : 0"
        Me.lblSuccess.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'ProgressBarControl1
        '
        Me.ProgressBarControl1.EditValue = 70
        Me.ProgressBarControl1.Location = New System.Drawing.Point(832, 22)
        Me.ProgressBarControl1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ProgressBarControl1.Name = "ProgressBarControl1"
        Me.ProgressBarControl1.Properties.ShowTitle = True
        Me.ProgressBarControl1.Size = New System.Drawing.Size(184, 13)
        Me.ProgressBarControl1.TabIndex = 338
        '
        'lblTotal
        '
        Me.lblTotal.AutoSize = True
        Me.lblTotal.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.lblTotal.ForeColor = System.Drawing.Color.White
        Me.lblTotal.Location = New System.Drawing.Point(39, 10)
        Me.lblTotal.Name = "lblTotal"
        Me.lblTotal.Size = New System.Drawing.Size(67, 21)
        Me.lblTotal.TabIndex = 332
        Me.lblTotal.Text = "Email : 0"
        '
        'lbltotalstmp
        '
        Me.lbltotalstmp.AutoSize = True
        Me.lbltotalstmp.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.lbltotalstmp.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.lbltotalstmp.Location = New System.Drawing.Point(249, 10)
        Me.lbltotalstmp.Name = "lbltotalstmp"
        Me.lbltotalstmp.Size = New System.Drawing.Size(68, 21)
        Me.lbltotalstmp.TabIndex = 337
        Me.lbltotalstmp.Text = "SMTP : 0"
        '
        'SeparatorControl5
        '
        Me.SeparatorControl5.LineThickness = 1
        Me.SeparatorControl5.Location = New System.Drawing.Point(234, 22)
        Me.SeparatorControl5.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl5.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl5.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SeparatorControl5.Name = "SeparatorControl5"
        Me.SeparatorControl5.Padding = New System.Windows.Forms.Padding(8, 7, 8, 7)
        Me.SeparatorControl5.Size = New System.Drawing.Size(87, 19)
        Me.SeparatorControl5.TabIndex = 299
        '
        'SeparatorControl2
        '
        Me.SeparatorControl2.LineThickness = 1
        Me.SeparatorControl2.Location = New System.Drawing.Point(393, 22)
        Me.SeparatorControl2.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SeparatorControl2.Name = "SeparatorControl2"
        Me.SeparatorControl2.Padding = New System.Windows.Forms.Padding(8, 7, 8, 7)
        Me.SeparatorControl2.Size = New System.Drawing.Size(87, 19)
        Me.SeparatorControl2.TabIndex = 299
        '
        'pnlResult
        '
        Me.pnlResult.Controls.Add(Me.SeparatorControl1)
        Me.pnlResult.Location = New System.Drawing.Point(379, 3)
        Me.pnlResult.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.pnlResult.Name = "pnlResult"
        Me.pnlResult.Size = New System.Drawing.Size(279, 38)
        Me.pnlResult.TabIndex = 344
        '
        'SeparatorControl1
        '
        Me.SeparatorControl1.AutoSizeMode = True
        Me.SeparatorControl1.LineThickness = 1
        Me.SeparatorControl1.Location = New System.Drawing.Point(164, 20)
        Me.SeparatorControl1.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SeparatorControl1.Name = "SeparatorControl1"
        Me.SeparatorControl1.Padding = New System.Windows.Forms.Padding(8, 7, 8, 7)
        Me.SeparatorControl1.Size = New System.Drawing.Size(87, 15)
        Me.SeparatorControl1.TabIndex = 299
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.lbl1)
        Me.Panel1.Location = New System.Drawing.Point(823, 0)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(204, 48)
        Me.Panel1.TabIndex = 345
        '
        'lbl1
        '
        Me.lbl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lbl1.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lbl1.ForeColor = System.Drawing.Color.White
        Me.lbl1.Location = New System.Drawing.Point(7, 6)
        Me.lbl1.Name = "lbl1"
        Me.lbl1.Size = New System.Drawing.Size(189, 18)
        Me.lbl1.TabIndex = 319
        Me.lbl1.Text = "Sending in Progress, Please Wait..."
        Me.lbl1.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'LogInNormalTextBox1
        '
        Me.LogInNormalTextBox1.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.LogInNormalTextBox1.EditValue = ""
        Me.LogInNormalTextBox1.Location = New System.Drawing.Point(627, 58)
        Me.LogInNormalTextBox1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LogInNormalTextBox1.Name = "LogInNormalTextBox1"
        Me.LogInNormalTextBox1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.LogInNormalTextBox1.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LogInNormalTextBox1.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.LogInNormalTextBox1.Properties.Appearance.Options.UseBackColor = True
        Me.LogInNormalTextBox1.Properties.Appearance.Options.UseFont = True
        Me.LogInNormalTextBox1.Properties.Appearance.Options.UseForeColor = True
        Me.LogInNormalTextBox1.Properties.NullValuePrompt = "Sleep Time"
        Me.LogInNormalTextBox1.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.LogInNormalTextBox1.Size = New System.Drawing.Size(292, 30)
        Me.LogInNormalTextBox1.TabIndex = 66
        '
        'SeparatorControl6
        '
        Me.SeparatorControl6.LineColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.SeparatorControl6.LineOrientation = System.Windows.Forms.Orientation.Vertical
        Me.SeparatorControl6.LineThickness = 1
        Me.SeparatorControl6.Location = New System.Drawing.Point(1038, 121)
        Me.SeparatorControl6.LookAndFeel.SkinName = "DevExpress Dark Style"
        Me.SeparatorControl6.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl6.Name = "SeparatorControl6"
        Me.SeparatorControl6.Size = New System.Drawing.Size(86, 235)
        Me.SeparatorControl6.TabIndex = 487
        '
        'SimpleButton5
        '
        Me.SimpleButton5.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton5.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton5.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton5.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton5.Appearance.Options.UseBackColor = True
        Me.SimpleButton5.Appearance.Options.UseBorderColor = True
        Me.SimpleButton5.Appearance.Options.UseFont = True
        Me.SimpleButton5.Appearance.Options.UseForeColor = True
        Me.SimpleButton5.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton5.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton5.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton5.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton5.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton5.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton5.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton5.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton5.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton5.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton5.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton5.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton5.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton5.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton5.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton5.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton5.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton5.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton5.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton5.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton5.Location = New System.Drawing.Point(1084, 96)
        Me.SimpleButton5.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton5.Name = "SimpleButton5"
        Me.SimpleButton5.Size = New System.Drawing.Size(119, 45)
        Me.SimpleButton5.TabIndex = 496
        Me.SimpleButton5.Text = "SimpleButton5"
        '
        'XtraTabPage6
        '
        Me.XtraTabPage6.Controls.Add(Me.Panel8)
        Me.XtraTabPage6.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.XtraTabPage6.Name = "XtraTabPage6"
        Me.XtraTabPage6.Size = New System.Drawing.Size(773, 238)
        Me.XtraTabPage6.Text = "XtraTabPage6"
        '
        'Panel8
        '
        Me.Panel8.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Panel8.Controls.Add(Me.XtraTabControl2)
        Me.Panel8.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel8.Location = New System.Drawing.Point(0, 0)
        Me.Panel8.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Panel8.Name = "Panel8"
        Me.Panel8.Size = New System.Drawing.Size(773, 238)
        Me.Panel8.TabIndex = 0
        '
        'XtraTabControl2
        '
        Me.XtraTabControl2.AppearancePage.Header.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.XtraTabControl2.AppearancePage.Header.Options.UseBackColor = True
        Me.XtraTabControl2.AppearancePage.HeaderActive.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.XtraTabControl2.AppearancePage.HeaderActive.ForeColor = System.Drawing.Color.Black
        Me.XtraTabControl2.AppearancePage.HeaderActive.Options.UseBackColor = True
        Me.XtraTabControl2.AppearancePage.HeaderActive.Options.UseForeColor = True
        Me.XtraTabControl2.Location = New System.Drawing.Point(123, 55)
        Me.XtraTabControl2.Name = "XtraTabControl2"
        Me.XtraTabControl2.SelectedTabPage = Me.TabAddLogo
        Me.XtraTabControl2.Size = New System.Drawing.Size(663, 219)
        Me.XtraTabControl2.TabIndex = 321
        Me.XtraTabControl2.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.TabSendAttachment, Me.TabConvertLetterToPNG, Me.TabAddLogo, Me.ConvertLinkToQRCODE, Me.XtraTabPage3, Me.XtraTabPage4, Me.XtraTabPageSleepTime})
        '
        'TabAddLogo
        '
        Me.TabAddLogo.Controls.Add(Me.Panel6)
        Me.TabAddLogo.Name = "TabAddLogo"
        Me.TabAddLogo.Size = New System.Drawing.Size(661, 188)
        Me.TabAddLogo.Text = "Add Logo"
        '
        'Panel6
        '
        Me.Panel6.BackgroundImage = Global.Best_Sender.My.Resources.Resources.BKSettings
        Me.Panel6.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.Panel6.Controls.Add(Me.Label29)
        Me.Panel6.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel6.Location = New System.Drawing.Point(0, 0)
        Me.Panel6.Name = "Panel6"
        Me.Panel6.Size = New System.Drawing.Size(661, 188)
        Me.Panel6.TabIndex = 319
        '
        'Label29
        '
        Me.Label29.Font = New System.Drawing.Font("Comfortaa", 10.25!, System.Drawing.FontStyle.Bold)
        Me.Label29.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.Label29.Location = New System.Drawing.Point(90, 17)
        Me.Label29.Name = "Label29"
        Me.Label29.Size = New System.Drawing.Size(535, 18)
        Me.Label29.TabIndex = 0
        Me.Label29.Text = "Letter logo"
        Me.Label29.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'TabSendAttachment
        '
        Me.TabSendAttachment.Controls.Add(Me.Panel4)
        Me.TabSendAttachment.Name = "TabSendAttachment"
        Me.TabSendAttachment.Size = New System.Drawing.Size(661, 188)
        Me.TabSendAttachment.Text = "Send Attachment"
        '
        'Panel4
        '
        Me.Panel4.BackgroundImage = Global.Best_Sender.My.Resources.Resources.BKSettings
        Me.Panel4.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.Panel4.Controls.Add(Me.txtbody)
        Me.Panel4.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel4.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.Panel4.Location = New System.Drawing.Point(0, 0)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(661, 188)
        Me.Panel4.TabIndex = 317
        '
        'txtbody
        '
        Me.txtbody.Location = New System.Drawing.Point(37, 15)
        Me.txtbody.Multiline = True
        Me.txtbody.Name = "txtbody"
        Me.txtbody.Size = New System.Drawing.Size(23, 26)
        Me.txtbody.TabIndex = 324
        Me.txtbody.Visible = False
        '
        'TabConvertLetterToPNG
        '
        Me.TabConvertLetterToPNG.Controls.Add(Me.Panel5)
        Me.TabConvertLetterToPNG.Name = "TabConvertLetterToPNG"
        Me.TabConvertLetterToPNG.Size = New System.Drawing.Size(661, 188)
        Me.TabConvertLetterToPNG.Text = "Letter To [PNG]"
        '
        'Panel5
        '
        Me.Panel5.BackgroundImage = Global.Best_Sender.My.Resources.Resources.BKSettings
        Me.Panel5.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel5.Location = New System.Drawing.Point(0, 0)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(661, 188)
        Me.Panel5.TabIndex = 318
        '
        'ConvertLinkToQRCODE
        '
        Me.ConvertLinkToQRCODE.Controls.Add(Me.Panel10)
        Me.ConvertLinkToQRCODE.Name = "ConvertLinkToQRCODE"
        Me.ConvertLinkToQRCODE.Size = New System.Drawing.Size(661, 188)
        Me.ConvertLinkToQRCODE.Text = "Convert Link To QRCode"
        '
        'Panel10
        '
        Me.Panel10.BackgroundImage = Global.Best_Sender.My.Resources.Resources.BKSettings
        Me.Panel10.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.Panel10.Controls.Add(Me.Label30)
        Me.Panel10.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel10.Location = New System.Drawing.Point(0, 0)
        Me.Panel10.Name = "Panel10"
        Me.Panel10.Size = New System.Drawing.Size(661, 188)
        Me.Panel10.TabIndex = 321
        '
        'Label30
        '
        Me.Label30.Font = New System.Drawing.Font("Comfortaa", 10.25!, System.Drawing.FontStyle.Bold)
        Me.Label30.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.Label30.Location = New System.Drawing.Point(184, 18)
        Me.Label30.Name = "Label30"
        Me.Label30.Size = New System.Drawing.Size(313, 18)
        Me.Label30.TabIndex = 0
        Me.Label30.Text = "Convert Link To QRCode"
        Me.Label30.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'XtraTabPage3
        '
        Me.XtraTabPage3.Controls.Add(Me.Panel9)
        Me.XtraTabPage3.Name = "XtraTabPage3"
        Me.XtraTabPage3.Size = New System.Drawing.Size(661, 188)
        Me.XtraTabPage3.Text = "Limit Send"
        '
        'Panel9
        '
        Me.Panel9.BackgroundImage = Global.Best_Sender.My.Resources.Resources.BKSettings
        Me.Panel9.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.Panel9.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel9.Location = New System.Drawing.Point(0, 0)
        Me.Panel9.Name = "Panel9"
        Me.Panel9.Size = New System.Drawing.Size(661, 188)
        Me.Panel9.TabIndex = 320
        '
        'XtraTabPage4
        '
        Me.XtraTabPage4.Controls.Add(Me.Panel11)
        Me.XtraTabPage4.Name = "XtraTabPage4"
        Me.XtraTabPage4.Size = New System.Drawing.Size(661, 188)
        Me.XtraTabPage4.Text = "H/N/L"
        '
        'Panel11
        '
        Me.Panel11.BackgroundImage = Global.Best_Sender.My.Resources.Resources.BKSettings
        Me.Panel11.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.Panel11.Controls.Add(Me.Label31)
        Me.Panel11.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel11.Location = New System.Drawing.Point(0, 0)
        Me.Panel11.Name = "Panel11"
        Me.Panel11.Size = New System.Drawing.Size(661, 188)
        Me.Panel11.TabIndex = 319
        '
        'Label31
        '
        Me.Label31.Font = New System.Drawing.Font("Comfortaa", 10.25!, System.Drawing.FontStyle.Bold)
        Me.Label31.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.Label31.Location = New System.Drawing.Point(62, 16)
        Me.Label31.Name = "Label31"
        Me.Label31.Size = New System.Drawing.Size(569, 18)
        Me.Label31.TabIndex = 0
        Me.Label31.Text = "Message importance "
        Me.Label31.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'XtraTabPageSleepTime
        '
        Me.XtraTabPageSleepTime.Controls.Add(Me.Panel12)
        Me.XtraTabPageSleepTime.Name = "XtraTabPageSleepTime"
        Me.XtraTabPageSleepTime.Size = New System.Drawing.Size(661, 188)
        Me.XtraTabPageSleepTime.Text = "Sleep Time"
        '
        'Panel12
        '
        Me.Panel12.BackgroundImage = Global.Best_Sender.My.Resources.Resources.BKSettings
        Me.Panel12.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.Panel12.Controls.Add(Me.Label32)
        Me.Panel12.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel12.Location = New System.Drawing.Point(0, 0)
        Me.Panel12.Name = "Panel12"
        Me.Panel12.Size = New System.Drawing.Size(661, 188)
        Me.Panel12.TabIndex = 321
        '
        'Label32
        '
        Me.Label32.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Label32.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.Label32.Location = New System.Drawing.Point(52, 18)
        Me.Label32.Name = "Label32"
        Me.Label32.Size = New System.Drawing.Size(576, 18)
        Me.Label32.TabIndex = 0
        Me.Label32.Text = "Sleep Time"
        Me.Label32.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'XtraTabPage5
        '
        Me.XtraTabPage5.Appearance.PageClient.BackColor = System.Drawing.Color.Gray
        Me.XtraTabPage5.Appearance.PageClient.Options.UseBackColor = True
        Me.XtraTabPage5.Controls.Add(Me.Panel15)
        Me.XtraTabPage5.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.XtraTabPage5.Name = "XtraTabPage5"
        Me.XtraTabPage5.Size = New System.Drawing.Size(773, 238)
        Me.XtraTabPage5.Text = "Settings Letter"
        '
        'Panel15
        '
        Me.Panel15.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Panel15.Controls.Add(Me.LayoutControl4)
        Me.Panel15.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel15.Location = New System.Drawing.Point(0, 0)
        Me.Panel15.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Panel15.Name = "Panel15"
        Me.Panel15.Size = New System.Drawing.Size(773, 238)
        Me.Panel15.TabIndex = 0
        '
        'LayoutControl4
        '
        Me.LayoutControl4.Controls.Add(Me.GroupControl8)
        Me.LayoutControl4.Controls.Add(Me.GroupControl7)
        Me.LayoutControl4.Controls.Add(Me.GroupControl5)
        Me.LayoutControl4.Controls.Add(Me.GroupControl3)
        Me.LayoutControl4.Controls.Add(Me.GroupControl4)
        Me.LayoutControl4.Controls.Add(Me.GroupControl2)
        Me.LayoutControl4.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl4.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LayoutControl4.Name = "LayoutControl4"
        Me.LayoutControl4.Root = Me.LayoutControlGroup3
        Me.LayoutControl4.Size = New System.Drawing.Size(773, 238)
        Me.LayoutControl4.TabIndex = 0
        Me.LayoutControl4.Text = "LayoutControl4"
        '
        'GroupControl8
        '
        Me.GroupControl8.Controls.Add(Me.SeparatorControl3)
        Me.GroupControl8.Controls.Add(Me.ComboBoxSleepTime)
        Me.GroupControl8.Controls.Add(Me.chk_limitSend)
        Me.GroupControl8.Controls.Add(Me.Label22)
        Me.GroupControl8.Controls.Add(Me.Label23)
        Me.GroupControl8.Controls.Add(Me.Label24)
        Me.GroupControl8.Controls.Add(Me.TextEdit1)
        Me.GroupControl8.Controls.Add(Me.SeparatorControl4)
        Me.GroupControl8.Location = New System.Drawing.Point(511, 117)
        Me.GroupControl8.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupControl8.Name = "GroupControl8"
        Me.GroupControl8.Size = New System.Drawing.Size(246, 105)
        Me.GroupControl8.TabIndex = 322
        Me.GroupControl8.Text = "Sleep Time / Limit Send"
        '
        'SeparatorControl3
        '
        Me.SeparatorControl3.LineThickness = 1
        Me.SeparatorControl3.Location = New System.Drawing.Point(78, 188)
        Me.SeparatorControl3.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl3.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl3.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SeparatorControl3.Name = "SeparatorControl3"
        Me.SeparatorControl3.Padding = New System.Windows.Forms.Padding(8, 7, 8, 7)
        Me.SeparatorControl3.Size = New System.Drawing.Size(160, 19)
        Me.SeparatorControl3.TabIndex = 301
        '
        'ComboBoxSleepTime
        '
        Me.ComboBoxSleepTime.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboBoxSleepTime.EditValue = "1000"
        Me.ComboBoxSleepTime.Location = New System.Drawing.Point(78, 49)
        Me.ComboBoxSleepTime.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ComboBoxSleepTime.Name = "ComboBoxSleepTime"
        Me.ComboBoxSleepTime.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboBoxSleepTime.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ComboBoxSleepTime.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBoxSleepTime.Properties.Appearance.Options.UseFont = True
        Me.ComboBoxSleepTime.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxSleepTime.Properties.Items.AddRange(New Object() {"1000", "2000", "3000", "4000", "5000", "6000", "7000", "8000", "9000"})
        Me.ComboBoxSleepTime.Size = New System.Drawing.Size(141, 38)
        Me.ComboBoxSleepTime.TabIndex = 303
        '
        'chk_limitSend
        '
        Me.chk_limitSend.Cursor = System.Windows.Forms.Cursors.Hand
        Me.chk_limitSend.Location = New System.Drawing.Point(78, 152)
        Me.chk_limitSend.Name = "chk_limitSend"
        Me.chk_limitSend.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.chk_limitSend.Properties.Appearance.ForeColor = System.Drawing.SystemColors.GrayText
        Me.chk_limitSend.Properties.Appearance.Options.UseFont = True
        Me.chk_limitSend.Properties.Appearance.Options.UseForeColor = True
        Me.chk_limitSend.Properties.Caption = "Send just this And Stop"
        Me.chk_limitSend.Size = New System.Drawing.Size(159, 22)
        Me.chk_limitSend.TabIndex = 303
        '
        'Label22
        '
        Me.Label22.Font = New System.Drawing.Font("Audiowide", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label22.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Label22.Location = New System.Drawing.Point(106, 170)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(103, 25)
        Me.Label22.TabIndex = 0
        Me.Label22.Text = "Success"
        Me.Label22.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label23
        '
        Me.Label23.AutoSize = True
        Me.Label23.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Label23.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label23.Location = New System.Drawing.Point(121, 103)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(87, 23)
        Me.Label23.TabIndex = 302
        Me.Label23.Text = "Limit Send"
        '
        'Label24
        '
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Label24.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label24.Location = New System.Drawing.Point(90, 32)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(139, 23)
        Me.Label24.TabIndex = 302
        Me.Label24.Text = "Select Sleep Time"
        '
        'TextEdit1
        '
        Me.TextEdit1.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextEdit1.EditValue = ""
        Me.TextEdit1.Enabled = False
        Me.TextEdit1.Location = New System.Drawing.Point(29, 126)
        Me.TextEdit1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TextEdit1.Name = "TextEdit1"
        Me.TextEdit1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextEdit1.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEdit1.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.TextEdit1.Properties.Appearance.Options.UseFont = True
        Me.TextEdit1.Properties.Appearance.Options.UseForeColor = True
        Me.TextEdit1.Properties.NullValuePrompt = "Enter your limit Number Send"
        Me.TextEdit1.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextEdit1.Size = New System.Drawing.Size(258, 30)
        Me.TextEdit1.TabIndex = 304
        Me.TextEdit1.ToolTipTitle = "Height"
        '
        'SeparatorControl4
        '
        Me.SeparatorControl4.LineThickness = 1
        Me.SeparatorControl4.Location = New System.Drawing.Point(62, 81)
        Me.SeparatorControl4.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl4.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl4.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SeparatorControl4.Name = "SeparatorControl4"
        Me.SeparatorControl4.Padding = New System.Windows.Forms.Padding(8, 7, 8, 7)
        Me.SeparatorControl4.Size = New System.Drawing.Size(193, 19)
        Me.SeparatorControl4.TabIndex = 301
        '
        'GroupControl7
        '
        Me.GroupControl7.Controls.Add(Me.Label25)
        Me.GroupControl7.Controls.Add(Me.PictureEdit4)
        Me.GroupControl7.Controls.Add(Me.ComboFlagStatus)
        Me.GroupControl7.Controls.Add(Me.Label26)
        Me.GroupControl7.Controls.Add(Me.Label27)
        Me.GroupControl7.Controls.Add(Me.ComboSensitivity)
        Me.GroupControl7.Location = New System.Drawing.Point(247, 117)
        Me.GroupControl7.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupControl7.Name = "GroupControl7"
        Me.GroupControl7.Size = New System.Drawing.Size(258, 105)
        Me.GroupControl7.TabIndex = 322
        Me.GroupControl7.Text = "Message importance "
        '
        'Label25
        '
        Me.Label25.AutoSize = True
        Me.Label25.ForeColor = System.Drawing.Color.Silver
        Me.Label25.Location = New System.Drawing.Point(289, 171)
        Me.Label25.Name = "Label25"
        Me.Label25.Size = New System.Drawing.Size(54, 18)
        Me.Label25.TabIndex = 47
        Me.Label25.Text = "Flagged"
        '
        'PictureEdit4
        '
        Me.PictureEdit4.EditValue = CType(resources.GetObject("PictureEdit4.EditValue"), Object)
        Me.PictureEdit4.Location = New System.Drawing.Point(306, 138)
        Me.PictureEdit4.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PictureEdit4.Name = "PictureEdit4"
        Me.PictureEdit4.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureEdit4.Properties.Appearance.Options.UseBackColor = True
        Me.PictureEdit4.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureEdit4.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.PictureEdit4.Size = New System.Drawing.Size(25, 27)
        Me.PictureEdit4.TabIndex = 48
        '
        'ComboFlagStatus
        '
        Me.ComboFlagStatus.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboFlagStatus.EditValue = "notFlagged"
        Me.ComboFlagStatus.Location = New System.Drawing.Point(26, 137)
        Me.ComboFlagStatus.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ComboFlagStatus.Name = "ComboFlagStatus"
        Me.ComboFlagStatus.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboFlagStatus.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ComboFlagStatus.Properties.Appearance.Options.UseBackColor = True
        Me.ComboFlagStatus.Properties.Appearance.Options.UseFont = True
        Me.ComboFlagStatus.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboFlagStatus.Properties.Items.AddRange(New Object() {"flagged", "notFlagged"})
        Me.ComboFlagStatus.Size = New System.Drawing.Size(272, 38)
        Me.ComboFlagStatus.TabIndex = 45
        '
        'Label26
        '
        Me.Label26.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.Label26.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.Label26.Location = New System.Drawing.Point(21, 65)
        Me.Label26.Name = "Label26"
        Me.Label26.Size = New System.Drawing.Size(141, 18)
        Me.Label26.TabIndex = 0
        Me.Label26.Text = "Message Sensitivity"
        Me.Label26.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label27
        '
        Me.Label27.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.Label27.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.Label27.Location = New System.Drawing.Point(22, 117)
        Me.Label27.Name = "Label27"
        Me.Label27.Size = New System.Drawing.Size(85, 18)
        Me.Label27.TabIndex = 0
        Me.Label27.Text = "Flag Status"
        Me.Label27.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'ComboSensitivity
        '
        Me.ComboSensitivity.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboSensitivity.EditValue = "Normal"
        Me.ComboSensitivity.Location = New System.Drawing.Point(26, 85)
        Me.ComboSensitivity.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ComboSensitivity.Name = "ComboSensitivity"
        Me.ComboSensitivity.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboSensitivity.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ComboSensitivity.Properties.Appearance.Options.UseBackColor = True
        Me.ComboSensitivity.Properties.Appearance.Options.UseFont = True
        Me.ComboSensitivity.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboSensitivity.Properties.Items.AddRange(New Object() {"Confidential", "Normal", "Personal", "Private"})
        Me.ComboSensitivity.Size = New System.Drawing.Size(272, 38)
        Me.ComboSensitivity.TabIndex = 45
        '
        'GroupControl5
        '
        Me.GroupControl5.Controls.Add(Me.Pic_QRCode)
        Me.GroupControl5.Controls.Add(Me.lbl_success_QRcode)
        Me.GroupControl5.Controls.Add(Me.txtWidth)
        Me.GroupControl5.Controls.Add(Me.PictureEdit1)
        Me.GroupControl5.Controls.Add(Me.txtHeight)
        Me.GroupControl5.Controls.Add(Me.PictureEdit3)
        Me.GroupControl5.Controls.Add(Me.txtCode)
        Me.GroupControl5.Controls.Add(Me.PictureEdit2)
        Me.GroupControl5.Controls.Add(Me.SimpleButton3)
        Me.GroupControl5.Controls.Add(Me.btnGenerate)
        Me.GroupControl5.Controls.Add(Me.btnExport)
        Me.GroupControl5.Location = New System.Drawing.Point(16, 117)
        Me.GroupControl5.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupControl5.Name = "GroupControl5"
        Me.GroupControl5.Size = New System.Drawing.Size(225, 105)
        Me.GroupControl5.TabIndex = 322
        Me.GroupControl5.Text = "Convert Link To QRCode"
        '
        'Pic_QRCode
        '
        Me.Pic_QRCode.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Pic_QRCode.Location = New System.Drawing.Point(132, 58)
        Me.Pic_QRCode.Name = "Pic_QRCode"
        Me.Pic_QRCode.Size = New System.Drawing.Size(169, 103)
        Me.Pic_QRCode.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.Pic_QRCode.TabIndex = 302
        Me.Pic_QRCode.TabStop = False
        '
        'lbl_success_QRcode
        '
        Me.lbl_success_QRcode.Font = New System.Drawing.Font("Audiowide", 10.0!, System.Drawing.FontStyle.Bold)
        Me.lbl_success_QRcode.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.lbl_success_QRcode.Location = New System.Drawing.Point(5, 165)
        Me.lbl_success_QRcode.Name = "lbl_success_QRcode"
        Me.lbl_success_QRcode.Size = New System.Drawing.Size(80, 20)
        Me.lbl_success_QRcode.TabIndex = 311
        Me.lbl_success_QRcode.Text = "Success"
        Me.lbl_success_QRcode.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'txtWidth
        '
        Me.txtWidth.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtWidth.EditValue = "185"
        Me.txtWidth.Location = New System.Drawing.Point(120, 166)
        Me.txtWidth.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txtWidth.Name = "txtWidth"
        Me.txtWidth.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtWidth.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtWidth.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtWidth.Properties.Appearance.Options.UseBackColor = True
        Me.txtWidth.Properties.Appearance.Options.UseFont = True
        Me.txtWidth.Properties.Appearance.Options.UseForeColor = True
        Me.txtWidth.Properties.NullValuePrompt = "Width"
        Me.txtWidth.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtWidth.Size = New System.Drawing.Size(93, 30)
        Me.txtWidth.TabIndex = 322
        Me.txtWidth.ToolTipTitle = "Height"
        '
        'PictureEdit1
        '
        Me.PictureEdit1.EditValue = CType(resources.GetObject("PictureEdit1.EditValue"), Object)
        Me.PictureEdit1.Location = New System.Drawing.Point(4, 28)
        Me.PictureEdit1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PictureEdit1.Name = "PictureEdit1"
        Me.PictureEdit1.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.PictureEdit1.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureEdit1.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.PictureEdit1.Size = New System.Drawing.Size(27, 25)
        Me.PictureEdit1.TabIndex = 318
        '
        'txtHeight
        '
        Me.txtHeight.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtHeight.EditValue = "185"
        Me.txtHeight.Location = New System.Drawing.Point(231, 166)
        Me.txtHeight.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txtHeight.Name = "txtHeight"
        Me.txtHeight.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtHeight.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtHeight.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtHeight.Properties.Appearance.Options.UseBackColor = True
        Me.txtHeight.Properties.Appearance.Options.UseFont = True
        Me.txtHeight.Properties.Appearance.Options.UseForeColor = True
        Me.txtHeight.Properties.NullValuePrompt = "Height"
        Me.txtHeight.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtHeight.Size = New System.Drawing.Size(70, 30)
        Me.txtHeight.TabIndex = 322
        Me.txtHeight.ToolTipTitle = "Height"
        '
        'PictureEdit3
        '
        Me.PictureEdit3.EditValue = CType(resources.GetObject("PictureEdit3.EditValue"), Object)
        Me.PictureEdit3.Location = New System.Drawing.Point(91, 165)
        Me.PictureEdit3.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PictureEdit3.Name = "PictureEdit3"
        Me.PictureEdit3.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureEdit3.Properties.Appearance.Options.UseBackColor = True
        Me.PictureEdit3.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureEdit3.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.PictureEdit3.Size = New System.Drawing.Size(27, 25)
        Me.PictureEdit3.TabIndex = 318
        '
        'txtCode
        '
        Me.txtCode.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtCode.EditValue = ""
        Me.txtCode.Location = New System.Drawing.Point(37, 31)
        Me.txtCode.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txtCode.Name = "txtCode"
        Me.txtCode.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtCode.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtCode.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtCode.Properties.Appearance.Options.UseBackColor = True
        Me.txtCode.Properties.Appearance.Options.UseFont = True
        Me.txtCode.Properties.Appearance.Options.UseForeColor = True
        Me.txtCode.Properties.NullValuePrompt = "Enter your link..."
        Me.txtCode.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtCode.Size = New System.Drawing.Size(264, 30)
        Me.txtCode.TabIndex = 317
        Me.txtCode.ToolTipTitle = "If you place a link here and click on Generate QRCode, "
        '
        'PictureEdit2
        '
        Me.PictureEdit2.EditValue = CType(resources.GetObject("PictureEdit2.EditValue"), Object)
        Me.PictureEdit2.Location = New System.Drawing.Point(208, 165)
        Me.PictureEdit2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PictureEdit2.Name = "PictureEdit2"
        Me.PictureEdit2.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureEdit2.Properties.Appearance.Options.UseBackColor = True
        Me.PictureEdit2.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureEdit2.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.PictureEdit2.Size = New System.Drawing.Size(27, 25)
        Me.PictureEdit2.TabIndex = 318
        '
        'SimpleButton3
        '
        Me.SimpleButton3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton3.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton3.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton3.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton3.Appearance.Options.UseBackColor = True
        Me.SimpleButton3.Appearance.Options.UseBorderColor = True
        Me.SimpleButton3.Appearance.Options.UseFont = True
        Me.SimpleButton3.Appearance.Options.UseForeColor = True
        Me.SimpleButton3.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton3.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton3.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton3.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton3.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton3.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton3.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton3.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton3.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton3.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton3.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton3.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton3.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton3.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton3.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton3.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton3.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton3.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton3.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton3.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.SimpleButton3.Location = New System.Drawing.Point(4, 131)
        Me.SimpleButton3.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SimpleButton3.Name = "SimpleButton3"
        Me.SimpleButton3.Size = New System.Drawing.Size(117, 27)
        Me.SimpleButton3.TabIndex = 321
        Me.SimpleButton3.Text = "Remove"
        '
        'btnGenerate
        '
        Me.btnGenerate.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnGenerate.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnGenerate.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.btnGenerate.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnGenerate.Appearance.Options.UseBackColor = True
        Me.btnGenerate.Appearance.Options.UseBorderColor = True
        Me.btnGenerate.Appearance.Options.UseFont = True
        Me.btnGenerate.Appearance.Options.UseForeColor = True
        Me.btnGenerate.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btnGenerate.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btnGenerate.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.btnGenerate.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnGenerate.AppearanceDisabled.Options.UseBackColor = True
        Me.btnGenerate.AppearanceDisabled.Options.UseBorderColor = True
        Me.btnGenerate.AppearanceDisabled.Options.UseFont = True
        Me.btnGenerate.AppearanceDisabled.Options.UseForeColor = True
        Me.btnGenerate.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btnGenerate.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnGenerate.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btnGenerate.AppearanceHovered.Options.UseBackColor = True
        Me.btnGenerate.AppearanceHovered.Options.UseBorderColor = True
        Me.btnGenerate.AppearanceHovered.Options.UseForeColor = True
        Me.btnGenerate.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnGenerate.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnGenerate.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btnGenerate.AppearancePressed.Options.UseBackColor = True
        Me.btnGenerate.AppearancePressed.Options.UseBorderColor = True
        Me.btnGenerate.AppearancePressed.Options.UseForeColor = True
        Me.btnGenerate.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnGenerate.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.QrcodeBTN
        Me.btnGenerate.Location = New System.Drawing.Point(4, 60)
        Me.btnGenerate.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.btnGenerate.Name = "btnGenerate"
        Me.btnGenerate.Size = New System.Drawing.Size(117, 27)
        Me.btnGenerate.TabIndex = 320
        Me.btnGenerate.Text = "Generate "
        '
        'btnExport
        '
        Me.btnExport.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnExport.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnExport.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.btnExport.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnExport.Appearance.Options.UseBackColor = True
        Me.btnExport.Appearance.Options.UseBorderColor = True
        Me.btnExport.Appearance.Options.UseFont = True
        Me.btnExport.Appearance.Options.UseForeColor = True
        Me.btnExport.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btnExport.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btnExport.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.btnExport.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnExport.AppearanceDisabled.Options.UseBackColor = True
        Me.btnExport.AppearanceDisabled.Options.UseBorderColor = True
        Me.btnExport.AppearanceDisabled.Options.UseFont = True
        Me.btnExport.AppearanceDisabled.Options.UseForeColor = True
        Me.btnExport.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btnExport.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnExport.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btnExport.AppearanceHovered.Options.UseBackColor = True
        Me.btnExport.AppearanceHovered.Options.UseBorderColor = True
        Me.btnExport.AppearanceHovered.Options.UseForeColor = True
        Me.btnExport.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnExport.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnExport.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btnExport.AppearancePressed.Options.UseBackColor = True
        Me.btnExport.AppearancePressed.Options.UseBorderColor = True
        Me.btnExport.AppearancePressed.Options.UseForeColor = True
        Me.btnExport.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnExport.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.qr_Save
        Me.btnExport.Location = New System.Drawing.Point(4, 95)
        Me.btnExport.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Size = New System.Drawing.Size(117, 27)
        Me.btnExport.TabIndex = 319
        Me.btnExport.Text = "Export "
        '
        'GroupControl3
        '
        Me.GroupControl3.Controls.Add(Me.ChkLogo)
        Me.GroupControl3.Controls.Add(Me.BntResetLetterLogo)
        Me.GroupControl3.Controls.Add(Me.lblLogoTitle)
        Me.GroupControl3.Controls.Add(Me.bntLetterLogo)
        Me.GroupControl3.Controls.Add(Me.piclogo)
        Me.GroupControl3.Location = New System.Drawing.Point(247, 16)
        Me.GroupControl3.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupControl3.Name = "GroupControl3"
        Me.GroupControl3.Size = New System.Drawing.Size(258, 95)
        Me.GroupControl3.TabIndex = 322
        Me.GroupControl3.Text = "Letter logo"
        '
        'ChkLogo
        '
        Me.ChkLogo.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ChkLogo.Location = New System.Drawing.Point(10, 25)
        Me.ChkLogo.Name = "ChkLogo"
        Me.ChkLogo.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.ChkLogo.Properties.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.ChkLogo.Properties.Appearance.Options.UseFont = True
        Me.ChkLogo.Properties.Appearance.Options.UseForeColor = True
        Me.ChkLogo.Properties.Caption = "Send without Logo......"
        Me.ChkLogo.Size = New System.Drawing.Size(147, 22)
        Me.ChkLogo.TabIndex = 48
        Me.ChkLogo.Visible = False
        '
        'BntResetLetterLogo
        '
        Me.BntResetLetterLogo.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetLetterLogo.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetLetterLogo.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntResetLetterLogo.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetLetterLogo.Appearance.Options.UseBackColor = True
        Me.BntResetLetterLogo.Appearance.Options.UseBorderColor = True
        Me.BntResetLetterLogo.Appearance.Options.UseFont = True
        Me.BntResetLetterLogo.Appearance.Options.UseForeColor = True
        Me.BntResetLetterLogo.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntResetLetterLogo.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntResetLetterLogo.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntResetLetterLogo.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntResetLetterLogo.AppearanceDisabled.Options.UseBackColor = True
        Me.BntResetLetterLogo.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntResetLetterLogo.AppearanceDisabled.Options.UseFont = True
        Me.BntResetLetterLogo.AppearanceDisabled.Options.UseForeColor = True
        Me.BntResetLetterLogo.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntResetLetterLogo.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetLetterLogo.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntResetLetterLogo.AppearanceHovered.Options.UseBackColor = True
        Me.BntResetLetterLogo.AppearanceHovered.Options.UseBorderColor = True
        Me.BntResetLetterLogo.AppearanceHovered.Options.UseForeColor = True
        Me.BntResetLetterLogo.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetLetterLogo.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetLetterLogo.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntResetLetterLogo.AppearancePressed.Options.UseBackColor = True
        Me.BntResetLetterLogo.AppearancePressed.Options.UseBorderColor = True
        Me.BntResetLetterLogo.AppearancePressed.Options.UseForeColor = True
        Me.BntResetLetterLogo.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntResetLetterLogo.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntResetLetterLogo.Location = New System.Drawing.Point(5, 87)
        Me.BntResetLetterLogo.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.BntResetLetterLogo.Name = "BntResetLetterLogo"
        Me.BntResetLetterLogo.Size = New System.Drawing.Size(158, 27)
        Me.BntResetLetterLogo.TabIndex = 50
        Me.BntResetLetterLogo.Text = "Remove Logo"
        '
        'lblLogoTitle
        '
        Me.lblLogoTitle.AutoSize = True
        Me.lblLogoTitle.BackColor = System.Drawing.Color.Transparent
        Me.lblLogoTitle.Font = New System.Drawing.Font("Tahoma", 7.0!)
        Me.lblLogoTitle.ForeColor = System.Drawing.Color.Gray
        Me.lblLogoTitle.Location = New System.Drawing.Point(219, 124)
        Me.lblLogoTitle.Name = "lblLogoTitle"
        Me.lblLogoTitle.Size = New System.Drawing.Size(59, 12)
        Me.lblLogoTitle.TabIndex = 47
        Me.lblLogoTitle.Text = "Add Logo..."
        '
        'bntLetterLogo
        '
        Me.bntLetterLogo.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntLetterLogo.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntLetterLogo.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bntLetterLogo.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntLetterLogo.Appearance.Options.UseBackColor = True
        Me.bntLetterLogo.Appearance.Options.UseBorderColor = True
        Me.bntLetterLogo.Appearance.Options.UseFont = True
        Me.bntLetterLogo.Appearance.Options.UseForeColor = True
        Me.bntLetterLogo.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntLetterLogo.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntLetterLogo.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bntLetterLogo.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntLetterLogo.AppearanceDisabled.Options.UseBackColor = True
        Me.bntLetterLogo.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntLetterLogo.AppearanceDisabled.Options.UseFont = True
        Me.bntLetterLogo.AppearanceDisabled.Options.UseForeColor = True
        Me.bntLetterLogo.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntLetterLogo.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntLetterLogo.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntLetterLogo.AppearanceHovered.Options.UseBackColor = True
        Me.bntLetterLogo.AppearanceHovered.Options.UseBorderColor = True
        Me.bntLetterLogo.AppearanceHovered.Options.UseForeColor = True
        Me.bntLetterLogo.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntLetterLogo.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntLetterLogo.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntLetterLogo.AppearancePressed.Options.UseBackColor = True
        Me.bntLetterLogo.AppearancePressed.Options.UseBorderColor = True
        Me.bntLetterLogo.AppearancePressed.Options.UseForeColor = True
        Me.bntLetterLogo.Cursor = System.Windows.Forms.Cursors.Hand
        Me.bntLetterLogo.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.imageimport
        Me.bntLetterLogo.Location = New System.Drawing.Point(5, 48)
        Me.bntLetterLogo.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.bntLetterLogo.Name = "bntLetterLogo"
        Me.bntLetterLogo.Size = New System.Drawing.Size(158, 27)
        Me.bntLetterLogo.TabIndex = 49
        Me.bntLetterLogo.Text = "Select Logo"
        '
        'piclogo
        '
        Me.piclogo.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.piclogo.Location = New System.Drawing.Point(165, 24)
        Me.piclogo.Name = "piclogo"
        Me.piclogo.Size = New System.Drawing.Size(166, 112)
        Me.piclogo.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.piclogo.TabIndex = 46
        Me.piclogo.TabStop = False
        '
        'GroupControl4
        '
        Me.GroupControl4.Controls.Add(Me.ComboBoxEdit1)
        Me.GroupControl4.Controls.Add(Me.bntResetLetterConvertor)
        Me.GroupControl4.Controls.Add(Me.Label28)
        Me.GroupControl4.Controls.Add(Me.trcThreads)
        Me.GroupControl4.Controls.Add(Me.txtTitilOffice365)
        Me.GroupControl4.Controls.Add(Me.Switch_LetterConvertorLink)
        Me.GroupControl4.Location = New System.Drawing.Point(16, 16)
        Me.GroupControl4.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupControl4.Name = "GroupControl4"
        Me.GroupControl4.Size = New System.Drawing.Size(225, 95)
        Me.GroupControl4.TabIndex = 322
        Me.GroupControl4.Text = "Letter Convertor "
        '
        'ComboBoxEdit1
        '
        Me.ComboBoxEdit1.EditValue = ""
        Me.ComboBoxEdit1.Location = New System.Drawing.Point(232, 27)
        Me.ComboBoxEdit1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ComboBoxEdit1.Name = "ComboBoxEdit1"
        Me.ComboBoxEdit1.Properties.AllowFocused = False
        Me.ComboBoxEdit1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.ComboBoxEdit1.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.ComboBoxEdit1.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.ComboBoxEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBoxEdit1.Properties.Appearance.Options.UseFont = True
        Me.ComboBoxEdit1.Properties.Appearance.Options.UseForeColor = True
        Me.ComboBoxEdit1.Properties.AppearanceDropDown.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(44, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.ComboBoxEdit1.Properties.AppearanceDropDown.ForeColor = System.Drawing.Color.White
        Me.ComboBoxEdit1.Properties.AppearanceDropDown.Options.UseBackColor = True
        Me.ComboBoxEdit1.Properties.AppearanceDropDown.Options.UseForeColor = True
        Me.ComboBoxEdit1.Properties.AppearanceFocused.ForeColor = System.Drawing.Color.White
        Me.ComboBoxEdit1.Properties.AppearanceFocused.Options.UseForeColor = True
        Me.ComboBoxEdit1.Properties.AppearanceItemHighlight.BackColor = System.Drawing.Color.DarkSlateGray
        Me.ComboBoxEdit1.Properties.AppearanceItemHighlight.ForeColor = System.Drawing.Color.White
        Me.ComboBoxEdit1.Properties.AppearanceItemHighlight.Options.UseBackColor = True
        Me.ComboBoxEdit1.Properties.AppearanceItemHighlight.Options.UseForeColor = True
        Me.ComboBoxEdit1.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.ComboBoxEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxEdit1.Properties.DropDownRows = 20
        Me.ComboBoxEdit1.Properties.Items.AddRange(New Object() {"Add Link", "Encoded Email 64", "Random Browser", "Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Country", "Random IP", "Random IP China", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Send With Logo", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show Random Fake Email Address Office", "Show Random Fake Phone Numbers", "Show Random Street New York", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.ComboBoxEdit1.Properties.LookAndFeel.SkinName = "Sharp"
        Me.ComboBoxEdit1.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ComboBoxEdit1.Properties.NullValuePrompt = "Select Tag..."
        Me.ComboBoxEdit1.Properties.Sorted = True
        Me.ComboBoxEdit1.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ComboBoxEdit1.Size = New System.Drawing.Size(65, 22)
        Me.ComboBoxEdit1.TabIndex = 45
        Me.ComboBoxEdit1.Visible = False
        '
        'bntResetLetterConvertor
        '
        Me.bntResetLetterConvertor.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntResetLetterConvertor.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntResetLetterConvertor.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bntResetLetterConvertor.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntResetLetterConvertor.Appearance.Options.UseBackColor = True
        Me.bntResetLetterConvertor.Appearance.Options.UseBorderColor = True
        Me.bntResetLetterConvertor.Appearance.Options.UseFont = True
        Me.bntResetLetterConvertor.Appearance.Options.UseForeColor = True
        Me.bntResetLetterConvertor.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntResetLetterConvertor.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntResetLetterConvertor.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bntResetLetterConvertor.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntResetLetterConvertor.AppearanceDisabled.Options.UseBackColor = True
        Me.bntResetLetterConvertor.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntResetLetterConvertor.AppearanceDisabled.Options.UseFont = True
        Me.bntResetLetterConvertor.AppearanceDisabled.Options.UseForeColor = True
        Me.bntResetLetterConvertor.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntResetLetterConvertor.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntResetLetterConvertor.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntResetLetterConvertor.AppearanceHovered.Options.UseBackColor = True
        Me.bntResetLetterConvertor.AppearanceHovered.Options.UseBorderColor = True
        Me.bntResetLetterConvertor.AppearanceHovered.Options.UseForeColor = True
        Me.bntResetLetterConvertor.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntResetLetterConvertor.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntResetLetterConvertor.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntResetLetterConvertor.AppearancePressed.Options.UseBackColor = True
        Me.bntResetLetterConvertor.AppearancePressed.Options.UseBorderColor = True
        Me.bntResetLetterConvertor.AppearancePressed.Options.UseForeColor = True
        Me.bntResetLetterConvertor.Cursor = System.Windows.Forms.Cursors.Hand
        Me.bntResetLetterConvertor.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.bntResetLetterConvertor.Location = New System.Drawing.Point(199, 48)
        Me.bntResetLetterConvertor.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.bntResetLetterConvertor.Name = "bntResetLetterConvertor"
        Me.bntResetLetterConvertor.Size = New System.Drawing.Size(101, 25)
        Me.bntResetLetterConvertor.TabIndex = 401
        Me.bntResetLetterConvertor.Text = "Remove"
        '
        'Label28
        '
        Me.Label28.AutoSize = True
        Me.Label28.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Label28.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label28.Location = New System.Drawing.Point(105, 116)
        Me.Label28.Name = "Label28"
        Me.Label28.Size = New System.Drawing.Size(98, 23)
        Me.Label28.TabIndex = 302
        Me.Label28.Text = "Speed Send"
        '
        'trcThreads
        '
        Me.trcThreads.Cursor = System.Windows.Forms.Cursors.Hand
        Me.trcThreads.EditValue = 1
        Me.trcThreads.Location = New System.Drawing.Point(37, 129)
        Me.trcThreads.Name = "trcThreads"
        Me.trcThreads.Properties.ExportMode = DevExpress.XtraEditors.Repository.ExportMode.DisplayText
        Me.trcThreads.Properties.LabelAppearance.Options.UseTextOptions = True
        Me.trcThreads.Properties.LabelAppearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.trcThreads.Properties.Maximum = 50
        Me.trcThreads.Properties.Minimum = 1
        Me.trcThreads.Size = New System.Drawing.Size(215, 45)
        Me.trcThreads.TabIndex = 326
        Me.trcThreads.Value = 1
        Me.trcThreads.Visible = False
        '
        'txtTitilOffice365
        '
        Me.txtTitilOffice365.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtTitilOffice365.EditValue = "No preview is available."
        Me.txtTitilOffice365.Location = New System.Drawing.Point(8, 25)
        Me.txtTitilOffice365.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txtTitilOffice365.Name = "txtTitilOffice365"
        Me.txtTitilOffice365.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtTitilOffice365.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtTitilOffice365.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtTitilOffice365.Properties.Appearance.Options.UseBackColor = True
        Me.txtTitilOffice365.Properties.Appearance.Options.UseFont = True
        Me.txtTitilOffice365.Properties.Appearance.Options.UseForeColor = True
        Me.txtTitilOffice365.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtTitilOffice365.Size = New System.Drawing.Size(292, 30)
        Me.txtTitilOffice365.TabIndex = 65
        Me.txtTitilOffice365.ToolTipTitle = "Choose a name For Attachment"
        '
        'Switch_LetterConvertorLink
        '
        Me.Switch_LetterConvertorLink.Location = New System.Drawing.Point(8, 52)
        Me.Switch_LetterConvertorLink.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Switch_LetterConvertorLink.Name = "Switch_LetterConvertorLink"
        Me.Switch_LetterConvertorLink.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Switch_LetterConvertorLink.Properties.Appearance.Options.UseFont = True
        Me.Switch_LetterConvertorLink.Properties.OffText = "Letter to [ HTML]"
        Me.Switch_LetterConvertorLink.Properties.OnText = "Letter To PNG"
        Me.Switch_LetterConvertorLink.Size = New System.Drawing.Size(174, 27)
        Me.Switch_LetterConvertorLink.TabIndex = 1
        '
        'GroupControl2
        '
        Me.GroupControl2.Controls.Add(Me.BntRemoveAttachment)
        Me.GroupControl2.Controls.Add(Me.lblattacmentFileCount)
        Me.GroupControl2.Controls.Add(Me.ToggleSwitch1)
        Me.GroupControl2.Controls.Add(Me.bntAddAttachment)
        Me.GroupControl2.Controls.Add(Me.txtattach)
        Me.GroupControl2.Controls.Add(Me.cbTags)
        Me.GroupControl2.Location = New System.Drawing.Point(511, 16)
        Me.GroupControl2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.Size = New System.Drawing.Size(246, 95)
        Me.GroupControl2.TabIndex = 322
        Me.GroupControl2.Text = "Send Attachment | Letter Attachment"
        '
        'BntRemoveAttachment
        '
        Me.BntRemoveAttachment.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntRemoveAttachment.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntRemoveAttachment.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntRemoveAttachment.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntRemoveAttachment.Appearance.Options.UseBackColor = True
        Me.BntRemoveAttachment.Appearance.Options.UseBorderColor = True
        Me.BntRemoveAttachment.Appearance.Options.UseFont = True
        Me.BntRemoveAttachment.Appearance.Options.UseForeColor = True
        Me.BntRemoveAttachment.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntRemoveAttachment.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntRemoveAttachment.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntRemoveAttachment.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntRemoveAttachment.AppearanceDisabled.Options.UseBackColor = True
        Me.BntRemoveAttachment.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntRemoveAttachment.AppearanceDisabled.Options.UseFont = True
        Me.BntRemoveAttachment.AppearanceDisabled.Options.UseForeColor = True
        Me.BntRemoveAttachment.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntRemoveAttachment.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntRemoveAttachment.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntRemoveAttachment.AppearanceHovered.Options.UseBackColor = True
        Me.BntRemoveAttachment.AppearanceHovered.Options.UseBorderColor = True
        Me.BntRemoveAttachment.AppearanceHovered.Options.UseForeColor = True
        Me.BntRemoveAttachment.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntRemoveAttachment.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntRemoveAttachment.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntRemoveAttachment.AppearancePressed.Options.UseBackColor = True
        Me.BntRemoveAttachment.AppearancePressed.Options.UseBorderColor = True
        Me.BntRemoveAttachment.AppearancePressed.Options.UseForeColor = True
        Me.BntRemoveAttachment.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntRemoveAttachment.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntRemoveAttachment.Location = New System.Drawing.Point(173, 118)
        Me.BntRemoveAttachment.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.BntRemoveAttachment.Name = "BntRemoveAttachment"
        Me.BntRemoveAttachment.Size = New System.Drawing.Size(101, 25)
        Me.BntRemoveAttachment.TabIndex = 400
        Me.BntRemoveAttachment.Text = "Remove"
        '
        'lblattacmentFileCount
        '
        Me.lblattacmentFileCount.AutoSize = True
        Me.lblattacmentFileCount.Cursor = System.Windows.Forms.Cursors.Help
        Me.lblattacmentFileCount.Font = New System.Drawing.Font("Comfortaa", 7.25!, System.Drawing.FontStyle.Bold)
        Me.lblattacmentFileCount.ForeColor = System.Drawing.Color.DarkGray
        Me.lblattacmentFileCount.Location = New System.Drawing.Point(111, 30)
        Me.lblattacmentFileCount.Name = "lblattacmentFileCount"
        Me.lblattacmentFileCount.Size = New System.Drawing.Size(92, 16)
        Me.lblattacmentFileCount.TabIndex = 42
        Me.lblattacmentFileCount.Text = "Attached files : 0"
        '
        'ToggleSwitch1
        '
        Me.ToggleSwitch1.Location = New System.Drawing.Point(57, 41)
        Me.ToggleSwitch1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ToggleSwitch1.Name = "ToggleSwitch1"
        Me.ToggleSwitch1.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ToggleSwitch1.Properties.Appearance.Options.UseFont = True
        Me.ToggleSwitch1.Properties.OffText = "Normal Attachment"
        Me.ToggleSwitch1.Properties.OnText = "HTML To [ PDF ]"
        Me.ToggleSwitch1.Size = New System.Drawing.Size(195, 27)
        Me.ToggleSwitch1.TabIndex = 326
        '
        'bntAddAttachment
        '
        Me.bntAddAttachment.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntAddAttachment.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntAddAttachment.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bntAddAttachment.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntAddAttachment.Appearance.Options.UseBackColor = True
        Me.bntAddAttachment.Appearance.Options.UseBorderColor = True
        Me.bntAddAttachment.Appearance.Options.UseFont = True
        Me.bntAddAttachment.Appearance.Options.UseForeColor = True
        Me.bntAddAttachment.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntAddAttachment.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntAddAttachment.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bntAddAttachment.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntAddAttachment.AppearanceDisabled.Options.UseBackColor = True
        Me.bntAddAttachment.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntAddAttachment.AppearanceDisabled.Options.UseFont = True
        Me.bntAddAttachment.AppearanceDisabled.Options.UseForeColor = True
        Me.bntAddAttachment.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntAddAttachment.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntAddAttachment.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntAddAttachment.AppearanceHovered.Options.UseBackColor = True
        Me.bntAddAttachment.AppearanceHovered.Options.UseBorderColor = True
        Me.bntAddAttachment.AppearanceHovered.Options.UseForeColor = True
        Me.bntAddAttachment.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntAddAttachment.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntAddAttachment.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntAddAttachment.AppearancePressed.Options.UseBackColor = True
        Me.bntAddAttachment.AppearancePressed.Options.UseBorderColor = True
        Me.bntAddAttachment.AppearancePressed.Options.UseForeColor = True
        Me.bntAddAttachment.Cursor = System.Windows.Forms.Cursors.Hand
        Me.bntAddAttachment.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.documentpdf
        Me.bntAddAttachment.Location = New System.Drawing.Point(39, 119)
        Me.bntAddAttachment.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.bntAddAttachment.Name = "bntAddAttachment"
        Me.bntAddAttachment.Size = New System.Drawing.Size(101, 25)
        Me.bntAddAttachment.TabIndex = 399
        Me.bntAddAttachment.Text = "Add"
        '
        'txtattach
        '
        Me.txtattach.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtattach.EditValue = ""
        Me.txtattach.Location = New System.Drawing.Point(39, 92)
        Me.txtattach.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txtattach.Name = "txtattach"
        Me.txtattach.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtattach.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtattach.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtattach.Properties.Appearance.Options.UseBackColor = True
        Me.txtattach.Properties.Appearance.Options.UseFont = True
        Me.txtattach.Properties.Appearance.Options.UseForeColor = True
        Me.txtattach.Properties.NullValuePrompt = "Name Attachment"
        Me.txtattach.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtattach.Size = New System.Drawing.Size(235, 30)
        Me.txtattach.TabIndex = 398
        Me.txtattach.ToolTipTitle = "Choose a name For Attachment"
        '
        'cbTags
        '
        Me.cbTags.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cbTags.EditValue = ""
        Me.cbTags.Location = New System.Drawing.Point(39, 65)
        Me.cbTags.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.cbTags.Name = "cbTags"
        Me.cbTags.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.cbTags.Properties.Appearance.Options.UseBackColor = True
        Me.cbTags.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbTags.Properties.Items.AddRange(New Object() {"Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.cbTags.Size = New System.Drawing.Size(235, 32)
        Me.cbTags.TabIndex = 397
        '
        'LayoutControlGroup3
        '
        Me.LayoutControlGroup3.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup3.GroupBordersVisible = False
        Me.LayoutControlGroup3.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem7, Me.LayoutControlItem8, Me.LayoutControlItem6, Me.LayoutControlItem9, Me.LayoutControlItem11, Me.LayoutControlItem12})
        Me.LayoutControlGroup3.Name = "LayoutControlGroup3"
        Me.LayoutControlGroup3.Size = New System.Drawing.Size(773, 238)
        Me.LayoutControlGroup3.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.GroupControl4
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(231, 101)
        Me.LayoutControlItem7.TextVisible = False
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.GroupControl3
        Me.LayoutControlItem8.Location = New System.Drawing.Point(231, 0)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(264, 101)
        Me.LayoutControlItem8.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.GroupControl2
        Me.LayoutControlItem6.Location = New System.Drawing.Point(495, 0)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(252, 101)
        Me.LayoutControlItem6.TextVisible = False
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.GroupControl5
        Me.LayoutControlItem9.Location = New System.Drawing.Point(0, 101)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(231, 111)
        Me.LayoutControlItem9.TextVisible = False
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.GroupControl7
        Me.LayoutControlItem11.Location = New System.Drawing.Point(231, 101)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(264, 111)
        Me.LayoutControlItem11.TextVisible = False
        '
        'LayoutControlItem12
        '
        Me.LayoutControlItem12.Control = Me.GroupControl8
        Me.LayoutControlItem12.Location = New System.Drawing.Point(495, 101)
        Me.LayoutControlItem12.Name = "LayoutControlItem12"
        Me.LayoutControlItem12.Size = New System.Drawing.Size(252, 111)
        Me.LayoutControlItem12.TextVisible = False
        '
        'cmbSites
        '
        Me.cmbSites.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cmbSites.EditValue = ""
        Me.cmbSites.Location = New System.Drawing.Point(745, 108)
        Me.cmbSites.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.cmbSites.Name = "cmbSites"
        Me.cmbSites.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cmbSites.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cmbSites.Properties.Appearance.Options.UseBackColor = True
        Me.cmbSites.Properties.Appearance.Options.UseFont = True
        Me.cmbSites.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cmbSites.Size = New System.Drawing.Size(148, 38)
        Me.cmbSites.TabIndex = 45
        '
        'XtraTabPage2
        '
        Me.XtraTabPage2.Controls.Add(Me.Panel7)
        Me.XtraTabPage2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.XtraTabPage2.Name = "XtraTabPage2"
        Me.XtraTabPage2.Size = New System.Drawing.Size(773, 238)
        Me.XtraTabPage2.Text = "   Preparing Letter   "
        '
        'Panel7
        '
        Me.Panel7.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Panel7.Controls.Add(Me.LayoutControl2)
        Me.Panel7.Controls.Add(Me.lblTrcThreads)
        Me.Panel7.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel7.Location = New System.Drawing.Point(0, 0)
        Me.Panel7.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Panel7.Name = "Panel7"
        Me.Panel7.Size = New System.Drawing.Size(773, 238)
        Me.Panel7.TabIndex = 317
        '
        'LayoutControl2
        '
        Me.LayoutControl2.Controls.Add(Me.GroupControl1)
        Me.LayoutControl2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LayoutControl2.Name = "LayoutControl2"
        Me.LayoutControl2.Root = Me.LayoutControlGroup1
        Me.LayoutControl2.Size = New System.Drawing.Size(773, 238)
        Me.LayoutControl2.TabIndex = 328
        Me.LayoutControl2.Text = "LayoutControl2"
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.LayoutControl3)
        Me.GroupControl1.Location = New System.Drawing.Point(16, 16)
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(741, 206)
        Me.GroupControl1.TabIndex = 4
        Me.GroupControl1.Text = "Add Letter"
        '
        'LayoutControl3
        '
        Me.LayoutControl3.Controls.Add(Me.txtLetter)
        Me.LayoutControl3.Controls.Add(Me.txtLetterPath)
        Me.LayoutControl3.Controls.Add(Me.txtNewLink)
        Me.LayoutControl3.Controls.Add(Me.GroupControl9)
        Me.LayoutControl3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl3.Location = New System.Drawing.Point(2, 29)
        Me.LayoutControl3.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LayoutControl3.Name = "LayoutControl3"
        Me.LayoutControl3.Root = Me.LayoutControlGroup2
        Me.LayoutControl3.Size = New System.Drawing.Size(737, 175)
        Me.LayoutControl3.TabIndex = 0
        Me.LayoutControl3.Text = "LayoutControl3"
        '
        'txtLetter
        '
        Me.txtLetter.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtLetter.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.txtLetter.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.txtLetter.ForeColor = System.Drawing.Color.White
        Me.txtLetter.Location = New System.Drawing.Point(16, 111)
        Me.txtLetter.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txtLetter.Name = "txtLetter"
        Me.txtLetter.Size = New System.Drawing.Size(705, 48)
        Me.txtLetter.TabIndex = 67
        Me.txtLetter.Text = ""
        '
        'txtLetterPath
        '
        Me.txtLetterPath.EditValue = ""
        Me.txtLetterPath.Location = New System.Drawing.Point(125, 16)
        Me.txtLetterPath.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txtLetterPath.Name = "txtLetterPath"
        Me.txtLetterPath.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtLetterPath.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtLetterPath.Properties.Appearance.Options.UseFont = True
        Me.txtLetterPath.Properties.Appearance.Options.UseForeColor = True
        Me.txtLetterPath.Properties.NullValuePrompt = "Path File HTML..."
        Me.txtLetterPath.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtLetterPath.Size = New System.Drawing.Size(240, 30)
        Me.txtLetterPath.StyleController = Me.LayoutControl3
        Me.txtLetterPath.TabIndex = 66
        '
        'txtNewLink
        '
        Me.txtNewLink.EditValue = ""
        Me.txtNewLink.Location = New System.Drawing.Point(480, 16)
        Me.txtNewLink.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.txtNewLink.Name = "txtNewLink"
        Me.txtNewLink.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtNewLink.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtNewLink.Properties.Appearance.Options.UseFont = True
        Me.txtNewLink.Properties.Appearance.Options.UseForeColor = True
        Me.txtNewLink.Properties.NullValuePrompt = "If this word is [-Link-] in Your Letter Add your page link here"
        Me.txtNewLink.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtNewLink.Size = New System.Drawing.Size(241, 30)
        Me.txtNewLink.StyleController = Me.LayoutControl3
        Me.txtNewLink.TabIndex = 65
        '
        'GroupControl9
        '
        Me.GroupControl9.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControl9.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.GroupControl9.AppearanceCaption.Options.UseFont = True
        Me.GroupControl9.Controls.Add(Me.Panel16)
        Me.GroupControl9.Location = New System.Drawing.Point(14, 307)
        Me.GroupControl9.Margin = New System.Windows.Forms.Padding(3, 1, 3, 1)
        Me.GroupControl9.Name = "GroupControl9"
        Me.GroupControl9.Size = New System.Drawing.Size(895, 13)
        Me.GroupControl9.TabIndex = 400
        Me.GroupControl9.Text = "Normal"
        '
        'Panel16
        '
        Me.Panel16.Controls.Add(Me.LayoutControl5)
        Me.Panel16.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel16.Location = New System.Drawing.Point(2, 29)
        Me.Panel16.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Panel16.Name = "Panel16"
        Me.Panel16.Size = New System.Drawing.Size(891, 0)
        Me.Panel16.TabIndex = 1
        '
        'LayoutControl5
        '
        Me.LayoutControl5.Controls.Add(Me.LetterEncoder_txtFilepath)
        Me.LayoutControl5.Controls.Add(Me.LetterEncoder_Richtext)
        Me.LayoutControl5.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl5.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl5.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LayoutControl5.Name = "LayoutControl5"
        Me.LayoutControl5.Root = Me.LayoutControlGroup5
        Me.LayoutControl5.Size = New System.Drawing.Size(891, 0)
        Me.LayoutControl5.TabIndex = 0
        Me.LayoutControl5.Text = "LayoutControl2"
        '
        'LetterEncoder_txtFilepath
        '
        Me.LetterEncoder_txtFilepath.EditValue = ""
        Me.LetterEncoder_txtFilepath.Location = New System.Drawing.Point(119, 16)
        Me.LetterEncoder_txtFilepath.Margin = New System.Windows.Forms.Padding(3, 1, 3, 1)
        Me.LetterEncoder_txtFilepath.Name = "LetterEncoder_txtFilepath"
        Me.LetterEncoder_txtFilepath.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LetterEncoder_txtFilepath.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseBackColor = True
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseFont = True
        Me.LetterEncoder_txtFilepath.Properties.Appearance.Options.UseForeColor = True
        Me.LetterEncoder_txtFilepath.Properties.NullValuePrompt = "Enter a vaild host name..."
        Me.LetterEncoder_txtFilepath.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.LetterEncoder_txtFilepath.Size = New System.Drawing.Size(756, 30)
        Me.LetterEncoder_txtFilepath.StyleController = Me.LayoutControl5
        Me.LetterEncoder_txtFilepath.TabIndex = 63
        '
        'LetterEncoder_Richtext
        '
        Me.LetterEncoder_Richtext.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_Richtext.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.LetterEncoder_Richtext.ForeColor = System.Drawing.Color.White
        Me.LetterEncoder_Richtext.Location = New System.Drawing.Point(16, 62)
        Me.LetterEncoder_Richtext.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LetterEncoder_Richtext.Name = "LetterEncoder_Richtext"
        Me.LetterEncoder_Richtext.Size = New System.Drawing.Size(859, 20)
        Me.LetterEncoder_Richtext.TabIndex = 68
        Me.LetterEncoder_Richtext.Text = ""
        '
        'LayoutControlGroup5
        '
        Me.LayoutControlGroup5.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup5.GroupBordersVisible = False
        Me.LayoutControlGroup5.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem10, Me.EmptySpaceItem2, Me.LayoutControlItem13})
        Me.LayoutControlGroup5.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup5.Size = New System.Drawing.Size(891, 98)
        Me.LayoutControlGroup5.TextVisible = False
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.LetterEncoder_txtFilepath
        Me.LayoutControlItem10.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem10.Name = "LayoutControlItem2"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(865, 36)
        Me.LayoutControlItem10.Text = "Path File HTML:"
        Me.LayoutControlItem10.TextSize = New System.Drawing.Size(87, 18)
        '
        'EmptySpaceItem2
        '
        Me.EmptySpaceItem2.Location = New System.Drawing.Point(0, 36)
        Me.EmptySpaceItem2.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem2.Size = New System.Drawing.Size(865, 10)
        '
        'LayoutControlItem13
        '
        Me.LayoutControlItem13.Control = Me.LetterEncoder_Richtext
        Me.LayoutControlItem13.Location = New System.Drawing.Point(0, 46)
        Me.LayoutControlItem13.Name = "LayoutControlItem3"
        Me.LayoutControlItem13.Size = New System.Drawing.Size(865, 26)
        Me.LayoutControlItem13.TextVisible = False
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup2.GroupBordersVisible = False
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem3, Me.EmptySpaceItem1, Me.LayoutControlItem4, Me.LayoutControlItem5})
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(737, 175)
        Me.LayoutControlGroup2.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.txtNewLink
        Me.LayoutControlItem3.Location = New System.Drawing.Point(355, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(356, 36)
        Me.LayoutControlItem3.Text = "Enter Link Page:"
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(93, 18)
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 36)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(711, 59)
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.txtLetterPath
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(355, 36)
        Me.LayoutControlItem4.Text = "Path File HTML...."
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(93, 18)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.txtLetter
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 95)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(711, 54)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem2})
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(773, 238)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.GroupControl1
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(747, 212)
        Me.LayoutControlItem2.TextVisible = False
        '
        'lblTrcThreads
        '
        Me.lblTrcThreads.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.lblTrcThreads.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.lblTrcThreads.Location = New System.Drawing.Point(854, 253)
        Me.lblTrcThreads.Name = "lblTrcThreads"
        Me.lblTrcThreads.Size = New System.Drawing.Size(64, 22)
        Me.lblTrcThreads.TabIndex = 327
        Me.lblTrcThreads.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblTrcThreads.Visible = False
        '
        'TabSmtp
        '
        Me.TabSmtp.Appearance.PageClient.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(44, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.TabSmtp.Appearance.PageClient.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(44, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.TabSmtp.Appearance.PageClient.BorderColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(44, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.TabSmtp.Appearance.PageClient.Options.UseBackColor = True
        Me.TabSmtp.Appearance.PageClient.Options.UseBorderColor = True
        Me.TabSmtp.Controls.Add(Me.Panel2)
        Me.TabSmtp.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TabSmtp.Name = "TabSmtp"
        Me.TabSmtp.Size = New System.Drawing.Size(773, 238)
        Me.TabSmtp.Text = "    Smtp List    "
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Panel2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel2.Controls.Add(Me.ProgressBarControl3)
        Me.Panel2.Controls.Add(Me.Label21)
        Me.Panel2.Controls.Add(Me.GridControl1)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel2.ForeColor = System.Drawing.Color.White
        Me.Panel2.Location = New System.Drawing.Point(0, 0)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(773, 238)
        Me.Panel2.TabIndex = 24
        '
        'ProgressBarControl3
        '
        Me.ProgressBarControl3.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.ProgressBarControl3.Location = New System.Drawing.Point(0, 223)
        Me.ProgressBarControl3.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ProgressBarControl3.Name = "ProgressBarControl3"
        Me.ProgressBarControl3.Properties.ShowTitle = True
        Me.ProgressBarControl3.Size = New System.Drawing.Size(771, 13)
        Me.ProgressBarControl3.TabIndex = 321
        Me.ProgressBarControl3.Visible = False
        '
        'Label21
        '
        Me.Label21.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.2!, System.Drawing.FontStyle.Bold)
        Me.Label21.ForeColor = System.Drawing.Color.White
        Me.Label21.Location = New System.Drawing.Point(327, 145)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(299, 19)
        Me.Label21.TabIndex = 320
        Me.Label21.Text = "Press Mouse Right Click to See Extra Options..."
        Me.Label21.TextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.Label21.Visible = False
        '
        'GridControl1
        '
        Me.GridControl1.Dock = System.Windows.Forms.DockStyle.Top
        Me.GridControl1.EmbeddedNavigator.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GridControl1.Location = New System.Drawing.Point(0, 0)
        Me.GridControl1.LookAndFeel.SkinMaskColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.GridControl1.LookAndFeel.SkinMaskColor2 = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.GridControl1.LookAndFeel.SkinName = "DevExpress Dark Style"
        Me.GridControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(771, 293)
        Me.GridControl1.TabIndex = 302
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Appearance.Empty.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.Empty.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.Empty.Options.UseBackColor = True
        Me.GridView1.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.EvenRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.EvenRow.Options.UseBackColor = True
        Me.GridView1.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.GridView1.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.GridView1.Appearance.FocusedRow.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridView1.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White
        Me.GridView1.Appearance.FocusedRow.Options.UseBackColor = True
        Me.GridView1.Appearance.FocusedRow.Options.UseFont = True
        Me.GridView1.Appearance.FocusedRow.Options.UseForeColor = True
        Me.GridView1.Appearance.GroupRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.GroupRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.GroupRow.Options.UseBackColor = True
        Me.GridView1.Appearance.OddRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.OddRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.OddRow.Options.UseBackColor = True
        Me.GridView1.Appearance.Row.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.Row.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.Row.Options.UseBackColor = True
        Me.GridView1.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.GridView1.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.GridView1.Appearance.SelectedRow.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridView1.Appearance.SelectedRow.ForeColor = System.Drawing.Color.White
        Me.GridView1.Appearance.SelectedRow.Options.UseBackColor = True
        Me.GridView1.Appearance.SelectedRow.Options.UseFont = True
        Me.GridView1.Appearance.SelectedRow.Options.UseForeColor = True
        Me.GridView1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.GridView1.ColumnPanelRowHeight = 23
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2, Me.GridColumn3, Me.GridColumn8, Me.GridColumn11, Me.GridColumn7, Me.GridColumn4, Me.GridColumn5, Me.GridColumn6, Me.GridColumn9, Me.GridColumn10, Me.GridColumn23, Me.GridColumn24, Me.GridColumn25, Me.GridColumn18, Me.GridColumn19})
        Me.GridView1.DetailHeight = 205
        Me.GridView1.FixedLineWidth = 1
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.GroupRowHeight = 20
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsFind.AllowFindPanel = False
        Me.GridView1.OptionsFind.AllowMruItems = False
        Me.GridView1.OptionsView.BestFitMaxRowCount = 2
        Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsView.EnableAppearanceOddRow = True
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.OptionsView.ShowIndicator = False
        Me.GridView1.RowHeight = 7
        '
        'GridColumn1
        '
        Me.GridColumn1.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn1.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn1.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn1.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn1.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn1.AppearanceCell.Options.UseFont = True
        Me.GridColumn1.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn1.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn1.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn1.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn1.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn1.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn1.AppearanceHeader.Options.UseFont = True
        Me.GridColumn1.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn1.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn1.Caption = "#"
        Me.GridColumn1.FieldName = "id"
        Me.GridColumn1.MinWidth = 10
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.OptionsColumn.AllowEdit = False
        Me.GridColumn1.OptionsColumn.AllowFocus = False
        Me.GridColumn1.OptionsColumn.AllowMove = False
        Me.GridColumn1.OptionsColumn.AllowShowHide = False
        Me.GridColumn1.OptionsColumn.FixedWidth = True
        Me.GridColumn1.OptionsColumn.ReadOnly = True
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        Me.GridColumn1.Width = 9
        '
        'GridColumn2
        '
        Me.GridColumn2.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn2.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn2.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn2.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn2.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn2.AppearanceCell.Options.UseFont = True
        Me.GridColumn2.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn2.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn2.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn2.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn2.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn2.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn2.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn2.AppearanceHeader.Options.UseFont = True
        Me.GridColumn2.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn2.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn2.Caption = "Host"
        Me.GridColumn2.FieldName = "smtphost"
        Me.GridColumn2.MinWidth = 10
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.OptionsColumn.AllowEdit = False
        Me.GridColumn2.OptionsColumn.AllowFocus = False
        Me.GridColumn2.OptionsColumn.AllowMove = False
        Me.GridColumn2.OptionsColumn.AllowShowHide = False
        Me.GridColumn2.OptionsColumn.AllowSize = False
        Me.GridColumn2.OptionsColumn.FixedWidth = True
        Me.GridColumn2.OptionsColumn.ReadOnly = True
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 2
        Me.GridColumn2.Width = 51
        '
        'GridColumn3
        '
        Me.GridColumn3.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn3.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn3.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn3.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn3.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn3.AppearanceCell.Options.UseFont = True
        Me.GridColumn3.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn3.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn3.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn3.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn3.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn3.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn3.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn3.AppearanceHeader.Options.UseFont = True
        Me.GridColumn3.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn3.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn3.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn3.Caption = "Email Address"
        Me.GridColumn3.FieldName = "smtpemail"
        Me.GridColumn3.MinWidth = 10
        Me.GridColumn3.Name = "GridColumn3"
        Me.GridColumn3.OptionsColumn.AllowEdit = False
        Me.GridColumn3.OptionsColumn.AllowFocus = False
        Me.GridColumn3.OptionsColumn.AllowMove = False
        Me.GridColumn3.OptionsColumn.AllowShowHide = False
        Me.GridColumn3.OptionsColumn.AllowSize = False
        Me.GridColumn3.OptionsColumn.FixedWidth = True
        Me.GridColumn3.OptionsColumn.ReadOnly = True
        Me.GridColumn3.Visible = True
        Me.GridColumn3.VisibleIndex = 3
        Me.GridColumn3.Width = 59
        '
        'GridColumn8
        '
        Me.GridColumn8.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn8.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn8.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn8.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn8.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn8.AppearanceCell.Options.UseFont = True
        Me.GridColumn8.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn8.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn8.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn8.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn8.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn8.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn8.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn8.AppearanceHeader.Options.UseFont = True
        Me.GridColumn8.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn8.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn8.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn8.Caption = "Port"
        Me.GridColumn8.FieldName = "smtpport"
        Me.GridColumn8.MinWidth = 10
        Me.GridColumn8.Name = "GridColumn8"
        Me.GridColumn8.OptionsColumn.AllowEdit = False
        Me.GridColumn8.OptionsColumn.AllowFocus = False
        Me.GridColumn8.OptionsColumn.AllowMove = False
        Me.GridColumn8.OptionsColumn.AllowShowHide = False
        Me.GridColumn8.OptionsColumn.AllowSize = False
        Me.GridColumn8.OptionsColumn.FixedWidth = True
        Me.GridColumn8.OptionsColumn.ReadOnly = True
        Me.GridColumn8.Visible = True
        Me.GridColumn8.VisibleIndex = 4
        Me.GridColumn8.Width = 15
        '
        'GridColumn11
        '
        Me.GridColumn11.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn11.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn11.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn11.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn11.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn11.AppearanceCell.Options.UseFont = True
        Me.GridColumn11.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn11.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn11.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn11.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn11.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn11.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn11.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn11.AppearanceHeader.Options.UseFont = True
        Me.GridColumn11.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn11.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn11.Caption = "From Name"
        Me.GridColumn11.FieldName = "smtpfromname"
        Me.GridColumn11.MinWidth = 10
        Me.GridColumn11.Name = "GridColumn11"
        Me.GridColumn11.OptionsColumn.AllowEdit = False
        Me.GridColumn11.OptionsColumn.AllowFocus = False
        Me.GridColumn11.OptionsColumn.AllowMove = False
        Me.GridColumn11.OptionsColumn.AllowShowHide = False
        Me.GridColumn11.OptionsColumn.AllowSize = False
        Me.GridColumn11.OptionsColumn.FixedWidth = True
        Me.GridColumn11.OptionsColumn.ReadOnly = True
        Me.GridColumn11.Visible = True
        Me.GridColumn11.VisibleIndex = 6
        Me.GridColumn11.Width = 44
        '
        'GridColumn7
        '
        Me.GridColumn7.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn7.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn7.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn7.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn7.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn7.AppearanceCell.Options.UseFont = True
        Me.GridColumn7.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn7.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn7.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn7.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn7.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn7.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn7.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn7.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn7.AppearanceHeader.Options.UseFont = True
        Me.GridColumn7.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn7.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn7.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn7.Caption = "From Mail"
        Me.GridColumn7.FieldName = "smtpfrommail"
        Me.GridColumn7.MinWidth = 10
        Me.GridColumn7.Name = "GridColumn7"
        Me.GridColumn7.OptionsColumn.AllowEdit = False
        Me.GridColumn7.OptionsColumn.AllowFocus = False
        Me.GridColumn7.OptionsColumn.AllowMove = False
        Me.GridColumn7.OptionsColumn.AllowShowHide = False
        Me.GridColumn7.OptionsColumn.AllowSize = False
        Me.GridColumn7.OptionsColumn.FixedWidth = True
        Me.GridColumn7.OptionsColumn.ReadOnly = True
        Me.GridColumn7.Visible = True
        Me.GridColumn7.VisibleIndex = 7
        Me.GridColumn7.Width = 44
        '
        'GridColumn4
        '
        Me.GridColumn4.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn4.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn4.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn4.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn4.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn4.AppearanceCell.Options.UseFont = True
        Me.GridColumn4.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn4.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn4.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn4.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn4.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn4.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn4.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn4.AppearanceHeader.Options.UseFont = True
        Me.GridColumn4.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn4.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn4.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn4.Caption = "Subject"
        Me.GridColumn4.FieldName = "smtpsubject"
        Me.GridColumn4.MinWidth = 10
        Me.GridColumn4.Name = "GridColumn4"
        Me.GridColumn4.OptionsColumn.AllowEdit = False
        Me.GridColumn4.OptionsColumn.AllowFocus = False
        Me.GridColumn4.OptionsColumn.AllowMove = False
        Me.GridColumn4.OptionsColumn.AllowShowHide = False
        Me.GridColumn4.OptionsColumn.AllowSize = False
        Me.GridColumn4.OptionsColumn.FixedWidth = True
        Me.GridColumn4.OptionsColumn.ReadOnly = True
        Me.GridColumn4.Visible = True
        Me.GridColumn4.VisibleIndex = 8
        Me.GridColumn4.Width = 44
        '
        'GridColumn5
        '
        Me.GridColumn5.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn5.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn5.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn5.AppearanceCell.ForeColor = System.Drawing.Color.LightGray
        Me.GridColumn5.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn5.AppearanceCell.Options.UseFont = True
        Me.GridColumn5.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn5.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn5.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn5.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn5.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn5.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn5.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn5.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn5.AppearanceHeader.Options.UseFont = True
        Me.GridColumn5.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn5.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn5.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn5.Caption = "Details"
        Me.GridColumn5.MinWidth = 10
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.OptionsColumn.AllowMove = False
        Me.GridColumn5.OptionsColumn.AllowShowHide = False
        Me.GridColumn5.OptionsColumn.AllowSize = False
        Me.GridColumn5.OptionsColumn.FixedWidth = True
        Me.GridColumn5.OptionsColumn.ReadOnly = True
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 9
        Me.GridColumn5.Width = 21
        '
        'GridColumn6
        '
        Me.GridColumn6.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn6.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn6.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn6.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn6.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn6.AppearanceCell.Options.UseFont = True
        Me.GridColumn6.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn6.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn6.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn6.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn6.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn6.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn6.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn6.AppearanceHeader.Options.UseFont = True
        Me.GridColumn6.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn6.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn6.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn6.Caption = "Remove"
        Me.GridColumn6.FieldName = "CanCheck"
        Me.GridColumn6.MinWidth = 10
        Me.GridColumn6.Name = "GridColumn6"
        Me.GridColumn6.OptionsColumn.AllowMove = False
        Me.GridColumn6.OptionsColumn.AllowShowHide = False
        Me.GridColumn6.OptionsColumn.AllowSize = False
        Me.GridColumn6.OptionsColumn.FixedWidth = True
        Me.GridColumn6.OptionsColumn.ReadOnly = True
        Me.GridColumn6.Visible = True
        Me.GridColumn6.VisibleIndex = 10
        Me.GridColumn6.Width = 21
        '
        'GridColumn9
        '
        Me.GridColumn9.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn9.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn9.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn9.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn9.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn9.AppearanceCell.Options.UseFont = True
        Me.GridColumn9.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn9.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn9.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn9.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn9.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn9.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn9.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn9.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn9.AppearanceHeader.Options.UseFont = True
        Me.GridColumn9.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn9.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn9.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn9.Caption = "SSL"
        Me.GridColumn9.FieldName = "smtpssl"
        Me.GridColumn9.MinWidth = 10
        Me.GridColumn9.Name = "GridColumn9"
        Me.GridColumn9.OptionsColumn.AllowMove = False
        Me.GridColumn9.OptionsColumn.AllowShowHide = False
        Me.GridColumn9.OptionsColumn.AllowSize = False
        Me.GridColumn9.OptionsColumn.FixedWidth = True
        Me.GridColumn9.Visible = True
        Me.GridColumn9.VisibleIndex = 5
        Me.GridColumn9.Width = 14
        '
        'GridColumn10
        '
        Me.GridColumn10.Caption = "GridColumn10"
        Me.GridColumn10.FieldName = "errormsg"
        Me.GridColumn10.MinWidth = 18
        Me.GridColumn10.Name = "GridColumn10"
        Me.GridColumn10.Width = 69
        '
        'GridColumn23
        '
        Me.GridColumn23.Caption = "smtppassword"
        Me.GridColumn23.FieldName = "smtppassword"
        Me.GridColumn23.MinWidth = 18
        Me.GridColumn23.Name = "GridColumn23"
        Me.GridColumn23.Width = 69
        '
        'GridColumn24
        '
        Me.GridColumn24.Caption = "GridColumn24"
        Me.GridColumn24.FieldName = "tag1"
        Me.GridColumn24.MinWidth = 18
        Me.GridColumn24.Name = "GridColumn24"
        Me.GridColumn24.Width = 69
        '
        'GridColumn25
        '
        Me.GridColumn25.Caption = "GridColumn25"
        Me.GridColumn25.FieldName = "tag2"
        Me.GridColumn25.MinWidth = 18
        Me.GridColumn25.Name = "GridColumn25"
        Me.GridColumn25.Width = 69
        '
        'GridColumn18
        '
        Me.GridColumn18.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn18.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn18.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn18.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn18.AppearanceCell.Options.UseFont = True
        Me.GridColumn18.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn18.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn18.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn18.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn18.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn18.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn18.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn18.AppearanceHeader.Options.UseFont = True
        Me.GridColumn18.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn18.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn18.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn18.Caption = "Smtp Status"
        Me.GridColumn18.FieldName = "smtpstatus"
        Me.GridColumn18.MinWidth = 17
        Me.GridColumn18.Name = "GridColumn18"
        Me.GridColumn18.OptionsColumn.AllowEdit = False
        Me.GridColumn18.OptionsColumn.AllowFocus = False
        Me.GridColumn18.OptionsColumn.AllowIncrementalSearch = False
        Me.GridColumn18.OptionsColumn.AllowMove = False
        Me.GridColumn18.OptionsColumn.AllowShowHide = False
        Me.GridColumn18.OptionsColumn.AllowSize = False
        Me.GridColumn18.OptionsColumn.FixedWidth = True
        Me.GridColumn18.OptionsColumn.ReadOnly = True
        Me.GridColumn18.Visible = True
        Me.GridColumn18.VisibleIndex = 1
        Me.GridColumn18.Width = 33
        '
        'GridColumn19
        '
        Me.GridColumn19.Caption = "GridColumn19"
        Me.GridColumn19.FieldName = "taghost"
        Me.GridColumn19.MinWidth = 17
        Me.GridColumn19.Name = "GridColumn19"
        Me.GridColumn19.Width = 64
        '
        'PnlWaits
        '
        Me.PnlWaits.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.PnlWaits.Appearance.Options.UseBackColor = True
        Me.PnlWaits.AppearanceCaption.BackColor = System.Drawing.Color.Transparent
        Me.PnlWaits.AppearanceCaption.Options.UseBackColor = True
        Me.PnlWaits.Controls.Add(Me.SimpleButton2)
        Me.PnlWaits.Controls.Add(Me.ProgressPanel2)
        Me.PnlWaits.Location = New System.Drawing.Point(435, 508)
        Me.PnlWaits.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PnlWaits.Name = "PnlWaits"
        Me.PnlWaits.ShowCaption = False
        Me.PnlWaits.Size = New System.Drawing.Size(438, 68)
        Me.PnlWaits.TabIndex = 308
        Me.PnlWaits.Text = "GroupControl2"
        Me.PnlWaits.Visible = False
        '
        'SimpleButton2
        '
        Me.SimpleButton2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton2.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton2.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton2.Appearance.Options.UseBackColor = True
        Me.SimpleButton2.Appearance.Options.UseBorderColor = True
        Me.SimpleButton2.Appearance.Options.UseFont = True
        Me.SimpleButton2.Appearance.Options.UseForeColor = True
        Me.SimpleButton2.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton2.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton2.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton2.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton2.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton2.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton2.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton2.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton2.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton2.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton2.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton2.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton2.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton2.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton2.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton2.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton2.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton2.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton2.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton2.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.SimpleButton2.Location = New System.Drawing.Point(288, 14)
        Me.SimpleButton2.LookAndFeel.SkinName = "Visual Studio 2013 Light"
        Me.SimpleButton2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(117, 41)
        Me.SimpleButton2.TabIndex = 392
        Me.SimpleButton2.Text = "&Cancel"
        '
        'ProgressPanel2
        '
        Me.ProgressPanel2.AnimationToTextDistance = 10
        Me.ProgressPanel2.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel2.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.ProgressPanel2.Appearance.Options.UseBackColor = True
        Me.ProgressPanel2.Appearance.Options.UseFont = True
        Me.ProgressPanel2.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel2.AppearanceCaption.Options.UseFont = True
        Me.ProgressPanel2.AppearanceDescription.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel2.AppearanceDescription.Options.UseFont = True
        Me.ProgressPanel2.BarAnimationElementThickness = 5
        Me.ProgressPanel2.Caption = "Please Wait..."
        Me.ProgressPanel2.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
        Me.ProgressPanel2.Description = "Check SMTP.."
        Me.ProgressPanel2.Location = New System.Drawing.Point(8, 10)
        Me.ProgressPanel2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ProgressPanel2.Name = "ProgressPanel2"
        Me.ProgressPanel2.Size = New System.Drawing.Size(217, 48)
        Me.ProgressPanel2.TabIndex = 395
        Me.ProgressPanel2.Text = "picWait"
        '
        'XtraTabPage1
        '
        Me.XtraTabPage1.Controls.Add(Me.Panel3)
        Me.XtraTabPage1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.XtraTabPage1.Name = "XtraTabPage1"
        Me.XtraTabPage1.Size = New System.Drawing.Size(773, 238)
        Me.XtraTabPage1.Text = "   Email List   "
        '
        'Panel3
        '
        Me.Panel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Panel3.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel3.Controls.Add(Me.ProgressBarControl4)
        Me.Panel3.Controls.Add(Me.GridControl2)
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel3.Location = New System.Drawing.Point(0, 0)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(773, 238)
        Me.Panel3.TabIndex = 315
        '
        'ProgressBarControl4
        '
        Me.ProgressBarControl4.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.ProgressBarControl4.Location = New System.Drawing.Point(0, 223)
        Me.ProgressBarControl4.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ProgressBarControl4.Name = "ProgressBarControl4"
        Me.ProgressBarControl4.Properties.ShowTitle = True
        Me.ProgressBarControl4.Size = New System.Drawing.Size(771, 13)
        Me.ProgressBarControl4.TabIndex = 309
        '
        'GridControl2
        '
        Me.GridControl2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GridControl2.EmbeddedNavigator.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.GridControl2.EmbeddedNavigator.Appearance.Options.UseBackColor = True
        Me.GridControl2.EmbeddedNavigator.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GridControl2.Location = New System.Drawing.Point(0, 0)
        Me.GridControl2.LookAndFeel.SkinMaskColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.GridControl2.LookAndFeel.SkinMaskColor2 = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.GridControl2.LookAndFeel.SkinName = "DevExpress Dark Style"
        Me.GridControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GridControl2.MainView = Me.GridView2
        Me.GridControl2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GridControl2.Name = "GridControl2"
        Me.GridControl2.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemButtonEdit2, Me.RepositoryItemCheckEdit1, Me.RepositoryItemTextEdit2, Me.RepositoryItemImageEdit1, Me.RepositoryItemPictureEdit1})
        Me.GridControl2.Size = New System.Drawing.Size(771, 236)
        Me.GridControl2.TabIndex = 308
        Me.GridControl2.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView2})
        '
        'GridView2
        '
        Me.GridView2.Appearance.Empty.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.Empty.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.Empty.Options.UseBackColor = True
        Me.GridView2.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.GridView2.Appearance.FocusedRow.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridView2.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White
        Me.GridView2.Appearance.FocusedRow.Options.UseBackColor = True
        Me.GridView2.Appearance.FocusedRow.Options.UseFont = True
        Me.GridView2.Appearance.FocusedRow.Options.UseForeColor = True
        Me.GridView2.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.GridView2.Appearance.SelectedRow.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridView2.Appearance.SelectedRow.ForeColor = System.Drawing.Color.White
        Me.GridView2.Appearance.SelectedRow.Options.UseBackColor = True
        Me.GridView2.Appearance.SelectedRow.Options.UseFont = True
        Me.GridView2.Appearance.SelectedRow.Options.UseForeColor = True
        Me.GridView2.AppearancePrint.Row.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.AppearancePrint.Row.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.AppearancePrint.Row.Options.UseBackColor = True
        Me.GridView2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.GridView2.ColumnPanelRowHeight = 23
        Me.GridView2.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn12, Me.GridColumn13, Me.GridColumn14, Me.col_status_icon, Me.GridColumn15, Me.GridColumn16, Me.GridColumn17, Me.GridColumn20, Me.GridColumn21, Me.GridColumn22, Me.GridColumn26, Me.GridColumn27, Me.GridColumn28})
        Me.GridView2.DetailHeight = 205
        Me.GridView2.FixedLineWidth = 1
        Me.GridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None
        Me.GridView2.GridControl = Me.GridControl2
        Me.GridView2.GroupRowHeight = 20
        Me.GridView2.Name = "GridView2"
        Me.GridView2.OptionsFind.AllowFindPanel = False
        Me.GridView2.OptionsFind.AllowMruItems = False
        Me.GridView2.OptionsView.BestFitMaxRowCount = 2
        Me.GridView2.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView2.OptionsView.EnableAppearanceOddRow = True
        Me.GridView2.OptionsView.ShowGroupPanel = False
        Me.GridView2.OptionsView.ShowIndicator = False
        Me.GridView2.RowHeight = 7
        '
        'GridColumn12
        '
        Me.GridColumn12.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn12.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn12.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn12.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn12.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn12.AppearanceCell.Options.UseFont = True
        Me.GridColumn12.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn12.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn12.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn12.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn12.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn12.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn12.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn12.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn12.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn12.AppearanceHeader.Options.UseFont = True
        Me.GridColumn12.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn12.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn12.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn12.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn12.Caption = "Number"
        Me.GridColumn12.FieldName = "id"
        Me.GridColumn12.MinWidth = 10
        Me.GridColumn12.Name = "GridColumn12"
        Me.GridColumn12.OptionsColumn.AllowEdit = False
        Me.GridColumn12.OptionsColumn.AllowFocus = False
        Me.GridColumn12.OptionsColumn.AllowMove = False
        Me.GridColumn12.OptionsColumn.AllowShowHide = False
        Me.GridColumn12.OptionsColumn.AllowSize = False
        Me.GridColumn12.OptionsColumn.FixedWidth = True
        Me.GridColumn12.OptionsColumn.ReadOnly = True
        Me.GridColumn12.Visible = True
        Me.GridColumn12.VisibleIndex = 0
        Me.GridColumn12.Width = 43
        '
        'GridColumn13
        '
        Me.GridColumn13.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn13.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn13.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn13.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn13.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn13.AppearanceCell.Options.UseFont = True
        Me.GridColumn13.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn13.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn13.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn13.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn13.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn13.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn13.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn13.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn13.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn13.AppearanceHeader.Options.UseFont = True
        Me.GridColumn13.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn13.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn13.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn13.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn13.Caption = "Email Address"
        Me.GridColumn13.ColumnEdit = Me.RepositoryItemTextEdit2
        Me.GridColumn13.FieldName = "emailaddress"
        Me.GridColumn13.MinWidth = 10
        Me.GridColumn13.Name = "GridColumn13"
        Me.GridColumn13.OptionsColumn.AllowEdit = False
        Me.GridColumn13.OptionsColumn.AllowFocus = False
        Me.GridColumn13.OptionsColumn.AllowMove = False
        Me.GridColumn13.OptionsColumn.AllowShowHide = False
        Me.GridColumn13.OptionsColumn.AllowSize = False
        Me.GridColumn13.OptionsColumn.FixedWidth = True
        Me.GridColumn13.OptionsColumn.ReadOnly = True
        Me.GridColumn13.Visible = True
        Me.GridColumn13.VisibleIndex = 1
        Me.GridColumn13.Width = 129
        '
        'RepositoryItemTextEdit2
        '
        Me.RepositoryItemTextEdit2.AutoHeight = False
        Me.RepositoryItemTextEdit2.Name = "RepositoryItemTextEdit2"
        Me.RepositoryItemTextEdit2.Padding = New System.Windows.Forms.Padding(8, 0, 0, 0)
        '
        'GridColumn14
        '
        Me.GridColumn14.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn14.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn14.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn14.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn14.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn14.AppearanceCell.Options.UseFont = True
        Me.GridColumn14.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn14.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn14.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn14.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn14.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn14.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn14.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn14.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn14.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn14.AppearanceHeader.Options.UseFont = True
        Me.GridColumn14.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn14.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn14.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn14.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn14.Caption = "Delivery Status"
        Me.GridColumn14.FieldName = "deliverystatus"
        Me.GridColumn14.MinWidth = 10
        Me.GridColumn14.Name = "GridColumn14"
        Me.GridColumn14.OptionsColumn.AllowEdit = False
        Me.GridColumn14.OptionsColumn.AllowFocus = False
        Me.GridColumn14.OptionsColumn.AllowMove = False
        Me.GridColumn14.OptionsColumn.AllowShowHide = False
        Me.GridColumn14.OptionsColumn.AllowSize = False
        Me.GridColumn14.OptionsColumn.FixedWidth = True
        Me.GridColumn14.OptionsColumn.ReadOnly = True
        Me.GridColumn14.Visible = True
        Me.GridColumn14.VisibleIndex = 2
        Me.GridColumn14.Width = 69
        '
        'col_status_icon
        '
        Me.col_status_icon.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.col_status_icon.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.col_status_icon.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.col_status_icon.AppearanceCell.Options.UseBackColor = True
        Me.col_status_icon.AppearanceCell.Options.UseFont = True
        Me.col_status_icon.AppearanceCell.Options.UseForeColor = True
        Me.col_status_icon.AppearanceCell.Options.UseTextOptions = True
        Me.col_status_icon.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.col_status_icon.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.col_status_icon.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.col_status_icon.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.col_status_icon.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.col_status_icon.AppearanceHeader.Options.UseBackColor = True
        Me.col_status_icon.AppearanceHeader.Options.UseFont = True
        Me.col_status_icon.AppearanceHeader.Options.UseForeColor = True
        Me.col_status_icon.AppearanceHeader.Options.UseTextOptions = True
        Me.col_status_icon.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.col_status_icon.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.col_status_icon.Caption = "I"
        Me.col_status_icon.MinWidth = 10
        Me.col_status_icon.Name = "col_status_icon"
        Me.col_status_icon.OptionsColumn.AllowEdit = False
        Me.col_status_icon.OptionsColumn.FixedWidth = True
        Me.col_status_icon.Visible = True
        Me.col_status_icon.VisibleIndex = 3
        Me.col_status_icon.Width = 17
        '
        'GridColumn15
        '
        Me.GridColumn15.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn15.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn15.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn15.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn15.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn15.AppearanceCell.Options.UseFont = True
        Me.GridColumn15.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn15.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn15.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn15.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn15.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn15.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn15.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn15.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn15.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn15.AppearanceHeader.Options.UseFont = True
        Me.GridColumn15.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn15.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn15.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn15.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn15.Caption = "Response Message"
        Me.GridColumn15.ColumnEdit = Me.RepositoryItemTextEdit2
        Me.GridColumn15.FieldName = "responsemessage"
        Me.GridColumn15.MinWidth = 10
        Me.GridColumn15.Name = "GridColumn15"
        Me.GridColumn15.OptionsColumn.AllowEdit = False
        Me.GridColumn15.OptionsColumn.AllowFocus = False
        Me.GridColumn15.OptionsColumn.AllowMove = False
        Me.GridColumn15.OptionsColumn.AllowShowHide = False
        Me.GridColumn15.OptionsColumn.AllowSize = False
        Me.GridColumn15.OptionsColumn.FixedWidth = True
        Me.GridColumn15.OptionsColumn.ReadOnly = True
        Me.GridColumn15.Visible = True
        Me.GridColumn15.VisibleIndex = 4
        Me.GridColumn15.Width = 147
        '
        'GridColumn16
        '
        Me.GridColumn16.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn16.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn16.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn16.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn16.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn16.AppearanceCell.Options.UseFont = True
        Me.GridColumn16.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn16.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn16.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn16.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn16.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn16.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn16.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn16.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn16.AppearanceHeader.Options.UseFont = True
        Me.GridColumn16.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn16.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn16.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn16.Caption = "Date"
        Me.GridColumn16.FieldName = "date"
        Me.GridColumn16.MinWidth = 10
        Me.GridColumn16.Name = "GridColumn16"
        Me.GridColumn16.OptionsColumn.AllowEdit = False
        Me.GridColumn16.OptionsColumn.AllowFocus = False
        Me.GridColumn16.OptionsColumn.AllowMove = False
        Me.GridColumn16.OptionsColumn.AllowShowHide = False
        Me.GridColumn16.OptionsColumn.AllowSize = False
        Me.GridColumn16.OptionsColumn.FixedWidth = True
        Me.GridColumn16.OptionsColumn.ReadOnly = True
        Me.GridColumn16.Visible = True
        Me.GridColumn16.VisibleIndex = 5
        Me.GridColumn16.Width = 44
        '
        'GridColumn17
        '
        Me.GridColumn17.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn17.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn17.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn17.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn17.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn17.AppearanceCell.Options.UseFont = True
        Me.GridColumn17.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn17.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn17.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn17.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn17.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn17.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn17.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn17.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn17.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn17.AppearanceHeader.Options.UseFont = True
        Me.GridColumn17.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn17.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn17.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn17.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn17.Caption = "Time"
        Me.GridColumn17.FieldName = "time"
        Me.GridColumn17.MinWidth = 10
        Me.GridColumn17.Name = "GridColumn17"
        Me.GridColumn17.OptionsColumn.AllowEdit = False
        Me.GridColumn17.OptionsColumn.AllowFocus = False
        Me.GridColumn17.OptionsColumn.AllowMove = False
        Me.GridColumn17.OptionsColumn.AllowShowHide = False
        Me.GridColumn17.OptionsColumn.AllowSize = False
        Me.GridColumn17.OptionsColumn.FixedWidth = True
        Me.GridColumn17.OptionsColumn.ReadOnly = True
        Me.GridColumn17.Visible = True
        Me.GridColumn17.VisibleIndex = 6
        Me.GridColumn17.Width = 44
        '
        'GridColumn20
        '
        Me.GridColumn20.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn20.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn20.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn20.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn20.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn20.AppearanceCell.Options.UseFont = True
        Me.GridColumn20.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn20.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn20.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn20.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn20.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn20.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn20.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn20.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn20.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn20.AppearanceHeader.Options.UseFont = True
        Me.GridColumn20.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn20.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn20.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn20.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn20.Caption = "Remove"
        Me.GridColumn20.ColumnEdit = Me.RepositoryItemButtonEdit2
        Me.GridColumn20.FieldName = "CanCheck"
        Me.GridColumn20.MinWidth = 10
        Me.GridColumn20.Name = "GridColumn20"
        Me.GridColumn20.OptionsColumn.AllowMove = False
        Me.GridColumn20.OptionsColumn.AllowShowHide = False
        Me.GridColumn20.OptionsColumn.AllowSize = False
        Me.GridColumn20.OptionsColumn.FixedWidth = True
        Me.GridColumn20.OptionsColumn.ReadOnly = True
        Me.GridColumn20.Visible = True
        Me.GridColumn20.VisibleIndex = 7
        Me.GridColumn20.Width = 29
        '
        'RepositoryItemButtonEdit2
        '
        Me.RepositoryItemButtonEdit2.AllowFocused = False
        Me.RepositoryItemButtonEdit2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.Appearance.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.Appearance.Options.UseBackColor = True
        Me.RepositoryItemButtonEdit2.AppearanceFocused.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.AppearanceFocused.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.AppearanceFocused.Options.UseBackColor = True
        Me.RepositoryItemButtonEdit2.AppearanceReadOnly.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.AppearanceReadOnly.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.AppearanceReadOnly.Options.UseBackColor = True
        Me.RepositoryItemButtonEdit2.AutoHeight = False
        Me.RepositoryItemButtonEdit2.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph)})
        Me.RepositoryItemButtonEdit2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.RepositoryItemButtonEdit2.Name = "RepositoryItemButtonEdit2"
        Me.RepositoryItemButtonEdit2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'GridColumn21
        '
        Me.GridColumn21.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn21.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn21.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn21.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn21.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn21.AppearanceCell.Options.UseFont = True
        Me.GridColumn21.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn21.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn21.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn21.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn21.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn21.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn21.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn21.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn21.AppearanceHeader.Options.UseFont = True
        Me.GridColumn21.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn21.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn21.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn21.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn21.Caption = "Select"
        Me.GridColumn21.ColumnEdit = Me.RepositoryItemCheckEdit1
        Me.GridColumn21.FieldName = "CanCheck"
        Me.GridColumn21.MinWidth = 10
        Me.GridColumn21.Name = "GridColumn21"
        Me.GridColumn21.OptionsColumn.AllowEdit = False
        Me.GridColumn21.OptionsColumn.AllowFocus = False
        Me.GridColumn21.OptionsColumn.AllowMove = False
        Me.GridColumn21.OptionsColumn.AllowShowHide = False
        Me.GridColumn21.OptionsColumn.AllowSize = False
        Me.GridColumn21.OptionsColumn.FixedWidth = True
        Me.GridColumn21.OptionsColumn.ReadOnly = True
        Me.GridColumn21.Width = 15
        '
        'RepositoryItemCheckEdit1
        '
        Me.RepositoryItemCheckEdit1.AutoHeight = False
        Me.RepositoryItemCheckEdit1.CheckBoxOptions.Style = DevExpress.XtraEditors.Controls.CheckBoxStyle.CheckBox
        Me.RepositoryItemCheckEdit1.LookAndFeel.SkinName = "Dark Side"
        Me.RepositoryItemCheckEdit1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.RepositoryItemCheckEdit1.Name = "RepositoryItemCheckEdit1"
        Me.RepositoryItemCheckEdit1.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        '
        'GridColumn22
        '
        Me.GridColumn22.Caption = "GridColumn10"
        Me.GridColumn22.FieldName = "errormsg"
        Me.GridColumn22.MinWidth = 18
        Me.GridColumn22.Name = "GridColumn22"
        Me.GridColumn22.Width = 69
        '
        'GridColumn26
        '
        Me.GridColumn26.Caption = "smtppassword"
        Me.GridColumn26.FieldName = "smtppassword"
        Me.GridColumn26.MinWidth = 18
        Me.GridColumn26.Name = "GridColumn26"
        Me.GridColumn26.Width = 69
        '
        'GridColumn27
        '
        Me.GridColumn27.Caption = "GridColumn24"
        Me.GridColumn27.FieldName = "tag1"
        Me.GridColumn27.MinWidth = 18
        Me.GridColumn27.Name = "GridColumn27"
        Me.GridColumn27.Width = 69
        '
        'GridColumn28
        '
        Me.GridColumn28.Caption = "GridColumn25"
        Me.GridColumn28.FieldName = "tag2"
        Me.GridColumn28.MinWidth = 18
        Me.GridColumn28.Name = "GridColumn28"
        Me.GridColumn28.Width = 69
        '
        'RepositoryItemImageEdit1
        '
        Me.RepositoryItemImageEdit1.AutoHeight = False
        Me.RepositoryItemImageEdit1.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemImageEdit1.Name = "RepositoryItemImageEdit1"
        '
        'RepositoryItemPictureEdit1
        '
        Me.RepositoryItemPictureEdit1.Name = "RepositoryItemPictureEdit1"
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.Appearance.Options.UseFont = True
        Me.XtraTabControl1.AppearancePage.Header.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.XtraTabControl1.AppearancePage.Header.Options.UseFont = True
        Me.XtraTabControl1.AppearancePage.HeaderActive.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.XtraTabControl1.AppearancePage.HeaderActive.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.WindowText
        Me.XtraTabControl1.AppearancePage.HeaderActive.Options.UseBackColor = True
        Me.XtraTabControl1.AppearancePage.HeaderActive.Options.UseForeColor = True
        Me.XtraTabControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.XtraTabControl1.BorderStylePage = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.XtraTabControl1.Location = New System.Drawing.Point(0, 76)
        Me.XtraTabControl1.LookAndFeel.SkinName = "Seven Classic"
        Me.XtraTabControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.TabSmtp
        Me.XtraTabControl1.Size = New System.Drawing.Size(776, 273)
        Me.XtraTabControl1.TabIndex = 399
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.XtraTabPage1, Me.TabSmtp, Me.XtraTabPage2, Me.XtraTabPage5, Me.XtraTabPage6})
        '
        'NumericUpDown1
        '
        Me.NumericUpDown1.Location = New System.Drawing.Point(900, 279)
        Me.NumericUpDown1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.NumericUpDown1.Name = "NumericUpDown1"
        Me.NumericUpDown1.Size = New System.Drawing.Size(103, 20)
        Me.NumericUpDown1.TabIndex = 497
        '
        'SpinEdit1
        '
        Me.SpinEdit1.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.SpinEdit1.Location = New System.Drawing.Point(878, 351)
        Me.SpinEdit1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SpinEdit1.Name = "SpinEdit1"
        Me.SpinEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.SpinEdit1.Size = New System.Drawing.Size(86, 32)
        Me.SpinEdit1.TabIndex = 498
        '
        'TextBoxTitle
        '
        Me.TextBoxTitle.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxTitle.EditValue = "Office 365"
        Me.TextBoxTitle.Location = New System.Drawing.Point(31, 165)
        Me.TextBoxTitle.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TextBoxTitle.Name = "TextBoxTitle"
        Me.TextBoxTitle.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxTitle.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxTitle.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxTitle.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxTitle.Properties.Appearance.Options.UseFont = True
        Me.TextBoxTitle.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxTitle.Properties.NullValuePrompt = "Title Office 365"
        Me.TextBoxTitle.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxTitle.Size = New System.Drawing.Size(162, 30)
        Me.TextBoxTitle.TabIndex = 499
        '
        'TextBoxLogoLM
        '
        Me.TextBoxLogoLM.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxLogoLM.EditValue = "https://uhf.microsoft.com/images/microsoft/RE1Mu3b.png"
        Me.TextBoxLogoLM.Location = New System.Drawing.Point(31, 209)
        Me.TextBoxLogoLM.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TextBoxLogoLM.Name = "TextBoxLogoLM"
        Me.TextBoxLogoLM.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxLogoLM.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxLogoLM.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxLogoLM.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxLogoLM.Properties.Appearance.Options.UseFont = True
        Me.TextBoxLogoLM.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxLogoLM.Properties.NullValuePrompt = "Link Logo"
        Me.TextBoxLogoLM.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxLogoLM.Size = New System.Drawing.Size(334, 30)
        Me.TextBoxLogoLM.TabIndex = 500
        '
        'ComboBox_TitleColor
        '
        Me.ComboBox_TitleColor.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboBox_TitleColor.EditValue = "Red"
        Me.ComboBox_TitleColor.Location = New System.Drawing.Point(31, 254)
        Me.ComboBox_TitleColor.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ComboBox_TitleColor.Name = "ComboBox_TitleColor"
        Me.ComboBox_TitleColor.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboBox_TitleColor.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ComboBox_TitleColor.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBox_TitleColor.Properties.Appearance.Options.UseFont = True
        Me.ComboBox_TitleColor.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBox_TitleColor.Properties.NullValuePrompt = "Title Color"
        Me.ComboBox_TitleColor.Size = New System.Drawing.Size(147, 38)
        Me.ComboBox_TitleColor.TabIndex = 501
        '
        'ComboBox_TextColor
        '
        Me.ComboBox_TextColor.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboBox_TextColor.EditValue = "Red"
        Me.ComboBox_TextColor.Location = New System.Drawing.Point(31, 303)
        Me.ComboBox_TextColor.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ComboBox_TextColor.Name = "ComboBox_TextColor"
        Me.ComboBox_TextColor.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboBox_TextColor.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ComboBox_TextColor.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBox_TextColor.Properties.Appearance.Options.UseFont = True
        Me.ComboBox_TextColor.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBox_TextColor.Properties.NullValuePrompt = "txt Color"
        Me.ComboBox_TextColor.Size = New System.Drawing.Size(147, 38)
        Me.ComboBox_TextColor.TabIndex = 502
        '
        'ComboBox_ButtonColor
        '
        Me.ComboBox_ButtonColor.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboBox_ButtonColor.EditValue = "Red"
        Me.ComboBox_ButtonColor.Location = New System.Drawing.Point(809, 184)
        Me.ComboBox_ButtonColor.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ComboBox_ButtonColor.Name = "ComboBox_ButtonColor"
        Me.ComboBox_ButtonColor.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboBox_ButtonColor.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ComboBox_ButtonColor.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBox_ButtonColor.Properties.Appearance.Options.UseFont = True
        Me.ComboBox_ButtonColor.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBox_ButtonColor.Properties.Items.AddRange(New Object() {"1000", "2000", "3000", "4000", "5000", "6000", "7000", "8000", "9000"})
        Me.ComboBox_ButtonColor.Properties.NullValuePrompt = "Button Color"
        Me.ComboBox_ButtonColor.Size = New System.Drawing.Size(194, 38)
        Me.ComboBox_ButtonColor.TabIndex = 503
        '
        'FontSizeMessage
        '
        Me.FontSizeMessage.EditValue = New Decimal(New Integer() {25, 0, 0, 0})
        Me.FontSizeMessage.Location = New System.Drawing.Point(31, 403)
        Me.FontSizeMessage.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.FontSizeMessage.Name = "FontSizeMessage"
        Me.FontSizeMessage.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.FontSizeMessage.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.FontSizeMessage.Properties.Appearance.Options.UseBackColor = True
        Me.FontSizeMessage.Properties.Appearance.Options.UseFont = True
        Me.FontSizeMessage.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.FontSizeMessage.Size = New System.Drawing.Size(147, 38)
        Me.FontSizeMessage.TabIndex = 504
        '
        'fonttitel
        '
        Me.fonttitel.EditValue = New Decimal(New Integer() {25, 0, 0, 0})
        Me.fonttitel.Location = New System.Drawing.Point(234, 254)
        Me.fonttitel.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.fonttitel.Name = "fonttitel"
        Me.fonttitel.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.fonttitel.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.fonttitel.Properties.Appearance.Options.UseBackColor = True
        Me.fonttitel.Properties.Appearance.Options.UseFont = True
        Me.fonttitel.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.fonttitel.Size = New System.Drawing.Size(147, 38)
        Me.fonttitel.TabIndex = 505
        '
        'Label33
        '
        Me.Label33.AutoSize = True
        Me.Label33.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.Label33.ForeColor = System.Drawing.Color.White
        Me.Label33.Location = New System.Drawing.Point(31, 147)
        Me.Label33.Name = "Label33"
        Me.Label33.Size = New System.Drawing.Size(44, 21)
        Me.Label33.TabIndex = 332
        Me.Label33.Text = "Title "
        '
        'Label34
        '
        Me.Label34.AutoSize = True
        Me.Label34.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.Label34.ForeColor = System.Drawing.Color.White
        Me.Label34.Location = New System.Drawing.Point(31, 191)
        Me.Label34.Name = "Label34"
        Me.Label34.Size = New System.Drawing.Size(81, 21)
        Me.Label34.TabIndex = 332
        Me.Label34.Text = "Link Logo "
        '
        'Label35
        '
        Me.Label35.AutoSize = True
        Me.Label35.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.Label35.ForeColor = System.Drawing.Color.White
        Me.Label35.Location = New System.Drawing.Point(31, 235)
        Me.Label35.Name = "Label35"
        Me.Label35.Size = New System.Drawing.Size(81, 21)
        Me.Label35.TabIndex = 332
        Me.Label35.Text = "TitleColor "
        '
        'Label36
        '
        Me.Label36.AutoSize = True
        Me.Label36.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.Label36.ForeColor = System.Drawing.Color.White
        Me.Label36.Location = New System.Drawing.Point(31, 285)
        Me.Label36.Name = "Label36"
        Me.Label36.Size = New System.Drawing.Size(72, 21)
        Me.Label36.TabIndex = 332
        Me.Label36.Text = "txt Color "
        '
        'Label37
        '
        Me.Label37.AutoSize = True
        Me.Label37.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.Label37.ForeColor = System.Drawing.Color.White
        Me.Label37.Location = New System.Drawing.Point(31, 334)
        Me.Label37.Name = "Label37"
        Me.Label37.Size = New System.Drawing.Size(101, 21)
        Me.Label37.TabIndex = 332
        Me.Label37.Text = "Button Color "
        '
        'Label38
        '
        Me.Label38.AutoSize = True
        Me.Label38.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.Label38.ForeColor = System.Drawing.Color.White
        Me.Label38.Location = New System.Drawing.Point(31, 384)
        Me.Label38.Name = "Label38"
        Me.Label38.Size = New System.Drawing.Size(142, 21)
        Me.Label38.TabIndex = 332
        Me.Label38.Text = "Message Font Size "
        '
        'Label39
        '
        Me.Label39.AutoSize = True
        Me.Label39.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.Label39.ForeColor = System.Drawing.Color.White
        Me.Label39.Location = New System.Drawing.Point(231, 235)
        Me.Label39.Name = "Label39"
        Me.Label39.Size = New System.Drawing.Size(107, 21)
        Me.Label39.TabIndex = 332
        Me.Label39.Text = "Title Font Size"
        '
        'RightAlignPictureBox
        '
        Me.RightAlignPictureBox.Cursor = System.Windows.Forms.Cursors.Hand
        Me.RightAlignPictureBox.EditValue = CType(resources.GetObject("RightAlignPictureBox.EditValue"), Object)
        Me.RightAlignPictureBox.Location = New System.Drawing.Point(352, 285)
        Me.RightAlignPictureBox.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.RightAlignPictureBox.Name = "RightAlignPictureBox"
        Me.RightAlignPictureBox.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.RightAlignPictureBox.Properties.Appearance.Options.UseBackColor = True
        Me.RightAlignPictureBox.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.RightAlignPictureBox.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.RightAlignPictureBox.Size = New System.Drawing.Size(29, 14)
        Me.RightAlignPictureBox.TabIndex = 506
        '
        'CenterAlignPictureBox
        '
        Me.CenterAlignPictureBox.EditValue = CType(resources.GetObject("CenterAlignPictureBox.EditValue"), Object)
        Me.CenterAlignPictureBox.Location = New System.Drawing.Point(291, 285)
        Me.CenterAlignPictureBox.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CenterAlignPictureBox.Name = "CenterAlignPictureBox"
        Me.CenterAlignPictureBox.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.CenterAlignPictureBox.Properties.Appearance.Options.UseBackColor = True
        Me.CenterAlignPictureBox.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.CenterAlignPictureBox.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.CenterAlignPictureBox.Size = New System.Drawing.Size(27, 14)
        Me.CenterAlignPictureBox.TabIndex = 507
        '
        'LeftAlignPictureBox
        '
        Me.LeftAlignPictureBox.Cursor = System.Windows.Forms.Cursors.Hand
        Me.LeftAlignPictureBox.EditValue = CType(resources.GetObject("LeftAlignPictureBox.EditValue"), Object)
        Me.LeftAlignPictureBox.Location = New System.Drawing.Point(234, 285)
        Me.LeftAlignPictureBox.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LeftAlignPictureBox.Name = "LeftAlignPictureBox"
        Me.LeftAlignPictureBox.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.LeftAlignPictureBox.Properties.Appearance.Options.UseBackColor = True
        Me.LeftAlignPictureBox.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.LeftAlignPictureBox.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.LeftAlignPictureBox.Size = New System.Drawing.Size(25, 14)
        Me.LeftAlignPictureBox.TabIndex = 508
        '
        'ButtonSaveHTML
        '
        Me.ButtonSaveHTML.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ButtonSaveHTML.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.ButtonSaveHTML.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ButtonSaveHTML.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.ButtonSaveHTML.Appearance.Options.UseBackColor = True
        Me.ButtonSaveHTML.Appearance.Options.UseBorderColor = True
        Me.ButtonSaveHTML.Appearance.Options.UseFont = True
        Me.ButtonSaveHTML.Appearance.Options.UseForeColor = True
        Me.ButtonSaveHTML.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.ButtonSaveHTML.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.ButtonSaveHTML.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.ButtonSaveHTML.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseBackColor = True
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseBorderColor = True
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseFont = True
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseForeColor = True
        Me.ButtonSaveHTML.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.ButtonSaveHTML.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ButtonSaveHTML.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.ButtonSaveHTML.AppearanceHovered.Options.UseBackColor = True
        Me.ButtonSaveHTML.AppearanceHovered.Options.UseBorderColor = True
        Me.ButtonSaveHTML.AppearanceHovered.Options.UseForeColor = True
        Me.ButtonSaveHTML.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.ButtonSaveHTML.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ButtonSaveHTML.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.ButtonSaveHTML.AppearancePressed.Options.UseBackColor = True
        Me.ButtonSaveHTML.AppearancePressed.Options.UseBorderColor = True
        Me.ButtonSaveHTML.AppearancePressed.Options.UseForeColor = True
        Me.ButtonSaveHTML.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ButtonSaveHTML.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.ButtonSaveHTML.Location = New System.Drawing.Point(19, 534)
        Me.ButtonSaveHTML.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ButtonSaveHTML.Name = "ButtonSaveHTML"
        Me.ButtonSaveHTML.Size = New System.Drawing.Size(159, 42)
        Me.ButtonSaveHTML.TabIndex = 509
        Me.ButtonSaveHTML.Text = "Save"
        '
        'BNT_Backup
        '
        Me.BNT_Backup.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BNT_Backup.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BNT_Backup.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BNT_Backup.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BNT_Backup.Appearance.Options.UseBackColor = True
        Me.BNT_Backup.Appearance.Options.UseBorderColor = True
        Me.BNT_Backup.Appearance.Options.UseFont = True
        Me.BNT_Backup.Appearance.Options.UseForeColor = True
        Me.BNT_Backup.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BNT_Backup.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BNT_Backup.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BNT_Backup.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BNT_Backup.AppearanceDisabled.Options.UseBackColor = True
        Me.BNT_Backup.AppearanceDisabled.Options.UseBorderColor = True
        Me.BNT_Backup.AppearanceDisabled.Options.UseFont = True
        Me.BNT_Backup.AppearanceDisabled.Options.UseForeColor = True
        Me.BNT_Backup.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BNT_Backup.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BNT_Backup.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BNT_Backup.AppearanceHovered.Options.UseBackColor = True
        Me.BNT_Backup.AppearanceHovered.Options.UseBorderColor = True
        Me.BNT_Backup.AppearanceHovered.Options.UseForeColor = True
        Me.BNT_Backup.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BNT_Backup.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BNT_Backup.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BNT_Backup.AppearancePressed.Options.UseBackColor = True
        Me.BNT_Backup.AppearancePressed.Options.UseBorderColor = True
        Me.BNT_Backup.AppearancePressed.Options.UseForeColor = True
        Me.BNT_Backup.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BNT_Backup.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.default32x32
        Me.BNT_Backup.Location = New System.Drawing.Point(206, 534)
        Me.BNT_Backup.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.BNT_Backup.Name = "BNT_Backup"
        Me.BNT_Backup.Size = New System.Drawing.Size(159, 42)
        Me.BNT_Backup.TabIndex = 510
        Me.BNT_Backup.Text = "Default"
        '
        'BntReset
        '
        Me.BntReset.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntReset.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntReset.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntReset.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntReset.Appearance.Options.UseBackColor = True
        Me.BntReset.Appearance.Options.UseBorderColor = True
        Me.BntReset.Appearance.Options.UseFont = True
        Me.BntReset.Appearance.Options.UseForeColor = True
        Me.BntReset.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntReset.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntReset.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntReset.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntReset.AppearanceDisabled.Options.UseBackColor = True
        Me.BntReset.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntReset.AppearanceDisabled.Options.UseFont = True
        Me.BntReset.AppearanceDisabled.Options.UseForeColor = True
        Me.BntReset.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntReset.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntReset.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntReset.AppearanceHovered.Options.UseBackColor = True
        Me.BntReset.AppearanceHovered.Options.UseBorderColor = True
        Me.BntReset.AppearanceHovered.Options.UseForeColor = True
        Me.BntReset.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntReset.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntReset.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntReset.AppearancePressed.Options.UseBackColor = True
        Me.BntReset.AppearancePressed.Options.UseBorderColor = True
        Me.BntReset.AppearancePressed.Options.UseForeColor = True
        Me.BntReset.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntReset.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntReset.Location = New System.Drawing.Point(50, 598)
        Me.BntReset.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.BntReset.Name = "BntReset"
        Me.BntReset.Size = New System.Drawing.Size(159, 42)
        Me.BntReset.TabIndex = 511
        Me.BntReset.Text = "Reset All"
        '
        'TextBoxSignature
        '
        Me.TextBoxSignature.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxSignature.EditValue = "Best Regards, Team BSV"
        Me.TextBoxSignature.Location = New System.Drawing.Point(215, 409)
        Me.TextBoxSignature.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TextBoxSignature.Name = "TextBoxSignature"
        Me.TextBoxSignature.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxSignature.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxSignature.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxSignature.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxSignature.Properties.Appearance.Options.UseFont = True
        Me.TextBoxSignature.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxSignature.Properties.NullValuePrompt = "Name Button "
        Me.TextBoxSignature.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxSignature.Size = New System.Drawing.Size(176, 30)
        Me.TextBoxSignature.TabIndex = 512
        '
        'Label40
        '
        Me.Label40.AutoSize = True
        Me.Label40.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.Label40.ForeColor = System.Drawing.Color.White
        Me.Label40.Location = New System.Drawing.Point(219, 384)
        Me.Label40.Name = "Label40"
        Me.Label40.Size = New System.Drawing.Size(103, 21)
        Me.Label40.TabIndex = 332
        Me.Label40.Text = "the Signature"
        '
        'TextButtonname
        '
        Me.TextButtonname.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextButtonname.EditValue = "Click Here"
        Me.TextButtonname.Location = New System.Drawing.Point(215, 360)
        Me.TextButtonname.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TextButtonname.Name = "TextButtonname"
        Me.TextButtonname.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextButtonname.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextButtonname.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextButtonname.Properties.Appearance.Options.UseBackColor = True
        Me.TextButtonname.Properties.Appearance.Options.UseFont = True
        Me.TextButtonname.Properties.Appearance.Options.UseForeColor = True
        Me.TextButtonname.Properties.NullValuePrompt = "Name Button "
        Me.TextButtonname.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextButtonname.Size = New System.Drawing.Size(176, 30)
        Me.TextButtonname.TabIndex = 513
        '
        'Label41
        '
        Me.Label41.AutoSize = True
        Me.Label41.Font = New System.Drawing.Font("Comfortaa", 9.249999!, System.Drawing.FontStyle.Bold)
        Me.Label41.ForeColor = System.Drawing.Color.White
        Me.Label41.Location = New System.Drawing.Point(221, 334)
        Me.Label41.Name = "Label41"
        Me.Label41.Size = New System.Drawing.Size(100, 21)
        Me.Label41.TabIndex = 332
        Me.Label41.Text = "Button Name"
        '
        'CheckBoxBoldText
        '
        Me.CheckBoxBoldText.Location = New System.Drawing.Point(309, 333)
        Me.CheckBoxBoldText.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CheckBoxBoldText.Name = "CheckBoxBoldText"
        Me.CheckBoxBoldText.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckBoxBoldText.Properties.Appearance.Options.UseFont = True
        Me.CheckBoxBoldText.Properties.Caption = "Bold Text"
        Me.CheckBoxBoldText.Size = New System.Drawing.Size(90, 27)
        Me.CheckBoxBoldText.TabIndex = 514
        '
        'GroupControl6
        '
        Me.GroupControl6.Location = New System.Drawing.Point(423, 322)
        Me.GroupControl6.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupControl6.Name = "GroupControl6"
        Me.GroupControl6.Size = New System.Drawing.Size(470, 133)
        Me.GroupControl6.TabIndex = 515
        Me.GroupControl6.Text = "Write Letter"
        '
        'GroupControl10
        '
        Me.GroupControl10.Controls.Add(Me.LayoutControl6)
        Me.GroupControl10.Location = New System.Drawing.Point(911, 389)
        Me.GroupControl10.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupControl10.Name = "GroupControl10"
        Me.GroupControl10.Size = New System.Drawing.Size(393, 286)
        Me.GroupControl10.TabIndex = 516
        Me.GroupControl10.Text = "Add Letter"
        '
        'LayoutControl6
        '
        Me.LayoutControl6.Controls.Add(Me.RichTextBox1)
        Me.LayoutControl6.Controls.Add(Me.TextEdit2)
        Me.LayoutControl6.Controls.Add(Me.TextEdit3)
        Me.LayoutControl6.Controls.Add(Me.GroupControl11)
        Me.LayoutControl6.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl6.Location = New System.Drawing.Point(2, 29)
        Me.LayoutControl6.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LayoutControl6.Name = "LayoutControl6"
        Me.LayoutControl6.Root = Me.LayoutControlGroup7
        Me.LayoutControl6.Size = New System.Drawing.Size(389, 255)
        Me.LayoutControl6.TabIndex = 0
        Me.LayoutControl6.Text = "LayoutControl6"
        '
        'RichTextBox1
        '
        Me.RichTextBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.RichTextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox1.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.RichTextBox1.ForeColor = System.Drawing.Color.White
        Me.RichTextBox1.Location = New System.Drawing.Point(16, 62)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(357, 177)
        Me.RichTextBox1.TabIndex = 67
        Me.RichTextBox1.Text = ""
        '
        'TextEdit2
        '
        Me.TextEdit2.EditValue = ""
        Me.TextEdit2.Location = New System.Drawing.Point(125, 16)
        Me.TextEdit2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TextEdit2.Name = "TextEdit2"
        Me.TextEdit2.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEdit2.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextEdit2.Properties.Appearance.Options.UseFont = True
        Me.TextEdit2.Properties.Appearance.Options.UseForeColor = True
        Me.TextEdit2.Properties.NullValuePrompt = "Path File HTML..."
        Me.TextEdit2.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextEdit2.Size = New System.Drawing.Size(66, 30)
        Me.TextEdit2.StyleController = Me.LayoutControl6
        Me.TextEdit2.TabIndex = 66
        '
        'TextEdit3
        '
        Me.TextEdit3.EditValue = ""
        Me.TextEdit3.Location = New System.Drawing.Point(306, 16)
        Me.TextEdit3.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TextEdit3.Name = "TextEdit3"
        Me.TextEdit3.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEdit3.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextEdit3.Properties.Appearance.Options.UseFont = True
        Me.TextEdit3.Properties.Appearance.Options.UseForeColor = True
        Me.TextEdit3.Properties.NullValuePrompt = "If this word is [-Link-] in Your Letter Add your page link here"
        Me.TextEdit3.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextEdit3.Size = New System.Drawing.Size(67, 30)
        Me.TextEdit3.StyleController = Me.LayoutControl6
        Me.TextEdit3.TabIndex = 65
        '
        'GroupControl11
        '
        Me.GroupControl11.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControl11.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.GroupControl11.AppearanceCaption.Options.UseFont = True
        Me.GroupControl11.Controls.Add(Me.Panel14)
        Me.GroupControl11.Location = New System.Drawing.Point(14, 307)
        Me.GroupControl11.Margin = New System.Windows.Forms.Padding(3, 1, 3, 1)
        Me.GroupControl11.Name = "GroupControl11"
        Me.GroupControl11.Size = New System.Drawing.Size(895, 13)
        Me.GroupControl11.TabIndex = 400
        Me.GroupControl11.Text = "Normal"
        '
        'Panel14
        '
        Me.Panel14.Controls.Add(Me.LayoutControl7)
        Me.Panel14.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel14.Location = New System.Drawing.Point(2, 29)
        Me.Panel14.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Panel14.Name = "Panel14"
        Me.Panel14.Size = New System.Drawing.Size(891, 0)
        Me.Panel14.TabIndex = 1
        '
        'LayoutControl7
        '
        Me.LayoutControl7.Controls.Add(Me.TextEdit4)
        Me.LayoutControl7.Controls.Add(Me.RichTextBox2)
        Me.LayoutControl7.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl7.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl7.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LayoutControl7.Name = "LayoutControl7"
        Me.LayoutControl7.Root = Me.LayoutControlGroup6
        Me.LayoutControl7.Size = New System.Drawing.Size(891, 0)
        Me.LayoutControl7.TabIndex = 0
        Me.LayoutControl7.Text = "LayoutControl2"
        '
        'TextEdit4
        '
        Me.TextEdit4.EditValue = ""
        Me.TextEdit4.Location = New System.Drawing.Point(119, 16)
        Me.TextEdit4.Margin = New System.Windows.Forms.Padding(3, 1, 3, 1)
        Me.TextEdit4.Name = "TextEdit4"
        Me.TextEdit4.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TextEdit4.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEdit4.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGoldenrod
        Me.TextEdit4.Properties.Appearance.Options.UseBackColor = True
        Me.TextEdit4.Properties.Appearance.Options.UseFont = True
        Me.TextEdit4.Properties.Appearance.Options.UseForeColor = True
        Me.TextEdit4.Properties.NullValuePrompt = "Enter a vaild host name..."
        Me.TextEdit4.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextEdit4.Size = New System.Drawing.Size(739, 30)
        Me.TextEdit4.StyleController = Me.LayoutControl7
        Me.TextEdit4.TabIndex = 63
        '
        'RichTextBox2
        '
        Me.RichTextBox2.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.RichTextBox2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox2.ForeColor = System.Drawing.Color.White
        Me.RichTextBox2.Location = New System.Drawing.Point(16, 62)
        Me.RichTextBox2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.RichTextBox2.Name = "RichTextBox2"
        Me.RichTextBox2.Size = New System.Drawing.Size(842, 20)
        Me.RichTextBox2.TabIndex = 68
        Me.RichTextBox2.Text = ""
        '
        'LayoutControlGroup6
        '
        Me.LayoutControlGroup6.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup6.GroupBordersVisible = False
        Me.LayoutControlGroup6.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem14, Me.EmptySpaceItem3, Me.LayoutControlItem15})
        Me.LayoutControlGroup6.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup6.Size = New System.Drawing.Size(874, 98)
        Me.LayoutControlGroup6.TextVisible = False
        '
        'LayoutControlItem14
        '
        Me.LayoutControlItem14.Control = Me.TextEdit4
        Me.LayoutControlItem14.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem14.Name = "LayoutControlItem2"
        Me.LayoutControlItem14.Size = New System.Drawing.Size(848, 36)
        Me.LayoutControlItem14.Text = "Path File HTML:"
        Me.LayoutControlItem14.TextSize = New System.Drawing.Size(87, 18)
        '
        'EmptySpaceItem3
        '
        Me.EmptySpaceItem3.Location = New System.Drawing.Point(0, 36)
        Me.EmptySpaceItem3.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem3.Size = New System.Drawing.Size(848, 10)
        '
        'LayoutControlItem15
        '
        Me.LayoutControlItem15.Control = Me.RichTextBox2
        Me.LayoutControlItem15.Location = New System.Drawing.Point(0, 46)
        Me.LayoutControlItem15.Name = "LayoutControlItem3"
        Me.LayoutControlItem15.Size = New System.Drawing.Size(848, 26)
        Me.LayoutControlItem15.TextVisible = False
        '
        'LayoutControlGroup7
        '
        Me.LayoutControlGroup7.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup7.GroupBordersVisible = False
        Me.LayoutControlGroup7.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem16, Me.EmptySpaceItem4, Me.LayoutControlItem17, Me.LayoutControlItem18})
        Me.LayoutControlGroup7.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup7.Size = New System.Drawing.Size(389, 255)
        Me.LayoutControlGroup7.TextVisible = False
        '
        'LayoutControlItem16
        '
        Me.LayoutControlItem16.Control = Me.TextEdit3
        Me.LayoutControlItem16.Location = New System.Drawing.Point(181, 0)
        Me.LayoutControlItem16.Name = "LayoutControlItem3"
        Me.LayoutControlItem16.Size = New System.Drawing.Size(182, 36)
        Me.LayoutControlItem16.Text = "Enter Link Page:"
        Me.LayoutControlItem16.TextSize = New System.Drawing.Size(93, 18)
        '
        'EmptySpaceItem4
        '
        Me.EmptySpaceItem4.Location = New System.Drawing.Point(0, 36)
        Me.EmptySpaceItem4.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem4.Size = New System.Drawing.Size(363, 10)
        '
        'LayoutControlItem17
        '
        Me.LayoutControlItem17.Control = Me.TextEdit2
        Me.LayoutControlItem17.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem17.Name = "LayoutControlItem4"
        Me.LayoutControlItem17.Size = New System.Drawing.Size(181, 36)
        Me.LayoutControlItem17.Text = "Path File HTML...."
        Me.LayoutControlItem17.TextSize = New System.Drawing.Size(93, 18)
        '
        'LayoutControlItem18
        '
        Me.LayoutControlItem18.Control = Me.RichTextBox1
        Me.LayoutControlItem18.Location = New System.Drawing.Point(0, 46)
        Me.LayoutControlItem18.Name = "LayoutControlItem5"
        Me.LayoutControlItem18.Size = New System.Drawing.Size(363, 183)
        Me.LayoutControlItem18.TextVisible = False
        '
        'ComboBoxEdit2
        '
        Me.ComboBoxEdit2.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboBoxEdit2.EditValue = ""
        Me.ComboBoxEdit2.Location = New System.Drawing.Point(775, -80)
        Me.ComboBoxEdit2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ComboBoxEdit2.Name = "ComboBoxEdit2"
        Me.ComboBoxEdit2.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboBoxEdit2.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ComboBoxEdit2.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBoxEdit2.Properties.Appearance.Options.UseFont = True
        Me.ComboBoxEdit2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxEdit2.Size = New System.Drawing.Size(148, 38)
        Me.ComboBoxEdit2.TabIndex = 45
        '
        'CheckFrench
        '
        Me.CheckFrench.Location = New System.Drawing.Point(339, 165)
        Me.CheckFrench.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CheckFrench.Name = "CheckFrench"
        Me.CheckFrench.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckFrench.Properties.Appearance.Options.UseFont = True
        Me.CheckFrench.Properties.Caption = " French"
        Me.CheckFrench.Size = New System.Drawing.Size(90, 27)
        Me.CheckFrench.TabIndex = 514
        '
        'CheckRussian
        '
        Me.CheckRussian.Location = New System.Drawing.Point(435, 165)
        Me.CheckRussian.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CheckRussian.Name = "CheckRussian"
        Me.CheckRussian.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckRussian.Properties.Appearance.Options.UseFont = True
        Me.CheckRussian.Properties.Caption = " Russian"
        Me.CheckRussian.Size = New System.Drawing.Size(90, 27)
        Me.CheckRussian.TabIndex = 514
        '
        'CheckJapanese
        '
        Me.CheckJapanese.Location = New System.Drawing.Point(531, 165)
        Me.CheckJapanese.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CheckJapanese.Name = "CheckJapanese"
        Me.CheckJapanese.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckJapanese.Properties.Appearance.Options.UseFont = True
        Me.CheckJapanese.Properties.Caption = " Japanese"
        Me.CheckJapanese.Size = New System.Drawing.Size(90, 27)
        Me.CheckJapanese.TabIndex = 514
        '
        'CheckHindi
        '
        Me.CheckHindi.Location = New System.Drawing.Point(627, 165)
        Me.CheckHindi.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CheckHindi.Name = "CheckHindi"
        Me.CheckHindi.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckHindi.Properties.Appearance.Options.UseFont = True
        Me.CheckHindi.Properties.Caption = " Hindi"
        Me.CheckHindi.Size = New System.Drawing.Size(90, 27)
        Me.CheckHindi.TabIndex = 514
        '
        'CheckArabic
        '
        Me.CheckArabic.Location = New System.Drawing.Point(723, 165)
        Me.CheckArabic.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CheckArabic.Name = "CheckArabic"
        Me.CheckArabic.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckArabic.Properties.Appearance.Options.UseFont = True
        Me.CheckArabic.Properties.Caption = " Arabic"
        Me.CheckArabic.Size = New System.Drawing.Size(90, 27)
        Me.CheckArabic.TabIndex = 514
        '
        'Remove
        '
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1210, 672)
        Me.Controls.Add(Me.GroupControl10)
        Me.Controls.Add(Me.PnlWaits)
        Me.Controls.Add(Me.XtraTabControl1)
        Me.Controls.Add(Me.GroupControl6)
        Me.Controls.Add(Me.CheckJapanese)
        Me.Controls.Add(Me.CheckArabic)
        Me.Controls.Add(Me.CheckHindi)
        Me.Controls.Add(Me.CheckRussian)
        Me.Controls.Add(Me.CheckFrench)
        Me.Controls.Add(Me.CheckBoxBoldText)
        Me.Controls.Add(Me.TextButtonname)
        Me.Controls.Add(Me.TextBoxSignature)
        Me.Controls.Add(Me.BntReset)
        Me.Controls.Add(Me.BNT_Backup)
        Me.Controls.Add(Me.ButtonSaveHTML)
        Me.Controls.Add(Me.LeftAlignPictureBox)
        Me.Controls.Add(Me.CenterAlignPictureBox)
        Me.Controls.Add(Me.RightAlignPictureBox)
        Me.Controls.Add(Me.fonttitel)
        Me.Controls.Add(Me.FontSizeMessage)
        Me.Controls.Add(Me.ComboBox_ButtonColor)
        Me.Controls.Add(Me.ComboBox_TextColor)
        Me.Controls.Add(Me.ComboBox_TitleColor)
        Me.Controls.Add(Me.TextBoxLogoLM)
        Me.Controls.Add(Me.TextBoxTitle)
        Me.Controls.Add(Me.SpinEdit1)
        Me.Controls.Add(Me.Label40)
        Me.Controls.Add(Me.Label38)
        Me.Controls.Add(Me.Label41)
        Me.Controls.Add(Me.Label37)
        Me.Controls.Add(Me.Label36)
        Me.Controls.Add(Me.Label35)
        Me.Controls.Add(Me.Label34)
        Me.Controls.Add(Me.Label39)
        Me.Controls.Add(Me.Label33)
        Me.Controls.Add(Me.NumericUpDown1)
        Me.Controls.Add(Me.ComboBoxEdit2)
        Me.Controls.Add(Me.cmbSites)
        Me.Controls.Add(Me.SimpleButton5)
        Me.Controls.Add(Me.SeparatorControl6)
        Me.Controls.Add(Me.Panel13)
        Me.Controls.Add(Me.GroupBox1)
        Me.Controls.Add(Me.LogInNormalTextBox1)
        Me.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Name = "Remove"
        Me.Text = "Remove"
        CType(Me.TxtSMTPServer.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PnlWait.ResumeLayout(False)
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.TxtSubject.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtTag_2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtTag_1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_limitSend.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtFromMail.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtFromName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtPort.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckSSL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtPassword.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel13.ResumeLayout(False)
        Me.Panel13.PerformLayout()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ProgressBarControl2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlResult.ResumeLayout(False)
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        CType(Me.LogInNormalTextBox1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage6.ResumeLayout(False)
        Me.Panel8.ResumeLayout(False)
        CType(Me.XtraTabControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl2.ResumeLayout(False)
        Me.TabAddLogo.ResumeLayout(False)
        Me.Panel6.ResumeLayout(False)
        Me.TabSendAttachment.ResumeLayout(False)
        Me.Panel4.ResumeLayout(False)
        Me.Panel4.PerformLayout()
        Me.TabConvertLetterToPNG.ResumeLayout(False)
        Me.ConvertLinkToQRCODE.ResumeLayout(False)
        Me.Panel10.ResumeLayout(False)
        Me.XtraTabPage3.ResumeLayout(False)
        Me.XtraTabPage4.ResumeLayout(False)
        Me.Panel11.ResumeLayout(False)
        Me.XtraTabPageSleepTime.ResumeLayout(False)
        Me.Panel12.ResumeLayout(False)
        Me.XtraTabPage5.ResumeLayout(False)
        Me.Panel15.ResumeLayout(False)
        CType(Me.LayoutControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl4.ResumeLayout(False)
        CType(Me.GroupControl8, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl8.ResumeLayout(False)
        Me.GroupControl8.PerformLayout()
        CType(Me.SeparatorControl3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBoxSleepTime.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chk_limitSend.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl7, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl7.ResumeLayout(False)
        Me.GroupControl7.PerformLayout()
        CType(Me.PictureEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboFlagStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboSensitivity.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl5.ResumeLayout(False)
        CType(Me.Pic_QRCode, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtWidth.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHeight.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtCode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl3.ResumeLayout(False)
        Me.GroupControl3.PerformLayout()
        CType(Me.ChkLogo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.piclogo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl4.ResumeLayout(False)
        Me.GroupControl4.PerformLayout()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.trcThreads.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.trcThreads, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtTitilOffice365.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Switch_LetterConvertorLink.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        Me.GroupControl2.PerformLayout()
        CType(Me.ToggleSwitch1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtattach.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbTags.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cmbSites.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage2.ResumeLayout(False)
        Me.Panel7.ResumeLayout(False)
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl2.ResumeLayout(False)
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl3.ResumeLayout(False)
        CType(Me.txtLetterPath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtNewLink.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl9, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl9.ResumeLayout(False)
        Me.Panel16.ResumeLayout(False)
        CType(Me.LayoutControl5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl5.ResumeLayout(False)
        CType(Me.LetterEncoder_txtFilepath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabSmtp.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        CType(Me.ProgressBarControl3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PnlWaits, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PnlWaits.ResumeLayout(False)
        Me.XtraTabPage1.ResumeLayout(False)
        Me.Panel3.ResumeLayout(False)
        CType(Me.ProgressBarControl4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemTextEdit2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemButtonEdit2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemCheckEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemImageEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemPictureEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        CType(Me.NumericUpDown1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SpinEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxTitle.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxLogoLM.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBox_TitleColor.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBox_TextColor.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBox_ButtonColor.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FontSizeMessage.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.fonttitel.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RightAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CenterAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LeftAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxSignature.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextButtonname.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckBoxBoldText.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl10, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl10.ResumeLayout(False)
        CType(Me.LayoutControl6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl6.ResumeLayout(False)
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl11, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl11.ResumeLayout(False)
        Me.Panel14.ResumeLayout(False)
        CType(Me.LayoutControl7, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl7.ResumeLayout(False)
        CType(Me.TextEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBoxEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckFrench.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckRussian.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckJapanese.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckHindi.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckArabic.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents TxtSMTPServer As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents PnlWait As Panel
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents DxErrorProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider
    Friend WithEvents GroupBox1 As GroupBox
    Friend WithEvents BntAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntTest As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txt_limitSend As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtFromMail As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtFromName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtPort As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Bnt_Reset As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents CheckSSL As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Label18 As Label
    Friend WithEvents Label17 As Label
    Friend WithEvents Label16 As Label
    Friend WithEvents Label15 As Label
    Friend WithEvents Label14 As Label
    Friend WithEvents Label13 As Label
    Friend WithEvents Label12 As Label
    Friend WithEvents Label11 As Label
    Friend WithEvents Label10 As Label
    Friend WithEvents Label9 As Label
    Friend WithEvents Label8 As Label
    Friend WithEvents Label6 As Label
    Friend WithEvents Label7 As Label
    Friend WithEvents Label5 As Label
    Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Label4 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents TxtPassword As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtTag_2 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TxtTag_1 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TxtSubject As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents Panel13 As Panel
    Friend WithEvents Label19 As Label
    Friend WithEvents PictureBox5 As PictureBox
    Friend WithEvents PictureBox4 As PictureBox
    Friend WithEvents ProgressBarControl2 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents Label20 As Label
    Friend WithEvents lblfailed As Label
    Friend WithEvents lblSuccess As Label
    Friend WithEvents ProgressBarControl1 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents lbl1 As Label
    Friend WithEvents lblTotal As Label
    Friend WithEvents lbltotalstmp As Label
    Friend WithEvents SeparatorControl1 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents SeparatorControl5 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents SeparatorControl2 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents pnlResult As Panel
    Friend WithEvents Panel1 As Panel
    Friend WithEvents LogInNormalTextBox1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SeparatorControl6 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents SimpleButton5 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabSmtp As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel2 As Panel
    Friend WithEvents ProgressBarControl3 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents PnlWaits As DevExpress.XtraEditors.GroupControl
    Friend WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ProgressPanel2 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents Label21 As Label
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn8 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn11 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn7 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn4 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn5 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn6 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn9 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn10 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn23 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn24 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn25 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn18 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn19 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents XtraTabPage1 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel3 As Panel
    Friend WithEvents ProgressBarControl4 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents GridControl2 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView2 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn12 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn13 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemTextEdit2 As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents GridColumn14 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents col_status_icon As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn15 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn16 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn17 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn20 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemButtonEdit2 As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents GridColumn21 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemCheckEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit
    Friend WithEvents GridColumn22 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn26 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn27 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn28 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemImageEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemImageEdit
    Friend WithEvents RepositoryItemPictureEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemPictureEdit
    Friend WithEvents XtraTabPage2 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel7 As Panel
    Friend WithEvents LayoutControl2 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LayoutControl3 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents txtLetter As RichTextBox
    Friend WithEvents txtLetterPath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtNewLink As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GroupControl9 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Panel16 As Panel
    Friend WithEvents LayoutControl5 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LetterEncoder_txtFilepath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LetterEncoder_Richtext As RichTextBox
    Friend WithEvents LayoutControlGroup5 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem2 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem13 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents lblTrcThreads As Label
    Friend WithEvents XtraTabPage5 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel15 As Panel
    Friend WithEvents LayoutControl4 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents GroupControl8 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents SeparatorControl3 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents ComboBoxSleepTime As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents chk_limitSend As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Label22 As Label
    Friend WithEvents Label23 As Label
    Friend WithEvents Label24 As Label
    Friend WithEvents TextEdit1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SeparatorControl4 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents GroupControl7 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Label25 As Label
    Friend WithEvents PictureEdit4 As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents cmbSites As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ComboFlagStatus As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Label26 As Label
    Friend WithEvents Label27 As Label
    Friend WithEvents ComboSensitivity As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents GroupControl5 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Pic_QRCode As PictureBox
    Friend WithEvents lbl_success_QRcode As Label
    Friend WithEvents txtWidth As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureEdit1 As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents txtHeight As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureEdit3 As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents txtCode As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureEdit2 As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents SimpleButton3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnGenerate As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnExport As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupControl3 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ChkLogo As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents BntResetLetterLogo As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lblLogoTitle As Label
    Friend WithEvents bntLetterLogo As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents piclogo As PictureBox
    Friend WithEvents GroupControl4 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ComboBoxEdit1 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents bntResetLetterConvertor As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label28 As Label
    Friend WithEvents trcThreads As DevExpress.XtraEditors.TrackBarControl
    Friend WithEvents txtTitilOffice365 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Switch_LetterConvertorLink As DevExpress.XtraEditors.ToggleSwitch
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents BntRemoveAttachment As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lblattacmentFileCount As Label
    Friend WithEvents ToggleSwitch1 As DevExpress.XtraEditors.ToggleSwitch
    Friend WithEvents bntAddAttachment As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtattach As DevExpress.XtraEditors.TextEdit
    Friend WithEvents cbTags As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents XtraTabPage6 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel8 As Panel
    Friend WithEvents XtraTabControl2 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabAddLogo As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel6 As Panel
    Friend WithEvents Label29 As Label
    Friend WithEvents TabSendAttachment As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel4 As Panel
    Friend WithEvents txtbody As TextBox
    Friend WithEvents TabConvertLetterToPNG As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel5 As Panel
    Friend WithEvents ConvertLinkToQRCODE As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel10 As Panel
    Friend WithEvents Label30 As Label
    Friend WithEvents XtraTabPage3 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel9 As Panel
    Friend WithEvents XtraTabPage4 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel11 As Panel
    Friend WithEvents Label31 As Label
    Friend WithEvents XtraTabPageSleepTime As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel12 As Panel
    Friend WithEvents Label32 As Label
    Friend WithEvents NumericUpDown1 As NumericUpDown
    Friend WithEvents SpinEdit1 As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents TextBoxLogoLM As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextBoxTitle As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ComboBox_TitleColor As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ComboBox_TextColor As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ComboBox_ButtonColor As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents FontSizeMessage As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents fonttitel As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents Label33 As Label
    Friend WithEvents Label34 As Label
    Friend WithEvents Label35 As Label
    Friend WithEvents Label36 As Label
    Friend WithEvents Label38 As Label
    Friend WithEvents Label37 As Label
    Friend WithEvents Label39 As Label
    Friend WithEvents RightAlignPictureBox As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents CenterAlignPictureBox As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents LeftAlignPictureBox As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ButtonSaveHTML As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BNT_Backup As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntReset As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextBoxSignature As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label40 As Label
    Friend WithEvents TextButtonname As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label41 As Label
    Friend WithEvents CheckBoxBoldText As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents GroupControl6 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents GroupControl10 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LayoutControl6 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents TextEdit2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEdit3 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GroupControl11 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Panel14 As Panel
    Friend WithEvents LayoutControl7 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents TextEdit4 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents RichTextBox2 As RichTextBox
    Friend WithEvents LayoutControlGroup6 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem14 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem3 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup7 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem4 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem17 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem18 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents CheckFrench As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ComboBoxEdit2 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents CheckRussian As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckJapanese As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckHindi As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckArabic As DevExpress.XtraEditors.CheckEdit
End Class
