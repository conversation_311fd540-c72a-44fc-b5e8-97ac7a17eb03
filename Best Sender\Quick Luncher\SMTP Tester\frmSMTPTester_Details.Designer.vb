﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSMTPTester_Details
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmSMTPTester_Details))
        Me.CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.DxErrorProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(Me.components)
        Me.TxtSMTPServer = New DevExpress.XtraEditors.TextEdit()
        Me.TxtUsername = New DevExpress.XtraEditors.TextEdit()
        Me.TxtPassword = New DevExpress.XtraEditors.TextEdit()
        Me.TxtPort = New DevExpress.XtraEditors.TextEdit()
        Me.bntSave = New DevExpress.XtraEditors.SimpleButton()
        Me.BehaviorManager1 = New DevExpress.Utils.Behaviors.BehaviorManager(Me.components)
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtSMTPServer.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtUsername.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtPassword.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtPort.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BehaviorManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'CheckEdit1
        '
        Me.CheckEdit1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.CheckEdit1.Location = New System.Drawing.Point(434, 222)
        Me.CheckEdit1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.CheckEdit1.Name = "CheckEdit1"
        Me.CheckEdit1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.CheckEdit1.Properties.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.CheckEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.CheckEdit1.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEdit1.Properties.Caption = "View password"
        Me.CheckEdit1.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style15
        Me.CheckEdit1.Size = New System.Drawing.Size(19, 22)
        Me.CheckEdit1.TabIndex = 356
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.White
        Me.Label4.Location = New System.Drawing.Point(79, 278)
        Me.Label4.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(45, 19)
        Me.Label4.TabIndex = 66
        Me.Label4.Text = "Port : "
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.White
        Me.Label2.Location = New System.Drawing.Point(79, 192)
        Me.Label2.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(79, 19)
        Me.Label2.TabIndex = 64
        Me.Label2.Text = "Password : "
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.White
        Me.Label1.Location = New System.Drawing.Point(79, 109)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(89, 19)
        Me.Label1.TabIndex = 62
        Me.Label1.Text = "User Name : "
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.Color.White
        Me.Label3.Location = New System.Drawing.Point(79, 30)
        Me.Label3.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(99, 19)
        Me.Label3.TabIndex = 60
        Me.Label3.Text = "SMTP Server : "
        '
        'DxErrorProvider1
        '
        Me.DxErrorProvider1.ContainerControl = Me
        '
        'TxtSMTPServer
        '
        Me.TxtSMTPServer.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtSMTPServer.EditValue = ""
        Me.TxtSMTPServer.Location = New System.Drawing.Point(79, 52)
        Me.TxtSMTPServer.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtSMTPServer.Name = "TxtSMTPServer"
        Me.TxtSMTPServer.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtSMTPServer.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtSMTPServer.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtSMTPServer.Properties.Appearance.Options.UseBackColor = True
        Me.TxtSMTPServer.Properties.Appearance.Options.UseFont = True
        Me.TxtSMTPServer.Properties.Appearance.Options.UseForeColor = True
        Me.TxtSMTPServer.Properties.NullValuePrompt = "Host SMTP"
        Me.TxtSMTPServer.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtSMTPServer.Size = New System.Drawing.Size(374, 30)
        Me.TxtSMTPServer.TabIndex = 500
        Me.TxtSMTPServer.ToolTipTitle = "https:// or http://"
        '
        'TxtUsername
        '
        Me.TxtUsername.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtUsername.EditValue = ""
        Me.TxtUsername.Location = New System.Drawing.Point(79, 131)
        Me.TxtUsername.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtUsername.Name = "TxtUsername"
        Me.TxtUsername.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtUsername.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtUsername.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtUsername.Properties.Appearance.Options.UseBackColor = True
        Me.TxtUsername.Properties.Appearance.Options.UseFont = True
        Me.TxtUsername.Properties.Appearance.Options.UseForeColor = True
        Me.TxtUsername.Properties.NullValuePrompt = "Enter a vaild email address..."
        Me.TxtUsername.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtUsername.Size = New System.Drawing.Size(374, 30)
        Me.TxtUsername.TabIndex = 500
        Me.TxtUsername.ToolTipTitle = "https:// or http://"
        '
        'TxtPassword
        '
        Me.TxtPassword.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtPassword.EditValue = "sssss"
        Me.TxtPassword.Location = New System.Drawing.Point(79, 218)
        Me.TxtPassword.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtPassword.Name = "TxtPassword"
        Me.TxtPassword.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtPassword.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtPassword.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtPassword.Properties.Appearance.Options.UseBackColor = True
        Me.TxtPassword.Properties.Appearance.Options.UseFont = True
        Me.TxtPassword.Properties.Appearance.Options.UseForeColor = True
        Me.TxtPassword.Properties.NullValuePrompt = "Password"
        Me.TxtPassword.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtPassword.Properties.UseSystemPasswordChar = True
        Me.TxtPassword.Size = New System.Drawing.Size(374, 30)
        Me.TxtPassword.TabIndex = 500
        Me.TxtPassword.ToolTipTitle = "Password"
        '
        'TxtPort
        '
        Me.TxtPort.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtPort.EditValue = ""
        Me.TxtPort.Location = New System.Drawing.Point(79, 300)
        Me.TxtPort.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtPort.Name = "TxtPort"
        Me.TxtPort.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtPort.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtPort.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtPort.Properties.Appearance.Options.UseBackColor = True
        Me.TxtPort.Properties.Appearance.Options.UseFont = True
        Me.TxtPort.Properties.Appearance.Options.UseForeColor = True
        Me.TxtPort.Properties.NullValuePrompt = "Port | 587 | 465 | 25 | 2525"
        Me.TxtPort.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtPort.Size = New System.Drawing.Size(374, 30)
        Me.TxtPort.TabIndex = 500
        Me.TxtPort.ToolTipTitle = "https:// or http://"
        '
        'bntSave
        '
        Me.bntSave.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSave.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSave.Appearance.Font = New System.Drawing.Font("Comfortaa", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bntSave.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSave.Appearance.Options.UseBackColor = True
        Me.bntSave.Appearance.Options.UseBorderColor = True
        Me.bntSave.Appearance.Options.UseFont = True
        Me.bntSave.Appearance.Options.UseForeColor = True
        Me.bntSave.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntSave.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntSave.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntSave.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntSave.AppearanceDisabled.Options.UseBackColor = True
        Me.bntSave.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntSave.AppearanceDisabled.Options.UseFont = True
        Me.bntSave.AppearanceDisabled.Options.UseForeColor = True
        Me.bntSave.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntSave.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSave.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntSave.AppearanceHovered.Options.UseBackColor = True
        Me.bntSave.AppearanceHovered.Options.UseBorderColor = True
        Me.bntSave.AppearanceHovered.Options.UseForeColor = True
        Me.bntSave.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSave.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSave.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntSave.AppearancePressed.Options.UseBackColor = True
        Me.bntSave.AppearancePressed.Options.UseBorderColor = True
        Me.bntSave.AppearancePressed.Options.UseForeColor = True
        Me.bntSave.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.bntSave.Location = New System.Drawing.Point(166, 360)
        Me.bntSave.Name = "bntSave"
        Me.bntSave.Size = New System.Drawing.Size(177, 56)
        Me.bntSave.TabIndex = 501
        Me.bntSave.Text = "Save Changes"
        '
        'frmSMTPTester_Details
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.Appearance.ForeColor = System.Drawing.Color.White
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(546, 457)
        Me.Controls.Add(Me.bntSave)
        Me.Controls.Add(Me.TxtPort)
        Me.Controls.Add(Me.CheckEdit1)
        Me.Controls.Add(Me.TxtPassword)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.TxtUsername)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.TxtSMTPServer)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Icon = CType(resources.GetObject("frmSMTPTester_Details.IconOptions.Icon"), System.Drawing.Icon)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Logo_NewBestSender
        Me.IconOptions.ShowIcon = False
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmSMTPTester_Details"
        Me.Opacity = 0.99R
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "  SMTP Account Details ..."
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtSMTPServer.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtUsername.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtPassword.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtPort.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BehaviorManager1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Label4 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents DxErrorProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider
    Friend WithEvents TxtPort As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtPassword As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtUsername As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtSMTPServer As DevExpress.XtraEditors.TextEdit
    Friend WithEvents bntSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BehaviorManager1 As DevExpress.Utils.Behaviors.BehaviorManager
End Class
