﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class IMAP_BSV
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.bntUploade = New DevExpress.XtraEditors.SimpleButton()
        Me.BntStop = New DevExpress.XtraEditors.SimpleButton()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.ProgressBarControl1 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
        Me.txtPortImap = New DevExpress.XtraEditors.TextEdit()
        Me.txtPasswordImap = New DevExpress.XtraEditors.TextEdit()
        Me.txtEmailImap = New DevExpress.XtraEditors.TextEdit()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.OpenFileDialog1 = New System.Windows.Forms.OpenFileDialog()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtPortImap.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtPasswordImap.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtEmailImap.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.bntUploade)
        Me.LayoutControl1.Controls.Add(Me.BntStop)
        Me.LayoutControl1.Controls.Add(Me.Label1)
        Me.LayoutControl1.Controls.Add(Me.ProgressBarControl1)
        Me.LayoutControl1.Controls.Add(Me.SimpleButton2)
        Me.LayoutControl1.Controls.Add(Me.SimpleButton1)
        Me.LayoutControl1.Controls.Add(Me.CheckEdit1)
        Me.LayoutControl1.Controls.Add(Me.txtPortImap)
        Me.LayoutControl1.Controls.Add(Me.txtPasswordImap)
        Me.LayoutControl1.Controls.Add(Me.txtEmailImap)
        Me.LayoutControl1.Controls.Add(Me.GridControl1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(972, 602)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'bntUploade
        '
        Me.bntUploade.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntUploade.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntUploade.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntUploade.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntUploade.Appearance.Options.UseBackColor = True
        Me.bntUploade.Appearance.Options.UseBorderColor = True
        Me.bntUploade.Appearance.Options.UseFont = True
        Me.bntUploade.Appearance.Options.UseForeColor = True
        Me.bntUploade.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntUploade.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntUploade.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntUploade.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntUploade.AppearanceDisabled.Options.UseBackColor = True
        Me.bntUploade.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntUploade.AppearanceDisabled.Options.UseFont = True
        Me.bntUploade.AppearanceDisabled.Options.UseForeColor = True
        Me.bntUploade.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntUploade.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntUploade.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntUploade.AppearanceHovered.Options.UseBackColor = True
        Me.bntUploade.AppearanceHovered.Options.UseBorderColor = True
        Me.bntUploade.AppearanceHovered.Options.UseForeColor = True
        Me.bntUploade.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntUploade.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntUploade.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntUploade.AppearancePressed.Options.UseBackColor = True
        Me.bntUploade.AppearancePressed.Options.UseBorderColor = True
        Me.bntUploade.AppearancePressed.Options.UseForeColor = True
        Me.bntUploade.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.bntUploade.Location = New System.Drawing.Point(16, 52)
        Me.bntUploade.Name = "bntUploade"
        Me.bntUploade.Size = New System.Drawing.Size(109, 38)
        Me.bntUploade.StyleController = Me.LayoutControl1
        Me.bntUploade.TabIndex = 490
        Me.bntUploade.Text = "Add List"
        '
        'BntStop
        '
        Me.BntStop.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntStop.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntStop.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntStop.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntStop.Appearance.Options.UseBackColor = True
        Me.BntStop.Appearance.Options.UseBorderColor = True
        Me.BntStop.Appearance.Options.UseFont = True
        Me.BntStop.Appearance.Options.UseForeColor = True
        Me.BntStop.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntStop.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntStop.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntStop.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntStop.AppearanceDisabled.Options.UseBackColor = True
        Me.BntStop.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntStop.AppearanceDisabled.Options.UseFont = True
        Me.BntStop.AppearanceDisabled.Options.UseForeColor = True
        Me.BntStop.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntStop.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntStop.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntStop.AppearanceHovered.Options.UseBackColor = True
        Me.BntStop.AppearanceHovered.Options.UseBorderColor = True
        Me.BntStop.AppearanceHovered.Options.UseForeColor = True
        Me.BntStop.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntStop.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntStop.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntStop.AppearancePressed.Options.UseBackColor = True
        Me.BntStop.AppearancePressed.Options.UseBorderColor = True
        Me.BntStop.AppearancePressed.Options.UseForeColor = True
        Me.BntStop.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.BntStop.Location = New System.Drawing.Point(348, 52)
        Me.BntStop.Name = "BntStop"
        Me.BntStop.Size = New System.Drawing.Size(91, 38)
        Me.BntStop.StyleController = Me.LayoutControl1
        Me.BntStop.TabIndex = 489
        Me.BntStop.Text = "Stop"
        '
        'Label1
        '
        Me.Label1.Location = New System.Drawing.Point(16, 111)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(940, 31)
        Me.Label1.TabIndex = 488
        Me.Label1.Text = "Label1"
        '
        'ProgressBarControl1
        '
        Me.ProgressBarControl1.Location = New System.Drawing.Point(16, 562)
        Me.ProgressBarControl1.Name = "ProgressBarControl1"
        Me.ProgressBarControl1.Properties.ShowTitle = True
        Me.ProgressBarControl1.Size = New System.Drawing.Size(940, 24)
        Me.ProgressBarControl1.StyleController = Me.LayoutControl1
        Me.ProgressBarControl1.TabIndex = 487
        '
        'SimpleButton2
        '
        Me.SimpleButton2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton2.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton2.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton2.Appearance.Options.UseBackColor = True
        Me.SimpleButton2.Appearance.Options.UseBorderColor = True
        Me.SimpleButton2.Appearance.Options.UseFont = True
        Me.SimpleButton2.Appearance.Options.UseForeColor = True
        Me.SimpleButton2.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton2.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton2.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton2.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton2.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton2.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton2.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton2.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton2.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton2.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton2.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton2.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton2.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton2.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton2.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton2.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton2.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton2.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton2.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton2.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.SimpleButton2.Location = New System.Drawing.Point(243, 52)
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(99, 38)
        Me.SimpleButton2.StyleController = Me.LayoutControl1
        Me.SimpleButton2.TabIndex = 486
        Me.SimpleButton2.Text = "Save"
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton1.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Options.UseBackColor = True
        Me.SimpleButton1.Appearance.Options.UseBorderColor = True
        Me.SimpleButton1.Appearance.Options.UseFont = True
        Me.SimpleButton1.Appearance.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton1.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton1.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton1.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton1.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton1.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Connect32x32
        Me.SimpleButton1.Location = New System.Drawing.Point(131, 52)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(106, 38)
        Me.SimpleButton1.StyleController = Me.LayoutControl1
        Me.SimpleButton1.TabIndex = 485
        Me.SimpleButton1.Text = "Connect"
        '
        'CheckEdit1
        '
        Me.CheckEdit1.Location = New System.Drawing.Point(616, 52)
        Me.CheckEdit1.Name = "CheckEdit1"
        Me.CheckEdit1.Properties.Caption = "SSL"
        Me.CheckEdit1.Size = New System.Drawing.Size(340, 22)
        Me.CheckEdit1.StyleController = Me.LayoutControl1
        Me.CheckEdit1.TabIndex = 484
        '
        'txtPortImap
        '
        Me.txtPortImap.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtPortImap.EditValue = ""
        Me.txtPortImap.Location = New System.Drawing.Point(523, 52)
        Me.txtPortImap.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtPortImap.Name = "txtPortImap"
        Me.txtPortImap.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtPortImap.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtPortImap.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtPortImap.Properties.Appearance.Options.UseBackColor = True
        Me.txtPortImap.Properties.Appearance.Options.UseFont = True
        Me.txtPortImap.Properties.Appearance.Options.UseForeColor = True
        Me.txtPortImap.Properties.NullValuePrompt = "993"
        Me.txtPortImap.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtPortImap.Size = New System.Drawing.Size(87, 30)
        Me.txtPortImap.StyleController = Me.LayoutControl1
        Me.txtPortImap.TabIndex = 481
        '
        'txtPasswordImap
        '
        Me.txtPasswordImap.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtPasswordImap.EditValue = ""
        Me.txtPasswordImap.Location = New System.Drawing.Point(523, 16)
        Me.txtPasswordImap.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtPasswordImap.Name = "txtPasswordImap"
        Me.txtPasswordImap.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtPasswordImap.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtPasswordImap.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtPasswordImap.Properties.Appearance.Options.UseBackColor = True
        Me.txtPasswordImap.Properties.Appearance.Options.UseFont = True
        Me.txtPasswordImap.Properties.Appearance.Options.UseForeColor = True
        Me.txtPasswordImap.Properties.NullValuePrompt = "********"
        Me.txtPasswordImap.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtPasswordImap.Size = New System.Drawing.Size(433, 30)
        Me.txtPasswordImap.StyleController = Me.LayoutControl1
        Me.txtPasswordImap.TabIndex = 482
        '
        'txtEmailImap
        '
        Me.txtEmailImap.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtEmailImap.EditValue = ""
        Me.txtEmailImap.Location = New System.Drawing.Point(94, 16)
        Me.txtEmailImap.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtEmailImap.Name = "txtEmailImap"
        Me.txtEmailImap.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtEmailImap.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtEmailImap.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtEmailImap.Properties.Appearance.Options.UseBackColor = True
        Me.txtEmailImap.Properties.Appearance.Options.UseFont = True
        Me.txtEmailImap.Properties.Appearance.Options.UseForeColor = True
        Me.txtEmailImap.Properties.NullValuePrompt = "Email"
        Me.txtEmailImap.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtEmailImap.Size = New System.Drawing.Size(345, 30)
        Me.txtEmailImap.StyleController = Me.LayoutControl1
        Me.txtEmailImap.TabIndex = 483
        '
        'GridControl1
        '
        Me.GridControl1.EmbeddedNavigator.Margin = New System.Windows.Forms.Padding(4)
        Me.GridControl1.Location = New System.Drawing.Point(16, 148)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(940, 398)
        Me.GridControl1.TabIndex = 480
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2, Me.GridColumn3, Me.GridColumn4, Me.GridColumn5})
        Me.GridView1.DetailHeight = 485
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsEditForm.PopupEditFormWidth = 933
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'GridColumn1
        '
        Me.GridColumn1.Caption = "#"
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        Me.GridColumn1.Width = 40
        '
        'GridColumn2
        '
        Me.GridColumn2.Caption = "Email inbox"
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 1
        Me.GridColumn2.Width = 271
        '
        'GridColumn3
        '
        Me.GridColumn3.Caption = "Received Date"
        Me.GridColumn3.Name = "GridColumn3"
        Me.GridColumn3.Visible = True
        Me.GridColumn3.VisibleIndex = 2
        Me.GridColumn3.Width = 271
        '
        'GridColumn4
        '
        Me.GridColumn4.Caption = "Email Spam"
        Me.GridColumn4.Name = "GridColumn4"
        Me.GridColumn4.Visible = True
        Me.GridColumn4.VisibleIndex = 3
        Me.GridColumn4.Width = 271
        '
        'GridColumn5
        '
        Me.GridColumn5.Caption = "Email Send"
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 4
        Me.GridColumn5.Width = 276
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.EmptySpaceItem1, Me.LayoutControlItem8, Me.LayoutControlItem3, Me.LayoutControlItem2, Me.LayoutControlItem1, Me.LayoutControlItem7, Me.LayoutControlItem9, Me.LayoutControlItem6, Me.LayoutControlItem5, Me.LayoutControlItem4, Me.LayoutControlItem10, Me.LayoutControlItem11})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(972, 602)
        Me.Root.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 536)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(946, 10)
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.GridControl1
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 132)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(946, 404)
        Me.LayoutControlItem8.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.SimpleButton1
        Me.LayoutControlItem3.Location = New System.Drawing.Point(115, 36)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(112, 44)
        Me.LayoutControlItem3.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.SimpleButton2
        Me.LayoutControlItem2.Location = New System.Drawing.Point(227, 36)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(105, 44)
        Me.LayoutControlItem2.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.ProgressBarControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 546)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(946, 30)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.txtEmailImap
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(429, 36)
        Me.LayoutControlItem7.Text = "Email : "
        Me.LayoutControlItem7.TextSize = New System.Drawing.Size(62, 18)
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.Label1
        Me.LayoutControlItem9.Location = New System.Drawing.Point(0, 80)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(946, 52)
        Me.LayoutControlItem9.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 15, 0)
        Me.LayoutControlItem9.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.txtPasswordImap
        Me.LayoutControlItem6.Location = New System.Drawing.Point(429, 0)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(517, 36)
        Me.LayoutControlItem6.Text = "Password : "
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(62, 18)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.txtPortImap
        Me.LayoutControlItem5.Location = New System.Drawing.Point(429, 36)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(171, 44)
        Me.LayoutControlItem5.Text = "Port : "
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(62, 18)
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.CheckEdit1
        Me.LayoutControlItem4.Location = New System.Drawing.Point(600, 36)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(346, 44)
        Me.LayoutControlItem4.TextVisible = False
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.BntStop
        Me.LayoutControlItem10.Location = New System.Drawing.Point(332, 36)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(97, 44)
        Me.LayoutControlItem10.TextVisible = False
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.bntUploade
        Me.LayoutControlItem11.Location = New System.Drawing.Point(0, 36)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(115, 44)
        Me.LayoutControlItem11.TextVisible = False
        '
        'BackgroundWorker1
        '
        '
        'OpenFileDialog1
        '
        Me.OpenFileDialog1.FileName = "OpenFileDialog1"
        '
        'IMAP_BSV
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(40, Byte), Integer), CType(CType(40, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.LayoutControl1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "IMAP_BSV"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "IMAP"
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtPortImap.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtPasswordImap.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtEmailImap.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
    End Sub
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents ProgressBarControl1 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents txtPortImap As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtPasswordImap As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtEmailImap As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn4 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn5 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents Label1 As Label
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents BntStop As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents bntUploade As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents OpenFileDialog1 As OpenFileDialog
End Class
