Imports System.IO
Imports System.Data
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraGrid.Views.Base
Imports System.Drawing.Drawing2D
Imports System.Diagnostics

Public Class frmRemovePassword
    ' تعريف متغيرات عامة
    Private emailTable As DataTable
    Private totalEmails As Integer = 0
    Private removedPasswords As Integer = 0
    Private duplicateEmails As Integer = 0
    Private currentOperation As String = ""  ' لتتبع نوع العملية الحالية: "LOAD", "PROCESS", "SAVE"

    ' تعريف المتغيرات كـ WithEvents
    Private WithEvents MainPanel As Panel
    Private WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Private WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Private WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Private WithEvents bntSave As DevExpress.XtraEditors.SimpleButton
    Private WithEvents bntStart As DevExpress.XtraEditors.SimpleButton
    Private WithEvents bntUploade As DevExpress.XtraEditors.SimpleButton
    Private WithEvents bntRestAll As DevExpress.XtraEditors.SimpleButton
    Private WithEvents SaveFileDialog1 As SaveFileDialog
    Private WithEvents OpenFileDialog1 As OpenFileDialog
    Private WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker

    ' تعريف أعمدة الشبكة
    Private colID As DevExpress.XtraGrid.Columns.GridColumn
    Private colEmailWithPass As DevExpress.XtraGrid.Columns.GridColumn
    Private colEmailOnly As DevExpress.XtraGrid.Columns.GridColumn

    ''' <summary>
    ''' حدث تحميل النموذج
    ''' </summary>
    Private Sub frmRemovePassword_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' تهيئة MainPanel ليكون في المنتصف
            CenterMainPanel()

            ' إضافة معالج حدث لتغيير حجم النموذج
            AddHandler Me.Resize, AddressOf frmRemovePassword_Resize

            ' إضافة معالج حدث Paint للـ MainPanel
            AddHandler MainPanel.Paint, AddressOf MainPanel_Paint

            ' تهيئة GridControl1
            InitializeGridControl()

            ' تطبيق تصميم بينانس على GridControl1
            ApplyBinanceStyleToGrid()

            ' تهيئة جدول البيانات
            InitializeDataTable()

            ' تعيين خصائص الأزرار
            SetupButtons()
        Catch ex As Exception
            XtraMessageBox.Show("Error loading form: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تعيين خصائص الأزرار
    ''' </summary>
    Private Sub SetupButtons()
        ' تعيين خصائص الأزرار
        bntUploade.Appearance.BackColor = Color.FromArgb(254, 219, 65)
        bntUploade.Appearance.ForeColor = Color.Black
        bntUploade.Appearance.Font = New Font("Comfortaa", 9, FontStyle.Bold)

        bntStart.Appearance.BackColor = Color.FromArgb(254, 219, 65)
        bntStart.Appearance.ForeColor = Color.Black
        bntStart.Appearance.Font = New Font("Comfortaa", 9, FontStyle.Bold)

        bntSave.Appearance.BackColor = Color.FromArgb(254, 219, 65)
        bntSave.Appearance.ForeColor = Color.Black
        bntSave.Appearance.Font = New Font("Comfortaa", 9, FontStyle.Bold)

        bntRestAll.Appearance.BackColor = Color.FromArgb(254, 219, 65)
        bntRestAll.Appearance.ForeColor = Color.Black
        bntRestAll.Appearance.Font = New Font("Comfortaa", 9, FontStyle.Bold)
    End Sub

    ''' <summary>
    ''' تهيئة جدول البيانات
    ''' </summary>
    Private Sub InitializeDataTable()
        ' إنشاء جدول البيانات
        emailTable = New DataTable()
        emailTable.Columns.Add("ID", GetType(Integer))
        emailTable.Columns.Add("EmailWithPass", GetType(String))
        emailTable.Columns.Add("EmailOnly", GetType(String))

        ' إضافة بعض البيانات للاختبار
        emailTable.Rows.Add(1, "<EMAIL>:password123", "<EMAIL>")
        emailTable.Rows.Add(2, "<EMAIL>|pass456", "<EMAIL>")
        emailTable.Rows.Add(3, "<EMAIL>=secret789", "<EMAIL>")

        ' تعيين مصدر بيانات GridControl1
        GridControl1.DataSource = emailTable

        ' تحديث GridControl1
        GridControl1.RefreshDataSource()
        GridView1.BestFitColumns()
    End Sub

    ''' <summary>
    ''' تهيئة GridControl1
    ''' </summary>
    Private Sub InitializeGridControl()
        ' إنشاء الأعمدة
        colID = New DevExpress.XtraGrid.Columns.GridColumn()
        colEmailWithPass = New DevExpress.XtraGrid.Columns.GridColumn()
        colEmailOnly = New DevExpress.XtraGrid.Columns.GridColumn()

        ' تعيين خصائص الأعمدة
        colID.Caption = "#"
        colID.FieldName = "ID"
        colID.Name = "colID"
        colID.Visible = True
        colID.VisibleIndex = 0
        colID.Width = 60

        colEmailWithPass.Caption = "Email with Password"
        colEmailWithPass.FieldName = "EmailWithPass"
        colEmailWithPass.Name = "colEmailWithPass"
        colEmailWithPass.Visible = True
        colEmailWithPass.VisibleIndex = 1
        colEmailWithPass.Width = 375

        colEmailOnly.Caption = "Email without Password"
        colEmailOnly.FieldName = "EmailOnly"
        colEmailOnly.Name = "colEmailOnly"
        colEmailOnly.Visible = True
        colEmailOnly.VisibleIndex = 2
        colEmailOnly.Width = 375

        ' إضافة الأعمدة إلى GridView1
        GridView1.Columns.Clear()
        GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {colID, colEmailWithPass, colEmailOnly})

        ' تعيين خصائص GridView1
        GridView1.OptionsBehavior.Editable = False
        GridView1.OptionsView.ShowGroupPanel = False
        GridView1.OptionsView.EnableAppearanceEvenRow = True
        GridView1.OptionsView.EnableAppearanceOddRow = True

        ' تعيين ألوان الصفوف
        GridView1.Appearance.EvenRow.BackColor = Color.FromArgb(30, 32, 38)
        GridView1.Appearance.OddRow.BackColor = Color.FromArgb(40, 42, 48)

        ' تعيين ألوان النص
        GridView1.Appearance.Row.ForeColor = Color.White
        GridView1.Appearance.HeaderPanel.ForeColor = Color.FromArgb(254, 219, 65)
        GridView1.Appearance.HeaderPanel.BackColor = Color.FromArgb(30, 32, 38)
        GridView1.Appearance.HeaderPanel.Font = New Font("Comfortaa", 9, FontStyle.Bold)
    End Sub

    ''' <summary>
    ''' تطبيق تصميم بينانس على GridControl1
    ''' </summary>
    Private Sub ApplyBinanceStyleToGrid()
        ' تطبيق تصميم بينانس على GridControl1
        GridControl1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
        GridControl1.LookAndFeel.UseDefaultLookAndFeel = False
        GridControl1.LookAndFeel.SkinName = "DevExpress Dark Style"

        ' تعيين خصائص الحدود
        GridControl1.LookAndFeel.UseWindowsXPTheme = False

        ' تطبيق تصميم بينانس على GridView1
        GridView1.Appearance.HeaderPanel.BackColor = Color.FromArgb(30, 32, 38)
        GridView1.Appearance.HeaderPanel.ForeColor = Color.FromArgb(254, 219, 65)
        GridView1.Appearance.HeaderPanel.Font = New Font("Comfortaa", 9, FontStyle.Bold)

        GridView1.Appearance.Row.BackColor = Color.FromArgb(30, 32, 38)
        GridView1.Appearance.Row.ForeColor = Color.White
        GridView1.Appearance.Row.Font = New Font("Comfortaa", 9, FontStyle.Regular)
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر Upload
    ''' </summary>
    Private Sub bntUploade_Click(sender As Object, e As EventArgs) Handles bntUploade.Click
        ' تهيئة OpenFileDialog
        OpenFileDialog1.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        OpenFileDialog1.Title = "Select a file"
        OpenFileDialog1.Filter = "Text Files|*.txt|All Files|*.*"

        ' عرض OpenFileDialog
        If OpenFileDialog1.ShowDialog() = DialogResult.OK Then
            ' إظهار ProgressPanel1
            ProgressPanel1.Visible = True
            ProgressPanel1.Description = "Loading file..."

            ' تعيين نوع العملية الحالية
            currentOperation = "LOAD"

            ' تشغيل BackgroundWorker1 لتحميل الملف
            If Not BackgroundWorker1.IsBusy Then
                BackgroundWorker1.RunWorkerAsync(OpenFileDialog1.FileName)
            End If
        End If
    End Sub



    ''' <summary>
    ''' معالج حدث ProgressChanged للـ BackgroundWorker1
    ''' </summary>
    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As System.ComponentModel.ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        ' تحديث وصف ProgressPanel1
        If currentOperation = "LOAD" Then
            ProgressPanel1.Description = $"Loading file... {e.ProgressPercentage}%"
        ElseIf currentOperation = "PROCESS" Then
            ProgressPanel1.Description = $"Processing emails... {e.ProgressPercentage}%"
        ElseIf currentOperation = "SAVE" Then
            ProgressPanel1.Description = $"Saving file... {e.ProgressPercentage}%"
        End If

        ' معالجة الدفعات من البيانات
        If e.UserState IsNot Nothing AndAlso TypeOf e.UserState Is String()() Then
            ' الحصول على الدفعة الحالية
            Dim batch As String()() = DirectCast(e.UserState, String()())

            ' طباعة معلومات للتصحيح
            Debug.WriteLine($"Processing batch of {batch.Length} emails")

            ' إضافة الدفعة إلى جدول البيانات
            For Each email As String() In batch
                emailTable.Rows.Add(email)
            Next

            ' تحديث GridControl1
            GridControl1.RefreshDataSource()
        End If
    End Sub



    ''' <summary>
    ''' معالج حدث النقر على زر Start
    ''' </summary>
    Private Sub bntStart_Click(sender As Object, e As EventArgs) Handles bntStart.Click
        ' التحقق من وجود بيانات
        If emailTable.Rows.Count = 0 Then
            XtraMessageBox.Show("Please upload a file first.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        ' إظهار ProgressPanel1
        ProgressPanel1.Visible = True
        ProgressPanel1.Description = "Processing emails..."

        ' تعيين نوع العملية الحالية
        currentOperation = "PROCESS"

        ' تشغيل BackgroundWorker1 لمعالجة البريد الإلكتروني
        If Not BackgroundWorker1.IsBusy Then
            BackgroundWorker1.RunWorkerAsync("PROCESS")
        End If
    End Sub

    ''' <summary>
    ''' معالج حدث DoWork للـ BackgroundWorker1
    ''' </summary>
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Try
            ' التحقق من نوع العملية
            If e.Argument.ToString() = "PROCESS" Then
                ' معالجة البريد الإلكتروني
                ProcessEmails(e)
            ElseIf TypeOf e.Argument Is String AndAlso (e.Argument.ToString().EndsWith(".txt") OrElse e.Argument.ToString().EndsWith(".xlsx")) Then
                ' حفظ الملف
                SaveFile(e)
            ElseIf TypeOf e.Argument Is String Then
                ' تحميل الملف
                LoadFile(e)
            End If
        Catch ex As Exception
            ' إرجاع الخطأ
            e.Result = ex
        End Try
    End Sub

    ''' <summary>
    ''' تحميل الملف
    ''' </summary>
    Private Sub LoadFile(e As System.ComponentModel.DoWorkEventArgs)
        Try
            Dim filePath As String = e.Argument.ToString()
            Dim emailList As New List(Of String())
            Dim id As Integer = 1
            Dim lineCount As Integer = 0
            Dim processedCount As Integer = 0

            ' حساب عدد الأسطر في الملف أولاً
            Using reader As New StreamReader(filePath)
                While Not reader.EndOfStream
                    reader.ReadLine()
                    lineCount += 1
                End While
            End Using

            ' معالجة الملف سطرًا بسطر لتوفير الذاكرة
            Using reader As New StreamReader(filePath)
                Dim line As String
                Dim currentLine As Integer = 0

                ' قراءة الملف سطرًا بسطر
                While Not reader.EndOfStream
                    line = reader.ReadLine()
                    currentLine += 1

                    ' تحديث تقدم العملية كل 1000 سطر
                    If currentLine Mod 1000 = 0 Then
                        BackgroundWorker1.ReportProgress(CInt((currentLine / lineCount) * 100))
                    End If

                    ' تجاهل الأسطر الفارغة
                    If String.IsNullOrWhiteSpace(line) Then Continue While

                    ' إضافة السطر إلى القائمة
                    emailList.Add({id.ToString(), line, ""})
                    id += 1
                    processedCount += 1

                    ' إذا وصلنا إلى 10000 سطر، نتوقف مؤقتًا لتحديث واجهة المستخدم
                    If emailList.Count >= 10000 Then
                        ' إرسال الدفعة الحالية
                        BackgroundWorker1.ReportProgress(CInt((currentLine / lineCount) * 100), emailList.ToArray())

                        ' إنشاء قائمة جديدة
                        emailList = New List(Of String())
                    End If
                End While
            End Using

            ' طباعة معلومات للتصحيح
            Debug.WriteLine($"Loaded {processedCount} emails from file")

            ' إرجاع النتيجة النهائية
            e.Result = emailList
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error loading file: {ex.Message}")

            ' إرجاع الخطأ
            e.Result = New Exception($"Error loading file: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالجة البريد الإلكتروني
    ''' </summary>
    Private Sub ProcessEmails(e As System.ComponentModel.DoWorkEventArgs)
        Try
            ' تهيئة متغيرات
            Dim processedEmails As New List(Of String())
            Dim uniqueEmails As New HashSet(Of String)(StringComparer.OrdinalIgnoreCase)
            removedPasswords = 0
            duplicateEmails = 0

            ' نسخ البيانات من جدول البيانات
            Dim rows As DataRow() = emailTable.Select()
            Dim totalRows As Integer = rows.Length

            ' طباعة معلومات للتصحيح
            Debug.WriteLine($"Processing {totalRows} emails")

            ' معالجة كل صف
            For i As Integer = 0 To totalRows - 1
                ' تحديث تقدم العملية كل 1000 سطر
                If i Mod 1000 = 0 Then
                    BackgroundWorker1.ReportProgress(CInt((i / totalRows) * 100))
                End If

                ' الحصول على البريد الإلكتروني مع كلمة المرور
                Dim emailWithPass As String = rows(i)("EmailWithPass").ToString()

                ' استخراج البريد الإلكتروني بدون كلمة المرور
                Dim emailOnly As String = ExtractEmail(emailWithPass)

                ' التحقق من وجود البريد الإلكتروني في القائمة
                If uniqueEmails.Contains(emailOnly) Then
                    duplicateEmails += 1
                    Continue For
                End If

                ' إضافة البريد الإلكتروني إلى القائمة
                uniqueEmails.Add(emailOnly)

                ' إضافة البريد الإلكتروني إلى قائمة البريد المعالج
                processedEmails.Add({(i + 1).ToString(), emailWithPass, emailOnly})

                ' زيادة عدد كلمات المرور المحذوفة
                If emailWithPass <> emailOnly Then
                    removedPasswords += 1
                End If

                ' إذا وصلنا إلى 10000 سطر، نتوقف مؤقتًا لتحديث واجهة المستخدم
                If processedEmails.Count >= 10000 Then
                    ' إرسال الدفعة الحالية
                    BackgroundWorker1.ReportProgress(CInt((i / totalRows) * 100), processedEmails.ToArray())

                    ' إنشاء قائمة جديدة
                    processedEmails = New List(Of String())
                End If
            Next

            ' طباعة معلومات للتصحيح
            Debug.WriteLine($"Processed {uniqueEmails.Count} unique emails, removed {removedPasswords} passwords, found {duplicateEmails} duplicates")

            ' إرجاع النتيجة
            e.Result = processedEmails
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error processing emails: {ex.Message}")

            ' إرجاع الخطأ
            e.Result = New Exception($"Error processing emails: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' استخراج البريد الإلكتروني من النص
    ''' </summary>
    Private Function ExtractEmail(emailWithPass As String) As String
        ' التحقق من وجود فاصل
        Dim separators() As Char = {":", "|", "=", "(", ")"}
        Dim firstSeparatorIndex As Integer = emailWithPass.IndexOfAny(separators)

        ' إذا وجد فاصل، استخراج البريد الإلكتروني
        If firstSeparatorIndex >= 0 Then
            Return emailWithPass.Substring(0, firstSeparatorIndex).Trim()
        End If

        ' إذا لم يوجد فاصل، التحقق من وجود سطر جديد
        If emailWithPass.Contains(Environment.NewLine) Then
            Return emailWithPass.Split(New String() {Environment.NewLine}, StringSplitOptions.RemoveEmptyEntries)(0).Trim()
        End If

        ' إذا لم يوجد فاصل أو سطر جديد، إرجاع النص كما هو
        Return emailWithPass.Trim()
    End Function

    ''' <summary>
    ''' معالج حدث RunWorkerCompleted للـ BackgroundWorker1
    ''' </summary>
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        ' إخفاء ProgressPanel1
        ProgressPanel1.Visible = False

        ' التحقق من وجود أخطاء
        If e.Error IsNot Nothing Then
            XtraMessageBox.Show("An error occurred: " & e.Error.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If

        ' التحقق من إلغاء العملية
        If e.Cancelled Then
            XtraMessageBox.Show("Operation was cancelled.", "Cancelled", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        ' التحقق من نوع النتيجة
        If TypeOf e.Result Is Exception Then
            ' عرض رسالة الخطأ
            Dim ex As Exception = DirectCast(e.Result, Exception)
            XtraMessageBox.Show("An error occurred: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        ElseIf TypeOf e.Result Is List(Of String()) Then
            ' معالجة نتيجة تحميل الملف أو معالجة البريد الإلكتروني
            Dim emailList As List(Of String()) = DirectCast(e.Result, List(Of String()))

            ' طباعة معلومات للتصحيح
            Debug.WriteLine($"Processing {emailList.Count} emails in RunWorkerCompleted")

            ' التحقق من أن القائمة ليست فارغة
            If emailList.Count > 0 Then
                ' مسح جدول البيانات
                emailTable.Rows.Clear()

                ' إضافة البيانات إلى جدول البيانات
                For Each email As String() In emailList
                    emailTable.Rows.Add(email)
                Next

                ' تحديث عدد البريد الإلكتروني
                totalEmails = emailList.Count

                ' تحديث GridControl1
                GridControl1.RefreshDataSource()
                GridView1.BestFitColumns()

                ' عرض معلومات إضافية إذا كانت العملية هي معالجة البريد الإلكتروني
                If currentOperation = "PROCESS" Then
                    ' تحديث وصف ProgressPanel1
                    ProgressPanel1.Caption = "Processing completed"
                    ProgressPanel1.Description = $"Total Emails: {totalEmails}" & Environment.NewLine &
                                                $"Removed Passwords: {removedPasswords}" & Environment.NewLine &
                                                $"Duplicate Emails: {duplicateEmails}"
                    ProgressPanel1.Visible = True
                ElseIf currentOperation = "LOAD" Then
                    ' عرض رسالة نجاح
                    ProgressPanel1.Caption = "Loading completed"
                    ProgressPanel1.Description = $"Total Emails Loaded: {totalEmails}"
                    ProgressPanel1.Visible = True

                    ' عرض رسالة للمستخدم
                    XtraMessageBox.Show($"Successfully loaded {totalEmails} emails from file.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
            Else
                ' عرض رسالة خطأ إذا كانت القائمة فارغة
                XtraMessageBox.Show("No emails found in the file.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If
        ElseIf TypeOf e.Result Is String AndAlso currentOperation = "SAVE" Then
            ' معالجة نتيجة حفظ الملف - فقط إذا كانت العملية هي حفظ الملف
            Dim folderPath As String = e.Result.ToString()

            ' عرض رسالة نجاح
            XtraMessageBox.Show("File saved successfully in 'Zero Pass' folder on your Desktop.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)

            ' فتح المجلد
            Process.Start("explorer.exe", folderPath)
        End If

        ' إعادة تعيين نوع العملية الحالية
        currentOperation = ""
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر Save
    ''' </summary>
    Private Sub bntSave_Click(sender As Object, e As EventArgs) Handles bntSave.Click
        ' التحقق من وجود بيانات
        If emailTable.Rows.Count = 0 Then
            XtraMessageBox.Show("Please process emails first.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        ' عرض SaveFileDialog
        SaveFileDialog1.Title = "Save File"
        SaveFileDialog1.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        SaveFileDialog1.FileName = "Zero Pass"
        SaveFileDialog1.Filter = "Text Files|*.txt|Excel Files|*.xlsx"

        If SaveFileDialog1.ShowDialog() = DialogResult.OK Then
            ' إظهار ProgressPanel1
            ProgressPanel1.Visible = True
            ProgressPanel1.Description = "Saving file..."

            ' تعيين نوع العملية الحالية
            currentOperation = "SAVE"

            ' تشغيل BackgroundWorker1 لحفظ الملف
            If Not BackgroundWorker1.IsBusy Then
                BackgroundWorker1.RunWorkerAsync(SaveFileDialog1.FileName)
            End If
        End If
    End Sub

    ''' <summary>
    ''' حفظ الملف
    ''' </summary>
    Private Sub SaveFile(e As System.ComponentModel.DoWorkEventArgs)
        Try
            ' الحصول على مسار الملف
            Dim filePath As String = e.Argument.ToString()

            ' إنشاء مجلد Zero Pass على سطح المكتب
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim folderPath As String = Path.Combine(desktopPath, "Zero Pass")

            If Not Directory.Exists(folderPath) Then
                Directory.CreateDirectory(folderPath)
            End If

            ' تحديد مسار الملف الوجهة
            Dim destFilePath As String = Path.Combine(folderPath, Path.GetFileName(filePath))

            ' التحقق من نوع الملف
            If Path.GetExtension(filePath).ToLower() = ".txt" Then
                ' حفظ الملف كنص
                Dim totalRows As Integer = emailTable.Rows.Count

                ' استخدام StreamWriter لتوفير الذاكرة
                Using writer As New StreamWriter(destFilePath)
                    ' استخراج البريد الإلكتروني من جدول البيانات
                    For i As Integer = 0 To totalRows - 1
                        ' تحديث تقدم العملية كل 1000 سطر
                        If i Mod 1000 = 0 Then
                            BackgroundWorker1.ReportProgress(CInt((i / totalRows) * 100))
                        End If

                        ' كتابة البريد الإلكتروني إلى الملف
                        writer.WriteLine(emailTable.Rows(i)("EmailOnly").ToString())
                    Next
                End Using
            ElseIf Path.GetExtension(filePath).ToLower() = ".xlsx" Then
                ' حفظ الملف كـ Excel
                GridControl1.ExportToXlsx(destFilePath)
            End If

            ' طباعة معلومات للتصحيح
            Debug.WriteLine($"Saved {emailTable.Rows.Count} emails to {destFilePath}")

            ' إرجاع مسار المجلد
            e.Result = folderPath
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error saving file: {ex.Message}")

            ' إرجاع الخطأ
            e.Result = New Exception($"Error saving file: {ex.Message}")
        End Try
    End Sub



    ''' <summary>
    ''' معالج حدث النقر على زر Reset All
    ''' </summary>
    Private Sub bntRestAll_Click(sender As Object, e As EventArgs) Handles bntRestAll.Click
        ' التحقق من وجود بيانات
        If emailTable.Rows.Count = 0 Then
            XtraMessageBox.Show("There is no data to reset.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        ' عرض رسالة تأكيد
        If XtraMessageBox.Show("Are you sure you want to reset all data?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            ' مسح جدول البيانات
            emailTable.Rows.Clear()

            ' إعادة تعيين المتغيرات
            totalEmails = 0
            removedPasswords = 0
            duplicateEmails = 0

            ' إخفاء ProgressPanel1
            ProgressPanel1.Visible = False
        End If
    End Sub

    ''' <summary>
    ''' معالج حدث Paint للـ MainPanel
    ''' </summary>
    Private Sub MainPanel_Paint(sender As Object, e As PaintEventArgs)
        ' رسم حدود منحنية للـ MainPanel
        Dim panel As Panel = DirectCast(sender, Panel)
        Dim radius As Integer = 20

        ' تعيين جودة الرسم
        e.Graphics.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
        e.Graphics.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
        e.Graphics.PixelOffsetMode = Drawing2D.PixelOffsetMode.HighQuality

        ' تعديل المستطيل ليكون داخل حدود اللوحة بشكل كامل
        ' نترك هامشًا كبيرًا (15 بكسل) لضمان ظهور الحدود بالكامل
        Dim rect As New Rectangle(15, 15, panel.Width - 30, panel.Height - 30)

        ' رسم مستطيل بحواف منحنية باللون الذهبي (254, 219, 65) بسمك 2 بكسل
        Using pen As New Pen(Color.FromArgb(254, 219, 65), 2)
            ' تعيين نهايات الخطوط لتكون دائرية
            pen.StartCap = Drawing2D.LineCap.Round
            pen.EndCap = Drawing2D.LineCap.Round
            pen.LineJoin = Drawing2D.LineJoin.Round
            pen.Alignment = Drawing2D.PenAlignment.Center

            ' رسم مستطيل بحواف منحنية
            e.Graphics.DrawPath(pen, GetRoundedRectPath(rect, radius))

            ' رسم مستطيل آخر بنفس الحجم لضمان ظهور الخط بشكل كامل
            ' هذه تقنية لتجنب مشكلة تقطيع الخطوط في GDI+
            e.Graphics.DrawPath(pen, GetRoundedRectPath(rect, radius))
        End Using
    End Sub

    ''' <summary>
    ''' إنشاء مسار لمستطيل بحواف منحنية
    ''' </summary>
    Private Function GetRoundedRectPath(rect As Rectangle, radius As Integer) As Drawing2D.GraphicsPath
        Dim path As New Drawing2D.GraphicsPath()

        ' تأكد من أن نصف القطر لا يتجاوز نصف عرض أو ارتفاع المستطيل
        radius = Math.Min(radius, Math.Min(rect.Width \ 2, rect.Height \ 2))

        ' تعديل المستطيل لضمان عدم تجاوز الحدود
        Dim adjustedRect As New Rectangle(
            rect.X,
            rect.Y,
            Math.Max(radius * 2, rect.Width),
            Math.Max(radius * 2, rect.Height)
        )

        ' إضافة الأقواس والخطوط للمسار بطريقة أكثر دقة
        ' الزاوية العلوية اليسرى
        path.AddArc(adjustedRect.X, adjustedRect.Y, radius * 2, radius * 2, 180, 90)

        ' الخط العلوي
        path.AddLine(
            adjustedRect.X + radius,
            adjustedRect.Y,
            adjustedRect.Right - radius,
            adjustedRect.Y
        )

        ' الزاوية العلوية اليمنى
        path.AddArc(
            adjustedRect.Right - radius * 2,
            adjustedRect.Y,
            radius * 2,
            radius * 2,
            270,
            90
        )

        ' الخط الأيمن
        path.AddLine(
            adjustedRect.Right,
            adjustedRect.Y + radius,
            adjustedRect.Right,
            adjustedRect.Bottom - radius
        )

        ' الزاوية السفلية اليمنى
        path.AddArc(
            adjustedRect.Right - radius * 2,
            adjustedRect.Bottom - radius * 2,
            radius * 2,
            radius * 2,
            0,
            90
        )

        ' الخط السفلي
        path.AddLine(
            adjustedRect.Right - radius,
            adjustedRect.Bottom,
            adjustedRect.X + radius,
            adjustedRect.Bottom
        )

        ' الزاوية السفلية اليسرى
        path.AddArc(
            adjustedRect.X,
            adjustedRect.Bottom - radius * 2,
            radius * 2,
            radius * 2,
            90,
            90
        )

        ' الخط الأيسر
        path.AddLine(
            adjustedRect.X,
            adjustedRect.Bottom - radius,
            adjustedRect.X,
            adjustedRect.Y + radius
        )

        ' إغلاق المسار
        path.CloseAllFigures()

        Return path
    End Function

    ''' <summary>
    ''' وضع MainPanel في المنتصف وجعلها منحنية من الأطراف
    ''' </summary>
    Private Sub CenterMainPanel()
        ' الحفاظ على الحجم الأصلي للـ MainPanel
        Dim originalWidth As Integer = 872 ' العرض الأصلي
        Dim originalHeight As Integer = 502 ' الارتفاع الأصلي

        ' تعيين حجم MainPanel إلى الحجم الأصلي
        MainPanel.Width = originalWidth
        MainPanel.Height = originalHeight

        ' حساب موقع MainPanel ليكون في المنتصف
        Dim x As Integer = (Me.ClientSize.Width - MainPanel.Width) \ 2
        Dim y As Integer = (Me.ClientSize.Height - MainPanel.Height) \ 2

        ' تعيين موقع MainPanel في المنتصف
        MainPanel.Location = New Point(Math.Max(0, x), Math.Max(0, y))

        ' جعل MainPanel منحنية من الأطراف
        ApplyRoundedCorners(MainPanel, 20)
    End Sub

    ''' <summary>
    ''' تطبيق حواف منحنية على لوحة
    ''' </summary>
    ''' <param name="panel">اللوحة المراد تطبيق الحواف المنحنية عليها</param>
    ''' <param name="radius">نصف قطر الانحناء</param>
    Private Sub ApplyRoundedCorners(panel As Panel, radius As Integer)
        ' تعديل المستطيل ليكون داخل حدود اللوحة بشكل كامل
        ' نترك هامشًا كبيرًا (12 بكسل) لضمان ظهور الحدود بالكامل
        Dim rect As New Rectangle(12, 12, panel.Width - 25, panel.Height - 25)

        ' إنشاء مسار منحني للحواف باستخدام الدالة المساعدة
        Dim path As Drawing2D.GraphicsPath = GetRoundedRectPath(rect, radius)

        ' تطبيق المسار على اللوحة
        panel.Region = New Region(path)

        ' تعيين خاصية BorderStyle إلى None لإخفاء الحدود الافتراضية
        panel.BorderStyle = BorderStyle.None
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmRemovePassword_Resize(sender As Object, e As EventArgs) Handles MyBase.Resize
        ' إعادة تعيين حجم وموقع MainPanel عند تغيير حجم النموذج
        CenterMainPanel()
    End Sub
End Class