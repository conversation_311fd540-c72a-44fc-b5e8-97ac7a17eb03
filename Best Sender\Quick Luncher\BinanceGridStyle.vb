Imports System.Drawing
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.Utils

''' <summary>
''' فئة مساعدة لتطبيق تصميم Binance على GridControl
''' </summary>
Public Class BinanceGridStyle
    ''' <summary>
    ''' ألوان تصميم Binance
    ''' </summary>
    Public Class BinanceColors
        ' ألوان الخلفية والنص
        Public Shared ReadOnly BackgroundColor As Color = Color.FromArgb(18, 22, 28)
        Public Shared ReadOnly TextColor As Color = Color.FromArgb(255, 255, 255)
        Public Shared ReadOnly AlternateRowColor As Color = Color.FromArgb(30, 35, 41)
        Public Shared ReadOnly HeaderTextColor As Color = Color.FromArgb(160, 174, 192)
        Public Shared ReadOnly HoveredRowColor As Color = Color.FromArgb(44, 47, 54)
        Public Shared ReadOnly SelectedRowColor As Color = Color.FromArgb(47, 52, 59)
        
        ' ألوان القيم
        Public Shared ReadOnly PositiveValueColor As Color = Color.FromArgb(0, 194, 146)
        Public Shared ReadOnly NegativeValueColor As Color = Color.FromArgb(255, 76, 76)
        Public Shared ReadOnly NeutralValueColor As Color = Color.FromArgb(255, 159, 67)
    End Class
    
    ''' <summary>
    ''' تطبيق تصميم Binance على GridControl
    ''' </summary>
    ''' <param name="gridControl">GridControl المراد تطبيق التصميم عليه</param>
    ''' <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
    Public Shared Sub ApplyBinanceStyle(gridControl As GridControl, Optional useWXITheme As Boolean = False)
        If gridControl Is Nothing Then Return
        
        ' تعيين إعدادات GridControl
        If useWXITheme Then
            ' استخدام ثيم WXI مع تعديلات Binance
            gridControl.LookAndFeel.UseDefaultLookAndFeel = True
            gridControl.LookAndFeel.SkinName = "WXI"
        Else
            ' تجاوز الثيم وتطبيق ألوان Binance مباشرة
            gridControl.LookAndFeel.UseDefaultLookAndFeel = False
            gridControl.BackColor = BinanceColors.BackgroundColor
            gridControl.ForeColor = BinanceColors.TextColor
        End If
        
        ' تطبيق التصميم على جميع GridViews
        For Each view As GridView In gridControl.Views
            ApplyBinanceStyleToGridView(view)
        Next
    End Sub
    
    ''' <summary>
    ''' تطبيق تصميم Binance على GridView
    ''' </summary>
    ''' <param name="gridView">GridView المراد تطبيق التصميم عليه</param>
    Public Shared Sub ApplyBinanceStyleToGridView(gridView As GridView)
        If gridView Is Nothing Then Return
        
        With gridView
            ' تعيين لون خلفية الجدول
            .Appearance.Empty.BackColor = BinanceColors.BackgroundColor
            .Appearance.Empty.Options.UseBackColor = True
            
            ' تعيين مظهر الصفوف
            .Appearance.Row.BackColor = BinanceColors.BackgroundColor
            .Appearance.Row.ForeColor = BinanceColors.TextColor
            .Appearance.Row.Options.UseBackColor = True
            .Appearance.Row.Options.UseForeColor = True
            
            ' تعيين مظهر الصفوف المتناوبة
            .OptionsView.EnableAppearanceEvenRow = True
            .Appearance.EvenRow.BackColor = BinanceColors.AlternateRowColor
            .Appearance.EvenRow.Options.UseBackColor = True
            
            ' تعيين مظهر رأس الأعمدة
            .Appearance.HeaderPanel.BackColor = BinanceColors.BackgroundColor
            .Appearance.HeaderPanel.ForeColor = BinanceColors.HeaderTextColor
            .Appearance.HeaderPanel.Font = New Font(.Appearance.HeaderPanel.Font, FontStyle.Bold)
            .Appearance.HeaderPanel.Options.UseBackColor = True
            .Appearance.HeaderPanel.Options.UseForeColor = True
            .Appearance.HeaderPanel.Options.UseFont = True
            .Appearance.HeaderPanel.TextOptions.HAlignment = HorzAlignment.Center
            .Appearance.HeaderPanel.Options.UseTextOptions = True
            
            ' تعيين مظهر الصف المحدد
            .Appearance.FocusedRow.BackColor = BinanceColors.SelectedRowColor
            .Appearance.FocusedRow.Options.UseBackColor = True
            
            ' تعيين مظهر الصف عند المرور عليه
            .Appearance.HideSelectionRow.BackColor = BinanceColors.HoveredRowColor
            .Appearance.HideSelectionRow.Options.UseBackColor = True
            
            ' إزالة خطوط الشبكة
            .OptionsView.ShowHorizontalLines = DefaultBoolean.False
            .OptionsView.ShowVerticalLines = DefaultBoolean.False
            
            ' تقليل ارتفاع الصفوف لعرض أكثر كثافة
            .RowHeight = 24
            
            ' تعيين الخط
            .Appearance.Row.Font = New Font("Segoe UI", 10)
            .Appearance.Row.Options.UseFont = True
            
            ' إضافة معالج حدث RowCellStyle لتطبيق ألوان مخصصة على القيم الموجبة والسالبة
            AddHandler gridView.RowCellStyle, AddressOf BinanceRowCellStyle
        End With
    End Sub
    
    ''' <summary>
    ''' معالج حدث تنسيق خلايا الصفوف لتطبيق ألوان مخصصة على القيم الموجبة والسالبة
    ''' </summary>
    Private Shared Sub BinanceRowCellStyle(sender As Object, e As RowCellStyleEventArgs)
        Dim view As GridView = TryCast(sender, GridView)
        If view Is Nothing Then Return
        
        ' تطبيق ألوان مخصصة على الأعمدة التي تحتوي على قيم رقمية
        If IsNumericColumn(view, e.Column) Then
            Try
                Dim value As Double = Convert.ToDouble(e.CellValue)
                If value > 0 Then
                    ' قيمة موجبة (ربح)
                    e.Appearance.ForeColor = BinanceColors.PositiveValueColor
                ElseIf value < 0 Then
                    ' قيمة سالبة (خسارة)
                    e.Appearance.ForeColor = BinanceColors.NegativeValueColor
                End If
            Catch ex As Exception
                ' تجاهل الأخطاء في التحويل
            End Try
        End If
        
        ' تطبيق ألوان مخصصة على أعمدة الحالة
        If IsStatusColumn(e.Column) Then
            Try
                Dim statusValue As String = e.CellValue.ToString().ToLower()
                Select Case statusValue
                    Case "valid", "success", "completed", "active", "working", "true", "yes", "1"
                        e.Appearance.ForeColor = BinanceColors.PositiveValueColor
                    Case "invalid", "fail", "failed", "error", "inactive", "false", "no", "0"
                        e.Appearance.ForeColor = BinanceColors.NegativeValueColor
                    Case "pending", "processing", "waiting", "neutral", "untested"
                        e.Appearance.ForeColor = BinanceColors.NeutralValueColor
                End Select
            Catch ex As Exception
                ' تجاهل الأخطاء
            End Try
        End If
    End Sub
    
    ''' <summary>
    ''' التحقق مما إذا كان العمود يحتوي على قيم رقمية
    ''' </summary>
    Private Shared Function IsNumericColumn(view As GridView, column As DevExpress.XtraGrid.Columns.GridColumn) As Boolean
        ' التحقق من اسم العمود
        Dim numericColumnNames() As String = {"price", "amount", "volume", "change", "percent", "value", "profit", "loss", "balance", "total", "qty", "quantity"}
        
        For Each name As String In numericColumnNames
            If column.FieldName.ToLower().Contains(name) Then
                Return True
            End If
        Next
        
        ' التحقق من نوع البيانات
        Try
            Dim dataType As Type = view.GetRowCellValue(0, column)?.GetType()
            If dataType IsNot Nothing Then
                Return (dataType Is GetType(Integer) OrElse
                        dataType Is GetType(Double) OrElse
                        dataType Is GetType(Decimal) OrElse
                        dataType Is GetType(Single) OrElse
                        dataType Is GetType(Long))
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
        
        Return False
    End Function
    
    ''' <summary>
    ''' التحقق مما إذا كان العمود يحتوي على قيم حالة
    ''' </summary>
    Private Shared Function IsStatusColumn(column As DevExpress.XtraGrid.Columns.GridColumn) As Boolean
        ' التحقق من اسم العمود
        Dim statusColumnNames() As String = {"status", "state", "condition", "result", "isvalid", "success", "active"}
        
        For Each name As String In statusColumnNames
            If column.FieldName.ToLower().Contains(name) Then
                Return True
            End If
        Next
        
        Return False
    End Function
End Class
