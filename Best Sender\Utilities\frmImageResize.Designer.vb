﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmImageResize
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmImageResize))
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.bntSave = New DevExpress.XtraEditors.SimpleButton()
        Me.txtHeight = New DevExpress.XtraEditors.TextEdit()
        Me.bntClear = New DevExpress.XtraEditors.SimpleButton()
        Me.txtWidth = New DevExpress.XtraEditors.TextEdit()
        Me.txtSourcePath = New DevExpress.XtraEditors.TextEdit()
        Me.PicLogo = New System.Windows.Forms.PictureBox()
        Me.bntBrowse = New DevExpress.XtraEditors.SimpleButton()
        Me.lblOriginalSize = New System.Windows.Forms.Label()
        Me.GroupBox3.SuspendLayout()
        CType(Me.txtHeight.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtWidth.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtSourcePath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PicLogo, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.Label7)
        Me.GroupBox3.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox3.ForeColor = System.Drawing.Color.Crimson
        Me.GroupBox3.Location = New System.Drawing.Point(70, 756)
        Me.GroupBox3.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Padding = New System.Windows.Forms.Padding(4)
        Me.GroupBox3.Size = New System.Drawing.Size(835, 64)
        Me.GroupBox3.TabIndex = 23
        Me.GroupBox3.TabStop = False
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.ForeColor = System.Drawing.Color.Crimson
        Me.Label7.Location = New System.Drawing.Point(201, 24)
        Me.Label7.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(318, 26)
        Me.Label7.TabIndex = 1
        Me.Label7.Text = "Good Size [ Width 190 ] x [ Height 40 ]"
        '
        'bntSave
        '
        Me.bntSave.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSave.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSave.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntSave.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSave.Appearance.Options.UseBackColor = True
        Me.bntSave.Appearance.Options.UseBorderColor = True
        Me.bntSave.Appearance.Options.UseFont = True
        Me.bntSave.Appearance.Options.UseForeColor = True
        Me.bntSave.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntSave.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntSave.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntSave.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntSave.AppearanceDisabled.Options.UseBackColor = True
        Me.bntSave.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntSave.AppearanceDisabled.Options.UseFont = True
        Me.bntSave.AppearanceDisabled.Options.UseForeColor = True
        Me.bntSave.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntSave.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSave.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntSave.AppearanceHovered.Options.UseBackColor = True
        Me.bntSave.AppearanceHovered.Options.UseBorderColor = True
        Me.bntSave.AppearanceHovered.Options.UseForeColor = True
        Me.bntSave.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSave.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSave.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntSave.AppearancePressed.Options.UseBackColor = True
        Me.bntSave.AppearancePressed.Options.UseBorderColor = True
        Me.bntSave.AppearancePressed.Options.UseForeColor = True
        Me.bntSave.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.bntSave.Location = New System.Drawing.Point(167, 160)
        Me.bntSave.Name = "bntSave"
        Me.bntSave.Size = New System.Drawing.Size(133, 38)
        Me.bntSave.TabIndex = 0
        Me.bntSave.Text = "Save"
        '
        'txtHeight
        '
        Me.txtHeight.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtHeight.EditValue = ""
        Me.txtHeight.Location = New System.Drawing.Point(307, 211)
        Me.txtHeight.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtHeight.Name = "txtHeight"
        Me.txtHeight.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtHeight.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtHeight.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtHeight.Properties.Appearance.Options.UseBackColor = True
        Me.txtHeight.Properties.Appearance.Options.UseFont = True
        Me.txtHeight.Properties.Appearance.Options.UseForeColor = True
        Me.txtHeight.Properties.ContextImageOptions.Image = CType(resources.GetObject("txtHeight.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txtHeight.Properties.NullValuePrompt = "Size Height"
        Me.txtHeight.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtHeight.Size = New System.Drawing.Size(150, 30)
        Me.txtHeight.TabIndex = 403
        '
        'bntClear
        '
        Me.bntClear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntClear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntClear.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntClear.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntClear.Appearance.Options.UseBackColor = True
        Me.bntClear.Appearance.Options.UseBorderColor = True
        Me.bntClear.Appearance.Options.UseFont = True
        Me.bntClear.Appearance.Options.UseForeColor = True
        Me.bntClear.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntClear.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntClear.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntClear.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntClear.AppearanceDisabled.Options.UseBackColor = True
        Me.bntClear.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntClear.AppearanceDisabled.Options.UseFont = True
        Me.bntClear.AppearanceDisabled.Options.UseForeColor = True
        Me.bntClear.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntClear.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntClear.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntClear.AppearanceHovered.Options.UseBackColor = True
        Me.bntClear.AppearanceHovered.Options.UseBorderColor = True
        Me.bntClear.AppearanceHovered.Options.UseForeColor = True
        Me.bntClear.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntClear.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntClear.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntClear.AppearancePressed.Options.UseBackColor = True
        Me.bntClear.AppearancePressed.Options.UseBorderColor = True
        Me.bntClear.AppearancePressed.Options.UseForeColor = True
        Me.bntClear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.bntClear.Location = New System.Drawing.Point(167, 206)
        Me.bntClear.Name = "bntClear"
        Me.bntClear.Size = New System.Drawing.Size(133, 38)
        Me.bntClear.TabIndex = 403
        Me.bntClear.Text = "Reset All"
        '
        'txtWidth
        '
        Me.txtWidth.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtWidth.EditValue = ""
        Me.txtWidth.Location = New System.Drawing.Point(307, 164)
        Me.txtWidth.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtWidth.Name = "txtWidth"
        Me.txtWidth.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtWidth.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtWidth.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtWidth.Properties.Appearance.Options.UseBackColor = True
        Me.txtWidth.Properties.Appearance.Options.UseFont = True
        Me.txtWidth.Properties.Appearance.Options.UseForeColor = True
        Me.txtWidth.Properties.ContextImageOptions.Image = CType(resources.GetObject("txtWidth.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txtWidth.Properties.NullValuePrompt = "Size Width "
        Me.txtWidth.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtWidth.Size = New System.Drawing.Size(150, 30)
        Me.txtWidth.TabIndex = 403
        '
        'txtSourcePath
        '
        Me.txtSourcePath.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtSourcePath.EditValue = ""
        Me.txtSourcePath.Location = New System.Drawing.Point(307, 113)
        Me.txtSourcePath.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtSourcePath.Name = "txtSourcePath"
        Me.txtSourcePath.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtSourcePath.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtSourcePath.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtSourcePath.Properties.Appearance.Options.UseBackColor = True
        Me.txtSourcePath.Properties.Appearance.Options.UseFont = True
        Me.txtSourcePath.Properties.Appearance.Options.UseForeColor = True
        Me.txtSourcePath.Properties.ContextImageOptions.Image = CType(resources.GetObject("txtSourcePath.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txtSourcePath.Properties.NullValuePrompt = "Please Select |JPG|PNG|GIF"
        Me.txtSourcePath.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtSourcePath.Size = New System.Drawing.Size(448, 44)
        Me.txtSourcePath.TabIndex = 403
        '
        'PicLogo
        '
        Me.PicLogo.Location = New System.Drawing.Point(465, 160)
        Me.PicLogo.Margin = New System.Windows.Forms.Padding(4)
        Me.PicLogo.Name = "PicLogo"
        Me.PicLogo.Size = New System.Drawing.Size(290, 278)
        Me.PicLogo.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PicLogo.TabIndex = 4
        Me.PicLogo.TabStop = False
        '
        'bntBrowse
        '
        Me.bntBrowse.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntBrowse.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntBrowse.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bntBrowse.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntBrowse.Appearance.Options.UseBackColor = True
        Me.bntBrowse.Appearance.Options.UseBorderColor = True
        Me.bntBrowse.Appearance.Options.UseFont = True
        Me.bntBrowse.Appearance.Options.UseForeColor = True
        Me.bntBrowse.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntBrowse.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntBrowse.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntBrowse.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntBrowse.AppearanceDisabled.Options.UseBackColor = True
        Me.bntBrowse.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntBrowse.AppearanceDisabled.Options.UseFont = True
        Me.bntBrowse.AppearanceDisabled.Options.UseForeColor = True
        Me.bntBrowse.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntBrowse.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntBrowse.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntBrowse.AppearanceHovered.Options.UseBackColor = True
        Me.bntBrowse.AppearanceHovered.Options.UseBorderColor = True
        Me.bntBrowse.AppearanceHovered.Options.UseForeColor = True
        Me.bntBrowse.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntBrowse.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntBrowse.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntBrowse.AppearancePressed.Options.UseBackColor = True
        Me.bntBrowse.AppearancePressed.Options.UseBorderColor = True
        Me.bntBrowse.AppearancePressed.Options.UseForeColor = True
        Me.bntBrowse.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.bntBrowse.Location = New System.Drawing.Point(167, 110)
        Me.bntBrowse.Name = "bntBrowse"
        Me.bntBrowse.Size = New System.Drawing.Size(133, 38)
        Me.bntBrowse.TabIndex = 403
        Me.bntBrowse.Text = "Browse"
        '
        'lblOriginalSize
        '
        Me.lblOriginalSize.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblOriginalSize.AutoSize = True
        Me.lblOriginalSize.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.lblOriginalSize.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.lblOriginalSize.Location = New System.Drawing.Point(461, 442)
        Me.lblOriginalSize.Name = "lblOriginalSize"
        Me.lblOriginalSize.Size = New System.Drawing.Size(0, 23)
        Me.lblOriginalSize.TabIndex = 404
        Me.lblOriginalSize.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'frmImageResize
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.lblOriginalSize)
        Me.Controls.Add(Me.bntBrowse)
        Me.Controls.Add(Me.PicLogo)
        Me.Controls.Add(Me.GroupBox3)
        Me.Controls.Add(Me.txtSourcePath)
        Me.Controls.Add(Me.bntSave)
        Me.Controls.Add(Me.txtWidth)
        Me.Controls.Add(Me.txtHeight)
        Me.Controls.Add(Me.bntClear)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.Sizable
        Me.IconOptions.Icon = CType(resources.GetObject("frmImageResize.IconOptions.Icon"), System.Drawing.Icon)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Logo_NewBestSender
        Me.IconOptions.ShowIcon = False
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmImageResize"
        Me.ShowInTaskbar = False
        Me.Text = "Image Resizer"
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        CType(Me.txtHeight.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtWidth.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtSourcePath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PicLogo, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents GroupBox3 As GroupBox
    Friend WithEvents Label7 As Label
    Friend WithEvents bntSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtHeight As DevExpress.XtraEditors.TextEdit
    Friend WithEvents bntClear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtWidth As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtSourcePath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PicLogo As PictureBox
    Friend WithEvents bntBrowse As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents lblOriginalSize As Label
End Class
