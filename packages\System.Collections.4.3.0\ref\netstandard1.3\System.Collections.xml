﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections</name>
  </assembly>
  <members>
    <member name="T:System.Collections.BitArray">
      <summary>Manages a compact array of bit values, which are represented as Booleans, where true indicates that the bit is on (1) and false indicates the bit is off (0).</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Boolean[])">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.BitArray" /> class that contains bit values copied from the specified array of Booleans.</summary>
      <param name="values">An array of Booleans to copy. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.BitArray" /> class that contains bit values copied from the specified array of bytes.</summary>
      <param name="bytes">An array of bytes containing the values to copy, where each byte represents eight consecutive bits. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="bytes" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Collections.BitArray)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.BitArray" /> class that contains bit values copied from the specified <see cref="T:System.Collections.BitArray" />.</summary>
      <param name="bits">The <see cref="T:System.Collections.BitArray" /> to copy. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bits" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.BitArray" /> class that can hold the specified number of bit values, which are initially set to false.</summary>
      <param name="length">The number of bit values in the new <see cref="T:System.Collections.BitArray" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.BitArray" /> class that can hold the specified number of bit values, which are initially set to the specified value.</summary>
      <param name="length">The number of bit values in the new <see cref="T:System.Collections.BitArray" />. </param>
      <param name="defaultValue">The Boolean value to assign to each bit. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32[])">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.BitArray" /> class that contains bit values copied from the specified array of 32-bit integers.</summary>
      <param name="values">An array of integers containing the values to copy, where each integer represents 32 consecutive bits. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="values" /> is greater than <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Collections.BitArray.And(System.Collections.BitArray)">
      <summary>Performs the bitwise AND operation on the elements in the current <see cref="T:System.Collections.BitArray" /> against the corresponding elements in the specified <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>The current instance containing the result of the bitwise AND operation on the elements in the current <see cref="T:System.Collections.BitArray" /> against the corresponding elements in the specified <see cref="T:System.Collections.BitArray" />.</returns>
      <param name="value">The <see cref="T:System.Collections.BitArray" /> with which to perform the bitwise AND operation. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Get(System.Int32)">
      <summary>Gets the value of the bit at a specific position in the <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>The value of the bit at position <paramref name="index" />.</returns>
      <param name="index">The zero-based index of the value to get. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the entire <see cref="T:System.Collections.BitArray" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Item(System.Int32)">
      <summary>Gets or sets the value of the bit at a specific position in the <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>The value of the bit at position <paramref name="index" />.</returns>
      <param name="index">The zero-based index of the value to get or set. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.BitArray.Count" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Length">
      <summary>Gets or sets the number of elements in the <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>The number of elements in the <see cref="T:System.Collections.BitArray" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is set to a value that is less than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Not">
      <summary>Inverts all the bit values in the current <see cref="T:System.Collections.BitArray" />, so that elements set to true are changed to false, and elements set to false are changed to true.</summary>
      <returns>The current instance with inverted bit values.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Or(System.Collections.BitArray)">
      <summary>Performs the bitwise OR operation on the elements in the current <see cref="T:System.Collections.BitArray" /> against the corresponding elements in the specified <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>The current instance containing the result of the bitwise OR operation on the elements in the current <see cref="T:System.Collections.BitArray" /> against the corresponding elements in the specified <see cref="T:System.Collections.BitArray" />.</returns>
      <param name="value">The <see cref="T:System.Collections.BitArray" /> with which to perform the bitwise OR operation. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Set(System.Int32,System.Boolean)">
      <summary>Sets the bit at a specific position in the <see cref="T:System.Collections.BitArray" /> to the specified value.</summary>
      <param name="index">The zero-based index of the bit to set. </param>
      <param name="value">The Boolean value to assign to the bit. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.SetAll(System.Boolean)">
      <summary>Sets all bits in the <see cref="T:System.Collections.BitArray" /> to the specified value.</summary>
      <param name="value">The Boolean value to assign to all bits. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.BitArray" /> to an <see cref="T:System.Array" />, starting at the specified <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.BitArray" />. The <see cref="T:System.Array" /> must have zero-based indexing. </param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or- The number of elements in the source <see cref="T:System.Collections.BitArray" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.BitArray" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#Count">
      <summary>Gets the number of elements in the <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>The number of elements in the <see cref="T:System.Collections.BitArray" />.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value that indicates whether access to the <see cref="T:System.Collections.BitArray" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.BitArray" /> is synchronized (thread safe); otherwise, false.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.BitArray" />.</returns>
    </member>
    <member name="M:System.Collections.BitArray.Xor(System.Collections.BitArray)">
      <summary>Performs the bitwise exclusive OR operation on the elements in the current <see cref="T:System.Collections.BitArray" /> against the corresponding elements in the specified <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>The current instance containing the result of the bitwise exclusive OR operation on the elements in the current <see cref="T:System.Collections.BitArray" /> against the corresponding elements in the specified <see cref="T:System.Collections.BitArray" />. </returns>
      <param name="value">The <see cref="T:System.Collections.BitArray" /> with which to perform the bitwise exclusive OR operation. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Collections.StructuralComparisons">
      <summary>Provides objects for performing a structural comparison of two collection objects.</summary>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralComparer">
      <summary>Gets a predefined object that performs a structural comparison of two objects.</summary>
      <returns>A predefined object that is used to perform a structural comparison of two collection objects.</returns>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralEqualityComparer">
      <summary>Gets a predefined object that compares two objects for structural equality.</summary>
      <returns>A predefined object that is used to compare two collection objects for structural equality.</returns>
    </member>
    <member name="T:System.Collections.Generic.Comparer`1">
      <summary>Provides a base class for implementations of the <see cref="T:System.Collections.Generic.IComparer`1" /> generic interface.</summary>
      <typeparam name="T">The type of objects to compare.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Comparer`1" /> class.</summary>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Compare(`0,`0)">
      <summary>When overridden in a derived class, performs a comparison of two objects of the same type and returns a value indicating whether one object is less than, equal to, or greater than the other.</summary>
      <returns>A signed integer that indicates the relative values of <paramref name="x" /> and <paramref name="y" />, as shown in the following table.Value Meaning Less than zero <paramref name="x" /> is less than <paramref name="y" />.Zero <paramref name="x" /> equals <paramref name="y" />.Greater than zero <paramref name="x" /> is greater than <paramref name="y" />.</returns>
      <param name="x">The first object to compare.</param>
      <param name="y">The second object to compare.</param>
      <exception cref="T:System.ArgumentException">Type <paramref name="T" /> does not implement either the <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface.</exception>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Create(System.Comparison{`0})">
      <summary>Creates a comparer by using the specified comparison.</summary>
      <returns>The new comparer.</returns>
      <param name="comparison">The comparison to use.</param>
    </member>
    <member name="P:System.Collections.Generic.Comparer`1.Default">
      <summary>Returns a default sort order comparer for the type specified by the generic argument.</summary>
      <returns>An object that inherits <see cref="T:System.Collections.Generic.Comparer`1" /> and serves as a sort order comparer for type <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Compares two objects and returns a value indicating whether one is less than, equal to, or greater than the other.</summary>
      <returns>A signed integer that indicates the relative values of <paramref name="x" /> and <paramref name="y" />, as shown in the following table.Value Meaning Less than zero<paramref name="x" /> is less than <paramref name="y" />.Zero<paramref name="x" /> equals <paramref name="y" />.Greater than zero<paramref name="x" /> is greater than <paramref name="y" />.</returns>
      <param name="x">The first object to compare.</param>
      <param name="y">The second object to compare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> or <paramref name="y" /> is of a type that cannot be cast to type <paramref name="T" />.-or-<paramref name="x" /> and <paramref name="y" /> do not implement either the <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface.</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2">
      <summary>Represents a collection of keys and values.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Dictionary`2" /> class that is empty, has the default initial capacity, and uses the default equality comparer for the key type.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Dictionary`2" /> class that contains elements copied from the specified <see cref="T:System.Collections.Generic.IDictionary`2" /> and uses the default equality comparer for the key type.</summary>
      <param name="dictionary">The <see cref="T:System.Collections.Generic.IDictionary`2" /> whose elements are copied to the new <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contains one or more duplicate keys.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Dictionary`2" /> class that contains elements copied from the specified <see cref="T:System.Collections.Generic.IDictionary`2" /> and uses the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="dictionary">The <see cref="T:System.Collections.Generic.IDictionary`2" /> whose elements are copied to the new <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.EqualityComparer`1" /> for the type of the key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contains one or more duplicate keys.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Dictionary`2" /> class that is empty, has the default initial capacity, and uses the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.EqualityComparer`1" /> for the type of the key.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Dictionary`2" /> class that is empty, has the specified initial capacity, and uses the default equality comparer for the key type.</summary>
      <param name="capacity">The initial number of elements that the <see cref="T:System.Collections.Generic.Dictionary`2" /> can contain.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Dictionary`2" /> class that is empty, has the specified initial capacity, and uses the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="capacity">The initial number of elements that the <see cref="T:System.Collections.Generic.Dictionary`2" /> can contain.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.EqualityComparer`1" /> for the type of the key.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Add(`0,`1)">
      <summary>Adds the specified key and value to the dictionary.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add. The value can be null for reference types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.ArgumentException">An element with the same key already exists in the <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Clear">
      <summary>Removes all keys and values from the <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Comparer">
      <summary>Gets the <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> that is used to determine equality of keys for the dictionary. </summary>
      <returns>The <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> generic interface implementation that is used to determine equality of keys for the current <see cref="T:System.Collections.Generic.Dictionary`2" /> and to provide hash values for the keys.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsKey(`0)">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.Dictionary`2" /> contains the specified key.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.Dictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">The key to locate in the <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsValue(`1)">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.Dictionary`2" /> contains a specific value.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.Dictionary`2" /> contains an element with the specified value; otherwise, false.</returns>
      <param name="value">The value to locate in the <see cref="T:System.Collections.Generic.Dictionary`2" />. The value can be null for reference types.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Count">
      <summary>Gets the number of key/value pairs contained in the <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>The number of key/value pairs contained in the <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> structure for the <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Item(`0)">
      <summary>Gets or sets the value associated with the specified key.</summary>
      <returns>The value associated with the specified key. If the specified key is not found, a get operation throws a <see cref="T:System.Collections.Generic.KeyNotFoundException" />, and a set operation creates a new element with the specified key.</returns>
      <param name="key">The key of the value to get or set.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">The property is retrieved and <paramref name="key" /> does not exist in the collection.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Keys">
      <summary>Gets a collection containing the keys in the <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> containing the keys in the <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Remove(`0)">
      <summary>Removes the value with the specified key from the <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>true if the element is successfully found and removed; otherwise, false.  This method returns false if <paramref name="key" /> is not found in the <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
      <param name="key">The key of the element to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Adds the specified value to the <see cref="T:System.Collections.Generic.ICollection`1" /> with the specified key.</summary>
      <param name="keyValuePair">The <see cref="T:System.Collections.Generic.KeyValuePair`2" /> structure representing the key and value to add to the <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">The key of <paramref name="keyValuePair" /> is null.</exception>
      <exception cref="T:System.ArgumentException">An element with the same key already exists in the <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specific key and value.</summary>
      <returns>true if <paramref name="keyValuePair" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="keyValuePair">The <see cref="T:System.Collections.Generic.KeyValuePair`2" /> structure to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.Generic.ICollection`1" /> to an array of type <see cref="T:System.Collections.Generic.KeyValuePair`2" />, starting at the specified array index.</summary>
      <param name="array">The one-dimensional array of type <see cref="T:System.Collections.Generic.KeyValuePair`2" /> that is the destination of the <see cref="T:System.Collections.Generic.KeyValuePair`2" /> elements copied from the <see cref="T:System.Collections.Generic.ICollection`1" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.ICollection`1" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether the dictionary is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.Dictionary`2" />, this property always returns false.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Removes a key and value from the dictionary.</summary>
      <returns>true if the key and value represented by <paramref name="keyValuePair" /> is successfully found and removed; otherwise, false. This method returns false if <paramref name="keyValuePair" /> is not found in the <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">The <see cref="T:System.Collections.Generic.KeyValuePair`2" /> structure representing the key and value to remove from the <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> of type <paramref name="TKey" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> of type <paramref name="TValue" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Gets a collection containing the keys of the <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</summary>
      <returns>A collection containing the keys of the <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Gets a collection containing the values of the <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</summary>
      <returns>A collection containing the values of the <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.Generic.ICollection`1" /> to an array, starting at the specified array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from <see cref="T:System.Collections.Generic.ICollection`1" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.Generic.ICollection`1" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.Generic.ICollection`1" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.Dictionary`2" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />. </returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Adds the specified key and value to the dictionary.</summary>
      <param name="key">The object to use as the key.</param>
      <param name="value">The object to use as the value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.Dictionary`2" />.-or-<paramref name="value" /> is of a type that is not assignable to <paramref name="TValue" />, the type of values in the <see cref="T:System.Collections.Generic.Dictionary`2" />.-or-A value with the same key already exists in the <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determines whether the <see cref="T:System.Collections.IDictionary" /> contains an element with the specified key.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">The key to locate in the <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IDictionaryEnumerator" /> for the <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>An <see cref="T:System.Collections.IDictionaryEnumerator" /> for the <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IDictionary" /> has a fixed size.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> has a fixed size; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.Dictionary`2" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IDictionary" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.Dictionary`2" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Gets or sets the value with the specified key.</summary>
      <returns>The value associated with the specified key, or null if <paramref name="key" /> is not in the dictionary or <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
      <param name="key">The key of the value to get.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.ArgumentException">A value is being assigned, and <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.Dictionary`2" />.-or-A value is being assigned, and <paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Keys">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> containing the keys of the <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> containing the keys of the <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Removes the element with the specified key from the <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">The key of the element to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Values">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> containing the values in the <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> containing the values in the <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.TryGetValue(`0,`1@)">
      <summary>Gets the value associated with the specified key.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.Dictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">The key of the value to get.</param>
      <param name="value">When this method returns, contains the value associated with the specified key, if the key is found; otherwise, the default value for the type of the <paramref name="value" /> parameter. This parameter is passed uninitialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Values">
      <summary>Gets a collection containing the values in the <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> containing the values in the <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.Dictionary`2" /> at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the dictionary at the current position of the enumerator, as a <see cref="T:System.Collections.DictionaryEntry" />.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Gets the key of the element at the current position of the enumerator.</summary>
      <returns>The key of the element in the dictionary at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Gets the value of the element at the current position of the enumerator.</summary>
      <returns>The value of the element in the dictionary at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator, as an <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection">
      <summary>Represents the collection of keys in a <see cref="T:System.Collections.Generic.Dictionary`2" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> class that reflects the keys in the specified <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <param name="dictionary">The <see cref="T:System.Collections.Generic.Dictionary`2" /> whose keys are reflected in the new <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Copies the <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> elements to an existing one-dimensional <see cref="T:System.Array" />, starting at the specified array index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.Retrieving the value of this property is an O(1) operation.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" /> for the <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Adds an item to the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Always thrown.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Always thrown.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specific value.</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, this property always returns true.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true if <paramref name="item" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false. This method also returns false if <paramref name="item" /> was not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">The object to remove from the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Always thrown.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.ICollection" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.  In the default implementation of <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, this property always returns the current instance.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection">
      <summary>Represents the collection of values in a <see cref="T:System.Collections.Generic.Dictionary`2" />. This class cannot be inherited. </summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> class that reflects the values in the specified <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <param name="dictionary">The <see cref="T:System.Collections.Generic.Dictionary`2" /> whose values are reflected in the new <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Copies the <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> elements to an existing one-dimensional <see cref="T:System.Array" />, starting at the specified array index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" /> for the <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Adds an item to the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Always thrown.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Always thrown.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specific value.</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, this property always returns true.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.ICollection`1" />. This implementation always throws <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true if <paramref name="item" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false. This method also returns false if <paramref name="item" /> was not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">The object to remove from the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Always thrown.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.ICollection" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.  In the default implementation of <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, this property always returns the current instance.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="T:System.Collections.Generic.EqualityComparer`1">
      <summary>Provides a base class for implementations of the <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> generic interface.</summary>
      <typeparam name="T">The type of objects to compare.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.EqualityComparer`1" /> class.</summary>
    </member>
    <member name="P:System.Collections.Generic.EqualityComparer`1.Default">
      <summary>Returns a default equality comparer for the type specified by the generic argument.</summary>
      <returns>The default instance of the <see cref="T:System.Collections.Generic.EqualityComparer`1" /> class for type <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.Equals(`0,`0)">
      <summary>When overridden in a derived class, determines whether two objects of type <paramref name="T" /> are equal.</summary>
      <returns>true if the specified objects are equal; otherwise, false.</returns>
      <param name="x">The first object to compare.</param>
      <param name="y">The second object to compare.</param>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.GetHashCode(`0)">
      <summary>When overridden in a derived class, serves as a hash function for the specified object for hashing algorithms and data structures, such as a hash table.</summary>
      <returns>A hash code for the specified object.</returns>
      <param name="obj">The object for which to get a hash code.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Determines whether the specified objects are equal.</summary>
      <returns>true if the specified objects are equal; otherwise, false.</returns>
      <param name="x">The first object to compare.</param>
      <param name="y">The second object to compare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> or <paramref name="y" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Returns a hash code for the specified object.</summary>
      <returns>A hash code for the specified object.</returns>
      <param name="obj">The <see cref="T:System.Object" /> for which a hash code is to be returned.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.-or-<paramref name="obj" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1">
      <summary>Represents a set of values.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <typeparam name="T">The type of elements in the hash set.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.HashSet`1" /> class that is empty and uses the default equality comparer for the set type.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.HashSet`1" /> class that uses the default equality comparer for the set type, contains elements copied from the specified collection, and has sufficient capacity to accommodate the number of elements copied.</summary>
      <param name="collection">The collection whose elements are copied to the new set.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.HashSet`1" /> class that uses the specified equality comparer for the set type, contains elements copied from the specified collection, and has sufficient capacity to accommodate the number of elements copied.</summary>
      <param name="collection">The collection whose elements are copied to the new set.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> implementation to use when comparing values in the set, or null to use the default <see cref="T:System.Collections.Generic.EqualityComparer`1" /> implementation for the set type.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.HashSet`1" /> class that is empty and uses the specified equality comparer for the set type.</summary>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> implementation to use when comparing values in the set, or null to use the default <see cref="T:System.Collections.Generic.EqualityComparer`1" /> implementation for the set type.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Add(`0)">
      <summary>Adds the specified element to a set.</summary>
      <returns>true if the element is added to the <see cref="T:System.Collections.Generic.HashSet`1" /> object; false if the element is already present.</returns>
      <param name="item">The element to add to the set.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Clear">
      <summary>Removes all elements from a <see cref="T:System.Collections.Generic.HashSet`1" /> object.</summary>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Comparer">
      <summary>Gets the <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> object that is used to determine equality for the values in the set.</summary>
      <returns>The <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> object that is used to determine equality for the values in the set.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Contains(`0)">
      <summary>Determines whether a <see cref="T:System.Collections.Generic.HashSet`1" /> object contains the specified element.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.HashSet`1" /> object contains the specified element; otherwise, false.</returns>
      <param name="item">The element to locate in the <see cref="T:System.Collections.Generic.HashSet`1" /> object.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[])">
      <summary>Copies the elements of a <see cref="T:System.Collections.Generic.HashSet`1" /> object to an array.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.Generic.HashSet`1" /> object. The array must have zero-based indexing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32)">
      <summary>Copies the elements of a <see cref="T:System.Collections.Generic.HashSet`1" /> object to an array, starting at the specified array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.Generic.HashSet`1" /> object. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> is greater than the length of the destination <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Copies the specified number of elements of a <see cref="T:System.Collections.Generic.HashSet`1" /> object to an array, starting at the specified array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.Generic.HashSet`1" /> object. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <param name="count">The number of elements to copy to <paramref name="array" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than 0.-or-<paramref name="count" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> is greater than the length of the destination <paramref name="array" />.-or-<paramref name="count" /> is greater than the available space from the <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Count">
      <summary>Gets the number of elements that are contained in a set.</summary>
      <returns>The number of elements that are contained in the set.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes all elements in the specified collection from the current <see cref="T:System.Collections.Generic.HashSet`1" /> object.</summary>
      <param name="other">The collection of items to remove from the <see cref="T:System.Collections.Generic.HashSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through a <see cref="T:System.Collections.Generic.HashSet`1" /> object.</summary>
      <returns>A <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> object for the <see cref="T:System.Collections.Generic.HashSet`1" /> object.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current <see cref="T:System.Collections.Generic.HashSet`1" /> object to contain only elements that are present in that object and in the specified collection.</summary>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.HashSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether a <see cref="T:System.Collections.Generic.HashSet`1" /> object is a proper subset of the specified collection.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.HashSet`1" /> object is a proper subset of <paramref name="other" />; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.HashSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether a <see cref="T:System.Collections.Generic.HashSet`1" /> object is a proper superset of the specified collection.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.HashSet`1" /> object is a proper superset of <paramref name="other" />; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.HashSet`1" /> object. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether a <see cref="T:System.Collections.Generic.HashSet`1" /> object is a subset of the specified collection.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.HashSet`1" /> object is a subset of <paramref name="other" />; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.HashSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether a <see cref="T:System.Collections.Generic.HashSet`1" /> object is a superset of the specified collection.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.HashSet`1" /> object is a superset of <paramref name="other" />; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.HashSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current <see cref="T:System.Collections.Generic.HashSet`1" /> object and a specified collection share common elements.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.HashSet`1" /> object and <paramref name="other" /> share at least one common element; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.HashSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Remove(`0)">
      <summary>Removes the specified element from a <see cref="T:System.Collections.Generic.HashSet`1" /> object.</summary>
      <returns>true if the element is successfully found and removed; otherwise, false.  This method returns false if <paramref name="item" /> is not found in the <see cref="T:System.Collections.Generic.HashSet`1" /> object.</returns>
      <param name="item">The element to remove.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Removes all elements that match the conditions defined by the specified predicate from a <see cref="T:System.Collections.Generic.HashSet`1" /> collection.</summary>
      <returns>The number of elements that were removed from the <see cref="T:System.Collections.Generic.HashSet`1" /> collection.</returns>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the elements to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether a <see cref="T:System.Collections.Generic.HashSet`1" /> object and the specified collection contain the same elements.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.HashSet`1" /> object is equal to <paramref name="other" />; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.HashSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current <see cref="T:System.Collections.Generic.HashSet`1" /> object to contain only elements that are present either in that object or in the specified collection, but not both.</summary>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.HashSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Adds an item to an <see cref="T:System.Collections.Generic.ICollection`1" /> object.</summary>
      <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1" /> object.</param>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether a collection is read-only.</summary>
      <returns>true if the collection is read-only; otherwise, false.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.TrimExcess">
      <summary>Sets the capacity of a <see cref="T:System.Collections.Generic.HashSet`1" /> object to the actual number of elements it contains, rounded up to a nearby, implementation-specific value.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current <see cref="T:System.Collections.Generic.HashSet`1" /> object to contain all elements that are present in itself, the specified collection, or both.</summary>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.HashSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.HashSet`1" /> object.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.HashSet`1" /> collection at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.Dispose">
      <summary>Releases all resources used by a <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> object.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.HashSet`1" /> collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator, as an <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1">
      <summary>Represents a doubly linked list.</summary>
      <typeparam name="T">Specifies the element type of the linked list.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.LinkedList`1" /> class that is empty.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.LinkedList`1" /> class that contains elements copied from the specified <see cref="T:System.Collections.IEnumerable" /> and has sufficient capacity to accommodate the number of elements copied. </summary>
      <param name="collection">The <see cref="T:System.Collections.IEnumerable" /> whose elements are copied to the new <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Adds the specified new node after the specified existing node in the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">The <see cref="T:System.Collections.Generic.LinkedListNode`1" /> after which to insert <paramref name="newNode" />.</param>
      <param name="newNode">The new <see cref="T:System.Collections.Generic.LinkedListNode`1" /> to add to the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> is null.-or-<paramref name="newNode" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> is not in the current <see cref="T:System.Collections.Generic.LinkedList`1" />.-or-<paramref name="newNode" /> belongs to another <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Adds a new node containing the specified value after the specified existing node in the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>The new <see cref="T:System.Collections.Generic.LinkedListNode`1" /> containing <paramref name="value" />.</returns>
      <param name="node">The <see cref="T:System.Collections.Generic.LinkedListNode`1" /> after which to insert a new <see cref="T:System.Collections.Generic.LinkedListNode`1" /> containing <paramref name="value" />.</param>
      <param name="value">The value to add to the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> is not in the current <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Adds the specified new node before the specified existing node in the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">The <see cref="T:System.Collections.Generic.LinkedListNode`1" /> before which to insert <paramref name="newNode" />.</param>
      <param name="newNode">The new <see cref="T:System.Collections.Generic.LinkedListNode`1" /> to add to the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> is null.-or-<paramref name="newNode" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> is not in the current <see cref="T:System.Collections.Generic.LinkedList`1" />.-or-<paramref name="newNode" /> belongs to another <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Adds a new node containing the specified value before the specified existing node in the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>The new <see cref="T:System.Collections.Generic.LinkedListNode`1" /> containing <paramref name="value" />.</returns>
      <param name="node">The <see cref="T:System.Collections.Generic.LinkedListNode`1" /> before which to insert a new <see cref="T:System.Collections.Generic.LinkedListNode`1" /> containing <paramref name="value" />.</param>
      <param name="value">The value to add to the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> is not in the current <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Adds the specified new node at the start of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">The new <see cref="T:System.Collections.Generic.LinkedListNode`1" /> to add at the start of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> belongs to another <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(`0)">
      <summary>Adds a new node containing the specified value at the start of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>The new <see cref="T:System.Collections.Generic.LinkedListNode`1" /> containing <paramref name="value" />.</returns>
      <param name="value">The value to add at the start of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Adds the specified new node at the end of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">The new <see cref="T:System.Collections.Generic.LinkedListNode`1" /> to add at the end of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> belongs to another <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(`0)">
      <summary>Adds a new node containing the specified value at the end of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>The new <see cref="T:System.Collections.Generic.LinkedListNode`1" /> containing <paramref name="value" />.</returns>
      <param name="value">The value to add at the end of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Clear">
      <summary>Removes all nodes from the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Contains(`0)">
      <summary>Determines whether a value is in the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true if <paramref name="value" /> is found in the <see cref="T:System.Collections.Generic.LinkedList`1" />; otherwise, false.</returns>
      <param name="value">The value to locate in the <see cref="T:System.Collections.Generic.LinkedList`1" />. The value can be null for reference types.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.CopyTo(`0[],System.Int32)">
      <summary>Copies the entire <see cref="T:System.Collections.Generic.LinkedList`1" /> to a compatible one-dimensional <see cref="T:System.Array" />, starting at the specified index of the target array.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.LinkedList`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.LinkedList`1" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Count">
      <summary>Gets the number of nodes actually contained in the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>The number of nodes actually contained in the <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Find(`0)">
      <summary>Finds the first node that contains the specified value.</summary>
      <returns>The first <see cref="T:System.Collections.Generic.LinkedListNode`1" /> that contains the specified value, if found; otherwise, null.</returns>
      <param name="value">The value to locate in the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.FindLast(`0)">
      <summary>Finds the last node that contains the specified value.</summary>
      <returns>The last <see cref="T:System.Collections.Generic.LinkedListNode`1" /> that contains the specified value, if found; otherwise, null.</returns>
      <param name="value">The value to locate in the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.First">
      <summary>Gets the first node of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>The first <see cref="T:System.Collections.Generic.LinkedListNode`1" /> of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" /> for the <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Last">
      <summary>Gets the last node of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>The last <see cref="T:System.Collections.Generic.LinkedListNode`1" /> of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Removes the specified node from the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">The <see cref="T:System.Collections.Generic.LinkedListNode`1" /> to remove from the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> is not in the current <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(`0)">
      <summary>Removes the first occurrence of the specified value from the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true if the element containing <paramref name="value" /> is successfully removed; otherwise, false.  This method also returns false if <paramref name="value" /> was not found in the original <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
      <param name="value">The value to remove from the <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveFirst">
      <summary>Removes the node at the start of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.LinkedList`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveLast">
      <summary>Removes the node at the end of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.LinkedList`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Adds an item at the end of the <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="value">The value to add at the end of the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.LinkedList`1" />, this property always returns false.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.ICollection" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.LinkedList`1" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.  In the default implementation of <see cref="T:System.Collections.Generic.LinkedList`1" />, this property always returns the current instance.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the linked list as a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the linked list as a collection.</returns>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.LinkedList`1" /> at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection. This class cannot be inherited.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedListNode`1">
      <summary>Represents a node in a <see cref="T:System.Collections.Generic.LinkedList`1" />. This class cannot be inherited.</summary>
      <typeparam name="T">Specifies the element type of the linked list.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedListNode`1.#ctor(`0)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.LinkedListNode`1" /> class, containing the specified value.</summary>
      <param name="value">The value to contain in the <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.List">
      <summary>Gets the <see cref="T:System.Collections.Generic.LinkedList`1" /> that the <see cref="T:System.Collections.Generic.LinkedListNode`1" /> belongs to.</summary>
      <returns>A reference to the <see cref="T:System.Collections.Generic.LinkedList`1" /> that the <see cref="T:System.Collections.Generic.LinkedListNode`1" /> belongs to, or null if the <see cref="T:System.Collections.Generic.LinkedListNode`1" /> is not linked.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Next">
      <summary>Gets the next node in the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>A reference to the next node in the <see cref="T:System.Collections.Generic.LinkedList`1" />, or null if the current node is the last element (<see cref="P:System.Collections.Generic.LinkedList`1.Last" />) of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Previous">
      <summary>Gets the previous node in the <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>A reference to the previous node in the <see cref="T:System.Collections.Generic.LinkedList`1" />, or null if the current node is the first element (<see cref="P:System.Collections.Generic.LinkedList`1.First" />) of the <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Value">
      <summary>Gets the value contained in the node.</summary>
      <returns>The value contained in the node.</returns>
    </member>
    <member name="T:System.Collections.Generic.List`1">
      <summary>Represents a strongly typed list of objects that can be accessed by index. Provides methods to search, sort, and manipulate lists.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <typeparam name="T">The type of elements in the list.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.List`1" /> class that is empty and has the default initial capacity.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.List`1" /> class that contains elements copied from the specified collection and has sufficient capacity to accommodate the number of elements copied.</summary>
      <param name="collection">The collection whose elements are copied to the new list.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.List`1" /> class that is empty and has the specified initial capacity.</summary>
      <param name="capacity">The number of elements that the new list can initially store.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than 0. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Add(`0)">
      <summary>Adds an object to the end of the <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="item">The object to be added to the end of the <see cref="T:System.Collections.Generic.List`1" />. The value can be null for reference types.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Adds the elements of the specified collection to the end of the <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="collection">The collection whose elements should be added to the end of the <see cref="T:System.Collections.Generic.List`1" />. The collection itself cannot be null, but it can contain elements that are null, if type <paramref name="T" /> is a reference type.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.AsReadOnly">
      <summary>Returns a read-only <see cref="T:System.Collections.Generic.IList`1" /> wrapper for the current collection.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> that acts as a read-only wrapper around the current <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>Searches a range of elements in the sorted <see cref="T:System.Collections.Generic.List`1" /> for an element using the specified comparer and returns the zero-based index of the element.</summary>
      <returns>The zero-based index of <paramref name="item" /> in the sorted <see cref="T:System.Collections.Generic.List`1" />, if <paramref name="item" /> is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="item" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="index">The zero-based starting index of the range to search.</param>
      <param name="count">The length of the range to search.</param>
      <param name="item">The object to locate. The value can be null for reference types.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing elements, or null to use the default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.-or-<paramref name="count" /> is less than 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> and <paramref name="count" /> do not denote a valid range in the <see cref="T:System.Collections.Generic.List`1" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> is null, and the default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" /> cannot find an implementation of the <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface for type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0)">
      <summary>Searches the entire sorted <see cref="T:System.Collections.Generic.List`1" /> for an element using the default comparer and returns the zero-based index of the element.</summary>
      <returns>The zero-based index of <paramref name="item" /> in the sorted <see cref="T:System.Collections.Generic.List`1" />, if <paramref name="item" /> is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="item" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">The object to locate. The value can be null for reference types.</param>
      <exception cref="T:System.InvalidOperationException">The default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" /> cannot find an implementation of the <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface for type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>Searches the entire sorted <see cref="T:System.Collections.Generic.List`1" /> for an element using the specified comparer and returns the zero-based index of the element.</summary>
      <returns>The zero-based index of <paramref name="item" /> in the sorted <see cref="T:System.Collections.Generic.List`1" />, if <paramref name="item" /> is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="item" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">The object to locate. The value can be null for reference types.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing elements.-or-null to use the default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> is null, and the default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" /> cannot find an implementation of the <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface for type <paramref name="T" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Capacity">
      <summary>Gets or sets the total number of elements the internal data structure can hold without resizing.</summary>
      <returns>The number of elements that the <see cref="T:System.Collections.Generic.List`1" /> can contain before resizing is required.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.List`1.Capacity" /> is set to a value that is less than <see cref="P:System.Collections.Generic.List`1.Count" />. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available on the system.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Clear">
      <summary>Removes all elements from the <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Contains(`0)">
      <summary>Determines whether an element is in the <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.List`1" />; otherwise, false.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.List`1" />. The value can be null for reference types.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>Copies a range of elements from the <see cref="T:System.Collections.Generic.List`1" /> to a compatible one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="index">The zero-based index in the source <see cref="T:System.Collections.Generic.List`1" /> at which copying begins.</param>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <param name="count">The number of elements to copy.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.-or-<paramref name="arrayIndex" /> is less than 0.-or-<paramref name="count" /> is less than 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> is equal to or greater than the <see cref="P:System.Collections.Generic.List`1.Count" /> of the source <see cref="T:System.Collections.Generic.List`1" />.-or-The number of elements from <paramref name="index" /> to the end of the source <see cref="T:System.Collections.Generic.List`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[])">
      <summary>Copies the entire <see cref="T:System.Collections.Generic.List`1" /> to a compatible one-dimensional array, starting at the beginning of the target array.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.List`1" /> is greater than the number of elements that the destination <paramref name="array" /> can contain.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[],System.Int32)">
      <summary>Copies the entire <see cref="T:System.Collections.Generic.List`1" /> to a compatible one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.List`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Exists(System.Predicate{`0})">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.List`1" /> contains elements that match the conditions defined by the specified predicate.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.List`1" /> contains one or more elements that match the conditions defined by the specified predicate; otherwise, false.</returns>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the elements to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Find(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the first occurrence within the entire <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>The first element that matches the conditions defined by the specified predicate, if found; otherwise, the default value for type <paramref name="T" />.</returns>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the element to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindAll(System.Predicate{`0})">
      <summary>Retrieves all the elements that match the conditions defined by the specified predicate.</summary>
      <returns>A <see cref="T:System.Collections.Generic.List`1" /> containing all the elements that match the conditions defined by the specified predicate, if found; otherwise, an empty <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the elements to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the first occurrence within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that starts at the specified index and contains the specified number of elements.</summary>
      <returns>The zero-based index of the first occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, –1.</returns>
      <param name="startIndex">The zero-based starting index of the search.</param>
      <param name="count">The number of elements in the section to search.</param>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the element to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is outside the range of valid indexes for the <see cref="T:System.Collections.Generic.List`1" />.-or-<paramref name="count" /> is less than 0.-or-<paramref name="startIndex" /> and <paramref name="count" /> do not specify a valid section in the <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the first occurrence within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that extends from the specified index to the last element.</summary>
      <returns>The zero-based index of the first occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, –1.</returns>
      <param name="startIndex">The zero-based starting index of the search.</param>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the element to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is outside the range of valid indexes for the <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the first occurrence within the entire <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>The zero-based index of the first occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, –1.</returns>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the element to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLast(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the last occurrence within the entire <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>The last element that matches the conditions defined by the specified predicate, if found; otherwise, the default value for type <paramref name="T" />.</returns>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the element to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the last occurrence within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that contains the specified number of elements and ends at the specified index.</summary>
      <returns>The zero-based index of the last occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, –1.</returns>
      <param name="startIndex">The zero-based starting index of the backward search.</param>
      <param name="count">The number of elements in the section to search.</param>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the element to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is outside the range of valid indexes for the <see cref="T:System.Collections.Generic.List`1" />.-or-<paramref name="count" /> is less than 0.-or-<paramref name="startIndex" /> and <paramref name="count" /> do not specify a valid section in the <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the last occurrence within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that extends from the first element to the specified index.</summary>
      <returns>The zero-based index of the last occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, –1.</returns>
      <param name="startIndex">The zero-based starting index of the backward search.</param>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the element to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is outside the range of valid indexes for the <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Predicate{`0})">
      <summary>Searches for an element that matches the conditions defined by the specified predicate, and returns the zero-based index of the last occurrence within the entire <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>The zero-based index of the last occurrence of an element that matches the conditions defined by <paramref name="match" />, if found; otherwise, –1.</returns>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the element to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ForEach(System.Action{`0})">
      <summary>Performs the specified action on each element of the <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="action">The <see cref="T:System.Action`1" /> delegate to perform on each element of the <see cref="T:System.Collections.Generic.List`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.List`1.Enumerator" /> for the <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetRange(System.Int32,System.Int32)">
      <summary>Creates a shallow copy of a range of elements in the source <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>A shallow copy of a range of elements in the source <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="index">The zero-based <see cref="T:System.Collections.Generic.List`1" /> index at which the range starts.</param>
      <param name="count">The number of elements in the range.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.-or-<paramref name="count" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> and <paramref name="count" /> do not denote a valid range of elements in the <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0)">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the entire <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>The zero-based index of the first occurrence of <paramref name="item" /> within the entire <see cref="T:System.Collections.Generic.List`1" />, if found; otherwise, –1.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.List`1" />. The value can be null for reference types.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that extends from the specified index to the last element.</summary>
      <returns>The zero-based index of the first occurrence of <paramref name="item" /> within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that extends from <paramref name="index" /> to the last element, if found; otherwise, –1.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.List`1" />. The value can be null for reference types.</param>
      <param name="index">The zero-based starting index of the search. 0 (zero) is valid in an empty list.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the range of valid indexes for the <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the first occurrence within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that starts at the specified index and contains the specified number of elements.</summary>
      <returns>The zero-based index of the first occurrence of <paramref name="item" /> within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that starts at <paramref name="index" /> and contains <paramref name="count" /> number of elements, if found; otherwise, –1.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.List`1" />. The value can be null for reference types.</param>
      <param name="index">The zero-based starting index of the search. 0 (zero) is valid in an empty list.</param>
      <param name="count">The number of elements in the section to search.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the range of valid indexes for the <see cref="T:System.Collections.Generic.List`1" />.-or-<paramref name="count" /> is less than 0.-or-<paramref name="index" /> and <paramref name="count" /> do not specify a valid section in the <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Insert(System.Int32,`0)">
      <summary>Inserts an element into the <see cref="T:System.Collections.Generic.List`1" /> at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
      <param name="item">The object to insert. The value can be null for reference types.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.-or-<paramref name="index" /> is greater than <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Inserts the elements of a collection into the <see cref="T:System.Collections.Generic.List`1" /> at the specified index.</summary>
      <param name="index">The zero-based index at which the new elements should be inserted.</param>
      <param name="collection">The collection whose elements should be inserted into the <see cref="T:System.Collections.Generic.List`1" />. The collection itself cannot be null, but it can contain elements that are null, if type <paramref name="T" /> is a reference type.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.-or-<paramref name="index" /> is greater than <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <returns>The element at the specified index.</returns>
      <param name="index">The zero-based index of the element to get or set.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.-or-<paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.Generic.List`1.Count" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0)">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the entire <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>The zero-based index of the last occurrence of <paramref name="item" /> within the entire the <see cref="T:System.Collections.Generic.List`1" />, if found; otherwise, –1.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.List`1" />. The value can be null for reference types.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that extends from the first element to the specified index.</summary>
      <returns>The zero-based index of the last occurrence of <paramref name="item" /> within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that extends from the first element to <paramref name="index" />, if found; otherwise, –1.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.List`1" />. The value can be null for reference types.</param>
      <param name="index">The zero-based starting index of the backward search.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the range of valid indexes for the <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>Searches for the specified object and returns the zero-based index of the last occurrence within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that contains the specified number of elements and ends at the specified index.</summary>
      <returns>The zero-based index of the last occurrence of <paramref name="item" /> within the range of elements in the <see cref="T:System.Collections.Generic.List`1" /> that contains <paramref name="count" /> number of elements and ends at <paramref name="index" />, if found; otherwise, –1.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.List`1" />. The value can be null for reference types.</param>
      <param name="index">The zero-based starting index of the backward search.</param>
      <param name="count">The number of elements in the section to search.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the range of valid indexes for the <see cref="T:System.Collections.Generic.List`1" />.-or-<paramref name="count" /> is less than 0.-or-<paramref name="index" /> and <paramref name="count" /> do not specify a valid section in the <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Remove(`0)">
      <summary>Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true if <paramref name="item" /> is successfully removed; otherwise, false.  This method also returns false if <paramref name="item" /> was not found in the <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="item">The object to remove from the <see cref="T:System.Collections.Generic.List`1" />. The value can be null for reference types.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAll(System.Predicate{`0})">
      <summary>Removes all the elements that match the conditions defined by the specified predicate.</summary>
      <returns>The number of elements removed from the <see cref="T:System.Collections.Generic.List`1" /> .</returns>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions of the elements to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAt(System.Int32)">
      <summary>Removes the element at the specified index of the <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="index">The zero-based index of the element to remove.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.-or-<paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveRange(System.Int32,System.Int32)">
      <summary>Removes a range of elements from the <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="index">The zero-based starting index of the range of elements to remove.</param>
      <param name="count">The number of elements to remove.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.-or-<paramref name="count" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> and <paramref name="count" /> do not denote a valid range of elements in the <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse">
      <summary>Reverses the order of the elements in the entire <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse(System.Int32,System.Int32)">
      <summary>Reverses the order of the elements in the specified range.</summary>
      <param name="index">The zero-based starting index of the range to reverse.</param>
      <param name="count">The number of elements in the range to reverse.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.-or-<paramref name="count" /> is less than 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> and <paramref name="count" /> do not denote a valid range of elements in the <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort">
      <summary>Sorts the elements in the entire <see cref="T:System.Collections.Generic.List`1" /> using the default comparer.</summary>
      <exception cref="T:System.InvalidOperationException">The default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" /> cannot find an implementation of the <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface for type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>Sorts the elements in the entire <see cref="T:System.Collections.Generic.List`1" /> using the specified comparer.</summary>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing elements, or null to use the default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> is null, and the default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" /> cannot find implementation of the <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface for type <paramref name="T" />.</exception>
      <exception cref="T:System.ArgumentException">The implementation of <paramref name="comparer" /> caused an error during the sort. For example, <paramref name="comparer" /> might not return 0 when comparing an item with itself.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Comparison{`0})">
      <summary>Sorts the elements in the entire <see cref="T:System.Collections.Generic.List`1" /> using the specified <see cref="T:System.Comparison`1" />.</summary>
      <param name="comparison">The <see cref="T:System.Comparison`1" /> to use when comparing elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The implementation of <paramref name="comparison" /> caused an error during the sort. For example, <paramref name="comparison" /> might not return 0 when comparing an item with itself.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Sorts the elements in a range of elements in <see cref="T:System.Collections.Generic.List`1" /> using the specified comparer.</summary>
      <param name="index">The zero-based starting index of the range to sort.</param>
      <param name="count">The length of the range to sort.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing elements, or null to use the default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.-or-<paramref name="count" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> and <paramref name="count" /> do not specify a valid range in the <see cref="T:System.Collections.Generic.List`1" />.-or-The implementation of <paramref name="comparer" /> caused an error during the sort. For example, <paramref name="comparer" /> might not return 0 when comparing an item with itself.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> is null, and the default comparer <see cref="P:System.Collections.Generic.Comparer`1.Default" /> cannot find implementation of the <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface for type <paramref name="T" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.List`1" />, this property always returns false.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.ICollection" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.List`1" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.  In the default implementation of <see cref="T:System.Collections.Generic.List`1" />, this property always returns the current instance.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Add(System.Object)">
      <summary>Adds an item to the <see cref="T:System.Collections.IList" />.</summary>
      <returns>The position into which the new element was inserted.</returns>
      <param name="item">The <see cref="T:System.Object" /> to add to the <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> is of a type that is not assignable to the <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Contains(System.Object)">
      <summary>Determines whether the <see cref="T:System.Collections.IList" /> contains a specific value.</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.IList" />; otherwise, false.</returns>
      <param name="item">The <see cref="T:System.Object" /> to locate in the <see cref="T:System.Collections.IList" />.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>Determines the index of a specific item in the <see cref="T:System.Collections.IList" />.</summary>
      <returns>The index of <paramref name="item" /> if found in the list; otherwise, –1.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> is of a type that is not assignable to the <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Inserts an item to the <see cref="T:System.Collections.IList" /> at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
      <param name="item">The object to insert into the <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is not a valid index in the <see cref="T:System.Collections.IList" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> is of a type that is not assignable to the <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsFixedSize">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IList" /> has a fixed size.</summary>
      <returns>true if the <see cref="T:System.Collections.IList" /> has a fixed size; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.List`1" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IList" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.IList" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.List`1" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <returns>The element at the specified index.</returns>
      <param name="index">The zero-based index of the element to get or set.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is not a valid index in the <see cref="T:System.Collections.IList" />.</exception>
      <exception cref="T:System.ArgumentException">The property is set and <paramref name="value" /> is of a type that is not assignable to the <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Remove(System.Object)">
      <summary>Removes the first occurrence of a specific object from the <see cref="T:System.Collections.IList" />.</summary>
      <param name="item">The object to remove from the <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> is of a type that is not assignable to the <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ToArray">
      <summary>Copies the elements of the <see cref="T:System.Collections.Generic.List`1" /> to a new array.</summary>
      <returns>An array containing copies of the elements of the <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrimExcess">
      <summary>Sets the capacity to the actual number of elements in the <see cref="T:System.Collections.Generic.List`1" />, if that number is less than a threshold value.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrueForAll(System.Predicate{`0})">
      <summary>Determines whether every element in the <see cref="T:System.Collections.Generic.List`1" /> matches the conditions defined by the specified predicate.</summary>
      <returns>true if every element in the <see cref="T:System.Collections.Generic.List`1" /> matches the conditions defined by the specified predicate; otherwise, false. If the list has no elements, the return value is true.</returns>
      <param name="match">The <see cref="T:System.Predicate`1" /> delegate that defines the conditions to check against the elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="T:System.Collections.Generic.List`1.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.List`1" /> at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Collections.Generic.List`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.List`1" /> at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="T:System.Collections.Generic.Queue`1">
      <summary>Represents a first-in, first-out collection of objects.</summary>
      <typeparam name="T">Specifies the type of elements in the queue.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Queue`1" /> class that is empty and has the default initial capacity.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Queue`1" /> class that contains elements copied from the specified collection and has sufficient capacity to accommodate the number of elements copied.</summary>
      <param name="collection">The collection whose elements are copied to the new <see cref="T:System.Collections.Generic.Queue`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Queue`1" /> class that is empty and has the specified initial capacity.</summary>
      <param name="capacity">The initial number of elements that the <see cref="T:System.Collections.Generic.Queue`1" /> can contain.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Clear">
      <summary>Removes all objects from the <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Contains(`0)">
      <summary>Determines whether an element is in the <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.Queue`1" />; otherwise, false.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.Queue`1" />. The value can be null for reference types.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.CopyTo(`0[],System.Int32)">
      <summary>Copies the <see cref="T:System.Collections.Generic.Queue`1" /> elements to an existing one-dimensional <see cref="T:System.Array" />, starting at the specified array index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.Queue`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Queue`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Dequeue">
      <summary>Removes and returns the object at the beginning of the <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>The object that is removed from the beginning of the <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enqueue(`0)">
      <summary>Adds an object to the end of the <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <param name="item">The object to add to the <see cref="T:System.Collections.Generic.Queue`1" />. The value can be null for reference types.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.Queue`1.Enumerator" /> for the <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Peek">
      <summary>Returns the object at the beginning of the <see cref="T:System.Collections.Generic.Queue`1" /> without removing it.</summary>
      <returns>The object at the beginning of the <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.ICollection" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.Queue`1" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.  In the default implementation of <see cref="T:System.Collections.Generic.Queue`1" />, this property always returns the current instance.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.ToArray">
      <summary>Copies the <see cref="T:System.Collections.Generic.Queue`1" /> elements to a new array.</summary>
      <returns>A new array containing elements copied from the <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.TrimExcess">
      <summary>Sets the capacity to the actual number of elements in the <see cref="T:System.Collections.Generic.Queue`1" />, if that number is less than 90 percent of current capacity.</summary>
    </member>
    <member name="T:System.Collections.Generic.Queue`1.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.Queue`1" /> at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Collections.Generic.Queue`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2">
      <summary>Represents a collection of key/value pairs that are sorted on the key. </summary>
      <typeparam name="TKey">The type of the keys in the dictionary.</typeparam>
      <typeparam name="TValue">The type of the values in the dictionary.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> class that is empty and uses the default <see cref="T:System.Collections.Generic.IComparer`1" /> implementation for the key type.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> class that is empty and uses the specified <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to compare keys.</summary>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.Comparer`1" /> for the type of the key.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> class that contains elements copied from the specified <see cref="T:System.Collections.Generic.IDictionary`2" /> and uses the default <see cref="T:System.Collections.Generic.IComparer`1" /> implementation for the key type.</summary>
      <param name="dictionary">The <see cref="T:System.Collections.Generic.IDictionary`2" /> whose elements are copied to the new <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contains one or more duplicate keys.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> class that contains elements copied from the specified <see cref="T:System.Collections.Generic.IDictionary`2" /> and uses the specified <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to compare keys.</summary>
      <param name="dictionary">The <see cref="T:System.Collections.Generic.IDictionary`2" /> whose elements are copied to the new <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.Comparer`1" /> for the type of the key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contains one or more duplicate keys.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Add(`0,`1)">
      <summary>Adds an element with the specified key and value into the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add. The value can be null for reference types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.ArgumentException">An element with the same key already exists in the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Clear">
      <summary>Removes all elements from the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Comparer">
      <summary>Gets the <see cref="T:System.Collections.Generic.IComparer`1" /> used to order the elements of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>The <see cref="T:System.Collections.Generic.IComparer`1" /> used to order the elements of the <see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsKey(`0)">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified key.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">The key to locate in the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsValue(`1)">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified value.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified value; otherwise, false.</returns>
      <param name="value">The value to locate in the <see cref="T:System.Collections.Generic.SortedDictionary`2" />. The value can be null for reference types.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> to the specified array of <see cref="T:System.Collections.Generic.KeyValuePair`2" /> structures, starting at the specified index.</summary>
      <param name="array">The one-dimensional array of <see cref="T:System.Collections.Generic.KeyValuePair`2" /> structures that is the destination of the elements copied from the current <see cref="T:System.Collections.Generic.SortedDictionary`2" /> The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.SortedDictionary`2" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Count">
      <summary>Gets the number of key/value pairs contained in the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>The number of key/value pairs contained in the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" /> for the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Item(`0)">
      <summary>Gets or sets the value associated with the specified key.</summary>
      <returns>The value associated with the specified key. If the specified key is not found, a get operation throws a <see cref="T:System.Collections.Generic.KeyNotFoundException" />, and a set operation creates a new element with the specified key.</returns>
      <param name="key">The key of the value to get or set.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">The property is retrieved and <paramref name="key" /> does not exist in the collection.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Keys">
      <summary>Gets a collection containing the keys in the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> containing the keys in the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Remove(`0)">
      <summary>Removes the element with the specified key from the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>true if the element is successfully removed; otherwise, false.  This method also returns false if <paramref name="key" /> is not found in the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
      <param name="key">The key of the element to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Adds an item to the <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="keyValuePair">The <see cref="T:System.Collections.Generic.KeyValuePair`2" /> structure to add to the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" /> is null.</exception>
      <exception cref="T:System.ArgumentException">An element with the same key already exists in the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specific key and value.</summary>
      <returns>true if <paramref name="keyValuePair" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="keyValuePair">The <see cref="T:System.Collections.Generic.KeyValuePair`2" /> structure to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false. In the default implementation of <see cref="T:System.Collections.Generic.SortedDictionary`2" />, this property always returns false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Removes the first occurrence of the specified element from the <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>true if <paramref name="keyValuePair" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false. This method also returns false if <paramref name="keyValuePair" /> was not found in the <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">The <see cref="T:System.Collections.Generic.KeyValuePair`2" /> structure to remove from the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Gets a collection containing the keys in the <see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>A collection containing the keys in the <see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Gets a collection containing the values in the <see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>A collection containing the values in the <see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.Generic.ICollection`1" /> to an array, starting at the specified array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.Generic.ICollection`1" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.Generic.ICollection`1" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.Generic.ICollection`1" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.SortedDictionary`2" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />. </returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Adds an element with the provided key and value to the <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">The object to use as the key of the element to add.</param>
      <param name="value">The object to use as the value of the element to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.IDictionary" />.-or-<paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.IDictionary" />.-or-An element with the same key already exists in the <see cref="T:System.Collections.IDictionary" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determines whether the <see cref="T:System.Collections.IDictionary" /> contains an element with the specified key.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.</returns>
      <param name="key">The key to locate in the <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IDictionaryEnumerator" /> for the <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>An <see cref="T:System.Collections.IDictionaryEnumerator" /> for the <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IDictionary" /> has a fixed size.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> has a fixed size; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.SortedDictionary`2" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IDictionary" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.SortedDictionary`2" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Gets or sets the element with the specified key.</summary>
      <returns>The element with the specified key, or null if <paramref name="key" /> is not in the dictionary or <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
      <param name="key">The key of the element to get.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.ArgumentException">A value is being assigned, and <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.-or-A value is being assigned, and <paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Keys">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> containing the keys of the <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> containing the keys of the <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Removes the element with the specified key from the <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">The key of the element to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Values">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> containing the values in the <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> containing the values in the <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.TryGetValue(`0,`1@)">
      <summary>Gets the value associated with the specified key.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">The key of the value to get.</param>
      <param name="value">When this method returns, the value associated with the specified key, if the key is found; otherwise, the default value for the type of the <paramref name="value" /> parameter. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Values">
      <summary>Gets a collection containing the values in the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> containing the values in the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Gets the element at the current position of the enumerator as a <see cref="T:System.Collections.DictionaryEntry" /> structure.</summary>
      <returns>The element in the collection at the current position of the dictionary, as a <see cref="T:System.Collections.DictionaryEntry" /> structure.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Gets the key of the element at the current position of the enumerator.</summary>
      <returns>The key of the element in the collection at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Gets the value of the element at the current position of the enumerator.</summary>
      <returns>The value of the element in the collection at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection">
      <summary>Represents the collection of keys in a <see cref="T:System.Collections.Generic.SortedDictionary`2" />. This class cannot be inherited. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> class that reflects the keys in the specified <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <param name="dictionary">The <see cref="T:System.Collections.Generic.SortedDictionary`2" /> whose keys are reflected in the new <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Copies the <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> elements to an existing one-dimensional array, starting at the specified array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> structure for the <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Adds an item to the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Always thrown; the collection is read-only.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Always thrown; the collection is read-only.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains the specified value.</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, this property always returns false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true if <paramref name="item" /> is successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false. This method also returns false if <paramref name="item" /> is not found in the <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">The object to remove from the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Always thrown; the collection is read-only.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.ICollection" /> to an array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.ICollection" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.  In the default implementation of <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, this property always returns the current instance.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection">
      <summary>Represents the collection of values in a <see cref="T:System.Collections.Generic.SortedDictionary`2" />. This class cannot be inherited</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> class that reflects the values in the specified <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <param name="dictionary">The <see cref="T:System.Collections.Generic.SortedDictionary`2" /> whose values are reflected in the new <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Copies the <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> elements to an existing one-dimensional array, starting at the specified array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> structure for the <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Adds an item to the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Always thrown; the collection is read-only.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Always thrown; the collection is read-only.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specified value.</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, this property always returns false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true if <paramref name="item" /> is successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false. This method also returns false if <paramref name="item" /> is not found in the <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">The object to remove from the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Always thrown; the collection is read-only.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.ICollection`1" />.  This implementation always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true if <paramref name="item" /> is successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false. This method also returns false if <paramref name="item" /> is not found in the <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <exception cref="T:System.NotSupportedException">Always thrown; the collection is read-only.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.ICollection" /> to an array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.ICollection" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.  In the default implementation of <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, this property always returns the current instance.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedList`2">
      <summary>Represents a collection of key/value pairs that are sorted by key based on the associated <see cref="T:System.Collections.Generic.IComparer`1" /> implementation. </summary>
      <typeparam name="TKey">The type of keys in the collection.</typeparam>
      <typeparam name="TValue">The type of values in the collection.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedList`2" /> class that is empty, has the default initial capacity, and uses the default <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedList`2" /> class that is empty, has the default initial capacity, and uses the specified <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing keys.-or-null to use the default <see cref="T:System.Collections.Generic.Comparer`1" /> for the type of the key.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedList`2" /> class that contains elements copied from the specified <see cref="T:System.Collections.Generic.IDictionary`2" />, has sufficient capacity to accommodate the number of elements copied, and uses the default <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="dictionary">The <see cref="T:System.Collections.Generic.IDictionary`2" /> whose elements are copied to the new <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contains one or more duplicate keys.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedList`2" /> class that contains elements copied from the specified <see cref="T:System.Collections.Generic.IDictionary`2" />, has sufficient capacity to accommodate the number of elements copied, and uses the specified <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="dictionary">The <see cref="T:System.Collections.Generic.IDictionary`2" /> whose elements are copied to the new <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing keys.-or-null to use the default <see cref="T:System.Collections.Generic.Comparer`1" /> for the type of the key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contains one or more duplicate keys.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedList`2" /> class that is empty, has the specified initial capacity, and uses the default <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="capacity">The initial number of elements that the <see cref="T:System.Collections.Generic.SortedList`2" /> can contain.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedList`2" /> class that is empty, has the specified initial capacity, and uses the specified <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="capacity">The initial number of elements that the <see cref="T:System.Collections.Generic.SortedList`2" /> can contain.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing keys.-or-null to use the default <see cref="T:System.Collections.Generic.Comparer`1" /> for the type of the key.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Add(`0,`1)">
      <summary>Adds an element with the specified key and value into the <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add. The value can be null for reference types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.ArgumentException">An element with the same key already exists in the <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Capacity">
      <summary>Gets or sets the number of elements that the <see cref="T:System.Collections.Generic.SortedList`2" /> can contain.</summary>
      <returns>The number of elements that the <see cref="T:System.Collections.Generic.SortedList`2" /> can contain.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.SortedList`2.Capacity" /> is set to a value that is less than <see cref="P:System.Collections.Generic.SortedList`2.Count" />.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available on the system.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Clear">
      <summary>Removes all elements from the <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Comparer">
      <summary>Gets the <see cref="T:System.Collections.Generic.IComparer`1" /> for the sorted list. </summary>
      <returns>The <see cref="T:System.IComparable`1" /> for the current <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsKey(`0)">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.SortedList`2" /> contains a specific key.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">The key to locate in the <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsValue(`1)">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.SortedList`2" /> contains a specific value.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified value; otherwise, false.</returns>
      <param name="value">The value to locate in the <see cref="T:System.Collections.Generic.SortedList`2" />. The value can be null for reference types.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Count">
      <summary>Gets the number of key/value pairs contained in the <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>The number of key/value pairs contained in the <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> of type <see cref="T:System.Collections.Generic.KeyValuePair`2" /> for the <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfKey(`0)">
      <summary>Searches for the specified key and returns the zero-based index within the entire <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>The zero-based index of <paramref name="key" /> within the entire <see cref="T:System.Collections.Generic.SortedList`2" />, if found; otherwise, -1.</returns>
      <param name="key">The key to locate in the <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfValue(`1)">
      <summary>Searches for the specified value and returns the zero-based index of the first occurrence within the entire <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>The zero-based index of the first occurrence of <paramref name="value" /> within the entire <see cref="T:System.Collections.Generic.SortedList`2" />, if found; otherwise, -1.</returns>
      <param name="value">The value to locate in the <see cref="T:System.Collections.Generic.SortedList`2" />.  The value can be null for reference types.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Item(`0)">
      <summary>Gets or sets the value associated with the specified key.</summary>
      <returns>The value associated with the specified key. If the specified key is not found, a get operation throws a <see cref="T:System.Collections.Generic.KeyNotFoundException" /> and a set operation creates a new element using the specified key.</returns>
      <param name="key">The key whose value to get or set.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">The property is retrieved and <paramref name="key" /> does not exist in the collection.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Keys">
      <summary>Gets a collection containing the keys in the <see cref="T:System.Collections.Generic.SortedList`2" />, in sorted order.</summary>
      <returns>A <see cref="T:System.Collections.Generic.IList`1" /> containing the keys in the <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Remove(`0)">
      <summary>Removes the element with the specified key from the <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>true if the element is successfully removed; otherwise, false.  This method also returns false if <paramref name="key" /> was not found in the original <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">The key of the element to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.RemoveAt(System.Int32)">
      <summary>Removes the element at the specified index of the <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <param name="index">The zero-based index of the element to remove.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or-<paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.Generic.SortedList`2.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Adds a key/value pair to the <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="keyValuePair">The <see cref="T:System.Collections.Generic.KeyValuePair`2" /> to add to the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specific element.</summary>
      <returns>true if <paramref name="keyValuePair" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="keyValuePair">The <see cref="T:System.Collections.Generic.KeyValuePair`2" /> to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.Generic.ICollection`1" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.ICollection`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.ICollection`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.SortedList`2" />, this property always returns false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Removes the first occurrence of a specific key/value pair from the <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>true if <paramref name="keyValuePair" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false. This method also returns false if <paramref name="keyValuePair" /> was not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">The <see cref="T:System.Collections.Generic.KeyValuePair`2" /> to remove from the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the keys of the <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Gets an <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> containing the values in the <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Gets an enumerable collection that contains the keys in the read-only dictionary.</summary>
      <returns>An enumerable collection that contains the keys in the read-only dictionary.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Gets an enumerable collection that contains the values in the read-only dictionary.</summary>
      <returns>An enumerable collection that contains the values in the read-only dictionary.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.ICollection" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.SortedList`2" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.  In the default implementation of <see cref="T:System.Collections.Generic.SortedList`2" />, this property always returns the current instance.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Adds an element with the provided key and value to the <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">The <see cref="T:System.Object" /> to use as the key of the element to add.</param>
      <param name="value">The <see cref="T:System.Object" /> to use as the value of the element to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.IDictionary" />.-or-<paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.IDictionary" />.-or-An element with the same key already exists in the <see cref="T:System.Collections.IDictionary" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determines whether the <see cref="T:System.Collections.IDictionary" /> contains an element with the specified key.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.</returns>
      <param name="key">The key to locate in the <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IDictionaryEnumerator" /> for the <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>An <see cref="T:System.Collections.IDictionaryEnumerator" /> for the <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IDictionary" /> has a fixed size.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> has a fixed size; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.SortedList`2" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IDictionary" /> is read-only.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> is read-only; otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.SortedList`2" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Gets or sets the element with the specified key.</summary>
      <returns>The element with the specified key, or null if <paramref name="key" /> is not in the dictionary or <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">The key of the element to get or set.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
      <exception cref="T:System.ArgumentException">A value is being assigned, and <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.-or-A value is being assigned, and <paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Keys">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> containing the keys of the <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> containing the keys of the <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Removes the element with the specified key from the <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">The key of the element to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Values">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> containing the values in the <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> containing the values in the <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TrimExcess">
      <summary>Sets the capacity to the actual number of elements in the <see cref="T:System.Collections.Generic.SortedList`2" />, if that number is less than 90 percent of current capacity.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TryGetValue(`0,`1@)">
      <summary>Gets the value associated with the specified key.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">The key whose value to get.</param>
      <param name="value">When this method returns, the value associated with the specified key, if the key is found; otherwise, the default value for the type of the <paramref name="value" /> parameter. This parameter is passed uninitialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Values">
      <summary>Gets a collection containing the values in the <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.IList`1" /> containing the values in the <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1">
      <summary>Represents a collection of objects that is maintained in sorted order.</summary>
      <typeparam name="T">The type of elements in the set.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedSet`1" /> class. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedSet`1" /> class that uses a specified comparer.</summary>
      <param name="comparer">The default comparer to use for comparing objects. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedSet`1" /> class that contains elements copied from a specified enumerable collection.</summary>
      <param name="collection">The enumerable collection to be copied. </param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IComparer{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.SortedSet`1" /> class that contains elements copied from a specified enumerable collection and that uses a specified comparer.</summary>
      <param name="collection">The enumerable collection to be copied. </param>
      <param name="comparer">The default comparer to use for comparing objects. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Add(`0)">
      <summary>Adds an element to the set and returns a value that indicates if it was successfully added.</summary>
      <returns>true if <paramref name="item" /> is added to the set; otherwise, false. </returns>
      <param name="item">The element to add to the set.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Clear">
      <summary>Removes all elements from the set.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Comparer">
      <summary>Gets the <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> object that is used to determine equality for the values in the <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>The comparer that is used to determine equality for the values in the <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Contains(`0)">
      <summary>Determines whether the set contains a specific element.</summary>
      <returns>true if the set contains <paramref name="item" />; otherwise, false.</returns>
      <param name="item">The element to locate in the set.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[])">
      <summary>Copies the complete <see cref="T:System.Collections.Generic.SortedSet`1" /> to a compatible one-dimensional array, starting at the beginning of the target array.</summary>
      <param name="array">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.SortedSet`1" /> exceeds the number of elements that the destination array can contain. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32)">
      <summary>Copies the complete <see cref="T:System.Collections.Generic.SortedSet`1" /> to a compatible one-dimensional array, starting at the specified array index.</summary>
      <param name="array">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.Generic.SortedSet`1" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentException">The number of elements in the source array is greater than the available space from <paramref name="index" /> to the end of the destination array.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Copies a specified number of elements from <see cref="T:System.Collections.Generic.SortedSet`1" /> to a compatible one-dimensional array, starting at the specified array index.</summary>
      <param name="array">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.Generic.SortedSet`1" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <param name="count">The number of elements to copy.</param>
      <exception cref="T:System.ArgumentException">The number of elements in the source array is greater than the available space from <paramref name="index" /> to the end of the destination array.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or-<paramref name="count" /> is less than zero.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Count">
      <summary>Gets the number of elements in the <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>The number of elements in the <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Removes all elements that are in a specified collection from the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object.</summary>
      <param name="other">The collection of items to remove from the <see cref="T:System.Collections.Generic.SortedSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>An enumerator that iterates through the <see cref="T:System.Collections.Generic.SortedSet`1" /> in sorted order.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetViewBetween(`0,`0)">
      <summary>Returns a view of a subset in a <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>A subset view that contains only the values in the specified range.</returns>
      <param name="lowerValue">The lowest desired value in the view.</param>
      <param name="upperValue">The highest desired value in the view. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="lowerValue" /> is more than <paramref name="upperValue" /> according to the comparer.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">A tried operation on the view was outside the range specified by <paramref name="lowerValue" /> and <paramref name="upperValue" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object so that it contains only elements that are also in a specified collection.</summary>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether a <see cref="T:System.Collections.Generic.SortedSet`1" /> object is a proper subset of the specified collection.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedSet`1" /> object is a proper subset of <paramref name="other" />; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether a <see cref="T:System.Collections.Generic.SortedSet`1" /> object is a proper superset of the specified collection.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedSet`1" /> object is a proper superset of <paramref name="other" />; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether a <see cref="T:System.Collections.Generic.SortedSet`1" /> object is a subset of the specified collection.</summary>
      <returns>true if the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object is a subset of <paramref name="other" />; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether a <see cref="T:System.Collections.Generic.SortedSet`1" /> object is a superset of the specified collection.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedSet`1" /> object is a superset of <paramref name="other" />; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Max">
      <summary>Gets the maximum value in the <see cref="T:System.Collections.Generic.SortedSet`1" />, as defined by the comparer.</summary>
      <returns>The maximum value in the set.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Min">
      <summary>Gets the minimum value in the <see cref="T:System.Collections.Generic.SortedSet`1" />, as defined by the comparer.</summary>
      <returns>The minimum value in the set.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object and a specified collection share common elements.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedSet`1" /> object and <paramref name="other" /> share at least one common element; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Remove(`0)">
      <summary>Removes a specified item from the <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>true if the element is found and successfully removed; otherwise, false. </returns>
      <param name="item">The element to remove.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Removes all elements that match the conditions defined by the specified predicate from a <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>The number of elements that were removed from the <see cref="T:System.Collections.Generic.SortedSet`1" /> collection.. </returns>
      <param name="match">The delegate that defines the conditions of the elements to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Reverse">
      <summary>Returns an <see cref="T:System.Collections.Generic.IEnumerable`1" /> that iterates over the <see cref="T:System.Collections.Generic.SortedSet`1" /> in reverse order.</summary>
      <returns>An enumerator that iterates over the <see cref="T:System.Collections.Generic.SortedSet`1" /> in reverse order.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determines whether the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object and the specified collection contain the same elements.</summary>
      <returns>true if the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object is equal to <paramref name="other" />; otherwise, false.</returns>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object so that it contains only elements that are present either in the current object or in the specified collection, but not both.</summary>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Adds an item to an <see cref="T:System.Collections.Generic.ICollection`1" /> object.</summary>
      <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1" /> object.</param>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Gets a value that indicates whether a <see cref="T:System.Collections.ICollection" /> is read-only.</summary>
      <returns>true if the collection is read-only; otherwise, false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the complete <see cref="T:System.Collections.Generic.SortedSet`1" /> to a compatible one-dimensional array, starting at the specified array index.</summary>
      <param name="array">A one-dimensional array that is the destination of the elements copied from the <see cref="T:System.Collections.Generic.SortedSet`1" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentException">The number of elements in the source array is greater than the available space from <paramref name="index" /> to the end of the destination array. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value that indicates whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized; otherwise, false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />. In the default implementation of <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, this property always returns the current instance.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifies the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object so that it contains all elements that are present in either the current object or the specified collection. </summary>
      <param name="other">The collection to compare to the current <see cref="T:System.Collections.Generic.SortedSet`1" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.SortedSet`1" /> object.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Collections.Generic.SortedSet`1.Enumerator" />. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.SortedSet`1" /> collection.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="T:System.Collections.Generic.Stack`1">
      <summary>Represents a variable size last-in-first-out (LIFO) collection of instances of the same specified type.</summary>
      <typeparam name="T">Specifies the type of elements in the stack.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Stack`1" /> class that is empty and has the default initial capacity.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Stack`1" /> class that contains elements copied from the specified collection and has sufficient capacity to accommodate the number of elements copied.</summary>
      <param name="collection">The collection to copy elements from.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Generic.Stack`1" /> class that is empty and has the specified initial capacity or the default initial capacity, whichever is greater.</summary>
      <param name="capacity">The initial number of elements that the <see cref="T:System.Collections.Generic.Stack`1" /> can contain.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Clear">
      <summary>Removes all objects from the <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Contains(`0)">
      <summary>Determines whether an element is in the <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.Stack`1" />; otherwise, false.</returns>
      <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.Stack`1" />. The value can be null for reference types.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.CopyTo(`0[],System.Int32)">
      <summary>Copies the <see cref="T:System.Collections.Generic.Stack`1" /> to an existing one-dimensional <see cref="T:System.Array" />, starting at the specified array index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.Stack`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Stack`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.GetEnumerator">
      <summary>Returns an enumerator for the <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.Stack`1.Enumerator" /> for the <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Peek">
      <summary>Returns the object at the top of the <see cref="T:System.Collections.Generic.Stack`1" /> without removing it.</summary>
      <returns>The object at the top of the <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Pop">
      <summary>Removes and returns the object at the top of the <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>The object removed from the top of the <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Push(`0)">
      <summary>Inserts an object at the top of the <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <param name="item">The object to push onto the <see cref="T:System.Collections.Generic.Stack`1" />. The value can be null for reference types.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.ICollection" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.  In the default implementation of <see cref="T:System.Collections.Generic.Stack`1" />, this property always returns false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.  In the default implementation of <see cref="T:System.Collections.Generic.Stack`1" />, this property always returns the current instance.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.ToArray">
      <summary>Copies the <see cref="T:System.Collections.Generic.Stack`1" /> to a new array.</summary>
      <returns>A new array containing copies of the elements of the <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.TrimExcess">
      <summary>Sets the capacity to the actual number of elements in the <see cref="T:System.Collections.Generic.Stack`1" />, if that number is less than 90 percent of current capacity.</summary>
    </member>
    <member name="T:System.Collections.Generic.Stack`1.Enumerator">
      <summary>Enumerates the elements of a <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the <see cref="T:System.Collections.Generic.Stack`1" /> at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Collections.Generic.Stack`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection. This class cannot be inherited.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
    </member>
  </members>
</doc>