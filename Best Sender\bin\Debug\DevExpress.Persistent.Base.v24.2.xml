﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>DevExpress.Persistent.Base.v24.2</name>
  </assembly>
  <members>
    <member name="N:DevExpress.ExpressApp.Chart">
      <summary>
        <para>Contains classes and interfaces specific to the <see href="https://docs.devexpress.com/eXpressAppFramework/113302/analytics/chart-module">Chart Module</see> functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.ExpressApp.Chart.ICustomizationEnabledProvider">
      <summary>
        <para>Declares members implemented by charting entities, that allow or prohibit end-user customization to charts displayed in a UI.</para>
      </summary>
    </member>
    <member name="P:DevExpress.ExpressApp.Chart.ICustomizationEnabledProvider.CustomizationEnabled">
      <summary>
        <para>Indicates whether end-users can customize charts settings in Windows Forms applications, via the context menu’s Invoke Wizard and Clear Settings Actions.</para>
      </summary>
      <value>true, if end-user customizations are allowed in Windows Forms applications; otherwise, false.</value>
    </member>
    <member name="N:DevExpress.ExpressApp.ConditionalAppearance">
      <summary>
        <para>Contains classes specific to the <see href="https://docs.devexpress.com/eXpressAppFramework/113286/conditional-appearance">Conditional Appearance Module</see> functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute">
      <summary>
        <para>Applied to business classes and their properties. Declares a conditional appearance rule.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.#ctor(System.String,DevExpress.ExpressApp.ConditionalAppearance.AppearanceItemType,System.String)">
      <summary>
        <para>Creates a new instance of the <see cref="T:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute"/> using the specified parameters.</para>
      </summary>
      <param name="id">A string that is the unique identifier for the appearance rule created using the current attribute instance.</param>
      <param name="appearanceItemType">An <see cref="T:DevExpress.ExpressApp.ConditionalAppearance.AppearanceItemType"/> enumeration value specifying the type of UI elements affected by the conditional appearance rule that is created using the current attribute instance.</param>
      <param name="criteria">A string that is the criterion for activation of the rule created using the current attribute instance.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.#ctor(System.String,System.String)">
      <summary>
        <para>Creates a new instance of the <see cref="T:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute"/> using the specified parameters.</para>
      </summary>
      <param name="id">A string that is the unique identifier for the appearance rule created using the current attribute instance.</param>
      <param name="criteria">A string that is the criterion for activation of the rule created using the current attribute instance.</param>
    </member>
    <member name="M:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.#ctor(System.String)">
      <summary>
        <para>Creates a new instance of the <see cref="T:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute"/> class.</para>
      </summary>
      <param name="id">A string that is the unique identifier for the appearance rule created using the current attribute instance.</param>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.AppearanceItemType">
      <summary>
        <para>Specifies the type of UI elements affected by the conditional appearance rule created using this attribute.</para>
      </summary>
      <value>The string representation of an <see cref="T:DevExpress.ExpressApp.ConditionalAppearance.AppearanceItemType"/> enumeration value specifying the type of UI elements affected by the conditional appearance rule.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.BackColor">
      <summary>
        <para>Specifies the background color of UI elements affected by the conditional appearance rule generated from this attribute instance.</para>
      </summary>
      <value>A string specifying the background color of UI elements affected by the conditional appearance rule.  A value should be recognizable by the System.Drawing.ColorConverter class.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.Context">
      <summary>
        <para>Specifies the <see href="https://docs.devexpress.com/eXpressAppFramework/112611/ui-construction/views">Views</see> representing the activity scope for the conditional appearance rule generated from the current attribute.</para>
      </summary>
      <value>A string that is semicolon separated names of the Views in which the conditional appearance rule is in effect.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.Criteria">
      <summary>
        <para>Specifies the criteria string used when determining whether <see cref="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.TargetItems">AppearanceAttribute.TargetItems</see> should be affected by the conditional appearance rule generated from the current attribute.</para>
      </summary>
      <value>A string representing the criterion under which the conditional appearance rule is in effect.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.DeclaringType">
      <summary>
        <para>This property is intended for internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.Enabled">
      <summary>
        <para>Specifies whether to enable UI elements affected by the conditional appearance rule generated from this attribute instance.</para>
      </summary>
      <value>true, if UI elements affected by the conditional appearance rule must be enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.FontColor">
      <summary>
        <para>Specifies the font color of <see cref="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.TargetItems">AppearanceAttribute.TargetItems</see> affected by the conditional appearance rule generated from this attribute instance.</para>
      </summary>
      <value>A string specifying the font color of target items affected by the conditional appearance rule. A value should be recognizable by the System.Drawing.ColorConverter class.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.FontStyle">
      <summary>
        <para>Specifies the font style of <see cref="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.TargetItems">AppearanceAttribute.TargetItems</see> affected by the conditional appearance rule generated from this attribute instance.</para>
      </summary>
      <value>An enumeration value that specifies the font style of target items affected by the conditional appearance rule.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.Id">
      <summary>
        <para>Specifies the unique identifier for the appearance rule created using the current attribute instance.</para>
      </summary>
      <value>A string containing the rule’s unique identifier.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.Method">
      <summary>
        <para>Specifies the name of the business class method used to determine whether the <see href="https://docs.devexpress.com/eXpressAppFramework/113286/conditional-appearance">Conditional Appearance</see> rule generated from this attribute is currently active.</para>
      </summary>
      <value>A string specifying the name of the method used to determine whether the rule is active.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.Priority">
      <summary>
        <para>Specifies the priority of the conditional appearance rule generated from the current attribute instance. Used when several rules affect the same UI element.</para>
      </summary>
      <value>An integer value specifying the volume of the conditional appearance rule.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.TargetItems">
      <summary>
        <para>Specifies the identifiers of UI elements affected by the conditional appearance rule.</para>
      </summary>
      <value>A string consisting of is semicolon separated identifiers of the UI elements affected by the conditional appearance rule.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.AppearanceAttribute.Visibility">
      <summary>
        <para>Specifies the visibility of UI elements affected by the conditional appearance rule generated from this attribute instance.</para>
      </summary>
      <value>A <see cref="T:DevExpress.ExpressApp.Editors.ViewItemVisibility"/> enumeration value specifying the visibility of UI elements affected by the conditional appearance rule.</value>
    </member>
    <member name="T:DevExpress.ExpressApp.ConditionalAppearance.AppearanceItemType">
      <summary>
        <para>Contains values specifying types of UI elements that can be affected by <see href="https://docs.devexpress.com/eXpressAppFramework/113286/conditional-appearance">conditional appearance rules</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.ExpressApp.ConditionalAppearance.AppearanceItemType.Action">
      <summary>
        <para>An appearance rule affects <see href="https://docs.devexpress.com/eXpressAppFramework/112622/ui-construction/controllers-and-actions/actions">Actions</see>. To specify Actions that are affected, pass their identifiers to the <see cref="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties.TargetItems">IAppearanceRuleProperties.TargetItems</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.ExpressApp.ConditionalAppearance.AppearanceItemType.LayoutItem">
      <summary>
        <para>An appearance rule affects <see href="https://docs.devexpress.com/eXpressAppFramework/112817/ui-construction/views/layout/view-items-layout-customization">Layout Items or Layout Groups</see>. To specify Items and Groups that are affected, pass their identifiers (Id values of the corresponding application model nodes) to the <see cref="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties.TargetItems">IAppearanceRuleProperties.TargetItems</see> property. The Detail View that contains these Items and Groups can be specified via the <see cref="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties.Context">IAppearanceRuleProperties.Context</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.ExpressApp.ConditionalAppearance.AppearanceItemType.ViewItem">
      <summary>
        <para>An appearance rule affects View Items (Property Editors, Static Text Items, etc.) and List Editor cells. To specify items/cells that are affected, pass their identifiers (Id values of the corresponding application model nodes) to the <see cref="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties.TargetItems">IAppearanceRuleProperties.TargetItems</see> property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.ExpressApp.ConditionalAppearance.IAppearance">
      <summary>
        <para>Declares the basic properties of conditional appearance rules. Serves as the base interface for the <see cref="T:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties"/> interface.</para>
      </summary>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearance.BackColor">
      <summary>
        <para>Specifies the background color of UI elements affected by the conditional appearance rule.</para>
      </summary>
      <value>A Nullable&lt;System.Drawing.Color&gt; object specifying the background color of UI elements affected by the conditional appearance rule.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearance.Enabled">
      <summary>
        <para>Specifies whether UI elements affected by the conditional appearance rule must be enabled.</para>
      </summary>
      <value>true, if UI elements affected by the conditional appearance rule must be enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearance.FontColor">
      <summary>
        <para>Specifies the font color of UI elements affected by the conditional appearance rule.</para>
      </summary>
      <value>A Nullable&lt;System.Drawing.Color&gt; object specifying the font color of UI elements affected by the conditional appearance rule.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearance.FontStyle">
      <summary>
        <para>Specifies the font style of UI elements affected by the conditional appearance rule.</para>
      </summary>
      <value>An enumeration value specifying the font color of UI elements affected by the conditional appearance rule.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearance.Priority">
      <summary>
        <para>Specifies the volume of the conditional appearance rule. Used when several rules affect the same UI element.</para>
      </summary>
      <value>An integer value specifying the volume of the conditional appearance rule.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearance.Visibility">
      <summary>
        <para>Specifies the visibility of UI elements affected by a conditional appearance rule.</para>
      </summary>
      <value>A Nullable&lt;<see cref="T:DevExpress.ExpressApp.Editors.ViewItemVisibility"/>&gt; enumeration value specifying the visibility of UI elements affected by a conditional appearance rule.</value>
    </member>
    <member name="T:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties">
      <summary>
        <para>Declares members implemented by conditional appearance rules and the <see cref="T:DevExpress.ExpressApp.ConditionalAppearance.IModelAppearanceRule"/> nodes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties.AppearanceItemType">
      <summary>
        <para>Specifies the type of UI elements affected by the conditional appearance rule.</para>
      </summary>
      <value>The string representation of an <see cref="T:DevExpress.ExpressApp.ConditionalAppearance.AppearanceItemType"/> enumeration value specifying the type of UI elements affected by the conditional appearance rule.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties.Context">
      <summary>
        <para>Specifies the <see href="https://docs.devexpress.com/eXpressAppFramework/112611/ui-construction/views">Views</see> in which the conditional appearance rule is in effect.</para>
      </summary>
      <value>A string that is semicolon separated names of the Views in which the conditional appearance rule is in effect.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties.Criteria">
      <summary>
        <para>Specifies the criteria string used when determining whether <see cref="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties.TargetItems">IAppearanceRuleProperties.TargetItems</see> should be affected by the conditional appearance rule.</para>
      </summary>
      <value>A string representing the criterion used when determining whether TargetItems should be affected by the conditional appearance rule.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties.DeclaringType">
      <summary>
        <para>Gets the target type of the appearance rule.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> object that specifies the target type.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties.Method">
      <summary>
        <para>Specifies the name of the business class method used to determine whether the <see href="https://docs.devexpress.com/eXpressAppFramework/113286/conditional-appearance">Conditional Appearance</see> rule is currently active.</para>
      </summary>
      <value>A string specifying the name of the method used to determine whether the rule is active.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ConditionalAppearance.IAppearanceRuleProperties.TargetItems">
      <summary>
        <para>Specifies the identifiers of UI elements affected by the conditional appearance rule.</para>
      </summary>
      <value>A string consisting of semicolon separated identifiers of the UI elements affected by the conditional appearance rule.</value>
    </member>
    <member name="N:DevExpress.ExpressApp.ReportsV2">
      <summary>
        <para>Contains interfaces and classes that are specific to the <see href="https://docs.devexpress.com/eXpressAppFramework/113591/shape-export-print-data/reports/reports-v2-module-overview">Reports V2 Module</see>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.ExpressApp.ReportsV2.IInplaceReportV2">
      <summary>
        <para>Implemented by persistent classes used to store reports that can be used as inplace repors.</para>
      </summary>
    </member>
    <member name="P:DevExpress.ExpressApp.ReportsV2.IInplaceReportV2.DataTypeName">
      <summary>
        <para>Gets the name of the report’s data type.</para>
      </summary>
      <value>A string which is the name of the report’s data type.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ReportsV2.IInplaceReportV2.IsInplaceReport">
      <summary>
        <para>Specifies whether or not the report is <see href="https://docs.devexpress.com/eXpressAppFramework/113602/shape-export-print-data/reports/in-place-reports">inplace</see>.</para>
      </summary>
      <value>true, if the report is inplace; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.ExpressApp.ReportsV2.IReportDataV2">
      <summary>
        <para>Implemented by persistent classes used to store reports used by the <see href="https://docs.devexpress.com/eXpressAppFramework/113591/shape-export-print-data/reports/reports-v2-module-overview">Reports V2 Module</see>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.ExpressApp.ReportsV2.IReportDataV2.Content">
      <summary>
        <para>Gets a report content.</para>
      </summary>
      <value>A byte array that is the report content.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ReportsV2.IReportDataV2.DataType">
      <summary>
        <para>Gets the type of persistent objects that are displayed within the report.</para>
      </summary>
      <value>A System.Type value that specifies the type of persistent objects displayed within the report.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ReportsV2.IReportDataV2.DisplayName">
      <summary>
        <para>Gets the report’s display name.</para>
      </summary>
      <value>A string which is the report’s display name.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ReportsV2.IReportDataV2.IsPredefined">
      <summary>
        <para>Gets a boolean value that indicates whether or not the report is predefined.</para>
      </summary>
      <value>true, if a report is predefined in code; false, if a report is created at runtime.</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ReportsV2.IReportDataV2.ParametersObjectType">
      <summary>
        <para>Gets the type of the reports parameter object.</para>
      </summary>
      <value>A System.Type of an object that specifies report parameters (typically, the <see cref="T:DevExpress.ExpressApp.ReportsV2.ReportParametersObjectBase"/> descendant).</value>
    </member>
    <member name="P:DevExpress.ExpressApp.ReportsV2.IReportDataV2.PredefinedReportType">
      <summary>
        <para>Gets the type of the predefined report.</para>
      </summary>
      <value>A System.Type object that specifies the type of the predefined report.</value>
    </member>
    <member name="T:DevExpress.ExpressApp.ReportsV2.ReportParametersObjectBase">
      <summary>
        <para>The base class for classes that specify parameters shown in a popup Detail View before a report is executed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.ExpressApp.ReportsV2.ReportParametersObjectBase.GetCriteria">
      <summary>
        <para>Returns the criteria used to filter data displayed in a report.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object used to filter data.</returns>
    </member>
    <member name="M:DevExpress.ExpressApp.ReportsV2.ReportParametersObjectBase.GetSorting">
      <summary>
        <para>Returns the list of properties rules used to sort data displayed in a report.</para>
      </summary>
      <returns>An array of <see cref="T:DevExpress.Xpo.SortProperty"/> objects which specify data sorting.</returns>
    </member>
    <member name="P:DevExpress.ExpressApp.ReportsV2.ReportParametersObjectBase.ObjectSpace">
      <summary>
        <para>Gets an Object Space used by the report parameters Detail View.</para>
      </summary>
      <value>An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> object used by the report parameters Detail View.</value>
    </member>
    <member name="N:DevExpress.Persistent.Base">
      <summary>
        <para>Contains interfaces and classes used in XAF <see href="https://docs.devexpress.com/eXpressAppFramework/118046/app-shell-and-base-infrastructure/application-solution-components/modules">modules</see> (special assemblies implementing particular features). Interfaces are implemented in some classes from the Business Class Library’s DevExpress.Persistent.BaseImpl.Xpo.dll assembly.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Base.FileAttachmentAttribute">
      <summary>
        <para>Applied to business classes that expose a property of the <see cref="T:DevExpress.Persistent.Base.IFileData"/> type. Activates Controllers that manage <see href="https://docs.devexpress.com/eXpressAppFramework/112781/document-management/file-attachments-module">file attachments</see> for the target business class. Specifies a property that stores a file attachment.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.FileAttachmentAttribute.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Base.FileAttachmentAttribute"/> class.</para>
      </summary>
      <param name="fileDataPropertyName">A string specifying the target class property which returns a <see cref="T:DevExpress.Persistent.Base.IFileData"/> object. This parameter’s value is assigned to the <see cref="P:DevExpress.Persistent.Base.FileAttachmentAttribute.FileDataPropertyName">FileAttachmentAttribute.FileDataPropertyName</see> property.</param>
    </member>
    <member name="P:DevExpress.Persistent.Base.FileAttachmentAttribute.FileDataPropertyName">
      <summary>
        <para>Specifies the target class’ property of the <see cref="T:DevExpress.Persistent.Base.IFileData"/> type to be used by Controllers that manage <see href="https://docs.devexpress.com/eXpressAppFramework/112781/document-management/file-attachments-module">file attachments</see>.</para>
      </summary>
      <value>A string specifying the target class’ property name which returns a <see cref="T:DevExpress.Persistent.Base.IFileData"/> object.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.FileTypeFilterAttribute">
      <summary>
        <para>Applied to file data business classes, interfaces and their properties. Specifies a file type filter which appears in the Open dialog’s “Files of type” box.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.FileTypeFilterAttribute.#ctor(System.String,System.Int32,System.String[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Base.FileTypeFilterAttribute"/>.</para>
      </summary>
      <param name="filterID">A string specifying the file type filter identifier. This parameter’s value is assigned to the <see cref="P:DevExpress.Persistent.Base.FileTypeFilterAttribute.FilterID">FileTypeFilterAttribute.FilterID</see> property.</param>
      <param name="index">An integer value specifying the index of the file type filter. This index allows you to arrange the type filters in the required order, when multiple FileTypeFilter attributes are applied.</param>
      <param name="extensions">A variable number of strings that specify the type filter’s file extensions. These extensions can be retrieved via the <see cref="M:DevExpress.Persistent.Base.FileTypeFilterAttribute.GetExtensions">FileTypeFilterAttribute.GetExtensions</see> method.</param>
    </member>
    <member name="M:DevExpress.Persistent.Base.FileTypeFilterAttribute.#ctor(System.String,System.String,System.Int32,System.String[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Base.FileTypeFilterAttribute"/>.</para>
      </summary>
      <param name="filterID">A string specifying the file type filter identifier. This parameter’s value is assigned to the <see cref="P:DevExpress.Persistent.Base.FileTypeFilterAttribute.FilterID">FileTypeFilterAttribute.FilterID</see> property.</param>
      <param name="filterCaption">A string specifying the file type filter description. This parameter’s value is assigned to the <see cref="P:DevExpress.Persistent.Base.FileTypeFilterAttribute.FilterCaption">FileTypeFilterAttribute.FilterCaption</see> property.</param>
      <param name="index">An integer value specifying the index of the file type filter. This index allows you to arrange the type filters in the required order, when multiple FileTypeFilter attributes are applied.</param>
      <param name="extensions">A variable number of strings that specify the type filter’s file extensions. These extensions can be retrieved via the <see cref="M:DevExpress.Persistent.Base.FileTypeFilterAttribute.GetExtensions">FileTypeFilterAttribute.GetExtensions</see> method.</param>
    </member>
    <member name="M:DevExpress.Persistent.Base.FileTypeFilterAttribute.#ctor(System.String,System.String,System.String[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Base.FileTypeFilterAttribute"/>.</para>
      </summary>
      <param name="filterID">A string specifying the file type filter identifier. This parameter’s value is assigned to the <see cref="P:DevExpress.Persistent.Base.FileTypeFilterAttribute.FilterID">FileTypeFilterAttribute.FilterID</see> property.</param>
      <param name="filterCaption">A string specifying the file type filter description. This parameter’s value is assigned to the <see cref="P:DevExpress.Persistent.Base.FileTypeFilterAttribute.FilterCaption">FileTypeFilterAttribute.FilterCaption</see> property.</param>
      <param name="extensions">A variable number of strings that specify the type filter’s file extensions. These extensions can be retrieved via the <see cref="M:DevExpress.Persistent.Base.FileTypeFilterAttribute.GetExtensions">FileTypeFilterAttribute.GetExtensions</see> method.</param>
    </member>
    <member name="M:DevExpress.Persistent.Base.FileTypeFilterAttribute.#ctor(System.String,System.String[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Base.FileTypeFilterAttribute"/>.</para>
      </summary>
      <param name="filterID">A string specifying the file type filter identifier. This parameter’s value is assigned to the <see cref="P:DevExpress.Persistent.Base.FileTypeFilterAttribute.FilterID">FileTypeFilterAttribute.FilterID</see> property.</param>
      <param name="extensions">A variable number of strings that specify the type filter’s file extensions. These extensions can be retrieved via the <see cref="M:DevExpress.Persistent.Base.FileTypeFilterAttribute.GetExtensions">FileTypeFilterAttribute.GetExtensions</see> method.</param>
    </member>
    <member name="P:DevExpress.Persistent.Base.FileTypeFilterAttribute.FileTypesFilter">
      <summary>
        <para>Returns the filter string passed to the dialog windows representing the Open dialog.</para>
      </summary>
      <value>A string that is passed to the dialog windows representing the Open dialog.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.FileTypeFilterAttribute.FilterCaption">
      <summary>
        <para>Specifies the file type filter description.</para>
      </summary>
      <value>A string holding the file type filter description.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.FileTypeFilterAttribute.FilterID">
      <summary>
        <para>Indicates the current file type filter identifier.</para>
      </summary>
      <value>A string value containing the current file type filter identifier.</value>
    </member>
    <member name="M:DevExpress.Persistent.Base.FileTypeFilterAttribute.GetExtensions">
      <summary>
        <para>Returns the list of file extensions associated with the current file type filter.</para>
      </summary>
      <returns>A List&lt;String&gt; object which specifies file extensions associated with the current file type filter.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Base.FileTypeFilterAttribute.Index">
      <summary>
        <para>Specifies the file type filter’s index.</para>
      </summary>
      <value>An integer value specifying the file type filter’s index.</value>
    </member>
    <member name="N:DevExpress.Persistent.Base.General">
      <summary>
        <para>Contains general interfaces and classes used in XAF <see href="https://docs.devexpress.com/eXpressAppFramework/118046/app-shell-and-base-infrastructure/application-solution-components/modules">modules</see> (special assemblies implementing particular features). Interfaces are implemented in some classes from the Business Class Library’s DevExpress.Persistent.BaseImpl.Xpo.dll assembly.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Base.General.ICategorizedItem">
      <summary>
        <para>Declares members implemented by the classes that can be represented in a UI via the <see cref="T:DevExpress.ExpressApp.TreeListEditors.Win.CategorizedListEditor"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.General.ICategorizedItem.Category">
      <summary>
        <para>Specifies the current object’s category.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Persistent.Base.General.ITreeNode"/> object that represents the current object’s category.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.General.INotificationItem">
      <summary>
        <para>Declares members of a <see href="https://docs.devexpress.com/eXpressAppFramework/116516/business-model-design-orm/non-persistent-objects">non-persistent class</see> displayed within a List View in the <see href="https://docs.devexpress.com/eXpressAppFramework/113692/event-planning-and-notifications/notifications/reminders-window-elements">reminders window</see>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.General.INotificationItem.NotificationSource">
      <summary>
        <para>Gets a business object associated with the current <see cref="T:DevExpress.Persistent.Base.General.INotificationItem"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Persistent.Base.General.ISupportNotifications"/> business object that is associated with the current notification item.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.General.INotificationsProvider">
      <summary>
        <para>Declares members implemented by <see href="https://docs.devexpress.com/eXpressAppFramework/113693/event-planning-and-notifications/notifications/notifications-service-and-notifications-providers">Notifications Providers</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.General.INotificationsProvider.Dismiss(System.Collections.Generic.IEnumerable{DevExpress.Persistent.Base.General.INotificationItem})">
      <summary>
        <para>Dismisses the specified notification item.</para>
      </summary>
      <param name="notificationItems">An <see cref="T:DevExpress.Persistent.Base.General.INotificationItem"/> object to be dismissed.</param>
    </member>
    <member name="M:DevExpress.Persistent.Base.General.INotificationsProvider.GetActiveNotificationsCount">
      <summary>
        <para>Returns the number of active notifications.</para>
      </summary>
      <returns>An integer value which is the number of active notifications.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Base.General.INotificationsProvider.GetNotificationItems">
      <summary>
        <para>Returns the list of notification items.</para>
      </summary>
      <returns>An IEnumerable&lt;<see cref="T:DevExpress.Persistent.Base.General.INotificationItem"/>&gt; list of notification items.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Base.General.INotificationsProvider.GetPostponedNotificationsCount">
      <summary>
        <para>Returns the number of postponed notifications.</para>
      </summary>
      <returns>An integer value which is the number of postponed notifications.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Base.General.INotificationsProvider.NotificationTypesInfo">
      <summary>
        <para>Gets information on business object types supported by the <see cref="T:DevExpress.Persistent.Base.General.INotificationsProvider"/> provider.</para>
      </summary>
      <value>A HashSet&lt;ITypeInfo&gt; object that provides information on business object types supported by the DefaultNotificatonProvider provider.</value>
    </member>
    <member name="M:DevExpress.Persistent.Base.General.INotificationsProvider.Postpone(System.Collections.Generic.IEnumerable{DevExpress.Persistent.Base.General.INotificationItem},System.TimeSpan)">
      <summary>
        <para>Postpones the specified notification item for the given amount of time.</para>
      </summary>
      <param name="notificationItems">A collection of <see cref="T:DevExpress.Persistent.Base.General.INotificationItem"/> notification items to postpone.</param>
      <param name="postponeTime">A <see cref="T:System.TimeSpan"/> object that specifies the amount of time to postpone the notification.</param>
    </member>
    <member name="T:DevExpress.Persistent.Base.General.ISupportNotifications">
      <summary>
        <para>Declares members of business objects that support the <see href="https://docs.devexpress.com/eXpressAppFramework/113688/event-planning-and-notifications/notifications-module">Notifications Module</see> functionality.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.General.ISupportNotifications.AlarmTime">
      <summary>
        <para>Specifies the time when the notification should be triggered.</para>
      </summary>
      <value>A <see cref="T:System.Nullable`1">Nullable</see>&lt;<see cref="T:System.DateTime"/>&gt; value that specifies the time when the notification should be triggered.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.General.ISupportNotifications.IsPostponed">
      <summary>
        <para>Specifies the value that indicates whether or not the notification is postponed.</para>
      </summary>
      <value>true, when the notification is postponed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.General.ISupportNotifications.NotificationMessage">
      <summary>
        <para>Gets the notification message text.</para>
      </summary>
      <value>A string which is the notification message text.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.General.ISupportNotifications.UniqueId">
      <summary>
        <para>Gets the unique identifier of the current notification object.</para>
      </summary>
      <value>An object that identifies the current <see cref="T:DevExpress.Persistent.Base.General.ISupportNotifications"/> business object.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.General.ITreeNode">
      <summary>
        <para>Declares members implemented by the classes that can be represented in a UI via the <see cref="T:DevExpress.ExpressApp.TreeListEditors.Win.TreeListEditor"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.General.ITreeNode.Children">
      <summary>
        <para>Provides access to the current node’s child nodes.</para>
      </summary>
      <value>A <see cref="T:System.ComponentModel.IBindingList"/> object that represents the current node’s child nodes.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.General.ITreeNode.Name">
      <summary>
        <para>Specifies the current node’s caption.</para>
      </summary>
      <value>A string that holds the current node’s caption.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.General.ITreeNode.Parent">
      <summary>
        <para>Specifies the parent tree node.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Persistent.Base.General.ITreeNode"/> object that represents the current node’s parent tree node.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.General.ITreeNodeImageProvider">
      <summary>
        <para>Can be implemented by the classes implementing the <see cref="T:DevExpress.Persistent.Base.General.ITreeNode"/> interface, to support raster node images.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.General.ITreeNodeImageProvider.GetImage(System.String@)">
      <summary>
        <para>Returns the raster image corresponding to a tree node.</para>
      </summary>
      <param name="imageName">The returned raster image’s name.</param>
      <returns>The raster image corresponding to a tree node.</returns>
    </member>
    <member name="T:DevExpress.Persistent.Base.General.ITreeNodeSvgImageProvider">
      <summary>
        <para>Can be implemented by the classes implementing the <see cref="T:DevExpress.Persistent.Base.General.ITreeNode"/> interface, to support SVG node images.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.General.ITreeNodeSvgImageProvider.GetSvgImage(System.String@)">
      <summary>
        <para>Returns the SVG image corresponding to a tree node.</para>
      </summary>
      <param name="imageName">A string holding the returned SVG image’s name.</param>
      <returns>A DevExpress.Utils.Svg.SvgImage object representing the SVG image corresponding to a tree node.</returns>
    </member>
    <member name="T:DevExpress.Persistent.Base.General.NotificationItemsEventArgs">
      <summary>
        <para>Arguments passed to the <see cref="E:DevExpress.ExpressApp.Notifications.NotificationsController.CustomProcessNotifications">NotificationsController.CustomProcessNotifications</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.General.NotificationItemsEventArgs.#ctor(System.Collections.Generic.IList{DevExpress.Persistent.Base.General.INotificationItem})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Base.General.NotificationItemsEventArgs"/> class.</para>
      </summary>
      <param name="notificationItems">A collection of <see cref="T:DevExpress.Persistent.Base.General.INotificationItem"/> objects.</param>
    </member>
    <member name="P:DevExpress.Persistent.Base.General.NotificationItemsEventArgs.NotificationItems">
      <summary>
        <para>Gets the list of <see cref="T:DevExpress.Persistent.Base.General.INotificationItem"/> objects to be processed.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Persistent.Base.General.INotificationItem"/> objects.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.IAreaInfo">
      <summary>
        <para>Declares properties of business objects that can be displayed as an area on a vector map using the <see href="https://docs.devexpress.com/eXpressAppFramework/114776/analytics/maps-module">Maps Module</see>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.IAreaInfo.Title">
      <summary>
        <para>Gets the <see href="https://docs.devexpress.com/eXpressAppFramework/114776/analytics/maps-module">$maps</see> vector map area title.</para>
      </summary>
      <value>A string which is the area title.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IAreaInfo.Tooltip">
      <summary>
        <para>Gets the vector maps marker tooltip.</para>
      </summary>
      <value>A string which is the marker tooltip.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IAreaInfo.Value">
      <summary>
        <para>Gets the value associated with the current vector map area.</para>
      </summary>
      <value>A single-precision floating-point number specifying the marker value.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.IBaseMapsMarker">
      <summary>
        <para>The base interface for interfaces declaring properties of business objects that can be displayed on a map using the <see href="https://docs.devexpress.com/eXpressAppFramework/114776/analytics/maps-module">Maps Module</see></para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.IBaseMapsMarker.Latitude">
      <summary>
        <para>Gets the <see href="https://docs.devexpress.com/eXpressAppFramework/114776/analytics/maps-module">maps</see> marker latitude.</para>
      </summary>
      <value>A floating-point value specifying the marker latitude.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IBaseMapsMarker.Longitude">
      <summary>
        <para>Gets the <see href="https://docs.devexpress.com/eXpressAppFramework/114776/analytics/maps-module">maps</see> marker longitude.</para>
      </summary>
      <value>A floating-point value specifying the marker longitude.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IBaseMapsMarker.Title">
      <summary>
        <para>Gets the <see href="https://docs.devexpress.com/eXpressAppFramework/114776/analytics/maps-module">$maps</see> marker title.</para>
      </summary>
      <value>A string which is the marker title.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.IDashboardData">
      <summary>
        <para>Declares members of the persistent classes used by the <see href="https://docs.devexpress.com/eXpressAppFramework/117449/analytics/dashboards-module">Dashboards Module</see> to store dashboards in the application database.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.IDashboardData.Content">
      <summary>
        <para>Specifies the dashboard configuration defined in the Dashboard Designer.</para>
      </summary>
      <value>An XML-formatted string which specifies the dashboard configuration.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IDashboardData.SynchronizeTitle">
      <summary>
        <para>Specifies if it is required to update the <see cref="P:DevExpress.Persistent.Base.IDashboardData.Title">IDashboardData.Title</see> value with the dashboard title from the dashboard configuration (stored in the <see cref="P:DevExpress.Persistent.Base.IDashboardData.Content">IDashboardData.Content</see> property).</para>
      </summary>
      <value>true, if it is required to update the dashboard title; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IDashboardData.Title">
      <summary>
        <para>Specifies the dashboard title.</para>
      </summary>
      <value>A string specifying the dashboard title.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.IFileData">
      <summary>
        <para>Declares members implemented by the classes that can be represented in a UI using the FileDataPropertyEditor.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.IFileData.Clear">
      <summary>
        <para>Clears the file data.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.IFileData.FileName">
      <summary>
        <para>Specifies the file name.</para>
      </summary>
      <value>A string is the file name.</value>
    </member>
    <member name="M:DevExpress.Persistent.Base.IFileData.LoadFromStream(System.String,System.IO.Stream)">
      <summary>
        <para>Loads the file data from a stream.</para>
      </summary>
      <param name="fileName">A string that is the file name.</param>
      <param name="stream">A Stream that is the file content.</param>
    </member>
    <member name="M:DevExpress.Persistent.Base.IFileData.SaveToStream(System.IO.Stream)">
      <summary>
        <para>Saves the file stored within the current <see cref="T:DevExpress.Persistent.Base.IFileData"/> object to the specified stream.</para>
      </summary>
      <param name="stream">A Stream object to be used to save the file.</param>
    </member>
    <member name="P:DevExpress.Persistent.Base.IFileData.Size">
      <summary>
        <para>Gets the size of file stored within the current <see cref="T:DevExpress.Persistent.Base.IFileData"/> object.</para>
      </summary>
      <value>An integer that is the file size.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.IMapsMarker">
      <summary>
        <para>Declares properties of business objects that can be displayed on a map using the <see href="https://docs.devexpress.com/eXpressAppFramework/114776/analytics/maps-module">Maps Module</see>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Base.IPermissionPolicyRole">
      <summary>
        <para>Implemented by business classes specifying a security role that supports the Allow/Deny <see href="https://docs.devexpress.com/eXpressAppFramework/116172/data-security-and-safety/security-system/security-object-model/permission-policy">Permission Policies</see>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.IPermissionPolicyRole.CanEditModel">
      <summary>
        <para>Specifies whether users associated with the current role can use the <see href="https://docs.devexpress.com/eXpressAppFramework/112830/installation-upgrade-version-history/visual-studio-integration/model-editor">Model Editor</see>.</para>
      </summary>
      <value>true, if users associated with the current role can use the Model Editor; otherwise - false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IPermissionPolicyRole.IsAdministrative">
      <summary>
        <para>Specifies whether users associated with the current role are administrators.</para>
      </summary>
      <value>true, if users associated with the current role are administrators; otherwise - false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IPermissionPolicyRole.Name">
      <summary>
        <para>Specifies the name of the security role.</para>
      </summary>
      <value>A string which is the name of the security role.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IPermissionPolicyRole.PermissionPolicy">
      <summary>
        <para>Specifies the Security System behavior when there are no explicitly specified permissions for a specific type, object or member.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Base.SecurityPermissionPolicy"/> enumeration value specifying the behavior when there are no explicitly specified permissions.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IPermissionPolicyRole.TypePermissions">
      <summary>
        <para>Gets the list of objects which contain type permissions associated with the current <see cref="T:DevExpress.Persistent.Base.IPermissionPolicyRole"/>.</para>
      </summary>
      <value>An <see cref="T:System.Collections.Generic.IEnumerable`1">IEnumerable</see>&lt;DevExpress.Persistent.Base.IPermissionPolicyTypePermissionObject&gt; object which is the list of the PermissionPolicyTypePermissionObject objects which are type permissions associated with the current role.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.IPermissionPolicyRoleWithUsers">
      <summary>
        <para>Implemented by business classes specifying a security user that supports the Allow/Deny <see href="https://docs.devexpress.com/eXpressAppFramework/116172/data-security-and-safety/security-system/security-object-model/permission-policy">Permission Policies</see> and exposes a collection of security roles.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.IPermissionPolicyRoleWithUsers.Users">
      <summary>
        <para>A list of users associated with the current role.</para>
      </summary>
      <value>An IList&lt;<see cref="T:DevExpress.Persistent.Base.IPermissionPolicyUser"/>&gt; collection which is the list of users associated with the current role.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.IPermissionPolicyUser">
      <summary>
        <para>Implemented by business classes specifying a security user that supports the Allow/Deny <see href="https://docs.devexpress.com/eXpressAppFramework/116172/data-security-and-safety/security-system/security-object-model/permission-policy">Permission Policies</see>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.IPermissionPolicyUser.Roles">
      <summary>
        <para>A list of roles associated with the current user.</para>
      </summary>
      <value>An IList&lt;<see cref="T:DevExpress.Persistent.Base.IPermissionPolicyRole"/>&gt; collection which is the list of roles associated with the current user.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.ISupportFullName">
      <summary>
        <para>Declares the property used to store a full path to the file specified by the object which implements the <see cref="T:DevExpress.Persistent.Base.IFileData"/> interface.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.ISupportFullName.FullName">
      <summary>
        <para>Gets or sets a full path to the file specified by the object which implements the <see cref="T:DevExpress.Persistent.Base.IFileData"/> interface.</para>
      </summary>
      <value>A string that is a full path to the file.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.IVectorMapsMarker">
      <summary>
        <para>Declares properties of business objects that can be displayed as a bubble marker on a vector map using the <see href="https://docs.devexpress.com/eXpressAppFramework/114776/analytics/maps-module">Maps Module</see>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.IVectorMapsMarker.Tooltip">
      <summary>
        <para>Gets the gets the vector maps marker tooltip.</para>
      </summary>
      <value>A string which is the marker tooltip.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IVectorMapsMarker.Value">
      <summary>
        <para>Gets the vector maps marker value.</para>
      </summary>
      <value>A single-precision floating-point number specifying the marker value.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.IVectorMapsPieMarker">
      <summary>
        <para>Declares properties of business objects that can be displayed as a pie chart marker on a vector map using the <see href="https://docs.devexpress.com/eXpressAppFramework/114776/analytics/maps-module">Maps Module</see>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.IVectorMapsPieMarker.Tooltip">
      <summary>
        <para>Gets the gets the vector maps pie marker tooltip.</para>
      </summary>
      <value>A string which is the pie marker tooltip.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.IVectorMapsPieMarker.Values">
      <summary>
        <para>Gets the list of values for the pie marker slices.</para>
      </summary>
      <value>A list of slice values.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.NonCloneableAttribute">
      <summary>
        <para>Applied to a business class’ property. Specifies that the target property’s value cannot be cloned when cloning objects via the <see href="https://docs.devexpress.com/eXpressAppFramework/112835/document-management/clone-object-module">Clone Object Module</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.NonCloneableAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Base.NonCloneableAttribute"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Base.NotClonedInfoAttribute">
      <summary>
        <para>Applied to a business class. Specifies the business class’ string property, which can hold the property values that were not cloned.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.NotClonedInfoAttribute.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Base.NotClonedInfoAttribute"/> class with the specified <see cref="P:DevExpress.Persistent.Base.NotClonedInfoAttribute.MemberName">NotClonedInfoAttribute.MemberName</see> property’s value.</para>
      </summary>
      <param name="memberName">A string holding the name of the target business class’ property whose value cannot be cloned. This parameter value is assigned to the <see cref="P:DevExpress.Persistent.Base.NotClonedInfoAttribute.MemberName">NotClonedInfoAttribute.MemberName</see> property.</param>
    </member>
    <member name="P:DevExpress.Persistent.Base.NotClonedInfoAttribute.MemberName">
      <summary>
        <para>Specifies the business class’ string property which can hold the property values that were not cloned.</para>
      </summary>
      <value>A string holding the name of the business class’ string property which can hold the property values that were not cloned.</value>
    </member>
    <member name="N:DevExpress.Persistent.Base.ReportsV2">
      <summary>
        <para>Contains interfaces and classes that are specific to <see href="https://docs.devexpress.com/eXpressAppFramework/113593/shape-export-print-data/reports/data-sources-for-reports-v2">Data Sources for Reports V2</see>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Base.ReportsV2.CollectionDataSource">
      <summary>
        <para>The data source component that loads a collection of business objects via the <see cref="T:DevExpress.ExpressApp.IObjectSpace"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.ReportsV2.CollectionDataSource.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Base.ReportsV2.CollectionDataSource"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Base.ReportsV2.DataSourceBase">
      <summary>
        <para>The abstract base class for <see href="https://docs.devexpress.com/eXpressAppFramework/113593/shape-export-print-data/reports/data-sources-for-reports-v2">data sources used in Reports V2 module</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Base.ReportsV2.DataSourceBase"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.BeginInit">
      <summary>
        <para>Signals the <see cref="T:DevExpress.Persistent.Base.ReportsV2.DataSourceBase"/> that initialization is starting.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.CreateObjectSpace(System.Type,System.IServiceProvider)">
      <summary>
        <para>Creates an <see href="https://docs.devexpress.com/eXpressAppFramework/113707/data-manipulation-and-business-logic/object-space">Object Space</see>.</para>
      </summary>
      <param name="dataType">A <see cref="T:System.Type"/> object that specifies the business object type for which the Object Space is retrieved.</param>
      <param name="serviceProvider">An IServiceProvider object.</param>
      <returns>An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> object.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.Criteria">
      <summary>
        <para>The criteria used to filter data in a report.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object used to filter data.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.CriteriaPropertyDescriptorProvider">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.CriteriaString">
      <summary>
        <para>Specifies the string representation of the criteria used to filter data in a report.</para>
      </summary>
      <value>The string representation of the <see cref="P:DevExpress.ExpressApp.CollectionSourceBase.Criteria">CollectionSourceBase.Criteria</see> value.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.DataType">
      <summary>
        <para>Gets the business object type specified by the <see cref="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.ObjectTypeName">DataSourceBase.ObjectTypeName</see> property.</para>
      </summary>
      <value>A type of the business object used by <see cref="T:DevExpress.Persistent.Base.ReportsV2.DataSourceBase"/> to load data to the report.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.EnableAsyncLoading">
      <summary>
        <para>Specifies whether or not the asynchronous report data source loading is enabled.</para>
      </summary>
      <value>true, if the asynchronous report data source loading is enabled; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.EndInit">
      <summary>
        <para>Signals the <see cref="T:DevExpress.Persistent.Base.ReportsV2.DataSourceBase"/> that initialization is complete.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.IsDesignMode">
      <summary>
        <para>Gets a value that indicates whether the <see cref="T:DevExpress.Persistent.Base.ReportsV2.DataSourceBase"/> is currently in design mode.</para>
      </summary>
      <value>true, if the data source component is in design mode; otherwise - false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.IsDisposed">
      <summary>
        <para>Gets a value that indicates whether the <see cref="T:DevExpress.Persistent.Base.ReportsV2.DataSourceBase"/> is disposed</para>
      </summary>
      <value>true - if the data source is disposed; otherwise - false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.Name">
      <summary>
        <para>Specifies the name of the current <see cref="T:DevExpress.Persistent.Base.ReportsV2.DataSourceBase"/> object.</para>
      </summary>
      <value>A string that is the current data source name.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.ObjectType">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.ObjectTypeName">
      <summary>
        <para>Specifies the business object type name to be used by the <see cref="T:DevExpress.Persistent.Base.ReportsV2.DataSourceBase"/> to load data.</para>
      </summary>
      <value>A string which is the business object type name.</value>
    </member>
    <member name="M:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.SetCriteria(DevExpress.Data.Filtering.CriteriaOperator)">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.Sorting">
      <summary>
        <para>Specifies the sorting to be applied to data.</para>
      </summary>
      <value>A SortingCollection object that specifies the sorting to be applied to data.</value>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.TopReturnedRecords">
      <summary>
        <para>Specifies the maximum number of records to be retrieved from a data store.</para>
      </summary>
      <value>An integer value that specifies the maximum number of records to be retrieved from a data store.</value>
    </member>
    <member name="M:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.UpdateCriteriaWithReportParameters(System.Collections.Generic.IEnumerable{DevExpress.Data.IParameter})">
      <summary>
        <para>Adds report parameter values to the criteria string.</para>
      </summary>
      <param name="parameters">An IEnumerable&lt;<see cref="T:DevExpress.Data.IParameter"/>&gt; list of report parameters.</param>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.DataSourceBase.UseRemoteTypedListProvider">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Base.ReportsV2.ISupportCriteria">
      <summary>
        <para>Implemented by <see href="https://docs.devexpress.com/eXpressAppFramework/113593/shape-export-print-data/reports/data-sources-for-reports-v2">report data sources</see> to support <see href="https://docs.devexpress.com/eXpressAppFramework/113594/filtering/in-reports/data-filtering-in-reports">data filtering</see> in the <see href="https://docs.devexpress.com/eXpressAppFramework/113591/shape-export-print-data/reports/reports-v2-module-overview">Reports V2 Module</see></para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.ISupportCriteria.Criteria">
      <summary>
        <para>The criteria used to filter data in a report.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object used to filter data.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.ReportsV2.ISupportSorting">
      <summary>
        <para>Implemented by <see href="https://docs.devexpress.com/eXpressAppFramework/113593/shape-export-print-data/reports/data-sources-for-reports-v2">report data sources</see> to support <see href="https://docs.devexpress.com/eXpressAppFramework/113595/shape-export-print-data/reports/data-sorting-in-reports-v2">data sorting</see> in the <see href="https://docs.devexpress.com/eXpressAppFramework/113591/shape-export-print-data/reports/reports-v2-module-overview">Reports V2 Module</see></para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.ISupportSorting.Sorting">
      <summary>
        <para>Specifies the sorting to be applied to data.</para>
      </summary>
      <value>A SortingCollection object that specifies the sorting to be applied to data.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.ReportsV2.ViewDataSource">
      <summary>
        <para>The data source component that retrieves a list of data records (a data view) via the <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> without loading complete business classes. Values in each data record can be obtained from specific business class properties directly, or be evaluated by the database server using complex expressions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Base.ReportsV2.ViewDataSource.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Base.ReportsV2.ViewDataSource"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.ReportsV2.ViewDataSource.Properties">
      <summary>
        <para>Gets the list of fields to be loaded from the data store.</para>
      </summary>
      <value>A ViewPropertiesCollection object that specifies the list of fields to be loaded from the data store.</value>
    </member>
    <member name="M:DevExpress.Persistent.Base.ReportsV2.ViewDataSource.RefreshProperties">
      <summary>
        <para>Refreshes the <see cref="P:DevExpress.Persistent.Base.ReportsV2.ViewDataSource.Properties">ViewDataSource.Properties</see> list.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Persistent.Base.Security">
      <summary>
        <para>Contains interfaces related to the <see href="https://docs.devexpress.com/eXpressAppFramework/113366/data-security-and-safety/security-system">XAF Security System</see>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Base.Security.IAuthenticationActiveDirectoryUser">
      <summary>
        <para>Declares members implemented by a <see href="https://docs.devexpress.com/eXpressAppFramework/113366/data-security-and-safety/security-system">Security System</see> user class that is compatible with <see cref="T:DevExpress.ExpressApp.Security.AuthenticationActiveDirectory"/> authentication.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.Security.IAuthenticationActiveDirectoryUser.UserName">
      <summary>
        <para>Specifies the user’s login name.</para>
      </summary>
      <value>A string which is the user’s login name.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.Security.IAuthenticationStandardUser">
      <summary>
        <para>Declares members implemented by <see href="https://docs.devexpress.com/eXpressAppFramework/113366/data-security-and-safety/security-system">Security System</see> user classes that are compatible with the <see cref="T:DevExpress.ExpressApp.Security.AuthenticationStandard"/> authentication.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Base.Security.IAuthenticationStandardUser.ChangePasswordOnFirstLogon">
      <summary>
        <para>Specifies whether the user must change password on the next logon.</para>
      </summary>
      <value>true, if the user must change password on the next logon; otherwise - false.</value>
    </member>
    <member name="M:DevExpress.Persistent.Base.Security.IAuthenticationStandardUser.ComparePassword(System.String)">
      <summary>
        <para>Compares the current user’s stored encrypted password with its plain-text representation.</para>
      </summary>
      <param name="password">A string which is the plain-text password for comparison.</param>
      <returns>true if the passwords are identical, otherwise - false</returns>
    </member>
    <member name="M:DevExpress.Persistent.Base.Security.IAuthenticationStandardUser.SetPassword(System.String)">
      <summary>
        <para>Changes the user password.</para>
      </summary>
      <param name="password">A string which is a new password.</param>
    </member>
    <member name="P:DevExpress.Persistent.Base.Security.IAuthenticationStandardUser.UserName">
      <summary>
        <para>Specifies the user’s login name.</para>
      </summary>
      <value>A string which is the user’s login name.</value>
    </member>
    <member name="T:DevExpress.Persistent.Base.SecurityPermissionPolicy">
      <summary>
        <para>Contains values that specify the Security System behavior when there are no explicitly specified permissions for a specific type, object or member.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Base.SecurityPermissionPolicy.AllowAllByDefault">
      <summary>
        <para>Full access is granted by default.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Base.SecurityPermissionPolicy.DenyAllByDefault">
      <summary>
        <para>Access is denied by default.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Base.SecurityPermissionPolicy.ReadOnlyAllByDefault">
      <summary>
        <para>Read-only access is granted by default.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Base.SecurityPermissionState">
      <summary>
        <para>Contains values that specify if access is granted or denied.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Base.SecurityPermissionState.Allow">
      <summary>
        <para>Access is allowed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Base.SecurityPermissionState.Deny">
      <summary>
        <para>Access is denied.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Persistent.Validation">
      <summary>
        <para>Contains interfaces and classes used in the XAF‘s <see href="https://docs.devexpress.com/eXpressAppFramework/113684/validation-module">Validation Module</see> module.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.CodeRuleAttribute">
      <summary>
        <para>Applied to a validation rule. Specifies that the rule is intended for a particular business class and does not have a corresponding validation attribute.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.CodeRuleAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.CodeRuleAttribute"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.CodeRuleAttribute.CreateRule">
      <summary>
        <para>Intended for internal use.</para>
      </summary>
      <returns>An IRule object which represents a validation rule decorated with the CodeRule attribute.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.CodeRuleAttribute.SetDeclaringClass(System.Type)">
      <summary>
        <para>For internal use only.</para>
      </summary>
      <param name="declaringClass">A class to be validated.</param>
    </member>
    <member name="T:DevExpress.Persistent.Validation.ContextIdentifier">
      <summary>
        <para>Identifies a <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Context</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifier.#ctor(System.String)">
      <summary>
        <para>Creates an instance of the <see cref="T:DevExpress.Persistent.Validation.ContextIdentifier"/> class.</para>
      </summary>
      <param name="id">A string which is a validation context that the created ContextIdentifier will identify. This parameter value is assigned to the <see cref="P:DevExpress.Persistent.Validation.ContextIdentifier.Id">ContextIdentifier.Id</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifier.CompareTo(System.Object)">
      <summary>
        <para>Compares the current <see cref="T:DevExpress.Persistent.Validation.ContextIdentifier"/> with another ContextIdentifier.</para>
      </summary>
      <param name="obj">An object which can be cast to the ContextIdentifier type, to compare to the current context identifier.</param>
      <returns>An integer value indicating whether the current instance precedes, follows, or occurs in the same position in the sort order as the other object.</returns>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ContextIdentifier.Delete">
      <summary>
        <para>Provides access to a <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> collection which contains a single <see cref="F:DevExpress.Persistent.Validation.DefaultContexts.Delete">DefaultContexts.Delete</see> validation context identifier.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifier.Equals(DevExpress.Persistent.Validation.ContextIdentifier)">
      <summary>
        <para>Checks whether the current <see cref="T:DevExpress.Persistent.Validation.ContextIdentifier"/> is equal to the specified object of the same type.</para>
      </summary>
      <param name="obj">An object to compare to the current ContextIdentifier.</param>
      <returns>true if the specified object equals the current ContextIdentifier; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifier.Equals(System.Object)">
      <summary>
        <para>Checks whether the specified object is equal to the current <see cref="T:DevExpress.Persistent.Validation.ContextIdentifier"/>.</para>
      </summary>
      <param name="obj">An object that can be cast to the <see cref="T:DevExpress.Persistent.Validation.ContextIdentifier"/> type, to compare to the current ContextIdentifier.</param>
      <returns>true if the specified context identifier’s <see cref="P:DevExpress.Persistent.Validation.ContextIdentifier.Id">ContextIdentifier.Id</see> property value equals the current ContextIdentifier‘s Id property value; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifier.GetHashCode">
      <summary>
        <para>Serves as a hash function for the <see cref="T:DevExpress.Persistent.Validation.ContextIdentifier"/>.</para>
      </summary>
      <returns>An integer value representing a hash code for the current ContextIdentifier.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ContextIdentifier.Id">
      <summary>
        <para>Specifies the validation context identified by the current <see cref="T:DevExpress.Persistent.Validation.ContextIdentifier"/>.</para>
      </summary>
      <value>A string which is a validation context identified by the ContextIdentifier.</value>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ContextIdentifier.Save">
      <summary>
        <para>Provides access to a <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> collection which contains a single <see cref="F:DevExpress.Persistent.Validation.DefaultContexts.Save">DefaultContexts.Save</see> validation context identifier.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifier.ToString">
      <summary>
        <para>Returns a string representation of the <see cref="T:DevExpress.Persistent.Validation.ContextIdentifier"/>.</para>
      </summary>
      <returns>A string representing the ContextIdentifier.</returns>
    </member>
    <member name="T:DevExpress.Persistent.Validation.ContextIdentifiers">
      <summary>
        <para>Identifies <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Contexts</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifiers.#ctor(System.Collections.Generic.IList{System.String})">
      <summary>
        <para>Creates an instance of the <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> class.</para>
      </summary>
      <param name="identifiers">An IList&lt;String&gt; object, which is a list of validation contexts that will be identified by the created ContextIdentifiers.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifiers.#ctor(System.String)">
      <summary>
        <para>Creates an instance of the <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> class.</para>
      </summary>
      <param name="identifiers">A string containing validation contexts separated by the <see cref="F:DevExpress.Persistent.Validation.ContextIdentifiers.Separator">ContextIdentifiers.Separator</see> string, that will be identified by the created ContextIdentifiers.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifiers.Contains(System.String)">
      <summary>
        <para>Indicates whether the current <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> identifies a particular validation context.</para>
      </summary>
      <param name="item">A validation context which the current ContextIdentifiers presumably identifies.</param>
      <returns>true, if the current ContextIdentifiers identifies the item validation context; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ContextIdentifiers.Count">
      <summary>
        <para>Specifies the number of the validation contexts identified by the current <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/>.</para>
      </summary>
      <value>An integer value specifying the number of the validation contexts identified by the current ContextIdentifiers.</value>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifiers.Equals(DevExpress.Persistent.Validation.ContextIdentifiers)">
      <summary>
        <para>Checks whether the current <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> is equal to the specified object of the same type.</para>
      </summary>
      <param name="obj">An object to compare to the current ContextIdentifiers.</param>
      <returns>true if the specified object equals the current ContextIdentifiers; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifiers.Equals(System.Object)">
      <summary>
        <para>Checks whether the specified object is equal to the current <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/>.</para>
      </summary>
      <param name="obj">An object that can be cast to the <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> type, to compare to the current ContextIdentifiers.</param>
      <returns>true if the specified ContextIdentifiers identifies the same validation contexts and in the same order as the current ContextIdentifiers.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifiers.GetHashCode">
      <summary>
        <para>Serves as a hash function for the <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/>.</para>
      </summary>
      <returns>An integer value representing a hash code for the current ContextIdentifiers.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ContextIdentifiers.Item(System.Int32)">
      <summary>
        <para>Provides access to the validation context identified by the current <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/>, with a specified index.</para>
      </summary>
      <param name="index">A zero-based index of the required validation context.</param>
      <value>The validation context with the specified index in the current ContextIdentifiers list.</value>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ContextIdentifiers.ToString">
      <summary>
        <para>Returns a string representation of the <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/>.</para>
      </summary>
      <returns>A string representing the ContextIdentifiers.</returns>
    </member>
    <member name="T:DevExpress.Persistent.Validation.CriteriaEvaluationBehavior">
      <summary>
        <para>Contains values that specify how filter criteria are evaluated within a transaction.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.CriteriaEvaluationBehavior.BeforeTransaction">
      <summary>
        <para>Only objects in the database are considered.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.CriteriaEvaluationBehavior.InTransaction">
      <summary>
        <para>Objects in the database and those that are currently retrieved from it are considered.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.CustomIsEmptyValueEventArgs">
      <summary>
        <para>Arguments passed to the <see cref="E:DevExpress.Persistent.Validation.RuleSet.CustomIsEmptyValue">RuleSet.CustomIsEmptyValue</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.CustomIsEmptyValueEventArgs.#ctor(System.Object,System.String,System.Object,System.IServiceProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.CustomIsEmptyValueEventArgs"/> class with specified settings.</para>
      </summary>
      <param name="targetObject">An object which is the validation rule’s target object.</param>
      <param name="propertyName">A string which is the name of the validation rule’s target property.</param>
      <param name="propertyValue">An object which is the validation rule’s target property value.</param>
      <param name="serviceProvider">The <see cref="T:System.IServiceProvider">IServiceProvider</see> used to access the application’s services.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.CustomIsEmptyValueEventArgs.#ctor(System.Object,System.String,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.CustomIsEmptyValueEventArgs"/> class.</para>
      </summary>
      <param name="targetObject">An object which is the validation rule’s target object.</param>
      <param name="propertyName">A string which is the name of the validation rule’s target property.</param>
      <param name="propertyValue">An object which is the validation rule’s target property value.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomIsEmptyValueEventArgs.IsEmpty">
      <summary>
        <para>Specifies whether or not the validation rule’s target property value is considered to be empty.</para>
      </summary>
      <value>true, if the validation rule’s target property value is considered to be empty; otherwise - false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomIsEmptyValueEventArgs.PropertyName">
      <summary>
        <para>Gets the name of the validation rule’s target property.</para>
      </summary>
      <value>A string which is the name of the validation rule’s target property.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomIsEmptyValueEventArgs.PropertyValue">
      <summary>
        <para>Gets the validation rule’s target property value.</para>
      </summary>
      <value>An object which is the validation rule’s target property value.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomIsEmptyValueEventArgs.ServiceProvider">
      <summary>
        <para>Gets the <see cref="T:System.IServiceProvider">IServiceProvider</see> used to access the application’s services.</para>
      </summary>
      <value>An object that implements the IServiceProvider interface.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomIsEmptyValueEventArgs.TargetObject">
      <summary>
        <para>Gets the validation rule’s target object.</para>
      </summary>
      <value>An object which is the validation rule’s target object.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.CustomNeedToValidateRuleEventArgs">
      <summary>
        <para>Arguments passed to the <see cref="E:DevExpress.Persistent.Validation.RuleSet.CustomNeedToValidateRule">RuleSet.CustomNeedToValidateRule</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.CustomNeedToValidateRuleEventArgs.#ctor(DevExpress.Persistent.Validation.IRule,System.Object,System.String,DevExpress.ExpressApp.IObjectSpace)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.CustomNeedToValidateRuleEventArgs"/> class.</para>
      </summary>
      <param name="rule">The <see cref="T:DevExpress.Persistent.Validation.IRule"/> object which represents a validation rule that is going to be checked.</param>
      <param name="target">An object that is going to be validated.</param>
      <param name="contextId">A validation context of the rule. Default contexts are stored in the <see cref="T:DevExpress.Persistent.Validation.DefaultContexts"/> enumeration.</param>
      <param name="objectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by a validation target.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomNeedToValidateRuleEventArgs.ContextId">
      <summary>
        <para>A validation context of the rule.</para>
      </summary>
      <value>Default contexts are listed in the <see cref="T:DevExpress.Persistent.Validation.DefaultContexts"/> enumeration.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomNeedToValidateRuleEventArgs.NeedToValidateRule">
      <summary>
        <para>Specifies whether the rule will be validated or not.</para>
      </summary>
      <value>If true, the rule will be validated; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomNeedToValidateRuleEventArgs.ObjectSpace">
      <summary>
        <para>An Object Space of a validation target.</para>
      </summary>
      <value>The <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> object used by the validation rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomNeedToValidateRuleEventArgs.Reason">
      <summary>
        <para>The reason for performing or rejecting the validation.</para>
      </summary>
      <value>A natural language text that represents the reason to validate the target or not.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomNeedToValidateRuleEventArgs.Rule">
      <summary>
        <para>A rule to be checked on a <see cref="P:DevExpress.Persistent.Validation.CustomNeedToValidateRuleEventArgs.Target">CustomNeedToValidateRuleEventArgs.Target</see>.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Persistent.Validation.IRule"/> object to be validated.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomNeedToValidateRuleEventArgs.Target">
      <summary>
        <para>A target object of validation.</para>
      </summary>
      <value>An object that is going to be validated.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.CustomValidateRuleEventArgs">
      <summary>
        <para>Arguments passed to the <see cref="E:DevExpress.Persistent.Validation.RuleSet.CustomValidateRule">RuleSet.CustomValidateRule</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.CustomValidateRuleEventArgs.#ctor(DevExpress.Persistent.Validation.IRule,System.Object,DevExpress.ExpressApp.IObjectSpace)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.CustomValidateRuleEventArgs"/> class.</para>
      </summary>
      <param name="rule">The <see cref="T:DevExpress.Persistent.Validation.IRule"/> object that represents a rule to be validated.</param>
      <param name="target">A target object of validation.</param>
      <param name="objectSpace">The <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by a validation target.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomValidateRuleEventArgs.ObjectSpace">
      <summary>
        <para>An Object Space of a validation target.</para>
      </summary>
      <value>The <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> object used by the validation rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomValidateRuleEventArgs.Rule">
      <summary>
        <para>A rule to be checked on a <see cref="P:DevExpress.Persistent.Validation.CustomValidateRuleEventArgs.Target">CustomValidateRuleEventArgs.Target</see>.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Persistent.Validation.IRule"/> object to be validated.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomValidateRuleEventArgs.RuleValidationResult">
      <summary>
        <para>The result of checking the <see cref="P:DevExpress.Persistent.Validation.CustomValidateRuleEventArgs.Rule">CustomValidateRuleEventArgs.Rule</see> on the <see cref="P:DevExpress.Persistent.Validation.CustomValidateRuleEventArgs.Target">CustomValidateRuleEventArgs.Target</see>.</para>
      </summary>
      <value>An editable validation result.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.CustomValidateRuleEventArgs.Target">
      <summary>
        <para>An object on which to check the <see cref="P:DevExpress.Persistent.Validation.CustomValidateRuleEventArgs.Rule">CustomValidateRuleEventArgs.Rule</see>.</para>
      </summary>
      <value>A target object for validation.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.DefaultContexts">
      <summary>
        <para>Contains values representing the default <see href="https://docs.devexpress.com/eXpressAppFramework/113685/validation/validation-contexts">Validation Contexts</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.DefaultContexts.Delete">
      <summary>
        <para>Specifies that the Validation Rule will be checked when deleting the required object.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.DefaultContexts.Save">
      <summary>
        <para>Specifies that the Validation Rule will be checked each time the required object (or its properties) is saved to a database.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.GenerateMessageTemplatesModelAttribute">
      <summary>
        <para>Applied to interfaces derived from <see cref="T:DevExpress.Persistent.Validation.IRuleBaseProperties"/>. Specifies that message templates for string properties whose names start with “MessageTemplate” are generated automatically.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.GenerateMessageTemplatesModelAttribute.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.GenerateMessageTemplatesModelAttribute"/> class.</para>
      </summary>
      <param name="messageTemplatePropertyName">A string which specifies the name of the validation rule.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.GenerateMessageTemplatesModelAttribute.MessageTemplatePropertyName">
      <summary>
        <para>Specifies the name of the validation rule.</para>
      </summary>
      <value>A string which specifies the name of the validation rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.IEmptyCheckable">
      <summary>
        <para>Declares the <see cref="P:DevExpress.Persistent.Validation.IEmptyCheckable.IsEmpty">IEmptyCheckable.IsEmpty</see> property to be implemented by classes to support validation via the <see cref="T:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IEmptyCheckable.IsEmpty">
      <summary>
        <para>Gets if the <see cref="T:DevExpress.Persistent.Validation.IEmptyCheckable"/> value is considered to be empty when validating via the <see cref="T:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute"/>.</para>
      </summary>
      <value>true, when the value is empty; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.IModelValidationDefaultErrorMessageTemplates">
      <summary>
        <para>The ErrorMessageTemplates node defines the default templates for the messages displayed when validation rules are broken.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.IRule">
      <summary>
        <para>Declares members implemented by <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRule.Id">
      <summary>
        <para>Returns the identifier of the current rule.</para>
      </summary>
      <value>A string value representing the current rule’s identifier.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRule.Properties">
      <summary>
        <para>Represents the Validation Rule’s properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Persistent.Validation.IRuleBaseProperties"/> object which represents the Validation Rule’s properties exported to the Application Model.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRule.UsedProperties">
      <summary>
        <para>Specifies the names of the properties to be highlighted when the current <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A ReadOnlyCollection&lt;String&gt; object representing a list of the property names that must be highlighted as invalid when the Validation Rule is broken.</value>
    </member>
    <member name="M:DevExpress.Persistent.Validation.IRule.Validate(System.Object)">
      <summary>
        <para>Checks that a particular object satisfies the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <param name="target">The object to check.</param>
      <returns>A <see cref="T:DevExpress.Persistent.Validation.RuleValidationResult"/> object which represents the results of checking the specified object via the Validation Rule.</returns>
    </member>
    <member name="T:DevExpress.Persistent.Validation.IRuleBaseProperties">
      <summary>
        <para>Declares members implemented by classes which represent <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleBaseProperties.CustomMessageTemplate">
      <summary>
        <para>Specifies custom informational text displayed in a validation error message for a broken <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> instead of the default message templates specified in the MessageTemplate… properties.</para>
      </summary>
      <value>Custom informational text displayed in a validation error message for a broken Validation Rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleBaseProperties.Id">
      <summary>
        <para>Specifies the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> identifier.</para>
      </summary>
      <value>A string holding the Validation Rule identifier.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleBaseProperties.InvertResult">
      <summary>
        <para>Specifies whether the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is applied directly.</para>
      </summary>
      <value>true, if an inverted Validation Rule will be checked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleBaseProperties.MessageTemplateSkipNullOrEmptyValues">
      <summary>
        <para>Specifies the template for the message displayed when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> was not checked because the target properties are not specified.</para>
      </summary>
      <value>A string holding the template for the message displayed when the Validation Rule was not checked because the target properties are not specified.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleBaseProperties.MessageTemplateTargetDoesNotSatisfyTargetCriteria">
      <summary>
        <para>Specifies the template for the message displayed when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> was not checked because the criteria specified by the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetCriteria">RuleBaseProperties.TargetCriteria</see> property is not satisfied by the validated object.</para>
      </summary>
      <value>Specifies the template for the message displayed when the Validation Rule was not checked because the criteria specified by the TargetCriteria property is not satisfied by the validated object.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleBaseProperties.Name">
      <summary>
        <para>Specifies the name of the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>A string holding the name of the Validation Rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleBaseProperties.ResultType">
      <summary>
        <para>Specifies the application behavior when the rule is broken.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.ValidationResultType"/> enumeration value that specifies the application behavior when the rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleBaseProperties.SkipNullOrEmptyValues">
      <summary>
        <para>Specifies whether the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is checked for the properties that are set to null (Nothing in VB), an empty string (for string type properties) or a minimal date (for DateTime type properties).</para>
      </summary>
      <value>true, if the Validation Rule is not checked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleBaseProperties.TargetContextIDs">
      <summary>
        <para>Specifies the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Contexts</see> for checking the current Validation Rule.</para>
      </summary>
      <value>A string representing a list of identifiers of the contexts when the current rule should be checked.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleBaseProperties.TargetCriteria">
      <summary>
        <para>Specifies the criteria that must be satisfied by the validated object to allow checking the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>A string that represents a criteria that must be satisfied to check the Validation Rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleBaseProperties.TargetType">
      <summary>
        <para>Specifies the type of the object for which the Validation Rule will be checked.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> object which represents the type of the object for which the Validation Rule will be checked.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.IRuleCollectionPropertyProperties">
      <summary>
        <para>Declares members implemented by classes which represent properties of <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see> that support <see href="https://docs.devexpress.com/eXpressAppFramework/113217/validation/collection-validation">collection validation</see>, exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleCollectionPropertyProperties.MessageTemplateCollectionValidationMessageSuffix">
      <summary>
        <para>Specifies the additional text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> applied to a collection property is broken.</para>
      </summary>
      <value>A string specifying the additional text that will be displayed in the Validation Error window when the Rule applied to a collection property is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleCollectionPropertyProperties.MessageTemplateTargetDoesNotSatisfyCollectionCriteria">
      <summary>
        <para>Specifies the message displayed when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> was not checked because a validated object is not an element of the target collection.</para>
      </summary>
      <value>A string specifying the message displayed when the Rule was not checked because a validated object is not an element of the target collection.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleCollectionPropertyProperties.TargetCollectionOwnerType">
      <summary>
        <para>If the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is applied to a collection property, specifies the type in which the collection property is declared.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> object specifying the type in which the collection property is declared.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleCollectionPropertyProperties.TargetCollectionPropertyName">
      <summary>
        <para>Specifies the collection property for which the current <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> will be checked.</para>
      </summary>
      <value>A string specifying the collection property for which the current Rule will be checked.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.IRuleSource">
      <summary>
        <para>Declares members that custom <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> Sources implement.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.IRuleSource.CreateRules">
      <summary>
        <para>Instantiates custom <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>.</para>
      </summary>
      <returns>An ICollection&lt;<see cref="T:DevExpress.Persistent.Validation.IRule"/>&gt; object, which represents custom Validation Rules.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleSource.Name">
      <summary>
        <para>Returns the name of the current <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> Source.</para>
      </summary>
      <value>A string representing the name of the current Validation Rule Source.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.IRuleSupportsCollectionAggregatesProperties">
      <summary>
        <para>Declares members implemented by classes which represent properties of <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see> that support <see href="https://docs.devexpress.com/eXpressAppFramework/113217/validation/collection-validation">collection validation using aggregate funcitons</see>, exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.IRuleSupportsCollectionAggregatesProperties.TargetCollectionAggregate">
      <summary>
        <para>Specifies the aggregate function to be checked by the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>, if a Rule is applied to a collection property.</para>
      </summary>
      <value>A Nullable&lt;Aggregate&gt; object specifying the aggregate function to be checked by the Validation Rule, if a Rule is applied to a collection property.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.ParametersMode">
      <summary>
        <para>Contains values specifying how criteria string parameters used in <see cref="T:DevExpress.Persistent.Validation.RuleRangeAttribute"/> and <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/> constructors are treated.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ParametersMode.Expression">
      <summary>
        <para>A criteria string is treated as an expression. So, you can use the <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">Criteria Language Syntax</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ParametersMode.Value">
      <summary>
        <para>A criteria string is treated as a string.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleBase">
      <summary>
        <para>Serves as the base class for <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleBase"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleBase.#ctor(DevExpress.Persistent.Validation.IRuleBaseProperties)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleBase"/> class and initializes the <see cref="P:DevExpress.Persistent.Validation.RuleBase.Properties">RuleBase.Properties</see> property.</para>
      </summary>
      <param name="properties">An <see cref="T:DevExpress.Persistent.Validation.IRuleBaseProperties"/> object whose property values are used to initialize <see cref="P:DevExpress.Persistent.Validation.RuleBase.Properties">RuleBase.Properties</see>.</param>
    </member>
    <member name="E:DevExpress.Persistent.Validation.RuleBase.CustomFormatValidationMessage">
      <summary>
        <para>Occurs when the validation result message is being formatted.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBase.DefaultMessageTemplateCollectionValidationMessageSuffix">
      <summary>
        <para>Specifies the default additional text displayed when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> applied to a collection property is broken.</para>
      </summary>
      <value>A string representing the default additional text displayed when the Validation Rule applied to a collection property is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBase.DefaultMessageTemplateSkipNullOrEmptyValues">
      <summary>
        <para>Specifies the default template for the message displayed when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> was not checked because one of the target property values is null or empty.</para>
      </summary>
      <value>A string holding the default template for the message displayed when the Validation Rule was not checked because one of the target property values is null or empty.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBase.DefaultMessageTemplateTargetDoesNotSatisfyCollectionCriteria">
      <summary>
        <para>Specifies the default template for the message displayed when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> was not checked because the validated object is not an element of the target collection.</para>
      </summary>
      <value>A string holding the default template for the message displayed when the Validation Rule was not checked because the validated object is not an element of the target collection.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBase.DefaultMessageTemplateTargetDoesNotSatisfyTargetCriteria">
      <summary>
        <para>Specifies the default template for the message displayed when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> was not checked because the criteria specified by the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetCriteria">RuleBaseProperties.TargetCriteria</see> property is not satisfied by the validated object.</para>
      </summary>
      <value>A string holding the default template for the message displayed when the Validation Rule was not checked because the criteria specified by the RuleBaseProperties.TargetCriteria property is not satisfied by the validated object.</value>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleBase.GetValidationResultMessage(System.String,DevExpress.Persistent.Validation.RuleBase,System.Object)">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBase.Id">
      <summary>
        <para>Returns the identifier of the current <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>A string value representing the current Rule’s identifier.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBase.Properties">
      <summary>
        <para>Represents the Validation Rule’s properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Persistent.Validation.IRuleBaseProperties"/> object which represents the Validation Rule’s properties exported to the Application Model.</value>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesCustomMessageTemplate">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.CustomMessageTemplate">RuleBaseProperties.CustomMessageTemplate</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesId">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.Id">RuleBaseProperties.Id</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesInvertResult">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.InvertResult">RuleBaseProperties.InvertResult</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesMessageTemplateCollectionValidationMessageSuffix">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.MessageTemplateCollectionValidationMessageSuffix">RuleBaseProperties.MessageTemplateCollectionValidationMessageSuffix</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesMessageTemplateSkipNullOrEmptyValues">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.MessageTemplateSkipNullOrEmptyValues">RuleBaseProperties.MessageTemplateSkipNullOrEmptyValues</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesMessageTemplateTargetDoesNotSatisfyCollectionCriteria">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.MessageTemplateTargetDoesNotSatisfyCollectionCriteria">RuleBaseProperties.MessageTemplateTargetDoesNotSatisfyCollectionCriteria</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesMessageTemplateTargetDoesNotSatisfyTargetCriteria">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.MessageTemplateTargetDoesNotSatisfyTargetCriteria">RuleBaseProperties.MessageTemplateTargetDoesNotSatisfyTargetCriteria</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesName">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.Name">RuleBaseProperties.Name</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesSkipNullOrEmptyValues">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.SkipNullOrEmptyValues">RuleBaseProperties.SkipNullOrEmptyValues</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesTargetContextIDs">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetContextIDs">RuleBaseProperties.TargetContextIDs</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesTargetCriteria">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetCriteria">RuleBaseProperties.TargetCriteria</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleBase.PropertiesTargetType">
      <summary>
        <para>Holds the name of the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetType">RuleBaseProperties.TargetType</see> property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBase.PropertiesType">
      <summary>
        <para>Specifies the type of the Validation Rule Properties class used by the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> object specifying the type of the Validation Rule Properties class used by the Validation Rule. By default, this property returns the <see cref="T:DevExpress.Persistent.Validation.RuleBaseProperties"/> type.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBase.TargetObject">
      <summary>
        <para>Specifies the object currently being validated by the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>An object currently being validated by the Validation Rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBase.UsedProperties">
      <summary>
        <para>Specifies the names of the properties to be highlighted when the current <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A ReadOnlyCollection&lt;String&gt; object representing a list of property names that must be highlighted as invalid when the Validation Rule is broken.</value>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleBase.Validate(System.Object)">
      <summary>
        <para>Checks that a particular object satisfies the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <param name="target">The object to check.</param>
      <returns>A <see cref="T:DevExpress.Persistent.Validation.RuleValidationResult"/> object which represents the results of checking the specified object via the Validation Rule.</returns>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleBaseAttribute">
      <summary>
        <para>Represents a base class for <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">validation system</see> attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleBaseAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleBaseAttribute"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">
      <summary>
        <para>Specifies informational text that is to be added to the exception message when the current <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string value that contains information on the current rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseAttribute.InvertResult">
      <summary>
        <para>Specifies whether the current rule should be inverted, to be checked.</para>
      </summary>
      <value>true, if an inverted rule will be checked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseAttribute.Name">
      <summary>
        <para>Specifies a name of the rule created using the current attribute.</para>
      </summary>
      <value>A string representing a name of the rule to be created.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseAttribute.ResultType">
      <summary>
        <para>Specifies the application behavior when the rule is broken.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.ValidationResultType"/> enumeration value that specifies the application behavior when the rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseAttribute.SkipNullOrEmptyValues">
      <summary>
        <para>Specifies whether the current rule is checked for the properties that are set to null (Nothing in VB), an empty string (for string type propeties) or a minimal date (for DateTime type properties).</para>
      </summary>
      <value>true if null-like values are not checked by the current rule; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">
      <summary>
        <para>Specifies a context for checking the current Rule.</para>
      </summary>
      <value>A string representing a list of identifiers of the contexts when the current rule should be checked.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetCriteria">
      <summary>
        <para>Specifies a criterion that must be satisfied by the validated object or property to check rules.</para>
      </summary>
      <value>A string that represents a criterion that must be satisfied to check validation rules.</value>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleBaseAttribute.TryAddTo(DevExpress.ExpressApp.DC.IBaseInfo,System.String,System.Func{System.String,DevExpress.Persistent.Validation.RuleBaseAttribute},System.Boolean)">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleBaseProperties">
      <summary>
        <para>Serves as the base class for classes which represent <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleBaseProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleBaseProperties"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleBaseProperties.Clone">
      <summary>
        <para>Creates a shallow copy of the current <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> Properties.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Persistent.Validation.RuleBaseProperties"/> object which represents a shallow copy of the current Validation Rule Properties.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.CustomMessageTemplate">
      <summary>
        <para>Specifies custom informational text displayed in a validation error message for a broken <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> instead of the default message templates specified in the MessageTemplate… properties.</para>
      </summary>
      <value>Custom informational text that is displayed in a validation error message for a broken Validation Rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.Id">
      <summary>
        <para>Specifies the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> identifier.</para>
      </summary>
      <value>A string holding the Validation Rule identifier.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.InvertResult">
      <summary>
        <para>Specifies whether the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is applied directly.</para>
      </summary>
      <value>true, if an inverted Validation Rule will be checked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.MessageTemplateCollectionValidationMessageSuffix">
      <summary>
        <para>Specifies the additional text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> applied to a collection property is broken.</para>
      </summary>
      <value>A string holding the additional text that will be displayed in the Validation Error window when the Validation Rule applied to a collection property is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.MessageTemplateSkipNullOrEmptyValues">
      <summary>
        <para>Specifies the template for the message displayed when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> was not checked because the target properties are not specified.</para>
      </summary>
      <value>A string holding the template for the message displayed when the Validation Rule was not checked because the target properties are not specified.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.MessageTemplateTargetDoesNotSatisfyCollectionCriteria">
      <summary>
        <para>Specifies the template for the message displayed when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> was not checked because the validated object is not an element of the target collection.</para>
      </summary>
      <value>A string holding the template for the message displayed when the Validation Rule was not checked because the validated object is not an element of the target collection.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.MessageTemplateTargetDoesNotSatisfyTargetCriteria">
      <summary>
        <para>Specifies the template for the message displayed when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> was not checked because the criteria specified by the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetCriteria">RuleBaseProperties.TargetCriteria</see> property is not satisfied by the validated object.</para>
      </summary>
      <value>A string holding the template for the message displayed when the Validation Rule was not checked because the criteria specified by the TargetCriteria property is not satisfied by the validated object.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.Name">
      <summary>
        <para>Specifies the name of the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>A string holding the name of the Validation Rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.ResultType">
      <summary>
        <para>Specifies the application behavior when the rule is broken.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.ValidationResultType"/> enumeration value that specifies the application behavior when the rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.SkipNullOrEmptyValues">
      <summary>
        <para>Specifies whether the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is checked for the properties that are set to null (Nothing in VB), an empty string (for string type properties) or a minimal date (for DateTime type properties).</para>
      </summary>
      <value>true, if the Validation Rule is not checked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetCollectionOwnerType">
      <summary>
        <para>Specifies the type in which the collection property is declared, if the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is applied to a collection property.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> object specifying the type in which the collection property is declared, if the Validation Rule is applied to a collection property.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetCollectionPropertyName">
      <summary>
        <para>Specifies the collection property for which the current <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> will be checked.</para>
      </summary>
      <value>A string holding the collection property name for which the Validation Rule will be checked.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetContextIDs">
      <summary>
        <para>Specifies the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Contexts</see> for checking the current Validation Rule.</para>
      </summary>
      <value>A string representing a list of identifiers of the contexts when the current rule should be checked.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetCriteria">
      <summary>
        <para>Specifies the criteria that must be satisfied by the validated object to allow checking the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>A string that represents a criteria that must be satisfied to check the Validation Rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetType">
      <summary>
        <para>Specifies the type of the object for which the Validation Rule will be checked.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> object which represents the type of the object for which the Validation Rule will be checked.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute">
      <summary>
        <para>Defines a validation rule that demands the target properties’ values combination be unique.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute.#ctor(DevExpress.Persistent.Validation.DefaultContexts,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute"/> class.</para>
      </summary>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="targetProperties">A string holding a comma-separated list of property names that represent the properties which must satisfy the Validation Rule.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="targetProperties">A string specifying the properties whose combination must be unique. The property names can be separated by the comma, semicolon and whitespace characters.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="targetProperties">A string specifying the properties whose combination must be unique. The property names can be separated by the comma, semicolon and whitespace characters.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute.#ctor(System.String,System.String,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="targetProperties">A string specifying the properties whose combination must be unique. The property names can be separated by the comma, semicolon and whitespace characters.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute.#ctor(System.String,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="targetProperties">A string specifying the properties whose combination must be unique. The property names can be separated by the comma, semicolon and whitespace characters.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute"/> class.</para>
      </summary>
      <param name="targetProperties">A string specifying the properties whose combination must be unique. The property names can be separated by the comma, semicolon and whitespace characters.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute.CriteriaEvaluationBehavior">
      <summary>
        <para>Specifies whether to look for objects that are currently retrieved from the database, in addition to the objects in the database itself.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.CriteriaEvaluationBehavior"/> enumeration value that specifies how the required objects are searched.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute.FoundObjectMessageFormat">
      <summary>
        <para>Gets or sets the format for specifying information on found objects in the validation error message.</para>
      </summary>
      <value>A string value that can include one variable. This variable will be set to the key property value of the found object.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute.FoundObjectMessagesSeparator">
      <summary>
        <para>Specifies a separator for the object list written in the error message for the current rule.</para>
      </summary>
      <value>A string value that represents a separator.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueAttribute.MessageTemplateCombinationOfPropertiesMustBeUnique">
      <summary>
        <para>Specifies the text to be written in the Validation Error window when the current rule is broken.</para>
      </summary>
      <value>A string value representing the text to be written to the Validation Error window when a rule is broken.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueProperties">
      <summary>
        <para>Represent the RuleCombinationOfPropertiesIsUnique <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueProperties.MessageTemplateCombinationOfPropertiesMustBeUnique">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleCombinationOfPropertiesIsUniqueProperties.TargetProperties">
      <summary>
        <para>Specifies the properties which must satisfy the current <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>A string holding a comma-separated list of property names that represent the properties which must satisfy the Validation Rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleCriteriaAttribute">
      <summary>
        <para>Defines a validation rule that demands an object of the target type satisfy a specified criterion.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCriteriaAttribute.#ctor(DevExpress.Persistent.Validation.DefaultContexts,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCriteriaAttribute"/> class.</para>
      </summary>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="criteria">A string value representing a criteria that must be satisfied by the target class’ objects. To learn how to set a criteria, refer to the <see href="https://docs.devexpress.com/eXpressAppFramework/113052/filtering/in-list-view/ways-to-build-criteria">Ways to Build Criteria</see> topic.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCriteriaAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCriteriaAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="criteria">A string value representing a criteria that must be satisfied by target class’ objects. To learn how to set criteria, refer to the <see href="https://docs.devexpress.com/eXpressAppFramework/113052/filtering/in-list-view/ways-to-build-criteria">Ways to Build Criteria</see> topic.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCriteriaAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCriteriaAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will be generated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="criteria">A string value representing a criteria that must be satisfied by target class’ objects. To learn how to set a criteria, refer to the <see href="https://docs.devexpress.com/eXpressAppFramework/113052/filtering/in-list-view/ways-to-build-criteria">Ways to Build Criteria</see> topic.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCriteriaAttribute.#ctor(System.String,System.String,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCriteriaAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="criteria">A string value representing a criteria that must be satisfied by target class’ objects. To learn how to set a criteria, refer to the <see href="https://docs.devexpress.com/eXpressAppFramework/113052/filtering/in-list-view/ways-to-build-criteria">Ways to Build Criteria</see> topic.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCriteriaAttribute.#ctor(System.String,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCriteriaAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="criteria">A string value representing a criteria that must be satisfied by target class’ objects. To learn how to set a criteria, refer to the <see href="https://docs.devexpress.com/eXpressAppFramework/113052/filtering/in-list-view/ways-to-build-criteria">Ways to Build Criteria</see> topic.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCriteriaAttribute.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCriteriaAttribute"/> class.</para>
      </summary>
      <param name="criteria">A string value representing a criteria that must be satisfied by target class’ objects. To learn how to set criteria, refer to the <see href="https://docs.devexpress.com/eXpressAppFramework/113052/filtering/in-list-view/ways-to-build-criteria">Ways to Build Criteria</see> topic.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleCriteriaAttribute.UsedProperties">
      <summary>
        <para>Specifies the names of the properties to be highlighted when the rule generated from the current attribute is broken.</para>
      </summary>
      <value>A string holding a comma-separated list of property names that represent the properties to be highlighted when the Validation Rule is broken.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleCriteriaProperties">
      <summary>
        <para>Represent the RuleCriteria <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleCriteriaProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleCriteriaProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleCriteriaProperties.Criteria">
      <summary>
        <para>Specifies the criteria that must be satisfied by the objects of the type set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetType">RuleBaseProperties.TargetType</see> property.</para>
      </summary>
      <value>A string representing the criteria that must be satisfied by the objects of the type set for the TargetType property.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleCriteriaProperties.MessageTemplateMustSatisfyCriteria">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleCriteriaProperties.UsedProperties">
      <summary>
        <para>Specifies the names of the properties to be highlighted when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding a comma-separated list of property names that represent the properties to be highlighted when the Validation Rule is broken.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute">
      <summary>
        <para>Defines a validation rule that demands a true value for the target public non-persistent Boolean property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute.#ctor(DevExpress.Persistent.Validation.DefaultContexts)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute"/> class.</para>
      </summary>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute.#ctor(System.String,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute.#ctor(System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute.TargetPropertyName">
      <summary>
        <para>Specifies the target collection’s element property that must be checked by the current rule.</para>
      </summary>
      <value>A string holding the name of the target collection’s element property that must be checked by the current rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleFromBoolPropertyAttribute.UsedProperties">
      <summary>
        <para>Specifies the names of the properties to be highlighted when the rule generated from the current attribute is broken.</para>
      </summary>
      <value>A string value that lists the names of the properties that must be highlighted as invalid when the rule is broken. The names must be separated by a comma.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleFromBoolPropertyProperties">
      <summary>
        <para>Represent the RuleFromBoolProperty <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleFromBoolPropertyProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleFromBoolPropertyProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleFromBoolPropertyProperties.MessageTemplateMustBeTrue">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleFromBoolPropertyProperties.UsedProperties">
      <summary>
        <para>Specifies the names of the properties to be highlighted when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding a comma-separated list of property names that represent the properties to be highlighted when the Validation Rule is broken.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleIsReferencedAttribute">
      <summary>
        <para>Defines a validation rule demanding that an object should be referenced in objects of a specified type.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleIsReferencedAttribute.#ctor(DevExpress.Persistent.Validation.DefaultContexts,System.Type,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleIsReferencedAttribute"/> class.</para>
      </summary>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="looksFor">The type of the objects to be searched for.</param>
      <param name="referencePropertyName">A string value representing the name of the property that can be set to the target object. This property must belong to the class specified by the looksFor parameter.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleIsReferencedAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.Type,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleIsReferencedAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="looksFor">A type of the objects to be tested.</param>
      <param name="referencePropertyName">A string value representing the name of the property that can be set to the target object. This property must belong to the class specified by the looksFor parameter.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleIsReferencedAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.Type,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleIsReferencedAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="looksFor">The type of the objects to be tested.</param>
      <param name="referencePropertyName">A string value representing the name of the property that can be set to the target object. This property must belong to the class specified by the looksFor parameter.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleIsReferencedAttribute.#ctor(System.String,System.String,System.Type,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleIsReferencedAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="looksFor">A type of the objects to be searched for.</param>
      <param name="referencePropertyName">A string value representing the name of the property that can be set to the target object. This property must belong to the class specified by the looksFor parameter.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleIsReferencedAttribute.#ctor(System.String,System.String,System.Type,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleIsReferencedAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="looksFor">The type of the objects to be searched for.</param>
      <param name="referencePropertyName">A string value representing the name of the property that can be set to the target object. This property must belong to the class specified by the looksFor parameter.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleIsReferencedAttribute.#ctor(System.Type,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleIsReferencedAttribute"/> class.</para>
      </summary>
      <param name="looksFor">A type of the objects to be tested.</param>
      <param name="referencePropertyName">A string value representing the name of the property that can be set to the target object. This property must belong to the class specified by the looksFor parameter.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleIsReferencedAttribute.CriteriaEvaluationBehavior">
      <summary>
        <para>Specifies whether to look for objects that are currently retrieved from the database, in addition to the objects in the database itself.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.CriteriaEvaluationBehavior"/> enumeration value representing the behavior for searching for the required objects.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleIsReferencedAttribute.FoundObjectMessageFormat">
      <summary>
        <para>Gets or sets the format for specifying information on the found objects that reference the target object in the validation error message.</para>
      </summary>
      <value>A sting value that can include one variable. This variable will be set to the key property value of the found object.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleIsReferencedAttribute.FoundObjectMessagesSeparator">
      <summary>
        <para>Specifies a separator for the object list written in the error message for the current rule.</para>
      </summary>
      <value>A string value that represents a separator.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleIsReferencedAttribute.LooksFor">
      <summary>
        <para>Specifies the type of objects to be looked for.</para>
      </summary>
      <value>A type of the objects to be tested.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleIsReferencedAttribute.MessageTemplateMustBeReferenced">
      <summary>
        <para>Specifies the text to be written in the Validation Error window when the current rule is broken.</para>
      </summary>
      <value>A string value representing the text to be written to the Validation Error window when a rule is broken.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleIsReferencedProperties">
      <summary>
        <para>Represent the RuleIsReferenced <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleIsReferencedProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleIsReferencedProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleIsReferencedProperties.LooksFor">
      <summary>
        <para>Specifies the type of the objects which are checked as to whether they have a reference to the object of the type specified by the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetType">RuleBaseProperties.TargetType</see> property.</para>
      </summary>
      <value>A <see cref="T:System.Type"/> object which specifies the type of the objects which are checked as to whether they have a reference to the object of the type specified by the TargetType property.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleIsReferencedProperties.MessageTemplateMustBeReferenced">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleIsReferencedProperties.ReferencePropertyName">
      <summary>
        <para>Specifies the property which is checked as to whether it contains a reference to an object of the type specified by the <see cref="P:DevExpress.Persistent.Validation.RuleBaseProperties.TargetType">RuleBaseProperties.TargetType</see> property. The specified property must belong to the class specified by the <see cref="P:DevExpress.Persistent.Validation.RuleIsReferencedProperties.LooksFor">RuleIsReferencedProperties.LooksFor</see> property.</para>
      </summary>
      <value>A string holding the name of the property which is checked as to whether it contains a reference to an object of the type specified by the TargetType property.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleObjectExistsAttribute">
      <summary>
        <para>Defines a validation rule that demands a particular type object that satisfies a specified criterion, exist in the database.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.#ctor(DevExpress.Persistent.Validation.DefaultContexts,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleObjectExistsAttribute"/> class.</para>
      </summary>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="criteria">A string value representing the criteria that must be satisfied by the target type objects.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleObjectExistsAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="criteria">A string value representing the criteria that must be satisfied by the target type objects.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleObjectExistsAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="criteria">A string value representing the criteria that must be satisfied by the target type objects.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.#ctor(System.String,System.String,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleObjectExistsAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="criteria">A string value representing the criteria that must be satisfied by the target type objects.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.#ctor(System.String,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleObjectExistsAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="criteria">A string value representing the criteria that must be satisfied by the target type objects.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleObjectExistsAttribute"/> class.</para>
      </summary>
      <param name="criteria">A string value representing the criteria that must be satisfied by the target type objects.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.CriteriaEvaluationBehavior">
      <summary>
        <para>Specifies whether to look for objects that are currently retrieved from the database, in addition to the objects in the database itself.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.CriteriaEvaluationBehavior"/> enumeration value representing the behavior for searching for the required objects.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.FoundObjectMessageFormat">
      <summary>
        <para>Gets or sets the format for specifying information on found objects in the validation error message.</para>
      </summary>
      <value>A string value that can include one variable. This variable will be set to the key property value of the found object.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.FoundObjectMessagesSeparator">
      <summary>
        <para>Specifies a separator for the object list written in the error message for the current rule.</para>
      </summary>
      <value>A string value that represents a separator.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.IncludeCurrentObject">
      <summary>
        <para>Specifies whether to check the current business object by this rule.</para>
      </summary>
      <value>true if the current object is included into the search scope; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.LooksFor">
      <summary>
        <para>Specifies the type of objects to be looked for.</para>
      </summary>
      <value>A type of the objects to be tested.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleObjectExistsAttribute.MessageTemplateMustExist">
      <summary>
        <para>Specifies the text to be written in the Validation Error window when the current rule is broken.</para>
      </summary>
      <value>A string value representing the text to be written to the Validation Error window when a rule is broken.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleObjectExistsProperties">
      <summary>
        <para>Represent the RuleObjectExists <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleObjectExistsProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleObjectExistsProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleObjectExistsProperties.Criteria">
      <summary>
        <para>Specifies the criteria according to which objects are searched in the database.</para>
      </summary>
      <value>A string representing the criteria according to which objects are searched in the database.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleObjectExistsProperties.LooksFor">
      <summary>
        <para>Specifies the name of the class whose objects will be searched for in the database.</para>
      </summary>
      <value>A string holding the name of the class whose objects will be searched for in the database.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleObjectExistsProperties.MessageTemplateMustExist">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RulePropertyValueProperties">
      <summary>
        <para>Serves as the base class for classes which represent property value checking <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RulePropertyValueProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RulePropertyValueProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RulePropertyValueProperties.TargetPropertyName">
      <summary>
        <para>Specifies the property which must satisfy the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>A string holding the name of the property which must satisfy the Validation Rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleRangeAttribute">
      <summary>
        <para>Defines a validation rule that demands the target property’s value be within the specified value range (including the range’s end points).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRangeAttribute.#ctor(DevExpress.Persistent.Validation.DefaultContexts,System.Object,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRangeAttribute"/> class.</para>
      </summary>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="minimumValue">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the left end point of the range that should contain the target property’s value.</param>
      <param name="maximumValue">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the right end point of the range that contains the target property’s value.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRangeAttribute.#ctor(System.Object,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRangeAttribute"/> class.</para>
      </summary>
      <param name="minimumValue">An object that represents the left end point of the range the target property’s value should be within.</param>
      <param name="maximumValue">An object that represents the right end point of the range the target property’s value should be within.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRangeAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.Object,System.Object,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRangeAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="minimumValue">An object that represents the left end point of the range the target property’s value should be within.</param>
      <param name="maximumValue">An object that represents the right end point of the range the target property’s value should be within.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRangeAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.Object,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRangeAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="minimumValue">An object that represents the left end point of the range that should contain the target property’s value.</param>
      <param name="maximumValue">An object that represents the right end point of the range that contains the target property’s value.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRangeAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String,System.String,DevExpress.Persistent.Validation.ParametersMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRangeAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="minimumValue">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the left end point of the range that should contain the target property’s value.</param>
      <param name="maximumValue">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the right end point of the range that contains the target property’s value.</param>
      <param name="mode">A <see cref="T:DevExpress.Persistent.Validation.ParametersMode"/> enumeration value specifying how the minimumValue and maximumValue parameter values are treated.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRangeAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String,System.String,System.String,DevExpress.Persistent.Validation.ParametersMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRangeAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="minimumValue">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the left end point of the range that should contain the target property’s value.</param>
      <param name="maximumValue">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the right end point of the range that contains the target property’s value.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
      <param name="mode">A <see cref="T:DevExpress.Persistent.Validation.ParametersMode"/> enumeration value specifying how the minimumValue and maximumValue parameter values are treated.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRangeAttribute.#ctor(System.String,System.String,System.Object,System.Object,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRangeAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="minimumValue">An object that represents the left end point of the range the target property’s value should be within.</param>
      <param name="maximumValue">An object that represents the right end point of the range the target property’s value should be within.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRangeAttribute.#ctor(System.String,System.String,System.Object,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRangeAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="minimumValue">An object that represents the left end point of the range that should contain the target property’s value.</param>
      <param name="maximumValue">An object that represents the right end point of the range that contains the target property’s value.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRangeAttribute.#ctor(System.String,System.String,System.String,System.String,DevExpress.Persistent.Validation.ParametersMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRangeAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="minimumValue">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the left end point of the range that should contain the target property’s value.</param>
      <param name="maximumValue">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the right end point of the range that contains the target property’s value.</param>
      <param name="mode">A <see cref="T:DevExpress.Persistent.Validation.ParametersMode"/> enumeration value specifying how the minimumValue and maximumValue parameter values are treated.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRangeAttribute.#ctor(System.String,System.String,System.String,System.String,System.String,DevExpress.Persistent.Validation.ParametersMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRangeAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="minimumValue">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the left end point of the range that should contain the target property’s value.</param>
      <param name="maximumValue">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the right end point of the range that contains the target property’s value.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
      <param name="mode">A <see cref="T:DevExpress.Persistent.Validation.ParametersMode"/> enumeration value specifying how the minimumValue and maximumValue parameter values are treated.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRangeAttribute.IsCollectionAggregateSet">
      <summary>
        <para>Indicates whether a value is assigned to the <see cref="P:DevExpress.Persistent.Validation.RuleRangeAttribute.TargetCollectionAggregate">RuleRangeAttribute.TargetCollectionAggregate</see> property.</para>
      </summary>
      <value>true, if a value is assigned to the <see cref="P:DevExpress.Persistent.Validation.RuleRangeAttribute.TargetCollectionAggregate">RuleRangeAttribute.TargetCollectionAggregate</see> property; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRangeAttribute.TargetCollectionAggregate">
      <summary>
        <para>Specifies the aggregate function to be checked by the current rule.</para>
      </summary>
      <value>A Devexpress.Data.Filtering.Aggregate enumeration value which specifies the aggregate function to be checked by the current rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRangeAttribute.TargetPropertyName">
      <summary>
        <para>Specifies the target collection’s element property that must be checked by the current rule.</para>
      </summary>
      <value>A string holding the name of the target collection’s element property that must be checked by the current rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleRangeProperties">
      <summary>
        <para>Represent the RuleRange <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRangeProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRangeProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRangeProperties.MaximumValue">
      <summary>
        <para>Specifies the maximum value allowed for the property value.</para>
      </summary>
      <value>An object which represents the maximum value allowed for the property value.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRangeProperties.MaximumValueExpression">
      <summary>
        <para>Specifies the expression whose value is treated as the maximum allowed property value.</para>
      </summary>
      <value>The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the right end point of the range that contains the target property’s value.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRangeProperties.MessageTemplateMustBeInRange">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRangeProperties.MinimumValue">
      <summary>
        <para>Specifies the minimum value allowed for the property value.</para>
      </summary>
      <value>An object which represents the minimum value allowed for the property value.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRangeProperties.MinimumValueExpression">
      <summary>
        <para>Specifies the expression whose value is treated as the minimum allowed property value.</para>
      </summary>
      <value>The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value represents the left end point of the range that should contain the target property’s value.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRangeProperties.TargetCollectionAggregate">
      <summary>
        <para>Specifies the aggregate function to be checked by the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>, if the rule is applied to a collection property.</para>
      </summary>
      <value>A Nullable&lt;Aggregate&gt; object which specifies the aggregate function to be checked by the Validation Rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute">
      <summary>
        <para>Defines a validation rule that demands the target property match a specified pattern.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute.#ctor(DevExpress.Persistent.Validation.DefaultContexts,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute"/> class.</para>
      </summary>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="pattern">A string value that represents the regular expression that the property must match.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="pattern">A string value that represented the regular expression that the property must match.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="pattern">A string value that represented the regular expression that the property must match.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute.#ctor(System.String,System.String,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="pattern">A string value that represents the regular expression that the property must match.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute.#ctor(System.String,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="pattern">A string value that represents the regular expression that the property must match.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute"/> class.</para>
      </summary>
      <param name="pattern">A string value that is the regular expression that the property must match.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRegularExpressionAttribute.TargetPropertyName">
      <summary>
        <para>Specifies the target collection’s element property that must be checked by the current rule.</para>
      </summary>
      <value>A string holding the name of the target collection’s element property that must be checked by the current rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleRegularExpressionProperties">
      <summary>
        <para>Represent the RuleRegularExpression <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRegularExpressionProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRegularExpressionProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRegularExpressionProperties.MessageTemplateMustMatchPattern">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRegularExpressionProperties.Pattern">
      <summary>
        <para>Specifies the pattern which the target property must match.</para>
      </summary>
      <value>A string holding the pattern which the target property must match.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute">
      <summary>
        <para>Defines a validation rule that demands that a property has a value.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute.#ctor(DevExpress.Persistent.Validation.DefaultContexts)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute"/> class.</para>
      </summary>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute.#ctor(System.String,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute.#ctor(System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRequiredFieldAttribute.TargetPropertyName">
      <summary>
        <para>Specifies the target collection’s element property that must be checked by the current rule.</para>
      </summary>
      <value>A string holding the name of the target collection’s element property that must be checked by the current rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleRequiredFieldProperties">
      <summary>
        <para>Represent the RuleRequiredField <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleRequiredFieldProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleRequiredFieldProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleRequiredFieldProperties.MessageTemplateMustNotBeEmpty">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleSearchObjectProperties">
      <summary>
        <para>Serves as the base class for classes which represent object searching <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSearchObjectProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleSearchObjectProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleSearchObjectProperties.CriteriaEvaluationBehavior">
      <summary>
        <para>Specifies whether to look for objects that are currently retrieved from the database, in addition to the objects in the database itself.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.CriteriaEvaluationBehavior"/> enumeration value specifying whether to look for objects that are currently retrieved from the database, in addition to the objects in the database itself.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleSearchObjectProperties.FoundObjectMessageFormat">
      <summary>
        <para>Specifies the format for the list of found objects.</para>
      </summary>
      <value>A string representing the format for the list of found objects.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleSearchObjectProperties.FoundObjectMessagesSeparator">
      <summary>
        <para>Specifies the character used to separate objects in the list of found objects (see <see cref="P:DevExpress.Persistent.Validation.RuleSearchObjectProperties.FoundObjectMessageFormat">RuleSearchObjectProperties.FoundObjectMessageFormat</see>).</para>
      </summary>
      <value>A string holding the character used to separate objects in the list of found objects.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleSearchObjectProperties.IncludeCurrentObject">
      <summary>
        <para>Specifies whether to check the current business object by the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>true, to check the current business object by the Validation Rule; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleSearchObjectProperties.MessageTemplateFoundObjects">
      <summary>
        <para>Specifies the text that is displayed before the list of found objects (see <see cref="P:DevExpress.Persistent.Validation.RuleSearchObjectProperties.FoundObjectMessageFormat">RuleSearchObjectProperties.FoundObjectMessageFormat</see>).</para>
      </summary>
      <value>A string containing the text that is displayed before the list of found objects.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleSet">
      <summary>
        <para>Specifies a set of <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see> that can be checked on an object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.#ctor(DevExpress.ExpressApp.Services.Localization.ICaptionHelperProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.#ctor(DevExpress.Persistent.Validation.RuleSet)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> class with the same rules and rule sources as its parameter.</para>
      </summary>
      <param name="source">The <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> object from which to copy the rules and rule sources.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.#ctor(System.Collections.Generic.IEnumerable{DevExpress.Persistent.Validation.IRule},System.Collections.Generic.IEnumerable{DevExpress.Persistent.Validation.IRuleSource},DevExpress.ExpressApp.Services.Localization.ICaptionHelperProvider)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.Clear">
      <summary>
        <para>Clears the <see cref="P:DevExpress.Persistent.Validation.RuleSet.RegisteredRules">RuleSet.RegisteredRules</see> and <see cref="P:DevExpress.Persistent.Validation.RuleSet.RegisteredSources">RuleSet.RegisteredSources</see> of the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Persistent.Validation.RuleSet.CustomIsEmptyValue">
      <summary>
        <para>Occurs when the <see cref="M:DevExpress.Persistent.Validation.RuleSet.IsEmptyValue(System.Object,System.String,System.Object)">RuleSet.IsEmptyValue</see> method is executed to determine whether or not the property value is considered empty.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Persistent.Validation.RuleSet.CustomNeedToValidateRule">
      <summary>
        <para>Occurs when the validation system determines whether or not a rule should be validated.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Persistent.Validation.RuleSet.CustomValidateRule">
      <summary>
        <para>Occurs when a rule is being validated.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.DropCachedRules">
      <summary>
        <para>Drops the Rule cache for all persistent Rule Sources.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleSet.EnableDelayedRuleRegistration">
      <summary>
        <para>Specifies whether all new rules are collected before the RuleSet.GetRules method returns them.</para>
      </summary>
      <value>true, if new rules will be registered automatically; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Persistent.Validation.RuleSet.EnableRulePropertyValueCache">
      <summary>
        <para>Specifies if the Validation Module caches Rule values.</para>
      </summary>
      <value>true if the Validation Module caches Rule values. If false, the Validation Module loads Rule values from the Application Model.</value>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.FindRule(System.String)">
      <summary>
        <para>Returns a rule by its ID.</para>
      </summary>
      <param name="id">The string identifier of the rule to search.</param>
      <returns>The <see cref="T:DevExpress.Persistent.Validation.IRule"/> object that is the rule whose ID is equal to the parameter.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.GetEnumerator">
      <summary>
        <para>Returns an enumerator that iterates through a collection of rules.</para>
      </summary>
      <returns>The enumerator that iterates through a collection of <see cref="T:DevExpress.Persistent.Validation.IRule"/> objects.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.GetRules">
      <summary>
        <para>Returns a list of all rules registered in the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</para>
      </summary>
      <returns>The list of <see cref="T:DevExpress.Persistent.Validation.IRule"/> objects registered in the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.GetRules(System.Collections.Generic.IEnumerable{System.Type})">
      <summary>
        <para>Returns a list of rules that belongs to the current <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</para>
      </summary>
      <param name="targetTypesForDelayedCollectRules">The list of <see cref="T:System.Type"/> objects, rules for which will be collected.</param>
      <returns>The list of <see cref="T:DevExpress.Persistent.Validation.IRule"/> objects for the types defined in a targetTypesForDelayedCollectRules parameter.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.GetRules(System.Collections.IEnumerable)">
      <summary>
        <para>Returns a list of all rules that belong to the objects listed in a parameter.</para>
      </summary>
      <param name="targetsForDelayedCollectRules">The list of objects for which to collect the rules.</param>
      <returns>The list of rules.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.GetRules(System.Object,DevExpress.Persistent.Validation.ContextIdentifiers)">
      <summary>
        <para>Returns a list of all rules for the given object and validation contexts.</para>
      </summary>
      <param name="target">An object for which to collect the rules.</param>
      <param name="contextIDs">The <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> object which is a set of validation contexts, rules for which will be collected. Default contexts are stored in the <see cref="T:DevExpress.Persistent.Validation.DefaultContexts"/> enumeration.</param>
      <returns>The list of rules.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.GetRules(System.Object)">
      <summary>
        <para>Returns a list of all rules that belong to the object specified in a parameter.</para>
      </summary>
      <param name="targetForDelayedCollectRules">An object for which to collect the rules.</param>
      <returns>The list of rules.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.GetRules(System.Type,DevExpress.Persistent.Validation.ContextIdentifiers)">
      <summary>
        <para>Returns a list of rules for the given type and validation contexts.</para>
      </summary>
      <param name="targetType">A type of object, rules for which will be collected.</param>
      <param name="contextIDs">The <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> object which is a set of validation contexts, rules for which will be collected. Default contexts are stored in the <see cref="T:DevExpress.Persistent.Validation.DefaultContexts"/> enumeration.</param>
      <returns>The list of rules that satisfy both conditions given in parameters.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleSet.IgnoreWarningAndInformationRules">
      <summary>
        <para>Specifies whether or not the <see cref="T:DevExpress.Persistent.Validation.ValidationOutcome"/>.Warning and <see cref="T:DevExpress.Persistent.Validation.ValidationOutcome"/>.Information rule validation result types are ignored (see <see cref="P:DevExpress.Persistent.Validation.IRuleBaseProperties.ResultType">IRuleBaseProperties.ResultType</see>).</para>
      </summary>
      <value>true, when  Warning and Information rule validation results are ignored; otherwise - false. The default is false.</value>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.IsEmptyValue(System.Object,System.String,System.Object,System.IServiceProvider)">
      <summary>
        <para>Determines whether or not the validation rule’s target property value is considered empty.</para>
      </summary>
      <param name="targetObject">An object which is the validation rule’s target object.</param>
      <param name="propertyName">A string which is the name of the validation rule’s target property.</param>
      <param name="propertyValue">An object which is the validation rule’s target property value.</param>
      <param name="serviceProvider">An object that implements the IServiceProvider interface.</param>
      <returns>true, if the value is considered empty; otherwise - false.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.IsEmptyValue(System.Object,System.String,System.Object)">
      <summary>
        <para>Determines whether or not the validation rule’s target property value is considered empty.</para>
      </summary>
      <param name="targetObject">An object which is the validation rule’s target object.</param>
      <param name="propertyName">A string which is the name of the validation rule’s target property.</param>
      <param name="propertyValue">An object which is the validation rule’s target property value.</param>
      <returns>true, if the value is considered empty; otherwise - false.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleSet.NeedToValidateAggregatedRulesInNestedObjectSpace">
      <summary>
        <para>Specifies whether or not the RuleSet.NeedToValidateRule method will return true when a rule with an assigned <see cref="P:DevExpress.Persistent.Validation.IRuleSupportsCollectionAggregatesProperties.TargetCollectionAggregate">IRuleSupportsCollectionAggregatesProperties.TargetCollectionAggregate</see> property and an object from nested Object Space are passed.</para>
      </summary>
      <value>true, if the NeedToValidateRule method will return true when a rule with an assigned TargetCollectionAggregate property and an object from nested Object Space are passed; otherwise - false.</value>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.NeedToValidateRule(DevExpress.ExpressApp.IObjectSpace,DevExpress.Persistent.Validation.IRule,System.Object,System.String,System.String@)">
      <summary>
        <para>Checks if the rule is marked to be validated.</para>
      </summary>
      <param name="targetObjectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by a validation target.</param>
      <param name="rule">The <see cref="T:DevExpress.Persistent.Validation.IRule"/> object whose validation shall be checked.</param>
      <param name="target">A rule’s target object.</param>
      <param name="contextId">A <see cref="P:DevExpress.Persistent.Validation.CustomNeedToValidateRuleEventArgs.ContextId">CustomNeedToValidateRuleEventArgs.ContextId</see> which is a validation context of the rule.</param>
      <param name="reason">Returns a reason to validate the rule or not.</param>
      <returns>true, if the rule will be validated; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.NeedToValidateRule(DevExpress.ExpressApp.IObjectSpace,DevExpress.Persistent.Validation.IRule,System.Object,System.String@)">
      <summary>
        <para>Checks if the rule is marked to be validated.</para>
      </summary>
      <param name="targetObjectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used to load objects being validated by a rule.</param>
      <param name="rule">A rule whose validation shall be checked.</param>
      <param name="target">A rule’s target object.</param>
      <param name="reason">Returns a string which is reason to validate the rule or not.</param>
      <returns>true, if the rule will be validated; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleSet.RegisteredRules">
      <summary>
        <para>Specifies a list of rules registered in the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</para>
      </summary>
      <value>The list of <see cref="T:DevExpress.Persistent.Validation.IRule"/> objects, registered in the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleSet.RegisteredSources">
      <summary>
        <para>Specifies a list of rule sources registered in the current <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</para>
      </summary>
      <value>The list of <see cref="T:DevExpress.Persistent.Validation.IRuleSource"/> objects, registered in the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</value>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.RegisterRules(DevExpress.ExpressApp.DC.ITypeInfo)">
      <summary>
        <para>Adds validation rules of the class given as a parameter to the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</para>
      </summary>
      <param name="targetType">Type, for which the corresponding class will be scanned for validation rules.</param>
    </member>
    <member name="E:DevExpress.Persistent.Validation.RuleSet.RuleValidated">
      <summary>
        <para>Occurs after the validation of a rule is complete.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.Validate(DevExpress.ExpressApp.IObjectSpace,System.Object,DevExpress.Persistent.Validation.ContextIdentifiers,DevExpress.Persistent.Validation.ValidationFailedDelegate)">
      <summary>
        <para>Validates an object against the RuleSet‘s rules with the given validation contexts, returns a result and throws a <see cref="T:DevExpress.Persistent.Validation.ValidationException"/> if the validation fails.</para>
      </summary>
      <param name="targetObjectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by a validation target.</param>
      <param name="target">An object to validate.</param>
      <param name="contextIDs">A <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> object which is a set of validation contexts, rules for which will be collected. Default contexts are stored in the <see cref="T:DevExpress.Persistent.Validation.DefaultContexts"/> enumeration.</param>
      <param name="validationFailedDelegate">A method to call if the validation fails.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.Validate(DevExpress.ExpressApp.IObjectSpace,System.Object,DevExpress.Persistent.Validation.ContextIdentifiers)">
      <summary>
        <para>Validates an object against RuleSet‘s rules with the given validation contexts, returns a result and throws a <see cref="T:DevExpress.Persistent.Validation.ValidationException"/> if the validation fails.</para>
      </summary>
      <param name="targetObjectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used to load objects being validated by a rule.</param>
      <param name="target">An object to validate.</param>
      <param name="contextIDs">The <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> object which is a set of validation contexts, rules for which will be collected. Default contexts are stored in the <see cref="T:DevExpress.Persistent.Validation.DefaultContexts"/> enumeration.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.ValidateAll(DevExpress.ExpressApp.IObjectSpace,System.Collections.IEnumerable,DevExpress.Persistent.Validation.ContextIdentifiers,DevExpress.Persistent.Validation.ValidationFailedDelegate,DevExpress.ExpressApp.Frame)">
      <summary>
        <para>Validates multiple objects against RuleSet‘s rules with the given validation contexts, returns a result and throws a <see cref="T:DevExpress.Persistent.Validation.ValidationException"/> if the validation fails.</para>
      </summary>
      <param name="targetObjectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by a validation target.</param>
      <param name="targets">The list of objects to check.</param>
      <param name="contextIDs">The <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> object which is a set of validation contexts, rules for which will be collected. Default contexts are stored in the <see cref="T:DevExpress.Persistent.Validation.DefaultContexts"/> enumeration.</param>
      <param name="validationFailedDelegate">A method to call if the validation fails.</param>
      <param name="sourceFrame">The <see href="https://docs.devexpress.com/eXpressAppFramework/112608/ui-construction/windows-and-frames">Frame</see> of the Controller that validates objects. This parameter is optional and used in WinForms applications only.</param>
      <returns>true, if validation passes; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.ValidateAll(DevExpress.ExpressApp.IObjectSpace,System.Collections.IEnumerable,DevExpress.Persistent.Validation.ContextIdentifiers)">
      <summary>
        <para>Validates multiple objects against RuleSet‘s rules with the given validation contexts, returns a result and throws a <see cref="T:DevExpress.Persistent.Validation.ValidationException"/> if the validation fails.</para>
      </summary>
      <param name="targetObjectSpace">The <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by a validation target.</param>
      <param name="targets">The list of objects to check.</param>
      <param name="contextIDs">The set of validation contexts to check the rule. Default contexts are stored in the <see cref="T:DevExpress.Persistent.Validation.DefaultContexts"/> enumeration.</param>
      <returns>true, if validation passes; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.ValidateAllTargets(DevExpress.ExpressApp.IObjectSpace,System.Collections.IEnumerable,DevExpress.Persistent.Validation.ContextIdentifiers)">
      <summary>
        <para>Silently (without raising the <see cref="E:DevExpress.Persistent.Validation.RuleSet.ValidationCompleted">RuleSet.ValidationCompleted</see> event and throwing the <see cref="T:DevExpress.Persistent.Validation.ValidationException"/>) validates multiple targets and returns the result.</para>
      </summary>
      <param name="targetObjectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by a validation target.</param>
      <param name="targets">The list of objects to check.</param>
      <param name="contextIDs">The <see cref="T:DevExpress.Persistent.Validation.ContextIdentifiers"/> object which is a set of validation contexts, rules for which will be collected. Default contexts are stored in the <see cref="T:DevExpress.Persistent.Validation.DefaultContexts"/> enumeration.</param>
      <returns>The result of the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> validation.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.ValidateAllTargets(DevExpress.ExpressApp.IObjectSpace,System.Collections.IEnumerable)">
      <summary>
        <para>Silently (without raising the <see cref="E:DevExpress.Persistent.Validation.RuleSet.ValidationCompleted">RuleSet.ValidationCompleted</see> event and throwing the <see cref="T:DevExpress.Persistent.Validation.ValidationException"/>) validates multiple targets and returns the result.</para>
      </summary>
      <param name="targetObjectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by a validation targets.</param>
      <param name="targets">The list of objects to check.</param>
      <returns>The result of the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> validation.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.ValidateRule(DevExpress.ExpressApp.IObjectSpace,DevExpress.Persistent.Validation.IRule,System.Object)">
      <summary>
        <para>Checks a rule on a target and returns a result.</para>
      </summary>
      <param name="targetObjectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by a validation target.</param>
      <param name="rule">The <see cref="T:DevExpress.Persistent.Validation.IRule"/> object that represents a rule to check.</param>
      <param name="target">An object to validate.</param>
      <returns>The validation result.</returns>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleSet.ValidateTarget(DevExpress.ExpressApp.IObjectSpace,System.Object,DevExpress.Persistent.Validation.ContextIdentifiers)">
      <summary>
        <para>Silently validates an object (without raising the <see cref="E:DevExpress.Persistent.Validation.RuleSet.ValidationCompleted">RuleSet.ValidationCompleted</see> event and throwing the <see cref="T:DevExpress.Persistent.Validation.ValidationException"/>) and returns a result.</para>
      </summary>
      <param name="targetObjectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by a validation target.</param>
      <param name="target">An object for which to check the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</param>
      <param name="contextIDs">The set of validation contexts. Default contexts are stored in the <see cref="T:DevExpress.Persistent.Validation.DefaultContexts"/> enumeration.</param>
      <returns>The result of validation.</returns>
    </member>
    <member name="E:DevExpress.Persistent.Validation.RuleSet.ValidationCompleted">
      <summary>
        <para>Occurs after validation of an entire <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> is complete.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleStringComparisonAttribute">
      <summary>
        <para>Defines a validation rule that demands the target string type property’s value satisfy a specified condition.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleStringComparisonAttribute.#ctor(DevExpress.Persistent.Validation.DefaultContexts,DevExpress.Persistent.Validation.StringComparisonType,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleStringComparisonAttribute"/> class.</para>
      </summary>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.StringComparisonType"/> enumeration value that represents the operator type to be used when comparing string values.</param>
      <param name="operandValue">A string value that represents the value to be compared with the target property’s value.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleStringComparisonAttribute.#ctor(DevExpress.Persistent.Validation.StringComparisonType,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleStringComparisonAttribute"/> class.</para>
      </summary>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.StringComparisonType"/> enumeration value that represents the operator type to be used when comparing string values.</param>
      <param name="operandValue">A string value that represents the value to be compared with the target property’s value.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleStringComparisonAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,DevExpress.Persistent.Validation.StringComparisonType,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleStringComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.StringComparisonType"/> enumeration value that represents the operator type to be used when comparing string values.</param>
      <param name="operandValue">A string value that represents the value to be compared with the target property’s value.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleStringComparisonAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,DevExpress.Persistent.Validation.StringComparisonType,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleStringComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.StringComparisonType"/> enumeration value that represents the operator type to be used when comparing string values.</param>
      <param name="operandValue">A string value that represents the value to be compared with the target property’s value.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleStringComparisonAttribute.#ctor(System.String,System.String,DevExpress.Persistent.Validation.StringComparisonType,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleStringComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.StringComparisonType"/> enumeration value that represents the operator type to be used when comparing string values.</param>
      <param name="operandValue">A string value that represents the value to be compared with the target property’s value.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleStringComparisonAttribute.#ctor(System.String,System.String,DevExpress.Persistent.Validation.StringComparisonType,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleStringComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.StringComparisonType"/> enumeration value that represents the operator type to be used when comparing string values.</param>
      <param name="operandValue">A string value that represents the value to be compared with the target property’s value.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleStringComparisonAttribute.IgnoreCase">
      <summary>
        <para>Specifies whether to perfom a case-sensitive comparison.</para>
      </summary>
      <value>true if a case-insensitive comparison will be performed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleStringComparisonAttribute.TargetPropertyName">
      <summary>
        <para>Specifies the target collection’s element property that must be checked by the current rule.</para>
      </summary>
      <value>A string holding the name of the target collection’s element property that must be checked by the current rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleStringComparisonProperties">
      <summary>
        <para>Represent the RuleStringComparison <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleStringComparisonProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleStringComparisonProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.IgnoreCase">
      <summary>
        <para>Specifies whether to perform a case-sensitive string comparison.</para>
      </summary>
      <value>true, to perform a case-insensitive string comparison; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.MessageTemplateMustBeEqual">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken. In effect if the <see cref="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.OperatorType">RuleStringComparisonProperties.OperatorType</see> is set to <see cref="F:DevExpress.Persistent.Validation.StringComparisonType.Equals">StringComparisonType.Equals</see>.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.MessageTemplateMustBeginWith">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken. In effect if the <see cref="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.OperatorType">RuleStringComparisonProperties.OperatorType</see> is set to <see cref="F:DevExpress.Persistent.Validation.StringComparisonType.StartsWith">StringComparisonType.StartsWith</see>.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.MessageTemplateMustContain">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken. In effect if the <see cref="F:DevExpress.Persistent.Validation.StringComparisonType.Contains">StringComparisonType.Contains</see>.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.MessageTemplateMustEndWith">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken. In effect if the <see cref="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.OperatorType">RuleStringComparisonProperties.OperatorType</see> is set to <see cref="F:DevExpress.Persistent.Validation.StringComparisonType.EndsWith">StringComparisonType.EndsWith</see>.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.MessageTemplateMustNotBeEqual">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken. In effect if the <see cref="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.OperatorType">RuleStringComparisonProperties.OperatorType</see> is set to <see cref="F:DevExpress.Persistent.Validation.StringComparisonType.NotEquals">StringComparisonType.NotEquals</see>.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.OperandValue">
      <summary>
        <para>Specifies the value which the property value is compared with.</para>
      </summary>
      <value>A string which the property value is compared with.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleStringComparisonProperties.OperatorType">
      <summary>
        <para>Specifies the operator type that must be used when comparing string values.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.StringComparisonType"/> enumeration value which specifies the operator type that must be used when comparing string values.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleUniqueValueAttribute">
      <summary>
        <para>Defines a validation rule that demands the target property’s value be unique.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleUniqueValueAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleUniqueValueAttribute"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleUniqueValueAttribute.#ctor(DevExpress.Persistent.Validation.DefaultContexts)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleUniqueValueAttribute"/> class.</para>
      </summary>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleUniqueValueAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleUniqueValueAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleUniqueValueAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleUniqueValueAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleUniqueValueAttribute.#ctor(System.String,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleUniqueValueAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleUniqueValueAttribute.#ctor(System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleUniqueValueAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleUniqueValueAttribute.CriteriaEvaluationBehavior">
      <summary>
        <para>Specifies whether to look for objects that are currently retrieved from the database, in addition to the objects in the database itself.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.CriteriaEvaluationBehavior"/> enumeration value representing the behavior for searching for the required objects.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleUniqueValueAttribute.FoundObjectMessageFormat">
      <summary>
        <para>Gets or sets the format for specifying information on found objects with the specified property value in the validation error message.</para>
      </summary>
      <value>A sting value that can include one variable. This variable will be set to the key property value of the found object.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleUniqueValueAttribute.FoundObjectMessagesSeparator">
      <summary>
        <para>Specifies a separator for the object list written in the error message for the current rule.</para>
      </summary>
      <value>A string value that represents a separator.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleUniqueValueAttribute.TargetPropertyName">
      <summary>
        <para>Specifies the target collection’s element property that must be checked by the current rule.</para>
      </summary>
      <value>A string holding the name of the target collection’s element property that must be checked by the current rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleUniqueValueProperties">
      <summary>
        <para>Represent the RuleUniqueValue <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleUniqueValueProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleUniqueValueProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleUniqueValueProperties.MessageTemplateMustBeUnique">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleUniqueValueProperties.TargetPropertyName">
      <summary>
        <para>Specifies the property which must satisfy the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>A string holding the name of the property which must satisfy the Validation Rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleValidatedEventArgs">
      <summary>
        <para>Arguments passed to the <see cref="E:DevExpress.Persistent.Validation.RuleSet.RuleValidated">RuleSet.RuleValidated</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValidatedEventArgs.#ctor(DevExpress.Persistent.Validation.RuleSetValidationResultItem,DevExpress.ExpressApp.IObjectSpace)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValidatedEventArgs"/> class.</para>
      </summary>
      <param name="ruleSetValidationResultItem">The validation result of one rule.</param>
      <param name="objectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used to load objects being validated by a rule.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValidatedEventArgs.ObjectSpace">
      <summary>
        <para>An Object Space of a validation target.</para>
      </summary>
      <value>An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> object used by the validation rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValidatedEventArgs.RuleSetValidationResultItem">
      <summary>
        <para>Specifies an item of the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>‘s validation results.</para>
      </summary>
      <value>The result of a rule’s validation.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleValidationResult">
      <summary>
        <para>Represents the results of checking an object via a <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValidationResult.#ctor(DevExpress.Persistent.Validation.IRule,DevExpress.Persistent.Validation.ValidationState,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValidationResult"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValidationResult.#ctor(DevExpress.Persistent.Validation.IRule,System.Object,DevExpress.Persistent.Validation.ValidationState,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValidationResult"/> class.</para>
      </summary>
      <param name="rule">An <see cref="T:DevExpress.Persistent.Validation.IRule"/> object which represents the checked Validation Rule.</param>
      <param name="target">An object that was checked with the rule.</param>
      <param name="validationResult">A <see cref="T:DevExpress.Persistent.Validation.ValidationState"/> enumeration value which specifies whether the checked object satisfies the rule.</param>
      <param name="formattedErrorMessage">A string holding the exception message, if the checked rule is broken.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValidationResult.ErrorMessage">
      <summary>
        <para>Specifies the exception message, if the checked <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken.</para>
      </summary>
      <value>A string holding the exception message.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValidationResult.Rule">
      <summary>
        <para>Specifies the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> that was checked.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Persistent.Validation.IRule"/> object which represents the checked Validation Rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValidationResult.State">
      <summary>
        <para>Specifies whether the checked object satisfies the <see cref="P:DevExpress.Persistent.Validation.RuleValidationResult.Rule">RuleValidationResult.Rule</see>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.ValidationState"/> enumeration value which specifies whether the checked object satisfies the <see cref="P:DevExpress.Persistent.Validation.RuleValidationResult.Rule">RuleValidationResult.Rule</see>.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValidationResult.ValidationOutcome">
      <summary>
        <para>Specifies the result of checking an object via a <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.ValidationOutcome"/> enumeration value that spacifies the result of checking an object via a <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute">
      <summary>
        <para>Defines a validation rule that demands the target property’s value satisfy a specified condition.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.#ctor(DevExpress.Persistent.Validation.DefaultContexts,DevExpress.Persistent.Validation.ValueComparisonType,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/> class.</para>
      </summary>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.ValueComparisonType"/> enumeration value that represents the operator type to be used when comparing values.</param>
      <param name="rightOperand">An object that represents the value to be compared with the target property’s value.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.#ctor(DevExpress.Persistent.Validation.ValueComparisonType,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/> class.</para>
      </summary>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.ValueComparisonType"/> enumeration value that represents the operator type to be used when comparing values.</param>
      <param name="rightOperand">An object that represents the value to be compared with the target property’s value.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,DevExpress.Persistent.Validation.ValueComparisonType,System.Object,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.ValueComparisonType"/> enumeration value that represents the operator type to be used when comparing values.</param>
      <param name="rightOperand">An object that represents the value to be compared with the target property’s value.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,DevExpress.Persistent.Validation.ValueComparisonType,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.ValueComparisonType"/> enumeration value that represents the operator type to be used when comparing values.</param>
      <param name="rightOperand">An object that represents the value to be compared with the target property’s value.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,DevExpress.Persistent.Validation.ValueComparisonType,System.String,DevExpress.Persistent.Validation.ParametersMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.ValueComparisonType"/> enumeration value that represents the operator type to be used when comparing values.</param>
      <param name="rightOperand">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value to be compared with the target property’s value.</param>
      <param name="mode">A <see cref="T:DevExpress.Persistent.Validation.ParametersMode"/> enumeration value specifying how the rightOperand parameter value is treated.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.#ctor(System.String,DevExpress.Persistent.Validation.DefaultContexts,DevExpress.Persistent.Validation.ValueComparisonType,System.String,System.String,DevExpress.Persistent.Validation.ParametersMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContexts">A DefaultContexts enumeration value that specifies a context for checking the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.ValueComparisonType"/> enumeration value that represents the operator type to be used when comparing values.</param>
      <param name="rightOperand">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value to be compared with the target property’s value.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
      <param name="mode">A <see cref="T:DevExpress.Persistent.Validation.ParametersMode"/> enumeration value specifying how the rightOperand parameter value is treated.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.#ctor(System.String,System.String,DevExpress.Persistent.Validation.ValueComparisonType,System.Object,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.ValueComparisonType"/> enumeration value that represents the operator type to be used when comparing values.</param>
      <param name="rightOperand">An object that represents the value to be compared with the target property’s value.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.#ctor(System.String,System.String,DevExpress.Persistent.Validation.ValueComparisonType,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.ValueComparisonType"/> enumeration value that represents the operator type to be used when comparing values.</param>
      <param name="rightOperand">An object that represents the value to be compared with the target property’s value.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.#ctor(System.String,System.String,DevExpress.Persistent.Validation.ValueComparisonType,System.String,DevExpress.Persistent.Validation.ParametersMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.ValueComparisonType"/> enumeration value that represents the operator type to be used when comparing values.</param>
      <param name="rightOperand">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value to be compared with the target property’s value.</param>
      <param name="mode">A <see cref="T:DevExpress.Persistent.Validation.ParametersMode"/> enumeration value specifying how the rightOperand parameter value is treated.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.#ctor(System.String,System.String,DevExpress.Persistent.Validation.ValueComparisonType,System.String,System.String,DevExpress.Persistent.Validation.ParametersMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/> class.</para>
      </summary>
      <param name="id">A string value representing the current rule identifier. You can pass an empty value as the id parameter. An appropriate identifier will begenerated automatically, and set for the ID property of the Validation | Rules | Rule node in the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</param>
      <param name="targetContextIDs">A string value that specifies a list of identifiers of the contexts when the current rule should be checked. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetContextIDs">RuleBaseAttribute.TargetContextIDs</see> property.</param>
      <param name="operatorType">A <see cref="T:DevExpress.Persistent.Validation.ValueComparisonType"/> enumeration value that represents the operator type to be used when comparing values.</param>
      <param name="rightOperand">The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value to be compared with the target property’s value.</param>
      <param name="messageTemplate">A string value representing information defining the current rule. This value is set for the <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.CustomMessageTemplate">RuleBaseAttribute.CustomMessageTemplate</see> property.</param>
      <param name="mode">A <see cref="T:DevExpress.Persistent.Validation.ParametersMode"/> enumeration value specifying how the rightOperand parameter value is treated.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.IsCollectionAggregateSet">
      <summary>
        <para>Indicates whether a value is assigned to the <see cref="P:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.TargetCollectionAggregate">RuleValueComparisonAttribute.TargetCollectionAggregate</see> property.</para>
      </summary>
      <value>true, if a value is assigned to the <see cref="P:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.TargetCollectionAggregate">RuleValueComparisonAttribute.TargetCollectionAggregate</see> property; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.TargetCollectionAggregate">
      <summary>
        <para>Specifies the aggregate function to be checked by the current rule.</para>
      </summary>
      <value>A Devexpress.Data.Filtering.Aggregate enumeration value which specifies the aggregate function to be checked by the current rule.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonAttribute.TargetPropertyName">
      <summary>
        <para>Specifies the target collection’s element property that must be checked by the current rule.</para>
      </summary>
      <value>A string holding the name of the target collection’s element property that must be checked by the current rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.RuleValueComparisonProperties">
      <summary>
        <para>Represent the RuleValueComparison <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see>‘ properties exported to the <see href="https://docs.devexpress.com/eXpressAppFramework/112580/ui-construction/application-model-ui-settings-storage/how-application-model-works">Application Model</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.RuleValueComparisonProperties.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonProperties"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.MessageTemplateMustBeEqualToOperand">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken. In effect if the <see cref="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.OperatorType">RuleValueComparisonProperties.OperatorType</see> is set to <see cref="F:DevExpress.Persistent.Validation.ValueComparisonType.Equals">ValueComparisonType.Equals</see>.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.MessageTemplateMustBeGreaterThanOperand">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken. In effect if the <see cref="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.OperatorType">RuleValueComparisonProperties.OperatorType</see> is set to <see cref="F:DevExpress.Persistent.Validation.ValueComparisonType.GreaterThan">ValueComparisonType.GreaterThan</see>.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.MessageTemplateMustBeGreaterThanOrEqualToOperand">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken. In effect if the <see cref="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.OperatorType">RuleValueComparisonProperties.OperatorType</see> is set to <see cref="F:DevExpress.Persistent.Validation.ValueComparisonType.GreaterThanOrEqual">ValueComparisonType.GreaterThanOrEqual</see>.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.MessageTemplateMustBeLessThanOperand">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken. In effect if the <see cref="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.OperatorType">RuleValueComparisonProperties.OperatorType</see> is set to <see cref="F:DevExpress.Persistent.Validation.ValueComparisonType.LessThan">ValueComparisonType.LessThan</see>.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.MessageTemplateMustBeLessThanOrEqualToOperand">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken. In effect if the <see cref="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.OperatorType">RuleValueComparisonProperties.OperatorType</see> is set to <see cref="F:DevExpress.Persistent.Validation.ValueComparisonType.LessThanOrEqual">ValueComparisonType.LessThanOrEqual</see>.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.MessageTemplateMustNotBeEqualToOperand">
      <summary>
        <para>Specifies the text that will be displayed in the Validation Error window when the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see> is broken. In effect if the <see cref="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.OperatorType">RuleValueComparisonProperties.OperatorType</see> is set to <see cref="F:DevExpress.Persistent.Validation.ValueComparisonType.NotEquals">ValueComparisonType.NotEquals</see>.</para>
      </summary>
      <value>A string holding the text that will be displayed in the Validation Error window when the Validation Rule is broken.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.OperatorType">
      <summary>
        <para>Specifies the operator type to be used for comparison.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Persistent.Validation.ValueComparisonType"/> enumeration values which specifies the operator type to be used for comparison.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.RightOperand">
      <summary>
        <para>Specifies the value to be compared with the target property’s value.</para>
      </summary>
      <value>An object which represents the value to be compared with the target property’s value.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.RightOperandExpression">
      <summary>
        <para>Specifies the expression whose value to be compared with the target property’s value.</para>
      </summary>
      <value>The string representation of an <see href="https://docs.devexpress.com/CoreLibraries/4928/devexpress-data-library/criteria-language-syntax">expression</see> whose value to be compared with the target property’s value.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.RuleValueComparisonProperties.TargetCollectionAggregate">
      <summary>
        <para>Specifies the aggregate function to be checked by the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>, if the rule is applied to a collection property.</para>
      </summary>
      <value>A Nullable&lt;Aggregate&gt; object which specifies the aggregate function to be checked by the Validation Rule.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.StringComparisonType">
      <summary>
        <para>Contains values which specify operator types to be used when comparing string values via the <see cref="T:DevExpress.Persistent.Validation.RuleStringComparisonAttribute"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.StringComparisonType.Contains">
      <summary>
        <para>Specifies that the target string property’s value must contain a particular substring.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.StringComparisonType.EndsWith">
      <summary>
        <para>Specifies that the target string property’s value must end with a particular substring.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.StringComparisonType.Equals">
      <summary>
        <para>Specifies that the target string property’s value must equal a particular string.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.StringComparisonType.NotEquals">
      <summary>
        <para>Specifies that the target string property’s value must not equal a particular string.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.StringComparisonType.StartsWith">
      <summary>
        <para>Specifies that the target string property’s value must start with a particular substring.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.ValidationCompletedEventArgs">
      <summary>
        <para>Arguments passed to the <see cref="E:DevExpress.Persistent.Validation.RuleSet.ValidationCompleted">RuleSet.ValidationCompleted</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ValidationCompletedEventArgs.#ctor(DevExpress.Persistent.Validation.RuleSetValidationResult,DevExpress.ExpressApp.IObjectSpace,DevExpress.ExpressApp.Frame)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.ValidationCompletedEventArgs"/> class with specified settings.</para>
      </summary>
      <param name="validationResult">The result of the entire <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> validation.</param>
      <param name="objectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</param>
      <param name="sourceFrame">The <see href="https://docs.devexpress.com/eXpressAppFramework/112608/ui-construction/windows-and-frames">Frame</see> of the Controller that validates objects. This parameter is used in WinForms applications only.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ValidationCompletedEventArgs.#ctor(DevExpress.Persistent.Validation.RuleSetValidationResult,DevExpress.ExpressApp.IObjectSpace)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.ValidationCompletedEventArgs"/> class.</para>
      </summary>
      <param name="validationResult">The result of the entire <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> validation.</param>
      <param name="objectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ValidationCompletedEventArgs.#ctor(DevExpress.Persistent.Validation.ValidationException,DevExpress.ExpressApp.IObjectSpace)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.ValidationCompletedEventArgs"/> class.</para>
      </summary>
      <param name="exception">An exception to raise if the validation fails.</param>
      <param name="objectSpace">An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> object used by the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ValidationCompletedEventArgs.Exception">
      <summary>
        <para>Specifies a validation exception that will be raised if the validation is unsuccessful.</para>
      </summary>
      <value>An exception to raise if the validation fails.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ValidationCompletedEventArgs.ObjectSpace">
      <summary>
        <para>Returns an <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</para>
      </summary>
      <value>An <see cref="T:DevExpress.ExpressApp.IObjectSpace"/> used by the <see cref="T:DevExpress.Persistent.Validation.RuleSet"/>.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ValidationCompletedEventArgs.Result">
      <summary>
        <para>Returns the result of the entire <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> validation.</para>
      </summary>
      <value>The validation result.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ValidationCompletedEventArgs.SourceFrame">
      <summary>
        <para>The <see href="https://docs.devexpress.com/eXpressAppFramework/112608/ui-construction/windows-and-frames">Frame</see> of the Controller that validates objects. Only WinForms applications use this property.</para>
      </summary>
      <value>The <see href="https://docs.devexpress.com/eXpressAppFramework/112608/ui-construction/windows-and-frames">Frame</see> of the Controller that validates objects.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ValidationCompletedEventArgs.Successful">
      <summary>
        <para>Indicates if the validation was successful.</para>
      </summary>
      <value>true, if the value if validation was successful; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.ValidationException">
      <summary>
        <para>Occurs if the validation of a RuleSet is unsuccessful.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ValidationException.#ctor(DevExpress.Persistent.Validation.RuleSetValidationResult,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.ValidationException"/> class.</para>
      </summary>
      <param name="result">The result of a <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> validation.</param>
      <param name="messageHeader">Use this parameter to complement a <see cref="P:DevExpress.Persistent.Validation.ValidationException.Message">ValidationException.Message</see>.</param>
      <param name="objectHeaderFormat">Use this parameter to complement a name of an invalid object.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ValidationException.#ctor(DevExpress.Persistent.Validation.RuleSetValidationResult)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.ValidationException"/> class.</para>
      </summary>
      <param name="result">The result of a <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> validation.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ValidationException.#ctor(DevExpress.Persistent.Validation.ValidationErrorMessageHandler,DevExpress.Persistent.Validation.RuleSetValidationResult)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.ValidationException"/> class.</para>
      </summary>
      <param name="messageHandler">Use this parameter to complement a <see cref="P:DevExpress.Persistent.Validation.ValidationException.Message">ValidationException.Message</see>.</param>
      <param name="result">The result of a <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> validation.</param>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ValidationException.#ctor(System.String,DevExpress.Persistent.Validation.RuleSetValidationResult)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Persistent.Validation.ValidationException"/> class.</para>
      </summary>
      <param name="message">A string that explains why the validation failed.</param>
      <param name="result">The result of a <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> validation.</param>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ValidationException.DefaultErrorMessageHandler">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value>For internal use.</value>
    </member>
    <member name="M:DevExpress.Persistent.Validation.ValidationException.GetMessages(DevExpress.Persistent.Validation.ValidationResultType)">
      <summary>
        <para>Returns the error message for a given outcome.</para>
      </summary>
      <param name="resultType">The <see cref="T:DevExpress.Persistent.Validation.ValidationResultType"/> element that represents the outcome type.</param>
      <returns>An error message string with the object name formatted by <see cref="P:DevExpress.Persistent.Validation.ValidationException.ObjectHeaderFormat">ValidationException.ObjectHeaderFormat</see>.</returns>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ValidationException.Message">
      <summary>
        <para>Returns an error message including a <see cref="P:DevExpress.Persistent.Validation.ValidationException.MessageHeader">ValidationException.MessageHeader</see>.</para>
      </summary>
      <value>The string that represents an error message.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ValidationException.MessageHeader">
      <summary>
        <para>The string that precedes a <see cref="P:DevExpress.Persistent.Validation.ValidationException.Message">ValidationException.Message</see>.</para>
      </summary>
      <value>The header string.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ValidationException.ObjectHeaderFormat">
      <summary>
        <para>A property that allows you to customize the string that represents an invalid object.</para>
      </summary>
      <value>The format string in which the “{0}” placeholder will be replaced by the object’s default property name.</value>
    </member>
    <member name="P:DevExpress.Persistent.Validation.ValidationException.Result">
      <summary>
        <para>The validation result.</para>
      </summary>
      <value>The result of a <see cref="T:DevExpress.Persistent.Validation.RuleSet"/> validation.</value>
    </member>
    <member name="T:DevExpress.Persistent.Validation.ValidationOutcome">
      <summary>
        <para>Contains values that specify the result of checking an object via a <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValidationOutcome.Error">
      <summary>
        <para>An error occurs.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValidationOutcome.Information">
      <summary>
        <para>An information is displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValidationOutcome.Skipped">
      <summary>
        <para>A rule is ignored by user.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValidationOutcome.Valid">
      <summary>
        <para>A rule is valid.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValidationOutcome.Warning">
      <summary>
        <para>A warning dialog is displayed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.ValidationResultType">
      <summary>
        <para>Contains values that specify the validation rule result type (error, warning or information).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValidationResultType.Error">
      <summary>
        <para>An error dialog is displayed when a rule is broken. A user must modify data in order to fix the error and proceed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValidationResultType.Information">
      <summary>
        <para>No dialog is displayed when a rule is broken. Instead, an information glyph appears near the rule’s target property in UI. Broken rule details are available in this glyph’s tooltip.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValidationResultType.Warning">
      <summary>
        <para>A warning dialog is displayed a rule is broken. A user can ignore this warning and proceed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.ValidationState">
      <summary>
        <para>Contains values that specify the possible states of a <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rule</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValidationState.Invalid">
      <summary>
        <para>Specifies that the validation rule has been checked and the current object did not satisfy it.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValidationState.Skipped">
      <summary>
        <para>Specifies that the validation rule has not been checked. This can be the case, for instance, when the validated property is not specified (see <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.SkipNullOrEmptyValues">RuleBaseAttribute.SkipNullOrEmptyValues</see>), or the validated object does not satisfy the specified TargetCriteria (see <see cref="P:DevExpress.Persistent.Validation.RuleBaseAttribute.TargetCriteria">RuleBaseAttribute.TargetCriteria</see>).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValidationState.Valid">
      <summary>
        <para>Specifies that the validation rule has been checked and the current object satisfied it.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.Validator">
      <summary>
        <para>Provides access to the <see href="https://docs.devexpress.com/eXpressAppFramework/113008/validation/validation-rules">Validation Rules</see> declared in an XAF application.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Persistent.Validation.ValueComparisonType">
      <summary>
        <para>Contains values which specify operator types to be used when comparing propery values via the <see cref="T:DevExpress.Persistent.Validation.RuleValueComparisonAttribute"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValueComparisonType.Equals">
      <summary>
        <para>Specifies that the target property’s value must equal a particular value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValueComparisonType.GreaterThan">
      <summary>
        <para>Specifies that the target property’s value must be greater then a particular value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValueComparisonType.GreaterThanOrEqual">
      <summary>
        <para>Specifies that the target property’s value must be greater then or equal to a particular value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValueComparisonType.LessThan">
      <summary>
        <para>Specifies that the target property’s value must be less than a particular value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValueComparisonType.LessThanOrEqual">
      <summary>
        <para>Specifies that the target property’s value must be less than or equal to a particular value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Persistent.Validation.ValueComparisonType.NotEquals">
      <summary>
        <para>Specifies that the target property’s value must not equal a particular value.</para>
      </summary>
    </member>
  </members>
</doc>