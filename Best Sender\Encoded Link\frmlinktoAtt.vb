﻿Imports System.IO
Imports System.Text
Imports System.Windows.Forms
Imports DevExpress.XtraBars.Ribbon
Imports DevExpress.XtraEditors
Public Class frmlinktoAtt
    Private IsDragging As Boolean = False
    Private MouseOffset As Point
    Dim dragging As Boolean
    Dim startPoint As Point
    Public Property RelatedRibbonPage As RibbonPage
    Private Sub frmlinktoAtt_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' إضافة معالج حدث لتغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmlinktoAtt_Resize

        ' توسيط العناصر كمجموعة واحدة
        CenterControlsAsGroup()

        ' إعداد ComboBox_SleepTime
        ComboBox_SleepTime.Properties.Items.Clear()
        ComboBox_SleepTime.Properties.Items.AddRange(New Object() {"1000", "2000", "3000", "4000", "5000", "6000", "7000", "8000", "9000"})
        ComboBox_SleepTime.Properties.NullValuePrompt = "Select Time (1000 = 1 second)"

        ' إعداد قيمة افتراضية
        ComboBox_SleepTime.EditValue = "5000" ' 5 ثوانٍ افتراضياً
    End Sub

    Private Sub frmlinktoAtt_Resize(sender As Object, e As EventArgs)
        ' توسيط العناصر كمجموعة واحدة عند تغيير حجم النموذج
        CenterControlsAsGroup()
    End Sub

    ''' <summary>
    ''' توسيط العناصر كمجموعة واحدة مع الحفاظ على ترتيبها النسبي
    ''' </summary>
    Private Sub CenterControlsAsGroup()
        Try
            ' التحقق من أن النموذج مهيأ بشكل كامل
            If Not Me.IsHandleCreated OrElse Me.IsDisposed Then
                Return
            End If

            ' إخفاء العناصر غير المرئية
            Button2.Visible = False
            Button4.Visible = False
            TextBox2.Visible = False
            TextBox7.Visible = False
            SeparatorControl1.Visible = False

            ' تحديد العناصر الرئيسية التي سيتم توسيطها
            Dim mainControls As New List(Of Control)()

            ' إضافة العناصر الرئيسية بترتيبها الصحيح
            If PictureBox1 IsNot Nothing AndAlso PictureBox1.Visible Then
                mainControls.Add(PictureBox1)
            End If



            If GroupControl1 IsNot Nothing AndAlso GroupControl1.Visible Then
                mainControls.Add(GroupControl1)
            End If

            ' التحقق من وجود عناصر للتوسيط
            If mainControls.Count = 0 Then
                Return
            End If

            ' حفظ المواقع الأصلية للعناصر
            Dim originalLocations As New Dictionary(Of Control, Point)()
            Dim originalSizes As New Dictionary(Of Control, Size)()

            ' حساب الحدود الخارجية للمجموعة
            Dim minX As Integer = Integer.MaxValue
            Dim minY As Integer = Integer.MaxValue
            Dim maxX As Integer = Integer.MinValue
            Dim maxY As Integer = Integer.MinValue

            ' حفظ المواقع الأصلية وحساب الحدود
            For Each ctrl As Control In mainControls
                ' حفظ الموقع والحجم الأصلي
                originalLocations.Add(ctrl, ctrl.Location)
                originalSizes.Add(ctrl, ctrl.Size)

                ' حساب الحدود
                minX = Math.Min(minX, ctrl.Left)
                minY = Math.Min(minY, ctrl.Top)
                maxX = Math.Max(maxX, ctrl.Left + ctrl.Width)
                maxY = Math.Max(maxY, ctrl.Top + ctrl.Height)
            Next

            ' حساب حجم المجموعة
            Dim groupWidth As Integer = maxX - minX
            Dim groupHeight As Integer = maxY - minY

            ' حساب الإزاحة المطلوبة لتوسيط المجموعة
            Dim offsetX As Integer = (Me.ClientSize.Width - groupWidth) \ 2 - minX
            Dim offsetY As Integer = (Me.ClientSize.Height - groupHeight) \ 2 - minY

            ' تطبيق الإزاحة على جميع العناصر للحفاظ على ترتيبها النسبي
            For Each ctrl As Control In mainControls
                Dim originalLocation As Point = originalLocations(ctrl)
                ctrl.Location = New Point(originalLocation.X + offsetX, originalLocation.Y + offsetY)
            Next

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in CenterControlsAsGroup: {ex.Message}")
        End Try
    End Sub

    ReadOnly FunctionList As List(Of String) = New List(Of String) From {"[-Email64-]", "[-Email-]", "[-IP-]", "[-IPChina-]", "[-Domain-]", "[-Name-]", "[-Date-]", "[-DateTomorrow-]",
        "[-RandomBrowser-]", "[-RCountry-]", "[-FakePhone-]", "[-FakeEmail-]", "[-NewYork-]", "[-UCase-]", "[-Link-]", "[-Logo-]", "[-RN1-]", "[-RN2-]", "[-RN3-]", "[-RN4-]",
        "[-RN5-]", "[-Word-]", "[-RN6-]", "[-RN7-]", "[-RN8-]", "[-RN9-]", "[-RN10-]", "[-RN12-]", "[-RCh1-]", "[-RCh2-]", "[-RCh3-]", "[-RCh4-]", "[-RCh5-]", "[-RCh6-]", "[-RCh7-]", "[-RCh8-]"}
    Private Sub ButtonSaveHTML_Click(sender As Object, e As EventArgs) Handles ButtonSaveHTML.Click
        If TextBox2.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please add Logo", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        If ComboBox_SleepTime.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Select Time Redirecting", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        If TextBox4.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Enter Length logo", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        If TextBox3.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Enter width Logo", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        Dim url As String = TextBox1.Text.Trim()

        ' Get the logo URL from TextBox2
        Dim logoUrl As String = TextBox2.Text.Trim()
        ' Get the height from TextBox3
        Dim height As String = TextBox3.Text.Trim()
        ' Get the width from TextBox4
        Dim width As String = TextBox4.Text.Trim()
        ' Get the countdown time from ComboBox_SleepTime and convert from milliseconds to seconds
        ' 1000 = 1 second, so divide by 1000
        Dim countdownTimeMs As Integer = Integer.Parse(ComboBox_SleepTime.Text.Trim())
        Dim countdownTime As Integer = countdownTimeMs \ 1000 ' تحويل من ميلي ثانية إلى ثانية
        If countdownTime < 1 Then countdownTime = 1 ' الحد الأدنى ثانية واحدة
        ' Generate the HTML code for the waiting page with the specified countdown time
        Dim htmlCode As String = "<!DOCTYPE html>" &
                         "<html>" &
                         "<head>" &
                         "<meta charset=""UTF-8"">" &
                         "<title>Waiting Page</title>" &
                         "<style>" &
                         "body {" &
                         "  font-family: Arial, sans-serif;" &
                         "  text-align: center;" &
                         "  margin-top: 100px;" &
                         "}" &
                         ".logo {" &
                         "  margin-bottom: 20px;" &
                         "}" &
                         "@keyframes pulse {" &
                         "  0% {" &
                         "    opacity: 1;" &
                         "  }" &
                         "  50% {" &
                         "    opacity: 0.5;" &
                         "  }" &
                         "  100% {" &
                         "    opacity: 1;" &
                         "  }" &
                         "}" &
                         "h1 {" &
                         "  animation: pulse 1s ease-in-out infinite;" &
                         "}" &
                         "</style>" &
                         "<script>" &
                         "function countdown() {" &
                         "  var seconds = " + countdownTime.ToString() + ";" &
                         "  var countdownElement = document.getElementById('countdown');" &
                         "  var countdownInterval = setInterval(function() {" &
                         "    countdownElement.textContent = seconds;" &
                         "    seconds--;" &
                         "    if (seconds < 0) {" &
                         "      clearInterval(countdownInterval);" &
                         "  window.location.href = '" + url + TextBox7.Text + TextBox8.Text + "';" &
                         "    }" &
                         "  }, 1000);" &
                         "}" &
                         "window.onload = countdown;" &
                         "</script>" &
                         "</head>" &
                         "<body>" &
                         "<div class=""logo""><img src=""" + logoUrl + """ alt=""Logo"" height=""" + height + """ width=""" + width + """></div>" &
                         "<h1>Please wait...</h1>" &
                         "<p>You will be redirected in <span id=""countdown"">" + countdownTime.ToString() + "</span> seconds.</p>" &
                         "</body>" &
                         "</html>"
        ' Display the HTML code in RichTextBox1
        RichTextBox1.Text = htmlCode
    End Sub

    Private Sub BNT_Backup_Click(sender As Object, e As EventArgs) Handles BNT_Backup.Click

        If RichTextBox1.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Click Encode Frist", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        ' تعريف المتغيرات اللازمة
        Dim saveFileDialog As New SaveFileDialog()
        ' تعيين الخصائص لمربع الحوار
        saveFileDialog.Filter = "File HTML (*.html)|*.html|All Files (*.*)|*.*"
        saveFileDialog.Title = "Save As"
        saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        ' افتح مربع الحوار وتحقق من اختيار المستخدم
        If saveFileDialog.ShowDialog() = DialogResult.OK Then
            ' احصل على المسار المحدد بواسطة المستخدم
            Dim filePath As String = saveFileDialog.FileName
            ' حفظ محتوى صفحة HTML في الملف
            Dim htmlContent As String = RichTextBox1.Text
            File.WriteAllText(filePath, htmlContent)
            ' عرض رسالة نجاح
            DevExpress.XtraEditors.XtraMessageBox.Show("Success File Saved : " & filePath)
            TextBox1.Text = ""
            RichTextBox1.Clear()
        End If
    End Sub

    Private Sub bntAddLogo_Click(sender As Object, e As EventArgs) Handles bntAddLogo.Click
        'TagBestSender.ShowDialog()
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "PNG Files (*.png)|*.png|JPEG Files (*.jpg)|*.jpg|All Image Files|*.png;*.jpg;*.jpeg;*.gif;*.bmp"
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            Try
                ' الحصول على المسار المحدد للصورة
                Dim imagePath As String = openFileDialog.FileName

                ' عرض الصورة في PictureBox2
                PictureBox2.Image = Image.FromFile(imagePath)

                ' عرض مسار الملف في TextBox2
                TextBox2.Text = imagePath

                ' قراءة محتوى الصورة
                Dim imageBytes As Byte() = File.ReadAllBytes(imagePath)
                ' تحويل محتوى الصورة إلى نص Base64
                Dim base64String As String = Convert.ToBase64String(imageBytes)
                ' إضافة بادئة "data:image/png;base64," إلى النص المشفر
                Dim encodedImage As String = "data:image/png;base64," + base64String
                ' عرض النص المشفر في TextBox2
                TextBox2.Text = encodedImage

            Catch ex As Exception
                DevExpress.XtraEditors.XtraMessageBox.Show("خطأ في تحميل الصورة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Dim text As String = ""
        text = RichTextBox1.Text
        Dim EncodedLetter As New RichTextBox
        EncodedLetter.Text = RichTextBox1.Text
        Dim lines As String() = text.Split(New String() {vbCrLf}, StringSplitOptions.None)
        Dim MemoEdit_ As New MemoEdit
        For i = 0 To lines.Length - 1
            If lines(i).Trim = "" Then
                GoTo nextLine
            Else
                MemoEdit_.Text = MemoEdit_.Text & vbNewLine & lines(i).Trim
                Dim line As String = lines(i).Trim
                Dim inputString As String = line.Trim
                Dim outputString As String = inputString.Replace("[", " [")
                outputString = outputString.Replace("]", "] ")
                Dim words As String() = outputString.Split(New Char() {" "c})
                For Each word As String In words
                    If word.Trim = "" Then GoTo skipEncryption
                    If word.StartsWith("[-") AndAlso word.EndsWith("-]") Then
                        GoTo skipEncryption
                    End If
                    Dim byt As Byte() = System.Text.Encoding.UTF8.GetBytes(word.ToString())
                    Dim Ecrypte_String = Convert.ToBase64String(byt)
                    Dim search As String = word
                    Dim replace As String = Ecrypte_String
                    Dim index As Integer = EncodedLetter.Text.IndexOf(search)
                    If index <> -1 Then
                        EncodedLetter.SelectionStart = index
                        EncodedLetter.SelectionLength = search.Length
                        EncodedLetter.SelectedText = replace
                    End If
skipEncryption:
                Next
            End If
nextLine:
        Next
        RichTextBox1.Text = EncodedLetter.Text
        Button4.PerformClick()
    End Sub

    Private Sub Button4_Click(sender As Object, e As EventArgs) Handles Button4.Click
        Dim htmlCode As String = $"<!DOCTYPE html><body id=""operations.rosario""></body><script>document.write(window.atob('{RichTextBox1.Text}'))</script>"
        RichTextBox1.Text = htmlCode
    End Sub

    Private Sub BntCopy_Click(sender As Object, e As EventArgs) Handles BntCopy.Click
        If RichTextBox1.Text <> "" Then
            ' Paste the text from the clipboard into the TextBox control named TextBox1
            RichTextBox1.Text = Clipboard.GetText()

        Else
            ' Show an error message if the clipboard does not contain a valid URL
            DevExpress.XtraEditors.XtraMessageBox.Show("Please put your link", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر bntPaste
    ''' </summary>
    Private Sub bntPaste_Click(sender As Object, e As EventArgs) Handles bntPaste.Click
        Try
            ' التحقق من وجود نص في الحافظة
            If Clipboard.ContainsText() Then
                ' لصق النص من الحافظة في TextBox1
                TextBox1.Text = Clipboard.GetText()
            Else
                ' عرض رسالة خطأ إذا لم تحتوي الحافظة على نص
                DevExpress.XtraEditors.XtraMessageBox.Show("لا يوجد نص في الحافظة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        Catch ex As Exception
            ' معالجة أي استثناءات قد تحدث
            DevExpress.XtraEditors.XtraMessageBox.Show("حدث خطأ أثناء لصق النص: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر BntReset
    ''' </summary>
    Private Sub BntReset_Click(sender As Object, e As EventArgs) Handles BntReset.Click
        Try
            ' إعادة تعيين جميع حقول الإدخال
            TextBox1.Text = ""
            TextBox3.Text = ""
            TextBox4.Text = ""
            ComboBox_SleepTime.Text = ""
            TextBox8.Text = "[-Email64-]"
            RichTextBox1.Text = ""

            ' إعادة تعيين أي عناصر أخرى إذا لزم الأمر
            TextBox2.Text = ""
            TextBox7.Text = ""

            ' تنظيف PictureBox2
            If PictureBox2.Image IsNot Nothing Then
                PictureBox2.Image.Dispose()
                PictureBox2.Image = Nothing
            End If

            ' عرض رسالة تأكيد
            DevExpress.XtraEditors.XtraMessageBox.Show("تم إعادة تعيين جميع الحقول بنجاح", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            ' معالجة أي استثناءات قد تحدث
            DevExpress.XtraEditors.XtraMessageBox.Show("حدث خطأ أثناء إعادة التعيين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير القيمة في ComboBox_SleepTime لعرض الوقت بالثواني
    ''' </summary>
    Private Sub ComboBox_SleepTime_EditValueChanged(sender As Object, e As EventArgs) Handles ComboBox_SleepTime.EditValueChanged
        Try
            If Not String.IsNullOrEmpty(ComboBox_SleepTime.Text) Then
                Dim value As Integer
                If Integer.TryParse(ComboBox_SleepTime.Text, value) Then
                    ' عرض الوقت بالثواني
                    Dim seconds As Double = value / 1000.0
                    ComboBox_SleepTime.Properties.NullValuePrompt = $"Selected: {seconds} seconds"
                Else
                    ComboBox_SleepTime.Properties.NullValuePrompt = "Select Time (1000 = 1 second)"
                End If
            Else
                ComboBox_SleepTime.Properties.NullValuePrompt = "Select Time (1000 = 1 second)"
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في التحقق
        End Try
    End Sub
End Class