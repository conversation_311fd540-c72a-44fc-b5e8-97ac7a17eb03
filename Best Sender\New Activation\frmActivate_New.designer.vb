﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmActivate_New
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmActivate_New))
        Me.TrmFadeIn = New System.Windows.Forms.Timer(Me.components)
        Me.TrmFadeOut = New System.Windows.Forms.Timer(Me.components)
        Me.BunifuDragControl1 = New Bunifu.Framework.UI.BunifuDragControl(Me.components)
        Me.ErrorProvider1 = New System.Windows.Forms.ErrorProvider(Me.components)
        Me.TextBox1 = New System.Windows.Forms.TextBox()
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.BunifuElipse1 = New Bunifu.Framework.UI.BunifuElipse(Me.components)
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.BntCLose = New DevExpress.XtraEditors.SimpleButton()
        Me.lblActivation = New DevExpress.XtraEditors.SimpleButton()
        Me.txtonlinekey = New DevExpress.XtraEditors.TextEdit()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.TXTUSERNAME = New DevExpress.XtraEditors.TextEdit()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.SeparatorControl3 = New DevExpress.XtraEditors.SeparatorControl()
        Me.BntTelegram = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.ErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtonlinekey.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TXTUSERNAME.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'TrmFadeIn
        '
        Me.TrmFadeIn.Enabled = True
        Me.TrmFadeIn.Interval = 15
        '
        'TrmFadeOut
        '
        Me.TrmFadeOut.Interval = 15
        '
        'BunifuDragControl1
        '
        Me.BunifuDragControl1.Fixed = True
        Me.BunifuDragControl1.Horizontal = True
        Me.BunifuDragControl1.TargetControl = Me
        Me.BunifuDragControl1.Vertical = True
        '
        'ErrorProvider1
        '
        Me.ErrorProvider1.ContainerControl = Me
        Me.ErrorProvider1.Icon = CType(resources.GetObject("ErrorProvider1.Icon"), System.Drawing.Icon)
        '
        'TextBox1
        '
        Me.TextBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(28, Byte), Integer), CType(CType(28, Byte), Integer), CType(CType(28, Byte), Integer))
        Me.TextBox1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.TextBox1.Font = New System.Drawing.Font("Tahoma", 9.75!)
        Me.TextBox1.ForeColor = System.Drawing.Color.Silver
        Me.TextBox1.Location = New System.Drawing.Point(1149, 25)
        Me.TextBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.TextBox1.Name = "TextBox1"
        Me.TextBox1.ReadOnly = True
        Me.TextBox1.Size = New System.Drawing.Size(46, 23)
        Me.TextBox1.TabIndex = 469
        Me.TextBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.TextBox1.Visible = False
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'BunifuElipse1
        '
        Me.BunifuElipse1.ElipseRadius = 50
        Me.BunifuElipse1.TargetControl = Me
        '
        'BntCLose
        '
        Me.BntCLose.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLose.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLose.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BntCLose.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLose.Appearance.Options.UseBackColor = True
        Me.BntCLose.Appearance.Options.UseBorderColor = True
        Me.BntCLose.Appearance.Options.UseFont = True
        Me.BntCLose.Appearance.Options.UseForeColor = True
        Me.BntCLose.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntCLose.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntCLose.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLose.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntCLose.AppearanceDisabled.Options.UseBackColor = True
        Me.BntCLose.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntCLose.AppearanceDisabled.Options.UseFont = True
        Me.BntCLose.AppearanceDisabled.Options.UseForeColor = True
        Me.BntCLose.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntCLose.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLose.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 12.25!)
        Me.BntCLose.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntCLose.AppearanceHovered.Options.UseBackColor = True
        Me.BntCLose.AppearanceHovered.Options.UseBorderColor = True
        Me.BntCLose.AppearanceHovered.Options.UseFont = True
        Me.BntCLose.AppearanceHovered.Options.UseForeColor = True
        Me.BntCLose.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLose.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLose.AppearancePressed.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntCLose.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntCLose.AppearancePressed.Options.UseBackColor = True
        Me.BntCLose.AppearancePressed.Options.UseBorderColor = True
        Me.BntCLose.AppearancePressed.Options.UseFont = True
        Me.BntCLose.AppearancePressed.Options.UseForeColor = True
        Me.BntCLose.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Exit32x32
        Me.BntCLose.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.RightCenter
        Me.BntCLose.Location = New System.Drawing.Point(203, 578)
        Me.BntCLose.Name = "BntCLose"
        Me.BntCLose.PaintStyle = DevExpress.XtraEditors.Controls.PaintStyles.Light
        Me.BntCLose.Size = New System.Drawing.Size(174, 36)
        Me.BntCLose.TabIndex = 495
        Me.BntCLose.Text = "Exit"
        Me.ToolTip1.SetToolTip(Me.BntCLose, "If you click here Exit App Best Sender VIP")
        '
        'lblActivation
        '
        Me.lblActivation.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.lblActivation.Appearance.Options.UseFont = True
        Me.lblActivation.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleRight
        Me.lblActivation.ImageOptions.SvgImage = CType(resources.GetObject("lblActivation.ImageOptions.SvgImage"), DevExpress.Utils.Svg.SvgImage)
        Me.lblActivation.Location = New System.Drawing.Point(189, 378)
        Me.lblActivation.Name = "lblActivation"
        Me.lblActivation.Size = New System.Drawing.Size(230, 53)
        Me.lblActivation.TabIndex = 482
        Me.lblActivation.Text = "Activate"
        '
        'txtonlinekey
        '
        Me.txtonlinekey.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtonlinekey.EditValue = ""
        Me.txtonlinekey.Location = New System.Drawing.Point(108, 303)
        Me.txtonlinekey.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtonlinekey.Name = "txtonlinekey"
        Me.txtonlinekey.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.txtonlinekey.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 11.25!, System.Drawing.FontStyle.Bold)
        Me.txtonlinekey.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtonlinekey.Properties.Appearance.Options.UseBackColor = True
        Me.txtonlinekey.Properties.Appearance.Options.UseFont = True
        Me.txtonlinekey.Properties.Appearance.Options.UseForeColor = True
        Me.txtonlinekey.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.txtonlinekey.Properties.NullValuePrompt = "Your license"
        Me.txtonlinekey.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtonlinekey.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtonlinekey.Size = New System.Drawing.Size(414, 38)
        Me.txtonlinekey.TabIndex = 480
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.BackColor = System.Drawing.Color.Transparent
        Me.Label3.ForeColor = System.Drawing.Color.LightCoral
        Me.Label3.Location = New System.Drawing.Point(530, 208)
        Me.Label3.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(11, 18)
        Me.Label3.TabIndex = 478
        Me.Label3.Text = " "
        '
        'Label4
        '
        Me.Label4.BackColor = System.Drawing.Color.Transparent
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.White
        Me.Label4.Location = New System.Drawing.Point(108, 493)
        Me.Label4.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label4.Name = "Label4"
        Me.Label4.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.Label4.Size = New System.Drawing.Size(360, 47)
        Me.Label4.TabIndex = 476
        Me.Label4.Text = "All rights reserved. copyright © 2023"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label4.Visible = False
        '
        'TXTUSERNAME
        '
        Me.TXTUSERNAME.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TXTUSERNAME.EditValue = ""
        Me.TXTUSERNAME.Location = New System.Drawing.Point(104, 201)
        Me.TXTUSERNAME.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TXTUSERNAME.Name = "TXTUSERNAME"
        Me.TXTUSERNAME.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.TXTUSERNAME.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TXTUSERNAME.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TXTUSERNAME.Properties.Appearance.Options.UseBackColor = True
        Me.TXTUSERNAME.Properties.Appearance.Options.UseFont = True
        Me.TXTUSERNAME.Properties.Appearance.Options.UseForeColor = True
        Me.TXTUSERNAME.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.TXTUSERNAME.Properties.NullValuePrompt = "Username / Email"
        Me.TXTUSERNAME.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TXTUSERNAME.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.TXTUSERNAME.Size = New System.Drawing.Size(418, 38)
        Me.TXTUSERNAME.TabIndex = 481
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.BackColor = System.Drawing.Color.Transparent
        Me.Label2.ForeColor = System.Drawing.Color.LightCoral
        Me.Label2.Location = New System.Drawing.Point(530, 315)
        Me.Label2.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(11, 18)
        Me.Label2.TabIndex = 479
        Me.Label2.Text = " "
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.ForeColor = System.Drawing.Color.White
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.Appearance.Options.UseForeColor = True
        Me.ProgressPanel1.AppearanceCaption.ForeColor = System.Drawing.Color.White
        Me.ProgressPanel1.AppearanceCaption.Options.UseForeColor = True
        Me.ProgressPanel1.AppearanceDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(2, Byte), Integer))
        Me.ProgressPanel1.AppearanceDescription.Options.UseForeColor = True
        Me.ProgressPanel1.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
        Me.ProgressPanel1.Location = New System.Drawing.Point(161, 371)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(292, 60)
        Me.ProgressPanel1.TabIndex = 488
        Me.ProgressPanel1.Text = "ProgressPanel1"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 12.25!)
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(2, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(68, 257)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(98, 28)
        Me.Label1.TabIndex = 493
        Me.Label1.Text = "Licensing"
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.Label7.Font = New System.Drawing.Font("Comfortaa", 12.25!)
        Me.Label7.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(2, Byte), Integer))
        Me.Label7.Location = New System.Drawing.Point(68, 155)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(62, 28)
        Me.Label7.TabIndex = 493
        Me.Label7.Text = "Email"
        '
        'PictureBox1
        '
        Me.PictureBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.UserNameImage
        Me.PictureBox1.Location = New System.Drawing.Point(66, 199)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(31, 40)
        Me.PictureBox1.TabIndex = 494
        Me.PictureBox1.TabStop = False
        '
        'PictureBox2
        '
        Me.PictureBox2.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.PictureBox2.Image = Global.Best_Sender.My.Resources.Resources.PassImage
        Me.PictureBox2.Location = New System.Drawing.Point(68, 301)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(31, 40)
        Me.PictureBox2.TabIndex = 494
        Me.PictureBox2.TabStop = False
        '
        'SeparatorControl3
        '
        Me.SeparatorControl3.LineThickness = 1
        Me.SeparatorControl3.Location = New System.Drawing.Point(657, 530)
        Me.SeparatorControl3.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl3.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl3.Name = "SeparatorControl3"
        Me.SeparatorControl3.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl3.Size = New System.Drawing.Size(350, 26)
        Me.SeparatorControl3.TabIndex = 327
        '
        'BntTelegram
        '
        Me.BntTelegram.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTelegram.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTelegram.Appearance.Font = New System.Drawing.Font("Comfortaa", 12.0!, System.Drawing.FontStyle.Bold)
        Me.BntTelegram.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTelegram.Appearance.Options.UseBackColor = True
        Me.BntTelegram.Appearance.Options.UseBorderColor = True
        Me.BntTelegram.Appearance.Options.UseFont = True
        Me.BntTelegram.Appearance.Options.UseForeColor = True
        Me.BntTelegram.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntTelegram.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntTelegram.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntTelegram.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntTelegram.AppearanceDisabled.Options.UseBackColor = True
        Me.BntTelegram.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntTelegram.AppearanceDisabled.Options.UseFont = True
        Me.BntTelegram.AppearanceDisabled.Options.UseForeColor = True
        Me.BntTelegram.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntTelegram.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTelegram.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntTelegram.AppearanceHovered.Options.UseBackColor = True
        Me.BntTelegram.AppearanceHovered.Options.UseBorderColor = True
        Me.BntTelegram.AppearanceHovered.Options.UseForeColor = True
        Me.BntTelegram.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTelegram.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTelegram.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntTelegram.AppearancePressed.Options.UseBackColor = True
        Me.BntTelegram.AppearancePressed.Options.UseBorderColor = True
        Me.BntTelegram.AppearancePressed.Options.UseForeColor = True
        Me.BntTelegram.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.telegram32x32
        Me.BntTelegram.Location = New System.Drawing.Point(731, 472)
        Me.BntTelegram.Name = "BntTelegram"
        Me.BntTelegram.Size = New System.Drawing.Size(190, 52)
        Me.BntTelegram.TabIndex = 334
        Me.BntTelegram.Text = "Telegram"
        '
        'frmActivate_New
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(27, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(26, Byte), Integer))
        Me.Appearance.ForeColor = System.Drawing.Color.Black
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackgroundImageLayoutStore = System.Windows.Forms.ImageLayout.Tile
        Me.BackgroundImageStore = Global.Best_Sender.My.Resources.Resources.LogoLogin
        Me.ClientSize = New System.Drawing.Size(1133, 636)
        Me.Controls.Add(Me.BntCLose)
        Me.Controls.Add(Me.PictureBox2)
        Me.Controls.Add(Me.SeparatorControl3)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.BntTelegram)
        Me.Controls.Add(Me.Label7)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.ProgressPanel1)
        Me.Controls.Add(Me.lblActivation)
        Me.Controls.Add(Me.txtonlinekey)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.TXTUSERNAME)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.TextBox1)
        Me.DoubleBuffered = True
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None
        Me.IconOptions.Icon = CType(resources.GetObject("frmActivate_New.IconOptions.Icon"), System.Drawing.Icon)
        Me.KeyPreview = True
        Me.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmActivate_New"
        Me.Opacity = 0R
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "  Panel Active ..."
        CType(Me.ErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtonlinekey.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TXTUSERNAME.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Private WithEvents TrmFadeIn As Timer
    Private WithEvents TrmFadeOut As Timer
    Friend WithEvents BunifuDragControl1 As Bunifu.Framework.UI.BunifuDragControl
    Friend WithEvents ErrorProvider1 As ErrorProvider
    Friend WithEvents TextBox1 As TextBox
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents BunifuElipse1 As Bunifu.Framework.UI.BunifuElipse
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents lblActivation As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtonlinekey As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label3 As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents TXTUSERNAME As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label2 As Label
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents Label7 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents PictureBox2 As PictureBox
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents SeparatorControl3 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents BntTelegram As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntCLose As DevExpress.XtraEditors.SimpleButton
End Class
