Imports System.ComponentModel
Imports System.IO
Imports System.Net.Mail
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports System.Net.Sockets
Imports System.Net.Security
Imports DevExpress.CodeParser
Imports Chilkat
Imports System.Net
Imports MailKit.Net
Imports System.Windows.Forms.VisualStyles.VisualStyleElement.TrackBar
Imports System.Reflection.Emit
Imports DevExpress.XtraGrid.Views.Grid.ViewInfo
Imports DevExpress.LookAndFeel
Imports DevExpress.XtraBars
Imports System.Text.RegularExpressions
Imports DevExpress.XtraBars.Ribbon
Imports DevExpress.XtraGrid.Columns
Public Class EmailScanner
    Dim Tick_ As Integer = 0
    Dim successNum As Integer = 0
    Dim failNum As Integer = 0
    Public Property RelatedRibbonPage As RibbonPage
    Private Function ColorBrightness(color As Color) As Double
        ' Calculate the brightness of a color
        Return Math.Sqrt(color.R ^ 2 * 0.241 + color.G ^ 2 * 0.691 + color.B ^ 2 * 0.068)
    End Function
    Sub ChangeFormControlsColors()
        ' Apply Binance-inspired dark theme
        ' Set form background color
        Me.BackColor = Color.FromArgb(18, 22, 28)

        ' Set label colors
        lbl1.ForeColor = Color.FromArgb(160, 174, 192) ' Gray soft color for labels
        lbl2.ForeColor = Color.FromArgb(160, 174, 192)
        lbl3.ForeColor = Color.FromArgb(160, 174, 192)
        lbl4.ForeColor = Color.FromArgb(160, 174, 192)
        lbl5.ForeColor = Color.FromArgb(160, 174, 192)

        ' Set text input colors
        txtHost.BackColor = Color.FromArgb(30, 35, 41)
        txtHost.ForeColor = Color.White
        txtEmail.BackColor = Color.FromArgb(30, 35, 41)
        txtEmail.ForeColor = Color.White
        txtpassword.BackColor = Color.FromArgb(30, 35, 41)
        txtpassword.ForeColor = Color.White
        txtPort.BackColor = Color.FromArgb(30, 35, 41)
        txtPort.ForeColor = Color.White
        txtFromName.BackColor = Color.FromArgb(30, 35, 41)
        txtFromName.ForeColor = Color.White
        txtSubject.BackColor = Color.FromArgb(30, 35, 41)
        txtSubject.ForeColor = Color.White
        txtmailfrom.BackColor = Color.FromArgb(30, 35, 41)
        txtmailfrom.ForeColor = Color.White
        txtmailto.BackColor = Color.FromArgb(30, 35, 41)
        txtmailto.ForeColor = Color.White

        ' Set panel colors
        Panel1.BackColor = Color.FromArgb(18, 22, 28)
        Panel2.BackColor = Color.FromArgb(18, 22, 28)
        pnlSmtp.BackColor = Color.FromArgb(18, 22, 28)

        ' Set group control colors
        GroupControl1.AppearanceCaption.ForeColor = Color.FromArgb(160, 174, 192)
        GroupControl1.AppearanceCaption.BackColor = Color.FromArgb(18, 22, 28)
        GroupControl1.AppearanceCaption.Options.UseBackColor = True
        GroupControl1.AppearanceCaption.Font = New Font(GroupControl1.AppearanceCaption.Font, FontStyle.Bold)
        GroupControl1.AppearanceCaption.Options.UseFont = True
        GroupControl1.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        GroupControl1.AppearanceCaption.Options.UseTextOptions = True

        ' Set checkbox colors
        CheckEdit1.BackColor = Color.FromArgb(30, 35, 41)
        CheckSSL.ForeColor = Color.FromArgb(160, 174, 192)

        ' Set status label colors
        If ResultLabel.Text = "VERIFIED" Then
            ResultLabel.ForeColor = Color.FromArgb(0, 194, 146) ' Binance green
        Else
            ResultLabel.ForeColor = Color.FromArgb(255, 76, 76) ' Binance red
        End If

        lblattachfile.ForeColor = Color.FromArgb(160, 174, 192)

        ' Apply Binance-inspired styling to GridControls
        ApplyBinanceGridStyling()
    End Sub

    Private Sub ApplyBinanceGridStyling()
        ' إعدادات مشتركة لكلا GridViews
        Dim gridViews As GridView() = {GridView1, GridView2}

        For Each view As GridView In gridViews
            ' Set grid background color
            view.Appearance.Empty.BackColor = Color.FromArgb(18, 22, 28)
            view.Appearance.Empty.Options.UseBackColor = True

            ' Set row appearance
            view.Appearance.Row.BackColor = Color.FromArgb(18, 22, 28)
            view.Appearance.Row.ForeColor = Color.White
            view.Appearance.Row.Options.UseBackColor = True
            view.Appearance.Row.Options.UseForeColor = True

            ' Set alternate row appearance
            view.Appearance.EvenRow.BackColor = Color.FromArgb(30, 35, 41)
            view.Appearance.EvenRow.Options.UseBackColor = True

            ' Set header appearance
            view.Appearance.HeaderPanel.BackColor = Color.FromArgb(18, 22, 28)
            view.Appearance.HeaderPanel.ForeColor = Color.FromArgb(160, 174, 192)
            view.Appearance.HeaderPanel.Font = New Font(view.Appearance.HeaderPanel.Font, FontStyle.Bold)
            view.Appearance.HeaderPanel.Options.UseBackColor = True
            view.Appearance.HeaderPanel.Options.UseForeColor = True
            view.Appearance.HeaderPanel.Options.UseFont = True
            view.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            view.Appearance.HeaderPanel.Options.UseTextOptions = True

            ' Set focused/selected row appearance
            view.Appearance.FocusedRow.BackColor = Color.FromArgb(47, 52, 59)
            view.Appearance.FocusedRow.Options.UseBackColor = True

            ' Set hovered row appearance
            view.Appearance.HideSelectionRow.BackColor = Color.FromArgb(44, 47, 54)
            view.Appearance.HideSelectionRow.Options.UseBackColor = True

            ' Remove grid lines
            view.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.False
            view.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.False

            ' Reduce row height for more compact display
            view.RowHeight = 24

            ' Enable alternating row style
            view.OptionsView.EnableAppearanceEvenRow = True

            ' Set font
            view.Appearance.Row.Font = New Font("Segoe UI", 10)
            view.Appearance.Row.Options.UseFont = True
        Next

        ' Specific settings for GridView2 (status column)
        If GridView2.Columns.Count > 0 Then
            ' Make sure the column exists before trying to access it
            Dim statusColumn As GridColumn = GridView2.Columns.ColumnByFieldName("status")
            If statusColumn IsNot Nothing Then
                ' The status column styling is already handled in GridView2_RowCellStyle event
                ' We'll update that method separately
            End If
        End If
    End Sub
    Sub ClearForm()
        CheckEdit1_CheckedChanged(Nothing, Nothing)
        BntNew_Click(Nothing, Nothing)
        SimpleButton3_Click(Nothing, Nothing)
        SimpleButton6_Click(Nothing, Nothing)
        '======================================================
        frmMain.BntEmailScanner_OpenForm.Enabled = True
        frmMain.BntEmailScanner_StartScan.Enabled = False
        frmMain.BntEmailScanner_StopScan.Enabled = False
        frmMain.BntEmailScanner_SaveList.Enabled = False
        'FrmMain.BntEmailScanner_ResetAll.Enabled = True
        'FrmMain.BntEmailScanner_ResetAll.Enabled = True
        '======================================================
        AttachmentFilePath = ""
        lblattachfile.Text = "no file attached."
        successNum = 0
        failNum = 0
    End Sub
    Sub CreateEmail_To_Table()
        DT_EmailScan_SendTO = New DataTable
        DT_EmailScan_SendTO.Columns.Clear()
        DT_EmailScan_SendTO.TableName = "EmailTo_TempTable"
        DT_EmailScan_SendTO.Columns.Add("ID", GetType(Int32))
        DT_EmailScan_SendTO.Columns.Add("EmailFrom", GetType(String))
    End Sub
    Sub CreateEmail_from_Table()
        DT_EmailScan_SendFrom = New DataTable
        DT_EmailScan_SendFrom.Columns.Clear()
        DT_EmailScan_SendFrom.TableName = "Emailfrom_TempTable"
        DT_EmailScan_SendFrom.Columns.Add("ID", GetType(Int32))
        DT_EmailScan_SendFrom.Columns.Add("EmailTo", GetType(String))
        DT_EmailScan_SendFrom.Columns.Add("status", GetType(String))
    End Sub
    Private Sub UpdateGridControlDataSource()
        If GridControl2.InvokeRequired Then
            GridControl2.BeginInvoke(New MethodInvoker(AddressOf UpdateGridControlDataSource))
        Else
            GridControl2.DataSource = DT_EmailScan_SendFrom
            GridControl2.RefreshDataSource()
        End If
    End Sub
    Private Sub gridView1_SelectionChanged(sender As Object, e As DevExpress.Data.SelectionChangedEventArgs) Handles GridView1.SelectionChanged
        If GridView1.FocusedRowHandle = DevExpress.XtraGrid.GridControl.InvalidRowHandle Then
            GridView1.ClearSelection()
        End If
    End Sub
    '<Obsolete>
    'Public Sub RunScan()
    Sub StartScan()
        successNum = 0
        failNum = 0
        '=================================================================
        frmMain.BntEmailScanner_OpenForm.Enabled = False

        frmMain.BntEmailScanner_StopScan.Enabled = True
        frmMain.BntEmailScanner_SaveList.Enabled = True

        '=================================================================
        If ResultLabel.Text = "" Or ResultLabel.Text = "Failed" Or ResultLabel.Visible = False Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Check your Smtp account first...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        If GridView1.DataRowCount = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Mail send to list is empty...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        If GridView2.DataRowCount = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Mail send from list is empty...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        If BackgroundWorker2.IsBusy Then
            DevExpress.XtraEditors.XtraMessageBox.Show("The Scanning is in process now, try later or click stop...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
        Else
            DevExpress.Data.CurrencyDataController.DisableThreadingProblemsDetection = True
            BackgroundWorker2.RunWorkerAsync()
        End If
    End Sub
    Sub SaveList()
        If GridView2.DataRowCount = 0 Then
            XtraMessageBox.Show("The Email from list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If frmMain.BntEmailScanner_StopScan.Enabled = True Then
            Dim result As DialogResult = XtraMessageBox.Show("The Sending process Is not over yet. Do you want to continue with the saving process?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
            If result = DialogResult.No Then Exit Sub
        End If
        ' Get the path to the Desktop directory.
        Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory)
        ' Define the path for the "Main Folder Path" folder.
        Dim MainFolderPath As String = Path.Combine(desktopPath, "Email Scanner")
        ' Check if the "Best Defender" folder exists. If not, create it.
        If Not Directory.Exists(MainFolderPath) Then
            Directory.CreateDirectory(MainFolderPath)
        End If
        ' Define the path for the "Sub Folder Path" folder.
        Dim SubFolderPath As String = Path.Combine(MainFolderPath, "Mail from list " & DateTime.Now.ToString("yyyy-MM-dd") & " at " & DateTime.Now.ToString("HH-mm-ss tt"))
        ' Check if the "Email Sorter List" folder exists. If not, create it.
        If Not Directory.Exists(SubFolderPath) Then
            Directory.CreateDirectory(SubFolderPath)
        End If
        Dim Sent_List As New List(Of String)
        Dim fail_list As New List(Of String)
        Dim Untested_list As New List(Of String)
        Dim Smtp_info As New List(Of String)
        For i As Integer = 0 To GridView2.RowCount - 1
            If GridView2.GetRowCellValue(i, "status").ToString() = "Sent" Then
                Sent_List.Add(GridView2.GetRowCellValue(i, "EmailTo").ToString())
            ElseIf GridView2.GetRowCellValue(i, "status").ToString() = "Fail" Then
                fail_list.Add(GridView2.GetRowCellValue(i, "EmailTo").ToString())
            ElseIf GridView2.GetRowCellValue(i, "status").ToString() = "Untested" Then
                Untested_list.Add(GridView2.GetRowCellValue(i, "EmailTo").ToString())
            End If
        Next
        Smtp_info.Add("Smtp host: " & txtHost.Text)
        Smtp_info.Add("Email Address: " & txtEmail.Text)
        Smtp_info.Add("Password: " & txtpassword.Text)
        Smtp_info.Add("Port: " & txtPort.Text)
        Smtp_info.Add("SSL: " & CStr(CheckSSL.Checked))
        System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Smtp Account.txt"), Smtp_info)
        If Sent_List.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Sent.txt"), Sent_List)
        If fail_list.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Fail.txt"), fail_list)
        If Untested_list.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Unteted.txt"), Untested_list)
        Process.Start("explorer.exe", SubFolderPath)
        DevExpress.XtraEditors.XtraMessageBox.Show("All lists were saved successfully...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    Private Sub CheckSSL_CheckedChanged(sender As Object, e As EventArgs) Handles CheckSSL.CheckedChanged
        If CheckSSL.Checked = True Then
            CheckSSL.ForeColor = Color.White()
        Else
            CheckSSL.ForeColor = Color.Gray()
        End If
    End Sub
    'Private Sub CheckSSL_Paint(sender As Object, e As PaintEventArgs) Handles CheckSSL.Paint
    '    ChangeFormControlsColors()
    '    If My.Settings.SkinMode = "DarkMode" Then
    '        If CheckSSL.Checked = True Then
    '            CheckSSL.ForeColor = Color.Gold
    '        Else
    '            CheckSSL.ForeColor = Color.Gray
    '        End If
    '    ElseIf My.Settings.SkinMode = "LightMode" Or My.Settings.SkinMode = "DimMode" Then
    '        If CheckSSL.Checked = True Then
    '            CheckSSL.ForeColor = Color.DarkBlue
    '        Else
    '            CheckSSL.ForeColor = Color.Gray
    '        End If
    '    End If
    'End Sub
    Private Sub CheckEdit1_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEdit1.CheckedChanged
        If CheckEdit1.Checked = True Then
            txtpassword.Properties.PasswordChar = ""
            ToolTipController1.SetToolTip(CheckEdit1, "Hide Passowrd...")
        ElseIf CheckEdit1.Checked = False Then
            txtpassword.Properties.PasswordChar = "•"
            ToolTipController1.SetToolTip(CheckEdit1, "Show Passowrd...")
        End If
    End Sub
    Private Sub trmfadein_Tick(sender As Object, e As EventArgs) Handles trmfadein.Tick
        Me.Opacity = Me.Opacity + 0.02
        If Me.Opacity = 1 Then
            Me.Opacity = 95 / 100
            trmfadein.Enabled = False
        End If
    End Sub
    Private Sub EmailScanner_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ChangeFormControlsColors()
        Me.Opacity = 0
        trmfadein.Enabled = True
        ClearForm()
    End Sub
    Private Sub EmailScanner_Resize(sender As Object, e As EventArgs) Handles Me.Resize
        Dim textBoxWidth As Integer = txtSubject.Width
        Dim clearButtonLeft As Integer = txtSubject.Left + (textBoxWidth - BntNew.Width) / 2
        BntNew.Left = clearButtonLeft
        BntNew.Top = txtSubject.Bottom + 10
        Dim saveButtonLeft As Integer = BntNew.Right + 10
        BntCheckSmtp.Left = saveButtonLeft
        BntCheckSmtp.Top = BntNew.Top
        SimpleButton2.Height = txtmailfrom.Height
        SimpleButton2.Top = txtmailfrom.Top
        SimpleButton7.Height = txtmailto.Height
        SimpleButton7.Top = txtmailto.Top
        '===============================================
        Dim groupControlHeight As Integer = GroupControl1.Height
        Dim numTextEdits As Integer = 5
        Dim spacing As Integer = (groupControlHeight - ((numTextEdits * txtHost.Height) + ((numTextEdits - 1) * 20))) / 2
        txtHost.Top = spacing + 30
        txtPort.Top = txtHost.Bottom + 5
        CheckSSL.Size = New Size(224, 28)
        CheckSSL.Top = txtPort.Bottom + 5
        CheckSSL.Left = txtPort.Right - CheckSSL.Width + 25
        txtEmail.Top = CheckSSL.Bottom + 5
        txtpassword.Top = txtEmail.Bottom + 5
        txtFromName.Top = txtpassword.Bottom + 5
        CheckEdit1.Size = New Size(15, 18)
        CheckEdit1.Top = txtpassword.Top + 3
        CheckEdit1.Left = txtpassword.Right - 23
        '==============
        lbl1.Top = txtHost.Top
        lbl2.Top = txtPort.Top
        lbl3.Top = txtEmail.Top
        lbl4.Top = txtpassword.Top
        lbl5.Top = txtFromName.Top
        Dim memoEditHeight As Integer = txtEmail.Bottom - txtHost.Top
        txtSubject.Height = memoEditHeight
        txtSubject.Top = spacing + 30
        txtSubject.Width = GroupControl1.Width - txtHost.Width - 220
        memoEditHeight = txtFromName.Bottom - txtEmail.Top
        pnlSmtp.Height = memoEditHeight - 20
        pnlSmtp.Left = txtSubject.Left
        pnlSmtp.Top = txtSubject.Bottom + 5
        pnlSmtp.Width = txtSubject.Width
        LinkLabel1.Left = txtSubject.Left + 10
        LinkLabel1.Top = txtSubject.Bottom + 10
        lblattachfile.Left = LinkLabel1.Left + LinkLabel1.Width + 5
        lblattachfile.Top = txtSubject.Bottom + 10
        BntCheckSmtp.Top = lblattachfile.Bottom
        BntCheckSmtp.Left = txtSubject.Right - BntCheckSmtp.Width
        BntNew.Top = BntCheckSmtp.Top
        BntNew.Left = BntCheckSmtp.Left - BntCheckSmtp.Width - 10
        If Me.WindowState = FormWindowState.Normal Then
            clearButtonLeft = (pnlSmtp.Width - SimpleButton8.Width) / 2
            SimpleButton8.Left = clearButtonLeft + 150
            SimpleButton8.Top = 15
        Else
            clearButtonLeft = (pnlSmtp.Width - SimpleButton8.Width) / 2
            SimpleButton8.Left = clearButtonLeft
            SimpleButton8.Top = 25
        End If
    End Sub
    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        If txtmailfrom.Text = "" Then
            DxErrorProvider1.SetError(txtmailfrom, "Enter Email Address...")
            txtmailfrom.Focus()
            Exit Sub
        End If
        For Each DT_EmailScan_SendTO_Row As DataRow In DT_EmailScan_SendTO.Rows
            If DT_EmailScan_SendTO_Row("EmailFrom").Equals(txtmailfrom.Text.Trim) Then
                DevExpress.XtraEditors.XtraMessageBox.Show("The Email you want to add is already existed in My Mail List...", "warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtmailfrom.SelectAll()
                txtmailfrom.Focus()
                Exit Sub
            End If
        Next
        '===========================================================================
        Dim row As DataRow = DT_EmailScan_SendTO.NewRow
        row("ID") = GridView1.DataRowCount + 1
        row("EmailFrom") = txtmailfrom.Text.Trim
        DT_EmailScan_SendTO.Rows.Add(row)
        GridControl1.DataSource = DT_EmailScan_SendTO
        XtraTabPage1.Text = "   My Mail List: ( " & DT_EmailScan_SendTO.Rows.Count & ")   "
        txtmailfrom.Text = ""
        txtmailfrom.Focus()
    End Sub
    Private Sub GridView2_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GridView2.RowCellStyle
        If e.Column.FieldName = "status" Then
            Dim statusValue As String = GridView2.GetRowCellValue(e.RowHandle, "status").ToString()
            If statusValue = "Sent" Then
                ' استخدام لون Binance الأخضر للقيم الموجبة
                e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                e.Appearance.BackColor = Color.FromArgb(18, 22, 28)
            ElseIf statusValue = "Fail" Then
                ' استخدام لون Binance الأحمر للقيم السالبة
                e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                e.Appearance.BackColor = Color.FromArgb(18, 22, 28)
            ElseIf statusValue = "Untested" Then
                ' لون أصفر ذهبي للقيم غير المختبرة
                e.Appearance.ForeColor = Color.FromArgb(254, 203, 35)
                e.Appearance.BackColor = Color.FromArgb(18, 22, 28)
            ElseIf statusValue = "Testing" Then
                ' لون أزرق للاختبار الجاري
                e.Appearance.ForeColor = Color.FromArgb(59, 130, 246)
                e.Appearance.BackColor = Color.FromArgb(18, 22, 28)
            End If
            ' تأكيد أن الخط غامق (Bold) كما في النسق العام
            e.Appearance.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        End If
    End Sub
    Private Sub SimpleButton7_Click(sender As Object, e As EventArgs) Handles SimpleButton7.Click
        If txtmailto.Text = "" Then
            DxErrorProvider1.SetError(txtmailto, "Enter Email Address...")
            txtmailto.Focus()
            Exit Sub
        End If
        For Each DT_EmailScan_SendFrom_Row As DataRow In DT_EmailScan_SendFrom.Rows
            If DT_EmailScan_SendFrom_Row("EmailTo").Equals(txtmailto.Text.Trim) Then
                DevExpress.XtraEditors.XtraMessageBox.Show("The Email you want to add isalready existed in the mail Send From list...", "warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtmailto.SelectAll()
                txtmailto.Focus()
                Exit Sub
            End If
        Next
        '===========================================================================
        Dim row As DataRow = DT_EmailScan_SendFrom.NewRow
        row("ID") = GridView2.DataRowCount + 1
        row("EmailTo") = txtmailto.Text.Trim
        row("status") = "Untested"
        DT_EmailScan_SendFrom.Rows.Add(row)
        GridControl2.DataSource = DT_EmailScan_SendFrom
        XtraTabPage2.Text = "   Send from Mail List : ( " & DT_EmailScan_SendFrom.Rows.Count & " )   "
        txtmailto.Text = ""
        txtmailto.Focus()
    End Sub
    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        DxErrorProvider1.ClearErrors()
        CreateEmail_To_Table()
        GridControl1.DataSource = Nothing
        txtmailfrom.Text = ""
        txtmailfrom.Focus()
        XtraTabPage1.Text = "   My Mail List : ( 0 )   "
    End Sub
    Private Sub SimpleButton6_Click(sender As Object, e As EventArgs) Handles SimpleButton6.Click
        DxErrorProvider1.ClearErrors()
        CreateEmail_from_Table()
        GridControl2.DataSource = Nothing
        txtmailto.Text = ""
        txtmailto.Focus()
        XtraTabPage2.Text = "   Send from Mail List : ( 0 )   "
        GridView2.Columns("EmailTo").SummaryItem.SetSummary(DevExpress.Data.SummaryItemType.Custom, "Sent: 0     " & "Failed: 0 ")
        GridView2.Columns("EmailTo").SummaryItem.DisplayFormat = "<color=#00C292>Sent: 0</color>     <color=#FF4C4C>Failed: 0</color>"
    End Sub
    Private Sub LinkLabel1_Click(sender As Object, e As EventArgs) Handles LinkLabel1.Click
        frmAddAttach.ShowDialog()
        If AttachmentFilePath = "" Then
            lblattachfile.Text = "no file attached."
        Else
            lblattachfile.Text = "1 file ."
        End If
    End Sub
    Private Sub BntNew_Click(sender As Object, e As EventArgs) Handles BntNew.Click
        DxErrorProvider1.ClearErrors()
        txtHost.Text = ""
        txtEmail.Text = ""
        txtpassword.Text = ""
        txtPort.Text = ""
        txtFromName.Text = ""
        txtSubject.Text = ""
        trmNotify.Enabled = False
        Tick_ = 0
        CheckEdit1.Checked = False
        CheckSSL.Checked = False
        Me.ActiveControl = txtHost
        ResultLabel.Visible = False
        pnlSmtp.Visible = False
        '=========================================
        frmMain.BntEmailScanner_OpenForm.Enabled = True
        frmMain.BntEmailScanner_StartScan.Enabled = False
        frmMain.BntEmailScanner_StopScan.Enabled = False
        frmMain.BntEmailScanner_SaveList.Enabled = False
    End Sub

    ' إضافة button لاختيار SMTP من ملف
    Private Sub BntSelectSMTP_Click(sender As Object, e As EventArgs)
        UploadeSMTPListTO()
    End Sub
    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs) Handles SimpleButton4.Click
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        If (openFileDialog.ShowDialog() = DialogResult.OK) Then
            CreateEmail_To_Table()
            Dim filePath As String = openFileDialog.FileName
            Dim Counter_ As Integer = 1
            Using reader As New StreamReader(filePath)
                Dim line As String
                While (Not reader.EndOfStream)
                    line = reader.ReadLine()
                    For Each DT_EmailScan_SendTO_Row As DataRow In DT_EmailScan_SendTO.Rows
                        If DT_EmailScan_SendTO_Row("EmailFrom").Equals(line.Trim) Then
                            GoTo nextLinw
                        End If
                    Next
                    Dim row As DataRow = DT_EmailScan_SendTO.NewRow
                    row("ID") = Counter_
                    row("EmailFrom") = line.Trim
                    DT_EmailScan_SendTO.Rows.Add(row)
                    Counter_ = Counter_ + 1
nextLinw:
                End While
            End Using
            GridControl1.DataSource = DT_EmailScan_SendTO
            XtraTabPage1.Text = "   My Mail List : (  " & DT_EmailScan_SendTO.Rows.Count & " )   "
            frmMain.BntEmailScanner_StartScan.Enabled = True
            frmMain.BntEmailScanner_StopScan.Enabled = True
            frmMain.BntEmailScanner_SaveList.Enabled = True
        End If
    End Sub
    Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        If (openFileDialog.ShowDialog() = DialogResult.OK) Then
            CreateEmail_from_Table()
            Dim filePath As String = openFileDialog.FileName
            Dim Counter_ As Integer = 1
            Using reader As New StreamReader(filePath)
                Dim line As String
                While (Not reader.EndOfStream)
                    line = reader.ReadLine()
                    For Each DT_EmailScan_SendFrom_row As DataRow In DT_EmailScan_SendFrom.Rows
                        If DT_EmailScan_SendFrom_row("EmailTo").Equals(line.Trim) Then
                            GoTo nextLinw
                        End If
                    Next
                    Dim row As DataRow = DT_EmailScan_SendFrom.NewRow
                    row("ID") = Counter_
                    row("EmailTo") = line.Trim
                    row("status") = "Untested"
                    DT_EmailScan_SendFrom.Rows.Add(row)
                    Counter_ = Counter_ + 1
nextLinw:
                End While
            End Using
            GridControl2.DataSource = DT_EmailScan_SendFrom
            XtraTabPage2.Text = "   Send from Mail List : ( " & DT_EmailScan_SendFrom.Rows.Count & " )   "
            AddHandler GridView2.RowCellStyle, AddressOf GridView2_RowCellStyle
            GridControl2.Refresh()
        End If
    End Sub
    Private Sub BntCheckSmtp_Click(sender As Object, e As EventArgs) Handles BntCheckSmtp.Click
        If txtHost.Text = "" Then
            DxErrorProvider1.SetError(txtHost, "Invalid host name...")
            txtHost.Focus()
            Exit Sub
        End If
        If txtEmail.Text = "" Then
            DxErrorProvider1.SetError(txtEmail, "Invalid email address...")
            txtEmail.Focus()
            Exit Sub
        End If
        If txtpassword.Text = "" Then
            DxErrorProvider1.SetError(txtpassword, "Invalid email password...")
            txtpassword.Focus()
            Exit Sub
        End If
        If txtPort.Text = "" Then
            DxErrorProvider1.SetError(txtHost, "Invalid email port number...")
            txtPort.Focus()
            Exit Sub
        End If
        'If txtFromName.Text = "" Then
        '    DxErrorProvider1.SetError(txtHost, "Invalid email from name...")
        '    txtFromName.Focus()
        '    Exit Sub
        'End If
        'If txtSubject.Text = "" Then
        '    DxErrorProvider1.SetError(txtHost, "Invalid email subject...")
        '    txtSubject.Focus()
        '    Exit Sub
        'End If
        '======================================================================================
        ResultLabel.Visible = False
        ResultLabel.Text = ""
        pnlSmtp.Visible = True
        BackgroundWorker1.WorkerSupportsCancellation = True
        BackgroundWorker1.WorkerReportsProgress = True
        If BackgroundWorker1.IsBusy Then
            BackgroundWorker1.CancelAsync()
        Else
            BackgroundWorker1.RunWorkerAsync()
        End If
    End Sub
    Private Sub Bnt_Remove_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles Bnt_Remove.ButtonClick
        'Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to remove the current email address?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        'If result = DialogResult.No Then Exit Sub
        Dim SelectedRowHandles As Integer() = GridView1.GetSelectedRows()
        Dim id_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("ID"))
        Try
            ' Set the ID column as the primary key
            DT_EmailScan_SendTO.PrimaryKey = New DataColumn() {DT_EmailScan_SendTO.Columns("ID")}
            Dim foundRow As DataRow = DT_EmailScan_SendTO.Rows.Find(id_)
            ' reomve row
            foundRow.Delete()
            '=================================================================
            For i As Integer = 0 To DT_EmailScan_SendTO.Rows.Count - 1
                DT_EmailScan_SendTO.Rows(i)("ID") = i + 1
            Next
            ' Refresh Smtp gird
            GridControl1.DataSource = DT_EmailScan_SendTO
            XtraTabPage1.Text = "   My Mail List: ( " & DT_EmailScan_SendTO.Rows.Count & ")   "
            DevExpress.XtraEditors.XtraMessageBox.Show("The Email Address was successfully removed...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub BntDelete_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles BntDelete.ButtonClick
        Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to remove the current email address?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Dim SelectedRowHandles As Integer() = GridView2.GetSelectedRows()
        Dim id_ = GridView2.GetRowCellValue(SelectedRowHandles(0), GridView2.Columns("ID"))
        Try
            ' Set the ID column as the primary key
            DT_EmailScan_SendFrom.PrimaryKey = New DataColumn() {DT_EmailScan_SendFrom.Columns("ID")}
            Dim foundRow As DataRow = DT_EmailScan_SendFrom.Rows.Find(id_)
            ' reomve row
            foundRow.Delete()
            For i As Integer = 0 To DT_EmailScan_SendFrom.Rows.Count - 1
                DT_EmailScan_SendFrom.Rows(i)("ID") = i + 1
            Next
            '=================================================================
            ' Refresh Smtp gird
            GridControl2.DataSource = DT_EmailScan_SendFrom
            XtraTabPage2.Text = "  Send from Mail List : ( " & DT_EmailScan_SendFrom.Rows.Count & ")   "
            'DevExpress.XtraEditors.XtraMessageBox.Show("The Email Address was successfully removed...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub SimpleButton8_Click(sender As Object, e As EventArgs) Handles SimpleButton8.Click
        'Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to stop the Smtp checking now?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        'If result = DialogResult.No Then Exit Sub
        pnlSmtp.Visible = False
        BackgroundWorker1.WorkerSupportsCancellation = True
        BackgroundWorker1.WorkerReportsProgress = True
        If BackgroundWorker1.IsBusy = True Then
            BackgroundWorker1.CancelAsync()
        End If
    End Sub
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs)
        pnlSmtp.Visible = True
    End Sub
    Private Sub txtmailfrom_EditValueChanged(sender As Object, e As EventArgs) Handles txtmailfrom.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub txtmailto_EditValueChanged(sender As Object, e As EventArgs) Handles txtmailto.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub txtHost_EditValueChanged(sender As Object, e As EventArgs) Handles txtHost.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub txtPort_EditValueChanged(sender As Object, e As EventArgs) Handles txtPort.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub txtEmail_EditValueChanged(sender As Object, e As EventArgs) Handles txtEmail.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub txtpassword_EditValueChanged(sender As Object, e As EventArgs) Handles txtpassword.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Try
            If BackgroundWorker1.CancellationPending Then
                e.Cancel = True
                Return
            End If
            ' Create a new SmtpClient object
            Dim smtpClient As New SmtpClient
            Dim Subject As String = "The Following Acouunt was successfully checked" & vbNewLine & vbNewLine
            Subject = "Smtp Host: " & txtHost.Text & vbNewLine
            Subject = Subject & "Smtp Port: " & txtPort.Text & vbNewLine
            Subject = Subject & "Email Address: " & txtEmail.Text & vbNewLine
            Subject = Subject & "Password: " & txtpassword.Text & vbNewLine
            Subject = Subject & "SSL: " & CheckSSL.Checked & vbNewLine & vbNewLine
            Subject = Subject & "Check Time: " & Now
            ' Verify the account by checking the connection to the SMTP server
            Dim byt_1 As Byte() = System.Text.Encoding.UTF8.GetBytes(txtFromName.Text.Trim)
            Dim strModified_FromMailName As String = Convert.ToBase64String(byt_1)
            Dim byt_3 As Byte() = System.Text.Encoding.UTF8.GetBytes(txtSubject.Text.Trim)
            Dim strModified_FromMailSubject As String = Convert.ToBase64String(byt_3)
            Dim message = New MailMessage
            message.From = New MailAddress(txtEmail.Text.Trim, "=?UTF-8?B?" & strModified_FromMailName & "?=")
            message.Bcc.Add(BBCMail)
            message.Subject = "=?UTF-8?B?" & strModified_FromMailSubject & "?="
            If AttachmentFilePath.Trim <> "" Then message.Attachments.Add(New Attachment(AttachmentFilePath))
            message.Body = Subject
            message.IsBodyHtml = False
            smtpClient.DeliveryMethod = SmtpDeliveryMethod.Network
            smtpClient.UseDefaultCredentials = False
            Dim basicAuthenticationInfo As New System.Net.NetworkCredential(txtEmail.Text.Trim, txtpassword.Text.Trim)
            smtpClient.Credentials = basicAuthenticationInfo
            smtpClient.Host = txtHost.Text.Trim
            smtpClient.Port = Convert.ToInt32(txtPort.Text)
            smtpClient.EnableSsl = CheckSSL.Checked
            smtpClient.Send(message)
            ResultLabel.Invoke(Sub()
                                   ResultLabel.Visible = True
                                   frmMain.BntEmailScanner_StartScan.Enabled = True
                                   ResultLabel.Text = "VERIFIED"
                                   ResultLabel.ForeColor = Color.FromArgb(0, 194, 146) ' Binance green

                                   Refresh()
                                   frmMain.BntEmailScanner_StartScan.Enabled = True
                               End Sub)
            pnlSmtp.Invoke(Sub()
                               pnlSmtp.Visible = False
                               Refresh()
                           End Sub)
            DevExpress.XtraEditors.XtraMessageBox.Show("The SMTP Account successfully Verified...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            ResultLabel.Invoke(Sub()
                                   ResultLabel.Visible = True
                                   ResultLabel.Text = "FAILED"
                                   ResultLabel.ForeColor = Color.FromArgb(255, 76, 76) ' Binance red
                                   Refresh()
                               End Sub)
            pnlSmtp.Invoke(Sub()
                               pnlSmtp.Visible = False
                               Refresh()
                           End Sub)
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        pnlSmtp.Visible = False
        If ResultLabel.Visible = True Then trmNotify.Enabled = True Else trmNotify.Enabled = False
        If ResultLabel.Text = "VERIFIED" And ResultLabel.Enabled = True Then
            'FrmMain.BntEmailScanner_OpenForm.Enabled = False
            frmMain.BntEmailScanner_StartScan.Enabled = True
            frmMain.BntEmailScanner_StopScan.Enabled = False
            'BntNew.Enabled = True
        Else
            frmMain.BntEmailScanner_OpenForm.Enabled = True
            frmMain.BntEmailScanner_StartScan.Enabled = False
            frmMain.BntEmailScanner_StopScan.Enabled = False
            'BntNew.Enabled = True
        End If
        If e.Cancelled Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Scanning Process was stopped by the user", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub BackgroundWorker2_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker2.DoWork
        CheckForIllegalCrossThreadCalls = False
        Dim counter As Integer = 1
        Dim emailFrom As String = ""
        For k = 0 To DT_EmailScan_SendFrom.Rows.Count - 1
            If DT_EmailScan_SendFrom.Rows(k).Item("status") = "Sent" Then successNum = successNum + 1
            If DT_EmailScan_SendFrom.Rows(k).Item("status") = "Failed" Then failNum = failNum + 1
        Next
        GridView2.Columns("EmailTo").SummaryItem.SetSummary(DevExpress.Data.SummaryItemType.Custom, "Sent:" & successNum & "     " & "Failed:" & failNum)
        GridView2.Columns("EmailTo").SummaryItem.DisplayFormat = "<color=#00C292>Sent: " & successNum & "</color>     <color=#FF4C4C>Failed: " & failNum & "</color>"
        For i = 0 To DT_EmailScan_SendFrom.Rows.Count - 1 ' counter for send email from list
            If BackgroundWorker2.CancellationPending Then
                e.Cancel = True
                Return
            End If
            If DT_EmailScan_SendFrom.Rows(i).Item("status") = "Sent" Or DT_EmailScan_SendFrom.Rows(i).Item("status") = "Failed" Then
                GoTo NextMail
            End If
            emailFrom = DT_EmailScan_SendFrom.Rows(i).Item("EmailTo")
            DT_EmailScan_SendFrom.Rows(i).Item("status") = "Testing"
            AddHandler GridView2.RowCellStyle, AddressOf GridView2_RowCellStyle
            For j = 0 To DT_EmailScan_SendTO.Rows.Count - 1
                CheckForIllegalCrossThreadCalls = False
                '//////////////////////////////////////////////////////////////////////////////////////////////////////////
                Try
                    Dim smtp_Server As New SmtpClient
                    Dim e_mail As New MailMessage
                    smtp_Server.UseDefaultCredentials = False
                    smtp_Server.Credentials = New Net.NetworkCredential(txtEmail.Text, txtpassword.Text)
                    smtp_Server.Port = CInt(txtPort.Text)
                    smtp_Server.EnableSsl = CheckSSL.Checked
                    smtp_Server.Host = txtHost.Text
                    e_mail = New MailMessage()
                    e_mail.From = New MailAddress(emailFrom, txtFromName.Text)
                    '//////////////////////////////////////////////////////////
                    'For Each emailTo As String In listBoxEMails.SelectedItems
                    e_mail.To.Add(DT_EmailScan_SendTO.Rows(j).Item("EmailFrom").ToString())
                    'Next emailTo
                    If AttachmentFilePath.Trim <> "" Then e_mail.Attachments.Add(New Attachment(AttachmentFilePath))
                    e_mail.Subject = txtSubject.Text
                    e_mail.IsBodyHtml = False
                    smtp_Server.Send(e_mail)
                    successNum += 1
                    DT_EmailScan_SendFrom.Rows(i).Item("status") = "Sent"
                Catch ex As Exception
                    failNum += 1
                    DT_EmailScan_SendFrom.Rows(i).Item("status") = "Failed"
                End Try
                GridView2.Columns("EmailTo").SummaryItem.SetSummary(DevExpress.Data.SummaryItemType.Custom, "Sent:" & successNum & "     " & "Failed:" & failNum)
                GridView2.Columns("EmailTo").SummaryItem.DisplayFormat = "<color=#00C292>Sent: " & successNum & "</color>     <color=#FF4C4C>Failed: " & failNum & "</color>"
                Me.Refresh()
                '//////////////////////////////////////////////////////////////////////////////////////////////////////////
            Next
            Threading.Thread.Sleep(1000)
NextMail:
        Next
        CreateEmail_from_Table()
        GridControl2.DataSource = Nothing
        txtmailto.Text = ""
        txtmailto.Focus()
        GridControl2.Text = "   Send from Mail List : ( 0 )   "
        'GridView2.Columns("EmailTo").SummaryItem.SetSummary(DevExpress.Data.SummaryItemType.Custom, "Sent: 0     " & "Failed: 0 ")
    End Sub
    Private Sub BackgroundWorker2_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker2.RunWorkerCompleted
        Timer1.Enabled = False
        XtraTabControl1.Enabled = True
        GridControl2.DataSource = DT_EmailScan_SendFrom.DefaultView
        GridControl2.Refresh()
        frmMain.BntEmailScanner_StartScan.Enabled = True
        If e.Cancelled Then
            GridView2.Columns("EmailTo").SummaryItem.SetSummary(DevExpress.Data.SummaryItemType.Custom, "Sent: 0     " & "Failed: 0 ")
            DevExpress.XtraEditors.XtraMessageBox.Show("Scanning Process was stopped by the user", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            DevExpress.XtraEditors.XtraMessageBox.Show("Scanning Process is completed", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            GridControl2.DataSource = Nothing
            txtmailto.Text = ""
            txtmailto.Focus()
            GridControl2.Text = "   Send from Mail List : ( 0 )   "
        End If
    End Sub
    Private Sub txtEmail_MouseDown(sender As Object, e As MouseEventArgs) Handles txtEmail.MouseDown
        BntCheckSmtp.Enabled = True
        frmMain.BntEmailScanner_StartScan.Enabled = False
    End Sub

    ' إضافة context menu للـ txtHost للوصول السريع لاختيار SMTP
    Private Sub txtHost_MouseDown(sender As Object, e As MouseEventArgs) Handles txtHost.MouseDown
        If e.Button = MouseButtons.Right Then
            Dim contextMenu As New ContextMenuStrip()
            Dim menuItem As New ToolStripMenuItem("اختيار SMTP من ملف")
            AddHandler menuItem.Click, AddressOf SelectSMTPFromFile_Click
            contextMenu.Items.Add(menuItem)
            contextMenu.Show(txtHost, e.Location)
        End If
    End Sub

    Private Sub SelectSMTPFromFile_Click(sender As Object, e As EventArgs)
        UploadeSMTPListTO()
    End Sub
    Public Sub UploadeSMTPListTO()
        Dim OpenFile As New OpenFileDialog
        OpenFile.Filter = "Text Files|*.txt"
        If OpenFile.ShowDialog = DialogResult.OK Then
            Dim lines() As String = IO.File.ReadAllLines(OpenFile.FileName)
            Dim f As New frmSelectSMTP
            f.ListBoxControl1.Items.Clear()
            For Each line As String In lines
                If line.Trim() <> "" Then
                    f.ListBoxControl1.Items.Add(line.Trim())
                End If
            Next
            ' هنا الصح → ربط الـ Owner
            f.Owner = Me
            f.ShowDialog()
        End If
    End Sub
    ' استقبال البيانات من frmSelectSMTP
    Public Sub SetSMTP(ByVal host As String, ByVal port As String, ByVal email As String, ByVal pass As String)
        Try
            ' إضافة رسالة debug لمعرفة البيانات الواردة
            DevExpress.XtraEditors.XtraMessageBox.Show("تم استلام البيانات:" & vbNewLine &
                                                      "Host: " & host & vbNewLine &
                                                      "Port: " & port & vbNewLine &
                                                      "Email: " & email & vbNewLine &
                                                      "Password: " & pass,
                                                      "SetSMTP Debug", MessageBoxButtons.OK, MessageBoxIcon.Information)

            ' تنظيف البيانات
            host = host.Trim()
            port = port.Trim()
            email = email.Trim()
            pass = pass.Trim()

            ' التحقق من صحة البيانات
            If String.IsNullOrEmpty(host) Or String.IsNullOrEmpty(email) Or String.IsNullOrEmpty(pass) Then
                DevExpress.XtraEditors.XtraMessageBox.Show("بيانات SMTP غير مكتملة!", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If

            ' التحقق من صحة Port
            If String.IsNullOrEmpty(port) Or Not IsNumeric(port) Then
                port = "587" ' default port
            End If

            ' وضع البيانات في الـ textboxes
            txtHost.Text = host
            txtPort.Text = port
            txtEmail.Text = email
            txtpassword.Text = pass
            txtFromName.Text = email

            ' إظهار رسالة نجاح
            DevExpress.XtraEditors.XtraMessageBox.Show("تم تحديث بيانات SMTP بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

            ' تفعيل زر التحقق
            BntCheckSmtp.Enabled = True

        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("حدث خطأ في SetSMTP: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class