<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SixLabors.ImageSharp.Drawing</name>
    </assembly>
    <members>
        <member name="T:SixLabors.ImageSharp.Drawing.GraphicsOptionsExtensions">
            <summary>
            Extensions methods fpor the <see cref="T:SixLabors.ImageSharp.GraphicsOptions"/> class.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.GraphicsOptionsExtensions.IsOpaqueColorWithoutBlending(SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.Color)">
            <summary>
            Evaluates if a given SOURCE color can completely replace a BACKDROP color given the current blending and composition settings.
            </summary>
            <param name="options">The graphics options.</param>
            <param name="color">The source color.</param>
            <returns>true if the color can be considered opaque</returns>
            <remarks>
            Blending and composition is an expensive operation, in some cases, like
            filling with a solid color, the blending can be avoided by a plain color replacement.
            This method can be useful for such processors to select the fast path.
            </remarks>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Brush">
            <summary>
            Represents a logical configuration of a brush which can be used to source pixel colors.
            </summary>
            <remarks>
            A brush is a simple class that will return an <see cref="T:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1" /> that will perform the
            logic for retrieving pixel values for specific locations.
            </remarks>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brush.CreateApplicator``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{``0},SixLabors.ImageSharp.RectangleF)">
            <summary>
            Creates the applicator for this brush.
            </summary>
            <typeparam name="TPixel">The pixel type.</typeparam>
            <param name="configuration">The configuration instance to use when performing operations.</param>
            <param name="options">The graphic options.</param>
            <param name="source">The source image.</param>
            <param name="region">The region the brush will be applied to.</param>
            <returns>
            The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1"/> for this brush.
            </returns>
            <remarks>
            The <paramref name="region" /> when being applied to things like shapes would usually be the
            bounding box of the shape not necessarily the bounds of the whole image.
            </remarks>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brush.Equals(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1">
            <summary>
            Performs the application of an <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Brush"/> implementation against individual scanlines.
            </summary>
            <typeparam name="TPixel">The pixel format.</typeparam>
            <seealso cref="T:System.IDisposable" />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1.#ctor(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1"/> class.
            </summary>
            <param name="configuration">The configuration instance to use when performing operations.</param>
            <param name="options">The graphics options.</param>
            <param name="target">The target image frame.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1.Configuration">
            <summary>
            Gets the configuration instance to use when performing operations.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1.Blender">
            <summary>
            Gets the pixel blender.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1.Target">
            <summary>
            Gets the target image frame.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1.Options">
            <summary>
            Gets the graphics options
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1.Dispose(System.Boolean)">
            <summary>
            Disposes the object and frees resources for the Garbage Collector.
            </summary>
            <param name="disposing">Whether to dispose managed and unmanaged objects.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.BrushApplicator`1.Apply(System.Span{System.Single},System.Int32,System.Int32)">
            <summary>
            Applies the opacity weighting for each pixel in a scanline to the target based on the
            pattern contained in the brush.
            </summary>
            <param name="scanline">
            A collection of opacity values between 0 and 1 to be merged with the brushed color value
            before being applied to the
            target.
            </param>
            <param name="x">The x-position in the target pixel space that the start of the scanline data corresponds to.</param>
            <param name="y">The y-position in  the target pixel space that whole scanline corresponds to.</param>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Brushes">
            <summary>
            A collection of methods for creating generic brushes.
            </summary>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.Brushes.Percent10Pattern">
            <summary>
            Percent10 Hatch Pattern
            </summary>
            ---> x axis
            ^
            | y - axis
            |
            see PatternBrush for details about how to make new patterns work
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.Brushes.Percent20Pattern">
            <summary>
            Percent20 pattern.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.Brushes.HorizontalPattern">
            <summary>
            Horizontal Hatch Pattern
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.Brushes.MinPattern">
            <summary>
            Min Pattern
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.Brushes.VerticalPattern">
            <summary>
            Vertical Pattern
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.Brushes.ForwardDiagonalPattern">
            <summary>
            Forward Diagonal Pattern
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.Brushes.BackwardDiagonalPattern">
            <summary>
            Backward Diagonal Pattern
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.Solid(SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a solid color
            </summary>
            <param name="color">The color.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.Percent10(SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Percent10 Hatch Pattern with the specified colors
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.Percent10(SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Percent10 Hatch Pattern with the specified colors
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <param name="backColor">Color of the background.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.Percent20(SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Percent20 Hatch Pattern with the specified foreground color and a
            transparent background.
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.Percent20(SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Percent20 Hatch Pattern with the specified colors
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <param name="backColor">Color of the background.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.Horizontal(SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Horizontal Hatch Pattern with the specified foreground color and a
            transparent background.
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.Horizontal(SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Horizontal Hatch Pattern with the specified colors
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <param name="backColor">Color of the background.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.Min(SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Min Hatch Pattern with the specified foreground color and a
            transparent background.
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.Min(SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Min Hatch Pattern with the specified colors
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <param name="backColor">Color of the background.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.Vertical(SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Vertical Hatch Pattern with the specified foreground color and a
            transparent background.
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.Vertical(SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Vertical Hatch Pattern with the specified colors
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <param name="backColor">Color of the background.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.ForwardDiagonal(SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Forward Diagonal Hatch Pattern with the specified foreground color and a
            transparent background.
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.ForwardDiagonal(SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Forward Diagonal Hatch Pattern with the specified colors
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <param name="backColor">Color of the background.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.BackwardDiagonal(SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Backward Diagonal Hatch Pattern with the specified foreground color and a
            transparent background.
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Brushes.BackwardDiagonal(SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Color)">
            <summary>
            Create as brush that will paint a Backward Diagonal Hatch Pattern with the specified colors
            </summary>
            <param name="foreColor">Color of the foreground.</param>
            <param name="backColor">Color of the background.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/></returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.ColorStop">
            <summary>
            A struct that defines a single color stop.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ColorStop.#ctor(System.Single,SixLabors.ImageSharp.Color@)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.ColorStop" /> struct.
            </summary>
            <param name="ratio">Where should it be? 0 is at the start, 1 at the end of the Gradient.</param>
            <param name="color">What color should be used at that point?</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.ColorStop.Ratio">
            <summary>
            Gets the point along the defined gradient axis.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.ColorStop.Color">
            <summary>
            Gets the color to be used.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawingHelpers.ToPixelMatrix``1(SixLabors.ImageSharp.DenseMatrix{SixLabors.ImageSharp.Color},SixLabors.ImageSharp.Configuration)">
            <summary>
            Convert a <see cref="T:SixLabors.ImageSharp.DenseMatrix`1"/> to a <see cref="T:SixLabors.ImageSharp.DenseMatrix`1"/> of the given pixel type.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions">
            <summary>
            Options for influencing the drawing functions.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions"/> class.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions.GraphicsOptions">
            <summary>
            Gets or sets the Graphics Options.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions.ShapeOptions">
            <summary>
            Gets or sets the Shape Options.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions.Transform">
            <summary>
            Gets or sets the Transform to apply during rasterization.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.DrawingOptionsDefaultsExtensions">
            <summary>
            Adds extensions that help working with <see cref="T:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions" />.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawingOptionsDefaultsExtensions.GetDrawingOptions(SixLabors.ImageSharp.Processing.IImageProcessingContext)">
            <summary>
            Gets the default shape processing options against The source image processing context.
            </summary>
            <param name="context">The image processing context to retrieve defaults from.</param>
            <returns>The globally configured default options.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawingOptionsDefaultsExtensions.SetDrawingTransform(SixLabors.ImageSharp.Processing.IImageProcessingContext,System.Numerics.Matrix3x2)">
            <summary>
            Sets the 2D transformation matrix to be used during rasterization when drawing shapes or text.
            </summary>
            <param name="context">The image processing context to store default against.</param>
            <param name="matrix">The matrix to use.</param>
            <returns>The passed in <paramref name="context"/> to allow chaining.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawingOptionsDefaultsExtensions.SetDrawingTransform(SixLabors.ImageSharp.Configuration,System.Numerics.Matrix3x2)">
            <summary>
            Sets the default 2D transformation matrix to be used during rasterization when drawing shapes or text.
            </summary>
            <param name="configuration">The configuration to store default against.</param>
            <param name="matrix">The default matrix to use.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawingOptionsDefaultsExtensions.GetDrawingTransform(SixLabors.ImageSharp.Processing.IImageProcessingContext)">
            <summary>
            Gets the default 2D transformation matrix to be used during rasterization when drawing shapes or text.
            </summary>
            <param name="context">The image processing context to retrieve defaults from.</param>
            <returns>The matrix.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawingOptionsDefaultsExtensions.GetDrawingTransform(SixLabors.ImageSharp.Configuration)">
            <summary>
            Gets the default 2D transformation matrix to be used during rasterization when drawing shapes or text.
            </summary>
            <param name="configuration">The configuration to retrieve defaults from.</param>
            <returns>The globally configured default matrix.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.EllipticGradientBrush">
            <summary>
            Gradient Brush with elliptic shape.
            The ellipse is defined by a center point,
            a point on the longest extension of the ellipse and
            the ratio between longest and shortest extension.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.EllipticGradientBrush.#ctor(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,System.Single,SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode,SixLabors.ImageSharp.Drawing.Processing.ColorStop[])">
            <inheritdoc cref="T:SixLabors.ImageSharp.Drawing.Processing.GradientBrush" />
            <param name="center">The center of the elliptical gradient and 0 for the color stops.</param>
            <param name="referenceAxisEnd">The end point of the reference axis of the ellipse.</param>
            <param name="axisRatio">
              The ratio of the axis widths.
              The second axis' is perpendicular to the reference axis and
              it's length is the reference axis' length multiplied by this factor.
            </param>
            <param name="repetitionMode">Defines how the colors of the gradients are repeated.</param>
            <param name="colorStops">the color stops as defined in base class.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.EllipticGradientBrush.CreateApplicator``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{``0},SixLabors.ImageSharp.RectangleF)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.EllipticGradientBrush.RadialGradientBrushApplicator`1">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.EllipticGradientBrush.RadialGradientBrushApplicator`1.#ctor(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{`0},SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,System.Single,SixLabors.ImageSharp.Drawing.Processing.ColorStop[],SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.EllipticGradientBrush.RadialGradientBrushApplicator`1" /> class.
            </summary>
            <param name="configuration">The configuration instance to use when performing operations.</param>
            <param name="options">The graphics options.</param>
            <param name="target">The target image.</param>
            <param name="center">Center of the ellipse.</param>
            <param name="referenceAxisEnd">Point on one angular points of the ellipse.</param>
            <param name="axisRatio">
            Ratio of the axis length's. Used to determine the length of the second axis,
            the first is defined by <see cref="F:SixLabors.ImageSharp.Drawing.Processing.EllipticGradientBrush.RadialGradientBrushApplicator`1.center"/> and <see cref="F:SixLabors.ImageSharp.Drawing.Processing.EllipticGradientBrush.RadialGradientBrushApplicator`1.referenceAxisEnd"/>.</param>
            <param name="colorStops">Definition of colors.</param>
            <param name="repetitionMode">Defines how the gradient colors are repeated.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.EllipticGradientBrush.RadialGradientBrushApplicator`1.PositionOnGradient(System.Single,System.Single)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.ClearExtensions">
            <summary>
            Adds extensions that allow the flood filling of images without blending.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color)">
            <summary>
            Flood fills the image with the specified color without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color)">
            <summary>
            Flood fills the image with the specified color without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The drawing options.</param>
            <param name="color">The color.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <summary>
            Flood fills the image with the specified brush without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <summary>
            Flood fills the image with the specified brush without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The drawing options.</param>
            <param name="brush">The brush.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearExtensions.CloneForClearOperation(SixLabors.ImageSharp.Drawing.Processing.DrawingOptions)">
            <summary>
            Clones the path graphic options and applies changes required to force clearing.
            </summary>
            <param name="drawingOptions">The drawing options to clone</param>
            <returns>A clone of shapeOptions with ColorBlendingMode, AlphaCompositionMode, and BlendPercentage set</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.ClearPathExtensions">
            <summary>
            Adds extensions that allow the flood filling of polygon outlines without blending.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearPathExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Flood fills the image within the provided region defined by an <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> using the specified
            color without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="region">The <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearPathExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Flood fills the image within the provided region defined by an <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> using the specified color
            without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The drawing options.</param>
            <param name="color">The color.</param>
            <param name="region">The <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearPathExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Flood fills the image within the provided region defined by an <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> using the specified brush
            without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="region">The <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearPathExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Flood fills the image within the provided region defined by an <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> using the specified brush
            without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The drawing options.</param>
            <param name="brush">The brush.</param>
            <param name="region">The <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.ClearRectangleExtensions">
            <summary>
            Adds extensions that allow the flood filling of rectangle outlines without blending.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearRectangleExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Flood fills the image in the rectangle of the provided rectangle with the specified color without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="rectangle">The rectangle defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearRectangleExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Flood fills the image in the rectangle of the provided rectangle with the specified color without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The drawing options.</param>
            <param name="color">The color.</param>
            <param name="rectangle">The rectangle defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearRectangleExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Flood fills the image in the rectangle of the provided rectangle with the specified brush without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="rectangle">The rectangle defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClearRectangleExtensions.Clear(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Flood fills the image at the given rectangle bounds with the specified brush without any blending.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The drawing options.</param>
            <param name="brush">The brush.</param>
            <param name="rectangle">The rectangle defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.ClipPathExtensions">
            <summary>
            Adds extensions that allow the application of processors within a clipped path.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ClipPathExtensions.Clip(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.IPath,System.Action{SixLabors.ImageSharp.Processing.IImageProcessingContext})">
            <summary>
            Applies the processing operation within the provided region defined by an <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="region">The <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> defining the region to operation within.</param>
            <param name="operation">The operation to perform.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.DrawBezierExtensions">
            <summary>
            Adds extensions that allow the drawing of Bezier paths.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawBezierExtensions.DrawBeziers(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open Bezier path with the supplied pen
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="pen">The pen.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawBezierExtensions.DrawBeziers(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open Bezier path with the supplied pen
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="pen">The pen.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawBezierExtensions.DrawBeziers(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open Bezier path at the provided thickness with the supplied brush
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawBezierExtensions.DrawBeziers(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open Bezier path at the provided thickness with the supplied brush
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawBezierExtensions.DrawBeziers(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open Bezier path at the provided thickness with the supplied brush
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="thickness">The thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawBezierExtensions.DrawBeziers(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open Bezier path at the provided thickness with the supplied brush
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="color">The color.</param>
            <param name="thickness">The thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.DrawLineExtensions">
            <summary>
            Adds extensions that allow the drawing of lines.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawLineExtensions.DrawLine(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open linear path at the provided thickness with the supplied brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The line thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawLineExtensions.DrawLine(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open linear path at the provided thickness with the supplied brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The line thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawLineExtensions.DrawLine(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open linear path at the provided thickness with the supplied brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="thickness">The line thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawLineExtensions.DrawLine(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open linear path at the provided thickness with the supplied brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="color">The color.</param>
            <param name="thickness">The line thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawLineExtensions.DrawLine(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open linear path with the supplied pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="pen">The pen.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawLineExtensions.DrawLine(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as an open linear path with the supplied pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="pen">The pen.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.DrawPathCollectionExtensions">
            <summary>
            Adds extensions that allow the drawing of collections of polygon outlines.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathCollectionExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.Drawing.IPathCollection)">
            <summary>
            Draws the outline of the polygon with the provided pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="pen">The pen.</param>
            <param name="paths">The paths.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathCollectionExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.Drawing.IPathCollection)">
            <summary>
            Draws the outline of the polygon with the provided pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="pen">The pen.</param>
            <param name="paths">The paths.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathCollectionExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.Drawing.IPathCollection)">
            <summary>
            Draws the outline of the polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The thickness.</param>
            <param name="paths">The shapes.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathCollectionExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.Drawing.IPathCollection)">
            <summary>
            Draws the outline of the polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The thickness.</param>
            <param name="paths">The paths.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathCollectionExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.Drawing.IPathCollection)">
            <summary>
            Draws the outline of the polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="color">The color.</param>
            <param name="thickness">The thickness.</param>
            <param name="paths">The paths.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathCollectionExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.Drawing.IPathCollection)">
            <summary>
            Draws the outline of the polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="thickness">The thickness.</param>
            <param name="paths">The paths.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.DrawPathExtensions">
            <summary>
            Adds extensions that allow the drawing of polygon outlines.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Draws the outline of the polygon with the provided pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="pen">The pen.</param>
            <param name="path">The path.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Draws the outline of the polygon with the provided pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="pen">The pen.</param>
            <param name="path">The path.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Draws the outline of the polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The thickness.</param>
            <param name="path">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Draws the outline of the polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The thickness.</param>
            <param name="path">The path.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Draws the outline of the polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="color">The color.</param>
            <param name="thickness">The thickness.</param>
            <param name="path">The path.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPathExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Draws the outline of the polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="thickness">The thickness.</param>
            <param name="path">The path.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.DrawPolygonExtensions">
            <summary>
            Adds extensions that allow the drawing of closed linear polygons.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPolygonExtensions.DrawPolygon(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as a closed linear polygon with the provided pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="pen">The pen.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPolygonExtensions.DrawPolygon(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as a closed linear polygon with the provided pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="pen">The pen.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPolygonExtensions.DrawPolygon(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as a closed linear polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPolygonExtensions.DrawPolygon(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as a closed linear polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPolygonExtensions.DrawPolygon(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as a closed linear polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="thickness">The thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawPolygonExtensions.DrawPolygon(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.PointF[])">
            <summary>
            Draws the provided points as a closed linear polygon with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="color">The color.</param>
            <param name="thickness">The thickness.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.DrawRectangleExtensions">
            <summary>
            Adds extensions that allow the drawing of rectangles.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawRectangleExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Draws the outline of the rectangle with the provided pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="pen">The pen.</param>
            <param name="shape">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawRectangleExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Draws the outline of the rectangle with the provided pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="pen">The pen.</param>
            <param name="shape">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawRectangleExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Draws the outline of the rectangle with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The thickness.</param>
            <param name="shape">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawRectangleExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Draws the outline of the rectangle with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="thickness">The thickness.</param>
            <param name="shape">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawRectangleExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Draws the outline of the rectangle with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="color">The color.</param>
            <param name="thickness">The thickness.</param>
            <param name="shape">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawRectangleExtensions.Draw(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,System.Single,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Draws the outline of the rectangle with the provided brush at the provided thickness.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="thickness">The thickness.</param>
            <param name="shape">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions">
            <summary>
            Adds extensions that allow the drawing of text.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,System.String,SixLabors.Fonts.Font,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.PointF)">
            <summary>
            Draws the text onto the image filled with the given color.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="text">The text to draw.</param>
            <param name="font">The font.</param>
            <param name="color">The color.</param>
            <param name="location">The location.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,System.String,SixLabors.Fonts.Font,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.PointF)">
            <summary>
            Draws the text using the supplied drawing options onto the image filled with the given color.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="drawingOptions">The drawing options.</param>
            <param name="text">The text to draw.</param>
            <param name="font">The font.</param>
            <param name="color">The color.</param>
            <param name="location">The location.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.RichTextOptions,System.String,SixLabors.ImageSharp.Color)">
            <summary>
            Draws the text  using the supplied text options onto the image filled via the brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="textOptions">The text rendering options.</param>
            <param name="text">The text to draw.</param>
            <param name="color">The color.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,System.String,SixLabors.Fonts.Font,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.PointF)">
            <summary>
            Draws the text onto the image filled via the brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="text">The text to draw.</param>
            <param name="font">The font.</param>
            <param name="brush">The brush used to fill the text.</param>
            <param name="location">The location.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,System.String,SixLabors.Fonts.Font,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.PointF)">
            <summary>
            Draws the text onto the image outlined via the pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="text">The text to draw.</param>
            <param name="font">The font.</param>
            <param name="pen">The pen used to outline the text.</param>
            <param name="location">The location.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,System.String,SixLabors.Fonts.Font,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.PointF)">
            <summary>
            Draws the text onto the image filled via the brush then outlined via the pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="text">The text to draw.</param>
            <param name="font">The font.</param>
            <param name="brush">The brush used to fill the text.</param>
            <param name="pen">The pen used to outline the text.</param>
            <param name="location">The location.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.RichTextOptions,System.String,SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <summary>
            Draws the text using the given options onto the image filled via the brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="textOptions">The text rendering options.</param>
            <param name="text">The text to draw.</param>
            <param name="brush">The brush used to fill the text.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.RichTextOptions,System.String,SixLabors.ImageSharp.Drawing.Processing.Pen)">
            <summary>
            Draws the text using the given options onto the image outlined via the pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="textOptions">The text rendering options.</param>
            <param name="text">The text to draw.</param>
            <param name="pen">The pen used to outline the text.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.RichTextOptions,System.String,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.Processing.Pen)">
            <summary>
            Draws the text using the given options onto the image filled via the brush then outlined via the pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="textOptions">The text rendering options.</param>
            <param name="text">The text to draw.</param>
            <param name="brush">The brush used to fill the text.</param>
            <param name="pen">The pen used to outline the text.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,System.String,SixLabors.Fonts.Font,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.PointF)">
            <summary>
            Draws the text onto the image outlined via the pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="drawingOptions">The drawing options.</param>
            <param name="text">The text to draw.</param>
            <param name="font">The font.</param>
            <param name="pen">The pen used to outline the text.</param>
            <param name="location">The location.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,System.String,SixLabors.Fonts.Font,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.PointF)">
            <summary>
            Draws the text onto the image filled via the brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="drawingOptions">The drawing options.</param>
            <param name="text">The text to draw.</param>
            <param name="font">The font.</param>
            <param name="brush">The brush used to fill the text.</param>
            <param name="location">The location.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,System.String,SixLabors.Fonts.Font,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.PointF)">
            <summary>
            Draws the text using the given drawing options onto the image filled via the brush then outlined via the pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="drawingOptions">The drawing options.</param>
            <param name="text">The text to draw.</param>
            <param name="font">The font.</param>
            <param name="brush">The brush used to fill the text.</param>
            <param name="pen">The pen used to outline the text.</param>
            <param name="location">The location.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.DrawTextExtensions.DrawText(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.RichTextOptions,System.String,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.Processing.Pen)">
            <summary>
            Draws the text using the given options onto the image filled via the brush then outlined via the pen.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="drawingOptions">The drawing options.</param>
            <param name="textOptions">The text rendering options.</param>
            <param name="text">The text to draw.</param>
            <param name="brush">The brush used to fill the text.</param>
            <param name="pen">The pen used to outline the text.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.FillExtensions">
            <summary>
            Adds extensions that allow the flood filling of images.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color)">
            <summary>
            Flood fills the image with the specified color.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color)">
            <summary>
            Flood fills the image with the specified color.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The drawing options.</param>
            <param name="color">The color.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <summary>
            Flood fills the image with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <summary>
            Flood fills the image with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The drawing options.</param>
            <param name="brush">The brush.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.FillPathBuilderExtensions">
            <summary>
            Adds extensions that allow the flood filling of polygon outlines.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathBuilderExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,System.Action{SixLabors.ImageSharp.Drawing.PathBuilder})">
            <summary>
            Flood fills the image within the provided region defined by an <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/> method
            using the specified color.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="region">The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/> method defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathBuilderExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,System.Action{SixLabors.ImageSharp.Drawing.PathBuilder})">
            <summary>
            Flood fills the image within the provided region defined by an <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/> method
            using the specified color.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The drawing options.</param>
            <param name="color">The color.</param>
            <param name="region">The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/> method defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathBuilderExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Action{SixLabors.ImageSharp.Drawing.PathBuilder})">
            <summary>
            Flood fills the image within the provided region defined by an <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/> method
            using the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="region">The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/> method defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathBuilderExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,System.Action{SixLabors.ImageSharp.Drawing.PathBuilder})">
            <summary>
            Flood fills the image within the provided region defined by an <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/> method
            using the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The graphics options.</param>
            <param name="brush">The brush.</param>
            <param name="region">The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/> method defining the region to fill.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.FillPathCollectionExtensions">
            <summary>
            Adds extensions that allow the filling of collections of polygon outlines.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathCollectionExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.IPathCollection)">
            <summary>
            Flood fills the image in the shape of the provided polygon with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The graphics options.</param>
            <param name="brush">The brush.</param>
            <param name="paths">The shapes.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathCollectionExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.IPathCollection)">
            <summary>
            Flood fills the image in the shape of the provided polygon with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="paths">The paths.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathCollectionExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Drawing.IPathCollection)">
            <summary>
            Flood fills the image in the shape of the provided polygon with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="color">The color.</param>
            <param name="paths">The paths.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathCollectionExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Drawing.IPathCollection)">
            <summary>
            Flood fills the image in the shape of the provided polygon with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="paths">The paths.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.FillPathExtensions">
            <summary>
            Adds extensions that allow the filling of polygon outlines.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Flood fills the image in the shape of the provided polygon with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="path">The logic path.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Flood fills the image in the shape of the provided polygon with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The drawing options.</param>
            <param name="color">The color.</param>
            <param name="path">The logic path.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Flood fills the image in the shape of the provided polygon with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="path">The logic path.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPathExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Flood fills the image in the shape of the provided polygon with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The drawing options.</param>
            <param name="brush">The brush.</param>
            <param name="path">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.FillPolygonExtensions">
            <summary>
            Adds extensions that allow the filling of closed linear polygons.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPolygonExtensions.FillPolygon(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.PointF[])">
            <summary>
            Flood fills the image in the shape of a linear polygon described by the points
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="brush">The brush.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPolygonExtensions.FillPolygon(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.PointF[])">
            <summary>
            Flood fills the image in the shape of a linear polygon described by the points
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPolygonExtensions.FillPolygon(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.PointF[])">
            <summary>
            Flood fills the image in the shape of a linear polygon described by the points
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="color">The color.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillPolygonExtensions.FillPolygon(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.PointF[])">
            <summary>
            Flood fills the image in the shape of a linear polygon described by the points
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.FillRectangleExtensions">
            <summary>
            Adds extensions that allow the filling of rectangles.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillRectangleExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Flood fills the image in the shape of the provided rectangle with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="brush">The brush.</param>
            <param name="shape">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillRectangleExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Flood fills the image in the shape of the provided rectangle with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="brush">The brush.</param>
            <param name="shape">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillRectangleExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Flood fills the image in the shape of the provided rectangle with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="options">The options.</param>
            <param name="color">The color.</param>
            <param name="shape">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.FillRectangleExtensions.Fill(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Flood fills the image in the shape of the provided rectangle with the specified brush.
            </summary>
            <param name="source">The source image processing context.</param>
            <param name="color">The color.</param>
            <param name="shape">The shape.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Processing.IImageProcessingContext"/> to allow chaining of operations.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.GradientBrush">
            <summary>
            Base class for Gradient brushes
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.GradientBrush.#ctor(SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode,SixLabors.ImageSharp.Drawing.Processing.ColorStop[])">
            <inheritdoc cref="T:SixLabors.ImageSharp.Drawing.Processing.Brush"/>
            <param name="repetitionMode">Defines how the colors are repeated beyond the interval [0..1]</param>
            <param name="colorStops">The gradient colors.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.GradientBrush.RepetitionMode">
            <summary>
            Gets how the colors are repeated beyond the interval [0..1].
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.GradientBrush.ColorStops">
            <summary>
            Gets the list of color stops for this gradient.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.GradientBrush.Equals(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.GradientBrush.GradientBrushApplicator`1">
            <summary>
            Base class for gradient brush applicators
            </summary>
            <typeparam name="TPixel">The pixel format.</typeparam>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.GradientBrush.GradientBrushApplicator`1.#ctor(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{`0},SixLabors.ImageSharp.Drawing.Processing.ColorStop[],SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.GradientBrush.GradientBrushApplicator`1"/> class.
            </summary>
            <param name="configuration">The configuration instance to use when performing operations.</param>
            <param name="options">The graphics options.</param>
            <param name="target">The target image.</param>
            <param name="colorStops">An array of color stops sorted by their position.</param>
            <param name="repetitionMode">Defines if and how the gradient should be repeated.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.GradientBrush.GradientBrushApplicator`1.Apply(System.Span{System.Single},System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.GradientBrush.GradientBrushApplicator`1.PositionOnGradient(System.Single,System.Single)">
            <summary>
            Calculates the position on the gradient for a given point.
            This method is abstract as it's content depends on the shape of the gradient.
            </summary>
            <param name="x">The x-coordinate of the point.</param>
            <param name="y">The y-coordinate of the point.</param>
            <returns>
            The position the given point has on the gradient.
            The position is not bound to the [0..1] interval.
            Values outside of that interval may be treated differently,
            e.g. for the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode" /> enum.
            </returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.GradientBrush.GradientBrushApplicator`1.Dispose(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode">
            <summary>
            Modes to repeat a gradient.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode.None">
            <summary>
            Don't repeat, keep the color of start and end beyond those points stable.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode.Repeat">
            <summary>
            Repeat the gradient.
            If it's a black-white gradient, with Repeat it will be Black->{gray}->White|Black->{gray}->White|...
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode.Reflect">
            <summary>
            Reflect the gradient.
            Similar to <see cref="F:SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode.Repeat"/>, but each other repetition uses inverse order of <see cref="T:SixLabors.ImageSharp.Drawing.Processing.ColorStop"/>s.
            Used on a Black-White gradient, Reflect leads to Black->{gray}->White->{gray}->White...
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode.DontFill">
            <summary>
            With DontFill a gradient does not touch any pixel beyond it's borders.
            For the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush"/> this is beyond the orthogonal through start and end,
            For <see cref="T:SixLabors.ImageSharp.Drawing.Processing.RadialGradientBrush" /> and <see cref="T:SixLabors.ImageSharp.Drawing.Processing.EllipticGradientBrush" /> it's beyond 1.0.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.ImageBrush">
            <summary>
            Provides an implementation of an image brush for painting images within areas.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.image">
            <summary>
            The image to paint.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.region">
            <summary>
            The region of the source image we will be using to paint.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.#ctor(SixLabors.ImageSharp.Image)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.ImageBrush"/> class.
            </summary>
            <param name="image">The image.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.#ctor(SixLabors.ImageSharp.Image,SixLabors.ImageSharp.RectangleF)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.ImageBrush"/> class.
            </summary>
            <param name="image">The image.</param>
            <param name="region">
            The region of interest.
            This overrides any region used to initialize the brush applicator.
            </param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.Equals(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.CreateApplicator``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{``0},SixLabors.ImageSharp.RectangleF)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.ImageBrushApplicator`1">
            <summary>
            The image brush applicator.
            </summary>
            <typeparam name="TPixel">The pixel format.</typeparam>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.ImageBrushApplicator`1.sourceRegion">
            <summary>
            The region of the source image we will be using to draw from.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.ImageBrushApplicator`1.offsetY">
            <summary>
            The Y offset.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.ImageBrushApplicator`1.offsetX">
            <summary>
            The X offset.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.ImageBrushApplicator`1.#ctor(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{`0},SixLabors.ImageSharp.Image{`0},SixLabors.ImageSharp.RectangleF,SixLabors.ImageSharp.RectangleF,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.ImageBrushApplicator`1"/> class.
            </summary>
            <param name="configuration">The configuration instance to use when performing operations.</param>
            <param name="options">The graphics options.</param>
            <param name="target">The target image.</param>
            <param name="image">The image.</param>
            <param name="targetRegion">The region of the target image we will be drawing to.</param>
            <param name="sourceRegion">The region of the source image we will be using to source pixels to draw from.</param>
            <param name="shouldDisposeImage">Whether to dispose the image on disposal of the applicator.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.ImageBrushApplicator`1.Dispose(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ImageBrush.ImageBrushApplicator`1.Apply(System.Span{System.Single},System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush">
            <summary>
            Provides an implementation of a brush for painting linear gradients within areas.
            Supported right now:
            - a set of colors in relative distances to each other.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.#ctor(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode,SixLabors.ImageSharp.Drawing.Processing.ColorStop[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush"/> class.
            </summary>
            <param name="p1">Start point</param>
            <param name="p2">End point</param>
            <param name="repetitionMode">defines how colors are repeated.</param>
            <param name="colorStops"><inheritdoc /></param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.Equals(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.CreateApplicator``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{``0},SixLabors.ImageSharp.RectangleF)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.LinearGradientBrushApplicator`1">
            <summary>
            The linear gradient brush applicator.
            </summary>
            <typeparam name="TPixel">The pixel format.</typeparam>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.LinearGradientBrushApplicator`1.alongX">
            <summary>
            the vector along the gradient, x component
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.LinearGradientBrushApplicator`1.alongY">
            <summary>
            the vector along the gradient, y component
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.LinearGradientBrushApplicator`1.acrossY">
            <summary>
            the vector perpendicular to the gradient, y component
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.LinearGradientBrushApplicator`1.acrossX">
            <summary>
            the vector perpendicular to the gradient, x component
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.LinearGradientBrushApplicator`1.alongsSquared">
            <summary>
            the result of <see cref="F:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.LinearGradientBrushApplicator`1.alongX"/>^2 + <see cref="F:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.LinearGradientBrushApplicator`1.alongY"/>^2
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.LinearGradientBrushApplicator`1.length">
            <summary>
            the length of the defined gradient (between source and end)
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.LinearGradientBrushApplicator`1.#ctor(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{`0},SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.Drawing.Processing.ColorStop[],SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.LinearGradientBrush.LinearGradientBrushApplicator`1" /> class.
            </summary>
            <param name="configuration">The configuration instance to use when performing operations.</param>
            <param name="options">The graphics options.</param>
            <param name="source">The source image.</param>
            <param name="start">The start point of the gradient.</param>
            <param name="end">The end point of the gradient.</param>
            <param name="colorStops">A tuple list of colors and their respective position between 0 and 1 on the line.</param>
            <param name="repetitionMode">Defines how the gradient colors are repeated.</param>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush">
            <summary>
            Provides an implementation of a brush for painting gradients between multiple color positions in 2D coordinates.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush.#ctor(SixLabors.ImageSharp.PointF[],SixLabors.ImageSharp.Color[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush"/> class.
            </summary>
            <param name="points">Points that constitute a polygon that represents the gradient area.</param>
            <param name="colors">Array of colors that correspond to each point in the polygon.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush.#ctor(SixLabors.ImageSharp.PointF[],SixLabors.ImageSharp.Color[],SixLabors.ImageSharp.Color)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush"/> class.
            </summary>
            <param name="points">Points that constitute a polygon that represents the gradient area.</param>
            <param name="colors">Array of colors that correspond to each point in the polygon.</param>
            <param name="centerColor">Color at the center of the gradient area to which the other colors converge.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush.Equals(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush.CreateApplicator``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{``0},SixLabors.ImageSharp.RectangleF)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush.Edge">
            <summary>
            An edge of the polygon that represents the gradient area.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush.PathGradientBrushApplicator`1">
            <summary>
            The path gradient brush applicator.
            </summary>
            <typeparam name="TPixel">The pixel format.</typeparam>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush.PathGradientBrushApplicator`1.#ctor(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{`0},System.Collections.Generic.IList{SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush.Edge},SixLabors.ImageSharp.Color,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush.PathGradientBrushApplicator`1"/> class.
            </summary>
            <param name="configuration">The configuration instance to use when performing operations.</param>
            <param name="options">The graphics options.</param>
            <param name="source">The source image.</param>
            <param name="edges">Edges of the polygon.</param>
            <param name="centerColor">Color at the center of the gradient area to which the other colors converge.</param>
            <param name="hasSpecialCenterColor">Whether the center color is different from a smooth gradient between the edges.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush.PathGradientBrushApplicator`1.Apply(System.Span{System.Single},System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PathGradientBrush.PathGradientBrushApplicator`1.Dispose(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush">
            <summary>
            Provides an implementation of a pattern brush for painting patterns.
            </summary>
            <remarks>
            The patterns that are used to create a custom pattern brush are made up of a repeating matrix of flags,
            where each flag denotes whether to draw the foreground color or the background color.
            so to create a new bool[,] with your flags
            <para>
            For example if you wanted to create a diagonal line that repeat every 4 pixels you would use a pattern like so
            1000
            0100
            0010
            0001
            </para>
            <para>
            or you want a horizontal stripe which is 3 pixels apart you would use a pattern like
             1
             0
             0
            </para>
            </remarks>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.PatternBrush.pattern">
            <summary>
            The pattern.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternBrush.#ctor(SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Color,System.Boolean[0:,0:])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/> class.
            </summary>
            <param name="foreColor">Color of the fore.</param>
            <param name="backColor">Color of the back.</param>
            <param name="pattern">The pattern.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternBrush.#ctor(SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Color,SixLabors.ImageSharp.DenseMatrix{System.Boolean}@)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/> class.
            </summary>
            <param name="foreColor">Color of the fore.</param>
            <param name="backColor">Color of the back.</param>
            <param name="pattern">The pattern.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternBrush.#ctor(SixLabors.ImageSharp.Drawing.Processing.PatternBrush)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush"/> class.
            </summary>
            <param name="brush">The brush.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternBrush.Equals(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternBrush.CreateApplicator``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{``0},SixLabors.ImageSharp.RectangleF)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush.PatternBrushApplicator`1">
            <summary>
            The pattern brush applicator.
            </summary>
            <typeparam name="TPixel">The pixel format.</typeparam>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternBrush.PatternBrushApplicator`1.#ctor(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{`0},SixLabors.ImageSharp.DenseMatrix{`0}@)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternBrush.PatternBrushApplicator`1" /> class.
            </summary>
            <param name="configuration">The configuration instance to use when performing operations.</param>
            <param name="options">The graphics options.</param>
            <param name="source">The source image.</param>
            <param name="pattern">The pattern.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternBrush.PatternBrushApplicator`1.Apply(System.Span{System.Single},System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternBrush.PatternBrushApplicator`1.Dispose(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.PatternPen">
            <summary>
            Defines a pen that can apply a pattern to a line with a set brush and thickness
            </summary>
            <remarks>
            The pattern will be in to the form of
            <code>
            new float[]{ 1f, 2f, 0.5f}
            </code>
            this will be converted into a pattern that is 3.5 times longer that the width with 3 sections.
            <list type="bullet">
            <item>Section 1 will be width long (making a square) and will be filled by the brush.</item>
            <item>Section 2 will be width * 2 long and will be empty.</item>
            <item>Section 3 will be width/2 long and will be filled.</item>
            </list>
            The pattern will immediately repeat without gap.
            </remarks>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternPen.#ctor(SixLabors.ImageSharp.Color,System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternPen"/> class.
            </summary>
            <param name="color">The color.</param>
            <param name="strokePattern">The stroke pattern.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternPen.#ctor(SixLabors.ImageSharp.Color,System.Single,System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternPen"/> class.
            </summary>
            <param name="color">The color.</param>
            <param name="strokeWidth">The stroke width in px units.</param>
            <param name="strokePattern">The stroke pattern.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternPen.#ctor(SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternPen"/> class.
            </summary>
            <param name="strokeFill">The brush used to fill the stroke outline.</param>
            <param name="strokeWidth">The stroke width in px units.</param>
            <param name="strokePattern">The stroke pattern.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternPen.#ctor(SixLabors.ImageSharp.Drawing.Processing.PenOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PatternPen"/> class.
            </summary>
            <param name="options">The pen options.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternPen.Equals(SixLabors.ImageSharp.Drawing.Processing.Pen)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PatternPen.GeneratePath(SixLabors.ImageSharp.Drawing.IPath,System.Single)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Pen">
            <summary>
            The base class for pens that can apply a pattern to a line with a set brush and thickness
            </summary>
            <remarks>
            The pattern will be in to the form of
            <code>
            new float[]{ 1f, 2f, 0.5f}
            </code>
            this will be converted into a pattern that is 3.5 times longer that the width with 3 sections.
            <list type="bullet">
            <item>Section 1 will be width long (making a square) and will be filled by the brush.</item>
            <item>Section 2 will be width * 2 long and will be empty.</item>
            <item>Section 3 will be width/2 long and will be filled.</item>
            </list>
            The pattern will immediately repeat without gap.
            </remarks>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pen.#ctor(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/> class.
            </summary>
            <param name="strokeFill">The brush used to fill the stroke outline.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pen.#ctor(SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/> class.
            </summary>
            <param name="strokeFill">The brush used to fill the stroke outline.</param>
            <param name="strokeWidth">The stroke width in px units.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pen.#ctor(SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/> class.
            </summary>
            <param name="strokeFill">The brush used to fill the stroke outline.</param>
            <param name="strokeWidth">The stroke width in px units.</param>
            <param name="strokePattern">The stroke pattern.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pen.#ctor(SixLabors.ImageSharp.Drawing.Processing.PenOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/> class.
            </summary>
            <param name="options">The pen options.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Pen.StrokeFill">
            <inheritdoc cref="P:SixLabors.ImageSharp.Drawing.Processing.PenOptions.StrokeFill"/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Pen.StrokeWidth">
            <inheritdoc cref="P:SixLabors.ImageSharp.Drawing.Processing.PenOptions.StrokeWidth"/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Pen.StrokePattern">
            <inheritdoc cref="P:SixLabors.ImageSharp.Drawing.Processing.PenOptions.StrokePattern"/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Pen.JointStyle">
            <inheritdoc cref="P:SixLabors.ImageSharp.Drawing.Processing.PenOptions.JointStyle"/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Pen.EndCapStyle">
            <inheritdoc cref="P:SixLabors.ImageSharp.Drawing.Processing.PenOptions.EndCapStyle"/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pen.GeneratePath(SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Applies the styling from the pen to a path and generate a new path with the final vector.
            </summary>
            <param name="path">The source path</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with the pen styling applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pen.GeneratePath(SixLabors.ImageSharp.Drawing.IPath,System.Single)">
            <summary>
            Applies the styling from the pen to a path and generate a new path with the final vector.
            </summary>
            <param name="path">The source path</param>
            <param name="strokeWidth">The stroke width in px units.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with the pen styling applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pen.Equals(SixLabors.ImageSharp.Drawing.Processing.Pen)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.PenOptions">
            <summary>
            Provides a set of configurations options for pens.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PenOptions.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PenOptions"/> struct.
            </summary>
            <param name="strokeWidth">The stroke width in px units.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PenOptions.#ctor(SixLabors.ImageSharp.Color,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PenOptions"/> struct.
            </summary>
            <param name="color">The color.</param>
            <param name="strokeWidth">The stroke width in px units.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PenOptions.#ctor(SixLabors.ImageSharp.Color,System.Single,System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PenOptions"/> struct.
            </summary>
            <param name="color">The color.</param>
            <param name="strokeWidth">The stroke width in px units.</param>
            <param name="strokePattern">The stroke pattern.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.PenOptions.#ctor(SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single,System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.PenOptions"/> struct.
            </summary>
            <param name="strokeFill">The brush used to fill the stroke outline.</param>
            <param name="strokeWidth">The stroke width in px units.</param>
            <param name="strokePattern">The stroke pattern.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.PenOptions.StrokeFill">
            <summary>
            Gets the brush used to fill the stroke outline. Defaults to <see cref="T:SixLabors.ImageSharp.Drawing.Processing.SolidBrush"/>.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.PenOptions.StrokeWidth">
            <summary>
            Gets the stroke width in px units. Defaults to 1px.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.PenOptions.StrokePattern">
            <summary>
            Gets the stroke pattern.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.PenOptions.JointStyle">
            <summary>
            Gets or sets the joint style.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.PenOptions.EndCapStyle">
            <summary>
            Gets or sets the end cap style.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Pens">
            <summary>
            Contains a collection of common pen styles.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.Solid(SixLabors.ImageSharp.Color)">
            <summary>
            Create a solid pen without any drawing patterns
            </summary>
            <param name="color">The color.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.Solid(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <summary>
            Create a solid pen without any drawing patterns
            </summary>
            <param name="brush">The brush.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.Solid(SixLabors.ImageSharp.Color,System.Single)">
            <summary>
            Create a solid pen without any drawing patterns
            </summary>
            <param name="color">The color.</param>
            <param name="width">The width.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.Solid(SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single)">
            <summary>
            Create a solid pen without any drawing patterns
            </summary>
            <param name="brush">The brush.</param>
            <param name="width">The width.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.Dash(SixLabors.ImageSharp.Color,System.Single)">
            <summary>
            Create a pen with a 'Dash' drawing patterns
            </summary>
            <param name="color">The color.</param>
            <param name="width">The width.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.Dash(SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single)">
            <summary>
            Create a pen with a 'Dash' drawing patterns
            </summary>
            <param name="brush">The brush.</param>
            <param name="width">The width.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.Dot(SixLabors.ImageSharp.Color,System.Single)">
            <summary>
            Create a pen with a 'Dot' drawing patterns
            </summary>
            <param name="color">The color.</param>
            <param name="width">The width.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.Dot(SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single)">
            <summary>
            Create a pen with a 'Dot' drawing patterns
            </summary>
            <param name="brush">The brush.</param>
            <param name="width">The width.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.DashDot(SixLabors.ImageSharp.Color,System.Single)">
            <summary>
            Create a pen with a 'Dash Dot' drawing patterns
            </summary>
            <param name="color">The color.</param>
            <param name="width">The width.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.DashDot(SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single)">
            <summary>
            Create a pen with a 'Dash Dot' drawing patterns
            </summary>
            <param name="brush">The brush.</param>
            <param name="width">The width.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.DashDotDot(SixLabors.ImageSharp.Color,System.Single)">
            <summary>
            Create a pen with a 'Dash Dot Dot' drawing patterns
            </summary>
            <param name="color">The color.</param>
            <param name="width">The width.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Pens.DashDotDot(SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single)">
            <summary>
            Create a pen with a 'Dash Dot Dot' drawing patterns
            </summary>
            <param name="brush">The brush.</param>
            <param name="width">The width.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Pen"/>.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.ClipPathProcessor">
            <summary>
            Allows the recursive application of processing operations against an image within a given region.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.ClipPathProcessor.#ctor(SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.IPath,System.Action{SixLabors.ImageSharp.Processing.IImageProcessingContext})">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.ClipPathProcessor"/> class.
            </summary>
            <param name="options">The drawing options.</param>
            <param name="path">The <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> defining the region to operate within.</param>
            <param name="operation">The operation to perform on the source.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.ClipPathProcessor.Options">
            <summary>
            Gets the drawing options.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.ClipPathProcessor.Region">
            <summary>
            Gets the <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> defining the region to operate within.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.ClipPathProcessor.Operation">
            <summary>
            Gets the operation to perform on the source.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.ClipPathProcessor.CreatePixelSpecificProcessor``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.Image{``0},SixLabors.ImageSharp.Rectangle)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.ClipPathProcessor`1">
            <summary>
            The main workhorse class. This has access to the pixel buffer but
            in an abstract/generic way.
            </summary>
            <typeparam name="TPixel">The type of pixel.</typeparam>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.DrawPathProcessor">
            <summary>
            Defines a processor to fill <see cref="T:SixLabors.ImageSharp.Image"/> pixels withing a given <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>
            with the given <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Brush"/> and blending defined by the given <see cref="T:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions"/>.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.DrawPathProcessor.#ctor(SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Pen,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.DrawPathProcessor" /> class.
            </summary>
            <param name="options">The graphics options.</param>
            <param name="pen">The details how to outline the region of interest.</param>
            <param name="path">The path to be filled.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.DrawPathProcessor.Pen">
            <summary>
            Gets the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Brush"/> used for filling the destination image.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.DrawPathProcessor.Path">
            <summary>
            Gets the path that this processor applies to.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.DrawPathProcessor.Options">
            <summary>
            Gets the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions"/> defining how to blend the brush pixels over the image pixels.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.DrawPathProcessor.CreatePixelSpecificProcessor``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.Image{``0},SixLabors.ImageSharp.Rectangle)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillPathProcessor">
            <summary>
            Defines a processor to fill <see cref="T:SixLabors.ImageSharp.Image"/> pixels withing a given <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>
            with the given <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Brush"/> and blending defined by the given <see cref="T:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions"/>.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillPathProcessor.MinimumSubpixelCount">
            <summary>
            Minimum subpixel count for rasterization, being applied even if antialiasing is off.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillPathProcessor.#ctor(SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillPathProcessor" /> class.
            </summary>
            <param name="options">The graphics options.</param>
            <param name="brush">The details how to fill the region of interest.</param>
            <param name="path">The logic path to be filled.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillPathProcessor.Brush">
            <summary>
            Gets the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Brush"/> used for filling the destination image.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillPathProcessor.Region">
            <summary>
            Gets the logic path that this processor applies to.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillPathProcessor.Options">
            <summary>
            Gets the <see cref="T:SixLabors.ImageSharp.GraphicsOptions"/> defining how to blend the brush pixels over the image pixels.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillPathProcessor.CreatePixelSpecificProcessor``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.Image{``0},SixLabors.ImageSharp.Rectangle)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillPathProcessor`1">
            <summary>
            Uses a brush and a shape to fill the shape with contents of the brush.
            </summary>
            <typeparam name="TPixel">The type of the color.</typeparam>
            <seealso cref="T:SixLabors.ImageSharp.Processing.Processors.ImageProcessor`1" />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillPathProcessor`1.OnFrameApply(SixLabors.ImageSharp.ImageFrame{`0})">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillProcessor">
            <summary>
            Defines a processor to fill an <see cref="T:SixLabors.ImageSharp.Image"/> with the given <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Brush"/>
            using blending defined by the given <see cref="T:SixLabors.ImageSharp.GraphicsOptions"/>.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillProcessor.#ctor(SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillProcessor"/> class.
            </summary>
            <param name="options">The <see cref="T:SixLabors.ImageSharp.GraphicsOptions"/> defining how to blend the brush pixels over the image pixels.</param>
            <param name="brush">The brush to use for filling.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillProcessor.Brush">
            <summary>
            Gets the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Brush"/> used for filling the destination image.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillProcessor.Options">
            <summary>
            Gets the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions"/> defining how to blend the brush pixels over the image pixels.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillProcessor.CreatePixelSpecificProcessor``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.Image{``0},SixLabors.ImageSharp.Rectangle)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillProcessor`1">
            <summary>
            Using the brush as a source of pixels colors blends the brush color with source.
            </summary>
            <typeparam name="TPixel">The pixel format.</typeparam>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillProcessor`1.OnFrameApply(SixLabors.ImageSharp.ImageFrame{`0})">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillProcessor`1.SolidBrushRowIntervalOperation.Invoke(SixLabors.ImageSharp.Memory.RowInterval@)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Drawing.FillProcessor`1.RowIntervalOperation.Invoke(SixLabors.ImageSharp.Memory.RowInterval@)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor">
            <summary>
            Defines a processor to draw text on an <see cref="T:SixLabors.ImageSharp.Image"/>.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor.#ctor(SixLabors.ImageSharp.Drawing.Processing.DrawingOptions,SixLabors.ImageSharp.Drawing.Processing.RichTextOptions,System.String,SixLabors.ImageSharp.Drawing.Processing.Brush,SixLabors.ImageSharp.Drawing.Processing.Pen)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor"/> class.
            </summary>
            <param name="drawingOptions">The drawing options.</param>
            <param name="textOptions">The text rendering options.</param>
            <param name="text">The text we want to render</param>
            <param name="brush">The brush to source pixel colors from.</param>
            <param name="pen">The pen to outline text with.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor.Brush">
            <summary>
            Gets the brush used to fill the glyphs.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor.DrawingOptions">
            <summary>
            Gets the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.DrawingOptions"/> defining blending modes and shape drawing settings.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor.TextOptions">
            <summary>
            Gets the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.RichTextOptions"/> defining text-specific drawing settings.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor.Text">
            <summary>
            Gets the text to draw.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor.Pen">
            <summary>
            Gets the pen used for outlining the text, if Null then we will not outline
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor.Location">
            <summary>
            Gets the location to draw the text at.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor.CreatePixelSpecificProcessor``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.Image{``0},SixLabors.ImageSharp.Rectangle)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor`1">
            <summary>
            Using the brush as a source of pixels colors blends the brush color with source.
            </summary>
            <typeparam name="TPixel">The pixel format.</typeparam>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.DrawTextProcessor`1.OnFrameApply(SixLabors.ImageSharp.ImageFrame{`0})">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.RichTextGlyphRenderer">
            <summary>
            Allows the rendering of rich text configured via <see cref="T:SixLabors.ImageSharp.Drawing.Processing.RichTextOptions"/>.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.RichTextGlyphRenderer.BeginText(SixLabors.Fonts.FontRectangle@)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.RichTextGlyphRenderer.BeginGlyph(SixLabors.Fonts.FontRectangle@,SixLabors.Fonts.GlyphRendererParameters@)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.Processors.Text.RichTextGlyphRenderer.SetColor(SixLabors.Fonts.GlyphColor)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.RadialGradientBrush">
            <summary>
            A radial gradient brush, defined by center point and radius.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RadialGradientBrush.#ctor(SixLabors.ImageSharp.PointF,System.Single,SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode,SixLabors.ImageSharp.Drawing.Processing.ColorStop[])">
            <inheritdoc cref="T:SixLabors.ImageSharp.Drawing.Processing.GradientBrush" />
            <param name="center">The center of the circular gradient and 0 for the color stops.</param>
            <param name="radius">The radius of the circular gradient and 1 for the color stops.</param>
            <param name="repetitionMode">Defines how the colors in the gradient are repeated.</param>
            <param name="colorStops">the color stops as defined in base class.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RadialGradientBrush.Equals(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RadialGradientBrush.CreateApplicator``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{``0},SixLabors.ImageSharp.RectangleF)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.RadialGradientBrush.RadialGradientBrushApplicator`1">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RadialGradientBrush.RadialGradientBrushApplicator`1.#ctor(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{`0},SixLabors.ImageSharp.PointF,System.Single,SixLabors.ImageSharp.Drawing.Processing.ColorStop[],SixLabors.ImageSharp.Drawing.Processing.GradientRepetitionMode)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.RadialGradientBrush.RadialGradientBrushApplicator`1" /> class.
            </summary>
            <param name="configuration">The configuration instance to use when performing operations.</param>
            <param name="options">The graphics options.</param>
            <param name="target">The target image.</param>
            <param name="center">Center point of the gradient.</param>
            <param name="radius">Radius of the gradient.</param>
            <param name="colorStops">Definition of colors.</param>
            <param name="repetitionMode">How the colors are repeated beyond the first gradient.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RadialGradientBrush.RadialGradientBrushApplicator`1.PositionOnGradient(System.Single,System.Single)">
            <summary>
            As this is a circular gradient, the position on the gradient is based on
            the distance of the point to the center.
            </summary>
            <param name="x">The x-coordinate of the target pixel.</param>
            <param name="y">The y-coordinate of the target pixel.</param>
            <returns>the position on the color gradient.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RadialGradientBrush.RadialGradientBrushApplicator`1.Apply(System.Span{System.Single},System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush">
            <summary>
            Provides an implementation of a brush that can recolor an image
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush.#ctor(SixLabors.ImageSharp.Color,SixLabors.ImageSharp.Color,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush" /> class.
            </summary>
            <param name="sourceColor">Color of the source.</param>
            <param name="targetColor">Color of the target.</param>
            <param name="threshold">The threshold as a value between 0 and 1.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush.Threshold">
            <summary>
            Gets the threshold.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush.SourceColor">
            <summary>
            Gets the source color.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush.TargetColor">
            <summary>
            Gets the target color.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush.CreateApplicator``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{``0},SixLabors.ImageSharp.RectangleF)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush.Equals(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush.RecolorBrushApplicator`1">
            <summary>
            The recolor brush applicator.
            </summary>
            <typeparam name="TPixel">The pixel format.</typeparam>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush.RecolorBrushApplicator`1.#ctor(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{`0},`0,`0,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush.RecolorBrushApplicator`1" /> class.
            </summary>
            <param name="configuration">The configuration instance to use when performing operations.</param>
            <param name="options">The options</param>
            <param name="source">The source image.</param>
            <param name="sourceColor">Color of the source.</param>
            <param name="targetColor">Color of the target.</param>
            <param name="threshold">The threshold .</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush.RecolorBrushApplicator`1.Apply(System.Span{System.Single},System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RecolorBrush.RecolorBrushApplicator`1.Dispose(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.RichTextOptions">
            <summary>
            Provides configuration options for rendering and shaping of rich text.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RichTextOptions.#ctor(SixLabors.Fonts.Font)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.RichTextOptions" /> class.
            </summary>
            <param name="font">The font.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.RichTextOptions.#ctor(SixLabors.ImageSharp.Drawing.Processing.RichTextOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.RichTextOptions" /> class from properties
            copied from the given instance.
            </summary>
            <param name="options">The options whose properties are copied into this instance.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.RichTextOptions.TextRuns">
            <summary>
            Gets or sets an optional collection of text runs to apply to the body of text.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.RichTextOptions.Path">
            <summary>
            Gets or sets an optional path to draw the text along.
            </summary>
            <remarks>
            When this property is not <see langword="null"/> the <see cref="P:SixLabors.Fonts.TextOptions.Origin"/>
            property is automatically applied as a translation to a copy of the path for processing.
            </remarks>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.RichTextRun">
            <summary>
            Represents a run of drawable text spanning a series of graphemes within a string.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.RichTextRun.Brush">
            <summary>
            Gets or sets the brush used for filling this run.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.RichTextRun.Pen">
            <summary>
            Gets or sets the pen used for outlining this run.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.RichTextRun.StrikeoutPen">
            <summary>
            Gets or sets the pen used for drawing strikeout features for this run.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.RichTextRun.UnderlinePen">
            <summary>
            Gets or sets the pen used for drawing underline features for this run.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.RichTextRun.OverlinePen">
            <summary>
            Gets or sets the pen used for drawing overline features for this run.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.ShapeGraphicOptionsDefaultsExtensions">
            <summary>
            Adds extensions that allow the configuration of <see cref="T:SixLabors.ImageSharp.Drawing.Processing.ShapeOptions"/>.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ShapeGraphicOptionsDefaultsExtensions.SetShapeOptions(SixLabors.ImageSharp.Processing.IImageProcessingContext,System.Action{SixLabors.ImageSharp.Drawing.Processing.ShapeOptions})">
            <summary>
            Sets the default shape processing options against The source image processing context.
            </summary>
            <param name="context">The image processing context to store default against.</param>
            <param name="optionsBuilder">The action to update instance of the default options used.</param>
            <returns>The passed in <paramref name="context"/> to allow chaining.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ShapeGraphicOptionsDefaultsExtensions.SetShapeOptions(SixLabors.ImageSharp.Configuration,System.Action{SixLabors.ImageSharp.Drawing.Processing.ShapeOptions})">
            <summary>
            Sets the default shape processing options against the configuration.
            </summary>
            <param name="configuration">The configuration to store default against.</param>
            <param name="optionsBuilder">The default options to use.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ShapeGraphicOptionsDefaultsExtensions.SetShapeOptions(SixLabors.ImageSharp.Processing.IImageProcessingContext,SixLabors.ImageSharp.Drawing.Processing.ShapeOptions)">
            <summary>
            Sets the default shape processing options against The source image processing context.
            </summary>
            <param name="context">The image processing context to store default against.</param>
            <param name="options">The default options to use.</param>
            <returns>The passed in <paramref name="context"/> to allow chaining.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ShapeGraphicOptionsDefaultsExtensions.SetShapeOptions(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.Drawing.Processing.ShapeOptions)">
            <summary>
            Sets the default shape processing options against the configuration.
            </summary>
            <param name="configuration">The configuration to store default against.</param>
            <param name="options">The default options to use.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ShapeGraphicOptionsDefaultsExtensions.GetShapeOptions(SixLabors.ImageSharp.Processing.IImageProcessingContext)">
            <summary>
            Gets the default shape processing options against The source image processing context.
            </summary>
            <param name="context">The image processing context to retrieve defaults from.</param>
            <returns>The globally configured default options.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ShapeGraphicOptionsDefaultsExtensions.GetShapeOptions(SixLabors.ImageSharp.Configuration)">
            <summary>
            Gets the default shape processing options against The source image processing context.
            </summary>
            <param name="configuration">The configuration to retrieve defaults from.</param>
            <returns>The globally configured default options.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.ShapeOptions">
            <summary>
            Options for influencing the drawing functions.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ShapeOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.ShapeOptions"/> class.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.ShapeOptions.ClippingOperation">
            <summary>
            Gets or sets the clipping operation.
            <para/>
            Defaults to <see cref="F:SixLabors.ImageSharp.Drawing.ClippingOperation.Difference"/>.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.ShapeOptions.IntersectionRule">
            <summary>
            Gets or sets the rule for calculating intersection points.
            <para/>
            Defaults to <see cref="F:SixLabors.ImageSharp.Drawing.IntersectionRule.EvenOdd"/>.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.ShapeOptions.DeepClone">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.SolidBrush">
            <summary>
            Provides an implementation of a solid brush for painting solid color areas.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidBrush.#ctor(SixLabors.ImageSharp.Color)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.SolidBrush"/> class.
            </summary>
            <param name="color">The color.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Processing.SolidBrush.Color">
            <summary>
            Gets the color.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidBrush.CreateApplicator``1(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{``0},SixLabors.ImageSharp.RectangleF)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidBrush.Equals(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.SolidBrush.SolidBrushApplicator`1">
            <summary>
            The solid brush applicator.
            </summary>
            <typeparam name="TPixel">The pixel format.</typeparam>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidBrush.SolidBrushApplicator`1.#ctor(SixLabors.ImageSharp.Configuration,SixLabors.ImageSharp.GraphicsOptions,SixLabors.ImageSharp.ImageFrame{`0},`0)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.SolidBrush.SolidBrushApplicator`1"/> class.
            </summary>
            <param name="configuration">The configuration instance to use when performing operations.</param>
            <param name="options">The graphics options.</param>
            <param name="source">The source image.</param>
            <param name="color">The color.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidBrush.SolidBrushApplicator`1.Apply(System.Span{System.Single},System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidBrush.SolidBrushApplicator`1.Dispose(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Processing.SolidPen">
            <summary>
            Defines a pen that can apply a pattern to a line with a set brush and thickness.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidPen.#ctor(SixLabors.ImageSharp.Color)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.SolidPen"/> class.
            </summary>
            <param name="color">The color.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidPen.#ctor(SixLabors.ImageSharp.Color,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.SolidPen"/> class.
            </summary>
            <param name="color">The color.</param>
            <param name="width">The width.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidPen.#ctor(SixLabors.ImageSharp.Drawing.Processing.Brush)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.SolidPen"/> class.
            </summary>
            <param name="strokeFill">The brush used to fill the stroke outline.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidPen.#ctor(SixLabors.ImageSharp.Drawing.Processing.Brush,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.SolidPen"/> class.
            </summary>
            <param name="strokeFill">The brush used to fill the stroke outline.</param>
            <param name="strokeWidth">The stroke width in px units.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidPen.#ctor(SixLabors.ImageSharp.Drawing.Processing.PenOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Processing.SolidPen"/> class.
            </summary>
            <param name="options">The pen options.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidPen.Equals(SixLabors.ImageSharp.Drawing.Processing.Pen)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Processing.SolidPen.GeneratePath(SixLabors.ImageSharp.Drawing.IPath,System.Single)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.ArcLineSegment">
            <summary>
            Represents a line segment that contains radii and angles that will be rendered as a elliptical arc.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ArcLineSegment.#ctor(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.SizeF,System.Single,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.ArcLineSegment"/> class.
            </summary>
            <param name="from">The absolute coordinates of the current point on the path.</param>
            <param name="to">The absolute coordinates of the final point of the arc.</param>
            <param name="radius">The radii of the ellipse (also known as its semi-major and semi-minor axes).</param>
            <param name="rotation">The angle, in degrees, from the x-axis of the current coordinate system to the x-axis of the ellipse.</param>
            <param name="largeArc">
            The large arc flag, and is <see langword="false"/> if an arc spanning less than or equal to 180 degrees
            is chosen, or <see langword="true"/> if an arc spanning greater than 180 degrees is chosen.
            </param>
            <param name="sweep">
            The sweep flag, and is <see langword="false"/> if the line joining center to arc sweeps through decreasing
            angles, or <see langword="true"/> if it sweeps through increasing angles.
            </param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ArcLineSegment.#ctor(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.SizeF,System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.ArcLineSegment"/> class.
            </summary>
            <param name="center">The coordinates of the center of the ellipse.</param>
            <param name="radius">The radii of the ellipse (also known as its semi-major and semi-minor axes).</param>
            <param name="rotation">The angle, in degrees, from the x-axis of the current coordinate system to the x-axis of the ellipse.</param>
            <param name="startAngle">
            The start angle of the elliptical arc prior to the stretch and rotate operations.
            (0 is at the 3 o'clock position of the arc's circle).
            </param>
            <param name="sweepAngle">The angle between <paramref name="startAngle"/> and the end of the arc.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.ArcLineSegment.EndPoint">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ArcLineSegment.Flatten">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ArcLineSegment.Transform(System.Numerics.Matrix3x2)">
            <summary>
            Transforms the current <see cref="T:SixLabors.ImageSharp.Drawing.ArcLineSegment"/> using specified matrix.
            </summary>
            <param name="matrix">The transformation matrix.</param>
            <returns>An <see cref="T:SixLabors.ImageSharp.Drawing.ArcLineSegment"/> with the matrix applied to it.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ArcLineSegment.SixLabors#ImageSharp#Drawing#ILineSegment#Transform(System.Numerics.Matrix3x2)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.ClipPathExtensions">
            <summary>
            Provides extension methods to <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> that allow the clipping of shapes.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ClipPathExtensions.Clip(SixLabors.ImageSharp.Drawing.IPath,SixLabors.ImageSharp.Drawing.IPath[])">
            <summary>
            Clips the specified subject path with the provided clipping paths.
            </summary>
            <param name="subjectPath">The subject path.</param>
            <param name="clipPaths">The clipping paths.</param>
            <returns>The clipped <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>.</returns>
            <exception cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException">Thrown when an error occurred while attempting to clip the polygon.</exception>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ClipPathExtensions.Clip(SixLabors.ImageSharp.Drawing.IPath,SixLabors.ImageSharp.Drawing.Processing.ShapeOptions,SixLabors.ImageSharp.Drawing.IPath[])">
            <summary>
            Clips the specified subject path with the provided clipping paths.
            </summary>
            <param name="subjectPath">The subject path.</param>
            <param name="options">The shape options.</param>
            <param name="clipPaths">The clipping paths.</param>
            <returns>The clipped <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>.</returns>
            <exception cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException">Thrown when an error occurred while attempting to clip the polygon.</exception>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ClipPathExtensions.Clip(SixLabors.ImageSharp.Drawing.IPath,System.Collections.Generic.IEnumerable{SixLabors.ImageSharp.Drawing.IPath})">
            <summary>
            Clips the specified subject path with the provided clipping paths.
            </summary>
            <param name="subjectPath">The subject path.</param>
            <param name="clipPaths">The clipping paths.</param>
            <returns>The clipped <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>.</returns>
            <exception cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException">Thrown when an error occurred while attempting to clip the polygon.</exception>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ClipPathExtensions.Clip(SixLabors.ImageSharp.Drawing.IPath,SixLabors.ImageSharp.Drawing.Processing.ShapeOptions,System.Collections.Generic.IEnumerable{SixLabors.ImageSharp.Drawing.IPath})">
            <summary>
            Clips the specified subject path with the provided clipping paths.
            </summary>
            <param name="subjectPath">The subject path.</param>
            <param name="options">The shape options.</param>
            <param name="clipPaths">The clipping paths.</param>
            <returns>The clipped <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>.</returns>
            <exception cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException">Thrown when an error occurred while attempting to clip the polygon.</exception>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.ClippingOperation">
            <summary>
            Provides options for boolean clipping operations.
            </summary>
            <remarks>
            All clipping operations except for Difference are commutative.
            </remarks>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.ClippingOperation.None">
            <summary>
            No clipping is performed.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.ClippingOperation.Intersection">
            <summary>
            Clips regions covered by both subject and clip polygons.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.ClippingOperation.Union">
            <summary>
            Clips regions covered by subject or clip polygons, or both polygons.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.ClippingOperation.Difference">
            <summary>
            Clips regions covered by subject, but not clip polygons.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.ClippingOperation.Xor">
            <summary>
            Clips regions covered by subject or clip polygons, but not both.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.ComplexPolygon">
            <summary>
            Represents a complex polygon made up of one or more shapes overlayed on each other,
            where overlaps causes holes.
            </summary>
            <seealso cref="T:SixLabors.ImageSharp.Drawing.IPath" />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ComplexPolygon.#ctor(SixLabors.ImageSharp.PointF[],SixLabors.ImageSharp.PointF[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.ComplexPolygon"/> class.
            </summary>
            <param name="contour">The contour path.</param>
            <param name="hole">The hole path.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ComplexPolygon.#ctor(System.Collections.Generic.IEnumerable{SixLabors.ImageSharp.Drawing.IPath})">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.ComplexPolygon" /> class.
            </summary>
            <param name="paths">The paths.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ComplexPolygon.#ctor(SixLabors.ImageSharp.Drawing.IPath[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.ComplexPolygon" /> class.
            </summary>
            <param name="paths">The paths.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.ComplexPolygon.PathType">
            <inheritdoc/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.ComplexPolygon.Paths">
            <summary>
            Gets the collection of paths that make up this shape.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.ComplexPolygon.Bounds">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ComplexPolygon.Transform(System.Numerics.Matrix3x2)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ComplexPolygon.Flatten">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ComplexPolygon.AsClosedPath">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ComplexPolygon.SixLabors#ImageSharp#Drawing#IPathInternals#PointAlongPath(System.Single)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ComplexPolygon.SixLabors#ImageSharp#Drawing#IInternalPathOwner#GetRingsAsInternalPath">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment">
            <summary>
            Represents a line segment that contains a lists of control points that will be rendered as a cubic bezier curve
            </summary>
            <seealso cref="T:SixLabors.ImageSharp.Drawing.ILineSegment" />
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment.linePoints">
            <summary>
            The line points.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment.#ctor(SixLabors.ImageSharp.PointF[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment"/> class.
            </summary>
            <param name="points">The points.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment.#ctor(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment"/> class.
            </summary>
            <param name="start">The start.</param>
            <param name="controlPoint1">The control point1.</param>
            <param name="controlPoint2">The control point2.</param>
            <param name="end">The end.</param>
            <param name="additionalPoints">The additional points.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment.ControlPoints">
            <summary>
            Gets the control points.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment.EndPoint">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment.Flatten">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment.Transform(System.Numerics.Matrix3x2)">
            <summary>
            Transforms the current LineSegment using specified matrix.
            </summary>
            <param name="matrix">The matrix.</param>
            <returns>A line segment with the matrix applied to it.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment.SixLabors#ImageSharp#Drawing#ILineSegment#Transform(System.Numerics.Matrix3x2)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.CubicBezierLineSegment.CalculateBezierPoint(System.Single,System.Numerics.Vector2,System.Numerics.Vector2,System.Numerics.Vector2,System.Numerics.Vector2)">
            <summary>
            Calculates the bezier point along the line.
            </summary>
            <param name="t">The position within the line.</param>
            <param name="p0">The p 0.</param>
            <param name="p1">The p 1.</param>
            <param name="p2">The p 2.</param>
            <param name="p3">The p 3.</param>
            <returns>
            The <see cref="T:System.Numerics.Vector2"/>.
            </returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.EllipsePolygon">
            <summary>
            An elliptical shape made up of a single path made up of one of more <see cref="T:SixLabors.ImageSharp.Drawing.ILineSegment"/>s.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EllipsePolygon.#ctor(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.SizeF)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.EllipsePolygon" /> class.
            </summary>
            <param name="location">The location the center of the ellipse will be placed.</param>
            <param name="size">The width/height of the final ellipse.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EllipsePolygon.#ctor(SixLabors.ImageSharp.PointF,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.EllipsePolygon" /> class.
            </summary>
            <param name="location">The location the center of the circle will be placed.</param>
            <param name="radius">The radius final circle.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EllipsePolygon.#ctor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.EllipsePolygon" /> class.
            </summary>
            <param name="x">The x-coordinate of the center of the ellipse.</param>
            <param name="y">The y-coordinate of the center of the ellipse.</param>
            <param name="width">The width the ellipse should have.</param>
            <param name="height">The height the ellipse should have.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EllipsePolygon.#ctor(System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.EllipsePolygon" /> class.
            </summary>
            <param name="x">The x-coordinate of the center of the circle.</param>
            <param name="y">The y-coordinate of the center of the circle.</param>
            <param name="radius">The radius final circle.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.EllipsePolygon.IsClosed">
            <inheritdoc/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.EllipsePolygon.Points">
            <inheritdoc/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.EllipsePolygon.Bounds">
            <inheritdoc />
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.EllipsePolygon.PathType">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EllipsePolygon.Transform(System.Numerics.Matrix3x2)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EllipsePolygon.AsClosedPath">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EllipsePolygon.Flatten">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EllipsePolygon.SixLabors#ImageSharp#Drawing#IPathInternals#PointAlongPath(System.Single)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EllipsePolygon.SixLabors#ImageSharp#Drawing#IInternalPathOwner#GetRingsAsInternalPath">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.EmptyPath">
            <summary>
            A path that is always empty.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.EmptyPath.ClosedPath">
            <summary>
            Gets the closed path instance of the empty path
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.EmptyPath.OpenPath">
            <summary>
            Gets the open path instance of the empty path
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.EmptyPath.PathType">
            <inheritdoc />
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.EmptyPath.Bounds">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EmptyPath.AsClosedPath">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EmptyPath.Flatten">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.EmptyPath.Transform(System.Numerics.Matrix3x2)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.EndCapStyle">
            <summary>
            The style to apply to the end cap when generating an outline.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.EndCapStyle.Butt">
            <summary>
            The outline stops exactly at the end of the path.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.EndCapStyle.Round">
            <summary>
            The outline extends with a rounded style passed the end of the path.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.EndCapStyle.Square">
            <summary>
            The outlines ends squared off passed the end of the path.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.EndCapStyle.Polygon">
            <summary>
            The outline is treated as a polygon.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.EndCapStyle.Joined">
            <summary>
            The outlines ends are joined and the path treated as a polyline
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.ArrayExtensions">
            <summary>
            Extensions on arrays.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ArrayExtensions.Merge``1(``0[],``0[])">
            <summary>
            Merges the specified source2.
            </summary>
            <typeparam name="T">the type of the array</typeparam>
            <param name="source1">The source1.</param>
            <param name="source2">The source2.</param>
            <returns>the Merged arrays</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Shapes.Helpers.TopologyUtilities">
            <summary>
            Implements some basic algorithms on raw data structures.
            Polygons are represented with a span of points,
            where first point should be repeated at the end.
            </summary>
            <remarks>
            Positive orientation means Clockwise in world coordinates (positive direction goes UP on paper).
            Since the Drawing library deals mostly with Screen coordinates where this is opposite,
            we use different terminology here to avoid confusion.
            </remarks>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.Helpers.TopologyUtilities.EnsureOrientation(System.Span{SixLabors.ImageSharp.PointF},System.Int32)">
            <summary>
            Positive: CCW in world coords (CW on screen)
            Negative: CW in world coords (CCW on screen)
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.Helpers.TopologyUtilities.GetPolygonOrientation(System.ReadOnlySpan{SixLabors.ImageSharp.PointF})">
            <summary>
            Zero: area is 0
            Positive: CCW in world coords (CW on screen)
            Negative: CW in world coords (CCW on screen)
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.Clipper">
            <summary>
            Library to clip polygons.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.Clipper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.Clipper"/> class.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.Clipper.GenerateClippedShapes(SixLabors.ImageSharp.Drawing.ClippingOperation,SixLabors.ImageSharp.Drawing.IntersectionRule)">
            <summary>
            Generates the clipped shapes from the previously provided paths.
            </summary>
            <param name="operation">The clipping operation.</param>
            <param name="rule">The intersection rule.</param>
            <returns>The <see cref="T:IPath[]"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.Clipper.AddPaths(System.Collections.Generic.IEnumerable{SixLabors.ImageSharp.Drawing.IPath},SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClippingType)">
            <summary>
            Adds the shapes.
            </summary>
            <param name="paths">The paths.</param>
            <param name="clippingType">The clipping type.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.Clipper.AddPath(SixLabors.ImageSharp.Drawing.IPath,SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClippingType)">
            <summary>
            Adds the path.
            </summary>
            <param name="path">The path.</param>
            <param name="clippingType">The clipping type.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.Clipper.AddPath(SixLabors.ImageSharp.Drawing.ISimplePath,SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClippingType)">
            <summary>
            Adds the path.
            </summary>
            <param name="path">The path.</param>
            <param name="clippingType">Type of the poly.</param>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException">
            <summary>
            The exception that is thrown when an error occurs clipping a polygon.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException"/> class.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException"/> class.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException" /> class with a specified error message and a
            reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a <see langword="null"/>
            reference if no inner exception is specified. </param>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperOffset">
            <summary>
            Wrapper for clipper offset
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperOffset.#ctor(System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperOffset"/> class.
            </summary>
            <param name="meterLimit">meter limit</param>
            <param name="arcTolerance">arc tolerance</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperOffset.Execute(System.Single)">
            <summary>
            Calculates an offset polygon based on the given path and width.
            </summary>
            <param name="width">Width</param>
            <returns>path offset</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperOffset.AddPath(System.ReadOnlySpan{SixLabors.ImageSharp.PointF},SixLabors.ImageSharp.Drawing.JointStyle,SixLabors.ImageSharp.Drawing.EndCapStyle)">
            <summary>
            Adds the path points
            </summary>
            <param name="pathPoints">The path points</param>
            <param name="jointStyle">Joint Style</param>
            <param name="endCapStyle">Endcap Style</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperOffset.AddPath(SixLabors.ImageSharp.Drawing.IPath,SixLabors.ImageSharp.Drawing.JointStyle,SixLabors.ImageSharp.Drawing.EndCapStyle)">
            <summary>
            Adds the path.
            </summary>
            <param name="path">The path.</param>
            <param name="jointStyle">Joint Style</param>
            <param name="endCapStyle">Endcap Style</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperOffset.AddPath(SixLabors.ImageSharp.Drawing.ISimplePath,SixLabors.ImageSharp.Drawing.JointStyle,SixLabors.ImageSharp.Drawing.EndCapStyle)">
            <summary>
            Adds the path.
            </summary>
            <param name="path">The path.</param>
            <param name="jointStyle">Joint Style</param>
            <param name="endCapStyle">Endcap Style</param>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClippingType">
            <summary>
            Defines the polygon clipping type.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClippingType.Subject">
            <summary>
            Represents a shape to act as a subject which will be clipped or merged.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClippingType.Clip">
            <summary>
            Represents a shape to act as a clipped path.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.FillRule">
            <summary>
            By far the most widely used filling rules for polygons are EvenOdd
            and NonZero, sometimes called Alternate and Winding respectively.
            <see href="https://en.wikipedia.org/wiki/Nonzero-rule"/>
            </summary>
            <remarks>
            TODO: This overlaps with the <see cref="T:SixLabors.ImageSharp.Drawing.IntersectionRule"/> enum.
            We should see if we can enhance the <see cref="T:SixLabors.ImageSharp.Drawing.Shapes.Rasterization.PolygonScanner"/> to support all these rules.
            </remarks>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.PolygonClipper">
            <summary>
            Contains functions that cover most polygon boolean and offsetting needs.
            Ported from <see href="https://github.com/AngusJohnson/Clipper2"/> and originally licensed
            under <see href="http://www.boost.org/LICENSE_1_0.txt"/>
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.PolygonOffsetter">
            <summary>
            Contains functions to offset paths (inflate/shrink).
            Ported from <see href="https://github.com/AngusJohnson/Clipper2"/> and originally licensed
            under <see href="http://www.boost.org/LICENSE_1_0.txt"/>
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Shapes.Rasterization.ActiveEdgeList">
            <summary>
            The list of active edges as an index buffer into <see cref="P:SixLabors.ImageSharp.Drawing.Shapes.Rasterization.ScanEdgeCollection.Edges"/>.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Shapes.Rasterization.ScanEdge">
            <summary>
            Holds coordinates, and coefficients for a polygon edge to be horizontally scanned.
            The edge's segment is defined with the reciprocal slope form:
            x = p * y + q
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Shapes.TessellatedMultipolygon">
            <summary>
            Compact representation of a multipolygon.
            Applies some rules which are optimal to implement geometric algorithms:
            - Outer contour is oriented "Positive" (CCW in world coords, CW on screen)
            - Holes are oriented "Negative" (CW in world, CCW on screen)
            - First vertex is always repeated at the end of the span in each ring
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.VectorExtensions">
            <summary>
            Extensions on arrays.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.VectorExtensions.Equivalent(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,System.Single)">
            <summary>
            Merges the specified source2.
            </summary>
            <param name="source1">The source1.</param>
            <param name="source2">The source2.</param>
            <param name="threshold">The threshold.</param>
            <returns>
            the Merged arrays
            </returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.VectorExtensions.Equivalent(System.Numerics.Vector2,System.Numerics.Vector2,System.Single)">
            <summary>
            Merges the specified source2.
            </summary>
            <param name="source1">The source1.</param>
            <param name="source2">The source2.</param>
            <param name="threshold">The threshold.</param>
            <returns>
            the Merged arrays
            </returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.IInternalPathOwner">
            <summary>
            An internal interface for shapes which are backed by <see cref="T:SixLabors.ImageSharp.Drawing.InternalPath"/>
            so we can have a fast path tessellating them.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.IInternalPathOwner.GetRingsAsInternalPath">
            <summary>
            Returns the rings as a readonly collection of <see cref="T:SixLabors.ImageSharp.Drawing.InternalPath"/> elements.
            </summary>
            <returns>The <see cref="T:System.Collections.Generic.IReadOnlyList`1"/>.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.ILineSegment">
            <summary>
            Represents a simple path segment
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.ILineSegment.EndPoint">
            <summary>
            Gets the end point.
            </summary>
            <value>
            The end point.
            </value>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ILineSegment.Flatten">
            <summary>
            Converts the <see cref="T:SixLabors.ImageSharp.Drawing.ILineSegment" /> into a simple linear path..
            </summary>
            <returns>Returns the current <see cref="T:SixLabors.ImageSharp.Drawing.ILineSegment" /> as simple linear path.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.ILineSegment.Transform(System.Numerics.Matrix3x2)">
            <summary>
            Transforms the current LineSegment using specified matrix.
            </summary>
            <param name="matrix">The matrix.</param>
            <returns>A line segment with the matrix applied to it.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.InternalPath">
            <summary>
            Internal logic for integrating linear paths.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.InternalPath.Epsilon">
            <summary>
            The epsilon for float comparison
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.InternalPath.MaxVector">
            <summary>
            The maximum vector
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.InternalPath.points">
            <summary>
            The points.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.InternalPath.closedPath">
            <summary>
            The closed path.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.InternalPath.#ctor(System.Collections.Generic.IReadOnlyList{SixLabors.ImageSharp.Drawing.ILineSegment},System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.InternalPath"/> class.
            </summary>
            <param name="segments">The segments.</param>
            <param name="isClosedPath">if set to <c>true</c> [is closed path].</param>
            <param name="removeCloseAndCollinear">Whether to remove close and collinear vertices</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.InternalPath.#ctor(SixLabors.ImageSharp.Drawing.ILineSegment,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.InternalPath" /> class.
            </summary>
            <param name="segment">The segment.</param>
            <param name="isClosedPath">if set to <c>true</c> [is closed path].</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.InternalPath.#ctor(System.ReadOnlyMemory{SixLabors.ImageSharp.PointF},System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.InternalPath" /> class.
            </summary>
            <param name="points">The points.</param>
            <param name="isClosedPath">if set to <c>true</c> [is closed path].</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.InternalPath.#ctor(SixLabors.ImageSharp.Drawing.InternalPath.PointData[],System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.InternalPath" /> class.
            </summary>
            <param name="points">The points.</param>
            <param name="isClosedPath">if set to <c>true</c> [is closed path].</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.InternalPath.Bounds">
            <summary>
            Gets the bounds.
            </summary>
            <value>
            The bounds.
            </value>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.InternalPath.Length">
            <summary>
            Gets the length.
            </summary>
            <value>
            The length.
            </value>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.InternalPath.PointCount">
            <summary>
            Gets the length.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.InternalPath.Points">
            <summary>
            Gets the points.
            </summary>
            <returns>The <see cref="T:System.Collections.Generic.IReadOnlyCollection`1"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.InternalPath.PointAlongPath(System.Single)">
            <summary>
            Calculates the point a certain distance a path.
            </summary>
            <param name="distanceAlongPath">The distance along the path to find details of.</param>
            <returns>
            Returns details about a point along a path.
            </returns>
            <exception cref="T:System.InvalidOperationException">Thrown if no points found.</exception>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.InternalPath.Simplify(System.Collections.Generic.IReadOnlyList{SixLabors.ImageSharp.Drawing.ILineSegment},System.Boolean,System.Boolean)">
            <summary>
            Simplifies the collection of segments.
            </summary>
            <param name="segments">The segments.</param>
            <param name="isClosed">Weather the path is closed or open.</param>
            <param name="removeCloseAndCollinear">Whether to remove close and collinear vertices</param>
            <returns>
            The <see cref="T:PointData[]"/>.
            </returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.IntersectionRule">
            <summary>
            Provides options for calculating intersection points.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.IntersectionRule.EvenOdd">
            <summary>
            Only odd numbered sub-regions are filled.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.IntersectionRule.NonZero">
            <summary>
            Only non-zero sub-regions are filled.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.IPath">
            <summary>
            Represents a logic path that can be drawn.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.IPath.PathType">
            <summary>
            Gets a value indicating whether this instance is closed, open or a composite path with a mixture of open and closed figures.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.IPath.Bounds">
            <summary>
            Gets the bounds enclosing the path.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.IPath.Flatten">
            <summary>
            Converts the <see cref="T:SixLabors.ImageSharp.Drawing.IPath" /> into a simple linear path.
            </summary>
            <returns>Returns the current <see cref="T:SixLabors.ImageSharp.Drawing.IPath" /> as simple linear path.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.IPath.Transform(System.Numerics.Matrix3x2)">
            <summary>
            Transforms the path using the specified matrix.
            </summary>
            <param name="matrix">The matrix.</param>
            <returns>A new path with the matrix applied to it.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.IPath.AsClosedPath">
            <summary>
            Returns this path with all figures closed.
            </summary>
            <returns>A new close <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.IPathCollection">
            <summary>
            Represents a logic path that can be drawn
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.IPathCollection.Bounds">
            <summary>
            Gets the bounds enclosing the path
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.IPathCollection.Transform(System.Numerics.Matrix3x2)">
            <summary>
            Transforms the path using the specified matrix.
            </summary>
            <param name="matrix">The matrix.</param>
            <returns>A new path with the matrix applied to it.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.IPathInternals">
            <summary>
            An interface for internal operations we don't want to expose on <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.IPathInternals.PointAlongPath(System.Single)">
            <summary>
            Returns information about a point at a given distance along a path.
            </summary>
            <param name="distance">The distance along the path to return details for.</param>
            <returns>
            The segment information.
            </returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.ISimplePath">
            <summary>
            Represents a simple (non-composite) path defined by a series of points.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.ISimplePath.IsClosed">
            <summary>
            Gets a value indicating whether this instance is a closed path.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.ISimplePath.Points">
            <summary>
            Gets the points that make this up as a simple linear path.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.JointStyle">
            <summary>
            The style to apply to the joints when generating an outline.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.JointStyle.Square">
            <summary>
            Joints are squared off 1 width distance from the corner.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.JointStyle.Round">
            <summary>
            Rounded joints. Joints generate with a rounded profile.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.JointStyle.Miter">
            <summary>
            Joints will generate to a long point unless the end of the point will exceed 20 times the width then we generate the joint using <see cref="F:SixLabors.ImageSharp.Drawing.JointStyle.Square"/>.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.LinearLineSegment">
            <summary>
            Represents a series of control points that will be joined by straight lines
            </summary>
            <seealso cref="T:SixLabors.ImageSharp.Drawing.ILineSegment" />
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.LinearLineSegment.points">
            <summary>
            The collection of points.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.LinearLineSegment.#ctor(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.LinearLineSegment"/> class.
            </summary>
            <param name="start">The start.</param>
            <param name="end">The end.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.LinearLineSegment.#ctor(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.LinearLineSegment" /> class.
            </summary>
            <param name="point1">The point1.</param>
            <param name="point2">The point2.</param>
            <param name="additionalPoints">Additional points</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.LinearLineSegment.#ctor(SixLabors.ImageSharp.PointF[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.LinearLineSegment"/> class.
            </summary>
            <param name="points">The points.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.LinearLineSegment.EndPoint">
            <summary>
            Gets the end point.
            </summary>
            <value>
            The end point.
            </value>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.LinearLineSegment.Flatten">
            <summary>
            Converts the <see cref="T:SixLabors.ImageSharp.Drawing.ILineSegment" /> into a simple linear path..
            </summary>
            <returns>
            Returns the current <see cref="T:SixLabors.ImageSharp.Drawing.ILineSegment" /> as simple linear path.
            </returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.LinearLineSegment.Transform(System.Numerics.Matrix3x2)">
            <summary>
            Transforms the current LineSegment using specified matrix.
            </summary>
            <param name="matrix">The matrix.</param>
            <returns>
            A line segment with the matrix applied to it.
            </returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.LinearLineSegment.SixLabors#ImageSharp#Drawing#ILineSegment#Transform(System.Numerics.Matrix3x2)">
            <summary>
            Transforms the current LineSegment using specified matrix.
            </summary>
            <param name="matrix">The matrix.</param>
            <returns>A line segment with the matrix applied to it.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.OutlinePathExtensions">
            <summary>
            Extensions to <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> that allow the generation of outlines.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.OutlinePathExtensions.GenerateOutline(SixLabors.ImageSharp.Drawing.IPath,System.Single)">
            <summary>
            Generates an outline of the path.
            </summary>
            <param name="path">The path to outline</param>
            <param name="width">The outline width.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> representing the outline.</returns>
            <exception cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException">Thrown when an offset cannot be calculated.</exception>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.OutlinePathExtensions.GenerateOutline(SixLabors.ImageSharp.Drawing.IPath,System.Single,SixLabors.ImageSharp.Drawing.JointStyle,SixLabors.ImageSharp.Drawing.EndCapStyle)">
            <summary>
            Generates an outline of the path.
            </summary>
            <param name="path">The path to outline</param>
            <param name="width">The outline width.</param>
            <param name="jointStyle">The style to apply to the joints.</param>
            <param name="endCapStyle">The style to apply to the end caps.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> representing the outline.</returns>
            <exception cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException">Thrown when an offset cannot be calculated.</exception>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.OutlinePathExtensions.GenerateOutline(SixLabors.ImageSharp.Drawing.IPath,System.Single,System.ReadOnlySpan{System.Single})">
            <summary>
            Generates an outline of the path with alternating on and off segments based on the pattern.
            </summary>
            <param name="path">The path to outline</param>
            <param name="width">The outline width.</param>
            <param name="pattern">The pattern made of multiples of the width.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> representing the outline.</returns>
            <exception cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException">Thrown when an offset cannot be calculated.</exception>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.OutlinePathExtensions.GenerateOutline(SixLabors.ImageSharp.Drawing.IPath,System.Single,System.ReadOnlySpan{System.Single},System.Boolean)">
            <summary>
            Generates an outline of the path with alternating on and off segments based on the pattern.
            </summary>
            <param name="path">The path to outline</param>
            <param name="width">The outline width.</param>
            <param name="pattern">The pattern made of multiples of the width.</param>
            <param name="startOff">Whether the first item in the pattern is on or off.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> representing the outline.</returns>
            <exception cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException">Thrown when an offset cannot be calculated.</exception>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.OutlinePathExtensions.GenerateOutline(SixLabors.ImageSharp.Drawing.IPath,System.Single,System.ReadOnlySpan{System.Single},SixLabors.ImageSharp.Drawing.JointStyle,SixLabors.ImageSharp.Drawing.EndCapStyle)">
            <summary>
            Generates an outline of the path with alternating on and off segments based on the pattern.
            </summary>
            <param name="path">The path to outline</param>
            <param name="width">The outline width.</param>
            <param name="pattern">The pattern made of multiples of the width.</param>
            <param name="jointStyle">The style to apply to the joints.</param>
            <param name="endCapStyle">The style to apply to the end caps.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> representing the outline.</returns>
            <exception cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException">Thrown when an offset cannot be calculated.</exception>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.OutlinePathExtensions.GenerateOutline(SixLabors.ImageSharp.Drawing.IPath,System.Single,System.ReadOnlySpan{System.Single},System.Boolean,SixLabors.ImageSharp.Drawing.JointStyle,SixLabors.ImageSharp.Drawing.EndCapStyle)">
            <summary>
            Generates an outline of the path with alternating on and off segments based on the pattern.
            </summary>
            <param name="path">The path to outline</param>
            <param name="width">The outline width.</param>
            <param name="pattern">The pattern made of multiples of the width.</param>
            <param name="startOff">Whether the first item in the pattern is on or off.</param>
            <param name="jointStyle">The style to apply to the joints.</param>
            <param name="endCapStyle">The style to apply to the end caps.</param>
            <returns>A new <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> representing the outline.</returns>
            <exception cref="T:SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper.ClipperException">Thrown when an offset cannot be calculated.</exception>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Path">
            <summary>
            A aggregate of <see cref="T:SixLabors.ImageSharp.Drawing.ILineSegment"/>s making a single logical path.
            </summary>
            <seealso cref="T:SixLabors.ImageSharp.Drawing.IPath" />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Path.#ctor(SixLabors.ImageSharp.PointF[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Path"/> class.
            </summary>
            <param name="points">The collection of points; processed as a series of linear line segments.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Path.#ctor(System.Collections.Generic.IEnumerable{SixLabors.ImageSharp.Drawing.ILineSegment})">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Path"/> class.
            </summary>
            <param name="segments">The segments.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Path.#ctor(SixLabors.ImageSharp.Drawing.Path)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Path" /> class.
            </summary>
            <param name="path">The path.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Path.#ctor(SixLabors.ImageSharp.Drawing.ILineSegment[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Path"/> class.
            </summary>
            <param name="segments">The segments.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Path.Empty">
            <summary>
            Gets the default empty path.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Path.SixLabors#ImageSharp#Drawing#ISimplePath#IsClosed">
            <inheritdoc/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Path.IsClosed">
            <inheritdoc cref="P:SixLabors.ImageSharp.Drawing.ISimplePath.IsClosed"/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Path.Points">
            <inheritdoc/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Path.Bounds">
            <inheritdoc />
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Path.PathType">
            <inheritdoc />
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Path.MaxIntersections">
            <summary>
            Gets the maximum number intersections that a shape can have when testing a line.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Path.LineSegments">
            <summary>
            Gets readonly collection of line segments.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Path.RemoveCloseAndCollinearPoints">
            <summary>
            Gets or sets a value indicating whether close or collinear vertices should be removed. TEST ONLY!
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Path.Transform(System.Numerics.Matrix3x2)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Path.AsClosedPath">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Path.Flatten">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Path.SixLabors#ImageSharp#Drawing#IPathInternals#PointAlongPath(System.Single)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Path.SixLabors#ImageSharp#Drawing#IInternalPathOwner#GetRingsAsInternalPath">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Path.TryParseSvgPath(System.String,SixLabors.ImageSharp.Drawing.IPath@)">
            <summary>
            Converts an SVG path string into an <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>.
            </summary>
            <param name="svgPath">The string containing the SVG path data.</param>
            <param name="value">
            When this method returns, contains the logic path converted from the given SVG path string; otherwise, <see langword="null"/>.
            This parameter is passed uninitialized.
            </param>
            <returns><see langword="true"/> if the input value can be parsed and converted; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Path.TryParseSvgPath(System.ReadOnlySpan{System.Char},SixLabors.ImageSharp.Drawing.IPath@)">
            <summary>
            Converts an SVG path string into an <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>.
            </summary>
            <param name="svgPath">The string containing the SVG path data.</param>
            <param name="value">
            When this method returns, contains the logic path converted from the given SVG path string; otherwise, <see langword="null"/>.
            This parameter is passed uninitialized.
            </param>
            <returns><see langword="true"/> if the input value can be parsed and converted; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.PathBuilder">
            <summary>
            Allow you to derivatively build shapes and paths.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder" /> class.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.#ctor(System.Numerics.Matrix3x2)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/> class.
            </summary>
            <param name="defaultTransform">The default transform.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.SetTransform(System.Numerics.Matrix3x2)">
            <summary>
            Sets the translation to be applied to all items to follow being applied to the <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.
            </summary>
            <param name="transform">The transform.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.SetOrigin(SixLabors.ImageSharp.PointF)">
            <summary>
            Sets the origin all subsequent point should be relative to.
            </summary>
            <param name="origin">The origin.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.ResetTransform">
            <summary>
            Resets the transform to the default.
            </summary>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.ResetOrigin">
            <summary>
            Resets the origin to the default.
            </summary>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.MoveTo(SixLabors.ImageSharp.PointF)">
            <summary>
            Moves to current point to the supplied vector.
            </summary>
            <param name="point">The point.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.LineTo(SixLabors.ImageSharp.PointF)">
            <summary>
            Draws the line connecting the current the current point to the new point.
            </summary>
            <param name="point">The point.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.LineTo(System.Single,System.Single)">
            <summary>
            Draws the line connecting the current the current point to the new point.
            </summary>
            <param name="x">The x.</param>
            <param name="y">The y.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddLine(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF)">
            <summary>
            Adds the line connecting the current point to the new point.
            </summary>
            <param name="start">The start.</param>
            <param name="end">The end.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddLine(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Adds the line connecting the current point to the new point.
            </summary>
            <param name="x1">The x1.</param>
            <param name="y1">The y1.</param>
            <param name="x2">The x2.</param>
            <param name="y2">The y2.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddLines(System.Collections.Generic.IEnumerable{SixLabors.ImageSharp.PointF})">
            <summary>
            Adds a series of line segments connecting the current point to the new points.
            </summary>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddLines(SixLabors.ImageSharp.PointF[])">
            <summary>
            Adds a series of line segments connecting the current point to the new points.
            </summary>
            <param name="points">The points.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddSegment(SixLabors.ImageSharp.Drawing.ILineSegment)">
            <summary>
            Adds the segment.
            </summary>
            <param name="segment">The segment.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.QuadraticBezierTo(System.Numerics.Vector2,System.Numerics.Vector2)">
            <summary>
            Draws a quadratic bezier from the current point to the <paramref name="point"/>
            </summary>
            <param name="secondControlPoint">The second control point.</param>
            <param name="point">The point.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.CubicBezierTo(System.Numerics.Vector2,System.Numerics.Vector2,System.Numerics.Vector2)">
            <summary>
            Draws a quadratic bezier from the current point to the <paramref name="point"/>
            </summary>
            <param name="secondControlPoint">The second control point.</param>
            <param name="thirdControlPoint">The third control point.</param>
            <param name="point">The point.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddQuadraticBezier(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF)">
            <summary>
            Adds a quadratic bezier curve to the current figure joining the <paramref name="startPoint"/> point to the <paramref name="endPoint"/>.
            </summary>
            <param name="startPoint">The start point.</param>
            <param name="controlPoint">The control point1.</param>
            <param name="endPoint">The end point.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddCubicBezier(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF)">
            <summary>
            Adds a cubic bezier curve to the current figure joining the <paramref name="startPoint"/> point to the <paramref name="endPoint"/>.
            </summary>
            <param name="startPoint">The start point.</param>
            <param name="controlPoint1">The control point1.</param>
            <param name="controlPoint2">The control point2.</param>
            <param name="endPoint">The end point.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.ArcTo(System.Single,System.Single,System.Single,System.Boolean,System.Boolean,SixLabors.ImageSharp.PointF)">
            <summary>
            <para>
            Adds an elliptical arc to the current figure. The arc curves from the last point to <paramref name="point"/>,
            choosing one of four possible routes: clockwise or counterclockwise, and smaller or larger.
            </para>
            <para>
            The arc sweep is always less than 360 degrees. The method appends a line
            to the last point if either radii are zero, or if last point is equal to <paramref name="point"/>.
            In addition the method scales the radii to fit last point and <paramref name="point"/> if both
            are greater than zero but too small to describe an arc.
            </para>
            </summary>
            <param name="radiusX">The x-radius of the ellipsis.</param>
            <param name="radiusY">The y-radius of the ellipsis.</param>
            <param name="rotation">The rotation along the X-axis; measured in degrees clockwise.</param>
            <param name="largeArc">
            The large arc flag, and is <see langword="false"/> if an arc spanning less than or equal to 180 degrees
            is chosen, or <see langword="true"/> if an arc spanning greater than 180 degrees is chosen.
            </param>
            <param name="sweep">
            The sweep flag, and is <see langword="false"/> if the line joining center to arc sweeps through decreasing
            angles, or <see langword="true"/> if it sweeps through increasing angles.
            </param>
            <param name="point">The end point of the arc.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddArc(SixLabors.ImageSharp.PointF,System.Single,System.Single,System.Single,System.Boolean,System.Boolean,SixLabors.ImageSharp.PointF)">
            <summary>
            <para>
            Adds an elliptical arc to the current figure. The arc curves from the <paramref name="startPoint"/> to <paramref name="endPoint"/>,
            choosing one of four possible routes: clockwise or counterclockwise, and smaller or larger.
            </para>
            <para>
            The arc sweep is always less than 360 degrees. The method appends a line
            to the last point if either radii are zero, or if last point is equal to <paramref name="endPoint"/>.
            In addition the method scales the radii to fit last point and <paramref name="endPoint"/> if both
            are greater than zero but too small to describe an arc.
            </para>
            </summary>
            <param name="startPoint">The start point of the arc.</param>
            <param name="radiusX">The x-radius of the ellipsis.</param>
            <param name="radiusY">The y-radius of the ellipsis.</param>
            <param name="rotation">The rotation along the X-axis; measured in degrees clockwise.</param>
            <param name="largeArc">
            The large arc flag, and is <see langword="false"/> if an arc spanning less than or equal to 180 degrees
            is chosen, or <see langword="true"/> if an arc spanning greater than 180 degrees is chosen.
            </param>
            <param name="sweep">
            The sweep flag, and is <see langword="false"/> if the line joining center to arc sweeps through decreasing
            angles, or <see langword="true"/> if it sweeps through increasing angles.
            </param>
            <param name="endPoint">The end point of the arc.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddArc(SixLabors.ImageSharp.RectangleF,System.Single,System.Single,System.Single)">
            <summary>
            Adds an elliptical arc to the current figure.
            </summary>
            <param name="rectangle">A <see cref="T:SixLabors.ImageSharp.RectangleF"/> that represents the rectangular bounds of the ellipse from which the arc is taken.</param>
            <param name="rotation">The angle, in degrees, from the x-axis of the current coordinate system to the x-axis of the ellipse.</param>
            <param name="startAngle">
            The start angle of the elliptical arc prior to the stretch and rotate operations. (0 is at the 3 o'clock position of the arc's circle).
            </param>
            <param name="sweepAngle">The angle between <paramref name="startAngle"/> and the end of the arc.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddArc(SixLabors.ImageSharp.Rectangle,System.Int32,System.Int32,System.Int32)">
            <summary>
            Adds an elliptical arc to the current figure.
            </summary>
            <param name="rectangle">A <see cref="T:SixLabors.ImageSharp.Rectangle"/> that represents the rectangular bounds of the ellipse from which the arc is taken.</param>
            <param name="rotation">The angle, in degrees, from the x-axis of the current coordinate system to the x-axis of the ellipse.</param>
            <param name="startAngle">
            The start angle of the elliptical arc prior to the stretch and rotate operations. (0 is at the 3 o'clock position of the arc's circle).
            </param>
            <param name="sweepAngle">The angle between <paramref name="startAngle"/> and the end of the arc.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddArc(SixLabors.ImageSharp.PointF,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Adds an elliptical arc to the current figure.
            </summary>
            <param name="center">The center <see cref="T:SixLabors.ImageSharp.PointF"/> of the ellipse from which the arc is taken.</param>
            <param name="radiusX">The x-radius of the ellipsis.</param>
            <param name="radiusY">The y-radius of the ellipsis.</param>
            <param name="rotation">The angle, in degrees, from the x-axis of the current coordinate system to the x-axis of the ellipse.</param>
            <param name="startAngle">
            The start angle of the elliptical arc prior to the stretch and rotate operations. (0 is at the 3 o'clock position of the arc's circle).
            </param>
            <param name="sweepAngle">The angle between <paramref name="startAngle"/> and the end of the arc.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddArc(SixLabors.ImageSharp.Point,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Adds an elliptical arc to the current figure.
            </summary>
            <param name="center">The center <see cref="T:SixLabors.ImageSharp.Point"/> of the ellipse from which the arc is taken.</param>
            <param name="radiusX">The x-radius of the ellipsis.</param>
            <param name="radiusY">The y-radius of the ellipsis.</param>
            <param name="rotation">The angle, in degrees, from the x-axis of the current coordinate system to the x-axis of the ellipse.</param>
            <param name="startAngle">
            The start angle of the elliptical arc prior to the stretch and rotate operations. (0 is at the 3 o'clock position of the arc's circle).
            </param>
            <param name="sweepAngle">The angle between <paramref name="startAngle"/> and the end of the arc.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddArc(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Adds an elliptical arc to the current figure.
            </summary>
            <param name="x">The x-coordinate of the center point of the ellipse from which the arc is taken.</param>
            <param name="y">The y-coordinate of the center point of the ellipse from which the arc is taken.</param>
            <param name="radiusX">The x-radius of the ellipsis.</param>
            <param name="radiusY">The y-radius of the ellipsis.</param>
            <param name="rotation">The angle, in degrees, from the x-axis of the current coordinate system to the x-axis of the ellipse.</param>
            <param name="startAngle">
            The start angle of the elliptical arc prior to the stretch and rotate operations. (0 is at the 3 o'clock position of the arc's circle).
            </param>
            <param name="sweepAngle">The angle between <paramref name="startAngle"/> and the end of the arc.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.AddArc(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Adds an elliptical arc to the current figure.
            </summary>
            <param name="x">The x-coordinate of the center point of the ellipse from which the arc is taken.</param>
            <param name="y">The y-coordinate of the center point of the ellipse from which the arc is taken.</param>
            <param name="radiusX">The x-radius of the ellipsis.</param>
            <param name="radiusY">The y-radius of the ellipsis.</param>
            <param name="rotation">The angle, in degrees, from the x-axis of the current coordinate system to the x-axis of the ellipse.</param>
            <param name="startAngle">
            The start angle of the elliptical arc prior to the stretch and rotate operations. (0 is at the 3 o'clock position of the arc's circle).
            </param>
            <param name="sweepAngle">The angle between <paramref name="startAngle"/> and the end of the arc.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.StartFigure">
            <summary>
            Starts a new figure but leaves the previous one open.
            </summary>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.CloseFigure">
            <summary>
            Closes the current figure.
            </summary>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.CloseAllFigures">
            <summary>
            Closes the current figure.
            </summary>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.Build">
            <summary>
            Builds a complex polygon from the current working set of working operations.
            </summary>
            <returns>The current set of operations as a complex polygon</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.Reset">
            <summary>
            Resets this instance, clearing any drawn paths and resetting any transforms.
            </summary>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.PathBuilder"/>.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathBuilder.Clear">
            <summary>
            Clears all drawn paths, Leaving any applied transforms.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.PathCollection">
            <summary>
            A aggregate of <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>s to apply common operations to them.
            </summary>
            <seealso cref="T:SixLabors.ImageSharp.Drawing.IPath" />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathCollection.#ctor(System.Collections.Generic.IEnumerable{SixLabors.ImageSharp.Drawing.IPath})">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.PathCollection"/> class.
            </summary>
            <param name="paths">The collection of paths</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathCollection.#ctor(SixLabors.ImageSharp.Drawing.IPath[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.PathCollection"/> class.
            </summary>
            <param name="paths">The collection of paths</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.PathCollection.Bounds">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathCollection.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathCollection.Transform(System.Numerics.Matrix3x2)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathCollection.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.PathExtensions">
            <summary>
            Convenience methods that can be applied to shapes and paths.
            </summary>
            <content>
            Convenience methods that can be applied to shapes and paths.
            </content>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.Rotate(SixLabors.ImageSharp.Drawing.IPathCollection,System.Single)">
            <summary>
            Creates a path rotated by the specified radians around its center.
            </summary>
            <param name="path">The path to rotate.</param>
            <param name="radians">The radians to rotate the path.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a rotate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.RotateDegree(SixLabors.ImageSharp.Drawing.IPathCollection,System.Single)">
            <summary>
            Creates a path rotated by the specified degrees around its center.
            </summary>
            <param name="shape">The path to rotate.</param>
            <param name="degree">The degree to rotate the path.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a rotate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.Translate(SixLabors.ImageSharp.Drawing.IPathCollection,SixLabors.ImageSharp.PointF)">
            <summary>
            Creates a path translated by the supplied position
            </summary>
            <param name="path">The path to translate.</param>
            <param name="position">The translation position.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a translate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.Translate(SixLabors.ImageSharp.Drawing.IPathCollection,System.Single,System.Single)">
            <summary>
            Creates a path translated by the supplied position
            </summary>
            <param name="path">The path to translate.</param>
            <param name="x">The amount to translate along the X axis.</param>
            <param name="y">The amount to translate along the Y axis.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a translate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.Scale(SixLabors.ImageSharp.Drawing.IPathCollection,System.Single,System.Single)">
            <summary>
            Creates a path translated by the supplied position
            </summary>
            <param name="path">The path to translate.</param>
            <param name="scaleX">The amount to scale along the X axis.</param>
            <param name="scaleY">The amount to scale along the Y axis.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a translate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.Scale(SixLabors.ImageSharp.Drawing.IPathCollection,System.Single)">
            <summary>
            Creates a path translated by the supplied position
            </summary>
            <param name="path">The path to translate.</param>
            <param name="scale">The amount to scale along both the x and y axis.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a translate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.Rotate(SixLabors.ImageSharp.Drawing.IPath,System.Single)">
            <summary>
            Creates a path rotated by the specified radians around its center.
            </summary>
            <param name="path">The path to rotate.</param>
            <param name="radians">The radians to rotate the path.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a rotate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.RotateDegree(SixLabors.ImageSharp.Drawing.IPath,System.Single)">
            <summary>
            Creates a path rotated by the specified degrees around its center.
            </summary>
            <param name="shape">The path to rotate.</param>
            <param name="degree">The degree to rotate the path.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a rotate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.Translate(SixLabors.ImageSharp.Drawing.IPath,SixLabors.ImageSharp.PointF)">
            <summary>
            Creates a path translated by the supplied position
            </summary>
            <param name="path">The path to translate.</param>
            <param name="position">The translation position.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a translate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.Translate(SixLabors.ImageSharp.Drawing.IPath,System.Single,System.Single)">
            <summary>
            Creates a path translated by the supplied position
            </summary>
            <param name="path">The path to translate.</param>
            <param name="x">The amount to translate along the X axis.</param>
            <param name="y">The amount to translate along the Y axis.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a translate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.Scale(SixLabors.ImageSharp.Drawing.IPath,System.Single,System.Single)">
            <summary>
            Creates a path translated by the supplied position
            </summary>
            <param name="path">The path to translate.</param>
            <param name="scaleX">The amount to scale along the X axis.</param>
            <param name="scaleY">The amount to scale along the Y axis.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a translate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.Scale(SixLabors.ImageSharp.Drawing.IPath,System.Single)">
            <summary>
            Creates a path translated by the supplied position
            </summary>
            <param name="path">The path to translate.</param>
            <param name="scale">The amount to scale along both the x and y axis.</param>
            <returns>A <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/> with a translate transform applied.</returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.ComputeLength(SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Calculates the approximate length of the path as though each segment were unrolled into a line.
            </summary>
            <param name="path">The path to compute the length for.</param>
            <returns>
            The <see cref="T:System.Single"/> representing the unrolled length.
            For closed paths, the length includes an implicit closing segment.
            </returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.PathExtensions.Reverse(SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Create a path with the segment order reversed.
            </summary>
            <param name="path">The path to reverse.</param>
            <returns>The reversed <see cref="T:SixLabors.ImageSharp.Drawing.IPath"/>.</returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.PathTypes">
            <summary>
            Describes the different type of paths.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.PathTypes.Open">
            <summary>
            Denotes a path containing a single simple open path
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.PathTypes.Closed">
            <summary>
            Denotes a path describing a single simple closed shape
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.PathTypes.Mixed">
            <summary>
            Denotes a path containing one or more child paths that could be open or closed.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.PointOrientation">
            <summary>
            Represents the orientation of a point from a line.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.PointOrientation.Collinear">
            <summary>
            The point is collinear.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.PointOrientation.Clockwise">
            <summary>
            The point is clockwise.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.PointOrientation.Counterclockwise">
            <summary>
            The point is counter-clockwise.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Polygon">
            <summary>
            A shape made up of a single closed path made up of one of more <see cref="T:SixLabors.ImageSharp.Drawing.ILineSegment"/>s
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Polygon.#ctor(SixLabors.ImageSharp.PointF[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Polygon"/> class.
            </summary>
            <param name="points">The collection of points; processed as a series of linear line segments.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Polygon.#ctor(SixLabors.ImageSharp.Drawing.ILineSegment[])">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Polygon"/> class.
            </summary>
            <param name="segments">The segments.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Polygon.#ctor(System.Collections.Generic.IEnumerable{SixLabors.ImageSharp.Drawing.ILineSegment})">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Polygon"/> class.
            </summary>
            <param name="segments">The segments.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Polygon.#ctor(SixLabors.ImageSharp.Drawing.ILineSegment)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Polygon" /> class.
            </summary>
            <param name="segment">The segment.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Polygon.#ctor(SixLabors.ImageSharp.Drawing.Path)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Polygon"/> class.
            </summary>
            <param name="path">The path.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Polygon.IsClosed">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Polygon.Transform(System.Numerics.Matrix3x2)">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.RectangularPolygon">
            <summary>
            A polygon tha allows the optimized drawing of rectangles.
            </summary>
            <seealso cref="T:SixLabors.ImageSharp.Drawing.IPath" />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RectangularPolygon.#ctor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.RectangularPolygon" /> class.
            </summary>
            <param name="x">The horizontal position of the rectangle.</param>
            <param name="y">The vertical position of the rectangle.</param>
            <param name="width">The width of the rectangle.</param>
            <param name="height">The height of the rectangle.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RectangularPolygon.#ctor(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.PointF)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.RectangularPolygon" /> class.
            </summary>
            <param name="topLeft">
            The <see cref="T:SixLabors.ImageSharp.PointF"/> which specifies the rectangles top/left point in a two-dimensional plane.
            </param>
            <param name="bottomRight">
            The <see cref="T:SixLabors.ImageSharp.PointF"/> which specifies the rectangles bottom/right point in a two-dimensional plane.
            </param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RectangularPolygon.#ctor(SixLabors.ImageSharp.PointF,SixLabors.ImageSharp.SizeF)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.RectangularPolygon"/> class.
            </summary>
            <param name="point">
            The <see cref="T:SixLabors.ImageSharp.PointF"/> which specifies the rectangles point in a two-dimensional plane.
            </param>
            <param name="size">
            The <see cref="T:SixLabors.ImageSharp.SizeF"/> which specifies the rectangles height and width.
            </param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RectangularPolygon.#ctor(SixLabors.ImageSharp.RectangleF)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.RectangularPolygon"/> class.
            </summary>
            <param name="rectangle">The rectangle.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Location">
            <summary>
            Gets the location.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Left">
            <summary>
            Gets the x-coordinate of the left edge.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.X">
            <summary>
            Gets the x-coordinate.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Right">
            <summary>
            Gets the x-coordinate of the right edge.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Top">
            <summary>
            Gets the y-coordinate of the top edge.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Y">
            <summary>
            Gets the y-coordinate.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Bottom">
            <summary>
            Gets the y-coordinate of the bottom edge.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Bounds">
            <inheritdoc/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.IsClosed">
            <inheritdoc/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Points">
            <inheritdoc/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Size">
            <summary>
            Gets the size.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Width">
            <summary>
            Gets the width.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Height">
            <summary>
            Gets the height.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.PathType">
            <inheritdoc/>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.RectangularPolygon.Center">
            <summary>
            Gets the center point.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RectangularPolygon.op_Explicit(SixLabors.ImageSharp.Drawing.Polygon)~SixLabors.ImageSharp.Drawing.RectangularPolygon">
            <summary>
            Converts the polygon to a rectangular polygon from its bounds.
            </summary>
            <param name="polygon">The polygon to convert.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RectangularPolygon.Transform(System.Numerics.Matrix3x2)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RectangularPolygon.SixLabors#ImageSharp#Drawing#IPathInternals#PointAlongPath(System.Single)">
            <inheritdoc />
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RectangularPolygon.Flatten">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RectangularPolygon.AsClosedPath">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.RegularPolygon">
            <summary>
            A shape made up of a single path made up of one of more <see cref="T:SixLabors.ImageSharp.Drawing.ILineSegment"/>s
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RegularPolygon.#ctor(SixLabors.ImageSharp.PointF,System.Int32,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.RegularPolygon" /> class.
            </summary>
            <param name="location">The location the center of the polygon will be placed.</param>
            <param name="vertices">The number of vertices the <see cref="T:SixLabors.ImageSharp.Drawing.RegularPolygon"/> should have.</param>
            <param name="radius">The radius of the circle that would touch all vertices.</param>
            <param name="angle">The angle of rotation in Radians</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RegularPolygon.#ctor(SixLabors.ImageSharp.PointF,System.Int32,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.RegularPolygon" /> class.
            </summary>
            <param name="location">The location the center of the polygon will be placed.</param>
            <param name="vertices">The number of vertices the <see cref="T:SixLabors.ImageSharp.Drawing.RegularPolygon"/> should have.</param>
            <param name="radius">The radius of the circle that would touch all vertices.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RegularPolygon.#ctor(System.Single,System.Single,System.Int32,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.RegularPolygon" /> class.
            </summary>
            <param name="x">The x-coordinate of the center of the polygon.</param>
            <param name="y">The y-coordinate of the center of the polygon.</param>
            <param name="vertices">The number of vertices the <see cref="T:SixLabors.ImageSharp.Drawing.RegularPolygon" /> should have.</param>
            <param name="radius">The radius of the circle that would touch all vertices.</param>
            <param name="angle">The angle of rotation in Radians</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.RegularPolygon.#ctor(System.Single,System.Single,System.Int32,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.RegularPolygon" /> class.
            </summary>
            <param name="x">The x-coordinate of the center of the polygon.</param>
            <param name="y">The y-coordinate of the center of the polygon.</param>
            <param name="vertices">The number of vertices the <see cref="T:SixLabors.ImageSharp.Drawing.RegularPolygon"/> should have.</param>
            <param name="radius">The radius of the circle that would touch all vertices.</param>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.SegmentInfo">
            <summary>
            Returns metadata about the point along a path.
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.SegmentInfo.Point">
            <summary>
            The point on the path
            </summary>
        </member>
        <member name="F:SixLabors.ImageSharp.Drawing.SegmentInfo.Angle">
            <summary>
            The angle of the segment. Measured in radians.
            </summary>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Star">
            <summary>
            A shape made up of a single closed path made up of one of more <see cref="T:SixLabors.ImageSharp.Drawing.ILineSegment"/>s
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Star.#ctor(SixLabors.ImageSharp.PointF,System.Int32,System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Star" /> class.
            </summary>
            <param name="location">The location the center of the polygon will be placed.</param>
            <param name="prongs">The number of points the <see cref="T:SixLabors.ImageSharp.Drawing.Star" /> should have.</param>
            <param name="innerRadii">The inner radii.</param>
            <param name="outerRadii">The outer radii.</param>
            <param name="angle">The angle of rotation in Radians</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Star.#ctor(SixLabors.ImageSharp.PointF,System.Int32,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Star" /> class.
            </summary>
            <param name="location">The location the center of the polygon will be placed.</param>
            <param name="prongs">The number of vertices the <see cref="T:SixLabors.ImageSharp.Drawing.Star" /> should have.</param>
            <param name="innerRadii">The inner radii.</param>
            <param name="outerRadii">The outer radii.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Star.#ctor(System.Single,System.Single,System.Int32,System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Star" /> class.
            </summary>
            <param name="x">The x-coordinate of the center of the polygon.</param>
            <param name="y">The y-coordinate of the center of the polygon.</param>
            <param name="prongs">The number of vertices the <see cref="T:SixLabors.ImageSharp.Drawing.RegularPolygon" /> should have.</param>
            <param name="innerRadii">The inner radii.</param>
            <param name="outerRadii">The outer radii.</param>
            <param name="angle">The angle of rotation in Radians</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Star.#ctor(System.Single,System.Single,System.Int32,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Star" /> class.
            </summary>
            <param name="x">The x-coordinate of the center of the polygon.</param>
            <param name="y">The y-coordinate of the center of the polygon.</param>
            <param name="prongs">The number of vertices the <see cref="T:SixLabors.ImageSharp.Drawing.RegularPolygon" /> should have.</param>
            <param name="innerRadii">The inner radii.</param>
            <param name="outerRadii">The outer radii.</param>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder">
            <summary>
            Defines a rendering surface that Fonts can use to generate Shapes.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder"/> class.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.#ctor(System.Numerics.Matrix3x2)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder"/> class.
            </summary>
            <param name="transform">The default transform.</param>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.Paths">
            <summary>
            Gets the paths that have been rendered by the current instance.
            </summary>
        </member>
        <member name="P:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.Builder">
            <summary>
            Gets the path builder for the current instance.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.SixLabors#Fonts#IGlyphRenderer#EndText">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.SixLabors#Fonts#IGlyphRenderer#BeginText(SixLabors.Fonts.FontRectangle@)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.SixLabors#Fonts#IGlyphRenderer#BeginGlyph(SixLabors.Fonts.FontRectangle@,SixLabors.Fonts.GlyphRendererParameters@)">
            <inheritdoc/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.SixLabors#Fonts#IGlyphRenderer#BeginFigure">
            <summary>
            Begins the figure.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.SixLabors#Fonts#IGlyphRenderer#CubicBezierTo(System.Numerics.Vector2,System.Numerics.Vector2,System.Numerics.Vector2)">
            <summary>
            Draws a cubic bezier from the current point  to the <paramref name="point"/>
            </summary>
            <param name="secondControlPoint">The second control point.</param>
            <param name="thirdControlPoint">The third control point.</param>
            <param name="point">The point.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.SixLabors#Fonts#IGlyphRenderer#EndGlyph">
            <summary>
            Ends the glyph.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.SixLabors#Fonts#IGlyphRenderer#EndFigure">
            <summary>
            Ends the figure.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.SixLabors#Fonts#IGlyphRenderer#LineTo(System.Numerics.Vector2)">
            <summary>
            Draws a line from the current point  to the <paramref name="point"/>.
            </summary>
            <param name="point">The point.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.SixLabors#Fonts#IGlyphRenderer#MoveTo(System.Numerics.Vector2)">
            <summary>
            Moves to current point to the supplied vector.
            </summary>
            <param name="point">The point.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.SixLabors#Fonts#IGlyphRenderer#QuadraticBezierTo(System.Numerics.Vector2,System.Numerics.Vector2)">
            <summary>
            Draws a quadratics bezier from the current point  to the <paramref name="point"/>
            </summary>
            <param name="secondControlPoint">The second control point.</param>
            <param name="point">The point.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.BeginText(SixLabors.Fonts.FontRectangle@)">
            <summary>Called before any glyphs have been rendered.</summary>
            <param name="bounds">The bounds the text will be rendered at and at what size.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.BeginGlyph(SixLabors.Fonts.FontRectangle@,SixLabors.Fonts.GlyphRendererParameters@)">
            <inheritdoc cref="M:SixLabors.Fonts.IGlyphRenderer.BeginGlyph(SixLabors.Fonts.FontRectangle@,SixLabors.Fonts.GlyphRendererParameters@)"/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.EndGlyph">
            <inheritdoc cref="M:SixLabors.Fonts.IGlyphRenderer.EndGlyph"/>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.BaseGlyphBuilder.EndText">
            <inheritdoc cref="M:SixLabors.Fonts.IGlyphRenderer.EndText"/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Text.GlyphBuilder">
            <summary>
            rendering surface that Fonts can use to generate Shapes.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.GlyphBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Text.GlyphBuilder"/> class.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.GlyphBuilder.#ctor(System.Numerics.Vector2)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Text.GlyphBuilder"/> class.
            </summary>
            <param name="origin">The origin.</param>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Text.PathGlyphBuilder">
            <summary>
            A rendering surface that Fonts can use to generate shapes by following a path.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.PathGlyphBuilder.#ctor(SixLabors.ImageSharp.Drawing.IPath)">
            <summary>
            Initializes a new instance of the <see cref="T:SixLabors.ImageSharp.Drawing.Text.PathGlyphBuilder"/> class.
            </summary>
            <param name="path">The path to render the glyphs along.</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Text.PathGlyphBuilder.BeginGlyph(SixLabors.Fonts.FontRectangle@,SixLabors.Fonts.GlyphRendererParameters@)">
            <inheritdoc/>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.TextBuilder">
            <summary>
            Provides mechanisms for building <see cref="T:SixLabors.ImageSharp.Drawing.IPathCollection"/> instances from text strings.
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.TextBuilder.GenerateGlyphs(System.String,SixLabors.Fonts.TextOptions)">
            <summary>
            Generates the shapes corresponding the glyphs described by the text options.
            </summary>
            <param name="text">The text to generate glyphs for.</param>
            <param name="textOptions">The text rendering options.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.IPathCollection"/></returns>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.TextBuilder.GenerateGlyphs(System.String,SixLabors.ImageSharp.Drawing.IPath,SixLabors.Fonts.TextOptions)">
            <summary>
            Generates the shapes corresponding the glyphs described by the text options along the described path.
            </summary>
            <param name="text">The text to generate glyphs for</param>
            <param name="path">The path to draw the text in relation to</param>
            <param name="textOptions">The text rendering options.</param>
            <returns>The <see cref="T:SixLabors.ImageSharp.Drawing.IPathCollection"/></returns>
        </member>
        <member name="T:SixLabors.ImageSharp.Drawing.Utilities.SortUtility">
            <summary>
            Optimized quick sort implementation for Span{float} input
            </summary>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Utilities.SortUtility.Sort(System.Span{System.Single})">
            <summary>
            Sorts the elements of <paramref name="data"/> in ascending order
            </summary>
            <param name="data">The items to sort</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Utilities.SortUtility.Sort``1(System.Span{System.Single},System.Span{``0})">
            <summary>
            Sorts the elements of <paramref name="values"/> in ascending order
            </summary>
            <param name="keys">The items to sort on</param>
            <param name="values">The items to sort</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Utilities.SortUtility.Sort``2(System.Span{System.Single},System.Span{``0},System.Span{``1})">
            <summary>
            Sorts the elements of <paramref name="keys"/> in ascending order, and swapping items in <paramref name="values1"/> and <paramref name="values2"/> in sequance with them.
            </summary>
            <param name="keys">The items to sort on</param>
            <param name="values1">The set of items to sort</param>
            <param name="values2">The 2nd set of items to sort</param>
        </member>
        <member name="M:SixLabors.ImageSharp.Drawing.Utilities.ThreadLocalBlenderBuffers`1.Dispose">
            <inheritdoc />
        </member>
        <member name="T:SixLabors.DebugGuard">
            <summary>
            Provides methods to protect against invalid parameters for a DEBUG build.
            </summary>
        </member>
        <member name="M:SixLabors.DebugGuard.NotNull``1(``0,System.String)">
            <summary>
            Ensures that the value is not null.
            </summary>
            <param name="value">The target object, which cannot be null.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:SixLabors.DebugGuard.NotNullOrWhiteSpace(System.String,System.String)">
            <summary>
            Ensures that the target value is not null, empty, or whitespace.
            </summary>
            <param name="value">The target string, which should be checked against being null or empty.</param>
            <param name="parameterName">Name of the parameter.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> is empty or contains only blanks.</exception>
        </member>
        <member name="M:SixLabors.DebugGuard.MustBeLessThan``1(``0,``0,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.DebugGuard.MustBeLessThanOrEqualTo``1(``0,``0,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.DebugGuard.MustBeGreaterThan``1(``0,``0,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.DebugGuard.MustBeGreaterThanOrEqualTo``1(``0,``0,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.DebugGuard.MustBeBetweenOrEqualTo``1(``0,``0,``0,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.DebugGuard.IsTrue(System.Boolean,System.String,System.String)">
            <summary>
            Verifies, that the method parameter with specified target value is true
            and throws an exception if it is found to be so.
            </summary>
            <param name="target">The target value, which cannot be false.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <param name="message">The error message, if any to add to the exception.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="target"/> is false.
            </exception>
        </member>
        <member name="M:SixLabors.DebugGuard.IsFalse(System.Boolean,System.String,System.String)">
            <summary>
            Verifies, that the method parameter with specified target value is false
            and throws an exception if it is found to be so.
            </summary>
            <param name="target">The target value, which cannot be true.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <param name="message">The error message, if any to add to the exception.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="target"/> is true.
            </exception>
        </member>
        <member name="M:SixLabors.DebugGuard.MustBeSizedAtLeast``1(System.ReadOnlySpan{``0},System.Int32,System.String)">
            <summary>
            Verifies, that the `source` span has the length of 'minLength', or longer.
            </summary>
            <typeparam name="T">The element type of the spans.</typeparam>
            <param name="source">The source span.</param>
            <param name="minLength">The minimum length.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="source"/> has less than <paramref name="minLength"/> items.
            </exception>
        </member>
        <member name="M:SixLabors.DebugGuard.MustBeSizedAtLeast``1(System.Span{``0},System.Int32,System.String)">
            <summary>
            Verifies, that the `source` span has the length of 'minLength', or longer.
            </summary>
            <typeparam name="T">The element type of the spans.</typeparam>
            <param name="source">The target span.</param>
            <param name="minLength">The minimum length.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="source"/> has less than <paramref name="minLength"/> items.
            </exception>
        </member>
        <member name="M:SixLabors.DebugGuard.DestinationShouldNotBeTooShort``2(System.ReadOnlySpan{``0},System.Span{``1},System.String)">
            <summary>
            Verifies that the 'destination' span is not shorter than 'source'.
            </summary>
            <typeparam name="TSource">The source element type.</typeparam>
            <typeparam name="TDest">The destination element type.</typeparam>
            <param name="source">The source span.</param>
            <param name="destination">The destination span.</param>
            <param name="destinationParamName">The name of the argument for 'destination'.</param>
        </member>
        <member name="M:SixLabors.DebugGuard.DestinationShouldNotBeTooShort``2(System.Span{``0},System.Span{``1},System.String)">
            <summary>
            Verifies that the 'destination' span is not shorter than 'source'.
            </summary>
            <typeparam name="TSource">The source element type.</typeparam>
            <typeparam name="TDest">The destination element type.</typeparam>
            <param name="source">The source span.</param>
            <param name="destination">The destination span.</param>
            <param name="destinationParamName">The name of the argument for 'destination'.</param>
        </member>
        <member name="T:SixLabors.Guard">
            <summary>
            Provides methods to protect against invalid parameters.
            </summary>
            <summary>
            Provides methods to protect against invalid parameters.
            </summary>
        </member>
        <member name="M:SixLabors.Guard.NotNull``1(``0,System.String)">
            <summary>
            Ensures that the value is not null.
            </summary>
            <param name="value">The target object, which cannot be null.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:SixLabors.Guard.NotNullOrWhiteSpace(System.String,System.String)">
            <summary>
            Ensures that the target value is not null, empty, or whitespace.
            </summary>
            <param name="value">The target string, which should be checked against being null or empty.</param>
            <param name="parameterName">Name of the parameter.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> is empty or contains only blanks.</exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan``1(``0,``0,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo``1(``0,``0,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan``1(``0,``0,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo``1(``0,``0,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo``1(``0,``0,``0,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <typeparam name="TValue">The type of the value.</typeparam>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.IsTrue(System.Boolean,System.String,System.String)">
            <summary>
            Verifies, that the method parameter with specified target value is true
            and throws an exception if it is found to be so.
            </summary>
            <param name="target">The target value, which cannot be false.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <param name="message">The error message, if any to add to the exception.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="target"/> is false.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.IsFalse(System.Boolean,System.String,System.String)">
            <summary>
            Verifies, that the method parameter with specified target value is false
            and throws an exception if it is found to be so.
            </summary>
            <param name="target">The target value, which cannot be true.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <param name="message">The error message, if any to add to the exception.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="target"/> is true.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeSizedAtLeast``1(System.ReadOnlySpan{``0},System.Int32,System.String)">
            <summary>
            Verifies, that the `source` span has the length of 'minLength', or longer.
            </summary>
            <typeparam name="T">The element type of the spans.</typeparam>
            <param name="source">The source span.</param>
            <param name="minLength">The minimum length.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="source"/> has less than <paramref name="minLength"/> items.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeSizedAtLeast``1(System.Span{``0},System.Int32,System.String)">
            <summary>
            Verifies, that the `source` span has the length of 'minLength', or longer.
            </summary>
            <typeparam name="T">The element type of the spans.</typeparam>
            <param name="source">The target span.</param>
            <param name="minLength">The minimum length.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="source"/> has less than <paramref name="minLength"/> items.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.DestinationShouldNotBeTooShort``2(System.ReadOnlySpan{``0},System.Span{``1},System.String)">
            <summary>
            Verifies that the 'destination' span is not shorter than 'source'.
            </summary>
            <typeparam name="TSource">The source element type.</typeparam>
            <typeparam name="TDest">The destination element type.</typeparam>
            <param name="source">The source span.</param>
            <param name="destination">The destination span.</param>
            <param name="destinationParamName">The name of the argument for 'destination'.</param>
        </member>
        <member name="M:SixLabors.Guard.DestinationShouldNotBeTooShort``2(System.Span{``0},System.Span{``1},System.String)">
            <summary>
            Verifies that the 'destination' span is not shorter than 'source'.
            </summary>
            <typeparam name="TSource">The source element type.</typeparam>
            <typeparam name="TDest">The destination element type.</typeparam>
            <param name="source">The source span.</param>
            <param name="destination">The destination span.</param>
            <param name="destinationParamName">The name of the argument for 'destination'.</param>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.Byte,System.Byte,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.Byte,System.Byte,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.Byte,System.Byte,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.Byte,System.Byte,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.Byte,System.Byte,System.Byte,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.SByte,System.SByte,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.SByte,System.SByte,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.SByte,System.SByte,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.SByte,System.SByte,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.SByte,System.SByte,System.SByte,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.Int16,System.Int16,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.Int16,System.Int16,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.Int16,System.Int16,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.Int16,System.Int16,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.Int16,System.Int16,System.Int16,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.UInt16,System.UInt16,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.UInt16,System.UInt16,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.UInt16,System.UInt16,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.UInt16,System.UInt16,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.UInt16,System.UInt16,System.UInt16,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.Char,System.Char,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.Char,System.Char,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.Char,System.Char,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.Char,System.Char,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.Char,System.Char,System.Char,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.Int32,System.Int32,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.Int32,System.Int32,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.Int32,System.Int32,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.Int32,System.Int32,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.UInt32,System.UInt32,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.UInt32,System.UInt32,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.UInt32,System.UInt32,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.UInt32,System.UInt32,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.UInt32,System.UInt32,System.UInt32,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.Single,System.Single,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.Single,System.Single,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.Single,System.Single,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.Single,System.Single,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.Int64,System.Int64,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.Int64,System.Int64,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.Int64,System.Int64,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.Int64,System.Int64,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.Int64,System.Int64,System.Int64,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.UInt64,System.UInt64,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.UInt64,System.UInt64,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.UInt64,System.UInt64,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.UInt64,System.UInt64,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.UInt64,System.UInt64,System.UInt64,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.Double,System.Double,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.Double,System.Double,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.Double,System.Double,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.Double,System.Double,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.Double,System.Double,System.Double,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThan(System.Decimal,System.Decimal,System.String)">
            <summary>
            Ensures that the specified value is less than a maximum value.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeLessThanOrEqualTo(System.Decimal,System.Decimal,System.String)">
            <summary>
            Verifies that the specified value is less than or equal to a maximum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is greater than the maximum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThan(System.Decimal,System.Decimal,System.String)">
            <summary>
            Verifies that the specified value is greater than a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo(System.Decimal,System.Decimal,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value
            and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value.
            </exception>
        </member>
        <member name="M:SixLabors.Guard.MustBeBetweenOrEqualTo(System.Decimal,System.Decimal,System.Decimal,System.String)">
            <summary>
            Verifies that the specified value is greater than or equal to a minimum value and less than
            or equal to a maximum value and throws an exception if it is not.
            </summary>
            <param name="value">The target value, which should be validated.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="parameterName">The name of the parameter that is to be checked.</param>
            <exception cref="T:System.ArgumentException">
            <paramref name="value"/> is less than the minimum value of greater than the maximum value.
            </exception>
        </member>
        <member name="T:SixLabors.ThrowHelper">
            <summary>
            Helper methods to throw exceptions
            </summary>
        </member>
        <member name="M:SixLabors.ThrowHelper.ThrowArgumentNullExceptionForNotNull(System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentNullException"/> when <see cref="M:SixLabors.Guard.NotNull``1(``0,System.String)"/> fails.
            </summary>
        </member>
        <member name="M:SixLabors.ThrowHelper.ThrowArgumentExceptionForNotNullOrWhitespace(System.String,System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentException"/> when <see cref="M:SixLabors.Guard.NotNullOrWhiteSpace(System.String,System.String)"/> fails.
            </summary>
        </member>
        <member name="M:SixLabors.ThrowHelper.ThrowArgumentOutOfRangeExceptionForMustBeLessThan``1(``0,``0,System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentOutOfRangeException"/> when <see cref="M:SixLabors.Guard.MustBeLessThan``1(``0,``0,System.String)"/> fails.
            </summary>
        </member>
        <member name="M:SixLabors.ThrowHelper.ThrowArgumentOutOfRangeExceptionForMustBeLessThanOrEqualTo``1(``0,``0,System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentOutOfRangeException"/> when <see cref="M:SixLabors.Guard.MustBeLessThanOrEqualTo``1(``0,``0,System.String)"/> fails.
            </summary>
        </member>
        <member name="M:SixLabors.ThrowHelper.ThrowArgumentOutOfRangeExceptionForMustBeGreaterThan``1(``0,``0,System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentOutOfRangeException"/> when <see cref="M:SixLabors.Guard.MustBeGreaterThan``1(``0,``0,System.String)"/> fails.
            </summary>
        </member>
        <member name="M:SixLabors.ThrowHelper.ThrowArgumentOutOfRangeExceptionForMustBeGreaterThanOrEqualTo``1(``0,``0,System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentOutOfRangeException"/> when <see cref="M:SixLabors.Guard.MustBeGreaterThanOrEqualTo``1(``0,``0,System.String)"/> fails.
            </summary>
        </member>
        <member name="M:SixLabors.ThrowHelper.ThrowArgumentOutOfRangeExceptionForMustBeBetweenOrEqualTo``1(``0,``0,``0,System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentOutOfRangeException"/> when <see cref="M:SixLabors.Guard.MustBeBetweenOrEqualTo``1(``0,``0,``0,System.String)"/> fails.
            </summary>
        </member>
        <member name="M:SixLabors.ThrowHelper.ThrowArgumentOutOfRangeExceptionForMustBeSizedAtLeast(System.Int32,System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentException"/> when <see cref="M:SixLabors.Guard.MustBeSizedAtLeast``1(System.ReadOnlySpan{``0},System.Int32,System.String)"/> fails.
            </summary>
        </member>
        <member name="M:SixLabors.ThrowHelper.ThrowArgumentException(System.String,System.String)">
            <summary>
            Throws a new <see cref="T:System.ArgumentException"/>.
            </summary>
            <param name="message">The message to include in the exception.</param>
            <param name="name">The argument name.</param>
            <exception cref="T:System.ArgumentException">Thrown with <paramref name="message"/> and <paramref name="name"/>.</exception>
        </member>
        <member name="M:SixLabors.ThrowHelper.ThrowArgumentNullException(System.String,System.String)">
            <summary>
            Throws a new <see cref="T:System.ArgumentNullException"/>.
            </summary>
            <param name="name">The argument name.</param>
            <param name="message">The message to include in the exception.</param>
            <exception cref="T:System.ArgumentNullException">Thrown with <paramref name="name"/> and <paramref name="message"/>.</exception>
        </member>
        <member name="M:SixLabors.ThrowHelper.ThrowArgumentOutOfRangeException(System.String,System.String)">
            <summary>
            Throws a new <see cref="T:System.ArgumentOutOfRangeException"/>.
            </summary>
            <param name="name">The argument name.</param>
            <param name="message">The message to include in the exception.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown with <paramref name="name"/> and <paramref name="message"/>.</exception>
        </member>
    </members>
</doc>
