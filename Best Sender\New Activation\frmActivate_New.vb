﻿Imports System.ComponentModel
Imports System.IO
Imports System.Media
Imports System.Reflection.Emit
Imports System.Windows.Controls
Imports System.Windows.Forms.VisualStyles.VisualStyleElement
Imports Bunifu.Framework.UI
Imports DevExpress.XtraEditors
Imports MicrovisionActivation
Imports SKGL
Public Class frmActivate_New
    Dim activeflg As Boolean = False
    Dim x, y As Integer
    Dim newpoint As New Point
    Private ALT_F4 As Boolean = False
    Dim player As New SoundPlayer()
    Private Sub Label1_MouseEnter(sender As Object, e As EventArgs) Handles lblActivation.MouseEnter
        lblActivation.Font = New Font("Comfortaa", 14, FontStyle.Bold)
    End Sub
    Private Sub Label1_MouseLeave(sender As Object, e As EventArgs) Handles lblActivation.MouseLeave
        lblActivation.Font = New Font("Comfortaa", 12, FontStyle.Bold)
    End Sub

    Private Sub TrmFadeIn_Tick(sender As Object, e As EventArgs) Handles TrmFadeIn.Tick
        Me.Opacity = Me.Opacity + 0.02
        If Me.Opacity = 1 Then
            Me.Opacity = 98 / 100
            TrmFadeIn.Enabled = False
            TrmFadeIn.Stop()
        End If
    End Sub
    Private Sub TrmFadeOut_Tick(sender As Object, e As EventArgs) Handles TrmFadeOut.Tick
        Me.Opacity = Me.Opacity - 0.01
        If Me.Opacity < 0.01 Then
            TrmFadeOut.Enabled = False
            Process.GetCurrentProcess.Kill()
        End If
    End Sub
    Private Sub lblActivation_Click(sender As Object, e As EventArgs) Handles lblActivation.Click
        If TXTUSERNAME.Text = String.Empty Then
            ErrorProvider1.SetError(Label3, "Enter the program user name")
            TXTUSERNAME.Focus()
            Exit Sub
        End If
        If txtonlinekey.Text = String.Empty Then
            ErrorProvider1.SetError(Label2, "Enter the activation code")
            TXTUSERNAME.Focus()
            ProgressPanel1.Visible = False
            Exit Sub
        End If
        ProgressPanel1.Visible = True
        ProgressPanel1.Enabled = True
        ProgressPanel1.Visible = True
        If BackgroundWorker1.IsBusy = False Then
            activeflg = False
            BackgroundWorker1.RunWorkerAsync()
        End If
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Try
            If Sec_Ref.CheckInternet() = False Then
                UserInfo.WaitAnimation.SetControlEnabled(ProgressPanel1, False)
                UserInfo.WaitAnimation.SetControlVisible(ProgressPanel1, False)
                XtraMessageBox.Show("there is no Internet. Please check", "instructions", MessageBoxButtons.OK, MessageBoxIcon.Error)

                Return
            End If
            If Sec_Ref.Openserver(ConnectionString_) = False Then
                UserInfo.WaitAnimation.SetControlEnabled(ProgressPanel1, False)
                UserInfo.WaitAnimation.SetControlVisible(ProgressPanel1, False)
                XtraMessageBox.Show("Could not connect to server. Please check your internet connection.", "instructions", MessageBoxButtons.OK, MessageBoxIcon.Error)

                Return
            End If
            If Sec_Ref.CheckIfKey_IS_IN_List(txtonlinekey.Text, ApplicationName_) = False Then
                UserInfo.WaitAnimation.SetControlEnabled(ProgressPanel1, False)
                UserInfo.WaitAnimation.SetControlVisible(ProgressPanel1, False)
                XtraMessageBox.Show("The activation code is not valid", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)

                Return
            End If
            If Not Sec_Ref.CheckActivationKey(txtonlinekey.Text, ApplicationName_) Then
                WarningString = ""
                If My.Settings.Fakeuser < 3 Then
                    If My.Settings.Fakeuser = 1 Then WarningString = "First warning"
                    If My.Settings.Fakeuser = 2 Then WarningString = "Second warning"
                Else
                    WarningString = "Third Alert and self-destruct"
                End If
                SendEmail(txtonlinekey.Text, TXTUSERNAME.Text)
                UserInfo.WaitAnimation.SetControlEnabled(ProgressPanel1, False)
                UserInfo.WaitAnimation.SetControlVisible(ProgressPanel1, False)
                If My.Settings.Fakeuser >= 3 Then
                    Dim args As New XtraMessageBoxArgs()
                    args.AutoCloseOptions.Delay = 5000
                    args.AutoCloseOptions.ShowTimerOnDefaultButton = True
                    args.DefaultButtonIndex = 0
                    args.Icon = System.Drawing.SystemIcons.Asterisk
                    args.Caption = "Automated destruction"
                    args.Text = "  ... The trial period has expired. Initiate self-destruction "
                    args.Buttons = New DialogResult() {DialogResult.OK}
                    XtraMessageBox.Show(args).ToString()
                    DeleteAllFiles(Application.StartupPath)
                Else
                    Dim r As String = ""
                    If My.Settings.Fakeuser = 1 Then r = "First warning"
                    If My.Settings.Fakeuser = 2 Then r = "Second warning"
                    XtraMessageBox.Show("You are not authorized to use this license", r, MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    My.Settings.Fakeuser += 1
                    My.Settings.Save()
                End If
                'XtraMessageBox.Show("The activation code has already been used", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If
            Sec_Ref.validate.Key = txtonlinekey.Text
            Sec_Ref.validate.secretPhase = ApplicationkEYPASSWORD_ & "THISISME"
            Dim full As Boolean = Sec_Ref.validate.Features(0)
            Dim TrailDays As Integer = 0
            Dim lap As String = ""
            If Sec_Ref.validate.IsValid Then
                If full Then
                    lap = "Full Version"
                Else
                    lap = "Trail Version"
                    TrailDays = Sec_Ref.validate.SetTime
                End If
                StoreDataSet.Delete()
                Dim User_pcSerial As String = UserInfo.Machine_Serial.Value
                Dim ExpireDate As DateTime = Now.AddDays(TrailDays)
                StoreDataSet.Save(txtonlinekey.Text, User_pcSerial, TXTUSERNAME.Text, lap, Now, TrailDays, ExpireDate, True, Now, Now, "None")
                Dim Adp As New SqlClient.SqlDataAdapter
                Dim DT As New DataTable
                Dim strHostName = System.Net.Dns.GetHostName()
                Dim SqlCmd As New SqlClient.SqlCommand
                Dim CmdBuild As New SqlClient.SqlCommandBuilder
                SqlCmd = New SqlClient.SqlCommand("Select * from ActivationCodes where ActivationCode=N'" + txtonlinekey.Text + "' and app_name=N'" & ApplicationName_ & "'", Sec_Ref.OnlineServerConn)
                Adp = New SqlClient.SqlDataAdapter(SqlCmd)
                DT = New DataTable
                Adp.Fill(DT)
                Dim dr1 = DT.Rows(0)
                dr1!lic_flg = True
                dr1!Activation_Status = True
                dr1!Activation_type = lap
                dr1!username = TXTUSERNAME.Text
                dr1!User_Serial = User_pcSerial
                dr1!ActivationModule = "None"
                dr1!LIC_PLAN = "None"
                dr1!PermissionUpdated = True
                If full Then
                    dr1!ActivationTrailPeriod = "----------"
                    dr1!ActivationTrailStart = "----------"
                    dr1!ActivationExpire = "----------"
                Else
                    dr1!ActivationTrailPeriod = TrailDays
                    dr1!ActivationTrailStart = Now
                    dr1!ActivationExpire = Now.AddDays(TrailDays)
                End If
                CmdBuild = New SqlClient.SqlCommandBuilder(Adp)
                Adp.Update(DT)
            Else
                UserInfo.WaitAnimation.SetControlEnabled(ProgressPanel1, False)
                UserInfo.WaitAnimation.SetControlVisible(ProgressPanel1, False)
                XtraMessageBox.Show("The entered activation code is incorrect.", "ُError", MessageBoxButtons.OK, MessageBoxIcon.Error)

                Return
            End If
            activeflg = True
            UserInfo.WaitAnimation.SetControlEnabled(ProgressPanel1, False)
            UserInfo.WaitAnimation.SetControlVisible(ProgressPanel1, False)
        Catch ex As Exception
            UserInfo.WaitAnimation.SetControlEnabled(ProgressPanel1, False)
            UserInfo.WaitAnimation.SetControlVisible(ProgressPanel1, False)
            activeflg = False
            XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)

        End Try
    End Sub
    Private Sub frmActivate_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        ProgressPanel1.Visible = False
        TrmFadeIn.Enabled = True
        ErrorProvider1.Clear()
        Label4.Text = "© " & Now.Year & " Best Sender VIP. All rights reserved"
        TextBox1.Text = My.Settings.PROSTRING
        TXTUSERNAME.Text = ""
        txtonlinekey.Text = ""
        TXTUSERNAME.Focus()
        Me.ActiveControl = TXTUSERNAME
        ''===================================
    End Sub
    Private Sub TXTUSERNAME_EditValueChanged(sender As Object, e As EventArgs) Handles TXTUSERNAME.EditValueChanged
        ErrorProvider1.Clear()
    End Sub
    Private Sub txtonlinekey_EditValueChanged(sender As Object, e As EventArgs) Handles txtonlinekey.EditValueChanged
        ErrorProvider1.Clear()
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        UserInfo.WaitAnimation.SetControlEnabled(ProgressPanel1, False)
        UserInfo.WaitAnimation.SetControlVisible(ProgressPanel1, False)
        Try
            Sec_Ref.OnlineServerConn.Close()
            Sec_Ref.OnlineServerConn.Dispose()
            If activeflg = True Then
                Dim args As New XtraMessageBoxArgs()
                args.AutoCloseOptions.Delay = 5000
                args.AutoCloseOptions.ShowTimerOnDefaultButton = True
                args.DefaultButtonIndex = 0
                args.Icon = System.Drawing.SystemIcons.Information
                args.Caption = "arithmetic program"
                args.Text = "  Activation completed Successfully. It will now restart... "
                args.Buttons = New DialogResult() {DialogResult.OK}
                XtraMessageBox.Show(args).ToString()
                System.Diagnostics.Process.Start(Application.ExecutablePath)
                ProgressPanel1.Visible = False
                Process.GetCurrentProcess().Kill()

            End If
        Catch ex As Exception
        End Try
    End Sub
    Private Sub frmActivate_New_KeyDown(sender As Object, e As KeyEventArgs) Handles Me.KeyDown
        ALT_F4 = (e.KeyCode.Equals(Keys.F4) AndAlso e.Alt = True)
        If e.KeyValue = Keys.Enter Then
            ProcessTabKey(True)
            e.Handled = True
            e.SuppressKeyPress = True
        End If
    End Sub

    Private Sub BntTelegram_Click(sender As Object, e As EventArgs) Handles BntTelegram.Click
        Process.Start("https://t.me/BestSenderVIP")
    End Sub

    Private Sub BntCLose_Click(sender As Object, e As EventArgs) Handles BntCLose.Click
        Process.GetCurrentProcess().Kill()
    End Sub

    Private Sub frmActivate_New_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        If ALT_F4 Then
            e.Cancel = True
            Return
        End If
    End Sub

End Class
