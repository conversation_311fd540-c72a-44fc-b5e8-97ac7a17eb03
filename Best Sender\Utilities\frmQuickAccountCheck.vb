﻿Imports System.ComponentModel
Imports System.IO
Imports System.Net.Mail
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports System.Net.Sockets
Imports System.Net.Security
Imports MailKit.Net.Imap
Imports MailKit.Security
Imports DevExpress.XtraLayout
Imports System.Drawing
Imports System.Drawing.Drawing2D

Public Class frmQuickAccountCheck
    ' تعريف MainPanel كمتغير عام في الفئة
    Private MainPanel As Panel
    Dim SmtpChecked As Boolean = False
    Private Sub CheckEdit1_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEdit1.CheckedChanged
        If CheckEdit1.Checked = True Then
            TxtPassword.Properties.PasswordChar = ""
        ElseIf CheckEdit1.Checked = False Then
            TxtPassword.Properties.PasswordChar = "•"
        End If
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Try
            ' الحصول على معلومات الاتصال
            Dim smtpServer As String = TxtSMTPServer.Text.Trim
            Dim port As Integer = Integer.Parse(TxtPort.Text.Trim)
            Dim email As String = TxtEmail.Text.Trim
            Dim password As String = TxtPassword.Text.Trim
            Dim enableSsl As Boolean = CheckSSL.Checked

            ' تحديد ما إذا كان يجب استخدام SSL بناءً على المنفذ إذا لم يتم تحديده يدويًا
            If port = 465 Then
                ' المنفذ 465 يستخدم SSL دائمًا
                enableSsl = True
            ElseIf port = 587 Then
                ' المنفذ 587 يستخدم STARTTLS (يمكن أن يكون SSL أو لا)
                ' نستخدم الإعداد الذي اختاره المستخدم
            ElseIf port = 25 Then
                ' المنفذ 25 عادة لا يستخدم SSL، ولكن بعض المزودين قد يستخدمونه
                ' نستخدم الإعداد الذي اختاره المستخدم
            ElseIf port = 2525 Then
                ' المنفذ 2525 مشابه للمنفذ 587
                ' نستخدم الإعداد الذي اختاره المستخدم
            End If

            ' تحديث حالة CheckSSL بناءً على المنفذ
            If CheckSSL.Checked <> enableSsl Then
                CheckSSL.Checked = enableSsl
            End If

            ' إنشاء عميل SMTP للتحقق من الاتصال
            Dim smtp As New SmtpClient()
            smtp.Host = smtpServer
            smtp.Port = port
            smtp.DeliveryMethod = SmtpDeliveryMethod.Network
            smtp.UseDefaultCredentials = False
            smtp.Credentials = New System.Net.NetworkCredential(email, password)
            smtp.EnableSsl = enableSsl

            ' تعيين مهلة الاتصال إلى 15 ثوانٍ (بعض الخوادم قد تستغرق وقتًا أطول)
            smtp.Timeout = 15000

            ' إنشاء رسالة اختبار (لن يتم إرسالها فعليًا)
            Dim testMessage As New MailMessage()
            testMessage.From = New MailAddress(email)
            testMessage.To.Add(email) ' إرسال إلى نفس البريد الإلكتروني
            testMessage.Subject = "Test Connection"
            testMessage.Body = "This is a test message to verify SMTP connection."

            ' تعطيل التحقق من شهادة SSL (مفيد للخوادم ذات الشهادات الذاتية)
            System.Net.ServicePointManager.ServerCertificateValidationCallback =
                Function(sender2, certificate, chain, sslPolicyErrors)
                    Return True
                End Function

            ' محاولة الاتصال بالخادم
            Dim connected As Boolean = False

            ' أولاً، نحاول الاتصال باستخدام TCP للتحقق من أن الخادم متاح
            Try
                Using client As New System.Net.Sockets.TcpClient()
                    ' تعيين مهلة الاتصال
                    Dim connectTask = client.ConnectAsync(smtpServer, port)

                    ' انتظار الاتصال لمدة 5 ثوانٍ كحد أقصى
                    If connectTask.Wait(5000) Then
                        connected = client.Connected
                    Else
                        Throw New TimeoutException("Connection to SMTP server timed out.")
                    End If
                End Using
            Catch ex As Exception
                ' إذا فشل الاتصال باستخدام TCP، نحاول استخدام SmtpClient مباشرة
                ' بعض الخوادم قد تكون محمية بجدار ناري يمنع اتصالات TCP المباشرة
                connected = True ' نفترض أن الاتصال ممكن ونترك SmtpClient يتعامل مع الأخطاء
            End Try

            If connected Then
                ' محاولة المصادقة باستخدام SMTP
                Dim cancellationTokenSource As New Threading.CancellationTokenSource()

                ' إعداد مؤقت لإلغاء العملية بعد 10 ثوانٍ
                Dim timer As New System.Threading.Timer(
                    Sub(state)
                        Try
                            cancellationTokenSource.Cancel()
                        Catch
                            ' تجاهل أي استثناءات
                        End Try
                    End Sub,
                    Nothing,
                    10000,
                    Threading.Timeout.Infinite)

                Try
                    ' محاولة المصادقة فقط (بدون إرسال الرسالة فعليًا)
                    ' استخدام SendMailAsync بطريقة متوافقة مع جميع إصدارات .NET
                    Dim task As Threading.Tasks.Task

                    ' استخدام Try/Catch لتجربة الإصدارات المختلفة من SendMailAsync
                    Try
                        ' محاولة استخدام الإصدار الذي يدعم CancellationToken
                        task = smtp.SendMailAsync(testMessage)
                    Catch ex As Exception
                        ' إذا فشلت المحاولة الأولى، نستخدم الإصدار البديل
                        task = Threading.Tasks.Task.Run(Sub() smtp.Send(testMessage))
                    End Try

                    ' انتظار لمدة قصيرة ثم إلغاء العملية
                    ' هذا يسمح بالمصادقة ولكن يمنع إرسال الرسالة فعليًا
                    System.Threading.Thread.Sleep(2000)
                    cancellationTokenSource.Cancel()

                    ' إذا وصلنا إلى هنا بدون استثناء، فإن الاتصال والمصادقة نجحا
                    DevExpress.XtraEditors.XtraMessageBox.Show("SMTP account verified successfully!" & vbCrLf &
                                                              "Server: " & smtpServer & vbCrLf &
                                                              "Port: " & port.ToString() & vbCrLf &
                                                              "SSL: " & enableSsl.ToString(),
                                                              "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)

                    ' تعيين SmtpChecked إلى True
                    SmtpChecked = True

                    ' عرض رسالة نجاح في Label5 باللون الأخضر
                    Me.Invoke(Sub()
                        Label5.Text = "SMTP connection is working properly!"
                        Label5.ForeColor = Color.FromArgb(34, 197, 94) ' اللون الأخضر
                    End Sub)
                Catch ex As Threading.Tasks.TaskCanceledException
                    ' تم إلغاء المهمة عمدًا، هذا متوقع
                    ' إذا وصلنا إلى هنا، فإن الاتصال والمصادقة نجحا
                    DevExpress.XtraEditors.XtraMessageBox.Show("SMTP account verified successfully!" & vbCrLf &
                                                              "Server: " & smtpServer & vbCrLf &
                                                              "Port: " & port.ToString() & vbCrLf &
                                                              "SSL: " & enableSsl.ToString(),
                                                              "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)

                    ' تعيين SmtpChecked إلى True
                    SmtpChecked = True

                    ' عرض رسالة نجاح في Label5 باللون الأخضر
                    Me.Invoke(Sub()
                        Label5.Text = "SMTP connection is working properly!"
                        Label5.ForeColor = Color.FromArgb(34, 197, 94) ' اللون الأخضر
                    End Sub)
                Catch ex As Exception
                    ' تحليل رسالة الخطأ للحصول على معلومات أكثر دقة
                    Dim errorMessage As String = ex.Message

                    ' التحقق من أنواع مختلفة من أخطاء المصادقة
                    If errorMessage.Contains("authentication") OrElse
                       errorMessage.Contains("5.7.0") OrElse
                       errorMessage.Contains("5.7.1") OrElse
                       errorMessage.Contains("535") OrElse
                       errorMessage.Contains("credentials") Then
                        Throw New Exception("Authentication failed. Please check your email and password.")
                    ElseIf errorMessage.Contains("timeout") Then
                        Throw New Exception("Connection timed out. The server might be down or the port might be blocked.")
                    ElseIf errorMessage.Contains("SSL/TLS") OrElse errorMessage.Contains("secure connection") Then
                        ' محاولة تغيير إعدادات SSL والمحاولة مرة أخرى
                        Throw New Exception("SSL/TLS error. Try " & If(enableSsl, "disabling", "enabling") & " SSL in the settings.")
                    ElseIf errorMessage.Contains("canceled") OrElse errorMessage.Contains("aborted") Then
                        ' تم إلغاء العملية، ولكن الاتصال نجح
                        DevExpress.XtraEditors.XtraMessageBox.Show("SMTP account verified successfully!" & vbCrLf &
                                                                  "Server: " & smtpServer & vbCrLf &
                                                                  "Port: " & port.ToString() & vbCrLf &
                                                                  "SSL: " & enableSsl.ToString(),
                                                                  "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)

                        ' تعيين SmtpChecked إلى True
                        SmtpChecked = True

                        ' عرض رسالة نجاح في Label5 باللون الأخضر
                        Me.Invoke(Sub()
                            Label5.Text = "SMTP connection is working properly!"
                            Label5.ForeColor = Color.FromArgb(34, 197, 94) ' اللون الأخضر
                        End Sub)
                    Else
                        ' خطأ آخر، إعادة رميه مع معلومات إضافية
                        Throw New Exception("Error connecting to SMTP server: " & errorMessage)
                    End If
                Finally
                    ' إيقاف المؤقت
                    timer.Dispose()
                End Try
            Else
                ' فشل الاتصال
                Throw New Exception("Could not connect to SMTP server. Please check server address and port.")
            End If
        Catch ex As Exception
            ' عرض رسالة الخطأ
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)

            ' عرض رسالة خطأ في Label5 باللون الأحمر الغامق
            Me.Invoke(Sub()
                Label5.Text = "SMTP connection failed: " & ex.Message
                Label5.ForeColor = Color.FromArgb(185, 28, 28) ' اللون الأحمر الغامق
            End Sub)

            ' تعيين e.Cancel إلى True لإشارة إلى أن العملية فشلت
            e.Cancel = True
        Finally
            ' إعادة تعيين التحقق من شهادة SSL إلى الإعداد الافتراضي
            System.Net.ServicePointManager.ServerCertificateValidationCallback = Nothing
        End Try
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        If e.Cancelled Then SmtpChecked = False
        PnlWait.Visible = False

        ' تجميد زر BntStop بدلاً من إخفائه
        BntStop.Enabled = False
    End Sub


    Private Sub CheckSSL_CheckedChanged(sender As Object, e As EventArgs) Handles CheckSSL.CheckedChanged
        If CheckSSL.Checked = True Then
            CheckSSL.ForeColor = Color.FromArgb(254, 219, 65) ' اللون الذهبي ليتناسب مع التصميم
        Else
            CheckSSL.ForeColor = Color.Gray()
        End If

        ' تحديث نص CheckSSL ليعكس المنفذ المستخدم
        If Not String.IsNullOrEmpty(TxtPort.Text.Trim) Then
            Dim port As Integer
            If Integer.TryParse(TxtPort.Text.Trim, port) Then
                If port = 465 And Not CheckSSL.Checked Then
                    ' المنفذ 465 يتطلب SSL
                    CheckSSL.Checked = True
                End If
            End If
        End If
    End Sub


    Private Sub BntTest_Click(sender As Object, e As EventArgs) Handles BntTest.Click
        If TxtSMTPServer.Text.Trim = "" Then
            MessageBox.Show("Invalid SMTP Server name...!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            TxtSMTPServer.Focus()
            Exit Sub
        End If
        If TxtEmail.Text.Trim = "" Then
            MessageBox.Show("Invalid email address...!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            TxtEmail.Focus()
            Exit Sub
        End If
        If TxtPassword.Text.Trim = "" Then
            MessageBox.Show("Invalid email Password...!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            TxtPassword.Focus()
            Exit Sub
        End If
        If TxtPort.Text.Trim = "" Then
            MessageBox.Show("Invalid Port Number...!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            TxtPort.Focus()
            Exit Sub
        End If
        SmtpChecked = False
        PnlWait.Visible = True

        ' تفعيل زر BntStop وجعله مرئيًا
        BntStop.Enabled = True
        BntStop.Visible = True

        ' مسح أي رسائل سابقة في Label5
        Label5.Text = ""

        If BackgroundWorker1.IsBusy Then BackgroundWorker1.CancelAsync() Else BackgroundWorker1.RunWorkerAsync()
    End Sub

    Private Sub BntStop_Click(sender As Object, e As EventArgs) Handles BntStop.Click
        If Not BackgroundWorker1.IsBusy Then
            BackgroundWorker1.RunWorkerAsync()
        Else
            BackgroundWorker1.CancelAsync()
        End If
    End Sub

    Private Sub BntResetAll_Click(sender As Object, e As EventArgs) Handles BntResetAll.Click
        If TxtSMTPServer.Text.Trim = "" Then
            MessageBox.Show("Invalid SMTP Server name...!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            TxtSMTPServer.Focus()
            Exit Sub
        End If
        If TxtEmail.Text.Trim = "" Then
            MessageBox.Show("Invalid email address...!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            TxtEmail.Focus()
            Exit Sub
        End If
        If TxtPassword.Text.Trim = "" Then
            MessageBox.Show("Invalid email Password...!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            TxtPassword.Focus()
            Exit Sub
        End If
        If TxtPort.Text.Trim = "" Then
            MessageBox.Show("Invalid Port Number...!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            TxtPort.Focus()
            Exit Sub
        End If
        BackgroundWorker1.WorkerSupportsCancellation = True
        BackgroundWorker1.WorkerReportsProgress = True
        PnlWait.Visible = False

        BntStop.Visible = False
        SmtpChecked = False
        TxtSMTPServer.Text = ""
        TxtEmail.Text = ""
        TxtPassword.Text = ""
        TxtPort.Text = ""
        CheckSSL.Checked = False
        CheckSSL_CheckedChanged(Nothing, Nothing)
        Me.ActiveControl = TxtSMTPServer
    End Sub

    ''' <summary>
    ''' حدث تحميل النموذج
    ''' </summary>
    Private Sub frmQuickAccountCheck_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' إنشاء MainPanel
        PnlWait.Visible = False
        CreateMainPanel()

        ' إضافة معالج حدث Paint للـ MainPanel
        AddHandler MainPanel.Paint, AddressOf MainPanel_Paint

        ' إضافة معالج حدث Resize للـ MainPanel
        AddHandler MainPanel.Resize, AddressOf MainPanel_Resize

        ' إضافة معالج حدث Resize للنموذج
        AddHandler Me.Resize, AddressOf frmQuickAccountCheck_Resize

        ' نقل العناصر إلى MainPanel
        MoveControlsToMainPanel()

        ' تهيئة MainPanel ليكون في المنتصف
        CenterMainPanel()
    End Sub

    ''' <summary>
    ''' إنشاء MainPanel
    ''' </summary>
    Private Sub CreateMainPanel()
        ' إنشاء MainPanel إذا لم يكن موجودًا
        If MainPanel Is Nothing Then
            MainPanel = New Panel()
            MainPanel.Name = "MainPanel"
            MainPanel.Anchor = AnchorStyles.None
            MainPanel.BackColor = Color.FromArgb(46, 46, 46)
            MainPanel.Size = New Size(Me.ClientSize.Width - 20, Me.ClientSize.Height - 20)
            MainPanel.Padding = New Padding(10)
            Me.Controls.Add(MainPanel)
            MainPanel.BringToFront()
        End If
    End Sub

    ''' <summary>
    ''' نقل العناصر إلى MainPanel
    ''' </summary>
    Private Sub MoveControlsToMainPanel()
        ' التأكد من وجود MainPanel
        If MainPanel Is Nothing Then Return

        ' نقل العناصر من النموذج إلى MainPanel
        If Not MainPanel.Controls.Contains(TxtSMTPServer) Then
            MainPanel.Controls.Add(TxtSMTPServer)
        End If

        If Not MainPanel.Controls.Contains(TxtEmail) Then
            MainPanel.Controls.Add(TxtEmail)
        End If

        If Not MainPanel.Controls.Contains(TxtPassword) Then
            MainPanel.Controls.Add(TxtPassword)
        End If

        If Not MainPanel.Controls.Contains(TxtPort) Then
            MainPanel.Controls.Add(TxtPort)
        End If

        If Not MainPanel.Controls.Contains(Label1) Then
            MainPanel.Controls.Add(Label1)
        End If

        If Not MainPanel.Controls.Contains(Label2) Then
            MainPanel.Controls.Add(Label2)
        End If

        If Not MainPanel.Controls.Contains(Label3) Then
            MainPanel.Controls.Add(Label3)
        End If

        If Not MainPanel.Controls.Contains(Label4) Then
            MainPanel.Controls.Add(Label4)
        End If

        If Not MainPanel.Controls.Contains(Label5) Then
            MainPanel.Controls.Add(Label5)
        End If

        If Not MainPanel.Controls.Contains(CheckEdit1) Then
            MainPanel.Controls.Add(CheckEdit1)
        End If

        If Not MainPanel.Controls.Contains(CheckSSL) Then
            MainPanel.Controls.Add(CheckSSL)
        End If

        If Not MainPanel.Controls.Contains(BntTest) Then
            MainPanel.Controls.Add(BntTest)
        End If

        If Not MainPanel.Controls.Contains(BntResetAll) Then
            MainPanel.Controls.Add(BntResetAll)
        End If

        If Not MainPanel.Controls.Contains(BntStop) Then
            MainPanel.Controls.Add(BntStop)
        End If

        If Not MainPanel.Controls.Contains(PnlWait) Then
            MainPanel.Controls.Add(PnlWait)
        End If

        If Not MainPanel.Controls.Contains(BntAddAccount) Then
            MainPanel.Controls.Add(BntAddAccount)
        End If

        If Not MainPanel.Controls.Contains(ListBoxControl1) Then
            MainPanel.Controls.Add(ListBoxControl1)
        End If
    End Sub

    ''' <summary>
    ''' وضع MainPanel في المنتصف
    ''' </summary>
    Private Sub CenterMainPanel()
        ' الحفاظ على الحجم الأصلي للـ MainPanel
        Dim originalWidth As Integer = 972 ' العرض الأصلي
        Dim originalHeight As Integer = 602 ' الارتفاع الأصلي

        ' تعيين حجم MainPanel إلى الحجم الأصلي
        MainPanel.Width = originalWidth
        MainPanel.Height = originalHeight

        ' حساب موقع MainPanel ليكون في المنتصف
        Dim x As Integer = (Me.ClientSize.Width - MainPanel.Width) \ 2
        Dim y As Integer = (Me.ClientSize.Height - MainPanel.Height) \ 2

        ' تعيين موقع MainPanel في المنتصف
        MainPanel.Location = New Point(Math.Max(0, x), Math.Max(0, y))

        ' توسيط العناصر داخل MainPanel
        CenterControlsInMainPanel()

        ' جعل MainPanel منحنية من الأطراف
        ApplyRoundedCorners(MainPanel, 20)
    End Sub

    ''' <summary>
    ''' توسيط العناصر داخل MainPanel
    ''' </summary>
    Private Sub CenterControlsInMainPanel()
        ' حساب نقطة المنتصف في MainPanel
        Dim centerX As Integer = MainPanel.Width \ 2
        Dim centerY As Integer = MainPanel.Height \ 2

        ' حساب المساحة الإجمالية التي تشغلها جميع العناصر
        Dim totalWidth As Integer = 800  ' تقريبًا عرض جميع العناصر مع المسافات بينها
        Dim totalHeight As Integer = 400 ' تقريبًا ارتفاع جميع العناصر مع المسافات بينها

        ' حساب نقطة البداية لوضع العناصر
        Dim startX As Integer = centerX - (totalWidth \ 2)
        Dim startY As Integer = centerY - (totalHeight \ 2)

        ' حساب عرض المجموعة الكاملة
        Dim totalGroupWidth As Integer = TxtSMTPServer.Width

        ' حساب نقطة البداية للمجموعة بحيث تكون في المنتصف
        Dim groupStartX As Integer = centerX - (totalGroupWidth \ 2)

        ' تحديد مواقع العناصر بناءً على نقطة البداية الجديدة
        Dim labelX As Integer = groupStartX
        Dim textX As Integer = groupStartX

        ' تعيين مواقع العناصر
        Label1.Location = New Point(labelX, startY)
        TxtSMTPServer.Location = New Point(textX, Label1.Bottom + 5)

        Label2.Location = New Point(labelX, TxtSMTPServer.Bottom + 10)
        TxtEmail.Location = New Point(textX, Label2.Bottom + 5)

        Label3.Location = New Point(labelX, TxtEmail.Bottom + 10)
        TxtPassword.Location = New Point(textX, Label3.Bottom + 5)
        CheckEdit1.Location = New Point(TxtPassword.Right - CheckEdit1.Width, TxtPassword.Top)

        Label4.Location = New Point(labelX, TxtPassword.Bottom + 10)
        TxtPort.Location = New Point(textX, Label4.Bottom + 5)

        CheckSSL.Location = New Point(labelX, TxtPort.Bottom + 20)

        ' توسيط الأزرار
        Dim buttonY As Integer = CheckSSL.Bottom + 20
        Dim buttonSpacing As Integer = 10
        Dim totalButtonsWidth As Integer = BntAddAccount.Width + buttonSpacing + BntTest.Width + buttonSpacing + BntStop.Width + buttonSpacing + BntResetAll.Width
        Dim buttonsStartX As Integer = centerX - (totalButtonsWidth \ 2)

        BntAddAccount.Location = New Point(buttonsStartX, buttonY)
        BntTest.Location = New Point(BntAddAccount.Right + buttonSpacing, buttonY)
        BntStop.Location = New Point(BntTest.Right + buttonSpacing, buttonY)
        BntResetAll.Location = New Point(BntStop.Right + buttonSpacing, buttonY)

        ' وضع Label5 أسفل الأزرار
        Label5.AutoSize = False
        Label5.TextAlign = ContentAlignment.MiddleCenter
        Label5.Width = 624 ' نفس عرض حقول الإدخال
        Label5.Height = 30
        Label5.Location = New Point(textX, BntTest.Bottom + 20)

        ' وضع PnlWait في مكانها المحدد (292, 58)
        PnlWait.Location = New Point(292, 58)
    End Sub

    ''' <summary>
    ''' معالج حدث Paint للـ MainPanel
    ''' </summary>
    Private Sub MainPanel_Paint(sender As Object, e As PaintEventArgs)
        ' رسم حدود منحنية للـ MainPanel
        Dim panel As Panel = DirectCast(sender, Panel)
        Dim radius As Integer = 20

        ' تعيين جودة الرسم للحصول على أفضل نتيجة
        e.Graphics.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
        e.Graphics.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
        e.Graphics.PixelOffsetMode = Drawing2D.PixelOffsetMode.HighQuality

        ' تعديل المستطيل ليكون داخل حدود اللوحة بشكل كامل
        ' نترك هامشًا كبيرًا (15 بكسل) لضمان ظهور الحدود بالكامل
        Dim rect As New Rectangle(15, 15, panel.Width - 30, panel.Height - 30)

        ' رسم مستطيل بحواف منحنية باللون الذهبي (254, 219, 65) بسمك 2 بكسل
        Using pen As New Pen(Color.FromArgb(254, 219, 65), 2)
            ' تعيين نهايات الخطوط لتكون دائرية
            pen.StartCap = Drawing2D.LineCap.Round
            pen.EndCap = Drawing2D.LineCap.Round
            pen.LineJoin = Drawing2D.LineJoin.Round
            pen.Alignment = Drawing2D.PenAlignment.Center

            ' رسم مستطيل بحواف منحنية
            e.Graphics.DrawPath(pen, GetRoundedRectPath(rect, radius))

            ' رسم مستطيل آخر بنفس الحجم لضمان ظهور الخط بشكل كامل
            ' هذه تقنية لتجنب مشكلة تقطيع الخطوط في GDI+
            e.Graphics.DrawPath(pen, GetRoundedRectPath(rect, radius))
        End Using
    End Sub

    ''' <summary>
    ''' إنشاء مسار لمستطيل بحواف منحنية
    ''' </summary>
    Private Function GetRoundedRectPath(rect As Rectangle, radius As Integer) As Drawing2D.GraphicsPath
        Dim path As New Drawing2D.GraphicsPath()

        ' تأكد من أن نصف القطر لا يتجاوز نصف عرض أو ارتفاع المستطيل
        radius = Math.Min(radius, Math.Min(rect.Width \ 2, rect.Height \ 2))

        ' تعديل المستطيل لضمان عدم تجاوز الحدود
        Dim adjustedRect As New Rectangle(
            rect.X,
            rect.Y,
            Math.Max(radius * 2, rect.Width),
            Math.Max(radius * 2, rect.Height)
        )

        ' إضافة الأقواس والخطوط للمسار بطريقة أكثر دقة
        ' الزاوية العلوية اليسرى
        path.AddArc(adjustedRect.X, adjustedRect.Y, radius * 2, radius * 2, 180, 90)

        ' الخط العلوي
        path.AddLine(
            adjustedRect.X + radius,
            adjustedRect.Y,
            adjustedRect.Right - radius,
            adjustedRect.Y
        )

        ' الزاوية العلوية اليمنى
        path.AddArc(
            adjustedRect.Right - radius * 2,
            adjustedRect.Y,
            radius * 2,
            radius * 2,
            270,
            90
        )

        ' الخط الأيمن
        path.AddLine(
            adjustedRect.Right,
            adjustedRect.Y + radius,
            adjustedRect.Right,
            adjustedRect.Bottom - radius
        )

        ' الزاوية السفلية اليمنى
        path.AddArc(
            adjustedRect.Right - radius * 2,
            adjustedRect.Bottom - radius * 2,
            radius * 2,
            radius * 2,
            0,
            90
        )

        ' الخط السفلي
        path.AddLine(
            adjustedRect.Right - radius,
            adjustedRect.Bottom,
            adjustedRect.X + radius,
            adjustedRect.Bottom
        )

        ' الزاوية السفلية اليسرى
        path.AddArc(
            adjustedRect.X,
            adjustedRect.Bottom - radius * 2,
            radius * 2,
            radius * 2,
            90,
            90
        )

        ' الخط الأيسر
        path.AddLine(
            adjustedRect.X,
            adjustedRect.Bottom - radius,
            adjustedRect.X,
            adjustedRect.Y + radius
        )

        ' إغلاق المسار
        path.CloseAllFigures()

        Return path
    End Function

    ''' <summary>
    ''' تطبيق حواف منحنية على لوحة
    ''' </summary>
    ''' <param name="panel">اللوحة المراد تطبيق الحواف المنحنية عليها</param>
    ''' <param name="radius">نصف قطر الانحناء</param>
    Private Sub ApplyRoundedCorners(panel As Panel, radius As Integer)
        ' تعديل المستطيل ليكون داخل حدود اللوحة بشكل كامل
        ' نترك هامشًا كبيرًا (12 بكسل) لضمان ظهور الحدود بالكامل
        Dim rect As New Rectangle(12, 12, panel.Width - 25, panel.Height - 25)

        ' إنشاء مسار منحني للحواف باستخدام الدالة المساعدة
        Dim path As Drawing2D.GraphicsPath = GetRoundedRectPath(rect, radius)

        ' تطبيق المسار على اللوحة
        panel.Region = New Region(path)

        ' تعيين خاصية BorderStyle إلى None لإخفاء الحدود الافتراضية
        panel.BorderStyle = BorderStyle.None
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmQuickAccountCheck_Resize(sender As Object, e As EventArgs)
        ' إعادة تعيين حجم وموقع MainPanel عند تغيير حجم النموذج
        CenterMainPanel()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم MainPanel
    ''' </summary>
    Private Sub MainPanel_Resize(sender As Object, e As EventArgs)
        ' إعادة تطبيق الحواف المنحنية عند تغيير حجم اللوحة
        ApplyRoundedCorners(MainPanel, 20)

        ' إعادة رسم اللوحة
        MainPanel.Invalidate()
    End Sub

    ''' <summary>
    ''' التحقق من وجود بيانات في الحقول وإظهار/إخفاء BntResetAll
    ''' </summary>
    Private Sub CheckFieldsAndUpdateButtons()
        ' التحقق من وجود بيانات في أي من الحقول
        Dim hasData As Boolean = (TxtSMTPServer.Text.Trim <> "" OrElse
                                 TxtEmail.Text.Trim <> "" OrElse
                                 TxtPassword.Text.Trim <> "" OrElse
                                 TxtPort.Text.Trim <> "")

        ' إظهار/إخفاء BntResetAll بناءً على وجود بيانات
        BntResetAll.Visible = hasData
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير النص في TxtSMTPServer
    ''' </summary>
    Private Sub TxtSMTPServer_TextChanged(sender As Object, e As EventArgs) Handles TxtSMTPServer.TextChanged
        CheckFieldsAndUpdateButtons()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير النص في TxtEmail
    ''' </summary>
    Private Sub TxtEmail_TextChanged(sender As Object, e As EventArgs) Handles TxtEmail.TextChanged
        CheckFieldsAndUpdateButtons()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير النص في TxtPassword
    ''' </summary>
    Private Sub TxtPassword_TextChanged(sender As Object, e As EventArgs) Handles TxtPassword.TextChanged
        CheckFieldsAndUpdateButtons()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير النص في TxtPort
    ''' </summary>
    Private Sub TxtPort_TextChanged(sender As Object, e As EventArgs) Handles TxtPort.TextChanged
        CheckFieldsAndUpdateButtons()

        ' تحديث حالة CheckSSL بناءً على المنفذ
        If Not String.IsNullOrEmpty(TxtPort.Text.Trim) Then
            Dim port As Integer
            If Integer.TryParse(TxtPort.Text.Trim, port) Then
                ' تعيين حالة SSL بناءً على المنفذ
                If port = 465 Then
                    ' المنفذ 465 يستخدم SSL دائمًا
                    CheckSSL.Checked = True
                ElseIf port = 587 Then
                    ' المنفذ 587 يستخدم STARTTLS عادة (يمكن أن يكون SSL أو لا)
                    ' نترك الإعداد كما هو أو نقترح تمكين SSL
                    If Not CheckSSL.Checked Then
                        ' اقتراح تمكين SSL للمنفذ 587
                        If DevExpress.XtraEditors.XtraMessageBox.Show(
                            "Port 587 typically uses STARTTLS. Would you like to enable SSL?",
                            "SSL Recommendation",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Question) = DialogResult.Yes Then
                            CheckSSL.Checked = True
                        End If
                    End If
                ElseIf port = 25 Then
                    ' المنفذ 25 عادة لا يستخدم SSL، ولكن بعض المزودين قد يستخدمونه
                    ' نترك الإعداد كما هو
                ElseIf port = 2525 Then
                    ' المنفذ 2525 مشابه للمنفذ 587
                    ' نترك الإعداد كما هو أو نقترح تمكين SSL
                    If Not CheckSSL.Checked Then
                        ' اقتراح تمكين SSL للمنفذ 2525
                        If DevExpress.XtraEditors.XtraMessageBox.Show(
                            "Port 2525 typically uses STARTTLS. Would you like to enable SSL?",
                            "SSL Recommendation",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Question) = DialogResult.Yes Then
                            CheckSSL.Checked = True
                        End If
                    End If
                End If
            End If
        End If
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر BntAddAccount
    ''' </summary>
    Private Sub BntAddAccount_Click(sender As Object, e As EventArgs) Handles BntAddAccount.Click
        ' إنشاء مربع حوار لاختيار ملف
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text files (*.txt)|*.txt|All files (*.*)|*.*"
        openFileDialog.Title = "Select SMTP Accounts File"

        ' إذا اختار المستخدم ملفًا
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            Try
                ' قراءة محتوى الملف
                Dim fileContent As String = File.ReadAllText(openFileDialog.FileName)

                ' تقسيم المحتوى إلى أسطر
                Dim lines As String() = fileContent.Split(New String() {Environment.NewLine}, StringSplitOptions.RemoveEmptyEntries)

                ' مسح ListBoxControl1
                ListBoxControl1.Items.Clear()

                ' إضافة رسالة توجيهية
                ListBoxControl1.Items.Add("Please select one SMTP account only")

                ' إضافة الأسطر إلى ListBoxControl1
                For Each line As String In lines
                    ' التحقق من أن السطر يحتوي على البيانات المطلوبة
                    If line.Contains("|") AndAlso line.Split("|"c).Length >= 4 Then
                        ListBoxControl1.Items.Add(line)
                    End If
                Next

                ' إخفاء جميع العناصر
                HideAllControls()

                ' إظهار ListBoxControl1
                ListBoxControl1.Visible = True

                ' وضع ListBoxControl1 في المنتصف
                CenterListBoxControl1()
            Catch ex As Exception
                MessageBox.Show("Error reading file: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    ''' <summary>
    ''' معالج حدث النقر المزدوج على ListBoxControl1
    ''' </summary>
    Private Sub ListBoxControl1_DoubleClick(sender As Object, e As EventArgs) Handles ListBoxControl1.DoubleClick
        ' التحقق من أن هناك عنصر محدد
        If ListBoxControl1.SelectedIndex > 0 Then ' تجاوز الرسالة التوجيهية
            ' الحصول على السطر المحدد
            Dim selectedLine As String = ListBoxControl1.SelectedItem.ToString()

            ' تقسيم السطر إلى أجزاء
            Dim parts As String() = selectedLine.Split("|"c)

            ' التحقق من أن السطر يحتوي على البيانات المطلوبة
            If parts.Length >= 4 Then
                ' تعيين البيانات في الحقول
                TxtSMTPServer.Text = parts(0)
                TxtEmail.Text = parts(1)
                TxtPassword.Text = parts(2)
                TxtPort.Text = parts(3)

                ' إخفاء ListBoxControl1
                ListBoxControl1.Visible = False

                ' إظهار جميع العناصر
                ShowAllControls()
            End If
        End If
    End Sub

    ''' <summary>
    ''' إخفاء جميع العناصر ما عدا ListBoxControl1
    ''' </summary>
    Private Sub HideAllControls()
        ' إخفاء الحقول والتسميات
        TxtSMTPServer.Visible = False
        TxtEmail.Visible = False
        TxtPassword.Visible = False
        TxtPort.Visible = False
        Label1.Visible = False
        Label2.Visible = False
        Label3.Visible = False
        Label4.Visible = False

        ' إخفاء الأزرار
        BntTest.Visible = False
        BntStop.Visible = False
        BntResetAll.Visible = False
        BntAddAccount.Visible = False

        ' إخفاء CheckEdit1 و CheckSSL
        CheckEdit1.Visible = False
        CheckSSL.Visible = False
    End Sub

    ''' <summary>
    ''' إظهار جميع العناصر ما عدا ListBoxControl1
    ''' </summary>
    Private Sub ShowAllControls()
        ' إظهار الحقول والتسميات
        TxtSMTPServer.Visible = True
        TxtEmail.Visible = True
        TxtPassword.Visible = True
        TxtPort.Visible = True
        Label1.Visible = True
        Label2.Visible = True
        Label3.Visible = True
        Label4.Visible = True

        ' إظهار الأزرار
        BntTest.Visible = True
        BntAddAccount.Visible = True

        ' إظهار CheckEdit1 و CheckSSL
        CheckEdit1.Visible = True
        CheckSSL.Visible = True

        ' التحقق من وجود بيانات في الحقول وتحديث حالة الأزرار
        CheckFieldsAndUpdateButtons()
    End Sub

    ''' <summary>
    ''' وضع ListBoxControl1 في المنتصف
    ''' </summary>
    Private Sub CenterListBoxControl1()
        ' حساب نقطة المنتصف في MainPanel
        Dim centerX As Integer = MainPanel.Width \ 2
        Dim centerY As Integer = MainPanel.Height \ 2

        ' حساب موقع ListBoxControl1 ليكون في المنتصف
        Dim x As Integer = centerX - (ListBoxControl1.Width \ 2)
        Dim y As Integer = centerY - (ListBoxControl1.Height \ 2)

        ' تعيين موقع ListBoxControl1 في المنتصف
        ListBoxControl1.Location = New Point(x, y)
    End Sub
End Class