﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections</name>
  </assembly>
  <members>
    <member name="T:System.Collections.BitArray">
      <summary>管理以布林 (Boolean) 表示的位元值之精簡陣列，其中 true 表示位元為開啟 (1)，而 false 表示位元為關閉 (0)。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Boolean[])">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 類別的新執行個體，其包含的位元值是從指定的布林陣列複製過來的。</summary>
      <param name="values">要複製的布林值陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Byte[])">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 類別的新執行個體，其包含的位元值是從指定的位元組陣列複製過來的。</summary>
      <param name="bytes">包含要複製值的位元組陣列，其中每一個位元組表示 8 個連續位元。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="bytes" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Collections.BitArray)">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 類別的新執行個體，其包含的位元值是從指定的 <see cref="T:System.Collections.BitArray" /> 複製過來的。</summary>
      <param name="bits">要複製的 <see cref="T:System.Collections.BitArray" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bits" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 類別的新執行個體，這個執行個體可保存指定數目的位元值 (其初始設定為 false)。</summary>
      <param name="length">在新 <see cref="T:System.Collections.BitArray" /> 中的位元值數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32,System.Boolean)">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 類別的新執行個體，其可以保存指定位元值數目 (初始設定為指定的值)。</summary>
      <param name="length">在新 <see cref="T:System.Collections.BitArray" /> 中的位元值數目。</param>
      <param name="defaultValue">要指派給每一個位元的布林值。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32[])">
      <summary>初始化 <see cref="T:System.Collections.BitArray" /> 類別的新執行個體，其包含的位元值是從指定的 32 位元整數陣列複製過來的。</summary>
      <param name="values">包含要複製值的整數陣列，其中每一個整數代表 32 個連續位元。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="values" /> is greater than <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Collections.BitArray.And(System.Collections.BitArray)">
      <summary>在目前 <see cref="T:System.Collections.BitArray" /> 中的項目上，針對指定的 <see cref="T:System.Collections.BitArray" /> 中的對應項目，執行位元的 AND 運算。</summary>
      <returns>目前執行個體，包含目前 <see cref="T:System.Collections.BitArray" /> 的項目與指定 <see cref="T:System.Collections.BitArray" /> 的對應項目之間位元 AND 運算的結果。</returns>
      <param name="value">
        <see cref="T:System.Collections.BitArray" />，用來執行位元的 AND 運算。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Get(System.Int32)">
      <summary>取得 <see cref="T:System.Collections.BitArray" /> 中指定位置的位元值。</summary>
      <returns>在 <paramref name="index" /> 位置的位元值。</returns>
      <param name="index">要取得的值的以零為起始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Collections.BitArray" /> 中逐一查看的列舉值。</summary>
      <returns>整個 <see cref="T:System.Collections.BitArray" /> 的 <see cref="T:System.Collections.IEnumerator" />。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Item(System.Int32)">
      <summary>取得或設定在 <see cref="T:System.Collections.BitArray" /> 中特定位置的位元值。</summary>
      <returns>在 <paramref name="index" /> 位置的位元值。</returns>
      <param name="index">要取得或設定的值之以零為起始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.BitArray.Count" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Length">
      <summary>取得或設定 <see cref="T:System.Collections.BitArray" /> 中的項目數。</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" /> 中的項目數。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is set to a value that is less than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Not">
      <summary>反轉目前 <see cref="T:System.Collections.BitArray" /> 中的位元值，使得設定為 true 的項目變更為 false，並且設定為 false 的項目變更為 true。</summary>
      <returns>具有已轉換位元值的目前執行個體。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Or(System.Collections.BitArray)">
      <summary>在目前 <see cref="T:System.Collections.BitArray" /> 中的項目上，針對指定的 <see cref="T:System.Collections.BitArray" /> 中的對應項目，執行位元的 OR 運算。</summary>
      <returns>目前執行個體，包含目前 <see cref="T:System.Collections.BitArray" /> 的項目與指定 <see cref="T:System.Collections.BitArray" /> 的對應項目之間位元 OR 運算的結果。</returns>
      <param name="value">
        <see cref="T:System.Collections.BitArray" />，用來執行位元的 OR 運算。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Set(System.Int32,System.Boolean)">
      <summary>將 <see cref="T:System.Collections.BitArray" /> 中特定位置的位元設定為指定值。</summary>
      <param name="index">要設定之位元的以零為起始的索引。</param>
      <param name="value">要指派給位元的布林值。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.SetAll(System.Boolean)">
      <summary>將 <see cref="T:System.Collections.BitArray" /> 中的所有位元設定為指定值。</summary>
      <param name="value">要指派給所有位元的布林值。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從特定的 <see cref="T:System.Array" /> 索引開始，將 <see cref="T:System.Collections.BitArray" /> 的項目複製至 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.BitArray" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零為起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零為起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or- The number of elements in the source <see cref="T:System.Collections.BitArray" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.BitArray" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#Count">
      <summary>取得 <see cref="T:System.Collections.BitArray" /> 中的項目數目。</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" /> 中的項目數。</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，指出 <see cref="T:System.Collections.BitArray" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Collections.BitArray" /> 的存取為同步 (安全執行緒)，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#SyncRoot">
      <summary>取得可用以同步存取 <see cref="T:System.Collections.BitArray" /> 的物件。</summary>
      <returns>可用以同步存取 <see cref="T:System.Collections.BitArray" /> 的物件。</returns>
    </member>
    <member name="M:System.Collections.BitArray.Xor(System.Collections.BitArray)">
      <summary>在目前 <see cref="T:System.Collections.BitArray" /> 中的項目上，針對指定的 <see cref="T:System.Collections.BitArray" /> 中的對應項目，執行位元互斥 OR 運算。</summary>
      <returns>目前執行個體，包含目前 <see cref="T:System.Collections.BitArray" /> 的項目與指定 <see cref="T:System.Collections.BitArray" /> 的對應項目之間位元互斥 OR 運算的結果。</returns>
      <param name="value">
        <see cref="T:System.Collections.BitArray" />，用來執行位元互斥 OR 運算。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Collections.StructuralComparisons">
      <summary>提供物件，用以執行兩個集合物件的結構比較。</summary>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralComparer">
      <summary>取得預先定義的物件，這個物件會執行兩個物件的結構比較。</summary>
      <returns>預先定義的物件，用來執行兩個集合物件的結構比較。</returns>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralEqualityComparer">
      <summary>取得預先定義的物件，這個物件會比較兩個物件的結構是否相等。</summary>
      <returns>預先定義的物件，用來比較兩個集合物件的結構是否相等。</returns>
    </member>
    <member name="T:System.Collections.Generic.Comparer`1">
      <summary>提供基底類別 (Base Class) 用於 <see cref="T:System.Collections.Generic.IComparer`1" /> 泛型介面的實作。</summary>
      <typeparam name="T">要比較之物件的型別。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.Comparer`1" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Compare(`0,`0)">
      <summary>在衍生類別中覆寫時，比較型別相同的兩個物件並傳回值，指出一個物件是小於、等於還是大於另一個物件。</summary>
      <returns>帶正負號的整數，表示 <paramref name="x" /> 和 <paramref name="y" /> 的相對值，如下表所示。值意義小於零<paramref name="x" /> 小於 <paramref name="y" />。Zero<paramref name="x" /> 等於 <paramref name="y" />。大於零<paramref name="x" /> 大於 <paramref name="y" />。</returns>
      <param name="x">要比較的第一個物件。</param>
      <param name="y">要比較的第二個物件。</param>
      <exception cref="T:System.ArgumentException">型別 <paramref name="T" /> 不實作 <see cref="T:System.IComparable`1" /> 泛型介面或 <see cref="T:System.IComparable" /> 介面。</exception>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Create(System.Comparison{`0})">
      <summary>使用指定的比較建立比較子。</summary>
      <returns>新的比較子。</returns>
      <param name="comparison">要使用的比較。</param>
    </member>
    <member name="P:System.Collections.Generic.Comparer`1.Default">
      <summary>傳回泛型引數指定之型別的預設排序次序比較子 (Comparer)。</summary>
      <returns>物件，繼承 <see cref="T:System.Collections.Generic.Comparer`1" />，並用做型別 <paramref name="T" /> 的排序次序比較子。</returns>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>比較兩個物件並傳回值，指出其中一個物件為小於、等於或大於另一個物件。</summary>
      <returns>帶正負號的整數，表示 <paramref name="x" /> 和 <paramref name="y" /> 的相對值，如下表所示。值意義小於零<paramref name="x" /> 小於 <paramref name="y" />。Zero<paramref name="x" /> 等於 <paramref name="y" />。大於零<paramref name="x" /> 大於 <paramref name="y" />。</returns>
      <param name="x">要比較的第一個物件。</param>
      <param name="y">要比較的第二個物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> 或 <paramref name="y" /> 的型別無法轉型為型別 <paramref name="T" />。-或-<paramref name="x" /> 和 <paramref name="y" /> 不實作 <see cref="T:System.IComparable`1" /> 泛型介面或 <see cref="T:System.IComparable" /> 介面。</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2">
      <summary>表示索引鍵和值的集合。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <typeparam name="TKey">字典中的索引鍵類型。</typeparam>
      <typeparam name="TValue">字典中的值類型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 類別的新執行個體，這個執行個體是空白的、具有預設的初始容量，並使用索引鍵類型的預設相等比較子。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 類別的新執行個體，其中包含從指定的 <see cref="T:System.Collections.Generic.IDictionary`2" /> 複製的項目，並使用索引鍵類型的預設相等比較子。</summary>
      <param name="dictionary">要將其項目複製到新 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的 <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一個或多個重複的索引鍵。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 類別的新執行個體，其中包含從指定的 <see cref="T:System.Collections.Generic.IDictionary`2" /> 複製的項目，並使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</summary>
      <param name="dictionary">要將其項目複製到新 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的 <see cref="T:System.Collections.Generic.Dictionary`2" />。</param>
      <param name="comparer">比較索引鍵時所要使用的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 實作，或是 null，表示要為索引鍵的類型使用預設 <see cref="T:System.Collections.Generic.EqualityComparer`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一個或多個重複的索引鍵。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 類別的新執行個體，這個執行個體是空白的、具有預設的初始容量，並使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</summary>
      <param name="comparer">比較索引鍵時所要使用的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 實作，或是 null，表示要為索引鍵的類型使用預設 <see cref="T:System.Collections.Generic.EqualityComparer`1" />。</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 類別的新執行個體，這個執行個體是空白的、具有指定的初始容量，並使用索引鍵類型的預設相等比較子。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 可包含的初始項目數。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> 小於 0。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2" /> 類別的新執行個體，這個執行個體是空白的、具有指定的初始容量，並使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 可包含的初始項目數。</param>
      <param name="comparer">比較索引鍵時所要使用的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 實作，或是 null，表示要為索引鍵的類型使用預設 <see cref="T:System.Collections.Generic.EqualityComparer`1" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> 小於 0。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Add(`0,`1)">
      <summary>將指定的索引鍵和值加入字典。</summary>
      <param name="key">要加入的項目的索引鍵。</param>
      <param name="value">要加入的項目的值。參考類型的值可以是 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 中已存在具有相同索引鍵的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Clear">
      <summary>從 <see cref="T:System.Collections.Generic.Dictionary`2" /> 移除所有索引鍵和值。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Comparer">
      <summary>取得 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />，用來判斷字典的索引鍵是否相等。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 泛型介面實作，用來判斷目前 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的索引鍵是否相等，並提供索引鍵的雜湊值。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsKey(`0)">
      <summary>判斷 <see cref="T:System.Collections.Generic.Dictionary`2" /> 是否包含特定索引鍵。</summary>
      <returns>如果 true 包含具有指定索引鍵的項目，則為 <see cref="T:System.Collections.Generic.Dictionary`2" />，否則為 false。</returns>
      <param name="key">要在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中尋找的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsValue(`1)">
      <summary>判斷 <see cref="T:System.Collections.Generic.Dictionary`2" /> 是否包含特定值。</summary>
      <returns>如果 true 包含具有指定值的項目，則為 <see cref="T:System.Collections.Generic.Dictionary`2" />，否則為 false。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中尋找的值。參考類型的值可以是 null。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Count">
      <summary>取得 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中所包含的索引鍵/值組數目。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 中所包含的索引鍵/值組數目。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中逐一查看的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 的 <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> 結構。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Item(`0)">
      <summary>取得或設定與指定之索引鍵相關聯的值。</summary>
      <returns>與指定之索引鍵關聯的值。如果找不到指定的索引鍵，則取得作業會擲回 <see cref="T:System.Collections.Generic.KeyNotFoundException" />，且設定作業會使用指定的索引鍵建立新項目。</returns>
      <param name="key">要取得或設定之值的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">會擷取屬性，且 <paramref name="key" /> 不存在於集合中。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Keys">
      <summary>取得集合，包含 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的索引鍵。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />，包含 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的索引鍵。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Remove(`0)">
      <summary>將具有指定索引鍵的值從 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中移除。</summary>
      <returns>如果成功找到並移除項目則為 true，否則為 false。如果在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中找不到 <paramref name="key" />，則這個方法會傳回 false。</returns>
      <param name="key">要移除的項目索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>將指定的值加入具有指定索引鍵的 <see cref="T:System.Collections.Generic.ICollection`1" />。</summary>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 結構，代表要加入 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的索引鍵和值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" /> 的索引鍵為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Collections.Generic.Dictionary`2" /> 中已存在具有相同索引鍵的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>判斷 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含特定索引鍵和值。</summary>
      <returns>如果在 true 中找到 <paramref name="keyValuePair" />，則為 <see cref="T:System.Collections.Generic.ICollection`1" />，否則為 false。</returns>
      <param name="keyValuePair">要在 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 中尋找的 <see cref="T:System.Collections.Generic.ICollection`1" /> 結構。</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>從指定的陣列索引處開始，將 <see cref="T:System.Collections.Generic.ICollection`1" /> 的項目複製到 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 類型的陣列。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 類型的一維陣列，是從 <see cref="T:System.Collections.Generic.ICollection`1" /> 複製過來之 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 項目的目的地。陣列必須有以零為起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，即開始複製的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">來源 <see cref="T:System.Collections.Generic.ICollection`1" /> 項目的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>取得值，指出字典是否為唯讀。</summary>
      <returns>如果 true 是唯讀，則為 <see cref="T:System.Collections.Generic.ICollection`1" />，否則為 false。在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的預設實作中，這個屬性一律會傳回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>從字典移除索引鍵和值。</summary>
      <returns>如果成功找到並移除 <paramref name="keyValuePair" /> 所代表的索引鍵和值，則為 true，否則為 false。如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找不到 <paramref name="keyValuePair" />，則這個方法會傳回 false。</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 結構，代表要從 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中移除的索引鍵和值。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>取得 <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的索引鍵。</summary>
      <returns>
        <paramref name="TKey" /> 類型的 <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的索引鍵。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>取得 <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值。</summary>
      <returns>
        <paramref name="TValue" /> 類型的 <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" />，可用於逐一查看集合。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>取得集合，其中包含 <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> 的索引鍵。</summary>
      <returns>集合，包含 <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> 的索引鍵。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>取得集合，其中包含 <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> 的值。</summary>
      <returns>集合，包含 <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" /> 的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從指定的陣列索引處開始，將 <see cref="T:System.Collections.Generic.ICollection`1" /> 的項目複製到陣列。</summary>
      <param name="array">一維陣列，是從 <see cref="T:System.Collections.Generic.ICollection`1" /> 複製過來之項目的目的端。陣列必須有以零為起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，即開始複製的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多維的。-或-<paramref name="array" /> 沒有以零起始的索引。-或-來源 <see cref="T:System.Collections.Generic.ICollection`1" /> 項目的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。-或-來源 <see cref="T:System.Collections.Generic.ICollection`1" /> 的型別無法自動轉換為目的 <paramref name="array" /> 的型別。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果 true 的存取為同步 (安全執行緒)，則為 <see cref="T:System.Collections.ICollection" />，否則為 false。在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的預設實作中，這個屬性一律會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>取得可用以同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</summary>
      <returns>可用以同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>將指定的索引鍵和值加入字典。</summary>
      <param name="key">要用做索引鍵的物件。</param>
      <param name="value">要用做值的物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> 屬於無法指派給 <paramref name="TKey" /> 的 <see cref="T:System.Collections.Generic.Dictionary`2" /> 索引鍵類型的類型。-或-<paramref name="value" /> 是無法指派給 <paramref name="TValue" /> 的型別 (<see cref="T:System.Collections.Generic.Dictionary`2" /> 中值的型別)。-或-<see cref="T:System.Collections.Generic.Dictionary`2" /> 中已存在具有相同索引鍵的值。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>判斷 <see cref="T:System.Collections.IDictionary" /> 是否包含具有指定之索引鍵的項目。</summary>
      <returns>如果 true 包含具有指定索引鍵的項目，則為 <see cref="T:System.Collections.IDictionary" />，否則為 false。</returns>
      <param name="key">要在 <see cref="T:System.Collections.IDictionary" /> 中尋找的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>傳回 <see cref="T:System.Collections.IDictionaryEnumerator" /> 的 <see cref="T:System.Collections.IDictionary" />。</summary>
      <returns>用於 <see cref="T:System.Collections.IDictionaryEnumerator" /> 的 <see cref="T:System.Collections.IDictionary" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>取得值，指出 <see cref="T:System.Collections.IDictionary" /> 是否有固定的大小。</summary>
      <returns>如果 true 有固定大小，則為 <see cref="T:System.Collections.IDictionary" />，否則為 false。在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的預設實作中，這個屬性一律會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.IDictionary" /> 是否唯讀。</summary>
      <returns>如果 true 是唯讀，則為 <see cref="T:System.Collections.IDictionary" />，否則為 false。在 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的預設實作中，這個屬性一律會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>取得或設定具有指定之索引鍵的值。</summary>
      <returns>與指定索引鍵相關聯的值；如果 <paramref name="key" /> 不在字典中，或 <paramref name="key" /> 的類型無法指派給 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的索引鍵類型 <paramref name="TKey" />，則為 null。</returns>
      <param name="key">要取得之值的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">正在指定值，而且 <paramref name="key" /> 是無法指派給 <paramref name="TKey" /> 的 <see cref="T:System.Collections.Generic.Dictionary`2" /> 索引鍵型別的型別。-或-正在指定值，而且 <paramref name="value" /> 是無法指派給 <paramref name="TValue" /> 的 <see cref="T:System.Collections.Generic.Dictionary`2" /> 實值型別的型別。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Keys">
      <summary>取得 <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 的索引鍵。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 的索引鍵。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>將有指定索引鍵的項目從 <see cref="T:System.Collections.IDictionary" /> 移除。</summary>
      <param name="key">要移除的項目索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Values">
      <summary>取得 <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 中的值。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 中的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.TryGetValue(`0,`1@)">
      <summary>取得指定索引鍵相關聯的值。</summary>
      <returns>如果 true 包含具有指定索引鍵的項目，則為 <see cref="T:System.Collections.Generic.Dictionary`2" />，否則為 false。</returns>
      <param name="key">要取得之值的索引鍵。</param>
      <param name="value">這個方法傳回時，如果找到索引鍵，則包含與指定索引鍵相關聯的值，否則為 <paramref name="value" /> 參數類型的預設值。這個參數會以未初始化的狀態傳遞。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Values">
      <summary>取得集合，包含 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />，包含 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的值。</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值目前位置的 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的項目。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.Dispose">
      <summary>釋放 <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> 所使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.MoveNext">
      <summary>將列舉值前移至 <see cref="T:System.Collections.Generic.Dictionary`2" /> 的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於字典中列舉值目前位置的項目，做為 <see cref="T:System.Collections.DictionaryEntry" />。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>取得位於列舉值目前位置之項目的索引鍵。</summary>
      <returns>位於字典中列舉值目前位置之項目的索引鍵。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>取得位於列舉值目前位置之項目的值。</summary>
      <returns>位於字典中列舉值目前位置之項目的值。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於集合中列舉值目前位置的項目，做為 <see cref="T:System.Object" />。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection">
      <summary>表示 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的索引鍵集合。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 類別的新執行個體，反映指定 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的索引鍵。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.Dictionary`2" />，其索引鍵反映在新 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>從指定的陣列索引處開始，複製 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 項目至現有一維 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 複製過來之元素的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。</exception>
      <exception cref="T:System.ArgumentException">來源 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 元素的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Count">
      <summary>取得 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 中所包含的元素數。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 中所包含的項目數。擷取這個屬性的值是一種 O(1) 運算。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 中逐一查看的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>將項目加入至 <see cref="T:System.Collections.Generic.ICollection`1" />。這個實作永遠都會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <param name="item">要加入 <see cref="T:System.Collections.Generic.ICollection`1" /> 的物件。</param>
      <exception cref="T:System.NotSupportedException">一定會擲回。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>將所有項目從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除。這個實作永遠都會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <exception cref="T:System.NotSupportedException">一定會擲回。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>判斷 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含特定值。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找到 <paramref name="item" />，則為 true，否則為 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中尋找的物件。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 是唯讀的則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的預設實作中，這個屬性永遠會傳回 true。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除特定物件的第一個相符項目。這個實作永遠都會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <returns>如果 <paramref name="item" /> 已成功從 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除，則為 true，否則為 false。如果在原始的 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找不到 <paramref name="item" />，則這個方法也會傳回 false。</returns>
      <param name="item">要從 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除的物件。</param>
      <exception cref="T:System.NotSupportedException">一定會擲回。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回會逐一查看集合的列舉程式。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" />，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從特定的 <see cref="T:System.Array" /> 索引開始，複製 <see cref="T:System.Collections.ICollection" /> 項目至 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.ICollection" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 為多維。-或-<paramref name="array" /> 沒有以零起始的索引。-或-來源 <see cref="T:System.Collections.ICollection" /> 項目的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。-或-來源 <see cref="T:System.Collections.ICollection" /> 的型別無法自動轉換為目的 <paramref name="array" /> 的型別。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Collections.ICollection" /> 的存取為同步 (安全執行緒)，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</summary>
      <returns>可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。在 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的預設實作中，這個屬性永遠會傳回目前的執行個體。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回會逐一查看集合的列舉程式。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，可用於逐一查看集合。</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Current">
      <summary>取得位於目前列舉值位置的項目。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 中位於目前列舉值位置的項目。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>釋放 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" /> 所使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>將列舉值前移至 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的下一個項目。</summary>
      <returns>如果列舉值成功前移至下一個項目，則為 true；如果列舉值超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置的項目。</summary>
      <returns>位於列舉值中目前位置的集合中的元素。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個項目之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection">
      <summary>表示 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的值集合。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 類別的新執行個體，反映指定 <see cref="T:System.Collections.Generic.Dictionary`2" /> 中的值。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.Dictionary`2" />，其值反映在新 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>從指定的陣列索引處開始，複製 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 項目至現有一維 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 複製過來之元素的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。</exception>
      <exception cref="T:System.ArgumentException">來源 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 元素的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Count">
      <summary>取得 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 中所包含的元素數。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 中所包含的項目數。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 中逐一查看的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>將項目加入至 <see cref="T:System.Collections.Generic.ICollection`1" />。這個實作永遠都會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <param name="item">要加入 <see cref="T:System.Collections.Generic.ICollection`1" /> 的物件。</param>
      <exception cref="T:System.NotSupportedException">一定會擲回。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>將所有項目從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除。這個實作永遠都會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <exception cref="T:System.NotSupportedException">一定會擲回。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>判斷 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含特定值。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找到 <paramref name="item" />，則為 true，否則為 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中尋找的物件。</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 是唯讀的則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的預設實作中，這個屬性永遠會傳回 true。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除特定物件的第一個相符項目。這個實作永遠都會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <returns>如果 <paramref name="item" /> 已成功從 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除，則為 true，否則為 false。如果在原始的 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找不到 <paramref name="item" />，則這個方法也會傳回 false。</returns>
      <param name="item">要從 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除的物件。</param>
      <exception cref="T:System.NotSupportedException">一定會擲回。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回會逐一查看集合的列舉程式。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" />，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從特定的 <see cref="T:System.Array" /> 索引開始，複製 <see cref="T:System.Collections.ICollection" /> 項目至 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.ICollection" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 為多維。-或-<paramref name="array" /> 沒有以零起始的索引。-或-來源 <see cref="T:System.Collections.ICollection" /> 項目的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。-或-來源 <see cref="T:System.Collections.ICollection" /> 的型別無法自動轉換為目的 <paramref name="array" /> 的型別。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Collections.ICollection" /> 的存取為同步 (安全執行緒)，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</summary>
      <returns>可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。在 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的預設實作中，這個屬性永遠會傳回目前的執行個體。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回會逐一查看集合的列舉程式。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，可用於逐一查看集合。</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值目前位置的 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 中的項目。</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>釋放 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" /> 所使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>將列舉值往前推進至 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值中目前位置的集合中的元素。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Collections.Generic.EqualityComparer`1">
      <summary>提供基底類別 (Base Class) 用於 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 泛型介面的實作。</summary>
      <typeparam name="T">要比較之物件的類型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Collections.Generic.EqualityComparer`1.Default">
      <summary>傳回泛型引數指定之類型的預設相等比較子 (Comparer)。</summary>
      <returns>類型 <paramref name="T" /> 之 <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 類別的預設執行個體。</returns>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.Equals(`0,`0)">
      <summary>在衍生類別中覆寫時，判斷類型為 <paramref name="T" /> 的兩個物件是否相等。</summary>
      <returns>如果指定的物件相等則為 true，否則為 false。</returns>
      <param name="x">要比較的第一個物件。</param>
      <param name="y">要比較的第二個物件。</param>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.GetHashCode(`0)">
      <summary>在衍生類別中覆寫時，做為雜湊演算法和資料結構之指定物件的雜湊函式，例如雜湊表。</summary>
      <returns>指定物件的雜湊碼。</returns>
      <param name="obj">要取得其雜湊碼的物件。</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>判斷指定的物件是否相等。</summary>
      <returns>如果指定的物件相等則為 true，否則為 false。</returns>
      <param name="x">要比較的第一個物件。</param>
      <param name="y">要比較的第二個物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> or <paramref name="y" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>傳回指定物件的雜湊碼。</summary>
      <returns>指定物件的雜湊碼。</returns>
      <param name="obj">要傳回雜湊碼的 <see cref="T:System.Object" />。</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.-or-<paramref name="obj" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1">
      <summary>表示一組值。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <typeparam name="T">雜湊集中項目的類型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.HashSet`1" /> 類別的新執行個體，這個執行個體是空白的，並使用集合類型的預設相等比較子。</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.HashSet`1" /> 類別的新執行個體，這個執行個體使用集合類型的預設相等比較子、包含從指定之集合複製的項目，並具有足以容納所複製項目數的容量。</summary>
      <param name="collection">集合 (Collection)，其項目會複製到新的集合 (Set)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.HashSet`1" /> 類別的新執行個體，這個執行個體使用集合類型的指定相等比較子、包含從指定之集合複製的項目，並具有足以容納所複製項目數的容量。</summary>
      <param name="collection">集合 (Collection)，其項目會複製到新的集合 (Set)。</param>
      <param name="comparer">在集合中比較各個值時所要使用的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 實作；若要針對集合類型使用預設 <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 實作，則為 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.HashSet`1" /> 類別的新執行個體，這個執行個體是空白的，並使用集合類型的指定相等比較子。</summary>
      <param name="comparer">在集合中比較各個值時所要使用的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 實作；若要針對集合類型使用預設 <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 實作，則為 null。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Add(`0)">
      <summary>將指定的項目加入至集合。</summary>
      <returns>如果將項目加入 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件，則為 true；如果項目已存在，則為 false。</returns>
      <param name="item">要加入至集合的項目。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Clear">
      <summary>將所有項目從 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件中移除。</summary>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Comparer">
      <summary>取得 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 物件，這個物件可用來判斷集合中的值是否相等。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 物件，可用來判斷集合中的值是否相等。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Contains(`0)">
      <summary>判斷 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件是否包含指定的項目。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件包含指定的項目，則為 true，否則為 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件中尋找的項目。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[])">
      <summary>將 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件的項目複製到陣列。</summary>
      <param name="array">一維陣列，這是從 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件複製之項目的目的地。陣列必須有以零為起始的索引。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32)">
      <summary>從指定的陣列索引開始，將 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件的項目複製到陣列。</summary>
      <param name="array">一維陣列，這是從 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件複製之項目的目的地。陣列必須有以零為起始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零起始的索引，即開始複製的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> 大於目的端 <paramref name="array" /> 的長度。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>從指定的陣列索引開始，將 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件的指定項目數複製到陣列。</summary>
      <param name="array">一維陣列，這是從 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件複製之項目的目的地。陣列必須有以零為起始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零起始的索引，即開始複製的位置。</param>
      <param name="count">要複製到 <paramref name="array" /> 的項目數。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小於 0。-或-<paramref name="count" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> 大於目的端 <paramref name="array" /> 的長度。-或-<paramref name="count" /> 大於 <paramref name="index" /> 和目的端 <paramref name="array" /> 結尾之間的可用空間。</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Count">
      <summary>取得集合中包含的項目數。</summary>
      <returns>集合中包含的項目數。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>將指定集合中的所有項目從目前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件中移除。</summary>
      <param name="other">要從 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件中移除的項目集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.GetEnumerator">
      <summary>傳回會逐一查看 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 物件的 <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> 物件。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改目前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件，以便僅包含該物件和指定之集合中同時出現的項目。</summary>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件是否為指定之集合的真子集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件是 <paramref name="other" /> 的真子集，則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件是否為指定之集合的真超集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件是 <paramref name="other" /> 的真超集，則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件是否為指定之集合的子集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件是 <paramref name="other" /> 的子集，則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件是否為指定之集合的超集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件是 <paramref name="other" /> 的超集，則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷目前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件與指定的集合是否共用通用項目。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件和 <paramref name="other" /> 至少共用一個通用項目，則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Remove(`0)">
      <summary>將指定的項目從 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件中移除。</summary>
      <returns>如果成功找到並移除項目則為 true，否則為 false。如果在 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件中找不到 <paramref name="item" />，則這個方法會傳回 false。</returns>
      <param name="item">要移除的項目。</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>將符合指定述詞所定義之條件的所有項目從 <see cref="T:System.Collections.Generic.HashSet`1" /> 集合中移除。</summary>
      <returns>已從 <see cref="T:System.Collections.Generic.HashSet`1" /> 集合中移除的項目數。</returns>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委派，定義要移除項目的條件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件和指定的集合是否包含相同項目。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件等於 <paramref name="other" />，則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改目前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件，以便僅包含該物件或指定之集合 (但非兩者) 中出現的項目。</summary>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>將項目加入 <see cref="T:System.Collections.Generic.ICollection`1" /> 物件。</summary>
      <param name="item">要加入 <see cref="T:System.Collections.Generic.ICollection`1" /> 物件的物件。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 是唯讀的。</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>取得值，指出集合是否為唯讀。</summary>
      <returns>如果集合是唯讀，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> 物件，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> 物件，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.TrimExcess">
      <summary>將 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件的容量設定為所包含之項目的實際數目，已四捨五入為鄰近的實作特定值。</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改目前的 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件，以包含該物件和 (或) 指定之集合中出現的所有項目。</summary>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.HashSet`1" /> 物件的項目。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值中目前位置之 <see cref="T:System.Collections.Generic.HashSet`1" /> 集合內的項目。</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.Dispose">
      <summary>釋放由 <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> 物件使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.MoveNext">
      <summary>讓列舉程式前進至 <see cref="T:System.Collections.Generic.HashSet`1" /> 集合中的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於集合中列舉值目前位置的項目，做為 <see cref="T:System.Object" />。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1">
      <summary>表示雙向連結串列 (Doubly-Linked List)。</summary>
      <typeparam name="T">指定連結串列的元素類型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.LinkedList`1" /> 類別的新執行個體，這個執行個體是空白的。</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.LinkedList`1" /> 類別的新執行個體，其包含從指定之 <see cref="T:System.Collections.IEnumerable" /> 複製的元素，且具有容納複製之元素數目的足夠容量。</summary>
      <param name="collection">
        <see cref="T:System.Collections.IEnumerable" />，要將其元素複製至新的 <see cref="T:System.Collections.Generic.LinkedList`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中指定的現有節點後加入指定的新節點。</summary>
      <param name="node">要在其後插入 <paramref name="newNode" /> 的 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="newNode">要加入至 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 為 null。-或-<paramref name="newNode" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 不在目前的 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。-或-<paramref name="newNode" /> 屬於另一個 <see cref="T:System.Collections.Generic.LinkedList`1" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中指定的現有節點後加入包含指定值的新節點。</summary>
      <returns>包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedListNode`1" />，要在其後插入包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="value">要加入至 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 不在目前的 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中指定的現有節點前加入指定的新節點。</summary>
      <param name="node">要在其之前插入 <paramref name="newNode" /> 的 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="newNode">要加入至 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 為 null。-或-<paramref name="newNode" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 不在目前的 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。-或-<paramref name="newNode" /> 屬於另一個 <see cref="T:System.Collections.Generic.LinkedList`1" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中指定的現有節點前加入包含指定值的新節點。</summary>
      <returns>包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedListNode`1" />，要在其之前插入包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <param name="value">要加入至 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 不在目前的 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(System.Collections.Generic.LinkedListNode{`0})">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的開頭加入指定的新節點。</summary>
      <param name="node">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 開頭加入的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 屬於另一個 <see cref="T:System.Collections.Generic.LinkedList`1" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(`0)">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的開頭加入包含指定值的新節點。</summary>
      <returns>包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 開頭加入的值。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(System.Collections.Generic.LinkedListNode{`0})">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的結尾加入指定的新節點。</summary>
      <param name="node">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 結尾加入的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 屬於另一個 <see cref="T:System.Collections.Generic.LinkedList`1" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(`0)">
      <summary>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的結尾加入包含指定值的新節點。</summary>
      <returns>包含 <paramref name="value" /> 的新 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 結尾加入的值。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Clear">
      <summary>從 <see cref="T:System.Collections.Generic.LinkedList`1" /> 移除所有節點。</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Contains(`0)">
      <summary>判斷值是否在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中找到 <paramref name="value" />，則為 true，否則為 false。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中尋找的值。參考型別的值可以是 null。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.CopyTo(`0[],System.Int32)">
      <summary>從目標陣列的指定索引開始，複製整個 <see cref="T:System.Collections.Generic.LinkedList`1" /> 至相容的一維 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.Generic.LinkedList`1" /> 複製過來之元素的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。</exception>
      <exception cref="T:System.ArgumentException">來源 <see cref="T:System.Collections.Generic.LinkedList`1" /> 元素的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Count">
      <summary>取得在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中實際包含的節點數。</summary>
      <returns>在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中實際包含的節點數。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Find(`0)">
      <summary>尋找包含指定值的第一個節點。</summary>
      <returns>如果找到，則為包含指定值的第一個 <see cref="T:System.Collections.Generic.LinkedListNode`1" />，否則為 null。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中尋找的值。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.FindLast(`0)">
      <summary>尋找包含指定值的最後一個節點。</summary>
      <returns>如果找到，則為包含指定值的最後一個 <see cref="T:System.Collections.Generic.LinkedListNode`1" />，否則為 null。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中尋找的值。</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.First">
      <summary>取得 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的第一個節點。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 的第一個 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中逐一查看的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 的 <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Last">
      <summary>取得 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的最後一個節點。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 的最後一個 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(System.Collections.Generic.LinkedListNode{`0})">
      <summary>從 <see cref="T:System.Collections.Generic.LinkedList`1" /> 移除指定的節點。</summary>
      <param name="node">要從 <see cref="T:System.Collections.Generic.LinkedList`1" /> 移除的 <see cref="T:System.Collections.Generic.LinkedListNode`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> 不在目前的 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(`0)">
      <summary>從 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中移除第一次出現的指定值。</summary>
      <returns>如果包含 <paramref name="value" /> 的元素已成功移除則為 true，否則為 false。如果在原始的 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中找不到 <paramref name="value" />，則這個方法也會傳回 false。</returns>
      <param name="value">要從 <see cref="T:System.Collections.Generic.LinkedList`1" /> 移除的值。</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveFirst">
      <summary>移除 <see cref="T:System.Collections.Generic.LinkedList`1" /> 開頭的節點。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 是空的。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveLast">
      <summary>移除 <see cref="T:System.Collections.Generic.LinkedList`1" /> 結尾的節點。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 是空的。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>將項目加入至 <see cref="T:System.Collections.Generic.ICollection`1" /> 的結尾。</summary>
      <param name="value">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 結尾加入的值。</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 是唯讀的則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回會逐一查看集合的列舉程式。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" />，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從特定的 <see cref="T:System.Array" /> 索引開始，複製 <see cref="T:System.Collections.ICollection" /> 項目至 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.ICollection" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 為多維。-或-<paramref name="array" /> 沒有以零起始的索引。-或-來源 <see cref="T:System.Collections.ICollection" /> 項目的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。-或-來源 <see cref="T:System.Collections.ICollection" /> 的型別無法自動轉換為目的 <paramref name="array" /> 的型別。</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Collections.ICollection" /> 的存取為同步 (安全執行緒)，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</summary>
      <returns>可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。在 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的預設實作中，這個屬性永遠會傳回目前的執行個體。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回做為集合逐一查看連結串列的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，可用來做為集合逐一查看連結串列。</returns>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值目前位置的 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中的項目。</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.Dispose">
      <summary>釋放 <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" /> 所使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.MoveNext">
      <summary>將列舉值前移至 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值中目前位置的集合中的元素。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。此類別無法被繼承。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedListNode`1">
      <summary>表示 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中的節點。此類別無法被繼承。</summary>
      <typeparam name="T">指定連結串列的元素類型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedListNode`1.#ctor(`0)">
      <summary>初始化 <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 類別 (含有指定的值) 的新執行個體。</summary>
      <param name="value">包含於 <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 中的值。</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.List">
      <summary>取得 <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 所屬的 <see cref="T:System.Collections.Generic.LinkedList`1" />。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 所屬之 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的參考；如果未連結 <see cref="T:System.Collections.Generic.LinkedListNode`1" />，則為 null。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Next">
      <summary>取得 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中的下一個節點。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 中下一個節點的參考，如果目前節點是 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的最後一個項目 (<see cref="P:System.Collections.Generic.LinkedList`1.Last" />)，則為 null。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Previous">
      <summary>取得 <see cref="T:System.Collections.Generic.LinkedList`1" /> 中的前一個節點。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" /> 中上一個節點的參考，如果目前節點是 <see cref="T:System.Collections.Generic.LinkedList`1" /> 的第一個項目 (<see cref="P:System.Collections.Generic.LinkedList`1.First" />)，則為 null。</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Value">
      <summary>取得包含於節點中的值。</summary>
      <returns>包含於節點中的值。</returns>
    </member>
    <member name="T:System.Collections.Generic.List`1">
      <summary>表示可以依照索引存取的強類型物件清單。提供搜尋、排序和管理清單的方法。若要瀏覽此類型的.NET Framework 原始碼，請參閱參考來源。</summary>
      <typeparam name="T">清單中項目的類型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.List`1" /> 類別的新執行個體，其為空白執行個體且具有預設的初始容量。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.List`1" /> 類別的新執行個體，其包含從指定之集合複製的元素，且具有容納複製之元素數目的足夠容量。</summary>
      <param name="collection">將其項目複製到新清單的來源集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Collections.Generic.List`1" /> 類別的新執行個體，這個執行個體是空白且可具有指定的初始容量。</summary>
      <param name="capacity">新清單一開始能夠儲存的項目數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> 小於 0。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Add(`0)">
      <summary>將物件加入至 <see cref="T:System.Collections.Generic.List`1" /> 的結尾。</summary>
      <param name="item">要加入至 <see cref="T:System.Collections.Generic.List`1" /> 結尾的物件。參考類型的值可以是 null。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>將特定集合的元素加入至 <see cref="T:System.Collections.Generic.List`1" /> 的結尾。</summary>
      <param name="collection">集合，其元素應加入至 <see cref="T:System.Collections.Generic.List`1" /> 的結尾。集合本身不能是 null，但它可以包含 null 的元素，如果型別 <paramref name="T" /> 是參考型別。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.AsReadOnly">
      <summary>傳回目前集合的唯讀 <see cref="T:System.Collections.Generic.IList`1" /> 包裝函式。</summary>
      <returns>
        <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />，做為目前 <see cref="T:System.Collections.Generic.List`1" /> 的唯讀包裝函式。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>使用指定的比較子在已經過排序之 <see cref="T:System.Collections.Generic.List`1" /> 內某段項目範圍搜尋指定的元素，並傳回該元素的索引 (從零開始起算)。</summary>
      <returns>如果有找到 <paramref name="item" />，則為已排序的 <see cref="T:System.Collections.Generic.List`1" /> 中 <paramref name="item" /> 之以零起始的索引，否則便為負數，此負數為大於 <paramref name="item" /> 的下一個元素索引之位元補數，或者，如果沒有更大的元素，則為 <see cref="P:System.Collections.Generic.List`1.Count" /> 的位元補數。</returns>
      <param name="index">要搜尋範圍內之以零為起始的起始索引。</param>
      <param name="count">搜尋範圍的長度。</param>
      <param name="item">要尋找的物件。參考類型的值可以是 null。</param>
      <param name="comparer">比較項目時要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作，或 null 表示使用預設比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。-或-<paramref name="count" /> 小於 0。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 和 <paramref name="count" /> 並不代表 <see cref="T:System.Collections.Generic.List`1" /> 中的有效範圍。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> 為 null，並且預設比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <see cref="T:System.IComparable`1" /> 泛型介面的實作或型別 <paramref name="T" /> 的 <see cref="T:System.IComparable" /> 介面。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0)">
      <summary>使用預設的比較子 (Comparer) 並傳回元素以零起始的索引，來搜尋元素之整個排序的 <see cref="T:System.Collections.Generic.List`1" />。</summary>
      <returns>如果有找到 <paramref name="item" />，則為已排序的 <see cref="T:System.Collections.Generic.List`1" /> 中 <paramref name="item" /> 之以零起始的索引，否則便為負數，此負數為大於 <paramref name="item" /> 的下一個元素索引之位元補數，或者，如果沒有更大的元素，則為 <see cref="P:System.Collections.Generic.List`1.Count" /> 的位元補數。</returns>
      <param name="item">要尋找的物件。參考類型的值可以是 null。</param>
      <exception cref="T:System.InvalidOperationException">預設比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <see cref="T:System.IComparable`1" /> 泛型介面的實作或型別 <paramref name="T" /> 的 <see cref="T:System.IComparable" /> 介面。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>使用指定的比較子並傳回元素以零起始的索引，來搜尋元素之整個排序的 <see cref="T:System.Collections.Generic.List`1" />。</summary>
      <returns>如果有找到 <paramref name="item" />，則為已排序的 <see cref="T:System.Collections.Generic.List`1" /> 中 <paramref name="item" /> 之以零起始的索引，否則便為負數，此負數為大於 <paramref name="item" /> 的下一個元素索引之位元補數，或者，如果沒有更大的元素，則為 <see cref="P:System.Collections.Generic.List`1.Count" /> 的位元補數。</returns>
      <param name="item">要尋找的物件。參考類型的值可以是 null。</param>
      <param name="comparer">比較元素時所要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作。-或-null 表示使用預設比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" />。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> 為 null，並且預設比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <see cref="T:System.IComparable`1" /> 泛型介面的實作或型別 <paramref name="T" /> 的 <see cref="T:System.IComparable" /> 介面。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Capacity">
      <summary>在不需要調整大小之下，取得或設定內部資料結構可以保存的項目總數。</summary>
      <returns>需要調整大小之前，<see cref="T:System.Collections.Generic.List`1" /> 可包含的元素數目。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.List`1.Capacity" /> 是設為小於 <see cref="P:System.Collections.Generic.List`1.Count" /> 的值。</exception>
      <exception cref="T:System.OutOfMemoryException">系統上沒有足夠的可用記憶體。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Clear">
      <summary>將所有元素從 <see cref="T:System.Collections.Generic.List`1" /> 移除。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Contains(`0)">
      <summary>判斷某元素是否在 <see cref="T:System.Collections.Generic.List`1" /> 中。</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.List`1" />; otherwise, false.</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中尋找的物件。參考類型的值可以是 null。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>從目標陣列的指定索引處開始，將元素範圍從 <see cref="T:System.Collections.Generic.List`1" /> 複製到相容的一維陣列。</summary>
      <param name="index">來源 <see cref="T:System.Collections.Generic.List`1" /> 中以零起始的索引，位於複製開始的位置。</param>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.Generic.List`1" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <param name="count">要複製的項目數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。-或-<paramref name="arrayIndex" /> 小於 0。-或-<paramref name="count" /> 小於 0。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 等於或大於來源 <see cref="T:System.Collections.Generic.List`1" /> 的 <see cref="P:System.Collections.Generic.List`1.Count" />。-或-從 <paramref name="index" /> 到來源 <see cref="T:System.Collections.Generic.List`1" /> 末尾的元素數目，超過從 <paramref name="arrayIndex" /> 到目的 <paramref name="array" /> 末尾之間的可用空間。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[])">
      <summary>將整個 <see cref="T:System.Collections.Generic.List`1" /> 複製到相容的一維陣列，從目標陣列的開頭開始。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.Generic.List`1" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">來源 <see cref="T:System.Collections.Generic.List`1" /> 中的元素數目大於目的 <paramref name="array" /> 可包含的元素數目。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[],System.Int32)">
      <summary>從目標陣列的指定索引處開始，將整個 <see cref="T:System.Collections.Generic.List`1" /> 複製到相容的一維陣列中。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.Generic.List`1" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">來源 <see cref="T:System.Collections.Generic.List`1" /> 項目的數量大於從 <paramref name="arrayIndex" /> 到目的 <paramref name="array" /> 結尾的可用空間。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Count">
      <summary>取得 <see cref="T:System.Collections.Generic.List`1" /> 中所包含的元素數。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> 中所包含的項目數。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Exists(System.Predicate{`0})">
      <summary>判斷 <see cref="T:System.Collections.Generic.List`1" /> 是否包含符合指定之述詞 (Predicate) 所定義之條件的元素。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.List`1" /> 包含的一個或多個元素符合指定之述詞所定義的條件，則為 true，否則為 false。</returns>
      <param name="match">定義要搜尋元素之條件的 <see cref="T:System.Predicate`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Find(System.Predicate{`0})">
      <summary>搜尋符合指定之述詞所定義的條件之元素，並傳回整個 <see cref="T:System.Collections.Generic.List`1" /> 內第一個相符的項目。</summary>
      <returns>第一個符合指定之述詞所定義的條件之元素 (如果找到的話)，否則為型別 <paramref name="T" /> 的預設值。</returns>
      <param name="match">定義要搜尋元素之條件的 <see cref="T:System.Predicate`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindAll(System.Predicate{`0})">
      <summary>擷取符合指定之述詞所定義的條件之所有項目。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" />，其中包含符合指定之述詞所定義的條件之所有元素 (如果有找到的話)，否則為空的 <see cref="T:System.Collections.Generic.List`1" />。</returns>
      <param name="match">定義要搜尋元素之條件的 <see cref="T:System.Predicate`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>搜尋符合指定之述詞所定義的條件之元素，並傳回 <see cref="T:System.Collections.Generic.List`1" /> 中從指定之索引開始，且包含指定之元素數目的元素範圍內第一個符合項目之以零起始的索引。</summary>
      <returns>如果有找到，是第一個符合 <paramref name="match" /> 定義條件的元素，否則為 -1。</returns>
      <param name="startIndex">搜尋之以零為起始的起始索引。</param>
      <param name="count">區段中要搜尋的項目數目。</param>
      <param name="match">定義要搜尋元素之條件的 <see cref="T:System.Predicate`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 是在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引範圍之外。-或-<paramref name="count" /> 小於 0。-或-<paramref name="startIndex" /> 和 <paramref name="count" /> 並未指定 <see cref="T:System.Collections.Generic.List`1" /> 中的有效區段。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>搜尋符合指定之述詞所定義的條件之元素，並傳回 <see cref="T:System.Collections.Generic.List`1" /> 中從指定之索引延伸到最後一個元素的元素範圍內第一個符合項目之以零起始的索引。</summary>
      <returns>如果有找到，是第一個符合 <paramref name="match" /> 定義條件的元素，否則為 -1。</returns>
      <param name="startIndex">搜尋之以零為起始的起始索引。</param>
      <param name="match">定義要搜尋元素之條件的 <see cref="T:System.Predicate`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 是在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引範圍之外。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Predicate{`0})">
      <summary>搜尋符合指定之述詞所定義的條件之元素，並傳回整個 <see cref="T:System.Collections.Generic.List`1" /> 內第一個相符項目之以零起始的索引。</summary>
      <returns>如果有找到，是第一個符合 <paramref name="match" /> 定義條件的元素，否則為 -1。</returns>
      <param name="match">定義要搜尋元素之條件的 <see cref="T:System.Predicate`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLast(System.Predicate{`0})">
      <summary>搜尋符合指定之述詞所定義的條件之元素，並傳回整個 <see cref="T:System.Collections.Generic.List`1" /> 內最後一個相符的項目。</summary>
      <returns>最後一個符合指定之述詞所定義的條件之元素 (如果找到的話)，否則為型別 <paramref name="T" /> 的預設值。</returns>
      <param name="match">定義要搜尋元素之條件的 <see cref="T:System.Predicate`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>搜尋符合指定之述詞所定義的條件之元素，並傳回 <see cref="T:System.Collections.Generic.List`1" /> 中包含指定之元素數目，且結束於指定之索引的元素範圍內最後一個符合項目之以零起始的索引。</summary>
      <returns>如果有找到，是最後一個符合 <paramref name="match" /> 定義條件的元素，否則為 -1。</returns>
      <param name="startIndex">向後搜尋之以零為起始的起始索引。</param>
      <param name="count">區段中要搜尋的項目數目。</param>
      <param name="match">定義要搜尋元素之條件的 <see cref="T:System.Predicate`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 是在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引範圍之外。-或-<paramref name="count" /> 小於 0。-或-<paramref name="startIndex" /> 和 <paramref name="count" /> 並未指定 <see cref="T:System.Collections.Generic.List`1" /> 中的有效區段。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>搜尋符合指定之述詞所定義的條件之元素，並傳回 <see cref="T:System.Collections.Generic.List`1" /> 中從第一個元素延伸到指定之索引的元素範圍內最後一個符合項目之以零起始的索引。</summary>
      <returns>如果有找到，是最後一個符合 <paramref name="match" /> 定義條件的元素，否則為 -1。</returns>
      <param name="startIndex">向後搜尋之以零為起始的起始索引。</param>
      <param name="match">定義要搜尋元素之條件的 <see cref="T:System.Predicate`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 是在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引範圍之外。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Predicate{`0})">
      <summary>搜尋符合指定之述詞所定義的條件之元素，並傳回整個 <see cref="T:System.Collections.Generic.List`1" /> 內最後一個相符項目之以零起始的索引。</summary>
      <returns>如果有找到，是最後一個符合 <paramref name="match" /> 定義條件的元素，否則為 -1。</returns>
      <param name="match">定義要搜尋元素之條件的 <see cref="T:System.Predicate`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ForEach(System.Action{`0})">
      <summary>在 <see cref="T:System.Collections.Generic.List`1" /> 的每一個項目上執行指定之動作。</summary>
      <param name="action">要在 <see cref="T:System.Collections.Generic.List`1" /> 的每一個項目上執行的 <see cref="T:System.Action`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Collections.Generic.List`1" /> 中逐一查看的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> 的 <see cref="T:System.Collections.Generic.List`1.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetRange(System.Int32,System.Int32)">
      <summary>為來源 <see cref="T:System.Collections.Generic.List`1" /> 中的項目範圍建立淺層複本。</summary>
      <returns>來源 <see cref="T:System.Collections.Generic.List`1" /> 中項目範圍的淺層複本。</returns>
      <param name="index">範圍起始處之以零起始的 <see cref="T:System.Collections.Generic.List`1" /> 索引。</param>
      <param name="count">範圍中的項目數。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。-或-<paramref name="count" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 和 <paramref name="count" /> 並不代表 <see cref="T:System.Collections.Generic.List`1" /> 中元素的有效範圍。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0)">
      <summary>搜尋指定的物件，並傳回整個 <see cref="T:System.Collections.Generic.List`1" /> 中第一個相符項目之以零起始的索引。</summary>
      <returns>The zero-based index of the first occurrence of <paramref name="item" /> within the entire <see cref="T:System.Collections.Generic.List`1" />, if found; otherwise, –1.</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中尋找的物件。參考類型的值可以是 null。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32)">
      <summary>在 <see cref="T:System.Collections.Generic.List`1" /> 中從指定的索引開始到最後一個項目這段範圍內，搜尋指定的物件第一次出現的位置，並傳回其索引值 (索引以零起始)。</summary>
      <returns>在 <see cref="T:System.Collections.Generic.List`1" /> 中從 <paramref name="index" /> 開始到最後一個項目這段範圍內，如果有找到指定的 <paramref name="item" /> 第一次出現的位置，則為該位置的索引 (從零開始起算)，否則為 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中尋找的物件。參考類型的值可以是 null。</param>
      <param name="index">搜尋之以零為起始的起始索引。0 (零) 在空白清單中有效。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 是在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引範圍之外。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32,System.Int32)">
      <summary>在 <see cref="T:System.Collections.Generic.List`1" /> 中從指定索引開始且包含指定個數項目的範圍內，搜尋指定的物件第一次出現的位置，並傳回其索引值 (索引以零起始)。</summary>
      <returns>在 <see cref="T:System.Collections.Generic.List`1" /> 中從 <paramref name="index" /> 開始且包含 <paramref name="count" /> 個項目的範圍內，如果有找到指定的 <paramref name="item" /> 第一次出現的位置，則為該位置的索引 (從零開始起算)，否則為 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中尋找的物件。參考類型的值可以是 null。</param>
      <param name="index">搜尋之以零為起始的起始索引。0 (零) 在空白清單中有效。</param>
      <param name="count">區段中要搜尋的項目數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 是在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引範圍之外。-或-<paramref name="count" /> 小於 0。-或-<paramref name="index" /> 和 <paramref name="count" /> 並未指定 <see cref="T:System.Collections.Generic.List`1" /> 中的有效區段。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Insert(System.Int32,`0)">
      <summary>將項目插入 <see cref="T:System.Collections.Generic.List`1" /> 中指定的索引處。</summary>
      <param name="index">應該插入 <paramref name="item" /> 之以零起始的索引。</param>
      <param name="item">要插入的物件。參考類型的值可以是 null。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。-或-<paramref name="index" /> 大於 <see cref="P:System.Collections.Generic.List`1.Count" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>將集合的元素插入至位於指定索引的 <see cref="T:System.Collections.Generic.List`1" /> 中。</summary>
      <param name="index">應插入新項目處的以零為起始的索引。</param>
      <param name="collection">集合，其項目應插入至 <see cref="T:System.Collections.Generic.List`1" />。集合本身不能是 null，但它可以包含 null 的元素，如果型別 <paramref name="T" /> 是參考型別。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。-或-<paramref name="index" /> 大於 <see cref="P:System.Collections.Generic.List`1.Count" />。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Item(System.Int32)">
      <summary>在指定的索引位置上取得或設定項目。</summary>
      <returns>在指定索引上的項目。</returns>
      <param name="index">要取得或設定之以零為起始的項目索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。-或-<paramref name="index" /> 等於或大於 <see cref="P:System.Collections.Generic.List`1.Count" />。 </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0)">
      <summary>搜尋指定的物件，並傳回整個 <see cref="T:System.Collections.Generic.List`1" /> 中最後一個相符項目之以零起始的索引。</summary>
      <returns>如果找到的話，則為在整個 <see cref="T:System.Collections.Generic.List`1" /> 中 <paramref name="item" /> 的最後一個符合元素之以零起始的索引，否則為 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中尋找的物件。參考類型的值可以是 null。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32)">
      <summary>在 <see cref="T:System.Collections.Generic.List`1" /> 中從第一個項目開始到指定的索引這段範圍內，搜尋指定的物件最後一次出現的位置，並傳回其索引值 (索引以零起始)。</summary>
      <returns>在 <see cref="T:System.Collections.Generic.List`1" /> 中從第一個項目開始到 <paramref name="index" /> 這段範圍內，如果有找到指定的 <paramref name="item" /> 最後一次出現的位置，則為該位置的索引 (從零開始起算)，否則為 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中尋找的物件。參考類型的值可以是 null。</param>
      <param name="index">向後搜尋之以零為起始的起始索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 是在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引範圍之外。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>在 <see cref="T:System.Collections.Generic.List`1" /> 中包含指定個數項目且結尾位於指定索引的範圍內，搜尋指定的物件最後一次出現的位置，並傳回其索引值 (索引以零起始)。</summary>
      <returns>在 <see cref="T:System.Collections.Generic.List`1" /> 中含有 <paramref name="count" /> 個項目且結尾位置為 <paramref name="index" /> 的範圍內，如果有找到指定的 <paramref name="item" /> 最後一次出現的位置，則為該位置的索引 (索引以零起始)，否則為 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.List`1" /> 中尋找的物件。參考類型的值可以是 null。</param>
      <param name="index">向後搜尋之以零為起始的起始索引。</param>
      <param name="count">區段中要搜尋的項目數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 是在 <see cref="T:System.Collections.Generic.List`1" /> 的有效索引範圍之外。-或-<paramref name="count" /> 小於 0。-或-<paramref name="index" /> 和 <paramref name="count" /> 並未指定 <see cref="T:System.Collections.Generic.List`1" /> 中的有效區段。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Remove(`0)">
      <summary>從 <see cref="T:System.Collections.Generic.List`1" /> 移除特定物件的第一個相符項目。</summary>
      <returns>如果成功移除 <paramref name="item" /> 則為 true，否則為 false。This method also returns false if <paramref name="item" /> was not found in the <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="item">要從 <see cref="T:System.Collections.Generic.List`1" /> 中移除的物件。參考類型的值可以是 null。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAll(System.Predicate{`0})">
      <summary>移除符合指定的述詞所定義之條件的所有項目。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" /> 中已移除的項目數。</returns>
      <param name="match">定義要移除項目之條件的 <see cref="T:System.Predicate`1" /> 委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAt(System.Int32)">
      <summary>移除 <see cref="T:System.Collections.Generic.List`1" /> 中指定之索引處的項目。</summary>
      <param name="index">移除項目之以零為起始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。-或-<paramref name="index" /> 等於或大於 <see cref="P:System.Collections.Generic.List`1.Count" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveRange(System.Int32,System.Int32)">
      <summary>從 <see cref="T:System.Collections.Generic.List`1" /> 移除的元素範圍。</summary>
      <param name="index">要移除之項目範圍內之以零為起始的起始索引。</param>
      <param name="count">要移除的項目數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。-或-<paramref name="count" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 和 <paramref name="count" /> 並不代表 <see cref="T:System.Collections.Generic.List`1" /> 中元素的有效範圍。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse">
      <summary>反向整個 <see cref="T:System.Collections.Generic.List`1" /> 中元素的順序。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse(System.Int32,System.Int32)">
      <summary>反向指定範圍中項目的順序。</summary>
      <param name="index">要反向範圍內之以零為起始的起始索引。</param>
      <param name="count">要反向範圍中的項目數。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。-或-<paramref name="count" /> 小於 0。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 和 <paramref name="count" /> 並不代表 <see cref="T:System.Collections.Generic.List`1" /> 中元素的有效範圍。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort">
      <summary>使用預設比較子來排序在整個 <see cref="T:System.Collections.Generic.List`1" /> 中的項目。</summary>
      <exception cref="T:System.InvalidOperationException">預設比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <see cref="T:System.IComparable`1" /> 泛型介面的實作或型別 <paramref name="T" /> 的 <see cref="T:System.IComparable" /> 介面。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>使用指定的比較子來排序在整個 <see cref="T:System.Collections.Generic.List`1" /> 中的元素。</summary>
      <param name="comparer">比較項目時要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作，或 null 表示使用預設比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" />。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> 為 null，並且預設比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <see cref="T:System.IComparable`1" /> 泛型介面的實作或型別 <paramref name="T" /> 的 <see cref="T:System.IComparable" /> 介面。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="comparer" /> 的實作在排序過程中造成錯誤。例如，在將項目與其本身比較時，<paramref name="comparer" /> 可能不會傳回 0。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Comparison{`0})">
      <summary>使用指定的 <see cref="T:System.Comparison`1" /> 來排序在整個 <see cref="T:System.Collections.Generic.List`1" /> 中的項目。</summary>
      <param name="comparison">比較元素時所要使用的 <see cref="T:System.Comparison`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="comparison" /> 的實作在排序過程中造成錯誤。例如，在將項目與其本身比較時，<paramref name="comparison" /> 可能不會傳回 0。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>使用指定的比較子對 <see cref="T:System.Collections.Generic.List`1" /> 中某段範圍內的元素進行排序。</summary>
      <param name="index">要排序範圍內之以零為起始的起始索引。</param>
      <param name="count">要排序的範圍長度。</param>
      <param name="comparer">比較項目時要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作，或 null 表示使用預設比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。-或-<paramref name="count" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 和 <paramref name="count" /> 不會指定 <see cref="T:System.Collections.Generic.List`1" /> 中的有效範圍。-或-<paramref name="comparer" /> 的實作在排序過程中造成錯誤。例如，在將項目與其本身比較時，<paramref name="comparer" /> 可能不會傳回 0。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> 為 null，並且預設比較子 <see cref="P:System.Collections.Generic.Comparer`1.Default" /> 找不到 <see cref="T:System.IComparable`1" /> 泛型介面的實作或型別 <paramref name="T" /> 的 <see cref="T:System.IComparable" /> 介面。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 是唯讀，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.List`1" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" />，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從特定的 <see cref="T:System.Array" /> 索引開始，將 <see cref="T:System.Collections.ICollection" /> 的項目複製至 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.ICollection" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 為多維。-或-<paramref name="array" /> 沒有以零起始的索引。-或-來源 <see cref="T:System.Collections.ICollection" /> 項目的數量大於從 <paramref name="arrayIndex" /> 到目的 <paramref name="array" /> 結尾的可用空間。-或-來源 <see cref="T:System.Collections.ICollection" /> 的型別無法自動轉換為目的 <paramref name="array" /> 的型別。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.在 <see cref="T:System.Collections.Generic.List`1" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</summary>
      <returns>可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。在 <see cref="T:System.Collections.Generic.List`1" /> 的預設實作中，這個屬性永遠會傳回目前的執行個體。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Add(System.Object)">
      <summary>將項目加入至 <see cref="T:System.Collections.IList" />。</summary>
      <returns>插入新項目的位置。</returns>
      <param name="item">要加入至 <see cref="T:System.Collections.IList" /> 的 <see cref="T:System.Object" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> 屬於無法指派給 <see cref="T:System.Collections.IList" /> 的類型。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Contains(System.Object)">
      <summary>判斷 <see cref="T:System.Collections.IList" /> 是否包含特定值。</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.IList" />; otherwise, false.</returns>
      <param name="item">要在 <see cref="T:System.Collections.IList" /> 中尋找的 <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>判斷 <see cref="T:System.Collections.IList" /> 中特定項目的索引。</summary>
      <returns>如果可在清單中找到，則為 <paramref name="item" /> 的索引，否則為 -1。</returns>
      <param name="item">要在 <see cref="T:System.Collections.IList" /> 中尋找的物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> 屬於無法指派給 <see cref="T:System.Collections.IList" /> 的類型。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>將項目插入位於指定索引處的 <see cref="T:System.Collections.IList" />。</summary>
      <param name="index">應該插入 <paramref name="item" /> 之以零起始的索引。</param>
      <param name="item">要插入 <see cref="T:System.Collections.IList" /> 的物件。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Collections.IList" /> 中的有效索引。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> 屬於無法指派給 <see cref="T:System.Collections.IList" /> 的類型。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsFixedSize">
      <summary>取得值，指出 <see cref="T:System.Collections.IList" /> 是否有固定的大小。</summary>
      <returns>如果 <see cref="T:System.Collections.IList" /> 有固定大小，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.List`1" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.IList" /> 是否唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.IList" /> 是唯讀，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.List`1" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#Item(System.Int32)">
      <summary>在指定的索引位置上取得或設定項目。</summary>
      <returns>在指定索引上的項目。</returns>
      <param name="index">要取得或設定之以零為起始的項目索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Collections.IList" /> 中的有效索引。</exception>
      <exception cref="T:System.ArgumentException">屬性已設定，且 <paramref name="value" /> 具有無法指派給 <see cref="T:System.Collections.IList" /> 的型別。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Remove(System.Object)">
      <summary>從 <see cref="T:System.Collections.IList" /> 移除特定物件的第一個相符項目。</summary>
      <param name="item">要從 <see cref="T:System.Collections.IList" /> 中移除的物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> 屬於無法指派給 <see cref="T:System.Collections.IList" /> 的類型。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ToArray">
      <summary>將 <see cref="T:System.Collections.Generic.List`1" /> 的元素複製到新的陣列。</summary>
      <returns>陣列，包含 <see cref="T:System.Collections.Generic.List`1" /> 的項目複本。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrimExcess">
      <summary>將容量設為 <see cref="T:System.Collections.Generic.List`1" /> 中項目的實際數目，如果該數目小於臨界值。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrueForAll(System.Predicate{`0})">
      <summary>判斷 <see cref="T:System.Collections.Generic.List`1" /> 中的每一個項目是否符合指定之述詞所定義的條件。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.List`1" /> 中的每一個項目都符合指定之述詞所定義的條件，則為 true，否則為 false。如果清單中沒有元素，則傳回值為 true。</returns>
      <param name="match">
        <see cref="T:System.Predicate`1" /> 委派，可定義檢查項目所根據的條件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
    </member>
    <member name="T:System.Collections.Generic.List`1.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.List`1" /> 的項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值目前位置的 <see cref="T:System.Collections.Generic.List`1" /> 中的項目。</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.Dispose">
      <summary>釋放 <see cref="T:System.Collections.Generic.List`1.Enumerator" /> 所使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.MoveNext">
      <summary>將列舉值前移至 <see cref="T:System.Collections.Generic.List`1" /> 的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值目前位置的 <see cref="T:System.Collections.Generic.List`1" /> 中的項目。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Collections.Generic.Queue`1">
      <summary>表示物件的先進先出 (FIFO) 集合。</summary>
      <typeparam name="T">指定佇列中項目的類型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.Queue`1" /> 類別的新執行個體，其為空白執行個體且具有預設的初始容量。</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Queue`1" /> 類別的新執行個體，其包含從指定之集合複製的項目，且具有容納複製之項目數目的足夠容量。</summary>
      <param name="collection">集合，要將其項目複製至新的 <see cref="T:System.Collections.Generic.Queue`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Int32)">
      <summary>為具有指定初始容量且為空的 <see cref="T:System.Collections.Generic.Queue`1" /> 類別，初始化新的執行個體。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Queue`1" /> 可包含的項目初始數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Clear">
      <summary>從 <see cref="T:System.Collections.Generic.Queue`1" /> 移除所有物件。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Contains(`0)">
      <summary>判斷某項目是否在 <see cref="T:System.Collections.Generic.Queue`1" /> 中。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.Queue`1" /> 中找到 <paramref name="item" />，則為 true，否則為 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.Queue`1" /> 中尋找的物件。參考類型的值可以是 null。</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.CopyTo(`0[],System.Int32)">
      <summary>從指定的陣列索引處開始，複製 <see cref="T:System.Collections.Generic.Queue`1" /> 項目至現有一維 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.Generic.Queue`1" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零為起始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零為起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Queue`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Count">
      <summary>取得 <see cref="T:System.Collections.Generic.Queue`1" /> 中所包含的項目數。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" /> 中所包含的項目數。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Dequeue">
      <summary>移除並傳回在 <see cref="T:System.Collections.Generic.Queue`1" /> 前端的物件。</summary>
      <returns>從 <see cref="T:System.Collections.Generic.Queue`1" /> 前端移除的物件。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enqueue(`0)">
      <summary>將物件加入至 <see cref="T:System.Collections.Generic.Queue`1" /> 的結尾。</summary>
      <param name="item">要加入 <see cref="T:System.Collections.Generic.Queue`1" /> 的物件。參考類型的值可以是 null。</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Collections.Generic.Queue`1" /> 中逐一查看的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" /> 的 <see cref="T:System.Collections.Generic.Queue`1.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Peek">
      <summary>傳回 <see cref="T:System.Collections.Generic.Queue`1" /> 前端的物件而不需移除它。</summary>
      <returns>在 <see cref="T:System.Collections.Generic.Queue`1" /> 前端的物件。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" />，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從特定的 <see cref="T:System.Array" /> 索引開始，將 <see cref="T:System.Collections.ICollection" /> 的項目複製至 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.ICollection" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零為起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零為起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Collections.ICollection" /> 的存取為同步 (安全執行緒)，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.Queue`1" /> 的預設實作中，這個屬性一律會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#SyncRoot">
      <summary>取得可用以同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</summary>
      <returns>可用以同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。在 <see cref="T:System.Collections.Generic.Queue`1" /> 的預設實作中，這個屬性一律會傳回目前的執行個體。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.ToArray">
      <summary>將 <see cref="T:System.Collections.Generic.Queue`1" /> 項目複製到新的陣列。</summary>
      <returns>新的陣列，包含複製自 <see cref="T:System.Collections.Generic.Queue`1" /> 的項目。</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.TrimExcess">
      <summary>如果該數目小於目前容量的 90%，則將容量設為 <see cref="T:System.Collections.Generic.Queue`1" /> 中項目的實際數目。</summary>
    </member>
    <member name="T:System.Collections.Generic.Queue`1.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.Queue`1" /> 的項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值目前位置的 <see cref="T:System.Collections.Generic.Queue`1" /> 中的項目。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.Dispose">
      <summary>釋放 <see cref="T:System.Collections.Generic.Queue`1.Enumerator" /> 所使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.MoveNext">
      <summary>將列舉值前移至 <see cref="T:System.Collections.Generic.Queue`1" /> 的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值中目前位置的集合中的元素。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2">
      <summary>代表根據索引鍵，所排序的索引鍵/值組集合。</summary>
      <typeparam name="TKey">字典中的索引鍵類型。</typeparam>
      <typeparam name="TValue">字典中的值類型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 類別的新執行個體，其為空且為索引鍵型別使用預設的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 類別的新執行個體，其為空且使用指定的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作來比較索引鍵。</summary>
      <param name="comparer">在比較索引鍵時所使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作，或是 null 以便對索引鍵的型別使用預設 <see cref="T:System.Collections.Generic.Comparer`1" />。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 類別的新執行個體，其包含從指定的 <see cref="T:System.Collections.Generic.IDictionary`2" /> 所複製的元素，並為索引鍵型別使用預設的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" />，要將其元素複製至新的 <see cref="T:System.Collections.Generic.SortedDictionary`2" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一個或多個重複的索引鍵。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 類別的新執行個體，其包含從指定的 <see cref="T:System.Collections.Generic.IDictionary`2" /> 所複製的元素，並使用指定的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作來比較索引鍵。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" />，要將其元素複製至新的 <see cref="T:System.Collections.Generic.SortedDictionary`2" />。</param>
      <param name="comparer">在比較索引鍵時所使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作，或是 null 以便對索引鍵的型別使用預設 <see cref="T:System.Collections.Generic.Comparer`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一個或多個重複的索引鍵。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Add(`0,`1)">
      <summary>將有指定索引鍵和數值的項目加入 <see cref="T:System.Collections.Generic.SortedDictionary`2" />。</summary>
      <param name="key">要加入的項目的索引鍵。</param>
      <param name="value">要加入的項目的值。參考類型的值可以是 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中已存在具有相同索引鍵的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Clear">
      <summary>移除 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的所有項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Comparer">
      <summary>取得 <see cref="T:System.Collections.Generic.IComparer`1" />，用於排序 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的項目。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IComparer`1" />，用於排序 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的項目</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsKey(`0)">
      <summary>判斷 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 是否包含具有指定之索引鍵的項目。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">要在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中找到的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsValue(`1)">
      <summary>判斷 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 是否包含具有指定值的項目。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified value; otherwise, false.</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中尋找的值。參考類型的值可以是 null。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>從指定索引處開始，複製 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的項目至 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 結構的指定陣列。</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 結構的一維陣列，是從目前 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 複製過來之項目的目的端。陣列必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，位於複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">來源 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 項目的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Count">
      <summary>取得 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中所包含的索引鍵/值組數目。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中所包含的索引鍵/值組數目。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.GetEnumerator">
      <summary>傳回列舉值，可逐一查看 <see cref="T:System.Collections.Generic.SortedDictionary`2" />。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的 <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Item(`0)">
      <summary>取得或設定與指定之索引鍵相關聯的值。</summary>
      <returns>與指定之索引鍵關聯的值。如果找不到指定的索引鍵，則取得作業會擲回 <see cref="T:System.Collections.Generic.KeyNotFoundException" />，且設定作業會使用指定的索引鍵建立新項目。</returns>
      <param name="key">要取得或設定之值的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">會擷取屬性，且 <paramref name="key" /> 不存在於集合中。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Keys">
      <summary>取得集合，包含 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的索引鍵。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />，包含 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中的索引鍵。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Remove(`0)">
      <summary>從 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中移除具有指定之索引鍵的項目。</summary>
      <returns>如果成功移除項目，則為 true，否則為 false。如果在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中找不到 <paramref name="key" />，則這個方法也會傳回 false。</returns>
      <param name="key">要移除的項目索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>將項目加入至 <see cref="T:System.Collections.Generic.ICollection`1" /> 中。</summary>
      <param name="keyValuePair">要加入至 <see cref="T:System.Collections.Generic.ICollection`1" /> 的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 結構。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中已存在具有相同索引鍵的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>判斷 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含特定索引鍵和值。</summary>
      <returns>true if <paramref name="keyValuePair" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="keyValuePair">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中尋找的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 結構。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否為唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 是唯讀，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>從 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除第一次出現的指定項目。</summary>
      <returns>true if <paramref name="keyValuePair" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.This method also returns false if <paramref name="keyValuePair" /> was not found in the <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">要從 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 結構。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>取得 <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的索引鍵。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的索引鍵。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>取得 <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>可用來逐一查看此集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>取得集合，其中包含中的索引鍵<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>包含索引鍵的集合<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>取得集合，其中包含的值<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>集合，其中包含的值<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從指定的陣列索引處開始，將 <see cref="T:System.Collections.Generic.ICollection`1" /> 的項目複製到陣列。</summary>
      <param name="array">從 <see cref="T:System.Collections.Generic.ICollection`1" /> 複製的項目之目的地一維陣列。陣列必須有以零為起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，位於複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多維的。-或-<paramref name="array" /> 沒有以零起始的索引。-或-來源 <see cref="T:System.Collections.Generic.ICollection`1" /> 項目的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。-或-來源 <see cref="T:System.Collections.Generic.ICollection`1" /> 的型別無法自動轉換為目的 <paramref name="array" /> 的型別。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步處理 <see cref="T:System.Collections.ICollection" /> 存取的物件。</summary>
      <returns>可用來同步處理 <see cref="T:System.Collections.ICollection" /> 存取的物件。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>將隨附有索引鍵和值的項目加入至 <see cref="T:System.Collections.IDictionary" />。</summary>
      <param name="key">做為要加入項目之索引鍵的物件。</param>
      <param name="value">做為要加入項目之值的物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.IDictionary" />.-或-<paramref name="value" /> 屬於無法指派給 <see cref="T:System.Collections.IDictionary" /> 的 <paramref name="TValue" /> 實值型別的型別。-或-<see cref="T:System.Collections.IDictionary" /> 中已存在具有相同索引鍵的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>判斷 <see cref="T:System.Collections.IDictionary" /> 是否包含具有指定之索引鍵的項目。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.</returns>
      <param name="key">要在 <see cref="T:System.Collections.IDictionary" /> 中找到的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>傳回 <see cref="T:System.Collections.IDictionary" /> 的 <see cref="T:System.Collections.IDictionaryEnumerator" />。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> 的 <see cref="T:System.Collections.IDictionaryEnumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>取得值，指出 <see cref="T:System.Collections.IDictionary" /> 是否有固定的大小。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> has a fixed size; otherwise, false.在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.IDictionary" /> 是否為唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.IDictionary" /> 是唯讀，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>取得或設定具有指定索引鍵的項目。</summary>
      <returns>具有指定索引鍵的元素，如果 <paramref name="key" /> 不在字典中，或 <paramref name="key" /> 的型別無法指派給 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的 <paramref name="TKey" /> 索引鍵型別則為 null。</returns>
      <param name="key">要取得的項目索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">A value is being assigned, and <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.-或-A value is being assigned, and <paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Keys">
      <summary>取得 <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 的索引鍵。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 的索引鍵。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>從 <see cref="T:System.Collections.IDictionary" /> 中移除具有指定之索引鍵的項目。</summary>
      <param name="key">要移除的項目索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Values">
      <summary>取得 <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 中的值。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 中的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>可用來逐一查看此集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.TryGetValue(`0,`1@)">
      <summary>取得指定索引鍵相關聯的值。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">要取得之值的索引鍵。</param>
      <param name="value">此方法傳回時，如果找到索引鍵，則為與指定之索引鍵相關聯的值，否則為 <paramref name="value" /> 參數的型別預設值。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Values">
      <summary>取得集合，包含 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />，包含 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的值。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值目前位置的 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的項目。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.Dispose">
      <summary>釋放 <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" /> 所使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.MoveNext">
      <summary>將列舉值前移至 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>取得位於列舉值目前位置的項目，做為 <see cref="T:System.Collections.DictionaryEntry" /> 結構。</summary>
      <returns>位於集合中字典目前位置的項目，做為 <see cref="T:System.Collections.DictionaryEntry" /> 結構。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>取得位於列舉值目前位置之項目的索引鍵。</summary>
      <returns>位於集合中列舉值目前位置之項目的索引鍵。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>取得位於列舉值目前位置之項目的值。</summary>
      <returns>位於集合中列舉值目前位置之項目的值。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值中目前位置的集合中的元素。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection">
      <summary>表示 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的索引鍵集合。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 類別的新執行個體，反映指定 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的索引鍵。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />，其索引鍵反映在新 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>從指定的陣列索引處開始，複製 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 項目至現有一維陣列。</summary>
      <param name="array">一維陣列，是從 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 複製過來之項目的目的端。陣列必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">來源 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 元素的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Count">
      <summary>取得 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中所包含的元素數。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中所包含的項目數。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中逐一查看的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> 結構。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>將項目加入至 <see cref="T:System.Collections.Generic.ICollection`1" />。這個實作一定會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <param name="item">要加入 <see cref="T:System.Collections.Generic.ICollection`1" /> 的物件。</param>
      <exception cref="T:System.NotSupportedException">一定會擲回，此集合是唯讀的。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>將所有項目從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除。這個實作一定會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <exception cref="T:System.NotSupportedException">一定會擲回，此集合是唯讀的。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>判斷 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含指定的值。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找到 <paramref name="item" />，則為 true，否則為 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中尋找的物件。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 是唯讀的則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除特定物件的第一個相符項目。這個實作一定會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <returns>如果 <paramref name="item" /> 已成功從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除則為 true，否則為 false。如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找不到 <paramref name="item" />，則這個方法也會傳回 false。</returns>
      <param name="item">要從 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除的物件。</param>
      <exception cref="T:System.NotSupportedException">一定會擲回，此集合是唯讀的。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回可逐一查看集合的列舉程式。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" />，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從特定的陣列索引處開始，將 <see cref="T:System.Collections.ICollection" /> 的項目複製到陣列中。</summary>
      <param name="array">一維陣列，是從 <see cref="T:System.Collections.ICollection" /> 複製過來之項目的目的端。陣列必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 為多維。-或-<paramref name="array" /> 沒有以零起始的索引。-或-來源 <see cref="T:System.Collections.ICollection" /> 項目的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。-或-來源 <see cref="T:System.Collections.ICollection" /> 的型別無法自動轉換為目的 <paramref name="array" /> 的型別。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Collections.ICollection" /> 的存取為同步 (安全執行緒)，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</summary>
      <returns>可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的預設實作中，這個屬性永遠會傳回目前的執行個體。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回可逐一查看集合的列舉程式。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，可用於逐一查看集合。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值目前位置的 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 中的項目。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>釋放 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> 所使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>將列舉值前移至 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值中目前位置的集合中的元素。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection">
      <summary>表示 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的值集合。這個類別無法被繼承。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 類別的新執行個體，反映指定 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 中的值。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />，其值反映在新 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 中。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>從指定的陣列索引處開始，複製 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 項目至現有一維陣列。</summary>
      <param name="array">一維陣列，是從 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 複製過來之項目的目的端。陣列必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">來源 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 元素的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Count">
      <summary>取得 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 中所包含的元素數。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 中所包含的項目數。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 中逐一查看的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> 結構。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>將項目加入至 <see cref="T:System.Collections.Generic.ICollection`1" />。這個實作一定會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <param name="item">要加入 <see cref="T:System.Collections.Generic.ICollection`1" /> 的物件。</param>
      <exception cref="T:System.NotSupportedException">一定會擲回，此集合是唯讀的。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>將所有項目從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除。這個實作一定會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <exception cref="T:System.NotSupportedException">一定會擲回，此集合是唯讀的。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>判斷 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含指定的值。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找到 <paramref name="item" />，則為 true，否則為 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中尋找的物件。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 是唯讀的則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除特定物件的第一個相符項目。這個實作一定會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <returns>如果 <paramref name="item" /> 已成功從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除則為 true，否則為 false。如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找不到 <paramref name="item" />，則這個方法也會傳回 false。</returns>
      <param name="item">要從 <see cref="T:System.Collections.Generic.ICollection`1" /> 中移除的物件。</param>
      <exception cref="T:System.NotSupportedException">一定會擲回，此集合是唯讀的。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除特定物件的第一個相符項目。這個實作一定會擲回 <see cref="T:System.NotSupportedException" />。</summary>
      <returns>如果 <paramref name="item" /> 已成功從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除則為 true，否則為 false。如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中找不到 <paramref name="item" />，則這個方法也會傳回 false。</returns>
      <exception cref="T:System.NotSupportedException">一定會擲回，此集合是唯讀的。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從特定的陣列索引處開始，將 <see cref="T:System.Collections.ICollection" /> 的項目複製到陣列中。</summary>
      <param name="array">一維陣列，是從 <see cref="T:System.Collections.ICollection" /> 複製過來之項目的目的端。陣列必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於 0。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 為多維。-或-<paramref name="array" /> 沒有以零起始的索引。-或-來源 <see cref="T:System.Collections.ICollection" /> 項目的數量大於從 <paramref name="index" /> 到目的 <paramref name="array" /> 結尾的可用空間。-或-來源 <see cref="T:System.Collections.ICollection" /> 的型別無法自動轉換為目的 <paramref name="array" /> 的型別。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Collections.ICollection" /> 的存取為同步 (安全執行緒)，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</summary>
      <returns>可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。在 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的預設實作中，這個屬性永遠會傳回目前的執行個體。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回可逐一查看集合的列舉程式。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，可用於逐一查看集合。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值目前位置的 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 中的項目。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>釋放 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> 所使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>將列舉值前移至 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值中目前位置的集合中的元素。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedList`2">
      <summary>表示根據關聯的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作，依索引鍵所排序的索引鍵/值組集合。</summary>
      <typeparam name="TKey">集合中的索引鍵類型。</typeparam>
      <typeparam name="TValue">集合中的值類型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 類別的新執行個體，該執行個體是空的、具有預設的初始容量，且使用預設的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 類別的新執行個體，這個執行個體是空白的、具有預設的初始容量，並使用指定的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <param name="comparer">比較索引鍵時所要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作。-或-null 表示使用索引鍵型別的預設 <see cref="T:System.Collections.Generic.Comparer`1" />。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 類別的新執行個體，其包含複製自指定 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的元素、具有足以容納所複製元素數目的容量，且使用預設的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" />，要將其元素複製至新的 <see cref="T:System.Collections.Generic.SortedList`2" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一個或多個重複的索引鍵。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 類別的新執行個體，其包含複製自指定 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的元素、具有足以容納所複製元素數目的容量，且使用指定的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" />，要將其元素複製至新的 <see cref="T:System.Collections.Generic.SortedList`2" />。</param>
      <param name="comparer">比較索引鍵時所要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作。-或-null 表示使用索引鍵型別的預設 <see cref="T:System.Collections.Generic.Comparer`1" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> 包含一個或多個重複的索引鍵。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 類別的新執行個體，該執行個體是空的、具有指定的初始容量，且使用預設的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.SortedList`2" /> 可包含的元素初始數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> 小於零。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedList`2" /> 類別的新執行個體，這個執行個體是空白的、具有指定的初始容量，並使用指定的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.SortedList`2" /> 可包含的元素初始數目。</param>
      <param name="comparer">比較索引鍵時所要使用的 <see cref="T:System.Collections.Generic.IComparer`1" /> 實作。-或-null 表示使用索引鍵型別的預設 <see cref="T:System.Collections.Generic.Comparer`1" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> 小於零。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Add(`0,`1)">
      <summary>將有指定索引鍵和數值的項目加入 <see cref="T:System.Collections.Generic.SortedList`2" />。</summary>
      <param name="key">要加入的項目的索引鍵。</param>
      <param name="value">要加入的項目的值。參考類型的值可以是 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Collections.Generic.SortedList`2" /> 中已存在具有相同索引鍵的元素。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Capacity">
      <summary>取得或設定 <see cref="T:System.Collections.Generic.SortedList`2" /> 可包含的元素數目。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> 可包含的元素數目。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.SortedList`2.Capacity" /> 是設為小於 <see cref="P:System.Collections.Generic.SortedList`2.Count" /> 的值。</exception>
      <exception cref="T:System.OutOfMemoryException">系統上沒有足夠的可用記憶體。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Clear">
      <summary>移除 <see cref="T:System.Collections.Generic.SortedList`2" /> 中的所有項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Comparer">
      <summary>取得已排序清單的 <see cref="T:System.Collections.Generic.IComparer`1" />。</summary>
      <returns>目前 <see cref="T:System.Collections.Generic.SortedList`2" /> 的 <see cref="T:System.IComparable`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsKey(`0)">
      <summary>判斷 <see cref="T:System.Collections.Generic.SortedList`2" /> 是否包含特定的索引鍵。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">要在 <see cref="T:System.Collections.Generic.SortedList`2" /> 中找到的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsValue(`1)">
      <summary>判斷 <see cref="T:System.Collections.Generic.SortedList`2" /> 是否包含特定值。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified value; otherwise, false.</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.SortedList`2" /> 中尋找的值。參考類型的值可以是 null。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Count">
      <summary>取得 <see cref="T:System.Collections.Generic.SortedList`2" /> 中所包含的索引鍵/值組數目。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> 中所包含的索引鍵/值組數目。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.GetEnumerator">
      <summary>傳回列舉值，可逐一查看 <see cref="T:System.Collections.Generic.SortedList`2" />。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" /> 之型別 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfKey(`0)">
      <summary>搜尋指定的索引鍵，並傳回整個 <see cref="T:System.Collections.Generic.SortedList`2" /> 內以零起始的索引。</summary>
      <returns>如果有找到，則為整個 <see cref="T:System.Collections.Generic.SortedList`2" /> 內 <paramref name="key" /> 之以零起始的索引，否則為 -1。</returns>
      <param name="key">要在 <see cref="T:System.Collections.Generic.SortedList`2" /> 中找到的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfValue(`1)">
      <summary>搜尋指定的值，並傳回整個 <see cref="T:System.Collections.Generic.SortedList`2" /> 內第一次出現之以零起始的索引。</summary>
      <returns>如果有找到，則是在整個 <see cref="T:System.Collections.Generic.SortedList`2" /> 內，<paramref name="value" /> 之第一次出現的以零起始的索引，否則為 -1。</returns>
      <param name="value">要在 <see cref="T:System.Collections.Generic.SortedList`2" /> 中尋找的值。參考類型的值可以是 null。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Item(`0)">
      <summary>取得或設定與指定之索引鍵相關聯的值。</summary>
      <returns>與指定之索引鍵關聯的值。如果找不到指定的索引鍵，則取得作業會擲回 <see cref="T:System.Collections.Generic.KeyNotFoundException" />，且設定作業會使用指定的索引鍵建立新項目。</returns>
      <param name="key">索引鍵，要讀取或設定其值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">會擷取屬性，且 <paramref name="key" /> 不存在於集合中。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Keys">
      <summary>取得包含 <see cref="T:System.Collections.Generic.SortedList`2" /> 中的索引鍵 (依已排序的順序) 的集合。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" />，包含 <see cref="T:System.Collections.Generic.IList`1" /> 中的索引鍵。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Remove(`0)">
      <summary>從 <see cref="T:System.Collections.Generic.SortedList`2" /> 中移除具有指定之索引鍵的項目。</summary>
      <returns>如果成功移除項目，則為 true，否則為 false。This method also returns false if <paramref name="key" /> was not found in the original <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">要移除的項目索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.RemoveAt(System.Int32)">
      <summary>移除 <see cref="T:System.Collections.Generic.SortedList`2" /> 之指定索引處的項目。</summary>
      <param name="index">移除項目之以零為起始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。-或-<paramref name="index" /> 等於或大於 <see cref="P:System.Collections.Generic.SortedList`2.Count" />。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>將索引鍵/值組加入 <see cref="T:System.Collections.Generic.ICollection`1" />。</summary>
      <param name="keyValuePair">要加入至 <see cref="T:System.Collections.Generic.ICollection`1" /> 的 <see cref="T:System.Collections.Generic.KeyValuePair`2" />。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>判斷 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否含有特定元素。</summary>
      <returns>true if <paramref name="keyValuePair" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="keyValuePair">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中尋找的 <see cref="T:System.Collections.Generic.KeyValuePair`2" />。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>從特定的 <see cref="T:System.Array" /> 索引開始，將 <see cref="T:System.Collections.Generic.ICollection`1" /> 的項目複製到 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.Generic.ICollection`1" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零起始的索引，位於複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小於零。</exception>
      <exception cref="T:System.ArgumentException">來源 <see cref="T:System.Collections.Generic.ICollection`1" /> 項目的數量大於從 <paramref name="arrayIndex" /> 到目的 <paramref name="array" /> 結尾的可用空間。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否為唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 是唯讀，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.SortedList`2" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除第一個出現的特定索引鍵/值組。</summary>
      <returns>true if <paramref name="keyValuePair" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.This method also returns false if <paramref name="keyValuePair" /> was not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">要從 <see cref="T:System.Collections.Generic.ICollection`1" /> 移除的 <see cref="T:System.Collections.Generic.KeyValuePair`2" />。</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>取得 <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的索引鍵。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的索引鍵。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>取得 <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />，包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>可用來逐一查看此集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>取得可列舉集合，其中包含唯讀字典中的索引鍵。</summary>
      <returns>包含唯讀字典中索引鍵的可列舉集合。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>取得可列舉集合，其中包含唯讀字典中的值。</summary>
      <returns>包含唯讀字典中之值的可列舉集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從特定的 <see cref="T:System.Array" /> 索引開始，將 <see cref="T:System.Collections.ICollection" /> 的項目複製到 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.ICollection" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零起始的索引，位於複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 小於零。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 是多維的。-或-<paramref name="array" /> 沒有以零起始的索引。-或-來源 <see cref="T:System.Collections.ICollection" /> 項目的數量大於從 <paramref name="arrayIndex" /> 到目的 <paramref name="array" /> 結尾的可用空間。-或-來源 <see cref="T:System.Collections.ICollection" /> 的型別無法自動轉換為目的 <paramref name="array" /> 的型別。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.在 <see cref="T:System.Collections.Generic.SortedList`2" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步處理 <see cref="T:System.Collections.ICollection" /> 存取的物件。</summary>
      <returns>可用來同步處理 <see cref="T:System.Collections.ICollection" /> 存取的物件。在 <see cref="T:System.Collections.Generic.SortedList`2" /> 的預設實作中，這個屬性永遠會傳回目前的執行個體。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>將隨附有索引鍵和值的項目加入至 <see cref="T:System.Collections.IDictionary" />。</summary>
      <param name="key">
        <see cref="T:System.Object" />，用做要加入之項目的索引鍵。</param>
      <param name="value">
        <see cref="T:System.Object" />，用做要加入之項目的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.IDictionary" />.-或-<paramref name="value" /> 屬於無法指派給 <see cref="T:System.Collections.IDictionary" /> 的 <paramref name="TValue" /> 實值型別的型別。-或-<see cref="T:System.Collections.IDictionary" /> 中已存在具有相同索引鍵的元素。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>判斷 <see cref="T:System.Collections.IDictionary" /> 是否包含具有指定之索引鍵的項目。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.</returns>
      <param name="key">要在 <see cref="T:System.Collections.IDictionary" /> 中找到的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#GetEnumerator">
      <summary>傳回 <see cref="T:System.Collections.IDictionary" /> 的 <see cref="T:System.Collections.IDictionaryEnumerator" />。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> 的 <see cref="T:System.Collections.IDictionaryEnumerator" />。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsFixedSize">
      <summary>取得值，指出 <see cref="T:System.Collections.IDictionary" /> 是否有固定的大小。</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> has a fixed size; otherwise, false.在 <see cref="T:System.Collections.Generic.SortedList`2" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.IDictionary" /> 是否為唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.IDictionary" /> 是唯讀，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.SortedList`2" /> 的預設實作中，這個屬性永遠會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>取得或設定具有指定索引鍵的項目。</summary>
      <returns>The element with the specified key, or null if <paramref name="key" /> is not in the dictionary or <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">要取得或設定之項目的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">A value is being assigned, and <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.-或-A value is being assigned, and <paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Keys">
      <summary>取得 <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 的索引鍵。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 的索引鍵。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>從 <see cref="T:System.Collections.IDictionary" /> 中移除具有指定之索引鍵的項目。</summary>
      <param name="key">要移除的項目索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Values">
      <summary>取得 <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 中的值。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />，包含 <see cref="T:System.Collections.IDictionary" /> 中的值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>可用來逐一查看此集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TrimExcess">
      <summary>如果該數目小於目前容量的 90%，則將容量設為 <see cref="T:System.Collections.Generic.SortedList`2" /> 中元素的實際數目。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TryGetValue(`0,`1@)">
      <summary>取得指定索引鍵相關聯的值。</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">要取得其值的索引鍵。</param>
      <param name="value">此方法傳回時，如果找到索引鍵，則為與指定之索引鍵相關聯的值，否則為 <paramref name="value" /> 參數的型別預設值。這個參數會以未初始化的狀態傳遞。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Values">
      <summary>取得集合，包含 <see cref="T:System.Collections.Generic.SortedList`2" /> 中的值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IList`1" />，包含 <see cref="T:System.Collections.Generic.SortedList`2" /> 中的值。</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1">
      <summary>表示依排序順序維護的物件集合。</summary>
      <typeparam name="T">資料集中項目的類型。</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedSet`1" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedSet`1" /> 類別的新執行個體，這個執行個體使用指定的比較子。</summary>
      <param name="comparer">用來比較物件的預設比較子。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedSet`1" /> 類別的新執行個體，這個執行個體包含從指定的可列舉集合複製的項目。</summary>
      <param name="collection">要複製的可列舉集合。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IComparer{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.SortedSet`1" /> 類別的新執行個體，這個執行個體包含從指定的可列舉集合複製的項目，而且使用指定的比較子。</summary>
      <param name="collection">要複製的可列舉集合。</param>
      <param name="comparer">用來比較物件的預設比較子。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Add(`0)">
      <summary>將項目加入至資料集，並傳回表示是否成功加入的值。</summary>
      <returns>如果 <paramref name="item" /> 已加入至資料集，則為 true，否則為 false。</returns>
      <param name="item">要加入至集合的項目。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Clear">
      <summary>將所有項目從資料集移除。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Comparer">
      <summary>取得 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 物件，這個物件用來判斷 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的值是否相等。</summary>
      <returns>比較子，用來判斷 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的值是否相等。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Contains(`0)">
      <summary>判斷資料集是否包含特定項目。</summary>
      <returns>如果資料集包含 <paramref name="item" />，則為 true，否則為 false。</returns>
      <param name="item">要在資料集中尋找的項目。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[])">
      <summary>從目標陣列的開頭開始，將完整的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 複製到相容的一維陣列。</summary>
      <param name="array">一維陣列，是從 <see cref="T:System.Collections.Generic.SortedSet`1" /> 複製項目之目的端。</param>
      <exception cref="T:System.ArgumentException">來源 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的元素數目大於目的陣列可包含的元素數目。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32)">
      <summary>從指定的陣列索引處開始，將完整的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 複製到相容的一維陣列中。</summary>
      <param name="array">一維陣列，是從 <see cref="T:System.Collections.Generic.SortedSet`1" /> 複製項目之目的端。陣列必須有以零為起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentException">來源陣列中的元素數目大於從 <paramref name="index" /> 至目的陣列結尾間的可用空間。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>從指定的陣列索引處開始，將指定的項目數目從 <see cref="T:System.Collections.Generic.SortedSet`1" /> 複製到相容的一維陣列。</summary>
      <param name="array">一維陣列，是從 <see cref="T:System.Collections.Generic.SortedSet`1" /> 複製項目之目的端。陣列必須有以零為起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <param name="count">要複製的項目數目。</param>
      <exception cref="T:System.ArgumentException">來源陣列中的元素數目大於從 <paramref name="index" /> 至目的陣列結尾間的可用空間。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。-或-<paramref name="count" /> 小於零。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Count">
      <summary>取得 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的項目數目。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的元素數。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>從目前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件中，移除指定之集合內的所有項目。</summary>
      <param name="other">要從 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件移除的項目集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中逐一查看的列舉值。</summary>
      <returns>可依排序順序逐一查看 <see cref="T:System.Collections.Generic.SortedSet`1" /> 的列舉值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetViewBetween(`0,`0)">
      <summary>傳回 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中資料子集的檢視。</summary>
      <returns>子集檢視，只包含指定之範圍內的值。</returns>
      <param name="lowerValue">檢視中所需的最小值。</param>
      <param name="upperValue">檢視中所需的最大值。</param>
      <exception cref="T:System.ArgumentException">根據比較子，<paramref name="lowerValue" /> 大於 <paramref name="upperValue" />。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">在檢視上的嘗試作業，超出指定的 <paramref name="lowerValue" /> 和 <paramref name="upperValue" /> 範圍。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改目前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件，使其僅包含同時也在指定之集合中出現的項目。</summary>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件是否為指定之集合的真子集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件是 <paramref name="other" /> 的真子集則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件是否為指定之集合的真超集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件是 <paramref name="other" /> 的真超集則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件比較的集合。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件是否為指定之集合的子集。</summary>
      <returns>如果目前 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件是 <paramref name="other" /> 的子集，則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件是否為指定之集合的超集。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件是 <paramref name="other" /> 的超集則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件比較的集合。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Max">
      <summary>取得 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的最大值，如比較子所定義。</summary>
      <returns>資料集中的最大值。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Min">
      <summary>取得 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中的最小值，如比較子所定義。</summary>
      <returns>資料集中的最小值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷目前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件與指定的集合是否共用通用的項目。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件和 <paramref name="other" /> 至少共用一個通用項目則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Remove(`0)">
      <summary>從 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中移除指定項目。</summary>
      <returns>如果找到並成功移除項目則為 true，否則為 false。</returns>
      <param name="item">要移除的項目。</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>從 <see cref="T:System.Collections.Generic.SortedSet`1" /> 中移除符合指定之述詞 (Predicate) 所定義條件的所有項目。</summary>
      <returns>已從 <see cref="T:System.Collections.Generic.SortedSet`1" /> 集合中移除的項目數目。 </returns>
      <param name="match">定義要移除項目之條件的委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Reverse">
      <summary>傳回以反向順序逐一查看 <see cref="T:System.Collections.Generic.SortedSet`1" /> 的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</summary>
      <returns>以反向順序逐一查看 <see cref="T:System.Collections.Generic.SortedSet`1" /> 的列舉程式。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>判斷目前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件和指定的集合是否包含相同項目。</summary>
      <returns>如果目前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件等於 <paramref name="other" />，則為 true，否則為 false。</returns>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改目前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件，使其僅包含目前物件或指定之集合 (但非兩者) 中出現的項目。</summary>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>將項目加入至 <see cref="T:System.Collections.Generic.ICollection`1" /> 物件。</summary>
      <param name="item">要加入至 <see cref="T:System.Collections.Generic.ICollection`1" /> 物件中的物件。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 是唯讀的。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>取得值，這個值表示 <see cref="T:System.Collections.ICollection" /> 是否為唯讀。</summary>
      <returns>如果集合是唯讀，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>可用來逐一查看集合的列舉值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從指定的陣列索引處開始，將完整的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 複製到相容的一維陣列中。</summary>
      <param name="array">一維陣列，是從 <see cref="T:System.Collections.Generic.SortedSet`1" /> 複製項目之目的端。陣列必須有以零為起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentException">來源陣列中的元素數目大於從 <paramref name="index" /> 至目的陣列結尾間的可用空間。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，指出 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Collections.ICollection" /> 的存取是同步，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</summary>
      <returns>可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。在 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 的預設實作中，這個屬性永遠會傳回目前的執行個體。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>可用來逐一查看集合的列舉值。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>修改目前的 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件，使其包含目前物件或指定之集合中出現的所有項目。</summary>
      <param name="other">要與目前 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件比較的集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> 為 null。</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.SortedSet`1" /> 物件的項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值中目前位置的集合中的元素。</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.Dispose">
      <summary>釋放 <see cref="T:System.Collections.Generic.SortedSet`1.Enumerator" /> 所使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.MoveNext">
      <summary>讓列舉程式前進至 <see cref="T:System.Collections.Generic.SortedSet`1" /> 集合中的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值中目前位置的集合中的元素。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Collections.Generic.Stack`1">
      <summary>表示相同特定類型之執行個體的各種大小後進先出 (LIFO) 集合。</summary>
      <typeparam name="T">指定堆疊中項目的類型。</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor">
      <summary>初始化 <see cref="T:System.Collections.Generic.Stack`1" /> 類別的新執行個體，其為空白執行個體且具有預設的初始容量。</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Collections.Generic.Stack`1" /> 類別的新執行個體，其包含從指定之集合複製的項目，且具有容納複製之項目數目的足夠容量。</summary>
      <param name="collection">要複製項目的來源集合。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Collections.Generic.Stack`1" /> 類別的新執行個體，其為空白且具有指定的初始容量或預設的初始容量 (兩者中較大的那一個)。</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Stack`1" /> 可包含的項目初始數目。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Clear">
      <summary>從 <see cref="T:System.Collections.Generic.Stack`1" /> 移除所有物件。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Contains(`0)">
      <summary>判斷某項目是否在 <see cref="T:System.Collections.Generic.Stack`1" /> 中。</summary>
      <returns>如果在 <see cref="T:System.Collections.Generic.Stack`1" /> 中找到 <paramref name="item" />，則為 true，否則為 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.Stack`1" /> 中尋找的物件。參考類型的值可以是 null。</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.CopyTo(`0[],System.Int32)">
      <summary>從指定的陣列索引處開始，複製 <see cref="T:System.Collections.Generic.Stack`1" /> 至現有一維 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.Generic.Stack`1" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零為起始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零為起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Stack`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Count">
      <summary>取得 <see cref="T:System.Collections.Generic.Stack`1" /> 中所包含的項目數。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" /> 中所包含的項目數。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.GetEnumerator">
      <summary>傳回 <see cref="T:System.Collections.Generic.Stack`1" /> 的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" /> 的 <see cref="T:System.Collections.Generic.Stack`1.Enumerator" />。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Peek">
      <summary>傳回 <see cref="T:System.Collections.Generic.Stack`1" /> 頂端的物件而不需移除它。</summary>
      <returns>在 <see cref="T:System.Collections.Generic.Stack`1" /> 頂端的物件。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Pop">
      <summary>移除並傳回在 <see cref="T:System.Collections.Generic.Stack`1" /> 頂端的物件。</summary>
      <returns>從 <see cref="T:System.Collections.Generic.Stack`1" /> 頂端移除的物件。</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Push(`0)">
      <summary>將物件插入 <see cref="T:System.Collections.Generic.Stack`1" /> 的頂端。</summary>
      <param name="item">要推送至 <see cref="T:System.Collections.Generic.Stack`1" /> 的物件。參考類型的值可以是 null。</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" />，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從特定的 <see cref="T:System.Array" /> 索引開始，將 <see cref="T:System.Collections.ICollection" /> 的項目複製至 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.ICollection" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零為起始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零為起始的索引，是複製開始的位置。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Collections.ICollection" /> 的存取為同步 (安全執行緒)，則為 true，否則為 false。在 <see cref="T:System.Collections.Generic.Stack`1" /> 的預設實作中，這個屬性一律會傳回 false。</returns>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#SyncRoot">
      <summary>取得可用以同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</summary>
      <returns>可用以同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。在 <see cref="T:System.Collections.Generic.Stack`1" /> 的預設實作中，這個屬性一律會傳回目前的執行個體。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回逐一查看集合的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，可用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.ToArray">
      <summary>將 <see cref="T:System.Collections.Generic.Stack`1" /> 複製到新的陣列。</summary>
      <returns>新陣列，包含 <see cref="T:System.Collections.Generic.Stack`1" /> 之項目的複本。</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.TrimExcess">
      <summary>如果該數目小於目前容量的 90%，則將容量設為 <see cref="T:System.Collections.Generic.Stack`1" /> 中項目的實際數目。</summary>
    </member>
    <member name="T:System.Collections.Generic.Stack`1.Enumerator">
      <summary>列舉 <see cref="T:System.Collections.Generic.Stack`1" /> 的項目。</summary>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值目前位置的 <see cref="T:System.Collections.Generic.Stack`1" /> 中的項目。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.Dispose">
      <summary>釋放 <see cref="T:System.Collections.Generic.Stack`1.Enumerator" /> 所使用的所有資源。</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.MoveNext">
      <summary>將列舉值前移至 <see cref="T:System.Collections.Generic.Stack`1" /> 的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>取得位於目前列舉值位置上的項目。</summary>
      <returns>位於列舉值中目前位置的集合中的元素。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。此類別無法被繼承。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
  </members>
</doc>