﻿Imports System.Globalization
Imports System.IO
Imports System.Net
Imports System.Net.Sockets
Imports System.Text
Imports System.Text.RegularExpressions
Module EmailValidation
    Public MailTable As New DataTable
    Public Sub CreateTable_EmailValidation()
        MailTable = New DataTable
        MailTable.Columns.Clear()
        MailTable.TableName = "TempTable"
        MailTable.Columns.Add("id", GetType(Integer))
        MailTable.Columns.Add("emailAddress", GetType(String))
        MailTable.Columns.Add("Status", GetType(String))
        MailTable.Columns.Add("DomanName", GetType(String))
        MailTable.Columns.Add("SMTPServer", GetType(String))
        MailTable.Columns.Add("SMTPReply", GetType(String))
    End Sub
    Private Function DomainMapper(match As Match) As String
        Dim idn As New IdnMapping()
        Dim domainName As String = match.Groups(2).Value
        Try
            domainName = idn.GetAscii(domainName)
        Catch __unusedException1__ As ArgumentException
            Return False
        End Try
        Return match.Groups(1).Value + domainName
    End Function
    Public Function IsValidEmail(ByVal email As String) As Boolean
        If String.IsNullOrWhiteSpace(email) Then Return False
        Try
            email = Regex.Replace(email, "(@)(.+)$", AddressOf DomainMapper, RegexOptions.None, TimeSpan.FromMilliseconds(200))
        Catch e As RegexMatchTimeoutException
            Return False
        Catch e As ArgumentException
            Return False
        End Try
        Try
            Return Regex.IsMatch(email, "^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250))
        Catch __unusedRegexMatchTimeoutException1__ As RegexMatchTimeoutException
            Return False
        End Try
    End Function
    Public Function EmailFormat(email As String) As Boolean
        Dim pattern As String = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        Dim match As Match = Regex.Match(email, pattern)
        If match.Success Then
            Return True
        Else
            Return False
        End If
    End Function
    Public Function get_Domain_Name(ByVal EmailAddress As String) As String
        ' Extract the domain from the email address
        Dim domain As String = EmailAddress.Split("@")(1)
        ' Try to resolve the domain to an IP address
        Try
            Dim hostEntry As IPHostEntry = Dns.GetHostEntry(domain)
            ' If the host entry is not null, the domain exists
            If hostEntry IsNot Nothing Then Return domain
        Catch ex As Exception
            Return "Unknown domain"
        End Try
    End Function
    Public Function VerifyEmailAddress(ByVal EmailAddress As String) As String
        Dim url As String = "https://avatarapi.com/avatar.asmx/VerifyEmail?email=" & EmailAddress
        Dim webClient As New WebClient()
        Dim jsonResult As String = webClient.DownloadString(url)
        Dim mailStatus As String = Regex.Match(jsonResult, "<Verification>(.*?)</Verification>").Groups(1).Value
        Dim mailExchange As String = Regex.Match(jsonResult, "<MailExchange>(.*?)</MailExchange>").Groups(1).Value
        Dim smptResponse As String = Regex.Match(jsonResult, "<SmtpResponse>(.*?)</SmtpResponse>").Groups(1).Value
        Return mailStatus & "|" & mailExchange & "|" & smptResponse
    End Function
    Function FindSmtpServer(emailAddress As String) As String
        Dim host As String = emailAddress.Split("@")(1)
        Dim ipAddresses() As IPAddress = Dns.GetHostAddresses(host)
        Dim smtpServer As String = String.Format("smtp.{0}", host)
        For Each ipAddress As IPAddress In ipAddresses
            Dim endpoint As New IPEndPoint(ipAddress, 25)
            Try
                Using client As New TcpClient(endpoint)
                    smtpServer = endpoint.ToString()
                    Exit For
                End Using
            Catch ex As SocketException
                Console.WriteLine(ex.Message)
            End Try
        Next
        Return smtpServer
    End Function
    Private Function ReadResponse(ByVal stream As NetworkStream) As String
        Dim responseBuilder As New StringBuilder()
        Dim buffer(1023) As Byte
        Do
            Dim byteCount As Integer = stream.Read(buffer, 0, buffer.Length)
            Dim response As String = Encoding.ASCII.GetString(buffer, 0, byteCount)
            responseBuilder.Append(response)
        Loop While stream.DataAvailable
        Return responseBuilder.ToString()
    End Function
    Public Function Get_SMTP_Server(ByVal domainName As String) As String
        ' Get the MX record for the domain
        Dim mxRecord As Net.IPHostEntry = Net.Dns.GetHostEntry(domainName)
        ' Get the first SMTP server name from the MX record
        Dim smtpServerName As String = mxRecord.HostName
        ' Try to connect to the SMTP server
        Dim smtpPort As Integer = 25
        Dim smtpClient As New TcpClient()
        Dim msgStr As String = ""
        Try
            smtpClient.Connect(smtpServerName, smtpPort)
            msgStr = ("Connected to SMTP server: " & smtpServerName) & vbNewLine
            ' Get the SMTP response from the server
            Dim smtpStream As NetworkStream = smtpClient.GetStream()
            Dim response As String = ReadResponse(smtpStream)
            msgStr = msgStr & ("SMTP response: " & response) & vbNewLine
            ' Get the SMTP port number
            Dim smtpEndPoint As IPEndPoint = DirectCast(smtpClient.Client.LocalEndPoint, IPEndPoint)
            msgStr = msgStr & ("SMTP port number: " & smtpEndPoint.Port) & vbNewLine
        Catch ex As Exception
            msgStr = ("Failed to connect to SMTP server: " & vbNewLine & ex.Message)
        End Try
        Return msgStr
    End Function
    Public Function Get_SMTP_Response(ByVal hostname As String) As String
        Dim port As Integer = 25
        Dim client As TcpClient = New TcpClient("google.com", port)
        Dim stream As NetworkStream = client.GetStream()
        Dim reader As StreamReader = New StreamReader(stream)
        Dim msgStr As String = ""
        ' Wait for the initial response from the server
        Dim response As String = reader.ReadLine()
        msgStr = ("Initial response from server: " & response) & vbNewLine
        ' Send the EHLO command to the server
        Dim command As String = "EHLO localhost" & vbCrLf
        Dim buffer As Byte() = Encoding.ASCII.GetBytes(command)
        stream.Write(buffer, 0, buffer.Length)
        ' Read the response from the server
        response = reader.ReadLine()
        While Not response.StartsWith("250")
            msgStr = ("Response from server: " & response)
            response = reader.ReadLine()
        End While
        msgStr = ("SMTP response from server: " & response)
        ' Close the connection to the server
        stream.Close()
        client.Close()
        Return msgStr
    End Function
End Module
