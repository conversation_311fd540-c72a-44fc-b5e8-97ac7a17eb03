﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="Timer2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="txt_width_Outlook_B.Properties.ContextImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABx0RVh0VGl0
        bGUAUnVsZXJIb3Jpem9udGFsO1J1bGVyO2vI4DYAAAI3SURBVDhPpdJbSFNxHAfwg93La266SxZEhVRY
        dKEmgZRd7EFSg25oq0xd6baiG10wSLTCrB56DUPKdM6tXdKVl00W1Sqt2em121sRrJ3t7OLLt985O4QP
        e2h04MP/nB98v+fH4TAA/kvSYSqEK43MJLOmmS2ZI5krmTeNME9j2I5iz6eOYrDtGkze3Az/9U340LoR
        Ey0bMHFtHd5dXQvflTXwXS7C64ur8fL8SnjPFGLIsOKFWMLe0mDq+wBxYuqbg9gR/2ojVsS/WEg/MSP+
        2SSKTt5DwGWA27hc2D+d8beuF8PRj7eTuCOKCef7NoSG6xAwl+HHfQ2GdAVCQQYz3lwkvvlvgE0EROxd
        Ct5A2NMIzlaJoGkHfj8uwc/OLXAdVQoFmcybS6vEVWMUjPrbER1vQeRtM3jvKYRcNWKQM+9GsGcruN5t
        4Eyl+NVVgoEahVCQxbw6Vwje1wbOXkUq4GwqoFAFOEs5grSutV6BUN8uhMw7Ee4vg0krR+BhKRyH5IkC
        7+llCI9dwNOmRbTidjhOqBFx6/CkQQXeWg7LcSViXiPMx/IRse+B6bAc/EgD7AdkQkE2M6ZfiuDwWTga
        1eCe1cKuU9HHOglrnRL8qJ6CCvBuI/q0eYh4DOipltGzHrb9uYmCwfoloyO6xejVqvC8Vo3u6nwMHlHh
        0cE8OOm+a18urSvDg705YqizMnF2Vy30UsECIv5R80m6JEOSKcmSZEtypFMIz0j6f6ci6TAVSYf/Dswf
        nDpncLDnQy0AAAAASUVORK5CYII=
</value>
  </data>
  <data name="txt_length_Outlook_B.Properties.ContextImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABp0RVh0VGl0
        bGUAUnVsZXJWZXJ0aWNhbDtSdWxlcjvWKTIFAAACWklEQVQ4T33TXUhTYRwG8PWB6SzNuelMCjPKmkUf
        dmWOiAi66yZYNWz5gZW1KSgiWK2SGYFBWhRRXfixudaaTgs/t7XhpjNdpaEQpH0R3czm7OycrYun98xF
        Qp3zwnP3/H/83/dwBAAEsbN6TLff6buRh7Hru+DV7sSoNhcjV2TwXJLBXbcD9pocG+nFsTN/shyIm2jI
        A/PFDfqTC/RHB+g5G5jZAdCzfaDHm+Cq3caWhf8DVkQB3T78+u4FM9MCZvoxwtOP0K3ORvfFzeg6mwmz
        SsqWE7mANa/q9yLyzQP63UMwUw8QnrqH8ORdRCabEXqpgaN6Cz/grd+DyFey/tv7oN/cAf36NhjfLXSW
        bYClNAOmwnS2vJYLiB+9thvhz3aEfE0kjeTeNxFyaBCyHgPlqMBgZRY/4CEvH57rQ2i8EdRYA0IjWpiL
        pdE8PSOFUSlhy+u4gIThy7kIf+gB5dWB8lzF4nMlFkxHwDjVoOxq9F7YyA+46mRg3lvw061FsLcIP/Ry
        LBgPw6RKw5NCCQwnUnkBobN2O+gZI4L95zDfKo8CAeMhLJqPYnGoHD1lGWw5iRNw1OSAmmiGv6UA/rYD
        mG8vQIcyFQaSjlNi6BUiXiDRVr0VQVsVAfLhb10CAgY5KFsp2aoEXcXRz5jMCQxWZSM4VBkD8jFPtmCv
        oT8pQrsiBW3HU/iB/oosBAc00eGlkC3Yd+g4iEBvIZ6pxJwAe+Kt5zcNvyjPRE+pFNaSdLJyGjqLJLCQ
        QfNpMVoVIifp/fszxZBVJAkkSbEkx7J+WYQkK/8CEPwGXB44m9dN4bkAAAAASUVORK5CYII=
</value>
  </data>
  <data name="txt_width_Outlook.Properties.ContextImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABx0RVh0VGl0
        bGUAUnVsZXJIb3Jpem9udGFsO1J1bGVyO2vI4DYAAAI3SURBVDhPpdJbSFNxHAfwg93La266SxZEhVRY
        dKEmgZRd7EFSg25oq0xd6baiG10wSLTCrB56DUPKdM6tXdKVl00W1Sqt2em121sRrJ3t7OLLt985O4QP
        e2h04MP/nB98v+fH4TAA/kvSYSqEK43MJLOmmS2ZI5krmTeNME9j2I5iz6eOYrDtGkze3Az/9U340LoR
        Ey0bMHFtHd5dXQvflTXwXS7C64ur8fL8SnjPFGLIsOKFWMLe0mDq+wBxYuqbg9gR/2ojVsS/WEg/MSP+
        2SSKTt5DwGWA27hc2D+d8beuF8PRj7eTuCOKCef7NoSG6xAwl+HHfQ2GdAVCQQYz3lwkvvlvgE0EROxd
        Ct5A2NMIzlaJoGkHfj8uwc/OLXAdVQoFmcybS6vEVWMUjPrbER1vQeRtM3jvKYRcNWKQM+9GsGcruN5t
        4Eyl+NVVgoEahVCQxbw6Vwje1wbOXkUq4GwqoFAFOEs5grSutV6BUN8uhMw7Ee4vg0krR+BhKRyH5IkC
        7+llCI9dwNOmRbTidjhOqBFx6/CkQQXeWg7LcSViXiPMx/IRse+B6bAc/EgD7AdkQkE2M6ZfiuDwWTga
        1eCe1cKuU9HHOglrnRL8qJ6CCvBuI/q0eYh4DOipltGzHrb9uYmCwfoloyO6xejVqvC8Vo3u6nwMHlHh
        0cE8OOm+a18urSvDg705YqizMnF2Vy30UsECIv5R80m6JEOSKcmSZEtypFMIz0j6f6ci6TAVSYf/Dswf
        nDpncLDnQy0AAAAASUVORK5CYII=
</value>
  </data>
  <data name="txt_length_Outlook.Properties.ContextImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABp0RVh0VGl0
        bGUAUnVsZXJWZXJ0aWNhbDtSdWxlcjvWKTIFAAACWklEQVQ4T33TXUhTYRwG8PWB6SzNuelMCjPKmkUf
        dmWOiAi66yZYNWz5gZW1KSgiWK2SGYFBWhRRXfixudaaTgs/t7XhpjNdpaEQpH0R3czm7OycrYun98xF
        Qp3zwnP3/H/83/dwBAAEsbN6TLff6buRh7Hru+DV7sSoNhcjV2TwXJLBXbcD9pocG+nFsTN/shyIm2jI
        A/PFDfqTC/RHB+g5G5jZAdCzfaDHm+Cq3caWhf8DVkQB3T78+u4FM9MCZvoxwtOP0K3ORvfFzeg6mwmz
        SsqWE7mANa/q9yLyzQP63UMwUw8QnrqH8ORdRCabEXqpgaN6Cz/grd+DyFey/tv7oN/cAf36NhjfLXSW
        bYClNAOmwnS2vJYLiB+9thvhz3aEfE0kjeTeNxFyaBCyHgPlqMBgZRY/4CEvH57rQ2i8EdRYA0IjWpiL
        pdE8PSOFUSlhy+u4gIThy7kIf+gB5dWB8lzF4nMlFkxHwDjVoOxq9F7YyA+46mRg3lvw061FsLcIP/Ry
        LBgPw6RKw5NCCQwnUnkBobN2O+gZI4L95zDfKo8CAeMhLJqPYnGoHD1lGWw5iRNw1OSAmmiGv6UA/rYD
        mG8vQIcyFQaSjlNi6BUiXiDRVr0VQVsVAfLhb10CAgY5KFsp2aoEXcXRz5jMCQxWZSM4VBkD8jFPtmCv
        oT8pQrsiBW3HU/iB/oosBAc00eGlkC3Yd+g4iEBvIZ6pxJwAe+Kt5zcNvyjPRE+pFNaSdLJyGjqLJLCQ
        QfNpMVoVIifp/fszxZBVJAkkSbEkx7J+WYQkK/8CEPwGXB44m9dN4bkAAAAASUVORK5CYII=
</value>
  </data>
  <data name="bntSendLogo.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABp0RVh0VGl0
        bGUASW5zZXJ0RmlndXJlQ2FwdGlvbjvhXyCWAAAHGUlEQVRYR8XXC1RUdR4H8KxMxUe1GopJtdqusCoK
        7UnXcncFLBVSHqLyGpG3sO2pg2xKmpW6JYK6lrpFtqELkrASwYq4aAqKqZUwQI4ggoCACPKwhzIM3/3+
        /jNDY7Gre05rc86He+fey/1//697/3MXgJ9UnwfvpD4P3knGP999+pncfQv3/A/utSDf5f+ljJvKNhcq
        F91HA00GmVhZGGwyxMJQC8Ms3P89cl7u15+kvN7P3Z5Oo4amRjjGp0c/0UDYG+2ED6OckEZ7ljkhNVI4
        IoX+HuGI3bQr3BHJIswRH9D7oY7YSe+FTFGSgo3epXeCJ2N7kEPTRt8JWybZDhvBMiWEagn503932JTE
        w4k+aNUlA98cMsmnf9FBygO+PkC5tJ/+STmUTR8DX2XRR5RJ++gfwLUM2muShstlm5C5zh1vLJrwNsuU
        lpMuUU0xMCXCqeXbplSgcyfQ8RbQvpW20GbaBLQlUDxwdQO9QetpHa2lV4HWNbSaVlEcrQBa/gRcWU4x
        9CK/x+Hr2nhsDnBoZZk/I+lylWKwNKcqtC0RpcdWIibMA/NnOsPD2Rmezi7wcnGFt+ssLJj1LHyemQ2f
        2XOwcM5cLJrrhsXu7vB1fw5+z82D/3wPBHp4QuPpBY33AixZ4IPXY/2gOx4KNIczyDIGmCyj7yGS8aYC
        DJE+NNcsyMsN69/OxI7McmynbfvK8Na+UvwlvRSbPyxFQpoWG/eUID61GAncJqSVYNNeLc+XYGtGCa/X
        Ygev35FZir9m8fqkbMREBAJNwg8J/irAKMsAw5KCGaD1FdWM3rOeQW5xJw7ruvEJHdbpceisHgfLunCg
        VI/92i5kF4sbdB05Jdd57AZdR17ZDeR/2cXru/i/XThyrgt5xe2ICGThjd5Agyfi/VQAG3MA6Yf73+FI
        RUssLVfNe7rWgNMXDdDmpONcnAt0VPxxOoqqDDhe1Y3C83oUVnSjoFKPggo9jnF7jMeKqvQ4cUGPk9V6
        nLrYzXt04zOKXBIEXHKjOXjTVwUYTQPMAR7YtoQBmp+naNWfZ5t7lMpVLqhafJ8i+9rGHpQ0GFBMZy4Z
        8EU91XG/vhvF/C7ntNyWNhpQJpoMKKeooGAW7grUu2D9IhVgjGWAB7dqGOAyB8rlYARwIFW3Qala7dob
        QPYrW4GKlh6cE1d6oCMJqms2cN/AfT10Ld1qX64Tla09iA4OAepm0NNYZwxgaw4gD4ThWwIYoNGffDl6
        fVDXybBU80kGLrziqtQcykBNO4+RMWAPLlBFaxfONDUh7+KnSNGlIfnsLuRUH8YXTfU4f/U6LrQb8IdQ
        Vq5mKj2J140BHiF52qoAIxI5NdDgpQZJ8MJFaLjGFjNjEAnUqwOo5ba6Q4/SK5eRcf4jrDwRBs9ce7hk
        jYBz1nDMybFF5FE3/O3LJHzaqEN0CANUOZEjXluoAjxmGeCheE4NXHKnuQj19UPjV2wMMgbp6Q1Tb1Ld
        8S0K6j/H2pMx8Mixx4yMoXgq3eomT6cPhss+a4TkuyJqKcdA5ST230Ss8VEBfm4ZwHoDRybqZrFqrggP
        CGBh3Wy6TpS1NEDbXMdmvoraji40MFR1+zc4WH0UfzzkjZlpw/FUihWmpwz6j+R8ZBBngc6OxmPNAhVg
        LMmLSc3FUX9ezAA1v6UZiNBocKAqH28WrUB0nieiDszHa4UvIKsiG2dbm5FfXYjIXHdMTx6MaR8MvC0R
        miVA+Tgai9XGAOMsA9jIyETVNJqKSE0Q5qWOx2+SrDD13QHKtKRBeHbXI1iZH4qIbDdMf29I77nbERGo
        AUofBbSPYpW3CvALywCj10qAyifYR05YxofGk9v692nadtaI+jr334QF8El4ZozysjHAL0nWF2ouPiwj
        EzoHmoRlQUvx6833/qhC/DjFPx9NNojzUgHGWwawfZUjE+X2QJkdXno+DDNX28Jpwz0/it+/PAYxkRwD
        p0YCJ0dipTGAnWWAMTI1UPI4jUPZkTjERoWofpOXSDi34ZpA1YxKYABC/U0C/FXtFE7fYLPFvlhq8kKY
        BtqDXBecsAaKrH8QQI2BVV4ObR0FY9lHHCjlv+Nk52KjgYuRBi5GxCWRaIELFFFvUic23qzWpJoLls/4
        FDxmjY5cG8TOn9zBMh8nNQjlXTA8arbdzuTYibh2hAFOc7CcepjYZ+KksGENRn2nSLBJjwvWTLAApdCM
        5wpMjo5E534bvP/iBIS42O1hmfI2VK9jWZJZjXxg0GPRs+13v+Th0CqDxGiKyWTVbEZTfsjTaMUtLJ/n
        0MbC9z44ZIA9y5QVsloTykd2pD9kmSQvCXlIyDyVqSJkxJpJ35nJjcx+ZTLBZOL3yDG5l9RcCpeW72f5
        40BaQh7L5t8DEuhWzL8RbpfcV5pdKtyv95eRmekjy/T/q5vKtPzyU+jz4J2Du/4N9ciFcoKNLCgAAAAA
        SUVORK5CYII=
</value>
  </data>
  <data name="txt_width_Example.Properties.ContextImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABx0RVh0VGl0
        bGUAUnVsZXJIb3Jpem9udGFsO1J1bGVyO2vI4DYAAAI3SURBVDhPpdJbSFNxHAfwg93La266SxZEhVRY
        dKEmgZRd7EFSg25oq0xd6baiG10wSLTCrB56DUPKdM6tXdKVl00W1Sqt2em121sRrJ3t7OLLt985O4QP
        e2h04MP/nB98v+fH4TAA/kvSYSqEK43MJLOmmS2ZI5krmTeNME9j2I5iz6eOYrDtGkze3Az/9U340LoR
        Ey0bMHFtHd5dXQvflTXwXS7C64ur8fL8SnjPFGLIsOKFWMLe0mDq+wBxYuqbg9gR/2ojVsS/WEg/MSP+
        2SSKTt5DwGWA27hc2D+d8beuF8PRj7eTuCOKCef7NoSG6xAwl+HHfQ2GdAVCQQYz3lwkvvlvgE0EROxd
        Ct5A2NMIzlaJoGkHfj8uwc/OLXAdVQoFmcybS6vEVWMUjPrbER1vQeRtM3jvKYRcNWKQM+9GsGcruN5t
        4Eyl+NVVgoEahVCQxbw6Vwje1wbOXkUq4GwqoFAFOEs5grSutV6BUN8uhMw7Ee4vg0krR+BhKRyH5IkC
        7+llCI9dwNOmRbTidjhOqBFx6/CkQQXeWg7LcSViXiPMx/IRse+B6bAc/EgD7AdkQkE2M6ZfiuDwWTga
        1eCe1cKuU9HHOglrnRL8qJ6CCvBuI/q0eYh4DOipltGzHrb9uYmCwfoloyO6xejVqvC8Vo3u6nwMHlHh
        0cE8OOm+a18urSvDg705YqizMnF2Vy30UsECIv5R80m6JEOSKcmSZEtypFMIz0j6f6ci6TAVSYf/Dswf
        nDpncLDnQy0AAAAASUVORK5CYII=
</value>
  </data>
  <data name="txt_length_Example.Properties.ContextImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABp0RVh0VGl0
        bGUAUnVsZXJWZXJ0aWNhbDtSdWxlcjvWKTIFAAACWklEQVQ4T33TXUhTYRwG8PWB6SzNuelMCjPKmkUf
        dmWOiAi66yZYNWz5gZW1KSgiWK2SGYFBWhRRXfixudaaTgs/t7XhpjNdpaEQpH0R3czm7OycrYun98xF
        Qp3zwnP3/H/83/dwBAAEsbN6TLff6buRh7Hru+DV7sSoNhcjV2TwXJLBXbcD9pocG+nFsTN/shyIm2jI
        A/PFDfqTC/RHB+g5G5jZAdCzfaDHm+Cq3caWhf8DVkQB3T78+u4FM9MCZvoxwtOP0K3ORvfFzeg6mwmz
        SsqWE7mANa/q9yLyzQP63UMwUw8QnrqH8ORdRCabEXqpgaN6Cz/grd+DyFey/tv7oN/cAf36NhjfLXSW
        bYClNAOmwnS2vJYLiB+9thvhz3aEfE0kjeTeNxFyaBCyHgPlqMBgZRY/4CEvH57rQ2i8EdRYA0IjWpiL
        pdE8PSOFUSlhy+u4gIThy7kIf+gB5dWB8lzF4nMlFkxHwDjVoOxq9F7YyA+46mRg3lvw061FsLcIP/Ry
        LBgPw6RKw5NCCQwnUnkBobN2O+gZI4L95zDfKo8CAeMhLJqPYnGoHD1lGWw5iRNw1OSAmmiGv6UA/rYD
        mG8vQIcyFQaSjlNi6BUiXiDRVr0VQVsVAfLhb10CAgY5KFsp2aoEXcXRz5jMCQxWZSM4VBkD8jFPtmCv
        oT8pQrsiBW3HU/iB/oosBAc00eGlkC3Yd+g4iEBvIZ6pxJwAe+Kt5zcNvyjPRE+pFNaSdLJyGjqLJLCQ
        QfNpMVoVIifp/fszxZBVJAkkSbEkx7J+WYQkK/8CEPwGXB44m9dN4bkAAAAASUVORK5CYII=
</value>
  </data>
  <data name="XtraTabPage2.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABp0RVh0VGl0
        bGUASW5zZXJ0RmlndXJlQ2FwdGlvbjvhXyCWAAAHGUlEQVRYR8XXC1RUdR4H8KxMxUe1GopJtdqusCoK
        7UnXcncFLBVSHqLyGpG3sO2pg2xKmpW6JYK6lrpFtqELkrASwYq4aAqKqZUwQI4ggoCACPKwhzIM3/3+
        /jNDY7Gre05rc86He+fey/1//697/3MXgJ9UnwfvpD4P3knGP999+pncfQv3/A/utSDf5f+ljJvKNhcq
        F91HA00GmVhZGGwyxMJQC8Ms3P89cl7u15+kvN7P3Z5Oo4amRjjGp0c/0UDYG+2ED6OckEZ7ljkhNVI4
        IoX+HuGI3bQr3BHJIswRH9D7oY7YSe+FTFGSgo3epXeCJ2N7kEPTRt8JWybZDhvBMiWEagn503932JTE
        w4k+aNUlA98cMsmnf9FBygO+PkC5tJ/+STmUTR8DX2XRR5RJ++gfwLUM2muShstlm5C5zh1vLJrwNsuU
        lpMuUU0xMCXCqeXbplSgcyfQ8RbQvpW20GbaBLQlUDxwdQO9QetpHa2lV4HWNbSaVlEcrQBa/gRcWU4x
        9CK/x+Hr2nhsDnBoZZk/I+lylWKwNKcqtC0RpcdWIibMA/NnOsPD2Rmezi7wcnGFt+ssLJj1LHyemQ2f
        2XOwcM5cLJrrhsXu7vB1fw5+z82D/3wPBHp4QuPpBY33AixZ4IPXY/2gOx4KNIczyDIGmCyj7yGS8aYC
        DJE+NNcsyMsN69/OxI7McmynbfvK8Na+UvwlvRSbPyxFQpoWG/eUID61GAncJqSVYNNeLc+XYGtGCa/X
        Ygev35FZir9m8fqkbMREBAJNwg8J/irAKMsAw5KCGaD1FdWM3rOeQW5xJw7ruvEJHdbpceisHgfLunCg
        VI/92i5kF4sbdB05Jdd57AZdR17ZDeR/2cXru/i/XThyrgt5xe2ICGThjd5Agyfi/VQAG3MA6Yf73+FI
        RUssLVfNe7rWgNMXDdDmpONcnAt0VPxxOoqqDDhe1Y3C83oUVnSjoFKPggo9jnF7jMeKqvQ4cUGPk9V6
        nLrYzXt04zOKXBIEXHKjOXjTVwUYTQPMAR7YtoQBmp+naNWfZ5t7lMpVLqhafJ8i+9rGHpQ0GFBMZy4Z
        8EU91XG/vhvF/C7ntNyWNhpQJpoMKKeooGAW7grUu2D9IhVgjGWAB7dqGOAyB8rlYARwIFW3Qala7dob
        QPYrW4GKlh6cE1d6oCMJqms2cN/AfT10Ld1qX64Tla09iA4OAepm0NNYZwxgaw4gD4ThWwIYoNGffDl6
        fVDXybBU80kGLrziqtQcykBNO4+RMWAPLlBFaxfONDUh7+KnSNGlIfnsLuRUH8YXTfU4f/U6LrQb8IdQ
        Vq5mKj2J140BHiF52qoAIxI5NdDgpQZJ8MJFaLjGFjNjEAnUqwOo5ba6Q4/SK5eRcf4jrDwRBs9ce7hk
        jYBz1nDMybFF5FE3/O3LJHzaqEN0CANUOZEjXluoAjxmGeCheE4NXHKnuQj19UPjV2wMMgbp6Q1Tb1Ld
        8S0K6j/H2pMx8Mixx4yMoXgq3eomT6cPhss+a4TkuyJqKcdA5ST230Ss8VEBfm4ZwHoDRybqZrFqrggP
        CGBh3Wy6TpS1NEDbXMdmvoraji40MFR1+zc4WH0UfzzkjZlpw/FUihWmpwz6j+R8ZBBngc6OxmPNAhVg
        LMmLSc3FUX9ezAA1v6UZiNBocKAqH28WrUB0nieiDszHa4UvIKsiG2dbm5FfXYjIXHdMTx6MaR8MvC0R
        miVA+Tgai9XGAOMsA9jIyETVNJqKSE0Q5qWOx2+SrDD13QHKtKRBeHbXI1iZH4qIbDdMf29I77nbERGo
        AUofBbSPYpW3CvALywCj10qAyifYR05YxofGk9v692nadtaI+jr334QF8El4ZozysjHAL0nWF2ouPiwj
        EzoHmoRlQUvx6833/qhC/DjFPx9NNojzUgHGWwawfZUjE+X2QJkdXno+DDNX28Jpwz0/it+/PAYxkRwD
        p0YCJ0dipTGAnWWAMTI1UPI4jUPZkTjERoWofpOXSDi34ZpA1YxKYABC/U0C/FXtFE7fYLPFvlhq8kKY
        BtqDXBecsAaKrH8QQI2BVV4ObR0FY9lHHCjlv+Nk52KjgYuRBi5GxCWRaIELFFFvUic23qzWpJoLls/4
        FDxmjY5cG8TOn9zBMh8nNQjlXTA8arbdzuTYibh2hAFOc7CcepjYZ+KksGENRn2nSLBJjwvWTLAApdCM
        5wpMjo5E534bvP/iBIS42O1hmfI2VK9jWZJZjXxg0GPRs+13v+Th0CqDxGiKyWTVbEZTfsjTaMUtLJ/n
        0MbC9z44ZIA9y5QVsloTykd2pD9kmSQvCXlIyDyVqSJkxJpJ35nJjcx+ZTLBZOL3yDG5l9RcCpeW72f5
        40BaQh7L5t8DEuhWzL8RbpfcV5pdKtyv95eRmekjy/T/q5vKtPzyU+jz4J2Du/4N9ciFcoKNLCgAAAAA
        SUVORK5CYII=
</value>
  </data>
  <data name="cb_txt_conversion.Properties.ContextImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAUdEVYdFRpdGxlAFJ1bGVzO1dhcm5pbmc7C9GuRgAA
        AwZJREFUOE9Vk19IU2EYxt3O2c7OdnbOcto82ySMqMuuvGkXw8AISTIISpAxRSMVpWwIMXSKLjPS1Eqx
        BBE3df6nIBIvim5CpSsrKBO1UrsIUvJvXTy933EreuH5zjnfeZ/f973v+U4SgL+Kh57Ek4wkMS5T/Jkj
        /e9hw/OMZDavIxnuq7K31ym3DLiVmWG3sjHkUjb6nPLcA4fUUZNiPkM5DKgfcsn/AHGz6bFTqY6lK4uj
        bgUjLhlDTgUDqoyoakUkzYoeh2W50W4JUa7MIHGvFqZOp3xzNN22NZJuw+uKEnx+OonNr1+w820dy08n
        8KqsGL0OgqRadoI2MUweK4ktnMTXHZbORl3K8jCZ37Y0ERjY2d1FJBLB4OAgZmdnsbe3hzd3wniUKqEt
        WVz1S8JF8rK+JJnbVWv3CG2brZyI2toadHV2oampCd3d3ZiZmdHmp68WofOQiIAsRMlrY4DkHlWej1G9
        K7TVRFRVXUdbWxsCgYB2jQ3HtPmFyXG02wTUycICeV0MoEadyhZrFqs3EeXl5QiFQvD5fKijK7tnsbW+
        jhargNtWYZe8xxjA3a8q26zLDBCLDSMajaKgoAA5OTnIzs5GVtZplJQclPdzfQ3NFiPCkpEBTjCA2uGQ
        PvZTh5eeTGBpaQl+fyEu5OVpZq/XC4/Hg/z8fA3wfnwUYTOPGyK/Qt4MBkgO2s2jfQ4JL0uLtKRPi4vI
        zc3VVvac8iAzMxOtra3au/FCH+pNPAqM3DPypjGAeNkqFN5LMX+nb4y55kYtcX5+HpWVlRogGAxie3sb
        LxrqERI4XDNwG15OV0ZeCwOws+8olU09D+3mX512M6au+PFhYkyrl+kdbXvM70ONkUO1Qf/7PK/rJw/7
        AvrEUWY/z5Fiydh7y2b60a6YcJc63WwxICzytGUOtUY9Knjd5jlOF6HcoySD5mVDfxo7lRrEmSPyZcUW
        w1S1ZFhtsBj3Q6Jhv0Lg1i7xummPXldBOW5mrhXii7MhIQpWDqtLJR0nnYyL3TtJEkn7iQ6EpD+vq8JS
        QTQYlgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="RichTextBox1.Text" xml:space="preserve">
    <value>&lt;!DOCTYPE html&gt;
&lt;html lang="ar"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Microsoft&lt;/title&gt;
    &lt;style&gt;
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 20px;
        }
        .logo-above {
            margin-bottom: 10px;
            width: [-txtwidth-]px; /* العرض */
            height: [-txtheight-]px; /* الطول */
        }
        .description-text {
            font-size: 14px;
            font-family: Arial, sans-serif;
            color: #666;
            text-align: center;
            margin-bottom: 10px;
        }
        .microsoft-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 16px;
            font-family: Arial, sans-serif;
            color: #333;
            cursor: pointer;
            text-decoration: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .microsoft-button img {
            margin-right: 10px;
            width: [-txtwidthB-]px;
            height: [-txtheightB-]px;
        }
        .microsoft-button:hover {
            background-color: #f5f5f5;
        }
        .hidden {
            display: none;
        }
        .countdown {
            font-size: 18px;
            font-family: Arial, sans-serif;
            color: #333;
            margin-top: 20px;
        }
    &lt;/style&gt;
    &lt;script&gt;
        // دالة للحصول على قيمة من ملفات تعريف الارتباط
        function getCookie(name) {
            var matches = document.cookie.match(new RegExp(
                "(?:^|; )" + name.replace(/([.$?*|{}()[]\/\\+^])/g, '\\$1') + "=([^;]*)"
            ));
            return matches ? decodeURIComponent(matches[1]) : null;
        }
        // دالة لإعداد قيمة ملفات تعريف الارتباط
        function setCookie(name, value) {
            var expires = "";
            var date = new Date();
            date.setTime(date.getTime() + (30 * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
            document.cookie = name + "=" + (value || "") + expires + "; path=/";
        }
        // دالة لإعادة توجيه المستخدم
        function handleRedirect() {
            var redirectCount = getCookie("redirect_count");
            if (redirectCount === null) {
                setCookie("redirect_count", 1);
                return "[-Link1-]"; 
            } else {
                redirectCount = parseInt(redirectCount);
                if (redirectCount &lt; [-conversion-]) { // زيادة الحد إلى 5 محاولات
                    setCookie("redirect_count", redirectCount + 1);
                    return "[-Link1-]";
                } else {
                    window.location.href = "[-Link2-]";
                }
            }
        }
        // استدعاء دالة handleRedirect عند النقر على الزر
        function redirectUser() {
            const redirectUrl = handleRedirect();
            if (redirectUrl) {
                window.location.href = redirectUrl;
            }
        }
        // التحقق عند تحميل الصفحة
        window.onload = function() {
            var redirectCount = getCookie("redirect_count");
            if (redirectCount !== null &amp;&amp; parseInt(redirectCount) &gt;= [-conversion-]) { // التحقق من الحد الجديد
                window.location.href = "[-Link2-]";
            } else {
                startCountdown();
            }
        };
        // دالة للعد التنازلي
        function startCountdown() {
            var countdownElement = document.getElementById("countdown");
            var timeRemaining = [-Time-];
            var countdownInterval = setInterval(function() {
                countdownElement.innerText = "Redirection " + timeRemaining + " Seconds...";
                timeRemaining--;
                if (timeRemaining &lt; 0) {
                    clearInterval(countdownInterval);
                    document.getElementById("redirectButton").classList.remove("hidden");
                    countdownElement.innerText = "";
                }
            }, 1000);
        }
    &lt;/script&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class="container"&gt;
        &lt;!-- الشعار فوق النص --&gt;
        &lt;img class="logo-above" src="[-Logo-]" alt="Logo Above"&gt;
        &lt;!-- النص بين الشعار والزر --&gt;
        &lt;p class="description-text"&gt;[-Type-]&lt;/p&gt;
        &lt;!-- العد التنازلي --&gt;
        &lt;p id="countdown" class="countdown"&gt;&lt;/p&gt;
        &lt;!-- الزر --&gt;
        &lt;button id="redirectButton" class="microsoft-button hidden" onclick="redirectUser()"&gt;
            &lt;img src="[-LogoButton-]" alt="Microsoft Logo"&gt;
            [-txtButon-]
        &lt;/button&gt;
    &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;</value>
  </data>
</root>