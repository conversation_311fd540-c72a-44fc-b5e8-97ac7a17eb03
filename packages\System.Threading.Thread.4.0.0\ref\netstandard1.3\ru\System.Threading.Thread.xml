﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Thread</name>
  </assembly>
  <members>
    <member name="T:System.Threading.ParameterizedThreadStart">
      <summary>Представляет метод, выполняющийся в объекте <see cref="T:System.Threading.Thread" />.</summary>
      <param name="obj">Объект, содержащий данные потоковой процедуры.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.Thread">
      <summary>Создает и контролирует поток, задает приоритет и возвращает статус. Исходный код .NET Framework для этого типа см. в указанном источнике.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.#ctor(System.Threading.ParameterizedThreadStart)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Threading.Thread" />, при этом указывается делегат, позволяющий объекту быть переданным в поток при запуске потока.</summary>
      <param name="start">Делегат, указывающий на методы, которые вызываются при запуске потока. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="start" /> is null. </exception>
    </member>
    <member name="M:System.Threading.Thread.#ctor(System.Threading.ThreadStart)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Threading.Thread" />.</summary>
      <param name="start">Делегат <see cref="T:System.Threading.ThreadStart" />, указывающий на методы, которые вызываются при запуске потока. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="start" /> parameter is null. </exception>
    </member>
    <member name="P:System.Threading.Thread.CurrentThread">
      <summary>Возвращает выполняющийся в данный момент поток.</summary>
      <returns>Объект <see cref="T:System.Threading.Thread" />, представляющий собой выполняющийся в данный момент поток.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.IsAlive">
      <summary>Возвращает значение, показывающее статус выполнения текущего потока.</summary>
      <returns>Значение true, если этот поток был запущен и не был завершен обычным образом либо был прерван; в противном случае — значение false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.IsBackground">
      <summary>Возвращает или задает значение, показывающее, является ли поток фоновым.</summary>
      <returns>Значение true, если этот поток является или станет фоновым потоком; в противном случае — значение false.</returns>
      <exception cref="T:System.Threading.ThreadStateException">The thread is dead. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Join">
      <summary>Блокирует вызывающий поток до завершения потока, продолжая отправлять стандартные сообщения COM и SendMessage.</summary>
      <exception cref="T:System.Threading.ThreadStateException">The caller attempted to join a thread that is in the <see cref="F:System.Threading.ThreadState.Unstarted" /> state. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">The thread is interrupted while waiting. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Join(System.Int32)">
      <summary>Блокирует вызывающий поток до завершения потока или истечения указанного времени, продолжая отправлять стандартные сообщения COM и SendMessage.</summary>
      <returns>Значение true, если поток завершился; значение false, если поток не завершился по истечении количества времени, заданного параметром <paramref name="millisecondsTimeout" />.</returns>
      <param name="millisecondsTimeout">Количество миллисекунд ожидания завершения потока. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> in milliseconds. </exception>
      <exception cref="T:System.Threading.ThreadStateException">The thread has not been started. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.ManagedThreadId">
      <summary>Возвращает уникальный идентификатор текущего управляемого потока. </summary>
      <returns>Целочисленное значение, представляющее уникальный идентификатор для этого управляемого потока.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.Name">
      <summary>Получает или задает имя потока.</summary>
      <returns>Строка, содержащая имя потока или null, если имя не задано.</returns>
      <exception cref="T:System.InvalidOperationException">A set operation was requested, but the Name property has already been set. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Sleep(System.Int32)">
      <summary>Приостанавливает текущий поток на заданное количество миллисекунд.</summary>
      <param name="millisecondsTimeout">Количество миллисекунд, на которое приостанавливается поток.Если значение аргумента <paramref name="millisecondsTimeout" /> равно нулю, поток освобождает оставшуюся часть своего интервала времени для любого потока с таким же приоритетом, готовым к выполнению.Если других готовых к выполнению потоков с таким же приоритетом нет, выполнение текущего потока не приостанавливается.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The time-out value is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Sleep(System.TimeSpan)">
      <summary>Приостанавливает текущий поток на заданное время.</summary>
      <param name="timeout">Время, на которое приостанавливается поток.Если значение аргумента <paramref name="millisecondsTimeout" /> равно <see cref="F:System.TimeSpan.Zero" />, поток освобождает оставшуюся часть своего интервала времени для любого потока с таким же приоритетом, готовым к выполнению.Если других готовых к выполнению потоков с таким же приоритетом нет, выполнение текущего потока не приостанавливается.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> in milliseconds, or is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Start">
      <summary>Вынуждает операционную систему изменить состояние текущего экземпляра на <see cref="F:System.Threading.ThreadState.Running" />.</summary>
      <exception cref="T:System.Threading.ThreadStateException">The thread has already been started. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available to start this thread. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Start(System.Object)">
      <summary>Заставляет операционную систему изменить состояние текущего экземпляра на <see cref="F:System.Threading.ThreadState.Running" />, а также (необязательно) передает объект с данными, используемыми методом в потоке.</summary>
      <param name="parameter">Объект, содержащий данные, используемые методом, который выполняется потоком.</param>
      <exception cref="T:System.Threading.ThreadStateException">The thread has already been started. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available to start this thread. </exception>
      <exception cref="T:System.InvalidOperationException">This thread was created using a <see cref="T:System.Threading.ThreadStart" /> delegate instead of a <see cref="T:System.Threading.ParameterizedThreadStart" /> delegate.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.ThreadState">
      <summary>Возвращает значение, содержащее состояния текущего потока.</summary>
      <returns>Одно из значений <see cref="T:System.Threading.ThreadState" />, показывающее состояние текущего потока.Начальное значение — Unstarted.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.ThreadStart">
      <summary>Представляет метод, выполняющийся в объекте <see cref="T:System.Threading.Thread" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.ThreadStartException">
      <summary>Исключение выдается, когда происходит сбой в управляемом потоке после запуска базового потока операционной системы, но до готовности потока к выполнению кода пользователя.</summary>
    </member>
    <member name="T:System.Threading.ThreadState">
      <summary>Задает состояния выполнения объекта <see cref="T:System.Threading.Thread" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Threading.ThreadState.Aborted">
      <summary>Состояние потока включает в себя значение <see cref="F:System.Threading.ThreadState.AbortRequested" />, и поток теперь не выполняет работу, но его состояние еще не изменилось на <see cref="F:System.Threading.ThreadState.Stopped" />.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.AbortRequested">
      <summary>Метод <see cref="M:System.Threading.Thread.Abort(System.Object)" /> был вызван для потока, но поток еще не получил исключение <see cref="T:System.Threading.ThreadAbortException" />, которое попытается завершить его.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Background">
      <summary>Поток выполняется как фоновый поток, в противоположность потокам переднего плана.Это состояние управляется заданием свойства <see cref="P:System.Threading.Thread.IsBackground" />.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Running">
      <summary>Поток был запущен, он не заблокирован, и нет ожидающего исключения <see cref="T:System.Threading.ThreadAbortException" />.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Stopped">
      <summary>Поток был остановлен.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.StopRequested">
      <summary>Поток получает запрос на остановку.Предназначено только для внутреннего использования.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Suspended">
      <summary>Поток был приостановлен.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.SuspendRequested">
      <summary>Запрашивается приостановка работы потока.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Unstarted">
      <summary>Метод <see cref="M:System.Threading.Thread.Start" /> не был вызван для потока.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.WaitSleepJoin">
      <summary>Поток заблокирован.Это может произойти в результате вызова метода <see cref="M:System.Threading.Thread.Sleep(System.Int32)" /> или метода <see cref="M:System.Threading.Thread.Join" />, в результате запроса блокировки, например при вызове метода <see cref="M:System.Threading.Monitor.Enter(System.Object)" /> или <see cref="M:System.Threading.Monitor.Wait(System.Object,System.Int32,System.Boolean)" /> или в результате ожидания объекта синхронизации потока, такого как <see cref="T:System.Threading.ManualResetEvent" />.</summary>
    </member>
    <member name="T:System.Threading.ThreadStateException">
      <summary>Исключение, которое выдается, когда объект <see cref="T:System.Threading.Thread" /> является неправильным свойством <see cref="P:System.Threading.Thread.ThreadState" /> для вызова метода.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Threading.ThreadStateException" /> со свойствами по умолчанию.</summary>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Threading.ThreadStateException" /> с указанным сообщением об ошибке.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения. </param>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Threading.ThreadStateException" /> указанным сообщением об ошибке и ссылкой на внутреннее исключение, которое стало причиной данного исключения.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения. </param>
      <param name="innerException">Исключение, которое вызвало текущее исключение.Если значение параметра <paramref name="innerException" /> не равно null, текущее исключение сгенерировано в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
  </members>
</doc>