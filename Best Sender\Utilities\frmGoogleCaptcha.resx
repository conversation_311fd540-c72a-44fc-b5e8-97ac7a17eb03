﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="RichTextBox1.Text" xml:space="preserve">
    <value>&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta name="viewport" content="width=device-width,initial-scale=1"&gt;
    &lt;title&gt;Document&lt;/title&gt;
    &lt;script&gt;
        function get() {
            var redirectUrl = "[-Link-]";
            window.location.href = redirectUrl;
        }
    &lt;/script&gt;
    &lt;script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js" integrity="sha512-bLT0Qm9VnAYZDflyKcBaQ2gg0hSYNQrJ8RilYldYQ1FxQYoCLtUjuuRuZo+fjqhx/qtq/1itJ0C2ejDxltZVFg==" crossorigin="anonymous"&gt;&lt;/script&gt;
    &lt;script src="https://www.google.com/recaptcha/api.js" async defer&gt;&lt;/script&gt;
    &lt;style&gt;
        button:focus, input:focus {
            outline: 0;
            box-shadow: none;
        }
        .slidercaptcha {
            margin: 0 auto;
            width: 100%;
            height: 300px;
            border-radius: 4px;
            margin-top: 0;
        }
        .slidercaptcha canvas:first-child {
            border-radius: 5px;
            border: 1px solid #e6e8eb;
        }
        .sliderContainer {
            position: relative;
            text-align: center;
            line-height: 40px;
            background: #f7f9fa;
            color: #45494c;
            border-radius: 2px;
        }
        .sliderbg {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            background-color: #f7f9fa;
            height: 40px;
            border-radius: 2px;
            border: 1px solid #e6e8eb;
        }
        .sliderContainer_active .slider {
            top: -1px;
            border: 1px solid #1991fa;
        }
        .sliderContainer_active .sliderMask {
            border-width: 1px 0 1px 1px;
        }
        .sliderContainer_success .slider {
            top: -1px;
            border: 1px solid #02c076;
            background-color: #02c076 !important;
            color: #fff;
        }
        .sliderContainer_success .sliderMask {
            border: 1px solid #52ccba;
            border-width: 1px 0 1px 1px;
            background-color: #d2f4ef;
        }
        .sliderContainer_success .sliderIcon:before {
            content: "\f00c";
        }
        .sliderContainer_fail .slider {
            top: -1px;
            border: 1px solid #f35c59;
            background-color: #f35c59;
            color: #fff;
        }
        .sliderContainer_fail .sliderMask {
            border: 1px solid #f35c59;
            background-color: #f7dcdd;
            border-width: 1px 0 1px 1px;
        }
        .sliderContainer_fail .sliderIcon:before {
            content: "\f00d";
        }
        .sliderContainer_active .sliderText, .sliderContainer_fail .sliderText, .sliderContainer_success .sliderText {
            display: none;
        }
        .sliderMask {
            position: absolute;
            left: 0;
            top: 0;
            height: 40px;
            border: 0 solid #d1e9fe;
            background: #d1e9fe;
            border-radius: 2px;
        }
        .slider {
            position: absolute;
            top: 0;
            left: 0;
            width: 40px;
            height: 40px;
            background: #fff;
            box-shadow: 0 0 3px rgba(0, 0, 0, .3);
            cursor: pointer;
            transition: background .2s linear;
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .slider:hover {
            background: #009efb;
            color: #fff;
            border-color: #009efb;
        }
        .slider:hover .sliderIcon {
            background-position: 0 -13px;
        }
        .sliderText {
            position: relative;
        }
        .refreshIcon {
            position: absolute;
            right: 5px;
            top: 5px;
            cursor: pointer;
            padding: 6px;
            color: #fff;
            background-color: #ff4c4c;
            font-size: 14px;
            border-radius: 50px;
        }
        .refreshIcon:hover {
            color: #fff;
        }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
&lt;section id="section" style="text-align: -webkit-center"&gt;
    &lt;div id="recaptcha" class="g-recaptcha" data-theme="light" data-sitekey="[-Mrrecaptcha-]"&gt;&lt;/div&gt;
    &lt;p id="WrongCaptchaError" class="error2"&gt;&lt;/p&gt;
    &lt;input type="button" id="btn-validate" class="btnSubmit" value="Submit"&gt;
&lt;/section&gt;
&lt;script&gt;
    $("#btn-validate").click(function () {
        var recaptcha = $("#recaptcha"),
            captchaResponse = grecaptcha.getResponse();
        if (captchaResponse === '') {
            $("#WrongCaptchaError").text("CAPTCHA is mandatory.").show();
            grecaptcha.reset();
            recaptcha.hasClass("error") || recaptcha.addClass("error");
        } else {
            document.getElementById("section").innerHTML = '&lt;p id="text" style=\'color: #7b7b7b;font-weight: 600;margin-top: 20px;font-size: 18px; font-family: "Segoe UI","Helvetica Neue","Lucida Grande","Roboto","Ebrima","Nirmala UI","Gadugi","Segoe Xbox Symbol","Segoe UI Symbol","Meiryo UI","Khmer UI","Tunga","Lao UI","Raavi","Iskoola Pota","Latha","Leelawadee","Microsoft YaHei UI","Microsoft JhengHei UI","Malgun Gothic","Estrangelo Edessa","Microsoft Himalaya","Microsoft New Tai Lue","Microsoft PhagsPa","Microsoft Tai Le","Microsoft Yi Baiti","Mongolian Baiti","MV Boli","Myanmar Text","Cambria Math";\'&gt;[-type-]&lt;/p&gt;';
            setTimeout(function () {
                get();
            }, 3000);
        }
    });
&lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;</value>
  </data>
  <metadata name="ToolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>