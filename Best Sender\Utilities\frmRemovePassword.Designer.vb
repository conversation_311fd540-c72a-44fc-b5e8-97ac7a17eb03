<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmRemovePassword
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim GridLevelNode1 As DevExpress.XtraGrid.GridLevelNode = New DevExpress.XtraGrid.GridLevelNode()
        Me.MainPanel = New System.Windows.Forms.Panel()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.bntSave = New DevExpress.XtraEditors.SimpleButton()
        Me.bntStart = New DevExpress.XtraEditors.SimpleButton()
        Me.bntUploade = New DevExpress.XtraEditors.SimpleButton()
        Me.bntRestAll = New DevExpress.XtraEditors.SimpleButton()
        Me.SaveFileDialog1 = New System.Windows.Forms.SaveFileDialog()
        Me.OpenFileDialog1 = New System.Windows.Forms.OpenFileDialog()
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.MainPanel.SuspendLayout()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'MainPanel
        '
        Me.MainPanel.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.MainPanel.Controls.Add(Me.GridControl1)
        Me.MainPanel.Controls.Add(Me.ProgressPanel1)
        Me.MainPanel.Controls.Add(Me.bntSave)
        Me.MainPanel.Controls.Add(Me.bntStart)
        Me.MainPanel.Controls.Add(Me.bntUploade)
        Me.MainPanel.Controls.Add(Me.bntRestAll)
        Me.MainPanel.Location = New System.Drawing.Point(50, 50)
        Me.MainPanel.Name = "MainPanel"
        Me.MainPanel.Size = New System.Drawing.Size(872, 502)
        Me.MainPanel.TabIndex = 0
        '
        'GridControl1
        '
        GridLevelNode1.RelationName = "Level1"
        Me.GridControl1.LevelTree.Nodes.AddRange(New DevExpress.XtraGrid.GridLevelNode() {GridLevelNode1})
        Me.GridControl1.Location = New System.Drawing.Point(30, 30)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(812, 380)
        Me.GridControl1.TabIndex = 0
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.Editable = False
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.Caption = "Processing..."
        Me.ProgressPanel1.Description = "Please wait"
        Me.ProgressPanel1.Location = New System.Drawing.Point(30, 420)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(400, 70)
        Me.ProgressPanel1.TabIndex = 5
        Me.ProgressPanel1.Text = "ProgressPanel1"
        Me.ProgressPanel1.Visible = False
        '
        'bntSave
        '
        Me.bntSave.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.bntSave.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.0!, System.Drawing.FontStyle.Bold)
        Me.bntSave.Appearance.ForeColor = System.Drawing.Color.Black
        Me.bntSave.Appearance.Options.UseBackColor = True
        Me.bntSave.Appearance.Options.UseFont = True
        Me.bntSave.Appearance.Options.UseForeColor = True
        Me.bntSave.Location = New System.Drawing.Point(652, 430)
        Me.bntSave.Name = "bntSave"
        Me.bntSave.Size = New System.Drawing.Size(190, 50)
        Me.bntSave.TabIndex = 4
        Me.bntSave.Text = "Save"
        '
        'bntStart
        '
        Me.bntStart.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.bntStart.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.0!, System.Drawing.FontStyle.Bold)
        Me.bntStart.Appearance.ForeColor = System.Drawing.Color.Black
        Me.bntStart.Appearance.Options.UseBackColor = True
        Me.bntStart.Appearance.Options.UseFont = True
        Me.bntStart.Appearance.Options.UseForeColor = True
        Me.bntStart.Location = New System.Drawing.Point(456, 430)
        Me.bntStart.Name = "bntStart"
        Me.bntStart.Size = New System.Drawing.Size(190, 50)
        Me.bntStart.TabIndex = 3
        Me.bntStart.Text = "Start"
        '
        'bntUploade
        '
        Me.bntUploade.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.bntUploade.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.0!, System.Drawing.FontStyle.Bold)
        Me.bntUploade.Appearance.ForeColor = System.Drawing.Color.Black
        Me.bntUploade.Appearance.Options.UseBackColor = True
        Me.bntUploade.Appearance.Options.UseFont = True
        Me.bntUploade.Appearance.Options.UseForeColor = True
        Me.bntUploade.Location = New System.Drawing.Point(30, 430)
        Me.bntUploade.Name = "bntUploade"
        Me.bntUploade.Size = New System.Drawing.Size(190, 50)
        Me.bntUploade.TabIndex = 2
        Me.bntUploade.Text = "Upload"
        '
        'bntRestAll
        '
        Me.bntRestAll.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.bntRestAll.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.0!, System.Drawing.FontStyle.Bold)
        Me.bntRestAll.Appearance.ForeColor = System.Drawing.Color.Black
        Me.bntRestAll.Appearance.Options.UseBackColor = True
        Me.bntRestAll.Appearance.Options.UseFont = True
        Me.bntRestAll.Appearance.Options.UseForeColor = True
        Me.bntRestAll.Location = New System.Drawing.Point(243, 430)
        Me.bntRestAll.Name = "bntRestAll"
        Me.bntRestAll.Size = New System.Drawing.Size(190, 50)
        Me.bntRestAll.TabIndex = 1
        Me.bntRestAll.Text = "Reset All"
        '
        'OpenFileDialog1
        '
        Me.OpenFileDialog1.FileName = "OpenFileDialog1"
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'frmRemovePassword
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(30, Byte), Integer), CType(CType(30, Byte), Integer), CType(CType(30, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.MainPanel)
        Me.IconOptions.ShowIcon = False
        Me.Name = "frmRemovePassword"
        Me.ShowInTaskbar = False
        Me.Text = "Remove Password"
        Me.MainPanel.ResumeLayout(False)
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
End Class
