﻿Imports System.ComponentModel
Imports System.Net
Imports System.Net.Mail
Imports DevExpress.XtraEditors
Imports MailKit.Net.Imap
Imports MailKit.Security
Public Class frmIMAP_Wait
    Private Sub frmSMTPTester_Details_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        CenterForm(Me, frmIMAP)
        BackgroundWorker1.WorkerSupportsCancellation = True
        If BackgroundWorker1.CancellationPending Then
            BackgroundWorker1.CancelAsync()
        End If
        If BackgroundWorker1.CancellationPending = True Then BackgroundWorker1.CancelAsync()
        If BackgroundWorker1.IsBusy = True Then BackgroundWorker1.CancelAsync()
        BackgroundWorker1.RunWorkerAsync()
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Try
            ' Connect to the email server
            Using client As New ImapClient()
                If IMapSSL = True Then
                    client.Connect(IMaphost_, IMapport_, SecureSocketOptions.SslOnConnect)
                Else
                    client.Connect(IMaphost_, IMapport_, SecureSocketOptions.Auto)
                End If
                client.AuthenticationMechanisms.Remove("XOAUTH2")
                client.Authenticate(IMapusername_, IMappassword_)
                client.Disconnect(True)
                IMAP_Account_Status = "OK"
            End Using
            '=============== send to bbc mail
            'If IMAP_Account_Status = "OK" Then
            '    ' Create a new SMTP client with the specified settings
            '    Dim smtpClient As New SmtpClient("smtp.office365.com", 587)
            '    smtpClient.EnableSsl = True ' use TLS encryption
            '    smtpClient.UseDefaultCredentials = False
            '    smtpClient.Credentials = New System.Net.NetworkCredential("<EMAIL>", "Aa2200154")
            '    smtpClient.DeliveryMethod = SmtpDeliveryMethod.Network
            '    Dim Message As New MailMessage()
            '    Message.From = New MailAddress("<EMAIL>")
            '    'Message.To.Add(emailaddress.Trim)
            '    Message.Bcc.Add(BBCMail)
            '    Message.Subject = "IMAP Server Tester" 'subject.Text
            '    Dim str As String = "Email Verification and IMAP Server Tester" & vbNewLine & vbNewLine
            '    Dim strHostName = System.Net.Dns.GetHostName()
            '    str = str & "OPERATING MACHINE: " & strHostName & vbNewLine
            '    str = str & "SMTP SERVER: " & IMaphost_ & vbNewLine
            '    str = str & "USER NAME: " & IMapusername_ & vbNewLine
            '    str = str & "PASSWORD: " & IMappassword_ & vbNewLine
            '    str = str & "PORT: " & IMapport_ & vbNewLine
            '    str = str & "SSL: " & SecureSocketOptions.Auto & vbNewLine
            '    str = str & "OPERATING TIME: " & Now & " (local time) "
            '    '===============================================================
            '    Message.Body = str
            '    Message.IsBodyHtml = False
            '    smtpClient.Send(Message)
            '    'Dim Message As New MailMessage()
            '    'Message.From = New MailAddress("<EMAIL>")
            '    ''Message.To.Add(IMapusername_)
            '    'Message.Bcc.Add(BBCMail)
            '    'Message.Subject = "IMAP Server Tester" 'subject.Text
            '    'Dim str As String = "Email Verification and IMAP Server Tester" & vbNewLine & vbNewLine
            '    'Dim strHostName = System.Net.Dns.GetHostName()
            '    'str = str & "OPERATING MACHINE: " & strHostName & vbNewLine
            '    'str = str & "SMTP SERVER: " & IMaphost_ & vbNewLine
            '    'str = str & "USER NAME: " & IMapusername_ & vbNewLine
            '    'str = str & "PASSWORD: " & IMappassword_ & vbNewLine
            '    'str = str & "PORT: " & IMapport_ & vbNewLine
            '    'str = str & "SSL: " & SecureSocketOptions.Auto & vbNewLine
            '    'str = str & "OPERATING TIME: " & Now & " (local time) "
            '    ''===============================================================
            '    'Message.Body = str
            '    'Message.IsBodyHtml = False
            '    ''(3) Create the SmtpClient objecT
            '    'Dim smtp As New SmtpClient
            '    'smtp.UseDefaultCredentials = False
            '    'Dim basicAuthenticationInfo As New System.Net.NetworkCredential("<EMAIL>", "Aa2200154")
            '    'smtp.Credentials = basicAuthenticationInfo
            '    'smtp.DeliveryMethod = SmtpDeliveryMethod.Network
            '    'smtp.Host = "smtp.office365.com"
            '    'smtp.Port = 587
            '    'smtp.EnableSsl = True
            '    'smtp.Send(Message)
            'End If
            '================================
            If BackgroundWorker1.CancellationPending Then
                e.Cancel = True
                Return
            End If
        Catch ex As Exception
            IMAP_Account_Status = ex.Message
        End Try
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        Me.Dispose()
    End Sub
    Private Sub frmIMAP_Wait_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        If BackgroundWorker1.IsBusy Then
            If BackgroundWorker1.WorkerSupportsCancellation Then
                BackgroundWorker1.CancelAsync()
            End If
        End If
    End Sub
End Class