﻿Imports System.ComponentModel
Imports System.IO
Imports Chilkat

Public Class Form1
    Dim x, y As Integer
    Dim newpoint As New Point
    Private _data As DataModel
    Private quota As Integer
    Private WithEvents timer As New System.Timers.Timer

    Private bw1 As BackgroundWorker
    Private bw2 As BackgroundWorker
    Private bw3 As BackgroundWorker
    Private bw4 As BackgroundWorker
    Private bw5 As BackgroundWorker
    Private bw6 As BackgroundWorker
    Private bw7 As BackgroundWorker
    Private bw8 As BackgroundWorker
    Private bw9 As BackgroundWorker
    Private bw10 As BackgroundWorker
    Private bw11 As BackgroundWorker
    Private linesLock As Object

    Friend Property Data As DataModel
        Get
            Return _data
        End Get
        Set(value As DataModel)
            _data = value
        End Set
    End Property

    Private Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Data = New DataModel()
        linesLock = New Object()
        timer.Interval = 10000
        timer.SynchronizingObject = Me
        timer.Enabled = True

        Button16.Enabled = True
        Button15.Enabled = True
        Button14.Enabled = False
        Button13.Enabled = False
        Button12.Enabled = False
        bw1 = New BackgroundWorker()
        bw2 = New BackgroundWorker()
        bw3 = New BackgroundWorker()
        bw4 = New BackgroundWorker()
        bw5 = New BackgroundWorker()
        bw6 = New BackgroundWorker()
        bw7 = New BackgroundWorker()
        bw8 = New BackgroundWorker()
        bw9 = New BackgroundWorker()
        bw10 = New BackgroundWorker()
        bw11 = New BackgroundWorker()
        InitializeBackgroundWorkers()
    End Sub

    Private Sub timer_Elapsed(sender As Object, e As Timers.ElapsedEventArgs) Handles timer.Elapsed
        'Add code here to check if the web service is updated and update label
        Button27.Text = Data.sortedMails.Count.ToString
        Button28.Text = Data.sortedMail1.Count.ToString
        Button29.Text = Data.sortedMail2.Count.ToString
        Button30.Text = Data.sortedMail3.Count.ToString
        Button31.Text = Data.sortedMail4.Count.ToString
        Button38.Text = Data.sortedMail5.Count.ToString
        Button34.Text = Data.sortedMail8.Count.ToString
        Button32.Text = Data.sortedMail6.Count.ToString
        Button51.Text = Data.sortedMail11.Count.ToString
        Button52.Text = Data.sortedMail12.Count.ToString
        Button88.Text = Data.sortedMail26.Count.ToString
        Button80.Text = Data.sortedMail28.Count.ToString
        Button53.Text = Data.sortedMail13.Count.ToString
        Button54.Text = Data.sortedMail14.Count.ToString
        Button55.Text = Data.sortedMail15.Count.ToString
        Button56.Text = Data.sortedMail16.Count.ToString
        Button57.Text = Data.sortedMail17.Count.ToString
        Button58.Text = Data.sortedMail18.Count.ToString
        Button59.Text = Data.sortedMail19.Count.ToString
        Button60.Text = Data.sortedMail20.Count.ToString
        Button61.Text = Data.sortedMail21.Count.ToString
        Button84.Text = Data.sortedMail22.Count.ToString
        Button85.Text = Data.sortedMail23.Count.ToString
        Button86.Text = Data.sortedMail24.Count.ToString
        Button87.Text = Data.sortedMail25.Count.ToString
        Button89.Text = Data.sortedMail27.Count.ToString
        Button35.Text = Data.sortedMail9.Count.ToString
        Button33.Text = Data.sortedMail7.Count.ToString
        Button36.Text = Data.sortedMail10.Count.ToString
    End Sub
    Private Sub InitializeBackgroundWorkers()
        AddHandler bw1.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw1.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw1.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw2.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw2.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw2.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw3.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw3.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw3.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw4.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw4.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw4.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw5.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw5.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw5.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw6.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork6)
        AddHandler bw6.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorker6Completed)

        AddHandler bw7.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw7.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw7.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw8.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw8.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw8.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw9.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw9.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw9.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw10.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw10.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw10.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw11.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw11.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw11.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)

        bw1.WorkerSupportsCancellation = True
        bw2.WorkerSupportsCancellation = True
        bw3.WorkerSupportsCancellation = True
        bw4.WorkerSupportsCancellation = True
        bw5.WorkerSupportsCancellation = True
        bw6.WorkerSupportsCancellation = True
        bw7.WorkerSupportsCancellation = True
        bw8.WorkerSupportsCancellation = True
        bw9.WorkerSupportsCancellation = True
        bw10.WorkerSupportsCancellation = True
        bw11.WorkerSupportsCancellation = True

        bw1.WorkerReportsProgress = True
        bw2.WorkerReportsProgress = True
        bw3.WorkerReportsProgress = True
        bw4.WorkerReportsProgress = True
        bw5.WorkerReportsProgress = True
        bw7.WorkerReportsProgress = True
        bw8.WorkerReportsProgress = True
        bw9.WorkerReportsProgress = True
        bw10.WorkerReportsProgress = True
        bw11.WorkerReportsProgress = True

    End Sub
    Private Sub backgroundWorkers_ProgressChanged(sender As Object, e As ProgressChangedEventArgs)
        ProgressBar1.Value = e.ProgressPercentage
        Label1.Text = e.ProgressPercentage.ToString + " %"
    End Sub
    Private Sub backgroundWorkers_DoWork(ByVal sender As Object, ByVal e As DoWorkEventArgs)
        Select Case e.Argument
            Case 1
                Extract_MX(0, quota, sender)
            Case 2
                Extract_MX(quota, quota, sender)
            Case 3
                Extract_MX(2 * quota, quota, sender)
            Case 4
                Extract_MX(3 * quota, quota, sender)
            Case 5
                Extract_MX(4 * quota, quota, sender)
            Case 6
                Extract_MX(5 * quota, quota, sender)
            Case 7
                Extract_MX(6 * quota, quota, sender)
            Case 8
                Extract_MX(7 * quota, quota, sender)
            Case 9
                Extract_MX(8 * quota, quota, sender)
            Case 10
                Extract_MX(9 * quota, Data.MailList.Count - 9 * quota, sender)
            Case Else

        End Select
    End Sub

    Private Sub backgroundWorkers_DoWork6(ByVal sender As Object, ByVal e As DoWorkEventArgs)
        functions.SaveLists(Data, CStr(e.Argument))
    End Sub

    ' This event handler deals with the results of the
    ' background operation.
    Private Sub backgroundWorkers_RunWorkerCompleted(ByVal sender As Object, ByVal e As RunWorkerCompletedEventArgs)
        ' First, handle the case where an exception was thrown.
        If e.Error IsNot Nothing Then
            MessageBox.Show(e.Error.Message)
        ElseIf e.Cancelled Then
        Else

        End If


    End Sub

    Private Sub backgroundWorkers_RunWorker6Completed(ByVal sender As Object, ByVal e As RunWorkerCompletedEventArgs)
        ' First, handle the case where an exception was thrown.
        If e.Error IsNot Nothing Then
            MessageBox.Show(e.Error.Message)
        ElseIf e.Cancelled Then
            MessageBox.Show("Saving Process cancelled !")
        Else
            MessageBox.Show("Mail Lists Successfully Saved !")
        End If
        Button16.Enabled = False
        Button15.Enabled = False
        Button14.Enabled = False
        Button13.Enabled = False
        Button12.Enabled = False
    End Sub

    Private Sub Extract_MX(ByVal index As Integer, ByVal count As Integer, ByVal sender As Object)

        Dim Client As MailMan = New MailMan()
        Client.UnlockComponent("ATMMINMAILQ_qOJ9Q2FfpW5O")

        Dim mxDomain, host As String
        Dim hostIndex = 0



        For Each email In Data.MailList.GetRange(index, count)
            If CType(sender, BackgroundWorker).CancellationPending Then
                Return
            End If

            SyncLock linesLock
                Data.lines_1 += 1
            End SyncLock

            Try
                host = email.Substring(email.IndexOf("@"c) + 1)
                hostIndex = Data.KnownDomains.IndexOf(host)
                If hostIndex <> -1 Then
                    functions.returndomain(Data.KnownMx(hostIndex), email, Data, Me)
                Else
                    mxDomain = Client.MxLookup(email)
                    If Client.LastMethodSuccess = True Then
                        'data.dt.Rows.Add(email, mxDomain, "");
                        functions.returndomain(mxDomain, email, Data, Me)
                    Else
                        functions.returndomain(host, email, Data, Me)

                    End If
                End If
                CType(sender, BackgroundWorker).ReportProgress(Data.percent)
            Catch ex As Exception
                MessageBox.Show(ex.Message)
            End Try

        Next
        'ProgressBar1.Value = Data.percent
        If Data.percent = 100 Then
            MessageBox.Show("Task Finished Successfully !")
            Button16.Invoke(Sub()
                                Button16.Enabled = True
                                Refresh()
                            End Sub)
            Button15.Invoke(Sub()
                                Button15.Enabled = True
                                Refresh()
                            End Sub)
            Button14.Invoke(Sub()
                                Button14.Enabled = False
                                Refresh()
                            End Sub)
            Button13.Invoke(Sub()
                                Button13.Enabled = True
                                Refresh()
                            End Sub)
            Button12.Invoke(Sub()
                                Button12.Enabled = True
                                Refresh()
                            End Sub)

        End If

    End Sub

    Private Sub Button16_Click(sender As Object, e As EventArgs) Handles Button16.Click
        Dim openFileDlg As OpenFileDialog = New OpenFileDialog()
        openFileDlg.Filter = "Text Files|*.txt;..."
        openFileDlg.InitialDirectory = Directory.GetCurrentDirectory()
        ' Launch OpenFileDialog by calling ShowDialog method
        ' Get the selected file name and display in a TextBox.
        ' Load content of file in a TextBlock
        If openFileDlg.ShowDialog() = DialogResult.OK Then
            'FileTxt1TextBox.Text = openFileDlg.FileTxt1;
            Data.Clear()
            Dim text = File.ReadAllText(openFileDlg.FileName)
            'data.lines_1 = text.Split('\n').Length;
            'data.AllLines = data.lines_1;
            text = text.Replace(Environment.NewLine, " ")
            Data.MailList = text.Split(" "c).ToList()
            Data.MailList = Data.MailList.Distinct().ToList()
            Data.lines_1 = Data.MailList.Count
            Data.AllLines = Data.lines_1
            MessageBox.Show("File Loaded Successfully! Press Start To start Sorting. ")

            Button15.Enabled = True
            Button14.Enabled = False
            Button13.Enabled = False
            Button12.Enabled = False
        End If
    End Sub

    Private Sub Button15_Click(sender As Object, e As EventArgs) Handles Button15.Click
        Dim emailCount = Data.MailList.Count
        quota = emailCount / 10
        Data.lines_1 = 0
        bw1.RunWorkerAsync(1)
        bw2.RunWorkerAsync(2)
        bw3.RunWorkerAsync(3)
        bw4.RunWorkerAsync(4)
        bw5.RunWorkerAsync(5)
        bw7.RunWorkerAsync(6)
        bw8.RunWorkerAsync(7)
        bw9.RunWorkerAsync(8)
        bw10.RunWorkerAsync(9)
        bw11.RunWorkerAsync(10)
        Button15.Enabled = False
        Button14.Enabled = True
    End Sub

    Private Sub Button14_Click(sender As Object, e As EventArgs) Handles Button14.Click
        bw1.CancelAsync()
        bw2.CancelAsync()
        bw3.CancelAsync()
        bw4.CancelAsync()
        bw5.CancelAsync()
        bw7.CancelAsync()
        bw8.CancelAsync()
        bw9.CancelAsync()
        bw10.CancelAsync()
        bw11.CancelAsync()
        MessageBox.Show("Task Cancelled !")

        Button16.Enabled = True
        Button15.Enabled = True
        Button14.Enabled = False
        Button13.Enabled = True
        Button12.Enabled = True
    End Sub

    Private Sub Button13_Click(sender As Object, e As EventArgs) Handles Button13.Click
        Dim path = ""
        Dim dlg As FolderBrowserDialog = New FolderBrowserDialog()
        Dim Result As DialogResult = dlg.ShowDialog()
        If Result = DialogResult.OK Then
            path = dlg.SelectedPath & "\Lists\"

            If Not Directory.Exists(path) Then Directory.CreateDirectory(path)
            bw6.RunWorkerAsync(path)
        End If
    End Sub

    Private Sub Button12_Click(sender As Object, e As EventArgs) Handles Button12.Click
        Data.Clear()
        bw1.CancelAsync()
        bw2.CancelAsync()
        bw3.CancelAsync()
        bw4.CancelAsync()
        bw5.CancelAsync()
        bw7.CancelAsync()
        bw8.CancelAsync()
        bw9.CancelAsync()
        bw10.CancelAsync()
        bw11.CancelAsync()
        MessageBox.Show("Lists Cleared !")
        Button27.Text = "0"
        Button28.Text = "0"
        Button29.Text = "0"
        Button30.Text = "0"
        Button31.Text = "0"
        Button32.Text = "0"
        Button33.Text = "0"
        Button34.Text = "0"
        Button35.Text = "0"
        Button36.Text = "0"
        Button38.Text = "0"
        Button51.Text = "0"
        Button52.Text = "0"
        Button53.Text = "0"
        Button54.Text = "0"
        Button55.Text = "0"
        Button56.Text = "0"
        Button57.Text = "0"
        Button58.Text = "0"
        Button59.Text = "0"
        Button60.Text = "0"
        Button61.Text = "0"
        Button84.Text = "0"
        Button85.Text = "0"
        Button86.Text = "0"
        Button87.Text = "0"
        Button88.Text = "0"
        Button89.Text = "0"
        ProgressBar1.Value = 0
        Label1.Text = "0 %"
        Button16.Enabled = True
        Button15.Enabled = True
        Button14.Enabled = False
        Button13.Enabled = False
        Button12.Enabled = False
    End Sub

    Private Sub BtnClose_Click(sender As Object, e As EventArgs) Handles BtnClose.Click
        Me.Close()
    End Sub

    Private Sub Button17_Click(sender As Object, e As EventArgs) Handles Button17.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Google Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMails)
        frm.ShowDialog()
    End Sub

    Private Sub Button18_Click(sender As Object, e As EventArgs) Handles Button18.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Yahoo Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail1)
        frm.ShowDialog()
    End Sub

    Private Sub Button19_Click(sender As Object, e As EventArgs) Handles Button19.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Hotmail Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail2)
        frm.ShowDialog()
    End Sub

    Private Sub Button20_Click(sender As Object, e As EventArgs) Handles Button20.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Outlook Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail3) + Environment.NewLine + functions.generateTxt(Data.sortedMail4)
        frm.ShowDialog()
    End Sub

    Private Sub Button21_Click(sender As Object, e As EventArgs) Handles Button21.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Office365 Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail4)
        frm.ShowDialog()
    End Sub

    Private Sub Button22_Click(sender As Object, e As EventArgs) Handles Button22.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "163 Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail6)
        frm.ShowDialog()
    End Sub

    Private Sub Button23_Click(sender As Object, e As EventArgs) Handles Button23.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "1 and 1 Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail7)
        frm.ShowDialog()
    End Sub

    Private Sub Button24_Click(sender As Object, e As EventArgs) Handles Button24.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "RackSpace Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail8)
        frm.ShowDialog()
    End Sub

    Private Sub Button25_Click(sender As Object, e As EventArgs) Handles Button25.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "MailRu Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail9)
        frm.ShowDialog()
    End Sub

    Private Sub Button26_Click(sender As Object, e As EventArgs) Handles Button26.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Other Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail10)
        frm.ShowDialog()
    End Sub

    Private Sub Button39_Click(sender As Object, e As EventArgs) Handles Button39.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "GoDaddy Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail5)
        frm.ShowDialog()
    End Sub

    Private Sub Button62_Click(sender As Object, e As EventArgs) Handles Button62.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "263 Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail11)
        frm.ShowDialog()
    End Sub

    Private Sub Button63_Click(sender As Object, e As EventArgs) Handles Button63.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Aliyun Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail12)
        frm.ShowDialog()
    End Sub

    Private Sub Button81_Click(sender As Object, e As EventArgs) Handles Button81.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Amazon Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail28)
        frm.ShowDialog()
    End Sub
    Private Sub Button64_Click(sender As Object, e As EventArgs) Handles Button64.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Anazana Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail13)
        frm.ShowDialog()
    End Sub
    Private Sub Button65_Click(sender As Object, e As EventArgs) Handles Button65.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "CoreMail Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail14)
        frm.ShowDialog()
    End Sub

    Private Sub Button66_Click(sender As Object, e As EventArgs) Handles Button66.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "EarthLink Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail15)
        frm.ShowDialog()
    End Sub

    Private Sub Button67_Click(sender As Object, e As EventArgs) Handles Button67.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Gmx Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail16)
        frm.ShowDialog()
    End Sub

    Private Sub Button68_Click(sender As Object, e As EventArgs) Handles Button68.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Hinet Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail17)
        frm.ShowDialog()
    End Sub

    Private Sub Button69_Click(sender As Object, e As EventArgs) Handles Button69.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "iCloud Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail18)
        frm.ShowDialog()
    End Sub

    Private Sub Button70_Click(sender As Object, e As EventArgs) Handles Button70.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "iiNet Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail19)
        frm.ShowDialog()
    End Sub

    Private Sub Button71_Click(sender As Object, e As EventArgs) Handles Button71.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "mimeCast Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail20)
        frm.ShowDialog()
    End Sub

    Private Sub Button72_Click(sender As Object, e As EventArgs) Handles Button72.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "nameCheap Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail21)
        frm.ShowDialog()
    End Sub

    Private Sub Button73_Click(sender As Object, e As EventArgs) Handles Button73.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Orange Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail22)
        frm.ShowDialog()
    End Sub

    Private Sub Button74_Click(sender As Object, e As EventArgs) Handles Button74.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "QQ Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail23)
        frm.ShowDialog()
    End Sub

    Private Sub Button75_Click(sender As Object, e As EventArgs) Handles Button75.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Web.de Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail24)
        frm.ShowDialog()
    End Sub

    Private Sub Button76_Click(sender As Object, e As EventArgs) Handles Button76.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Yandex Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail25)
        frm.ShowDialog()
    End Sub

    Private Sub Button77_Click(sender As Object, e As EventArgs) Handles Button77.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Zambria Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail26)
        frm.ShowDialog()
    End Sub

    Private Sub Button78_Click(sender As Object, e As EventArgs) Handles Button78.Click
        Dim frm As ListsForm = New ListsForm()
        frm.Text = "Zoho Mails"
        frm.TextBox1.Text = functions.generateTxt(Data.sortedMail27)
        frm.ShowDialog()
    End Sub
    Private Sub Panel2_MouseDown(sender As Object, e As MouseEventArgs) Handles PanelUp2.MouseDown
        x = Control.MousePosition.X - Me.Location.X
        y = Control.MousePosition.Y - Me.Location.Y

    End Sub

    Private Sub BtnHide_Click(sender As Object, e As EventArgs) Handles BtnHide.Click
        WindowState = FormWindowState.Minimized
    End Sub

    Private Sub PanelUp2_Paint(sender As Object, e As PaintEventArgs) Handles PanelUp2.Paint

    End Sub

    Private Sub FlowLayoutPanel2_Paint(sender As Object, e As PaintEventArgs) Handles FlowLayoutPanel2.Paint

    End Sub

    Private Sub Panel2_MouseMove(sender As Object, e As MouseEventArgs) Handles PanelUp2.MouseMove
        If e.Button = Windows.Forms.MouseButtons.Left Then
            newpoint = Control.MousePosition
            newpoint.X -= x
            newpoint.Y -= y
            Me.Location = newpoint
            Application.DoEvents()
        End If
    End Sub
End Class