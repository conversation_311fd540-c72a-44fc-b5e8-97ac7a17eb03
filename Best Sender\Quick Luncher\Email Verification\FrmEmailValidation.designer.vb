﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class FrmEmailValidation
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FrmEmailValidation))
        Me.DefaultLookAndFeel1 = New DevExpress.LookAndFeel.DefaultLookAndFeel(Me.components)
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.BackgroundWorker2 = New System.ComponentModel.BackgroundWorker()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.picWait = New System.Windows.Forms.PictureBox()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemTextEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemMemoEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit()
        Me.GridColumn6 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.BntBrowse = New DevExpress.XtraEditors.SimpleButton()
        Me.txtPath = New DevExpress.XtraEditors.TextEdit()
        Me.Bnt_Save = New DevExpress.XtraEditors.SimpleButton()
        Me.Bnt_Reset = New DevExpress.XtraEditors.SimpleButton()
        Me.Bnt_Start = New DevExpress.XtraEditors.SimpleButton()
        Me.trmfadein = New System.Windows.Forms.Timer(Me.components)
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        CType(Me.picWait, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemMemoEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtPath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'DefaultLookAndFeel1
        '
        Me.DefaultLookAndFeel1.EnableBonusSkins = True
        Me.DefaultLookAndFeel1.LookAndFeel.SkinName = "WXI"
        '
        'Timer1
        '
        Me.Timer1.Interval = 5000
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'BackgroundWorker2
        '
        Me.BackgroundWorker2.WorkerReportsProgress = True
        Me.BackgroundWorker2.WorkerSupportsCancellation = True
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.GroupControl2)
        Me.GroupControl1.Controls.Add(Me.GridControl1)
        Me.GroupControl1.Controls.Add(Me.Label1)
        Me.GroupControl1.Controls.Add(Me.BntBrowse)
        Me.GroupControl1.Controls.Add(Me.txtPath)
        Me.GroupControl1.Location = New System.Drawing.Point(22, 26)
        Me.GroupControl1.LookAndFeel.SkinName = "Sharp Plus"
        Me.GroupControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.ShowCaption = False
        Me.GroupControl1.Size = New System.Drawing.Size(1301, 766)
        Me.GroupControl1.TabIndex = 304
        Me.GroupControl1.Text = " "
        '
        'GroupControl2
        '
        Me.GroupControl2.Appearance.BackColor = System.Drawing.Color.White
        Me.GroupControl2.Appearance.Options.UseBackColor = True
        Me.GroupControl2.Controls.Add(Me.picWait)
        Me.GroupControl2.Location = New System.Drawing.Point(408, 320)
        Me.GroupControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.ShowCaption = False
        Me.GroupControl2.Size = New System.Drawing.Size(468, 204)
        Me.GroupControl2.TabIndex = 55
        Me.GroupControl2.Text = "GroupControl2"
        Me.GroupControl2.Visible = False
        '
        'picWait
        '
        Me.picWait.Dock = System.Windows.Forms.DockStyle.Fill
        Me.picWait.Image = Global.Best_Sender.My.Resources.Resources.finding_signatures
        Me.picWait.Location = New System.Drawing.Point(2, 2)
        Me.picWait.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.picWait.Name = "picWait"
        Me.picWait.Size = New System.Drawing.Size(464, 200)
        Me.picWait.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.picWait.TabIndex = 2
        Me.picWait.TabStop = False
        '
        'GridControl1
        '
        Me.GridControl1.EmbeddedNavigator.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GridControl1.Location = New System.Drawing.Point(20, 109)
        Me.GridControl1.LookAndFeel.SkinMaskColor = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridControl1.LookAndFeel.SkinMaskColor2 = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridControl1.LookAndFeel.SkinName = "DevExpress Dark Style"
        Me.GridControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemTextEdit1, Me.RepositoryItemMemoEdit1})
        Me.GridControl1.Size = New System.Drawing.Size(1264, 626)
        Me.GridControl1.TabIndex = 54
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Appearance.Empty.BackColor = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridView1.Appearance.Empty.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridView1.Appearance.Empty.Options.UseBackColor = True
        Me.GridView1.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridView1.Appearance.EvenRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridView1.Appearance.EvenRow.Options.UseBackColor = True
        Me.GridView1.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(74, Byte), Integer), CType(CType(77, Byte), Integer), CType(CType(92, Byte), Integer))
        Me.GridView1.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(74, Byte), Integer), CType(CType(77, Byte), Integer), CType(CType(92, Byte), Integer))
        Me.GridView1.Appearance.FocusedRow.Options.UseBackColor = True
        Me.GridView1.Appearance.GroupRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridView1.Appearance.GroupRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridView1.Appearance.GroupRow.Options.UseBackColor = True
        Me.GridView1.Appearance.OddRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridView1.Appearance.OddRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridView1.Appearance.OddRow.Options.UseBackColor = True
        Me.GridView1.Appearance.Row.BackColor = System.Drawing.Color.FromArgb(CType(CType(90, Byte), Integer), CType(CType(93, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.GridView1.Appearance.Row.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(90, Byte), Integer), CType(CType(93, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.GridView1.Appearance.Row.Options.UseBackColor = True
        Me.GridView1.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(90, Byte), Integer), CType(CType(93, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.GridView1.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(90, Byte), Integer), CType(CType(93, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.GridView1.Appearance.SelectedRow.Options.UseBackColor = True
        Me.GridView1.ColumnPanelRowHeight = 39
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2, Me.GridColumn3, Me.GridColumn4, Me.GridColumn5, Me.GridColumn6})
        Me.GridView1.DetailHeight = 393
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.GroupRowHeight = 0
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsEditForm.PopupEditFormWidth = 933
        Me.GridView1.OptionsFind.AllowFindPanel = False
        Me.GridView1.OptionsFind.AllowMruItems = False
        Me.GridView1.OptionsView.BestFitMode = DevExpress.XtraGrid.Views.Grid.GridBestFitMode.Full
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView1.OptionsView.ShowIndicator = False
        Me.GridView1.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.[True]
        Me.GridView1.RowHeight = 28
        '
        'GridColumn1
        '
        Me.GridColumn1.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn1.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn1.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn1.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn1.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn1.AppearanceCell.Options.UseFont = True
        Me.GridColumn1.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn1.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn1.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn1.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn1.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn1.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn1.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn1.AppearanceHeader.Options.UseFont = True
        Me.GridColumn1.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn1.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn1.Caption = "#"
        Me.GridColumn1.FieldName = "id"
        Me.GridColumn1.MinWidth = 12
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.OptionsColumn.AllowEdit = False
        Me.GridColumn1.OptionsColumn.AllowFocus = False
        Me.GridColumn1.OptionsColumn.AllowMove = False
        Me.GridColumn1.OptionsColumn.AllowShowHide = False
        Me.GridColumn1.OptionsColumn.AllowSize = False
        Me.GridColumn1.OptionsColumn.FixedWidth = True
        Me.GridColumn1.OptionsColumn.ReadOnly = True
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        Me.GridColumn1.Width = 30
        '
        'GridColumn2
        '
        Me.GridColumn2.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn2.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn2.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn2.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn2.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn2.AppearanceCell.Options.UseFont = True
        Me.GridColumn2.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn2.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn2.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn2.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn2.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn2.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn2.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn2.AppearanceHeader.Options.UseFont = True
        Me.GridColumn2.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn2.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn2.Caption = "Email Address"
        Me.GridColumn2.ColumnEdit = Me.RepositoryItemTextEdit1
        Me.GridColumn2.FieldName = "emailAddress"
        Me.GridColumn2.MinWidth = 12
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.OptionsColumn.AllowEdit = False
        Me.GridColumn2.OptionsColumn.AllowFocus = False
        Me.GridColumn2.OptionsColumn.AllowMove = False
        Me.GridColumn2.OptionsColumn.AllowShowHide = False
        Me.GridColumn2.OptionsColumn.AllowSize = False
        Me.GridColumn2.OptionsColumn.FixedWidth = True
        Me.GridColumn2.OptionsColumn.ReadOnly = True
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 1
        Me.GridColumn2.Width = 170
        '
        'RepositoryItemTextEdit1
        '
        Me.RepositoryItemTextEdit1.AutoHeight = False
        Me.RepositoryItemTextEdit1.Name = "RepositoryItemTextEdit1"
        Me.RepositoryItemTextEdit1.Padding = New System.Windows.Forms.Padding(8, 0, 8, 0)
        '
        'GridColumn3
        '
        Me.GridColumn3.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn3.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn3.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn3.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn3.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn3.AppearanceCell.Options.UseFont = True
        Me.GridColumn3.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn3.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn3.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn3.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn3.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn3.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn3.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn3.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn3.AppearanceHeader.Options.UseFont = True
        Me.GridColumn3.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn3.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn3.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn3.Caption = "Verification"
        Me.GridColumn3.FieldName = "Status"
        Me.GridColumn3.MinWidth = 12
        Me.GridColumn3.Name = "GridColumn3"
        Me.GridColumn3.OptionsColumn.AllowEdit = False
        Me.GridColumn3.OptionsColumn.AllowFocus = False
        Me.GridColumn3.OptionsColumn.AllowMove = False
        Me.GridColumn3.OptionsColumn.AllowShowHide = False
        Me.GridColumn3.OptionsColumn.AllowSize = False
        Me.GridColumn3.OptionsColumn.FixedWidth = True
        Me.GridColumn3.OptionsColumn.ReadOnly = True
        Me.GridColumn3.Visible = True
        Me.GridColumn3.VisibleIndex = 2
        Me.GridColumn3.Width = 80
        '
        'GridColumn4
        '
        Me.GridColumn4.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn4.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn4.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn4.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn4.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn4.AppearanceCell.Options.UseFont = True
        Me.GridColumn4.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn4.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn4.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn4.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn4.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn4.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn4.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn4.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn4.AppearanceHeader.Options.UseFont = True
        Me.GridColumn4.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn4.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn4.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn4.Caption = "Domain Name"
        Me.GridColumn4.FieldName = "DomanName"
        Me.GridColumn4.MinWidth = 12
        Me.GridColumn4.Name = "GridColumn4"
        Me.GridColumn4.OptionsColumn.AllowEdit = False
        Me.GridColumn4.OptionsColumn.AllowFocus = False
        Me.GridColumn4.OptionsColumn.AllowMove = False
        Me.GridColumn4.OptionsColumn.AllowShowHide = False
        Me.GridColumn4.OptionsColumn.AllowSize = False
        Me.GridColumn4.OptionsColumn.FixedWidth = True
        Me.GridColumn4.OptionsColumn.ReadOnly = True
        Me.GridColumn4.Visible = True
        Me.GridColumn4.VisibleIndex = 3
        Me.GridColumn4.Width = 110
        '
        'GridColumn5
        '
        Me.GridColumn5.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn5.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn5.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn5.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn5.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn5.AppearanceCell.Options.UseFont = True
        Me.GridColumn5.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn5.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn5.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn5.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn5.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn5.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn5.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn5.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn5.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn5.AppearanceHeader.Options.UseFont = True
        Me.GridColumn5.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn5.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn5.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn5.Caption = "Mail Exchange"
        Me.GridColumn5.ColumnEdit = Me.RepositoryItemMemoEdit1
        Me.GridColumn5.FieldName = "SMTPServer"
        Me.GridColumn5.MinWidth = 12
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.OptionsColumn.AllowEdit = False
        Me.GridColumn5.OptionsColumn.AllowFocus = False
        Me.GridColumn5.OptionsColumn.AllowMove = False
        Me.GridColumn5.OptionsColumn.AllowShowHide = False
        Me.GridColumn5.OptionsColumn.AllowSize = False
        Me.GridColumn5.OptionsColumn.FixedWidth = True
        Me.GridColumn5.OptionsColumn.ReadOnly = True
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 4
        Me.GridColumn5.Width = 199
        '
        'RepositoryItemMemoEdit1
        '
        Me.RepositoryItemMemoEdit1.Name = "RepositoryItemMemoEdit1"
        Me.RepositoryItemMemoEdit1.Padding = New System.Windows.Forms.Padding(8, 0, 8, 0)
        '
        'GridColumn6
        '
        Me.GridColumn6.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn6.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.GridColumn6.AppearanceCell.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn6.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn6.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn6.AppearanceCell.Options.UseFont = True
        Me.GridColumn6.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn6.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn6.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn6.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.GridColumn6.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn6.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(59, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.GridColumn6.AppearanceHeader.Font = New System.Drawing.Font("Comfortaa", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridColumn6.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn6.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn6.AppearanceHeader.Options.UseFont = True
        Me.GridColumn6.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn6.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn6.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn6.Caption = "Smtp Response"
        Me.GridColumn6.ColumnEdit = Me.RepositoryItemMemoEdit1
        Me.GridColumn6.FieldName = "SMTPReply"
        Me.GridColumn6.MinWidth = 12
        Me.GridColumn6.Name = "GridColumn6"
        Me.GridColumn6.OptionsColumn.AllowEdit = False
        Me.GridColumn6.OptionsColumn.AllowFocus = False
        Me.GridColumn6.OptionsColumn.AllowMove = False
        Me.GridColumn6.OptionsColumn.AllowShowHide = False
        Me.GridColumn6.OptionsColumn.AllowSize = False
        Me.GridColumn6.OptionsColumn.FixedWidth = True
        Me.GridColumn6.OptionsColumn.ReadOnly = True
        Me.GridColumn6.Visible = True
        Me.GridColumn6.VisibleIndex = 5
        Me.GridColumn6.Width = 199
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.PowderBlue
        Me.Label1.Location = New System.Drawing.Point(21, 15)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(236, 19)
        Me.Label1.TabIndex = 53
        Me.Label1.Text = "   Select Email Address list ( *.txt )    "
        '
        'BntBrowse
        '
        Me.BntBrowse.AllowFocus = False
        Me.BntBrowse.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.BntBrowse.Appearance.Options.UseBackColor = True
        Me.BntBrowse.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntBrowse.AppearanceHovered.Options.UseBackColor = True
        Me.BntBrowse.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntBrowse.AppearancePressed.Options.UseBackColor = True
        Me.BntBrowse.Location = New System.Drawing.Point(1206, 54)
        Me.BntBrowse.LookAndFeel.SkinName = "Darkroom"
        Me.BntBrowse.LookAndFeel.UseDefaultLookAndFeel = False
        Me.BntBrowse.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntBrowse.Name = "BntBrowse"
        Me.BntBrowse.Size = New System.Drawing.Size(77, 33)
        Me.BntBrowse.TabIndex = 51
        Me.BntBrowse.Text = "Browse.."
        '
        'txtPath
        '
        Me.txtPath.EditValue = ""
        Me.txtPath.Location = New System.Drawing.Point(20, 54)
        Me.txtPath.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtPath.Name = "txtPath"
        Me.txtPath.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.txtPath.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtPath.Properties.Appearance.ForeColor = System.Drawing.Color.LightGray
        Me.txtPath.Properties.Appearance.Options.UseBackColor = True
        Me.txtPath.Properties.Appearance.Options.UseFont = True
        Me.txtPath.Properties.Appearance.Options.UseForeColor = True
        Me.txtPath.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.txtPath.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.txtPath.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtPath.Properties.ReadOnly = True
        Me.txtPath.Size = New System.Drawing.Size(1178, 26)
        Me.txtPath.TabIndex = 50
        '
        'Bnt_Save
        '
        Me.Bnt_Save.AllowFocus = False
        Me.Bnt_Save.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.Bnt_Save.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.Bnt_Save.Appearance.Options.UseBackColor = True
        Me.Bnt_Save.Appearance.Options.UseFont = True
        Me.Bnt_Save.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.Bnt_Save.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.Bnt_Save.AppearanceDisabled.Options.UseFont = True
        Me.Bnt_Save.AppearanceDisabled.Options.UseForeColor = True
        Me.Bnt_Save.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.Bnt_Save.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.Bnt_Save.AppearanceHovered.Options.UseBackColor = True
        Me.Bnt_Save.AppearanceHovered.Options.UseFont = True
        Me.Bnt_Save.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.Bnt_Save.AppearancePressed.Options.UseBackColor = True
        Me.Bnt_Save.Location = New System.Drawing.Point(1130, 820)
        Me.Bnt_Save.LookAndFeel.SkinName = "Darkroom"
        Me.Bnt_Save.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Bnt_Save.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Bnt_Save.Name = "Bnt_Save"
        Me.Bnt_Save.Size = New System.Drawing.Size(183, 57)
        Me.Bnt_Save.TabIndex = 307
        Me.Bnt_Save.Text = "Save"
        '
        'Bnt_Reset
        '
        Me.Bnt_Reset.AllowFocus = False
        Me.Bnt_Reset.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.Bnt_Reset.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.Bnt_Reset.Appearance.Options.UseBackColor = True
        Me.Bnt_Reset.Appearance.Options.UseFont = True
        Me.Bnt_Reset.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.Bnt_Reset.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.Bnt_Reset.AppearanceDisabled.Options.UseFont = True
        Me.Bnt_Reset.AppearanceDisabled.Options.UseForeColor = True
        Me.Bnt_Reset.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.Bnt_Reset.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.Bnt_Reset.AppearanceHovered.Options.UseBackColor = True
        Me.Bnt_Reset.AppearanceHovered.Options.UseFont = True
        Me.Bnt_Reset.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.Bnt_Reset.AppearancePressed.Options.UseBackColor = True
        Me.Bnt_Reset.Location = New System.Drawing.Point(33, 820)
        Me.Bnt_Reset.LookAndFeel.SkinName = "Darkroom"
        Me.Bnt_Reset.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Bnt_Reset.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Bnt_Reset.Name = "Bnt_Reset"
        Me.Bnt_Reset.Size = New System.Drawing.Size(183, 57)
        Me.Bnt_Reset.TabIndex = 306
        Me.Bnt_Reset.Text = "Reset"
        '
        'Bnt_Start
        '
        Me.Bnt_Start.AllowFocus = False
        Me.Bnt_Start.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.Bnt_Start.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.Bnt_Start.Appearance.Options.UseBackColor = True
        Me.Bnt_Start.Appearance.Options.UseFont = True
        Me.Bnt_Start.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.Bnt_Start.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.Bnt_Start.AppearanceDisabled.Options.UseFont = True
        Me.Bnt_Start.AppearanceDisabled.Options.UseForeColor = True
        Me.Bnt_Start.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.Bnt_Start.AppearanceHovered.Font = New System.Drawing.Font("Comfortaa", 10.8!)
        Me.Bnt_Start.AppearanceHovered.Options.UseBackColor = True
        Me.Bnt_Start.AppearanceHovered.Options.UseFont = True
        Me.Bnt_Start.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.Bnt_Start.AppearancePressed.Options.UseBackColor = True
        Me.Bnt_Start.Location = New System.Drawing.Point(941, 820)
        Me.Bnt_Start.LookAndFeel.SkinName = "Darkroom"
        Me.Bnt_Start.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Bnt_Start.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Bnt_Start.Name = "Bnt_Start"
        Me.Bnt_Start.Size = New System.Drawing.Size(183, 57)
        Me.Bnt_Start.TabIndex = 305
        Me.Bnt_Start.Text = "Start"
        '
        'trmfadein
        '
        Me.trmfadein.Enabled = True
        Me.trmfadein.Interval = 1
        '
        'FrmEmailValidation
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1348, 903)
        Me.Controls.Add(Me.Bnt_Save)
        Me.Controls.Add(Me.Bnt_Reset)
        Me.Controls.Add(Me.Bnt_Start)
        Me.Controls.Add(Me.GroupControl1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Icon = CType(resources.GetObject("FrmEmailValidation.IconOptions.Icon"), System.Drawing.Icon)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Logo_NewBestSender
        Me.LookAndFeel.SkinName = "Office 2010 Blue"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "FrmEmailValidation"
        Me.Opacity = 0R
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "  Email Validator"
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        CType(Me.picWait, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemMemoEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtPath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents DefaultLookAndFeel1 As DevExpress.LookAndFeel.DefaultLookAndFeel
    Public WithEvents Timer1 As Timer
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents BackgroundWorker2 As System.ComponentModel.BackgroundWorker
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Public WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemTextEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn4 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn5 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemMemoEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit
    Friend WithEvents GridColumn6 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Label1 As Label
    Friend WithEvents BntBrowse As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtPath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents picWait As PictureBox
    Friend WithEvents Bnt_Save As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Bnt_Reset As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Bnt_Start As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents trmfadein As Timer
End Class
