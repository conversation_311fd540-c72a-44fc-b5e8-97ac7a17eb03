﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="TextBox8.Properties.ContextImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAp0RVh0VGl0
        bGUAVGFnOxyRcdsAAAKkSURBVDhPpZNtSFNhFMevqdO0zcxM0qY1FN/SUKyhpYHiy126gVbmW2+WzRJd
        IkvJpvgydZZEBUEJBaVkQklKRDHXdFlZKmr2YmpFRETUlwj7EPw7zx2b+xD0oQM/zr3PPed3z3N5Lgfg
        v/hXLCNcCFcHnAknYklgaVdwFoOCGzbw3FArzw23ZrBllx5tUrq5JR3mZkKfBnNTGrTylTvomYhwsguG
        WjI4/FoQoGB212uarfw741nQGjEPLFoZuaRFRaxEyWrsAlNjKofFOXbJmkWX1fLMhQcdwLf7wIczGOvM
        w5WicLzu1wE/Z/D4/AHW6WEXGOuSWWLNbhcPxWXN32sHvg4A83pgTo+e0lhWGZyYWgR1mRZdur3sXuz4
        EYXmc/tjVHN3W4EvfcCbemC2jtBhurscvaUxuF2ngrmvDTnKPQiLSU+1CVize3t+VPbbgWbg803gZY2V
        mWrg1Qng/QWS3sL30UZM91aiY982RMRlDtoEIkNuZPbsnQbg03XgRZWV6Upg8jgwRXlKQ9ca/J5pwA1N
        Egqi1x4J3cT/sAk8+jVbrGNPlFkZPwaMERNHgefEuJqmqYXl9E6Uy/3avHwCA2SRKXaBW2HkKqWpRSkU
        4dlhYLSY8kErT4nJKlgMKuwOlpRQvXdQaFJKUNh2o03ATpt4V4gkz9TE01tp3JFCogB4lC9MMqTnkSHz
        1bqKPNdIZfGbA4MTzNKQhHibgAWTeKnWexYZdck0QQkwnAM8KcbDhmQoAiUVftLo3IAN8k5/mbyLcjzl
        paNcH+vNEpN4Z0k9SgZPJQp7N+kSoVi3vIzWVxPO+HiVY5zcKGH1S4LaaC+uNkpYZBIf3t9d3V0QCsqs
        2ZdwrokQc4zq8BUCLBy34BjsbxMTrJFZ2f1fAwD3B2pPwXIwzEauAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="TextBox4.Properties.ContextImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABx0RVh0VGl0
        bGUAUnVsZXJIb3Jpem9udGFsO1J1bGVyO2vI4DYAAAI3SURBVDhPpdJbSFNxHAfwg93La266SxZEhVRY
        dKEmgZRd7EFSg25oq0xd6baiG10wSLTCrB56DUPKdM6tXdKVl00W1Sqt2em121sRrJ3t7OLLt985O4QP
        e2h04MP/nB98v+fH4TAA/kvSYSqEK43MJLOmmS2ZI5krmTeNME9j2I5iz6eOYrDtGkze3Az/9U340LoR
        Ey0bMHFtHd5dXQvflTXwXS7C64ur8fL8SnjPFGLIsOKFWMLe0mDq+wBxYuqbg9gR/2ojVsS/WEg/MSP+
        2SSKTt5DwGWA27hc2D+d8beuF8PRj7eTuCOKCef7NoSG6xAwl+HHfQ2GdAVCQQYz3lwkvvlvgE0EROxd
        Ct5A2NMIzlaJoGkHfj8uwc/OLXAdVQoFmcybS6vEVWMUjPrbER1vQeRtM3jvKYRcNWKQM+9GsGcruN5t
        4Eyl+NVVgoEahVCQxbw6Vwje1wbOXkUq4GwqoFAFOEs5grSutV6BUN8uhMw7Ee4vg0krR+BhKRyH5IkC
        7+llCI9dwNOmRbTidjhOqBFx6/CkQQXeWg7LcSViXiPMx/IRse+B6bAc/EgD7AdkQkE2M6ZfiuDwWTga
        1eCe1cKuU9HHOglrnRL8qJ6CCvBuI/q0eYh4DOipltGzHrb9uYmCwfoloyO6xejVqvC8Vo3u6nwMHlHh
        0cE8OOm+a18urSvDg705YqizMnF2Vy30UsECIv5R80m6JEOSKcmSZEtypFMIz0j6f6ci6TAVSYf/Dswf
        nDpncLDnQy0AAAAASUVORK5CYII=
</value>
  </data>
  <data name="TextBox3.Properties.ContextImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABp0RVh0VGl0
        bGUAUnVsZXJWZXJ0aWNhbDtSdWxlcjvWKTIFAAACWklEQVQ4T33TXUhTYRwG8PWB6SzNuelMCjPKmkUf
        dmWOiAi66yZYNWz5gZW1KSgiWK2SGYFBWhRRXfixudaaTgs/t7XhpjNdpaEQpH0R3czm7OycrYun98xF
        Qp3zwnP3/H/83/dwBAAEsbN6TLff6buRh7Hru+DV7sSoNhcjV2TwXJLBXbcD9pocG+nFsTN/shyIm2jI
        A/PFDfqTC/RHB+g5G5jZAdCzfaDHm+Cq3caWhf8DVkQB3T78+u4FM9MCZvoxwtOP0K3ORvfFzeg6mwmz
        SsqWE7mANa/q9yLyzQP63UMwUw8QnrqH8ORdRCabEXqpgaN6Cz/grd+DyFey/tv7oN/cAf36NhjfLXSW
        bYClNAOmwnS2vJYLiB+9thvhz3aEfE0kjeTeNxFyaBCyHgPlqMBgZRY/4CEvH57rQ2i8EdRYA0IjWpiL
        pdE8PSOFUSlhy+u4gIThy7kIf+gB5dWB8lzF4nMlFkxHwDjVoOxq9F7YyA+46mRg3lvw061FsLcIP/Ry
        LBgPw6RKw5NCCQwnUnkBobN2O+gZI4L95zDfKo8CAeMhLJqPYnGoHD1lGWw5iRNw1OSAmmiGv6UA/rYD
        mG8vQIcyFQaSjlNi6BUiXiDRVr0VQVsVAfLhb10CAgY5KFsp2aoEXcXRz5jMCQxWZSM4VBkD8jFPtmCv
        oT8pQrsiBW3HU/iB/oosBAc00eGlkC3Yd+g4iEBvIZ6pxJwAe+Kt5zcNvyjPRE+pFNaSdLJyGjqLJLCQ
        QfNpMVoVIifp/fszxZBVJAkkSbEkx7J+WYQkK/8CEPwGXB44m9dN4bkAAAAASUVORK5CYII=
</value>
  </data>
</root>