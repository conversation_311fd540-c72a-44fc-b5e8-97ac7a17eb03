<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DnsClient</name>
    </assembly>
    <members>
        <member name="T:DnsClient.DnsDatagramReader">
            <summary>
            Helper to read from DNS datagrams.
            </summary>
            <remarks>
            The API of this class might change over time and receive breaking changes. Use at own risk.
            </remarks>
        </member>
        <member name="M:DnsClient.DnsDatagramReader.ParseString(System.ArraySegment{System.Byte})">
            <summary>
            As defined in https://tools.ietf.org/html/rfc1035#section-5.1 except '()' or '@' or '.'
            </summary>
        </member>
        <member name="T:DnsClient.Protocol.DnsHeaderFlag">
            <summary>
            The flags of the header's second 16bit value
            </summary>
        </member>
        <member name="T:DnsClient.Protocol.AaaaRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing an IPv6 <see cref="T:System.Net.IPAddress"/>.
            <para>
            A 128 bit IPv6 address is encoded in the data portion of an AAAA
            resource record in network byte order(high-order byte first).
            </para>
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc3596#section-2.2">RFC 3596</seealso>
        </member>
        <member name="M:DnsClient.Protocol.AaaaRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Net.IPAddress)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.AaaaRecord"/> class.
            </summary>
            <inheritdoc />
        </member>
        <member name="T:DnsClient.Protocol.AddressRecord">
            <summary>
            Base class for <see cref="T:DnsClient.Protocol.DnsResourceRecord"/>s transporting an <see cref="T:System.Net.IPAddress"/>.
            </summary>
            <seealso cref="T:DnsClient.Protocol.ARecord"/>
            <seealso cref="T:DnsClient.Protocol.AaaaRecord"/>
        </member>
        <member name="P:DnsClient.Protocol.AddressRecord.Address">
            <summary>
            Gets the <see cref="T:System.Net.IPAddress"/>.
            </summary>
            <value>
            The address.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.AddressRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Net.IPAddress)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.AddressRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="address">The address.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="address"/> or <paramref name="info"/> is null</exception>
        </member>
        <member name="T:DnsClient.Protocol.AfsDbRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing an AFS database location.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1183#section-1">RFC 1183</seealso>
            <seealso href="https://tools.ietf.org/html/rfc5864">RFC 5864</seealso>
        </member>
        <member name="P:DnsClient.Protocol.AfsDbRecord.SubType">
            <summary>
            Gets the <see cref="T:DnsClient.Protocol.AfsType"/>.
            </summary>
            <value>
            The sub type.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.AfsDbRecord.Hostname">
            <summary>
            Gets the hostname.
            </summary>
            <value>
            The hostname.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.AfsDbRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.Protocol.AfsType,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.AfsDbRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="type">The type.</param>
            <param name="name">The name.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="name"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.AfsType">
            <summary>
            Type used by <see cref="T:DnsClient.Protocol.AfsDbRecord"/>.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.AfsType.Afs">
            <summary>
            AFS is a registered trademark of Transarc Corporation
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.AfsType.Dce">
            <summary>
            The Distributed Computing Environment
            </summary>
        </member>
        <member name="T:DnsClient.Protocol.ARecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing an IPv4 <see cref="T:System.Net.IPAddress"/>.
            Hosts that have multiple Internet addresses will have multiple A records.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="M:DnsClient.Protocol.ARecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Net.IPAddress)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.ARecord"/> class.
            </summary>
            <inheritdoc />
        </member>
        <member name="T:DnsClient.Protocol.CaaRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a certification authority authorization.
            <para>
            The Certification Authority Authorization (CAA) DNS Resource Record
            allows a DNS domain name holder to specify one or more Certification
            Authorities(CAs) authorized to issue certificates for that domain.
            </para>
            <para>
            CAA Resource Records allow a public Certification Authority to
            implement additional controls to reduce the risk of unintended
            certificate mis-issue.This document defines the syntax of the CAA
            record and rules for processing CAA records by certificate issuers.
            </para>
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc6844">RFC 6844</seealso>
        </member>
        <member name="P:DnsClient.Protocol.CaaRecord.Flags">
            <summary>
            One octet containing the flags.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.CaaRecord.Tag">
            <summary>
            The property identifier, a sequence of US-ASCII characters.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.CaaRecord.Value">
            <summary>
            A sequence of octets representing the property value.
            Property values are encoded as binary values and MAY employ sub-formats.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.CaaRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Byte,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.CaaRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="flags">The flags.</param>
            <param name="tag">The tag.</param>
            <param name="value">The value.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="tag"/> or <paramref name="value"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.CNameRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing the canonical name for an alias.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.1">RFC 1035</seealso>
        </member>
        <member name="P:DnsClient.Protocol.CNameRecord.CanonicalName">
            <summary>
            Gets the canonical name for an alias.
            </summary>
            <value>
            The canonical name.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.CNameRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.CNameRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="canonicalName">The canonical name.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="canonicalName"/> or <paramref name="info"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.DnsKeyRecord">
            <summary>
            a <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a DnsKey record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4034#section-2"/>
        </member>
        <member name="P:DnsClient.Protocol.DnsKeyRecord.Flags">
            <summary>
            Gets the DNSKEY's flags value.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.DnsKeyRecord.Protocol">
            <summary>
            Gets the DNSKEY's protocol value.
            The value must be 3, and the <see cref="T:DnsClient.Protocol.DnsKeyRecord"/> MUST be treated as invalid
            during signature verification if it is found to be some value other than 3.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.DnsKeyRecord.Algorithm">
            <summary>
            Gets the <see cref="P:DnsClient.Protocol.DnsKeyRecord.PublicKey"/>'s cryptographic algorithm and determines the format of the <see cref="P:DnsClient.Protocol.DnsKeyRecord.PublicKey"/>.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.DnsKeyRecord.PublicKey">
            <summary>
            Gets the public key material.
            The format depends on the <see cref="P:DnsClient.Protocol.DnsKeyRecord.Algorithm"/> of the key being stored.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.DnsKeyRecord.PublicKeyAsString">
            <summary>
            Gets the base64 string representation of the <see cref="P:DnsClient.Protocol.DnsKeyRecord.PublicKey"/>.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.DnsKeyRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Int32,System.Byte,System.Byte,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.DnsKeyRecord"/> class
            </summary>
            <param name="info"></param>
            <param name="flags"></param>
            <param name="protocol"></param>
            <param name="algorithm"></param>
            <param name="publicKey"></param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="publicKey"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.DnsResourceRecord">
            <summary>
            Base class for all resource records.
            </summary>
            <seealso cref="T:DnsClient.Protocol.ResourceRecordInfo" />
        </member>
        <member name="M:DnsClient.Protocol.DnsResourceRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.DnsResourceRecord" /> class.
            </summary>
            <param name="info">The information.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> is null.</exception>
        </member>
        <member name="M:DnsClient.Protocol.DnsResourceRecord.ToString">
            <inheritdoc />
        </member>
        <member name="M:DnsClient.Protocol.DnsResourceRecord.ToString(System.Int32)">
            <summary>
            Same as <c>ToString</c> but offsets the <see cref="P:DnsClient.Protocol.ResourceRecordInfo.DomainName"/>
            by <paramref name="offset"/>.
            Set the offset to -32 for example to make it print nicely in consoles.
            </summary>
            <param name="offset">The offset.</param>
            <returns>A string representing this instance.</returns>
        </member>
        <member name="M:DnsClient.Protocol.DnsResourceRecord.RecordToString">
            <summary>
            Returns a string representation of the record's value only.
            <see cref="M:DnsClient.Protocol.DnsResourceRecord.ToString(System.Int32)"/> uses this to compose the full string value of this instance.
            </summary>
            <returns>A string representing this record.</returns>
        </member>
        <member name="T:DnsClient.Protocol.ResourceRecordInfo">
            <summary>
            The type represents a <see cref="T:DnsClient.Protocol.DnsResourceRecord"/>.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.ResourceRecordInfo.DomainName">
            <summary>
            The domain name used to query.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.ResourceRecordInfo.RecordType">
            <summary>
            Specifies type of resource record.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.ResourceRecordInfo.RecordClass">
            <summary>
            Specifies type class of resource record, mostly IN but can be CS, CH or HS .
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.ResourceRecordInfo.TimeToLive">
            <summary>
            Gets the current time to live value for the record.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.ResourceRecordInfo.InitialTimeToLive">
            <summary>
            Gets or sets the original time to live returned from the server.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.ResourceRecordInfo.RawDataLength">
            <summary>
            Gets the number of bytes for this resource record stored in RDATA
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.ResourceRecordInfo.#ctor(System.String,DnsClient.Protocol.ResourceRecordType,DnsClient.QueryClass,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.ResourceRecordInfo" /> class.
            </summary>
            <param name="domainName">The domain name used by the query.</param>
            <param name="recordType">Type of the record.</param>
            <param name="recordClass">The record class.</param>
            <param name="timeToLive">The time to live.</param>
            <param name="rawDataLength">Length of the raw data.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="domainName"/> is null.</exception>
        </member>
        <member name="M:DnsClient.Protocol.ResourceRecordInfo.#ctor(DnsClient.DnsString,DnsClient.Protocol.ResourceRecordType,DnsClient.QueryClass,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.ResourceRecordInfo" /> class.
            </summary>
            <param name="domainName">The <see cref="T:DnsClient.DnsString" /> used by the query.</param>
            <param name="recordType">Type of the record.</param>
            <param name="recordClass">The record class.</param>
            <param name="timeToLive">The time to live.</param>
            <param name="rawDataLength">Length of the raw data.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="domainName" /> is null or empty.</exception>
        </member>
        <member name="T:DnsClient.Protocol.DsRecord">
            <summary>
            a <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a DS record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4034#section-5.1"/>
        </member>
        <member name="P:DnsClient.Protocol.DsRecord.KeyTag">
            <summary>
            Gets the key tag value of the <see cref="T:DnsClient.Protocol.DnsKeyRecord"/> referred to by this record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4034#appendix-B">Key Tag Calculation</seealso>
        </member>
        <member name="P:DnsClient.Protocol.DsRecord.Algorithm">
            <summary>
            Gets the algorithm of the <see cref="T:DnsClient.Protocol.DnsKeyRecord"/> referred to by this record.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.DsRecord.DigestType">
            <summary>
            Gets the algorithm used to construct the digest.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.DsRecord.Digest">
            <summary>
            Gets the digest of the <see cref="T:DnsClient.Protocol.DnsKeyRecord"/> this record refers to.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.DsRecord.DigestAsString">
            <summary>
            Gets the hexadecimal string representation of the <see cref="P:DnsClient.Protocol.DsRecord.Digest"/>.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.DsRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Int32,System.Byte,System.Byte,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.DnsKeyRecord"/> class
            </summary>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="digest"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.EmptyRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> not representing any specific resource record.
            Used if unsupported <see cref="T:DnsClient.Protocol.ResourceRecordType"/>s are found in the result.
            </summary>
            <seealso cref="T:DnsClient.Protocol.DnsResourceRecord" />
        </member>
        <member name="M:DnsClient.Protocol.EmptyRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.EmptyRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.HInfoRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> used to acquire general information about a host.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.11">RFC 1035</seealso>
            <seealso href="https://tools.ietf.org/html/rfc1010">RFC 1010</seealso>
        </member>
        <member name="P:DnsClient.Protocol.HInfoRecord.Cpu">
            <summary>
            Gets a <c>string</c> which specifies the CPU type.
            </summary>
            <value>
            The CPU.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.HInfoRecord.OS">
            <summary>
            Gets a <c>string</c> which specifies the operating system type.
            </summary>
            <value>
            The OS.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.HInfoRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.HInfoRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="cpu">The CPU.</param>
            <param name="os">The OS.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.MbRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a domain name which specifies a host which has the specified mailbox.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.3">RFC 1035</seealso>
        </member>
        <member name="P:DnsClient.Protocol.MbRecord.MadName">
            <summary>
            Gets the domain name which specifies a host which has the specified mailbox.
            </summary>
            <value>
            The domain name.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.MbRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.MbRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="domainName">The domain name.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="domainName"/> or <paramref name="info"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.MgRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a domain name which specifies a mailbox which is a member of the mail group specified by the domain name.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.6">RFC 1035</seealso>
        </member>
        <member name="P:DnsClient.Protocol.MgRecord.MgName">
            <summary>
            Gets a domain name which specifies a mailbox which is a member of the mail group specified by the domain name.
            </summary>
            <value>
            The domain name.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.MgRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.MgRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="domainName">The domain name.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="domainName"/> or <paramref name="info"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.MInfoRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing mailbox or mail list information.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.11">RFC 1035</seealso>
        </member>
        <member name="P:DnsClient.Protocol.MInfoRecord.RMailBox">
            <summary>
            Gets the domain name which specifies a mailbox which is responsible for the mailing list or mailbox.
            </summary>
            <value>
            The domain name.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.MInfoRecord.EmailBox">
            <summary>
            Gets the domain name which specifies a mailbox which is to receive error messages related to the mailing list or mailbox.
            </summary>
            <value>
            The domain name.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.MInfoRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.DnsString,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.MInfoRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="rmailBox">The <c>RMAILBX</c>.</param>
            <param name="emailBox">The <c>EMAILBX</c>.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="rmailBox"/> or <paramref name="emailBox"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.MrRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a mailbox rename domain name.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.8">RFC 1035</seealso>
        </member>
        <member name="P:DnsClient.Protocol.MrRecord.NewName">
            <summary>
            Gets the domain name which specifies a mailbox which is the proper rename of the specified mailbox.
            </summary>
            <value>
            The domain name.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.MrRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.MrRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="name">The domain name.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="name"/> or <paramref name="info"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.MxRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a mail exchange.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.9">RFC 1035</seealso>
            <seealso href="https://tools.ietf.org/html/rfc974">RFC 974</seealso>
        </member>
        <member name="P:DnsClient.Protocol.MxRecord.Preference">
            <summary>
            Gets a 16 bit integer which specifies the preference given to
            this RR among others at the same owner.
            Lower values are preferred.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.MxRecord.Exchange">
            <summary>
            A domain name which specifies a host willing to act as a mail exchange.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.MxRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.UInt16,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.MxRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="preference">The preference.</param>
            <param name="domainName">Name of the domain.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="domainName"/> or <paramref name="info"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.NAPtrRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing Naming Authority Pointer
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2915">RFC 2915</seealso>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.ServiceKeySip">
            <summary>
            A known value of <see cref="P:DnsClient.Protocol.NAPtrRecord.Services"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.ServiceKeyEmail">
            <summary>
            A known value of <see cref="P:DnsClient.Protocol.NAPtrRecord.Services"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.ServiceKeyWeb">
            <summary>
            A known value of <see cref="P:DnsClient.Protocol.NAPtrRecord.Services"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.ServiceKeySipUdp">
            <summary>
            A known value of <see cref="P:DnsClient.Protocol.NAPtrRecord.Services"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.ServiceKeySipTcp">
            <summary>
            A known value of <see cref="P:DnsClient.Protocol.NAPtrRecord.Services"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.ServiceKeySipsTcp">
            <summary>
            A known value of <see cref="P:DnsClient.Protocol.NAPtrRecord.Services"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.ServiceKeySipWebsocket">
            <summary>
            A known value of <see cref="P:DnsClient.Protocol.NAPtrRecord.Services"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.ServiceKeySipsWebsocket">
            <summary>
            A known value of <see cref="P:DnsClient.Protocol.NAPtrRecord.Services"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.AFlag">
            <summary>
            One of the values of the <see cref="P:DnsClient.Protocol.NAPtrRecord.Flags"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            At this time only four flags, "S", "A", "U", and "P", are defined.
            The "S", "A" and "U" flags denote a terminal lookup.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2915">RFC 2915</seealso>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.PFlag">
            <summary>
            One of the values of the <see cref="P:DnsClient.Protocol.NAPtrRecord.Flags"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            At this time only four flags, "S", "A", "U", and "P", are defined.
            The "S", "A" and "U" flags denote a terminal lookup.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2915">RFC 2915</seealso>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.SFlag">
            <summary>
            One of the values of the <see cref="P:DnsClient.Protocol.NAPtrRecord.Flags"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            At this time only four flags, "S", "A", "U", and "P", are defined.
            The "S", "A" and "U" flags denote a terminal lookup.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2915">RFC 2915</seealso>
        </member>
        <member name="F:DnsClient.Protocol.NAPtrRecord.UFlag">
            <summary>
            One of the values of the <see cref="P:DnsClient.Protocol.NAPtrRecord.Flags"/> property of a <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.
            At this time only four flags, "S", "A", "U", and "P", are defined.
            The "S", "A" and "U" flags denote a terminal lookup.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2915">RFC 2915</seealso>
        </member>
        <member name="P:DnsClient.Protocol.NAPtrRecord.Order">
            <summary>
            Gets the order.
            </summary>
            <value>
            The order.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.NAPtrRecord.Preference">
            <summary>
            Gets the preference.
            </summary>
            <value>
            The preference.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.NAPtrRecord.Flags">
            <summary>
            Gets the flags.
            </summary>
            <value>
            The flags.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.NAPtrRecord.Services">
            <summary>
            Gets the services.
            </summary>
            <value>
            The services.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.NAPtrRecord.RegularExpression">
            <summary>
            Gets the regular expression.
            </summary>
            <value>
            The regular expression.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.NAPtrRecord.Replacement">
            <summary>
            Gets the replacement.
            </summary>
            <value>
            The replacement.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.NAPtrRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Int32,System.Int32,System.String,System.String,System.String,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.NAPtrRecord" /> class.
            </summary>
            <param name="info">The information.</param>
            <param name="order">The order.</param>
            <param name="preference">The preference.</param>
            <param name="flags">The flags.</param>
            <param name="services">The services.</param>
            <param name="regexp">The regular expression.</param>
            <param name="replacement">The replacement.</param>
        </member>
        <member name="T:DnsClient.Protocol.NSec3ParamRecord">
            <summary>
            a <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a NSEC3PARAM record.
            </summary>
            <seealso href="https://datatracker.ietf.org/doc/html/rfc5155#section-4"/>
            <see cref="F:DnsClient.Protocol.ResourceRecordType.NSEC3PARAM"/>
        </member>
        <member name="P:DnsClient.Protocol.NSec3ParamRecord.HashAlgorithm">
            <summary>
            Gets the cryptographic hash algorithm used to construct the hash-value.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3ParamRecord.Flags">
            <summary>
            Gets the flags field value containing 8 one-bit flags that can be used to indicate different processing.
            All undefined flags must be zero.
            The only flag defined by this specification is the Opt-Out flag.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3ParamRecord.Iterations">
            <summary>
            Gets the number of additional times the hash function has been performed.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3ParamRecord.Salt">
            <summary>
            Gets the salt field which is appended to the original owner name before hashing
            in order to defend against pre-calculated dictionary attacks.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3ParamRecord.SaltAsString">
            <summary>
            Gets the salt field which is appended to the original owner name before hashing
            in order to defend against pre-calculated dictionary attacks.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.NSec3ParamRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Byte,System.Byte,System.Int32,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.NSec3ParamRecord"/> class
            </summary>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="salt"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.NSec3Record">
            <summary>
            a <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a NSEC3 record.
            </summary>
            <seealso href="https://datatracker.ietf.org/doc/html/rfc5155"/>
        </member>
        <member name="P:DnsClient.Protocol.NSec3Record.HashAlgorithm">
            <summary>
            Gets the cryptographic hash algorithm used to construct the hash-value.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3Record.Flags">
            <summary>
            Gets the flags field value containing 8 one-bit flags that can be used to indicate different processing.
            All undefined flags must be zero.
            The only flag defined by this specification is the Opt-Out flag.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3Record.Iterations">
            <summary>
            Gets the number of additional times the hash function has been performed.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3Record.Salt">
            <summary>
            Gets the salt field which is appended to the original owner name before hashing
            in order to defend against pre-calculated dictionary attacks.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3Record.SaltAsString">
            <summary>
            Gets the salt field which is appended to the original owner name before hashing
            in order to defend against pre-calculated dictionary attacks.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3Record.NextOwnersName">
            <summary>
            Gets the name of the next hashed owner in hash order.
            This value is in binary format.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3Record.NextOwnersNameAsString">
            <summary>
            Gets the name of the next hashed owner in hash order.
            This value is in binary format.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3Record.TypeBitMapsRaw">
            <summary>
            Gets the type bit maps field which identifies the RRSet types that exist at the original owner name of the NSEC3 RR.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSec3Record.TypeBitMaps">
            <summary>
            Gets the type bit maps field which identifies the RRSet types that exist at the original owner name of the NSEC3 RR.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.NSec3Record.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Byte,System.Byte,System.Int32,System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.NSec3Record"/> class
            </summary>
            <exception cref="T:System.ArgumentNullException">
            If <paramref name="info"/>, <paramref name="nextOwnersName"/>, <paramref name="salt"/> or <paramref name="bitmap"/> is null.
            </exception>
        </member>
        <member name="T:DnsClient.Protocol.NSecRecord">
            <summary>
            a <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a NSEC record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4034#section-2"/>
        </member>
        <member name="P:DnsClient.Protocol.NSecRecord.NextDomainName">
            <summary>
            Gets the next owner name (in the canonical ordering of the zone) that has authoritative data
            or contains a delegation point NS RRset.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSecRecord.TypeBitMapsRaw">
            <summary>
            Gets the type bit maps field which identifies the RRSet types that exist at the NSEC RR's owner name.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.NSecRecord.TypeBitMaps">
            <summary>
            Gets the type bit maps field which identifies the RRSet types that exist at the NSEC RR's owner name.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.NSecRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.DnsString,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.NSecRecord"/> class
            </summary>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/>, <paramref name="nextDomainName"/> or <paramref name="typeBitMaps"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.NsRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing an authoritative name server.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.11">RFC 1035</seealso>
        </member>
        <member name="P:DnsClient.Protocol.NsRecord.NSDName">
            <summary>
            Gets the domain name which specifies a host which should be authoritative for the specified class and domain.
            </summary>
            <value>
            The domain name.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.NsRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.NsRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="name">The name.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="name"/> or <paramref name="info"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.NullRecord">
            <summary>
            Experimental RR, not sure if the implementation is actually correct either (not tested).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.10">RFC 1035</seealso>
        </member>
        <member name="P:DnsClient.Protocol.NullRecord.Anything">
            <summary>
            Gets any data stored in this record.
            </summary>
            <value>
            The byte array.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.NullRecord.AsString">
            <summary>
            Gets the raw data of this record as UTF8 string.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.NullRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.NullRecord" /> class.
            </summary>
            <param name="info">The information.</param>
            <param name="anything">Anything.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="anything"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.Options.OptRecord">
            <summary>
            A options resource record.
            </summary>
        </member>
        <member name="T:DnsClient.Protocol.PtrRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a pointer. These RRs are used
            in special domains to point to some other location in the domain space.
            </summary>
            <seealso cref="T:DnsClient.Protocol.DnsResourceRecord" />
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.12">RFC 1035</seealso>
        </member>
        <member name="P:DnsClient.Protocol.PtrRecord.PtrDomainName">
            <summary>
            Gets the domain name which points to some location in the domain name space.
            </summary>
            <value>
            The domain name.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.PtrRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.PtrRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="ptrDomainName">The domain name.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="ptrDomainName"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.ResourceRecordType">
            <summary>
            The resource record types. The <c>enum</c> contains only the types supported by this library at this moment.
            The <see cref="T:DnsClient.Protocol.ResourceRecordType"/> is used to identify any <see cref="T:DnsClient.Protocol.DnsResourceRecord"/>.
            <para>
            Resource record types are a subset of <see cref="T:DnsClient.QueryType"/>.
            </para>
            </summary>
            <seealso cref="T:DnsClient.Protocol.DnsResourceRecord"/>
            <seealso cref="T:DnsClient.Protocol.ResourceRecordType"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.A">
            <summary>
            A host address.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.ARecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.NS">
            <summary>
            An authoritative name server.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.11">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.NsRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.MD">
            <summary>
            A mail destination (OBSOLETE - use MX).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.MF">
            <summary>
            A mail forwarder (OBSOLETE - use MX).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.CNAME">
            <summary>
            The canonical name for an alias.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.1">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.CNameRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.SOA">
            <summary>
            Marks the start of a zone of authority.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.13">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.SoaRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.MB">
            <summary>
            A mailbox domain name (EXPERIMENTAL).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.3">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.MbRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.MG">
            <summary>
            A mail group member (EXPERIMENTAL).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.6">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.MgRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.MR">
            <summary>
            A mailbox rename domain name (EXPERIMENTAL).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.8">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.MrRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.NULL">
            <summary>
            A Null resource record (EXPERIMENTAL).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.8">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.NullRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.WKS">
            <summary>
            A well known service description.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc3232">RFC 3232</seealso>
            <seealso cref="T:DnsClient.Protocol.WksRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.PTR">
            <summary>
            A domain name pointer.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.12">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.PtrRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.HINFO">
            <summary>
            Host information.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.11">RFC 1035</seealso>
            <seealso href="https://tools.ietf.org/html/rfc1010">RFC 1010</seealso>
            <seealso cref="T:DnsClient.Protocol.HInfoRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.MINFO">
            <summary>
            Mailbox or mail list information.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.11">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.MInfoRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.MX">
            <summary>
            Mail exchange.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.9">RFC 1035</seealso>
            <seealso href="https://tools.ietf.org/html/rfc974">RFC 974</seealso>
            <seealso cref="T:DnsClient.Protocol.MxRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.TXT">
            <summary>
            Text resources.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3">RFC 1035</seealso>
            <seealso href="https://tools.ietf.org/html/rfc1464">RFC 1464</seealso>
            <seealso cref="T:DnsClient.Protocol.TxtRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.RP">
            <summary>
            Responsible Person.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1183">RFC 1183</seealso>
            <seealso cref="T:DnsClient.Protocol.RpRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.AFSDB">
            <summary>
            AFS Data Base location.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1183#section-1">RFC 1183</seealso>
            <seealso href="https://tools.ietf.org/html/rfc5864">RFC 5864</seealso>
            <seealso cref="T:DnsClient.Protocol.AfsDbRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.AAAA">
            <summary>
            An IPv6 host address.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc3596#section-2.2">RFC 3596</seealso>
            <seealso cref="T:DnsClient.Protocol.AaaaRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.SRV">
            <summary>
            A resource record which specifies the location of the server(s) for a specific protocol and domain.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2782">RFC 2782</seealso>
            <seealso cref="T:DnsClient.Protocol.SrvRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.NAPTR">
            <summary>
            The Naming Authority Pointer rfc3403
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2915">RFC 2915</seealso>
            <seealso cref="T:DnsClient.Protocol.NAPtrRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.OPT">
            <summary>
            Option record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc6891">RFC 6891</seealso>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.DS">
            <summary>
            DS rfc4034
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4034#section-5.1">RFC 4034</seealso>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.SSHFP">
            <summary>
            SSH finger print record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4255">RFC 4255</seealso>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.RRSIG">
            <summary>
            RRSIG rfc3755.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc3755">RFC 3755</seealso>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.NSEC">
            <summary>
            NSEC rfc4034.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4034#section-4">RFC 4034</seealso>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.DNSKEY">
            <summary>
            DNSKEY rfc4034.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4034#section-2"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.NSEC3">
            <summary>
            NSEC3 rfc5155.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc5155">RFC 5155</seealso>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.NSEC3PARAM">
            <summary>
            NSEC3PARAM rfc5155.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc5155#section-4">RFC 5155</seealso>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.TLSA">
            <summary>
            TLSA rfc6698.
            </summary>
            <seealso href="https://https://tools.ietf.org/html/rfc6698">RFC 6698</seealso>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.SPF">
            <summary>
            SPF records don't officially have a dedicated RR type, <see cref="F:DnsClient.Protocol.ResourceRecordType.TXT"/> should be used instead.
            The behavior of TXT and SPF are the same.
            </summary>
            <remarks>
            This library will return a TXT record but will set the header type to SPF if such a record is returned.
            </remarks>
            <seealso href="https://tools.ietf.org/html/rfc7208">RFC 7208</seealso>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.URI">
            <summary>
            A Uniform Resource Identifier (URI) resource record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc7553">RFC 7553</seealso>
            <seealso cref="T:DnsClient.Protocol.UriRecord"/>
        </member>
        <member name="F:DnsClient.Protocol.ResourceRecordType.CAA">
            <summary>
            A certification authority authorization.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc6844">RFC 6844</seealso>
            <seealso cref="T:DnsClient.Protocol.CaaRecord"/>
        </member>
        <member name="T:DnsClient.Protocol.RpRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a responsible person.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1183#section-2.2">RFC 1183</seealso>
        </member>
        <member name="P:DnsClient.Protocol.RpRecord.MailboxDomainName">
            <summary>
            Gets a domain name that specifies the mailbox for the responsible person.
            </summary>
            <value>
            The mailbox domain.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.RpRecord.TextDomainName">
            <summary>
            Gets a domain name for which TXT RR's exist.
            </summary>
            <value>
            The text domain.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.RpRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.DnsString,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.RpRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="mailbox">The mailbox domain.</param>
            <param name="textName">The text domain.</param>
            <exception cref="T:System.ArgumentNullException">
            If <paramref name="info"/> or <paramref name="mailbox"/> or <paramref name="textName"/> is null.
            </exception>
        </member>
        <member name="T:DnsClient.Protocol.RRSigRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a RRSIG record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4033"/>
            <seealso href="https://tools.ietf.org/html/rfc4034"/>
            <seealso href="https://tools.ietf.org/html/rfc4035"/>
        </member>
        <member name="P:DnsClient.Protocol.RRSigRecord.CoveredType">
            <summary>
            Gets the type of the RRset that is covered by this <see cref="T:DnsClient.Protocol.RRSigRecord"/>.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.RRSigRecord.Algorithm">
            <summary>
            Gets the cryptographic algorithm used to create the <see cref="P:DnsClient.Protocol.RRSigRecord.Signature"/>.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.RRSigRecord.Labels">
            <summary>
            Gets the number of labels in the original <see cref="T:DnsClient.Protocol.RRSigRecord"/> RR owner name.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.RRSigRecord.OriginalTtl">
            <summary>
            Gets the TTL of the covered RRset as it appears in the authoritative zone.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.RRSigRecord.SignatureExpiration">
            <summary>
            Gets the expiration date of the <see cref="P:DnsClient.Protocol.RRSigRecord.Signature"/>.
            This record MUST NOT be used for authentication prior to the <see cref="P:DnsClient.Protocol.RRSigRecord.SignatureInception"/>
            and MUST NOT be used for authentication after the <see cref="P:DnsClient.Protocol.RRSigRecord.SignatureExpiration"/>.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.RRSigRecord.SignatureInception">
            <summary>
            Gets the inception date of the <see cref="P:DnsClient.Protocol.RRSigRecord.Signature"/>.
            This record MUST NOT be used for authentication prior to the <see cref="P:DnsClient.Protocol.RRSigRecord.SignatureInception"/>
            and MUST NOT be used for authentication after the <see cref="P:DnsClient.Protocol.RRSigRecord.SignatureExpiration"/>.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.RRSigRecord.KeyTag">
            <summary>
            Gets the key tag value of the <see cref="T:DnsClient.Protocol.DnsKeyRecord"/> that validates this <see cref="P:DnsClient.Protocol.RRSigRecord.Signature"/>.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4034#appendix-B">Key Tag Calculation</seealso>
        </member>
        <member name="P:DnsClient.Protocol.RRSigRecord.SignersName">
            <summary>
            Gets the value which identifies the owner name of the <see cref="T:DnsClient.Protocol.DnsKeyRecord"/>
            that a validator is supposed to use to validate this <see cref="P:DnsClient.Protocol.RRSigRecord.Signature"/>.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.RRSigRecord.Signature">
            <summary>
            Gets the cryptographic signature that covers the RRSIG RDATA (excluding the Signature field)
            and the RRset specified by the RRSIG owner name, RRSIG class, and RRSIG Type Covered field.
            The format of this field depends on the algorithm in use.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.RRSigRecord.SignatureAsString">
            <summary>
            Gets the base64 string representation of the <see cref="P:DnsClient.Protocol.RRSigRecord.Signature"/>.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.RRSigRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Int32,System.Byte,System.Byte,System.Int64,System.Int64,System.Int64,System.Int32,DnsClient.DnsString,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.RRSigRecord"/> class.
            </summary>
            <param name="info"></param>
            <param name="coveredType"></param>
            <param name="algorithm"></param>
            <param name="labels"></param>
            <param name="originalTtl"></param>
            <param name="signatureExpiration"></param>
            <param name="signatureInception"></param>
            <param name="keyTag"></param>
            <param name="signersName"></param>
            <param name="signature"></param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/>, <paramref name="signersName"/> or <paramref name="signature"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.SoaRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a SOA (Start Of Authority) record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.13">RFC 1035</seealso>
        </member>
        <member name="P:DnsClient.Protocol.SoaRecord.Expire">
            <summary>
            Gets a 32 bit time value that specifies the upper limit on
            the time interval that can elapse before the zone is no
            longer authoritative.
            </summary>
            <value>
            The expiration.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.SoaRecord.Minimum">
            <summary>
            Gets the unsigned 32 bit minimum TTL field that should be
            exported with any RR from this zone.
            </summary>
            <value>
            The minimum TTL.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.SoaRecord.MName">
            <summary>
            Gets the domain name of the name server that was the original or primary source of data for this zone.
            </summary>
            <value>
            The domain name.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.SoaRecord.Refresh">
            <summary>
            Gets a 32 bit time interval before the zone should be refreshed.
            </summary>
            <value>
            The refresh time.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.SoaRecord.Retry">
            <summary>
            Gets a 32 bit time interval that should elapse before a failed refresh should be retried.
            </summary>
            <value>
            The retry time.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.SoaRecord.RName">
            <summary>
            Gets a domain name which specifies the mailbox of the person responsible for this zone.
            </summary>
            <value>
            The responsible mailbox domain name.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.SoaRecord.Serial">
            <summary>
            Gets the unsigned 32 bit version number of the original copy
            of the zone.Zone transfers preserve this value. This value wraps and should be compared using sequence space arithmetic.
            </summary>
            <value>
            The serial number.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.SoaRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.DnsString,DnsClient.DnsString,System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.SoaRecord" /> class.
            </summary>
            <param name="info">The information.</param>
            <param name="mName">Name original domain name.</param>
            <param name="rName">Name responsible domain name.</param>
            <param name="serial">The serial number.</param>
            <param name="refresh">The refresh time.</param>
            <param name="retry">The retry time.</param>
            <param name="expire">The expire time.</param>
            <param name="minimum">The minimum TTL.</param>
            <exception cref="T:System.ArgumentNullException">
            If <paramref name="info"/> or <paramref name="mName"/> or <paramref name="rName"/> is null.
            </exception>
        </member>
        <member name="T:DnsClient.Protocol.SrvRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a location of the server(s) for a specific protocol and domain.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2782">RFC 2782</seealso>
        </member>
        <member name="P:DnsClient.Protocol.SrvRecord.Port">
            <summary>
            Gets the port.
            </summary>
            <value>
            The port.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.SrvRecord.Priority">
            <summary>
            Gets the priority.
            </summary>
            <value>
            The priority.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.SrvRecord.Target">
            <summary>
            Gets the target domain name.
            </summary>
            <value>
            The target.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.SrvRecord.Weight">
            <summary>
            Gets the weight.
            </summary>
            <value>
            The weight.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.SrvRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.UInt16,System.UInt16,System.UInt16,DnsClient.DnsString)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.SrvRecord" /> class.
            </summary>
            <param name="info">The information.</param>
            <param name="priority">The priority.</param>
            <param name="weight">The weight.</param>
            <param name="port">The port.</param>
            <param name="target">The target.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="target"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.SshfpRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing an SSH fingerprint
            <para>
            SSHFP RRs are used to hold SSH fingerprints. Upon connecting to a
            host an SSH client may choose to query for this to check the fingerprint(s)
            </para>
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4255">RFC 4255</seealso>
            <seealso href="https://tools.ietf.org/html/rfc6594">RFC 6594</seealso>
            <seealso href="https://tools.ietf.org/html/rfc7479">RFC 7479</seealso>
        </member>
        <member name="M:DnsClient.Protocol.SshfpRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,DnsClient.Protocol.SshfpAlgorithm,DnsClient.Protocol.SshfpFingerprintType,System.String)">
            <summary>
            
            </summary>
            <param name="info">The information.</param>
            <param name="algorithm">The algorithm.</param>
            <param name="fingerprintType">The fingerprint type.</param>
            <param name="fingerprint">The fingerprint.</param>
        </member>
        <member name="P:DnsClient.Protocol.SshfpRecord.Algorithm">
            <summary>
            Algorithm used for the fingerprint
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.SshfpRecord.FingerprintType">
            <summary>
            Fingerprint type used for the fingerprint
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.SshfpRecord.Fingerprint">
            <summary>
            Fingerprint as defined in the RR
            </summary>
        </member>
        <member name="T:DnsClient.Protocol.SshfpAlgorithm">
            <summary>
            Algorithm used by <see cref="T:DnsClient.Protocol.SshfpRecord"/>
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.SshfpAlgorithm.Reserved">
            <summary>
            Reserved for later use
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.SshfpAlgorithm.RSA">
            <summary>
            RSA
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.SshfpAlgorithm.DSS">
            <summary>
            DSS
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.SshfpAlgorithm.ECDSA">
            <summary>
            Elliptic Curve DSA
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.SshfpAlgorithm.Ed25519">
            <summary>
            Edwards-curve DSA
            </summary>
        </member>
        <member name="T:DnsClient.Protocol.SshfpFingerprintType">
            <summary>
            Fingerprint type used by <see cref="T:DnsClient.Protocol.SshfpRecord"/>
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.SshfpFingerprintType.Reserved">
            <summary>
            Reserved for later use
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.SshfpFingerprintType.SHA1">
            <summary>
            SHA-1 fingerprint
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.SshfpFingerprintType.SHA256">
            <summary>
            SHA-256 fingerprint
            </summary>
        </member>
        <member name="T:DnsClient.Protocol.TlsaRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a TLSA record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc6698#Section2.1"/>
            <seealso href="https://tools.ietf.org/html/rfc7671"/>
            <seealso href="https://en.wikipedia.org/wiki/DNS-based_Authentication_of_Named_Entities#TLSA_RR"/>
        </member>
        <member name="P:DnsClient.Protocol.TlsaRecord.CertificateUsage">
            <summary>
            Gets the <see cref="T:DnsClient.Protocol.TlsaCertificateUsage"/>, which specifies the provided association
            that will be used to match the certificate presented in the TLS handshake.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.TlsaRecord.Selector">
            <summary>
            Gets the <see cref="T:DnsClient.Protocol.TlsaSelector"/>, which specifies which part of the TLS certificate
            presented by the server will be matched against the <see cref="P:DnsClient.Protocol.TlsaRecord.CertificateAssociationData"/>.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.TlsaRecord.MatchingType">
            <summary>
            Gets the <see cref="T:DnsClient.Protocol.TlsaMatchingType"/>, which specifies how the <see cref="P:DnsClient.Protocol.TlsaRecord.CertificateAssociationData"/> is presented.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.TlsaRecord.CertificateAssociationData">
            <summary>
            Gets the "certificate association data" to be matched.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.TlsaRecord.CertificateAssociationDataAsString">
            <summary>
            Gets the string representation of the <see cref="P:DnsClient.Protocol.TlsaRecord.CertificateAssociationData"/> in hexadecimal.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.TlsaRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Byte,System.Byte,System.Byte,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.TlsaRecord"/> class.
            </summary>
            <param name="info"></param>
            <param name="certificateUsage"></param>
            <param name="selector"></param>
            <param name="matchingType"></param>
            <param name="certificateAssociationData"></param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="certificateAssociationData"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.TlsaCertificateUsage">
            <summary>
            The usage flag specifies the provided association that will be used to match the certificate presented in the TLS handshake.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.TlsaCertificateUsage.PKIXTA">
            <summary>
            Certificate authority constraint.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.TlsaCertificateUsage.PKIXEE">
            <summary>
            Service certificate constraint.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.TlsaCertificateUsage.DANETA">
            <summary>
            Trust Anchor Assertion.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.TlsaCertificateUsage.DANEEE">
            <summary>
            Domain issued certificate.
            </summary>
        </member>
        <member name="T:DnsClient.Protocol.TlsaSelector">
            <summary>
            Flag which specifies which part of the TLS certificate presented by the server will be matched against the association data.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.TlsaSelector.FullCertificate">
            <summary>
            Select the entire certificate for matching.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.TlsaSelector.PublicKey">
            <summary>
            Select the public key for certificate matching.
            </summary>
        </member>
        <member name="T:DnsClient.Protocol.TlsaMatchingType">
            <summary>
            Flag which specifies how the certificate association is presented.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.TlsaMatchingType.ExactMatch">
            <summary>
            Exact match, the entire information selected is present in the certificate association data.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.TlsaMatchingType.SHA256">
            <summary>
            SHA-256 hash of selected content.
            </summary>
        </member>
        <member name="F:DnsClient.Protocol.TlsaMatchingType.SHA512">
            <summary>
            SHA-512 hash of selected content.
            </summary>
        </member>
        <member name="T:DnsClient.Protocol.TxtRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a text resource.
            <para>
            TXT RRs are used to hold descriptive text. The semantics of the text
            depends on the domain where it is found.
            </para>
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3">RFC 1035</seealso>
            <seealso href="https://tools.ietf.org/html/rfc1464">RFC 1464</seealso>
        </member>
        <member name="P:DnsClient.Protocol.TxtRecord.EscapedText">
            <summary>
            Gets the list of TXT values of this resource record in escaped form, valid for root file.
            </summary>
            <remarks>
            See https://tools.ietf.org/html/rfc1035#section-5.1 for escape details.
            </remarks>
        </member>
        <member name="P:DnsClient.Protocol.TxtRecord.Text">
            <summary>
            Gets the actual <c>UTF8</c> representation of the text values of this record.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.TxtRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.String[],System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.TxtRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="values">The values.</param>
            <param name="utf8Values">The UTF8 values.</param>
            <exception cref="T:System.ArgumentNullException">
            If <paramref name="info"/> or <paramref name="utf8Values"/> or <paramref name="values"/> is null.
            </exception>
        </member>
        <member name="T:DnsClient.Protocol.UnknownRecord">
            <summary>
            Experimental RR, not sure if the implementation is actually correct either (not tested).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.10">RFC 1035</seealso>
        </member>
        <member name="P:DnsClient.Protocol.UnknownRecord.Data">
            <summary>
            Gets any data stored in this record.
            </summary>
            <value>
            The byte array.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.UnknownRecord.DataAsString">
            <summary>
            Gets the unknown bytes as Base64 string.
            </summary>
        </member>
        <member name="M:DnsClient.Protocol.UnknownRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.UnknownRecord" /> class.
            </summary>
            <param name="info">The information.</param>
            <param name="data">The raw data.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="data"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.UriRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a Uniform Resource Identifier (URI) resource.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc7553">RFC 7553</seealso>
        </member>
        <member name="P:DnsClient.Protocol.UriRecord.Target">
            <summary>
            Gets or sets the target Uri.
            </summary>
            <value>
            The target.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.UriRecord.Priority">
            <summary>
            Gets or sets the priority.
            </summary>
            <value>
            The priority.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.UriRecord.Weight">
            <summary>
            Gets or sets the weight.
            </summary>
            <value>
            The weight.
            </value>
        </member>
        <member name="M:DnsClient.Protocol.UriRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Int32,System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.UriRecord"/> class.
            </summary>
            <param name="info">The information.</param>
            <param name="priority">The priority.</param>
            <param name="weight">The weight.</param>
            <param name="target">The target.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="info"/> or <paramref name="target"/> is null.</exception>
        </member>
        <member name="T:DnsClient.Protocol.WksRecord">
            <summary>
            A <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> representing a Well Known Service description.
            </summary>
            <remarks>
            Instead of describing the supported protocols in RFCs, the list is now published on http://www.iana.org/.
            </remarks>
            <seealso href="http://www.iana.org/assignments/protocol-numbers/protocol-numbers.xhtml"/>
            <seealso href="https://tools.ietf.org/html/rfc3232">RFC 3232, the most current update.</seealso>
        </member>
        <member name="P:DnsClient.Protocol.WksRecord.Address">
            <summary>
            Gets the address.
            </summary>
            <value>
            The address.
            </value>
        </member>
        <member name="P:DnsClient.Protocol.WksRecord.Protocol">
            <summary>
            Gets the Protocol.
            </summary>
            <remarks>
            According to https://tools.ietf.org/html/rfc6335, only ports for TCP, UDP, DCCP and SCTP services will be assigned.
            </remarks>
        </member>
        <member name="P:DnsClient.Protocol.WksRecord.Bitmap">
            <summary>
            Gets the binary raw bitmap.
            Use <see cref="P:DnsClient.Protocol.WksRecord.Ports"/> to determine which ports are actually configured.
            </summary>
        </member>
        <member name="P:DnsClient.Protocol.WksRecord.Ports">
            <summary>
            Gets the list of assigned ports.
            <para>
            For example, if this list contains port 25, which is assigned to
            the <c>SMTP</c> service. This means that a SMTP services
            is running on <see cref="P:DnsClient.Protocol.WksRecord.Address"/> with transport <see cref="P:DnsClient.Protocol.WksRecord.Protocol"/>.
            </para>
            </summary>
            <seealso href="http://www.iana.org/assignments/port-numbers">Port numbers</seealso>
        </member>
        <member name="M:DnsClient.Protocol.WksRecord.#ctor(DnsClient.Protocol.ResourceRecordInfo,System.Net.IPAddress,System.Int32,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.Protocol.WksRecord" /> class.
            </summary>
            <param name="info">The information.</param>
            <param name="address">The address.</param>
            <param name="protocol">The protocol.</param>
            <param name="bitmap">The raw data.</param>
            <exception cref="T:System.ArgumentNullException">
            If <paramref name="address"/> or <paramref name="info"/> or <paramref name="bitmap"/> is null.
            </exception>
        </member>
        <member name="T:DnsClient.DnsOpCode">
            <summary>
            Specifies kind of query in this message.
            This value is set by the originator of a query and copied into the response.
            </summary>
        </member>
        <member name="F:DnsClient.DnsOpCode.Query">
            <summary>
            A standard query.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsOpCode.IQuery">
            <summary>
            An inverse query.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc3425">RFC 3425</seealso>
        </member>
        <member name="F:DnsClient.DnsOpCode.Status">
            <summary>
            A server status request.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsOpCode.Unassinged3">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsOpCode.Notify">
            <summary>
            Notify query.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1996">RFC 1996</seealso>
        </member>
        <member name="F:DnsClient.DnsOpCode.Update">
            <summary>
            Update query.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2136">RFC 2136</seealso>
        </member>
        <member name="F:DnsClient.DnsOpCode.Unassinged6">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsOpCode.Unassinged7">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsOpCode.Unassinged8">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsOpCode.Unassinged9">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsOpCode.Unassinged10">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsOpCode.Unassinged11">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsOpCode.Unassinged12">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsOpCode.Unassinged13">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsOpCode.Unassinged14">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsOpCode.Unassinged15">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="T:DnsClient.DnsQueryExtensions">
            <summary>
            Extension methods for the <see cref="T:DnsClient.IDnsQuery"/> contract.
            <para>
            The methods implement common queries which are more complex and have some business logic.
            </para>
            </summary>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.GetHostEntry(DnsClient.IDnsQuery,System.String)">
            <summary>
            The <c>GetHostEntry</c> method queries a DNS server for the IP addresses and aliases associated with the <paramref name="hostNameOrAddress"/>.
            In case <paramref name="hostNameOrAddress"/> is an <see cref="T:System.Net.IPAddress"/>, <c>GetHostEntry</c> does a reverse lookup on that first to determine the hostname.
            <para>
            IP addresses found are returned in <see cref="P:System.Net.IPHostEntry.AddressList"/>.
            <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> records are used to populate the <see cref="P:System.Net.IPHostEntry.Aliases"/>.<br/>
            The <see cref="P:System.Net.IPHostEntry.HostName"/> property will be set to the resolved hostname or <paramref name="hostNameOrAddress"/>.
            </para>
            </summary>
            <example>
            The following code example uses the <see cref="M:DnsClient.DnsQueryExtensions.GetHostEntry(DnsClient.IDnsQuery,System.String)"/> method to resolve an IP address or hostname to an <see cref="T:System.Net.IPHostEntry"/> instance.
            <code>
            <![CDATA[
            public static void PrintHostEntry(string hostOrIp)
            {
                var lookup = new LookupClient();
                IPHostEntry hostEntry = lookup.GetHostEntry(hostOrIp);
                Console.WriteLine(hostEntry.HostName);
                foreach (var ip in hostEntry.AddressList)
                {
                    Console.WriteLine(ip);
                }
                foreach (var alias in hostEntry.Aliases)
                {
                    Console.WriteLine(alias);
                }
            }
            ]]>
            </code>
            </example>
            <remarks>
            The method has some logic to populate the <see cref="P:System.Net.IPHostEntry.Aliases"/> list:
            <list type="bullet">
            <item>
            <term>
            In case of sub-domain queries or similar, there might be multiple <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> records for one <see cref="T:System.Net.IPAddress"/>,
            </term>
            </item><item>
            <term>
            If only one <see cref="T:System.Net.IPAddress"/> is in the result set, all the aliases found will be returned.
            </term>
            </item><item>
            <term>
            If more than one <see cref="T:System.Net.IPAddress"/> is in the result set, aliases are returned only if at least one doesn't match the queried hostname.
            </term>
            </item>
            </list>
            </remarks>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="hostNameOrAddress">The <see cref="T:System.Net.IPAddress"/> or host name to query for.</param>
            <returns>
            An <see cref="T:System.Net.IPHostEntry"/> instance that contains address information about the host specified in <paramref name="hostNameOrAddress"/>.
            In case the <paramref name="hostNameOrAddress"/> could not be resolved to a domain name, this method returns <c>null</c>,
            unless <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is set to true, then it might throw a <see cref="T:DnsClient.DnsResponseException"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="hostNameOrAddress"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">In case <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is set to true and a DNS error occurs.</exception>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.GetHostEntryAsync(DnsClient.IDnsQuery,System.String)">
            <summary>
            The <c>GetHostEntryAsync</c> method queries a DNS server for the IP addresses and aliases associated with the <paramref name="hostNameOrAddress"/>.
            In case <paramref name="hostNameOrAddress"/> is an <see cref="T:System.Net.IPAddress"/>, <c>GetHostEntry</c> does a reverse lookup on that first to determine the hostname.
            <para>
            IP addresses found are returned in <see cref="P:System.Net.IPHostEntry.AddressList"/>.
            <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> records are used to populate the <see cref="P:System.Net.IPHostEntry.Aliases"/>.<br/>
            The <see cref="P:System.Net.IPHostEntry.HostName"/> property will be set to the resolved hostname or <paramref name="hostNameOrAddress"/>.
            </para>
            </summary>
            <example>
            The following code example uses the <see cref="M:DnsClient.DnsQueryExtensions.GetHostEntryAsync(DnsClient.IDnsQuery,System.String)"/> method to resolve an IP address or hostname to an <see cref="T:System.Net.IPHostEntry"/> instance.
            <code>
            <![CDATA[
            public static async Task PrintHostEntry(string hostOrIp)
            {
                var lookup = new LookupClient();
                IPHostEntry hostEntry = await lookup.GetHostEntryAsync(hostOrIp);
                Console.WriteLine(hostEntry.HostName);
                foreach (var ip in hostEntry.AddressList)
                {
                    Console.WriteLine(ip);
                }
                foreach (var alias in hostEntry.Aliases)
                {
                    Console.WriteLine(alias);
                }
            }
            ]]>
            </code>
            </example>
            <remarks>
            The method has some logic to populate the <see cref="P:System.Net.IPHostEntry.Aliases"/> list:
            <list type="bullet">
            <item>
            <term>
            In case of sub-domain queries or similar, there might be multiple <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> records for one <see cref="T:System.Net.IPAddress"/>,
            </term>
            </item><item>
            <term>
            If only one <see cref="T:System.Net.IPAddress"/> is in the result set, all the aliases found will be returned.
            </term>
            </item><item>
            <term>
            If more than one <see cref="T:System.Net.IPAddress"/> is in the result set, aliases are returned only if at least one doesn't match the queried hostname.
            </term>
            </item>
            </list>
            </remarks>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="hostNameOrAddress">The <see cref="T:System.Net.IPAddress"/> or host name to query for.</param>
            <returns>
            An <see cref="T:System.Net.IPHostEntry"/> instance that contains address information about the host specified in <paramref name="hostNameOrAddress"/>.
            In case the <paramref name="hostNameOrAddress"/> could not be resolved to a domain name, this method returns <c>null</c>,
            unless <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is set to true, then it might throw a <see cref="T:DnsClient.DnsResponseException"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="hostNameOrAddress"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">In case <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is set to true and a DNS error occurs.</exception>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.GetHostEntry(DnsClient.IDnsQuery,System.Net.IPAddress)">
            <summary>
            The <c>GetHostEntry</c> method does a reverse lookup on the IP <paramref name="address"/>,
            and queries a DNS server for the IP addresses and aliases associated with the resolved hostname.
            <para>
            IP addresses found are returned in <see cref="P:System.Net.IPHostEntry.AddressList"/>.
            <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> records are used to populate the <see cref="P:System.Net.IPHostEntry.Aliases"/>.<br/>
            The <see cref="P:System.Net.IPHostEntry.HostName"/> property will be set to the resolved hostname of the <paramref name="address"/>.
            </para>
            </summary>
            <example>
            The following code example uses the <see cref="M:DnsClient.DnsQueryExtensions.GetHostEntry(DnsClient.IDnsQuery,System.Net.IPAddress)"/> method to resolve an IP address to an <see cref="T:System.Net.IPHostEntry"/> instance.
            <code>
            <![CDATA[
            public static void PrintHostEntry(IPAddress address)
            {
                var lookup = new LookupClient();
                IPHostEntry hostEntry = lookup.GetHostEntry(address);
                Console.WriteLine(hostEntry.HostName);
                foreach (var ip in hostEntry.AddressList)
                {
                    Console.WriteLine(ip);
                }
                foreach (var alias in hostEntry.Aliases)
                {
                    Console.WriteLine(alias);
                }
            }
            ]]>
            </code>
            </example>
            <remarks>
            The method has some logic to populate the <see cref="P:System.Net.IPHostEntry.Aliases"/> list:
            <list type="bullet">
            <item>
            <term>
            In case of sub-domain queries or similar, there might be multiple <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> records for one <see cref="T:System.Net.IPAddress"/>,
            </term>
            </item><item>
            <term>
            If only one <see cref="T:System.Net.IPAddress"/> is in the result set, all the aliases found will be returned.
            </term>
            </item><item>
            <term>
            If more than one <see cref="T:System.Net.IPAddress"/> is in the result set, aliases are returned only if at least one doesn't match the queried hostname.
            </term>
            </item>
            </list>
            </remarks>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="address">The <see cref="T:System.Net.IPAddress"/> to query for.</param>
            <returns>
            An <see cref="T:System.Net.IPHostEntry"/> instance that contains address information about the host specified in <paramref name="address"/>.
            In case the <paramref name="address"/> could not be resolved to a domain name, this method returns <c>null</c>,
            unless <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is set to true, then it might throw a <see cref="T:DnsClient.DnsResponseException"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="address"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">In case <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is set to true and a DNS error occurs.</exception>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.GetHostEntryAsync(DnsClient.IDnsQuery,System.Net.IPAddress)">
            <summary>
            The <c>GetHostEntryAsync</c> method does a reverse lookup on the IP <paramref name="address"/>,
            and queries a DNS server for the IP addresses and aliases associated with the resolved hostname.
            <para>
            IP addresses found are returned in <see cref="P:System.Net.IPHostEntry.AddressList"/>.
            <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> records are used to populate the <see cref="P:System.Net.IPHostEntry.Aliases"/>.<br/>
            The <see cref="P:System.Net.IPHostEntry.HostName"/> property will be set to the resolved hostname of the <paramref name="address"/>.
            </para>
            </summary>
            <example>
            The following code example uses the <see cref="M:DnsClient.DnsQueryExtensions.GetHostEntryAsync(DnsClient.IDnsQuery,System.Net.IPAddress)"/> method to resolve an IP address to an <see cref="T:System.Net.IPHostEntry"/> instance.
            <code>
            <![CDATA[
            public static async Task PrintHostEntry(IPAddress address)
            {
                var lookup = new LookupClient();
                IPHostEntry hostEntry = await lookup.GetHostEntryAsync(address);
                Console.WriteLine(hostEntry.HostName);
                foreach (var ip in hostEntry.AddressList)
                {
                    Console.WriteLine(ip);
                }
                foreach (var alias in hostEntry.Aliases)
                {
                    Console.WriteLine(alias);
                }
            }
            ]]>
            </code>
            </example>
            <remarks>
            The method has some logic to populate the <see cref="P:System.Net.IPHostEntry.Aliases"/> list:
            <list type="bullet">
            <item>
            <term>
            In case of sub-domain queries or similar, there might be multiple <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> records for one <see cref="T:System.Net.IPAddress"/>,
            </term>
            </item><item>
            <term>
            If only one <see cref="T:System.Net.IPAddress"/> is in the result set, all the aliases found will be returned.
            </term>
            </item><item>
            <term>
            If more than one <see cref="T:System.Net.IPAddress"/> is in the result set, aliases are returned only if at least one doesn't match the queried hostname.
            </term>
            </item>
            </list>
            </remarks>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="address">The <see cref="T:System.Net.IPAddress"/> to query for.</param>
            <returns>
            An <see cref="T:System.Net.IPHostEntry"/> instance that contains address information about the host specified in <paramref name="address"/>.
            In case the <paramref name="address"/> could not be resolved to a domain name, this method returns <c>null</c>,
            unless <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is set to true, then it might throw a <see cref="T:DnsClient.DnsResponseException"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="address"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">In case <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is set to true and a DNS error occurs.</exception>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.GetHostName(DnsClient.IDnsQuery,System.Net.IPAddress)">
            <summary>
            The <c>GetHostName</c> method queries a DNS server to resolve the hostname of the <paramref name="address"/> via reverse lookup.
            </summary>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="address">The <see cref="T:System.Net.IPAddress"/> to resolve.</param>
            <returns>
            The hostname if the reverse lookup was successful or <c>null</c>, in case the host was not found.
            If <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is set to <c>true</c>, this method will throw an <see cref="T:DnsClient.DnsResponseException"/> instead of returning <c>null</c>!
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="address"/>is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">If no host has been found and <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is <c>true</c>.</exception>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.GetHostNameAsync(DnsClient.IDnsQuery,System.Net.IPAddress)">
            <summary>
            The <c>GetHostNameAsync</c> method queries a DNS server to resolve the hostname of the <paramref name="address"/> via reverse lookup.
            </summary>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="address">The <see cref="T:System.Net.IPAddress"/> to resolve.</param>
            <returns>
            The hostname if the reverse lookup was successful or <c>null</c>, in case the host was not found.
            If <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is set to <c>true</c>, this method will throw an <see cref="T:DnsClient.DnsResponseException"/> instead of returning <c>null</c>!
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="address"/>is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">If no host has been found and <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is <c>true</c>.</exception>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.ResolveService(DnsClient.IDnsQuery,System.String,System.String,System.Net.Sockets.ProtocolType)">
            <summary>
            The <c>ResolveService</c> method does a <see cref="F:DnsClient.QueryType.SRV"/> lookup for <c>_{<paramref name="serviceName"/>}[._{<paramref name="protocol"/>}].{<paramref name="baseDomain"/>}</c>
            and aggregates the result (hostname, port and list of <see cref="T:System.Net.IPAddress"/>s) to a <see cref="T:DnsClient.ServiceHostEntry"/>.
            <para>
            This method expects matching A or AAAA records to populate the <see cref="P:System.Net.IPHostEntry.AddressList"/>,
            and/or a <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> record to populate the <see cref="P:System.Net.IPHostEntry.HostName"/> property of the result.
            </para>
            </summary>
            <remarks>
            The returned list of <see cref="T:System.Net.IPAddress"/>s and/or the hostname can be empty if no matching additional records are found.
            </remarks>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="baseDomain">The base domain, which will be appended to the end of the query string.</param>
            <param name="serviceName">The name of the service to look for. Must not have any <c>_</c> prefix.</param>
            <param name="protocol">
            The protocol of the service to query for.
            Set it to <see cref="F:System.Net.Sockets.ProtocolType.Unknown"/> or <see cref="F:System.Net.Sockets.ProtocolType.Unspecified"/> to not pass any protocol.
            </param>
            <returns>A collection of <see cref="T:DnsClient.ServiceHostEntry"/>s.</returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="baseDomain"/> or <paramref name="serviceName"/> are null.</exception>
            <seealso href="https://tools.ietf.org/html/rfc2782">RFC 2782</seealso>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.ResolveServiceAsync(DnsClient.IDnsQuery,System.String,System.String,System.Net.Sockets.ProtocolType)">
            <summary>
            The <c>ResolveServiceAsync</c> method does  a <see cref="F:DnsClient.QueryType.SRV"/> lookup for <c>_{<paramref name="serviceName"/>}[._{<paramref name="protocol"/>}].{<paramref name="baseDomain"/>}</c>
            and aggregates the result (hostname, port and list of <see cref="T:System.Net.IPAddress"/>s) to a <see cref="T:DnsClient.ServiceHostEntry"/>.
            <para>
            This method expects matching A or AAAA records to populate the <see cref="P:System.Net.IPHostEntry.AddressList"/>,
            and/or a <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> record to populate the <see cref="P:System.Net.IPHostEntry.HostName"/> property of the result.
            </para>
            </summary>
            <remarks>
            The returned list of <see cref="T:System.Net.IPAddress"/>s and/or the hostname can be empty if no matching additional records are found.
            </remarks>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="baseDomain">The base domain, which will be appended to the end of the query string.</param>
            <param name="serviceName">The name of the service to look for. Must not have any <c>_</c> prefix.</param>
            <param name="protocol">
            The protocol of the service to query for.
            Set it to <see cref="F:System.Net.Sockets.ProtocolType.Unknown"/> or <see cref="F:System.Net.Sockets.ProtocolType.Unspecified"/> to not pass any protocol.
            </param>
            <returns>A collection of <see cref="T:DnsClient.ServiceHostEntry"/>s.</returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="baseDomain"/> or <paramref name="serviceName"/> are null.</exception>
            <seealso href="https://tools.ietf.org/html/rfc2782">RFC 2782</seealso>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.ResolveService(DnsClient.IDnsQuery,System.String,System.String,System.String)">
            <summary>
            The <c>ResolveService</c> method does a <see cref="F:DnsClient.QueryType.SRV"/> lookup for <c>_{<paramref name="serviceName"/>}[._{<paramref name="tag"/>}].{<paramref name="baseDomain"/>}</c>
            and aggregates the result (hostname, port and list of <see cref="T:System.Net.IPAddress"/>s) to a <see cref="T:DnsClient.ServiceHostEntry"/>.
            <para>
            This method expects matching A or AAAA records to populate the <see cref="P:System.Net.IPHostEntry.AddressList"/>,
            and/or a <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> record to populate the <see cref="P:System.Net.IPHostEntry.HostName"/> property of the result.
            </para>
            </summary>
            <remarks>
            The returned list of <see cref="T:System.Net.IPAddress"/>s and/or the hostname can be empty if no matching additional records are found.
            </remarks>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="baseDomain">The base domain, which will be appended to the end of the query string.</param>
            <param name="serviceName">The name of the service to look for. Must not have any <c>_</c> prefix.</param>
            <param name="tag">An optional tag. Must not have any <c>_</c> prefix.</param>
            <returns>A collection of <see cref="T:DnsClient.ServiceHostEntry"/>s.</returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="baseDomain"/> or <paramref name="serviceName"/> are null.</exception>
            <seealso href="https://tools.ietf.org/html/rfc2782">RFC 2782</seealso>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.ResolveService(DnsClient.IDnsQuery,System.String)">
            <summary>
            The <c>ResolveService</c> method does a <see cref="F:DnsClient.QueryType.SRV"/> lookup for <c>{<paramref name="service"/>}</c>
            and aggregates the result (hostname, port and list of <see cref="T:System.Net.IPAddress"/>s) to a <see cref="T:DnsClient.ServiceHostEntry"/>.
            <para>
            This method expects matching A or AAAA records to populate the <see cref="P:System.Net.IPHostEntry.AddressList"/>,
            and/or a <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> record to populate the <see cref="P:System.Net.IPHostEntry.HostName"/> property of the result.
            </para>
            </summary>
            <remarks>
            The returned list of <see cref="T:System.Net.IPAddress"/>s and/or the hostname can be empty if no matching additional records are found.
            </remarks>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="service">The name of the service to look for. Must have <c>_</c> prefix and domain and protocol.</param>
            <returns>A collection of <see cref="T:DnsClient.ServiceHostEntry"/>s.</returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="service"/> are null.</exception>
            <seealso href="https://tools.ietf.org/html/rfc2782">RFC 2782</seealso>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.ResolveServiceAsync(DnsClient.IDnsQuery,System.String,System.String,System.String)">
            <summary>
            The <c>ResolveServiceAsync</c> method does a <see cref="F:DnsClient.QueryType.SRV"/> lookup for <c>_{<paramref name="serviceName"/>}[._{<paramref name="tag"/>}].{<paramref name="baseDomain"/>}</c>
            and aggregates the result (hostname, port and list of <see cref="T:System.Net.IPAddress"/>s) to a <see cref="T:DnsClient.ServiceHostEntry"/>.
            <para>
            This method expects matching A or AAAA records to populate the <see cref="P:System.Net.IPHostEntry.AddressList"/>,
            and/or a <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> record to populate the <see cref="P:System.Net.IPHostEntry.HostName"/> property of the result.
            </para>
            </summary>
            <remarks>
            The returned list of <see cref="T:System.Net.IPAddress"/>s and/or the hostname can be empty if no matching additional records are found.
            </remarks>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="baseDomain">The base domain, which will be appended to the end of the query string.</param>
            <param name="serviceName">The name of the service to look for. Must not have any <c>_</c> prefix.</param>
            <param name="tag">An optional tag. Must not have any <c>_</c> prefix.</param>
            <returns>A collection of <see cref="T:DnsClient.ServiceHostEntry"/>s.</returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="baseDomain"/> or <paramref name="serviceName"/> are null.</exception>
            <seealso href="https://tools.ietf.org/html/rfc2782">RFC 2782</seealso>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.ResolveServiceAsync(DnsClient.IDnsQuery,System.String)">
            <summary>
            The <c>ResolveServiceAsync</c> method does a <see cref="F:DnsClient.QueryType.SRV"/> lookup for <c>{<paramref name="service"/>}</c>
            and aggregates the result (hostname, port and list of <see cref="T:System.Net.IPAddress"/>s) to a <see cref="T:DnsClient.ServiceHostEntry"/>.
            <para>
            This method expects matching A or AAAA records to populate the <see cref="P:System.Net.IPHostEntry.AddressList"/>,
            and/or a <see cref="F:DnsClient.Protocol.ResourceRecordType.CNAME"/> record to populate the <see cref="P:System.Net.IPHostEntry.HostName"/> property of the result.
            </para>
            </summary>
            <remarks>
            The returned list of <see cref="T:System.Net.IPAddress"/>s and/or the hostname can be empty if no matching additional records are found.
            </remarks>
            <param name="query">The <see cref="T:DnsClient.IDnsQuery"/> instance.</param>
            <param name="service">The name of the service to look for. Must have <c>_</c> prefix and domain and protocol.</param>
            <returns>A collection of <see cref="T:DnsClient.ServiceHostEntry"/>s.</returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="service"/> are null.</exception>
            <seealso href="https://tools.ietf.org/html/rfc2782">RFC 2782</seealso>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.ConcatServiceName(System.String,System.String,System.String)">
            <summary>
            Constructs a DNS <see cref="F:DnsClient.QueryType.SRV"/> query string from the constituent parts.
            </summary>
            <param name="baseDomain">The base domain, which will be appended to the end of the query string.</param>
            <param name="serviceName">The name of the service to look for. Must not have any <c>_</c> prefix.</param>
            <param name="tag">An optional tag. Must not have any <c>_</c> prefix.</param>
            <returns>A service string that can be used in a DNS service query.</returns>
        </member>
        <member name="M:DnsClient.DnsQueryExtensions.ResolveServiceProcessResult(DnsClient.IDnsQueryResponse)">
            <summary>
            Transforms a DNS query result into a collection of <see cref="T:DnsClient.ServiceHostEntry"/> objects.
            </summary>
            <param name="result">The DNS </param>
            <returns>A collection of <see cref="T:DnsClient.ServiceHostEntry"/>s.</returns>
        </member>
        <member name="T:DnsClient.ServiceHostEntry">
            <summary>
            Extends <see cref="T:System.Net.IPHostEntry"/> by the <see cref="P:DnsClient.ServiceHostEntry.Port"/> property.
            </summary>
            <seealso cref="T:System.Net.IPHostEntry" />
        </member>
        <member name="P:DnsClient.ServiceHostEntry.Port">
            <summary>
            Gets or sets the port.
            </summary>
            <value>
            The port of this entry.
            </value>
        </member>
        <member name="P:DnsClient.ServiceHostEntry.Priority">
            <summary>
            Gets or sets priority of the original <see cref="F:DnsClient.Protocol.ResourceRecordType.SRV"/> record.
            Might be zero if not provided.
            </summary>
            <value>
            The priority of this entry.
            </value>
        </member>
        <member name="P:DnsClient.ServiceHostEntry.Weight">
            <summary>
            Gets or sets weight of the original <see cref="F:DnsClient.Protocol.ResourceRecordType.SRV"/> record.
            Might be zero if not provided.
            </summary>
            <value>
            The weight of this entry.
            </value>
        </member>
        <member name="T:DnsClient.DnsQueryOptions">
            <summary>
            The options used to override the defaults of <see cref="T:DnsClient.LookupClient"/> per query.
            </summary>
        </member>
        <member name="F:DnsClient.DnsQueryOptions.MinimumBufferSize">
            <summary>
            The minimum payload size. Anything equal or less than that will default back to this value and might disable EDNS.
            </summary>
        </member>
        <member name="F:DnsClient.DnsQueryOptions.MaximumBufferSize">
            <summary>
            The maximum reasonable payload size.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.EnableAuditTrail">
            <summary>
            Gets or sets a flag indicating whether each <see cref="T:DnsClient.IDnsQueryResponse"/> will contain a full documentation of the response(s).
            Default is <c>False</c>.
            </summary>
            <seealso cref="P:DnsClient.IDnsQueryResponse.AuditTrail"/>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.UseCache">
            <summary>
            Gets or sets a flag indicating whether DNS queries should use response caching or not.
            The cache duration is calculated by the resource record of the response. Usually, the lowest TTL is used.
            Default is <c>True</c>.
            </summary>
            <remarks>
            In case the DNS Server returns records with a TTL of zero. The response cannot be cached.
            </remarks>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.Recursion">
            <summary>
            Gets or sets a flag indicating whether DNS queries should instruct the DNS server to do recursive lookups, or not.
            Default is <c>True</c>.
            </summary>
            <value>The flag indicating if recursion should be used or not.</value>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.Retries">
            <summary>
            Gets or sets the number of tries to get a response from one name server before trying the next one.
            Only transient errors, like network or connection errors will be retried.
            Default is <c>2</c> which will be three tries total.
            <para>
            If all configured <see cref="P:DnsClient.DnsQueryAndServerOptions.NameServers"/> error out after retries, an exception will be thrown at the end.
            </para>
            </summary>
            <value>The number of retries.</value>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.ThrowDnsErrors">
            <summary>
            Gets or sets a flag indicating whether the <see cref="T:DnsClient.ILookupClient"/> should throw a <see cref="T:DnsClient.DnsResponseException"/>
            in case the query result has a <see cref="T:DnsClient.DnsResponseCode"/> other than <see cref="F:DnsClient.DnsResponseCode.NoError"/>.
            Default is <c>False</c>.
            </summary>
            <remarks>
            <para>
            If set to <c>False</c>, the query will return a result with an <see cref="P:DnsClient.IDnsQueryResponse.ErrorMessage"/>
            which contains more information.
            </para>
            <para>
            If set to <c>True</c>, any query method of <see cref="T:DnsClient.IDnsQuery"/> will throw an <see cref="T:DnsClient.DnsResponseException"/> if
            the response header indicates an error.
            </para>
            <para>
            If both, <see cref="P:DnsClient.DnsQueryOptions.ContinueOnDnsError"/> and <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> are set to <c>True</c>,
            <see cref="T:DnsClient.ILookupClient"/> will continue to query all configured <see cref="P:DnsClient.DnsQueryAndServerOptions.NameServers"/>.
            If none of the servers yield a valid response, a <see cref="T:DnsClient.DnsResponseException"/> will be thrown
            with the error of the last response.
            </para>
            </remarks>
            <seealso cref="T:DnsClient.DnsResponseCode"/>
            <seealso cref="P:DnsClient.DnsQueryOptions.ContinueOnDnsError"/>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.UseRandomNameServer">
            <summary>
            Gets or sets a flag indicating whether the <see cref="T:DnsClient.ILookupClient"/> can cycle through all
            configured <see cref="P:DnsClient.DnsQueryAndServerOptions.NameServers"/> on each consecutive request, basically using a random server, or not.
            Default is <c>True</c>.
            If only one <see cref="T:DnsClient.NameServer"/> is configured, this setting is not used.
            </summary>
            <remarks>
            <para>
            If <c>False</c>, configured endpoint will be used in random order.
            If <c>True</c>, the order will be preserved.
            </para>
            <para>
            Even if <see cref="P:DnsClient.DnsQueryOptions.UseRandomNameServer"/> is set to <c>True</c>, the endpoint might still get
            disabled and might not being used for some time if it errors out, e.g. no connection can be established.
            </para>
            </remarks>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.ContinueOnDnsError">
            <summary>
            Gets or sets a flag indicating whether to query the next configured <see cref="P:DnsClient.DnsQueryAndServerOptions.NameServers"/> in case the response of the last query
            returned a <see cref="T:DnsClient.DnsResponseCode"/> other than <see cref="F:DnsClient.DnsResponseCode.NoError"/>.
            Default is <c>True</c>.
            </summary>
            <remarks>
            If <c>True</c>, lookup client will continue until a server returns a valid result, or,
            if no <see cref="P:DnsClient.DnsQueryAndServerOptions.NameServers"/> yield a valid result, the last response with the error will be returned.
            In case no server yields a valid result and <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is also enabled, an exception
            will be thrown containing the error of the last response.
            <para>
            If  <c>True</c> and <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled, the exception will be thrown on first encounter without trying any other servers.
            </para>
            </remarks>
            <seealso cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.ContinueOnEmptyResponse">
            <summary>
            Gets or sets a flag indicating whether to query the next configured <see cref="P:DnsClient.DnsQueryAndServerOptions.NameServers"/>
            if the response does not have an error <see cref="T:DnsClient.DnsResponseCode"/> but the query was not answered by the response.
            Default is <c>True</c>.
            </summary>
            <remarks>
            The query is answered if there is at least one <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> in the answers section
            matching the <see cref="T:DnsClient.DnsQuestion"/>'s <see cref="T:DnsClient.QueryType"/>.
            <para>
            If there are zero answers in the response, the query is not answered, independent of the <see cref="T:DnsClient.QueryType"/>.
            If there are answers in the response, the <see cref="T:DnsClient.QueryType"/> is used to find a matching record,
            query types <see cref="F:DnsClient.QueryType.ANY"/> and <see cref="F:DnsClient.QueryType.AXFR"/> will be ignored by this check.
            </para>
            </remarks>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.Timeout">
            <summary>
            Gets or sets the request timeout in milliseconds. <see cref="P:DnsClient.DnsQueryOptions.Timeout"/> is used for limiting the connection and request time for one operation.
            Timeout must be greater than zero and less than <see cref="F:System.Int32.MaxValue"/>.
            If <see cref="F:System.Threading.Timeout.InfiniteTimeSpan"/> (or -1) is used, no timeout will be applied.
            Default is 5 seconds.
            </summary>
            <remarks>
            If a very short timeout is configured, queries will more likely result in <see cref="T:System.TimeoutException"/>s.
            <para>
            Important to note, <see cref="T:System.TimeoutException"/>s will be retried, if <see cref="P:DnsClient.DnsQueryOptions.Retries"/> are not disabled (set to <c>0</c>).
            This should help in case one or more configured DNS servers are not reachable or under load for example.
            </para>
            </remarks>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.UseTcpFallback">
            <summary>
            Gets or sets a flag indicating whether TCP should be used in case a UDP response is truncated.
            Default is <c>True</c>.
            <para>
            If <c>False</c>, truncated results will potentially yield no or incomplete answers.
            </para>
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.UseTcpOnly">
            <summary>
            Gets or sets a flag indicating whether UDP should not be used at all.
            Default is <c>False</c>.
            <para>
            Enable this only if UDP cannot be used because of your firewall rules for example.
            Also, zone transfers (see <see cref="F:DnsClient.QueryType.AXFR"/>) must use TCP only.
            </para>
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.ExtendedDnsBufferSize">
            <summary>
            Gets or sets the maximum buffer used for UDP requests.
            Defaults to <c>4096</c>.
            <para>
            If this value is less or equal to <c>512</c> bytes, EDNS might be disabled.
            </para>
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.RequestDnsSecRecords">
            <summary>
            Gets or sets a flag indicating whether EDNS should be enabled and the <c>DO</c> flag should be set.
            Defaults to <c>False</c>.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.CacheFailedResults">
            <summary>
            Gets or sets a flag indicating whether the DNS failures are being cached. The purpose of caching 
            failures is to reduce repeated lookup attempts within a short space of time.
            Defaults to <c>False</c>.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryOptions.FailedResultsCacheDuration">
            <summary>
            Gets or sets the duration to cache failed lookups. Does not apply if failed lookups are not being cached.
            Defaults to <c>5 seconds</c>.
            </summary>
        </member>
        <member name="M:DnsClient.DnsQueryOptions.op_Implicit(DnsClient.DnsQueryOptions)~DnsClient.DnsQuerySettings">
            <summary>
            Converts the query options into readonly settings.
            </summary>
            <param name="fromOptions">The options.</param>
        </member>
        <member name="T:DnsClient.DnsQueryAndServerOptions">
            <summary>
            The options used to override the defaults of <see cref="T:DnsClient.LookupClient"/> per query.
            </summary>
        </member>
        <member name="M:DnsClient.DnsQueryAndServerOptions.#ctor">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.DnsQueryAndServerOptions"/> without name servers.
            If no nameservers are configured, a query will fallback to the nameservers already configured on the <see cref="T:DnsClient.LookupClient"/> instance.
            </summary>
        </member>
        <member name="M:DnsClient.DnsQueryAndServerOptions.#ctor(DnsClient.NameServer[])">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.DnsQueryAndServerOptions"/>.
            </summary>
            <param name="nameServers">A collection of name servers.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="nameServers"/> is null.</exception>
        </member>
        <member name="M:DnsClient.DnsQueryAndServerOptions.#ctor(System.Net.IPEndPoint[])">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.DnsQueryAndServerOptions"/>.
            </summary>
            <param name="nameServers">A collection of name servers.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="nameServers"/> is null.</exception>
        </member>
        <member name="M:DnsClient.DnsQueryAndServerOptions.#ctor(System.Net.IPAddress[])">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.DnsQueryAndServerOptions"/>.
            </summary>
            <param name="nameServers">A collection of name servers.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="nameServers"/> is null.</exception>
        </member>
        <member name="P:DnsClient.DnsQueryAndServerOptions.NameServers">
            <summary>
            Gets a list of name servers which should be used to query.
            </summary>
        </member>
        <member name="M:DnsClient.DnsQueryAndServerOptions.op_Implicit(DnsClient.DnsQueryAndServerOptions)~DnsClient.DnsQueryAndServerSettings">
            <summary>
            Converts the query options into readonly settings.
            </summary>
            <param name="fromOptions">The options.</param>
        </member>
        <member name="T:DnsClient.LookupClientOptions">
            <summary>
            The options used to configure defaults in <see cref="T:DnsClient.LookupClient"/> and to optionally use specific settings per query.
            </summary>
        </member>
        <member name="M:DnsClient.LookupClientOptions.#ctor">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.LookupClientOptions"/> with default settings.
            </summary>
        </member>
        <member name="M:DnsClient.LookupClientOptions.#ctor(DnsClient.NameServer[])">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.LookupClientOptions"/>.
            </summary>
            <param name="nameServers">A collection of name servers.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="nameServers"/> is null.</exception>
        </member>
        <member name="M:DnsClient.LookupClientOptions.#ctor(System.Net.IPEndPoint[])">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.LookupClientOptions"/>.
            </summary>
            <param name="nameServers">A collection of name servers.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="nameServers"/> is null.</exception>
        </member>
        <member name="M:DnsClient.LookupClientOptions.#ctor(System.Net.IPAddress[])">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.LookupClientOptions"/>.
            </summary>
            <param name="nameServers">A collection of name servers.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="nameServers"/> is null.</exception>
        </member>
        <member name="P:DnsClient.LookupClientOptions.AutoResolveNameServers">
            <summary>
            Gets or sets a flag indicating whether the name server collection should be automatically resolved.
            Default is <c>True</c>.
            </summary>
            <remarks>
            If name servers are configured manually via the constructor, this flag is set to false.
            If you want both, your manually configured servers and auto resolved name servers,
            you can use both (ctor or) <see cref="P:DnsClient.DnsQueryAndServerOptions.NameServers"/> and <see cref="P:DnsClient.LookupClientOptions.AutoResolveNameServers"/> set to <c>True</c>.
            </remarks>
        </member>
        <member name="P:DnsClient.LookupClientOptions.MinimumCacheTimeout">
            <summary>
            Gets or sets a <see cref="T:System.TimeSpan"/> which can override the TTL of a resource record in case the
            TTL of the record is lower than this minimum value.
            Default is <c>Null</c>.
            <para>
            This is useful in case the server returns records with zero TTL.
            </para>
            </summary>
            <remarks>
            This setting gets ignored in case <see cref="P:DnsClient.DnsQueryOptions.UseCache"/> is set to <c>False</c>,
            or the value is set to <c>Null</c> or <see cref="F:System.TimeSpan.Zero"/>.
            The maximum value is 24 days or <see cref="F:System.Threading.Timeout.Infinite"/> (choose a wise setting).
            </remarks>
        </member>
        <member name="P:DnsClient.LookupClientOptions.MaximumCacheTimeout">
            <summary>
            Gets a <see cref="T:System.TimeSpan"/> which can override the TTL of a resource record in case the
            TTL of the record is higher than this maximum value.
            Default is <c>Null</c>.
            </summary>
            <remarks>
            This setting gets ignored in case <see cref="P:DnsClient.DnsQueryOptions.UseCache"/> is set to <c>False</c>,
            or the value is set to <c>Null</c>, <see cref="F:System.Threading.Timeout.Infinite"/> or <see cref="F:System.TimeSpan.Zero"/>.
            The maximum value is 24 days (which shouldn't be used).
            </remarks>
        </member>
        <member name="T:DnsClient.DnsQuerySettings">
            <summary>
            The options used to override the defaults of <see cref="T:DnsClient.LookupClient"/> per query.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.EnableAuditTrail">
            <summary>
            Gets a flag indicating whether each <see cref="T:DnsClient.IDnsQueryResponse"/> will contain a full documentation of the response(s).
            Default is <c>False</c>.
            </summary>
            <seealso cref="P:DnsClient.IDnsQueryResponse.AuditTrail"/>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.UseCache">
            <summary>
            Gets a flag indicating whether DNS queries should use response caching or not.
            The cache duration is calculated by the resource record of the response. Usually, the lowest TTL is used.
            Default is <c>True</c>.
            </summary>
            <remarks>
            In case the DNS Server returns records with a TTL of zero. The response cannot be cached.
            </remarks>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.Recursion">
            <summary>
            Gets a flag indicating whether DNS queries should instruct the DNS server to do recursive lookups, or not.
            Default is <c>True</c>.
            </summary>
            <value>The flag indicating if recursion should be used or not.</value>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.Retries">
            <summary>
            Gets the number of tries to get a response from one name server before trying the next one.
            Only transient errors, like network or connection errors will be retried.
            Default is <c>5</c>.
            <para>
            If all configured <see cref="P:DnsClient.DnsQueryAndServerSettings.NameServers"/> error out after retries, an exception will be thrown at the end.
            </para>
            </summary>
            <value>The number of retries.</value>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.ThrowDnsErrors">
            <summary>
            Gets a flag indicating whether the <see cref="T:DnsClient.ILookupClient"/> should throw a <see cref="T:DnsClient.DnsResponseException"/>
            in case the query result has a <see cref="T:DnsClient.DnsResponseCode"/> other than <see cref="F:DnsClient.DnsResponseCode.NoError"/>.
            Default is <c>False</c>.
            </summary>
            <remarks>
            <para>
            If set to <c>False</c>, the query will return a result with an <see cref="P:DnsClient.IDnsQueryResponse.ErrorMessage"/>
            which contains more information.
            </para>
            <para>
            If set to <c>True</c>, any query method of <see cref="T:DnsClient.IDnsQuery"/> will throw an <see cref="T:DnsClient.DnsResponseException"/> if
            the response header indicates an error.
            </para>
            <para>
            If both, <see cref="P:DnsClient.DnsQuerySettings.ContinueOnDnsError"/> and <see cref="P:DnsClient.DnsQuerySettings.ThrowDnsErrors"/> are set to <c>True</c>,
            <see cref="T:DnsClient.ILookupClient"/> will continue to query all configured <see cref="P:DnsClient.DnsQueryAndServerSettings.NameServers"/>.
            If none of the servers yield a valid response, a <see cref="T:DnsClient.DnsResponseException"/> will be thrown
            with the error of the last response.
            </para>
            </remarks>
            <seealso cref="T:DnsClient.DnsResponseCode"/>
            <seealso cref="P:DnsClient.DnsQuerySettings.ContinueOnDnsError"/>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.UseRandomNameServer">
            <summary>
            Gets a flag indicating whether the <see cref="T:DnsClient.ILookupClient"/> can cycle through all
            configured <see cref="P:DnsClient.DnsQueryAndServerSettings.NameServers"/> on each consecutive request, basically using a random server, or not.
            Default is <c>True</c>.
            If only one <see cref="T:DnsClient.NameServer"/> is configured, this setting is not used.
            </summary>
            <remarks>
            <para>
            If <c>False</c>, configured endpoint will be used in random order.
            If <c>True</c>, the order will be preserved.
            </para>
            <para>
            Even if <see cref="P:DnsClient.DnsQuerySettings.UseRandomNameServer"/> is set to <c>True</c>, the endpoint might still get
            disabled and might not being used for some time if it errors out, e.g. no connection can be established.
            </para>
            </remarks>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.ContinueOnDnsError">
            <summary>
            Gets a flag indicating whether to query the next configured <see cref="P:DnsClient.DnsQueryAndServerSettings.NameServers"/> in case the response of the last query
            returned a <see cref="T:DnsClient.DnsResponseCode"/> other than <see cref="F:DnsClient.DnsResponseCode.NoError"/>.
            Default is <c>True</c>.
            </summary>
            <remarks>
            If <c>True</c>, lookup client will continue until a server returns a valid result, or,
            if no <see cref="P:DnsClient.DnsQueryAndServerSettings.NameServers"/> yield a valid result, the last response with the error will be returned.
            In case no server yields a valid result and <see cref="P:DnsClient.DnsQuerySettings.ThrowDnsErrors"/> is also enabled, an exception
            will be thrown containing the error of the last response.
            <para>
            If  <c>True</c> and <see cref="P:DnsClient.DnsQuerySettings.ThrowDnsErrors"/> is enabled, the exception will be thrown on first encounter without trying any other servers.
            </para>
            </remarks>
            <seealso cref="P:DnsClient.DnsQuerySettings.ThrowDnsErrors"/>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.ContinueOnEmptyResponse">
            <summary>
            Gets or sets a flag indicating whether to query the next configured <see cref="P:DnsClient.DnsQueryAndServerOptions.NameServers"/>
            if the response does not have an error <see cref="T:DnsClient.DnsResponseCode"/> but the query was not answered by the response.
            Default is <c>True</c>.
            </summary>
            <remarks>
            The query is answered if there is at least one <see cref="T:DnsClient.Protocol.DnsResourceRecord"/> in the answers section
            matching the <see cref="T:DnsClient.DnsQuestion"/>'s <see cref="T:DnsClient.QueryType"/>.
            <para>
            If there are zero answers in the response, the query is not answered, independent of the <see cref="T:DnsClient.QueryType"/>.
            If there are answers in the response, the <see cref="T:DnsClient.QueryType"/> is used to find a matching record,
            query types <see cref="F:DnsClient.QueryType.ANY"/> and <see cref="F:DnsClient.QueryType.AXFR"/> will be ignored by this check.
            </para>
            </remarks>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.Timeout">
            <summary>
            Gets the request timeout in milliseconds. <see cref="P:DnsClient.DnsQuerySettings.Timeout"/> is used for limiting the connection and request time for one operation.
            Timeout must be greater than zero and less than <see cref="F:System.Int32.MaxValue"/>.
            If <see cref="F:System.Threading.Timeout.InfiniteTimeSpan"/> (or -1) is used, no timeout will be applied.
            Default is 5 seconds.
            </summary>
            <remarks>
            If a very short timeout is configured, queries will more likely result in <see cref="T:System.TimeoutException"/>s.
            <para>
            Important to note, <see cref="T:System.TimeoutException"/>s will be retried, if <see cref="P:DnsClient.DnsQuerySettings.Retries"/> are not disabled (set to <c>0</c>).
            This should help in case one or more configured DNS servers are not reachable or under load for example.
            </para>
            </remarks>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.UseTcpFallback">
            <summary>
            Gets a flag indicating whether TCP should be used in case a UDP response is truncated.
            Default is <c>True</c>.
            <para>
            If <c>False</c>, truncated results will potentially yield no or incomplete answers.
            </para>
            </summary>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.UseTcpOnly">
            <summary>
            Gets a flag indicating whether UDP should not be used at all.
            Default is <c>False</c>.
            <para>
            Enable this only if UDP cannot be used because of your firewall rules for example.
            Also, zone transfers (see <see cref="F:DnsClient.QueryType.AXFR"/>) must use TCP only.
            </para>
            </summary>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.UseExtendedDns">
            <summary>
            Gets a flag indicating whether EDNS is enabled based on the values
            of <see cref="P:DnsClient.DnsQuerySettings.ExtendedDnsBufferSize"/> and <see cref="P:DnsClient.DnsQuerySettings.RequestDnsSecRecords"/>.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.ExtendedDnsBufferSize">
            <summary>
            Gets the maximum buffer used for UDP requests.
            Defaults to <c>4096</c>.
            <para>
            If this value is less or equal to <c>512</c> bytes, EDNS might be disabled.
            </para>
            </summary>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.RequestDnsSecRecords">
            <summary>
            Gets a flag indicating whether EDNS should be enabled and the <c>DO</c> flag should be set.
            Defaults to <c>False</c>.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.CacheFailedResults">
            <summary>
            Gets a flag indicating whether the DNS failures are being cached. The purpose of caching 
            failures is to reduce repeated lookup attempts within a short space of time.
            Defaults to <c>False</c>.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQuerySettings.FailedResultsCacheDuration">
            <summary>
            If failures are being cached this value indicates how long they will be held in the cache for.
            Defaults to <c>5 seconds</c>.
            </summary>
        </member>
        <member name="M:DnsClient.DnsQuerySettings.#ctor(DnsClient.DnsQueryOptions)">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.DnsQueryAndServerSettings"/>.
            </summary>
        </member>
        <member name="M:DnsClient.DnsQuerySettings.Equals(System.Object)">
            <inheritdocs />
        </member>
        <member name="M:DnsClient.DnsQuerySettings.Equals(DnsClient.DnsQuerySettings)">
            <inheritdocs />
        </member>
        <member name="T:DnsClient.DnsQueryAndServerSettings">
            <summary>
            The readonly version of <see cref="T:DnsClient.DnsQueryOptions"/> used to customize settings per query.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryAndServerSettings.NameServers">
            <summary>
            Gets a collection of name servers which should be used to query.
            </summary>
        </member>
        <member name="M:DnsClient.DnsQueryAndServerSettings.#ctor(DnsClient.DnsQueryAndServerOptions)">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.DnsQueryAndServerSettings"/>.
            </summary>
        </member>
        <member name="M:DnsClient.DnsQueryAndServerSettings.#ctor(DnsClient.DnsQueryAndServerOptions,System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer})">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.DnsQueryAndServerSettings"/>.
            </summary>
        </member>
        <member name="M:DnsClient.DnsQueryAndServerSettings.Equals(System.Object)">
            <inheritdocs />
        </member>
        <member name="M:DnsClient.DnsQueryAndServerSettings.Equals(DnsClient.DnsQueryAndServerSettings)">
            <inheritdocs />
        </member>
        <member name="T:DnsClient.LookupClientSettings">
            <summary>
            The readonly version of <see cref="T:DnsClient.LookupClientOptions"/> used as default settings in <see cref="T:DnsClient.LookupClient"/>.
            </summary>
        </member>
        <member name="M:DnsClient.LookupClientSettings.#ctor(DnsClient.LookupClientOptions)">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.LookupClientSettings"/>.
            </summary>
        </member>
        <member name="P:DnsClient.LookupClientSettings.MinimumCacheTimeout">
            <summary>
            Gets a <see cref="T:System.TimeSpan"/> which can override the TTL of a resource record in case the
            TTL of the record is lower than this minimum value.
            Default is <c>Null</c>.
            <para>
            This is useful in cases where the server returns records with zero TTL.
            </para>
            </summary>
            <remarks>
            This setting gets ignored in case <see cref="P:DnsClient.DnsQueryOptions.UseCache"/> is set to <c>False</c>.
            The maximum value is 24 days or <see cref="F:System.Threading.Timeout.Infinite"/>.
            </remarks>
        </member>
        <member name="P:DnsClient.LookupClientSettings.MaximumCacheTimeout">
            <summary>
            Gets a <see cref="T:System.TimeSpan"/> which can override the TTL of a resource record in case the
            TTL of the record is higher than this maximum value.
            Default is <c>Null</c>.
            </summary>
            <remarks>
            This setting gets ignored in case <see cref="P:DnsClient.DnsQueryOptions.UseCache"/> is set to <c>False</c>.
            The maximum value is 24 days.
            Setting it to <see cref="F:System.Threading.Timeout.Infinite"/> would be equal to not providing a value.
            </remarks>
        </member>
        <member name="M:DnsClient.LookupClientSettings.Equals(System.Object)">
            <inheritdocs />
        </member>
        <member name="M:DnsClient.LookupClientSettings.Equals(DnsClient.LookupClientSettings)">
            <inheritdocs />
        </member>
        <member name="T:DnsClient.DnsQueryResponse">
            <summary>
            The response returned by any query performed by <see cref="T:DnsClient.IDnsQuery"/> with all answer sections, header and message information.
            </summary>
            <seealso cref="T:DnsClient.IDnsQuery"/>
            <seealso cref="T:DnsClient.ILookupClient"/>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.NameServer">
            <summary>
            Gets the name server which responded with this result.
            </summary>
            <value>
            The name server.
            </value>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.Additionals">
            <summary>
            Gets a list of additional records.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.AllRecords">
            <summary>
            Gets a list of all answers, additional and authority records.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.AuditTrail">
            <summary>
            Gets the audit trail if <see cref="P:DnsClient.DnsQueryOptions.EnableAuditTrail"/>. as set to <c>true</c>, <c>null</c> otherwise.
            </summary>
            <value>
            The audit trail.
            </value>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.Answers">
            <summary>
            Gets a list of answer records.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.Authorities">
            <summary>
            Gets a list of authority records.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.ErrorMessage">
            <summary>
            Returns a string value representing the error response code in case an error occurred,
            otherwise '<see cref="F:DnsClient.DnsResponseCode.NoError"/>'.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.HasError">
            <summary>
            A flag indicating if the header contains a response code other than <see cref="F:DnsClient.DnsResponseCode.NoError"/>.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.Header">
            <summary>
            Gets the header of the response.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.Questions">
            <summary>
            Gets the list of questions.
            </summary>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.MessageSize">
            <summary>
            Gets the size of the message.
            </summary>
            <value>
            The size of the message.
            </value>
        </member>
        <member name="P:DnsClient.DnsQueryResponse.Settings">
            <summary>
            Gets the settings used to produce this response.
            </summary>
        </member>
        <member name="M:DnsClient.DnsQueryResponse.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:DnsClient.DnsQueryResponse.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:DnsClient.DnsQuestion">
            <summary>
            The <see cref="T:DnsClient.DnsQuestion"/> class transports information of the lookup query performed by <see cref="T:DnsClient.IDnsQuery"/>.
            <para>
            A list of questions is returned by <see cref="T:DnsClient.IDnsQueryResponse"/> (although, the list will always contain only one <see cref="T:DnsClient.DnsQuestion"/>).
            </para>
            </summary>
        </member>
        <member name="P:DnsClient.DnsQuestion.QueryName">
            <summary>
            Gets the domain name the lookup was running for.
            </summary>
            <value>
            The name of the query.
            </value>
        </member>
        <member name="P:DnsClient.DnsQuestion.QuestionClass">
            <summary>
            Gets the question class.
            </summary>
            <value>
            The question class.
            </value>
        </member>
        <member name="P:DnsClient.DnsQuestion.QuestionType">
            <summary>
            Gets the type of the question.
            </summary>
            <value>
            The type of the question.
            </value>
        </member>
        <member name="M:DnsClient.DnsQuestion.#ctor(System.String,DnsClient.QueryType,DnsClient.QueryClass)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.DnsQuestion"/> class.
            </summary>
            <param name="query">The query.</param>
            <param name="questionType">Type of the question.</param>
            <param name="questionClass">The question class.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="query"/> is null.</exception>
        </member>
        <member name="M:DnsClient.DnsQuestion.#ctor(DnsClient.DnsString,DnsClient.QueryType,DnsClient.QueryClass)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.DnsQuestion"/> class.
            </summary>
            <param name="query">The query.</param>
            <param name="questionType">Type of the question.</param>
            <param name="questionClass">The question class.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="query"/> is null.</exception>
        </member>
        <member name="M:DnsClient.DnsQuestion.ToString">
            <inheritdoc />
        </member>
        <member name="M:DnsClient.DnsQuestion.ToString(System.Int32)">
            <summary>
            Returns the information of this instance in a friendly format with an optional <paramref name="offset"/>.
            </summary>
            <param name="offset">The optional offset which can be used for pretty printing.</param>
            <returns>The string representation of this instance.</returns>
        </member>
        <member name="T:DnsClient.DnsRequestMessage">
            <summary>
            Represents a simple request message which can be send through <see cref="T:DnsClient.DnsMessageHandler"/>.
            </summary>
        </member>
        <member name="T:DnsClient.DnsHeaderResponseCode">
            <summary>
            4 bit response codes of the <see cref="T:DnsClient.IDnsQueryResponse"/>'s header.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc6895#section-2.3">RFC 6895</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.NoError">
            <summary>
            No error condition
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.FormatError">
            <summary>
            Format error. The name server was unable to interpret the query.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.ServerFailure">
            <summary>
            Server failure. The name server was unable to process this query due to a problem with the name server.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.NotExistentDomain">
            <summary>
            Name Error. Meaningful only for responses from an authoritative name server,
            this code signifies that the domain name referenced in the query does not exist.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.NotImplemented">
            <summary>
            Not Implemented. The name server does not support the requested kind of query.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.Refused">
            <summary>
            Refused. The name server refuses to perform the specified operation for policy reasons.
            For example, a name server may not wish to provide the information to the particular requester,
            or a name server may not wish to perform a particular operation (e.g., zone transfer) for particular data.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.ExistingDomain">
            <summary>
            Name Exists when it should not.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2136">RFC 2136</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.ExistingResourceRecordSet">
            <summary>
            Resource record set exists when it should not.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2136">RFC 2136</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.MissingResourceRecordSet">
            <summary>
            Resource record set that should exist but does not.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2136">RFC 2136</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.NotAuthorized">
            <summary>
            Server Not Authoritative for zone / Not Authorized.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2136">RFC 2136</seealso>
            <seealso href="https://tools.ietf.org/html/rfc2845">RFC 2845</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.NotZone">
            <summary>
            Name not contained in zone.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2136">RFC 2136</seealso>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.Unassinged11">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.Unassinged12">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.Unassinged13">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.Unassinged14">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsHeaderResponseCode.Unassinged15">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="T:DnsClient.DnsResponseCode">
            <summary>
            Extended response codes of the <see cref="T:DnsClient.IDnsQueryResponse"/> with OPT.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc6895#section-2.3">RFC 6895</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.NoError">
            <summary>
            No error condition
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.FormatError">
            <summary>
            Format error. The name server was unable to interpret the query.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.ServerFailure">
            <summary>
            Server failure. The name server was unable to process this query due to a problem with the name server.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.NotExistentDomain">
            <summary>
            Name Error. Meaningful only for responses from an authoritative name server,
            this code signifies that the domain name referenced in the query does not exist.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.NotImplemented">
            <summary>
            Not Implemented. The name server does not support the requested kind of query.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.Refused">
            <summary>
            Refused. The name server refuses to perform the specified operation for policy reasons.
            For example, a name server may not wish to provide the information to the particular requester,
            or a name server may not wish to perform a particular operation (e.g., zone transfer) for particular data.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.ExistingDomain">
            <summary>
            Name Exists when it should not.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2136">RFC 2136</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.ExistingResourceRecordSet">
            <summary>
            Resource record set exists when it should not.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2136">RFC 2136</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.MissingResourceRecordSet">
            <summary>
            Resource record set that should exist but does not.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2136">RFC 2136</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.NotAuthorized">
            <summary>
            Server Not Authoritative for zone / Not Authorized.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2136">RFC 2136</seealso>
            <seealso href="https://tools.ietf.org/html/rfc2845">RFC 2845</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.NotZone">
            <summary>
            Name not contained in zone.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2136">RFC 2136</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.Unassinged11">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsResponseCode.Unassinged12">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsResponseCode.Unassinged13">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsResponseCode.Unassinged14">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsResponseCode.Unassinged15">
            <summary>
            Unassigned value
            </summary>
        </member>
        <member name="F:DnsClient.DnsResponseCode.BadVersionOrBadSignature">
            <summary>
            Bad OPT Version or TSIG Signature Failure.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2671">RFC 2671</seealso>
            <seealso href="https://tools.ietf.org/html/rfc2845">RFC 2845</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.BadKey">
            <summary>
            Key not recognized.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2845">RFC 2845</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.BadTime">
            <summary>
            Signature out of time window.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2845">RFC 2845</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.BadMode">
            <summary>
            Bad TKEY Mode.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2930">RFC 2930</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.BadName">
            <summary>
            Duplicate key name.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2930">RFC 2930</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.BadAlgorithm">
            <summary>
            Algorithm not supported.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2930">RFC 2930</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.BadTruncation">
            <summary>
            Bad Truncation.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4635">RFC 4635</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.BadCookie">
            <summary>
            Bad/missing Server Cookie
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc7873">RFC 7873</seealso>
        </member>
        <member name="F:DnsClient.DnsResponseCode.Unassigned">
            <summary>
            Unknown error.
            </summary>
        </member>
        <member name="F:DnsClient.DnsResponseCode.ConnectionTimeout">
            <summary>
            Indicates a timeout error. Connection to the remote server couldn't be established.
            </summary>
        </member>
        <member name="T:DnsClient.DnsResponseException">
            <summary>
            A DnsClient specific exception transporting additional information about the query causing this exception.
            </summary>
            <seealso cref="T:System.Exception" />
        </member>
        <member name="P:DnsClient.DnsResponseException.Code">
            <summary>
            Gets the response code.
            </summary>
            <value>
            The response code.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseException.AuditTrail">
            <summary>
            Gets the audit trail if <see cref="P:DnsClient.DnsQueryOptions.EnableAuditTrail"/>. as set to <c>true</c>, <c>null</c> otherwise.
            </summary>
            <value>
            The audit trail.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseException.DnsError">
            <summary>
            Gets a human readable error message.
            </summary>
            <value>
            The error message.
            </value>
        </member>
        <member name="M:DnsClient.DnsResponseException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.DnsResponseException"/> class
            with <see cref="P:DnsClient.DnsResponseException.Code"/> set to <see cref="F:DnsClient.DnsResponseCode.Unassigned"/>.
            </summary>
        </member>
        <member name="M:DnsClient.DnsResponseException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.DnsResponseException"/> class
            with <see cref="P:DnsClient.DnsResponseException.Code"/> set to <see cref="F:DnsClient.DnsResponseCode.Unassigned"/>
            and a custom <paramref name="message"/>.
            </summary>
        </member>
        <member name="M:DnsClient.DnsResponseException.#ctor(DnsClient.DnsResponseCode)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.DnsResponseException"/> class
            with the standard error text for the given <paramref name="code"/>.
            </summary>
        </member>
        <member name="M:DnsClient.DnsResponseException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.DnsResponseException"/> class
            with <see cref="P:DnsClient.DnsResponseException.Code"/> set to <see cref="F:DnsClient.DnsResponseCode.Unassigned"/>
            and a custom <paramref name="message"/> and inner <see cref="T:System.Exception"/>.
            </summary>
        </member>
        <member name="M:DnsClient.DnsResponseException.#ctor(DnsClient.DnsResponseCode,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.DnsResponseException"/> class
            with a custom <paramref name="message"/> and the given <paramref name="code"/>.
            </summary>
        </member>
        <member name="M:DnsClient.DnsResponseException.#ctor(DnsClient.DnsResponseCode,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.DnsResponseException"/> class
            with a custom <paramref name="message"/> and the given <paramref name="code"/>.
            </summary>
        </member>
        <member name="T:DnsClient.DnsResponseHeader">
            <summary>
            The header section of a <see cref="T:DnsClient.IDnsQueryResponse"/>.
            </summary>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.AdditionalCount">
            <summary>
            Gets the number of additional records in the <see cref="T:DnsClient.IDnsQueryResponse"/>.
            </summary>
            <value>
            The number of additional records.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.AnswerCount">
            <summary>
            Gets the number of answer records in the <see cref="T:DnsClient.IDnsQueryResponse"/>.
            </summary>
            <value>
            The number of answer records.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.FutureUse">
            <summary>
            Gets a value indicating whether the future use flag is set.
            </summary>
            <value>
              The future use flag.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.HasAuthorityAnswer">
            <summary>
            Gets a value indicating whether this instance has authority answers.
            </summary>
            <value>
              <c>true</c> if this instance has authority answers; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.Id">
            <summary>
            Gets the identifier.
            </summary>
            <value>
            The identifier.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.IsAuthenticData">
            <summary>
            Gets a value indicating whether the result is authentic data.
            </summary>
            <value>
              <c>true</c> if the result is authentic; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.IsCheckingDisabled">
            <summary>
            Gets a value indicating whether checking is disabled.
            </summary>
            <value>
              <c>true</c> if checking is disabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.HasQuery">
            <summary>
            Gets a value indicating whether this instance has a query.
            </summary>
            <value>
              <c>true</c> if this instance has a query; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.NameServerCount">
            <summary>
            Gets the number of name servers.
            </summary>
            <value>
            The number of name servers.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.OPCode">
            <summary>
            Gets the kind of query defined by <see cref="T:DnsClient.DnsOpCode"/>.
            </summary>
            <value>
            The query kind.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.QuestionCount">
            <summary>
            Gets the number of questions of the <see cref="T:DnsClient.IDnsQueryResponse"/>.
            </summary>
            <value>
            The number of questions.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.RecursionAvailable">
            <summary>
            Gets a value indicating whether recursion is available on the DNS server.
            </summary>
            <value>
              <c>true</c> if recursion is available; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.ResponseCode">
            <summary>
            Gets the response code.
            </summary>
            <value>
            The response code.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.ResultTruncated">
            <summary>
            Gets a value indicating whether the result was truncated.
            </summary>
            <value>
              <c>true</c> if the result was truncated; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:DnsClient.DnsResponseHeader.RecursionDesired">
            <summary>
            Gets a value indicating whether recursion desired flag was set by the request.
            </summary>
            <value>
              <c>true</c> if the recursion desired flag was set; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:DnsClient.DnsResponseHeader.#ctor(System.Int32,System.UInt16,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.DnsResponseHeader"/> class.
            </summary>
            <param name="id">The identifier.</param>
            <param name="flags">The flags.</param>
            <param name="questionCount">The question count.</param>
            <param name="answerCount">The answer count.</param>
            <param name="additionalCount">The additional count.</param>
            <param name="serverCount">The server count.</param>
        </member>
        <member name="M:DnsClient.DnsResponseHeader.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents this instance.
            </returns>
        </member>
        <member name="M:DnsClient.DnsResponseMessage.AsQueryResponse(DnsClient.NameServer,DnsClient.DnsQuerySettings)">
            <summary>
            Gets the readonly representation of this message which can be returned.
            </summary>
        </member>
        <member name="T:DnsClient.DnsString">
            <summary>
            The <see cref="T:DnsClient.DnsString"/> type is used to normalize and validate domain names and labels.
            </summary>
        </member>
        <member name="F:DnsClient.DnsString.ACEPrefix">
            <summary>
            The ACE prefix indicates that the domain name label contains not normally supported characters and that the label has been encoded.
            </summary>
        </member>
        <member name="F:DnsClient.DnsString.LabelMaxLength">
            <summary>
            The maximum length in bytes for one label.
            </summary>
        </member>
        <member name="F:DnsClient.DnsString.QueryMaxLength">
            <summary>
            The maximum supported total length in bytes for a domain name. The calculation of the actual
            bytes this <see cref="T:DnsClient.DnsString"/> consumes includes all bytes used for to encode it as octet string.
            </summary>
        </member>
        <member name="F:DnsClient.DnsString.RootLabel">
            <summary>
            The root label ".".
            </summary>
        </member>
        <member name="P:DnsClient.DnsString.Original">
            <summary>
            Gets the original value.
            </summary>
        </member>
        <member name="P:DnsClient.DnsString.Value">
            <summary>
            Gets the validated and eventually modified value.
            </summary>
        </member>
        <member name="P:DnsClient.DnsString.NumberOfLabels">
            <summary>
            Gets the number of labels of this <see cref="T:DnsClient.DnsString"/> or null if not applicable.
            This property is only set if the <see cref="M:DnsClient.DnsString.Parse(System.String)"/> method was used to create this instance.
            </summary>
        </member>
        <member name="M:DnsClient.DnsString.op_Implicit(DnsClient.DnsString)~System.String">
            <summary>
            Performs an implicit conversion from <see cref="T:DnsClient.DnsString"/> to <see cref="T:System.String"/>.
            </summary>
            <param name="name">The name.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:DnsClient.DnsString.op_Addition(DnsClient.DnsString,DnsClient.DnsString)">
             <summary>
            
             </summary>
             <param name="a"></param>
             <param name="b"></param>
             <returns></returns>
        </member>
        <member name="M:DnsClient.DnsString.op_Addition(DnsClient.DnsString,System.String)">
             <summary>
            
             </summary>
             <param name="a"></param>
             <param name="b"></param>
             <returns></returns>
        </member>
        <member name="M:DnsClient.DnsString.ToString">
            <inheritdoc />
        </member>
        <member name="M:DnsClient.DnsString.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:DnsClient.DnsString.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:DnsClient.DnsString.Parse(System.String)">
            <summary>
            Parses the given <paramref name="query"/> and validates all labels.
            </summary>
            <remarks>
            An empty string will be interpreted as root label.
            </remarks>
            <param name="query">A domain name.</param>
            <returns>The <see cref="T:DnsClient.DnsString"/> representing the given <paramref name="query"/>.</returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="query"/> is null.</exception>
        </member>
        <member name="M:DnsClient.DnsString.FromResponseQueryString(System.String)">
            <summary>
            Transforms names with the <see cref="F:DnsClient.DnsString.ACEPrefix"/> to the Unicode variant and adds a trailing '.' at the end if not present.
            The original value will be kept in this instance in case it is needed.
            </summary>
            <remarks>
            The method does not parse the domain name unless it contains a <see cref="F:DnsClient.DnsString.ACEPrefix"/>.
            </remarks>
            <param name="query">The value to check.</param>
            <returns>The <see cref="T:DnsClient.DnsString"/> representation.</returns>
        </member>
        <member name="T:DnsClient.IDnsQuery">
            <summary>
            Generic contract to query DNS endpoints. Implemented by <see cref="T:DnsClient.LookupClient"/>.
            </summary>
        </member>
        <member name="M:DnsClient.IDnsQuery.Query(System.String,DnsClient.QueryType,DnsClient.QueryClass)">
            <summary>
            Performs a DNS lookup for the given <paramref name="query" />, <paramref name="queryType" /> and <paramref name="queryClass" />.
            </summary>
            <param name="query">The domain name query.</param>
            <param name="queryType">The <see cref="T:DnsClient.QueryType" />.</param>
            <param name="queryClass">The <see cref="T:DnsClient.QueryClass"/>.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="query"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.Query(DnsClient.DnsQuestion)">
            <summary>
            Performs a DNS lookup for the given <paramref name="question" />.
            </summary>
            <param name="question">The domain name query.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="question"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.Query(DnsClient.DnsQuestion,DnsClient.DnsQueryAndServerOptions)">
            <summary>
            Performs a DNS lookup for the given <paramref name="question" />.
            </summary>
            <param name="question">The domain name query.</param>
            <param name="queryOptions">Query options to be used instead of <see cref="T:DnsClient.LookupClient"/>'s settings.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="question"/> or <paramref name="queryOptions"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryCache(DnsClient.DnsQuestion)">
            <summary>
            Returns cached results for the given <paramref name="question" /> from the in-memory cache, if available, or <c>Null</c> otherwise.
            </summary>
            <remarks>
            This method will not perform a full lookup if there is nothing found in cache or the cache is disabled!
            </remarks>
            <param name="question">The domain name query.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the cached response headers and lists of resource records.
            If no matching cache entry is found <c>Null</c> is returned.
            </returns>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryCache(System.String,DnsClient.QueryType,DnsClient.QueryClass)">
            <summary>
            Returns cached results for the given <paramref name="query" />, <paramref name="queryType" /> and <paramref name="queryClass" />
            against the in-memory cache, if available, or <c>Null</c> otherwise.
            </summary>
            <remarks>
            This method will not perform a full lookup if there is nothing found in cache or the cache is disabled!
            </remarks>
            <param name="query">The domain name query.</param>
            <param name="queryType">The <see cref="T:DnsClient.QueryType" />.</param>
            <param name="queryClass">The <see cref="T:DnsClient.QueryClass"/>.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the cached response headers and lists of resource records.
            If no matching cache entry is found <c>Null</c> is returned.
            </returns>        
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryAsync(System.String,DnsClient.QueryType,DnsClient.QueryClass,System.Threading.CancellationToken)">
            <summary>
            Performs a DNS lookup for the given <paramref name="query" />, <paramref name="queryType" /> and <paramref name="queryClass" />
            </summary>
            <param name="query">The domain name query.</param>
            <param name="queryType">The <see cref="T:DnsClient.QueryType" />.</param>
            <param name="queryClass">The <see cref="T:DnsClient.QueryClass" />.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="query"/> is null.</exception>
            <exception cref="T:System.OperationCanceledException">If cancellation has been requested for the passed in <paramref name="cancellationToken"/>.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryAsync(DnsClient.DnsQuestion,System.Threading.CancellationToken)">
            <summary>
            Performs a DNS lookup for the given <paramref name="question" />.
            </summary>
            <param name="question">The domain name query.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="question"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryAsync(DnsClient.DnsQuestion,DnsClient.DnsQueryAndServerOptions,System.Threading.CancellationToken)">
            <summary>
            Performs a DNS lookup for the given <paramref name="question" />.
            </summary>
            <param name="question">The domain name query.</param>
            <param name="queryOptions">Query options to be used instead of <see cref="T:DnsClient.LookupClient"/>'s settings.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="question"/> or <paramref name="queryOptions"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryReverse(System.Net.IPAddress)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress"/>.
            </summary>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress"/>.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="ipAddress"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryReverse(System.Net.IPAddress,DnsClient.DnsQueryAndServerOptions)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress"/>.
            </summary>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress"/>.</param>
            <param name="queryOptions">Query options to be used instead of <see cref="T:DnsClient.LookupClient"/>'s settings.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="ipAddress"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryReverseAsync(System.Net.IPAddress,System.Threading.CancellationToken)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress" />.
            </summary>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress" />.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="ipAddress"/> is null.</exception>
            <exception cref="T:System.OperationCanceledException">If cancellation has been requested for the passed in <paramref name="cancellationToken"/>.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryReverseAsync(System.Net.IPAddress,DnsClient.DnsQueryAndServerOptions,System.Threading.CancellationToken)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress" />.
            </summary>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress" />.</param>
            <param name="queryOptions">Query options to be used instead of <see cref="T:DnsClient.LookupClient"/>'s settings.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentNullException">If <paramref name="ipAddress"/> is null.</exception>
            <exception cref="T:System.OperationCanceledException">If cancellation has been requested for the passed in <paramref name="cancellationToken"/>.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServer(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.String,DnsClient.QueryType,DnsClient.QueryClass)">
            <summary>
            Performs a DNS lookup for the given <paramref name="query" />, <paramref name="queryType" /> and <paramref name="queryClass" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <remarks>
            To query specific servers can be useful in cases where you have to use a different DNS server than initially configured
            (without creating a new instance of <see cref="T:DnsClient.ILookupClient"/> for example).
            </remarks>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="query">The domain name query.</param>
            <param name="queryType">The <see cref="T:DnsClient.QueryType" />.</param>
            <param name="queryClass">The <see cref="T:DnsClient.QueryClass" />.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="query"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServer(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},DnsClient.DnsQuestion)">
            <summary>
            Performs a DNS lookup for the given <paramref name="question" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <remarks>
            To query specific servers can be useful in cases where you have to use a different DNS server than initially configured
            (without creating a new instance of <see cref="T:DnsClient.ILookupClient"/> for example).
            </remarks>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="question">The domain name query.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/> or <paramref name="question"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServer(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},DnsClient.DnsQuestion,DnsClient.DnsQueryOptions)">
            <summary>
            Performs a DNS lookup for the given <paramref name="question" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <remarks>
            To query specific servers can be useful in cases where you have to use a different DNS server than initially configured
            (without creating a new instance of <see cref="T:DnsClient.ILookupClient"/> for example).
            </remarks>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="question">The domain name query.</param>
            <param name="queryOptions">Query options to be used instead of <see cref="T:DnsClient.LookupClient"/>'s settings.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/>, <paramref name="question"/> or <paramref name="queryOptions"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServer(System.Collections.Generic.IReadOnlyCollection{System.Net.IPEndPoint},System.String,DnsClient.QueryType,DnsClient.QueryClass)">
            <summary>
            Performs a DNS lookup for the given <paramref name="query" />, <paramref name="queryType" /> and <paramref name="queryClass" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <remarks>
            To query specific servers can be useful in cases where you have to use a different DNS server than initially configured
            (without creating a new instance of <see cref="T:DnsClient.ILookupClient"/> for example).
            </remarks>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="query">The domain name query.</param>
            <param name="queryType">The <see cref="T:DnsClient.QueryType" />.</param>
            <param name="queryClass">The <see cref="T:DnsClient.QueryClass" />.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="query"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServer(System.Collections.Generic.IReadOnlyCollection{System.Net.IPAddress},System.String,DnsClient.QueryType,DnsClient.QueryClass)">
            <summary>
            Performs a DNS lookup for the given <paramref name="query" />, <paramref name="queryType" /> and <paramref name="queryClass" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <remarks>
            To query specific servers can be useful in cases where you have to use a different DNS server than initially configured
            (without creating a new instance of <see cref="T:DnsClient.ILookupClient"/> for example).
            </remarks>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="query">The domain name query.</param>
            <param name="queryType">The <see cref="T:DnsClient.QueryType" />.</param>
            <param name="queryClass">The <see cref="T:DnsClient.QueryClass" />.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="query"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerAsync(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.String,DnsClient.QueryType,DnsClient.QueryClass,System.Threading.CancellationToken)">
            <summary>
            Performs a DNS lookup for the given <paramref name="query" />, <paramref name="queryType" /> and <paramref name="queryClass" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <remarks>
            To query specific servers can be useful in cases where you have to use a different DNS server than initially configured
            (without creating a new instance of <see cref="T:DnsClient.ILookupClient"/> for example).
            </remarks>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="query">The domain name query.</param>
            <param name="queryType">The <see cref="T:DnsClient.QueryType" />.</param>
            <param name="queryClass">The <see cref="T:DnsClient.QueryClass" />.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="query"/> is null.</exception>
            <exception cref="T:System.OperationCanceledException">If cancellation has been requested for the passed in <paramref name="cancellationToken"/>.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerAsync(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},DnsClient.DnsQuestion,System.Threading.CancellationToken)">
            <summary>
            Performs a DNS lookup for the given <paramref name="question" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <remarks>
            To query specific servers can be useful in cases where you have to use a different DNS server than initially configured
            (without creating a new instance of <see cref="T:DnsClient.ILookupClient"/> for example).
            </remarks>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="question">The domain name query.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/> or <paramref name="question"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerAsync(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},DnsClient.DnsQuestion,DnsClient.DnsQueryOptions,System.Threading.CancellationToken)">
            <summary>
            Performs a DNS lookup for the given <paramref name="question" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <remarks>
            To query specific servers can be useful in cases where you have to use a different DNS server than initially configured
            (without creating a new instance of <see cref="T:DnsClient.ILookupClient"/> for example).
            </remarks>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="question">The domain name query.</param>
            <param name="queryOptions">Query options to be used instead of <see cref="T:DnsClient.LookupClient"/>'s settings.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/>, <paramref name="question"/> or <paramref name="queryOptions"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerAsync(System.Collections.Generic.IReadOnlyCollection{System.Net.IPAddress},System.String,DnsClient.QueryType,DnsClient.QueryClass,System.Threading.CancellationToken)">
            <summary>
            Performs a DNS lookup for the given <paramref name="query" />, <paramref name="queryType" /> and <paramref name="queryClass" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <remarks>
            To query specific servers can be useful in cases where you have to use a different DNS server than initially configured
            (without creating a new instance of <see cref="T:DnsClient.ILookupClient"/> for example).
            </remarks>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="query">The domain name query.</param>
            <param name="queryType">The <see cref="T:DnsClient.QueryType" />.</param>
            <param name="queryClass">The <see cref="T:DnsClient.QueryClass" />.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="query"/> is null.</exception>
            <exception cref="T:System.OperationCanceledException">If cancellation has been requested for the passed in <paramref name="cancellationToken"/>.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerAsync(System.Collections.Generic.IReadOnlyCollection{System.Net.IPEndPoint},System.String,DnsClient.QueryType,DnsClient.QueryClass,System.Threading.CancellationToken)">
            <summary>
            Performs a DNS lookup for the given <paramref name="query" />, <paramref name="queryType" /> and <paramref name="queryClass" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <remarks>
            To query specific servers can be useful in cases where you have to use a different DNS server than initially configured
            (without creating a new instance of <see cref="T:DnsClient.ILookupClient"/> for example).
            </remarks>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="query">The domain name query.</param>
            <param name="queryType">The <see cref="T:DnsClient.QueryType" />.</param>
            <param name="queryClass">The <see cref="T:DnsClient.QueryClass" />.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which contains the response headers and lists of resource records.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="query"/> is null.</exception>
            <exception cref="T:System.OperationCanceledException">If cancellation has been requested for the passed in <paramref name="cancellationToken"/>.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerReverse(System.Collections.Generic.IReadOnlyCollection{System.Net.IPAddress},System.Net.IPAddress)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress" />.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/> or <paramref name="ipAddress"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerReverse(System.Collections.Generic.IReadOnlyCollection{System.Net.IPEndPoint},System.Net.IPAddress)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress" />.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/> or <paramref name="ipAddress"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerReverse(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.Net.IPAddress)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress" />.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/> or <paramref name="ipAddress"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerReverse(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.Net.IPAddress,DnsClient.DnsQueryOptions)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress" />.</param>
            <param name="queryOptions">Query options to be used instead of <see cref="T:DnsClient.LookupClient"/>'s settings.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/>, <paramref name="ipAddress"/> or <paramref name="queryOptions"/> is null.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerReverseAsync(System.Collections.Generic.IReadOnlyCollection{System.Net.IPAddress},System.Net.IPAddress,System.Threading.CancellationToken)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress" />.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/> or <paramref name="ipAddress"/> is null.</exception>
            <exception cref="T:System.OperationCanceledException">If cancellation has been requested for the passed in <paramref name="cancellationToken"/>.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerReverseAsync(System.Collections.Generic.IReadOnlyCollection{System.Net.IPEndPoint},System.Net.IPAddress,System.Threading.CancellationToken)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress" />.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/> or <paramref name="ipAddress"/> is null.</exception>
            <exception cref="T:System.OperationCanceledException">If cancellation has been requested for the passed in <paramref name="cancellationToken"/>.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerReverseAsync(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.Net.IPAddress,System.Threading.CancellationToken)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress" />.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/> or <paramref name="ipAddress"/> is null.</exception>
            <exception cref="T:System.OperationCanceledException">If cancellation has been requested for the passed in <paramref name="cancellationToken"/>.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="M:DnsClient.IDnsQuery.QueryServerReverseAsync(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.Net.IPAddress,DnsClient.DnsQueryOptions,System.Threading.CancellationToken)">
            <summary>
            Does a reverse lookup for the <paramref name="ipAddress" />
            using only the passed in <paramref name="servers"/>.
            </summary>
            <param name="servers">The list of one or more server(s) which should be used for the lookup.</param>
            <param name="ipAddress">The <see cref="T:System.Net.IPAddress" />.</param>
            <param name="queryOptions">Query options to be used instead of <see cref="T:DnsClient.LookupClient"/>'s settings.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>
            The <see cref="T:DnsClient.IDnsQueryResponse" /> which might contain the <see cref="T:DnsClient.Protocol.PtrRecord" /> for the <paramref name="ipAddress"/>.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If the <paramref name="servers"/> collection doesn't contain any elements.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="servers"/>, <paramref name="ipAddress"/> or <paramref name="queryOptions"/> is null.</exception>
            <exception cref="T:System.OperationCanceledException">If cancellation has been requested for the passed in <paramref name="cancellationToken"/>.</exception>
            <exception cref="T:DnsClient.DnsResponseException">After retries and fallbacks, if none of the servers were accessible, timed out or (if <see cref="P:DnsClient.DnsQueryOptions.ThrowDnsErrors"/> is enabled) returned error results.</exception>
        </member>
        <member name="T:DnsClient.IDnsQueryResponse">
            <summary>
            Contract defining the result of a query performed by <see cref="T:DnsClient.IDnsQuery"/>.
            </summary>
            <seealso cref="T:DnsClient.IDnsQuery"/>
            <seealso cref="T:DnsClient.ILookupClient"/>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.Questions">
            <summary>
            Gets the list of questions.
            </summary>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.Additionals">
            <summary>
            Gets a list of additional records.
            </summary>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.AllRecords">
            <summary>
            Gets a list of all answers, additional and authority records.
            </summary>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.Answers">
            <summary>
            Gets a list of answer records.
            </summary>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.Authorities">
            <summary>
            Gets a list of authority records.
            </summary>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.AuditTrail">
            <summary>
            Gets the audit trail if <see cref="P:DnsClient.DnsQueryOptions.EnableAuditTrail"/>. as set to <c>true</c>, <c>null</c> otherwise.
            </summary>
            <value>
            The audit trail.
            </value>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.ErrorMessage">
            <summary>
            Returns a string value representing the error response code in case an error occurred,
            otherwise '<see cref="F:DnsClient.DnsResponseCode.NoError"/>'.
            </summary>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.HasError">
            <summary>
            A flag indicating if the header contains a response code other than <see cref="F:DnsClient.DnsResponseCode.NoError"/>.
            </summary>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.Header">
            <summary>
            Gets the header of the response.
            </summary>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.MessageSize">
            <summary>
            Gets the size of the message.
            </summary>
            <value>
            The size of the message.
            </value>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.NameServer">
            <summary>
            Gets the name server which responded with this result.
            </summary>
            <value>
            The name server.
            </value>
        </member>
        <member name="P:DnsClient.IDnsQueryResponse.Settings">
            <summary>
            Gets the settings used to produce this response.
            </summary>
        </member>
        <member name="T:DnsClient.ILookupClient">
            <summary>
            The contract for the LookupClient.
            <para>
            The interfaces for the query methods and the lookup client properties are separated so that one can
            inject or expose only the <see cref="T:DnsClient.IDnsQuery"/> without exposing the configuration options.
            </para>
            </summary>
        </member>
        <member name="P:DnsClient.ILookupClient.NameServers">
            <summary>
            Gets the list of configured or resolved name servers of the <see cref="T:DnsClient.ILookupClient"/> instance.
            </summary>
        </member>
        <member name="P:DnsClient.ILookupClient.Settings">
            <summary>
            Gets the configured settings of the <see cref="T:DnsClient.ILookupClient"/> instance.
            </summary>
        </member>
        <member name="T:DnsClient.Internal.Base32Hex">
             <summary>
             Base32 encoder with the extended hey alphabet
             </summary>
             <remarks>
             See https://datatracker.ietf.org/doc/html/rfc4648#section-7
             <![CDATA[
                          Table 4: The "Extended Hex" Base 32 Alphabet
            
                 Value Encoding  Value Encoding  Value Encoding  Value Encoding
                     0 0             9 9            18 I            27 R
                     1 1            10 A            19 J            28 S
                     2 2            11 B            20 K            29 T
                     3 3            12 C            21 L            30 U
                     4 4            13 D            22 M            31 V
                     5 5            14 E            23 N
                     6 6            15 F            24 O         (pad) =
                     7 7            16 G            25 P
                     8 8            17 H            26 Q
            
             ]]>
             </remarks>
             <seealso href="https://datatracker.ietf.org/doc/html/rfc4648#section-7">RFC4648</seealso>
        </member>
        <member name="M:DnsClient.Internal.Base32Hex.FromBase32HexString(System.String)">
            <summary>
            Converts the specified string, which encodes binary data as base-32 digits
            using the extended hex alphabet, to an equivalent 8-bit unsigned integer array.
            </summary>
            <param name="input">The string to convert.</param>
            <returns>An array of 8-bit unsigned integers that is equivalent to <paramref name="input"/>.</returns>
        </member>
        <member name="M:DnsClient.Internal.Base32Hex.ToBase32HexString(System.Byte[])">
            <summary>
            Converts an array of 8-bit unsigned integers to its equivalent string
            representation that is encoded with base-32 digits using the extended hex alphabet.
            </summary>
            <param name="input">An array of 8-bit unsigned integers.</param>
            <returns>The string representation in base 32 hex of <paramref name="input"/>.</returns>
        </member>
        <member name="M:DnsClient.Windows.NameResolutionPolicy.Resolve(System.Boolean,System.Boolean)">
            <summary>
            Resolve all names from the Name Resolution policy in Windows.
            </summary>
            <returns>Returns a list of name servers</returns>
        </member>
        <member name="T:DnsClient.LookupClient">
             <summary>
             The <see cref="T:DnsClient.LookupClient"/> is the main query class of this library and should be used for any kind of DNS lookup query.
             <para>
             It implements <see cref="T:DnsClient.ILookupClient"/> and <see cref="T:DnsClient.IDnsQuery"/> which contains a number of extension methods, too.
             The extension methods internally all invoke the standard <see cref="T:DnsClient.IDnsQuery"/> queries though.
             </para>
             </summary>
             <seealso cref="T:DnsClient.IDnsQuery"/>
             <seealso cref="T:DnsClient.ILookupClient"/>
             <example>
             A basic example without specifying any DNS server, which will use the DNS server configured by your local network.
             <code>
             <![CDATA[
             var client = new LookupClient();
             var result = client.Query("google.com", QueryType.A);
            
             foreach (var aRecord in result.Answers.ARecords())
             {
                 Console.WriteLine(aRecord);
             }
             ]]>
             </code>
             </example>
        </member>
        <member name="P:DnsClient.LookupClient.NameServers">
            <inheritdoc/>
        </member>
        <member name="P:DnsClient.LookupClient.Settings">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.#ctor">
             <summary>
             Creates a new instance of <see cref="T:DnsClient.LookupClient"/> without specifying any name server.
             This will implicitly use the name server(s) configured by the local network adapter(s).
             </summary>
             <remarks>
             This uses <see cref="M:DnsClient.NameServer.ResolveNameServers(System.Boolean,System.Boolean)"/>.
             The resulting list of name servers is highly dependent on the local network configuration and OS.
             </remarks>
             <example>
             In the following example, we will create a new <see cref="T:DnsClient.LookupClient"/> without explicitly defining any DNS server.
             This will use the DNS server configured by your local network.
             <code>
             <![CDATA[
             var client = new LookupClient();
             var result = client.Query("google.com", QueryType.A);
            
             foreach (var aRecord in result.Answers.ARecords())
             {
                 Console.WriteLine(aRecord);
             }
             ]]>
             </code>
             </example>
        </member>
        <member name="M:DnsClient.LookupClient.#ctor(System.Net.IPAddress[])">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.LookupClient"/> with default settings and one or more DNS servers identified by their <see cref="T:System.Net.IPAddress"/>.
            The default port <c>53</c> will be used for all <see cref="T:System.Net.IPAddress"/>s provided.
            </summary>
            <param name="nameServers">The <see cref="T:System.Net.IPAddress"/>(s) to be used by this <see cref="T:DnsClient.LookupClient"/> instance.</param>
            <example>
            Connecting to one or more DNS server using the default port:
            <code>
            <![CDATA[
            // configuring the client to use google's public IPv4 DNS servers.
            var client = new LookupClient(IPAddress.Parse("*******"), IPAddress.Parse("*******"));
            ]]>
            </code>
            </example>
            <exception cref="T:System.ArgumentNullException">If <paramref name="nameServers"/>is <c>null</c>.</exception>
        </member>
        <member name="M:DnsClient.LookupClient.#ctor(System.Net.IPAddress,System.Int32)">
            <summary>
            Create a new instance of <see cref="T:DnsClient.LookupClient"/> with default settings and one DNS server defined by <paramref name="address"/> and <paramref name="port"/>.
            </summary>
            <param name="address">The <see cref="T:System.Net.IPAddress"/> of the DNS server.</param>
            <param name="port">The port of the DNS server.</param>
            <example>
            Connecting to one specific DNS server which does not run on the default port <c>53</c>:
            <code>
            <![CDATA[
            var client = new LookupClient(IPAddress.Parse("127.0.0.1"), 8600);
            ]]>
            </code>
            </example>
            <exception cref="T:System.ArgumentNullException">If <paramref name="address"/>is <c>null</c>.</exception>
        </member>
        <member name="M:DnsClient.LookupClient.#ctor(System.Net.IPEndPoint[])">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.LookupClient"/> with default settings and the given name servers.
            </summary>
            <param name="nameServers">The <see cref="T:System.Net.IPEndPoint"/>(s) to be used by this <see cref="T:DnsClient.LookupClient"/> instance.</param>
            <example>
            Connecting to one specific DNS server which does not run on the default port <c>53</c>:
            <code>
            <![CDATA[
            var endpoint = new IPEndPoint(IPAddress.Parse("127.0.0.1"), 8600);
            var client = new LookupClient(endpoint);
            ]]>
            </code>
            <para>
            The <see cref="T:DnsClient.NameServer"/> class also contains predefined <see cref="T:System.Net.IPEndPoint"/>s for the public Google DNS servers, which can be used as follows:
            <code>
            <![CDATA[
            var client = new LookupClient(NameServer.GooglePublicDns, NameServer.GooglePublicDnsIPv6);
            ]]>
            </code>
            </para>
            </example>
            <exception cref="T:System.ArgumentNullException">If <paramref name="nameServers"/>is <c>null</c>.</exception>
        </member>
        <member name="M:DnsClient.LookupClient.#ctor(DnsClient.NameServer[])">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.LookupClient"/> with default settings and the given name servers.
            </summary>
            <param name="nameServers">The <see cref="T:DnsClient.NameServer"/>(s) to be used by this <see cref="T:DnsClient.LookupClient"/> instance.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="nameServers"/>is <c>null</c>.</exception>
        </member>
        <member name="M:DnsClient.LookupClient.#ctor(DnsClient.LookupClientOptions)">
            <summary>
            Creates a new instance of <see cref="T:DnsClient.LookupClient"/> with custom settings.
            </summary>
            <param name="options">The options to use with this <see cref="T:DnsClient.LookupClient"/> instance.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="options"/>is <c>null</c>.</exception>
        </member>
        <member name="M:DnsClient.LookupClient.QueryReverse(System.Net.IPAddress)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryReverse(System.Net.IPAddress,DnsClient.DnsQueryAndServerOptions)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryReverseAsync(System.Net.IPAddress,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryReverseAsync(System.Net.IPAddress,DnsClient.DnsQueryAndServerOptions,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.Query(System.String,DnsClient.QueryType,DnsClient.QueryClass)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.Query(DnsClient.DnsQuestion)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.Query(DnsClient.DnsQuestion,DnsClient.DnsQueryAndServerOptions)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryCache(System.String,DnsClient.QueryType,DnsClient.QueryClass)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryCache(DnsClient.DnsQuestion)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryAsync(System.String,DnsClient.QueryType,DnsClient.QueryClass,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryAsync(DnsClient.DnsQuestion,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryAsync(DnsClient.DnsQuestion,DnsClient.DnsQueryAndServerOptions,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServer(System.Collections.Generic.IReadOnlyCollection{System.Net.IPAddress},System.String,DnsClient.QueryType,DnsClient.QueryClass)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServer(System.Collections.Generic.IReadOnlyCollection{System.Net.IPEndPoint},System.String,DnsClient.QueryType,DnsClient.QueryClass)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServer(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.String,DnsClient.QueryType,DnsClient.QueryClass)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServer(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},DnsClient.DnsQuestion)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServer(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},DnsClient.DnsQuestion,DnsClient.DnsQueryOptions)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerAsync(System.Collections.Generic.IReadOnlyCollection{System.Net.IPAddress},System.String,DnsClient.QueryType,DnsClient.QueryClass,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerAsync(System.Collections.Generic.IReadOnlyCollection{System.Net.IPEndPoint},System.String,DnsClient.QueryType,DnsClient.QueryClass,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerAsync(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.String,DnsClient.QueryType,DnsClient.QueryClass,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerAsync(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},DnsClient.DnsQuestion,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerAsync(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},DnsClient.DnsQuestion,DnsClient.DnsQueryOptions,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerReverse(System.Collections.Generic.IReadOnlyCollection{System.Net.IPAddress},System.Net.IPAddress)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerReverse(System.Collections.Generic.IReadOnlyCollection{System.Net.IPEndPoint},System.Net.IPAddress)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerReverse(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.Net.IPAddress)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerReverse(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.Net.IPAddress,DnsClient.DnsQueryOptions)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerReverseAsync(System.Collections.Generic.IReadOnlyCollection{System.Net.IPAddress},System.Net.IPAddress,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerReverseAsync(System.Collections.Generic.IReadOnlyCollection{System.Net.IPEndPoint},System.Net.IPAddress,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerReverseAsync(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.Net.IPAddress,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.QueryServerReverseAsync(System.Collections.Generic.IReadOnlyCollection{DnsClient.NameServer},System.Net.IPAddress,DnsClient.DnsQueryOptions,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:DnsClient.LookupClient.GetReverseQuestion(System.Net.IPAddress)">
            <summary>
            Gets a reverse lookup question for an <see cref="T:System.Net.IPAddress"/>.
            </summary>
            <param name="ipAddress">The address.</param>
            <returns>A <see cref="T:DnsClient.DnsQuestion"/> with the proper arpa domain query for the given address.</returns>
        </member>
        <member name="T:DnsClient.NameServer">
            <summary>
            Represents a name server instance used by <see cref="T:DnsClient.ILookupClient"/>.
            Also, comes with some static methods to resolve name servers from the local network configuration.
            </summary>
        </member>
        <member name="F:DnsClient.NameServer.DefaultPort">
            <summary>
            The default DNS server port.
            </summary>
        </member>
        <member name="F:DnsClient.NameServer.GooglePublicDns">
            <summary>
            The public Google DNS IPv4 endpoint.
            </summary>
        </member>
        <member name="F:DnsClient.NameServer.GooglePublicDns2">
            <summary>
            The second public Google DNS IPv6 endpoint.
            </summary>
        </member>
        <member name="F:DnsClient.NameServer.GooglePublicDnsIPv6">
            <summary>
            The public Google DNS IPv6 endpoint.
            </summary>
        </member>
        <member name="F:DnsClient.NameServer.GooglePublicDns2IPv6">
            <summary>
            The second public Google DNS IPv6 endpoint.
            </summary>
        </member>
        <member name="F:DnsClient.NameServer.Cloudflare">
            <summary>
            A public Cloudflare DNS endpoint.
            </summary>
        </member>
        <member name="F:DnsClient.NameServer.Cloudflare2">
            <summary>
            A public Cloudflare DNS endpoint.
            </summary>
        </member>
        <member name="F:DnsClient.NameServer.CloudflareIPv6">
            <summary>
            A public Cloudflare DNS IPv6 endpoint.
            </summary>
        </member>
        <member name="F:DnsClient.NameServer.Cloudflare2IPv6">
            <summary>
            A public Cloudflare DNS IPv6 endpoint.
            </summary>
        </member>
        <member name="M:DnsClient.NameServer.#ctor(System.Net.IPAddress)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.NameServer"/> class.
            </summary>
            <param name="endPoint">The name server endpoint.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="endPoint"/>is <c>null</c>.</exception>
        </member>
        <member name="M:DnsClient.NameServer.#ctor(System.Net.IPAddress,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.NameServer"/> class.
            </summary>
            <param name="endPoint">The name server endpoint.</param>
            <param name="port">The name server port.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="endPoint"/>is <c>null</c>.</exception>
        </member>
        <member name="M:DnsClient.NameServer.#ctor(System.Net.IPEndPoint)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.NameServer"/> class.
            </summary>
            <param name="endPoint">The name server endpoint.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="endPoint"/>is <c>null</c>.</exception>
        </member>
        <member name="M:DnsClient.NameServer.#ctor(System.Net.IPAddress,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.NameServer"/> class.
            </summary>
            <param name="endPoint">The name server endpoint.</param>
            <param name="dnsSuffix">An optional DNS suffix (can be null).</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="endPoint"/>is <c>null</c>.</exception>
        </member>
        <member name="M:DnsClient.NameServer.#ctor(System.Net.IPAddress,System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.NameServer"/> class.
            </summary>
            <param name="endPoint">The name server endpoint.</param>
            <param name="port">The name server port.</param>
            <param name="dnsSuffix">An optional DNS suffix (can be null).</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="endPoint"/>is <c>null</c>.</exception>
        </member>
        <member name="M:DnsClient.NameServer.#ctor(System.Net.IPEndPoint,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.NameServer"/> class.
            </summary>
            <param name="endPoint">The name server endpoint.</param>
            <param name="dnsSuffix">An optional DNS suffix (can be null).</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="endPoint"/>is <c>null</c>.</exception>
        </member>
        <member name="M:DnsClient.NameServer.op_Implicit(System.Net.IPEndPoint)~DnsClient.NameServer">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.NameServer"/> class from a <see cref="P:DnsClient.NameServer.IPEndPoint"/>.
            </summary>
            <param name="endPoint">The endpoint.</param>
        </member>
        <member name="M:DnsClient.NameServer.op_Implicit(System.Net.IPAddress)~DnsClient.NameServer">
            <summary>
            Initializes a new instance of the <see cref="T:DnsClient.NameServer"/> class from a <see cref="T:System.Net.IPAddress"/>.
            </summary>
            <param name="address">The address.</param>
        </member>
        <member name="P:DnsClient.NameServer.Address">
            <summary>
            Gets the string representation of the configured <see cref="T:System.Net.IPAddress"/>.
            </summary>
        </member>
        <member name="P:DnsClient.NameServer.Port">
            <summary>
            Gets the port.
            </summary>
        </member>
        <member name="P:DnsClient.NameServer.AddressFamily">
            <summary>
            Gets the address family.
            </summary>
        </member>
        <member name="P:DnsClient.NameServer.SupportedUdpPayloadSize">
            <summary>
            Gets the size of the supported UDP payload.
            <para>
            This value might get updated by <see cref="T:DnsClient.ILookupClient"/> by reading the options records returned by a query.
            </para>
            </summary>
            <value>
            The size of the supported UDP payload.
            </value>
        </member>
        <member name="P:DnsClient.NameServer.DnsSuffix">
            <summary>
            Gets an optional DNS suffix which a resolver can use to append to queries or to find servers suitable for a query.
            </summary>
        </member>
        <member name="M:DnsClient.NameServer.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents this instance.
            </returns>
        </member>
        <member name="M:DnsClient.NameServer.Equals(System.Object)">
            <inheritdocs />
        </member>
        <member name="M:DnsClient.NameServer.Equals(DnsClient.NameServer)">
            <inheritdocs />
        </member>
        <member name="M:DnsClient.NameServer.GetHashCode">
            <inheritdocs />
        </member>
        <member name="M:DnsClient.NameServer.ResolveNameServers(System.Boolean,System.Boolean)">
            <summary>
            Gets a list of name servers by iterating over the available network interfaces.
            <para>
            If <paramref name="fallbackToGooglePublicDns" /> is enabled, this method will return the Google public DNS endpoints if no
            local DNS server was found.
            </para>
            </summary>
            <param name="skipIPv6SiteLocal">If set to <c>true</c> local IPv6 sites are skipped.</param>
            <param name="fallbackToGooglePublicDns">If set to <c>true</c> the public Google DNS servers are returned if no other servers could be found.</param>
            <returns>
            The list of name servers.
            </returns>
        </member>
        <member name="M:DnsClient.NameServer.ResolveNameServersNative">
            <summary>
            Using my custom native implementation to support UWP apps and such until <see cref="M:System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces"/>
            gets an implementation in netstandard2.1.
            </summary>
            <remarks>
            DnsClient has been changed in version 1.1.0.
            It will not invoke this when resolving default DNS servers. It is up to the user to decide what to do based on what platform the code is running on.
            </remarks>
            <returns>
            The list of name servers.
            </returns>
        </member>
        <member name="M:DnsClient.NameServer.ResolveNameResolutionPolicyServers">
            <summary>
            On a Windows machine query the Name Resolution Policy table for a list of policy-defined name servers.
            </summary>
            <returns>Returns a collection of name servers from the policy table</returns>
        </member>
        <member name="T:DnsClient.QueryClass">
            <summary>
            CLASS fields appear in resource records.
            </summary>
        </member>
        <member name="F:DnsClient.QueryClass.IN">
            <summary>
            The Internet.
            </summary>
        </member>
        <member name="F:DnsClient.QueryClass.CS">
            <summary>
            The CSNET class (Obsolete - used only for examples in some obsolete RFCs).
            </summary>
        </member>
        <member name="F:DnsClient.QueryClass.CH">
            <summary>
            The CHAOS class.
            </summary>
        </member>
        <member name="F:DnsClient.QueryClass.HS">
            <summary>
            Hesiod [Dyer 87].
            </summary>
        </member>
        <member name="T:DnsClient.QueryType">
            <summary>
            The query type field appear in the question part of a query.
            Query types are a superset of <see cref="T:DnsClient.Protocol.ResourceRecordType"/>.
            </summary>
        </member>
        <member name="F:DnsClient.QueryType.A">
            <summary>
            A host address.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.ARecord"/>
        </member>
        <member name="F:DnsClient.QueryType.NS">
            <summary>
            An authoritative name server.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.11">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.NsRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.MD">
            <summary>
            A mail destination (OBSOLETE - use MX).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.QueryType.MF">
            <summary>
            A mail forwarder (OBSOLETE - use MX).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035">RFC 1035</seealso>
        </member>
        <member name="F:DnsClient.QueryType.CNAME">
            <summary>
            The canonical name for an alias.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.1">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.CNameRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.SOA">
            <summary>
            Marks the start of a zone of authority.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.13">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.SoaRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.MB">
            <summary>
            A mailbox domain name (EXPERIMENTAL).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.3">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.MbRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.MG">
            <summary>
            A mail group member (EXPERIMENTAL).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.6">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.MgRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.MR">
            <summary>
            A mailbox rename domain name (EXPERIMENTAL).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.8">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.MrRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.NULL">
            <summary>
            A Null resource record (EXPERIMENTAL).
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.8">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.NullRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.WKS">
            <summary>
            A well known service description.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc3232">RFC 3232</seealso>
            <seealso cref="T:DnsClient.Protocol.WksRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.PTR">
            <summary>
            A domain name pointer.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.12">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.PtrRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.HINFO">
            <summary>
            Host information.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.11">RFC 1035</seealso>
            <seealso href="https://tools.ietf.org/html/rfc1010">RFC 1010</seealso>
            <seealso cref="T:DnsClient.Protocol.HInfoRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.MINFO">
            <summary>
            Mailbox or mail list information.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.11">RFC 1035</seealso>
            <seealso cref="T:DnsClient.Protocol.MInfoRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.MX">
            <summary>
            Mail exchange.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3.9">RFC 1035</seealso>
            <seealso href="https://tools.ietf.org/html/rfc974">RFC 974</seealso>
            <seealso cref="T:DnsClient.Protocol.MxRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.TXT">
            <summary>
            Text resources.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1035#section-3.3">RFC 1035</seealso>
            <seealso href="https://tools.ietf.org/html/rfc1464">RFC 1464</seealso>
            <seealso cref="T:DnsClient.Protocol.TxtRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.RP">
            <summary>
            Responsible Person.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1183">RFC 1183</seealso>
            <seealso cref="T:DnsClient.Protocol.RpRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.AFSDB">
            <summary>
            AFS Data Base location.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc1183#section-1">RFC 1183</seealso>
            <seealso href="https://tools.ietf.org/html/rfc5864">RFC 5864</seealso>
            <seealso cref="T:DnsClient.Protocol.AfsDbRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.AAAA">
            <summary>
            An IPv6 host address.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc3596#section-2.2">RFC 3596</seealso>
            <seealso cref="T:DnsClient.Protocol.AaaaRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.SRV">
            <summary>
            A resource record which specifies the location of the server(s) for a specific protocol and domain.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2782">RFC 2782</seealso>
            <seealso cref="T:DnsClient.Protocol.SrvRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.NAPTR">
            <summary>
            The Naming Authority Pointer rfc2915
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc2915">RFC 2915</seealso>
            <seealso cref="T:DnsClient.Protocol.NAPtrRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.DS">
            <summary>
            DS rfc4034
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4034#section-5.1">RFC 4034</seealso>
        </member>
        <member name="F:DnsClient.QueryType.RRSIG">
            <summary>
            RRSIG rfc3755.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc3755">RFC 3755</seealso>
        </member>
        <member name="F:DnsClient.QueryType.NSEC">
            <summary>
            NSEC rfc4034.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4034#section-4">RFC 4034</seealso>
        </member>
        <member name="F:DnsClient.QueryType.DNSKEY">
            <summary>
            DNSKEY rfc4034
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4034#section-2">RFC 4034</seealso>
        </member>
        <member name="F:DnsClient.QueryType.NSEC3">
            <summary>
            NSEC3 rfc5155.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc5155">RFC 5155</seealso>
        </member>
        <member name="F:DnsClient.QueryType.NSEC3PARAM">
            <summary>
            NSEC3PARAM rfc5155.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc5155#section-4">RFC 5155</seealso>
        </member>
        <member name="F:DnsClient.QueryType.TLSA">
            <summary>
            TLSA rfc6698
            </summary>
            <seealso href="https://https://tools.ietf.org/html/rfc6698">RFC 6698</seealso>
        </member>
        <member name="F:DnsClient.QueryType.SPF">
            <summary>
            SPF records don't officially have a dedicated RR type, <see cref="F:DnsClient.Protocol.ResourceRecordType.TXT"/> should be used instead.
            The behavior of TXT and SPF are the same.
            </summary>
            <remarks>
            This library will return a TXT record but will set the header type to SPF if such a record is returned.
            </remarks>
            <seealso href="https://tools.ietf.org/html/rfc7208">RFC 7208</seealso>
        </member>
        <member name="F:DnsClient.QueryType.AXFR">
            <summary>
            DNS zone transfer request.
            This can be used only if <see cref="P:DnsClient.DnsQuerySettings.UseTcpOnly"/> is set to true as <c>AXFR</c> is only supported via TCP.
            <para>
            The DNS Server might only return results for the request if the client connection/IP is allowed to do so.
            </para>
            </summary>
        </member>
        <member name="F:DnsClient.QueryType.ANY">
            <summary>
            Generic any query *.
            </summary>
        </member>
        <member name="F:DnsClient.QueryType.URI">
            <summary>
            A Uniform Resource Identifier (URI) resource record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc7553">RFC 7553</seealso>
            <seealso cref="T:DnsClient.Protocol.UriRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.CAA">
            <summary>
            A certification authority authorization.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc6844">RFC 6844</seealso>
            <seealso cref="T:DnsClient.Protocol.CaaRecord"/>
        </member>
        <member name="F:DnsClient.QueryType.SSHFP">
            <summary>
            A SSH Fingerprint resource record.
            </summary>
            <seealso href="https://tools.ietf.org/html/rfc4255">RFC 4255</seealso>
            <seealso cref="T:DnsClient.Protocol.SshfpRecord"/>
        </member>
        <member name="P:DnsClient.Logging.LoggerFactory">
            <summary>
            Gets or sets the <see cref="T:DnsClient.Internal.ILoggerFactory"/> DnsClient should use.
            Per default it will log to <see cref="P:DnsClient.Tracing.Source"/>.
            </summary>
        </member>
        <member name="T:System.IO.RowConfigReader">
            <summary>
            Helper for reading config files where each row is a key-value data pair.
            The input key-values must not have any whitespace within them.
            Keys are only matched if they begin a line, with no preceding whitespace.
            </summary>
        </member>
        <member name="M:System.IO.RowConfigReader.#ctor(System.String)">
            <summary>
            Constructs a new RowConfigReader which reads from the given string.
            <param name="buffer">The string to parse through.</param>
            </summary>
        </member>
        <member name="M:System.IO.RowConfigReader.#ctor(System.String,System.StringComparison)">
            <summary>
            Constructs a new RowConfigReader which reads from the given string.
            <param name="buffer">The string to parse through.</param>
            <param name="comparisonKind">The comparison kind to use.</param>
            </summary>
        </member>
        <member name="M:System.IO.RowConfigReader.GetNextValue(System.String)">
            <summary>
            Gets the next occurrence of the given key, from the current position of the reader,
            or throws if no occurrence of the key exists in the remainder of the string.
            </summary>
        </member>
        <member name="M:System.IO.RowConfigReader.TryGetNextValue(System.String,System.String@)">
            <summary>
            Tries to get the next occurrence of the given key from the current position of the reader.
            If successful, returns true and stores the result in 'value'. Otherwise, returns false.
            </summary>
        </member>
        <member name="M:System.IO.RowConfigReader.GetNextValueAsInt32(System.String)">
            <summary>
            Gets the next occurrence of the key in the string, and parses it as an Int32.
            Throws if the key is not found in the remainder of the string, or if the key
            cannot be successfully parsed into an Int32.
            </summary>
            <remarks>
            This is mainly provided as a helper because most Linux config/info files
            store integral data.
            </remarks>
        </member>
        <member name="M:System.IO.RowConfigReader.ReadFirstValueFromString(System.String,System.String)">
            <summary>
            Reads the value of the first occurrence of the given key contained in the string given.
            </summary>
            <param name="data">The key-value row configuration string.</param>
            <param name="key">The key to find.</param>
            <returns>The value of the row containing the first occurrence of the key.</returns>
        </member>
        <member name="T:System.Net.IpAddressExtensions">
            <summary>
            Extension methods for <see cref="T:System.Net.IPAddress"/>.
            </summary>
        </member>
        <member name="M:System.Net.IpAddressExtensions.GetArpaName(System.Net.IPAddress)">
            <summary>
            Translates a IPv4 or IPv6 <see cref="T:System.Net.IPAddress"/> into an <see href="https://en.wikipedia.org/wiki/.arpa">arpa address</see>.
            Used for reverse DNS lookup to get the domain name of the given <see cref="T:System.Net.IPAddress"/>.
            </summary>
            <param name="ip">The address to translate.</param>
            <returns>The arpa representation of the address.</returns>
            <seealso cref="M:DnsClient.IDnsQuery.QueryReverse(System.Net.IPAddress)"/>
            <seealso cref="M:DnsClient.IDnsQuery.QueryReverseAsync(System.Net.IPAddress,System.Threading.CancellationToken)"/>
            <seealso href="https://en.wikipedia.org/wiki/.arpa"/>
        </member>
        <member name="T:System.Linq.RecordCollectionExtension">
            <summary>
            Extension methods for <see cref="T:System.Collections.Generic.IEnumerable`1"/> where <c>T</c> is <see cref="T:DnsClient.Protocol.DnsResourceRecord"/>.
            </summary>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.AddressRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.ARecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.ARecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.ARecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.ARecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.ARecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.NsRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.NsRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.NsRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.CnameRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.CNameRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.CNameRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.SoaRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.SoaRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.SoaRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.MbRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.MbRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.MbRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.MgRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.MgRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.MgRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.MrRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.MrRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.MrRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.NullRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.NullRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.NullRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.WksRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.WksRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.WksRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.PtrRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.PtrRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.PtrRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.HInfoRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.HInfoRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.HInfoRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.MxRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.MxRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.MxRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.TxtRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.TxtRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.TxtRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.RpRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.RpRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.RpRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.AfsDbRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.AfsDbRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.AfsDbRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.AaaaRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.AaaaRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.AaaaRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.SrvRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.SrvRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.SrvRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.NAPtrRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.NAPtrRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.NAPtrRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.UriRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.UriRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.UriRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.CaaRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.CaaRecord"/>s only.
            </summary>
            <param name="records">The records.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.CaaRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.TlsaRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.TlsaRecord"/>s only
            </summary>
            <param name="records"></param>
            <returns>The list of <see cref="T:DnsClient.Protocol.TlsaRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.RRSigRecords(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord})">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.RRSigRecord"/>s only
            </summary>
            <param name="records"></param>
            <returns>The list of <see cref="T:DnsClient.Protocol.RRSigRecord"/>.</returns>
        </member>
        <member name="M:System.Linq.RecordCollectionExtension.OfRecordType(System.Collections.Generic.IEnumerable{DnsClient.Protocol.DnsResourceRecord},DnsClient.Protocol.ResourceRecordType)">
            <summary>
            Filters the elements of an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to return <see cref="T:DnsClient.Protocol.DnsResourceRecord"/>s
            which have the <paramref name="type"/>.
            </summary>
            <param name="records">The records.</param>
            <param name="type">The <see cref="T:DnsClient.Protocol.ResourceRecordType"/> to filter for.</param>
            <returns>The list of <see cref="T:DnsClient.Protocol.ARecord"/>.</returns>
        </member>
    </members>
</doc>
