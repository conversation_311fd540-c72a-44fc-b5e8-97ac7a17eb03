Imports System.Diagnostics

Public Class frmSupport
    ' BntDiscord
    Private Sub BntDiscord_MouseEnter(sender As Object, e As EventArgs) Handles BntDiscord.MouseEnter
        Label2.ForeColor = Color.FromArgb(34, 197, 94)
    End Sub
    Private Sub BntDiscord_MouseLeave(sender As Object, e As EventArgs) Handles BntDiscord.MouseLeave
        Label2.ForeColor = Color.FromArgb(254, 219, 65)
    End Sub
    ' BntTelegram
    Private Sub BntTelegram_MouseEnter(sender As Object, e As EventArgs) Handles BntTelegram.MouseEnter
        lblTelegram.ForeColor = Color.FromArgb(34, 197, 94)
    End Sub
    Private Sub BntTelegram_MouseLeave(sender As Object, e As EventArgs) Handles BntTelegram.MouseLeave
        lblTelegram.ForeColor = Color.FromArgb(254, 219, 65)
    End Sub
    ' BntSignal
    Private Sub BntSignal_MouseEnter(sender As Object, e As EventArgs) Handles BntSignal.MouseEnter
        Label5.ForeColor = Color.FromArgb(34, 197, 94)
    End Sub
    Private Sub BntSignal_MouseLeave(sender As Object, e As EventArgs) Handles BntSignal.MouseLeave
        Label5.ForeColor = Color.FromArgb(254, 219, 65)
    End Sub
    ' BntTGChannel
    Private Sub BntTGChannel_MouseEnter(sender As Object, e As EventArgs) Handles BntTGChannel.MouseEnter
        Label9.ForeColor = Color.FromArgb(34, 197, 94)
    End Sub
    Private Sub BntTGChannel_MouseLeave(sender As Object, e As EventArgs) Handles BntTGChannel.MouseLeave
        Label9.ForeColor = Color.FromArgb(254, 219, 65)
    End Sub
    ' BntShopping
    Private Sub BntShopping_MouseEnter(sender As Object, e As EventArgs) Handles BntShopping.MouseEnter
        Label6.ForeColor = Color.FromArgb(34, 197, 94)
    End Sub
    Private Sub BntShopping_MouseLeave(sender As Object, e As EventArgs) Handles BntShopping.MouseLeave
        Label6.ForeColor = Color.FromArgb(254, 219, 65)
    End Sub
    ' BntWeb
    Private Sub BntWeb_MouseEnter(sender As Object, e As EventArgs) Handles BntWeb.MouseEnter
        Label4.ForeColor = Color.FromArgb(34, 197, 94)
    End Sub
    Private Sub BntWeb_MouseLeave(sender As Object, e As EventArgs) Handles BntWeb.MouseLeave
        Label4.ForeColor = Color.FromArgb(254, 219, 65)
    End Sub
    ' BntYouTube
    Private Sub BntYouTube_MouseEnter(sender As Object, e As EventArgs) Handles BntYouTube.MouseEnter
        Label10.ForeColor = Color.FromArgb(34, 197, 94)
    End Sub
    Private Sub BntYouTube_MouseLeave(sender As Object, e As EventArgs) Handles BntYouTube.MouseLeave
        Label10.ForeColor = Color.FromArgb(254, 219, 65)
    End Sub
    ' BntEmail
    Private Sub BntEmail_MouseLeave(sender As Object, e As EventArgs) Handles BntEmail.MouseLeave
        Label1.ForeColor = Color.FromArgb(254, 219, 65)
    End Sub
    Private Sub BntDiscord_Click(sender As Object, e As EventArgs) Handles BntDiscord.Click
        Process.Start("https://discord.com/users/1247383168408092794")
    End Sub
    Private Sub BntTelegram_Click(sender As Object, e As EventArgs) Handles BntTelegram.Click
        Process.Start("https://t.me/BestSenderVIP")
    End Sub
    Private Sub BntSignal_Click(sender As Object, e As EventArgs) Handles BntSignal.Click
        Process.Start("https://signal.me/#eu/N_S6zRFQZxUy7qNMGhjUkDHeWTP5swskArw9cYHyjET-gmX07CtCMq9Al3RPTpEJ")
    End Sub
    Private Sub BntTGChannel_Click(sender As Object, e As EventArgs) Handles BntTGChannel.Click
        Process.Start("https://t.me/bsvtrusted")
    End Sub
    Private Sub BntShopping_Click(sender As Object, e As EventArgs) Handles BntShopping.Click
        Process.Start("https://www.bestsendervip.com/shop")
    End Sub
    Private Sub BntWeb_Click(sender As Object, e As EventArgs) Handles BntWeb.Click
        Process.Start("https://www.bestsendervip.com/")
    End Sub
    Private Sub frmSupport_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' إضافة معالج حدث لتغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmSupport_Resize

        ' تطبيق تصميم Binance
        ApplyBinanceStyle()

        ' وضع العناصر في المنتصف عند تحميل النموذج
        CenterControls()
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على النموذج
    ''' </summary>
    Private Sub ApplyBinanceStyle()
        Try
            ' تعيين لون خلفية النموذج
            Me.BackColor = Color.FromArgb(26, 26, 29)

            ' تطبيق تصميم Binance على GroupControls
            For Each ctrl As Control In Me.Controls
                If TypeOf ctrl Is DevExpress.XtraEditors.GroupControl Then
                    Dim groupControl As DevExpress.XtraEditors.GroupControl = DirectCast(ctrl, DevExpress.XtraEditors.GroupControl)

                    ' تعيين لون خلفية GroupControl
                    groupControl.Appearance.BackColor = Color.FromArgb(30, 32, 38)
                    groupControl.Appearance.Options.UseBackColor = True

                    ' تعيين لون حدود GroupControl
                    groupControl.LookAndFeel.UseDefaultLookAndFeel = False
                    groupControl.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
                    groupControl.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
                    groupControl.Appearance.BorderColor = Color.FromArgb(254, 219, 65)
                    groupControl.Appearance.Options.UseBorderColor = True

                    ' تعيين لون نص GroupControl
                    groupControl.AppearanceCaption.ForeColor = Color.White
                    groupControl.AppearanceCaption.Options.UseForeColor = True
                    groupControl.AppearanceCaption.BackColor = Color.FromArgb(30, 32, 38)
                    groupControl.AppearanceCaption.Options.UseBackColor = True

                    ' إضافة معالج حدث Paint للـ GroupControl
                    AddHandler groupControl.Paint, AddressOf GroupControl_Paint

                    ' تطبيق تصميم Binance على العناصر الفرعية
                    ApplyBinanceStyleToChildControls(groupControl)
                End If
            Next

            Debug.WriteLine("Binance style applied successfully")
        Catch ex As Exception
            Debug.WriteLine($"Error applying Binance style: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث Paint للـ GroupControl
    ''' </summary>
    Private Sub GroupControl_Paint(sender As Object, e As PaintEventArgs)
        Try
            ' رسم حدود منحنية للـ GroupControl
            Dim groupControl As DevExpress.XtraEditors.GroupControl = DirectCast(sender, DevExpress.XtraEditors.GroupControl)
            Dim radius As Integer = 20 ' زيادة نصف القطر للحصول على حواف أكثر انحناءً

            ' تعيين جودة الرسم
            e.Graphics.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
            e.Graphics.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
            e.Graphics.PixelOffsetMode = Drawing2D.PixelOffsetMode.HighQuality

            ' تعديل المستطيل ليكون داخل حدود اللوحة بشكل كامل
            ' نترك هامشًا أكبر (10 بكسل) لضمان ظهور الحدود بالكامل
            Dim rect As New Rectangle(10, 10, groupControl.Width - 20, groupControl.Height - 20)

            ' رسم مستطيل بحواف منحنية باللون الذهبي (254, 219, 65) بسمك 2 بكسل
            Using pen As New Pen(Color.FromArgb(254, 219, 65), 2)
                ' تعيين نهايات الخطوط لتكون دائرية
                pen.StartCap = Drawing2D.LineCap.Round
                pen.EndCap = Drawing2D.LineCap.Round
                pen.LineJoin = Drawing2D.LineJoin.Round
                pen.Alignment = Drawing2D.PenAlignment.Center

                ' رسم مستطيل بحواف منحنية
                e.Graphics.DrawPath(pen, GetRoundedRectPath(rect, radius))

                ' رسم مستطيل آخر بنفس الحجم لضمان ظهور الخط بشكل كامل
                ' هذه تقنية لتجنب مشكلة تقطيع الخطوط في GDI+
                e.Graphics.DrawPath(pen, GetRoundedRectPath(rect, radius))
            End Using
        Catch ex As Exception
            Debug.WriteLine($"Error in GroupControl_Paint: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' إنشاء مسار لمستطيل بحواف منحنية
    ''' </summary>
    Private Function GetRoundedRectPath(rect As Rectangle, radius As Integer) As Drawing2D.GraphicsPath
        Dim path As New Drawing2D.GraphicsPath()

        ' تأكد من أن نصف القطر لا يتجاوز نصف عرض أو ارتفاع المستطيل
        radius = Math.Min(radius, Math.Min(rect.Width \ 2, rect.Height \ 2))

        ' إضافة الأقواس والخطوط للمسار
        ' الزاوية العلوية اليسرى
        path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90)

        ' الخط العلوي
        path.AddLine(rect.X + radius, rect.Y, rect.Right - radius, rect.Y)

        ' الزاوية العلوية اليمنى
        path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90)

        ' الخط الأيمن
        path.AddLine(rect.Right, rect.Y + radius, rect.Right, rect.Bottom - radius)

        ' الزاوية السفلية اليمنى
        path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90)

        ' الخط السفلي
        path.AddLine(rect.Right - radius, rect.Bottom, rect.X + radius, rect.Bottom)

        ' الزاوية السفلية اليسرى
        path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90)

        ' الخط الأيسر
        path.AddLine(rect.X, rect.Bottom - radius, rect.X, rect.Y + radius)

        ' إغلاق المسار
        path.CloseAllFigures()

        Return path
    End Function

    ''' <summary>
    ''' تطبيق تصميم Binance على العناصر الفرعية
    ''' </summary>
    Private Sub ApplyBinanceStyleToChildControls(parent As Control)
        For Each ctrl As Control In parent.Controls
            ' تطبيق تصميم Binance على Labels
            If TypeOf ctrl Is Label Then
                Dim label As Label = DirectCast(ctrl, Label)

                ' تعيين لون نص Label
                If label.ForeColor <> Color.FromArgb(254, 219, 65) AndAlso label.ForeColor <> Color.FromArgb(34, 197, 94) Then
                    label.ForeColor = Color.White
                End If
            End If

            ' لا نقوم بتغيير الأزرار - نتركها كما هي
            ' تطبيق تصميم Binance على العناصر الفرعية بشكل متكرر
            If ctrl.Controls.Count > 0 Then
                ApplyBinanceStyleToChildControls(ctrl)
            End If
        Next
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmSupport_Resize(sender As Object, e As EventArgs)
        ' وضع العناصر في المنتصف عند تغيير حجم النموذج
        CenterControls()
    End Sub

    ''' <summary>
    ''' وضع جميع العناصر في المنتصف مع الحفاظ على ترتيبهم الأصلي
    ''' </summary>
    Private Sub CenterControls()
        Try
            ' الحصول على حجم النموذج
            Dim formWidth As Integer = Me.ClientSize.Width
            Dim formHeight As Integer = Me.ClientSize.Height

            ' الحصول على مجموعة العناصر الرئيسية
            Dim controlsToCenter As New Dictionary(Of Control, Point)

            ' حفظ المواقع الأصلية للعناصر
            For Each ctrl As Control In Me.Controls
                If TypeOf ctrl Is DevExpress.XtraEditors.GroupControl Then
                    ' حفظ الموقع الأصلي للعنصر
                    controlsToCenter.Add(ctrl, ctrl.Location)
                End If
            Next

            ' إذا لم تكن هناك عناصر للتوسيط، فلا داعي للاستمرار
            If controlsToCenter.Count = 0 Then
                Return
            End If

            ' حساب أقصى وأدنى قيم X و Y للعناصر
            Dim minX As Integer = Integer.MaxValue
            Dim minY As Integer = Integer.MaxValue
            Dim maxX As Integer = Integer.MinValue
            Dim maxY As Integer = Integer.MinValue

            For Each kvp As KeyValuePair(Of Control, Point) In controlsToCenter
                Dim ctrl As Control = kvp.Key
                Dim rightEdge As Integer = ctrl.Location.X + ctrl.Width
                Dim bottomEdge As Integer = ctrl.Location.Y + ctrl.Height

                If ctrl.Location.X < minX Then minX = ctrl.Location.X
                If ctrl.Location.Y < minY Then minY = ctrl.Location.Y
                If rightEdge > maxX Then maxX = rightEdge
                If bottomEdge > maxY Then maxY = bottomEdge
            Next

            ' حساب العرض والارتفاع الإجمالي للعناصر
            Dim totalWidth As Integer = maxX - minX
            Dim totalHeight As Integer = maxY - minY

            ' حساب الإزاحة المطلوبة لتوسيط العناصر
            Dim offsetX As Integer = (formWidth - totalWidth) \ 2 - minX
            Dim offsetY As Integer = (formHeight - totalHeight) \ 2 - minY

            ' تطبيق الإزاحة على جميع العناصر للحفاظ على مواقعها النسبية
            For Each kvp As KeyValuePair(Of Control, Point) In controlsToCenter
                Dim ctrl As Control = kvp.Key
                Dim originalLocation As Point = kvp.Value

                ' تعيين الموقع الجديد مع الحفاظ على الموقع النسبي
                ctrl.Location = New Point(originalLocation.X + offsetX, originalLocation.Y + offsetY)
            Next

            Debug.WriteLine($"Centered controls. Form size: {formWidth}x{formHeight}, Total size: {totalWidth}x{totalHeight}, Offset: {offsetX},{offsetY}")
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error centering controls: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub
    Private Sub BntYouTube_Click(sender As Object, e As EventArgs) Handles BntYouTube.Click
        Process.Start("https://www.youtube.com/@BestSenderVIP2024")
    End Sub
    Private Sub BntEmail_Click(sender As Object, e As EventArgs) Handles BntEmail.Click
        Clipboard.SetText(Label1.Text)
        Label1.ForeColor = Color.FromArgb(34, 197, 94)
        MessageBox.Show("Copy Completed Successfully", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
End Class