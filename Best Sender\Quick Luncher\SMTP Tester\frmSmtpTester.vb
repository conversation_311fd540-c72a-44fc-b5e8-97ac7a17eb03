﻿Imports System.ComponentModel
Imports System.IO
Imports System.Net.Mail
Imports System.Threading
Imports DevExpress.Utils
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Imports Microsoft.VisualBasic.Devices
Public Class frmSmtpTester
    Dim CheckAll As Boolean = False
    Dim myValue As Boolean = False
    Dim delitems As List(Of String) = New List(Of String)
    Dim StopBYError As Boolean = False
    Dim MainErrormsg As String = ""
    Sub CreateSMTPTable()
        DT_SMTPTester = New DataTable
        DT_SMTPTester.Columns.Clear()
        DT_SMTPTester.TableName = "TempTable"
        DT_SMTPTester.Columns.Add("ID", GetType(Int32))
        DT_SMTPTester.Columns.Add("smtpserver", GetType(String))
        DT_SMTPTester.Columns.Add("username", GetType(String))
        DT_SMTPTester.Columns.Add("password", GetType(String))
        DT_SMTPTester.Columns.Add("port", GetType(String))
        DT_SMTPTester.Columns.Add("status", GetType(String))
        DT_SMTPTester.Columns.Add("errormsg", GetType(String))
        DT_SMTPTester.Columns.Add("CanCheck", GetType(Boolean))
        DT_SMTPTester.Columns.Add("emailFrom", GetType(String))
    End Sub
    Private Sub UpdateGridControlDataSource()
        If GridControl1.InvokeRequired Then
            GridControl1.BeginInvoke(New MethodInvoker(AddressOf UpdateGridControlDataSource))
        Else
            GridControl1.DataSource = DT_SMTPTester
            GridControl1.RefreshDataSource()
        End If
    End Sub
    Private Sub frmEmailSorter_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Opacity = 0
        trmfadein.Enabled = True
        BackgroundWorker1.WorkerSupportsCancellation = True
        BackgroundWorker1.WorkerReportsProgress = True

        ' تطبيق تصميم Binance على GridControl
        ApplyBinanceGridStyling()
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على GridControl
    ''' </summary>
    Private Sub ApplyBinanceGridStyling()
        ' تعيين لون خلفية النموذج
        Me.BackColor = Color.FromArgb(18, 22, 28)
        GroupControl1.Appearance.BackColor = Color.FromArgb(18, 22, 28)
        GroupControl1.LookAndFeel.SkinMaskColor = Color.FromArgb(18, 22, 28)
        GroupControl1.LookAndFeel.SkinMaskColor2 = Color.FromArgb(18, 22, 28)

        ' تعيين لون خلفية LayoutControl
        LayoutControl1.BackColor = Color.FromArgb(18, 22, 28)
        LayoutControl2.BackColor = Color.FromArgb(18, 22, 28)

        ' تعيين لون خلفية Panel
        Panel1.BackColor = Color.FromArgb(18, 22, 28)
        Panel2.BackColor = Color.FromArgb(18, 22, 28)

        ' تعيين لون النص في Label
        Label2.ForeColor = Color.FromArgb(160, 174, 192)
        Label3.ForeColor = Color.FromArgb(160, 174, 192)

        ' تعيين لون خلفية TextEdit
        txtPath.Properties.Appearance.BackColor = Color.FromArgb(30, 35, 41)
        txtPath.Properties.Appearance.ForeColor = Color.White

        ' تعيين لون CheckSSL
        CheckSSL.ForeColor = Color.FromArgb(254, 219, 65
                                            )

        ' تعيين إعدادات GridControl
        GridControl1.LookAndFeel.SkinMaskColor = Color.FromArgb(18, 22, 28)
        GridControl1.LookAndFeel.SkinMaskColor2 = Color.FromArgb(18, 22, 28)

        ' تعيين إعدادات GridView
        With GridView1
            ' تعيين لون خلفية الجدول
            .Appearance.Empty.BackColor = Color.FromArgb(18, 22, 28)
            .Appearance.Empty.Options.UseBackColor = True

            ' تعيين مظهر الصفوف
            .Appearance.Row.BackColor = Color.FromArgb(18, 22, 28)
            .Appearance.Row.ForeColor = Color.White
            .Appearance.Row.Options.UseBackColor = True
            .Appearance.Row.Options.UseForeColor = True

            ' تعيين مظهر الصفوف المتناوبة
            .Appearance.EvenRow.BackColor = Color.FromArgb(30, 35, 41)
            .Appearance.EvenRow.Options.UseBackColor = True

            ' تعيين مظهر رأس الأعمدة
            .Appearance.HeaderPanel.BackColor = Color.FromArgb(18, 22, 28)
            .Appearance.HeaderPanel.ForeColor = Color.FromArgb(160, 174, 192)
            .Appearance.HeaderPanel.Font = New Font(.Appearance.HeaderPanel.Font, FontStyle.Bold)
            .Appearance.HeaderPanel.Options.UseBackColor = True
            .Appearance.HeaderPanel.Options.UseForeColor = True
            .Appearance.HeaderPanel.Options.UseFont = True
            .Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            .Appearance.HeaderPanel.Options.UseTextOptions = True

            ' تعيين مظهر الصف المحدد
            .Appearance.FocusedRow.BackColor = Color.FromArgb(47, 52, 59)
            .Appearance.FocusedRow.Options.UseBackColor = True

            ' تعيين مظهر الصف عند المرور عليه
            .Appearance.HideSelectionRow.BackColor = Color.FromArgb(44, 47, 54)
            .Appearance.HideSelectionRow.Options.UseBackColor = True

            ' إزالة خطوط الشبكة
            .OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.False
            .OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.False

            ' تقليل ارتفاع الصفوف لعرض أكثر كثافة
            .RowHeight = 28

            ' تمكين نمط الصفوف المتناوبة
            .OptionsView.EnableAppearanceEvenRow = True

            ' تعيين الخط
            .Appearance.Row.Font = New Font("Segoe UI", 10)
            .Appearance.Row.Options.UseFont = True
        End With

        ' إعادة تعيين معالج حدث تنسيق الخلايا
        RemoveHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
        AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle

        ' إضافة معالج حدث رسم الخلايا المخصص لعمود الحالة
        RemoveHandler GridView1.CustomDrawCell, AddressOf GridView1_CustomDrawCell
        AddHandler GridView1.CustomDrawCell, AddressOf GridView1_CustomDrawCell
    End Sub

    ''' <summary>
    ''' معالج حدث رسم الخلايا المخصص لعرض الأيقونات والألوان المخصصة في عمود الحالة
    ''' </summary>
    Private Sub GridView1_CustomDrawCell(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs) Handles GridView1.CustomDrawCell
        ' التحقق من أن العمود هو عمود الحالة
        If e.Column.FieldName = "status" Then
            ' الحصول على قيمة الحالة
            Dim statusValue As String = GridView1.GetRowCellValue(e.RowHandle, "status").ToString()

            ' تعيين الألوان والأيقونات حسب الحالة
            Dim textColor As Color
            Dim icon As Image = Nothing

            Select Case statusValue
                Case "Working"
                    ' استخدام لون Binance الأخضر للقيم الموجبة
                    textColor = Color.FromArgb(34, 197, 94)
                    ' محاولة تحميل الأيقونة من الموارد
                    Try
                        icon = My.Resources.Valid16x16
                    Catch ex As Exception
                        ' إذا لم تكن الأيقونة موجودة، استمر بدون أيقونة
                    End Try

                Case "Fail"
                    ' استخدام لون أحمر غامق للقيم السالبة
                    textColor = Color.FromArgb(180, 30, 30)
                    ' محاولة تحميل الأيقونة من الموارد
                    Try
                        icon = My.Resources.Notworking16x16
                    Catch ex As Exception
                        ' إذا لم تكن الأيقونة موجودة، استمر بدون أيقونة
                    End Try

                Case "Untested"
                    ' استخدام لون برتقالي للقيم غير المختبرة
                    textColor = Color.FromArgb(255, 159, 67)
                    ' محاولة تحميل الأيقونة من الموارد
                    Try
                        icon = My.Resources.pending16x16
                    Catch ex As Exception
                        ' إذا لم تكن الأيقونة موجودة، استمر بدون أيقونة
                    End Try

                Case "Testng"
                    ' استخدام لون أبيض للاختبار الجاري
                    textColor = Color.FromArgb(255, 255, 255)
                    ' محاولة تحميل الأيقونة من الموارد
                    Try
                        icon = My.Resources.pending16x16
                    Catch ex As Exception
                        ' إذا لم تكن الأيقونة موجودة، استمر بدون أيقونة
                    End Try

                Case Else
                    ' استخدام اللون الافتراضي لأي حالة أخرى
                    textColor = Color.White
            End Select

            ' رسم خلفية الخلية
            e.Appearance.BackColor = Color.FromArgb(18, 22, 28)
            e.Appearance.FillRectangle(e.Cache, e.Bounds)

            ' تحديد موضع الأيقونة والنص
            Dim iconBounds As Rectangle = Rectangle.Empty
            Dim textBounds As Rectangle = e.Bounds

            ' إذا كانت الأيقونة موجودة، قم برسمها
            If icon IsNot Nothing Then
                ' تحديد موضع الأيقونة (في يسار الخلية)
                iconBounds = New Rectangle(e.Bounds.X + 4, e.Bounds.Y + (e.Bounds.Height - 16) \ 2, 16, 16)
                ' رسم الأيقونة
                e.Cache.DrawImage(icon, iconBounds)
                ' تعديل موضع النص ليكون بعد الأيقونة
                textBounds = New Rectangle(iconBounds.Right + 4, e.Bounds.Y, e.Bounds.Width - iconBounds.Width - 8, e.Bounds.Height)
            End If

            ' تعيين خصائص النص
            e.Appearance.Font = New Font("Segoe UI", 10, FontStyle.Bold)
            e.Appearance.ForeColor = textColor
            e.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
            e.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center

            ' رسم النص
            e.Cache.DrawString(statusValue, e.Appearance.Font, New SolidBrush(textColor), textBounds, e.Appearance.GetStringFormat())

            ' إخبار DevExpress أننا قمنا برسم الخلية بأنفسنا
            e.Handled = True
        End If
    End Sub

    Private Sub trmfadein_Tick(sender As Object, e As EventArgs) Handles trmfadein.Tick
        Me.Opacity = Me.Opacity + 0.02
        If Me.Opacity = 1 Then
            Me.Opacity = 99 / 100
            trmfadein.Enabled = False
            trmfadein.Stop()
        End If
    End Sub
    Private Sub LetterEncoder_bntSearch_Click(sender As Object, e As EventArgs)
    End Sub
    Private Sub CheckEdit1_CheckedChanged(sender As Object, e As EventArgs) Handles CheckSSL.CheckedChanged
        If CheckSSL.Checked = True Then
            CheckSSL.ForeColor = Color.FromArgb(0, 194, 146) ' لون Binance الأخضر
        Else
            CheckSSL.ForeColor = Color.FromArgb(160, 174, 192) ' لون رمادي ناعم
        End If
    End Sub
    Private Sub GridView1_RowCellStyle(sender As Object, e As RowCellStyleEventArgs)
        If e.Column.FieldName = "status" Then
            Dim statusValue As String = GridView1.GetRowCellValue(e.RowHandle, "status").ToString()
            If statusValue = "Fail" Then
                ' استخدام لون Binance الأحمر للقيم السالبة
                e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                e.Appearance.BackColor = Color.FromArgb(18, 22, 28)
            ElseIf statusValue = "Working" Then
                ' استخدام لون Binance الأخضر للقيم الموجبة
                e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                e.Appearance.BackColor = Color.FromArgb(18, 22, 28)
            ElseIf statusValue = "Untested" Then
                ' لون أصفر ذهبي للقيم غير المختبرة
                e.Appearance.ForeColor = Color.FromArgb(254, 203, 35)
                e.Appearance.BackColor = Color.FromArgb(18, 22, 28)
            ElseIf statusValue = "Testng" Then
                ' لون أزرق للاختبار الجاري
                e.Appearance.ForeColor = Color.FromArgb(59, 130, 246)
                e.Appearance.BackColor = Color.FromArgb(18, 22, 28)
            End If
            ' تأكيد أن الخط غامق (Bold) كما في النسق العام
            e.Appearance.Font = New Font("Segoe UI", 10, FontStyle.Bold)
        End If
    End Sub
    Private Sub BntSelectAll_Click(sender As Object, e As EventArgs) Handles BntSelectAll.Click
        If GridView1.DataRowCount = 0 Then Exit Sub
        If BntSelectAll.Text = "Select All" Then
            BntSelectAll.Text = "Unselect All"
            For i = 0 To GridView1.DataRowCount - 1
                GridView1.SetRowCellValue(i, "CanCheck", True)
                GridView1.UpdateCurrentRow()
                GridView1.PostEditor()
            Next
        ElseIf BntSelectAll.Text = "Unselect All" Then
            BntSelectAll.Text = "Select All"
            For i = 0 To GridView1.DataRowCount - 1
                GridView1.SetRowCellValue(i, "CanCheck", False)
                GridView1.UpdateCurrentRow()
                GridView1.PostEditor()
            Next
        End If
    End Sub
    Private Sub Col_Chk_CheckedChanged(sender As Object, e As EventArgs)
        myValue = (If(GridView1.GetRowCellValue(GridView1.FocusedRowHandle, "CanCheck") Is Nothing, False, CBool(GridView1.GetRowCellValue(GridView1.FocusedRowHandle, "CanCheck"))))
        GridView1.SetRowCellValue(GridView1.FocusedRowHandle, "CanCheck", Not myValue)
        GridView1.UpdateCurrentRow()
        GridView1.PostEditor()
    End Sub
    Private Sub Bnt_Remove_ButtonClick_1(sender As Object, e As Controls.ButtonPressedEventArgs) Handles Bnt_Remove.ButtonClick
        Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to remove the current SMTP Server Account?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Dim SelectedRowHandles As Integer() = GridView1.GetSelectedRows()
        Dim id_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("ID"))
        Try
            ' Set the ID column as the primary key
            DT_SMTPTester.PrimaryKey = New DataColumn() {DT_SMTPTester.Columns("ID")}
            Dim foundRow As DataRow = DT_SMTPTester.Rows.Find(id_)
            ' reomve row
            foundRow.Delete()
            For i As Integer = 0 To DT_SMTPTester.Rows.Count - 1
                DT_SMTPTester.Rows(i)("ID") = i + 1
            Next
            '=================================================================
            ' Refresh Smtp gird
            GridControl1.DataSource = DT_SMTPTester
            DevExpress.XtraEditors.XtraMessageBox.Show("Account Workingfully removed...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub BNT_Edit_ButtonClick_1(sender As Object, e As Controls.ButtonPressedEventArgs) Handles BNT_Edit.ButtonClick
        Dim SelectedRowHandles As Integer() = GridView1.GetSelectedRows()
        Dim id_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("ID"))
        Dim smtpserver_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("smtpserver"))
        Dim username_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("username"))
        Dim password_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("password"))
        Dim port_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("port"))
        '=======================================================================================================
        frmSMTPTester_Details.Tag = id_
        frmSMTPTester_Details.TxtSMTPServer.Text = smtpserver_
        frmSMTPTester_Details.TxtUsername.Text = username_
        frmSMTPTester_Details.TxtPassword.Text = password_
        frmSMTPTester_Details.TxtPort.Text = port_
        frmSMTPTester_Details.TxtSMTPServer.Tag = smtpserver_
        frmSMTPTester_Details.TxtUsername.Tag = username_
        frmSMTPTester_Details.TxtPassword.Tag = password_
        frmSMTPTester_Details.TxtPort.Tag = port_
        frmSMTPTester_Details.ShowDialog()
    End Sub
    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        UpdateGridControlDataSource()
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Dim smtphost_ As String = ""
        Dim emailaddress_ As String = ""
        Dim password_ As String = ""
        Dim port_ As String = ""
        Dim emailFrom As String = ""
        Dim Errormsg As String = ""
        Try
            For i = 0 To DT_SMTPTester.Rows.Count - 1
                If BackgroundWorker1.CancellationPending Then
                    e.Cancel = True
                    Return
                End If
                CheckForIllegalCrossThreadCalls = False
                If DT_SMTPTester.Rows(i).Item("Status") <> "Untested" Or DT_SMTPTester.Rows(i).Item("Status") = "Fail" Then GoTo nextRow
                DT_SMTPTester.Rows(i).Item("Status") = "Testng"
                AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                smtphost_ = DT_SMTPTester.Rows(i).Item("smtpserver")
                emailaddress_ = DT_SMTPTester.Rows(i).Item("username")
                password_ = DT_SMTPTester.Rows(i).Item("password")
                port_ = DT_SMTPTester.Rows(i).Item("port")
                emailFrom = DT_SMTPTester.Rows(i).Item("emailFrom")
                Errormsg = Test_SmtpAccount(smtphost_, emailaddress_, password_, port_, CheckSSL.Checked, emailFrom)
                If Errormsg = "Working" Then
                    DT_SMTPTester.Rows(i).Item("errormsg") = ""
                    DT_SMTPTester.Rows(i).Item("Status") = "Working"
                Else
                    DT_SMTPTester.Rows(i).Item("errormsg") = Errormsg
                    DT_SMTPTester.Rows(i).Item("Status") = "Fail"
                End If
nextRow:
                CheckForIllegalCrossThreadCalls = False
                ProgressPanel1.Invoke(Sub()
                                          ProgressPanel1.Description = i + 1 & " out of " & DT_SMTPTester.Rows.Count & " has been tested."
                                          Refresh()
                                      End Sub)
            Next
            StopBYError = False
        Catch ex As Exception
            MainErrormsg = ex.Message
            'ProgressPanel1.Invoke(Sub()
            '                          ProgressPanel1.Visible = False
            '                          Refresh()
            '                      End Sub)
            StopBYError = True
        End Try
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        Timer1.Enabled = False
        ProgressPanel1.Visible = False
        GridControl1.DataSource = DT_SMTPTester.DefaultView
        GridControl1.Refresh()
        BntStartBntStart.Caption = "&Start"
        BntStartBntStart.ImageOptions.Image = My.Resources.Start_Image
        BntNew.Enabled = True
        Bnt_Save.Enabled = True
        SimpleButton1.Enabled = True
        If StopBYError = True Then
            XtraMessageBox.Show(MainErrormsg, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Exit Sub
        End If
        If e.Cancelled Then
            XtraMessageBox.Show("Testing proccess was cancelled by the user...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
        Else
            XtraMessageBox.Show("Testing proccess Workingfully completed...!", "Confirmation", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub GridView1_RowCellClick(sender As Object, e As RowCellClickEventArgs) Handles GridView1.RowCellClick
        If e.Column.FieldName = "status" Then
            Dim statusValue As String = GridView1.GetRowCellValue(e.RowHandle, "status").ToString()
            If statusValue = "Fail" Then
                Dim errorMessage As String = GridView1.GetRowCellValue(e.RowHandle, "errormsg").ToString()
                DevExpress.XtraEditors.XtraMessageBox.Show(errorMessage, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            ElseIf statusValue = "Working" Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Account Workingfully Tested...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ElseIf statusValue = "Untested" Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Account is no tested yet...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ElseIf statusValue = "Testng" Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Account under verification now...", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If
        End If
    End Sub
    Private Sub BtnStop_Click(sender As Object, e As EventArgs)
        'Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to stop the process now?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        'If result = DialogResult.No Then Exit Sub
        'IMAP_Accountno_Status = ""
        'BackgroundWorker1.WorkerSupportsCancellation = True
        'BackgroundWorker1.CancelAsync()
        'رجع الى فوق دا لو الكود دا مظبطش
        '-------------------------------
        Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to stop the process now?",
                                                     "Question",
                                                     MessageBoxButtons.YesNo,
                                                     MessageBoxIcon.Question,
                                                     MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        ' إلغاء العملية
        IMAP_Accountno_Status = ""
        BackgroundWorker1.WorkerSupportsCancellation = True
        BackgroundWorker1.CancelAsync()
        ' إخفاء النموذج بدلاً من إغلاقه
        Me.Hide()
    End Sub
    Private Sub SimpleButton1_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles SimpleButton1.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If ProgressPanel1.Visible = True Then
            XtraMessageBox.Show("The Verification Process in finished yet, Please wait...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        '===== check if there is any verified smtp accounts
        Dim Str_ As String = ""
        Dim Verified_ As Integer = 0
        Dim Working_list As New List(Of String)
        Dim smtphost_ As String = ""
        Dim emailaddress_ As String = ""
        Dim password_ As String = ""
        Dim port_ As String = ""
        Dim emailFrom_ As String = ""
        Dim SSl_ As Boolean = CheckSSL.Checked
        For i As Integer = 0 To GridView1.RowCount - 1
            smtphost_ = DT_SMTPTester.Rows(i).Item("smtpserver")
            emailaddress_ = DT_SMTPTester.Rows(i).Item("username")
            password_ = DT_SMTPTester.Rows(i).Item("password")
            port_ = DT_SMTPTester.Rows(i).Item("port")
            emailFrom_ = DT_SMTPTester.Rows(i).Item("emailFrom")
            If GridView1.GetRowCellValue(i, "status").ToString() = "Working" Then
                Str_ = smtphost_ & "|" & emailaddress_ & "|" & password_ & "|" & port_ & "|" & emailFrom_
                Verified_ = Verified_ + 1
                Working_list.Add(Str_)
            End If
        Next
        '================================================================================
        If Verified_ = 0 Then
            XtraMessageBox.Show("No Verified Smtp acount found...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim result As DialogResult = XtraMessageBox.Show("( " & Verified_ & " ) Verified Smtp acount found. If you want to Transfer them to Sender clcik YES.", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            If LS_SenderSmtp.Count > 0 Then
                For Each item_ In Working_list
                    Dim itemArray_ = Split(item_, "|")
                    For i = 0 To LS_SenderSmtp.Count - 1
                        If Not LS_SenderSmtp(i).smtphost.Equals(itemArray_(0).Trim) AndAlso Not LS_SenderSmtp(i).smtpemail.Equals(itemArray_(1).Trim) Then
                            Dim line As New SenderSmtpSettings()
                            With line
                                .id = LS_SenderSmtp.Count + 1
                                .smtpssl = SSl_
                                .smtpstatus = "Working"
                                .smtphost = itemArray_(0).Trim
                                .smtpemail = itemArray_(1).Trim
                                .smtppassword = itemArray_(2).Trim
                                .smtpport = itemArray_(3).Trim
                                .smtpfrommail = itemArray_(4).Trim
                            End With
                            LS_SenderSmtp.Add(line)
                        End If
                    Next
                Next
            Else
                LS_SenderSmtp.Clear()
                For Each item_ In Working_list
                    Dim itemArray_ = Split(item_, "|")
                    Dim line As New SenderSmtpSettings()
                    With line
                        .id = LS_SenderSmtp.Count + 1
                        .smtpssl = SSl_
                        .smtpstatus = "Working"
                        .smtphost = itemArray_(0).Trim
                        .smtpemail = itemArray_(1).Trim
                        .smtppassword = itemArray_(2).Trim
                        .smtpport = itemArray_(3).Trim
                        .smtpfrommail = itemArray_(4).Trim
                    End With
                    LS_SenderSmtp.Add(line)
                Next
            End If
            frmEmailSender.GridControl1.RefreshDataSource()
            XtraMessageBox.Show("Operation Workingfully Done. Go to Sender to check the list..", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub GridControl1_MouseDown(sender As Object, e As MouseEventArgs) Handles GridControl1.MouseDown
        ' التحقق من أن النقر بالزر الأيمن
        If e.Button = MouseButtons.Right Then
            PopupMenu1.ShowPopup(GridControl1.PointToScreen(e.Location))
        End If
    End Sub
    Private Sub BntBrowse_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntBrowse.ItemClick
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        If (openFileDialog.ShowDialog() = DialogResult.OK) Then
            CreateSMTPTable()
            Dim filePath As String = openFileDialog.FileName
            Dim Counter_ As Integer = 1
            txtPath.Text = filePath
            Using reader As New StreamReader(filePath)
                Dim line As String
                While (Not reader.EndOfStream)
                    line = reader.ReadLine()
                    Dim values() = line.Split("|")
                    If values.Length < 4 Then GoTo nextLinw
                    For Each DT_SMTPTesterrow As DataRow In DT_SMTPTester.Rows
                        If DT_SMTPTesterrow("smtpserver").Equals(values(0).Trim) AndAlso DT_SMTPTesterrow("username").Equals(values(1).Trim) Then
                            GoTo nextLinw
                        End If
                    Next
                    Dim row As DataRow = DT_SMTPTester.NewRow
                    row("ID") = Counter_
                    row("smtpserver") = values(0).Trim
                    row("username") = values(1).Trim
                    row("password") = values(2).Trim
                    row("port") = values(3).Trim
                    row("status") = "Untested"
                    row("errormsg") = "Nil"
                    row("CanCheck") = True
                    If values.Length > 4 Then
                        row("emailFrom") = values(4).Trim
                    Else
                        row("emailFrom") = ""
                    End If
                    DT_SMTPTester.Rows.Add(row)
                    Counter_ = Counter_ + 1
nextLinw:
                End While
            End Using
            GridControl1.DataSource = DT_SMTPTester
            BntSelectAll.Text = "Unselect All"
            AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
        End If
    End Sub
    Private Sub BntNew_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntNew.ItemClick
        txtPath.Text = ""
        CheckSSL.Checked = False
        GridControl1.DataSource = Nothing
        Timer1.Enabled = False
        ProgressPanel1.Visible = False
        BntStartBntStart.Caption = "&Start"
        MainErrormsg = ""
        StopBYError = False
        CreateSMTPTable()
    End Sub
    Private Sub BntStartBntStart_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntStartBntStart.ItemClick
        StopBYError = False
        MainErrormsg = ""
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("Add atleast one Smtp account to start...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        BntStartBntStart.Caption = "&Cancel"
        BntStartBntStart.ImageOptions.Image = My.Resources.Stop32x32
        DevExpress.Data.CurrencyDataController.DisableThreadingProblemsDetection = True
        ProgressPanel1.Description = "0 out of " & DT_SMTPTester.Rows.Count & " has been tested."
        ProgressPanel1.Visible = True
        Timer1.Enabled = True
        BntNew.Enabled = False
        Bnt_Save.Enabled = False
        SimpleButton1.Enabled = False
        If Not BackgroundWorker1.IsBusy Then
            BackgroundWorker1.RunWorkerAsync()
        Else
            BackgroundWorker1.CancelAsync()
        End If
        GridView1.ClearSelection()
    End Sub
    Private Sub Bnt_Save_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles Bnt_Save.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If ProgressPanel1.Visible = True Then
            Dim result As DialogResult = XtraMessageBox.Show("The Verification process Is Not over yet. Do you want to continue with the saving process?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
            If result = DialogResult.No Then Exit Sub
        End If
        ' Get the path to the Desktop directory.
        Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory)
        ' Define the path for the "Main Folder Path" folder.
        Dim MainFolderPath As String = Path.Combine(desktopPath, "Smtp Accounts Tester")
        ' Check if the "Best Defender" folder exists. If not, create it.
        If Not Directory.Exists(MainFolderPath) Then
            Directory.CreateDirectory(MainFolderPath)
        End If
        ' Define the path for the "Sub Folder Path" folder.
        Dim SubFolderPath As String = Path.Combine(MainFolderPath, "List Dated " & DateTime.Now.ToString("yyyy-MM-dd") & " at " & DateTime.Now.ToString("HH-mm-ss tt"))
        ' Check if the "Email Sorter List" folder exists. If not, create it.
        If Not Directory.Exists(SubFolderPath) Then
            Directory.CreateDirectory(SubFolderPath)
        End If
        Dim Working_list As New List(Of String)
        Dim Fail_List As New List(Of String)
        Dim Untested_list As New List(Of String)
        Dim smtphost_ As String = ""
        Dim emailaddress_ As String = ""
        Dim password_ As String = ""
        Dim port_ As String = ""
        Dim Str_ As String = ""
        For i As Integer = 0 To GridView1.RowCount - 1
            smtphost_ = DT_SMTPTester.Rows(i).Item("smtpserver")
            emailaddress_ = DT_SMTPTester.Rows(i).Item("username")
            password_ = DT_SMTPTester.Rows(i).Item("password")
            port_ = DT_SMTPTester.Rows(i).Item("port")
            Str_ = smtphost_ & "|" & emailaddress_ & "|" & password_ & "|" & port_
            If GridView1.GetRowCellValue(i, "status").ToString() = "Working" Then
                Working_list.Add(Str_.ToString())
            ElseIf GridView1.GetRowCellValue(i, "status").ToString() = "Fail" Then
                Fail_List.Add(Str_.ToString())
            ElseIf GridView1.GetRowCellValue(i, "status").ToString() = "Untested" Then
                Untested_list.Add(Str_.ToString())
            End If
        Next
        If Working_list.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Verified.txt"), Working_list)
        If Fail_List.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Failed.txt"), Fail_List)
        If Untested_list.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Untested.txt"), Untested_list)
        Process.Start("explorer.exe", SubFolderPath)
        DevExpress.XtraEditors.XtraMessageBox.Show("All lists were saved Workingfully...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub



End Class