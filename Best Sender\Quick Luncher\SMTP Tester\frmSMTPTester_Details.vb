﻿Imports DevExpress.XtraEditors
Public Class frmSMTPTester_Details
    Private Sub CheckEdit1_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEdit1.CheckedChanged
        If CheckEdit1.Checked = True Then
            TxtPassword.Properties.PasswordChar = ""
        ElseIf CheckEdit1.Checked = False Then
            TxtPassword.Properties.PasswordChar = "•"
        End If
    End Sub
    Private Sub frmSMTPTester_Details_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        CenterForm(Me, frmSmtpTester)
        DxErrorProvider1.ClearErrors()
    End Sub

    Private Sub TxtSMTPServer_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtUsername_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPassword_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPort_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub

    Private Sub bntSave_Click(sender As Object, e As EventArgs) Handles bntSave.Click
        If TxtSMTPServer.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtSMTPServer, "Invalid SMTP Server name...!")
            TxtSMTPServer.Focus()
            Exit Sub
        End If
        If TxtUsername.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtSMTPServer, "Invalid User Name...!")
            TxtUsername.Focus()
            Exit Sub
        End If
        If TxtPassword.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtSMTPServer, "Invalid User Password...!")
            TxtPassword.Focus()
            Exit Sub
        End If
        If TxtPort.Text.Trim = "" Then
            DxErrorProvider1.SetError(TxtSMTPServer, "Invalid Port Number...!")
            TxtPort.Focus()
            Exit Sub
        End If
        If (TxtSMTPServer.Text.Trim <> Trim(TxtSMTPServer.Tag)) And TxtUsername.Text.Trim <> Trim(TxtUsername.Tag) Then
            For Each row As DataRow In DT_SMTPTester.Rows
                If row("smtpserver").Equals(TxtSMTPServer.Text.Trim) AndAlso row("username").Equals(TxtUsername.Text.Trim) Then
                    DevExpress.XtraEditors.XtraMessageBox.Show("The SMTP Server and user name already existed...", "warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Exit Sub
                End If
            Next
        End If
        Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to save the current changes?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            ' Set the ID column as the primary key
            DT_SMTPTester.PrimaryKey = New DataColumn() {DT_SMTPTester.Columns("ID")}
            Dim foundRow As DataRow = DT_SMTPTester.Rows.Find(Me.Tag)
            ' Update the values in the found row
            foundRow("smtpserver") = TxtSMTPServer.Text.Trim
            foundRow("username") = TxtUsername.Text.Trim
            foundRow("password") = TxtPassword.Text.Trim
            foundRow("port") = TxtPort.Text.Trim
            '=================================================================
            ' Refresh Smtp gird
            frmSmtpTester.GridControl1.DataSource = DT_SMTPTester
            DevExpress.XtraEditors.XtraMessageBox.Show("Account info Successfully updated...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Me.Dispose()
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class