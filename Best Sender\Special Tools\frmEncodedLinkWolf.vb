﻿Imports System.IO
Imports System.Text
Imports System.Drawing
Imports System.Windows.Forms
Imports System.Net
Imports System.Diagnostics
Public Class frmEncodedLinkWolf
    Private IsDragging As Boolean = False
    Private MouseOffset As Point
    Private Const VBQuote As String = """"
    Private HttpUtility As Object
    Private Response As Object
    Private htmlCode As String
    Private Function HtmlEncode(ByVal text As String) As String
        Return WebUtility.HtmlEncode(text)
    End Function
    Private Sub ConvertFile(ByVal SourceFile As String, ByVal DestFile As String, ByVal Title As String, Optional ByVal DisableClick As Boolean = False)
        Dim sr As StreamReader = Nothing
        Dim sw As StreamWriter = Nothing
        Dim sb As New StringBuilder()
        Dim Buffer As String = vbNullString
        'Faça um backup do nome do arquivo original.
        ' File.Copy(SourceFile, DestFile, True)
        'Read in original file data.
        sr = New StreamReader(SourceFile)
        'Read file contents.
        Buffer = sr.ReadToEnd
        'Close file.
        sr.Close()
        'Check if need to disable right click.
        If DisableClick Then
            Buffer = Buffer & "http://lablanche.ru/bitrix/rk.php?goto=(" & VBQuote & "return false" & VBQuote & ")</script>"
        End If
        'Append header.
        'sb.AppendLine("<html>")
        'sb.AppendLine("<head>")
        'sb.AppendLine("<title>" & Title & "</title>")
        'sb.AppendLine("<body>")
        'Append javascript.
        sb.AppendLine("" & VBQuote & "" & VBQuote & "" & VBQuote & "" & VBQuote & ">")
        sb.Append("http://lablanche.ru/bitrix/rk.php?goto=")
        'Convert string to hex.
        For Each c As Char In Buffer
            sb.Append("%" & Asc(c).ToString("X2"))
        Next c
        'Append end part.
        'sb.Append("'));" & vbCrLf)
        'sb.AppendLine("</script>")
        'sb.AppendLine("</body>")
        'sb.AppendLine("</html>")
        'Write to new file.
        sw = New StreamWriter(DestFile)
        'Write to file.
        sw.Write(sb.ToString())
        'Fechar arquivo.
        sw.Close()
    End Sub
    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        ProgressBarControl1.Increment(1)
        If ProgressBarControl1.Position = ProgressBarControl1.Properties.Maximum Then
            Timer1.Stop()
            If Not File.Exists(txtHttps.Text) Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Input source file not found.", "file not found", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                Exit Sub
                'LogInButton2.Focus()
            Else
                'Criptografe o arquivo.
                ConvertFile(txtHttps.Text, txtLinkEncodedWolf.Text, LogInNormalTextBox3.Text, LogInCheckBox1.Checked)
                'Apresentação de mensagem de acabamento.
                DevExpress.XtraEditors.XtraMessageBox.Show("The File has been Successfully Encrypted!", Text, MessageBoxButtons.OK, MessageBoxIcon.Information)
                'Redefina o sontrols.
                txtHttps.Text = vbNullString
                txtLinkEncodedWolf.Text = vbNullString
                LogInCheckBox1.Checked = False
                'LogInButton2.Focus()
            End If
            DevExpress.XtraEditors.XtraMessageBox.Show("Done Crypter Link", "Link Encode, HTML ", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Me.Close()
        End If
    End Sub

    Private Sub BunifuImageButton2_Click(sender As Object, e As EventArgs)
    End Sub
    Private Sub frmEncodedLinkWolf_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' استرجاع القيم المخزنة
        txtHttps.Text = My.Settings.txtHttps
        txtmyLinkURL.Text = My.Settings.txtmyLinkURL
        txtLinkEncodedWolf.Text = My.Settings.txtLinkEncodedWolf

        ' إضافة معالج حدث تغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmEncodedLinkWolf_Resize

        ' توسيط العناصر عند التحميل
        CenterControlsInForm()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmEncodedLinkWolf_Resize(sender As Object, e As EventArgs)
        ' توسيط العناصر عند تغيير حجم النموذج
        CenterControlsInForm()
    End Sub

    ''' <summary>
    ''' توسيط جميع العناصر في النموذج كمجموعة واحدة
    ''' </summary>
    Private Sub CenterControlsInForm()
        Try
            ' التحقق من أن النموذج مهيأ بشكل كامل
            If Not Me.IsHandleCreated OrElse Me.IsDisposed Then
                Return
            End If

            ' إنشاء مجموعة من العناصر
            Dim mainControls As New List(Of Control)

            ' إضافة العناصر الرئيسية
            mainControls.Add(PictureBox1)
            mainControls.Add(SeparatorControl2)
            mainControls.Add(Label2)
            mainControls.Add(txtHttps)
            mainControls.Add(Label3)
            mainControls.Add(txtmyLinkURL)
            mainControls.Add(Label4)
            mainControls.Add(txtLinkEncodedWolf)

            ' حساب أقصى عرض للعناصر
            Dim maxWidth As Integer = 0
            For Each ctrl As Control In mainControls
                If ctrl.Width > maxWidth Then
                    maxWidth = ctrl.Width
                End If
            Next

            ' حساب المركز الأفقي للنموذج
            Dim centerX As Integer = Me.ClientSize.Width \ 2

            ' حساب نقطة البداية للمجموعة (أقصى يسار)
            Dim startX As Integer = centerX - (maxWidth \ 2)

            ' توسيط العناصر الرئيسية
            PictureBox1.Left = centerX - (PictureBox1.Width \ 2)
            SeparatorControl2.Left = centerX - (SeparatorControl2.Width \ 2)

            ' توسيط التسميات وحقول النص
            Label2.Left = startX
            txtHttps.Left = startX

            Label3.Left = startX
            txtmyLinkURL.Left = startX

            Label4.Left = startX
            txtLinkEncodedWolf.Left = startX

            ' تحديث موقع ComboBox
            cmbEncryptionMode.Left = txtHttps.Right - cmbEncryptionMode.Width

            ' توسيط مجموعة الأزرار
            Dim buttonWidth As Integer = LogInButtonWithProgress1.Width + bntCopy.Width + BntSave.Width + BntClear.Width + 15 * 3 ' المسافة بين الأزرار
            Dim buttonStartX As Integer = centerX - (buttonWidth \ 2)

            LogInButtonWithProgress1.Left = buttonStartX
            bntCopy.Left = LogInButtonWithProgress1.Right + 15
            BntSave.Left = bntCopy.Right + 15
            BntClear.Left = BntSave.Right + 15

            ' تطبيق أسلوب Binance على العناصر
            ApplyBinanceStyle()
        Catch ex As Exception
            ' تجاهل الأخطاء التي قد تحدث أثناء توسيط العناصر
            Debug.WriteLine("Error in CenterControlsInForm: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' تطبيق أسلوب Binance على العناصر
    ''' </summary>
    Private Sub ApplyBinanceStyle()
        Try
            ' لا نقوم بتعيين لون خلفية النموذج
            ' Me.BackColor = Color.FromArgb(24, 26, 32) ' لون خلفية داكن

            ' تطبيق الأسلوب على حقول النص
            For Each control As Control In Me.Controls
                If TypeOf control Is DevExpress.XtraEditors.TextEdit Then
                    Dim textEdit As DevExpress.XtraEditors.TextEdit = DirectCast(control, DevExpress.XtraEditors.TextEdit)

                    ' تعيين ألوان حقل النص
                    textEdit.Properties.Appearance.BackColor = Color.FromArgb(24, 26, 32)
                    textEdit.Properties.Appearance.ForeColor = Color.FromArgb(254, 219, 65)
                    textEdit.Properties.Appearance.BorderColor = Color.FromArgb(96, 96, 96)

                    ' تعيين ألوان حقل النص عند التركيز
                    textEdit.Properties.AppearanceFocused.BackColor = Color.FromArgb(36, 39, 48)
                    textEdit.Properties.AppearanceFocused.ForeColor = Color.FromArgb(254, 219, 65)
                    textEdit.Properties.AppearanceFocused.BorderColor = Color.FromArgb(254, 219, 65)
                End If

                ' تطبيق الأسلوب على التسميات
                If TypeOf control Is Label Then
                    Dim label As Label = DirectCast(control, Label)
                    label.ForeColor = Color.FromArgb(254, 219, 65)
                End If
            Next

            ' تطبيق الأسلوب على SeparatorControl
            If SeparatorControl2 IsNot Nothing Then
                SeparatorControl2.LineColor = Color.FromArgb(254, 219, 65)
                SeparatorControl2.LineThickness = 2
            End If
        Catch ex As Exception
            Debug.WriteLine("Error in ApplyBinanceStyle: " & ex.Message)
        End Try
    End Sub




    Private Sub BntSave_Click(sender As Object, e As EventArgs) Handles BntSave.Click
        If txtHttps.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please add https:// or http://", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            txtHttps.Focus()
            Return
        End If
        If txtmyLinkURL.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Exsample www.Google.com", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            txtmyLinkURL.Focus()
            Return
        End If
        If txtLinkEncodedWolf.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Click Encoded Your Link ", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            txtLinkEncodedWolf.Focus()
            Return
        End If
        Try
            ' الحصول على مسار سطح المكتب
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            ' تحديد مسار المجلد
            Dim folderPath As String = Path.Combine(desktopPath, "Encode Link")
            ' إنشاء المجلد إذا لم يكن موجودًا
            If Not Directory.Exists(folderPath) Then
                Directory.CreateDirectory(folderPath)
            End If
            ' توليد اسم الملف مع التاريخ والوقت
            Dim fileName As String = "EncodedLink_" & DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss") & ".txt"
            Dim filePath As String = Path.Combine(folderPath, fileName)
            ' حفظ النص داخل الملف
            File.WriteAllText(filePath, txtLinkEncodedWolf.Text, Encoding.UTF8)
            ' فتح المجلد بعد الحفظ مباشرة
            Process.Start("explorer.exe", folderPath)
            My.Settings.txtHttps = txtHttps.Text
            My.Settings.txtmyLinkURL = txtmyLinkURL.Text
            My.Settings.txtLinkEncodedWolf = txtLinkEncodedWolf.Text
            ' حفظ التغييرات
            My.Settings.Save()
            ' إبلاغ المستخدم بنجاح العملية
            DevExpress.XtraEditors.XtraMessageBox.Show("The file has been saved successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("An error occurred while saving the file: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LogInButtonWithProgress1_Click(sender As Object, e As EventArgs) Handles LogInButtonWithProgress1.Click
        If txtHttps.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please add https:// or http://", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            txtHttps.Focus()
            Return
        End If
        If txtmyLinkURL.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Exsample www.Google.com", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            txtmyLinkURL.Focus()
            Return
        End If
        ' تحديد طريقة التشفير المختارة
        If cmbEncryptionMode.SelectedIndex = 0 Then ' Encryption Method 1
            Dim sb As New StringBuilder()
            Dim Buffer As String = vbNullString
            Buffer = txtHttps.Text
            ' تحويل النص إلى Hex
            For Each c As Char In txtmyLinkURL.Text
                sb.Append("%" & Asc(c).ToString("X2"))
            Next c
            ' حفظ الناتج في textbox3
            txtLinkEncodedWolf.Text = txtHttps.Text & sb.ToString()
        ElseIf cmbEncryptionMode.SelectedIndex = 1 Then ' Encryption Method 2
            ' دمج النصوص بدون تشفير
            txtLinkEncodedWolf.Text = txtHttps.Text & txtmyLinkURL.Text
        End If
        ' إضافة العلامة النهائية
        txtLinkEncodedWolf.Text &= "#[-RN7-][-RN7-]&amp;{}{}{}[-RCh8-]{?@#$^&amp;&amp;!!!!~~!![-RCh8-]!@!!~!!!!!@@@#[-RCh8-]##$$%%^&amp;&amp;()()***&amp;&amp;^^[-RCh8-][-RN7-][-RN7-]&amp;{}{}{}[-RCh8-]{?@#$^&amp;&amp;!!!!~~!![-RCh8-]!@!!~!!!!!@@@#[-RCh8-]##$$%%^&amp;&amp;()()***&amp;&amp;^^[-RCh8-][-RN7-][-RN7-]&amp;{}{}{}[-RCh8-]{?@#$^&amp;&amp;!!!!~~!![-RCh8-]!@!!~!!!!!@@@#[-RCh8-]##$$%%^&amp;&amp;()()***&amp;&amp;^^[-RCh8-][-RN7-][-RN7-]&amp;{}{}{}[-RCh8-]{?@#$^&amp;&amp;!!!!~~!![-RCh8-]!@!!~!!!!!@@@#[-RCh8-]##$$%%^&amp;&amp;()()***&amp;&amp;^^[-RCh8-][-RN7-][-RN7-]&amp;{}{}{}[-RCh8-]{?@#$^&amp;&amp;!!!!~~!![-RCh8-]!@!!~!!!!!@@@#[-RCh8-]##$$%%^&amp;&amp;()()***&amp;&amp;^^[-RCh8-][-RN7-][-RN7-]&amp;{}{}{}[-RCh8-]{?@#$^&amp;&amp;!!!!~~!![-RCh8-]!@!!~!!!!!@@@#[-RCh8-]##$$%%^&amp;&amp;()()***&amp;&amp;^^[-RCh8-][-RN7-][-RN7-]&amp;{}{}{}[-RCh8-]{?@#$^&amp;&amp;!!!!~~!![-RCh8-]!@!!~!!!!!@@@#[-RCh8-]##$$%%^&amp;&amp;()()***&amp;&amp;^^[-RCh8-][-RN7-][-RN7-]&amp;{}{}{}[-RCh8-]{?@#$^&amp;&amp;!!!!~~!![-RCh8-]!@!!~!!!!!@@@#[-RCh8-]##$$%%^&amp;&amp;()()***&amp;&amp;^^[-RCh8-][-RN7-][-RN7-]&amp;{}{}{}[-RCh8-]{?@#$^&amp;&amp;!!!!~~!![-RCh8-]!@!!~!!!!!@@@#[-RCh8-]##$$%%^&amp;&amp;()()***&amp;&amp;^^[-RCh8-][-RN7-][-RN7-]&amp;{}{}{}[-RCh8-]{?@#$^&amp;&amp;!!!!~~!![-RCh8-]!@!!~!!!!!@@@#[-RCh8-]##$$%%^&amp;&amp;()()***&amp;&amp;^^[-RCh8-][-RN7-][-RN7-]&amp;{}{}{}[-RCh8-]{?@#$^&amp;&amp;!!!!~~!![-RCh8-]!@!!~!!!!!@@@#[-RCh8-]##$$%%^&amp;&amp;()()***&amp;&amp;^^[-RCh8-]"
        txtLinkEncodedWolf.SelectAll()
    End Sub

    Private Sub bntCopy_Click(sender As Object, e As EventArgs) Handles bntCopy.Click
        If txtLinkEncodedWolf.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Are You Sure you put your link First? ", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            txtLinkEncodedWolf.Focus()
            Return
        End If
        Clipboard.SetText(txtLinkEncodedWolf.Text)
    End Sub

    Private Sub BntClear_Click(sender As Object, e As EventArgs) Handles BntClear.Click
        Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show(
       "Do you want to delete all data?",  ' Message
       "Confirm Delete",                   ' Title
       MessageBoxButtons.YesNo,            ' Buttons
       MessageBoxIcon.Question)            ' Icon
        ' If user clicked Yes, clear the text boxes
        If result = DialogResult.Yes Then
            txtHttps.Clear()
            txtmyLinkURL.Clear()
            txtLinkEncodedWolf.Clear()
            ' Optional: Show confirmation message
            DevExpress.XtraEditors.XtraMessageBox.Show("Data has been cleared successfully.",
                                                  "Success",
                                                  MessageBoxButtons.OK,
                                                  MessageBoxIcon.Information)
        End If
    End Sub
End Class