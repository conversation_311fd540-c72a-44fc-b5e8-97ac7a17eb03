﻿Imports System.IO
Imports DevExpress.XtraEditors
Imports System.Drawing
Public Class frmLetterMakerST
    ' إضافة ColorDialog للألوان
    Private ButtonColorDialog As New ColorDialog()
    Private TextColorDialog As New ColorDialog()
    Private TitleColorDialog As New ColorDialog()

    ' متغيرات لتخزين الألوان المختارة
    Private SelectedButtonColor As Color = ColorTranslator.FromHtml("#007BFF") ' اللون الافتراضي للزر
    Private SelectedTextColor As Color = ColorTranslator.FromHtml("#DC3545") ' اللون الافتراضي للنص
    Private SelectedTitleColor As Color = ColorTranslator.FromHtml("#000000") ' اللون الافتراضي للعنوان
    Private Sub frmLetterMakerST_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' إعداد خصائص SpinEdit
        fonttitel.Properties.MinValue = 10
        fonttitel.Properties.MaxValue = 36
        fonttitel.Properties.AllowMouseWheel = True
        fonttitel.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False
        fonttitel.Properties.AllowFocused = True
        fonttitel.Properties.ReadOnly = False
        fonttitel.Value = 25 ' القيمة الافتراضية للعنوان
        FontSizeMessage.Properties.MinValue = 10
        FontSizeMessage.Properties.MaxValue = 36
        FontSizeMessage.Properties.AllowMouseWheel = True
        FontSizeMessage.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False
        FontSizeMessage.Properties.AllowFocused = True
        FontSizeMessage.Properties.ReadOnly = False
        FontSizeMessage.Value = 18 ' القيمة الافتراضية للنص
        ' إعدادات المحاذاة
        TextBoxMessage.TextAlign = HorizontalAlignment.Center
        LeftAlignPictureBox.BorderStyle = BorderStyle.None
        CenterAlignPictureBox.BorderStyle = BorderStyle.Fixed3D
        RightAlignPictureBox.BorderStyle = BorderStyle.None

        ' إعداد ColorDialog
        ButtonColorDialog.AnyColor = True
        ButtonColorDialog.FullOpen = True
        TextColorDialog.AnyColor = True
        TextColorDialog.FullOpen = True
        TitleColorDialog.AnyColor = True
        TitleColorDialog.FullOpen = True

        ' تعيين الألوان الافتراضية
        ComboBox_ButtonColor.Text = ColorTranslator.ToHtml(SelectedButtonColor)
        ComboBox_TextColor.Text = ColorTranslator.ToHtml(SelectedTextColor)
        ComboBox_TitleColor.Text = ColorTranslator.ToHtml(SelectedTitleColor)

        ' إضافة معالجات الأحداث لأزرار اختيار اللون
        AddHandler ComboBox_ButtonColor.Click, AddressOf ComboBox_ButtonColor_Click
        AddHandler ComboBox_TextColor.Click, AddressOf ComboBox_TextColor_Click
        AddHandler ComboBox_TitleColor.Click, AddressOf ComboBox_TitleColor_Click
        ' استرجاع الإعدادات المحفوظة
        If Not String.IsNullOrEmpty(My.Settings.Title) Then TextBoxTitle.Text = My.Settings.Title
        If Not String.IsNullOrEmpty(My.Settings.TextBoxLogoLM) Then TextBoxLogoLM.Text = My.Settings.TextBoxLogoLM
        ' استرجاع حجم الخط مع التحقق من الصحة
        Dim fontSize As Integer
        If Integer.TryParse(My.Settings.FontSizeMessage, fontSize) AndAlso fontSize >= 10 AndAlso fontSize <= 36 Then
            FontSizeMessage.Value = fontSize
        Else
            FontSizeMessage.Value = 18
        End If
        Dim titleFontSize As Integer
        If Integer.TryParse(My.Settings.FontTitle, titleFontSize) AndAlso titleFontSize >= 10 AndAlso titleFontSize <= 36 Then
            fonttitel.Value = titleFontSize
        Else
            fonttitel.Value = 25
        End If
        ' استرجاع الألوان
        If Not String.IsNullOrEmpty(My.Settings.ButtonColor) Then ComboBox_ButtonColor.SelectedItem = My.Settings.ButtonColor
        If Not String.IsNullOrEmpty(My.Settings.TextColor) Then ComboBox_TextColor.SelectedItem = My.Settings.TextColor
        If Not String.IsNullOrEmpty(My.Settings.TitleColor) Then ComboBox_TitleColor.SelectedItem = My.Settings.TitleColor
        ' استرجاع النصوص
        If Not String.IsNullOrEmpty(My.Settings.Message) Then TextBoxMessage.Text = My.Settings.Message
        If Not String.IsNullOrEmpty(My.Settings.ButtonName) Then TextButtonname.Text = My.Settings.ButtonName
        If Not String.IsNullOrEmpty(My.Settings.TextBoxLinklk) Then TextBoxLinklk.Text = My.Settings.TextBoxLinklk
        If Not String.IsNullOrEmpty(My.Settings.Signature) Then TextBoxSignature.Text = My.Settings.Signature
        ' تحديد القيم الافتراضية إذا كانت فارغة
        If String.IsNullOrEmpty(TextBoxTitle.Text) Then TextBoxTitle.Text = "Office365"
        If String.IsNullOrEmpty(TextBoxMessage.Text) Then TextBoxMessage.Text = "Dear [-Email-]," & vbCrLf & "We are pleased to inform you about the latest HR updates and policies."
        If String.IsNullOrEmpty(TextBoxSignature.Text) Then TextBoxSignature.Text = "Best Regards," & vbCrLf & "Team BSV"
        If String.IsNullOrEmpty(TextBoxLogoLM.Text) Then TextBoxLogoLM.Text = "https://uhf.microsoft.com/images/microsoft/RE1Mu3b.png"
        If String.IsNullOrEmpty(TextButtonname.Text) Then TextButtonname.Text = "Click Here"
        If String.IsNullOrEmpty(TextBoxLinklk.Text) Then TextBoxLinklk.Text = "https://www.Example.com"
        ' تحديث المعاينة
        GenerateHTML()
    End Sub
    Private Sub GenerateHTML()
        Try
            ' تنظيف WebBrowser قبل التحديث
            WebBrowserPreview.Navigate("about:blank")
            If WebBrowserPreview.Document IsNot Nothing Then
                WebBrowserPreview.Document.Write(String.Empty)
            End If

            ' استخدام الألوان المختارة مباشرة
            Dim buttonColor As String = ColorTranslator.ToHtml(SelectedButtonColor)
            Dim textColor As String = ColorTranslator.ToHtml(SelectedTextColor)
            Dim titleColor As String = ColorTranslator.ToHtml(SelectedTitleColor)
            ' الحصول على القيم المدخلة
            Dim userLink As String = If(String.IsNullOrEmpty(TextBoxLinklk.Text.Trim()), "https://www.Example.com", TextBoxLinklk.Text.Trim())
            Dim userTitle As String = If(String.IsNullOrEmpty(TextBoxTitle.Text.Trim()), "Office 365", TextBoxTitle.Text.Trim())
            Dim userMessage As String = TextBoxMessage.Text.Replace(vbCrLf, "<br>")
            Dim userSignature As String = TextBoxSignature.Text.Replace(vbCrLf, "<br>")
            Dim logoURL As String = If(String.IsNullOrEmpty(TextBoxLogoLM.Text.Trim()), "https://uhf.microsoft.com/images/microsoft/RE1Mu3b.png", TextBoxLogoLM.Text.Trim())
            Dim buttonText As String = If(String.IsNullOrEmpty(TextButtonname.Text.Trim()), "Click Here", TextButtonname.Text.Trim())
            ' أحجام الخطوط
            Dim titleFontSize As Integer = CInt(fonttitel.Value)
            Dim messageFontSize As Integer = CInt(FontSizeMessage.Value)
            ' نمط النص
            Dim textStyle As String = If(CheckBoxBoldText.Checked, "font-weight: bold;", "")
            ' تحديد المحاذاة
            Dim textAlign As String = "left"
            If TextBoxMessage.TextAlign = HorizontalAlignment.Center Then
                textAlign = "center"
            ElseIf TextBoxMessage.TextAlign = HorizontalAlignment.Right Then
                textAlign = "right"
            End If
            ' إنشاء كود HTML
            Dim htmlCode As String = "<html><body style='background-color:#f3f3f3; padding:20px;'>" &
                "<table align='" & textAlign & "' width='600' style='background:white; padding:20px; border-collapse:collapse;'>" &
                "<tr><td align='" & textAlign & "'><img src='" & logoURL & "' alt='Logo'></td></tr>" &
                "<tr><td style='font-size:" & titleFontSize & "px; font-weight:bold; color:" & titleColor & "; text-align:" & textAlign & ";'>" & userTitle & "</td></tr>" &
                "<tr><td style='font-size:" & messageFontSize & "px; color:" & textColor & "; " & textStyle & "; text-align:" & textAlign & ";'>" & userMessage & "</td></tr>" &
                "<tr><td align='" & textAlign & "' style='padding-top:20px;'>" &
                "<a href='" & userLink & "' style='background:" & buttonColor & "; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; display:inline-block;'>" & buttonText & "</a>" &
                "</td></tr>" &
                "<tr><td style='font-size:14px; color:gray; text-align:" & textAlign & "; padding-top:20px;'>" & userSignature & "</td></tr>" &
                "</table></body></html>"
            ' عرض المعاينة
            WebBrowserPreview.DocumentText = htmlCode
        Catch ex As Exception
            XtraMessageBox.Show("An error occurred: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    ' أحداث تغيير القيم
    Private Sub AnyInputChanged(sender As Object, e As EventArgs) Handles TextBoxMessage.TextChanged,
                                                                        TextBoxLogoLM.TextChanged,
                                                                        TextButtonname.TextChanged,
                                                                        TextBoxSignature.TextChanged,
                                                                        TextBoxLinklk.TextChanged,
                                                                        TextBoxTitle.TextChanged,
                                                                        CheckBoxBoldText.CheckedChanged
        GenerateHTML()
    End Sub
    ' أحداث SpinEdit
    Private Sub fonttitel_EditValueChanged(sender As Object, e As EventArgs) Handles fonttitel.EditValueChanged
        GenerateHTML()
    End Sub
    Private Sub FontSizeMessage_EditValueChanged(sender As Object, e As EventArgs) Handles FontSizeMessage.EditValueChanged
        GenerateHTML()
    End Sub
    ' التحكم في إدخال الأرقام فقط في SpinEdit
    Private Sub SpinEdit_KeyPress(sender As Object, e As KeyPressEventArgs) Handles fonttitel.KeyPress, FontSizeMessage.KeyPress
        If Not Char.IsDigit(e.KeyChar) AndAlso Not Char.IsControl(e.KeyChar) Then
            e.Handled = True
        End If
    End Sub
    ' أحداث المحاذاة
    ' أحداث الأزرار
    Private Sub BntReset_Click(sender As Object, e As EventArgs) Handles BntReset.Click
        Dim result As DialogResult = XtraMessageBox.Show(
            "Are you sure you want to reset all settings?",
            "Confirm Reset",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Warning)
        If result = DialogResult.Yes Then
            TextBoxTitle.Clear()
            TextBoxLogoLM.Clear()

            ' إعادة تعيين الألوان الافتراضية
            SelectedButtonColor = ColorTranslator.FromHtml("#007BFF")
            SelectedTextColor = ColorTranslator.FromHtml("#DC3545")
            SelectedTitleColor = ColorTranslator.FromHtml("#000000")

            ' تحديث نص ComboBox
            ComboBox_ButtonColor.Text = ColorTranslator.ToHtml(SelectedButtonColor)
            ComboBox_TextColor.Text = ColorTranslator.ToHtml(SelectedTextColor)
            ComboBox_TitleColor.Text = ColorTranslator.ToHtml(SelectedTitleColor)

            TextBoxMessage.Clear()
            TextButtonname.Clear()
            TextBoxLinklk.Clear()
            TextBoxSignature.Clear()
            fonttitel.Value = 25
            FontSizeMessage.Value = 18
            GenerateHTML()
        End If
    End Sub
    Private Sub BNT_Backup_Click(sender As Object, e As EventArgs) Handles BNT_Backup.Click
        Dim result As DialogResult = XtraMessageBox.Show(
            "Do you want to Restore the Settings to Default?",
            "Confirm Reset",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Warning)
        If result = DialogResult.Yes Then
            ' إعادة تعيين القيم الافتراضية
            TextBoxTitle.Text = "Office365"
            TextBoxMessage.Text = "Dear [-Email-]," & vbCrLf & "We are pleased to inform you about the latest HR updates and policies."
            TextBoxSignature.Text = "Best Regards," & vbCrLf & "Team BSV"
            TextBoxLogoLM.Text = "https://uhf.microsoft.com/images/microsoft/RE1Mu3b.png"
            TextButtonname.Text = "Click Here"
            TextBoxLinklk.Text = "https://www.Example.com"
            fonttitel.Value = 25
            FontSizeMessage.Value = 18

            ' إعادة تعيين الألوان الافتراضية
            SelectedButtonColor = ColorTranslator.FromHtml("#007BFF") ' أزرق
            SelectedTextColor = ColorTranslator.FromHtml("#DC3545") ' أحمر
            SelectedTitleColor = ColorTranslator.FromHtml("#000000") ' أسود

            ' تحديث نص ComboBox
            ComboBox_ButtonColor.Text = ColorTranslator.ToHtml(SelectedButtonColor)
            ComboBox_TextColor.Text = ColorTranslator.ToHtml(SelectedTextColor)
            ComboBox_TitleColor.Text = ColorTranslator.ToHtml(SelectedTitleColor)

            TextBoxMessage.TextAlign = HorizontalAlignment.Center
            LeftAlignPictureBox.BorderStyle = BorderStyle.None
            CenterAlignPictureBox.BorderStyle = BorderStyle.Fixed3D
            RightAlignPictureBox.BorderStyle = BorderStyle.None
            GenerateHTML()
        End If
    End Sub
    Private Sub ButtonSaveHTML_Click(sender As Object, e As EventArgs) Handles ButtonSaveHTML.Click
        Try
            ' مسار سطح المكتب + مجلد "Letter Maker"
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim saveFolder As String = Path.Combine(desktopPath, "Letter Maker")
            ' إنشاء المجلد إذا لم يكن موجوداً
            If Not Directory.Exists(saveFolder) Then
                Directory.CreateDirectory(saveFolder)
            End If
            ' اسم الملف الافتراضي مع التاريخ والوقت لتجنب التكرار
            Dim defaultFileName As String = "Letter_" & DateTime.Now.ToString("yyyyMMdd_HHmmss") & ".html"
            Dim filePath As String = Path.Combine(saveFolder, defaultFileName)
            ' عرض مربع الحفظ للمستخدم
            Dim saveFileDialog As New SaveFileDialog()
            saveFileDialog.Filter = "HTML Files|*.html"
            saveFileDialog.Title = "Save as HTML File"
            saveFileDialog.InitialDirectory = saveFolder
            saveFileDialog.FileName = defaultFileName
            If saveFileDialog.ShowDialog() = DialogResult.OK Then
                ' الحصول على المسار الذي اختاره المستخدم
                filePath = saveFileDialog.FileName
                ' التأكد من أن WebBrowser يحتوي على محتوى
                If String.IsNullOrEmpty(WebBrowserPreview.DocumentText) Then
                    GenerateHTML() ' إنشاء المحتوى إذا كان فارغاً
                End If
                ' حفظ المحتوى في الملف
                File.WriteAllText(filePath, WebBrowserPreview.DocumentText, System.Text.Encoding.UTF8)
                ' حفظ الإعدادات
                My.Settings.Title = TextBoxTitle.Text
                My.Settings.TextBoxLogoLM = TextBoxLogoLM.Text
                My.Settings.FontSizeMessage = CInt(FontSizeMessage.Text)
                My.Settings.FontTitle = fonttitel.Text
                My.Settings.ButtonColor = ColorTranslator.ToHtml(SelectedButtonColor)
                My.Settings.TextColor = ColorTranslator.ToHtml(SelectedTextColor)
                My.Settings.TitleColor = ColorTranslator.ToHtml(SelectedTitleColor)
                My.Settings.Message = TextBoxMessage.Text
                My.Settings.ButtonName = TextButtonname.Text
                My.Settings.TextBoxLinklk = TextBoxLinklk.Text
                My.Settings.Signature = TextBoxSignature.Text
                My.Settings.Save()
                XtraMessageBox.Show("File saved successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Process.Start("explorer.exe", saveFolder)
            End If
        Catch ex As Exception
            XtraMessageBox.Show("An error occurred while saving: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    ' أحداث عرض التلميحات
    Private Sub Control_MouseHover(sender As Object, e As EventArgs) Handles fonttitel.MouseHover,
                                                                            FontSizeMessage.MouseHover,
                                                                            TextBoxLinklk.MouseHover,
                                                                            TextBoxLogoLM.MouseHover,
                                                                            TextButtonname.MouseHover,
                                                                            TextBoxSignature.MouseHover,
                                                                            TextBoxTitle.MouseHover,
                                                                            ButtonSaveHTML.MouseHover,
                                                                            BNT_Backup.MouseHover,
                                                                            BntReset.MouseHover,
                                                                            TextBoxMessage.MouseHover,
                                                                            ComboBox_ButtonColor.MouseHover,
                                                                            ComboBox_TitleColor.MouseHover,
                                                                            ComboBox_TextColor.MouseHover
        Dim ctrl As Control = DirectCast(sender, Control)
        Select Case ctrl.Name
            Case "fonttitel" : Label1.Text = "Title Font Size"
            Case "FontSizeMessage" : Label1.Text = "Font Size Message"
            Case "TextBoxLinklk" : Label1.Text = "Enter Link in Button"
            Case "TextBoxLogoLM" : Label1.Text = "Enter Link Logo"
            Case "TextButtonname" : Label1.Text = "Button Text Name"
            Case "TextBoxSignature" : Label1.Text = "Best Regards, Team"
            Case "TextBoxTitle" : Label1.Text = "Enter Title"
            Case "ButtonSaveHTML" : Label1.Text = "Save Letter"
            Case "BNT_Backup", "BntReset" : Label1.Text = "Reset All Settings"
            Case "TextBoxMessage" : Label1.Text = "Write Letter"
            Case "ComboBox_ButtonColor" : Label1.Text = "Button Color"
            Case "ComboBox_TitleColor" : Label1.Text = "Title Color"
            Case "ComboBox_TextColor" : Label1.Text = "Text Color"
        End Select
        Label1.Visible = True
    End Sub
    Private Sub Control_MouseLeave(sender As Object, e As EventArgs) Handles fonttitel.MouseLeave,
                                                                            FontSizeMessage.MouseLeave,
                                                                            TextBoxLinklk.MouseLeave,
                                                                            TextBoxLogoLM.MouseLeave,
                                                                            TextButtonname.MouseLeave,
                                                                            TextBoxSignature.MouseLeave,
                                                                            TextBoxTitle.MouseLeave,
                                                                            ButtonSaveHTML.MouseLeave,
                                                                            BNT_Backup.MouseLeave,
                                                                            BntReset.MouseLeave,
                                                                            TextBoxMessage.MouseLeave,
                                                                            ComboBox_ButtonColor.MouseLeave,
                                                                            ComboBox_TitleColor.MouseLeave,
                                                                            ComboBox_TextColor.MouseLeave
        Label1.Visible = False
    End Sub
    Private Sub LeftAlignPictureBox_Click(sender As Object, e As EventArgs) Handles LeftAlignPictureBox.Click
        TextBoxMessage.TextAlign = HorizontalAlignment.Left
        LeftAlignPictureBox.BorderStyle = BorderStyle.Fixed3D
        CenterAlignPictureBox.BorderStyle = BorderStyle.None
        RightAlignPictureBox.BorderStyle = BorderStyle.None
        GenerateHTML()
    End Sub
    Private Sub CenterAlignPictureBox_Click(sender As Object, e As EventArgs) Handles CenterAlignPictureBox.Click
        TextBoxMessage.TextAlign = HorizontalAlignment.Center
        LeftAlignPictureBox.BorderStyle = BorderStyle.None
        CenterAlignPictureBox.BorderStyle = BorderStyle.Fixed3D
        RightAlignPictureBox.BorderStyle = BorderStyle.None
        GenerateHTML()
    End Sub
    Private Sub RightAlignPictureBox_Click(sender As Object, e As EventArgs) Handles RightAlignPictureBox.Click
        TextBoxMessage.TextAlign = HorizontalAlignment.Right
        LeftAlignPictureBox.BorderStyle = BorderStyle.None
        CenterAlignPictureBox.BorderStyle = BorderStyle.None
        RightAlignPictureBox.BorderStyle = BorderStyle.Fixed3D
        GenerateHTML()
    End Sub
    Private Sub cmbSites_SelectedIndexChanged(sender As Object, e As EventArgs)
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر اختيار لون الزر
    ''' </summary>
    Private Sub ComboBox_ButtonColor_Click(sender As Object, e As EventArgs)
        ' تعيين اللون الحالي في مربع الحوار
        ButtonColorDialog.Color = SelectedButtonColor

        ' عرض مربع حوار اختيار اللون
        If ButtonColorDialog.ShowDialog() = DialogResult.OK Then
            ' تحديث اللون المختار
            SelectedButtonColor = ButtonColorDialog.Color

            ' تحديث نص ComboBox ليعرض قيمة اللون بتنسيق Hex
            ComboBox_ButtonColor.Text = ColorTranslator.ToHtml(SelectedButtonColor)

            ' تحديث المعاينة
            GenerateHTML()
        End If
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر اختيار لون النص
    ''' </summary>
    Private Sub ComboBox_TextColor_Click(sender As Object, e As EventArgs)
        ' تعيين اللون الحالي في مربع الحوار
        TextColorDialog.Color = SelectedTextColor

        ' عرض مربع حوار اختيار اللون
        If TextColorDialog.ShowDialog() = DialogResult.OK Then
            ' تحديث اللون المختار
            SelectedTextColor = TextColorDialog.Color

            ' تحديث نص ComboBox ليعرض قيمة اللون بتنسيق Hex
            ComboBox_TextColor.Text = ColorTranslator.ToHtml(SelectedTextColor)

            ' تحديث المعاينة
            GenerateHTML()
        End If
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر اختيار لون العنوان
    ''' </summary>
    Private Sub ComboBox_TitleColor_Click(sender As Object, e As EventArgs)
        ' تعيين اللون الحالي في مربع الحوار
        TitleColorDialog.Color = SelectedTitleColor

        ' عرض مربع حوار اختيار اللون
        If TitleColorDialog.ShowDialog() = DialogResult.OK Then
            ' تحديث اللون المختار
            SelectedTitleColor = TitleColorDialog.Color

            ' تحديث نص ComboBox ليعرض قيمة اللون بتنسيق Hex
            ComboBox_TitleColor.Text = ColorTranslator.ToHtml(SelectedTitleColor)

            ' تحديث المعاينة
            GenerateHTML()
        End If
    End Sub
End Class