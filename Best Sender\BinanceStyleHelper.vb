Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.Utils

''' <summary>
''' فئة مساعدة لتطبيق تصميم Binance على جميع عناصر GridControl
''' </summary>
Public Module BinanceStyleHelper
    ''' <summary>
    ''' تطبيق تصميم Binance على GridControl
    ''' </summary>
    ''' <param name="gridControl">GridControl المراد تطبيق التصميم عليه</param>
    ''' <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
    Public Sub ApplyBinanceStyleToGrid(gridControl As GridControl, Optional useWXITheme As Boolean = True)
        If gridControl Is Nothing Then Return
        
        ' تطبيق تصميم Binance باستخدام الفئة الموجودة
        BinanceGridStyle.ApplyBinanceStyle(gridControl, useWXITheme)
    End Sub
    
    ''' <summary>
    ''' تطبيق تصميم Binance على جميع عناصر GridControl في النموذج
    ''' </summary>
    ''' <param name="form">النموذج المراد تطبيق التصميم على عناصر GridControl فيه</param>
    ''' <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
    Public Sub ApplyBinanceStyleToAllGridsInForm(form As Form, Optional useWXITheme As Boolean = True)
        If form Is Nothing Then Return
        
        ' البحث عن جميع عناصر GridControl في النموذج وتطبيق التصميم عليها
        For Each control As Control In form.Controls
            If TypeOf control Is GridControl Then
                ApplyBinanceStyleToGrid(DirectCast(control, GridControl), useWXITheme)
            End If
            
            ' البحث في العناصر المتداخلة
            FindAndApplyStyleToGrids(control, useWXITheme)
        Next
    End Sub
    
    ''' <summary>
    ''' البحث عن عناصر GridControl في العناصر المتداخلة وتطبيق التصميم عليها
    ''' </summary>
    Private Sub FindAndApplyStyleToGrids(parentControl As Control, useWXITheme As Boolean)
        For Each control As Control In parentControl.Controls
            If TypeOf control Is GridControl Then
                ApplyBinanceStyleToGrid(DirectCast(control, GridControl), useWXITheme)
            End If
            
            ' البحث في العناصر المتداخلة بشكل متكرر
            If control.Controls.Count > 0 Then
                FindAndApplyStyleToGrids(control, useWXITheme)
            End If
        Next
    End Sub
End Module
