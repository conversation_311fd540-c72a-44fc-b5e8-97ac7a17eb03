﻿Imports Chilkat
Imports System
Imports System.Collections.Generic
Imports System.Collections.ObjectModel
Imports System.ComponentModel
Imports System.Linq
Friend Class DataModel
    Implements INotifyPropertyChanged
    Private _client As MailMan
    Public Const clientPropertyName As String = "client"
    Public Property client As MailMan
        Get
            Return _client
        End Get
        Set(ByVal value As MailMan)
            If _client IsNot value Then
                _client = value
                OnPropertyChanged(clientPropertyName)
            End If
        End Set
    End Property
    Private _sortedMails As ObservableCollection(Of String)
    Public Const sortedMailsPropertyName As String = "sortedMails"
    Public Property sortedMails As ObservableCollection(Of String)
        Get
            Return _sortedMails
        End Get
        Set(ByVal value As ObservableCollection(Of String))
            If _sortedMails IsNot value Then
                _sortedMails = value
                OnPropertyChanged(sortedMailsPropertyName)
            End If
        End Set
    End Property
    Private _sortedMail1 As ObservableCollection(Of String)
    Public Const sortedMail1PropertyName As String = "sortedMail1"
    Public Property sortedMail1 As ObservableCollection(Of String)
        Get
            Return _sortedMail1
        End Get
        Set(ByVal value As ObservableCollection(Of String))
            If _sortedMail1 IsNot value Then
                _sortedMail1 = value
                OnPropertyChanged(sortedMail1PropertyName)
            End If
        End Set
    End Property
    Private _sortedMail2 As ObservableCollection(Of String)
    Public Const sortedMail2PropertyName As String = "sortedMail2"
    Public Property sortedMail2 As ObservableCollection(Of String)
        Get
            Return _sortedMail2
        End Get
        Set(ByVal value As ObservableCollection(Of String))
            If _sortedMail2 IsNot value Then
                _sortedMail2 = value
                OnPropertyChanged(sortedMail2PropertyName)
            End If
        End Set
    End Property
    Private _sortedMail3 As ObservableCollection(Of String)
    Public Const sortedMail3PropertyName As String = "sortedMail3"
    Public Property sortedMail3 As ObservableCollection(Of String)
        Get
            Return _sortedMail3
        End Get
        Set(ByVal value As ObservableCollection(Of String))
            If _sortedMail3 IsNot value Then
                _sortedMail3 = value
                OnPropertyChanged(sortedMail3PropertyName)
            End If
        End Set
    End Property
    Private _sortedMail4 As ObservableCollection(Of String)
    Public Const sortedMail4PropertyName As String = "sortedMail4"
    Public Property sortedMail4 As ObservableCollection(Of String)
        Get
            Return _sortedMail4
        End Get
        Set(ByVal value As ObservableCollection(Of String))
            If _sortedMail4 IsNot value Then
                _sortedMail4 = value
                OnPropertyChanged(sortedMail4PropertyName)
            End If
        End Set
    End Property
    Private _sortedMail5 As ObservableCollection(Of String)
    Public Const sortedMail5PropertyName As String = "sortedMail5"
    Public Property sortedMail5 As ObservableCollection(Of String)
        Get
            Return _sortedMail5
        End Get
        Set(ByVal value As ObservableCollection(Of String))
            If _sortedMail5 IsNot value Then
                _sortedMail5 = value
                OnPropertyChanged(sortedMail5PropertyName)
            End If
        End Set
    End Property
    Private _sortedMail6 As ObservableCollection(Of String)
    Public Const sortedMail6PropertyName As String = "sortedMail6"
    Public Property sortedMail6 As ObservableCollection(Of String)
        Get
            Return _sortedMail6
        End Get
        Set(ByVal value As ObservableCollection(Of String))
            If _sortedMail6 IsNot value Then
                _sortedMail6 = value
                OnPropertyChanged(sortedMail6PropertyName)
            End If
        End Set
    End Property
    Private _sortedMail7 As ObservableCollection(Of String)
    Public Const sortedMail7PropertyName As String = "sortedMail7"
    Public Property sortedMail7 As ObservableCollection(Of String)
        Get
            Return _sortedMail7
        End Get
        Set(ByVal value As ObservableCollection(Of String))
            If _sortedMail7 IsNot value Then
                _sortedMail7 = value
                OnPropertyChanged(sortedMail7PropertyName)
            End If
        End Set
    End Property
    Private _sortedMail8 As ObservableCollection(Of String)
    Public Const sortedMail8PropertyName As String = "sortedMail8"
    Public Property sortedMail8 As ObservableCollection(Of String)
        Get
            Return _sortedMail8
        End Get
        Set(ByVal value As ObservableCollection(Of String))
            If _sortedMail8 IsNot value Then
                _sortedMail8 = value
                OnPropertyChanged(sortedMail8PropertyName)
            End If
        End Set
    End Property
    Private _sortedMail9 As ObservableCollection(Of String)
    Public Const sortedMail9PropertyName As String = "sortedMail9"
    Public Property sortedMail9 As ObservableCollection(Of String)
        Get
            Return _sortedMail9
        End Get
        Set(ByVal value As ObservableCollection(Of String))
            If _sortedMail9 IsNot value Then
                _sortedMail9 = value
                OnPropertyChanged(sortedMail9PropertyName)
            End If
        End Set
    End Property
    Private _sortedMail10 As ObservableCollection(Of String)
    Public Const sortedMail10PropertyName As String = "sortedMail10"
    Public Property sortedMail10 As ObservableCollection(Of String)
        Get
            Return _sortedMail10
        End Get
        Set(ByVal value As ObservableCollection(Of String))
            If _sortedMail10 IsNot value Then
                _sortedMail10 = value
                OnPropertyChanged(sortedMail10PropertyName)
            End If
        End Set
    End Property
    Public Property sortedMail11 As ObservableCollection(Of String)
    Public Property sortedMail12 As ObservableCollection(Of String)
    Public Property sortedMail13 As ObservableCollection(Of String)
    Public Property sortedMail14 As ObservableCollection(Of String)
    Public Property sortedMail15 As ObservableCollection(Of String)
    Public Property sortedMail16 As ObservableCollection(Of String)
    Public Property sortedMail17 As ObservableCollection(Of String)
    Public Property sortedMail18 As ObservableCollection(Of String)
    Public Property sortedMail19 As ObservableCollection(Of String)
    Public Property sortedMail20 As ObservableCollection(Of String)
    Public Property sortedMail21 As ObservableCollection(Of String)
    Public Property sortedMail22 As ObservableCollection(Of String)
    Public Property sortedMail23 As ObservableCollection(Of String)
    Public Property sortedMail24 As ObservableCollection(Of String)
    Public Property sortedMail25 As ObservableCollection(Of String)
    Public Property sortedMail26 As ObservableCollection(Of String)
    Public Property sortedMail27 As ObservableCollection(Of String)
    Public Property sortedMail28 As ObservableCollection(Of String)
    Public Property sortedMail29 As ObservableCollection(Of String)
    Private _processing As Boolean
    Public Const sortedProcessingPropertyName As String = "Processing"
    Public Property Processing As Boolean
        Get
            Return _processing
        End Get
        Set(ByVal value As Boolean)
            If _processing <> value Then
                _processing = value
                OnPropertyChanged(sortedProcessingPropertyName)
            End If
        End Set
    End Property
    Private _KnownDomains As List(Of String)
    Public Const KnownDomainsPropertyName As String = "KnownDomains"
    Public Property KnownDomains As List(Of String)
        Get
            Return _KnownDomains
        End Get
        Set(ByVal value As List(Of String))
            If _KnownDomains IsNot value Then
                _KnownDomains = value
                OnPropertyChanged(KnownDomainsPropertyName)
            End If
        End Set
    End Property
    Private _MailList As List(Of String)
    Public Const MailListPropertyName As String = "MailList"
    Public Property MailList As List(Of String)
        Get
            Return _MailList
        End Get
        Set(ByVal value As List(Of String))
            If _MailList IsNot value Then
                _MailList = value
                OnPropertyChanged(MailListPropertyName)
            End If
        End Set
    End Property
    Private _KnownMx As List(Of String)
    Public Const KnownMxPropertyName As String = "KnownMx"
    Public Property KnownMx As List(Of String)
        Get
            Return _KnownMx
        End Get
        Set(ByVal value As List(Of String))
            If _KnownMx IsNot value Then
                _KnownMx = value
                OnPropertyChanged(KnownMxPropertyName)
            End If
        End Set
    End Property
    Private _text_1 As String
    Public Const text_1PropertyName As String = "text_1"
    Public Property text_1 As String
        Get
            Return _text_1
        End Get
        Set(ByVal value As String)
            If Not Equals(_text_1, value) Then
                _text_1 = value
                OnPropertyChanged(text_1PropertyName)
            End If
        End Set
    End Property
    Private _AllLines As Integer
    Public Const AllLinesPropertyName As String = "AllLines"
    Public Property AllLines As Integer
        Get
            Return _AllLines
        End Get
        Set(ByVal value As Integer)
            If _AllLines <> value Then
                _AllLines = value
                OnPropertyChanged(AllLinesPropertyName)
            End If
        End Set
    End Property
    Private _lines_1 As Integer
    Public Const lines_1PropertyName As String = "lines_1"
    Public Property lines_1 As Integer
        Get
            Return _lines_1
        End Get
        Set(ByVal value As Integer)
            If _lines_1 <> value Then
                _lines_1 = value
                If _AllLines <> 0 Then
                    percent = _lines_1 * 100 / _AllLines
                Else
                    percent = 0
                End If
                OnPropertyChanged(lines_1PropertyName)
            End If
        End Set
    End Property
    Private _percent As Double
    Public Const percentPropertyName As String = "percent"
    Public Property percent As Double
        Get
            Return _percent
        End Get
        Set(ByVal value As Double)
            If _percent <> value Then
                _percent = value
                OnPropertyChanged(percentPropertyName)
            End If
        End Set
    End Property
    Private _Enabler As ObservableCollection(Of Boolean)
    Public Const EnablerPropertyName As String = "Enabler"
    Public Property Enabler As ObservableCollection(Of Boolean)
        Get
            Return _Enabler
        End Get
        Set(ByVal value As ObservableCollection(Of Boolean))
            If _Enabler IsNot value Then
                _Enabler = value
                OnPropertyChanged(EnablerPropertyName)
            End If
        End Set
    End Property
    Public Sub New()
        text_1 = ""
        lines_1 = 0
        Enabler = New ObservableCollection(Of Boolean) From {
            True,
            False,
            False,
            False,
            False
        }
        'text_2 = "";
        'lines_2 = 0;
        MailList = New List(Of String) From {
        }
        KnownDomains = Global.Best_Sender.My.Resources.knowndomain.Split(New Char() {Microsoft.VisualBasic.Strings.ChrW(13), Microsoft.VisualBasic.Strings.ChrW(10)}, StringSplitOptions.RemoveEmptyEntries).ToList()
        KnownMx = Global.Best_Sender.My.Resources.knownmx.Split(New Char() {Microsoft.VisualBasic.Strings.ChrW(13), Microsoft.VisualBasic.Strings.ChrW(10)}, StringSplitOptions.RemoveEmptyEntries).ToList()
        'SeparatorsList = new List<char>(){
        '    ':','|','+','/','=',';'
        '};
        Processing = False
        sortedMails = New ObservableCollection(Of String) From {
        }
        sortedMail1 = New ObservableCollection(Of String) From {
        }
        sortedMail2 = New ObservableCollection(Of String) From {
        }
        sortedMail3 = New ObservableCollection(Of String) From {
        }
        sortedMail4 = New ObservableCollection(Of String) From {
        }
        sortedMail5 = New ObservableCollection(Of String) From {
        }
        sortedMail6 = New ObservableCollection(Of String) From {
        }
        sortedMail7 = New ObservableCollection(Of String) From {
        }
        sortedMail8 = New ObservableCollection(Of String) From {
        }
        sortedMail9 = New ObservableCollection(Of String) From {
        }
        sortedMail10 = New ObservableCollection(Of String) From {
        }
        sortedMail11 = New ObservableCollection(Of String) From {
        }
        sortedMail12 = New ObservableCollection(Of String) From {
        }
        sortedMail13 = New ObservableCollection(Of String) From {
        }
        sortedMail14 = New ObservableCollection(Of String) From {
        }
        sortedMail15 = New ObservableCollection(Of String) From {
        }
        sortedMail16 = New ObservableCollection(Of String) From {
        }
        sortedMail17 = New ObservableCollection(Of String) From {
        }
        sortedMail18 = New ObservableCollection(Of String) From {
        }
        sortedMail19 = New ObservableCollection(Of String) From {
        }
        sortedMail20 = New ObservableCollection(Of String) From {
        }
        sortedMail21 = New ObservableCollection(Of String) From {
        }
        sortedMail22 = New ObservableCollection(Of String) From {
        }
        sortedMail23 = New ObservableCollection(Of String) From {
        }
        sortedMail24 = New ObservableCollection(Of String) From {
        }
        sortedMail25 = New ObservableCollection(Of String) From {
        }
        sortedMail26 = New ObservableCollection(Of String) From {
        }
        sortedMail27 = New ObservableCollection(Of String) From {
        }
        sortedMail28 = New ObservableCollection(Of String) From {
        }
        sortedMail29 = New ObservableCollection(Of String) From {
        }
    End Sub
    Public Event PropertyChanged As PropertyChangedEventHandler Implements INotifyPropertyChanged.PropertyChanged
    Private Sub OnPropertyChanged(ByVal propertyName As String)
        RaiseEvent PropertyChanged(Me, New PropertyChangedEventArgs(propertyName))
    End Sub
    Public Sub Clear()
        text_1 = ""
        lines_1 = 0
        percent = 0
        AllLines = 0
        sortedMails.Clear()
        sortedMail1.Clear()
        sortedMail2.Clear()
        sortedMail3.Clear()
        sortedMail4.Clear()
        sortedMail5.Clear()
        sortedMail6.Clear()
        sortedMail7.Clear()
        sortedMail8.Clear()
        sortedMail9.Clear()
        sortedMail10.Clear()
        sortedMail11.Clear()
        sortedMail12.Clear()
        sortedMail13.Clear()
        sortedMail14.Clear()
        sortedMail15.Clear()
        sortedMail16.Clear()
        sortedMail17.Clear()
        sortedMail18.Clear()
        sortedMail19.Clear()
        sortedMail20.Clear()
        sortedMail21.Clear()
        sortedMail22.Clear()
        sortedMail23.Clear()
        sortedMail24.Clear()
        sortedMail25.Clear()
        sortedMail26.Clear()
        sortedMail27.Clear()
        sortedMail28.Clear()
        sortedMail29.Clear()
    End Sub
End Class
