<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmEmailSender
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmEmailSender))
        Dim EditorButtonImageOptions1 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject1 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject2 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject3 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject4 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim EditorButtonImageOptions2 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject5 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject6 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject7 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject8 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim EditorButtonImageOptions3 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject9 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject10 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject11 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject12 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Me.trmfadein = New System.Windows.Forms.Timer(Me.components)
        Me.trmfadeout = New System.Windows.Forms.Timer(Me.components)
        Me.RadialMenu1 = New DevExpress.XtraBars.Ribbon.RadialMenu(Me.components)
        Me.btnAddEMail_From = New DevExpress.XtraBars.BarButtonItem()
        Me.BarManager1 = New DevExpress.XtraBars.BarManager(Me.components)
        Me.barDockControlTop = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlBottom = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlLeft = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlRight = New DevExpress.XtraBars.BarDockControl()
        Me.BarButtonItem1 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem2 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem3 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem4 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem5 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem6 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem7 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem8 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem9 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem10 = New DevExpress.XtraBars.BarButtonItem()
        Me.BntEList = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Clear_E_L = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem11 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem12 = New DevExpress.XtraBars.BarButtonItem()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.DxErrorProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(Me.components)
        Me.TabEncode = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.WebBrowser1 = New System.Windows.Forms.WebBrowser()
        Me.LetterEncoder_bntBrowse = New DevExpress.XtraEditors.SimpleButton()
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.ToolTip2 = New System.Windows.Forms.ToolTip(Me.components)
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.ToolTip3 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Label13 = New System.Windows.Forms.Label()
        Me.lblSuccess = New System.Windows.Forms.Label()
        Me.lblfailed = New System.Windows.Forms.Label()
        Me.trmUpdateMailGrid = New System.Windows.Forms.Timer(Me.components)
        Me.PopupMenu1 = New DevExpress.XtraBars.PopupMenu(Me.components)
        Me.BackgroundWorker2 = New System.ComponentModel.BackgroundWorker()
        Me.XtraTabPage2 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel7 = New System.Windows.Forms.Panel()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.LayoutControl3 = New DevExpress.XtraLayout.LayoutControl()
        Me.txtLetter = New System.Windows.Forms.RichTextBox()
        Me.txtLetterPath = New DevExpress.XtraEditors.TextEdit()
        Me.txtNewLink = New DevExpress.XtraEditors.TextEdit()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.lblTrcThreads = New System.Windows.Forms.Label()
        Me.TabSmtp = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.SmptTestWait = New DevExpress.XtraEditors.GroupControl()
        Me.picWait = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemTextEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn8 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn11 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn7 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.BNT_Details = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.GridColumn6 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Bnt_Remove = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.GridColumn9 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Col_Chk = New DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit()
        Me.GridColumn10 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn23 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn24 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn25 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn18 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn19 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.XtraTabPage1 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.ProgressBarControl2 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.GridControl2 = New DevExpress.XtraGrid.GridControl()
        Me.GridView2 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn12 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn13 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemTextEdit2 = New DevExpress.XtraEditors.Repository.RepositoryItemTextEdit()
        Me.GridColumn14 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.col_status_icon = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn15 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn16 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn17 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn20 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemButtonEdit2 = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.GridColumn21 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemCheckEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit()
        Me.GridColumn22 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn26 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn27 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn28 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemImageEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemImageEdit()
        Me.RepositoryItemPictureEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemPictureEdit()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.XtraTabPage3 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.ToggleSwitch3 = New DevExpress.XtraEditors.ToggleSwitch()
        Me.lblTotalPproxyInvalid = New System.Windows.Forms.Label()
        Me.lblProxyvalid = New System.Windows.Forms.Label()
        Me.lblTotalProxy = New System.Windows.Forms.Label()
        Me.ToggleSwitch2 = New DevExpress.XtraEditors.ToggleSwitch()
        Me.BntSaveProxyvalid = New DevExpress.XtraEditors.SimpleButton()
        Me.BntRemoveAll = New DevExpress.XtraEditors.SimpleButton()
        Me.BntRemoveInvalid = New DevExpress.XtraEditors.SimpleButton()
        Me.BntStartCheckProxy = New DevExpress.XtraEditors.SimpleButton()
        Me.BntSelectListProxy = New DevExpress.XtraEditors.SimpleButton()
        Me.BntProxyReport = New DevExpress.XtraEditors.SimpleButton()
        Me.ProgressBarControl3 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.GridControl3 = New DevExpress.XtraGrid.GridControl()
        Me.GridView3 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.RepositoryItemButtonEdit3 = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.XtraTabPage6 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel8 = New System.Windows.Forms.Panel()
        Me.XtraTabControl2 = New DevExpress.XtraTab.XtraTabControl()
        Me.XtraTabPage12 = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControl4 = New DevExpress.XtraEditors.GroupControl()
        Me.txtTitilOffice365 = New DevExpress.XtraEditors.TextEdit()
        Me.ComboBoxEdit1 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Switch_LetterConvertorLink = New DevExpress.XtraEditors.ToggleSwitch()
        Me.txt_LetterConvertorLink = New DevExpress.XtraEditors.TextEdit()
        Me.bntResetLetterConvertor = New DevExpress.XtraEditors.SimpleButton()
        Me.XtraTabPage10 = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControl6 = New DevExpress.XtraEditors.GroupControl()
        Me.txtbody = New System.Windows.Forms.TextBox()
        Me.lblattacmentFileCount = New System.Windows.Forms.Label()
        Me.ToggleSwitch1 = New DevExpress.XtraEditors.ToggleSwitch()
        Me.cbTags = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.bntAddAttachment = New DevExpress.XtraEditors.SimpleButton()
        Me.txtattach = New DevExpress.XtraEditors.TextEdit()
        Me.BntRemoveAttachment = New DevExpress.XtraEditors.SimpleButton()
        Me.XtraTabPage8 = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControl9 = New DevExpress.XtraEditors.GroupControl()
        Me.ChkLogo = New DevExpress.XtraEditors.CheckEdit()
        Me.lblLogoTitle = New System.Windows.Forms.Label()
        Me.piclogo = New System.Windows.Forms.PictureBox()
        Me.BntResetLetterLogo = New DevExpress.XtraEditors.SimpleButton()
        Me.bntLetterLogo = New DevExpress.XtraEditors.SimpleButton()
        Me.SeparatorControl3 = New DevExpress.XtraEditors.SeparatorControl()
        Me.XtraTabPage9 = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControl5 = New DevExpress.XtraEditors.GroupControl()
        Me.SimpleButton3 = New DevExpress.XtraEditors.SimpleButton()
        Me.Pic_QRCode = New System.Windows.Forms.PictureBox()
        Me.btnExport = New DevExpress.XtraEditors.SimpleButton()
        Me.txtCode = New DevExpress.XtraEditors.TextEdit()
        Me.btnGenerate = New DevExpress.XtraEditors.SimpleButton()
        Me.txtHeight = New DevExpress.XtraEditors.TextEdit()
        Me.txtWidth = New DevExpress.XtraEditors.TextEdit()
        Me.XtraTabPage11 = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControl7 = New DevExpress.XtraEditors.GroupControl()
        Me.cmbPriority = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.ComboSensitivity = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.ComboFlagStatus = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.XtraTabPage7 = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControl8 = New DevExpress.XtraEditors.GroupControl()
        Me.txtSleepTime = New System.Windows.Forms.Label()
        Me.ComboBoxSleepTime = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.SeparatorControl4 = New DevExpress.XtraEditors.SeparatorControl()
        Me.txtLimitSend = New System.Windows.Forms.Label()
        Me.trcThreads = New DevExpress.XtraEditors.TrackBarControl()
        Me.lblsuccess_Time = New System.Windows.Forms.Label()
        Me.txt_limitSend = New DevExpress.XtraEditors.TextEdit()
        Me.chk_limitSend = New DevExpress.XtraEditors.CheckEdit()
        Me.XtraTabPage5 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel9 = New System.Windows.Forms.Panel()
        Me.WebBrowser2 = New System.Windows.Forms.WebBrowser()
        Me.LayoutControl4 = New DevExpress.XtraLayout.LayoutControl()
        Me.LayoutControlGroup3 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.PopupMenu2 = New DevExpress.XtraBars.PopupMenu(Me.components)
        Me.BntAddMailList = New DevExpress.XtraBars.BarButtonItem()
        Me.BntClearEList = New DevExpress.XtraBars.BarButtonItem()
        Me.BarManager2 = New DevExpress.XtraBars.BarManager(Me.components)
        Me.BarDockControl1 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl2 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl3 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl4 = New DevExpress.XtraBars.BarDockControl()
        Me.PopupMenu3 = New DevExpress.XtraBars.PopupMenu(Me.components)
        Me.BarButtonItem13 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem14 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem16 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarManager3 = New DevExpress.XtraBars.BarManager(Me.components)
        Me.BarDockControl5 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl6 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl7 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl8 = New DevExpress.XtraBars.BarDockControl()
        Me.BarButtonItem15 = New DevExpress.XtraBars.BarButtonItem()
        Me.Bar4 = New DevExpress.XtraBars.Bar()
        Me.SeparatorControl1 = New DevExpress.XtraEditors.SeparatorControl()
        Me.pnlResult = New System.Windows.Forms.Panel()
        Me.PictureBox4 = New System.Windows.Forms.PictureBox()
        Me.lblTotal = New System.Windows.Forms.Label()
        Me.lbltotalstmp = New System.Windows.Forms.Label()
        Me.pnlWait = New System.Windows.Forms.Panel()
        Me.ProgressBarControl1 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.lbl1 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Panel13 = New System.Windows.Forms.Panel()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.GridColumn31 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn36 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn37 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn43 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.BarManager4 = New DevExpress.XtraBars.BarManager(Me.components)
        Me.BarDockControl9 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl10 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl11 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl12 = New DevExpress.XtraBars.BarDockControl()
        Me.BarButtonItem22 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem23 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem24 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem25 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem26 = New DevExpress.XtraBars.BarButtonItem()
        Me.BntStopScan = New DevExpress.XtraBars.BarButtonItem()
        Me.BntRemoveAllpending = New DevExpress.XtraBars.BarButtonItem()
        Me.PopupMenu6 = New DevExpress.XtraBars.PopupMenu(Me.components)
        CType(Me.RadialMenu1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabEncode.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.PopupMenu1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage2.SuspendLayout()
        Me.Panel7.SuspendLayout()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl3.SuspendLayout()
        CType(Me.txtLetterPath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtNewLink.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabSmtp.SuspendLayout()
        Me.Panel2.SuspendLayout()
        CType(Me.SmptTestWait, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SmptTestWait.SuspendLayout()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BNT_Details, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Bnt_Remove, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Col_Chk, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage1.SuspendLayout()
        Me.Panel3.SuspendLayout()
        CType(Me.ProgressBarControl2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemTextEdit2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemButtonEdit2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemCheckEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemImageEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemPictureEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.XtraTabPage3.SuspendLayout()
        Me.Panel5.SuspendLayout()
        CType(Me.ToggleSwitch3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ToggleSwitch2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ProgressBarControl3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemButtonEdit3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage6.SuspendLayout()
        Me.Panel8.SuspendLayout()
        CType(Me.XtraTabControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl2.SuspendLayout()
        Me.XtraTabPage12.SuspendLayout()
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl4.SuspendLayout()
        CType(Me.txtTitilOffice365.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Switch_LetterConvertorLink.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_LetterConvertorLink.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage10.SuspendLayout()
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl6.SuspendLayout()
        CType(Me.ToggleSwitch1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cbTags.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtattach.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage8.SuspendLayout()
        CType(Me.GroupControl9, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl9.SuspendLayout()
        CType(Me.ChkLogo.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.piclogo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage9.SuspendLayout()
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl5.SuspendLayout()
        CType(Me.Pic_QRCode, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtCode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtHeight.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtWidth.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage11.SuspendLayout()
        CType(Me.GroupControl7, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl7.SuspendLayout()
        CType(Me.cmbPriority.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboSensitivity.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboFlagStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage7.SuspendLayout()
        CType(Me.GroupControl8, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl8.SuspendLayout()
        CType(Me.ComboBoxSleepTime.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.trcThreads, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.trcThreads.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_limitSend.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.chk_limitSend.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage5.SuspendLayout()
        Me.Panel9.SuspendLayout()
        CType(Me.LayoutControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl4.SuspendLayout()
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PopupMenu2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BarManager2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PopupMenu3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BarManager3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlResult.SuspendLayout()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlWait.SuspendLayout()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel13.SuspendLayout()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BarManager4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PopupMenu6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'trmfadein
        '
        Me.trmfadein.Enabled = True
        Me.trmfadein.Interval = 1
        '
        'trmfadeout
        '
        Me.trmfadeout.Interval = 1
        '
        'RadialMenu1
        '
        Me.RadialMenu1.AlphaChannel = CType(245, Byte)
        Me.RadialMenu1.ArcHoveredThickness = 2
        Me.RadialMenu1.AutoExpand = True
        Me.RadialMenu1.ButtonRadius = 30
        Me.RadialMenu1.Glyph = CType(resources.GetObject("RadialMenu1.Glyph"), System.Drawing.Image)
        Me.RadialMenu1.InnerRadius = 0
        Me.RadialMenu1.ItemAutoSize = DevExpress.XtraBars.Ribbon.RadialMenuItemAutoSize.Spring
        Me.RadialMenu1.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.btnAddEMail_From)})
        Me.RadialMenu1.Manager = Me.BarManager1
        Me.RadialMenu1.Name = "RadialMenu1"
        '
        'btnAddEMail_From
        '
        Me.btnAddEMail_From.Caption = "Set All Email From All SMTP"
        Me.btnAddEMail_From.Id = 9
        Me.btnAddEMail_From.ImageOptions.SvgImage = CType(resources.GetObject("btnAddEMail_From.ImageOptions.SvgImage"), DevExpress.Utils.Svg.SvgImage)
        Me.btnAddEMail_From.Name = "btnAddEMail_From"
        '
        'BarManager1
        '
        Me.BarManager1.DockControls.Add(Me.barDockControlTop)
        Me.BarManager1.DockControls.Add(Me.barDockControlBottom)
        Me.BarManager1.DockControls.Add(Me.barDockControlLeft)
        Me.BarManager1.DockControls.Add(Me.barDockControlRight)
        Me.BarManager1.DockWindowTabFont = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BarManager1.Form = Me
        Me.BarManager1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.BarButtonItem1, Me.BarButtonItem2, Me.BarButtonItem3, Me.BarButtonItem4, Me.BarButtonItem5, Me.BarButtonItem6, Me.BarButtonItem7, Me.BarButtonItem8, Me.BarButtonItem9, Me.btnAddEMail_From, Me.BarButtonItem10, Me.BntEList, Me.Bnt_Clear_E_L, Me.BarButtonItem11, Me.BarButtonItem12})
        Me.BarManager1.MaxItemId = 15
        '
        'barDockControlTop
        '
        Me.barDockControlTop.CausesValidation = False
        Me.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top
        Me.barDockControlTop.Location = New System.Drawing.Point(0, 0)
        Me.barDockControlTop.Manager = Me.BarManager1
        Me.barDockControlTop.Margin = New System.Windows.Forms.Padding(4)
        Me.barDockControlTop.Size = New System.Drawing.Size(1169, 0)
        '
        'barDockControlBottom
        '
        Me.barDockControlBottom.CausesValidation = False
        Me.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.barDockControlBottom.Location = New System.Drawing.Point(0, 603)
        Me.barDockControlBottom.Manager = Me.BarManager1
        Me.barDockControlBottom.Margin = New System.Windows.Forms.Padding(4)
        Me.barDockControlBottom.Size = New System.Drawing.Size(1169, 0)
        '
        'barDockControlLeft
        '
        Me.barDockControlLeft.CausesValidation = False
        Me.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left
        Me.barDockControlLeft.Location = New System.Drawing.Point(0, 0)
        Me.barDockControlLeft.Manager = Me.BarManager1
        Me.barDockControlLeft.Margin = New System.Windows.Forms.Padding(4)
        Me.barDockControlLeft.Size = New System.Drawing.Size(0, 603)
        '
        'barDockControlRight
        '
        Me.barDockControlRight.CausesValidation = False
        Me.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right
        Me.barDockControlRight.Location = New System.Drawing.Point(1169, 0)
        Me.barDockControlRight.Manager = Me.BarManager1
        Me.barDockControlRight.Margin = New System.Windows.Forms.Padding(4)
        Me.barDockControlRight.Size = New System.Drawing.Size(0, 603)
        '
        'BarButtonItem1
        '
        Me.BarButtonItem1.Caption = "Add Smtp Bulk"
        Me.BarButtonItem1.Id = 0
        Me.BarButtonItem1.ImageOptions.Image = CType(resources.GetObject("BarButtonItem1.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem1.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem1.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem1.ItemAppearance.Normal.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BarButtonItem1.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem1.Name = "BarButtonItem1"
        '
        'BarButtonItem2
        '
        Me.BarButtonItem2.Caption = "Test All Smtp Accounts"
        Me.BarButtonItem2.Id = 1
        Me.BarButtonItem2.ImageOptions.Image = CType(resources.GetObject("BarButtonItem2.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem2.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem2.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem2.ItemAppearance.Normal.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.BarButtonItem2.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem2.Name = "BarButtonItem2"
        '
        'BarButtonItem3
        '
        Me.BarButtonItem3.Caption = "Add Name,Mail and Subject to all"
        Me.BarButtonItem3.Id = 2
        Me.BarButtonItem3.ImageOptions.Image = CType(resources.GetObject("BarButtonItem3.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem3.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem3.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem3.ItemAppearance.Normal.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.BarButtonItem3.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem3.Name = "BarButtonItem3"
        '
        'BarButtonItem4
        '
        Me.BarButtonItem4.Caption = "Delete Failed Accounts"
        Me.BarButtonItem4.Id = 3
        Me.BarButtonItem4.ImageOptions.Image = CType(resources.GetObject("BarButtonItem4.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem4.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem4.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem4.Name = "BarButtonItem4"
        '
        'BarButtonItem5
        '
        Me.BarButtonItem5.Caption = "Save Working Accounts"
        Me.BarButtonItem5.Id = 4
        Me.BarButtonItem5.ImageOptions.Image = CType(resources.GetObject("BarButtonItem5.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem5.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem5.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem5.Name = "BarButtonItem5"
        '
        'BarButtonItem6
        '
        Me.BarButtonItem6.Caption = "Check SSL for All"
        Me.BarButtonItem6.Id = 5
        Me.BarButtonItem6.ImageOptions.Image = CType(resources.GetObject("BarButtonItem6.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem6.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem6.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem6.Name = "BarButtonItem6"
        '
        'BarButtonItem7
        '
        Me.BarButtonItem7.Caption = "Uncheck SSL for All"
        Me.BarButtonItem7.Id = 6
        Me.BarButtonItem7.ImageOptions.Image = CType(resources.GetObject("BarButtonItem7.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem7.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem7.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem7.Name = "BarButtonItem7"
        '
        'BarButtonItem8
        '
        Me.BarButtonItem8.Caption = "Delete unverified"
        Me.BarButtonItem8.Id = 7
        Me.BarButtonItem8.ImageOptions.SvgImage = CType(resources.GetObject("BarButtonItem8.ImageOptions.SvgImage"), DevExpress.Utils.Svg.SvgImage)
        Me.BarButtonItem8.Name = "BarButtonItem8"
        '
        'BarButtonItem9
        '
        Me.BarButtonItem9.Caption = "Delete Untested Accounts"
        Me.BarButtonItem9.Id = 8
        Me.BarButtonItem9.ImageOptions.Image = CType(resources.GetObject("BarButtonItem9.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem9.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem9.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem9.Name = "BarButtonItem9"
        '
        'BarButtonItem10
        '
        Me.BarButtonItem10.Caption = "Convert Host To IP"
        Me.BarButtonItem10.Id = 10
        Me.BarButtonItem10.ImageOptions.Image = CType(resources.GetObject("BarButtonItem10.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem10.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem10.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem10.Name = "BarButtonItem10"
        '
        'BntEList
        '
        Me.BntEList.Caption = "Add List Email"
        Me.BntEList.Id = 11
        Me.BntEList.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Emailimage
        Me.BntEList.Name = "BntEList"
        '
        'Bnt_Clear_E_L
        '
        Me.Bnt_Clear_E_L.Caption = "Clear List Email"
        Me.Bnt_Clear_E_L.Id = 12
        Me.Bnt_Clear_E_L.ImageOptions.LargeImage = CType(resources.GetObject("Bnt_Clear_E_L.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.Bnt_Clear_E_L.Name = "Bnt_Clear_E_L"
        '
        'BarButtonItem11
        '
        Me.BarButtonItem11.Caption = "Add Email List"
        Me.BarButtonItem11.Id = 13
        Me.BarButtonItem11.Name = "BarButtonItem11"
        '
        'BarButtonItem12
        '
        Me.BarButtonItem12.Caption = "Clear Email List"
        Me.BarButtonItem12.Id = 14
        Me.BarButtonItem12.Name = "BarButtonItem12"
        '
        'DxErrorProvider1
        '
        Me.DxErrorProvider1.ContainerControl = Me
        '
        'TabEncode
        '
        Me.TabEncode.Appearance.PageClient.BackColor = System.Drawing.Color.MediumAquamarine
        Me.TabEncode.Appearance.PageClient.Options.UseBackColor = True
        Me.TabEncode.AutoScroll = True
        Me.TabEncode.Controls.Add(Me.Panel1)
        Me.TabEncode.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TabEncode.Name = "TabEncode"
        Me.TabEncode.Size = New System.Drawing.Size(952, 503)
        '
        'Panel1
        '
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.WebBrowser1)
        Me.Panel1.Controls.Add(Me.LetterEncoder_bntBrowse)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(952, 503)
        Me.Panel1.TabIndex = 23
        '
        'WebBrowser1
        '
        Me.WebBrowser1.Location = New System.Drawing.Point(29, 88)
        Me.WebBrowser1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.WebBrowser1.MinimumSize = New System.Drawing.Size(17, 16)
        Me.WebBrowser1.Name = "WebBrowser1"
        Me.WebBrowser1.Size = New System.Drawing.Size(17, 16)
        Me.WebBrowser1.TabIndex = 24
        Me.WebBrowser1.Visible = False
        '
        'LetterEncoder_bntBrowse
        '
        Me.LetterEncoder_bntBrowse.AllowFocus = False
        Me.LetterEncoder_bntBrowse.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.LetterEncoder_bntBrowse.Appearance.Options.UseBackColor = True
        Me.LetterEncoder_bntBrowse.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.LetterEncoder_bntBrowse.AppearanceHovered.Options.UseBackColor = True
        Me.LetterEncoder_bntBrowse.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.LetterEncoder_bntBrowse.AppearancePressed.Options.UseBackColor = True
        Me.LetterEncoder_bntBrowse.Location = New System.Drawing.Point(772, 22)
        Me.LetterEncoder_bntBrowse.LookAndFeel.SkinName = "Darkroom"
        Me.LetterEncoder_bntBrowse.LookAndFeel.UseDefaultLookAndFeel = False
        Me.LetterEncoder_bntBrowse.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.LetterEncoder_bntBrowse.Name = "LetterEncoder_bntBrowse"
        Me.LetterEncoder_bntBrowse.Size = New System.Drawing.Size(79, 24)
        Me.LetterEncoder_bntBrowse.TabIndex = 23
        '
        'Timer1
        '
        Me.Timer1.Interval = 500
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'Label13
        '
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(203, Byte), Integer), CType(CType(121, Byte), Integer))
        Me.Label13.Location = New System.Drawing.Point(944, 881)
        Me.Label13.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(197, 27)
        Me.Label13.TabIndex = 342
        Me.Label13.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.ToolTip3.SetToolTip(Me.Label13, "The Number of Mail that was Sent")
        Me.Label13.Visible = False
        '
        'lblSuccess
        '
        Me.lblSuccess.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblSuccess.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(203, Byte), Integer), CType(CType(121, Byte), Integer))
        Me.lblSuccess.Location = New System.Drawing.Point(22, 12)
        Me.lblSuccess.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblSuccess.Name = "lblSuccess"
        Me.lblSuccess.Size = New System.Drawing.Size(95, 27)
        Me.lblSuccess.TabIndex = 315
        Me.lblSuccess.Text = "Sent : 0"
        Me.lblSuccess.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.ToolTip3.SetToolTip(Me.lblSuccess, "The Number of Mail that was Sent")
        '
        'lblfailed
        '
        Me.lblfailed.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblfailed.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(128, Byte), Integer))
        Me.lblfailed.Location = New System.Drawing.Point(174, 12)
        Me.lblfailed.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblfailed.Name = "lblfailed"
        Me.lblfailed.Size = New System.Drawing.Size(95, 27)
        Me.lblfailed.TabIndex = 316
        Me.lblfailed.Text = "Failed : 0"
        Me.lblfailed.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.ToolTip3.SetToolTip(Me.lblfailed, "The Number of Mail that not Send")
        '
        'trmUpdateMailGrid
        '
        Me.trmUpdateMailGrid.Interval = 5000
        '
        'PopupMenu1
        '
        Me.PopupMenu1.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem1, True), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem6, True), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem3, True), New DevExpress.XtraBars.LinkPersistInfo(Me.btnAddEMail_From), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem2), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem4), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem10), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem5), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem9), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem7, True)})
        Me.PopupMenu1.Manager = Me.BarManager1
        Me.PopupMenu1.MenuAppearance.AppearanceMenu.Normal.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.PopupMenu1.MenuAppearance.AppearanceMenu.Normal.Options.UseFont = True
        Me.PopupMenu1.MenuAppearance.HeaderItemAppearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.PopupMenu1.MenuAppearance.HeaderItemAppearance.Options.UseFont = True
        Me.PopupMenu1.MenuAppearance.MenuBar.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.PopupMenu1.MenuAppearance.MenuBar.Options.UseFont = True
        Me.PopupMenu1.MenuAppearance.MenuCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.PopupMenu1.MenuAppearance.MenuCaption.Options.UseFont = True
        Me.PopupMenu1.MenuAppearance.SideStrip.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.PopupMenu1.MenuAppearance.SideStrip.Options.UseFont = True
        Me.PopupMenu1.MenuAppearance.SideStripNonRecent.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.PopupMenu1.MenuAppearance.SideStripNonRecent.Options.UseFont = True
        Me.PopupMenu1.Name = "PopupMenu1"
        '
        'BackgroundWorker2
        '
        Me.BackgroundWorker2.WorkerReportsProgress = True
        Me.BackgroundWorker2.WorkerSupportsCancellation = True
        '
        'XtraTabPage2
        '
        Me.XtraTabPage2.Controls.Add(Me.Panel7)
        Me.XtraTabPage2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage2.Name = "XtraTabPage2"
        Me.XtraTabPage2.Size = New System.Drawing.Size(1134, 468)
        Me.XtraTabPage2.Text = "   Preparing Letter   "
        '
        'Panel7
        '
        Me.Panel7.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Panel7.Controls.Add(Me.GroupControl1)
        Me.Panel7.Controls.Add(Me.lblTrcThreads)
        Me.Panel7.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel7.Location = New System.Drawing.Point(0, 0)
        Me.Panel7.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Panel7.Name = "Panel7"
        Me.Panel7.Size = New System.Drawing.Size(1134, 468)
        Me.Panel7.TabIndex = 317
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.LayoutControl3)
        Me.GroupControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControl1.Location = New System.Drawing.Point(0, 0)
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(1134, 468)
        Me.GroupControl1.TabIndex = 328
        Me.GroupControl1.Text = "Add Letter"
        '
        'LayoutControl3
        '
        Me.LayoutControl3.Controls.Add(Me.txtLetter)
        Me.LayoutControl3.Controls.Add(Me.txtLetterPath)
        Me.LayoutControl3.Controls.Add(Me.txtNewLink)
        Me.LayoutControl3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl3.Location = New System.Drawing.Point(2, 29)
        Me.LayoutControl3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LayoutControl3.Name = "LayoutControl3"
        Me.LayoutControl3.Root = Me.LayoutControlGroup2
        Me.LayoutControl3.Size = New System.Drawing.Size(1130, 437)
        Me.LayoutControl3.TabIndex = 0
        Me.LayoutControl3.Text = "LayoutControl3"
        '
        'txtLetter
        '
        Me.txtLetter.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtLetter.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.txtLetter.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.txtLetter.ForeColor = System.Drawing.Color.White
        Me.txtLetter.Location = New System.Drawing.Point(16, 82)
        Me.txtLetter.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtLetter.Name = "txtLetter"
        Me.txtLetter.Size = New System.Drawing.Size(1098, 339)
        Me.txtLetter.TabIndex = 67
        Me.txtLetter.Text = ""
        '
        'txtLetterPath
        '
        Me.txtLetterPath.EditValue = ""
        Me.txtLetterPath.Location = New System.Drawing.Point(125, 16)
        Me.txtLetterPath.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtLetterPath.Name = "txtLetterPath"
        Me.txtLetterPath.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtLetterPath.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtLetterPath.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtLetterPath.Properties.Appearance.Options.UseBackColor = True
        Me.txtLetterPath.Properties.Appearance.Options.UseFont = True
        Me.txtLetterPath.Properties.Appearance.Options.UseForeColor = True
        Me.txtLetterPath.Properties.NullValuePrompt = "Path File HTML..."
        Me.txtLetterPath.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtLetterPath.Size = New System.Drawing.Size(436, 30)
        Me.txtLetterPath.StyleController = Me.LayoutControl3
        Me.txtLetterPath.TabIndex = 66
        '
        'txtNewLink
        '
        Me.txtNewLink.EditValue = ""
        Me.txtNewLink.Location = New System.Drawing.Point(676, 16)
        Me.txtNewLink.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtNewLink.Name = "txtNewLink"
        Me.txtNewLink.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.txtNewLink.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtNewLink.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.txtNewLink.Properties.Appearance.Options.UseBackColor = True
        Me.txtNewLink.Properties.Appearance.Options.UseFont = True
        Me.txtNewLink.Properties.Appearance.Options.UseForeColor = True
        Me.txtNewLink.Properties.NullValuePrompt = "If this word is [-Link-] in Your Letter Add your page link here"
        Me.txtNewLink.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtNewLink.Size = New System.Drawing.Size(438, 30)
        Me.txtNewLink.StyleController = Me.LayoutControl3
        Me.txtNewLink.TabIndex = 65
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup2.GroupBordersVisible = False
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem3, Me.EmptySpaceItem1, Me.LayoutControlItem4, Me.LayoutControlItem5})
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(1130, 437)
        Me.LayoutControlGroup2.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.txtNewLink
        Me.LayoutControlItem3.Location = New System.Drawing.Point(551, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(553, 36)
        Me.LayoutControlItem3.Text = "Enter Link Page:"
        Me.LayoutControlItem3.TextSize = New System.Drawing.Size(93, 18)
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 36)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(1104, 30)
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.txtLetterPath
        Me.LayoutControlItem4.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(551, 36)
        Me.LayoutControlItem4.Text = "Path File HTML...."
        Me.LayoutControlItem4.TextSize = New System.Drawing.Size(93, 18)
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.txtLetter
        Me.LayoutControlItem5.Location = New System.Drawing.Point(0, 66)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(1104, 345)
        Me.LayoutControlItem5.TextVisible = False
        '
        'lblTrcThreads
        '
        Me.lblTrcThreads.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.lblTrcThreads.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.lblTrcThreads.Location = New System.Drawing.Point(997, 351)
        Me.lblTrcThreads.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblTrcThreads.Name = "lblTrcThreads"
        Me.lblTrcThreads.Size = New System.Drawing.Size(74, 30)
        Me.lblTrcThreads.TabIndex = 327
        Me.lblTrcThreads.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblTrcThreads.Visible = False
        '
        'TabSmtp
        '
        Me.TabSmtp.Appearance.PageClient.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(44, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.TabSmtp.Appearance.PageClient.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(44, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.TabSmtp.Appearance.PageClient.BorderColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(44, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.TabSmtp.Appearance.PageClient.Options.UseBackColor = True
        Me.TabSmtp.Appearance.PageClient.Options.UseBorderColor = True
        Me.TabSmtp.Controls.Add(Me.Panel2)
        Me.TabSmtp.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TabSmtp.Name = "TabSmtp"
        Me.TabSmtp.Size = New System.Drawing.Size(1134, 468)
        Me.TabSmtp.Text = "    Smtp List    "
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Panel2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel2.Controls.Add(Me.SmptTestWait)
        Me.Panel2.Controls.Add(Me.GridControl1)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel2.ForeColor = System.Drawing.Color.White
        Me.Panel2.Location = New System.Drawing.Point(0, 0)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1134, 468)
        Me.Panel2.TabIndex = 24
        '
        'SmptTestWait
        '
        Me.SmptTestWait.Appearance.BackColor = System.Drawing.Color.White
        Me.SmptTestWait.Appearance.Options.UseBackColor = True
        Me.SmptTestWait.Controls.Add(Me.picWait)
        Me.SmptTestWait.Controls.Add(Me.SimpleButton1)
        Me.SmptTestWait.Location = New System.Drawing.Point(421, 169)
        Me.SmptTestWait.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SmptTestWait.Name = "SmptTestWait"
        Me.SmptTestWait.ShowCaption = False
        Me.SmptTestWait.Size = New System.Drawing.Size(283, 58)
        Me.SmptTestWait.TabIndex = 308
        Me.SmptTestWait.Text = "GroupControl2"
        Me.SmptTestWait.Visible = False
        '
        'picWait
        '
        Me.picWait.AnimationToTextDistance = 10
        Me.picWait.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.picWait.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.picWait.Appearance.Options.UseBackColor = True
        Me.picWait.Appearance.Options.UseFont = True
        Me.picWait.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.picWait.AppearanceCaption.Options.UseFont = True
        Me.picWait.AppearanceDescription.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.picWait.AppearanceDescription.Options.UseFont = True
        Me.picWait.BarAnimationElementThickness = 5
        Me.picWait.Caption = "Please Wait..."
        Me.picWait.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
        Me.picWait.Description = "Check SMTP.."
        Me.picWait.Location = New System.Drawing.Point(7, 12)
        Me.picWait.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.picWait.Name = "picWait"
        Me.picWait.Size = New System.Drawing.Size(172, 35)
        Me.picWait.TabIndex = 396
        Me.picWait.Text = "picWait"
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton1.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Options.UseBackColor = True
        Me.SimpleButton1.Appearance.Options.UseBorderColor = True
        Me.SimpleButton1.Appearance.Options.UseFont = True
        Me.SimpleButton1.Appearance.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton1.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton1.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton1.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton1.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton1.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton1.Location = New System.Drawing.Point(186, 11)
        Me.SimpleButton1.LookAndFeel.SkinName = "Visual Studio 2013 Light"
        Me.SimpleButton1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(90, 39)
        Me.SimpleButton1.TabIndex = 392
        Me.SimpleButton1.Text = "&Cancel"
        '
        'GridControl1
        '
        Me.GridControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GridControl1.EmbeddedNavigator.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GridControl1.Location = New System.Drawing.Point(0, 0)
        Me.GridControl1.LookAndFeel.SkinMaskColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.GridControl1.LookAndFeel.SkinMaskColor2 = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.GridControl1.LookAndFeel.SkinName = "DevExpress Dark Style"
        Me.GridControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.Bnt_Remove, Me.Col_Chk, Me.BNT_Details, Me.RepositoryItemTextEdit1})
        Me.GridControl1.Size = New System.Drawing.Size(1132, 466)
        Me.GridControl1.TabIndex = 302
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.GridView1.ColumnPanelRowHeight = 32
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2, Me.GridColumn3, Me.GridColumn8, Me.GridColumn11, Me.GridColumn7, Me.GridColumn4, Me.GridColumn5, Me.GridColumn6, Me.GridColumn9, Me.GridColumn10, Me.GridColumn23, Me.GridColumn24, Me.GridColumn25, Me.GridColumn18, Me.GridColumn19})
        Me.GridView1.DetailHeight = 284
        Me.GridView1.FixedLineWidth = 1
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.GroupRowHeight = 27
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsEditForm.PopupEditFormWidth = 934
        Me.GridView1.OptionsFind.AllowFindPanel = False
        Me.GridView1.OptionsFind.AllowMruItems = False
        Me.GridView1.OptionsFilter.AllowFilterEditor = False
        Me.GridView1.OptionsFilter.AllowColumnMRUFilterList = False
        Me.GridView1.OptionsFilter.AllowMRUFilterList = False
        Me.GridView1.OptionsFilter.ShowAllTableValuesInFilterPopup = False
        Me.GridView1.OptionsView.ShowAutoFilterRow = False
        Me.GridView1.OptionsView.BestFitMaxRowCount = 2
        Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsView.EnableAppearanceOddRow = True
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.OptionsView.ShowIndicator = False
        Me.GridView1.RowHeight = 9
        '
        'GridColumn1
        '
        Me.GridColumn1.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn1.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn1.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn1.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.GridColumn1.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn1.AppearanceCell.Options.UseFont = True
        Me.GridColumn1.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn1.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn1.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn1.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn1.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn1.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn1.AppearanceHeader.Options.UseFont = True
        Me.GridColumn1.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn1.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn1.Caption = "#"
        Me.GridColumn1.FieldName = "id"
        Me.GridColumn1.MinWidth = 11
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.OptionsColumn.AllowEdit = False
        Me.GridColumn1.OptionsColumn.AllowFocus = False
        Me.GridColumn1.OptionsColumn.AllowMove = False
        Me.GridColumn1.OptionsColumn.AllowShowHide = False
        Me.GridColumn1.OptionsColumn.FixedWidth = True
        Me.GridColumn1.OptionsColumn.ReadOnly = True
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        Me.GridColumn1.Width = 10
        '
        'GridColumn2
        '
        Me.GridColumn2.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn2.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn2.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn2.AppearanceCell.ForeColor = System.Drawing.Color.White
        Me.GridColumn2.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn2.AppearanceCell.Options.UseFont = True
        Me.GridColumn2.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn2.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn2.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn2.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn2.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn2.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn2.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn2.AppearanceHeader.Options.UseFont = True
        Me.GridColumn2.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn2.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn2.Caption = "Host"
        Me.GridColumn2.ColumnEdit = Me.RepositoryItemTextEdit1
        Me.GridColumn2.FieldName = "smtphost"
        Me.GridColumn2.MinWidth = 11
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.OptionsColumn.AllowEdit = False
        Me.GridColumn2.OptionsColumn.AllowFocus = False
        Me.GridColumn2.OptionsColumn.AllowMove = False
        Me.GridColumn2.OptionsColumn.AllowShowHide = False
        Me.GridColumn2.OptionsColumn.AllowSize = False
        Me.GridColumn2.OptionsColumn.FixedWidth = True
        Me.GridColumn2.OptionsColumn.ReadOnly = True
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 2
        Me.GridColumn2.Width = 59
        '
        'RepositoryItemTextEdit1
        '
        Me.RepositoryItemTextEdit1.AutoHeight = False
        Me.RepositoryItemTextEdit1.Name = "RepositoryItemTextEdit1"
        Me.RepositoryItemTextEdit1.Padding = New System.Windows.Forms.Padding(8, 0, 0, 0)
        '
        'GridColumn3
        '
        Me.GridColumn3.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn3.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn3.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn3.AppearanceCell.ForeColor = System.Drawing.Color.White
        Me.GridColumn3.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn3.AppearanceCell.Options.UseFont = True
        Me.GridColumn3.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn3.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn3.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn3.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn3.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn3.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn3.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn3.AppearanceHeader.Options.UseFont = True
        Me.GridColumn3.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn3.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn3.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn3.Caption = "Email Address"
        Me.GridColumn3.ColumnEdit = Me.RepositoryItemTextEdit1
        Me.GridColumn3.FieldName = "smtpemail"
        Me.GridColumn3.MinWidth = 11
        Me.GridColumn3.Name = "GridColumn3"
        Me.GridColumn3.OptionsColumn.AllowEdit = False
        Me.GridColumn3.OptionsColumn.AllowFocus = False
        Me.GridColumn3.OptionsColumn.AllowMove = False
        Me.GridColumn3.OptionsColumn.AllowShowHide = False
        Me.GridColumn3.OptionsColumn.AllowSize = False
        Me.GridColumn3.OptionsColumn.FixedWidth = True
        Me.GridColumn3.OptionsColumn.ReadOnly = True
        Me.GridColumn3.Visible = True
        Me.GridColumn3.VisibleIndex = 3
        Me.GridColumn3.Width = 69
        '
        'GridColumn8
        '
        Me.GridColumn8.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn8.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn8.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn8.AppearanceCell.ForeColor = System.Drawing.Color.White
        Me.GridColumn8.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn8.AppearanceCell.Options.UseFont = True
        Me.GridColumn8.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn8.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn8.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn8.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn8.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn8.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn8.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn8.AppearanceHeader.Options.UseFont = True
        Me.GridColumn8.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn8.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn8.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn8.Caption = "Port"
        Me.GridColumn8.FieldName = "smtpport"
        Me.GridColumn8.MinWidth = 11
        Me.GridColumn8.Name = "GridColumn8"
        Me.GridColumn8.OptionsColumn.AllowEdit = False
        Me.GridColumn8.OptionsColumn.AllowFocus = False
        Me.GridColumn8.OptionsColumn.AllowMove = False
        Me.GridColumn8.OptionsColumn.AllowShowHide = False
        Me.GridColumn8.OptionsColumn.AllowSize = False
        Me.GridColumn8.OptionsColumn.FixedWidth = True
        Me.GridColumn8.OptionsColumn.ReadOnly = True
        Me.GridColumn8.Visible = True
        Me.GridColumn8.VisibleIndex = 4
        Me.GridColumn8.Width = 17
        '
        'GridColumn11
        '
        Me.GridColumn11.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn11.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn11.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn11.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn11.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn11.AppearanceCell.Options.UseFont = True
        Me.GridColumn11.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn11.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn11.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn11.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn11.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn11.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn11.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn11.AppearanceHeader.Options.UseFont = True
        Me.GridColumn11.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn11.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn11.Caption = "From Name"
        Me.GridColumn11.FieldName = "smtpfromname"
        Me.GridColumn11.MinWidth = 11
        Me.GridColumn11.Name = "GridColumn11"
        Me.GridColumn11.OptionsColumn.AllowEdit = False
        Me.GridColumn11.OptionsColumn.AllowFocus = False
        Me.GridColumn11.OptionsColumn.AllowMove = False
        Me.GridColumn11.OptionsColumn.AllowShowHide = False
        Me.GridColumn11.OptionsColumn.AllowSize = False
        Me.GridColumn11.OptionsColumn.FixedWidth = True
        Me.GridColumn11.OptionsColumn.ReadOnly = True
        Me.GridColumn11.Visible = True
        Me.GridColumn11.VisibleIndex = 6
        Me.GridColumn11.Width = 52
        '
        'GridColumn7
        '
        Me.GridColumn7.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn7.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn7.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn7.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn7.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn7.AppearanceCell.Options.UseFont = True
        Me.GridColumn7.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn7.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn7.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn7.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn7.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn7.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn7.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn7.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn7.AppearanceHeader.Options.UseFont = True
        Me.GridColumn7.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn7.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn7.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn7.Caption = "From Mail"
        Me.GridColumn7.FieldName = "smtpfrommail"
        Me.GridColumn7.MinWidth = 11
        Me.GridColumn7.Name = "GridColumn7"
        Me.GridColumn7.OptionsColumn.AllowEdit = False
        Me.GridColumn7.OptionsColumn.AllowFocus = False
        Me.GridColumn7.OptionsColumn.AllowMove = False
        Me.GridColumn7.OptionsColumn.AllowShowHide = False
        Me.GridColumn7.OptionsColumn.AllowSize = False
        Me.GridColumn7.OptionsColumn.FixedWidth = True
        Me.GridColumn7.OptionsColumn.ReadOnly = True
        Me.GridColumn7.Visible = True
        Me.GridColumn7.VisibleIndex = 7
        Me.GridColumn7.Width = 52
        '
        'GridColumn4
        '
        Me.GridColumn4.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn4.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn4.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn4.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn4.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn4.AppearanceCell.Options.UseFont = True
        Me.GridColumn4.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn4.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn4.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn4.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn4.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn4.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn4.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn4.AppearanceHeader.Options.UseFont = True
        Me.GridColumn4.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn4.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn4.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn4.Caption = "Subject"
        Me.GridColumn4.FieldName = "smtpsubject"
        Me.GridColumn4.MinWidth = 11
        Me.GridColumn4.Name = "GridColumn4"
        Me.GridColumn4.OptionsColumn.AllowEdit = False
        Me.GridColumn4.OptionsColumn.AllowFocus = False
        Me.GridColumn4.OptionsColumn.AllowMove = False
        Me.GridColumn4.OptionsColumn.AllowShowHide = False
        Me.GridColumn4.OptionsColumn.AllowSize = False
        Me.GridColumn4.OptionsColumn.FixedWidth = True
        Me.GridColumn4.OptionsColumn.ReadOnly = True
        Me.GridColumn4.Visible = True
        Me.GridColumn4.VisibleIndex = 8
        Me.GridColumn4.Width = 52
        '
        'GridColumn5
        '
        Me.GridColumn5.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn5.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn5.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn5.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn5.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn5.AppearanceCell.Options.UseFont = True
        Me.GridColumn5.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn5.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn5.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn5.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn5.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn5.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn5.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn5.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn5.AppearanceHeader.Options.UseFont = True
        Me.GridColumn5.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn5.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn5.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn5.Caption = "Details"
        Me.GridColumn5.ColumnEdit = Me.BNT_Details
        Me.GridColumn5.MinWidth = 11
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.OptionsColumn.AllowMove = False
        Me.GridColumn5.OptionsColumn.AllowShowHide = False
        Me.GridColumn5.OptionsColumn.AllowSize = False
        Me.GridColumn5.OptionsColumn.FixedWidth = True
        Me.GridColumn5.OptionsColumn.ReadOnly = True
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 9
        Me.GridColumn5.Width = 24
        '
        'BNT_Details
        '
        Me.BNT_Details.AllowFocused = False
        Me.BNT_Details.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_Details.Appearance.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_Details.Appearance.Options.UseBackColor = True
        Me.BNT_Details.AppearanceFocused.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_Details.AppearanceFocused.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_Details.AppearanceFocused.Options.UseBackColor = True
        Me.BNT_Details.AppearanceReadOnly.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_Details.AppearanceReadOnly.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BNT_Details.AppearanceReadOnly.Options.UseBackColor = True
        Me.BNT_Details.AutoHeight = False
        EditorButtonImageOptions1.Image = CType(resources.GetObject("EditorButtonImageOptions1.Image"), System.Drawing.Image)
        Me.BNT_Details.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, False, EditorButtonImageOptions1, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject1, SerializableAppearanceObject2, SerializableAppearanceObject3, SerializableAppearanceObject4, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.BNT_Details.LookAndFeel.UseDefaultLookAndFeel = False
        Me.BNT_Details.Name = "BNT_Details"
        Me.BNT_Details.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'GridColumn6
        '
        Me.GridColumn6.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn6.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn6.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn6.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn6.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn6.AppearanceCell.Options.UseFont = True
        Me.GridColumn6.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn6.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn6.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn6.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn6.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn6.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn6.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn6.AppearanceHeader.Options.UseFont = True
        Me.GridColumn6.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn6.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn6.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn6.Caption = "Remove"
        Me.GridColumn6.ColumnEdit = Me.Bnt_Remove
        Me.GridColumn6.FieldName = "CanCheck"
        Me.GridColumn6.MinWidth = 11
        Me.GridColumn6.Name = "GridColumn6"
        Me.GridColumn6.OptionsColumn.AllowMove = False
        Me.GridColumn6.OptionsColumn.AllowShowHide = False
        Me.GridColumn6.OptionsColumn.AllowSize = False
        Me.GridColumn6.OptionsColumn.FixedWidth = True
        Me.GridColumn6.OptionsColumn.ReadOnly = True
        Me.GridColumn6.Visible = True
        Me.GridColumn6.VisibleIndex = 10
        Me.GridColumn6.Width = 24
        '
        'Bnt_Remove
        '
        Me.Bnt_Remove.AllowFocused = False
        Me.Bnt_Remove.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.Bnt_Remove.Appearance.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.Bnt_Remove.Appearance.Options.UseBackColor = True
        Me.Bnt_Remove.AppearanceFocused.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.Bnt_Remove.AppearanceFocused.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.Bnt_Remove.AppearanceFocused.Options.UseBackColor = True
        Me.Bnt_Remove.AppearanceReadOnly.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.Bnt_Remove.AppearanceReadOnly.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.Bnt_Remove.AppearanceReadOnly.Options.UseBackColor = True
        Me.Bnt_Remove.AutoHeight = False
        EditorButtonImageOptions2.Image = CType(resources.GetObject("EditorButtonImageOptions2.Image"), System.Drawing.Image)
        Me.Bnt_Remove.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, False, EditorButtonImageOptions2, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject5, SerializableAppearanceObject6, SerializableAppearanceObject7, SerializableAppearanceObject8, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.Bnt_Remove.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Bnt_Remove.Name = "Bnt_Remove"
        Me.Bnt_Remove.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'GridColumn9
        '
        Me.GridColumn9.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn9.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn9.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn9.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(128, Byte), Integer))
        Me.GridColumn9.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn9.AppearanceCell.Options.UseFont = True
        Me.GridColumn9.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn9.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn9.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn9.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn9.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn9.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn9.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn9.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn9.AppearanceHeader.Options.UseFont = True
        Me.GridColumn9.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn9.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn9.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn9.Caption = "SSL"
        Me.GridColumn9.ColumnEdit = Me.Col_Chk
        Me.GridColumn9.FieldName = "smtpssl"
        Me.GridColumn9.MinWidth = 11
        Me.GridColumn9.Name = "GridColumn9"
        Me.GridColumn9.OptionsColumn.AllowMove = False
        Me.GridColumn9.OptionsColumn.AllowShowHide = False
        Me.GridColumn9.OptionsColumn.AllowSize = False
        Me.GridColumn9.OptionsColumn.FixedWidth = True
        Me.GridColumn9.Visible = True
        Me.GridColumn9.VisibleIndex = 5
        Me.GridColumn9.Width = 17
        '
        'Col_Chk
        '
        Me.Col_Chk.AutoHeight = False
        Me.Col_Chk.CheckBoxOptions.Style = DevExpress.XtraEditors.Controls.CheckBoxStyle.CheckBox
        Me.Col_Chk.LookAndFeel.SkinName = "Dark Side"
        Me.Col_Chk.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Col_Chk.Name = "Col_Chk"
        Me.Col_Chk.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        '
        'GridColumn10
        '
        Me.GridColumn10.Caption = "GridColumn10"
        Me.GridColumn10.FieldName = "errormsg"
        Me.GridColumn10.MinWidth = 21
        Me.GridColumn10.Name = "GridColumn10"
        Me.GridColumn10.Width = 80
        '
        'GridColumn23
        '
        Me.GridColumn23.Caption = "smtppassword"
        Me.GridColumn23.FieldName = "smtppassword"
        Me.GridColumn23.MinWidth = 21
        Me.GridColumn23.Name = "GridColumn23"
        Me.GridColumn23.Width = 80
        '
        'GridColumn24
        '
        Me.GridColumn24.Caption = "GridColumn24"
        Me.GridColumn24.FieldName = "tag1"
        Me.GridColumn24.MinWidth = 21
        Me.GridColumn24.Name = "GridColumn24"
        Me.GridColumn24.Width = 80
        '
        'GridColumn25
        '
        Me.GridColumn25.Caption = "GridColumn25"
        Me.GridColumn25.FieldName = "tag2"
        Me.GridColumn25.MinWidth = 21
        Me.GridColumn25.Name = "GridColumn25"
        Me.GridColumn25.Width = 80
        '
        'GridColumn18
        '
        Me.GridColumn18.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn18.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn18.AppearanceCell.ForeColor = System.Drawing.Color.White
        Me.GridColumn18.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn18.AppearanceCell.Options.UseFont = True
        Me.GridColumn18.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn18.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn18.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn18.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn18.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn18.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn18.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn18.AppearanceHeader.Options.UseFont = True
        Me.GridColumn18.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn18.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn18.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn18.Caption = "Smtp Status"
        Me.GridColumn18.FieldName = "smtpstatus"
        Me.GridColumn18.Name = "GridColumn18"
        Me.GridColumn18.OptionsColumn.AllowEdit = False
        Me.GridColumn18.OptionsColumn.AllowFocus = False
        Me.GridColumn18.OptionsColumn.AllowIncrementalSearch = False
        Me.GridColumn18.OptionsColumn.AllowMove = False
        Me.GridColumn18.OptionsColumn.AllowShowHide = False
        Me.GridColumn18.OptionsColumn.AllowSize = False
        Me.GridColumn18.OptionsColumn.FixedWidth = True
        Me.GridColumn18.OptionsColumn.ReadOnly = True
        Me.GridColumn18.Visible = True
        Me.GridColumn18.VisibleIndex = 1
        Me.GridColumn18.Width = 38
        '
        'GridColumn19
        '
        Me.GridColumn19.Caption = "GridColumn19"
        Me.GridColumn19.FieldName = "taghost"
        Me.GridColumn19.Name = "GridColumn19"
        Me.GridColumn19.Width = 74
        '
        'XtraTabPage1
        '
        Me.XtraTabPage1.Controls.Add(Me.Panel3)
        Me.XtraTabPage1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage1.Name = "XtraTabPage1"
        Me.XtraTabPage1.Size = New System.Drawing.Size(1134, 468)
        Me.XtraTabPage1.Text = "   Email List   "
        '
        'Panel3
        '
        Me.Panel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Panel3.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel3.Controls.Add(Me.ProgressBarControl2)
        Me.Panel3.Controls.Add(Me.GridControl2)
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel3.Location = New System.Drawing.Point(0, 0)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(1134, 468)
        Me.Panel3.TabIndex = 315
        '
        'ProgressBarControl2
        '
        Me.ProgressBarControl2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.ProgressBarControl2.Location = New System.Drawing.Point(0, 442)
        Me.ProgressBarControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ProgressBarControl2.MenuManager = Me.BarManager1
        Me.ProgressBarControl2.Name = "ProgressBarControl2"
        Me.ProgressBarControl2.Properties.ShowTitle = True
        Me.ProgressBarControl2.Size = New System.Drawing.Size(1132, 24)
        Me.ProgressBarControl2.TabIndex = 309
        '
        'GridControl2
        '
        Me.GridControl2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GridControl2.EmbeddedNavigator.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GridControl2.Location = New System.Drawing.Point(0, 0)
        Me.GridControl2.LookAndFeel.SkinMaskColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.GridControl2.LookAndFeel.SkinMaskColor2 = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.GridControl2.LookAndFeel.SkinName = "DevExpress Dark Style"
        Me.GridControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GridControl2.MainView = Me.GridView2
        Me.GridControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GridControl2.Name = "GridControl2"
        Me.GridControl2.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemButtonEdit2, Me.RepositoryItemCheckEdit1, Me.RepositoryItemTextEdit2, Me.RepositoryItemImageEdit1, Me.RepositoryItemPictureEdit1})
        Me.GridControl2.Size = New System.Drawing.Size(1132, 466)
        Me.GridControl2.TabIndex = 308
        Me.GridControl2.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView2})
        '
        'GridView2
        '
        Me.GridView2.Appearance.Empty.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.Empty.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.Empty.Options.UseBackColor = True
        Me.GridView2.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.EvenRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.EvenRow.ForeColor = System.Drawing.Color.White
        Me.GridView2.Appearance.EvenRow.Options.UseBackColor = True
        Me.GridView2.Appearance.EvenRow.Options.UseForeColor = True
        Me.GridView2.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.FocusedRow.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridView2.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White
        Me.GridView2.Appearance.FocusedRow.Options.UseBackColor = True
        Me.GridView2.Appearance.FocusedRow.Options.UseFont = True
        Me.GridView2.Appearance.FocusedRow.Options.UseForeColor = True
        Me.GridView2.Appearance.OddRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.OddRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.OddRow.ForeColor = System.Drawing.Color.White
        Me.GridView2.Appearance.OddRow.Options.UseBackColor = True
        Me.GridView2.Appearance.OddRow.Options.UseForeColor = True
        Me.GridView2.Appearance.Row.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.Row.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.Row.ForeColor = System.Drawing.Color.White
        Me.GridView2.Appearance.Row.Options.UseBackColor = True
        Me.GridView2.Appearance.Row.Options.UseForeColor = True
        Me.GridView2.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.Appearance.SelectedRow.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridView2.Appearance.SelectedRow.ForeColor = System.Drawing.Color.White
        Me.GridView2.Appearance.SelectedRow.Options.UseBackColor = True
        Me.GridView2.Appearance.SelectedRow.Options.UseFont = True
        Me.GridView2.Appearance.SelectedRow.Options.UseForeColor = True
        Me.GridView2.AppearancePrint.Row.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.AppearancePrint.Row.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView2.AppearancePrint.Row.Options.UseBackColor = True
        Me.GridView2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.GridView2.ColumnPanelRowHeight = 32
        Me.GridView2.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn12, Me.GridColumn13, Me.GridColumn14, Me.col_status_icon, Me.GridColumn15, Me.GridColumn16, Me.GridColumn17, Me.GridColumn20, Me.GridColumn21, Me.GridColumn22, Me.GridColumn26, Me.GridColumn27, Me.GridColumn28})
        Me.GridView2.DetailHeight = 284
        Me.GridView2.FixedLineWidth = 1
        Me.GridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None
        Me.GridView2.GridControl = Me.GridControl2
        Me.GridView2.GroupRowHeight = 27
        Me.GridView2.Name = "GridView2"
        Me.GridView2.OptionsEditForm.PopupEditFormWidth = 934
        Me.GridView2.OptionsFind.AllowFindPanel = False
        Me.GridView2.OptionsFind.AllowMruItems = False
        Me.GridView2.OptionsView.BestFitMaxRowCount = 2
        Me.GridView2.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView2.OptionsView.EnableAppearanceOddRow = True
        Me.GridView2.OptionsView.ShowGroupPanel = False
        Me.GridView2.OptionsView.ShowIndicator = False
        Me.GridView2.RowHeight = 9
        '
        'GridColumn12
        '
        Me.GridColumn12.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn12.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn12.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold)
        Me.GridColumn12.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.GridColumn12.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn12.AppearanceCell.Options.UseFont = True
        Me.GridColumn12.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn12.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn12.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn12.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn12.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn12.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn12.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn12.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn12.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn12.AppearanceHeader.Options.UseFont = True
        Me.GridColumn12.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn12.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn12.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn12.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn12.Caption = "Number"
        Me.GridColumn12.FieldName = "id"
        Me.GridColumn12.MinWidth = 11
        Me.GridColumn12.Name = "GridColumn12"
        Me.GridColumn12.OptionsColumn.AllowEdit = False
        Me.GridColumn12.OptionsColumn.AllowFocus = False
        Me.GridColumn12.OptionsColumn.AllowMove = False
        Me.GridColumn12.OptionsColumn.AllowShowHide = False
        Me.GridColumn12.OptionsColumn.AllowSize = False
        Me.GridColumn12.OptionsColumn.FixedWidth = True
        Me.GridColumn12.OptionsColumn.ReadOnly = True
        Me.GridColumn12.Visible = True
        Me.GridColumn12.VisibleIndex = 0
        Me.GridColumn12.Width = 50
        '
        'GridColumn13
        '
        Me.GridColumn13.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn13.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn13.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn13.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.GridColumn13.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn13.AppearanceCell.Options.UseFont = True
        Me.GridColumn13.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn13.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn13.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn13.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn13.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn13.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn13.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn13.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn13.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn13.AppearanceHeader.Options.UseFont = True
        Me.GridColumn13.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn13.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn13.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn13.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn13.Caption = "Email Address"
        Me.GridColumn13.ColumnEdit = Me.RepositoryItemTextEdit2
        Me.GridColumn13.FieldName = "emailaddress"
        Me.GridColumn13.MinWidth = 11
        Me.GridColumn13.Name = "GridColumn13"
        Me.GridColumn13.OptionsColumn.AllowEdit = False
        Me.GridColumn13.OptionsColumn.AllowFocus = False
        Me.GridColumn13.OptionsColumn.AllowMove = False
        Me.GridColumn13.OptionsColumn.AllowShowHide = False
        Me.GridColumn13.OptionsColumn.AllowSize = False
        Me.GridColumn13.OptionsColumn.FixedWidth = True
        Me.GridColumn13.OptionsColumn.ReadOnly = True
        Me.GridColumn13.Visible = True
        Me.GridColumn13.VisibleIndex = 1
        Me.GridColumn13.Width = 150
        '
        'RepositoryItemTextEdit2
        '
        Me.RepositoryItemTextEdit2.AutoHeight = False
        Me.RepositoryItemTextEdit2.Name = "RepositoryItemTextEdit2"
        Me.RepositoryItemTextEdit2.Padding = New System.Windows.Forms.Padding(8, 0, 0, 0)
        '
        'GridColumn14
        '
        Me.GridColumn14.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn14.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn14.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn14.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn14.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn14.AppearanceCell.Options.UseFont = True
        Me.GridColumn14.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn14.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn14.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn14.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn14.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn14.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn14.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn14.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn14.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn14.AppearanceHeader.Options.UseFont = True
        Me.GridColumn14.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn14.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn14.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn14.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn14.Caption = "Delivery Status"
        Me.GridColumn14.FieldName = "deliverystatus"
        Me.GridColumn14.MinWidth = 11
        Me.GridColumn14.Name = "GridColumn14"
        Me.GridColumn14.OptionsColumn.AllowEdit = False
        Me.GridColumn14.OptionsColumn.AllowFocus = False
        Me.GridColumn14.OptionsColumn.AllowMove = False
        Me.GridColumn14.OptionsColumn.AllowShowHide = False
        Me.GridColumn14.OptionsColumn.AllowSize = False
        Me.GridColumn14.OptionsColumn.FixedWidth = True
        Me.GridColumn14.OptionsColumn.ReadOnly = True
        Me.GridColumn14.Visible = True
        Me.GridColumn14.VisibleIndex = 2
        Me.GridColumn14.Width = 80
        '
        'col_status_icon
        '
        Me.col_status_icon.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.col_status_icon.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.col_status_icon.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.col_status_icon.AppearanceCell.Options.UseBackColor = True
        Me.col_status_icon.AppearanceCell.Options.UseFont = True
        Me.col_status_icon.AppearanceCell.Options.UseForeColor = True
        Me.col_status_icon.AppearanceCell.Options.UseTextOptions = True
        Me.col_status_icon.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.col_status_icon.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.col_status_icon.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.col_status_icon.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.col_status_icon.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.col_status_icon.AppearanceHeader.Options.UseBackColor = True
        Me.col_status_icon.AppearanceHeader.Options.UseFont = True
        Me.col_status_icon.AppearanceHeader.Options.UseForeColor = True
        Me.col_status_icon.AppearanceHeader.Options.UseTextOptions = True
        Me.col_status_icon.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.col_status_icon.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.col_status_icon.Caption = "I"
        Me.col_status_icon.MinWidth = 11
        Me.col_status_icon.Name = "col_status_icon"
        Me.col_status_icon.OptionsColumn.AllowEdit = False
        Me.col_status_icon.OptionsColumn.FixedWidth = True
        Me.col_status_icon.Visible = True
        Me.col_status_icon.VisibleIndex = 3
        Me.col_status_icon.Width = 20
        '
        'GridColumn15
        '
        Me.GridColumn15.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn15.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn15.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn15.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn15.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn15.AppearanceCell.Options.UseFont = True
        Me.GridColumn15.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn15.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn15.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.GridColumn15.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn15.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn15.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn15.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn15.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn15.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn15.AppearanceHeader.Options.UseFont = True
        Me.GridColumn15.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn15.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn15.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn15.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn15.Caption = "Response Message"
        Me.GridColumn15.ColumnEdit = Me.RepositoryItemTextEdit2
        Me.GridColumn15.FieldName = "responsemessage"
        Me.GridColumn15.MinWidth = 11
        Me.GridColumn15.Name = "GridColumn15"
        Me.GridColumn15.OptionsColumn.AllowEdit = False
        Me.GridColumn15.OptionsColumn.AllowFocus = False
        Me.GridColumn15.OptionsColumn.AllowMove = False
        Me.GridColumn15.OptionsColumn.AllowShowHide = False
        Me.GridColumn15.OptionsColumn.AllowSize = False
        Me.GridColumn15.OptionsColumn.FixedWidth = True
        Me.GridColumn15.OptionsColumn.ReadOnly = True
        Me.GridColumn15.Visible = True
        Me.GridColumn15.VisibleIndex = 4
        Me.GridColumn15.Width = 171
        '
        'GridColumn16
        '
        Me.GridColumn16.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn16.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn16.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn16.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn16.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn16.AppearanceCell.Options.UseFont = True
        Me.GridColumn16.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn16.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn16.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn16.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn16.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn16.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn16.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn16.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn16.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn16.AppearanceHeader.Options.UseFont = True
        Me.GridColumn16.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn16.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn16.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn16.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn16.Caption = "Date"
        Me.GridColumn16.FieldName = "date"
        Me.GridColumn16.MinWidth = 11
        Me.GridColumn16.Name = "GridColumn16"
        Me.GridColumn16.OptionsColumn.AllowEdit = False
        Me.GridColumn16.OptionsColumn.AllowFocus = False
        Me.GridColumn16.OptionsColumn.AllowMove = False
        Me.GridColumn16.OptionsColumn.AllowShowHide = False
        Me.GridColumn16.OptionsColumn.AllowSize = False
        Me.GridColumn16.OptionsColumn.FixedWidth = True
        Me.GridColumn16.OptionsColumn.ReadOnly = True
        Me.GridColumn16.Visible = True
        Me.GridColumn16.VisibleIndex = 5
        Me.GridColumn16.Width = 52
        '
        'GridColumn17
        '
        Me.GridColumn17.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn17.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn17.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn17.AppearanceCell.ForeColor = System.Drawing.Color.White
        Me.GridColumn17.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn17.AppearanceCell.Options.UseFont = True
        Me.GridColumn17.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn17.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn17.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn17.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn17.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn17.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn17.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn17.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn17.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn17.AppearanceHeader.Options.UseFont = True
        Me.GridColumn17.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn17.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn17.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn17.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn17.Caption = "Time"
        Me.GridColumn17.FieldName = "time"
        Me.GridColumn17.MinWidth = 11
        Me.GridColumn17.Name = "GridColumn17"
        Me.GridColumn17.OptionsColumn.AllowEdit = False
        Me.GridColumn17.OptionsColumn.AllowFocus = False
        Me.GridColumn17.OptionsColumn.AllowMove = False
        Me.GridColumn17.OptionsColumn.AllowShowHide = False
        Me.GridColumn17.OptionsColumn.AllowSize = False
        Me.GridColumn17.OptionsColumn.FixedWidth = True
        Me.GridColumn17.OptionsColumn.ReadOnly = True
        Me.GridColumn17.Visible = True
        Me.GridColumn17.VisibleIndex = 6
        Me.GridColumn17.Width = 52
        '
        'GridColumn20
        '
        Me.GridColumn20.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn20.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn20.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn20.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn20.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn20.AppearanceCell.Options.UseFont = True
        Me.GridColumn20.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn20.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn20.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn20.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn20.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn20.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn20.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn20.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn20.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn20.AppearanceHeader.Options.UseFont = True
        Me.GridColumn20.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn20.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn20.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn20.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn20.Caption = "Remove"
        Me.GridColumn20.ColumnEdit = Me.RepositoryItemButtonEdit2
        Me.GridColumn20.FieldName = "CanCheck"
        Me.GridColumn20.MinWidth = 11
        Me.GridColumn20.Name = "GridColumn20"
        Me.GridColumn20.OptionsColumn.AllowMove = False
        Me.GridColumn20.OptionsColumn.AllowShowHide = False
        Me.GridColumn20.OptionsColumn.AllowSize = False
        Me.GridColumn20.OptionsColumn.FixedWidth = True
        Me.GridColumn20.OptionsColumn.ReadOnly = True
        Me.GridColumn20.Visible = True
        Me.GridColumn20.VisibleIndex = 7
        Me.GridColumn20.Width = 34
        '
        'RepositoryItemButtonEdit2
        '
        Me.RepositoryItemButtonEdit2.AllowFocused = False
        Me.RepositoryItemButtonEdit2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.Appearance.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.Appearance.Options.UseBackColor = True
        Me.RepositoryItemButtonEdit2.AppearanceFocused.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.AppearanceFocused.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.AppearanceFocused.Options.UseBackColor = True
        Me.RepositoryItemButtonEdit2.AppearanceReadOnly.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.AppearanceReadOnly.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RepositoryItemButtonEdit2.AppearanceReadOnly.Options.UseBackColor = True
        Me.RepositoryItemButtonEdit2.AutoHeight = False
        EditorButtonImageOptions3.Image = CType(resources.GetObject("EditorButtonImageOptions3.Image"), System.Drawing.Image)
        Me.RepositoryItemButtonEdit2.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, False, EditorButtonImageOptions3, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject9, SerializableAppearanceObject10, SerializableAppearanceObject11, SerializableAppearanceObject12, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.RepositoryItemButtonEdit2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.RepositoryItemButtonEdit2.Name = "RepositoryItemButtonEdit2"
        Me.RepositoryItemButtonEdit2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'GridColumn21
        '
        Me.GridColumn21.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn21.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn21.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn21.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn21.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn21.AppearanceCell.Options.UseFont = True
        Me.GridColumn21.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn21.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn21.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn21.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn21.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn21.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn21.AppearanceHeader.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.GridColumn21.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn21.AppearanceHeader.Options.UseFont = True
        Me.GridColumn21.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn21.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn21.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn21.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn21.Caption = "Select"
        Me.GridColumn21.ColumnEdit = Me.RepositoryItemCheckEdit1
        Me.GridColumn21.FieldName = "CanCheck"
        Me.GridColumn21.MinWidth = 11
        Me.GridColumn21.Name = "GridColumn21"
        Me.GridColumn21.OptionsColumn.AllowEdit = False
        Me.GridColumn21.OptionsColumn.AllowFocus = False
        Me.GridColumn21.OptionsColumn.AllowMove = False
        Me.GridColumn21.OptionsColumn.AllowShowHide = False
        Me.GridColumn21.OptionsColumn.AllowSize = False
        Me.GridColumn21.OptionsColumn.FixedWidth = True
        Me.GridColumn21.OptionsColumn.ReadOnly = True
        Me.GridColumn21.Width = 17
        '
        'RepositoryItemCheckEdit1
        '
        Me.RepositoryItemCheckEdit1.AutoHeight = False
        Me.RepositoryItemCheckEdit1.CheckBoxOptions.Style = DevExpress.XtraEditors.Controls.CheckBoxStyle.CheckBox
        Me.RepositoryItemCheckEdit1.LookAndFeel.SkinName = "Dark Side"
        Me.RepositoryItemCheckEdit1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.RepositoryItemCheckEdit1.Name = "RepositoryItemCheckEdit1"
        Me.RepositoryItemCheckEdit1.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        '
        'GridColumn22
        '
        Me.GridColumn22.Caption = "GridColumn10"
        Me.GridColumn22.FieldName = "errormsg"
        Me.GridColumn22.MinWidth = 21
        Me.GridColumn22.Name = "GridColumn22"
        Me.GridColumn22.Width = 80
        '
        'GridColumn26
        '
        Me.GridColumn26.Caption = "smtppassword"
        Me.GridColumn26.FieldName = "smtppassword"
        Me.GridColumn26.MinWidth = 21
        Me.GridColumn26.Name = "GridColumn26"
        Me.GridColumn26.Width = 80
        '
        'GridColumn27
        '
        Me.GridColumn27.Caption = "GridColumn24"
        Me.GridColumn27.FieldName = "tag1"
        Me.GridColumn27.MinWidth = 21
        Me.GridColumn27.Name = "GridColumn27"
        Me.GridColumn27.Width = 80
        '
        'GridColumn28
        '
        Me.GridColumn28.Caption = "GridColumn25"
        Me.GridColumn28.FieldName = "tag2"
        Me.GridColumn28.MinWidth = 21
        Me.GridColumn28.Name = "GridColumn28"
        Me.GridColumn28.Width = 80
        '
        'RepositoryItemImageEdit1
        '
        Me.RepositoryItemImageEdit1.AutoHeight = False
        Me.RepositoryItemImageEdit1.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.RepositoryItemImageEdit1.Name = "RepositoryItemImageEdit1"
        '
        'RepositoryItemPictureEdit1
        '
        Me.RepositoryItemPictureEdit1.Name = "RepositoryItemPictureEdit1"
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.XtraTabControl1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.Appearance.Options.UseFont = True
        Me.XtraTabControl1.AppearancePage.Header.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.XtraTabControl1.AppearancePage.Header.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.XtraTabControl1.AppearancePage.Header.Font = New System.Drawing.Font("Dubai", 11.0!)
        Me.XtraTabControl1.AppearancePage.Header.Options.UseBackColor = True
        Me.XtraTabControl1.AppearancePage.Header.Options.UseFont = True
        Me.XtraTabControl1.AppearancePage.HeaderActive.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.XtraTabControl1.AppearancePage.HeaderActive.Options.UseBackColor = True
        Me.XtraTabControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.XtraTabControl1.BorderStylePage = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.XtraTabControl1.Location = New System.Drawing.Point(16, 16)
        Me.XtraTabControl1.LookAndFeel.SkinName = "Seven Classic"
        Me.XtraTabControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabControl1.MultiLine = DevExpress.Utils.DefaultBoolean.[False]
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.TabSmtp
        Me.XtraTabControl1.Size = New System.Drawing.Size(1137, 505)
        Me.XtraTabControl1.TabIndex = 301
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.XtraTabPage1, Me.TabSmtp, Me.XtraTabPage2, Me.XtraTabPage3, Me.XtraTabPage6})
        '
        'XtraTabPage3
        '
        Me.XtraTabPage3.Controls.Add(Me.Panel5)
        Me.XtraTabPage3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage3.Name = "XtraTabPage3"
        Me.XtraTabPage3.Size = New System.Drawing.Size(1134, 468)
        Me.XtraTabPage3.Text = "   Proxy Manager   "
        '
        'Panel5
        '
        Me.Panel5.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Panel5.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel5.Controls.Add(Me.ToggleSwitch3)
        Me.Panel5.Controls.Add(Me.lblTotalPproxyInvalid)
        Me.Panel5.Controls.Add(Me.lblProxyvalid)
        Me.Panel5.Controls.Add(Me.lblTotalProxy)
        Me.Panel5.Controls.Add(Me.ToggleSwitch2)
        Me.Panel5.Controls.Add(Me.BntSaveProxyvalid)
        Me.Panel5.Controls.Add(Me.BntRemoveAll)
        Me.Panel5.Controls.Add(Me.BntRemoveInvalid)
        Me.Panel5.Controls.Add(Me.BntStartCheckProxy)
        Me.Panel5.Controls.Add(Me.BntSelectListProxy)
        Me.Panel5.Controls.Add(Me.BntProxyReport)
        Me.Panel5.Controls.Add(Me.ProgressBarControl3)
        Me.Panel5.Controls.Add(Me.GridControl3)
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel5.Location = New System.Drawing.Point(0, 0)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(1134, 468)
        Me.Panel5.TabIndex = 0
        '
        'ToggleSwitch3
        '
        Me.ToggleSwitch3.Location = New System.Drawing.Point(902, 27)
        Me.ToggleSwitch3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ToggleSwitch3.Name = "ToggleSwitch3"
        Me.ToggleSwitch3.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.ToggleSwitch3.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.ToggleSwitch3.Properties.Appearance.Options.UseFont = True
        Me.ToggleSwitch3.Properties.Appearance.Options.UseForeColor = True
        Me.ToggleSwitch3.Properties.OffText = "Normal "
        Me.ToggleSwitch3.Properties.OnText = "Send With Proxy"
        Me.ToggleSwitch3.Size = New System.Drawing.Size(213, 24)
        Me.ToggleSwitch3.TabIndex = 328
        '
        'lblTotalPproxyInvalid
        '
        Me.lblTotalPproxyInvalid.Location = New System.Drawing.Point(0, 0)
        Me.lblTotalPproxyInvalid.Name = "lblTotalPproxyInvalid"
        Me.lblTotalPproxyInvalid.Size = New System.Drawing.Size(100, 23)
        Me.lblTotalPproxyInvalid.TabIndex = 0
        '
        'lblProxyvalid
        '
        Me.lblProxyvalid.Location = New System.Drawing.Point(0, 0)
        Me.lblProxyvalid.Name = "lblProxyvalid"
        Me.lblProxyvalid.Size = New System.Drawing.Size(100, 23)
        Me.lblProxyvalid.TabIndex = 1
        '
        'lblTotalProxy
        '
        Me.lblTotalProxy.Location = New System.Drawing.Point(0, 0)
        Me.lblTotalProxy.Name = "lblTotalProxy"
        Me.lblTotalProxy.Size = New System.Drawing.Size(100, 23)
        Me.lblTotalProxy.TabIndex = 2
        '
        'ToggleSwitch2
        '
        Me.ToggleSwitch2.Location = New System.Drawing.Point(0, 0)
        Me.ToggleSwitch2.Name = "ToggleSwitch2"
        Me.ToggleSwitch2.Properties.OffText = "Off"
        Me.ToggleSwitch2.Properties.OnText = "On"
        Me.ToggleSwitch2.Size = New System.Drawing.Size(95, 24)
        Me.ToggleSwitch2.TabIndex = 3
        '
        'BntSaveProxyvalid
        '
        Me.BntSaveProxyvalid.Location = New System.Drawing.Point(0, 0)
        Me.BntSaveProxyvalid.Name = "BntSaveProxyvalid"
        Me.BntSaveProxyvalid.Size = New System.Drawing.Size(75, 23)
        Me.BntSaveProxyvalid.TabIndex = 4
        '
        'BntRemoveAll
        '
        Me.BntRemoveAll.Location = New System.Drawing.Point(0, 0)
        Me.BntRemoveAll.Name = "BntRemoveAll"
        Me.BntRemoveAll.Size = New System.Drawing.Size(75, 23)
        Me.BntRemoveAll.TabIndex = 5
        '
        'BntRemoveInvalid
        '
        Me.BntRemoveInvalid.Location = New System.Drawing.Point(0, 0)
        Me.BntRemoveInvalid.Name = "BntRemoveInvalid"
        Me.BntRemoveInvalid.Size = New System.Drawing.Size(75, 23)
        Me.BntRemoveInvalid.TabIndex = 6
        '
        'BntStartCheckProxy
        '
        Me.BntStartCheckProxy.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntStartCheckProxy.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntStartCheckProxy.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntStartCheckProxy.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntStartCheckProxy.Appearance.Options.UseBackColor = True
        Me.BntStartCheckProxy.Appearance.Options.UseBorderColor = True
        Me.BntStartCheckProxy.Appearance.Options.UseFont = True
        Me.BntStartCheckProxy.Appearance.Options.UseForeColor = True
        Me.BntStartCheckProxy.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntStartCheckProxy.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntStartCheckProxy.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntStartCheckProxy.AppearanceHovered.Options.UseBackColor = True
        Me.BntStartCheckProxy.AppearanceHovered.Options.UseBorderColor = True
        Me.BntStartCheckProxy.AppearanceHovered.Options.UseForeColor = True
        Me.BntStartCheckProxy.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntStartCheckProxy.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntStartCheckProxy.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntStartCheckProxy.AppearancePressed.Options.UseBackColor = True
        Me.BntStartCheckProxy.AppearancePressed.Options.UseBorderColor = True
        Me.BntStartCheckProxy.AppearancePressed.Options.UseForeColor = True
        Me.BntStartCheckProxy.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntStartCheckProxy.Location = New System.Drawing.Point(182, 16)
        Me.BntStartCheckProxy.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntStartCheckProxy.Name = "BntStartCheckProxy"
        Me.BntStartCheckProxy.Size = New System.Drawing.Size(158, 45)
        Me.BntStartCheckProxy.TabIndex = 3
        Me.BntStartCheckProxy.Text = "Start Check Proxy"
        '
        'BntSelectListProxy
        '
        Me.BntSelectListProxy.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSelectListProxy.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSelectListProxy.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntSelectListProxy.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSelectListProxy.Appearance.Options.UseBackColor = True
        Me.BntSelectListProxy.Appearance.Options.UseBorderColor = True
        Me.BntSelectListProxy.Appearance.Options.UseFont = True
        Me.BntSelectListProxy.Appearance.Options.UseForeColor = True
        Me.BntSelectListProxy.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntSelectListProxy.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSelectListProxy.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntSelectListProxy.AppearanceHovered.Options.UseBackColor = True
        Me.BntSelectListProxy.AppearanceHovered.Options.UseBorderColor = True
        Me.BntSelectListProxy.AppearanceHovered.Options.UseForeColor = True
        Me.BntSelectListProxy.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSelectListProxy.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSelectListProxy.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntSelectListProxy.AppearancePressed.Options.UseBackColor = True
        Me.BntSelectListProxy.AppearancePressed.Options.UseBorderColor = True
        Me.BntSelectListProxy.AppearancePressed.Options.UseForeColor = True
        Me.BntSelectListProxy.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntSelectListProxy.Location = New System.Drawing.Point(16, 16)
        Me.BntSelectListProxy.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntSelectListProxy.Name = "BntSelectListProxy"
        Me.BntSelectListProxy.Size = New System.Drawing.Size(158, 45)
        Me.BntSelectListProxy.TabIndex = 2
        Me.BntSelectListProxy.Text = "Select List Proxy"
        '
        'BntProxyReport
        '
        Me.BntProxyReport.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.BntProxyReport.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.BntProxyReport.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntProxyReport.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntProxyReport.Appearance.Options.UseBackColor = True
        Me.BntProxyReport.Appearance.Options.UseBorderColor = True
        Me.BntProxyReport.Appearance.Options.UseFont = True
        Me.BntProxyReport.Appearance.Options.UseForeColor = True
        Me.BntProxyReport.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(205, Byte), Integer), CType(CType(50, Byte), Integer))
        Me.BntProxyReport.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(205, Byte), Integer), CType(CType(50, Byte), Integer))
        Me.BntProxyReport.AppearanceHovered.ForeColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntProxyReport.AppearanceHovered.Options.UseBackColor = True
        Me.BntProxyReport.AppearanceHovered.Options.UseBorderColor = True
        Me.BntProxyReport.AppearanceHovered.Options.UseForeColor = True
        Me.BntProxyReport.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(40, Byte), Integer))
        Me.BntProxyReport.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(40, Byte), Integer))
        Me.BntProxyReport.AppearancePressed.ForeColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntProxyReport.AppearancePressed.Options.UseBackColor = True
        Me.BntProxyReport.AppearancePressed.Options.UseBorderColor = True
        Me.BntProxyReport.AppearancePressed.Options.UseForeColor = True
        Me.BntProxyReport.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntProxyReport.Location = New System.Drawing.Point(680, 16)
        Me.BntProxyReport.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntProxyReport.Name = "BntProxyReport"
        Me.BntProxyReport.Size = New System.Drawing.Size(158, 45)
        Me.BntProxyReport.TabIndex = 7
        Me.BntProxyReport.Text = "Proxy Report"
        '
        'ProgressBarControl3
        '
        Me.ProgressBarControl3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ProgressBarControl3.Location = New System.Drawing.Point(16, 796)
        Me.ProgressBarControl3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ProgressBarControl3.Name = "ProgressBarControl3"
        Me.ProgressBarControl3.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ProgressBarControl3.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.ProgressBarControl3.Properties.LookAndFeel.SkinName = "Office 2019 Black"
        Me.ProgressBarControl3.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ProgressBarControl3.Properties.ProgressViewStyle = DevExpress.XtraEditors.Controls.ProgressViewStyle.Solid
        Me.ProgressBarControl3.Size = New System.Drawing.Size(2032, 20)
        Me.ProgressBarControl3.TabIndex = 1
        '
        'GridControl3
        '
        Me.GridControl3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl3.Location = New System.Drawing.Point(-1, 100)
        Me.GridControl3.MainView = Me.GridView3
        Me.GridControl3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GridControl3.Name = "GridControl3"
        Me.GridControl3.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.RepositoryItemButtonEdit3})
        Me.GridControl3.Size = New System.Drawing.Size(1129, 363)
        Me.GridControl3.TabIndex = 0
        Me.GridControl3.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView3})
        '
        'GridView3
        '
        Me.GridView3.GridControl = Me.GridControl3
        Me.GridView3.Name = "GridView3"
        '
        'RepositoryItemButtonEdit3
        '
        Me.RepositoryItemButtonEdit3.AutoHeight = False
        Me.RepositoryItemButtonEdit3.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(), New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Delete)})
        Me.RepositoryItemButtonEdit3.Name = "RepositoryItemButtonEdit3"
        Me.RepositoryItemButtonEdit3.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'XtraTabPage6
        '
        Me.XtraTabPage6.Controls.Add(Me.Panel8)
        Me.XtraTabPage6.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage6.Name = "XtraTabPage6"
        Me.XtraTabPage6.Size = New System.Drawing.Size(1134, 468)
        Me.XtraTabPage6.Text = "  Settings Sender"
        '
        'Panel8
        '
        Me.Panel8.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Panel8.Controls.Add(Me.XtraTabControl2)
        Me.Panel8.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel8.Location = New System.Drawing.Point(0, 0)
        Me.Panel8.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Panel8.Name = "Panel8"
        Me.Panel8.Size = New System.Drawing.Size(1134, 468)
        Me.Panel8.TabIndex = 0
        '
        'XtraTabControl2
        '
        Me.XtraTabControl2.AppearancePage.HeaderActive.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.XtraTabControl2.AppearancePage.HeaderActive.Options.UseBackColor = True
        Me.XtraTabControl2.Location = New System.Drawing.Point(56, 33)
        Me.XtraTabControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabControl2.Name = "XtraTabControl2"
        Me.XtraTabControl2.SelectedTabPage = Me.XtraTabPage12
        Me.XtraTabControl2.Size = New System.Drawing.Size(749, 414)
        Me.XtraTabControl2.TabIndex = 0
        Me.XtraTabControl2.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.XtraTabPage10, Me.XtraTabPage8, Me.XtraTabPage9, Me.XtraTabPage11, Me.XtraTabPage12, Me.XtraTabPage7})
        '
        'XtraTabPage12
        '
        Me.XtraTabPage12.Appearance.PageClient.BackColor = System.Drawing.Color.Black
        Me.XtraTabPage12.Appearance.PageClient.Options.UseBackColor = True
        Me.XtraTabPage12.Controls.Add(Me.GroupControl4)
        Me.XtraTabPage12.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage12.Name = "XtraTabPage12"
        Me.XtraTabPage12.Size = New System.Drawing.Size(747, 383)
        Me.XtraTabPage12.Text = "  Letter Convertor"
        '
        'GroupControl4
        '
        Me.GroupControl4.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.249999!)
        Me.GroupControl4.AppearanceCaption.Options.UseFont = True
        Me.GroupControl4.Controls.Add(Me.txtTitilOffice365)
        Me.GroupControl4.Controls.Add(Me.ComboBoxEdit1)
        Me.GroupControl4.Controls.Add(Me.Switch_LetterConvertorLink)
        Me.GroupControl4.Controls.Add(Me.txt_LetterConvertorLink)
        Me.GroupControl4.Controls.Add(Me.bntResetLetterConvertor)
        Me.GroupControl4.Location = New System.Drawing.Point(164, 30)
        Me.GroupControl4.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl4.Name = "GroupControl4"
        Me.GroupControl4.Size = New System.Drawing.Size(416, 315)
        Me.GroupControl4.TabIndex = 327
        Me.GroupControl4.Text = "Letter Convertor "
        '
        'txtTitilOffice365
        '
        Me.txtTitilOffice365.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtTitilOffice365.EditValue = "No preview is available."
        Me.txtTitilOffice365.Location = New System.Drawing.Point(32, 76)
        Me.txtTitilOffice365.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtTitilOffice365.Name = "txtTitilOffice365"
        Me.txtTitilOffice365.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtTitilOffice365.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtTitilOffice365.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtTitilOffice365.Properties.Appearance.Options.UseBackColor = True
        Me.txtTitilOffice365.Properties.Appearance.Options.UseFont = True
        Me.txtTitilOffice365.Properties.Appearance.Options.UseForeColor = True
        Me.txtTitilOffice365.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtTitilOffice365.Size = New System.Drawing.Size(363, 30)
        Me.txtTitilOffice365.TabIndex = 65
        Me.txtTitilOffice365.ToolTipTitle = "Choose a name For Attachment"
        '
        'ComboBoxEdit1
        '
        Me.ComboBoxEdit1.EditValue = ""
        Me.ComboBoxEdit1.Location = New System.Drawing.Point(318, 53)
        Me.ComboBoxEdit1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ComboBoxEdit1.Name = "ComboBoxEdit1"
        Me.ComboBoxEdit1.Properties.AllowFocused = False
        Me.ComboBoxEdit1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.ComboBoxEdit1.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.ComboBoxEdit1.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.ComboBoxEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBoxEdit1.Properties.Appearance.Options.UseFont = True
        Me.ComboBoxEdit1.Properties.Appearance.Options.UseForeColor = True
        Me.ComboBoxEdit1.Properties.AppearanceDropDown.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(44, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.ComboBoxEdit1.Properties.AppearanceDropDown.ForeColor = System.Drawing.Color.White
        Me.ComboBoxEdit1.Properties.AppearanceDropDown.Options.UseBackColor = True
        Me.ComboBoxEdit1.Properties.AppearanceDropDown.Options.UseForeColor = True
        Me.ComboBoxEdit1.Properties.AppearanceFocused.ForeColor = System.Drawing.Color.White
        Me.ComboBoxEdit1.Properties.AppearanceFocused.Options.UseForeColor = True
        Me.ComboBoxEdit1.Properties.AppearanceItemHighlight.BackColor = System.Drawing.Color.DarkSlateGray
        Me.ComboBoxEdit1.Properties.AppearanceItemHighlight.ForeColor = System.Drawing.Color.White
        Me.ComboBoxEdit1.Properties.AppearanceItemHighlight.Options.UseBackColor = True
        Me.ComboBoxEdit1.Properties.AppearanceItemHighlight.Options.UseForeColor = True
        Me.ComboBoxEdit1.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.ComboBoxEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxEdit1.Properties.DropDownRows = 20
        Me.ComboBoxEdit1.Properties.Items.AddRange(New Object() {"Add Link", "Encoded Email 64", "Random Browser", "Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Country", "Random IP", "Random IP China", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Send With Logo", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show Random Fake Email Address Office", "Show Random Fake Phone Numbers", "Show Random Street New York", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.ComboBoxEdit1.Properties.LookAndFeel.SkinName = "Sharp"
        Me.ComboBoxEdit1.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ComboBoxEdit1.Properties.NullValuePrompt = "Select Tag..."
        Me.ComboBoxEdit1.Properties.Sorted = True
        Me.ComboBoxEdit1.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.ComboBoxEdit1.Size = New System.Drawing.Size(76, 18)
        Me.ComboBoxEdit1.TabIndex = 45
        Me.ComboBoxEdit1.Visible = False
        '
        'Switch_LetterConvertorLink
        '
        Me.Switch_LetterConvertorLink.Location = New System.Drawing.Point(84, 123)
        Me.Switch_LetterConvertorLink.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Switch_LetterConvertorLink.Name = "Switch_LetterConvertorLink"
        Me.Switch_LetterConvertorLink.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.Switch_LetterConvertorLink.Properties.Appearance.Options.UseFont = True
        Me.Switch_LetterConvertorLink.Properties.OffText = "Letter to [ HTML]"
        Me.Switch_LetterConvertorLink.Properties.OnText = "Letter To PNG"
        Me.Switch_LetterConvertorLink.Size = New System.Drawing.Size(260, 24)
        Me.Switch_LetterConvertorLink.TabIndex = 1
        '
        'txt_LetterConvertorLink
        '
        Me.txt_LetterConvertorLink.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_LetterConvertorLink.EditValue = ""
        Me.txt_LetterConvertorLink.Location = New System.Drawing.Point(32, 169)
        Me.txt_LetterConvertorLink.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_LetterConvertorLink.Name = "txt_LetterConvertorLink"
        Me.txt_LetterConvertorLink.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_LetterConvertorLink.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_LetterConvertorLink.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.txt_LetterConvertorLink.Properties.Appearance.Options.UseBackColor = True
        Me.txt_LetterConvertorLink.Properties.Appearance.Options.UseFont = True
        Me.txt_LetterConvertorLink.Properties.Appearance.Options.UseForeColor = True
        Me.txt_LetterConvertorLink.Properties.ContextImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.link1
        Me.txt_LetterConvertorLink.Properties.NullValuePrompt = "Link Here"
        Me.txt_LetterConvertorLink.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_LetterConvertorLink.Size = New System.Drawing.Size(363, 30)
        Me.txt_LetterConvertorLink.TabIndex = 66
        Me.txt_LetterConvertorLink.ToolTipTitle = "Link Here"
        '
        'bntResetLetterConvertor
        '
        Me.bntResetLetterConvertor.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntResetLetterConvertor.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntResetLetterConvertor.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntResetLetterConvertor.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntResetLetterConvertor.Appearance.Options.UseBackColor = True
        Me.bntResetLetterConvertor.Appearance.Options.UseBorderColor = True
        Me.bntResetLetterConvertor.Appearance.Options.UseFont = True
        Me.bntResetLetterConvertor.Appearance.Options.UseForeColor = True
        Me.bntResetLetterConvertor.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntResetLetterConvertor.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntResetLetterConvertor.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntResetLetterConvertor.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntResetLetterConvertor.AppearanceDisabled.Options.UseBackColor = True
        Me.bntResetLetterConvertor.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntResetLetterConvertor.AppearanceDisabled.Options.UseFont = True
        Me.bntResetLetterConvertor.AppearanceDisabled.Options.UseForeColor = True
        Me.bntResetLetterConvertor.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntResetLetterConvertor.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntResetLetterConvertor.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntResetLetterConvertor.AppearanceHovered.Options.UseBackColor = True
        Me.bntResetLetterConvertor.AppearanceHovered.Options.UseBorderColor = True
        Me.bntResetLetterConvertor.AppearanceHovered.Options.UseForeColor = True
        Me.bntResetLetterConvertor.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntResetLetterConvertor.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntResetLetterConvertor.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntResetLetterConvertor.AppearancePressed.Options.UseBackColor = True
        Me.bntResetLetterConvertor.AppearancePressed.Options.UseBorderColor = True
        Me.bntResetLetterConvertor.AppearancePressed.Options.UseForeColor = True
        Me.bntResetLetterConvertor.Cursor = System.Windows.Forms.Cursors.Hand
        Me.bntResetLetterConvertor.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.bntResetLetterConvertor.Location = New System.Drawing.Point(38, 230)
        Me.bntResetLetterConvertor.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.bntResetLetterConvertor.Name = "bntResetLetterConvertor"
        Me.bntResetLetterConvertor.Size = New System.Drawing.Size(158, 45)
        Me.bntResetLetterConvertor.TabIndex = 401
        Me.bntResetLetterConvertor.Text = "Remove"
        '
        'XtraTabPage10
        '
        Me.XtraTabPage10.Controls.Add(Me.GroupControl6)
        Me.XtraTabPage10.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage10.Name = "XtraTabPage10"
        Me.XtraTabPage10.Size = New System.Drawing.Size(747, 383)
        Me.XtraTabPage10.Text = "  Attachment"
        '
        'GroupControl6
        '
        Me.GroupControl6.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.249999!)
        Me.GroupControl6.AppearanceCaption.Options.UseFont = True
        Me.GroupControl6.Controls.Add(Me.txtbody)
        Me.GroupControl6.Controls.Add(Me.lblattacmentFileCount)
        Me.GroupControl6.Controls.Add(Me.ToggleSwitch1)
        Me.GroupControl6.Controls.Add(Me.cbTags)
        Me.GroupControl6.Controls.Add(Me.bntAddAttachment)
        Me.GroupControl6.Controls.Add(Me.txtattach)
        Me.GroupControl6.Controls.Add(Me.BntRemoveAttachment)
        Me.GroupControl6.Location = New System.Drawing.Point(164, 30)
        Me.GroupControl6.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl6.Name = "GroupControl6"
        Me.GroupControl6.Size = New System.Drawing.Size(416, 315)
        Me.GroupControl6.TabIndex = 329
        Me.GroupControl6.Text = "Send Attachment | Letter Attachment"
        '
        'txtbody
        '
        Me.txtbody.Location = New System.Drawing.Point(270, 7)
        Me.txtbody.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtbody.Multiline = True
        Me.txtbody.Name = "txtbody"
        Me.txtbody.Size = New System.Drawing.Size(18, 16)
        Me.txtbody.TabIndex = 403
        Me.txtbody.Visible = False
        '
        'lblattacmentFileCount
        '
        Me.lblattacmentFileCount.Cursor = System.Windows.Forms.Cursors.Help
        Me.lblattacmentFileCount.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.lblattacmentFileCount.ForeColor = System.Drawing.Color.DarkGray
        Me.lblattacmentFileCount.Location = New System.Drawing.Point(32, 78)
        Me.lblattacmentFileCount.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblattacmentFileCount.Name = "lblattacmentFileCount"
        Me.lblattacmentFileCount.Size = New System.Drawing.Size(118, 27)
        Me.lblattacmentFileCount.TabIndex = 42
        Me.lblattacmentFileCount.Text = "Attached files : 0"
        '
        'ToggleSwitch1
        '
        Me.ToggleSwitch1.Location = New System.Drawing.Point(183, 72)
        Me.ToggleSwitch1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ToggleSwitch1.Name = "ToggleSwitch1"
        Me.ToggleSwitch1.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.ToggleSwitch1.Properties.Appearance.Options.UseFont = True
        Me.ToggleSwitch1.Properties.OffText = "Normal Attachment"
        Me.ToggleSwitch1.Properties.OnText = "HTML To [ PDF ]"
        Me.ToggleSwitch1.Size = New System.Drawing.Size(213, 24)
        Me.ToggleSwitch1.TabIndex = 326
        '
        'cbTags
        '
        Me.cbTags.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cbTags.EditValue = ""
        Me.cbTags.Location = New System.Drawing.Point(31, 119)
        Me.cbTags.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cbTags.Name = "cbTags"
        Me.cbTags.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.cbTags.Properties.Appearance.Options.UseBackColor = True
        Me.cbTags.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbTags.Properties.Items.AddRange(New Object() {"Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.cbTags.Size = New System.Drawing.Size(349, 32)
        Me.cbTags.TabIndex = 397
        '
        'bntAddAttachment
        '
        Me.bntAddAttachment.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntAddAttachment.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntAddAttachment.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntAddAttachment.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntAddAttachment.Appearance.Options.UseBackColor = True
        Me.bntAddAttachment.Appearance.Options.UseBorderColor = True
        Me.bntAddAttachment.Appearance.Options.UseFont = True
        Me.bntAddAttachment.Appearance.Options.UseForeColor = True
        Me.bntAddAttachment.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntAddAttachment.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntAddAttachment.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntAddAttachment.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntAddAttachment.AppearanceDisabled.Options.UseBackColor = True
        Me.bntAddAttachment.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntAddAttachment.AppearanceDisabled.Options.UseFont = True
        Me.bntAddAttachment.AppearanceDisabled.Options.UseForeColor = True
        Me.bntAddAttachment.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntAddAttachment.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntAddAttachment.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntAddAttachment.AppearanceHovered.Options.UseBackColor = True
        Me.bntAddAttachment.AppearanceHovered.Options.UseBorderColor = True
        Me.bntAddAttachment.AppearanceHovered.Options.UseForeColor = True
        Me.bntAddAttachment.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntAddAttachment.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntAddAttachment.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntAddAttachment.AppearancePressed.Options.UseBackColor = True
        Me.bntAddAttachment.AppearancePressed.Options.UseBorderColor = True
        Me.bntAddAttachment.AppearancePressed.Options.UseForeColor = True
        Me.bntAddAttachment.Cursor = System.Windows.Forms.Cursors.Hand
        Me.bntAddAttachment.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.documentpdf
        Me.bntAddAttachment.Location = New System.Drawing.Point(221, 227)
        Me.bntAddAttachment.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.bntAddAttachment.Name = "bntAddAttachment"
        Me.bntAddAttachment.Size = New System.Drawing.Size(158, 45)
        Me.bntAddAttachment.TabIndex = 399
        Me.bntAddAttachment.Text = "Add"
        '
        'txtattach
        '
        Me.txtattach.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtattach.EditValue = ""
        Me.txtattach.Location = New System.Drawing.Point(31, 171)
        Me.txtattach.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtattach.Name = "txtattach"
        Me.txtattach.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtattach.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtattach.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtattach.Properties.Appearance.Options.UseBackColor = True
        Me.txtattach.Properties.Appearance.Options.UseFont = True
        Me.txtattach.Properties.Appearance.Options.UseForeColor = True
        Me.txtattach.Properties.NullValuePrompt = "Name Attachment"
        Me.txtattach.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtattach.Size = New System.Drawing.Size(349, 30)
        Me.txtattach.TabIndex = 398
        Me.txtattach.ToolTipTitle = "Choose a name For Attachment"
        '
        'BntRemoveAttachment
        '
        Me.BntRemoveAttachment.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntRemoveAttachment.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntRemoveAttachment.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntRemoveAttachment.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntRemoveAttachment.Appearance.Options.UseBackColor = True
        Me.BntRemoveAttachment.Appearance.Options.UseBorderColor = True
        Me.BntRemoveAttachment.Appearance.Options.UseFont = True
        Me.BntRemoveAttachment.Appearance.Options.UseForeColor = True
        Me.BntRemoveAttachment.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntRemoveAttachment.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntRemoveAttachment.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntRemoveAttachment.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntRemoveAttachment.AppearanceDisabled.Options.UseBackColor = True
        Me.BntRemoveAttachment.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntRemoveAttachment.AppearanceDisabled.Options.UseFont = True
        Me.BntRemoveAttachment.AppearanceDisabled.Options.UseForeColor = True
        Me.BntRemoveAttachment.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntRemoveAttachment.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntRemoveAttachment.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntRemoveAttachment.AppearanceHovered.Options.UseBackColor = True
        Me.BntRemoveAttachment.AppearanceHovered.Options.UseBorderColor = True
        Me.BntRemoveAttachment.AppearanceHovered.Options.UseForeColor = True
        Me.BntRemoveAttachment.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntRemoveAttachment.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntRemoveAttachment.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntRemoveAttachment.AppearancePressed.Options.UseBackColor = True
        Me.BntRemoveAttachment.AppearancePressed.Options.UseBorderColor = True
        Me.BntRemoveAttachment.AppearancePressed.Options.UseForeColor = True
        Me.BntRemoveAttachment.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntRemoveAttachment.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntRemoveAttachment.Location = New System.Drawing.Point(31, 227)
        Me.BntRemoveAttachment.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntRemoveAttachment.Name = "BntRemoveAttachment"
        Me.BntRemoveAttachment.Size = New System.Drawing.Size(158, 45)
        Me.BntRemoveAttachment.TabIndex = 400
        Me.BntRemoveAttachment.Text = "Remove"
        '
        'XtraTabPage8
        '
        Me.XtraTabPage8.Controls.Add(Me.GroupControl9)
        Me.XtraTabPage8.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage8.Name = "XtraTabPage8"
        Me.XtraTabPage8.Size = New System.Drawing.Size(747, 383)
        Me.XtraTabPage8.Text = "  Add Logo"
        '
        'GroupControl9
        '
        Me.GroupControl9.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.249999!)
        Me.GroupControl9.AppearanceCaption.Options.UseFont = True
        Me.GroupControl9.Controls.Add(Me.ChkLogo)
        Me.GroupControl9.Controls.Add(Me.lblLogoTitle)
        Me.GroupControl9.Controls.Add(Me.piclogo)
        Me.GroupControl9.Controls.Add(Me.BntResetLetterLogo)
        Me.GroupControl9.Controls.Add(Me.bntLetterLogo)
        Me.GroupControl9.Controls.Add(Me.SeparatorControl3)
        Me.GroupControl9.Location = New System.Drawing.Point(164, 30)
        Me.GroupControl9.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl9.Name = "GroupControl9"
        Me.GroupControl9.Size = New System.Drawing.Size(416, 327)
        Me.GroupControl9.TabIndex = 327
        Me.GroupControl9.Text = "Letter logo"
        '
        'ChkLogo
        '
        Me.ChkLogo.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ChkLogo.Location = New System.Drawing.Point(0, 33)
        Me.ChkLogo.Margin = New System.Windows.Forms.Padding(4)
        Me.ChkLogo.Name = "ChkLogo"
        Me.ChkLogo.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.ChkLogo.Properties.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.ChkLogo.Properties.Appearance.Options.UseFont = True
        Me.ChkLogo.Properties.Appearance.Options.UseForeColor = True
        Me.ChkLogo.Properties.Caption = "Send without Logo......"
        Me.ChkLogo.Size = New System.Drawing.Size(139, 22)
        Me.ChkLogo.TabIndex = 48
        Me.ChkLogo.Visible = False
        '
        'lblLogoTitle
        '
        Me.lblLogoTitle.BackColor = System.Drawing.Color.Transparent
        Me.lblLogoTitle.Font = New System.Drawing.Font("Tahoma", 7.0!)
        Me.lblLogoTitle.ForeColor = System.Drawing.Color.Gray
        Me.lblLogoTitle.ImageAlign = System.Drawing.ContentAlignment.TopRight
        Me.lblLogoTitle.Location = New System.Drawing.Point(172, 202)
        Me.lblLogoTitle.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblLogoTitle.Name = "lblLogoTitle"
        Me.lblLogoTitle.Size = New System.Drawing.Size(69, 19)
        Me.lblLogoTitle.TabIndex = 47
        Me.lblLogoTitle.Text = "Add Logo..."
        '
        'piclogo
        '
        Me.piclogo.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.piclogo.Location = New System.Drawing.Point(130, 50)
        Me.piclogo.Margin = New System.Windows.Forms.Padding(4)
        Me.piclogo.Name = "piclogo"
        Me.piclogo.Size = New System.Drawing.Size(157, 150)
        Me.piclogo.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.piclogo.TabIndex = 46
        Me.piclogo.TabStop = False
        '
        'BntResetLetterLogo
        '
        Me.BntResetLetterLogo.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetLetterLogo.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetLetterLogo.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntResetLetterLogo.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetLetterLogo.Appearance.Options.UseBackColor = True
        Me.BntResetLetterLogo.Appearance.Options.UseBorderColor = True
        Me.BntResetLetterLogo.Appearance.Options.UseFont = True
        Me.BntResetLetterLogo.Appearance.Options.UseForeColor = True
        Me.BntResetLetterLogo.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntResetLetterLogo.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntResetLetterLogo.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntResetLetterLogo.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntResetLetterLogo.AppearanceDisabled.Options.UseBackColor = True
        Me.BntResetLetterLogo.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntResetLetterLogo.AppearanceDisabled.Options.UseFont = True
        Me.BntResetLetterLogo.AppearanceDisabled.Options.UseForeColor = True
        Me.BntResetLetterLogo.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntResetLetterLogo.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetLetterLogo.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntResetLetterLogo.AppearanceHovered.Options.UseBackColor = True
        Me.BntResetLetterLogo.AppearanceHovered.Options.UseBorderColor = True
        Me.BntResetLetterLogo.AppearanceHovered.Options.UseForeColor = True
        Me.BntResetLetterLogo.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetLetterLogo.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetLetterLogo.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntResetLetterLogo.AppearancePressed.Options.UseBackColor = True
        Me.BntResetLetterLogo.AppearancePressed.Options.UseBorderColor = True
        Me.BntResetLetterLogo.AppearancePressed.Options.UseForeColor = True
        Me.BntResetLetterLogo.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntResetLetterLogo.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntResetLetterLogo.Location = New System.Drawing.Point(39, 237)
        Me.BntResetLetterLogo.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntResetLetterLogo.Name = "BntResetLetterLogo"
        Me.BntResetLetterLogo.Size = New System.Drawing.Size(158, 45)
        Me.BntResetLetterLogo.TabIndex = 50
        Me.BntResetLetterLogo.Text = "Remove Logo"
        '
        'bntLetterLogo
        '
        Me.bntLetterLogo.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntLetterLogo.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntLetterLogo.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntLetterLogo.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntLetterLogo.Appearance.Options.UseBackColor = True
        Me.bntLetterLogo.Appearance.Options.UseBorderColor = True
        Me.bntLetterLogo.Appearance.Options.UseFont = True
        Me.bntLetterLogo.Appearance.Options.UseForeColor = True
        Me.bntLetterLogo.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntLetterLogo.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntLetterLogo.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntLetterLogo.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntLetterLogo.AppearanceDisabled.Options.UseBackColor = True
        Me.bntLetterLogo.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntLetterLogo.AppearanceDisabled.Options.UseFont = True
        Me.bntLetterLogo.AppearanceDisabled.Options.UseForeColor = True
        Me.bntLetterLogo.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntLetterLogo.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntLetterLogo.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntLetterLogo.AppearanceHovered.Options.UseBackColor = True
        Me.bntLetterLogo.AppearanceHovered.Options.UseBorderColor = True
        Me.bntLetterLogo.AppearanceHovered.Options.UseForeColor = True
        Me.bntLetterLogo.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntLetterLogo.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntLetterLogo.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntLetterLogo.AppearancePressed.Options.UseBackColor = True
        Me.bntLetterLogo.AppearancePressed.Options.UseBorderColor = True
        Me.bntLetterLogo.AppearancePressed.Options.UseForeColor = True
        Me.bntLetterLogo.Cursor = System.Windows.Forms.Cursors.Hand
        Me.bntLetterLogo.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.imageimport
        Me.bntLetterLogo.Location = New System.Drawing.Point(237, 237)
        Me.bntLetterLogo.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.bntLetterLogo.Name = "bntLetterLogo"
        Me.bntLetterLogo.Size = New System.Drawing.Size(158, 45)
        Me.bntLetterLogo.TabIndex = 49
        Me.bntLetterLogo.Text = "Select Logo"
        '
        'SeparatorControl3
        '
        Me.SeparatorControl3.LineThickness = 1
        Me.SeparatorControl3.Location = New System.Drawing.Point(115, 215)
        Me.SeparatorControl3.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl3.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl3.Name = "SeparatorControl3"
        Me.SeparatorControl3.Padding = New System.Windows.Forms.Padding(10, 9, 10, 9)
        Me.SeparatorControl3.Size = New System.Drawing.Size(186, 27)
        Me.SeparatorControl3.TabIndex = 301
        '
        'XtraTabPage9
        '
        Me.XtraTabPage9.Controls.Add(Me.GroupControl5)
        Me.XtraTabPage9.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage9.Name = "XtraTabPage9"
        Me.XtraTabPage9.Size = New System.Drawing.Size(747, 383)
        Me.XtraTabPage9.Text = "  QRCode"
        '
        'GroupControl5
        '
        Me.GroupControl5.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.249999!)
        Me.GroupControl5.AppearanceCaption.Options.UseFont = True
        Me.GroupControl5.Controls.Add(Me.SimpleButton3)
        Me.GroupControl5.Controls.Add(Me.Pic_QRCode)
        Me.GroupControl5.Controls.Add(Me.btnExport)
        Me.GroupControl5.Controls.Add(Me.txtCode)
        Me.GroupControl5.Controls.Add(Me.btnGenerate)
        Me.GroupControl5.Controls.Add(Me.txtHeight)
        Me.GroupControl5.Controls.Add(Me.txtWidth)
        Me.GroupControl5.Location = New System.Drawing.Point(164, 19)
        Me.GroupControl5.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl5.Name = "GroupControl5"
        Me.GroupControl5.Size = New System.Drawing.Size(416, 315)
        Me.GroupControl5.TabIndex = 325
        Me.GroupControl5.Text = "Convert Link To QRCode"
        '
        'SimpleButton3
        '
        Me.SimpleButton3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton3.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton3.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton3.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton3.Appearance.Options.UseBackColor = True
        Me.SimpleButton3.Appearance.Options.UseBorderColor = True
        Me.SimpleButton3.Appearance.Options.UseFont = True
        Me.SimpleButton3.Appearance.Options.UseForeColor = True
        Me.SimpleButton3.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton3.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton3.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton3.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton3.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton3.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton3.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton3.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton3.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton3.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton3.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton3.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton3.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton3.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton3.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton3.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton3.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton3.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton3.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton3.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.SimpleButton3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.SimpleButton3.Location = New System.Drawing.Point(36, 240)
        Me.SimpleButton3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SimpleButton3.Name = "SimpleButton3"
        Me.SimpleButton3.Size = New System.Drawing.Size(158, 45)
        Me.SimpleButton3.TabIndex = 321
        Me.SimpleButton3.Text = "Remove"
        '
        'Pic_QRCode
        '
        Me.Pic_QRCode.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Pic_QRCode.Location = New System.Drawing.Point(249, 94)
        Me.Pic_QRCode.Margin = New System.Windows.Forms.Padding(4)
        Me.Pic_QRCode.Name = "Pic_QRCode"
        Me.Pic_QRCode.Size = New System.Drawing.Size(144, 158)
        Me.Pic_QRCode.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.Pic_QRCode.TabIndex = 302
        Me.Pic_QRCode.TabStop = False
        '
        'btnExport
        '
        Me.btnExport.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnExport.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnExport.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnExport.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnExport.Appearance.Options.UseBackColor = True
        Me.btnExport.Appearance.Options.UseBorderColor = True
        Me.btnExport.Appearance.Options.UseFont = True
        Me.btnExport.Appearance.Options.UseForeColor = True
        Me.btnExport.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btnExport.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btnExport.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnExport.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnExport.AppearanceDisabled.Options.UseBackColor = True
        Me.btnExport.AppearanceDisabled.Options.UseBorderColor = True
        Me.btnExport.AppearanceDisabled.Options.UseFont = True
        Me.btnExport.AppearanceDisabled.Options.UseForeColor = True
        Me.btnExport.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btnExport.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnExport.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btnExport.AppearanceHovered.Options.UseBackColor = True
        Me.btnExport.AppearanceHovered.Options.UseBorderColor = True
        Me.btnExport.AppearanceHovered.Options.UseForeColor = True
        Me.btnExport.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnExport.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnExport.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btnExport.AppearancePressed.Options.UseBackColor = True
        Me.btnExport.AppearancePressed.Options.UseBorderColor = True
        Me.btnExport.AppearancePressed.Options.UseForeColor = True
        Me.btnExport.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnExport.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.qr_Save
        Me.btnExport.Location = New System.Drawing.Point(36, 173)
        Me.btnExport.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Size = New System.Drawing.Size(158, 45)
        Me.btnExport.TabIndex = 319
        Me.btnExport.Text = "Export "
        '
        'txtCode
        '
        Me.txtCode.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtCode.EditValue = ""
        Me.txtCode.Location = New System.Drawing.Point(8, 50)
        Me.txtCode.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtCode.Name = "txtCode"
        Me.txtCode.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtCode.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtCode.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtCode.Properties.Appearance.Options.UseBackColor = True
        Me.txtCode.Properties.Appearance.Options.UseFont = True
        Me.txtCode.Properties.Appearance.Options.UseForeColor = True
        Me.txtCode.Properties.ContextImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.link2
        Me.txtCode.Properties.NullValuePrompt = "Enter Your Link..."
        Me.txtCode.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtCode.Size = New System.Drawing.Size(234, 30)
        Me.txtCode.TabIndex = 317
        Me.txtCode.ToolTipTitle = "If you place a link here and click on Generate QRCode, "
        '
        'btnGenerate
        '
        Me.btnGenerate.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnGenerate.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnGenerate.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnGenerate.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnGenerate.Appearance.Options.UseBackColor = True
        Me.btnGenerate.Appearance.Options.UseBorderColor = True
        Me.btnGenerate.Appearance.Options.UseFont = True
        Me.btnGenerate.Appearance.Options.UseForeColor = True
        Me.btnGenerate.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btnGenerate.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btnGenerate.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnGenerate.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnGenerate.AppearanceDisabled.Options.UseBackColor = True
        Me.btnGenerate.AppearanceDisabled.Options.UseBorderColor = True
        Me.btnGenerate.AppearanceDisabled.Options.UseFont = True
        Me.btnGenerate.AppearanceDisabled.Options.UseForeColor = True
        Me.btnGenerate.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btnGenerate.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnGenerate.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btnGenerate.AppearanceHovered.Options.UseBackColor = True
        Me.btnGenerate.AppearanceHovered.Options.UseBorderColor = True
        Me.btnGenerate.AppearanceHovered.Options.UseForeColor = True
        Me.btnGenerate.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnGenerate.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnGenerate.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btnGenerate.AppearancePressed.Options.UseBackColor = True
        Me.btnGenerate.AppearancePressed.Options.UseBorderColor = True
        Me.btnGenerate.AppearancePressed.Options.UseForeColor = True
        Me.btnGenerate.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnGenerate.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.QrcodeBTN
        Me.btnGenerate.Location = New System.Drawing.Point(36, 108)
        Me.btnGenerate.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.btnGenerate.Name = "btnGenerate"
        Me.btnGenerate.Size = New System.Drawing.Size(158, 45)
        Me.btnGenerate.TabIndex = 320
        Me.btnGenerate.Text = "Generate "
        '
        'txtHeight
        '
        Me.txtHeight.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtHeight.EditValue = "185"
        Me.txtHeight.Location = New System.Drawing.Point(249, 259)
        Me.txtHeight.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtHeight.Name = "txtHeight"
        Me.txtHeight.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtHeight.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtHeight.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtHeight.Properties.Appearance.Options.UseBackColor = True
        Me.txtHeight.Properties.Appearance.Options.UseFont = True
        Me.txtHeight.Properties.Appearance.Options.UseForeColor = True
        Me.txtHeight.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.gaugestylelinearvertical_16x16
        Me.txtHeight.Properties.NullValuePrompt = "Height"
        Me.txtHeight.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtHeight.Size = New System.Drawing.Size(144, 30)
        Me.txtHeight.TabIndex = 322
        Me.txtHeight.ToolTipTitle = "Height"
        '
        'txtWidth
        '
        Me.txtWidth.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtWidth.EditValue = "185"
        Me.txtWidth.Location = New System.Drawing.Point(249, 50)
        Me.txtWidth.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtWidth.Name = "txtWidth"
        Me.txtWidth.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtWidth.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtWidth.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtWidth.Properties.Appearance.Options.UseBackColor = True
        Me.txtWidth.Properties.Appearance.Options.UseFont = True
        Me.txtWidth.Properties.Appearance.Options.UseForeColor = True
        Me.txtWidth.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.gaugestylelinearhorizontal_16x16
        Me.txtWidth.Properties.NullValuePrompt = "Width"
        Me.txtWidth.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtWidth.Size = New System.Drawing.Size(144, 30)
        Me.txtWidth.TabIndex = 322
        Me.txtWidth.ToolTipTitle = "Height"
        '
        'XtraTabPage11
        '
        Me.XtraTabPage11.Controls.Add(Me.GroupControl7)
        Me.XtraTabPage11.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage11.Name = "XtraTabPage11"
        Me.XtraTabPage11.Size = New System.Drawing.Size(747, 383)
        Me.XtraTabPage11.Text = "  Message importance "
        '
        'GroupControl7
        '
        Me.GroupControl7.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.GroupControl7.Appearance.Options.UseBackColor = True
        Me.GroupControl7.Appearance.Options.UseFont = True
        Me.GroupControl7.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.249999!)
        Me.GroupControl7.AppearanceCaption.Options.UseFont = True
        Me.GroupControl7.Controls.Add(Me.cmbPriority)
        Me.GroupControl7.Controls.Add(Me.Label1)
        Me.GroupControl7.Controls.Add(Me.ComboSensitivity)
        Me.GroupControl7.Controls.Add(Me.Label2)
        Me.GroupControl7.Controls.Add(Me.ComboFlagStatus)
        Me.GroupControl7.Location = New System.Drawing.Point(164, 30)
        Me.GroupControl7.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl7.Name = "GroupControl7"
        Me.GroupControl7.Size = New System.Drawing.Size(416, 315)
        Me.GroupControl7.TabIndex = 324
        Me.GroupControl7.Text = "Message importance "
        '
        'cmbPriority
        '
        Me.cmbPriority.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cmbPriority.EditValue = "Normal"
        Me.cmbPriority.Location = New System.Drawing.Point(32, 58)
        Me.cmbPriority.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cmbPriority.Name = "cmbPriority"
        Me.cmbPriority.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cmbPriority.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.cmbPriority.Properties.Appearance.Options.UseBackColor = True
        Me.cmbPriority.Properties.Appearance.Options.UseFont = True
        Me.cmbPriority.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cmbPriority.Properties.Items.AddRange(New Object() {"High", "Low", "Normal"})
        Me.cmbPriority.Size = New System.Drawing.Size(360, 32)
        Me.cmbPriority.TabIndex = 45
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.249999!)
        Me.Label1.Location = New System.Drawing.Point(136, 111)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(151, 24)
        Me.Label1.TabIndex = 46
        Me.Label1.Text = "Message Sensitivity"
        '
        'ComboSensitivity
        '
        Me.ComboSensitivity.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboSensitivity.EditValue = "Normal"
        Me.ComboSensitivity.Location = New System.Drawing.Point(32, 143)
        Me.ComboSensitivity.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ComboSensitivity.Name = "ComboSensitivity"
        Me.ComboSensitivity.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboSensitivity.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.ComboSensitivity.Properties.Appearance.Options.UseBackColor = True
        Me.ComboSensitivity.Properties.Appearance.Options.UseFont = True
        Me.ComboSensitivity.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboSensitivity.Properties.Items.AddRange(New Object() {"Confidential", "Normal", "Personal", "Private"})
        Me.ComboSensitivity.Size = New System.Drawing.Size(360, 32)
        Me.ComboSensitivity.TabIndex = 47
        '
        'Label2
        '
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(172, 195)
        Me.Label2.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(80, 27)
        Me.Label2.TabIndex = 48
        Me.Label2.Text = "Flag Status"
        '
        'ComboFlagStatus
        '
        Me.ComboFlagStatus.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboFlagStatus.EditValue = "notFlagged"
        Me.ComboFlagStatus.Location = New System.Drawing.Point(32, 231)
        Me.ComboFlagStatus.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ComboFlagStatus.Name = "ComboFlagStatus"
        Me.ComboFlagStatus.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboFlagStatus.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.ComboFlagStatus.Properties.Appearance.Options.UseBackColor = True
        Me.ComboFlagStatus.Properties.Appearance.Options.UseFont = True
        Me.ComboFlagStatus.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboFlagStatus.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.status_16x16
        Me.ComboFlagStatus.Properties.Items.AddRange(New Object() {"flagged", "notFlagged"})
        Me.ComboFlagStatus.Size = New System.Drawing.Size(360, 32)
        Me.ComboFlagStatus.TabIndex = 45
        '
        'XtraTabPage7
        '
        Me.XtraTabPage7.Controls.Add(Me.GroupControl8)
        Me.XtraTabPage7.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage7.Name = "XtraTabPage7"
        Me.XtraTabPage7.Size = New System.Drawing.Size(747, 383)
        Me.XtraTabPage7.Text = "  Sleep Time"
        '
        'GroupControl8
        '
        Me.GroupControl8.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.249999!)
        Me.GroupControl8.AppearanceCaption.Options.UseFont = True
        Me.GroupControl8.Controls.Add(Me.txtSleepTime)
        Me.GroupControl8.Controls.Add(Me.ComboBoxSleepTime)
        Me.GroupControl8.Controls.Add(Me.SeparatorControl4)
        Me.GroupControl8.Controls.Add(Me.txtLimitSend)
        Me.GroupControl8.Controls.Add(Me.trcThreads)
        Me.GroupControl8.Controls.Add(Me.lblsuccess_Time)
        Me.GroupControl8.Controls.Add(Me.txt_limitSend)
        Me.GroupControl8.Controls.Add(Me.chk_limitSend)
        Me.GroupControl8.Location = New System.Drawing.Point(164, 30)
        Me.GroupControl8.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl8.Name = "GroupControl8"
        Me.GroupControl8.Size = New System.Drawing.Size(416, 327)
        Me.GroupControl8.TabIndex = 323
        Me.GroupControl8.Text = "Sleep Time / Limit Send"
        '
        'txtSleepTime
        '
        Me.txtSleepTime.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.249999!)
        Me.txtSleepTime.Location = New System.Drawing.Point(17, 76)
        Me.txtSleepTime.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.txtSleepTime.Name = "txtSleepTime"
        Me.txtSleepTime.Size = New System.Drawing.Size(111, 19)
        Me.txtSleepTime.TabIndex = 327
        Me.txtSleepTime.Text = "Select Sleep Time"
        '
        'ComboBoxSleepTime
        '
        Me.ComboBoxSleepTime.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboBoxSleepTime.EditValue = "1000"
        Me.ComboBoxSleepTime.Location = New System.Drawing.Point(134, 63)
        Me.ComboBoxSleepTime.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ComboBoxSleepTime.Name = "ComboBoxSleepTime"
        Me.ComboBoxSleepTime.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboBoxSleepTime.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.ComboBoxSleepTime.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBoxSleepTime.Properties.Appearance.Options.UseFont = True
        Me.ComboBoxSleepTime.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxSleepTime.Properties.Items.AddRange(New Object() {"1000", "2000", "3000", "4000", "5000", "6000", "7000", "8000", "9000"})
        Me.ComboBoxSleepTime.Size = New System.Drawing.Size(231, 32)
        Me.ComboBoxSleepTime.TabIndex = 303
        '
        'SeparatorControl4
        '
        Me.SeparatorControl4.LineColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.SeparatorControl4.LineThickness = 1
        Me.SeparatorControl4.Location = New System.Drawing.Point(60, 269)
        Me.SeparatorControl4.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl4.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl4.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl4.Name = "SeparatorControl4"
        Me.SeparatorControl4.Padding = New System.Windows.Forms.Padding(10, 9, 10, 9)
        Me.SeparatorControl4.Size = New System.Drawing.Size(304, 19)
        Me.SeparatorControl4.TabIndex = 301
        '
        'txtLimitSend
        '
        Me.txtLimitSend.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.249999!)
        Me.txtLimitSend.Location = New System.Drawing.Point(24, 120)
        Me.txtLimitSend.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.txtLimitSend.Name = "txtLimitSend"
        Me.txtLimitSend.Size = New System.Drawing.Size(78, 24)
        Me.txtLimitSend.TabIndex = 328
        Me.txtLimitSend.Text = "Limit Send"
        '
        'trcThreads
        '
        Me.trcThreads.Cursor = System.Windows.Forms.Cursors.Hand
        Me.trcThreads.EditValue = 1
        Me.trcThreads.Location = New System.Drawing.Point(122, 217)
        Me.trcThreads.Margin = New System.Windows.Forms.Padding(4)
        Me.trcThreads.Name = "trcThreads"
        Me.trcThreads.Properties.ExportMode = DevExpress.XtraEditors.Repository.ExportMode.DisplayText
        Me.trcThreads.Properties.LabelAppearance.Options.UseTextOptions = True
        Me.trcThreads.Properties.LabelAppearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.trcThreads.Properties.Maximum = 50
        Me.trcThreads.Properties.Minimum = 1
        Me.trcThreads.Size = New System.Drawing.Size(200, 45)
        Me.trcThreads.TabIndex = 326
        Me.trcThreads.Value = 1
        Me.trcThreads.Visible = False
        '
        'lblsuccess_Time
        '
        Me.lblsuccess_Time.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblsuccess_Time.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.lblsuccess_Time.Location = New System.Drawing.Point(17, 148)
        Me.lblsuccess_Time.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblsuccess_Time.Name = "lblsuccess_Time"
        Me.lblsuccess_Time.Size = New System.Drawing.Size(90, 43)
        Me.lblsuccess_Time.TabIndex = 0
        Me.lblsuccess_Time.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'txt_limitSend
        '
        Me.txt_limitSend.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_limitSend.EditValue = ""
        Me.txt_limitSend.Enabled = False
        Me.txt_limitSend.Location = New System.Drawing.Point(134, 112)
        Me.txt_limitSend.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_limitSend.Name = "txt_limitSend"
        Me.txt_limitSend.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_limitSend.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_limitSend.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_limitSend.Properties.Appearance.Options.UseBackColor = True
        Me.txt_limitSend.Properties.Appearance.Options.UseFont = True
        Me.txt_limitSend.Properties.Appearance.Options.UseForeColor = True
        Me.txt_limitSend.Properties.NullValuePrompt = "Enter your limit Number Send"
        Me.txt_limitSend.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_limitSend.Size = New System.Drawing.Size(231, 30)
        Me.txt_limitSend.TabIndex = 304
        Me.txt_limitSend.ToolTipTitle = "Height"
        '
        'chk_limitSend
        '
        Me.chk_limitSend.Cursor = System.Windows.Forms.Cursors.Hand
        Me.chk_limitSend.Location = New System.Drawing.Point(134, 163)
        Me.chk_limitSend.Margin = New System.Windows.Forms.Padding(4)
        Me.chk_limitSend.Name = "chk_limitSend"
        Me.chk_limitSend.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.chk_limitSend.Properties.Appearance.ForeColor = System.Drawing.SystemColors.GrayText
        Me.chk_limitSend.Properties.Appearance.Options.UseFont = True
        Me.chk_limitSend.Properties.Appearance.Options.UseForeColor = True
        Me.chk_limitSend.Properties.Caption = "Send just this And Stop"
        Me.chk_limitSend.Size = New System.Drawing.Size(214, 22)
        Me.chk_limitSend.TabIndex = 303
        '
        'XtraTabPage5
        '
        Me.XtraTabPage5.Controls.Add(Me.Panel9)
        Me.XtraTabPage5.Name = "XtraTabPage5"
        Me.XtraTabPage5.Size = New System.Drawing.Size(1101, 436)
        '
        'Panel9
        '
        Me.Panel9.Controls.Add(Me.WebBrowser2)
        Me.Panel9.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel9.Location = New System.Drawing.Point(0, 0)
        Me.Panel9.Name = "Panel9"
        Me.Panel9.Size = New System.Drawing.Size(1101, 436)
        Me.Panel9.TabIndex = 0
        '
        'WebBrowser2
        '
        Me.WebBrowser2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.WebBrowser2.Location = New System.Drawing.Point(0, 0)
        Me.WebBrowser2.MinimumSize = New System.Drawing.Size(20, 20)
        Me.WebBrowser2.Name = "WebBrowser2"
        Me.WebBrowser2.Size = New System.Drawing.Size(1101, 436)
        Me.WebBrowser2.TabIndex = 1
        '
        'LayoutControl4
        '
        Me.LayoutControl4.Controls.Add(Me.XtraTabControl1)
        Me.LayoutControl4.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl4.Location = New System.Drawing.Point(0, 66)
        Me.LayoutControl4.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LayoutControl4.Name = "LayoutControl4"
        Me.LayoutControl4.Root = Me.LayoutControlGroup3
        Me.LayoutControl4.Size = New System.Drawing.Size(1169, 537)
        Me.LayoutControl4.TabIndex = 348
        Me.LayoutControl4.Text = "LayoutControl4"
        '
        'LayoutControlGroup3
        '
        Me.LayoutControlGroup3.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup3.GroupBordersVisible = False
        Me.LayoutControlGroup3.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem10})
        Me.LayoutControlGroup3.Name = "LayoutControlGroup3"
        Me.LayoutControlGroup3.Size = New System.Drawing.Size(1169, 537)
        Me.LayoutControlGroup3.TextVisible = False
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.XtraTabControl1
        Me.LayoutControlItem10.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(1143, 511)
        Me.LayoutControlItem10.TextVisible = False
        '
        'PopupMenu2
        '
        Me.PopupMenu2.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BntAddMailList), New DevExpress.XtraBars.LinkPersistInfo(Me.BntClearEList)})
        Me.PopupMenu2.Manager = Me.BarManager2
        Me.PopupMenu2.Name = "PopupMenu2"
        '
        'BntAddMailList
        '
        Me.BntAddMailList.Caption = "Add Email List"
        Me.BntAddMailList.Id = 0
        Me.BntAddMailList.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Emailimage
        Me.BntAddMailList.Name = "BntAddMailList"
        '
        'BntClearEList
        '
        Me.BntClearEList.Caption = "Clear Email List"
        Me.BntClearEList.Id = 1
        Me.BntClearEList.ImageOptions.LargeImage = CType(resources.GetObject("BntClearEList.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BntClearEList.Name = "BntClearEList"
        '
        'BarManager2
        '
        Me.BarManager2.DockControls.Add(Me.BarDockControl1)
        Me.BarManager2.DockControls.Add(Me.BarDockControl2)
        Me.BarManager2.DockControls.Add(Me.BarDockControl3)
        Me.BarManager2.DockControls.Add(Me.BarDockControl4)
        Me.BarManager2.Form = Me
        Me.BarManager2.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.BntAddMailList, Me.BntClearEList})
        Me.BarManager2.MaxItemId = 2
        '
        'BarDockControl1
        '
        Me.BarDockControl1.CausesValidation = False
        Me.BarDockControl1.Dock = System.Windows.Forms.DockStyle.Top
        Me.BarDockControl1.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl1.Manager = Me.BarManager2
        Me.BarDockControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BarDockControl1.Size = New System.Drawing.Size(1169, 0)
        '
        'BarDockControl2
        '
        Me.BarDockControl2.CausesValidation = False
        Me.BarDockControl2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.BarDockControl2.Location = New System.Drawing.Point(0, 603)
        Me.BarDockControl2.Manager = Me.BarManager2
        Me.BarDockControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BarDockControl2.Size = New System.Drawing.Size(1169, 0)
        '
        'BarDockControl3
        '
        Me.BarDockControl3.CausesValidation = False
        Me.BarDockControl3.Dock = System.Windows.Forms.DockStyle.Left
        Me.BarDockControl3.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl3.Manager = Me.BarManager2
        Me.BarDockControl3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BarDockControl3.Size = New System.Drawing.Size(0, 603)
        '
        'BarDockControl4
        '
        Me.BarDockControl4.CausesValidation = False
        Me.BarDockControl4.Dock = System.Windows.Forms.DockStyle.Right
        Me.BarDockControl4.Location = New System.Drawing.Point(1169, 0)
        Me.BarDockControl4.Manager = Me.BarManager2
        Me.BarDockControl4.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BarDockControl4.Size = New System.Drawing.Size(0, 603)
        '
        'PopupMenu3
        '
        Me.PopupMenu3.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem13), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem14), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem16)})
        Me.PopupMenu3.Manager = Me.BarManager3
        Me.PopupMenu3.Name = "PopupMenu3"
        '
        'BarButtonItem13
        '
        Me.BarButtonItem13.Caption = "Add Letter"
        Me.BarButtonItem13.Id = 0
        Me.BarButtonItem13.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.sendmht
        Me.BarButtonItem13.Name = "BarButtonItem13"
        '
        'BarButtonItem14
        '
        Me.BarButtonItem14.Caption = "Clear Letter"
        Me.BarButtonItem14.Id = 1
        Me.BarButtonItem14.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_16x165
        Me.BarButtonItem14.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.trash_32x323
        Me.BarButtonItem14.Name = "BarButtonItem14"
        '
        'BarButtonItem16
        '
        Me.BarButtonItem16.Caption = "Paste Letter"
        Me.BarButtonItem16.Id = 3
        Me.BarButtonItem16.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.paste_16x16
        Me.BarButtonItem16.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.paste_32x32
        Me.BarButtonItem16.Name = "BarButtonItem16"
        '
        'BarManager3
        '
        Me.BarManager3.DockControls.Add(Me.BarDockControl5)
        Me.BarManager3.DockControls.Add(Me.BarDockControl6)
        Me.BarManager3.DockControls.Add(Me.BarDockControl7)
        Me.BarManager3.DockControls.Add(Me.BarDockControl8)
        Me.BarManager3.Form = Me
        Me.BarManager3.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.BarButtonItem13, Me.BarButtonItem14, Me.BarButtonItem15, Me.BarButtonItem16})
        Me.BarManager3.MaxItemId = 4
        '
        'BarDockControl5
        '
        Me.BarDockControl5.CausesValidation = False
        Me.BarDockControl5.Dock = System.Windows.Forms.DockStyle.Top
        Me.BarDockControl5.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl5.Manager = Me.BarManager3
        Me.BarDockControl5.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BarDockControl5.Size = New System.Drawing.Size(1169, 0)
        '
        'BarDockControl6
        '
        Me.BarDockControl6.CausesValidation = False
        Me.BarDockControl6.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.BarDockControl6.Location = New System.Drawing.Point(0, 603)
        Me.BarDockControl6.Manager = Me.BarManager3
        Me.BarDockControl6.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BarDockControl6.Size = New System.Drawing.Size(1169, 0)
        '
        'BarDockControl7
        '
        Me.BarDockControl7.CausesValidation = False
        Me.BarDockControl7.Dock = System.Windows.Forms.DockStyle.Left
        Me.BarDockControl7.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl7.Manager = Me.BarManager3
        Me.BarDockControl7.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BarDockControl7.Size = New System.Drawing.Size(0, 603)
        '
        'BarDockControl8
        '
        Me.BarDockControl8.CausesValidation = False
        Me.BarDockControl8.Dock = System.Windows.Forms.DockStyle.Right
        Me.BarDockControl8.Location = New System.Drawing.Point(1169, 0)
        Me.BarDockControl8.Manager = Me.BarManager3
        Me.BarDockControl8.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BarDockControl8.Size = New System.Drawing.Size(0, 603)
        '
        'BarButtonItem15
        '
        Me.BarButtonItem15.Caption = "Edit Letter"
        Me.BarButtonItem15.Id = 2
        Me.BarButtonItem15.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.snaptogglefieldhighlighting
        Me.BarButtonItem15.Name = "BarButtonItem15"
        '
        'Bar4
        '
        Me.Bar4.BarName = "Tools"
        Me.Bar4.DockCol = 0
        Me.Bar4.DockRow = 0
        Me.Bar4.DockStyle = DevExpress.XtraBars.BarDockStyle.Top
        Me.Bar4.Offset = 23
        Me.Bar4.Text = "Tools"
        '
        'SeparatorControl1
        '
        Me.SeparatorControl1.LineAlignment = DevExpress.XtraEditors.Alignment.Far
        Me.SeparatorControl1.LineThickness = 1
        Me.SeparatorControl1.Location = New System.Drawing.Point(410, 36)
        Me.SeparatorControl1.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl1.Name = "SeparatorControl1"
        Me.SeparatorControl1.Padding = New System.Windows.Forms.Padding(10, 9, 10, 9)
        Me.SeparatorControl1.Size = New System.Drawing.Size(207, 22)
        Me.SeparatorControl1.TabIndex = 299
        Me.SeparatorControl1.Visible = False
        '
        'pnlResult
        '
        Me.pnlResult.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.pnlResult.Controls.Add(Me.lblfailed)
        Me.pnlResult.Controls.Add(Me.lblSuccess)
        Me.pnlResult.Controls.Add(Me.PictureBox4)
        Me.pnlResult.Location = New System.Drawing.Point(52, 3)
        Me.pnlResult.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.pnlResult.Name = "pnlResult"
        Me.pnlResult.Size = New System.Drawing.Size(350, 51)
        Me.pnlResult.TabIndex = 315
        '
        'PictureBox4
        '
        Me.PictureBox4.Location = New System.Drawing.Point(4, 3)
        Me.PictureBox4.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.PictureBox4.Name = "PictureBox4"
        Me.PictureBox4.Size = New System.Drawing.Size(340, 45)
        Me.PictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBox4.TabIndex = 314
        Me.PictureBox4.TabStop = False
        '
        'lblTotal
        '
        Me.lblTotal.AutoSize = True
        Me.lblTotal.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.2!, System.Drawing.FontStyle.Bold)
        Me.lblTotal.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.lblTotal.Location = New System.Drawing.Point(666, 19)
        Me.lblTotal.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblTotal.Name = "lblTotal"
        Me.lblTotal.Size = New System.Drawing.Size(37, 13)
        Me.lblTotal.TabIndex = 332
        Me.lblTotal.Text = "Email"
        Me.lblTotal.Visible = False
        '
        'lbltotalstmp
        '
        Me.lbltotalstmp.AutoSize = True
        Me.lbltotalstmp.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.2!, System.Drawing.FontStyle.Bold)
        Me.lbltotalstmp.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(203, Byte), Integer), CType(CType(121, Byte), Integer))
        Me.lbltotalstmp.Location = New System.Drawing.Point(844, 19)
        Me.lbltotalstmp.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbltotalstmp.Name = "lbltotalstmp"
        Me.lbltotalstmp.Size = New System.Drawing.Size(41, 13)
        Me.lbltotalstmp.TabIndex = 337
        Me.lbltotalstmp.Text = "SMTP"
        Me.lbltotalstmp.Visible = False
        '
        'pnlWait
        '
        Me.pnlWait.Controls.Add(Me.ProgressBarControl1)
        Me.pnlWait.Controls.Add(Me.lbl1)
        Me.pnlWait.Dock = System.Windows.Forms.DockStyle.Right
        Me.pnlWait.Location = New System.Drawing.Point(920, 0)
        Me.pnlWait.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.pnlWait.Name = "pnlWait"
        Me.pnlWait.Size = New System.Drawing.Size(249, 66)
        Me.pnlWait.TabIndex = 314
        Me.pnlWait.Visible = False
        '
        'ProgressBarControl1
        '
        Me.ProgressBarControl1.EditValue = 70
        Me.ProgressBarControl1.Location = New System.Drawing.Point(4, 36)
        Me.ProgressBarControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ProgressBarControl1.MenuManager = Me.BarManager1
        Me.ProgressBarControl1.Name = "ProgressBarControl1"
        Me.ProgressBarControl1.Properties.ShowTitle = True
        Me.ProgressBarControl1.Size = New System.Drawing.Size(242, 24)
        Me.ProgressBarControl1.TabIndex = 320
        '
        'lbl1
        '
        Me.lbl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lbl1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lbl1.ForeColor = System.Drawing.Color.White
        Me.lbl1.Location = New System.Drawing.Point(7, 7)
        Me.lbl1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbl1.Name = "lbl1"
        Me.lbl1.Size = New System.Drawing.Size(237, 25)
        Me.lbl1.TabIndex = 319
        Me.lbl1.Text = "Sending in Progress, Please Wait..."
        Me.lbl1.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 18.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.Label7.Image = Global.Best_Sender.My.Resources.Resources.Send
        Me.Label7.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Label7.Location = New System.Drawing.Point(447, 12)
        Me.Label7.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(0, 29)
        Me.Label7.TabIndex = 325
        Me.Label7.Visible = False
        '
        'Panel13
        '
        Me.Panel13.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Panel13.Controls.Add(Me.PictureBox2)
        Me.Panel13.Controls.Add(Me.PictureBox1)
        Me.Panel13.Controls.Add(Me.Label7)
        Me.Panel13.Controls.Add(Me.pnlWait)
        Me.Panel13.Controls.Add(Me.lbltotalstmp)
        Me.Panel13.Controls.Add(Me.lblTotal)
        Me.Panel13.Controls.Add(Me.pnlResult)
        Me.Panel13.Controls.Add(Me.SeparatorControl1)
        Me.Panel13.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel13.Location = New System.Drawing.Point(0, 0)
        Me.Panel13.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Panel13.Name = "Panel13"
        Me.Panel13.Size = New System.Drawing.Size(1169, 66)
        Me.Panel13.TabIndex = 347
        '
        'PictureBox2
        '
        Me.PictureBox2.Image = Global.Best_Sender.My.Resources.Resources.smtpServerOne
        Me.PictureBox2.Location = New System.Drawing.Point(816, 12)
        Me.PictureBox2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(24, 27)
        Me.PictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox2.TabIndex = 338
        Me.PictureBox2.TabStop = False
        Me.PictureBox2.Visible = False
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.Emailimage
        Me.PictureBox1.Location = New System.Drawing.Point(636, 12)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(24, 27)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 338
        Me.PictureBox1.TabStop = False
        Me.PictureBox1.Visible = False
        '
        'GridColumn31
        '
        Me.GridColumn31.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn31.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn31.AppearanceCell.ForeColor = System.Drawing.Color.White
        Me.GridColumn31.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn31.AppearanceCell.Options.UseFont = True
        Me.GridColumn31.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn31.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn31.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn31.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn31.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn31.AppearanceHeader.Options.UseFont = True
        Me.GridColumn31.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn31.Caption = "Proxy"
        Me.GridColumn31.FieldName = "proxy"
        Me.GridColumn31.Name = "GridColumn31"
        Me.GridColumn31.Visible = True
        Me.GridColumn31.VisibleIndex = 0
        Me.GridColumn31.Width = 200
        '
        'GridColumn36
        '
        Me.GridColumn36.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn36.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn36.AppearanceCell.ForeColor = System.Drawing.Color.White
        Me.GridColumn36.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn36.AppearanceCell.Options.UseFont = True
        Me.GridColumn36.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn36.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn36.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn36.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn36.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn36.AppearanceHeader.Options.UseFont = True
        Me.GridColumn36.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn36.Caption = "Edit"
        Me.GridColumn36.FieldName = "edit"
        Me.GridColumn36.Name = "GridColumn36"
        Me.GridColumn36.Visible = True
        Me.GridColumn36.VisibleIndex = 1
        Me.GridColumn36.Width = 150
        '
        'GridColumn37
        '
        Me.GridColumn37.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn37.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn37.AppearanceCell.ForeColor = System.Drawing.Color.White
        Me.GridColumn37.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn37.AppearanceCell.Options.UseFont = True
        Me.GridColumn37.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn37.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn37.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn37.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn37.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn37.AppearanceHeader.Options.UseFont = True
        Me.GridColumn37.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn37.Caption = "Delete"
        Me.GridColumn37.ColumnEdit = Me.RepositoryItemButtonEdit3
        Me.GridColumn37.FieldName = "delete"
        Me.GridColumn37.Name = "GridColumn37"
        Me.GridColumn37.Visible = True
        Me.GridColumn37.VisibleIndex = 2
        Me.GridColumn37.Width = 80
        '
        'GridColumn43
        '
        Me.GridColumn43.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn43.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn43.AppearanceCell.ForeColor = System.Drawing.Color.White
        Me.GridColumn43.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn43.AppearanceCell.Options.UseFont = True
        Me.GridColumn43.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn43.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn43.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn43.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn43.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn43.AppearanceHeader.Options.UseFont = True
        Me.GridColumn43.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn43.Caption = "Status"
        Me.GridColumn43.FieldName = "status"
        Me.GridColumn43.Name = "GridColumn43"
        Me.GridColumn43.Visible = True
        Me.GridColumn43.VisibleIndex = 3
        Me.GridColumn43.Width = 80
        '
        'BarManager4
        '
        Me.BarManager4.DockControls.Add(Me.BarDockControl9)
        Me.BarManager4.DockControls.Add(Me.BarDockControl10)
        Me.BarManager4.DockControls.Add(Me.BarDockControl11)
        Me.BarManager4.DockControls.Add(Me.BarDockControl12)
        Me.BarManager4.Form = Me
        Me.BarManager4.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.BarButtonItem22, Me.BarButtonItem23, Me.BarButtonItem24, Me.BarButtonItem25, Me.BarButtonItem26, Me.BntStopScan, Me.BntRemoveAllpending})
        Me.BarManager4.MaxItemId = 7
        '
        'BarDockControl9
        '
        Me.BarDockControl9.CausesValidation = False
        Me.BarDockControl9.Dock = System.Windows.Forms.DockStyle.Top
        Me.BarDockControl9.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl9.Manager = Me.BarManager4
        Me.BarDockControl9.Size = New System.Drawing.Size(1169, 0)
        '
        'BarDockControl10
        '
        Me.BarDockControl10.CausesValidation = False
        Me.BarDockControl10.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.BarDockControl10.Location = New System.Drawing.Point(0, 603)
        Me.BarDockControl10.Manager = Me.BarManager4
        Me.BarDockControl10.Size = New System.Drawing.Size(1169, 0)
        '
        'BarDockControl11
        '
        Me.BarDockControl11.CausesValidation = False
        Me.BarDockControl11.Dock = System.Windows.Forms.DockStyle.Left
        Me.BarDockControl11.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl11.Manager = Me.BarManager4
        Me.BarDockControl11.Size = New System.Drawing.Size(0, 603)
        '
        'BarDockControl12
        '
        Me.BarDockControl12.CausesValidation = False
        Me.BarDockControl12.Dock = System.Windows.Forms.DockStyle.Right
        Me.BarDockControl12.Location = New System.Drawing.Point(1169, 0)
        Me.BarDockControl12.Manager = Me.BarManager4
        Me.BarDockControl12.Size = New System.Drawing.Size(0, 603)
        '
        'BarButtonItem22
        '
        Me.BarButtonItem22.Caption = "Add List Proxy"
        Me.BarButtonItem22.Id = 0
        Me.BarButtonItem22.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BarButtonItem22.Name = "BarButtonItem22"
        '
        'BarButtonItem23
        '
        Me.BarButtonItem23.Caption = "Start Check Proxy"
        Me.BarButtonItem23.Id = 1
        Me.BarButtonItem23.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.BarButtonItem23.Name = "BarButtonItem23"
        '
        'BarButtonItem24
        '
        Me.BarButtonItem24.Caption = "Remove Invalid"
        Me.BarButtonItem24.Id = 2
        Me.BarButtonItem24.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Notworking32x32
        Me.BarButtonItem24.Name = "BarButtonItem24"
        '
        'BarButtonItem25
        '
        Me.BarButtonItem25.Caption = "Save Proxy valid"
        Me.BarButtonItem25.Id = 3
        Me.BarButtonItem25.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.BarButtonItem25.Name = "BarButtonItem25"
        '
        'BarButtonItem26
        '
        Me.BarButtonItem26.Caption = "Remove All"
        Me.BarButtonItem26.Id = 4
        Me.BarButtonItem26.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BarButtonItem26.Name = "BarButtonItem26"
        '
        'BntStopScan
        '
        Me.BntStopScan.Caption = "Stop"
        Me.BntStopScan.Id = 5
        Me.BntStopScan.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.BntStopScan.Name = "BntStopScan"
        '
        'BntRemoveAllpending
        '
        Me.BntRemoveAllpending.Caption = "Clear Pending"
        Me.BntRemoveAllpending.Id = 6
        Me.BntRemoveAllpending.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.pending32x32
        Me.BntRemoveAllpending.Name = "BntRemoveAllpending"
        '
        'PopupMenu6
        '
        Me.PopupMenu6.Manager = Me.BarManager4
        Me.PopupMenu6.Name = "PopupMenu6"
        '
        'frmEmailSender
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(41, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(45, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1169, 603)
        Me.Controls.Add(Me.LayoutControl4)
        Me.Controls.Add(Me.Panel13)
        Me.Controls.Add(Me.Label13)
        Me.Controls.Add(Me.barDockControlLeft)
        Me.Controls.Add(Me.barDockControlRight)
        Me.Controls.Add(Me.barDockControlBottom)
        Me.Controls.Add(Me.barDockControlTop)
        Me.Controls.Add(Me.BarDockControl3)
        Me.Controls.Add(Me.BarDockControl4)
        Me.Controls.Add(Me.BarDockControl2)
        Me.Controls.Add(Me.BarDockControl1)
        Me.Controls.Add(Me.BarDockControl7)
        Me.Controls.Add(Me.BarDockControl8)
        Me.Controls.Add(Me.BarDockControl6)
        Me.Controls.Add(Me.BarDockControl5)
        Me.Controls.Add(Me.BarDockControl11)
        Me.Controls.Add(Me.BarDockControl12)
        Me.Controls.Add(Me.BarDockControl10)
        Me.Controls.Add(Me.BarDockControl9)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Logo_NewBestSender
        Me.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Name = "frmEmailSender"
        Me.Opacity = 0R
        Me.ShowInTaskbar = False
        Me.Text = "Best Sender VIP"
        CType(Me.RadialMenu1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabEncode.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        CType(Me.PopupMenu1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage2.ResumeLayout(False)
        Me.Panel7.ResumeLayout(False)
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl3.ResumeLayout(False)
        CType(Me.txtLetterPath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtNewLink.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabSmtp.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        CType(Me.SmptTestWait, System.ComponentModel.ISupportInitialize).EndInit()
        Me.SmptTestWait.ResumeLayout(False)
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemTextEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BNT_Details, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Bnt_Remove, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Col_Chk, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage1.ResumeLayout(False)
        Me.Panel3.ResumeLayout(False)
        CType(Me.ProgressBarControl2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemTextEdit2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemButtonEdit2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemCheckEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemImageEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemPictureEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.XtraTabPage3.ResumeLayout(False)
        Me.Panel5.ResumeLayout(False)
        CType(Me.ToggleSwitch3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ToggleSwitch2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ProgressBarControl3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemButtonEdit3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage6.ResumeLayout(False)
        Me.Panel8.ResumeLayout(False)
        CType(Me.XtraTabControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl2.ResumeLayout(False)
        Me.XtraTabPage12.ResumeLayout(False)
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl4.ResumeLayout(False)
        CType(Me.txtTitilOffice365.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Switch_LetterConvertorLink.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_LetterConvertorLink.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage10.ResumeLayout(False)
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl6.ResumeLayout(False)
        Me.GroupControl6.PerformLayout()
        CType(Me.ToggleSwitch1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cbTags.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtattach.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage8.ResumeLayout(False)
        CType(Me.GroupControl9, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl9.ResumeLayout(False)
        CType(Me.ChkLogo.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.piclogo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage9.ResumeLayout(False)
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl5.ResumeLayout(False)
        CType(Me.Pic_QRCode, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtCode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtHeight.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtWidth.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage11.ResumeLayout(False)
        CType(Me.GroupControl7, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl7.ResumeLayout(False)
        CType(Me.cmbPriority.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboSensitivity.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboFlagStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage7.ResumeLayout(False)
        CType(Me.GroupControl8, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl8.ResumeLayout(False)
        Me.GroupControl8.PerformLayout()
        CType(Me.ComboBoxSleepTime.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.trcThreads.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.trcThreads, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_limitSend.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.chk_limitSend.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage5.ResumeLayout(False)
        Me.Panel9.ResumeLayout(False)
        CType(Me.LayoutControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl4.ResumeLayout(False)
        CType(Me.LayoutControlGroup3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PopupMenu2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BarManager2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PopupMenu3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BarManager3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlResult.ResumeLayout(False)
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlWait.ResumeLayout(False)
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel13.ResumeLayout(False)
        Me.Panel13.PerformLayout()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BarManager4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PopupMenu6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents trmfadein As Timer
    Friend WithEvents trmfadeout As Timer
    Friend WithEvents RadialMenu1 As DevExpress.XtraBars.Ribbon.RadialMenu
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents DxErrorProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider
    Friend WithEvents TabEncode As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents WebBrowser1 As WebBrowser
    Friend WithEvents LetterEncoder_bntBrowse As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Timer1 As Timer
    Friend WithEvents ToolTip2 As ToolTip
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents ToolTip3 As ToolTip
    Friend WithEvents trmUpdateMailGrid As Timer
    Friend WithEvents barDockControlLeft As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarManager1 As DevExpress.XtraBars.BarManager
    Friend WithEvents barDockControlTop As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlBottom As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlRight As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarButtonItem1 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem2 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem3 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents PopupMenu1 As DevExpress.XtraBars.PopupMenu
    Friend WithEvents BackgroundWorker2 As System.ComponentModel.BackgroundWorker
    Friend WithEvents BarButtonItem4 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem5 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem6 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem7 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem8 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem9 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents btnAddEMail_From As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem10 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Label13 As Label
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabSmtp As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents SmptTestWait As DevExpress.XtraEditors.GroupControl
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemTextEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn8 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn11 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn7 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn4 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn5 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents BNT_Details As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents GridColumn6 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Bnt_Remove As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents GridColumn9 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Col_Chk As DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit
    Friend WithEvents GridColumn10 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn23 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn24 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn25 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn18 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn19 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents XtraTabPage1 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel3 As Panel
    Friend WithEvents GridControl2 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView2 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn12 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn13 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemTextEdit2 As DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
    Friend WithEvents GridColumn14 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents col_status_icon As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn15 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn16 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn17 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn20 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemButtonEdit2 As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents GridColumn21 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemCheckEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit
    Friend WithEvents GridColumn22 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn26 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn27 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn28 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemImageEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemImageEdit
    Friend WithEvents RepositoryItemPictureEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemPictureEdit
    Friend WithEvents XtraTabPage2 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel7 As Panel
    Friend WithEvents lblTrcThreads As Label
    Friend WithEvents picWait As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LayoutControl3 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents txtLetter As RichTextBox
    Friend WithEvents txtLetterPath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtNewLink As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControl4 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup3 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents Panel2 As Panel
    Friend WithEvents Panel1 As Panel
    Friend WithEvents ProgressBarControl2 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents XtraTabPage6 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel8 As Panel
    Friend WithEvents BntEList As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Bnt_Clear_E_L As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem11 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem12 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarDockControl3 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarManager2 As DevExpress.XtraBars.BarManager
    Friend WithEvents BarDockControl1 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl2 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl4 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BntAddMailList As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntClearEList As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents PopupMenu2 As DevExpress.XtraBars.PopupMenu
    Friend WithEvents BarDockControl7 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarManager3 As DevExpress.XtraBars.BarManager
    Friend WithEvents BarDockControl5 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl6 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl8 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarButtonItem13 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem14 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem15 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents PopupMenu3 As DevExpress.XtraBars.PopupMenu
    Friend WithEvents Panel13 As Panel
    Friend WithEvents Label7 As Label
    Friend WithEvents pnlWait As Panel
    Friend WithEvents ProgressBarControl1 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents lbl1 As Label
    Friend WithEvents lbltotalstmp As Label
    Friend WithEvents lblTotal As Label
    Friend WithEvents pnlResult As Panel
    Friend WithEvents lblfailed As Label
    Friend WithEvents lblSuccess As Label
    Friend WithEvents PictureBox4 As PictureBox
    Friend WithEvents SeparatorControl1 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents Bar4 As DevExpress.XtraBars.Bar
    Friend WithEvents XtraTabPage5 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel9 As Panel
    Friend WithEvents WebBrowser2 As WebBrowser
    Friend WithEvents BarButtonItem16 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents PictureBox2 As PictureBox
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents XtraTabControl2 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents XtraTabPage12 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupControl4 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents txtTitilOffice365 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ComboBoxEdit1 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Switch_LetterConvertorLink As DevExpress.XtraEditors.ToggleSwitch
    Friend WithEvents txt_LetterConvertorLink As DevExpress.XtraEditors.TextEdit
    Friend WithEvents bntResetLetterConvertor As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents XtraTabPage10 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupControl6 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents txtbody As TextBox
    Friend WithEvents lblattacmentFileCount As Label
    Friend WithEvents ToggleSwitch1 As DevExpress.XtraEditors.ToggleSwitch
    Friend WithEvents cbTags As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents bntAddAttachment As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtattach As DevExpress.XtraEditors.TextEdit
    Friend WithEvents BntRemoveAttachment As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents XtraTabPage8 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupControl9 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ChkLogo As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents lblLogoTitle As Label
    Friend WithEvents piclogo As PictureBox
    Friend WithEvents BntResetLetterLogo As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents bntLetterLogo As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SeparatorControl3 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents XtraTabPage9 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupControl5 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents SimpleButton3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Pic_QRCode As PictureBox
    Friend WithEvents btnExport As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtCode As DevExpress.XtraEditors.TextEdit
    Friend WithEvents btnGenerate As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtHeight As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtWidth As DevExpress.XtraEditors.TextEdit
    Friend WithEvents XtraTabPage11 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupControl7 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents cmbPriority As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Label1 As Label
    Friend WithEvents ComboSensitivity As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Label2 As Label
    Friend WithEvents ComboFlagStatus As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents XtraTabPage7 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupControl8 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents txtSleepTime As Label
    Friend WithEvents ComboBoxSleepTime As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents SeparatorControl4 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents txtLimitSend As Label
    Friend WithEvents trcThreads As DevExpress.XtraEditors.TrackBarControl
    Friend WithEvents lblsuccess_Time As Label
    Friend WithEvents txt_limitSend As DevExpress.XtraEditors.TextEdit
    Friend WithEvents chk_limitSend As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents BackgroundWorker3 As System.ComponentModel.BackgroundWorker
    Friend WithEvents PopupMenu4 As DevExpress.XtraBars.PopupMenu
    Friend WithEvents PopupMenu5 As DevExpress.XtraBars.PopupMenu
    Friend WithEvents BarButtonItem17 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem18 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem19 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem20 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem21 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntStopCheckProxy As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents XtraTabPage3 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel5 As Panel
    Friend WithEvents GridControl3 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridColumn31 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn36 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn37 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn43 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ProgressBarControl3 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents BntSelectListProxy As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntStartCheckProxy As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntRemoveInvalid As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntRemoveAll As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntSaveProxyvalid As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntProxyReport As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ToggleSwitch2 As DevExpress.XtraEditors.ToggleSwitch
    Friend WithEvents lblTotalProxy As System.Windows.Forms.Label
    Friend WithEvents lblProxyvalid As System.Windows.Forms.Label
    Friend WithEvents lblTotalPproxyInvalid As System.Windows.Forms.Label
    Friend WithEvents RepositoryItemButtonEdit3 As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents GridView3 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents ToggleSwitch3 As DevExpress.XtraEditors.ToggleSwitch
    Friend WithEvents BarDockControl9 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarManager4 As DevExpress.XtraBars.BarManager
    Friend WithEvents BarDockControl10 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl11 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl12 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarButtonItem22 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem23 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem24 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem25 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem26 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntStopScan As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntRemoveAllpending As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents PopupMenu6 As DevExpress.XtraBars.PopupMenu
End Class
