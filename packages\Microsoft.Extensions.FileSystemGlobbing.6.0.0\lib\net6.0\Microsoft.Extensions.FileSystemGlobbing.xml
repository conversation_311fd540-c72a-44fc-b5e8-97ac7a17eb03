<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.FileSystemGlobbing</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase">
            <summary>
            Represents a directory
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase.EnumerateFileSystemInfos">
            <summary>
            Enumerates all files and directories in the directory.
            </summary>
            <returns>Collection of files and directories</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase.GetDirectory(System.String)">
            <summary>
            Returns an instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase" /> that represents a subdirectory
            </summary>
            <param name="path">The directory name</param>
            <returns>Instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase" /> even if directory does not exist</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase.GetFile(System.String)">
            <summary>
            Returns an instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoBase" /> that represents a file in the directory
            </summary>
            <param name="path">The file name</param>
            <returns>Instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoBase" /> even if file does not exist</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoWrapper">
            <summary>
            Wraps an instance of <see cref="T:System.IO.DirectoryInfo" /> and provides implementation of
            <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoWrapper.#ctor(System.IO.DirectoryInfo)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoWrapper" />.
            </summary>
            <param name="directoryInfo">The <see cref="T:System.IO.DirectoryInfo" />.</param>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoWrapper.EnumerateFileSystemInfos">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoWrapper.GetDirectory(System.String)">
            <summary>
            Returns an instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase" /> that represents a subdirectory.
            </summary>
            <remarks>
            If <paramref name="name" /> equals '..', this returns the parent directory.
            </remarks>
            <param name="name">The directory name</param>
            <returns>The directory</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoWrapper.GetFile(System.String)">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoWrapper.Name">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoWrapper.FullName">
            <summary>
            Returns the full path to the directory.
            </summary>
            <remarks>
            Equals the value of <seealso cref="P:System.IO.FileSystemInfo.FullName" />.
            </remarks>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoWrapper.ParentDirectory">
            <summary>
            Returns the parent directory.
            </summary>
            <remarks>
            Equals the value of <seealso cref="P:System.IO.DirectoryInfo.Parent" />.
            </remarks>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoBase">
            <summary>
            Represents a file
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoWrapper">
            <summary>
            Wraps an instance of <see cref="T:System.IO.FileInfo" /> to provide implementation of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoBase" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoWrapper.#ctor(System.IO.FileInfo)">
            <summary>
            Initializes instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoWrapper" /> to wrap the specified object <see cref="T:System.IO.FileInfo" />.
            </summary>
            <param name="fileInfo">The <see cref="T:System.IO.FileInfo" /></param>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoWrapper.Name">
            <summary>
            The file name. (Overrides <see cref="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileSystemInfoBase.Name" />).
            </summary>
            <remarks>
            Equals the value of <see cref="P:System.IO.FileInfo.Name" />.
            </remarks>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoWrapper.FullName">
            <summary>
            The full path of the file. (Overrides <see cref="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileSystemInfoBase.FullName" />).
            </summary>
            <remarks>
            Equals the value of <see cref="P:System.IO.FileSystemInfo.Name" />.
            </remarks>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoWrapper.ParentDirectory">
            <summary>
            The directory containing the file. (Overrides <see cref="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileSystemInfoBase.ParentDirectory" />).
            </summary>
            <remarks>
            Equals the value of <see cref="P:System.IO.FileInfo.Directory" />.
            </remarks>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileSystemInfoBase">
            <summary>
            Shared abstraction for files and directories
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileSystemInfoBase.Name">
            <summary>
            A string containing the name of the file or directory
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileSystemInfoBase.FullName">
            <summary>
            A string containing the full path of the file or directory
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileSystemInfoBase.ParentDirectory">
            <summary>
            The parent directory for the current file or directory
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch">
            <summary>
            Represents a file that was matched by searching using a globbing pattern
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.Path">
            <summary>
            The path to the file matched, relative to the beginning of the matching search pattern.
            </summary>
            <remarks>
            If the matcher searched for "src/Project/**/*.cs" and the pattern matcher found "src/Project/Interfaces/IFile.cs",
            then <see cref="P:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.Stem" /> = "Interfaces/IFile.cs" and <see cref="P:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.Path" /> = "src/Project/Interfaces/IFile.cs".
            </remarks>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.Stem">
            <summary>
            The subpath to the file matched, relative to the first wildcard in the matching search pattern.
            </summary>
            <remarks>
            If the matcher searched for "src/Project/**/*.cs" and the pattern matcher found "src/Project/Interfaces/IFile.cs",
            then <see cref="P:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.Stem" /> = "Interfaces/IFile.cs" and <see cref="P:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.Path" /> = "src/Project/Interfaces/IFile.cs".
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.#ctor(System.String,System.String)">
            <summary>
            Initializes new instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch" />
            </summary>
            <param name="path">The path to the file matched, relative to the beginning of the matching search pattern.</param>
            <param name="stem">The subpath to the file matched, relative to the first wildcard in the matching search pattern.</param>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.Equals(Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch)">
            <summary>
            Determines if the specified match is equivalent to the current match using a case-insensitive comparison.
            </summary>
            <param name="other">The other match to be compared</param>
            <returns>True if <see cref="P:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.Path" /> and <see cref="P:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.Stem" /> are equal using case-insensitive comparison</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.Equals(System.Object)">
            <summary>
            Determines if the specified object is equivalent to the current match using a case-insensitive comparison.
            </summary>
            <param name="obj">The object to be compared</param>
            <returns>True when <see cref="M:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.Equals(Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch)" /></returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch.GetHashCode">
            <summary>
            Gets a hash for the file pattern match.
            </summary>
            <returns>Some number</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.InMemoryDirectoryInfo">
            <summary>
            Avoids using disk for uses like Pattern Matching.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.InMemoryDirectoryInfo.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new InMemoryDirectoryInfo with the root directory and files given.
            </summary>
            <param name="rootDir">The root directory that this FileSystem will use.</param>
            <param name="files">Collection of file names. If relative paths <paramref name="rootDir"/> will be prepended to the paths.</param>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.InMemoryDirectoryInfo.FullName">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.InMemoryDirectoryInfo.Name">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.InMemoryDirectoryInfo.ParentDirectory">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.InMemoryDirectoryInfo.EnumerateFileSystemInfos">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.InMemoryDirectoryInfo.GetDirectory(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.InMemoryDirectoryInfo.GetFile(System.String)">
            <summary>
            Returns an instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoBase"/> that matches the <paramref name="path"/> given.
            </summary>
            <param name="path">The filename.</param>
            <returns>Instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoBase"/> if the file exists, null otherwise.</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Internal.ILinearPattern">
            <summary>
            This API supports infrastructure and is not intended to be used
            directly from your code. This API may change or be removed in future releases.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Internal.IPathSegment">
            <summary>
            This API supports infrastructure and is not intended to be used
            directly from your code. This API may change or be removed in future releases.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Internal.IPattern">
            <summary>
            This API supports infrastructure and is not intended to be used
            directly from your code. This API may change or be removed in future releases.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Internal.IPatternContext">
            <summary>
            This API supports infrastructure and is not intended to be used
            directly from your code. This API may change or be removed in future releases.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Internal.IRaggedPattern">
            <summary>
            This API supports infrastructure and is not intended to be used
            directly from your code. This API may change or be removed in future releases.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Internal.MatcherContext">
            <summary>
            This API supports infrastructure and is not intended to be used
            directly from your code. This API may change or be removed in future releases.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Internal.PatternTestResult">
            <summary>
            This API supports infrastructure and is not intended to be used
            directly from your code. This API may change or be removed in future releases.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.Matcher">
            <summary>
            Searches the file system for files with names that match specified patterns.
            </summary>
            <remarks>
                <para>
                Patterns specified in <seealso cref="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.AddInclude(System.String)" /> and <seealso cref="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.AddExclude(System.String)" /> can use
                the following formats to match multiple files or directories.
                </para>
                <list type="bullet">
                    <item>
                        <term>
                        exact directory and file name
                        </term>
                        <description>
                            <list type="bullet">
                                <item>
                                    <term>"one.txt"</term>
                                </item>
                                <item>
                                    <term>"dir/two.txt"</term>
                                </item>
                            </list>
                        </description>
                    </item>
                    <item>
                        <term>
                        wildcards (*) in file and directory names that represent zero to many characters not including
                        directory separators characters
                        </term>
                        <description>
                            <list type="bullet">
                            <item>
                                <term>"*.txt"</term><description>all files with .txt file extension</description>
                            </item>
                            <item>
                                <term>"*.*"</term><description>all files with an extension</description>
                            </item>
                            <item>
                                <term>"*"</term><description>all files in top level directory</description>
                            </item>
                            <item>
                                <term>".*"</term><description>filenames beginning with '.'</description>
                            </item>
                            - "*word* - all files with 'word' in the filename
                            <item>
                                <term>"readme.*"</term>
                                <description>all files named 'readme' with any file extension</description>
                            </item>
                            <item>
                                <term>"styles/*.css"</term>
                                <description>all files with extension '.css' in the directory 'styles/'</description>
                            </item>
                            <item>
                                <term>"scripts/*/*"</term>
                                <description>all files in 'scripts/' or one level of subdirectory under 'scripts/'</description>
                            </item>
                            <item>
                                <term>"images*/*"</term>
                                <description>all files in a folder with name that is or begins with 'images'</description>
                            </item>
                            </list>
                        </description>
                    </item>
                    <item>
                        <term>arbitrary directory depth ("/**/")</term>
                        <description>
                            <list type="bullet">
                                <item>
                                    <term>"**/*"</term><description>all files in any subdirectory</description>
                                </item>
                                <item>
                                    <term>"dir/**/*"</term><description>all files in any subdirectory under 'dir/'</description>
                                </item>
                            </list>
                        </description>
                    </item>
                    <item>
                        <term>relative paths</term>
                        <description>
                        '../shared/*' - all files in a diretory named 'shared' at the sibling level to the base directory given
                        to <see cref="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.Execute(Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase)" />
                        </description>
                    </item>
                </list>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.#ctor">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Matcher" /> using case-insensitive matching
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.#ctor(System.StringComparison)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Matcher" /> using the string comparison method specified
            </summary>
            <param name="comparisonType">The <see cref="T:System.StringComparison" /> to use</param>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.AddInclude(System.String)">
            <summary>
                <para>
                Add a file name pattern that the matcher should use to discover files. Patterns are relative to the root
                directory given when <see cref="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.Execute(Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase)" /> is called.
                </para>
                <para>
                Use the forward slash '/' to represent directory separator. Use '*' to represent wildcards in file and
                directory names. Use '**' to represent arbitrary directory depth. Use '..' to represent a parent directory.
                </para>
            </summary>
            <param name="pattern">The globbing pattern</param>
            <returns>The matcher</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.AddExclude(System.String)">
            <summary>
                <para>
                Add a file name pattern for files the matcher should exclude from the results. Patterns are relative to the
                root directory given when <see cref="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.Execute(Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase)" /> is called.
                </para>
                <para>
                Use the forward slash '/' to represent directory separator. Use '*' to represent wildcards in file and
                directory names. Use '**' to represent arbitrary directory depth. Use '..' to represent a parent directory.
                </para>
            </summary>
            <param name="pattern">The globbing pattern</param>
            <returns>The matcher</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.Execute(Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase)">
            <summary>
            Searches the directory specified for all files matching patterns added to this instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Matcher" />
            </summary>
            <param name="directoryInfo">The root directory for the search</param>
            <returns>Always returns instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.PatternMatchingResult" />, even if not files were matched</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.MatcherExtensions.AddExcludePatterns(Microsoft.Extensions.FileSystemGlobbing.Matcher,System.Collections.Generic.IEnumerable{System.String}[])">
            <summary>
            Adds multiple exclude patterns to <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Matcher" />. <seealso cref="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.AddExclude(System.String)" />
            </summary>
            <param name="matcher">The matcher to which the exclude patterns are added</param>
            <param name="excludePatternsGroups">A list of globbing patterns</param>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.MatcherExtensions.AddIncludePatterns(Microsoft.Extensions.FileSystemGlobbing.Matcher,System.Collections.Generic.IEnumerable{System.String}[])">
            <summary>
            Adds multiple patterns to include in <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Matcher" />. See <seealso cref="M:Microsoft.Extensions.FileSystemGlobbing.Matcher.AddInclude(System.String)" />
            </summary>
            <param name="matcher">The matcher to which the include patterns are added</param>
            <param name="includePatternsGroups">A list of globbing patterns</param>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.MatcherExtensions.GetResultsInFullPath(Microsoft.Extensions.FileSystemGlobbing.Matcher,System.String)">
            <summary>
            Searches the directory specified for all files matching patterns added to this instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Matcher" />
            </summary>
            <param name="matcher">The matcher</param>
            <param name="directoryPath">The root directory for the search</param>
            <returns>Absolute file paths of all files matched. Empty enumerable if no files matched given patterns.</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.MatcherExtensions.Match(Microsoft.Extensions.FileSystemGlobbing.Matcher,System.String)">
            <summary>
            Matches the file passed in with the patterns in the matcher without going to disk.
            </summary>
            <param name="matcher">The matcher that holds the patterns and pattern matching type.</param>
            <param name="file">The file to run the matcher against.</param>
            <returns>The match results.</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.MatcherExtensions.Match(Microsoft.Extensions.FileSystemGlobbing.Matcher,System.String,System.String)">
            <summary>
            Matches the file passed in with the patterns in the matcher without going to disk.
            </summary>
            <param name="matcher">The matcher that holds the patterns and pattern matching type.</param>
            <param name="rootDir">The root directory for the matcher to match the file from.</param>
            <param name="file">The file to run the matcher against.</param>
            <returns>The match results.</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.MatcherExtensions.Match(Microsoft.Extensions.FileSystemGlobbing.Matcher,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Matches the files passed in with the patterns in the matcher without going to disk.
            </summary>
            <param name="matcher">The matcher that holds the patterns and pattern matching type.</param>
            <param name="files">The files to run the matcher against.</param>
            <returns>The match results.</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.MatcherExtensions.Match(Microsoft.Extensions.FileSystemGlobbing.Matcher,System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Matches the files passed in with the patterns in the matcher without going to disk.
            </summary>
            <param name="matcher">The matcher that holds the patterns and pattern matching type.</param>
            <param name="rootDir">The root directory for the matcher to match the files from.</param>
            <param name="files">The files to run the matcher against.</param>
            <returns>The match results.</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileSystemGlobbing.PatternMatchingResult">
            <summary>
            Represents a collection of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch" />
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.PatternMatchingResult.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch})">
            <summary>
            Initializes the result with a collection of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch" />
            </summary>
            <param name="files">A collection of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch" /></param>
        </member>
        <member name="M:Microsoft.Extensions.FileSystemGlobbing.PatternMatchingResult.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch},System.Boolean)">
            <summary>
            Initializes the result with a collection of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch" />
            </summary>
            <param name="files">A collection of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch" /></param>
            <param name="hasMatches">A value that determines if <see cref="T:Microsoft.Extensions.FileSystemGlobbing.PatternMatchingResult"/> has any matches.</param>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.PatternMatchingResult.Files">
            <summary>
            A collection of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.FilePatternMatch" />
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileSystemGlobbing.PatternMatchingResult.HasMatches">
            <summary>
            Gets a value that determines if this instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.PatternMatchingResult"/> has any matches.
            </summary>
        </member>
        <member name="P:System.SR.CannotDeclarePathSegment">
            <summary>Cannot declare path segment before entering a directory.</summary>
        </member>
        <member name="P:System.SR.CannotTestDirectory">
            <summary>Cannot test directory before entering a directory.</summary>
        </member>
        <member name="P:System.SR.CannotTestFile">
            <summary>Cannot test file before entering a directory.</summary>
        </member>
        <member name="P:System.SR.UnexpectedStringComparisonType">
            <summary>Unexpected StringComparison type: {0}</summary>
        </member>
    </members>
</doc>
