﻿Imports System.IO
Imports DevExpress.XtraEditors
Imports HtmlAgilityPack
Imports System.Security.Cryptography
Imports Org.BouncyCastle.Crypto
Imports Org.BouncyCastle.Crypto.Engines
Imports Org.BouncyCastle.Crypto.Modes
Imports Org.BouncyCastle.Crypto.Paddings
Imports Org.BouncyCastle.Crypto.Parameters
Imports System.Text.RegularExpressions
Imports System.Text
Imports System.ComponentModel
Imports System.Diagnostics ' For Process.Start
Imports System.Windows.Forms

Public Class frmLetterEncoddvb
    ReadOnly fontword As List(Of String) = New List(Of String) From {"***************************************************",
        "##################################################################",
        "-----------------------------------------------------------------------",
        "___________________________________________________________________",
        "_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-_-",
        "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"}
    ReadOnly FunctionList As List(Of String) = New List(Of String) From {"[-Email-]", "[-IP-]", "[-IPChina-]", "[-Domain-]", "[-Name-]", "[-Date-]", "[-DateTomorrow-]",
        "[-RandomBrowser-]", "[-RCountry-]", "[-FakePhone-]", "[-FakeEmail-]", "[-NewYork-]", "[-UCase-]", "[-Link-]", "[-Logo-]", "[-RN1-]", "[-RN2-]", "[-RN3-]", "[-RN4-]",
        "[-RN5-]", "[-RN6-]", "[-RN7-]", "[-RN8-]", "[-RN9-]", "[-RN10-]", "[-RN12-]", "[-RCh1-]", "[-RCh2-]", "[-RCh3-]", "[-RCh4-]", "[-RCh5-]", "[-RCh6-]", "[-RCh7-]", "[-RCh8-]"}

#Region "Letter Encoder"
    Dim CheckOP As Integer = 0

    Function EncryptHTML_Entities(textToEncode As String) As String
        Dim encrypted As New StringBuilder()
        For Each c As Char In textToEncode
            encrypted.Append("&#" & AscW(c) & ";")
        Next
        Return encrypted.ToString()
    End Function

    Private Sub EncryptHTMLFile(filePath As String, password As String)
        Dim doc As New HtmlAgilityPack.HtmlDocument()
        doc.Load(filePath)
        Dim outerHtml As String = doc.DocumentNode.OuterHtml
        Dim engine As IBlockCipher = New BlowfishEngine()
        Dim cipher As IBlockCipher = New CbcBlockCipher(engine)
        Dim paddedCipher As PaddedBufferedBlockCipher = New PaddedBufferedBlockCipher(cipher, New Pkcs7Padding())
        Dim salt() As Byte = {&H0, &H1, &H2, &H3, &H4, &H5, &H6, &H7}
        Dim keyAsKeyParameter = New KeyParameter(DeriveKey(password, salt, engine.GetBlockSize()))
        Dim iv = New ParametersWithIV(keyAsKeyParameter, salt)
        paddedCipher.Init(True, iv)
        Dim lines As String() = outerHtml.Split(New String() {Environment.NewLine}, StringSplitOptions.None)
        For i As Integer = 0 To lines.Length - 1
            Dim lineText As String = lines(i)
            Dim lineBytes() As Byte = System.Text.Encoding.UTF8.GetBytes(lineText)
            Dim encryptedLineBytes() As Byte = paddedCipher.DoFinal(lineBytes)
            Dim encryptedLine As String = System.Convert.ToBase64String(encryptedLineBytes)
            lines(i) = encryptedLine
        Next
        System.IO.File.WriteAllLines(filePath, lines, Encoding.UTF8)
    End Sub

    Private Function DeriveKey(password As String, salt() As Byte, keySize As Integer) As Byte()
        Dim deriveBytes As New Rfc2898DeriveBytes(password, salt, 1000)
        Return deriveBytes.GetBytes(keySize)
    End Function

    Private Sub BackgroundWorker1_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Dim worker As BackgroundWorker = CType(sender, BackgroundWorker)
        Dim originalHtml As String = ""
        Dim encryptedOutputHtml As String = ""

        Me.Invoke(Sub()
                      originalHtml = LetterEncoder_Richtext.Text
                  End Sub)

        If String.IsNullOrWhiteSpace(originalHtml) Then
            worker.ReportProgress(0, "Please load or paste HTML content first!")
            e.Cancel = True
            Return
        End If

        Dim encryptionTypeIndex As Integer = -1
        Me.Invoke(Sub() encryptionTypeIndex = cmbEncryption.SelectedIndex)

        If encryptionTypeIndex = -1 Then
            worker.ReportProgress(0, "Please select an encryption type.")
            e.Cancel = True
            Return
        End If

        Dim htmlDoc As New HtmlAgilityPack.HtmlDocument()
        htmlDoc.LoadHtml(originalHtml)
        htmlDoc.OptionOutputAsXml = False
        htmlDoc.OptionFixNestedTags = True

        Dim textNodesToProcess As List(Of HtmlAgilityPack.HtmlNode) = htmlDoc.DocumentNode.DescendantsAndSelf().Where(Function(n) n.NodeType = HtmlAgilityPack.HtmlNodeType.Text).ToList()
        Dim totalNodesToProcess = textNodesToProcess.Count
        If totalNodesToProcess = 0 Then totalNodesToProcess = 1

        Dim currentProgress As Integer = 0

        Select Case encryptionTypeIndex
            Case 0 ' "Best Sender Vip" - HTML Entity Encoding
                worker.ReportProgress(10, "Starting HTML Entity encryption...")
                currentProgress = 10
                Dim processedNodeCountCase0 As Integer = 0
                For Each node As HtmlAgilityPack.HtmlNode In textNodesToProcess
                    If worker.CancellationPending Then
                        e.Cancel = True
                        Return
                    End If
                    processedNodeCountCase0 += 1
                    currentProgress = 10 + CInt((processedNodeCountCase0 / totalNodesToProcess) * 80)
                    worker.ReportProgress(currentProgress, $"Processing node {processedNodeCountCase0} of {totalNodesToProcess}")

                    Dim originalNodeInnerHtml As String = node.InnerHtml
                    If node.ParentNode IsNot Nothing Then
                        Dim parentName = node.ParentNode.Name.ToLowerInvariant()
                        If parentName = "script" OrElse parentName = "style" OrElse parentName = "a" OrElse parentName = "button" OrElse parentName = "img" Then
                            node.InnerHtml = HtmlEntity.Entitize(HtmlEntity.DeEntitize(originalNodeInnerHtml), True, True)
                            Continue For
                        End If
                    End If
                    Dim decodedText As String = HtmlEntity.DeEntitize(originalNodeInnerHtml)
                    If String.IsNullOrWhiteSpace(decodedText) Then
                        node.InnerHtml = HtmlEntity.Entitize(decodedText, True, True)
                        Continue For
                    End If
                    Dim words As String() = decodedText.Split(" "c)
                    Dim encodedWords As New List(Of String)
                    For Each word As String In words
                        If String.IsNullOrWhiteSpace(word) Then
                            encodedWords.Add(word)
                            Continue For
                        End If
                        Dim skipEncryptionForThisWord As Boolean = False
                        For Each func As String In FunctionList
                            If word.Contains(func) Then
                                skipEncryptionForThisWord = True
                                Exit For
                            End If
                        Next
                        If skipEncryptionForThisWord Then
                            encodedWords.Add(HtmlEntity.Entitize(word, True, True))
                            Continue For
                        End If
                        If word.StartsWith("http", StringComparison.OrdinalIgnoreCase) OrElse word.StartsWith("www.", StringComparison.OrdinalIgnoreCase) OrElse Regex.IsMatch(word, "\.(png|jpg|jpeg|gif|ico)$", RegexOptions.IgnoreCase) Then
                            skipEncryptionForThisWord = True
                        End If
                        If skipEncryptionForThisWord Then
                            encodedWords.Add(HtmlEntity.Entitize(word, True, True))
                        Else
                            encodedWords.Add(EncryptHTML_Entities(word))
                        End If
                    Next
                    node.InnerHtml = String.Join(" ", encodedWords)
                Next
                encryptedOutputHtml = htmlDoc.DocumentNode.OuterHtml
                worker.ReportProgress(100, "HTML Entity encryption complete.")

            Case 1 ' "Devil Encryption"
                worker.ReportProgress(10, "Starting Devil encryption...")
                currentProgress = 10
                Dim currentCheckOP As Integer = 0
                Me.Invoke(Sub() currentCheckOP = CheckOP)

                If currentCheckOP = 0 Then
                    worker.ReportProgress(0, "For Devil Encryption, please select a language type (French, Russian, etc.). This selection should be made before starting encryption.")
                    e.Cancel = True
                    Return
                End If

                Dim randomTexts As String() = Nothing
                Me.Invoke(Sub()
                              Select Case currentCheckOP
                                  ' تأكد أن أسماء عناصر التحكم هذه (Func_Hinid, Func_Arabic, etc.) صحيحة وموجودة على الفورم
                                  Case 40 : If Func_Hinid IsNot Nothing Then randomTexts = Func_Hinid.Text.Split(vbCrLf.ToCharArray(), StringSplitOptions.RemoveEmptyEntries)
                                  Case 50 : If Func_Arabic IsNot Nothing Then randomTexts = Func_Arabic.Text.Split(vbCrLf.ToCharArray(), StringSplitOptions.RemoveEmptyEntries)
                                  Case 20 : If Func_ٌRussian IsNot Nothing Then randomTexts = Func_ٌRussian.Text.Split(vbCrLf.ToCharArray(), StringSplitOptions.RemoveEmptyEntries)
                                  Case 10 : If Func_ٌFranch IsNot Nothing Then randomTexts = Func_ٌFranch.Text.Split(vbCrLf.ToCharArray(), StringSplitOptions.RemoveEmptyEntries)
                                  Case 30 : If Func_Japanese IsNot Nothing Then randomTexts = Func_Japanese.Text.Split(vbCrLf.ToCharArray(), StringSplitOptions.RemoveEmptyEntries)
                              End Select
                          End Sub)

                If randomTexts Is Nothing OrElse randomTexts.Length = 0 Then
                    worker.ReportProgress(0, "Selected language for Devil Encryption has no random text defined.")
                    e.Cancel = True
                    Return
                End If

                Dim random As New Random()
                Dim processedNodeCountCase1 As Integer = 0
                For Each node As HtmlAgilityPack.HtmlNode In textNodesToProcess
                    If worker.CancellationPending Then
                        e.Cancel = True
                        Return
                    End If
                    processedNodeCountCase1 += 1
                    currentProgress = 10 + CInt((processedNodeCountCase1 / totalNodesToProcess) * 80)
                    worker.ReportProgress(currentProgress, $"Processing node {processedNodeCountCase1} of {totalNodesToProcess}")

                    Dim originalNodeInnerHtml As String = node.InnerHtml
                    If node.ParentNode IsNot Nothing Then
                        Dim parentName = node.ParentNode.Name.ToLowerInvariant()
                        If parentName = "script" OrElse parentName = "style" OrElse parentName = "a" OrElse parentName = "button" OrElse parentName = "img" Then
                            node.InnerHtml = HtmlEntity.Entitize(HtmlEntity.DeEntitize(originalNodeInnerHtml), True, True)
                            Continue For
                        End If
                    End If
                    Dim innerText As String = HtmlEntity.DeEntitize(originalNodeInnerHtml)
                    If String.IsNullOrWhiteSpace(innerText) Then
                        node.InnerHtml = HtmlEntity.Entitize(innerText, True, True)
                        Continue For
                    End If
                    innerText = Regex.Replace(innerText, "(​|" & ChrW(&H200B) & ")", String.Empty)
                    innerText = Regex.Replace(innerText, "( | )", " ")
                    innerText = innerText.Replace("’", "'")
                    innerText = innerText.Replace("–", "--")

                    Dim linesArr As String() = innerText.Split(New String() {vbLf, vbCrLf, vbCr}, StringSplitOptions.None)
                    Dim encodedLines As New List(Of String)()
                    For Each lineItem As String In linesArr
                        Dim words As String() = lineItem.Split(" "c)
                        Dim encodedWords As New List(Of String)()
                        For Each word As String In words
                            If String.IsNullOrWhiteSpace(word) Then
                                encodedWords.Add(word)
                                Continue For
                            End If
                            Dim encodeWord As Boolean = True
                            For Each func As String In FunctionList
                                If word.Contains(func) Then
                                    encodeWord = False
                                    Exit For
                                End If
                            Next
                            If Not encodeWord Then
                                encodedWords.Add(HtmlEntity.Entitize(word, True, True))
                                Continue For
                            End If
                            If word.StartsWith("http", StringComparison.OrdinalIgnoreCase) OrElse word.StartsWith("www.", StringComparison.OrdinalIgnoreCase) OrElse Regex.IsMatch(word, "\.(png|jpg|jpeg|gif|ico)$", RegexOptions.IgnoreCase) Then
                                encodeWord = False
                            End If
                            If encodeWord Then
                                Dim encodedChars As New StringBuilder()
                                For Each c As Char In word
                                    If Not Char.IsWhiteSpace(c) Then
                                        Dim randomIndex As Integer = random.Next(randomTexts.Length)
                                        Dim randomTextVal As String = randomTexts(randomIndex)
                                        Dim randomItemIndex As Integer = random.Next(0, fontword.Count)
                                        Dim randomItemVal As String = fontword(randomItemIndex)
                                        encodedChars.Append(c)
                                        Dim spanContent As String = HtmlEntity.Entitize(randomTextVal & randomItemVal, True, True)
                                        encodedChars.Append($"<span style=""font-size:0.00000000000000000000000000000048927%;"">{spanContent}</span>")
                                    Else
                                        encodedChars.Append(c)
                                    End If
                                Next
                                encodedWords.Add(encodedChars.ToString())
                            Else
                                encodedWords.Add(HtmlEntity.Entitize(word, True, True))
                            End If
                        Next
                        encodedLines.Add(String.Join(" ", encodedWords))
                    Next
                    node.InnerHtml = String.Join(vbLf, encodedLines)
                Next
                encryptedOutputHtml = htmlDoc.DocumentNode.OuterHtml
                worker.ReportProgress(100, "Devil encryption complete.")
        End Select
        e.Result = encryptedOutputHtml
    End Sub

    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        ProgressBarControl1.EditValue = e.ProgressPercentage
        If e.UserState IsNot Nothing AndAlso TypeOf e.UserState Is String Then
            lblStatus.Text = e.UserState.ToString()
        End If
    End Sub

    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        If e.Cancelled Then
            If lblStatus.Text.Contains("Please") OrElse lblStatus.Text.Contains("select") OrElse lblStatus.Text.Contains("language type") Then
                XtraMessageBox.Show(lblStatus.Text, "Input Required", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Else
                XtraMessageBox.Show("Operation was cancelled by user or due to an input error.", "Cancelled", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        ElseIf e.Error IsNot Nothing Then
            XtraMessageBox.Show($"An error occurred during the operation: {e.Error.Message}{Environment.NewLine}{e.Error.StackTrace}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            lblStatus.Text = "Error!"
        ElseIf e.Result IsNot Nothing AndAlso TypeOf e.Result Is String Then
            Dim resultHtml As String = CStr(e.Result)
            LetterEncoder_Richtext2.Text = resultHtml
            If WebBrowser1 IsNot Nothing Then
                WebBrowser1.DocumentText = "0"
                WebBrowser1.DocumentText = resultHtml
            End If
            lblStatus.Text = "Encryption successful!"
            XtraMessageBox.Show("Encryption process completed successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            lblStatus.Text = "Operation completed with no result or unexpected result type."
            If WebBrowser1 IsNot Nothing Then WebBrowser1.DocumentText = "<html><body>Encryption failed or produced no output.</body></html>"
        End If

        bntEncodedLetter.Enabled = True
        ProgressBarControl1.EditValue = 0
        If Not (e.Error IsNot Nothing OrElse e.Cancelled) Then
            lblStatus.Text = "Ready"
        End If
    End Sub

    Private Sub LetterEncoder_bntEncrypt_Click(sender As Object, e As EventArgs) Handles bntEncodedLetter.Click
        If cmbEncryption.SelectedIndex = -1 Then
            XtraMessageBox.Show("Please select an encryption type first!", "Input Required", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        If String.IsNullOrWhiteSpace(LetterEncoder_Richtext.Text) Then
            XtraMessageBox.Show("Please load or paste HTML content into the left editor first!", "Input Required", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If cmbEncryption.SelectedIndex = 1 AndAlso CheckOP = 0 Then
            XtraMessageBox.Show("For Devil Encryption, please select a language type (French, Russian, etc.) from the checkboxes.", "Input Required", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        bntEncodedLetter.Enabled = False
        lblStatus.Text = "Processing..."
        ProgressBarControl1.EditValue = 0
        LetterEncoder_Richtext2.ResetText()
        If WebBrowser1 IsNot Nothing Then WebBrowser1.DocumentText = "<html><body>Processing...</body></html>"
        BackgroundWorker1.RunWorkerAsync()
    End Sub

    Private Sub bntSaveLetter_Click(sender As Object, e As EventArgs) Handles bntSendToSender.Click
        If String.IsNullOrWhiteSpace(LetterEncoder_Richtext2.Text) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("There is no encrypted message to save. Please encrypt a letter first.", "Nothing to Save", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        Try
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim targetFolder As String = Path.Combine(desktopPath, "Letter Encoded")
            If Not Directory.Exists(targetFolder) Then
                Directory.CreateDirectory(targetFolder)
            End If
            Dim fileName As String = $"Letter Encoded_{DateTime.Now:yyyyMMdd_HHmmss}.html"
            Dim filePath As String = Path.Combine(targetFolder, fileName)
            System.IO.File.WriteAllText(filePath, LetterEncoder_Richtext2.Text, System.Text.Encoding.UTF8)
            Process.Start(New ProcessStartInfo(filePath) With {.UseShellExecute = True})
            DevExpress.XtraEditors.XtraMessageBox.Show($"Encrypted letter saved to:{Environment.NewLine}{filePath}{Environment.NewLine}and opened automatically.", "Save Successful", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show($"An error occurred while saving or opening the file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
#End Region

#Region "Letter HTMl Editor"
    ' Empty Region
#End Region

    Private Sub frmLetterEncoddvb_Load(sender As Object, e As EventArgs) Handles Me.Load
        BackgroundWorker1.WorkerReportsProgress = True
        BackgroundWorker1.WorkerSupportsCancellation = True
        cmbEncryption.Properties.Items.Clear()
        cmbEncryption.Properties.Items.Add("Best Sender Vip (HTML Entities)") ' Index 0
        cmbEncryption.Properties.Items.Add("Devil Encryption (Hidden Text)")  ' Index 1
        cmbEncryption.SelectedIndex = 0
        cmbEncryption_SelectedIndexChanged(cmbEncryption, EventArgs.Empty)
        lblStatus.Text = "Ready"
        LetterEncoder_Richtext_TextChanged(Nothing, Nothing)
        If WebBrowser1 IsNot Nothing Then
            WebBrowser1.ScriptErrorsSuppressed = True
            WebBrowser1.DocumentText = "<html><body style='font-family: Segoe UI; color: #ccc; background-color: #282a36;'>Preview will appear here.</body></html>"
        End If
        Create_EncodedTypes() ' <--- استدعاء لملء النصوص العشوائية
    End Sub

    Private Sub LetterEncoder_Richtext_TextChanged(sender As Object, e As EventArgs) Handles LetterEncoder_Richtext.TextChanged
        Dim hasText As Boolean = Not String.IsNullOrWhiteSpace(LetterEncoder_Richtext.Text)
        bntEncodedLetter.Enabled = hasText
        If WebBrowser1 IsNot Nothing AndAlso LetterEncoder_Richtext.ContainsFocus Then
            WebBrowser1.DocumentText = "0"
            WebBrowser1.DocumentText = LetterEncoder_Richtext.Text
        End If
    End Sub

    Private Sub UpdateDevilEncryptionCheckState(changedCheckBox As CheckEdit)
        Dim allChecks As CheckEdit() = {CheckFrench, CheckRussian, CheckJapanese, CheckHindi, CheckArabic}
        Dim newCheckOPValue As Integer = 0
        For Each chk As CheckEdit In allChecks
            If chk Is changedCheckBox Then
                If chk.Checked Then
                    Select Case chk.Name
                        Case "CheckFrench" : newCheckOPValue = 10
                        Case "CheckRussian" : newCheckOPValue = 20
                        Case "CheckJapanese" : newCheckOPValue = 30
                        Case "CheckHindi" : newCheckOPValue = 40
                        Case "CheckArabic" : newCheckOPValue = 50
                    End Select
                    chk.ForeColor = Color.White
                Else
                    newCheckOPValue = 0
                    chk.ForeColor = Color.DimGray
                End If
            Else
                chk.Checked = False
                chk.ForeColor = Color.DimGray
            End If
        Next
        CheckOP = newCheckOPValue
        Debug.WriteLine($"UpdateDevilEncryptionCheckState: CheckOP updated to: {CheckOP}")
    End Sub

    Private Sub CheckFrench_CheckedChanged(sender As Object, e As EventArgs) Handles CheckFrench.CheckedChanged
        UpdateDevilEncryptionCheckState(TryCast(sender, CheckEdit))
    End Sub
    Private Sub CheckRussian_CheckedChanged(sender As Object, e As EventArgs) Handles CheckRussian.CheckedChanged
        UpdateDevilEncryptionCheckState(TryCast(sender, CheckEdit))
    End Sub
    Private Sub CheckJapanese_CheckedChanged(sender As Object, e As EventArgs) Handles CheckJapanese.CheckedChanged
        UpdateDevilEncryptionCheckState(TryCast(sender, CheckEdit))
    End Sub
    Private Sub CheckHindi_CheckedChanged(sender As Object, e As EventArgs) Handles CheckHindi.CheckedChanged
        UpdateDevilEncryptionCheckState(TryCast(sender, CheckEdit))
    End Sub
    Private Sub CheckArabic_CheckedChanged(sender As Object, e As EventArgs) Handles CheckArabic.CheckedChanged
        UpdateDevilEncryptionCheckState(TryCast(sender, CheckEdit))
    End Sub

    Private Sub LetterEncoder_bntClearAll_Click(sender As Object, e As EventArgs) Handles BntClearAll.Click
        LetterEncoder_txtFilepath.ResetText()
        textSershNormal.ResetText()
        LetterEncoder_Richtext.ResetText()
        ClearRichTextBoxFormatting(LetterEncoder_Richtext)
        txtSearch2.ResetText()
        LetterEncoder_Richtext2.ResetText()
        ClearRichTextBoxFormatting(LetterEncoder_Richtext2)
        cmbEncryption.SelectedIndex = 0
        bntEncodedLetter.Enabled = False
        LetterEncoder_Richtext.Focus()
        lblStatus.Text = "Ready"
        ProgressBarControl1.EditValue = 0
        If WebBrowser1 IsNot Nothing Then WebBrowser1.DocumentText = "<html><body style='font-family: Segoe UI; color: #ccc; background-color: #282a36;'>Preview will appear here.</body></html>"
    End Sub

    Private Sub SetDevilCheckControlsEnabled(areEnabled As Boolean)
        Dim controlsToToggle As Control() = {CheckFrench, CheckRussian, CheckJapanese, CheckHindi, CheckArabic}

        If Me.IsHandleCreated Then
            Me.BeginInvoke(New Action(Sub()
                                          For Each ctrl As Control In controlsToToggle
                                              If ctrl IsNot Nothing AndAlso Not ctrl.IsDisposed Then
                                                  ctrl.Enabled = areEnabled
                                              End If
                                          Next
                                          If CheckFrench IsNot Nothing AndAlso Not CheckFrench.IsDisposed AndAlso CheckFrench.Parent IsNot Nothing Then
                                              CheckFrench.Parent.Refresh()
                                          End If
                                      End Sub))
        Else
            For Each ctrl As Control In controlsToToggle
                If ctrl IsNot Nothing Then
                    ctrl.Enabled = areEnabled
                End If
            Next
        End If
    End Sub

    Private Sub cmbEncryption_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbEncryption.SelectedIndexChanged
        Debug.WriteLine($"--- cmbEncryption_SelectedIndexChanged Fired ---")
        Debug.WriteLine($"Selected Index: {cmbEncryption.SelectedIndex}, Selected Text: {cmbEncryption.Text}")

        Dim devilEncryptionIsSelected As Boolean = (cmbEncryption.SelectedIndex = 1)
        Debug.WriteLine($"Devil Encryption Selected: {devilEncryptionIsSelected}")

        SetDevilCheckControlsEnabled(devilEncryptionIsSelected)

        If Not devilEncryptionIsSelected Then
            CheckOP = 0
            Dim tempChecks As CheckEdit() = {CheckFrench, CheckRussian, CheckJapanese, CheckHindi, CheckArabic}
            For Each chk As CheckEdit In tempChecks
                If chk IsNot Nothing AndAlso Not chk.IsDisposed Then
                    chk.Checked = False
                End If
            Next
        End If
        Debug.WriteLine($"--- cmbEncryption_SelectedIndexChanged Finished. CheckOP: {CheckOP} ---")
    End Sub

    Private Sub LetterEncoder_bntBrowse_Click(sender As Object, e As EventArgs) Handles BntAddLetter.Click
        Dim ofd As New OpenFileDialog()
        ofd.Filter = "HTML Files (*.html;*.htm)|*.html;*.htm|All files (*.*)|*.*"
        ofd.Title = "Open HTML File"
        If ofd.ShowDialog() = DialogResult.OK Then
            Try
                LetterEncoder_txtFilepath.Text = ofd.FileName
                Dim doc As New HtmlAgilityPack.HtmlDocument()
                doc.Load(ofd.FileName, True)
                Dim loadedHtml As String = doc.DocumentNode.OuterHtml
                LetterEncoder_Richtext.Text = loadedHtml
                LetterEncoder_Richtext2.ResetText()
                lblStatus.Text = "File loaded. Ready to encrypt."
                If WebBrowser1 IsNot Nothing Then
                    WebBrowser1.DocumentText = "0"
                    WebBrowser1.DocumentText = loadedHtml
                End If
            Catch fileEx As FileNotFoundException
                XtraMessageBox.Show($"Error: File not found at {ofd.FileName}", "File Load Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Catch ex As Exception
                XtraMessageBox.Show($"Error loading file: {ex.Message}. Please ensure the file is accessible and correctly encoded (preferably UTF-8).", "File Load Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub ClearRichTextBoxFormatting(rtb As RichTextBox)
        If rtb Is Nothing OrElse rtb.IsDisposed OrElse rtb.Disposing Then Return
        rtb.SelectAll()
        rtb.SelectionColor = Color.White
        rtb.SelectionBackColor = Color.FromArgb(40, 42, 59)
        rtb.DeselectAll()
    End Sub

    Private Sub LetterEncoder_bntClear_Click(sender As Object, e As EventArgs) Handles bntClearNormal.Click
        textSershNormal.ResetText()
        ClearRichTextBoxFormatting(LetterEncoder_Richtext)
        LetterEncoder_Richtext.Focus()
        lblStatus.Text = "Ready"
    End Sub

    Private Sub SearchInRichTextBox(rtb As RichTextBox, searchTextBox As TextEdit)
        Dim searchText As String = searchTextBox.Text
        If String.IsNullOrWhiteSpace(rtb.Text) Then Return
        ClearRichTextBoxFormatting(rtb)
        If String.IsNullOrWhiteSpace(searchText) Then
            If lblStatus IsNot Nothing AndAlso (lblStatus.Text.StartsWith("Found") OrElse lblStatus.Text.StartsWith("No matches")) Then
                lblStatus.Text = "Ready"
            End If
            Return
        End If
        Dim matchCount As Integer = 0
        Dim searchWords() As String = searchText.Split(New Char() {","c, " "c}, StringSplitOptions.RemoveEmptyEntries)
        For Each wordToFind As String In searchWords
            If String.IsNullOrWhiteSpace(wordToFind) Then Continue For
            Dim startIndex As Integer = 0
            While startIndex < rtb.TextLength
                Dim wordStartIndex As Integer = rtb.Find(wordToFind, startIndex, RichTextBoxFinds.None)
                If wordStartIndex <> -1 Then
                    rtb.SelectionStart = wordStartIndex
                    rtb.SelectionLength = wordToFind.Length
                    rtb.SelectionColor = Color.Black
                    rtb.SelectionBackColor = Color.Yellow
                    matchCount += 1
                    startIndex = wordStartIndex + wordToFind.Length
                Else
                    Exit While
                End If
            End While
        Next
        rtb.DeselectAll()
        If matchCount = 0 AndAlso Not String.IsNullOrWhiteSpace(searchText) Then
            lblStatus.Text = $"No matches found for '{searchText}'."
        ElseIf matchCount > 0 Then
            lblStatus.Text = $"Found {matchCount} match(es) for '{searchText}'."
        End If
    End Sub

    Private Sub LetterEncoder_bntSearch_Click(sender As Object, e As EventArgs) Handles BntSerchNormal.Click
        SearchInRichTextBox(LetterEncoder_Richtext, textSershNormal)
        LetterEncoder_Richtext.Focus()
    End Sub

    Private Sub textSershNormal_EditValueChanged(sender As Object, e As EventArgs) Handles textSershNormal.EditValueChanged
        SearchInRichTextBox(LetterEncoder_Richtext, textSershNormal)
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles bntSerch2.Click
        SearchInRichTextBox(LetterEncoder_Richtext2, txtSearch2)
        LetterEncoder_Richtext2.Focus()
    End Sub
    Private Sub txtSearch2_EditValueChanged(sender As Object, e As EventArgs) Handles txtSearch2.EditValueChanged
        SearchInRichTextBox(LetterEncoder_Richtext2, txtSearch2)
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles bntCLear2.Click
        txtSearch2.ResetText()
        ClearRichTextBoxFormatting(LetterEncoder_Richtext2)
        LetterEncoder_Richtext2.Focus()
        lblStatus.Text = "Ready"
    End Sub

    Private Sub Create_EncodedTypes()
        ' تأكد من أن أسماء عناصر التحكم هذه تتطابق مع ما لديك في الـ Designer
        ' وأنها من نوع يسمح بتعيين خاصية .Text (مثل TextEdit, MemoEdit)
        Try
            If Func_Arabic IsNot Nothing Then
                If String.IsNullOrWhiteSpace(Func_Arabic.Text) Then
                    Func_Arabic.Text = "كلمةعشوائيةجدا" & vbCrLf &
                                       "نصآخرغريبومختلف" & vbCrLf &
                                       "بعضالحروفالمبعثرةهناوهناك" & vbCrLf &
                                       "سلسلةمنالرموزوالكلماتغيرالمفهومة" & vbCrLf &
                                       "هذا نص تجريبي آخر للتشفير باللغة العربية"
                    Debug.WriteLine("Func_Arabic.Text populated.")
                End If
            Else
                Debug.WriteLine("Error: Func_Arabic control is Nothing (not found on form).")
            End If

            If Func_Hinid IsNot Nothing Then
                If String.IsNullOrWhiteSpace(Func_Hinid.Text) Then
                    Func_Hinid.Text = "एकयादृच्छिकशब्दयहाँ" & vbCrLf &
                                      "अजीबोगरीबपाठसंदेश" & vbCrLf &
                                      "कुछबिखरेहुएअक्षरऔरसंख्याएँ"
                    Debug.WriteLine("Func_Hinid.Text populated.")
                End If
            Else
                Debug.WriteLine("Error: Func_Hinid control is Nothing (not found on form).")
            End If

            If Func_ٌRussian IsNot Nothing Then
                If String.IsNullOrWhiteSpace(Func_ٌRussian.Text) Then
                    Func_ٌRussian.Text = "случайноесловотекст" & vbCrLf &
                                        "странныйсимвольныйнабор" & vbCrLf &
                                        "ещеоднастрокадляпримера"
                    Debug.WriteLine("Func_ٌRussian.Text populated.")
                End If
            Else
                Debug.WriteLine("Error: Func_ٌRussian control is Nothing (not found on form).")
            End If

            If Func_ٌFranch IsNot Nothing Then
                If String.IsNullOrWhiteSpace(Func_ٌFranch.Text) Then
                    Func_ٌFranch.Text = "motfrançaisaléatoire" & vbCrLf &
                                       "textetrèsétrangeici" & vbCrLf &
                                       "uncodemystérieuxpourvous"
                    Debug.WriteLine("Func_ٌFranch.Text populated.")
                End If
            Else
                Debug.WriteLine("Error: Func_ٌFranch control is Nothing (not found on form).")
            End If

            If Func_Japanese IsNot Nothing Then
                If String.IsNullOrWhiteSpace(Func_Japanese.Text) Then
                    Func_Japanese.Text = "ランダムな日本の単語" & vbCrLf &
                                         "奇妙なテキスト文字列" & vbCrLf &
                                         "いくつかの散在文字"
                    Debug.WriteLine("Func_Japanese.Text populated.")
                End If
            Else
                Debug.WriteLine("Error: Func_Japanese control is Nothing (not found on form).")
            End If

        Catch ex As Exception
            Debug.WriteLine($"Error in Create_EncodedTypes: {ex.Message}")
            ' يمكنك إضافة XtraMessageBox هنا إذا أردت إعلام المستخدم بالخطأ
            ' XtraMessageBox.Show($"Error initializing random texts: {ex.Message}", "Initialization Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub HTMLEDITOR_BntSave_Click(sender As Object, e As EventArgs) Handles bntSaveLetter.Click
        If String.IsNullOrWhiteSpace(LetterEncoder_Richtext.Text) Then
            XtraMessageBox.Show("There is no content to save.", "Empty Content", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        Dim sdf As New SaveFileDialog
        sdf.Filter = "HTML files (*.html;*.htm)|*.html;*.htm|All files (*.*)|*.*"
        sdf.Title = "Save Original HTML As"
        sdf.FileName = "OriginalLetter.html"
        sdf.DefaultExt = ".html"
        If sdf.ShowDialog() = DialogResult.OK Then
            Try
                File.WriteAllText(sdf.FileName, LetterEncoder_Richtext.Text, System.Text.Encoding.UTF8)
                XtraMessageBox.Show("Original HTML file saved successfully!", "Save Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                XtraMessageBox.Show($"Error saving file: {ex.Message}", "Save Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
End Class