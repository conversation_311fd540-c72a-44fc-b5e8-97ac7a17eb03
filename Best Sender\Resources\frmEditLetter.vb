﻿Imports System.Text.RegularExpressions
Imports System.Drawing.Imaging
Imports DevExpress.XtraEditors
Imports System.Security.Permissions
Imports ZXing
Imports ZXing.QrCode
Imports ZXing.Common
Imports ZXing.Rendering
Imports DevExpress.XtraBars
Imports DevExpress.XtraGrid
<PermissionSet(SecurityAction.Demand, Name:="FullTrust")>
Public Class frmEditLetter
    Private isUpdating As Boolean = False
    Private originalHtmlContent As String = String.Empty
    Private qrCodeImage As Image = Nothing
    Private webBrowserAvailable As Boolean = False

    Public Sub New()
        Try
            ' Check if we can create ActiveX controls (STA thread required)
            If Threading.Thread.CurrentThread.GetApartmentState() = Threading.ApartmentState.STA Then
                webBrowserAvailable = True
            Else
                webBrowserAvailable = False
            End If

            ' Initialize the form
            InitializeComponent()

            ' If WebBrowser is not available, hide the preview tab
            If Not webBrowserAvailable Then
                XtraTabPage2.PageVisible = False
            End If

        Catch ex As Exception
            ' If there's an error creating the form, try without WebBrowser
            webBrowserAvailable = False
            Debug.WriteLine($"Error in frmEditLetter constructor: {ex.Message}")
        End Try
    End Sub
    ' Constants for placeholders
    Private Const QR_PLACEHOLDER As String = "[-QRCode-]"
    Private Const EMAIL_PLACEHOLDER As String = "[-Email-]"
    Private Const DEFAULT_EMAIL As String = "<EMAIL>"
    ' Custom QR Renderer Class
    Public Class CustomPatternRenderer
        Implements IBarcodeRenderer(Of Bitmap)
        Public Function Render(matrix As BitMatrix, format As BarcodeFormat, content As String) As Bitmap Implements IBarcodeRenderer(Of Bitmap).Render
            Return Render(matrix, format, content, Nothing)
        End Function
        Public Function Render(matrix As BitMatrix, format As BarcodeFormat, content As String, options As EncodingOptions) As Bitmap Implements IBarcodeRenderer(Of Bitmap).Render
            Dim width = matrix.Width
            Dim height = matrix.Height
            Dim bmp As New Bitmap(width, height, Imaging.PixelFormat.Format32bppRgb)
            Dim rnd As New Random()
            Using g As Graphics = Graphics.FromImage(bmp)
                g.Clear(Color.White)
                For y = 0 To height - 1
                    For x = 0 To width - 1
                        If matrix(x, y) Then
                            ' Randomly choose between square or circle
                            If rnd.NextDouble() > 0.5 Then
                                g.FillRectangle(Brushes.Black, x, y, 1, 1)
                            Else
                                g.FillEllipse(Brushes.Black, x - 0.5F, y - 0.5F, 2, 2)
                            End If
                        End If
                    Next
                Next
            End Using
            Return bmp
        End Function
    End Class
    ' Initialize browser emulation
    Private Sub SetBrowserEmulation()
        Try
            Dim appName = System.IO.Path.GetFileNameWithoutExtension(Reflection.Assembly.GetExecutingAssembly().Location)
            Microsoft.Win32.Registry.SetValue(
                "HKEY_CURRENT_USER\Software\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_BROWSER_EMULATION",
                appName, 11001, Microsoft.Win32.RegistryValueKind.DWord)
        Catch ex As Exception
            Debug.WriteLine("Error setting browser emulation: " & ex.Message)
        End Try
    End Sub
    Private Sub frmEditLetter_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' Set browser emulation if WebBrowser is available
            If webBrowserAvailable Then
                SetBrowserEmulation()
            End If

            InitializeControls()

            ' Load content from main form
            If Not String.IsNullOrEmpty(frmEmailSender.txtLetter.Text) Then
                originalHtmlContent = frmEmailSender.txtLetter.Text
                RichTextBox2.Text = originalHtmlContent
            End If
            ' Load QR Code image if exists
            If frmEmailSender.Pic_QRCode.Image IsNot Nothing Then
                SetQRCodeImage(frmEmailSender.Pic_QRCode.Image)
            End If

            ' Only update WebBrowser if it's available
            If webBrowserAvailable Then
                UpdateWebBrowser()
            End If
        Catch ex As Exception
            XtraMessageBox.Show("Error loading data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub InitializeControls()
        isUpdating = True
        Try
            ' Only initialize WebBrowser if it's available
            If webBrowserAvailable AndAlso WebBrowser1 IsNot Nothing Then
                WebBrowser1.DocumentText = "<html><body></body></html>"
            End If
        Catch ex As Exception
            Debug.WriteLine("Error initializing WebBrowser: " & ex.Message)
        End Try
        RichTextBox2.Clear()
        isUpdating = False
    End Sub
    Public Sub SetQRCodeImage(img As Image)
        ' Dispose old image if exists
        If qrCodeImage IsNot Nothing Then
            qrCodeImage.Dispose()
        End If
        ' Copy new image
        qrCodeImage = If(img IsNot Nothing, New Bitmap(img), Nothing)
        UpdateWebBrowser()
    End Sub
    Private Sub RichTextBox2_TextChanged(sender As Object, e As EventArgs) Handles RichTextBox2.TextChanged
        If Not isUpdating Then
            UpdateWebBrowser()
        End If
    End Sub
    Private Sub UpdateWebBrowser()
        If isUpdating OrElse RichTextBox2.IsDisposed Then Return

        ' Only update WebBrowser if it's available
        If Not webBrowserAvailable OrElse WebBrowser1 Is Nothing Then
            Return
        End If

        Try
            Dim htmlContent = BuildHtmlDocument(RichTextBox2.Text)
            ' Alternative method to ensure proper loading
            WebBrowser1.Navigate("about:blank")
            While WebBrowser1.ReadyState <> WebBrowserReadyState.Complete
                Application.DoEvents()
            End While
            WebBrowser1.Document?.OpenNew(True)
            WebBrowser1.Document?.Write(htmlContent)
            WebBrowser1.Refresh()
        Catch ex As Exception
            Debug.WriteLine("WebBrowser update error: " & ex.ToString())
            ' Don't show error message for threading issues
            If Not ex.Message.Contains("apartment") Then
                XtraMessageBox.Show("Error displaying content: " & ex.Message, "Error",
                                 MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        End Try
    End Sub
    Private Function BuildHtmlDocument(content As String) As String
        Dim processedContent = ProcessHtmlContent(content)
        Return $"
        <!DOCTYPE html>
        <html>
        <head>
            <meta http-equiv='X-UA-Compatible' content='IE=edge'>
            <meta charset='UTF-8'>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 15px; }}
                img {{ max-width: 100%; height: auto; }}
                .error {{ color: red; }}
            </style>
        </head>
        <body>
            {processedContent}
        </body>
        </html>"
    End Function
    Private Function ProcessHtmlContent(htmlContent As String) As String
        If String.IsNullOrWhiteSpace(htmlContent) Then Return "&nbsp;"
        Dim processed = htmlContent
        ' Process new lines
        processed = processed.Replace(Environment.NewLine, "<br/>")
        ' Process placeholders
        processed = processed.Replace(EMAIL_PLACEHOLDER,
                    $"<span class='email'>{DEFAULT_EMAIL}</span>")
        processed = ProcessQRCode(processed)
        Return processed
    End Function
    Private Function ProcessQRCode(content As String) As String
        If Not content.Contains(QR_PLACEHOLDER) Then Return content
        ' If no QRCode image exists, generate a random one
        If qrCodeImage Is Nothing Then
            qrCodeImage = GenerateCustomQR(Guid.NewGuid().ToString(), 200)
        End If
        Try
            Dim base64 = ImageToBase64(qrCodeImage)
            Return content.Replace(QR_PLACEHOLDER,
                $"<img src='data:image/png;base64,{base64}' alt='QR Code'/>")
        Catch ex As Exception
            Return content.Replace(QR_PLACEHOLDER,
                $"<span class='error'>QR Code Error: {ex.Message}</span>")
        End Try
    End Function
    Public Function GenerateCustomQR(content As String, size As Integer) As Bitmap
        Dim options As New QrCodeEncodingOptions With {
            .DisableECI = True,
            .CharacterSet = "UTF-8",
            .Width = size,
            .Height = size,
            .Margin = 1
        }
        Dim writer As New BarcodeWriter With {
            .Format = BarcodeFormat.QR_CODE,
            .Options = options,
            .Renderer = New CustomPatternRenderer()
        }
        Return writer.Write(If(String.IsNullOrEmpty(content), Guid.NewGuid().ToString(), content))
    End Function
    Private Function ImageToBase64(img As Image) As String
        Using ms As New IO.MemoryStream()
            img.Save(ms, ImageFormat.Png)
            Return Convert.ToBase64String(ms.ToArray())
        End Using
    End Function
    Private Sub SearchDelayTimer_Tick(sender As Object, e As EventArgs) Handles SearchDelayTimer.Tick
        SearchDelayTimer.Stop() ' نوقف المؤقت عشان ما يكرر البحث
        ' ننفذ نفس كود البحث والتلوين هنا
        If String.IsNullOrEmpty(txtFind.Text) Then
            RichTextBox2.Select(0, 0)
            Return
        End If
        Dim originalSelectionStart As Integer = RichTextBox2.SelectionStart
        Dim originalSelectionLength As Integer = RichTextBox2.SelectionLength
        RichTextBox2.SelectAll()
        RichTextBox2.SelectionBackColor = Color.Gray
        Dim index As Integer = 0
        While index < RichTextBox2.TextLength
            index = RichTextBox2.Find(txtFind.Text, index, RichTextBoxFinds.None)
            If index = -1 Then Exit While
            RichTextBox2.Select(index, txtFind.Text.Length)
            RichTextBox2.SelectionBackColor = Color.Red
            index += txtFind.Text.Length
        End While
        RichTextBox2.Select(originalSelectionStart, originalSelectionLength)
    End Sub
    Private Sub BTNSave_Click(sender As Object, e As EventArgs)
        If String.IsNullOrEmpty(txtFind.Text) OrElse String.IsNullOrEmpty(txtFind.Text) Then
            Return
        End If
        Try
            ' استبدال الكلمة التي تم العثور عليها بالكلمة الجديدة في RichTextBox
            RichTextBox2.Text = RichTextBox2.Text.Replace(txtFind.Text, txtFind.Text)
            ' تحديث النص في frmEmailSender
            frmEmailSender.txtLetter.Text = RichTextBox2.Text
            ' إظهار رسالة بنجاح العملية
            XtraMessageBox.Show("Changes saved successfully", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            ' في حال حدوث خطأ
            XtraMessageBox.Show("Error saving changes: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub frmEditLetter_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        ' Clean up resources
        If qrCodeImage IsNot Nothing Then
            qrCodeImage.Dispose()
            qrCodeImage = Nothing
        End If
    End Sub
    Private Sub BntSerch_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntSerch.ItemClick
        ' عند الضغط على زر البحث نبحث عن الكلمة في RichTextBox
        If String.IsNullOrEmpty(txtFind.Text) Then
            Return
        End If
        Dim index = RichTextBox2.Find(txtFind.Text, 0, RichTextBoxFinds.None)
        If index >= 0 Then
            RichTextBox2.Select(index, txtFind.Text.Length)
            RichTextBox2.ScrollToCaret()
        Else
            XtraMessageBox.Show("Word not found.", "Search Result", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub BntChange_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntChange.ItemClick
        If String.IsNullOrEmpty(txtFind.Text) OrElse String.IsNullOrEmpty(txtChange.Text) Then
            Return
        End If
        Try
            ' استبدال الكلمة الموجودة في txtFind بالكلمة الموجودة في txtChange
            Dim updatedText As String = RichTextBox2.Text.Replace(txtFind.Text, txtChange.Text)
            ' تعيين النص الجديد لـ RichTextBox
            RichTextBox2.Text = updatedText
            ' تحديث النص في frmEmailSender
            frmEmailSender.txtLetter.Text = updatedText
            ' إظهار رسالة بنجاح العملية
            XtraMessageBox.Show("Change applied successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            XtraMessageBox.Show("Error applying change: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub BntCopyLetter_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntCopyLetter.ItemClick
        If String.IsNullOrWhiteSpace(RichTextBox2.Text) Then
            XtraMessageBox.Show("You Don't have any letter.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        Else
            Clipboard.SetText(RichTextBox2.Text)
            XtraMessageBox.Show("Letter copied Successfully!", "Copied", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub RichTextBox2_MouseDown(sender As Object, e As MouseEventArgs) Handles RichTextBox2.MouseDown
        'التحقق من أن النقر بالزر الأيمن
        If e.Button = MouseButtons.Right Then
            PopupMenu1.ShowPopup(RichTextBox2.PointToScreen(e.Location))
        End If
    End Sub
    Private Sub ComboBoxEdit2_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBoxEdit2.SelectedIndexChanged
        If ComboBoxEdit2.SelectedIndex >= 0 Then
            Select Case ComboBoxEdit2.SelectedItem.ToString()
                Case "Receiver Email"
                    txtChange.Text = "[-Email-]"
                Case "Encoded Email 64"
                    txtChange.Text = "[-Email64-]"
                Case "Start Name With Upper Case"
                    txtChange.Text = "[-UCase-]"
                Case "Add Link"
                    txtChange.Text = "[-Link-]"
                Case "Send With Logo"
                    txtChange.Text = "[-Logo-]"
                Case "Show QRCode Link"
                    txtChange.Text = "[-QRCode-]"
                Case "Random Character 3"
                    txtChange.Text = "[-RCh3-]"
                Case "Random Character 4"
                    txtChange.Text = "[-RCh4-]"
                Case "Random Character 5"
                    txtChange.Text = "[-RCh5-]"
                Case "Random Character 6"
                    txtChange.Text = "[-RCh6-]"
                Case "Random Character 7"
                    txtChange.Text = "[-RCh7-]"
                Case "Random Number 3"
                    txtChange.Text = "[-RN3-]"
                Case "Random Number 4"
                    txtChange.Text = "[-RN4-]"
                Case "Random Number 5"
                    txtChange.Text = "[-RN5-]"
                Case "Random Number 6"
                    txtChange.Text = "[-RN6-]"
                Case "Random Number 7"
                    txtChange.Text = "[-RN7-]"
                Case "Random IP"
                    txtChange.Text = "[-IP-]"
                Case "Random IP China"
                    txtChange.Text = "[-IPChina-]"
                Case "Show The Domain"
                    txtChange.Text = "[-Domain-]"
                Case "Show Name"
                    txtChange.Text = "[-Name-]"
                Case "Show Company Name"
                    txtChange.Text = "[-CompanyName-]"
                Case "Show Date"
                    txtChange.Text = "[-Date-]"
                Case "Show Time"
                    txtChange.Text = "[-Time-]"
                Case "Show Date Tomorrow"
                    txtChange.Text = "[-DateTomorrow-]"
                Case "Random Country"
                    txtChange.Text = "[-RCountry-]"
                Case "Random Browser"
                    txtChange.Text = "[-RandomBrowser-]"
                Case "Show Random Fake phone Numbers"
                    txtChange.Text = "[-FakePhone-]"
                Case "Show Random Fake Email Address Office"
                    txtChange.Text = "[-FakeEmail-]"
                Case "Show Random Street New York"
                    txtChange.Text = "[-NewYork-]"
            End Select
        End If
    End Sub
    Private Sub txtFind_TextChanged(sender As Object, e As EventArgs) Handles txtFind.TextChanged, txtChange.TextChanged
        SearchDelayTimer.Stop() ' نوقف المؤقت أولاً
        SearchDelayTimer.Start() ' نعيد تشغيله
    End Sub
    Private Sub BarButtonItem3_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem3.ItemClick
        ' جلب أو إنشاء نسخة من frmEmailSender
        Dim emailForm As frmEmailSender = Nothing
        ' التحقق إذا كانت الفورم مفتوحة بالفعل
        For Each frm As Form In Application.OpenForms
            If TypeOf frm Is frmEmailSender Then
                emailForm = DirectCast(frm, frmEmailSender)
                Exit For
            End If
        Next
        '' لو مش مفتوحة، نفتحها
        'If emailForm Is Nothing Then
        '    emailForm = New frmEmailSender
        '    emailForm.Show()
        'Else
        '    emailForm.BringToFront()
        'End If
        ' نقل البيانات مباشرة
        emailForm.txtLetter.Clear()
        emailForm.txtLetter.Text = Me.RichTextBox2.Text
    End Sub
    Private Sub BarButtonItem4_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem4.ItemClick
        RichTextBox2.Clear()
        txtChange.Clear()
        txtFind.Clear()
    End Sub
End Class