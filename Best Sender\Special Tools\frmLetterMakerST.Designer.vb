﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmLetterMakerST
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmLetterMakerST))
        Me.BarManager1 = New DevExpress.XtraBars.BarManager(Me.components)
        Me.barDockControlTop = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlBottom = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlLeft = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlRight = New DevExpress.XtraBars.BarDockControl()
        Me.BarButtonItem1 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem2 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem3 = New DevExpress.XtraBars.BarButtonItem()
        Me.PopupMenu1 = New DevExpress.XtraBars.PopupMenu(Me.components)
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.LayoutControl3 = New DevExpress.XtraLayout.LayoutControl()
        Me.GroupControl6 = New DevExpress.XtraEditors.GroupControl()
        Me.LeftAlignPictureBox = New DevExpress.XtraEditors.PictureEdit()
        Me.CenterAlignPictureBox = New DevExpress.XtraEditors.PictureEdit()
        Me.RightAlignPictureBox = New DevExpress.XtraEditors.PictureEdit()
        Me.LayoutControl4 = New DevExpress.XtraLayout.LayoutControl()
        Me.TextBoxMessage = New System.Windows.Forms.TextBox()
        Me.LayoutControlGroup4 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem18 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup2 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem19 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.LayoutControl2 = New DevExpress.XtraLayout.LayoutControl()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.WebBrowserPreview = New System.Windows.Forms.WebBrowser()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem20 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.BntReset = New DevExpress.XtraEditors.SimpleButton()
        Me.BNT_Backup = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonSaveHTML = New DevExpress.XtraEditors.SimpleButton()
        Me.fonttitel = New DevExpress.XtraEditors.SpinEdit()
        Me.TextButtonname = New DevExpress.XtraEditors.TextEdit()
        Me.TextBoxSignature = New DevExpress.XtraEditors.TextEdit()
        Me.FontSizeMessage = New DevExpress.XtraEditors.SpinEdit()
        Me.ComboBox_ButtonColor = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ComboBox_TextColor = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.ComboBox_TitleColor = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TextBoxLinklk = New DevExpress.XtraEditors.TextEdit()
        Me.TextBoxLogoLM = New DevExpress.XtraEditors.TextEdit()
        Me.TextBoxTitle = New DevExpress.XtraEditors.TextEdit()
        Me.CheckBoxBoldText = New DevExpress.XtraEditors.CheckEdit()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem12 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem13 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem14 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem15 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem17 = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PopupMenu1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel3.SuspendLayout()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        Me.Panel2.SuspendLayout()
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl3.SuspendLayout()
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl6.SuspendLayout()
        CType(Me.LeftAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CenterAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RightAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl4.SuspendLayout()
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl2.SuspendLayout()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.fonttitel.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextButtonname.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxSignature.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.FontSizeMessage.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBox_ButtonColor.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBox_TextColor.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBox_TitleColor.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxLinklk.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxLogoLM.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxTitle.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckBoxBoldText.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'BarManager1
        '
        Me.BarManager1.DockControls.Add(Me.barDockControlTop)
        Me.BarManager1.DockControls.Add(Me.barDockControlBottom)
        Me.BarManager1.DockControls.Add(Me.barDockControlLeft)
        Me.BarManager1.DockControls.Add(Me.barDockControlRight)
        Me.BarManager1.Form = Me
        Me.BarManager1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.BarButtonItem1, Me.BarButtonItem2, Me.BarButtonItem3})
        Me.BarManager1.MaxItemId = 3
        '
        'barDockControlTop
        '
        Me.barDockControlTop.CausesValidation = False
        Me.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top
        Me.barDockControlTop.Location = New System.Drawing.Point(0, 0)
        Me.barDockControlTop.Manager = Me.BarManager1
        Me.barDockControlTop.Size = New System.Drawing.Size(980, 0)
        '
        'barDockControlBottom
        '
        Me.barDockControlBottom.CausesValidation = False
        Me.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.barDockControlBottom.Location = New System.Drawing.Point(0, 604)
        Me.barDockControlBottom.Manager = Me.BarManager1
        Me.barDockControlBottom.Size = New System.Drawing.Size(980, 0)
        '
        'barDockControlLeft
        '
        Me.barDockControlLeft.CausesValidation = False
        Me.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left
        Me.barDockControlLeft.Location = New System.Drawing.Point(0, 0)
        Me.barDockControlLeft.Manager = Me.BarManager1
        Me.barDockControlLeft.Size = New System.Drawing.Size(0, 604)
        '
        'barDockControlRight
        '
        Me.barDockControlRight.CausesValidation = False
        Me.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right
        Me.barDockControlRight.Location = New System.Drawing.Point(980, 0)
        Me.barDockControlRight.Manager = Me.BarManager1
        Me.barDockControlRight.Size = New System.Drawing.Size(0, 604)
        '
        'BarButtonItem1
        '
        Me.BarButtonItem1.Caption = "Save"
        Me.BarButtonItem1.Id = 0
        Me.BarButtonItem1.Name = "BarButtonItem1"
        '
        'BarButtonItem2
        '
        Me.BarButtonItem2.Caption = "Default Settings"
        Me.BarButtonItem2.Id = 1
        Me.BarButtonItem2.Name = "BarButtonItem2"
        '
        'BarButtonItem3
        '
        Me.BarButtonItem3.Caption = "Reset All"
        Me.BarButtonItem3.Id = 2
        Me.BarButtonItem3.Name = "BarButtonItem3"
        '
        'PopupMenu1
        '
        Me.PopupMenu1.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem1), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem2), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem3)})
        Me.PopupMenu1.Manager = Me.BarManager1
        Me.PopupMenu1.Name = "PopupMenu1"
        '
        'Panel3
        '
        Me.Panel3.Controls.Add(Me.LayoutControl1)
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel3.Location = New System.Drawing.Point(0, 0)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(980, 604)
        Me.Panel3.TabIndex = 5
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.Panel2)
        Me.LayoutControl1.Controls.Add(Me.Panel1)
        Me.LayoutControl1.Controls.Add(Me.BntReset)
        Me.LayoutControl1.Controls.Add(Me.BNT_Backup)
        Me.LayoutControl1.Controls.Add(Me.ButtonSaveHTML)
        Me.LayoutControl1.Controls.Add(Me.fonttitel)
        Me.LayoutControl1.Controls.Add(Me.TextButtonname)
        Me.LayoutControl1.Controls.Add(Me.TextBoxSignature)
        Me.LayoutControl1.Controls.Add(Me.FontSizeMessage)
        Me.LayoutControl1.Controls.Add(Me.ComboBox_ButtonColor)
        Me.LayoutControl1.Controls.Add(Me.ComboBox_TextColor)
        Me.LayoutControl1.Controls.Add(Me.ComboBox_TitleColor)
        Me.LayoutControl1.Controls.Add(Me.TextBoxLinklk)
        Me.LayoutControl1.Controls.Add(Me.TextBoxLogoLM)
        Me.LayoutControl1.Controls.Add(Me.TextBoxTitle)
        Me.LayoutControl1.Controls.Add(Me.CheckBoxBoldText)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(980, 604)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.LayoutControl3)
        Me.Panel2.Location = New System.Drawing.Point(16, 420)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(948, 168)
        Me.Panel2.TabIndex = 555
        '
        'LayoutControl3
        '
        Me.LayoutControl3.Controls.Add(Me.GroupControl6)
        Me.LayoutControl3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl3.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl3.Name = "LayoutControl3"
        Me.LayoutControl3.Root = Me.LayoutControlGroup2
        Me.LayoutControl3.Size = New System.Drawing.Size(948, 168)
        Me.LayoutControl3.TabIndex = 0
        Me.LayoutControl3.Text = "LayoutControl3"
        '
        'GroupControl6
        '
        Me.GroupControl6.Controls.Add(Me.LeftAlignPictureBox)
        Me.GroupControl6.Controls.Add(Me.CenterAlignPictureBox)
        Me.GroupControl6.Controls.Add(Me.RightAlignPictureBox)
        Me.GroupControl6.Controls.Add(Me.LayoutControl4)
        Me.GroupControl6.Location = New System.Drawing.Point(16, 16)
        Me.GroupControl6.Name = "GroupControl6"
        Me.GroupControl6.Size = New System.Drawing.Size(916, 136)
        Me.GroupControl6.TabIndex = 516
        Me.GroupControl6.Text = "Write Letter"
        '
        'LeftAlignPictureBox
        '
        Me.LeftAlignPictureBox.Cursor = System.Windows.Forms.Cursors.Hand
        Me.LeftAlignPictureBox.EditValue = CType(resources.GetObject("LeftAlignPictureBox.EditValue"), Object)
        Me.LeftAlignPictureBox.Location = New System.Drawing.Point(100, 3)
        Me.LeftAlignPictureBox.Name = "LeftAlignPictureBox"
        Me.LeftAlignPictureBox.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.LeftAlignPictureBox.Properties.Appearance.Options.UseBackColor = True
        Me.LeftAlignPictureBox.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.LeftAlignPictureBox.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.LeftAlignPictureBox.Size = New System.Drawing.Size(29, 20)
        Me.LeftAlignPictureBox.TabIndex = 511
        '
        'CenterAlignPictureBox
        '
        Me.CenterAlignPictureBox.EditValue = CType(resources.GetObject("CenterAlignPictureBox.EditValue"), Object)
        Me.CenterAlignPictureBox.Location = New System.Drawing.Point(167, 4)
        Me.CenterAlignPictureBox.Name = "CenterAlignPictureBox"
        Me.CenterAlignPictureBox.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.CenterAlignPictureBox.Properties.Appearance.Options.UseBackColor = True
        Me.CenterAlignPictureBox.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.CenterAlignPictureBox.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.CenterAlignPictureBox.Size = New System.Drawing.Size(32, 20)
        Me.CenterAlignPictureBox.TabIndex = 510
        '
        'RightAlignPictureBox
        '
        Me.RightAlignPictureBox.Cursor = System.Windows.Forms.Cursors.Hand
        Me.RightAlignPictureBox.EditValue = CType(resources.GetObject("RightAlignPictureBox.EditValue"), Object)
        Me.RightAlignPictureBox.Location = New System.Drawing.Point(238, 4)
        Me.RightAlignPictureBox.Name = "RightAlignPictureBox"
        Me.RightAlignPictureBox.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.RightAlignPictureBox.Properties.Appearance.Options.UseBackColor = True
        Me.RightAlignPictureBox.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.RightAlignPictureBox.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.[Auto]
        Me.RightAlignPictureBox.Size = New System.Drawing.Size(34, 20)
        Me.RightAlignPictureBox.TabIndex = 509
        '
        'LayoutControl4
        '
        Me.LayoutControl4.Controls.Add(Me.TextBoxMessage)
        Me.LayoutControl4.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl4.Location = New System.Drawing.Point(2, 29)
        Me.LayoutControl4.Name = "LayoutControl4"
        Me.LayoutControl4.Root = Me.LayoutControlGroup4
        Me.LayoutControl4.Size = New System.Drawing.Size(912, 105)
        Me.LayoutControl4.TabIndex = 0
        Me.LayoutControl4.Text = "LayoutControl4"
        '
        'TextBoxMessage
        '
        Me.TextBoxMessage.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TextBoxMessage.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.TextBoxMessage.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.TextBoxMessage.ForeColor = System.Drawing.Color.White
        Me.TextBoxMessage.Location = New System.Drawing.Point(16, 16)
        Me.TextBoxMessage.Margin = New System.Windows.Forms.Padding(4)
        Me.TextBoxMessage.Multiline = True
        Me.TextBoxMessage.Name = "TextBoxMessage"
        Me.TextBoxMessage.Size = New System.Drawing.Size(880, 73)
        Me.TextBoxMessage.TabIndex = 56
        Me.TextBoxMessage.Text = "Dear [-Email-]," & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "We are pleased to inform you about the latest HR updates and pol" &
    "icies."
        Me.TextBoxMessage.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'LayoutControlGroup4
        '
        Me.LayoutControlGroup4.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup4.GroupBordersVisible = False
        Me.LayoutControlGroup4.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem18})
        Me.LayoutControlGroup4.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup4.Size = New System.Drawing.Size(912, 105)
        Me.LayoutControlGroup4.TextVisible = False
        '
        'LayoutControlItem18
        '
        Me.LayoutControlItem18.Control = Me.TextBoxMessage
        Me.LayoutControlItem18.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem18.Name = "LayoutControlItem12"
        Me.LayoutControlItem18.Size = New System.Drawing.Size(886, 79)
        Me.LayoutControlItem18.TextVisible = False
        '
        'LayoutControlGroup2
        '
        Me.LayoutControlGroup2.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup2.GroupBordersVisible = False
        Me.LayoutControlGroup2.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem19})
        Me.LayoutControlGroup2.Name = "LayoutControlGroup2"
        Me.LayoutControlGroup2.Size = New System.Drawing.Size(948, 168)
        Me.LayoutControlGroup2.TextVisible = False
        '
        'LayoutControlItem19
        '
        Me.LayoutControlItem19.Control = Me.GroupControl6
        Me.LayoutControlItem19.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem19.Name = "LayoutControlItem19"
        Me.LayoutControlItem19.Size = New System.Drawing.Size(922, 142)
        Me.LayoutControlItem19.TextVisible = False
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.LayoutControl2)
        Me.Panel1.Location = New System.Drawing.Point(354, 16)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(610, 398)
        Me.Panel1.TabIndex = 554
        '
        'LayoutControl2
        '
        Me.LayoutControl2.Controls.Add(Me.Label1)
        Me.LayoutControl2.Controls.Add(Me.WebBrowserPreview)
        Me.LayoutControl2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl2.Name = "LayoutControl2"
        Me.LayoutControl2.Root = Me.LayoutControlGroup1
        Me.LayoutControl2.Size = New System.Drawing.Size(610, 398)
        Me.LayoutControl2.TabIndex = 0
        Me.LayoutControl2.Text = "LayoutControl2"
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(16, 16)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(578, 24)
        Me.Label1.TabIndex = 5
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'WebBrowserPreview
        '
        Me.WebBrowserPreview.Location = New System.Drawing.Point(16, 46)
        Me.WebBrowserPreview.MinimumSize = New System.Drawing.Size(20, 20)
        Me.WebBrowserPreview.Name = "WebBrowserPreview"
        Me.WebBrowserPreview.ScriptErrorsSuppressed = True
        Me.WebBrowserPreview.Size = New System.Drawing.Size(578, 336)
        Me.WebBrowserPreview.TabIndex = 4
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem16, Me.LayoutControlItem20})
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(610, 398)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem16
        '
        Me.LayoutControlItem16.Control = Me.WebBrowserPreview
        Me.LayoutControlItem16.Location = New System.Drawing.Point(0, 30)
        Me.LayoutControlItem16.Name = "LayoutControlItem16"
        Me.LayoutControlItem16.Size = New System.Drawing.Size(584, 342)
        Me.LayoutControlItem16.TextVisible = False
        '
        'LayoutControlItem20
        '
        Me.LayoutControlItem20.Control = Me.Label1
        Me.LayoutControlItem20.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem20.Name = "LayoutControlItem20"
        Me.LayoutControlItem20.Size = New System.Drawing.Size(584, 30)
        Me.LayoutControlItem20.TextVisible = False
        '
        'BntReset
        '
        Me.BntReset.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntReset.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntReset.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntReset.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntReset.Appearance.Options.UseBackColor = True
        Me.BntReset.Appearance.Options.UseBorderColor = True
        Me.BntReset.Appearance.Options.UseFont = True
        Me.BntReset.Appearance.Options.UseForeColor = True
        Me.BntReset.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntReset.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntReset.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntReset.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntReset.AppearanceDisabled.Options.UseBackColor = True
        Me.BntReset.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntReset.AppearanceDisabled.Options.UseFont = True
        Me.BntReset.AppearanceDisabled.Options.UseForeColor = True
        Me.BntReset.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntReset.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntReset.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntReset.AppearanceHovered.Options.UseBackColor = True
        Me.BntReset.AppearanceHovered.Options.UseBorderColor = True
        Me.BntReset.AppearanceHovered.Options.UseForeColor = True
        Me.BntReset.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntReset.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntReset.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntReset.AppearancePressed.Options.UseBackColor = True
        Me.BntReset.AppearancePressed.Options.UseBorderColor = True
        Me.BntReset.AppearancePressed.Options.UseForeColor = True
        Me.BntReset.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntReset.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntReset.Location = New System.Drawing.Point(236, 376)
        Me.BntReset.Name = "BntReset"
        Me.BntReset.Size = New System.Drawing.Size(112, 38)
        Me.BntReset.StyleController = Me.LayoutControl1
        Me.BntReset.TabIndex = 553
        Me.BntReset.Text = "Reset All"
        Me.BntReset.ToolTip = "Reset All"
        '
        'BNT_Backup
        '
        Me.BNT_Backup.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BNT_Backup.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BNT_Backup.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BNT_Backup.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BNT_Backup.Appearance.Options.UseBackColor = True
        Me.BNT_Backup.Appearance.Options.UseBorderColor = True
        Me.BNT_Backup.Appearance.Options.UseFont = True
        Me.BNT_Backup.Appearance.Options.UseForeColor = True
        Me.BNT_Backup.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BNT_Backup.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BNT_Backup.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BNT_Backup.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BNT_Backup.AppearanceDisabled.Options.UseBackColor = True
        Me.BNT_Backup.AppearanceDisabled.Options.UseBorderColor = True
        Me.BNT_Backup.AppearanceDisabled.Options.UseFont = True
        Me.BNT_Backup.AppearanceDisabled.Options.UseForeColor = True
        Me.BNT_Backup.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BNT_Backup.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BNT_Backup.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BNT_Backup.AppearanceHovered.Options.UseBackColor = True
        Me.BNT_Backup.AppearanceHovered.Options.UseBorderColor = True
        Me.BNT_Backup.AppearanceHovered.Options.UseForeColor = True
        Me.BNT_Backup.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BNT_Backup.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BNT_Backup.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BNT_Backup.AppearancePressed.Options.UseBackColor = True
        Me.BNT_Backup.AppearancePressed.Options.UseBorderColor = True
        Me.BNT_Backup.AppearancePressed.Options.UseForeColor = True
        Me.BNT_Backup.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BNT_Backup.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.default32x32
        Me.BNT_Backup.Location = New System.Drawing.Point(133, 376)
        Me.BNT_Backup.Name = "BNT_Backup"
        Me.BNT_Backup.Size = New System.Drawing.Size(97, 38)
        Me.BNT_Backup.StyleController = Me.LayoutControl1
        Me.BNT_Backup.TabIndex = 552
        Me.BNT_Backup.Text = "Default"
        Me.BNT_Backup.ToolTip = "Default Settings"
        '
        'ButtonSaveHTML
        '
        Me.ButtonSaveHTML.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ButtonSaveHTML.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.ButtonSaveHTML.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ButtonSaveHTML.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.ButtonSaveHTML.Appearance.Options.UseBackColor = True
        Me.ButtonSaveHTML.Appearance.Options.UseBorderColor = True
        Me.ButtonSaveHTML.Appearance.Options.UseFont = True
        Me.ButtonSaveHTML.Appearance.Options.UseForeColor = True
        Me.ButtonSaveHTML.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.ButtonSaveHTML.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.ButtonSaveHTML.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.ButtonSaveHTML.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseBackColor = True
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseBorderColor = True
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseFont = True
        Me.ButtonSaveHTML.AppearanceDisabled.Options.UseForeColor = True
        Me.ButtonSaveHTML.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.ButtonSaveHTML.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ButtonSaveHTML.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.ButtonSaveHTML.AppearanceHovered.Options.UseBackColor = True
        Me.ButtonSaveHTML.AppearanceHovered.Options.UseBorderColor = True
        Me.ButtonSaveHTML.AppearanceHovered.Options.UseForeColor = True
        Me.ButtonSaveHTML.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.ButtonSaveHTML.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ButtonSaveHTML.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.ButtonSaveHTML.AppearancePressed.Options.UseBackColor = True
        Me.ButtonSaveHTML.AppearancePressed.Options.UseBorderColor = True
        Me.ButtonSaveHTML.AppearancePressed.Options.UseForeColor = True
        Me.ButtonSaveHTML.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ButtonSaveHTML.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.ButtonSaveHTML.Location = New System.Drawing.Point(16, 376)
        Me.ButtonSaveHTML.Name = "ButtonSaveHTML"
        Me.ButtonSaveHTML.Size = New System.Drawing.Size(111, 38)
        Me.ButtonSaveHTML.StyleController = Me.LayoutControl1
        Me.ButtonSaveHTML.TabIndex = 551
        Me.ButtonSaveHTML.Text = "Save"
        Me.ButtonSaveHTML.ToolTip = "Save Letter"
        '
        'fonttitel
        '
        Me.fonttitel.EditValue = New Decimal(New Integer() {25, 0, 0, 0})
        Me.fonttitel.Location = New System.Drawing.Point(16, 60)
        Me.fonttitel.Name = "fonttitel"
        Me.fonttitel.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.fonttitel.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.fonttitel.Properties.Appearance.Options.UseBackColor = True
        Me.fonttitel.Properties.Appearance.Options.UseFont = True
        Me.fonttitel.Properties.Appearance.Options.UseTextOptions = True
        Me.fonttitel.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.fonttitel.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.fonttitel.Properties.IsFloatValue = False
        Me.fonttitel.Properties.MaskSettings.Set("mask", "n0")
        Me.fonttitel.Properties.NullValuePrompt = "Title Font Size"
        Me.fonttitel.Size = New System.Drawing.Size(194, 38)
        Me.fonttitel.StyleController = Me.LayoutControl1
        Me.fonttitel.TabIndex = 547
        '
        'TextButtonname
        '
        Me.TextButtonname.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextButtonname.EditValue = ""
        Me.TextButtonname.Location = New System.Drawing.Point(16, 296)
        Me.TextButtonname.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextButtonname.Name = "TextButtonname"
        Me.TextButtonname.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextButtonname.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextButtonname.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextButtonname.Properties.Appearance.Options.UseBackColor = True
        Me.TextButtonname.Properties.Appearance.Options.UseFont = True
        Me.TextButtonname.Properties.Appearance.Options.UseForeColor = True
        Me.TextButtonname.Properties.NullValuePrompt = "Name Button "
        Me.TextButtonname.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextButtonname.Size = New System.Drawing.Size(332, 30)
        Me.TextButtonname.StyleController = Me.LayoutControl1
        Me.TextButtonname.TabIndex = 549
        '
        'TextBoxSignature
        '
        Me.TextBoxSignature.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxSignature.EditValue = ""
        Me.TextBoxSignature.Location = New System.Drawing.Point(16, 340)
        Me.TextBoxSignature.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBoxSignature.Name = "TextBoxSignature"
        Me.TextBoxSignature.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxSignature.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxSignature.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxSignature.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxSignature.Properties.Appearance.Options.UseFont = True
        Me.TextBoxSignature.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxSignature.Properties.NullValuePrompt = "Best Regards, Team BSV"
        Me.TextBoxSignature.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxSignature.Size = New System.Drawing.Size(332, 30)
        Me.TextBoxSignature.StyleController = Me.LayoutControl1
        Me.TextBoxSignature.TabIndex = 548
        '
        'FontSizeMessage
        '
        Me.FontSizeMessage.EditValue = New Decimal(New Integer() {25, 0, 0, 0})
        Me.FontSizeMessage.Location = New System.Drawing.Point(216, 156)
        Me.FontSizeMessage.Name = "FontSizeMessage"
        Me.FontSizeMessage.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.[False]
        Me.FontSizeMessage.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.FontSizeMessage.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.FontSizeMessage.Properties.Appearance.Options.UseBackColor = True
        Me.FontSizeMessage.Properties.Appearance.Options.UseFont = True
        Me.FontSizeMessage.Properties.Appearance.Options.UseTextOptions = True
        Me.FontSizeMessage.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.FontSizeMessage.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.FontSizeMessage.Properties.IsFloatValue = False
        Me.FontSizeMessage.Properties.MaskSettings.Set("mask", "N00")
        Me.FontSizeMessage.Properties.NullValuePrompt = "Massage Font Size"
        Me.FontSizeMessage.Size = New System.Drawing.Size(132, 38)
        Me.FontSizeMessage.StyleController = Me.LayoutControl1
        Me.FontSizeMessage.TabIndex = 546
        '
        'ComboBox_ButtonColor
        '
        Me.ComboBox_ButtonColor.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboBox_ButtonColor.EditValue = "Red"
        Me.ComboBox_ButtonColor.Location = New System.Drawing.Point(16, 104)
        Me.ComboBox_ButtonColor.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ComboBox_ButtonColor.Name = "ComboBox_ButtonColor"
        Me.ComboBox_ButtonColor.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboBox_ButtonColor.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ComboBox_ButtonColor.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBox_ButtonColor.Properties.Appearance.Options.UseFont = True
        Me.ComboBox_ButtonColor.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBox_ButtonColor.Properties.NullValuePrompt = "Button Color"
        Me.ComboBox_ButtonColor.Size = New System.Drawing.Size(194, 38)
        Me.ComboBox_ButtonColor.StyleController = Me.LayoutControl1
        Me.ComboBox_ButtonColor.TabIndex = 545
        '
        'ComboBox_TextColor
        '
        Me.ComboBox_TextColor.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboBox_TextColor.EditValue = "Red"
        Me.ComboBox_TextColor.Location = New System.Drawing.Point(216, 104)
        Me.ComboBox_TextColor.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ComboBox_TextColor.Name = "ComboBox_TextColor"
        Me.ComboBox_TextColor.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboBox_TextColor.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ComboBox_TextColor.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBox_TextColor.Properties.Appearance.Options.UseFont = True
        Me.ComboBox_TextColor.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBox_TextColor.Properties.NullValuePrompt = "txt Color"
        Me.ComboBox_TextColor.Size = New System.Drawing.Size(132, 38)
        Me.ComboBox_TextColor.StyleController = Me.LayoutControl1
        Me.ComboBox_TextColor.TabIndex = 544
        '
        'ComboBox_TitleColor
        '
        Me.ComboBox_TitleColor.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboBox_TitleColor.EditValue = "Red"
        Me.ComboBox_TitleColor.Location = New System.Drawing.Point(16, 156)
        Me.ComboBox_TitleColor.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ComboBox_TitleColor.Name = "ComboBox_TitleColor"
        Me.ComboBox_TitleColor.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboBox_TitleColor.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ComboBox_TitleColor.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBox_TitleColor.Properties.Appearance.Options.UseFont = True
        Me.ComboBox_TitleColor.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBox_TitleColor.Properties.NullValuePrompt = "Title Color"
        Me.ComboBox_TitleColor.Size = New System.Drawing.Size(194, 38)
        Me.ComboBox_TitleColor.StyleController = Me.LayoutControl1
        Me.ComboBox_TitleColor.TabIndex = 543
        '
        'TextBoxLinklk
        '
        Me.TextBoxLinklk.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxLinklk.EditValue = ""
        Me.TextBoxLinklk.Location = New System.Drawing.Point(16, 208)
        Me.TextBoxLinklk.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBoxLinklk.Name = "TextBoxLinklk"
        Me.TextBoxLinklk.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxLinklk.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxLinklk.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxLinklk.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxLinklk.Properties.Appearance.Options.UseFont = True
        Me.TextBoxLinklk.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxLinklk.Properties.NullValuePrompt = "Button Link"
        Me.TextBoxLinklk.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxLinklk.Size = New System.Drawing.Size(332, 30)
        Me.TextBoxLinklk.StyleController = Me.LayoutControl1
        Me.TextBoxLinklk.TabIndex = 541
        '
        'TextBoxLogoLM
        '
        Me.TextBoxLogoLM.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxLogoLM.EditValue = ""
        Me.TextBoxLogoLM.Location = New System.Drawing.Point(16, 252)
        Me.TextBoxLogoLM.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBoxLogoLM.Name = "TextBoxLogoLM"
        Me.TextBoxLogoLM.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxLogoLM.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxLogoLM.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxLogoLM.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxLogoLM.Properties.Appearance.Options.UseFont = True
        Me.TextBoxLogoLM.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxLogoLM.Properties.NullValuePrompt = "Link Logo"
        Me.TextBoxLogoLM.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxLogoLM.Size = New System.Drawing.Size(332, 30)
        Me.TextBoxLogoLM.StyleController = Me.LayoutControl1
        Me.TextBoxLogoLM.TabIndex = 542
        '
        'TextBoxTitle
        '
        Me.TextBoxTitle.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxTitle.EditValue = ""
        Me.TextBoxTitle.Location = New System.Drawing.Point(16, 16)
        Me.TextBoxTitle.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBoxTitle.Name = "TextBoxTitle"
        Me.TextBoxTitle.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxTitle.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxTitle.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxTitle.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxTitle.Properties.Appearance.Options.UseFont = True
        Me.TextBoxTitle.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxTitle.Properties.NullValuePrompt = "Title Office 365"
        Me.TextBoxTitle.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxTitle.Size = New System.Drawing.Size(132, 30)
        Me.TextBoxTitle.StyleController = Me.LayoutControl1
        Me.TextBoxTitle.TabIndex = 540
        '
        'CheckBoxBoldText
        '
        Me.CheckBoxBoldText.Location = New System.Drawing.Point(216, 60)
        Me.CheckBoxBoldText.Name = "CheckBoxBoldText"
        Me.CheckBoxBoldText.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckBoxBoldText.Properties.Appearance.Options.UseFont = True
        Me.CheckBoxBoldText.Properties.Caption = "Bold Text"
        Me.CheckBoxBoldText.Size = New System.Drawing.Size(132, 27)
        Me.CheckBoxBoldText.StyleController = Me.LayoutControl1
        Me.CheckBoxBoldText.TabIndex = 550
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem2, Me.LayoutControlItem9, Me.LayoutControlItem11, Me.LayoutControlItem12, Me.LayoutControlItem13, Me.LayoutControlItem6, Me.LayoutControlItem14, Me.LayoutControlItem7, Me.LayoutControlItem10, Me.LayoutControlItem8, Me.LayoutControlItem15, Me.LayoutControlItem1, Me.LayoutControlItem4, Me.LayoutControlItem5, Me.LayoutControlItem3, Me.LayoutControlItem17})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(980, 604)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.fonttitel
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 44)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(200, 44)
        Me.LayoutControlItem2.TextVisible = False
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.ComboBox_ButtonColor
        Me.LayoutControlItem9.Location = New System.Drawing.Point(0, 88)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(200, 52)
        Me.LayoutControlItem9.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 8)
        Me.LayoutControlItem9.TextVisible = False
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.ComboBox_TitleColor
        Me.LayoutControlItem11.Location = New System.Drawing.Point(0, 140)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(200, 52)
        Me.LayoutControlItem11.TextVisible = False
        '
        'LayoutControlItem12
        '
        Me.LayoutControlItem12.Control = Me.TextBoxLinklk
        Me.LayoutControlItem12.Location = New System.Drawing.Point(0, 192)
        Me.LayoutControlItem12.Name = "LayoutControlItem12"
        Me.LayoutControlItem12.Size = New System.Drawing.Size(338, 44)
        Me.LayoutControlItem12.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 8)
        Me.LayoutControlItem12.TextVisible = False
        '
        'LayoutControlItem13
        '
        Me.LayoutControlItem13.Control = Me.TextBoxLogoLM
        Me.LayoutControlItem13.Location = New System.Drawing.Point(0, 236)
        Me.LayoutControlItem13.Name = "LayoutControlItem13"
        Me.LayoutControlItem13.Size = New System.Drawing.Size(338, 44)
        Me.LayoutControlItem13.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 8)
        Me.LayoutControlItem13.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.TextButtonname
        Me.LayoutControlItem6.Location = New System.Drawing.Point(0, 280)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(338, 44)
        Me.LayoutControlItem6.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 8)
        Me.LayoutControlItem6.TextVisible = False
        '
        'LayoutControlItem14
        '
        Me.LayoutControlItem14.Control = Me.TextBoxTitle
        Me.LayoutControlItem14.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem14.Name = "LayoutControlItem14"
        Me.LayoutControlItem14.Size = New System.Drawing.Size(338, 44)
        Me.LayoutControlItem14.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 200, 0, 8)
        Me.LayoutControlItem14.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.TextBoxSignature
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 324)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(338, 36)
        Me.LayoutControlItem7.TextVisible = False
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.ComboBox_TextColor
        Me.LayoutControlItem10.Location = New System.Drawing.Point(200, 88)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(138, 52)
        Me.LayoutControlItem10.TextVisible = False
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.FontSizeMessage
        Me.LayoutControlItem8.Location = New System.Drawing.Point(200, 140)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(138, 52)
        Me.LayoutControlItem8.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 8)
        Me.LayoutControlItem8.TextVisible = False
        '
        'LayoutControlItem15
        '
        Me.LayoutControlItem15.Control = Me.CheckBoxBoldText
        Me.LayoutControlItem15.Location = New System.Drawing.Point(200, 44)
        Me.LayoutControlItem15.Name = "LayoutControlItem15"
        Me.LayoutControlItem15.Size = New System.Drawing.Size(138, 44)
        Me.LayoutControlItem15.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.ButtonSaveHTML
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 360)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(117, 44)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.BNT_Backup
        Me.LayoutControlItem4.Location = New System.Drawing.Point(117, 360)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(103, 44)
        Me.LayoutControlItem4.TextVisible = False
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.BntReset
        Me.LayoutControlItem5.Location = New System.Drawing.Point(220, 360)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(118, 44)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.Panel1
        Me.LayoutControlItem3.Location = New System.Drawing.Point(338, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(616, 404)
        Me.LayoutControlItem3.TextVisible = False
        '
        'LayoutControlItem17
        '
        Me.LayoutControlItem17.Control = Me.Panel2
        Me.LayoutControlItem17.Location = New System.Drawing.Point(0, 404)
        Me.LayoutControlItem17.Name = "LayoutControlItem17"
        Me.LayoutControlItem17.Size = New System.Drawing.Size(954, 174)
        Me.LayoutControlItem17.TextVisible = False
        '
        'frmLetterMakerST
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(980, 604)
        Me.Controls.Add(Me.Panel3)
        Me.Controls.Add(Me.barDockControlLeft)
        Me.Controls.Add(Me.barDockControlRight)
        Me.Controls.Add(Me.barDockControlBottom)
        Me.Controls.Add(Me.barDockControlTop)
        Me.IconOptions.ShowIcon = False
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "frmLetterMakerST"
        Me.ShowInTaskbar = False
        Me.Text = "Letter Maker"
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PopupMenu1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel3.ResumeLayout(False)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        CType(Me.LayoutControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl3.ResumeLayout(False)
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl6.ResumeLayout(False)
        CType(Me.LeftAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CenterAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RightAlignPictureBox.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl4.ResumeLayout(False)
        CType(Me.LayoutControlGroup4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem19, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl2.ResumeLayout(False)
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem20, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.fonttitel.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextButtonname.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxSignature.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.FontSizeMessage.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBox_ButtonColor.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBox_TextColor.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBox_TitleColor.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxLinklk.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxLogoLM.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxTitle.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckBoxBoldText.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem17, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents BarManager1 As DevExpress.XtraBars.BarManager
    Friend WithEvents barDockControlTop As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlBottom As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlLeft As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlRight As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarButtonItem1 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem2 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem3 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents PopupMenu1 As DevExpress.XtraBars.PopupMenu
    Friend WithEvents Panel3 As Panel
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents Panel2 As Panel
    Friend WithEvents Panel1 As Panel
    Friend WithEvents LayoutControl2 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents WebBrowserPreview As WebBrowser
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents BntReset As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BNT_Backup As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonSaveHTML As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents fonttitel As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents TextButtonname As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextBoxSignature As DevExpress.XtraEditors.TextEdit
    Friend WithEvents FontSizeMessage As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents ComboBox_ButtonColor As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ComboBox_TextColor As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ComboBox_TitleColor As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TextBoxLinklk As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextBoxLogoLM As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextBoxTitle As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CheckBoxBoldText As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem12 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem13 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem14 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem15 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem17 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GroupControl6 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LayoutControl4 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents TextBoxMessage As TextBox
    Friend WithEvents LayoutControlGroup4 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem18 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents Label1 As Label
    Friend WithEvents LayoutControlItem20 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LeftAlignPictureBox As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents CenterAlignPictureBox As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents RightAlignPictureBox As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents LayoutControl3 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents LayoutControlGroup2 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem19 As DevExpress.XtraLayout.LayoutControlItem
End Class
