﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.AppContext</name>
  </assembly>
  <members>
    <member name="T:System.AppContext">
      <summary>为成员提供用于设置和检索有关应用程序的上下文数据。</summary>
    </member>
    <member name="P:System.AppContext.BaseDirectory">
      <summary>获取程序集冲突解决程序用来探测程序集的基目录的路径名。</summary>
      <returns>该程序集冲突解决程序用来探测程序集的基目录的路径名。</returns>
    </member>
    <member name="M:System.AppContext.SetSwitch(System.String,System.Boolean)">
      <summary>设置一个开关的值。</summary>
      <param name="switchName">开关的名称。</param>
      <param name="isEnabled">开关的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="switchName" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="switchName" /> 为 <see cref="F:System.String.Empty" />。</exception>
    </member>
    <member name="M:System.AppContext.TryGetSwitch(System.String,System.Boolean@)">
      <summary>Trues 来获取一个开关的值。</summary>
      <returns>true如果<paramref name="switchName" />设置和<paramref name="isEnabled" />参数中包含的开关 ； 值否则为false。</returns>
      <param name="switchName">开关的名称。</param>
      <param name="isEnabled">此方法返回时，包含值的<paramref name="switchName" />如果<paramref name="switchName" />找，或false如果<paramref name="switchName" />找不到。此参数未经初始化即被传递。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="switchName" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="switchName" /> 为 <see cref="F:System.String.Empty" />。</exception>
    </member>
  </members>
</doc>