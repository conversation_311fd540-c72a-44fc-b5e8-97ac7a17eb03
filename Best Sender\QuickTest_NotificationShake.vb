' ملف اختبار سريع لتأثير الاهتزاز
' يمكن إضافة هذا الكود مؤقتاً في frmMain للاختبار

#Region "اختبار سريع للاهتزاز - يمكن حذفه بعد الاختبار"

' إضافة زر اختبار مؤقت (يمكن إضافته في أي مكان مناسب)
Private Sub TestShakeButton_Click(sender As Object, e As EventArgs)
    ' محاكاة وجود تحديث متاح
    currentversion = "1.0"
    newestversion = "2.0"
    nv = 200
    cv = 100
    
    ' تحديث النص وبدء الاهتزاز
    UpdateNotificationText()
    StartNotificationShake()
    notificationIocn.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
    
    ' رسالة للمطور
    XtraMessageBox.Show("تم بدء اختبار الاهتزاز! ستلاحظ:" & vbCrLf & 
                       "1. اهتزاز الأيقونة بقوة" & vbCrLf & 
                       "2. تغيير ألوان النص" & vbCrLf & 
                       "3. النص بالإنجليزية" & vbCrLf & 
                       "4. سيتوقف تلقائياً بعد 5 ثوانٍ", 
                       "اختبار الاهتزاز", 
                       MessageBoxButtons.OK, 
                       MessageBoxIcon.Information)
End Sub

' اختبار إيقاف الاهتزاز
Private Sub StopShakeButton_Click(sender As Object, e As EventArgs)
    StopNotificationShake()
    XtraMessageBox.Show("تم إيقاف الاهتزاز!", "إيقاف الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information)
End Sub

' اختبار تحديث النص فقط
Private Sub TestTextButton_Click(sender As Object, e As EventArgs)
    currentversion = "1.5"
    newestversion = "2.1"
    UpdateNotificationText()
    notificationIocn.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
    XtraMessageBox.Show("تم تحديث النص فقط بدون اهتزاز", "اختبار النص", MessageBoxButtons.OK, MessageBoxIcon.Information)
End Sub

#End Region

' ملاحظات للمطور:
' 1. أضف الأزرار أعلاه مؤقتاً في النموذج للاختبار
' 2. يمكن استدعاء TestShakeButton_Click() من أي مكان
' 3. احذف هذا الملف بعد التأكد من عمل التأثير
' 4. التأثير سيعمل تلقائياً عند وجود تحديث حقيقي

' كود سريع لإضافة زر اختبار في النموذج:
'
' Dim testButton As New Button()
' testButton.Text = "اختبار الاهتزاز"
' testButton.Location = New Point(10, 10)
' testButton.Size = New Size(120, 30)
' AddHandler testButton.Click, AddressOf TestShakeButton_Click
' Me.Controls.Add(testButton)
