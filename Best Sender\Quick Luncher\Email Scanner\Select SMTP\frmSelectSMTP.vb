﻿Imports DevExpress.XtraEditors
Public Class frmSelectSMTP
    Private Sub ListBoxControl1_DoubleClick(sender As Object, e As EventArgs) Handles ListBoxControl1.DoubleClick
        Try
            If ListBoxControl1.SelectedItem Is Nothing Then Exit Sub
            Dim line As String = ListBoxControl1.SelectedItem.ToString.Trim
            Dim host As String = "", port As String = "587", email As String = "", pass As String = ""

            ' إضافة رسالة debug لمعرفة البيانات المختارة
            DevExpress.XtraEditors.XtraMessageBox.Show("Selected line: " & line, "Debug Info", MessageBoxButtons.OK, MessageBoxIcon.Information)

            ' لو الشكل : smtp|email|pass|port
            If line.Contains("|") Then
                Dim parts() As String = line.Split("|"c)
                If parts.Length >= 4 Then
                    host = parts(0).Trim
                    email = parts(1).Trim
                    pass = parts(2).Trim
                    port = parts(3).Trim
                ElseIf parts.Length = 3 Then
                    host = parts(0).Trim
                    email = parts(1).Trim
                    pass = parts(2).Trim
                    port = "587" ' default port
                End If

            ' لو الشكل : smtp:email:pass:port
            ElseIf line.Contains(":") AndAlso Not line.ToLower.Contains("server") AndAlso Not line.ToLower.Contains("host") Then
                Dim parts() As String = line.Split(":"c)
                If parts.Length >= 4 Then
                    host = parts(0).Trim
                    email = parts(1).Trim
                    pass = parts(2).Trim
                    port = parts(3).Trim
                ElseIf parts.Length = 3 Then
                    host = parts(0).Trim
                    email = parts(1).Trim
                    pass = parts(2).Trim
                    port = "587" ' default port
                End If

            ' لو الشكل : smtp,email,pass,port
            ElseIf line.Contains(",") Then
                Dim parts() As String = line.Split(","c)
                If parts.Length >= 4 Then
                    host = parts(0).Trim
                    email = parts(1).Trim
                    pass = parts(2).Trim
                    port = parts(3).Trim
                ElseIf parts.Length = 3 Then
                    host = parts(0).Trim
                    email = parts(1).Trim
                    pass = parts(2).Trim
                    port = "587" ' default port
                End If

            ' لو الشكل : smtp;email;pass;port
            ElseIf line.Contains(";") Then
                Dim parts() As String = line.Split(";"c)
                If parts.Length >= 4 Then
                    host = parts(0).Trim
                    email = parts(1).Trim
                    pass = parts(2).Trim
                    port = parts(3).Trim
                ElseIf parts.Length = 3 Then
                    host = parts(0).Trim
                    email = parts(1).Trim
                    pass = parts(2).Trim
                    port = "587" ' default port
                End If

            ' لو الشكل :
            ' Host Server: smtp
            ' Email : email
            ' pass : pass
            ElseIf line.ToLower.Contains("server") Or line.ToLower.Contains("host") Then
                ' ناخد كل السطور الى بعده لغاية لما نلاقي Email و Pass
                Dim index As Integer = ListBoxControl1.SelectedIndex
                Dim count As Integer = ListBoxControl1.Items.Count
                For i As Integer = index To Math.Min(index + 10, count - 1) ' نحدد البحث في 10 سطور فقط
                    Dim l As String = ListBoxControl1.Items(i).ToString.ToLower.Trim
                    If l.Contains("server") Or l.Contains("host") Then
                        If l.Contains(":") Then
                            host = l.Split(":")(1).Trim
                        End If
                    ElseIf l.Contains("email") Then
                        If l.Contains(":") Then
                            email = l.Split(":")(1).Trim
                        End If
                    ElseIf l.Contains("pass") Then
                        If l.Contains(":") Then
                            pass = l.Split(":")(1).Trim
                        End If
                    ElseIf l.Contains("port") Then
                        If l.Contains(":") Then
                            port = l.Split(":")(1).Trim
                        End If
                    End If
                    If host <> "" AndAlso email <> "" AndAlso pass <> "" Then
                        Exit For
                    End If
                Next
            End If

            ' التحقق من صحة البيانات
            If host = "" Or email = "" Or pass = "" Then
                DevExpress.XtraEditors.XtraMessageBox.Show("لم يتم العثور على بيانات SMTP صحيحة في السطر المختار." & vbNewLine &
                                                          "تأكد من أن التنسيق صحيح:" & vbNewLine &
                                                          "smtp|email|password|port" & vbNewLine &
                                                          "أو smtp:email:password:port" & vbNewLine &
                                                          "أو smtp,email,password,port",
                                                          "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If

            ' التحقق من صحة Port
            If Not IsNumeric(port) Then
                port = "587" ' default port
            End If

            ' إضافة رسالة debug لمعرفة البيانات المستخرجة
            DevExpress.XtraEditors.XtraMessageBox.Show("Host: " & host & vbNewLine &
                                                      "Email: " & email & vbNewLine &
                                                      "Password: " & pass & vbNewLine &
                                                      "Port: " & port,
                                                      "البيانات المستخرجة", MessageBoxButtons.OK, MessageBoxIcon.Information)

            ' ارسال البيانات الى EmailScanner
            If Me.Owner IsNot Nothing AndAlso TypeOf Me.Owner Is EmailScanner Then
                CType(Me.Owner, EmailScanner).SetSMTP(host, port, email, pass)
                Me.Close()
            Else
                DevExpress.XtraEditors.XtraMessageBox.Show("خطأ في ربط النافذة الأساسية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("حدث خطأ: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' إضافة method للاختيار بالضغط على Enter
    Private Sub ListBoxControl1_KeyDown(sender As Object, e As KeyEventArgs) Handles ListBoxControl1.KeyDown
        If e.KeyCode = Keys.Enter Then
            ListBoxControl1_DoubleClick(sender, Nothing)
        End If
    End Sub

    ' تحميل البيانات داخل ListBoxControl1 يتم من EmailScanner زي ما عملنا قبل كده
    ' الكود ده فقط للتعامل مع الاختيار من الـ ListBoxControl1
End Class
