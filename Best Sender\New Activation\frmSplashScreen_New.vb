﻿Imports System.ComponentModel
Imports System.Data.SqlClient
Imports DevExpress.XtraEditors
Imports MicrovisionActivation
Public Class frmSplashScreen_New
    Dim TICK_ As Integer = 0
    Private ALT_F4 As Boolean = False
    Private Sub Timer2_Tick(sender As Object, e As EventArgs) Handles Timer2.Tick
        If TICK_ Mod 2 = 0 Then
            lblLoading.Visible = False
        Else
            lblLoading.Visible = True
        End If
        TICK_ = TICK_ + 1
    End Sub
    Private Sub frmSplashScreen_New_Load(sender As Object, e As EventArgs) Handles Me.Load
        If My.Settings.PROSTRING = String.Empty Then
            Dim PRODUCTKEY = MicrovisionActivation.UserInfo.Machine_Serial.Value()
            My.Settings.PROSTRING = PRODUCTKEY
            My.Settings.Save()
        End If
        If Not StoreDataSet.CheckFile() Then
            Using frm As frmActivate_New = New frmActivate_New()
                If frm.ShowDialog() <> DialogResult.OK Then Process.GetCurrentProcess().Kill()
            End Using
        End If
        If StoreDataSet.StoredaTable.Rows.Count = 0 Then
            Using frm As frmActivate_New = New frmActivate_New()
                If frm.ShowDialog() <> DialogResult.OK Then Process.GetCurrentProcess().Kill()
            End Using
        End If
        StoreDataSet.dataset = Nothing
        ActivationKeyCode = StoreDataSet.StoredaTable.Rows(0)("System_ACTIVATION").ToString()
        ActivationKeyOwner = StoreDataSet.StoredaTable.Rows(0)("System_onwer").ToString()
        ActivateionType = StoreDataSet.StoredaTable.Rows(0)("ActivationType").ToString()
        ActivationDateTime = StoreDataSet.StoredaTable.Rows(0)("System_date").ToString()
        If ActivateionType = "Trail Version" Then
            ActivationExpireDate = StoreDataSet.StoredaTable.Rows(0)("System_Expiredate")
            Dim r1 = DateTime.Parse(Now)
            Dim r2 = DateTime.Parse(ActivationExpireDate)
            Dim result = r2 - r1
            ActivationRemainDays = MicrovisionActivation.Sec_Ref.FormatTimeSpan(result)
            'ActivationRemainDays = (ActivationExpireDate - date.Today).Days
            'ActivationTrailDays = StoreDataSet.StoredaTable.Rows(0)("System_TrailDays")
        Else
            ActivationExpireDate = Nothing
            ActivationRemainDays = 0
            ActivationTrailDays = 0
        End If
        MachineSerial = StoreDataSet.StoredaTable.Rows(0)("System_Serial").ToString()
        ActivationStatus = StoreDataSet.StoredaTable.Rows(0)("lic_flg")
        Sec_Ref.validate.Key = ActivationKeyCode
        Sec_Ref.validate.secretPhase = ApplicationkEYPASSWORD_ & "THISISME"
        If Not Sec_Ref.validate.IsValid Then
            Me.Hide()
            StoreDataSet.Delete()
            XtraMessageBox.Show("You are not allowed to use this activation on your current device", "instructions", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Process.GetCurrentProcess().Kill()
        End If
        If ActivateionType = "Trail Version" Then
            If ActivationRemainDays = "" Then
                Me.Hide()
                StoreDataSet.Delete()
                XtraMessageBox.Show("Trial period has expired. The program will now terminate", "instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Process.GetCurrentProcess().Kill()
                Process.Start("https://www.bestsendervip.com")
            End If
        End If
        If MachineSerial <> My.Settings.PROSTRING Then
            Me.Hide()
            StoreDataSet.Delete()
            XtraMessageBox.Show("You are not allowed to use this activation on your current device", "instructions", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Process.GetCurrentProcess().Kill()
        End If
        If BackgroundWorker1.IsBusy = False Then
            activeflg = False
            BackgroundWorker1.RunWorkerAsync()
        End If
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Sec_Ref.onlineCheckIsBusy = True
        If Sec_Ref.CheckInternet() = True Then
            If Sec_Ref.Openserver(My.Settings.appver) = True Then
                If Sec_Ref.CheckActivationKeyWork(GlobalVariables.ActivationKeyCode, MachineSerial, ApplicationName_) = True Then
                    ActivationStatus = False
                    'StoreDataSet.Delete()
                Else
                    ActivationStatus = True
                End If
            Else
                ActivationStatus = Convert.ToBoolean(StoreDataSet.StoredaTable.Rows(0)("lic_flg"))
            End If
            '================================ GET AND UPDATE ActivationModule ONLINE ===========================================
            ActivationModule = Sec_Ref.Get_ActivationModule(MachineSerial, ActivationKeyCode, ApplicationName_)
            StoreDataSet.UpdateActivationModule(ActivationModule)
            'CreateLicensePermissionList(ActivationModule)
            Try
                Dim sqlQuery As String = "UPDATE ActivationCodes SET PermissionUpdated= 'true' where User_Serial=N'" & MachineSerial & "' and ActivationCode=N'" & ActivationKeyCode & "' and App_Name=N'" & ApplicationName_ & "'"
                Dim command As New SqlCommand(sqlQuery, MicrovisionActivation.Sec_Ref.OnlineServerConn)
                command.ExecuteNonQuery()
            Catch ex As Exception
            End Try
        Else
            '================================ GET ActivationModule OFFLINE ===========================================
            ActivationModule = StoreDataSet.StoredaTable.Rows(0)("ActivationModule").ToString()
            'CreateLicensePermissionList(ActivationModule)
        End If
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        If ActivationStatus = False Then
            Me.Hide()
            Dim args As New XtraMessageBoxArgs()
            args.AutoCloseOptions.Delay = 5000
            args.AutoCloseOptions.ShowTimerOnDefaultButton = True
            args.DefaultButtonIndex = 0
            args.Icon = System.Drawing.SystemIcons.Information
            args.Caption = ApplicationName_
            args.Text = "  Your license has been suspended, the program will be closed within..... "
            args.Buttons = New DialogResult() {DialogResult.OK}
            XtraMessageBox.Show(args).ToString()
            Process.GetCurrentProcess().Kill()
        Else
            timer1.Enabled = True
            Sec_Ref.onlineCheckIsBusy = False
        End If
    End Sub
    Private Sub timer1_Tick(sender As Object, e As EventArgs) Handles timer1.Tick
        If Sec_Ref.onlineCheckIsBusy = False Then
            Me.Hide()
            timer1.Enabled = False
            frmMain.ShowDialog()
        End If
    End Sub
    Private Sub frmSplashScreen_New_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        If ALT_F4 Then
            e.Cancel = True
            Return
        End If
    End Sub
    Private Sub frmSplashScreen_New_KeyDown(sender As Object, e As KeyEventArgs) Handles Me.KeyDown
        ALT_F4 = (e.KeyCode.Equals(Keys.F4) AndAlso e.Alt = True)
    End Sub
End Class
