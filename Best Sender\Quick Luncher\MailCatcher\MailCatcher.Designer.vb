﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MailCatcher
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.txtWord = New DevExpress.XtraEditors.TextEdit()
        Me.BTN_SaveLink = New DevExpress.XtraEditors.SimpleButton()
        Me.BTN_SaveEmail = New DevExpress.XtraEditors.SimpleButton()
        Me.btnClear = New DevExpress.XtraEditors.SimpleButton()
        Me.btnStop = New DevExpress.XtraEditors.SimpleButton()
        Me.bntStart = New DevExpress.XtraEditors.SimpleButton()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.LayoutControl2 = New DevExpress.XtraLayout.LayoutControl()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumnEmail = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumnLink = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Panel1.SuspendLayout()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.txtWord.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel2.SuspendLayout()
        Me.Panel3.SuspendLayout()
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl2.SuspendLayout()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.LayoutControl1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(972, 68)
        Me.Panel1.TabIndex = 0
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.txtWord)
        Me.LayoutControl1.Controls.Add(Me.BTN_SaveLink)
        Me.LayoutControl1.Controls.Add(Me.BTN_SaveEmail)
        Me.LayoutControl1.Controls.Add(Me.btnClear)
        Me.LayoutControl1.Controls.Add(Me.btnStop)
        Me.LayoutControl1.Controls.Add(Me.bntStart)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(972, 68)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'txtWord
        '
        Me.txtWord.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtWord.EditValue = ""
        Me.txtWord.Location = New System.Drawing.Point(746, 16)
        Me.txtWord.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtWord.Name = "txtWord"
        Me.txtWord.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtWord.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtWord.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtWord.Properties.Appearance.Options.UseBackColor = True
        Me.txtWord.Properties.Appearance.Options.UseFont = True
        Me.txtWord.Properties.Appearance.Options.UseForeColor = True
        Me.txtWord.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EmailFromScanner32x32
        Me.txtWord.Properties.NullValuePrompt = "@gmail,Office,outlook"
        Me.txtWord.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtWord.Size = New System.Drawing.Size(193, 44)
        Me.txtWord.StyleController = Me.LayoutControl1
        Me.txtWord.TabIndex = 67
        '
        'BTN_SaveLink
        '
        Me.BTN_SaveLink.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_SaveLink.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_SaveLink.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_SaveLink.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_SaveLink.Appearance.Options.UseBackColor = True
        Me.BTN_SaveLink.Appearance.Options.UseBorderColor = True
        Me.BTN_SaveLink.Appearance.Options.UseFont = True
        Me.BTN_SaveLink.Appearance.Options.UseForeColor = True
        Me.BTN_SaveLink.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BTN_SaveLink.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BTN_SaveLink.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_SaveLink.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BTN_SaveLink.AppearanceDisabled.Options.UseBackColor = True
        Me.BTN_SaveLink.AppearanceDisabled.Options.UseBorderColor = True
        Me.BTN_SaveLink.AppearanceDisabled.Options.UseFont = True
        Me.BTN_SaveLink.AppearanceDisabled.Options.UseForeColor = True
        Me.BTN_SaveLink.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BTN_SaveLink.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_SaveLink.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BTN_SaveLink.AppearanceHovered.Options.UseBackColor = True
        Me.BTN_SaveLink.AppearanceHovered.Options.UseBorderColor = True
        Me.BTN_SaveLink.AppearanceHovered.Options.UseForeColor = True
        Me.BTN_SaveLink.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_SaveLink.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_SaveLink.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BTN_SaveLink.AppearancePressed.Options.UseBackColor = True
        Me.BTN_SaveLink.AppearancePressed.Options.UseBorderColor = True
        Me.BTN_SaveLink.AppearancePressed.Options.UseForeColor = True
        Me.BTN_SaveLink.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.LinkDomain32x32
        Me.BTN_SaveLink.Location = New System.Drawing.Point(537, 16)
        Me.BTN_SaveLink.Name = "BTN_SaveLink"
        Me.BTN_SaveLink.Size = New System.Drawing.Size(106, 38)
        Me.BTN_SaveLink.StyleController = Me.LayoutControl1
        Me.BTN_SaveLink.TabIndex = 4
        Me.BTN_SaveLink.Text = "Save Link"
        '
        'BTN_SaveEmail
        '
        Me.BTN_SaveEmail.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_SaveEmail.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_SaveEmail.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_SaveEmail.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_SaveEmail.Appearance.Options.UseBackColor = True
        Me.BTN_SaveEmail.Appearance.Options.UseBorderColor = True
        Me.BTN_SaveEmail.Appearance.Options.UseFont = True
        Me.BTN_SaveEmail.Appearance.Options.UseForeColor = True
        Me.BTN_SaveEmail.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BTN_SaveEmail.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BTN_SaveEmail.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_SaveEmail.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BTN_SaveEmail.AppearanceDisabled.Options.UseBackColor = True
        Me.BTN_SaveEmail.AppearanceDisabled.Options.UseBorderColor = True
        Me.BTN_SaveEmail.AppearanceDisabled.Options.UseFont = True
        Me.BTN_SaveEmail.AppearanceDisabled.Options.UseForeColor = True
        Me.BTN_SaveEmail.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BTN_SaveEmail.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_SaveEmail.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BTN_SaveEmail.AppearanceHovered.Options.UseBackColor = True
        Me.BTN_SaveEmail.AppearanceHovered.Options.UseBorderColor = True
        Me.BTN_SaveEmail.AppearanceHovered.Options.UseForeColor = True
        Me.BTN_SaveEmail.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_SaveEmail.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_SaveEmail.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BTN_SaveEmail.AppearancePressed.Options.UseBackColor = True
        Me.BTN_SaveEmail.AppearancePressed.Options.UseBorderColor = True
        Me.BTN_SaveEmail.AppearancePressed.Options.UseForeColor = True
        Me.BTN_SaveEmail.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Emailimage
        Me.BTN_SaveEmail.Location = New System.Drawing.Point(402, 16)
        Me.BTN_SaveEmail.Name = "BTN_SaveEmail"
        Me.BTN_SaveEmail.Size = New System.Drawing.Size(129, 38)
        Me.BTN_SaveEmail.StyleController = Me.LayoutControl1
        Me.BTN_SaveEmail.TabIndex = 3
        Me.BTN_SaveEmail.Text = "Save Email"
        '
        'btnClear
        '
        Me.btnClear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnClear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnClear.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnClear.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnClear.Appearance.Options.UseBackColor = True
        Me.btnClear.Appearance.Options.UseBorderColor = True
        Me.btnClear.Appearance.Options.UseFont = True
        Me.btnClear.Appearance.Options.UseForeColor = True
        Me.btnClear.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btnClear.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btnClear.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnClear.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnClear.AppearanceDisabled.Options.UseBackColor = True
        Me.btnClear.AppearanceDisabled.Options.UseBorderColor = True
        Me.btnClear.AppearanceDisabled.Options.UseFont = True
        Me.btnClear.AppearanceDisabled.Options.UseForeColor = True
        Me.btnClear.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btnClear.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnClear.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btnClear.AppearanceHovered.Options.UseBackColor = True
        Me.btnClear.AppearanceHovered.Options.UseBorderColor = True
        Me.btnClear.AppearanceHovered.Options.UseForeColor = True
        Me.btnClear.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnClear.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnClear.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btnClear.AppearancePressed.Options.UseBackColor = True
        Me.btnClear.AppearancePressed.Options.UseBorderColor = True
        Me.btnClear.AppearancePressed.Options.UseForeColor = True
        Me.btnClear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.btnClear.Location = New System.Drawing.Point(273, 16)
        Me.btnClear.Name = "btnClear"
        Me.btnClear.Size = New System.Drawing.Size(123, 38)
        Me.btnClear.StyleController = Me.LayoutControl1
        Me.btnClear.TabIndex = 2
        Me.btnClear.Text = "Reset All"
        '
        'btnStop
        '
        Me.btnStop.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnStop.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnStop.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnStop.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnStop.Appearance.Options.UseBackColor = True
        Me.btnStop.Appearance.Options.UseBorderColor = True
        Me.btnStop.Appearance.Options.UseFont = True
        Me.btnStop.Appearance.Options.UseForeColor = True
        Me.btnStop.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btnStop.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btnStop.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnStop.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnStop.AppearanceDisabled.Options.UseBackColor = True
        Me.btnStop.AppearanceDisabled.Options.UseBorderColor = True
        Me.btnStop.AppearanceDisabled.Options.UseFont = True
        Me.btnStop.AppearanceDisabled.Options.UseForeColor = True
        Me.btnStop.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btnStop.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnStop.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btnStop.AppearanceHovered.Options.UseBackColor = True
        Me.btnStop.AppearanceHovered.Options.UseBorderColor = True
        Me.btnStop.AppearanceHovered.Options.UseForeColor = True
        Me.btnStop.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnStop.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnStop.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btnStop.AppearancePressed.Options.UseBackColor = True
        Me.btnStop.AppearancePressed.Options.UseBorderColor = True
        Me.btnStop.AppearancePressed.Options.UseForeColor = True
        Me.btnStop.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.btnStop.Location = New System.Drawing.Point(145, 16)
        Me.btnStop.Name = "btnStop"
        Me.btnStop.Size = New System.Drawing.Size(122, 38)
        Me.btnStop.StyleController = Me.LayoutControl1
        Me.btnStop.TabIndex = 1
        Me.btnStop.Text = "Stop Search"
        '
        'bntStart
        '
        Me.bntStart.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntStart.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntStart.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntStart.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntStart.Appearance.Options.UseBackColor = True
        Me.bntStart.Appearance.Options.UseBorderColor = True
        Me.bntStart.Appearance.Options.UseFont = True
        Me.bntStart.Appearance.Options.UseForeColor = True
        Me.bntStart.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntStart.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntStart.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntStart.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntStart.AppearanceDisabled.Options.UseBackColor = True
        Me.bntStart.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntStart.AppearanceDisabled.Options.UseFont = True
        Me.bntStart.AppearanceDisabled.Options.UseForeColor = True
        Me.bntStart.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntStart.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntStart.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntStart.AppearanceHovered.Options.UseBackColor = True
        Me.bntStart.AppearanceHovered.Options.UseBorderColor = True
        Me.bntStart.AppearanceHovered.Options.UseForeColor = True
        Me.bntStart.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntStart.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntStart.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntStart.AppearancePressed.Options.UseBackColor = True
        Me.bntStart.AppearancePressed.Options.UseBorderColor = True
        Me.bntStart.AppearancePressed.Options.UseForeColor = True
        Me.bntStart.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.bntStart.Location = New System.Drawing.Point(16, 16)
        Me.bntStart.Name = "bntStart"
        Me.bntStart.Size = New System.Drawing.Size(123, 38)
        Me.bntStart.StyleController = Me.LayoutControl1
        Me.bntStart.TabIndex = 0
        Me.bntStart.Text = "Start Search"
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.EmptySpaceItem1, Me.LayoutControlItem2, Me.LayoutControlItem3, Me.LayoutControlItem4, Me.LayoutControlItem5, Me.LayoutControlItem6})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(955, 86)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.bntStart
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(129, 50)
        Me.LayoutControlItem1.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 50)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(929, 10)
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.btnStop
        Me.LayoutControlItem2.Location = New System.Drawing.Point(129, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(128, 50)
        Me.LayoutControlItem2.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.btnClear
        Me.LayoutControlItem3.Location = New System.Drawing.Point(257, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(129, 50)
        Me.LayoutControlItem3.TextVisible = False
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.BTN_SaveEmail
        Me.LayoutControlItem4.Location = New System.Drawing.Point(386, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(135, 50)
        Me.LayoutControlItem4.TextVisible = False
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.Control = Me.BTN_SaveLink
        Me.LayoutControlItem5.Location = New System.Drawing.Point(521, 0)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(112, 50)
        Me.LayoutControlItem5.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.AppearanceItemCaption.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.LayoutControlItem6.AppearanceItemCaption.Options.UseFont = True
        Me.LayoutControlItem6.Control = Me.txtWord
        Me.LayoutControlItem6.ImageOptions.Alignment = System.Drawing.ContentAlignment.MiddleRight
        Me.LayoutControlItem6.Location = New System.Drawing.Point(633, 0)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(296, 50)
        Me.LayoutControlItem6.Text = "Enter a word"
        Me.LayoutControlItem6.TextSize = New System.Drawing.Size(81, 19)
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.ProgressPanel1)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 511)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(972, 91)
        Me.Panel2.TabIndex = 1
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.AnimationToTextDistance = 10
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.Appearance.Options.UseFont = True
        Me.ProgressPanel1.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.0!, System.Drawing.FontStyle.Bold)
        Me.ProgressPanel1.AppearanceCaption.Options.UseFont = True
        Me.ProgressPanel1.AppearanceDescription.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel1.AppearanceDescription.Options.UseFont = True
        Me.ProgressPanel1.BarAnimationElementThickness = 5
        Me.ProgressPanel1.Caption = "Please Wait..."
        Me.ProgressPanel1.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
        Me.ProgressPanel1.Description = "0 out of 0 Found"
        Me.ProgressPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ProgressPanel1.Location = New System.Drawing.Point(0, 0)
        Me.ProgressPanel1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(972, 91)
        Me.ProgressPanel1.TabIndex = 396
        Me.ProgressPanel1.Text = "picWait"
        '
        'Panel3
        '
        Me.Panel3.Controls.Add(Me.LayoutControl2)
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel3.Location = New System.Drawing.Point(0, 68)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(972, 443)
        Me.Panel3.TabIndex = 2
        '
        'LayoutControl2
        '
        Me.LayoutControl2.Controls.Add(Me.GridControl1)
        Me.LayoutControl2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl2.Name = "LayoutControl2"
        Me.LayoutControl2.Root = Me.LayoutControlGroup1
        Me.LayoutControl2.Size = New System.Drawing.Size(972, 443)
        Me.LayoutControl2.TabIndex = 0
        Me.LayoutControl2.Text = "LayoutControl2"
        '
        'GridControl1
        '
        Me.GridControl1.Location = New System.Drawing.Point(16, 16)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(940, 411)
        Me.GridControl1.TabIndex = 4
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumnEmail, Me.GridColumnLink})
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'GridColumnEmail
        '
        Me.GridColumnEmail.Caption = "Mail Results"
        Me.GridColumnEmail.Name = "GridColumnEmail"
        Me.GridColumnEmail.Visible = True
        Me.GridColumnEmail.VisibleIndex = 0
        '
        'GridColumnLink
        '
        Me.GridColumnLink.Caption = "Link Results"
        Me.GridColumnLink.Name = "GridColumnLink"
        Me.GridColumnLink.Visible = True
        Me.GridColumnLink.VisibleIndex = 1
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem7})
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(972, 443)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.GridControl1
        Me.LayoutControlItem7.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(946, 417)
        Me.LayoutControlItem7.TextVisible = False
        '
        'MailCatcher
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.Panel3)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.Panel1)
        Me.IconOptions.ShowIcon = False
        Me.Name = "MailCatcher"
        Me.ShowInTaskbar = False
        Me.Text = "MailCatcher"
        Me.Panel1.ResumeLayout(False)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.txtWord.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel2.ResumeLayout(False)
        Me.Panel3.ResumeLayout(False)
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl2.ResumeLayout(False)
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel1 As Panel
    Friend WithEvents Panel2 As Panel
    Friend WithEvents Panel3 As Panel
    Friend WithEvents BTN_SaveLink As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BTN_SaveEmail As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnClear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnStop As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents bntStart As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents txtWord As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents LayoutControl2 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumnEmail As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumnLink As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
End Class
