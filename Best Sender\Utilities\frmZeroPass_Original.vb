Imports System.IO
Imports DevExpress.XtraEditors
Imports System.Text
Imports DevExpress.XtraBars
Imports DevExpress.XtraBars.Ribbon
Imports System.Drawing.Drawing2D
Imports System.Diagnostics
Imports System.Text.RegularExpressions
Imports System.Runtime.CompilerServices
Imports System.Threading.Tasks
Imports System.Threading

Public Class frmZeroPass
    Public Property RelatedRibbonPage As RibbonPage

    ' متغير للتحكم في عملية التوقف
    Private isCancelled As Boolean = False

    ' متغيرات للتحكم في العمليات غير المتزامنة
    Private emailExtractionCancellationToken As Threading.CancellationTokenSource
    Private fileLoadingCancellationToken As Threading.CancellationTokenSource

    ' لوحة التقدم
    Private pnlWait As DevExpress.XtraWaitForm.ProgressPanel

    Private Sub frmZeroPass_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تهيئة التسميات
        Label1.Text = ""
        Label2.Text = ""
        Label3.Text = ""

        ' تعطيل الأزرار في البداية
        frmMain.BarButtonItem63.Enabled = True  ' رفع القائمة
        frmMain.BarButtonItem65.Enabled = False ' بدء العملية
        frmMain.BarButtonItem66.Enabled = False ' حفظ البريد
        frmMain.BarButtonItem67.Enabled = False ' حذف الجميع
        frmMain.BarButtonItem36.Enabled = False ' إيقاف العملية

        ' إضافة معالج حدث لتغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmZeroPass_Resize

        ' تطبيق تصميم بينانس على عناصر التحكم أولاً
        ApplyBinanceStyle()

        ' ترتيب العناصر بشكل بسيط وسريع
        ArrangeControlsSimple()

        ' تحسين أداء النموذج
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint Or ControlStyles.UserPaint Or ControlStyles.DoubleBuffer, True)
        Me.UpdateStyles()

        ' تعيين الحد الأدنى لحجم النموذج
        Me.MinimumSize = New Size(400, 300)

        ' تمكين الأزرار بعد التهيئة الكاملة
        frmMain.BarButtonItem63.Enabled = True  ' رفع القائمة
        frmMain.BarButtonItem65.Enabled = False ' بدء العملية
        frmMain.BarButtonItem66.Enabled = False ' حفظ البريد
        frmMain.BarButtonItem67.Enabled = False ' حذف الجميع
        frmMain.BarButtonItem36.Enabled = False ' إيقاف العملية
    End Sub

    ' متغير للتحكم في التأخير
    Private autoExtractionTimer As System.Windows.Forms.Timer

    ''' <summary>
    ''' رفع القائمة - BarButtonItem63
    ''' </summary>
    Public Sub Uploade_List()
        Try
            ' تعطيل الأزرار أثناء التحميل
            frmMain.BarButtonItem63.Enabled = False
            frmMain.BarButtonItem65.Enabled = False
            frmMain.BarButtonItem66.Enabled = False
            frmMain.BarButtonItem67.Enabled = False
            frmMain.BarButtonItem36.Enabled = False

            ' مسح البيانات السابقة
            MemoEdit2.Clear()
            MemoEdit1.Clear()
            Label1.Text = ""
            Label2.Text = ""
            Label3.Text = ""

            ' إنشاء حوار اختيار الملف
            Dim openFileDialog1 As New OpenFileDialog()
            openFileDialog1.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            openFileDialog1.Title = "Select Email List File"
            openFileDialog1.Filter = "Text Files|*.txt|All Files|*.*"
            openFileDialog1.Multiselect = False

            If openFileDialog1.ShowDialog() = DialogResult.OK Then
                Dim filePath As String = openFileDialog1.FileName

                ' عرض رسالة تحميل
                Label1.Text = "Loading file..."
                Label2.Text = "Please wait..."
                Label3.Text = ""

                ' إنشاء CancellationToken جديد لتحميل الملف
                fileLoadingCancellationToken = New Threading.CancellationTokenSource()

                ' تحميل الملف في خيط منفصل لتجنب التجميد
                Task.Run(Sub() LoadFileAsyncImproved(filePath, fileLoadingCancellationToken.Token))
            Else
                ' إعادة تمكين الأزرار إذا تم إلغاء التحديد
                frmMain.BarButtonItem63.Enabled = True
            End If

        Catch ex As Exception
            ' في حالة الخطأ، إعادة تمكين الأزرار
            frmMain.BarButtonItem63.Enabled = True
            XtraMessageBox.Show($"Error selecting file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تحميل الملف بشكل محسن لتجنب التجميد
    ''' </summary>
    Private Sub LoadFileAsyncImproved(filePath As String, cancellationToken As Threading.CancellationToken)
        Try
            ' الحصول على معلومات الملف
            Dim fileInfo As New FileInfo(filePath)
            Dim fileSize As Long = fileInfo.Length

            ' إظهار رسالة تحميل
            Me.Invoke(Sub()
                          Label2.Text = "Loading file..."
                      End Sub)

            ' مسح المحتوى السابق
            Me.Invoke(Sub()
                          MemoEdit1.Text = ""
                      End Sub)

            ' للملفات الصغيرة (أقل من 5MB) - قراءة مباشرة
            If fileSize < 5 * 1024 * 1024 Then
                ' قراءة سريعة للملفات الصغيرة
                Dim content As String = File.ReadAllText(filePath, System.Text.Encoding.UTF8)
                
                Me.Invoke(Sub()
                              MemoEdit1.Text = content
                              UpdateEmailCountAsync()
                              frmMain.BarButtonItem63.Enabled = True
                              frmMain.BarButtonItem65.Enabled = True
                              frmMain.BarButtonItem66.Enabled = False
                              frmMain.BarButtonItem67.Enabled = True
                              Label2.Text = "File loaded successfully"

                              ' بدء استخراج البريد الإلكتروني تلقائياً بعد تأخير قصير
                              Task.Run(Sub()
                                           Threading.Thread.Sleep(500)
                                           Me.Invoke(Sub()
                                                         StartAutoEmailExtraction()
                                                     End Sub)
                                       End Sub)
                          End Sub)
            Else
                ' للملفات الكبيرة - قراءة تدريجية
                LoadLargeFileAsync(filePath, cancellationToken)
            End If

        Catch ex As Exception
            ' عرض رسالة خطأ في الخيط الرئيسي
            Me.Invoke(Sub()
                          XtraMessageBox.Show($"Error loading file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                          Label1.Text = ""
                          Label2.Text = "Failed to load file"
                          Label3.Text = ""
                          frmMain.BarButtonItem63.Enabled = True
                      End Sub)
        End Try
    End Sub

    ''' <summary>
    ''' تحميل الملفات الكبيرة بشكل تدريجي
    ''' </summary>
    Private Sub LoadLargeFileAsync(filePath As String, cancellationToken As Threading.CancellationToken)
        Try
            Dim totalRead As Long = 0
            Dim fileInfo As New FileInfo(filePath)
            Dim fileSize As Long = fileInfo.Length
            Dim chunkSize As Integer = 64 * 1024 ' 64KB per chunk
            Dim buffer(chunkSize - 1) As Char

            Using reader As New StreamReader(filePath, System.Text.Encoding.UTF8, True, chunkSize)
                While Not reader.EndOfStream
                    ' التحقق من طلب الإلغاء
                    If cancellationToken.IsCancellationRequested Then
                        Debug.WriteLine("File loading cancelled")
                        Return
                    End If

                    Dim charsRead As Integer = reader.ReadBlock(buffer, 0, chunkSize)
                    totalRead += charsRead

                    ' تحديث النص في الخيط الرئيسي
                    Dim chunk As String = New String(buffer, 0, charsRead)
                    Me.Invoke(Sub()
                                  MemoEdit1.AppendText(chunk)

                                  ' تحديث التقدم
                                  Dim progress As Integer = CInt((totalRead / fileSize) * 100)
                                  Label2.Text = $"Loading... {progress}% ({totalRead / 1024:N0} KB)"

                                  ' تحديث واجهة المستخدم
                                  Application.DoEvents()
                              End Sub)

                    ' تأخير قصير لتجنب التجميد
                    Threading.Thread.Sleep(5)
                End While
            End Using

            ' تحديث واجهة المستخدم النهائية وبدء استخراج البريد فوراً
            Me.Invoke(Sub()
                          UpdateEmailCountAsync()
                          frmMain.BarButtonItem63.Enabled = True
                          frmMain.BarButtonItem65.Enabled = True
                          frmMain.BarButtonItem66.Enabled = False
                          frmMain.BarButtonItem67.Enabled = True
                          Label2.Text = "File loaded successfully"

                          ' بدء استخراج البريد الإلكتروني تلقائياً بعد تأخير قصير
                          Task.Run(Sub()
                                       Threading.Thread.Sleep(500)
                                       Me.Invoke(Sub()
                                                     StartAutoEmailExtraction()
                                                 End Sub)
                                   End Sub)
                      End Sub)

        Catch ex As Exception
            Me.Invoke(Sub()
                          XtraMessageBox.Show($"Error loading large file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                          Label2.Text = "Failed to load file"
                          frmMain.BarButtonItem63.Enabled = True
                      End Sub)
        End Try
    End Sub

    ''' <summary>
    ''' بدء العملية - BarButtonItem65
    ''' </summary>
    Public Sub Start_Remove_List()
        Try
            ' التحقق من وجود بيانات
            If String.IsNullOrWhiteSpace(MemoEdit1.Text) Then
                XtraMessageBox.Show("Select the List Email...!!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                Exit Sub
            End If

            ' التحقق من أن BackgroundWorker1 غير مشغول
            If BackgroundWorker1.IsBusy Then
                XtraMessageBox.Show("A process is already running. Please wait for it to complete or click Stop to cancel it.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' تعيين أن العملية يدوية
            isAutoProcessing = False

            ' إعادة تعيين متغير التوقف
            isCancelled = False

            ' تهيئة واجهة المستخدم
            PrepareUIForProcessing()

            ' طباعة معلومات للتصحيح
            Debug.WriteLine("Starting BackgroundWorker1 (Manual)")

            ' تشغيل BackgroundWorker1
            BackgroundWorker1.RunWorkerAsync(MemoEdit1.Lines)
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error starting process: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)

            ' إخفاء لوحة التقدم
            HideProgressPanel()

            ' عرض رسالة خطأ
            XtraMessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' حفظ البريد - BarButtonItem66
    ''' </summary>
    Public Sub Save_List()
        Try
            If String.IsNullOrWhiteSpace(MemoEdit2.Text) Then
                XtraMessageBox.Show("You do not have a list of emails. Please select or enter a list before saving.", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                Exit Sub
            End If

            ' Create the folder on the Desktop
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim folderPath As String = Path.Combine(desktopPath, "ZeroPass")
            If Not Directory.Exists(folderPath) Then
                Directory.CreateDirectory(folderPath)
            End If

            ' Full file path
            Dim filePath As String = Path.Combine(folderPath, "ZeroPass.txt")

            ' Save the file
            File.WriteAllText(filePath, MemoEdit2.Text)

            ' Notify the user
            XtraMessageBox.Show("File saved successfully in 'ZeroPass' folder on your Desktop.", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)

            ' Open the folder
            Process.Start("explorer.exe", folderPath)

        Catch ex As Exception
            XtraMessageBox.Show($"Error saving file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' حذف الجميع - BarButtonItem67
    ''' </summary>
    Public Sub Remove_Password()
        Try
            If String.IsNullOrWhiteSpace(MemoEdit1.Text) AndAlso
               String.IsNullOrWhiteSpace(MemoEdit2.Text) AndAlso
               String.IsNullOrWhiteSpace(Label1.Text) AndAlso
               String.IsNullOrWhiteSpace(Label2.Text) AndAlso
               String.IsNullOrWhiteSpace(Label3.Text) Then
                XtraMessageBox.Show("There is no data to delete.", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ElseIf XtraMessageBox.Show("Do you want to delete the old data?", "Confirm Deletion", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                MemoEdit2.Clear()
                MemoEdit1.Clear()
                Label1.Text = ""
                Label2.Text = ""
                Label3.Text = ""
                frmMain.BarButtonItem65.Enabled = False
                frmMain.BarButtonItem66.Enabled = False
            End If
        Catch ex As Exception
            XtraMessageBox.Show($"Error clearing data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' إيقاف العملية - BarButtonItem36
    ''' </summary>
    Public Sub Stop_Process()
        Try
            ' تعيين متغير الإلغاء
            isCancelled = True

            ' إلغاء BackgroundWorker إذا كان يعمل
            If BackgroundWorker1 IsNot Nothing AndAlso BackgroundWorker1.IsBusy Then
                BackgroundWorker1.CancelAsync()
            End If

            ' إلغاء عمليات التحميل
            If fileLoadingCancellationToken IsNot Nothing Then
                fileLoadingCancellationToken.Cancel()
            End If

            ' إلغاء عمليات الاستخراج
            If emailExtractionCancellationToken IsNot Nothing Then
                emailExtractionCancellationToken.Cancel()
            End If

            ' إخفاء لوحة التقدم
            HideProgressPanel()

            ' تحديث واجهة المستخدم
            Label2.Text = "Process stopped by user"
            frmMain.BarButtonItem63.Enabled = True
            frmMain.BarButtonItem65.Enabled = True
            frmMain.BarButtonItem66.Enabled = True
            frmMain.BarButtonItem67.Enabled = True
            frmMain.BarButtonItem36.Enabled = False

            Debug.WriteLine("Process stopped by user")

        Catch ex As Exception
            Debug.WriteLine($"Error stopping process: {ex.Message}")
            XtraMessageBox.Show($"Error stopping process: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' متغير لتتبع ما إذا كانت العملية تلقائية أم يدوية
    Private isAutoProcessing As Boolean = False

    ''' <summary>
    ''' بدء استخراج البريد الإلكتروني تلقائياً بعد تحميل الملف
    ''' </summary>
    Private Sub StartAutoEmailExtraction()
        Try
            ' التحقق من وجود بيانات
            If String.IsNullOrWhiteSpace(MemoEdit1.Text) Then
                Debug.WriteLine("No data found for auto extraction")
                Return
            End If

            ' التحقق من أن BackgroundWorker1 غير مشغول
            If BackgroundWorker1.IsBusy Then
                Debug.WriteLine("BackgroundWorker1 is busy, skipping auto extraction")
                Return
            End If

            ' تعيين أن العملية تلقائية
            isAutoProcessing = True

            ' عرض رسالة تلقائية
            Label2.Text = "Auto-extracting emails..."
            Application.DoEvents()

            ' إعادة تعيين متغير التوقف
            isCancelled = False

            ' تهيئة واجهة المستخدم للمعالجة التلقائية
            PrepareUIForAutoProcessing()

            ' طباعة معلومات للتصحيح
            Debug.WriteLine("Starting auto email extraction")

            ' تشغيل BackgroundWorker1
            BackgroundWorker1.RunWorkerAsync(MemoEdit1.Lines)
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error in auto extraction: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)

            ' إخفاء لوحة التقدم
            HideProgressPanel()

            ' عرض رسالة خطأ
            Label2.Text = "Auto extraction failed"
        End Try
    End Sub

    ''' <summary>
    ''' تهيئة واجهة المستخدم للمعالجة التلقائية
    ''' </summary>
    Private Sub PrepareUIForAutoProcessing()
        Try
            ' إظهار لوحة التقدم مع رسالة مخصصة
            ShowProgressPanelForAuto()

            ' تهيئة MemoEdit2
            MemoEdit2.Text = ""
            MemoEdit2.BackColor = Color.FromArgb(26, 26, 29)
            MemoEdit2.ForeColor = Color.FromArgb(34, 203, 121)

            ' تعطيل الأزرار مؤقتاً
            frmMain.BarButtonItem63.Enabled = False
            frmMain.BarButtonItem65.Enabled = False
            frmMain.BarButtonItem66.Enabled = False
            frmMain.BarButtonItem67.Enabled = False
            frmMain.BarButtonItem36.Enabled = True ' تمكين زر الإيقاف

        Catch ex As Exception
            Debug.WriteLine($"Error preparing UI for auto processing: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تهيئة واجهة المستخدم للمعالجة (يدوي)
    ''' </summary>
    Private Sub PrepareUIForProcessing()
        Try
            ' إظهار لوحة التقدم
            ShowProgressPanel()

            ' تهيئة MemoEdit2
            MemoEdit2.Text = ""
            MemoEdit2.BackColor = Color.FromArgb(26, 26, 29)
            MemoEdit2.ForeColor = Color.FromArgb(34, 203, 121)

            ' تعطيل الأزرار
            frmMain.BarButtonItem63.Enabled = False
            frmMain.BarButtonItem65.Enabled = False
            frmMain.BarButtonItem66.Enabled = False
            frmMain.BarButtonItem67.Enabled = False
            frmMain.BarButtonItem36.Enabled = True ' تمكين زر الإيقاف

        Catch ex As Exception
            Debug.WriteLine($"Error preparing UI: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
            Throw ' إعادة رمي الاستثناء للتعامل معه في الدالة الأصلية
        End Try
    End Sub

    ' باقي الكود يبقى كما هو...
    ' (سأضيف باقي الدوال في الرد التالي)

End Class
