﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.ReaderWriter</name>
  </assembly>
  <members>
    <member name="T:System.Xml.ConformanceLevel">
      <summary>Spécifie l'ampleur des contrôles d'entrée ou de sortie que les objets <see cref="T:System.Xml.XmlReader" /> et <see cref="T:System.Xml.XmlWriter" /> effectuent.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Auto">
      <summary>L'objet <see cref="T:System.Xml.XmlReader" /> ou <see cref="T:System.Xml.XmlWriter" /> détecte automatiquement si une vérification au niveau du document ou du fragment doit être effectuée et procède au contrôle approprié.Si vous encapsulez un autre objet <see cref="T:System.Xml.XmlReader" /> ou <see cref="T:System.Xml.XmlWriter" />, l'objet externe n'effectue aucune vérification de conformité supplémentaire.Le contrôle de conformité doit être fait par l'objet sous-jacent.Pour plus de détails sur la détermination du niveau de conformité, consultez les propriétés <see cref="P:System.Xml.XmlReaderSettings.ConformanceLevel" /> et <see cref="P:System.Xml.XmlWriterSettings.ConformanceLevel" />.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Document">
      <summary>Les données XML respectent les règles définissant un document XML 1.0 bien formé, tel que défini par le W3C.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Fragment">
      <summary>Les données XML constituent un fragment XML bien formé, tel que défini par le W3C.</summary>
    </member>
    <member name="T:System.Xml.DtdProcessing">
      <summary>Spécifie les options de traitement des DTD.L'énumération <see cref="T:System.Xml.DtdProcessing" /> est utilisée par la classe <see cref="T:System.Xml.XmlReaderSettings" />.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Ignore">
      <summary>Entraîne le fait que l'élément DOCTYPE est ignoré.Aucun traitement de DTD ne se poursuit.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Prohibit">
      <summary>Spécifie que lorsqu'une DTD est rencontrée, un <see cref="T:System.Xml.XmlException" /> est levé avec un message signalant que les DTD sont interdites.Il s'agit du comportement par défaut.</summary>
    </member>
    <member name="T:System.Xml.IXmlLineInfo">
      <summary>Fournit une interface pour permettre à une classe de retourner des informations de ligne et de position.</summary>
    </member>
    <member name="M:System.Xml.IXmlLineInfo.HasLineInfo">
      <summary>Obtient une valeur indiquant si la classe peut retourner des informations de ligne.</summary>
      <returns>true si <see cref="P:System.Xml.IXmlLineInfo.LineNumber" /> et <see cref="P:System.Xml.IXmlLineInfo.LinePosition" /> peuvent être fournis ; sinon, false.</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LineNumber">
      <summary>Obtient le numéro de la ligne active.</summary>
      <returns>Le numéro de la ligne active ou 0 si aucune information de ligne n'est disponible (par exemple, <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> retourne false).</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LinePosition">
      <summary>Obtient la position de la ligne active.</summary>
      <returns>La position de la ligne active ou 0 si aucune information de ligne n'est disponible (par exemple, <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> retourne false).</returns>
    </member>
    <member name="T:System.Xml.IXmlNamespaceResolver">
      <summary>Fournit un accès en lecture seule à un jeu de mappages de préfixes et d'espaces de noms.</summary>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Obtient une collection de mappages de préfixes sur des espaces de noms définis qui sont actuellement dans la portée.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> qui contient les espaces de noms actuellement dans la portée.</returns>
      <param name="scope">Valeur de <see cref="T:System.Xml.XmlNamespaceScope" /> qui spécifie le type de nœuds d'espace de noms à retourner.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupNamespace(System.String)">
      <summary>Obtient l'URI de l'espace de noms mappé sur le préfixe spécifié.</summary>
      <returns>L'URI de l'espace de noms qui est mappé au préfixe ; null si le préfixe n'est pas mappé à un URI d'espace de noms.</returns>
      <param name="prefix">Préfixe dont vous recherchez l'URI de l'espace de noms.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupPrefix(System.String)">
      <summary>Obtient le préfixe qui est mappé sur l'URI de l'espace de noms spécifié.</summary>
      <returns>Le préfixe qui est mappé sur l'URI de l'espace de noms ; null si l'URI de l'espace de noms n'est pas mappé sur un préfixe.</returns>
      <param name="namespaceName">URI de l'espace de noms dont vous recherchez le préfixe.</param>
    </member>
    <member name="T:System.Xml.NamespaceHandling">
      <summary>Spécifie si vous souhaitez supprimer les déclarations d'espace de noms en double dans le <see cref="T:System.Xml.XmlWriter" />. </summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.Default">
      <summary>Spécifie que les déclarations d'espace de noms en double ne seront pas supprimées.</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.OmitDuplicates">
      <summary>Spécifie que les déclarations d'espace de noms en double seront supprimées.Pour l'espace de noms en double à supprimer, le préfixe et l'espace de noms doivent correspondre.</summary>
    </member>
    <member name="T:System.Xml.NameTable">
      <summary>Implémente un <see cref="T:System.Xml.XmlNameTable" /> à thread unique.</summary>
    </member>
    <member name="M:System.Xml.NameTable.#ctor">
      <summary>Initialise une nouvelle instance de la classe NameTable.</summary>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>Atomise la chaîne spécifiée et l'ajoute à NameTable.</summary>
      <returns>La chaîne atomisée ou, le cas échéant, la chaîne existante dans NameTable.Si <paramref name="len" /> est égal à zéro, String.Empty est retourné.</returns>
      <param name="key">Tableau de caractères contenant les chaînes à ajouter. </param>
      <param name="start">Index de base zéro dans le tableau spécifiant le premier caractère de la chaîne. </param>
      <param name="len">Nombre de caractères dans la chaîne. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />ou <paramref name="start" /> &gt;= <paramref name="key" />.Length ou <paramref name="len" /> &gt;= <paramref name="key" />.Length Les conditions ci-dessus n'entraînent pas la levée d'une exception si <paramref name="len" />=0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.String)">
      <summary>Atomise la chaîne spécifiée et l'ajoute à NameTable.</summary>
      <returns>La chaîne atomisée ou, le cas échéant, la chaîne existante dans le NameTable.</returns>
      <param name="key">Chaîne à ajouter. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> a la valeur null. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>Obtient la chaîne atomisée contenant les mêmes caractères que la plage de caractères spécifiée dans le tableau donné.</summary>
      <returns>Chaîne atomisée ou null si la chaîne n'a pas encore été atomisée.Si <paramref name="len" /> est égal à zéro, String.Empty est retourné.</returns>
      <param name="key">Tableau de caractères contenant le nom à rechercher. </param>
      <param name="start">Index de base zéro dans le tableau spécifiant le premier caractère du nom. </param>
      <param name="len">Nombre de caractères dans le nom. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />ou <paramref name="start" /> &gt;= <paramref name="key" />.Length ou <paramref name="len" /> &gt;= <paramref name="key" />.Length Les conditions ci-dessus n'entraînent pas la levée d'une exception si <paramref name="len" />=0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.String)">
      <summary>Obtient la chaîne atomisée de valeur spécifiée.</summary>
      <returns>L'objet de chaîne atomisée ou null si la chaîne n'a pas encore été atomisée.</returns>
      <param name="value">Nom à rechercher. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> a la valeur null. </exception>
    </member>
    <member name="T:System.Xml.NewLineHandling">
      <summary>Spécifie comment gérer les sauts de ligne.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Entitize">
      <summary>Les caractères de nouvelle ligne sont définis comme "entitize".Ce paramètre conserve tous les caractères lorsque la sortie est lue par un <see cref="T:System.Xml.XmlReader" /> normalisant.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.None">
      <summary>Les caractères de nouvelle ligne restent inchangés.La sortie est identique à l'entrée.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Replace">
      <summary>Les caractères de nouvelle ligne sont remplacés pour correspondre au caractère spécifié dans la propriété <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" />.</summary>
    </member>
    <member name="T:System.Xml.ReadState">
      <summary>Spécifie l'état du lecteur.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Closed">
      <summary>La méthode <see cref="M:System.Xml.XmlReader.Close" /> a été appelée.</summary>
    </member>
    <member name="F:System.Xml.ReadState.EndOfFile">
      <summary>La fin du fichier a été atteinte avec succès.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Error">
      <summary>Une erreur s'est produite et empêche l'opération de lecture de se poursuivre.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Initial">
      <summary>La méthode Read n'a pas été appelée.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Interactive">
      <summary>La méthode Read a été appelée.Des méthodes supplémentaires peuvent être appelées sur le lecteur.</summary>
    </member>
    <member name="T:System.Xml.WriteState">
      <summary>Spécifie l'état de <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Attribute">
      <summary>Indique qu'une valeur d'attribut est en cours d'écriture.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Closed">
      <summary>Indique que la méthode <see cref="M:System.Xml.XmlWriter.Close" /> a été appelée.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Content">
      <summary>Indique que le contenu d'élément est en cours d'écriture.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Element">
      <summary>Indique qu'une balise de début d'élément est en cours d'écriture.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Error">
      <summary>Une exception a été levée et a laissé le <see cref="T:System.Xml.XmlWriter" /> dans un état non valide.Vous pouvez appeler la méthode <see cref="M:System.Xml.XmlWriter.Close" /> pour mettre le <see cref="T:System.Xml.XmlWriter" /> à l'état <see cref="F:System.Xml.WriteState.Closed" />.Toute autre méthode <see cref="T:System.Xml.XmlWriter" /> appelle les résultats dans un <see cref="T:System.InvalidOperationException" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Prolog">
      <summary>Indique que le prologue est en cours d'écriture.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Start">
      <summary>Indique qu'une méthode Write n'a pas encore été appelée.</summary>
    </member>
    <member name="T:System.Xml.XmlConvert">
      <summary>Encode et décode les noms XML, et fournit des méthodes pour la conversion entre les types Common Language Runtime et les types XSD (XML Schema Definition).Lors de la conversion de types de données, les valeurs retournées sont indépendantes des paramètres régionaux.</summary>
    </member>
    <member name="M:System.Xml.XmlConvert.DecodeName(System.String)">
      <summary>Décode un nom.Cette méthode fait le contraire des méthodes <see cref="M:System.Xml.XmlConvert.EncodeName(System.String)" /> et <see cref="M:System.Xml.XmlConvert.EncodeLocalName(System.String)" />.</summary>
      <returns>Nom décodé.</returns>
      <param name="name">Nom à transformer. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeLocalName(System.String)">
      <summary>Convertit le nom en un nom local XML valide.</summary>
      <returns>Nom encodé.</returns>
      <param name="name">Nom à encoder. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeName(System.String)">
      <summary>Convertit le nom en un nom XML valide.</summary>
      <returns>Retourne le nom avec les caractères non valides remplacés par une chaîne d'échappement.</returns>
      <param name="name">Nom à traduire. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeNmToken(System.String)">
      <summary>Vérifie que le nom est valide selon la spécification XML.</summary>
      <returns>Nom encodé.</returns>
      <param name="name">Nom à encoder. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToBoolean(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.Boolean" />.</summary>
      <returns>Valeur Boolean, c'est-à-dire true ou false.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> does not represent a Boolean value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToByte(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.Byte" />.</summary>
      <returns>Équivalent Byte de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToChar(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.Char" />.</summary>
      <returns>Char représentant le caractère unique.</returns>
      <param name="s">Chaîne contenant un seul caractère à convertir. </param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="s" /> parameter is null. </exception>
      <exception cref="T:System.FormatException">The <paramref name="s" /> parameter contains more than one character. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTime(System.String,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un élément <see cref="T:System.DateTime" /> en utilisant le mode <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> spécifié.</summary>
      <returns>Équivalent <see cref="T:System.DateTime" /> de la chaîne <see cref="T:System.String" />.</returns>
      <param name="s">Valeur de la chaîne <see cref="T:System.String" /> à convertir.</param>
      <param name="dateTimeOption">Une des valeurs de <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> qui spécifient si la date doit être convertie en heure locale ou conservée en temps UTC, s'il s'agit d'une date UTC.</param>
      <exception cref="T:System.NullReferenceException">
        <paramref name="s" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="dateTimeOption" /> value is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is an empty string or is not in a valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> fournie en un équivalent <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Équivalent <see cref="T:System.DateTimeOffset" /> de la chaîne fournie.</returns>
      <param name="s">Chaîne à convertir.Remarque   La chaîne doit être conforme à un sous-ensemble de la recommandation du W3C sur le type XML dateTime.Pour plus d'informations, consultez http://www.w3.org/TR/xmlschema-2/#dateTime.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The argument passed to this method is outside the range of allowable values.For information about allowable values, see <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.FormatException">The argument passed to this method does not conform to a subset of the W3C Recommendations for the XML dateTime type.For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> fournie en un équivalent <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Équivalent <see cref="T:System.DateTimeOffset" /> de la chaîne fournie.</returns>
      <param name="s">Chaîne à convertir.</param>
      <param name="format">Format à partir duquel <paramref name="s" /> est convertie.Le paramètre de format peut correspondre à un sous-ensemble de recommandations du W3C pour le type XML dateTime.(Pour plus d'informations consultez http://www.w3.org/TR/xmlschema-2/#dateTime.) La chaîne <paramref name="s" /> est validée par rapport à ce format.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> or <paramref name="format" /> is an empty string or is not in the specified format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String[])">
      <summary>Convertit la chaîne <see cref="T:System.String" /> fournie en un équivalent <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Équivalent <see cref="T:System.DateTimeOffset" /> de la chaîne fournie.</returns>
      <param name="s">Chaîne à convertir.</param>
      <param name="formats">Tableau de formats à partir duquel <paramref name="s" /> peut être convertie.Chaque format figurant dans <paramref name="formats" /> peut correspondre à un des sous-ensembles de la recommandation W3C pour le type XML dateTime.(Pour plus d'informations consultez http://www.w3.org/TR/xmlschema-2/#dateTime.) La chaîne <paramref name="s" /> est validée par rapport à un de ces formats.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDecimal(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.Decimal" />.</summary>
      <returns>Équivalent Decimal de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDouble(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.Double" />.</summary>
      <returns>Équivalent Double de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToGuid(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.Guid" />.</summary>
      <returns>Équivalent Guid de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt16(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.Int16" />.</summary>
      <returns>Équivalent Int16 de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt32(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.Int32" />.</summary>
      <returns>Équivalent Int32 de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt64(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.Int64" />.</summary>
      <returns>Équivalent Int64 de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSByte(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.SByte" />.</summary>
      <returns>Équivalent SByte de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSingle(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.Single" />.</summary>
      <returns>Équivalent Single de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Boolean)">
      <summary>Convertit le <see cref="T:System.Boolean" /> en <see cref="T:System.String" />.</summary>
      <returns>Une représentation sous forme de chaîne de l'élément Boolean, c'est-à-dire "true" ou "false".</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Byte)">
      <summary>Convertit le <see cref="T:System.Byte" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de Byte.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Char)">
      <summary>Convertit le <see cref="T:System.Char" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de Char.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTime,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Convertit l'élément <see cref="T:System.DateTime" /> en une chaîne <see cref="T:System.String" /> en utilisant le mode <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> spécifié.</summary>
      <returns>Équivalent <see cref="T:System.String" /> de l'élément <see cref="T:System.DateTime" />.</returns>
      <param name="value">Valeur <see cref="T:System.DateTime" /> à convertir.</param>
      <param name="dateTimeOption">Une des valeurs de <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> qui spécifient comment traiter la valeur <see cref="T:System.DateTime" />.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="dateTimeOption" /> value is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> or <paramref name="dateTimeOption" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset)">
      <summary>Convertit l'élément <see cref="T:System.DateTimeOffset" /> fourni en une chaîne <see cref="T:System.String" />.</summary>
      <returns>Représentation <see cref="T:System.String" /> de l'élément <see cref="T:System.DateTimeOffset" /> fourni.</returns>
      <param name="value">
        <see cref="T:System.DateTimeOffset" /> à convertir.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset,System.String)">
      <summary>Convertit l'élément <see cref="T:System.DateTimeOffset" /> fourni en une chaîne <see cref="T:System.String" /> dans le format spécifié.</summary>
      <returns>Représentation <see cref="T:System.String" /> dans le format spécifié de l'élément <see cref="T:System.DateTimeOffset" />.</returns>
      <param name="value">
        <see cref="T:System.DateTimeOffset" /> à convertir.</param>
      <param name="format">Format vers lequel <paramref name="s" /> est convertie.Le paramètre de format peut correspondre à un sous-ensemble de recommandations du W3C pour le type XML dateTime.(Pour plus d'informations consultez http://www.w3.org/TR/xmlschema-2/#dateTime.)</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Decimal)">
      <summary>Convertit le <see cref="T:System.Decimal" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de Decimal.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Double)">
      <summary>Convertit le <see cref="T:System.Double" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de Double.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Guid)">
      <summary>Convertit le <see cref="T:System.Guid" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de Guid.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int16)">
      <summary>Convertit le <see cref="T:System.Int16" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de Int16.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int32)">
      <summary>Convertit le <see cref="T:System.Int32" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de Int32.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int64)">
      <summary>Convertit le <see cref="T:System.Int64" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de Int64.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.SByte)">
      <summary>Convertit le <see cref="T:System.SByte" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de SByte.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Single)">
      <summary>Convertit le <see cref="T:System.Single" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de Single.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.TimeSpan)">
      <summary>Convertit le <see cref="T:System.TimeSpan" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de TimeSpan.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt16)">
      <summary>Convertit le <see cref="T:System.UInt16" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de UInt16.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt32)">
      <summary>Convertit le <see cref="T:System.UInt32" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de UInt32.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt64)">
      <summary>Convertit le <see cref="T:System.UInt64" /> en <see cref="T:System.String" />.</summary>
      <returns>Représentation sous forme de chaîne de UInt64.</returns>
      <param name="value">Valeur à convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToTimeSpan(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.TimeSpan" />.</summary>
      <returns>Équivalent TimeSpan de la chaîne.</returns>
      <param name="s">Chaîne à convertir.Le format de chaîne doit être conforme à la recommandation W3C intitulée Schema Part 2 : Datatypes pour les durées.</param>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in correct format to represent a TimeSpan value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt16(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.UInt16" />.</summary>
      <returns>Équivalent UInt16 de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt32(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.UInt32" />.</summary>
      <returns>Équivalent UInt32 de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt64(System.String)">
      <summary>Convertit la chaîne <see cref="T:System.String" /> en un équivalent <see cref="T:System.UInt64" />.</summary>
      <returns>Équivalent UInt64 de la chaîne.</returns>
      <param name="s">Chaîne à convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyName(System.String)">
      <summary>Vérifie que le nom est un nom valide selon la recommandation W3C XML (Extended Markup Language).</summary>
      <returns>Le nom, s'il s'agit d'un nom XML valide.</returns>
      <param name="name">Nom à vérifier. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid XML name. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNCName(System.String)">
      <summary>Vérifie que le nom est un NCName valide selon la recommandation W3C XML (Extended Markup Language).Un élément NCName est un nom qui ne peut pas contenir un signe deux-points.</summary>
      <returns>Le nom, s'il s'agit d'un NCName valide.</returns>
      <param name="name">Nom à vérifier. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid non-colon name. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNMTOKEN(System.String)">
      <summary>Vérifie que la chaîne est un NMTOKEN valide selon la recommandation du W3C intitulée XML Schema Part2 : Datatypes</summary>
      <returns>Jeton de nom, s'il s'agit d'un NMTOKEN valide.</returns>
      <param name="name">Chaîne à vérifier.</param>
      <exception cref="T:System.Xml.XmlException">The string is not a valid name token.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyPublicId(System.String)">
      <summary>Retourne l'instance de chaîne passée si tous les caractères de l'argument de chaîne sont des caractères d'ID publics valides.</summary>
      <returns>Retourne la chaîne passée si tous les caractères de l'argument sont des caractères d'ID publics valides.</returns>
      <param name="publicId">
        <see cref="T:System.String" /> qui contient l'ID à valider.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyWhitespace(System.String)">
      <summary>Retourne l'instance de chaîne passée si tous les caractères de l'argument de chaîne sont des caractères d'espace valides. </summary>
      <returns>Retourne l'instance de chaîne passée si tous les caractères de l'argument de chaîne sont des caractères d'espace valides, sinon null.</returns>
      <param name="content">
        <see cref="T:System.String" /> à vérifier.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyXmlChars(System.String)">
      <summary>Retourne les caractères de la chaîne passée si tous les caractères et caractères de la paire de substitution de l'argument de chaîne sont des caractères XML valides, sinon une exception XmlException est levée avec des informations relatives au premier caractère non valide rencontré. </summary>
      <returns>Retourne les caractères de la chaîne passée si tous les caractères et les caractères de la paire de substitution de l'argument de chaîne sont des caractères XML valides, sinon une exception XmlException est levée avec des informations sur le premier caractère non valide rencontré.</returns>
      <param name="content">Chaîne <see cref="T:System.String" /> qui contient les caractères à vérifier.</param>
    </member>
    <member name="T:System.Xml.XmlDateTimeSerializationMode">
      <summary>Spécifie la façon de traiter la valeur d'heure lors de la conversion entre chaîne et <see cref="T:System.DateTime" />.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Local">
      <summary>Traiter en tant qu'heure locale.Si l'objet <see cref="T:System.DateTime" /> représente une heure UTC (Universal Time Coordinates), il est converti en heure locale.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.RoundtripKind">
      <summary>Les informations relatives au fuseau horaire doivent être conservées lors de la conversion.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Unspecified">
      <summary>Traiter en tant qu'heure locale si un <see cref="T:System.DateTime" /> est converti en chaîne.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Utc">
      <summary>Traiter en tant qu'heure UTC.Si l'objet <see cref="T:System.DateTime" /> représente une heure locale, il est converti en UTC.</summary>
    </member>
    <member name="T:System.Xml.XmlException">
      <summary>Retourne des informations détaillées sur la dernière exception.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor">
      <summary>Initialise une nouvelle instance de la classe XmlException.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe XmlException avec un message d'erreur spécifié.</summary>
      <param name="message">Description de l'erreur. </param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe XmlException.</summary>
      <param name="message">Description de la condition d'erreur. </param>
      <param name="innerException">
        <see cref="T:System.Exception" /> qui a levé XmlException, le cas échéant.Cette valeur peut être null.</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception,System.Int32,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe XmlException avec le message, l'exception interne, le numéro de ligne et la position de ligne spécifiés.</summary>
      <param name="message">Description de l'erreur. </param>
      <param name="innerException">Exception qui constitue la cause de l'exception actuelle.Cette valeur peut être null.</param>
      <param name="lineNumber">Numéro de la ligne indiquant l'endroit où l'erreur s'est produite. </param>
      <param name="linePosition">Position de la ligne indiquant l'endroit où l'erreur s'est produite. </param>
    </member>
    <member name="P:System.Xml.XmlException.LineNumber">
      <summary>Obtient le numéro de la ligne indiquant l'endroit où l'erreur s'est produite.</summary>
      <returns>Numéro de la ligne indiquant l'endroit où l'erreur s'est produite.</returns>
    </member>
    <member name="P:System.Xml.XmlException.LinePosition">
      <summary>Obtient la position de la ligne indiquant l'endroit où l'erreur s'est produite.</summary>
      <returns>Position de la ligne indiquant l'endroit où l'erreur s'est produite.</returns>
    </member>
    <member name="P:System.Xml.XmlException.Message">
      <summary>Obtient un message décrivant l'exception actuelle.</summary>
      <returns>Message d'erreur indiquant la raison de l'exception.</returns>
    </member>
    <member name="T:System.Xml.XmlNamespaceManager">
      <summary>Résout des espaces de noms dans une collection, ajoute des espaces de noms à une collection, en supprime de celle-ci et gère la portée de ces espaces de noms. </summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.#ctor(System.Xml.XmlNameTable)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Xml.XmlNamespaceManager" /> avec le <see cref="T:System.Xml.XmlNameTable" /> spécifié.</summary>
      <param name="nameTable">
        <see cref="T:System.Xml.XmlNameTable" /> à utiliser. </param>
      <exception cref="T:System.NullReferenceException">null is passed to the constructor </exception>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.AddNamespace(System.String,System.String)">
      <summary>Ajoute l'espace de noms spécifié à la collection.</summary>
      <param name="prefix">Préfixe à associer à l'espace de noms ajouté.Utilisez String.Empty pour ajouter un espace de noms par défaut.Remarque : si <see cref="T:System.Xml.XmlNamespaceManager" /> est utilisé pour la résolution des espaces de noms dans une expression XPath (XML Path Language), un préfixe doit être spécifié.Si une expression XPath n'inclut pas de préfixe, l'URI (Uniform Resource Identifier) d'espace de noms est supposé être un espace de noms vide.Pour plus d'informations sur les expressions XPath et <see cref="T:System.Xml.XmlNamespaceManager" />, reportez-vous aux méthodes <see cref="M:System.Xml.XmlNode.SelectNodes(System.String)" /> et <see cref="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.XmlNamespaceManager)" />.</param>
      <param name="uri">Espace de noms à ajouter. </param>
      <exception cref="T:System.ArgumentException">The value for <paramref name="prefix" /> is "xml" or "xmlns". </exception>
      <exception cref="T:System.ArgumentNullException">The value for <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.DefaultNamespace">
      <summary>Obtient l'URI de l'espace de noms de l'espace de noms par défaut.</summary>
      <returns>Retourne l'URI de l'espace de noms de l'espace de noms par défaut ou String.Empty s'il n'existe aucun espace de noms par défaut.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetEnumerator">
      <summary>Retourne un énumérateur qui peut être utilisé pour itérer au sein des espaces de noms de <see cref="T:System.Xml.XmlNamespaceManager" />.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> contenant les préfixes stockés par <see cref="T:System.Xml.XmlNamespaceManager" />.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Obtient une collection de noms d'espace de noms indexés par préfixe qui peut être utilisée pour énumérer les espaces de noms figurant actuellement dans la portée.</summary>
      <returns>Collection de paires d'espace de noms et préfixe actuellement dans la portée.</returns>
      <param name="scope">Valeur d'énumération qui spécifie le type de nœuds d'espace de noms à retourner.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.HasNamespace(System.String)">
      <summary>Obtient une valeur indiquant si le préfixe fourni possède un espace de noms défini pour la portée actuelle faisant l'objet d'un push.</summary>
      <returns>true si un espace de noms est défini ; sinon, false.</returns>
      <param name="prefix">Préfixe de l'espace de noms que vous souhaitez rechercher. </param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupNamespace(System.String)">
      <summary>Obtient l'URI de l'espace de noms du préfixe spécifié.</summary>
      <returns>Retourne l'URI de l'espace de noms pour <paramref name="prefix" /> ou null en l'absence d'un espace de noms mappé.La chaîne retournée est atomisée.Pour plus d'informations sur les chaînes atomisées, consultez la classe <see cref="T:System.Xml.XmlNameTable" />.</returns>
      <param name="prefix">Préfixe dont vous souhaitez résoudre l'URI de l'espace de noms.Pour mettre en correspondance l'espace de noms par défaut, passez String.Empty.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupPrefix(System.String)">
      <summary>Recherche le préfixe déclaré pour l'URI de l'espace de noms spécifié.</summary>
      <returns>Préfixe correspondant.S'il n'y a aucun préfixe mappé, la méthode retourne String.Empty.Si une valeur nulle est fournie, null est retourné.</returns>
      <param name="uri">Espace de noms à résoudre pour le préfixe. </param>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.NameTable">
      <summary>Obtient le <see cref="T:System.Xml.XmlNameTable" /> associé à cet objet.</summary>
      <returns>
        <see cref="T:System.Xml.XmlNameTable" /> utilisé par cet objet.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PopScope">
      <summary>Dépile une portée espace de noms de la pile.</summary>
      <returns>true s'il reste des portées espaces de noms sur la pile ; false s'il n'existe plus d'espaces de noms à dépiler.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PushScope">
      <summary>Exécute un push d'une portée espace de noms dans la pile.</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.RemoveNamespace(System.String,System.String)">
      <summary>Supprime l'espace de noms indiqué pour le préfixe spécifié.</summary>
      <param name="prefix">Préfixe de l'espace de noms. </param>
      <param name="uri">Espace de noms à supprimer pour le préfixe spécifié.L'espace de noms supprimé provient de la portée espace de noms en cours.Les espaces de noms situés en dehors de la portée actuelle sont ignorés.</param>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="T:System.Xml.XmlNamespaceScope">
      <summary>Définit la portée espace de noms.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.All">
      <summary>Tous les espaces de noms définis dans la portée du nœud actuel.Ceci inclut l'espace de noms xmlns:xml, qui est toujours déclaré implicitement.L'ordre des espaces de noms retournés n'est pas défini.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.ExcludeXml">
      <summary>Tous les espaces de noms définis dans la portée du nœud actuel, à l'exclusion de l'espace de noms xmlns:xml, qui est toujours déclaré implicitement.L'ordre des espaces de noms retournés n'est pas défini.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.Local">
      <summary>Tous les espaces de noms qui sont définis localement sur le nœud actuel.</summary>
    </member>
    <member name="T:System.Xml.XmlNameTable">
      <summary>Table d'objets de chaînes atomisées.</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Xml.XmlNameTable" />. </summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>En cas de substitution dans une classe dérivée, atomise la chaîne spécifiée et l'ajoute à XmlNameTable.</summary>
      <returns>Nouvelle chaîne atomisée ou, le cas échéant, la chaîne existante.Si la longueur a la valeur zéro, String.Empty est retourné.</returns>
      <param name="array">Tableau de caractères contenant le nom à ajouter. </param>
      <param name="offset">Index de base zéro dans le tableau spécifiant le premier caractère du nom. </param>
      <param name="length">Nombre de caractères dans le nom. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />ou <paramref name="offset" /> &gt;= <paramref name="array" />.Length ou <paramref name="length" /> &gt; <paramref name="array" />.Length Les conditions ci-dessus n'entraînent pas la levée d'une exception si <paramref name="length" />=0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.String)">
      <summary>En cas de substitution dans une classe dérivée, atomise la chaîne spécifiée et l'ajoute à XmlNameTable.</summary>
      <returns>Nouvelle chaîne atomisée ou, le cas échéant, la chaîne existante.</returns>
      <param name="array">Nom à ajouter. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>En cas de substitution dans une classe dérivée, obtient la chaîne atomisée contenant les mêmes caractères que la plage de caractères spécifiée dans le tableau donné.</summary>
      <returns>Chaîne atomisée ou null si la chaîne n'a pas encore été atomisée.Si <paramref name="length" /> a la valeur zéro, String.Empty est retourné.</returns>
      <param name="array">Tableau de caractères contenant le nom à rechercher. </param>
      <param name="offset">Index de base zéro dans le tableau spécifiant le premier caractère du nom. </param>
      <param name="length">Nombre de caractères dans le nom. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />ou <paramref name="offset" /> &gt;= <paramref name="array" />.Length ou <paramref name="length" /> &gt; <paramref name="array" />.Length Les conditions ci-dessus n'entraînent pas la levée d'une exception si <paramref name="length" />=0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.String)">
      <summary>En cas de substitution dans une classe dérivée, obtient la chaîne atomisée contenant la même valeur que la chaîne spécifiée.</summary>
      <returns>Chaîne atomisée ou null si la chaîne n'a pas encore été atomisée.</returns>
      <param name="array">Nom à rechercher. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> a la valeur null. </exception>
    </member>
    <member name="T:System.Xml.XmlNodeType">
      <summary>Spécifie le type de nœud.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Attribute">
      <summary>Attribut (par exemple, id='123').</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.CDATA">
      <summary>Section CDATA (par exemple, &lt;![CDATA[my escaped text]]&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Comment">
      <summary>Commentaire (par exemple, &lt;!-- my comment --&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Document">
      <summary>Objet document qui, en tant que racine de l'arborescence de documents, permet d'accéder à l'intégralité du document XML.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentFragment">
      <summary>Fragment de document.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentType">
      <summary>Déclaration de type du document, indiquée par la balise suivante (par exemple, &lt;!DOCTYPE...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Element">
      <summary>Élément (par exemple, &lt;item&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndElement">
      <summary>Balise d'élément de fin (par exemple, &lt;/item&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndEntity">
      <summary>Retourné lorsque XmlReader parvient à la fin du remplacement de l'entité, à la suite d'un appel à <see cref="M:System.Xml.XmlReader.ResolveEntity" />.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Entity">
      <summary>Déclaration d'entité (par exemple, &lt;!ENTITY...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EntityReference">
      <summary>Référence à une entité (par exemple, &amp;num;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.None">
      <summary>Ceci est retourné par <see cref="T:System.Xml.XmlReader" /> si aucune méthode Read n'a été appelée.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Notation">
      <summary>Notation dans la déclaration de type du document (par exemple, &lt;!NOTATION...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.ProcessingInstruction">
      <summary>Instruction de traitement (par exemple, &lt;?pi test?&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.SignificantWhitespace">
      <summary>Espace blanc entre le balisage dans un modèle de contenu mixte ou espace blanc dans la portée xml:space="preserve".</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Text">
      <summary>Texte d'un nœud.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Whitespace">
      <summary>Espace blanc entre le balisage.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.XmlDeclaration">
      <summary>Déclaration XML (par exemple, &lt;?xml version='1.0'?&gt;).</summary>
    </member>
    <member name="T:System.Xml.XmlParserContext">
      <summary>Fournit toutes les informations de contexte requises par <see cref="T:System.Xml.XmlReader" /> pour analyser un fragment XML.</summary>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace)">
      <summary>Initialise une nouvelle instance de la classe XmlParserContext avec les <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, URI de base, xml:lang, xml:space et valeurs de type de document spécifiés.</summary>
      <param name="nt">
        <see cref="T:System.Xml.XmlNameTable" /> à utiliser pour atomiser les chaînes.Si la valeur est null, la table de noms servant à construire <paramref name="nsMgr" /> est utilisée à la place.Pour plus d'informations concernant les chaînes atomisées, consultez <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" /> à utiliser pour la recherche d'informations d'espace de noms, ou null. </param>
      <param name="docTypeName">Nom de la déclaration de type du document. </param>
      <param name="pubId">Identificateur public. </param>
      <param name="sysId">Identificateur système. </param>
      <param name="internalSubset">Sous-ensemble interne DTD.Le sous-ensemble DTD est utilisé pour la résolution d'entité, pas pour la validation de document.</param>
      <param name="baseURI">URI de base du fragment XML (emplacement à partir duquel le fragment a été chargé). </param>
      <param name="xmlLang">Portée xml:lang. </param>
      <param name="xmlSpace">Valeur <see cref="T:System.Xml.XmlSpace" /> indiquant la portée xml:space. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> n'est pas le même XmlNameTable utilisé pour construire <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Initialise une nouvelle instance de la classe XmlParserContext avec les <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, URI de base, xml:lang, xml:space, encodage et valeurs de type de document spécifiés.</summary>
      <param name="nt">
        <see cref="T:System.Xml.XmlNameTable" /> à utiliser pour atomiser les chaînes.Si la valeur est null, la table de noms servant à construire <paramref name="nsMgr" /> est utilisée à la place.Pour plus d'informations concernant les chaînes atomisées, consultez <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" /> à utiliser pour la recherche d'informations d'espace de noms, ou null. </param>
      <param name="docTypeName">Nom de la déclaration de type du document. </param>
      <param name="pubId">Identificateur public. </param>
      <param name="sysId">Identificateur système. </param>
      <param name="internalSubset">Sous-ensemble interne DTD.Le DTD est utilisé pour la résolution d'entité, pas pour la validation de document.</param>
      <param name="baseURI">URI de base du fragment XML (emplacement à partir duquel le fragment a été chargé). </param>
      <param name="xmlLang">Portée xml:lang. </param>
      <param name="xmlSpace">Valeur <see cref="T:System.Xml.XmlSpace" /> indiquant la portée xml:space. </param>
      <param name="enc">Objet <see cref="T:System.Text.Encoding" /> indiquant le paramètre d'encodage. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> n'est pas le même XmlNameTable utilisé pour construire <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace)">
      <summary>Initialise une nouvelle instance de la classe XmlParserContext avec les valeurs <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang et xml:space spécifiées.</summary>
      <param name="nt">
        <see cref="T:System.Xml.XmlNameTable" /> à utiliser pour atomiser les chaînes.Si la valeur est null, la table de noms servant à construire <paramref name="nsMgr" /> est utilisée à la place.Pour plus d'informations concernant les chaînes atomisées, consultez <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" /> à utiliser pour la recherche d'informations d'espace de noms, ou null. </param>
      <param name="xmlLang">Portée xml:lang. </param>
      <param name="xmlSpace">Valeur <see cref="T:System.Xml.XmlSpace" /> indiquant la portée xml:space. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> n'est pas le même XmlNameTable utilisé pour construire <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Initialise une nouvelle instance de la classe XmlParserContext avec les <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang, xml:space spécifiés et l'encodage spécifié.</summary>
      <param name="nt">
        <see cref="T:System.Xml.XmlNameTable" /> à utiliser pour atomiser les chaînes.Si la valeur est null, la table de noms servant à construire <paramref name="nsMgr" /> est utilisée à la place.Pour plus d'informations sur les chaînes atomisées, consultez <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" /> à utiliser pour la recherche d'informations d'espace de noms, ou null. </param>
      <param name="xmlLang">Portée xml:lang. </param>
      <param name="xmlSpace">Valeur <see cref="T:System.Xml.XmlSpace" /> indiquant la portée xml:space. </param>
      <param name="enc">Objet <see cref="T:System.Text.Encoding" /> indiquant le paramètre d'encodage. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> n'est pas le même XmlNameTable utilisé pour construire <paramref name="nsMgr" />. </exception>
    </member>
    <member name="P:System.Xml.XmlParserContext.BaseURI">
      <summary>Obtient ou définit l'URI de base.</summary>
      <returns>URI de base à utiliser pour résoudre le fichier DTD.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.DocTypeName">
      <summary>Obtient ou définit le nom de la déclaration de type du document.</summary>
      <returns>Nom de la déclaration de type du document.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.Encoding">
      <summary>Obtient ou définit le type d'encodage.</summary>
      <returns>Objet <see cref="T:System.Text.Encoding" /> indiquant le type d'encodage.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.InternalSubset">
      <summary>Obtient ou définit le sous-ensemble interne DTD.</summary>
      <returns>Sous-ensemble interne DTD.Par exemple, cette propriété retourne tout ce qui est contenu entre crochets &lt;!DOCTYPE doc [...]&gt;.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NamespaceManager">
      <summary>Obtient ou définit l'<see cref="T:System.Xml.XmlNamespaceManager" />.</summary>
      <returns>XmlNamespaceManager.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NameTable">
      <summary>Obtient le <see cref="T:System.Xml.XmlNameTable" /> utilisé pour atomiser les chaînes.Pour plus d'informations sur les chaînes atomisées, consultez <see cref="T:System.Xml.XmlNameTable" />.</summary>
      <returns>XmlNameTable.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.PublicId">
      <summary>Obtient ou définit l'identificateur public.</summary>
      <returns>Identificateur public.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.SystemId">
      <summary>Obtient ou définit l'identificateur système.</summary>
      <returns>Identificateur système.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlLang">
      <summary>Obtient ou définit la portée xml:lang en cours.</summary>
      <returns>Portée xml:lang en cours.S'il n'existe aucun xml:lang dans la portée, String.Empty est retournée.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlSpace">
      <summary>Obtient ou définit la portée xml:space en cours.</summary>
      <returns>Valeur <see cref="T:System.Xml.XmlSpace" /> indiquant la portée xml:space.</returns>
    </member>
    <member name="T:System.Xml.XmlQualifiedName">
      <summary>Représente un nom qualifié XML.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Xml.XmlQualifiedName" /> avec le nom spécifié.</summary>
      <param name="name">Le nom local à utiliser comme nom de l'objet <see cref="T:System.Xml.XmlQualifiedName" />. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Xml.XmlQualifiedName" /> avec le nom et l'espace de noms spécifiés.</summary>
      <param name="name">Le nom local à utiliser comme nom de l'objet <see cref="T:System.Xml.XmlQualifiedName" />. </param>
      <param name="ns">Espace de noms pour l'objet <see cref="T:System.Xml.XmlQualifiedName" />. </param>
    </member>
    <member name="F:System.Xml.XmlQualifiedName.Empty">
      <summary>Fournit une chaîne vide <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Xml.XmlQualifiedName" /> spécifié est identique à l'objet <see cref="T:System.Xml.XmlQualifiedName" /> en cours. </summary>
      <returns>true si les deux sont le même objet d'instance ; sinon, false.</returns>
      <param name="other">
        <see cref="T:System.Xml.XmlQualifiedName" /> à comparer. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.GetHashCode">
      <summary>Retourne le code de hachage pour <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Code de hachage de cet objet.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.IsEmpty">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Xml.XmlQualifiedName" /> est vide.</summary>
      <returns>true si le nom et l'espace de noms sont des chaînes vides ; sinon false.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Name">
      <summary>Obtient une représentation de chaîne du nom complet de <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Une représentation du nom complet ou String.Empty si un nom n'est pas défini pour l'objet.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Namespace">
      <summary>Obtient une représentation d'espace de noms de <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Une représentation de l'espace de noms, ou String.Empty si un espace de noms n'est pas défini pour l'objet.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Equality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Compare deux objets <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>true si les deux objets ont le même nom et les mêmes valeurs d'espace de noms ; sinon false.</returns>
      <param name="a">
        <see cref="T:System.Xml.XmlQualifiedName" /> à comparer. </param>
      <param name="b">
        <see cref="T:System.Xml.XmlQualifiedName" /> à comparer. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Inequality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Compare deux objets <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>true si les valeurs de nom et d'espace de noms diffèrent pour les deux objets ; sinon false.</returns>
      <param name="a">
        <see cref="T:System.Xml.XmlQualifiedName" /> à comparer. </param>
      <param name="b">
        <see cref="T:System.Xml.XmlQualifiedName" /> à comparer. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString">
      <summary>Retourne la valeur de chaîne de <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>La valeur de chaîne de <see cref="T:System.Xml.XmlQualifiedName" /> au format de namespace:localname.Si l'objet n'a pas un espace de noms défini, cette méthode retourne uniquement le nom local.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString(System.String,System.String)">
      <summary>Retourne la valeur de chaîne de <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>La valeur de chaîne de <see cref="T:System.Xml.XmlQualifiedName" /> au format de namespace:localname.Si l'objet n'a pas un espace de noms défini, cette méthode retourne uniquement le nom local.</returns>
      <param name="name">Nom de l'objet. </param>
      <param name="ns">Espace de noms pour l'objet. </param>
    </member>
    <member name="T:System.Xml.XmlReader">
      <summary>Représente un lecteur fournissant un accès rapide, non mis en cache et en avant uniquement vers les données XML.Pour parcourir le code source de .NET Framework pour ce type, consultez la Source de référence.</summary>
    </member>
    <member name="M:System.Xml.XmlReader.#ctor">
      <summary>Initialise une nouvelle instance de la classe XmlReader.</summary>
    </member>
    <member name="P:System.Xml.XmlReader.AttributeCount">
      <summary>En cas de substitution dans une classe dérivée, obtient le nombre d'attributs du nœud actuel.</summary>
      <returns>Nombre d'attributs du nœud actuel.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.BaseURI">
      <summary>En cas de substitution dans une classe dérivée, obtient l'URI de base du nœud actuel.</summary>
      <returns>URI de base du nœud actuel.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadBinaryContent">
      <summary>Obtient une valeur qui indique si <see cref="T:System.Xml.XmlReader" /> implémente les méthodes de lecture de contenu binaire.</summary>
      <returns>true si les méthodes de lecture de contenu binaire sont implémentées ; sinon, false.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadValueChunk">
      <summary>Obtient une valeur indiquant si <see cref="T:System.Xml.XmlReader" /> implémente la méthode <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> spécifiée.</summary>
      <returns>true si <see cref="T:System.Xml.XmlReader" /> implémente la méthode <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> ; sinon, false.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanResolveEntity">
      <summary>Obtient une valeur indiquant si ce lecteur peut analyser et résoudre les entités.</summary>
      <returns>true si le lecteur peut analyser et résoudre les entités ; sinon, false.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream)">
      <summary>Crée un nouveau <see cref="T:System.Xml.XmlReader" /> instance à l'aide du flux spécifié avec les paramètres par défaut.</summary>
      <returns>Objet permettant de lire les données XML contenues dans le flux de données.</returns>
      <param name="input">Flux contenant les données XML.<see cref="T:System.Xml.XmlReader" /> analyse les premiers octets du flux à la recherche d'une marque d'ordre d'octet ou d'un autre signe d'encodage.Quand l'encodage est déterminé, il est utilisé pour continuer à lire le flux, et le traitement continue à analyser l'entrée en tant que flux de caractères (Unicode).</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="input" /> est null.</exception>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Xml.XmlReader" /> n'a pas les autorisations nécessaires pour accéder à l'emplacement des données XML.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings)">
      <summary>Crée un nouveau <see cref="T:System.Xml.XmlReader" /> instance avec les paramètres et les flux de données spécifié.</summary>
      <returns>Objet permettant de lire les données XML contenues dans le flux de données.</returns>
      <param name="input">Flux contenant les données XML.<see cref="T:System.Xml.XmlReader" /> analyse les premiers octets du flux à la recherche d'une marque d'ordre d'octet ou d'un autre signe d'encodage.Quand l'encodage est déterminé, il est utilisé pour continuer à lire le flux, et le traitement continue à analyser l'entrée en tant que flux de caractères (Unicode).</param>
      <param name="settings">Les paramètres du nouveau <see cref="T:System.Xml.XmlReader" /> instance.Cette valeur peut être null.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="input" /> est null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Crée un nouveau <see cref="T:System.Xml.XmlReader" /> instance à l'aide des informations de contexte, les paramètres et les flux de données spécifiées pour l'analyse.</summary>
      <returns>Objet permettant de lire les données XML contenues dans le flux de données.</returns>
      <param name="input">Flux contenant les données XML. <see cref="T:System.Xml.XmlReader" /> analyse les premiers octets du flux à la recherche d'une marque d'ordre d'octet ou d'un autre signe d'encodage.Quand l'encodage est déterminé, il est utilisé pour continuer à lire le flux, et le traitement continue à analyser l'entrée en tant que flux de caractères (Unicode).</param>
      <param name="settings">Les paramètres du nouveau <see cref="T:System.Xml.XmlReader" /> instance.Cette valeur peut être null.</param>
      <param name="inputContext">Les informations de contexte nécessaires à l'analyse du fragment XML.Les informations de contexte peuvent inclure la <see cref="T:System.Xml.XmlNameTable" /> à utiliser, l'encodage, la portée espace de noms, la portée xml:lang et xml:space actuelle, l'URI de base et la définition de type de document.Cette valeur peut être null.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="input" /> est null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader)">
      <summary>Crée un nouveau <see cref="T:System.Xml.XmlReader" /> à l'aide du lecteur de texte spécifié.</summary>
      <returns>Objet permettant de lire les données XML contenues dans le flux de données.</returns>
      <param name="input">Lecteur de texte à partir duquel lire les données XML.Comme un lecteur de texte retourne un flux de caractères Unicode, l'encodage spécifié dans la déclaration XML n'est pas utilisé par le lecteur XML pour décoder le flux de données.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="input" /> est null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings)">
      <summary>Crée un nouveau <see cref="T:System.Xml.XmlReader" /> à l'aide de la lecture du texte spécifié et les paramètres.</summary>
      <returns>Objet permettant de lire les données XML contenues dans le flux de données.</returns>
      <param name="input">Lecteur de texte à partir duquel lire les données XML.Comme un lecteur de texte retourne un flux de caractères Unicode, l'encodage spécifié dans la déclaration XML n'est pas utilisé par le lecteur XML pour décoder le flux de données.</param>
      <param name="settings">Les paramètres du nouveau <see cref="T:System.Xml.XmlReader" />.Cette valeur peut être null.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="input" /> est null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Crée un nouveau <see cref="T:System.Xml.XmlReader" /> instance pour l'analyse à l'aide des informations de lecteur, les paramètres et le contexte de texte spécifié.</summary>
      <returns>Objet permettant de lire les données XML contenues dans le flux de données.</returns>
      <param name="input">Lecteur de texte à partir duquel lire les données XML.Comme un lecteur de texte retourne un flux de caractères Unicode, l'encodage spécifié dans la déclaration XML n'est pas utilisé par le lecteur XML pour décoder le flux de données.</param>
      <param name="settings">Les paramètres du nouveau <see cref="T:System.Xml.XmlReader" /> instance.Cette valeur peut être null.</param>
      <param name="inputContext">Les informations de contexte nécessaires à l'analyse du fragment XML.Les informations de contexte peuvent inclure la <see cref="T:System.Xml.XmlNameTable" /> à utiliser, l'encodage, la portée espace de noms, la portée xml:lang et xml:space actuelle, l'URI de base et la définition de type de document.Cette valeur peut être null.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="input" /> est null.</exception>
      <exception cref="T:System.ArgumentException">Les propriétés <see cref="P:System.Xml.XmlReaderSettings.NameTable" /> et <see cref="P:System.Xml.XmlParserContext.NameTable" /> contiennent toutes deux des valeurs.(Seule une de ces propriétés NameTable peut être définie et utilisée).</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String)">
      <summary>Crée une instance de <see cref="T:System.Xml.XmlReader" /> avec l'URI spécifié.</summary>
      <returns>Objet permettant de lire les données XML contenues dans le flux de données.</returns>
      <param name="inputUri">URI du fichier qui contient les données XML.La classe <see cref="T:System.Xml.XmlUrlResolver" /> permet de convertir un chemin d'accès en représentation de données canonique.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="inputUri" /> est null.</exception>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Xml.XmlReader" /> n'a pas les autorisations nécessaires pour accéder à l'emplacement des données XML.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier identifié par l'URI n'existe pas.</exception>
      <exception cref="T:System.UriFormatException">Dans les .NET pour applications Windows Store  ou la Bibliothèque de classes portable, intercepte l'exception de classe de base, <see cref="T:System.FormatException" />, sinon.Le format d'URI n'est pas correct.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String,System.Xml.XmlReaderSettings)">
      <summary>Crée un nouveau <see cref="T:System.Xml.XmlReader" /> à l'aide de l'URI et les paramètres spécifiés.</summary>
      <returns>Objet permettant de lire les données XML contenues dans le flux de données.</returns>
      <param name="inputUri">URI du fichier contenant les données XML.L'objet <see cref="T:System.Xml.XmlResolver" /> sur l'objet <see cref="T:System.Xml.XmlReaderSettings" /> permet de convertir un chemin d'accès en représentation de données canonique.Si <see cref="P:System.Xml.XmlReaderSettings.XmlResolver" /> est null, un nouvel objet <see cref="T:System.Xml.XmlUrlResolver" /> est utilisé.</param>
      <param name="settings">Les paramètres du nouveau <see cref="T:System.Xml.XmlReader" /> instance.Cette valeur peut être null.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="inputUri" /> est null.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Impossible de trouver le fichier spécifié par l'URI.</exception>
      <exception cref="T:System.UriFormatException">Dans les .NET pour applications Windows Store  ou la Bibliothèque de classes portable, intercepte l'exception de classe de base, <see cref="T:System.FormatException" />, sinon.Le format d'URI n'est pas correct.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.Xml.XmlReader,System.Xml.XmlReaderSettings)">
      <summary>Crée un nouveau <see cref="T:System.Xml.XmlReader" /> instance à l'aide du lecteur XML spécifié et les paramètres.</summary>
      <returns>Un objet qui est encapsulé autour du texte spécifié <see cref="T:System.Xml.XmlReader" /> objet.</returns>
      <param name="reader">L'objet à utiliser comme lecteur XML sous-jacent.</param>
      <param name="settings">Les paramètres du nouveau <see cref="T:System.Xml.XmlReader" /> instance.Le niveau de conformité de l'objet <see cref="T:System.Xml.XmlReaderSettings" /> doit soit correspondre au niveau de conformité du lecteur sous-jacent, soit avoir la valeur <see cref="F:System.Xml.ConformanceLevel.Auto" />.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="reader" /> est null.</exception>
      <exception cref="T:System.InvalidOperationException">Si l'objet <see cref="T:System.Xml.XmlReaderSettings" /> spécifie un niveau de conformité qui n'est pas cohérent avec le niveau de conformité du lecteur sous-jacent.ouLe <see cref="T:System.Xml.XmlReader" /> sous-jacent est dans un état <see cref="F:System.Xml.ReadState.Error" /> ou <see cref="F:System.Xml.ReadState.Closed" />.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Depth">
      <summary>En cas de substitution dans une classe dérivée, obtient la profondeur du nœud actuel dans le document XML.</summary>
      <returns>Profondeur du nœud actuel dans le document XML.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose">
      <summary>Libère toutes les ressources utilisées par l'instance actuelle de la classe <see cref="T:System.Xml.XmlReader" />.</summary>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par <see cref="T:System.Xml.XmlReader" /> et libère éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour ne libérer que les ressources non managées.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.EOF">
      <summary>En cas de substitution dans une classe dérivée, obtient une valeur indiquant si le lecteur est placé à la fin du flux.</summary>
      <returns>true si le lecteur est placé à la fin du flux ; sinon, false.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.Int32)">
      <summary>En cas de substitution dans une classe dérivée, obtient la valeur de l'attribut avec l'index spécifié.</summary>
      <returns>Valeur de l'attribut spécifié.Cette méthode ne déplace pas le lecteur.</returns>
      <param name="i">Index de l'attribut.L'index est de base zéro.Le premier attribut possède l'index 0.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" /> est hors limites.Il doit être non négatif et inférieur à la taille de la collection d'attributs.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String)">
      <summary>En cas de substitution dans une classe dérivée, obtient la valeur de l'attribut avec le <see cref="P:System.Xml.XmlReader.Name" /> spécifié.</summary>
      <returns>Valeur de l'attribut spécifié.Si l'attribut est introuvable ou si la valeur est String.Empty, null est retourné.</returns>
      <param name="name">Nom qualifié de l'attribut.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, obtient la valeur de l'attribut avec le <see cref="P:System.Xml.XmlReader.LocalName" /> et le <see cref="P:System.Xml.XmlReader.NamespaceURI" /> spécifiés.</summary>
      <returns>Valeur de l'attribut spécifié.Si l'attribut est introuvable ou si la valeur est String.Empty, null est retourné.Cette méthode ne déplace pas le lecteur.</returns>
      <param name="name">Le nom local de l'attribut.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'attribut.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> a la valeur null.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetValueAsync">
      <summary>Obtient de façon asynchrone la valeur du nœud actuel.</summary>
      <returns>Valeur du nœud actuel.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasAttributes">
      <summary>Obtient une valeur indiquant si le nœud actuel a des attributs.</summary>
      <returns>true si le nœud actuel possède des attributs ; sinon, false.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasValue">
      <summary>En cas de substitution dans une classe dérivée, obtient une valeur indiquant si le nœud actuel peut posséder <see cref="P:System.Xml.XmlReader.Value" />.</summary>
      <returns>true si le nœud sur lequel le lecteur est placé actuellement peut posséder Value ; sinon, false.Si false, le nœud a une valeur de String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsDefault">
      <summary>En cas de substitution dans une classe dérivée, obtient une valeur indiquant si le nœud actuel est un attribut généré à partir de la valeur par défaut définie dans le DTD ou le schéma.</summary>
      <returns>true si le nœud actuel est un attribut dont la valeur a été générée à partir de la valeur par défaut définie dans le DTD ou le schéma ; false si la valeur d'attribut a été définie explicitement.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsEmptyElement">
      <summary>En cas de substitution dans une classe dérivée, obtient une valeur indiquant si le nœud actuel est un élément vide (par exemple, &lt;MyElement/&gt;).</summary>
      <returns>true si le nœud actuel est un élément (la propriété <see cref="P:System.Xml.XmlReader.NodeType" /> est égale à XmlNodeType.Element) qui se termine par /&gt; ; sinon, false.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsName(System.String)">
      <summary>Retourne une valeur indiquant si l'argument de chaîne est un nom XML valide.</summary>
      <returns>true si le nom est valide ; sinon, false.</returns>
      <param name="str">Nom à valider.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="str" /> est null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsNameToken(System.String)">
      <summary>Retourne une valeur indiquant si l'argument de chaîne est un jeton de nom XML valide.</summary>
      <returns>true si le jeton de nom est valide ; sinon, false.</returns>
      <param name="str">Jeton de nom à valider.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="str" /> est null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement">
      <summary>Appelle <see cref="M:System.Xml.XmlReader.MoveToContent" /> et vérifie si le nœud de contenu actuel est une balise de début ou une balise d'élément vide.</summary>
      <returns>true si <see cref="M:System.Xml.XmlReader.MoveToContent" /> trouve une balise de début ou une balise d'élément vide ; false si un type de nœud autre que XmlNodeType.Element est trouvé.</returns>
      <exception cref="T:System.Xml.XmlException">Code XML incorrect dans le flux d'entrée.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String)">
      <summary>Appelle <see cref="M:System.Xml.XmlReader.MoveToContent" />, vérifie si le nœud de contenu actuel est une balise de début ou une balise d'élément vide, puis vérifie également si la propriété <see cref="P:System.Xml.XmlReader.Name" /> de l'élément trouvé correspond à l'argument spécifié.</summary>
      <returns>true si le nœud résultant est un élément et si la propriété Name correspond à la chaîne spécifiée.false si un type de nœud autre que XmlNodeType.Element a été trouvé ou si la propriété Name de l'élément ne correspond pas à la chaîne spécifiée.</returns>
      <param name="name">Chaîne comparée à la propriété Name de l'élément trouvé.</param>
      <exception cref="T:System.Xml.XmlException">Code XML incorrect dans le flux d'entrée.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String,System.String)">
      <summary>Appelle <see cref="M:System.Xml.XmlReader.MoveToContent" />, vérifie si le nœud de contenu actuel est une balise de début ou une balise d'élément vide, puis vérifie également si les propriétés <see cref="P:System.Xml.XmlReader.LocalName" /> et <see cref="P:System.Xml.XmlReader.NamespaceURI" /> de l'élément trouvé correspondent aux chaînes spécifiées.</summary>
      <returns>true si le nœud résultant est un élément.false si un type de nœud autre que XmlNodeType.Element a été trouvé ou si les propriétés LocalName et NamespaceURI de l'élément ne correspondent pas aux chaînes spécifiées.</returns>
      <param name="localname">Chaîne à comparer à la propriété LocalName de l'élément trouvé.</param>
      <param name="ns">Chaîne à comparer à la propriété NamespaceURI de l'élément trouvé.</param>
      <exception cref="T:System.Xml.XmlException">Code XML incorrect dans le flux d'entrée.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.Int32)">
      <summary>En cas de substitution dans une classe dérivée, obtient la valeur de l'attribut avec l'index spécifié.</summary>
      <returns>Valeur de l'attribut spécifié.</returns>
      <param name="i">Index de l'attribut.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String)">
      <summary>En cas de substitution dans une classe dérivée, obtient la valeur de l'attribut avec le <see cref="P:System.Xml.XmlReader.Name" /> spécifié.</summary>
      <returns>Valeur de l'attribut spécifié.Si l'attribut est introuvable, null est retournée.</returns>
      <param name="name">Nom qualifié de l'attribut.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, obtient la valeur de l'attribut avec le <see cref="P:System.Xml.XmlReader.LocalName" /> et le <see cref="P:System.Xml.XmlReader.NamespaceURI" /> spécifiés.</summary>
      <returns>Valeur de l'attribut spécifié.Si l'attribut est introuvable, null est retournée.</returns>
      <param name="name">Le nom local de l'attribut.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'attribut.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.LocalName">
      <summary>En cas de substitution dans une classe dérivée, obtient le nom local du nœud actuel.</summary>
      <returns>Nom du nœud actuel dont le préfixe est supprimé.Par exemple, LocalName est book pour l'élément &lt;bk:book&gt;.Pour les types de nœuds ne possédant pas de nom (par exemple Text, Comment, etc.), cette propriété retourne String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.LookupNamespace(System.String)">
      <summary>En cas de substitution dans une classe dérivée, résout un préfixe de l'espace de noms dans la portée de l'élément actuel.</summary>
      <returns>URI de l'espace de noms vers lequel le préfixe est mappé ou null si aucun préfixe correspondant n'est trouvé.</returns>
      <param name="prefix">Préfixe dont vous souhaitez résoudre l'URI de l'espace de noms.Pour établir une correspondance avec l'espace de noms par défaut, passez une chaîne vide.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.Int32)">
      <summary>En cas de substitution dans une classe dérivée, se déplace vers l'attribut avec l'index spécifié.</summary>
      <param name="i">Index de l'attribut.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre a une valeur négative.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String)">
      <summary>En cas de substitution dans une classe dérivée, se déplace vers l'attribut avec le <see cref="P:System.Xml.XmlReader.Name" /> spécifié.</summary>
      <returns>true si l'attribut est trouvé ; sinon, false.Si la valeur est false, la position du lecteur ne change pas.</returns>
      <param name="name">Nom qualifié de l'attribut.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.ArgumentException">Le paramètre est une chaîne vide.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, se déplace vers l'attribut avec le <see cref="P:System.Xml.XmlReader.LocalName" /> et le <see cref="P:System.Xml.XmlReader.NamespaceURI" /> spécifiés.</summary>
      <returns>true si l'attribut est trouvé ; sinon, false.Si la valeur est false, la position du lecteur ne change pas.</returns>
      <param name="name">Le nom local de l'attribut.</param>
      <param name="ns">L'URI de l'espace de noms de l'attribut.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.ArgumentNullException">Les deux valeurs des paramètres sont null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContent">
      <summary>Vérifie si le nœud actuel est un nœud de contenu (texte non constitué d'espaces blancs, CDATA, Element, EndElement, EntityReference ou EndEntity).Si le nœud n'est pas un nœud de contenu, le lecteur avance jusqu'au nœud de contenu suivant ou jusqu'à la fin du fichier.Il ignore les nœuds possédant les types suivants : ProcessingInstruction, DocumentType, Comment, Whitespace ou SignificantWhitespace.</summary>
      <returns>
        <see cref="P:System.Xml.XmlReader.NodeType" /> du nœud actuel trouvé par la méthode ou XmlNodeType.None si le lecteur a atteint la fin du flux d'entrée.</returns>
      <exception cref="T:System.Xml.XmlException">Code XML incorrect dans le flux d'entrée.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContentAsync">
      <summary>Vérifie de façon asynchrone si le nœud actuel est un nœud de contenu.Si le nœud n'est pas un nœud de contenu, le lecteur avance jusqu'au nœud de contenu suivant ou jusqu'à la fin du fichier.</summary>
      <returns>
        <see cref="P:System.Xml.XmlReader.NodeType" /> du nœud actuel trouvé par la méthode ou XmlNodeType.None si le lecteur a atteint la fin du flux d'entrée.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToElement">
      <summary>En cas de substitution dans une classe dérivée, se déplace vers l'élément contenant le nœud d'attribut actuel.</summary>
      <returns>true si le lecteur est placé sur un attribut (le lecteur se déplace vers l'élément possédant l'attribut) ; false si le lecteur n'est pas placé sur un attribut (la position du lecteur ne change pas).</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToFirstAttribute">
      <summary>En cas de substitution dans une classe dérivée, se déplace vers le premier attribut.</summary>
      <returns>true si un attribut existe (le lecteur se déplace vers le premier attribut) ; sinon, false (la position du lecteur ne change pas).</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToNextAttribute">
      <summary>En cas de substitution dans une classe dérivée, se déplace vers l'attribut suivant.</summary>
      <returns>true s'il existe un attribut suivant ; false s'il n'existe plus d'attributs.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Name">
      <summary>En cas de substitution dans une classe dérivée, obtient le nom qualifié du nœud actuel.</summary>
      <returns>Nom qualifié du nœud actuel.Par exemple, Name est bk:book pour l'élément &lt;bk:book&gt;.Le nom retourné dépend du <see cref="P:System.Xml.XmlReader.NodeType" /> du nœud.Les types de nœuds suivants retournent les valeurs répertoriées.Tous les autres types de nœuds retournent une chaîne vide.Type de nœud Nom AttributeNom de l'attribut. DocumentTypeNom du type de document. ElementNom de la balise. EntityReferenceNom de l'entité référencée. ProcessingInstructionCible de l'instruction de traitement. XmlDeclarationChaîne littérale xml. </returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NamespaceURI">
      <summary>En cas de substitution dans une classe dérivée, obtient l'URI de l'espace de noms (tel qu'il est défini dans la spécification relative aux espaces de noms du W3C) du nœud sur lequel le lecteur est placé.</summary>
      <returns>URI d'espace de noms du nœud actuel ; sinon, une chaîne vide.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NameTable">
      <summary>En cas de substitution dans une classe dérivée, obtient le <see cref="T:System.Xml.XmlNameTable" /> associé à cette implémentation.</summary>
      <returns>XmlNameTable vous permettant d'obtenir la version atomisée d'une chaîne du nœud.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NodeType">
      <summary>En cas de substitution dans une classe dérivée, obtient le type du nœud actuel.</summary>
      <returns>Une des valeurs d'énumération qui spécifient le type du nœud actuel.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Prefix">
      <summary>En cas de substitution dans une classe dérivée, obtient le préfixe de l'espace de noms associé au nœud actuel.</summary>
      <returns>Préfixe de l'espace de noms associé au nœud actuel.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Read">
      <summary>En cas de substitution dans une classe dérivée, lit le nœud suivant à partir du flux.</summary>
      <returns>trueSi le nœud suivant a été lu avec succès ; Sinon, false.</returns>
      <exception cref="T:System.Xml.XmlException">Une erreur s'est produite lors de l'analyse XML.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAsync">
      <summary>Lit de façon asynchrone le nœud suivant à partir du flux de données.</summary>
      <returns>true si le nœud suivant a été lu correctement ; false s'il n'existe plus de nœuds à lire.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAttributeValue">
      <summary>En cas de substitution dans une classe dérivée, analyse la valeur d'attribut dans un ou plusieurs nœuds Text, EntityReference ou EndEntity.</summary>
      <returns>true s'il existe des nœuds à retourner.false si le lecteur n'est pas placé sur un nœud d'attribut lorsque l'appel initial est effectué ou si toutes les valeurs d'attributs ont été lues.Un attribut vide, par exemple misc="", retourne true avec un nœud unique et la valeur String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Lit le contenu en tant qu'objet du type spécifié.</summary>
      <returns>Contenu de texte concaténé ou valeur d'attribut converti(e) en type demandé.</returns>
      <param name="returnType">Type de la valeur à retourner.Remarque   Avec le .NET Framework version 3.5, la valeur du paramètre <paramref name="returnType" /> peut maintenant être le type <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Objet <see cref="T:System.Xml.IXmlNamespaceResolver" /> permettant de résoudre tous les préfixes d'espaces de noms liés à la conversion de type.Par exemple, il peut être utilisé lors de la conversion d'un objet <see cref="T:System.Xml.XmlQualifiedName" /> en xs:string.Cette valeur peut être null.</param>
      <exception cref="T:System.FormatException">Le format du contenu n'est pas correct pour le type cible.</exception>
      <exception cref="T:System.InvalidCastException">La tentative de cast n'est pas valide.</exception>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="returnType" /> est null.</exception>
      <exception cref="T:System.InvalidOperationException">Le nœud actuel n'est pas un type de nœud pris en charge.Voir le tableau ci-dessous pour plus d'informations.</exception>
      <exception cref="T:System.OverflowException">Lire Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Lit de façon asynchrone le contenu en tant qu'objet du type spécifié.</summary>
      <returns>Contenu de texte concaténé ou valeur d'attribut converti(e) en type demandé.</returns>
      <param name="returnType">Type de la valeur à retourner.</param>
      <param name="namespaceResolver">Objet <see cref="T:System.Xml.IXmlNamespaceResolver" /> permettant de résoudre tous les préfixes d'espaces de noms liés à la conversion de type.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Lit le contenu et retourne les octets binaires décodés au format Base64.</summary>
      <returns>Nombre d'octets écrits dans la mémoire tampon.</returns>
      <param name="buffer">Mémoire tampon dans laquelle copier le texte obtenu.Cette valeur ne peut pas être null.</param>
      <param name="index">Offset de la mémoire tampon où commence la copie du résultat.</param>
      <param name="count">Nombre maximal d'octets à copier dans la mémoire tampon.Le nombre réel d'octets copiés est retourné à partir de cette méthode.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="buffer" /> est null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)" /> n'est pas pris en charge sur le nœud actuel.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'index de la mémoire tampon (ou l'index augmenté de la valeur du paramètre count) est supérieur à la taille de la mémoire tampon allouée.</exception>
      <exception cref="T:System.NotSupportedException">L'implémentation de <see cref="T:System.Xml.XmlReader" /> ne prend pas en charge cette méthode.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Lit de façon asynchrone le contenu et retourne les octets binaires décodés au format Base64.</summary>
      <returns>Nombre d'octets écrits dans la mémoire tampon.</returns>
      <param name="buffer">Mémoire tampon dans laquelle copier le texte obtenu.Cette valeur ne peut pas être null.</param>
      <param name="index">Offset de la mémoire tampon où commence la copie du résultat.</param>
      <param name="count">Nombre maximal d'octets à copier dans la mémoire tampon.Le nombre réel d'octets copiés est retourné à partir de cette méthode.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Lit le contenu et retourne les octets binaires décodés au format BinHex.</summary>
      <returns>Nombre d'octets écrits dans la mémoire tampon.</returns>
      <param name="buffer">Mémoire tampon dans laquelle copier le texte obtenu.Cette valeur ne peut pas être null.</param>
      <param name="index">Offset de la mémoire tampon où commence la copie du résultat.</param>
      <param name="count">Nombre maximal d'octets à copier dans la mémoire tampon.Le nombre réel d'octets copiés est retourné à partir de cette méthode.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="buffer" /> est null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)" /> n'est pas pris en charge sur le nœud actuel.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'index de la mémoire tampon (ou l'index augmenté de la valeur du paramètre count) est supérieur à la taille de la mémoire tampon allouée.</exception>
      <exception cref="T:System.NotSupportedException">L'implémentation de <see cref="T:System.Xml.XmlReader" /> ne prend pas en charge cette méthode.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Lit de façon asynchrone le contenu et retourne les octets binaires décodés au format BinHex.</summary>
      <returns>Nombre d'octets écrits dans la mémoire tampon.</returns>
      <param name="buffer">Mémoire tampon dans laquelle copier le texte obtenu.Cette valeur ne peut pas être null.</param>
      <param name="index">Offset de la mémoire tampon où commence la copie du résultat.</param>
      <param name="count">Nombre maximal d'octets à copier dans la mémoire tampon.Le nombre réel d'octets copiés est retourné à partir de cette méthode.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBoolean">
      <summary>Lit le contenu de texte à la position actuelle comme un Boolean.</summary>
      <returns>Contenu de texte sous la forme d'un objet <see cref="T:System.Boolean" />.</returns>
      <exception cref="T:System.InvalidCastException">La tentative de cast n'est pas valide.</exception>
      <exception cref="T:System.FormatException">Le format de chaîne n'est pas valide.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDateTimeOffset">
      <summary>Lit le contenu de texte à la position actuelle comme un objet <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Contenu de texte sous la forme d'un objet <see cref="T:System.DateTimeOffset" />.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDecimal">
      <summary>Lit le contenu de texte à la position actuelle comme un objet <see cref="T:System.Decimal" />.</summary>
      <returns>Contenu de texte à la position actuelle comme un objet <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.InvalidCastException">La tentative de cast n'est pas valide.</exception>
      <exception cref="T:System.FormatException">Le format de chaîne n'est pas valide.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDouble">
      <summary>Lit le contenu de texte à la position actuelle en tant que nombre à virgule flottante double précision.</summary>
      <returns>Contenu de texte sous la forme d'un nombre à virgule flottante double précision.</returns>
      <exception cref="T:System.InvalidCastException">La tentative de cast n'est pas valide.</exception>
      <exception cref="T:System.FormatException">Le format de chaîne n'est pas valide.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsFloat">
      <summary>Lit le contenu de texte à la position actuelle en tant que nombre à virgule flottante simple précision.</summary>
      <returns>Contenu de texte à la position actuelle en tant que nombre à virgule flottante simple précision.</returns>
      <exception cref="T:System.InvalidCastException">La tentative de cast n'est pas valide.</exception>
      <exception cref="T:System.FormatException">Le format de chaîne n'est pas valide.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsInt">
      <summary>Lit le contenu de texte à la position actuelle comme un entier signé de 32 bits.</summary>
      <returns>Contenu de texte sous la forme d'un entier signé de 32 bits.</returns>
      <exception cref="T:System.InvalidCastException">La tentative de cast n'est pas valide.</exception>
      <exception cref="T:System.FormatException">Le format de chaîne n'est pas valide.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsLong">
      <summary>Lit le contenu de texte à la position actuelle comme un entier signé de 64 bits.</summary>
      <returns>Contenu de texte sous la forme d'un entier signé de 64 bits.</returns>
      <exception cref="T:System.InvalidCastException">La tentative de cast n'est pas valide.</exception>
      <exception cref="T:System.FormatException">Le format de chaîne n'est pas valide.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObject">
      <summary>Lit le contenu de texte à la position actuelle comme un <see cref="T:System.Object" />.</summary>
      <returns>Contenu de texte sous la forme de l'objet CLR le plus approprié.</returns>
      <exception cref="T:System.InvalidCastException">La tentative de cast n'est pas valide.</exception>
      <exception cref="T:System.FormatException">Le format de chaîne n'est pas valide.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObjectAsync">
      <summary>Lit de façon asynchrone le contenu de texte à la position actuelle comme un objet <see cref="T:System.Object" />.</summary>
      <returns>Contenu de texte sous la forme de l'objet CLR le plus approprié.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsString">
      <summary>Lit le contenu de texte à la position actuelle comme un objet <see cref="T:System.String" />.</summary>
      <returns>Contenu de texte sous la forme d'un objet <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidCastException">La tentative de cast n'est pas valide.</exception>
      <exception cref="T:System.FormatException">Le format de chaîne n'est pas valide.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsStringAsync">
      <summary>Lit de façon asynchrone le contenu de texte à la position actuelle comme un objet <see cref="T:System.String" />.</summary>
      <returns>Contenu de texte sous la forme d'un objet <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Lit le contenu de l'élément en tant que type demandé.</summary>
      <returns>Contenu d'élément converti en l'objet typé demandé.</returns>
      <param name="returnType">Type de la valeur à retourner.Remarque   Avec le .NET Framework version 3.5, la valeur du paramètre <paramref name="returnType" /> peut maintenant être le type <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Objet <see cref="T:System.Xml.IXmlNamespaceResolver" /> permettant de résoudre tous les préfixes d'espaces de noms liés à la conversion de type.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouImpossible de convertir le contenu de l'élément en type demandé.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.OverflowException">Lire Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver,System.String,System.String)">
      <summary>Vérifie que le nom local spécifié et l'URI de l'espace de noms correspondent à ceux de l'élément actuel, puis lit le contenu d'élément en tant que type demandé.</summary>
      <returns>Contenu d'élément converti en l'objet typé demandé.</returns>
      <param name="returnType">Type de la valeur à retourner.Remarque   Avec le .NET Framework version 3.5, la valeur du paramètre <paramref name="returnType" /> peut maintenant être le type <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Objet <see cref="T:System.Xml.IXmlNamespaceResolver" /> permettant de résoudre tous les préfixes d'espaces de noms liés à la conversion de type.</param>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouImpossible de convertir le contenu de l'élément en type demandé.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.ArgumentException">Le nom local et l'URI de l'espace de noms spécifiés ne correspondent pas à l'élément actuel lu.</exception>
      <exception cref="T:System.OverflowException">Lire Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Lit de façon asynchrone le contenu de l'élément en tant que type demandé.</summary>
      <returns>Contenu d'élément converti en l'objet typé demandé.</returns>
      <param name="returnType">Type de la valeur à retourner.</param>
      <param name="namespaceResolver">Objet <see cref="T:System.Xml.IXmlNamespaceResolver" /> permettant de résoudre tous les préfixes d'espaces de noms liés à la conversion de type.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Lit l'élément et décode le contenu au format Base64.</summary>
      <returns>Nombre d'octets écrits dans la mémoire tampon.</returns>
      <param name="buffer">Mémoire tampon dans laquelle copier le texte obtenu.Cette valeur ne peut pas être null.</param>
      <param name="index">Offset de la mémoire tampon où commence la copie du résultat.</param>
      <param name="count">Nombre maximal d'octets à copier dans la mémoire tampon.Le nombre réel d'octets copiés est retourné à partir de cette méthode.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="buffer" /> est null.</exception>
      <exception cref="T:System.InvalidOperationException">Le nœud actuel n'est pas un nœud d'élément.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'index de la mémoire tampon (ou l'index augmenté de la valeur du paramètre count) est supérieur à la taille de la mémoire tampon allouée.</exception>
      <exception cref="T:System.NotSupportedException">L'implémentation de <see cref="T:System.Xml.XmlReader" /> ne prend pas en charge cette méthode.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément contient un contenu mixte.</exception>
      <exception cref="T:System.FormatException">Impossible de convertir le contenu en type demandé.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Lit de façon asynchrone l'élément et décode le contenu au format Base64.</summary>
      <returns>Nombre d'octets écrits dans la mémoire tampon.</returns>
      <param name="buffer">Mémoire tampon dans laquelle copier le texte obtenu.Cette valeur ne peut pas être null.</param>
      <param name="index">Offset de la mémoire tampon où commence la copie du résultat.</param>
      <param name="count">Nombre maximal d'octets à copier dans la mémoire tampon.Le nombre réel d'octets copiés est retourné à partir de cette méthode.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Lit l'élément et décode le contenu au format BinHex.</summary>
      <returns>Nombre d'octets écrits dans la mémoire tampon.</returns>
      <param name="buffer">Mémoire tampon dans laquelle copier le texte obtenu.Cette valeur ne peut pas être null.</param>
      <param name="index">Offset de la mémoire tampon où commence la copie du résultat.</param>
      <param name="count">Nombre maximal d'octets à copier dans la mémoire tampon.Le nombre réel d'octets copiés est retourné à partir de cette méthode.</param>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="buffer" /> est null.</exception>
      <exception cref="T:System.InvalidOperationException">Le nœud actuel n'est pas un nœud d'élément.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'index de la mémoire tampon (ou l'index augmenté de la valeur du paramètre count) est supérieur à la taille de la mémoire tampon allouée.</exception>
      <exception cref="T:System.NotSupportedException">L'implémentation de <see cref="T:System.Xml.XmlReader" /> ne prend pas en charge cette méthode.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément contient un contenu mixte.</exception>
      <exception cref="T:System.FormatException">Impossible de convertir le contenu en type demandé.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Lit de façon asynchrone l'élément et décode le contenu au format BinHex.</summary>
      <returns>Nombre d'octets écrits dans la mémoire tampon.</returns>
      <param name="buffer">Mémoire tampon dans laquelle copier le texte obtenu.Cette valeur ne peut pas être null.</param>
      <param name="index">Offset de la mémoire tampon où commence la copie du résultat.</param>
      <param name="count">Nombre maximal d'octets à copier dans la mémoire tampon.Le nombre réel d'octets copiés est retourné à partir de cette méthode.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean">
      <summary>Lit l'élément actuel et retourne le contenu en tant qu'objet <see cref="T:System.Boolean" />.</summary>
      <returns>Contenu de l'élément sous la forme d'un objet <see cref="T:System.Boolean" />.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en objet <see cref="T:System.Boolean" />.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean(System.String,System.String)">
      <summary>Vérifie que le nom local spécifié et l'URI de l'espace de noms correspondent à ceux de l'élément actuel, puis lit l'élément actuel et retourne le contenu en tant qu'objet <see cref="T:System.Boolean" />.</summary>
      <returns>Contenu de l'élément sous la forme d'un objet <see cref="T:System.Boolean" />.</returns>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouImpossible de convertir le contenu de l'élément en type demandé.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.ArgumentException">Le nom local et l'URI de l'espace de noms spécifiés ne correspondent pas à l'élément actuel lu.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal">
      <summary>Lit l'élément actuel et retourne le contenu en tant qu'objet <see cref="T:System.Decimal" />.</summary>
      <returns>Contenu de l'élément sous la forme d'un objet <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal(System.String,System.String)">
      <summary>Vérifie que le nom local spécifié et l'URI de l'espace de noms correspondent à ceux de l'élément actuel, puis lit l'élément actuel et retourne le contenu en tant qu'objet <see cref="T:System.Decimal" />.</summary>
      <returns>Contenu de l'élément sous la forme d'un objet <see cref="T:System.Decimal" />.</returns>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.ArgumentException">Le nom local et l'URI de l'espace de noms spécifiés ne correspondent pas à l'élément actuel lu.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble">
      <summary>Lit l'élément actuel et retourne le contenu en tant que nombre à virgule flottante double précision.</summary>
      <returns>Contenu d'élément sous la forme d'un nombre à virgule flottante double précision.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en nombre à virgule flottante double précision.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble(System.String,System.String)">
      <summary>Vérifie que le nom local et l'URI de l'espace de noms spécifiés correspondent à ceux de l'élément actuel, puis lit l'élément actuel et retourne le contenu sous la forme d'un nombre à virgule flottante double précision.</summary>
      <returns>Contenu d'élément sous la forme d'un nombre à virgule flottante double précision.</returns>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouImpossible de convertir le contenu de l'élément en type demandé.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.ArgumentException">Le nom local et l'URI de l'espace de noms spécifiés ne correspondent pas à l'élément actuel lu.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat">
      <summary>Lit l'élément actuel et retourne le contenu en tant que nombre à virgule flottante simple précision.</summary>
      <returns>Contenu d'élément sous la forme d'un nombre à virgule flottante simple précision.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en nombre à virgule flottante simple précision.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat(System.String,System.String)">
      <summary>Vérifie que le nom local et l'URI de l'espace de noms spécifiés correspondent à ceux de l'élément actuel, puis lit l'élément actuel et retourne le contenu sous la forme d'un nombre à virgule flottante simple précision.</summary>
      <returns>Contenu d'élément sous la forme d'un nombre à virgule flottante simple précision.</returns>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en nombre à virgule flottante simple précision.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.ArgumentException">Le nom local et l'URI de l'espace de noms spécifiés ne correspondent pas à l'élément actuel lu.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt">
      <summary>Lit l'élément actuel et retourne le contenu comme un entier signé de 32 bits.</summary>
      <returns>Contenu d'élément sous la forme d'un entier signé de 32 bits.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en un entier signé de 32 bits.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt(System.String,System.String)">
      <summary>Vérifie que le nom local spécifié et l'URI de l'espace de noms correspondent à ceux de l'élément actuel, puis lit l'élément actuel et retourne le contenu en tant qu'entier signé de 32 bits.</summary>
      <returns>Contenu d'élément sous la forme d'un entier signé de 32 bits.</returns>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en un entier signé de 32 bits.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.ArgumentException">Le nom local et l'URI de l'espace de noms spécifiés ne correspondent pas à l'élément actuel lu.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong">
      <summary>Lit l'élément actuel et retourne le contenu comme un entier signé de 64 bits.</summary>
      <returns>Contenu d'élément sous la forme d'un entier signé de 64 bits.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en un entier signé de 64 bits.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong(System.String,System.String)">
      <summary>Vérifie que le nom local spécifié et l'URI de l'espace de noms correspondent à ceux de l'élément actuel, puis lit l'élément actuel et retourne le contenu en tant qu'entier signé de 64 bits.</summary>
      <returns>Contenu d'élément sous la forme d'un entier signé de 64 bits.</returns>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en un entier signé de 64 bits.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.ArgumentException">Le nom local et l'URI de l'espace de noms spécifiés ne correspondent pas à l'élément actuel lu.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject">
      <summary>Lit l'élément actuel et retourne le contenu en tant que <see cref="T:System.Object" />.</summary>
      <returns>Objet CLR boxed du type le plus approprié.La propriété <see cref="P:System.Xml.XmlReader.ValueType" /> détermine le type CLR approprié.Si le contenu est de type liste, cette méthode retourne un tableau d'objets boxed du type approprié.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouImpossible de convertir le contenu de l'élément en type demandé</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject(System.String,System.String)">
      <summary>Vérifie que le nom local et l'URI de l'espace de noms spécifiés correspondent à ceux de l'élément actuel, puis lit l'élément actuel et retourne le contenu en tant qu'objet <see cref="T:System.Object" />.</summary>
      <returns>Objet CLR boxed du type le plus approprié.La propriété <see cref="P:System.Xml.XmlReader.ValueType" /> détermine le type CLR approprié.Si le contenu est de type liste, cette méthode retourne un tableau d'objets boxed du type approprié.</returns>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouImpossible de convertir le contenu de l'élément en type demandé.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.ArgumentException">Le nom local et l'URI de l'espace de noms spécifiés ne correspondent pas à l'élément actuel lu.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObjectAsync">
      <summary>Lit de façon asynchrone l'élément actuel et retourne le contenu en tant que <see cref="T:System.Object" />.</summary>
      <returns>Objet CLR boxed du type le plus approprié.La propriété <see cref="P:System.Xml.XmlReader.ValueType" /> détermine le type CLR approprié.Si le contenu est de type liste, cette méthode retourne un tableau d'objets boxed du type approprié.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString">
      <summary>Lit l'élément actuel et retourne le contenu en tant qu'objet <see cref="T:System.String" />.</summary>
      <returns>Contenu de l'élément sous la forme d'un objet <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en objet <see cref="T:System.String" />.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString(System.String,System.String)">
      <summary>Vérifie que le nom local spécifié et l'URI de l'espace de noms correspondent à ceux de l'élément actuel, puis lit l'élément actuel et retourne le contenu en tant qu'objet <see cref="T:System.String" />.</summary>
      <returns>Contenu de l'élément sous la forme d'un objet <see cref="T:System.String" />.</returns>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément.</exception>
      <exception cref="T:System.Xml.XmlException">L'élément en cours contient des éléments enfants.ouLe contenu de l'élément ne peut pas être converti en objet <see cref="T:System.String" />.</exception>
      <exception cref="T:System.ArgumentNullException">La méthode est appelée avec des arguments null.</exception>
      <exception cref="T:System.ArgumentException">Le nom local et l'URI de l'espace de noms spécifiés ne correspondent pas à l'élément actuel lu.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsStringAsync">
      <summary>Lit de façon asynchrone l'élément actuel et retourne le contenu en tant qu'objet <see cref="T:System.String" />.</summary>
      <returns>Contenu de l'élément sous la forme d'un objet <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadEndElement">
      <summary>Vérifie que le nœud de contenu actuel est une balise de fin et avance le lecteur jusqu'au nœud suivant.</summary>
      <exception cref="T:System.Xml.XmlException">Le nœud actuel n'est pas une balise de fin ou un code XML incorrect est trouvé dans le flux d'entrée.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXml">
      <summary>En cas de substitution dans une classe dérivée, lit tout le contenu, y compris le balisage, sous forme de chaîne.</summary>
      <returns>Tout le contenu XML, y compris le balisage, du nœud actuel.Si le nœud actuel n'a pas d'enfants, une chaîne vide est retournée.Si le nœud actuel n'est ni un élément ni un attribut, une chaîne vide est retournée.</returns>
      <exception cref="T:System.Xml.XmlException">XML était incorrect ou une erreur s'est produite lors de l'analyse XML.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXmlAsync">
      <summary>Lit de façon asynchrone tout le contenu, notamment le balisage, en tant que chaîne.</summary>
      <returns>Tout le contenu XML, y compris le balisage, du nœud actuel.Si le nœud actuel n'a pas d'enfants, une chaîne vide est retournée.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXml">
      <summary>En cas de substitution dans une classe dérivée, lit le contenu, y compris le balisage, représentant ce nœud et tous ses enfants.</summary>
      <returns>Si le lecteur est placé sur un nœud d'élément ou d'attribut, cette méthode retourne tout le contenu XML, y compris le balisage, du nœud actuel et de tous ses enfants ; sinon, elle retourne une chaîne vide.</returns>
      <exception cref="T:System.Xml.XmlException">XML était incorrect ou une erreur s'est produite lors de l'analyse XML.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXmlAsync">
      <summary>Lit de façon asynchrone le contenu, notamment le balisage, qui représente ce nœud et tous ses enfants.</summary>
      <returns>Si le lecteur est placé sur un nœud d'élément ou d'attribut, cette méthode retourne tout le contenu XML, y compris le balisage, du nœud actuel et de tous ses enfants ; sinon, elle retourne une chaîne vide.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement">
      <summary>Vérifie que le nœud actuel est un élément et avance le lecteur jusqu'au nœud suivant.</summary>
      <exception cref="T:System.Xml.XmlException">Code XML incorrect dans le flux d'entrée.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String)">
      <summary>Vérifie que le nœud de contenu actuel est un élément avec le <see cref="P:System.Xml.XmlReader.Name" /> spécifié, puis avance le lecteur jusqu'au nœud suivant.</summary>
      <param name="name">Nom qualifié de l'élément.</param>
      <exception cref="T:System.Xml.XmlException">Code XML incorrect dans le flux d'entrée. ou Le <see cref="P:System.Xml.XmlReader.Name" /> de l'élément ne correspond pas au <paramref name="name" /> donné.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String,System.String)">
      <summary>Vérifie que le nœud de contenu actuel est un élément avec le <see cref="P:System.Xml.XmlReader.LocalName" /> et le <see cref="P:System.Xml.XmlReader.NamespaceURI" /> spécifiés, puis avance le lecteur jusqu'au nœud suivant.</summary>
      <param name="localname">Le nom local de l'élément.</param>
      <param name="ns">L'URI de l'espace de noms de l'élément.</param>
      <exception cref="T:System.Xml.XmlException">Code XML incorrect dans le flux d'entrée.ouLes propriétés <see cref="P:System.Xml.XmlReader.LocalName" /> et <see cref="P:System.Xml.XmlReader.NamespaceURI" /> de l'élément trouvé ne correspondent pas aux arguments spécifiés.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ReadState">
      <summary>En cas de substitution dans une classe dérivée, obtient l'état du lecteur.</summary>
      <returns>L'une des valeurs d'énumération qui spécifie l'état du lecteur.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadSubtree">
      <summary>Retourne une nouvelle instance de XmlReader qui permet de lire le nœud actuel, ainsi que tous ses descendants.</summary>
      <returns>Une nouvelle instance de lecteur XML définie sur <see cref="F:System.Xml.ReadState.Initial" />.Appel de la <see cref="M:System.Xml.XmlReader.Read" /> méthode positionne le nouveau lecteur sur le nœud qui était actif avant l'appel à la <see cref="M:System.Xml.XmlReader.ReadSubtree" /> (méthode).</returns>
      <exception cref="T:System.InvalidOperationException">Lorsque cette méthode est appelée, le lecteur XML n'est pas positionné sur un élément.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String)">
      <summary>Avance le <see cref="T:System.Xml.XmlReader" /> vers l'élément descendant suivant portant le nom qualifié spécifié.</summary>
      <returns>true si un élément descendant correspondant est trouvé ; sinon, false.Si aucun élément enfant correspondant n'est trouvé, le <see cref="T:System.Xml.XmlReader" /> est placé sur la balise de fin (<see cref="P:System.Xml.XmlReader.NodeType" /> est XmlNodeType.EndElement) de l'élément.Si <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément lorsque <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String)" /> est appelé, cette méthode retourne false et la position de <see cref="T:System.Xml.XmlReader" /> ne change pas.</returns>
      <param name="name">Nom qualifié de l'élément vers lequel se déplacer.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.ArgumentException">Le paramètre est une chaîne vide.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)">
      <summary>Avance <see cref="T:System.Xml.XmlReader" /> vers le nœud descendant suivant doté du nom local et de l'URI de l'espace de noms spécifiés.</summary>
      <returns>true si un élément descendant correspondant est trouvé ; sinon, false.Si aucun élément enfant correspondant n'est trouvé, le <see cref="T:System.Xml.XmlReader" /> est placé sur la balise de fin (<see cref="P:System.Xml.XmlReader.NodeType" /> est XmlNodeType.EndElement) de l'élément.Si <see cref="T:System.Xml.XmlReader" /> n'est pas placé sur un élément lorsque <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)" /> est appelé, cette méthode retourne false et la position de <see cref="T:System.Xml.XmlReader" /> ne change pas.</returns>
      <param name="localName">Nom local de l'élément vers lequel se déplacer.</param>
      <param name="namespaceURI">URI de l'espace de noms de l'élément vers lequel se déplacer.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.ArgumentNullException">Les deux valeurs des paramètres sont null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String)">
      <summary>Lit jusqu'à trouver un élément avec le nom qualifié spécifié.</summary>
      <returns>true si un élément correspondant est trouvé ; sinon, false et <see cref="T:System.Xml.XmlReader" /> est dans un état de fin de fichier.</returns>
      <param name="name">Nom qualifié de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.ArgumentException">Le paramètre est une chaîne vide.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String,System.String)">
      <summary>Lit jusqu'à trouver un élément avec le nom local et l'URI de l'espace de noms spécifiés.</summary>
      <returns>true si un élément correspondant est trouvé ; sinon, false et <see cref="T:System.Xml.XmlReader" /> est dans un état de fin de fichier.</returns>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="namespaceURI">L'URI de l'espace de noms de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.ArgumentNullException">Les deux valeurs des paramètres sont null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String)">
      <summary>Avance le XmlReader vers l'élément frère suivant portant le nom qualifié spécifié.</summary>
      <returns>true si un élément frère correspondant est trouvé ; sinon, false.Si aucun élément frère correspondant n'est trouvé, le XmlReader est placé sur la balise de fin (<see cref="P:System.Xml.XmlReader.NodeType" /> est XmlNodeType.EndElement) de l'élément parent.</returns>
      <param name="name">Nom qualifié de l'élément frère vers lequel se déplacer.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.ArgumentException">Le paramètre est une chaîne vide.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String,System.String)">
      <summary>Avance XmlReader vers l'élément frère suivant doté du nom local et de l'URI de l'espace de noms spécifiés.</summary>
      <returns>true si un élément frère correspondant est trouvé ; sinon, false.Si aucun élément frère correspondant n'est trouvé, le XmlReader est placé sur la balise de fin (<see cref="P:System.Xml.XmlReader.NodeType" /> est XmlNodeType.EndElement) de l'élément parent.</returns>
      <param name="localName">Nom local de l'élément frère vers lequel se déplacer.</param>
      <param name="namespaceURI">URI de l'espace de noms de l'élément frère vers lequel se déplacer.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.ArgumentNullException">Les deux valeurs des paramètres sont null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)">
      <summary>Lit des flux de texte volumineux incorporés dans un document XML.</summary>
      <returns>Nombre total de caractères lus dans la mémoire tampon.La valeur zéro est retournée quand il n'y a plus de contenu de texte.</returns>
      <param name="buffer">Tableau de caractères servant de mémoire tampon dans laquelle le texte est écrit.Cette valeur ne peut pas être null.</param>
      <param name="index">Offset dans la mémoire tampon où le <see cref="T:System.Xml.XmlReader" /> peut commencer à copier les résultats.</param>
      <param name="count">Nombre maximal de caractères à copier dans la mémoire tampon.Le nombre réel de caractères copiés est retourné à partir de cette méthode.</param>
      <exception cref="T:System.InvalidOperationException">Le nœud actuel n'a pas de valeur (<see cref="P:System.Xml.XmlReader.HasValue" /> est false).</exception>
      <exception cref="T:System.ArgumentNullException">La valeur <paramref name="buffer" /> est null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">L'index de la mémoire tampon, ou l'index augmenté de la valeur du paramètre count, est supérieur à la taille de la mémoire tampon allouée.</exception>
      <exception cref="T:System.NotSupportedException">L'implémentation de <see cref="T:System.Xml.XmlReader" /> ne prend pas en charge cette méthode.</exception>
      <exception cref="T:System.Xml.XmlException">La forme des données XML n'est pas correcte.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunkAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Lit de façon asynchrone des flux de texte volumineux incorporés dans un document XML.</summary>
      <returns>Nombre total de caractères lus dans la mémoire tampon.La valeur zéro est retournée quand il n'y a plus de contenu de texte.</returns>
      <param name="buffer">Tableau de caractères servant de mémoire tampon dans laquelle le texte est écrit.Cette valeur ne peut pas être null.</param>
      <param name="index">Offset dans la mémoire tampon où le <see cref="T:System.Xml.XmlReader" /> peut commencer à copier les résultats.</param>
      <param name="count">Nombre maximal de caractères à copier dans la mémoire tampon.Le nombre réel de caractères copiés est retourné à partir de cette méthode.</param>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ResolveEntity">
      <summary>En cas de substitution dans une classe dérivée, résout la référence d'entité des nœuds EntityReference.</summary>
      <exception cref="T:System.InvalidOperationException">Le lecteur n'est pas placé sur un nœud EntityReference ; cette implémentation du lecteur ne permet pas de résoudre les entités (<see cref="P:System.Xml.XmlReader.CanResolveEntity" /> retourne false).</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Settings">
      <summary>Obtient l'objet <see cref="T:System.Xml.XmlReaderSettings" /> permettant de créer cette instance de <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlReaderSettings" /> permettant de créer cette instance du lecteur.Si ce lecteur n'a pas été créé à l'aide de la méthode <see cref="Overload:System.Xml.XmlReader.Create" />, cette propriété retourne null.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Skip">
      <summary>Ignore les enfants du nœud actuel.</summary>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.SkipAsync">
      <summary>Ignore de façon asynchrone les enfants du nœud actuel.</summary>
      <returns>Nœud actuel.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
      <exception cref="T:System.InvalidOperationException">Une méthode asynchrone <see cref="T:System.Xml.XmlReader" /> a été appelée sans définir l'indicateur <see cref="P:System.Xml.XmlReaderSettings.Async" /> sur true.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant de définir XmlReaderSettings.Async si vous souhaitez utiliser les méthodes Async.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Value">
      <summary>En cas de substitution dans une classe dérivée, obtient la valeur texte du nœud actuel.</summary>
      <returns>La valeur retournée dépend du <see cref="P:System.Xml.XmlReader.NodeType" /> du nœud.Le tableau suivant répertorie les types de nœuds possédant une valeur de retour.Tous les autres types de nœuds retournent String.Empty.Type de nœud Valeur AttributeValeur de l'attribut. CDATAContenu de la section CDATA. CommentContenu du commentaire. DocumentTypeSous-ensemble interne. ProcessingInstructionContenu entier, à l'exclusion de la cible. SignificantWhitespaceEspace blanc entre les balisages dans un modèle de contenu mixte. TextContenu du nœud de texte. WhitespaceEspace blanc entre les balisages. XmlDeclarationContenu de la déclaration. </returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ValueType">
      <summary>Obtient le type de CLR du nœud actuel.</summary>
      <returns>Type CLR qui correspond à la valeur typée du nœud.La valeur par défaut est System.String.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlLang">
      <summary>En cas de substitution dans une classe dérivée, obtient la portée xml:lang en cours.</summary>
      <returns>Portée xml:lang en cours.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlSpace">
      <summary>En cas de substitution dans une classe dérivée, obtient la portée xml:space en cours.</summary>
      <returns>Une des valeurs de <see cref="T:System.Xml.XmlSpace" />.S'il n'existe pas de portée xml:space, cette propriété prend la valeur par défaut XmlSpace.None.</returns>
      <exception cref="T:System.InvalidOperationException">Une méthode <see cref="T:System.Xml.XmlReader" /> a été appelée avant la fin d'une opération asynchrone précédente.Dans ce cas, <see cref="T:System.InvalidOperationException" /> est levée avec le message indiquant qu'une opération asynchrone est déjà en cours.</exception>
    </member>
    <member name="T:System.Xml.XmlReaderSettings">
      <summary>Spécifie un jeu de fonctionnalités à prendre en charge sur l'objet <see cref="T:System.Xml.XmlReader" /> créé par la méthode <see cref="Overload:System.Xml.XmlReader.Create" />. </summary>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Xml.XmlReaderSettings" />.</summary>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.Async">
      <summary>Obtient ou définit une valeur indiquant si les méthodes <see cref="T:System.Xml.XmlReader" /> asynchrones peuvent être utilisées sur une instance <see cref="T:System.Xml.XmlReader" /> particulière.</summary>
      <returns>true si des méthodes asynchrones peuvent être utilisées ; sinon, false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CheckCharacters">
      <summary>Obtient ou définit une valeur indiquant si la vérification des caractères doit être assurée.</summary>
      <returns>true pour assurer la vérification des caractères ; sinon, false.La valeur par défaut est true.RemarqueSi le <see cref="T:System.Xml.XmlReader" /> traite des données de texte, il vérifie toujours que les noms XML et le contenu de texte sont valides, indépendamment du paramètre de propriété.L'attribution à <see cref="P:System.Xml.XmlReaderSettings.CheckCharacters" /> de la valeur false désactive la vérification de caractères pour la recherche de références d'entité de caractère.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Clone">
      <summary>Crée une copie de l'instance de <see cref="T:System.Xml.XmlReaderSettings" />.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlReaderSettings" /> cloné.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CloseInput">
      <summary>Obtient ou définit une valeur indiquant si le flux sous-jacent ou <see cref="T:System.IO.TextReader" /> doit être fermé à la fermeture du lecteur.</summary>
      <returns>true pour fermer le flux sous-jacent ou <see cref="T:System.IO.TextReader" /> à la fermeture du lecteur ; sinon false.La valeur par défaut est false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.ConformanceLevel">
      <summary>Obtient ou définit le niveau de conformité que <see cref="T:System.Xml.XmlReader" /> respecte.</summary>
      <returns>Une des valeurs d'énumération qui spécifie le niveau de conformité appliqué par le lecteur XML.La valeur par défaut est <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.DtdProcessing">
      <summary>Obtient ou définit une valeur qui détermine le traitement des DTD.</summary>
      <returns>L'une des valeurs d'énumération qui détermine le traitement des DTD.La valeur par défaut est <see cref="F:System.Xml.DtdProcessing.Prohibit" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreComments">
      <summary>Obtient ou définit une valeur indiquant si les commentaires doivent être ignorés.</summary>
      <returns>true pour ignorer les commentaires ; sinon false.La valeur par défaut est false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreProcessingInstructions">
      <summary>Obtient ou définit une valeur indiquant si les instructions de traitement doivent être ignorées.</summary>
      <returns>true pour ignorer les instructions de traitement ; sinon false.La valeur par défaut est false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreWhitespace">
      <summary>Obtient ou définit une valeur indiquant si les espaces blancs non significatifs doivent être ignorés.</summary>
      <returns>true pour ignorer l'espace blanc ; sinon false.La valeur par défaut est false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LineNumberOffset">
      <summary>Obtient ou définit l'offset du numéro de ligne de l'objet <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Offset de numéro de ligne.La valeur par défaut est 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LinePositionOffset">
      <summary>Obtient ou définit l'offset de position de ligne de l'objet <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Décalage de position de ligne.La valeur par défaut est 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersFromEntities">
      <summary>Obtient ou définit une valeur correspondant au nombre maximal autorisé de caractères dans un document, qui résultent du développement des entités.</summary>
      <returns>Nombre maximal autorisé de caractères résultant du développement des entités.La valeur par défaut est 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersInDocument">
      <summary>Obtient ou définit une valeur correspondant au nombre maximal autorisé de caractères dans un document XML.Zéro (0) signifie que la taille du document XML n'est pas limitée.Une valeur non nulle spécifie la taille maximale, en caractères.</summary>
      <returns>Nombre maximal autorisé de caractères dans un document XML.La valeur par défaut est 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.NameTable">
      <summary>Obtient ou définit <see cref="T:System.Xml.XmlNameTable" /> servant aux comparaisons de chaînes atomisées.</summary>
      <returns>
        <see cref="T:System.Xml.XmlNameTable" /> qui stocke toutes les chaînes atomisées utilisées par toutes les instances <see cref="T:System.Xml.XmlReader" /> créées à l'aide de cet objet <see cref="T:System.Xml.XmlReaderSettings" />.La valeur par défaut est null.L'instance de <see cref="T:System.Xml.XmlReader" /> créée utilisera un nouveau <see cref="T:System.Xml.NameTable" /> vide si cette valeur est null.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Reset">
      <summary>Réinitialise les membres de la classe de paramètres à leurs valeurs par défaut.</summary>
    </member>
    <member name="T:System.Xml.XmlSpace">
      <summary>Spécifie la portée xml:space en cours.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Default">
      <summary>La portée xml:space est default.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.None">
      <summary>Pas de portée xml:space.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Preserve">
      <summary>La portée xml:space est preserve.</summary>
    </member>
    <member name="T:System.Xml.XmlWriter">
      <summary>Représente un writer qui fournit un moyen rapide, sans mise en cache et en avant de générer des flux de données ou des fichiers contenant des données XML.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream)">
      <summary>Crée une instance de <see cref="T:System.Xml.XmlWriter" /> à l'aide du flux spécifié.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Flux dans lequel vous voulez écrire.<see cref="T:System.Xml.XmlWriter" /> écrit la syntaxe du texte XML 1.0 et l'ajoute au flux de données spécifié.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream,System.Xml.XmlWriterSettings)">
      <summary>Crée une instance de <see cref="T:System.Xml.XmlWriter" /> à l'aide du flux et de l'objet <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Flux dans lequel vous voulez écrire.<see cref="T:System.Xml.XmlWriter" /> écrit la syntaxe du texte XML 1.0 et l'ajoute au flux de données spécifié.</param>
      <param name="settings">Objet <see cref="T:System.Xml.XmlWriterSettings" /> permettant de configurer la nouvelle instance de <see cref="T:System.Xml.XmlWriter" />.S'il est null, un <see cref="T:System.Xml.XmlWriterSettings" /> avec des paramètres par défaut est utilisé.Si <see cref="T:System.Xml.XmlWriter" /> est utilisé avec la méthode <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, vous devez utiliser la propriété <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> pour obtenir un objet <see cref="T:System.Xml.XmlWriterSettings" /> avec les paramètres corrects.Cela garantit que l'objet <see cref="T:System.Xml.XmlWriter" /> créé dispose des paramètres de sortie corrects.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter)">
      <summary>Crée une instance de <see cref="T:System.Xml.XmlWriter" /> à l'aide du <see cref="T:System.IO.TextWriter" /> spécifié.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">
        <see cref="T:System.IO.TextWriter" /> dans lequel écrire.<see cref="T:System.Xml.XmlWriter" /> écrit la syntaxe du texte XML 1.0 et l'ajoute au <see cref="T:System.IO.TextWriter" /> spécifié.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter,System.Xml.XmlWriterSettings)">
      <summary>Crée une nouvelle instance de <see cref="T:System.Xml.XmlWriter" /> à l'aide des objets <see cref="T:System.IO.TextWriter" /> et <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">
        <see cref="T:System.IO.TextWriter" /> dans lequel écrire.<see cref="T:System.Xml.XmlWriter" /> écrit la syntaxe du texte XML 1.0 et l'ajoute au <see cref="T:System.IO.TextWriter" /> spécifié.</param>
      <param name="settings">Objet <see cref="T:System.Xml.XmlWriterSettings" /> permettant de configurer la nouvelle instance de <see cref="T:System.Xml.XmlWriter" />.S'il est null, un <see cref="T:System.Xml.XmlWriterSettings" /> avec des paramètres par défaut est utilisé.Si <see cref="T:System.Xml.XmlWriter" /> est utilisé avec la méthode <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, vous devez utiliser la propriété <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> pour obtenir un objet <see cref="T:System.Xml.XmlWriterSettings" /> avec les paramètres corrects.Cela garantit que l'objet <see cref="T:System.Xml.XmlWriter" /> créé dispose des paramètres de sortie corrects.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder)">
      <summary>Crée une instance de <see cref="T:System.Xml.XmlWriter" /> à l'aide du <see cref="T:System.Text.StringBuilder" /> spécifié.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">
        <see cref="T:System.Text.StringBuilder" /> dans lequel écrire.Le contenu écrit par le <see cref="T:System.Xml.XmlWriter" /> est ajouté au <see cref="T:System.Text.StringBuilder" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder,System.Xml.XmlWriterSettings)">
      <summary>Crée une nouvelle instance de <see cref="T:System.Xml.XmlWriter" /> à l'aide des objets <see cref="T:System.Text.StringBuilder" /> et <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">
        <see cref="T:System.Text.StringBuilder" /> dans lequel écrire.Le contenu écrit par le <see cref="T:System.Xml.XmlWriter" /> est ajouté au <see cref="T:System.Text.StringBuilder" />.</param>
      <param name="settings">Objet <see cref="T:System.Xml.XmlWriterSettings" /> permettant de configurer la nouvelle instance de <see cref="T:System.Xml.XmlWriter" />.S'il est null, un <see cref="T:System.Xml.XmlWriterSettings" /> avec des paramètres par défaut est utilisé.Si <see cref="T:System.Xml.XmlWriter" /> est utilisé avec la méthode <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, vous devez utiliser la propriété <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> pour obtenir un objet <see cref="T:System.Xml.XmlWriterSettings" /> avec les paramètres corrects.Cela garantit que l'objet <see cref="T:System.Xml.XmlWriter" /> créé dispose des paramètres de sortie corrects.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter)">
      <summary>Crée une instance de <see cref="T:System.Xml.XmlWriter" /> à l'aide de l'objet <see cref="T:System.Xml.XmlWriter" /> spécifié.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlWriter" /> autour de l'objet <see cref="T:System.Xml.XmlWriter" /> spécifié.</returns>
      <param name="output">L'objet <see cref="T:System.Xml.XmlWriter" /> à utiliser comme writer sous-jacent.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter,System.Xml.XmlWriterSettings)">
      <summary>Crée une instance de <see cref="T:System.Xml.XmlWriter" /> à l'aide des objets <see cref="T:System.Xml.XmlWriter" /> et <see cref="T:System.Xml.XmlWriterSettings" /> spécifiés.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlWriter" /> autour de l'objet <see cref="T:System.Xml.XmlWriter" /> spécifié.</returns>
      <param name="output">L'objet <see cref="T:System.Xml.XmlWriter" /> à utiliser comme writer sous-jacent.</param>
      <param name="settings">Objet <see cref="T:System.Xml.XmlWriterSettings" /> permettant de configurer la nouvelle instance de <see cref="T:System.Xml.XmlWriter" />.S'il est null, un <see cref="T:System.Xml.XmlWriterSettings" /> avec des paramètres par défaut est utilisé.Si <see cref="T:System.Xml.XmlWriter" /> est utilisé avec la méthode <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, vous devez utiliser la propriété <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> pour obtenir un objet <see cref="T:System.Xml.XmlWriterSettings" /> avec les paramètres corrects.Cela garantit que l'objet <see cref="T:System.Xml.XmlWriter" /> créé dispose des paramètres de sortie corrects.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose">
      <summary>Libère toutes les ressources utilisées par l'instance actuelle de la classe <see cref="T:System.Xml.XmlWriter" />.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par <see cref="T:System.Xml.XmlWriter" /> et libère éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Flush">
      <summary>En cas de substitution dans une classe dérivée, vide le contenu de la mémoire tampon dans les flux sous-jacents, puis vide le flux sous-jacent.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.FlushAsync">
      <summary>Vide de façon asynchrone le contenu de la mémoire tampon dans les flux sous-jacents, puis vide le flux sous-jacent.</summary>
      <returns>Tâche qui représente l'opération Flush asynchrone.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.LookupPrefix(System.String)">
      <summary>En cas de substitution dans une classe dérivée, retourne le préfixe le plus proche défini dans la portée espace de noms actuelle pour l'URI de l'espace de noms.</summary>
      <returns>Le préfixe correspondant ou null, s'il n'existe aucun URI d'espace de noms correspondant dans la portée actuelle.</returns>
      <param name="ns">URI de l'espace de noms dont vous recherchez le préfixe.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ns" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.Settings">
      <summary>Obtient l'objet <see cref="T:System.Xml.XmlWriterSettings" /> permettant de créer cette instance de <see cref="T:System.Xml.XmlWriter" />.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlWriterSettings" /> permettant de créer cette instance de writer.Si ce writer n'a pas été créé à l'aide de la méthode <see cref="Overload:System.Xml.XmlWriter.Create" />, cette propriété retourne null.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributes(System.Xml.XmlReader,System.Boolean)">
      <summary>En cas de substitution dans une classe dérivée, écrit tous les attributs trouvés à la position actuelle dans <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="reader">XmlReader à partir duquel les attributs doivent être copiés.</param>
      <param name="defattr">true pour copier les attributs par défaut à partir de XmlReader ; sinon, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null. </exception>
      <exception cref="T:System.Xml.XmlException">The reader is not positioned on an element, attribute or XmlDeclaration node. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributesAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>Écrit de façon asynchrone tous les attributs trouvés à la position actuelle dans le <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Tâche qui représente l'opération WriteAttributes asynchrone.</returns>
      <param name="reader">XmlReader à partir duquel les attributs doivent être copiés.</param>
      <param name="defattr">true pour copier les attributs par défaut à partir de XmlReader ; sinon, false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit l'attribut avec le nom local et la valeur spécifiés.</summary>
      <param name="localName">Le nom local de l'attribut.</param>
      <param name="value">Valeur de l'attribut.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit un attribut avec le nom local, l'URI de l'espace de noms et la valeur spécifiés.</summary>
      <param name="localName">Le nom local de l'attribut.</param>
      <param name="ns">URI de l'espace de noms à associer à l'attribut.</param>
      <param name="value">Valeur de l'attribut.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit l'attribut avec le préfixe, le nom local, l'URI de l'espace de noms et la valeur spécifiés.</summary>
      <param name="prefix">Préfixe de l'espace de noms de cet attribut.</param>
      <param name="localName">Le nom local de l'attribut.</param>
      <param name="ns">L'URI de l'espace de noms de l'attribut.</param>
      <param name="value">Valeur de l'attribut.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.Xml.XmlException">The <paramref name="localName" /> or <paramref name="ns" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Écrit de façon asynchrone l'attribut avec le préfixe, le nom local, l'URI de l'espace de noms et la valeur spécifiés.</summary>
      <returns>Tâche qui représente l'opération WriteAttributeString asynchrone.</returns>
      <param name="prefix">Préfixe de l'espace de noms de cet attribut.</param>
      <param name="localName">Le nom local de l'attribut.</param>
      <param name="ns">L'URI de l'espace de noms de l'attribut.</param>
      <param name="value">Valeur de l'attribut.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>En cas de substitution dans une classe dérivée, code les octets binaires spécifiés au format Base64 et écrit le texte obtenu.</summary>
      <param name="buffer">Tableau d'octets à encoder.</param>
      <param name="index">Emplacement dans la mémoire tampon indiquant le début des octets à écrire.</param>
      <param name="count">Nombre d'octets à écrire.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Encode de façon asynchrone les octets binaires spécifiés au format base64 et écrit le texte résultant.</summary>
      <returns>Tâche qui représente l'opération WriteBase64 asynchrone.</returns>
      <param name="buffer">Tableau d'octets à encoder.</param>
      <param name="index">Emplacement dans la mémoire tampon indiquant le début des octets à écrire.</param>
      <param name="count">Nombre d'octets à écrire.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>En cas de substitution dans une classe dérivée, code les octets binaires spécifiés au format BinHex et écrit le texte obtenu.</summary>
      <param name="buffer">Tableau d'octets à encoder.</param>
      <param name="index">Emplacement dans la mémoire tampon indiquant le début des octets à écrire.</param>
      <param name="count">Nombre d'octets à écrire.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Encode de façon asynchrone les octets binaires spécifiés au format BinHex et écrit le texte résultant.</summary>
      <returns>Tâche qui représente l'opération WriteBinHex asynchrone.</returns>
      <param name="buffer">Tableau d'octets à encoder.</param>
      <param name="index">Emplacement dans la mémoire tampon indiquant le début des octets à écrire.</param>
      <param name="count">Nombre d'octets à écrire.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCData(System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit un bloc &lt;![CDATA[...]]&gt; contenant le texte spécifié.</summary>
      <param name="text">Texte à placer dans le bloc CDATA.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCDataAsync(System.String)">
      <summary>Écrit de façon asynchrone un bloc &lt;![CDATA[…]]&gt; contenant le texte spécifié.</summary>
      <returns>Tâche qui représente l'opération WriteCData asynchrone.</returns>
      <param name="text">Texte à placer dans le bloc CDATA.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntity(System.Char)">
      <summary>En cas de substitution dans une classe dérivée, force la génération d'une entité de caractère pour la valeur du caractère Unicode spécifiée.</summary>
      <param name="ch">Caractère Unicode pour lequel une entité de caractère doit être générée.</param>
      <exception cref="T:System.ArgumentException">The character is in the surrogate pair character range, 0xd800 - 0xdfff.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntityAsync(System.Char)">
      <summary>Force de façon asynchrone la génération d'une entité de caractère pour la valeur du caractère Unicode spécifiée.</summary>
      <returns>Tâche qui représente l'opération WriteCharEntity asynchrone.</returns>
      <param name="ch">Caractère Unicode pour lequel une entité de caractère doit être générée.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteChars(System.Char[],System.Int32,System.Int32)">
      <summary>En cas de substitution dans une classe dérivée, écrit du texte mémoire tampon par mémoire tampon.</summary>
      <param name="buffer">Tableau de caractères contenant le texte à écrire.</param>
      <param name="index">Emplacement dans la mémoire tampon indiquant le début du texte à écrire.</param>
      <param name="count">Nombre de caractères à écrire.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />; the call results in surrogate pair characters being split or an invalid surrogate pair being written.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="buffer" /> parameter value is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharsAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Écrit de façon asynchrone du texte mémoire tampon par mémoire tampon.</summary>
      <returns>Tâche qui représente l'opération WriteChars asynchrone.</returns>
      <param name="buffer">Tableau de caractères contenant le texte à écrire.</param>
      <param name="index">Emplacement dans la mémoire tampon indiquant le début du texte à écrire.</param>
      <param name="count">Nombre de caractères à écrire.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteComment(System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit un commentaire &lt;!--...--&gt; contenant le texte spécifié.</summary>
      <param name="text">Texte à placer dans le commentaire.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well-formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCommentAsync(System.String)">
      <summary>Écrit de façon asynchrone un commentaire &lt;!--...--&gt; contenant le texte spécifié.</summary>
      <returns>Tâche qui représente l'opération WriteComment asynchrone.</returns>
      <param name="text">Texte à placer dans le commentaire.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocType(System.String,System.String,System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit la déclaration DOCTYPE avec le nom et les attributs facultatifs spécifiés.</summary>
      <param name="name">Nom de DOCTYPE.Ne doit pas être vide.</param>
      <param name="pubid">Si la valeur est non null, elle écrit également PUBLIC "pubid" "sysid", <paramref name="pubid" /> et <paramref name="sysid" /> étant remplacés par la valeur des arguments spécifiés.</param>
      <param name="sysid">Si <paramref name="pubid" /> est null et que <paramref name="sysid" /> est non null, elle écrit SYSTEM "sysid", <paramref name="sysid" /> étant remplacé par la valeur de cet argument.</param>
      <param name="subset">Si la valeur est non null, elle écrit [subset] où subset est remplacé par la valeur de cet argument.</param>
      <exception cref="T:System.InvalidOperationException">This method was called outside the prolog (after the root element). </exception>
      <exception cref="T:System.ArgumentException">The value for <paramref name="name" /> would result in invalid XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocTypeAsync(System.String,System.String,System.String,System.String)">
      <summary>Écrit de façon asynchrone la déclaration DOCTYPE avec le nom et les attributs facultatifs spécifiés.</summary>
      <returns>Tâche qui représente l'opération WriteDocType asynchrone.</returns>
      <param name="name">Nom de DOCTYPE.Ne doit pas être vide.</param>
      <param name="pubid">Si la valeur est non null, elle écrit également PUBLIC "pubid" "sysid", <paramref name="pubid" /> et <paramref name="sysid" /> étant remplacés par la valeur des arguments spécifiés.</param>
      <param name="sysid">Si <paramref name="pubid" /> est null et que <paramref name="sysid" /> est non null, elle écrit SYSTEM "sysid", <paramref name="sysid" /> étant remplacé par la valeur de cet argument.</param>
      <param name="subset">Si la valeur est non null, elle écrit [subset] où subset est remplacé par la valeur de cet argument.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String)">
      <summary>Écrit un élément avec la valeur et le nom locaux spécifiés.</summary>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="value">Valeur de l'élément.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String)">
      <summary>Écrit un élément avec le nom local, l'URI de l'espace de noms et la valeur spécifiés.</summary>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="ns">URI de l'espace de noms à associer à l'élément.</param>
      <param name="value">Valeur de l'élément.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String,System.String)">
      <summary>Écrit un élément avec le préfixe spécifié, le nom local, l'URI de l'espace de noms et la valeur spécifiés.</summary>
      <param name="prefix">Le préfixe de l'élément.</param>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="ns">L'URI de l'espace de noms de l'élément.</param>
      <param name="value">Valeur de l'élément.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Écrit de façon asynchrone un élément avec le préfixe spécifié, le nom local, l'URI de l'espace de noms et la valeur spécifiés.</summary>
      <returns>Tâche qui représente l'opération WriteElementString asynchrone.</returns>
      <param name="prefix">Le préfixe de l'élément.</param>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="ns">L'URI de l'espace de noms de l'élément.</param>
      <param name="value">Valeur de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttribute">
      <summary>En cas de substitution dans une classe dérivée, ferme le précédent appel de <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" />.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttributeAsync">
      <summary>Ferme de façon asynchrone l'appel <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> précédent.</summary>
      <returns>Tâche qui représente l'opération WriteEndAttribute asynchrone.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocument">
      <summary>En cas de substitution dans une classe dérivée, ferme les éléments ou attributs ouverts, et replace le writer à l'état Start.</summary>
      <exception cref="T:System.ArgumentException">The XML document is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocumentAsync">
      <summary>Ferme de façon asynchrone les éléments ou attributs ouverts, et replace le writer à l'état Start.</summary>
      <returns>Tâche qui représente l'opération WriteEndDocument asynchrone.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElement">
      <summary>En cas de substitution dans une classe dérivée, ferme un élément et dépile la portée espace de noms correspondante.</summary>
      <exception cref="T:System.InvalidOperationException">This results in an invalid XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElementAsync">
      <summary>Ferme de façon asynchrone un élément et exécute un pop sur la portée espace de noms correspondante.</summary>
      <returns>Tâche qui représente l'opération WriteEndElement asynchrone.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRef(System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit une référence d'entité comme suit : &amp;name;.</summary>
      <param name="name">Nom de la référence d'entité.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRefAsync(System.String)">
      <summary>Écrit de façon asynchrone une référence d'entité comme suit : &amp;name;.</summary>
      <returns>Tâche qui représente l'opération WriteEntityRef asynchrone.</returns>
      <param name="name">Nom de la référence d'entité.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElement">
      <summary>En cas de substitution dans une classe dérivée, ferme un élément et dépile la portée espace de noms correspondante.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElementAsync">
      <summary>Ferme de façon asynchrone un élément et exécute un pop sur la portée espace de noms correspondante.</summary>
      <returns>Tâche qui représente l'opération WriteFullEndElement asynchrone.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteName(System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit le nom spécifié, en vérifiant qu'il s'agit d'un nom valide conformément à la recommandation du W3C intitulée Extensible Markup Language (XML) 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <param name="name">Nom à écrire.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid XML name; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNameAsync(System.String)">
      <summary>Écrit de façon asynchrone le nom spécifié, en vérifiant qu'il s'agit d'un nom valide conformément à la recommandation du W3C intitulée Extensible Markup Language (XML) 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <returns>Tâche qui représente l'opération WriteName asynchrone.</returns>
      <param name="name">Nom à écrire.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmToken(System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit le nom spécifié, en vérifiant qu'il s'agit d'un NmToken valide conformément à la recommandation du W3C intitulée Extensible Markup Language (XML) 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <param name="name">Nom à écrire.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid NmToken; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmTokenAsync(System.String)">
      <summary>Écrit de façon asynchrone le nom spécifié, en vérifiant qu'il s'agit d'un NmToken valide conformément à la recommandation du W3C intitulée Extensible Markup Language (XML) 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <returns>Tâche qui représente l'opération WriteNmToken asynchrone.</returns>
      <param name="name">Nom à écrire.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNode(System.Xml.XmlReader,System.Boolean)">
      <summary>En cas de substitution dans une classe dérivée, copie tout le contenu du lecteur vers le writer, puis déplace le lecteur vers le début du frère suivant.</summary>
      <param name="reader">
        <see cref="T:System.Xml.XmlReader" /> à lire.</param>
      <param name="defattr">true pour copier les attributs par défaut à partir de XmlReader ; sinon, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains invalid characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNodeAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>Copie de façon asynchrone tout le contenu du lecteur vers le writer, puis déplace le lecteur vers le début du frère suivant.</summary>
      <returns>Tâche qui représente l'opération WriteNode asynchrone.</returns>
      <param name="reader">
        <see cref="T:System.Xml.XmlReader" /> à lire.</param>
      <param name="defattr">true pour copier les attributs par défaut à partir de XmlReader ; sinon, false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstruction(System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit une instruction de traitement avec un espace entre le nom et le texte : &lt;?nom texte?&gt;.</summary>
      <param name="name">Nom de l'instruction de traitement.</param>
      <param name="text">Texte à inclure dans l'instruction de traitement.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.<paramref name="name" /> is either null or String.Empty.This method is being used to create an XML declaration after <see cref="M:System.Xml.XmlWriter.WriteStartDocument" /> has already been called. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstructionAsync(System.String,System.String)">
      <summary>Écrit de façon asynchrone une instruction de traitement avec un espace entre le nom et le texte, comme suit : &lt;?nom texte?&gt;.</summary>
      <returns>Tâche qui représente l'opération WriteProcessingInstruction asynchrone.</returns>
      <param name="name">Nom de l'instruction de traitement.</param>
      <param name="text">Texte à inclure dans l'instruction de traitement.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedName(System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit le nom qualifié de l'espace de noms.Cette méthode recherche le préfixe situé dans la portée de l'espace de noms spécifié.</summary>
      <param name="localName">Nom local à écrire.</param>
      <param name="ns">URI d'espace de noms de ce nom.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="localName" /> is either null or String.Empty.<paramref name="localName" /> is not a valid name. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedNameAsync(System.String,System.String)">
      <summary>Écrit de façon asynchrone le nom qualifié de l'espace de noms.Cette méthode recherche le préfixe situé dans la portée de l'espace de noms spécifié.</summary>
      <returns>Tâche qui représente l'opération WriteQualifiedName asynchrone.</returns>
      <param name="localName">Nom local à écrire.</param>
      <param name="ns">URI d'espace de noms de ce nom.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.Char[],System.Int32,System.Int32)">
      <summary>En cas de substitution dans une classe dérivée, écrit manuellement un balisage brut à partir d'une mémoire tampon de caractères.</summary>
      <param name="buffer">Tableau de caractères contenant le texte à écrire.</param>
      <param name="index">Emplacement dans la mémoire tampon indiquant le début du texte à écrire.</param>
      <param name="count">Nombre de caractères à écrire.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit manuellement un balisage brut à partir d'une chaîne.</summary>
      <param name="data">Chaîne contenant le texte à écrire.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Écrit manuellement de façon asynchrone un balisage brut à partir d'une mémoire tampon de caractères.</summary>
      <returns>Tâche qui représente l'opération WriteRaw asynchrone.</returns>
      <param name="buffer">Tableau de caractères contenant le texte à écrire.</param>
      <param name="index">Emplacement dans la mémoire tampon indiquant le début du texte à écrire.</param>
      <param name="count">Nombre de caractères à écrire.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.String)">
      <summary>Écrit de façon asynchrone un balisage brut à partir d'une chaîne.</summary>
      <returns>Tâche qui représente l'opération WriteRaw asynchrone.</returns>
      <param name="data">Chaîne contenant le texte à écrire.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String)">
      <summary>Écrit le début d'un attribut avec le nom local spécifié.</summary>
      <param name="localName">Le nom local de l'attribut.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)">
      <summary>Écrit le début d'un attribut avec le nom local et l'URI de l'espace de noms spécifiés.</summary>
      <param name="localName">Le nom local de l'attribut.</param>
      <param name="ns">L'URI de l'espace de noms de l'attribut.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit le début d'un attribut avec le préfixe, le nom local et l'URI de l'espace de noms spécifiés.</summary>
      <param name="prefix">Préfixe de l'espace de noms de cet attribut.</param>
      <param name="localName">Le nom local de l'attribut.</param>
      <param name="ns">URI d'espace de noms de cet attribut.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttributeAsync(System.String,System.String,System.String)">
      <summary>Écrit de façon asynchrone le début d'un attribut avec le préfixe, le nom local et l'URI de l'espace de noms spécifiés.</summary>
      <returns>Tâche qui représente l'opération WriteStartAttribute asynchrone.</returns>
      <param name="prefix">Préfixe de l'espace de noms de cet attribut.</param>
      <param name="localName">Le nom local de l'attribut.</param>
      <param name="ns">URI d'espace de noms de cet attribut.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument">
      <summary>En cas de substitution dans une classe dérivée, écrit la déclaration XML avec la version "1.0".</summary>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument(System.Boolean)">
      <summary>En cas de substitution dans une classe dérivée, écrit la déclaration XML avec la version "1.0" et l'attribut autonome.</summary>
      <param name="standalone">Si la valeur est true, elle écrit "standalone=yes"; si la valeur est false, elle écrit "standalone=no".</param>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync">
      <summary>Écrit de façon asynchrone la déclaration XML avec la version « 1.0 ».</summary>
      <returns>Tâche qui représente l'opération WriteStartDocument asynchrone.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync(System.Boolean)">
      <summary>Écrit de façon asynchrone la déclaration XML avec la version « 1.0 » et l'attribut autonome.</summary>
      <returns>Tâche qui représente l'opération WriteStartDocument asynchrone.</returns>
      <param name="standalone">Si la valeur est true, elle écrit "standalone=yes"; si la valeur est false, elle écrit "standalone=no".</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit une balise de début avec le nom local spécifié.</summary>
      <param name="localName">Le nom local de l'élément.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit la balise de début spécifiée et l'associe à l'espace de noms indiqué.</summary>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="ns">URI de l'espace de noms à associer à l'élément.Si cet espace de noms est déjà dans la portée et qu'il possède un préfixe associé, le writer écrit automatiquement ce préfixe également.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String,System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit la balise de début spécifiée, puis l'associe à l'espace de noms et au préfixe indiqués.</summary>
      <param name="prefix">Préfixe d'espace de noms de cet élément.</param>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="ns">URI de l'espace de noms à associer à l'élément.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElementAsync(System.String,System.String,System.String)">
      <summary>Écrit de façon asynchrone la balise de début indiquée et l'associe à l'espace de noms et au préfixe spécifiés.</summary>
      <returns>Tâche qui représente l'opération WriteStartElement asynchrone.</returns>
      <param name="prefix">Préfixe d'espace de noms de cet élément.</param>
      <param name="localName">Le nom local de l'élément.</param>
      <param name="ns">URI de l'espace de noms à associer à l'élément.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.WriteState">
      <summary>En cas de substitution dans une classe dérivée, obtient l'état du writer.</summary>
      <returns>Une des valeurs de <see cref="T:System.Xml.WriteState" />.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteString(System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit le texte spécifié.</summary>
      <param name="text">Texte à écrire.</param>
      <exception cref="T:System.ArgumentException">The text string contains an invalid surrogate pair.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStringAsync(System.String)">
      <summary>Écrit de façon asynchrone le texte spécifié.</summary>
      <returns>Tâche qui représente l'opération WriteString asynchrone.</returns>
      <param name="text">Texte à écrire.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntity(System.Char,System.Char)">
      <summary>En cas de substitution dans une classe dérivée, génère et écrit l'entité de caractère de substitution correspondant à la paire de caractères de substitution.</summary>
      <param name="lowChar">Substitut faible.Il doit s'agir d'une valeur comprise entre 0xDC00 et 0xDFFF.</param>
      <param name="highChar">Substitut étendu.Il doit s'agir d'une valeur comprise entre 0xD800 et 0xDBFF.</param>
      <exception cref="T:System.ArgumentException">An invalid surrogate character pair was passed.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntityAsync(System.Char,System.Char)">
      <summary>Génère de façon asynchrone et écrit l'entité de caractère de substitution correspondant à la paire de caractères de substitution.</summary>
      <returns>Tâche qui représente l'opération WriteSurrogateCharEntity asynchrone.</returns>
      <param name="lowChar">Substitut faible.Il doit s'agir d'une valeur comprise entre 0xDC00 et 0xDFFF.</param>
      <param name="highChar">Substitut étendu.Il doit s'agir d'une valeur comprise entre 0xD800 et 0xDBFF.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Boolean)">
      <summary>Écrit une valeur <see cref="T:System.Boolean" />.</summary>
      <param name="value">Valeur <see cref="T:System.Boolean" /> à écrire.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.DateTimeOffset)">
      <summary>Écrit une valeur <see cref="T:System.DateTimeOffset" />.</summary>
      <param name="value">Valeur <see cref="T:System.DateTimeOffset" /> à écrire.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Decimal)">
      <summary>Écrit une valeur <see cref="T:System.Decimal" />.</summary>
      <param name="value">Valeur <see cref="T:System.Decimal" /> à écrire.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Double)">
      <summary>Écrit une valeur <see cref="T:System.Double" />.</summary>
      <param name="value">Valeur <see cref="T:System.Double" /> à écrire.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int32)">
      <summary>Écrit une valeur <see cref="T:System.Int32" />.</summary>
      <param name="value">Valeur <see cref="T:System.Int32" /> à écrire.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int64)">
      <summary>Écrit une valeur <see cref="T:System.Int64" />.</summary>
      <param name="value">Valeur <see cref="T:System.Int64" /> à écrire.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Object)">
      <summary>Écrit la valeur de l'objet.</summary>
      <param name="value">Valeur de l'objet à écrire.Remarque   Avec le .NET Framework version 3.5, cette méthode accepte <see cref="T:System.DateTimeOffset" /> en tant que paramètre.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Single)">
      <summary>Écrit un nombre à virgule flottante simple précision.</summary>
      <param name="value">Nombre à virgule flottante simple précision à écrire.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.String)">
      <summary>Écrit une valeur <see cref="T:System.String" />.</summary>
      <param name="value">Valeur <see cref="T:System.String" /> à écrire.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespace(System.String)">
      <summary>En cas de substitution dans une classe dérivée, écrit l'espace blanc spécifié.</summary>
      <param name="ws">Chaîne d'espaces blancs.</param>
      <exception cref="T:System.ArgumentException">The string contains non-white space characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespaceAsync(System.String)">
      <summary>Écrit de façon asynchrone l'espace blanc spécifié.</summary>
      <returns>Tâche qui représente l'opération WriteWhitespace asynchrone.</returns>
      <param name="ws">Chaîne d'espaces blancs.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlLang">
      <summary>En cas de substitution dans une classe dérivée, obtient la portée xml:lang actuelle.</summary>
      <returns>Portée xml:lang actuelle.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlSpace">
      <summary>En cas de substitution dans une classe dérivée, obtient <see cref="T:System.Xml.XmlSpace" /> représentant la portée xml:spaceactuelle.</summary>
      <returns>Obtient un XmlSpace représentant la portée xml:space actuelle.Valeur Signification NoneValeur par défaut si aucune portée xml:space n'existe.DefaultLa portée actuelle est xml:space="default".PreserveLa portée actuelle est xml:space="preserve".</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="T:System.Xml.XmlWriterSettings">
      <summary>Spécifie un jeu de fonctionnalités à prendre en charge sur l'objet <see cref="T:System.Xml.XmlWriter" /> créé par la méthode <see cref="Overload:System.Xml.XmlWriter.Create" />.</summary>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Async">
      <summary>Obtient ou définit une valeur qui indique si les méthodes <see cref="T:System.Xml.XmlWriter" /> asynchrones peuvent être utilisées sur une instance <see cref="T:System.Xml.XmlWriter" /> particulière.</summary>
      <returns>true si des méthodes asynchrones peuvent être utilisées ; sinon, false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CheckCharacters">
      <summary>Obtient ou définit une valeur qui indique si le writer XML doit vérifier que tous les caractères du document sont conformes à la section « 2.2 Characters » de la W3C XML 1.0 Recommendation..</summary>
      <returns>true pour assurer la vérification des caractères ; sinon, false.La valeur par défaut est true.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Clone">
      <summary>Crée une copie de l'instance de <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Objet <see cref="T:System.Xml.XmlWriterSettings" /> cloné.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CloseOutput">
      <summary>Obtient ou définit une valeur indiquant si <see cref="T:System.Xml.XmlWriter" /> doit également fermer le flux sous-jacent ou <see cref="T:System.IO.TextWriter" /> quand la méthode <see cref="M:System.Xml.XmlWriter.Close" /> est appelée.</summary>
      <returns>true pour également fermer le flux sous-jacent ou <see cref="T:System.IO.TextWriter" /> ; sinon, false.La valeur par défaut est false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.ConformanceLevel">
      <summary>Obtient ou définit le niveau de conformité de vérification de sortie XML du writer XML.</summary>
      <returns>Une des valeurs d'énumération qui spécifie le niveau de conformité (document, fragment ou détection automatique).La valeur par défaut est <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Encoding">
      <summary>Obtient ou définit le type d'encodage de texte à utiliser.</summary>
      <returns>Encodage de texte à utiliser.La valeur par défaut est Encoding.UTF8.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Indent">
      <summary>Obtient ou définit une valeur indiquant si les éléments doivent être mis en retrait.</summary>
      <returns>true pour écrire des éléments individuels sur de nouvelles lignes et les mettre en retrait ; sinon, false.La valeur par défaut est false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.IndentChars">
      <summary>Obtient ou définit la chaîne de caractères à utiliser au moment de la mise en retrait.Ce paramètre est utilisé quand la propriété <see cref="P:System.Xml.XmlWriterSettings.Indent" /> a la valeur true.</summary>
      <returns>Chaîne de caractères à utiliser au moment de la mise en retrait.Elle peut avoir n'importe quelle valeur de chaîne.Toutefois, pour garantir la validité du XML, vous devez spécifier uniquement des caractères d'espace blanc valides, tels que les espaces, les tabulations, les retours chariot ou les sauts de ligne.Par défaut, il s'agit de deux espaces.</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.IndentChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NamespaceHandling">
      <summary>Obtient ou définit une valeur qui indique si <see cref="T:System.Xml.XmlWriter" /> doit supprimer des déclarations d'espace de noms en double pendant l'écriture du contenu XML.Le comportement par défaut consiste pour le writer à générer la sortie de toutes les déclarations d'espace de noms qui sont présentes dans le programme de résolution d'espace de noms du writer.</summary>
      <returns>L'énumération <see cref="T:System.Xml.NamespaceHandling" /> utilisée pour spécifier s'il faut supprimer les déclarations d'espace de noms en double dans le <see cref="T:System.Xml.XmlWriter" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineChars">
      <summary>Obtient ou définit la chaîne de caractères à utiliser pour les sauts de ligne.</summary>
      <returns>Chaîne de caractères à utiliser pour les sauts de ligne.Elle peut avoir n'importe quelle valeur de chaîne.Toutefois, pour garantir la validité du XML, vous devez spécifier uniquement des caractères d'espace blanc valides, tels que les espaces, les tabulations, les retours chariot ou les sauts de ligne.La valeur par défaut est \r\n (retour chariot, nouvelle ligne).</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineHandling">
      <summary>Obtient ou définit une valeur indiquant s'il convient de normaliser des sauts de ligne dans la sortie.</summary>
      <returns>Une des valeurs de <see cref="T:System.Xml.NewLineHandling" />.La valeur par défaut est <see cref="F:System.Xml.NewLineHandling.Replace" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineOnAttributes">
      <summary>Obtient ou définit une valeur indiquant s'il convient d'écrire des attributs sur une nouvelle ligne.</summary>
      <returns>true pour écrire des attributs sur des lignes ; sinon, false.La valeur par défaut est false.RemarqueCe paramètre n'a aucun effet si la propriété <see cref="P:System.Xml.XmlWriterSettings.Indent" /> a la valeur false.Quand <see cref="P:System.Xml.XmlWriterSettings.NewLineOnAttributes" /> a la valeur true, chaque attribut est ajouté avec une nouvelle ligne et un niveau supplémentaire de mise en retrait.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.OmitXmlDeclaration">
      <summary>Obtient ou définit une valeur indiquant si une déclaration XML doit être omise.</summary>
      <returns>true pour omettre la déclaration XML ; sinon, false.La valeur par défaut est false, une déclaration XML est écrite.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Reset">
      <summary>Réinitialise les membres de la classe de paramètres à leurs valeurs par défaut.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.WriteEndDocumentOnClose">
      <summary>Obtient ou définit une valeur qui indique si <see cref="T:System.Xml.XmlWriter" /> ajoutera des indicateurs de fermeture à tous les indicateurs d'éléments non fermés quand la méthode <see cref="M:System.Xml.XmlWriter.Close" /> est appelée.</summary>
      <returns>true si toutes les balises d'élément non fermées seront fermées ; sinon, false.La valeur par défaut est true.</returns>
    </member>
    <member name="T:System.Xml.Schema.XmlSchema">
      <summary>Représentation en mémoire d'un schéma XML, comme spécifié dans les spécifications XML Schema Part 1: Structures et XML Schema Part 2: Datatypes du World Wide Web Consortium (W3C).</summary>
    </member>
    <member name="T:System.Xml.Schema.XmlSchemaForm">
      <summary>Indique si les attributs ou les éléments doivent être qualifiés à l'aide d'un préfixe d'espace de noms.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.None">
      <summary>Aucune forme d'élément et d'attribut n'est spécifiée dans le schéma.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Qualified">
      <summary>Les éléments et les attributs doivent être qualifiés à l'aide d'un préfixe d'espace de noms.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Unqualified">
      <summary>Les éléments et les attributs ne doivent pas obligatoirement être qualifiés à l'aide d'un préfixe d'espace de noms.</summary>
    </member>
    <member name="T:System.Xml.Serialization.IXmlSerializable">
      <summary>Offre une mise en forme personnalisée pour la sérialisation et la désérialisation XML.</summary>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.GetSchema">
      <summary>Cette méthode est réservée et ne doit pas être utilisée.Lorsque vous implémentez l'interface IXmlSerializable, vous devez retourner la valeur null (Nothing dans Visual Basic) à partir cette méthode et, si la spécification d'un schéma personnalisé est requise, appliquez à la place <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> à la classe.</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchema" /> qui décrit la représentation XML de l'objet qui est généré par la méthode <see cref="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)" /> et utilisé par la méthode <see cref="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)">
      <summary>Génère un objet à partir de sa représentation XML.</summary>
      <param name="reader">
        <see cref="T:System.Xml.XmlReader" /> source à partir de laquelle l'objet est désérialisé. </param>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)">
      <summary>Convertit un objet en sa représentation XML.</summary>
      <param name="writer">
        <see cref="T:System.Xml.XmlWriter" /> flux dans lequel l'objet est sérialisé. </param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaProviderAttribute">
      <summary>Dans le cadre d'une application à un type, stocke le nom d'une méthode statique du type qui retourne un schéma XML et un <see cref="T:System.Xml.XmlQualifiedName" /> (ou <see cref="T:System.Xml.Schema.XmlSchemaType" /> pour les types anonymes) qui contrôle la sérialisation du type.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaProviderAttribute.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" />, en acceptant le nom de la méthode statique qui fournit le schéma XML du type.</summary>
      <param name="methodName">Nom de la méthode statique qui doit être implémentée.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.IsAny">
      <summary>Obtient ou définit une valeur qui détermine si la classe cible est un caractère générique, ou que le schéma pour la classe contient uniquement un élément xs:any.</summary>
      <returns>true si la classe est un caractère générique ou si le schéma contient uniquement l'élément xs:any ; sinon, false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.MethodName">
      <summary>Obtient le nom de la méthode statique qui fournit le schéma XML du type et le nom de son type de données de schéma XML.</summary>
      <returns>Nom de la méthode qui est appelée par l'infrastructure XML pour retourner un schéma XML.</returns>
    </member>
  </members>
</doc>