<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="Best_Sender.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <applicationSettings>
    <DevExpress.LookAndFeel.Design.AppSettings>
      <setting name="DefaultAppSkin" serializeAs="String">
        <value>Skin/WXI</value>
      </setting>
      <setting name="DefaultPalette" serializeAs="String">
        <value>Sharpness</value>
      </setting>
      <setting name="TouchUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="CompactUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchScaleFactor" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DirectX" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterUserSkins" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterBonusSkins" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="FontBehavior" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DefaultAppFont" serializeAs="String">
        <value>Comfortaa;8.249999</value>
      </setting>
      <setting name="DPIAwarenessMode" serializeAs="String">
        <value></value>
      </setting>
      <setting name="CustomPaletteCollection" serializeAs="Xml">
        <value />
      </setting>
    </DevExpress.LookAndFeel.Design.AppSettings>
  </applicationSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2" />
  </startup>
  <userSettings>
    <Best_Sender.My.MySettings>
      <setting name="prefixes" serializeAs="String">
        <value>KS|Kosovo|+381
AL|Albania|+355
AF|Afghanistan|+93
GB|UK|+44
US|USA|+1
DZ|Algeria|+213
AD|Andorra|+376
AO|Angola|+244
AI|Anguilla|+1264
AG|Antigua&amp;Barbuda|+1268
AR|Argentina|+54
AM|Armenia|+374
AW|Aruba|+297
AU|Australia|+61
AT|Austria|+43
AZ|Azerbaijan|+994
BS|Bahamas|+1242
BH|Bahrain|+973
BD|Bangladesh|+880
BB|Barbados|+1246
BY|Belarus|+375
BE|Belgium|+32
BZ|Belize|+501
BJ|Benin|+229
BM|Bermuda|+1441
BT|Bhutan|+975
BO|Bolivia|+591
BA|BosniaHerzegovina|+387
BW|Botswana|+267
BR|Brazil|+55
BN|Brunei|+673
BG|Bulgaria|+359
BF|BurkinaFaso|+226
BI|Burundi|+257
KH|Cambodia|+855
CM|Cameroon|+237
CA|Canada|+1
CV|CapeVerdeIslands|+238
KY|CaymanIslands|+1345
CF|CentralAfricanRepublic|+236
CL|Chile|+56
CN|China|+86
CO|Colombia|+57
KM|Comoros|+269
CG|Congo|+242
CK|CookIslands|+682
CR|CostaRica|+506
HR|Croatia|+385
CU|Cuba|+53
CY|CyprusNorth|+90392
CY|CyprusSouth|+357
CZ|CzechRepublic|+42
DK|Denmark|+45
DJ|Djibouti|+253
DM|Dominica|+1809
DO|DominicanRepublic|+1809
EC|Ecuador|+593
EG|Egypt|+20
SV|ElSalvador|+503
GQ|EquatorialGuinea|+240
ER|Eritrea|+291
EE|Estonia|+372
ET|Ethiopia|+251
FK|FalklandIslands|+500
FO|FaroeIslands|+298
FJ|Fiji|+679
FI|Finland|+358
FR|France|+33
GF|FrenchGuiana|+594
PF|FrenchPolynesia|+689
GA|Gabon|+241
GM|Gambia|+220
GE|Georgia|+7880
DE|Germany|+49
GH|Ghana|+233
GI|Gibraltar|+350
GR|Greece|+30
GL|Greenland|+299
GD|Grenada|+1473
GP|Guadeloupe|+590
GU|Guam|+671
GT|Guatemala|+502
GN|Guinea|+224
GW|Guinea-Bissau|+245
GY|Guyana|+592
HT|Haiti|+509
HN|Honduras|+504
HK|HongKong|+852
HU|Hungary|+36
IS|Iceland|+354
IN|India|+91
ID|Indonesia|+62
IR|Iran|+98
IQ|Iraq|+964
IE|Ireland|+353
IL|Israel|+972
IT|Italy|+39
JM|Jamaica|+1876
JP|Japan|+81
JO|Jordan|+962
KZ|Kazakhstan|+7
KE|Kenya|+254
KI|Kiribati|+686
KP|KoreaNorth|+850
KR|KoreaSouth|+82
KW|Kuwait|+965
KG|Kyrgyzstan|+996
LA|Laos|+856
LV|Latvia|+371
LB|Lebanon|+961
LS|Lesotho|+266
LR|Liberia|+231
LY|Libya|+218
LI|Liechtenstein|+417
LT|Lithuania|+370
LU|Luxembourg|+352
MO|Macao|+853
MK|Macedonia|+389
MG|Madagascar|+261
MW|Malawi|+265
MY|Malaysia|+60
MV|Maldives|+960
ML|Mali|+223
MT|Malta|+356
MH|MarshallIslands|+692
MQ|Martinique|+596
MR|Mauritania|+222
YT|Mayotte|+269
MX|Mexico|+52
FM|Micronesia|+691
MD|Moldova|+373
MC|Monaco|+377
MN|Mongolia|+976
ME|Montenegro|+382
MS|Montserrat|+1664
MA|Morocco|+212
MZ|Mozambique|+258
MN|Myanmar|+95
NA|Namibia|+264
NR|Nauru|+674
NP|Nepal|+977
NL|Netherlands|+31
NC|NewCaledonia|+687
NZ|NewZealand|+64
NI|Nicaragua|+505
NE|Niger|+227
NG|Nigeria|+234
NU|Niue|+683
NF|NorfolkIslands|+672
NP|NorthernMarianas|+670
NO|Norway|+47
OM|Oman|+968
PW|Palau|+680
PA|Panama|+507
PG|PapuaNewGuinea|+675
PY|Paraguay|+595
PE|Peru|+51
PH|Philippines|+63
PL|Poland|+48
PT|Portugal|+351
PR|PuertoRico|+1787
QA|Qatar|+974
RE|Reunion|+262
RO|Romania|+40
RS|Serbia|+381
RU|Russia|+7
RW|Rwanda|+250
SM|SanMarino|+378
ST|SaoTome&amp;Principe|+239
SA|SaudiArabia|+966
SN|Senegal|+221
CS|Serbia|+381
SC|Seychelles|+248
SL|SierraLeone|+232
SG|Singapore|+65
SK|SlovakRepublic|+421
SI|Slovenia|+386
SB|SolomonIslands|+677
SO|Somalia|+252
ZA|SouthAfrica|+27
ES|Spain|+34
LK|SriLanka|+94
SH|St.Helena|+290
KN|St.Kitts|+1869
SC|St.Lucia|+1758
SD|Sudan|+249
SR|Suriname|+597
SZ|Swaziland|+268
SE|Sweden|+46
CH|Switzerland|+41
SI|Syria|+963
TW|Taiwan|+886
TJ|Tajikstan|+7
TH|Thailand|+66
TG|Togo|+228
TO|Tonga|+676
TT|Trinidad&amp;Tobago|+1868
TN|Tunisia|+216
TR|Turkey|+90
TM|Turkmenistan|+7
TM|Turkmenistan|+993
TC|Turks&amp;CaicosIslands|+1649
TV|Tuvalu|+688
UG|Uganda|+256
GB|SelectedUK|+44
UA|Ukraine|+380
AE|UnitedArabEmirates|+971
UY|Uruguay|+598
UZ|Uzbekistan|+7
VU|Vanuatu|+678
VA|VaticanCity|+379
VE|Venezuela|+58
VN|Vietnam|+84
VG|VirginIslands-British|+1284
VI|VirginIslands-US|+1340
WF|Wallis&amp;Futuna|+681
YE|YemenNorth|+969
YE|YemenSouth|+967
ZM|Zambia|+260
ZW|Zimbabwe|+263</value>
      </setting>
      <setting name="MailSorterPath" serializeAs="String">
        <value />
      </setting>
      <setting name="Chk_MailSorterPath" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="appver" serializeAs="String">
        <value>Data Source=SQL5091.site4now.net;Initial Catalog=db_a8d9ed_bestsender;User Id=db_a8d9ed_bestsender_admin;Password=220Aa0154</value>
      </setting>
      <setting name="hastring" serializeAs="String">
        <value>hkhfhfhdgh</value>
      </setting>
      <setting name="appname" serializeAs="String">
        <value>Best Sender</value>
      </setting>
      <setting name="PROSTRING" serializeAs="String">
        <value />
      </setting>
      <setting name="Fakeuser" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="TrainWarning" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="Setting" serializeAs="String">
        <value />
      </setting>
      <setting name="Host" serializeAs="String">
        <value />
      </setting>
      <setting name="Email" serializeAs="String">
        <value />
      </setting>
      <setting name="Password" serializeAs="String">
        <value />
      </setting>
      <setting name="Port" serializeAs="String">
        <value />
      </setting>
      <setting name="FromName" serializeAs="String">
        <value />
      </setting>
      <setting name="txtSubject" serializeAs="String">
        <value />
      </setting>
      <setting name="SMTP_DATA_TABLE_XML" serializeAs="String">
        <value />
      </setting>
      <setting name="EMAILS_DATA_TABLE_XML" serializeAs="String">
        <value />
      </setting>
      <setting name="DmPath" serializeAs="String">
        <value />
      </setting>
      <setting name="ExtPath" serializeAs="String">
        <value />
      </setting>
      <setting name="TrcThread" serializeAs="String">
        <value>1</value>
      </setting>
      <setting name="LETTER" serializeAs="String">
        <value />
      </setting>
      <setting name="LETTER_LINK" serializeAs="String">
        <value />
      </setting>
      <setting name="LETTER_CON_LINK" serializeAs="String">
        <value />
      </setting>
      <setting name="SETTING_MDF" serializeAs="String">
        <value />
      </setting>
      <setting name="Link" serializeAs="String">
        <value />
      </setting>
      <setting name="Tag" serializeAs="String">
        <value />
      </setting>
      <setting name="Type" serializeAs="String">
        <value />
      </setting>
      <setting name="Logo_URL" serializeAs="String">
        <value />
      </setting>
      <setting name="Width" serializeAs="String">
        <value />
      </setting>
      <setting name="Length" serializeAs="String">
        <value />
      </setting>
      <setting name="LinkGoogleCaptcha" serializeAs="String">
        <value />
      </setting>
      <setting name="TypeGoogleCaptcha" serializeAs="String">
        <value />
      </setting>
      <setting name="SiteKey" serializeAs="String">
        <value />
      </setting>
      <setting name="URL_CloudFlare" serializeAs="String">
        <value />
      </setting>
      <setting name="Link_Logo_CludFlare" serializeAs="String">
        <value />
      </setting>
      <setting name="Type_CloudFlare" serializeAs="String">
        <value />
      </setting>
      <setting name="Width_CloudFlare" serializeAs="String">
        <value />
      </setting>
      <setting name="Length_CloudFlare" serializeAs="String">
        <value />
      </setting>
      <setting name="word" serializeAs="String">
        <value />
      </setting>
      <setting name="txt_Logo_URL_VIP" serializeAs="String">
        <value />
      </setting>
      <setting name="txt_word_VIP" serializeAs="String">
        <value />
      </setting>
      <setting name="txt_type_VIP" serializeAs="String">
        <value />
      </setting>
      <setting name="txt_Link_VIP" serializeAs="String">
        <value />
      </setting>
      <setting name="txt_Link_Puzzle" serializeAs="String">
        <value />
      </setting>
      <setting name="txt_type_Puzzle" serializeAs="String">
        <value />
      </setting>
      <setting name="Logo_Page" serializeAs="String">
        <value />
      </setting>
      <setting name="Logo_Button" serializeAs="String">
        <value />
      </setting>
      <setting name="Link_New" serializeAs="String">
        <value />
      </setting>
      <setting name="Button_txt" serializeAs="String">
        <value />
      </setting>
      <setting name="Outlook_txt" serializeAs="String">
        <value />
      </setting>
      <setting name="Type_txt" serializeAs="String">
        <value />
      </setting>
      <setting name="Link_txt" serializeAs="String">
        <value />
      </setting>
      <setting name="TextBox2LinkPage" serializeAs="String">
        <value />
      </setting>
      <setting name="TextBox1MaskLink" serializeAs="String">
        <value />
      </setting>
      <setting name="LogoBKAuthentication" serializeAs="String">
        <value />
      </setting>
      <setting name="LogoUp" serializeAs="String">
        <value />
      </setting>
      <setting name="txtWord" serializeAs="String">
        <value />
      </setting>
      <setting name="txtPage" serializeAs="String">
        <value />
      </setting>
      <setting name="URLAuthentication" serializeAs="String">
        <value />
      </setting>
      <setting name="txtLetter" serializeAs="String">
        <value />
      </setting>
      <setting name="Title" serializeAs="String">
        <value />
      </setting>
      <setting name="Logo" serializeAs="String">
        <value />
      </setting>
      <setting name="FontSizeMessage" serializeAs="String">
        <value />
      </setting>
      <setting name="FontTitle" serializeAs="String">
        <value />
      </setting>
      <setting name="ButtonColor" serializeAs="String">
        <value />
      </setting>
      <setting name="TextColor" serializeAs="String">
        <value />
      </setting>
      <setting name="TitleColor" serializeAs="String">
        <value />
      </setting>
      <setting name="Message" serializeAs="String">
        <value />
      </setting>
      <setting name="ButtonName" serializeAs="String">
        <value />
      </setting>
      <setting name="Signature" serializeAs="String">
        <value />
      </setting>
      <setting name="TextBoxLogoLM" serializeAs="String">
        <value />
      </setting>
      <setting name="TextBoxLinklk" serializeAs="String">
        <value />
      </setting>
      <setting name="txtHttps" serializeAs="String">
        <value />
      </setting>
      <setting name="txtmyLinkURL" serializeAs="String">
        <value />
      </setting>
      <setting name="txtLinkEncodedWolf" serializeAs="String">
        <value />
      </setting>
      <setting name="TitelPass" serializeAs="String">
        <value />
      </setting>
      <setting name="LogoPass" serializeAs="String">
        <value />
      </setting>
      <setting name="PagePass1" serializeAs="String">
        <value />
      </setting>
      <setting name="PagePass2" serializeAs="String">
        <value />
      </setting>
      <setting name="LinkPass" serializeAs="String">
        <value />
      </setting>
      <setting name="EmailTagPass" serializeAs="String">
        <value />
      </setting>
      <setting name="TextBoxpassword" serializeAs="String">
        <value />
      </setting>
      <setting name="UploadCpanel" serializeAs="String">
        <value />
      </setting>
      <setting name="LinkPageCpanel" serializeAs="String">
        <value />
      </setting>
      <setting name="LinkEncodeCpanel" serializeAs="String">
        <value />
      </setting>
    </Best_Sender.My.MySettings>
  </userSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Reflection.TypeExtensions" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.0" newVersion="4.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.4.0" newVersion="4.1.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="HtmlAgilityPack" publicKeyToken="bd319b19eaf3b43a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.11.38.0" newVersion="1.11.38.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="zxing" publicKeyToken="4e88037ac681fe60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-0.16.9.0" newVersion="0.16.9.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="IronSoftware.Drawing.Common" publicKeyToken="8d7e55c97b3e9835" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2024.12.0.3" newVersion="2024.12.0.3" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.1" newVersion="6.0.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Permissions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Collections" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>