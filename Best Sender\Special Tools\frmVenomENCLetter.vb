Imports System.IO
Imports System.Text.RegularExpressions
Imports System.Text
Imports DevExpress.XtraEditors
Imports System.Diagnostics

''' <summary>
''' فورم تشفير الرسائل مع تنظيف &nbsp; entities
''' يقوم بحذف جميع أشكال &nbsp; قبل التشفير لضمان نظافة الكود
''' </summary>
Public Class frmVenomENCLetter
    Dim loadedHtml As String = ""
    Dim cssBlocks As New List(Of String)
    Dim dynamicWords As New List(Of String) From {
        "[-Email-]", "[-IP-]", "[-IPChina-]", "[-Domain-]", "[-Name-]", "[-Date-]", "[-DateTomorrow-]",
        "[-RandomBrowser-]", "[-RCountry-]", "[-FakePhone-]", "[-FakeEmail-]", "[-NewYork-]", "[-UCase-]",
        "[-Link-]", "[-Logo-]", "[-QRCode-]", "[-RN1-]", "[-RN2-]", "[-RN3-]", "[-RN4-]", "[-RN5-]", "[-RN6-]",
        "[-RN7-]", "[-RN8-]", "[-RN9-]", "[-RN10-]", "[-RN12-]", "[-RCh1-]", "[-RCh2-]", "[-RCh3-]", "[-RCh4-]",
        "[-RCh5-]", "[-RCh6-]", "[-RCh7-]", "[-RCh8-]"
    }
    Private Function EncryptText(text As String) As String
        ' تحميل الحرف من ملف الموارد
        Dim rtlMark As String = My.Resources._1.Replace(vbCrLf, "").Replace(vbLf, "").Replace(vbCr, "")
        ' استخدام StringBuilder لتحسين الأداء
        Dim encryptedText As New StringBuilder()
        For Each c As Char In text
            If Char.IsLetterOrDigit(c) Then ' تشفير الحروف فقط
                encryptedText.Append(c).Append(rtlMark)
            ElseIf Char.IsWhiteSpace(c) Then ' الحفاظ على المسافات كما هي
                encryptedText.Append(c)
            Else
                encryptedText.Append(c) ' ترك الرموز كما هي
            End If
        Next
        Return encryptedText.ToString()
    End Function

    ''' <summary>
    ''' حذف جميع أشكال &nbsp; من النص HTML
    ''' </summary>
    ''' <param name="html">النص HTML المراد تنظيفه</param>
    ''' <returns>النص بعد حذف جميع أشكال &nbsp;</returns>
    Private Function CleanNbspEntities(html As String) As String
        If String.IsNullOrEmpty(html) Then
            Return html
        End If

        Try
            Dim cleanedHtml As String = html

            ' 🧹 حذف &nbsp; العادي
            cleanedHtml = cleanedHtml.Replace("&nbsp;", "")

            ' 🧹 حذف &nbsp; مع أحرف غير مرئية (الحالة المذكورة تحديداً)
            cleanedHtml = cleanedHtml.Replace("&n‏‏b‏‏s‏‏p‏‏;", "")

            ' 🧹 حذف جميع أشكال &nbsp; بأحرف كبيرة وصغيرة
            Dim nbspVariations() As String = {
                "&NBSP;", "&Nbsp;", "&nBsp;", "&nbSp;", "&nbsP;",
                "&NBsp;", "&NbSp;", "&NbsP;", "&nBSp;", "&nBsP;", "&nbSP;"
            }

            For Each variation As String In nbspVariations
                cleanedHtml = cleanedHtml.Replace(variation, "")
            Next

            ' 🧹 استخدام Regex لحذف أي شكل من &nbsp; مع أحرف غير مرئية أو تحكم
            ' هذا النمط يبحث عن & متبوعة بـ n مع أي أحرف غير مرئية ثم b s p ;
            Dim patterns() As String = {
                "&n[\u200B-\u200F\u202A-\u202E\u2060-\u206F]*b[\u200B-\u200F\u202A-\u202E\u2060-\u206F]*s[\u200B-\u200F\u202A-\u202E\u2060-\u206F]*p[\u200B-\u200F\u202A-\u202E\u2060-\u206F]*;",
                "&n[^\w\s]*b[^\w\s]*s[^\w\s]*p[^\w\s]*;",
                "&\s*n\s*b\s*s\s*p\s*;",
                "&#160;", "&#xa0;", "&#xA0;"
            }

            For Each pattern As String In patterns
                cleanedHtml = Regex.Replace(cleanedHtml, pattern, "", RegexOptions.IgnoreCase)
            Next

            ' 🧹 حذف أحرف التحكم والأحرف غير المرئية الشائعة
            Dim controlChars() As String = {
                "\u200B", "\u200C", "\u200D", "\u200E", "\u200F",
                "\u202A", "\u202B", "\u202C", "\u202D", "\u202E",
                "\u2060", "\u2061", "\u2062", "\u2063", "\u206A",
                "\u206B", "\u206C", "\u206D", "\u206E", "\u206F",
                "\uFEFF"
            }

            For Each controlChar As String In controlChars
                cleanedHtml = cleanedHtml.Replace(controlChar, "")
            Next

            ' 🧹 تنظيف المسافات الزائدة (لكن بحذر للحفاظ على تنسيق HTML)
            ' حذف المسافات الزائدة فقط داخل النصوص وليس في الوسوم
            cleanedHtml = Regex.Replace(cleanedHtml, ">(\s+)<", "><", RegexOptions.Multiline)

            Return cleanedHtml

        Catch ex As Exception
            ' في حالة حدوث خطأ، إرجاع النص الأصلي مع تسجيل الخطأ
            Debug.WriteLine($"Error cleaning nbsp entities: {ex.Message}")
            Debug.WriteLine($"Stack trace: {ex.StackTrace}")
            Return html
        End Try
    End Function

    ''' <summary>
    ''' حساب عدد &nbsp; entities الموجودة في النص
    ''' </summary>
    ''' <param name="html">النص HTML المراد فحصه</param>
    ''' <returns>عدد &nbsp; entities الموجودة</returns>
    Private Function CountNbspEntities(html As String) As Integer
        If String.IsNullOrEmpty(html) Then
            Return 0
        End If

        Try
            Dim count As Integer = 0

            ' عد &nbsp; العادي
            count += CountOccurrences(html, "&nbsp;")

            ' عد &nbsp; مع أحرف غير مرئية
            count += CountOccurrences(html, "&n‏‏b‏‏s‏‏p‏‏;")

            ' عد أشكال أخرى من &nbsp;
            Dim nbspVariations() As String = {
                "&NBSP;", "&Nbsp;", "&nBsp;", "&nbSp;", "&nbsP;",
                "&NBsp;", "&NbSp;", "&NbsP;", "&nBSp;", "&nBsP;", "&nbSP;"
            }

            For Each variation As String In nbspVariations
                count += CountOccurrences(html, variation)
            Next

            ' عد الأشكال الرقمية
            count += CountOccurrences(html, "&#160;")
            count += CountOccurrences(html, "&#xa0;")
            count += CountOccurrences(html, "&#xA0;")

            ' استخدام Regex لعد الأشكال المعقدة
            Dim patterns() As String = {
                "&n[\u200B-\u200F\u202A-\u202E\u2060-\u206F]+b[\u200B-\u200F\u202A-\u202E\u2060-\u206F]*s[\u200B-\u200F\u202A-\u202E\u2060-\u206F]*p[\u200B-\u200F\u202A-\u202E\u2060-\u206F]*;",
                "&n[^\w\s]+b[^\w\s]*s[^\w\s]*p[^\w\s]*;"
            }

            For Each pattern As String In patterns
                Dim matches As MatchCollection = Regex.Matches(html, pattern, RegexOptions.IgnoreCase)
                count += matches.Count
            Next

            Return count

        Catch ex As Exception
            Debug.WriteLine($"Error counting nbsp entities: {ex.Message}")
            Return 0
        End Try
    End Function

    ''' <summary>
    ''' حساب عدد مرات تكرار نص معين في نص آخر
    ''' </summary>
    ''' <param name="text">النص المراد البحث فيه</param>
    ''' <param name="searchText">النص المراد البحث عنه</param>
    ''' <returns>عدد مرات التكرار</returns>
    Private Function CountOccurrences(text As String, searchText As String) As Integer
        If String.IsNullOrEmpty(text) OrElse String.IsNullOrEmpty(searchText) Then
            Return 0
        End If

        Dim count As Integer = 0
        Dim index As Integer = 0

        While index < text.Length
            index = text.IndexOf(searchText, index, StringComparison.OrdinalIgnoreCase)
            If index = -1 Then
                Exit While
            End If
            count += 1
            index += searchText.Length
        End While

        Return count
    End Function
    Private Sub frmVenomENCLetter_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' جعل النموذج غير مرئي أثناء تطبيق التصميم
        Me.Opacity = 0

        ' تعطيل زر الحفظ في البداية
        LetterEncoder_bntSave.Enabled = False

        ' تعيين عنوان النموذج
        Me.Text = "Venom Encoded Letter"

        ' تطبيق تصميم Binance مباشرة
        Try
            ApplyBinanceStyle()

            ' إضافة معالج حدث لتغيير حجم النموذج
            AddHandler Me.Resize, AddressOf frmVenomENCLetter_Resize

            ' وضع العناصر في المنتصف عند تحميل النموذج
            CenterControls()

            ' إضافة عنوان في أعلى النموذج
            AddHeaderImage()

            ' جعل النموذج مرئي بعد تطبيق التصميم
            Me.Opacity = 1
        Catch ex As Exception
            ' في حالة حدوث خطأ، جعل النموذج مرئي على أي حال
            Me.Opacity = 1
            Debug.WriteLine($"Error in frmVenomENCLetter_Load: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmVenomENCLetter_Resize(sender As Object, e As EventArgs)
        ' وضع العناصر في المنتصف عند تغيير حجم النموذج
        CenterControls()
    End Sub

    ''' <summary>
    ''' توسيط جميع العناصر في النموذج
    ''' </summary>
    Private Sub CenterControls()
        Try
            ' الحصول على حجم النموذج
            Dim formWidth As Integer = Me.ClientSize.Width
            Dim formHeight As Integer = Me.ClientSize.Height

            ' البحث عن HeaderPictureBox
            Dim headerPictureBox As PictureBox = Nothing
            For Each ctrl As Control In Me.Controls
                If TypeOf ctrl Is PictureBox AndAlso ctrl.Name = "HeaderPictureBox" Then
                    headerPictureBox = DirectCast(ctrl, PictureBox)
                    Exit For
                End If
            Next

            ' تنسيق أحجام GroupControl1 و GroupControl2
            Dim panelWidth As Integer = (formWidth - 40) / 2
            Dim panelHeight As Integer = formHeight - (If(headerPictureBox IsNot Nothing, headerPictureBox.Height, 0)) - 15

            ' تنسيق GroupControl1
            GroupControl1.Size = New Size(panelWidth, panelHeight)
            GroupControl1.Location = New Point(10, If(headerPictureBox IsNot Nothing, headerPictureBox.Bottom + 5, 10))

            ' تنسيق GroupControl2
            GroupControl2.Size = New Size(panelWidth, panelHeight)
            GroupControl2.Location = New Point(GroupControl1.Right + 20, If(headerPictureBox IsNot Nothing, headerPictureBox.Bottom + 5, 10))

            ' تنسيق LayoutControl1
            LayoutControl1.Dock = DockStyle.Fill

            ' تنسيق أحجام الأزرار
            LetterEncoder_bntBrowse.Size = New Size(100, 30)
            LetterEncoder_bntEncrypt.Size = New Size(100, 30)
            LetterEncoder_bntClearAll.Size = New Size(100, 30)
            LetterEncoder_bntSave.Size = New Size(100, 30)

            ' تنسيق LetterEncoder_txtFilepath
            LetterEncoder_txtFilepath.Size = New Size(300, LetterEncoder_txtFilepath.Height)

            Debug.WriteLine($"Controls resized. Form size: {formWidth}x{formHeight}")
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error resizing controls: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' إضافة صورة العنوان في أعلى النموذج
    ''' </summary>
    Private Sub AddHeaderImage()
        Try
            ' إنشاء PictureBox للصورة
            Dim headerPictureBox As New PictureBox()
            headerPictureBox.Name = "HeaderPictureBox"
            headerPictureBox.BackColor = Color.Transparent
            headerPictureBox.SizeMode = PictureBoxSizeMode.Zoom ' جعل الصورة قابلة للتكبير

            ' تحميل الصورة من الموارد
            Try
                headerPictureBox.Image = My.Resources.VenomENImage
                Debug.WriteLine("Image loaded from resources successfully")
            Catch ex As Exception
                Debug.WriteLine($"Error loading image from resources: {ex.Message}")
                ' إذا لم يتم العثور على الصورة، نستخدم صورة افتراضية
                headerPictureBox.BackColor = Color.FromArgb(13, 13, 13)
                headerPictureBox.Image = Nothing
            End Try

            ' تعيين حجم وموقع الصورة
            Dim imageHeight As Integer = 50
            headerPictureBox.Size = New Size(200, imageHeight)
            headerPictureBox.Location = New Point((Me.ClientSize.Width - headerPictureBox.Width) \ 2, 0)
            headerPictureBox.Dock = DockStyle.Top ' جعل الصورة في أعلى النموذج

            ' إضافة PictureBox إلى النموذج
            Me.Controls.Add(headerPictureBox)
            headerPictureBox.BringToFront()

            Debug.WriteLine("Header image added successfully")
        Catch ex As Exception
            Debug.WriteLine($"Error adding header image: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على النموذج
    ''' </summary>
    Private Sub ApplyBinanceStyle()
        Try
            ' تعيين لون خلفية النموذج - لون Binance الأساسي الداكن
            Me.BackColor = Color.FromArgb(13, 13, 13) ' لون أكثر قتامة مثل Binance

            ' تنسيق LayoutControl1
            LayoutControl1.BackColor = Color.FromArgb(13, 13, 13)
            LayoutControl1.ForeColor = Color.White

            ' تنسيق GroupControl1
            GroupControl1.Appearance.BackColor = Color.FromArgb(22, 26, 30) ' لون Binance للبطاقات
            GroupControl1.Appearance.BorderColor = Color.FromArgb(30, 35, 40) ' حدود رمادية داكنة
            GroupControl1.Appearance.ForeColor = Color.White
            GroupControl1.Appearance.Options.UseBackColor = True
            GroupControl1.Appearance.Options.UseBorderColor = True
            GroupControl1.Appearance.Options.UseForeColor = True

            GroupControl1.AppearanceCaption.BackColor = Color.FromArgb(22, 26, 30)
            GroupControl1.AppearanceCaption.BorderColor = Color.FromArgb(30, 35, 40)
            GroupControl1.AppearanceCaption.ForeColor = Color.White
            GroupControl1.AppearanceCaption.Options.UseBackColor = True
            GroupControl1.AppearanceCaption.Options.UseBorderColor = True
            GroupControl1.AppearanceCaption.Options.UseForeColor = True
            GroupControl1.Text = "Normal"

            ' تنسيق GroupControl2
            GroupControl2.Appearance.BackColor = Color.FromArgb(22, 26, 30) ' لون Binance للبطاقات
            GroupControl2.Appearance.BorderColor = Color.FromArgb(30, 35, 40) ' حدود رمادية داكنة
            GroupControl2.Appearance.ForeColor = Color.White
            GroupControl2.Appearance.Options.UseBackColor = True
            GroupControl2.Appearance.Options.UseBorderColor = True
            GroupControl2.Appearance.Options.UseForeColor = True

            GroupControl2.AppearanceCaption.BackColor = Color.FromArgb(22, 26, 30)
            GroupControl2.AppearanceCaption.BorderColor = Color.FromArgb(30, 35, 40)
            GroupControl2.AppearanceCaption.ForeColor = Color.White
            GroupControl2.AppearanceCaption.Options.UseBackColor = True
            GroupControl2.AppearanceCaption.Options.UseBorderColor = True
            GroupControl2.AppearanceCaption.Options.UseForeColor = True
            GroupControl2.Text = "Encoded"

            ' تنسيق LetterEncoder_Richtext - مثل حقول الإدخال في Binance
            LetterEncoder_Richtext.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_Richtext.ForeColor = Color.White
            LetterEncoder_Richtext.BorderStyle = BorderStyle.None
            LetterEncoder_Richtext.Font = New Font("Segoe UI", 9.5F)

            ' تنسيق LetterEncoder_Richtext2 - مع لون النص الأصفر المميز لـ Binance
            LetterEncoder_Richtext2.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_Richtext2.ForeColor = Color.FromArgb(240, 185, 11) ' اللون الأصفر الذهبي لـ Binance
            LetterEncoder_Richtext2.BorderStyle = BorderStyle.None
            LetterEncoder_Richtext2.Font = New Font("Segoe UI", 9.5F)

            ' تنسيق LetterEncoder_txtFilepath - مثل حقول البحث في Binance
            LetterEncoder_txtFilepath.Properties.Appearance.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_txtFilepath.Properties.Appearance.ForeColor = Color.White
            LetterEncoder_txtFilepath.Properties.Appearance.Options.UseBackColor = True
            LetterEncoder_txtFilepath.Properties.Appearance.Options.UseForeColor = True
            LetterEncoder_txtFilepath.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
            LetterEncoder_txtFilepath.Properties.Appearance.Font = New Font("Segoe UI", 9)


            Debug.WriteLine("Binance style applied successfully")
        Catch ex As Exception
            Debug.WriteLine($"Error applying Binance style: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تنسيق الزر بأسلوب Binance (حواف فقط)
    ''' </summary>
    Private Sub FormatBinanceButton(button As DevExpress.XtraEditors.SimpleButton, tooltipText As String)
        Try
            ' تعيين نص التلميح
            button.ToolTip = tooltipText

            ' تعيين خصائص الزر - أسلوب Binance
            button.Appearance.BackColor = Color.FromArgb(40, 40, 40) ' خلفية داكنة
            button.Appearance.ForeColor = Color.White ' نص أبيض
            button.Cursor = Cursors.Hand
            button.Appearance.Options.UseBackColor = True
            button.Appearance.Options.UseForeColor = True

            ' تعيين حواف الزر باللون الأصفر
            button.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
            button.Appearance.BorderColor = Color.FromArgb(96, 96, 96) ' لون الحواف العادي
            button.Appearance.Options.UseBorderColor = True

            ' تعيين خصائص الزر عند المرور فوقه
            button.Appearance.Options.UseFont = True
            button.Appearance.Font = New Font("Segoe UI", 9, FontStyle.Regular)

            ' تعيين خصائص الزر عند الضغط عليه
            button.PaintStyle = DevExpress.XtraEditors.Controls.PaintStyles.Light

            ' تعيين الحواف المنحنية
            button.LookAndFeel.UseDefaultLookAndFeel = False
            button.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat

            ' تعيين خصائص الزر عند المرور فوقه - تغيير لون الحواف فقط
            button.AppearanceHovered.BorderColor = Color.FromArgb(254, 219, 65) ' لون الحواف الأصفر عند المرور
            button.AppearanceHovered.Options.UseBorderColor = True

            ' تعيين خصائص الزر عند الضغط عليه
            button.AppearancePressed.BorderColor = Color.FromArgb(254, 219, 65) ' لون الحواف الأصفر عند الضغط
            button.AppearancePressed.Options.UseBorderColor = True
        Catch ex As Exception
            Debug.WriteLine($"Error formatting button: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub
    Private Sub LetterEncoder_bntSave_Click(sender As Object, e As EventArgs) Handles LetterEncoder_bntSave.Click
        If String.IsNullOrEmpty(loadedHtml) Then
            XtraMessageBox.Show("No HTML file has been loaded!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If
        Dim saveFileDialog As New SaveFileDialog()
        saveFileDialog.Filter = "HTML Files|*.html;*.htm"
        If saveFileDialog.ShowDialog() = DialogResult.OK Then
            File.WriteAllText(saveFileDialog.FileName, loadedHtml)
            XtraMessageBox.Show("The file was saved Successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LetterEncoder_txtFilepath.ResetText()
            LetterEncoder_Richtext.ResetText()
            LetterEncoder_Richtext2.ResetText()
            LetterEncoder_bntEncrypt.Enabled = True
            LetterEncoder_bntSave.Enabled = False
        End If
    End Sub
    Private Sub LetterEncoder_bntEncrypt_Click(sender As Object, e As EventArgs) Handles LetterEncoder_bntEncrypt.Click
        If String.IsNullOrEmpty(loadedHtml) Then
            XtraMessageBox.Show("Please upload an HTML file first!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If
        Dim tempHtml As String = loadedHtml

        ' 🧹 حذف &nbsp; قبل التشفير
        Dim originalHtml As String = tempHtml
        tempHtml = CleanNbspEntities(tempHtml)

        ' إظهار رسالة إذا تم حذف &nbsp; أثناء التشفير
        If originalHtml <> tempHtml Then
            Dim removedCount As Integer = CountNbspEntities(originalHtml)
            Debug.WriteLine($"Removed {removedCount} &nbsp; entities before encryption")
        End If

        cssBlocks.Clear()
        ' 🛠️ استثناء <style> ... </style>
        Dim stylePattern As String = "<style.*?>.*?</style>"
        tempHtml = Regex.Replace(tempHtml, stylePattern, Function(m)
                                                             cssBlocks.Add(m.Value)
                                                             Return "<style>"
                                                         End Function, RegexOptions.Singleline)
        ' 🛠️ استثناء <link rel="stylesheet">
        Dim linkPattern As String = "<link[^>]*?rel=['""]?stylesheet['""]?[^>]*?>"
        tempHtml = Regex.Replace(tempHtml, linkPattern, Function(m)
                                                            cssBlocks.Add(m.Value)
                                                            Return "<link>"
                                                        End Function, RegexOptions.Singleline)
        ' 🛠️ تشفير النصوص الظاهرة فقط داخل الوسوم
        Dim textRegex As New Regex(">([^<>]+)<")
        tempHtml = textRegex.Replace(tempHtml, Function(m)
                                                   Dim text As String = m.Groups(1).Value
                                                   ' استثناء الكلمات الديناميكية من التشفير
                                                   If dynamicWords.Any(Function(word) text.Contains(word)) Then
                                                       Return ">" & text & "<"
                                                   End If
                                                   ' تشفير الأحرف فقط
                                                   Return ">" & EncryptText(text) & "<"
                                               End Function)
        ' 🛠️ إعادة إدخال أكواد <style> و <link>
        Dim index As Integer = 0
        tempHtml = Regex.Replace(tempHtml, "<style>", Function(m)
                                                          Dim css As String = cssBlocks(index)
                                                          index += 1
                                                          Return css
                                                      End Function, RegexOptions.Singleline)
        tempHtml = Regex.Replace(tempHtml, "<link>", Function(m)
                                                         Dim css As String = cssBlocks(index)
                                                         index += 1
                                                         Return css
                                                     End Function, RegexOptions.Singleline)
        ' ✅ تحديث المحتوى
        loadedHtml = tempHtml
        LetterEncoder_Richtext2.Text = loadedHtml
        XtraMessageBox.Show("Encryption completed Successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        LetterEncoder_bntEncrypt.Enabled = False
        LetterEncoder_bntSave.Enabled = True
    End Sub
    Private Sub LetterEncoder_bntBrowse_Click(sender As Object, e As EventArgs) Handles LetterEncoder_bntBrowse.Click
        LetterEncoder_txtFilepath.ResetText()
        LetterEncoder_Richtext.ResetText()
        LetterEncoder_Richtext2.ResetText()
        LetterEncoder_bntEncrypt.Enabled = True
        LetterEncoder_bntSave.Enabled = False
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "HTML Files|*.html;*.htm"
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            LetterEncoder_txtFilepath.Text = openFileDialog.FileName
            loadedHtml = File.ReadAllText(openFileDialog.FileName)

            ' 🧹 تنظيف &nbsp; عند تحميل الملف لإظهار النص المنظف
            Dim cleanedHtmlForDisplay As String = CleanNbspEntities(loadedHtml)
            LetterEncoder_Richtext.Text = cleanedHtmlForDisplay

            ' إظهار رسالة إذا تم العثور على &nbsp; وحذفها مع إحصائيات
            If loadedHtml <> cleanedHtmlForDisplay Then
                Dim removedCount As Integer = CountNbspEntities(loadedHtml)
                XtraMessageBox.Show($"Found and removed {removedCount} &nbsp; entities from the HTML file." & vbNewLine &
                                   "The cleaned version will be used for encryption.", "Cleanup Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ' تحديث loadedHtml بالنسخة المنظفة
                loadedHtml = cleanedHtmlForDisplay
            End If
        End If
    End Sub
    Private Sub LetterEncoder_bntClearAll_Click(sender As Object, e As EventArgs) Handles LetterEncoder_bntClearAll.Click
        LetterEncoder_txtFilepath.ResetText()
        LetterEncoder_Richtext.ResetText()
        LetterEncoder_Richtext2.ResetText()
        LetterEncoder_bntEncrypt.Enabled = True
    End Sub
End Class