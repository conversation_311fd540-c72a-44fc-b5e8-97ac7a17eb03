﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmCheckOffce356
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmCheckOffce356))
        Me.trmfadein = New System.Windows.Forms.Timer(Me.components)
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.BntEdit = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.RepositoryItemCheckEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit()
        Me.lblError = New System.Windows.Forms.Label()
        Me.lblHostError = New System.Windows.Forms.Label()
        Me.lblstatus = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.BtnBrowse = New DevExpress.XtraEditors.SimpleButton()
        Me.BtnStart = New DevExpress.XtraEditors.SimpleButton()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.BtnStop = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl1 = New System.Windows.Forms.Label()
        Me.BntSave = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.BntEdit, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemCheckEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'trmfadein
        '
        Me.trmfadein.Enabled = True
        Me.trmfadein.Interval = 1
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'BntEdit
        '
        Me.BntEdit.AutoHeight = False
        Me.BntEdit.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.BntEdit.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph)})
        Me.BntEdit.Name = "BntEdit"
        Me.BntEdit.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'RepositoryItemCheckEdit1
        '
        Me.RepositoryItemCheckEdit1.AllowFocused = False
        Me.RepositoryItemCheckEdit1.AutoHeight = False
        Me.RepositoryItemCheckEdit1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.[Default]
        Me.RepositoryItemCheckEdit1.Name = "RepositoryItemCheckEdit1"
        Me.RepositoryItemCheckEdit1.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        '
        'lblError
        '
        Me.lblError.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblError.ForeColor = System.Drawing.Color.White
        Me.lblError.Location = New System.Drawing.Point(620, 987)
        Me.lblError.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblError.Name = "lblError"
        Me.lblError.Size = New System.Drawing.Size(104, 28)
        Me.lblError.TabIndex = 67
        Me.lblError.Text = "0"
        Me.lblError.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblHostError
        '
        Me.lblHostError.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblHostError.ForeColor = System.Drawing.Color.White
        Me.lblHostError.Location = New System.Drawing.Point(620, 955)
        Me.lblHostError.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblHostError.Name = "lblHostError"
        Me.lblHostError.Size = New System.Drawing.Size(104, 28)
        Me.lblHostError.TabIndex = 66
        Me.lblHostError.Text = "0"
        Me.lblHostError.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblstatus
        '
        Me.lblstatus.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblstatus.ForeColor = System.Drawing.Color.White
        Me.lblstatus.Location = New System.Drawing.Point(620, 914)
        Me.lblstatus.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblstatus.Name = "lblstatus"
        Me.lblstatus.Size = New System.Drawing.Size(227, 28)
        Me.lblstatus.TabIndex = 61
        Me.lblstatus.Text = "Stand by"
        Me.lblstatus.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label7
        '
        Me.Label7.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.ForeColor = System.Drawing.Color.White
        Me.Label7.Location = New System.Drawing.Point(497, 987)
        Me.Label7.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(104, 28)
        Me.Label7.TabIndex = 60
        Me.Label7.Text = "Error : "
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label8
        '
        Me.Label8.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.ForeColor = System.Drawing.Color.White
        Me.Label8.Location = New System.Drawing.Point(497, 955)
        Me.Label8.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(104, 28)
        Me.Label8.TabIndex = 59
        Me.Label8.Text = "Host Erorr : "
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label2
        '
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.White
        Me.Label2.Location = New System.Drawing.Point(497, 914)
        Me.Label2.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(104, 28)
        Me.Label2.TabIndex = 54
        Me.Label2.Text = "Status : "
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'GridView1
        '
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'GridControl1
        '
        Me.GridControl1.Location = New System.Drawing.Point(12, 99)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(948, 491)
        Me.GridControl1.TabIndex = 68
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'BtnBrowse
        '
        Me.BtnBrowse.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnBrowse.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnBrowse.Appearance.Font = New System.Drawing.Font("Comfortaa", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BtnBrowse.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnBrowse.Appearance.Options.UseBackColor = True
        Me.BtnBrowse.Appearance.Options.UseBorderColor = True
        Me.BtnBrowse.Appearance.Options.UseFont = True
        Me.BtnBrowse.Appearance.Options.UseForeColor = True
        Me.BtnBrowse.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BtnBrowse.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BtnBrowse.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BtnBrowse.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BtnBrowse.AppearanceDisabled.Options.UseBackColor = True
        Me.BtnBrowse.AppearanceDisabled.Options.UseBorderColor = True
        Me.BtnBrowse.AppearanceDisabled.Options.UseFont = True
        Me.BtnBrowse.AppearanceDisabled.Options.UseForeColor = True
        Me.BtnBrowse.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BtnBrowse.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnBrowse.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BtnBrowse.AppearanceHovered.Options.UseBackColor = True
        Me.BtnBrowse.AppearanceHovered.Options.UseBorderColor = True
        Me.BtnBrowse.AppearanceHovered.Options.UseForeColor = True
        Me.BtnBrowse.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnBrowse.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnBrowse.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BtnBrowse.AppearancePressed.Options.UseBackColor = True
        Me.BtnBrowse.AppearancePressed.Options.UseBorderColor = True
        Me.BtnBrowse.AppearancePressed.Options.UseForeColor = True
        Me.BtnBrowse.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BtnBrowse.Location = New System.Drawing.Point(11, 45)
        Me.BtnBrowse.Name = "BtnBrowse"
        Me.BtnBrowse.Size = New System.Drawing.Size(157, 35)
        Me.BtnBrowse.TabIndex = 69
        Me.BtnBrowse.Text = "Browse"
        '
        'BtnStart
        '
        Me.BtnStart.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnStart.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnStart.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BtnStart.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnStart.Appearance.Options.UseBackColor = True
        Me.BtnStart.Appearance.Options.UseBorderColor = True
        Me.BtnStart.Appearance.Options.UseFont = True
        Me.BtnStart.Appearance.Options.UseForeColor = True
        Me.BtnStart.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BtnStart.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BtnStart.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BtnStart.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BtnStart.AppearanceDisabled.Options.UseBackColor = True
        Me.BtnStart.AppearanceDisabled.Options.UseBorderColor = True
        Me.BtnStart.AppearanceDisabled.Options.UseFont = True
        Me.BtnStart.AppearanceDisabled.Options.UseForeColor = True
        Me.BtnStart.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BtnStart.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnStart.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BtnStart.AppearanceHovered.Options.UseBackColor = True
        Me.BtnStart.AppearanceHovered.Options.UseBorderColor = True
        Me.BtnStart.AppearanceHovered.Options.UseForeColor = True
        Me.BtnStart.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnStart.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnStart.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BtnStart.AppearancePressed.Options.UseBackColor = True
        Me.BtnStart.AppearancePressed.Options.UseBorderColor = True
        Me.BtnStart.AppearancePressed.Options.UseForeColor = True
        Me.BtnStart.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.BtnStart.Location = New System.Drawing.Point(174, 45)
        Me.BtnStart.Name = "BtnStart"
        Me.BtnStart.Size = New System.Drawing.Size(157, 35)
        Me.BtnStart.TabIndex = 70
        Me.BtnStart.Text = "Start"
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.ForeColor = System.Drawing.Color.White
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.Appearance.Options.UseForeColor = True
        Me.ProgressPanel1.AppearanceCaption.ForeColor = System.Drawing.Color.White
        Me.ProgressPanel1.AppearanceCaption.Options.UseForeColor = True
        Me.ProgressPanel1.AppearanceDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.ProgressPanel1.AppearanceDescription.Options.UseForeColor = True
        Me.ProgressPanel1.Location = New System.Drawing.Point(674, 27)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(146, 66)
        Me.ProgressPanel1.TabIndex = 71
        Me.ProgressPanel1.Text = "ProgressPanel1"
        '
        'BtnStop
        '
        Me.BtnStop.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnStop.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnStop.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BtnStop.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnStop.Appearance.Options.UseBackColor = True
        Me.BtnStop.Appearance.Options.UseBorderColor = True
        Me.BtnStop.Appearance.Options.UseFont = True
        Me.BtnStop.Appearance.Options.UseForeColor = True
        Me.BtnStop.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BtnStop.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BtnStop.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BtnStop.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BtnStop.AppearanceDisabled.Options.UseBackColor = True
        Me.BtnStop.AppearanceDisabled.Options.UseBorderColor = True
        Me.BtnStop.AppearanceDisabled.Options.UseFont = True
        Me.BtnStop.AppearanceDisabled.Options.UseForeColor = True
        Me.BtnStop.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BtnStop.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnStop.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BtnStop.AppearanceHovered.Options.UseBackColor = True
        Me.BtnStop.AppearanceHovered.Options.UseBorderColor = True
        Me.BtnStop.AppearanceHovered.Options.UseForeColor = True
        Me.BtnStop.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnStop.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnStop.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BtnStop.AppearancePressed.Options.UseBackColor = True
        Me.BtnStop.AppearancePressed.Options.UseBorderColor = True
        Me.BtnStop.AppearancePressed.Options.UseForeColor = True
        Me.BtnStop.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.BtnStop.Location = New System.Drawing.Point(337, 45)
        Me.BtnStop.Name = "BtnStop"
        Me.BtnStop.Size = New System.Drawing.Size(157, 35)
        Me.BtnStop.TabIndex = 72
        Me.BtnStop.Text = "Stop"
        '
        'LabelControl1
        '
        Me.LabelControl1.AutoSize = True
        Me.LabelControl1.ForeColor = System.Drawing.Color.White
        Me.LabelControl1.Location = New System.Drawing.Point(853, 45)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(0, 18)
        Me.LabelControl1.TabIndex = 73
        '
        'BntSave
        '
        Me.BntSave.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntSave.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.Appearance.Options.UseBackColor = True
        Me.BntSave.Appearance.Options.UseBorderColor = True
        Me.BntSave.Appearance.Options.UseFont = True
        Me.BntSave.Appearance.Options.UseForeColor = True
        Me.BntSave.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntSave.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntSave.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntSave.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntSave.AppearanceDisabled.Options.UseBackColor = True
        Me.BntSave.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntSave.AppearanceDisabled.Options.UseFont = True
        Me.BntSave.AppearanceDisabled.Options.UseForeColor = True
        Me.BntSave.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntSave.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntSave.AppearanceHovered.Options.UseBackColor = True
        Me.BntSave.AppearanceHovered.Options.UseBorderColor = True
        Me.BntSave.AppearanceHovered.Options.UseForeColor = True
        Me.BntSave.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntSave.AppearancePressed.Options.UseBackColor = True
        Me.BntSave.AppearancePressed.Options.UseBorderColor = True
        Me.BntSave.AppearancePressed.Options.UseForeColor = True
        Me.BntSave.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.BntSave.Location = New System.Drawing.Point(500, 45)
        Me.BntSave.Name = "BntSave"
        Me.BntSave.Size = New System.Drawing.Size(157, 35)
        Me.BntSave.TabIndex = 74
        Me.BntSave.Text = "Save"
        '
        'frmCheckOffce356
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.BntSave)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.BtnStop)
        Me.Controls.Add(Me.ProgressPanel1)
        Me.Controls.Add(Me.BtnStart)
        Me.Controls.Add(Me.BtnBrowse)
        Me.Controls.Add(Me.GridControl1)
        Me.Controls.Add(Me.lblstatus)
        Me.Controls.Add(Me.lblError)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.lblHostError)
        Me.Controls.Add(Me.Label7)
        Me.Controls.Add(Me.Label8)
        Me.FormBorderEffect = DevExpress.XtraEditors.FormBorderEffect.Glow
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Icon = CType(resources.GetObject("frmCheckOffce356.IconOptions.Icon"), System.Drawing.Icon)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Logo_NewBestSender
        Me.IconOptions.ShowIcon = False
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Name = "frmCheckOffce356"
        Me.Opacity = 0.98R
        Me.ShowInTaskbar = False
        Me.Text = " Check Office 356.."
        CType(Me.BntEdit, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemCheckEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents trmfadein As Timer
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents BntEdit As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents RepositoryItemCheckEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit
    Friend WithEvents Label7 As Label
    Friend WithEvents Label8 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents lblError As Label
    Friend WithEvents lblHostError As Label
    Friend WithEvents lblstatus As Label
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents BtnBrowse As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BtnStart As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents BtnStop As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl1 As Label
    Friend WithEvents BntSave As DevExpress.XtraEditors.SimpleButton
End Class
