﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmRedirectLink
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmRedirectLink))
        Me.bntCleartxtTag = New DevExpress.XtraEditors.SimpleButton()
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.txt_Link_Logo_CludFlare = New System.Windows.Forms.TextBox()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Label7 = New System.Windows.Forms.Label()
        Me.PictureBox_Logo = New System.Windows.Forms.PictureBox()
        Me.txt_width_CloudFlare = New DevExpress.XtraEditors.TextEdit()
        Me.txt_length_CloudFlare = New DevExpress.XtraEditors.TextEdit()
        Me.cb_txt_Time = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.txt_type_CloudFlare = New DevExpress.XtraEditors.TextEdit()
        Me.txt_URL_CloudFlare = New DevExpress.XtraEditors.TextEdit()
        Me.txt_logo_Next = New DevExpress.XtraEditors.TextEdit()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.MainPanel = New System.Windows.Forms.Panel()
        Me.lbl_Size_Logo = New System.Windows.Forms.Label()
        CType(Me.PictureBox_Logo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_width_CloudFlare.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_length_CloudFlare.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_type_CloudFlare.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_URL_CloudFlare.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_logo_Next.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'bntCleartxtTag
        '
        Me.bntCleartxtTag.AllowFocus = False
        Me.bntCleartxtTag.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.bntCleartxtTag.Appearance.Options.UseBackColor = True
        Me.bntCleartxtTag.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.bntCleartxtTag.AppearanceHovered.Options.UseBackColor = True
        Me.bntCleartxtTag.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.bntCleartxtTag.AppearancePressed.Options.UseBackColor = True
        Me.bntCleartxtTag.ImageOptions.Image = CType(resources.GetObject("bntCleartxtTag.ImageOptions.Image"), System.Drawing.Image)
        Me.bntCleartxtTag.Location = New System.Drawing.Point(1222, 180)
        Me.bntCleartxtTag.LookAndFeel.SkinName = "Darkroom"
        Me.bntCleartxtTag.LookAndFeel.UseDefaultLookAndFeel = False
        Me.bntCleartxtTag.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.bntCleartxtTag.Name = "bntCleartxtTag"
        Me.bntCleartxtTag.Size = New System.Drawing.Size(27, 43)
        Me.bntCleartxtTag.TabIndex = 514
        Me.bntCleartxtTag.Visible = False
        '
        'RichTextBox1
        '
        Me.RichTextBox1.Location = New System.Drawing.Point(1188, 58)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(67, 41)
        Me.RichTextBox1.TabIndex = 508
        Me.RichTextBox1.Text = resources.GetString("RichTextBox1.Text")
        Me.RichTextBox1.Visible = False
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Comfortaa", 10.75!, System.Drawing.FontStyle.Bold)
        Me.Label10.ForeColor = System.Drawing.Color.White
        Me.Label10.Location = New System.Drawing.Point(1128, 104)
        Me.Label10.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(204, 24)
        Me.Label10.TabIndex = 482
        Me.Label10.Text = "Place the Direct logo link"
        Me.Label10.Visible = False
        '
        'txt_Link_Logo_CludFlare
        '
        Me.txt_Link_Logo_CludFlare.Font = New System.Drawing.Font("Comfortaa", 10.75!, System.Drawing.FontStyle.Bold)
        Me.txt_Link_Logo_CludFlare.Location = New System.Drawing.Point(1224, 132)
        Me.txt_Link_Logo_CludFlare.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.txt_Link_Logo_CludFlare.Multiline = True
        Me.txt_Link_Logo_CludFlare.Name = "txt_Link_Logo_CludFlare"
        Me.txt_Link_Logo_CludFlare.Size = New System.Drawing.Size(89, 41)
        Me.txt_Link_Logo_CludFlare.TabIndex = 481
        Me.txt_Link_Logo_CludFlare.Visible = False
        '
        'Label7
        '
        Me.Label7.Font = New System.Drawing.Font("Audiowide", 14.25!, System.Drawing.FontStyle.Bold)
        Me.Label7.ForeColor = System.Drawing.Color.White
        Me.Label7.Location = New System.Drawing.Point(344, 154)
        Me.Label7.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(234, 28)
        Me.Label7.TabIndex = 603
        Me.Label7.Text = "Cloudflare"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'PictureBox_Logo
        '
        Me.PictureBox_Logo.Location = New System.Drawing.Point(731, 222)
        Me.PictureBox_Logo.Name = "PictureBox_Logo"
        Me.PictureBox_Logo.Size = New System.Drawing.Size(209, 161)
        Me.PictureBox_Logo.TabIndex = 602
        Me.PictureBox_Logo.TabStop = False
        '
        'lbl_Size_Logo
        '
        Me.lbl_Size_Logo.AutoSize = True
        Me.lbl_Size_Logo.Font = New System.Drawing.Font("Comfortaa", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lbl_Size_Logo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.lbl_Size_Logo.Location = New System.Drawing.Point(731, 390)
        Me.lbl_Size_Logo.Name = "lbl_Size_Logo"
        Me.lbl_Size_Logo.Size = New System.Drawing.Size(0, 20)
        Me.lbl_Size_Logo.TabIndex = 605
        Me.lbl_Size_Logo.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'txt_width_CloudFlare
        '
        Me.txt_width_CloudFlare.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_width_CloudFlare.EditValue = "300"
        Me.txt_width_CloudFlare.Location = New System.Drawing.Point(474, 426)
        Me.txt_width_CloudFlare.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_width_CloudFlare.Name = "txt_width_CloudFlare"
        Me.txt_width_CloudFlare.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_width_CloudFlare.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_width_CloudFlare.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_width_CloudFlare.Properties.Appearance.Options.UseBackColor = True
        Me.txt_width_CloudFlare.Properties.Appearance.Options.UseFont = True
        Me.txt_width_CloudFlare.Properties.Appearance.Options.UseForeColor = True
        Me.txt_width_CloudFlare.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_width_CloudFlare.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_width_CloudFlare.Properties.NullValuePrompt = "width Logo"
        Me.txt_width_CloudFlare.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_width_CloudFlare.Size = New System.Drawing.Size(106, 30)
        Me.txt_width_CloudFlare.TabIndex = 601
        '
        'txt_length_CloudFlare
        '
        Me.txt_length_CloudFlare.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_length_CloudFlare.EditValue = "102"
        Me.txt_length_CloudFlare.Location = New System.Drawing.Point(360, 426)
        Me.txt_length_CloudFlare.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_length_CloudFlare.Name = "txt_length_CloudFlare"
        Me.txt_length_CloudFlare.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_length_CloudFlare.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_length_CloudFlare.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_length_CloudFlare.Properties.Appearance.Options.UseBackColor = True
        Me.txt_length_CloudFlare.Properties.Appearance.Options.UseFont = True
        Me.txt_length_CloudFlare.Properties.Appearance.Options.UseForeColor = True
        Me.txt_length_CloudFlare.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_length_CloudFlare.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_length_CloudFlare.Properties.NullValuePrompt = "Height logo"
        Me.txt_length_CloudFlare.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_length_CloudFlare.Size = New System.Drawing.Size(106, 30)
        Me.txt_length_CloudFlare.TabIndex = 599
        '
        'cb_txt_Time
        '
        Me.cb_txt_Time.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cb_txt_Time.EditValue = "3000"
        Me.cb_txt_Time.Location = New System.Drawing.Point(212, 421)
        Me.cb_txt_Time.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cb_txt_Time.Name = "cb_txt_Time"
        Me.cb_txt_Time.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cb_txt_Time.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cb_txt_Time.Properties.Appearance.Options.UseBackColor = True
        Me.cb_txt_Time.Properties.Appearance.Options.UseFont = True
        Me.cb_txt_Time.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cb_txt_Time.Properties.ContextImageOptions.Image = CType(resources.GetObject("cb_txt_Time.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.cb_txt_Time.Properties.Items.AddRange(New Object() {"1000", "2000", "3000", "4000", "5000", "6000", "7000", "8000", "9000"})
        Me.cb_txt_Time.Size = New System.Drawing.Size(130, 38)
        Me.cb_txt_Time.TabIndex = 600
        '
        'txt_type_CloudFlare
        '
        Me.txt_type_CloudFlare.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_type_CloudFlare.EditValue = ""
        Me.txt_type_CloudFlare.Location = New System.Drawing.Point(208, 223)
        Me.txt_type_CloudFlare.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_type_CloudFlare.Name = "txt_type_CloudFlare"
        Me.txt_type_CloudFlare.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_type_CloudFlare.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_type_CloudFlare.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_type_CloudFlare.Properties.Appearance.Options.UseBackColor = True
        Me.txt_type_CloudFlare.Properties.Appearance.Options.UseFont = True
        Me.txt_type_CloudFlare.Properties.Appearance.Options.UseForeColor = True
        Me.txt_type_CloudFlare.Properties.NullValuePrompt = "Decoding document. Please wait..."
        Me.txt_type_CloudFlare.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_type_CloudFlare.Size = New System.Drawing.Size(516, 30)
        Me.txt_type_CloudFlare.TabIndex = 597
        Me.txt_type_CloudFlare.ToolTipTitle = "Decoding document. Please wait..."
        '
        'txt_URL_CloudFlare
        '
        Me.txt_URL_CloudFlare.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_URL_CloudFlare.EditValue = ""
        Me.txt_URL_CloudFlare.Location = New System.Drawing.Point(208, 288)
        Me.txt_URL_CloudFlare.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_URL_CloudFlare.Name = "txt_URL_CloudFlare"
        Me.txt_URL_CloudFlare.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_URL_CloudFlare.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_URL_CloudFlare.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_URL_CloudFlare.Properties.Appearance.Options.UseBackColor = True
        Me.txt_URL_CloudFlare.Properties.Appearance.Options.UseFont = True
        Me.txt_URL_CloudFlare.Properties.Appearance.Options.UseForeColor = True
        Me.txt_URL_CloudFlare.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_URL_CloudFlare.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_URL_CloudFlare.Properties.NullValuePrompt = "https://www.Example.com/?="
        Me.txt_URL_CloudFlare.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_URL_CloudFlare.Size = New System.Drawing.Size(516, 30)
        Me.txt_URL_CloudFlare.TabIndex = 596
        Me.txt_URL_CloudFlare.ToolTipTitle = "https://www.Example.com/?="
        '
        'txt_logo_Next
        '
        Me.txt_logo_Next.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_logo_Next.EditValue = ""
        Me.txt_logo_Next.Location = New System.Drawing.Point(208, 353)
        Me.txt_logo_Next.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_logo_Next.Name = "txt_logo_Next"
        Me.txt_logo_Next.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_logo_Next.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_logo_Next.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_logo_Next.Properties.Appearance.Options.UseBackColor = True
        Me.txt_logo_Next.Properties.Appearance.Options.UseFont = True
        Me.txt_logo_Next.Properties.Appearance.Options.UseForeColor = True
        Me.txt_logo_Next.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_logo_Next.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_logo_Next.Properties.NullValuePrompt = "Put The Other Logo"
        Me.txt_logo_Next.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_logo_Next.Size = New System.Drawing.Size(516, 30)
        Me.txt_logo_Next.TabIndex = 598
        Me.txt_logo_Next.ToolTipTitle = "Put The Other Logo"
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.Cloudflare_Logo
        Me.PictureBox1.Location = New System.Drawing.Point(343, 54)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(237, 128)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 595
        Me.PictureBox1.TabStop = False
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label6.Location = New System.Drawing.Point(479, 397)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(88, 21)
        Me.Label6.TabIndex = 589
        Me.Label6.Text = "Width Logo"
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label5.Location = New System.Drawing.Point(364, 397)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(91, 21)
        Me.Label5.TabIndex = 590
        Me.Label5.Text = "Height logo"
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label4.Location = New System.Drawing.Point(208, 397)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(102, 21)
        Me.Label4.TabIndex = 591
        Me.Label4.Text = "Time Redirect"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label3.Location = New System.Drawing.Point(208, 325)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(134, 21)
        Me.Label3.TabIndex = 592
        Me.Label3.Text = "Put the other logo"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(208, 260)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(113, 21)
        Me.Label2.TabIndex = 593
        Me.Label2.Text = "Enter Your Text"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label1.Location = New System.Drawing.Point(208, 195)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(114, 21)
        Me.Label1.TabIndex = 594
        Me.Label1.Text = "Enter Your link "
        '
        'MainPanel
        '
        Me.MainPanel.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.MainPanel.Location = New System.Drawing.Point(0, 0)
        Me.MainPanel.Name = "MainPanel"
        Me.MainPanel.Padding = New System.Windows.Forms.Padding(10)
        Me.MainPanel.Size = New System.Drawing.Size(972, 602)
        Me.MainPanel.TabIndex = 604
        '
        'frmRedirectLink
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.MainPanel)
        Me.Controls.Add(Me.txt_Link_Logo_CludFlare)
        Me.Controls.Add(Me.bntCleartxtTag)
        Me.Controls.Add(Me.RichTextBox1)
        Me.Controls.Add(Me.Label10)

        ' Add controls to MainPanel
        Me.MainPanel.Controls.Add(Me.Label7)
        Me.MainPanel.Controls.Add(Me.PictureBox_Logo)
        Me.MainPanel.Controls.Add(Me.lbl_Size_Logo)
        Me.MainPanel.Controls.Add(Me.txt_width_CloudFlare)
        Me.MainPanel.Controls.Add(Me.txt_length_CloudFlare)
        Me.MainPanel.Controls.Add(Me.cb_txt_Time)
        Me.MainPanel.Controls.Add(Me.txt_type_CloudFlare)
        Me.MainPanel.Controls.Add(Me.txt_URL_CloudFlare)
        Me.MainPanel.Controls.Add(Me.txt_logo_Next)
        Me.MainPanel.Controls.Add(Me.PictureBox1)
        Me.MainPanel.Controls.Add(Me.Label6)
        Me.MainPanel.Controls.Add(Me.Label5)
        Me.MainPanel.Controls.Add(Me.Label4)
        Me.MainPanel.Controls.Add(Me.Label3)
        Me.MainPanel.Controls.Add(Me.Label2)
        Me.MainPanel.Controls.Add(Me.Label1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.linked_file
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.Name = "frmRedirectLink"
        Me.ShowInTaskbar = False
        Me.Text = "Redirect CloudFlare "
        CType(Me.PictureBox_Logo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_width_CloudFlare.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_length_CloudFlare.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_type_CloudFlare.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_URL_CloudFlare.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_logo_Next.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents bntCleartxtTag As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label10 As Label
    Friend WithEvents txt_Link_Logo_CludFlare As TextBox
    Friend WithEvents Label7 As Label
    Friend WithEvents PictureBox_Logo As PictureBox
    Friend WithEvents txt_width_CloudFlare As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txt_length_CloudFlare As DevExpress.XtraEditors.TextEdit
    Friend WithEvents cb_txt_Time As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents txt_type_CloudFlare As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txt_URL_CloudFlare As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txt_logo_Next As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents Label6 As Label
    Friend WithEvents Label5 As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents MainPanel As Panel
    Friend WithEvents lbl_Size_Logo As Label
End Class
