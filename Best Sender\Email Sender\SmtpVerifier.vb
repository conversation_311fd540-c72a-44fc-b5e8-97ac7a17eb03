Imports System.Net
Imports System.Net.Mail
Imports System.Net.Security
Imports System.Security.Cryptography.X509Certificates
Imports System.Threading
Imports System.Threading.Tasks

Public Class SmtpVerifier
    ' دالة محسنة للتحقق من حساب SMTP
    Public Shared Async Function VerifySmtpAccount(
        hostName As String,
        username As String,
        password As String,
        port As Integer,
        useSSL As Boolean,
        fromEmail As String,
        cancellationToken As CancellationToken,
        Optional apiKey As String = "",
        Optional secretKey As String = "") As Task(Of SmtpVerificationResult)
        
        Dim result As New SmtpVerificationResult With {
            .IsSuccess = False,
            .ErrorMessage = "",
            .Provider = Nothing,
            .ConnectionDetails = New SmtpConnectionDetails With {
                .Host = hostName,
                .Username = username,
                .Port = port,
                .UseSSL = useSSL
            }
        }
        
        Try
            ' التعرف على مزود خدمة SMTP
            result.Provider = SmtpProviders.DetectProvider(hostName)
            
            ' التحقق أولاً باستخدام API إذا كان المزود يدعم ذلك
            If result.Provider.SupportsAPI AndAlso Not String.IsNullOrEmpty(apiKey) Then
                Dim apiResult = Await SmtpApiHandler.VerifySmtpAccountViaApi(result.Provider, username, password, apiKey, secretKey)
                If apiResult Then
                    result.IsSuccess = True
                    result.VerificationMethod = "API"
                    Return result
                End If
            End If
            
            ' إذا فشل التحقق عبر API أو لم يكن متاحًا، استخدم التحقق العادي عبر SMTP
            Dim verificationTask = VerifySmtpWithTimeout(hostName, username, password, port, useSSL, fromEmail, cancellationToken)
            
            ' انتظار اكتمال المهمة مع مهلة زمنية
            If Await Task.WhenAny(verificationTask, Task.Delay(15000, cancellationToken)) = verificationTask Then
                ' المهمة اكتملت في الوقت المحدد
                Dim verificationResult = Await verificationTask
                result.IsSuccess = verificationResult.IsSuccess
                result.ErrorMessage = verificationResult.ErrorMessage
                result.VerificationMethod = "SMTP"
            Else
                ' تجاوز المهلة الزمنية
                result.IsSuccess = False
                result.ErrorMessage = "Connection timeout"
                result.VerificationMethod = "SMTP"
            End If
            
            Return result
        Catch ex As Exception When TypeOf ex Is TaskCanceledException OrElse cancellationToken.IsCancellationRequested
            result.IsSuccess = False
            result.ErrorMessage = "Operation cancelled"
            result.VerificationMethod = "Cancelled"
            Return result
        Catch ex As Exception
            result.IsSuccess = False
            result.ErrorMessage = ex.Message
            result.VerificationMethod = "SMTP"
            Return result
        End Try
    End Function
    
    ' دالة للتحقق من SMTP مع مهلة زمنية
    Private Shared Async Function VerifySmtpWithTimeout(
        hostName As String,
        username As String,
        password As String,
        port As Integer,
        useSSL As Boolean,
        fromEmail As String,
        cancellationToken As CancellationToken) As Task(Of SmtpVerificationResult)
        
        Dim result As New SmtpVerificationResult With {
            .IsSuccess = False,
            .ErrorMessage = "",
            .Provider = SmtpProviders.DetectProvider(hostName),
            .ConnectionDetails = New SmtpConnectionDetails With {
                .Host = hostName,
                .Username = username,
                .Port = port,
                .UseSSL = useSSL
            },
            .VerificationMethod = "SMTP"
        }
        
        Try
            ' تجاوز التحقق من شهادة SSL إذا كان ضروريًا
            ServicePointManager.ServerCertificateValidationCallback = AddressOf ValidateRemoteCertificate
            
            ' إعداد عميل SMTP
            Using smtpClient As New SmtpClient(hostName, port)
                smtpClient.EnableSsl = useSSL
                smtpClient.UseDefaultCredentials = False
                smtpClient.Credentials = New NetworkCredential(username, password)
                smtpClient.DeliveryMethod = SmtpDeliveryMethod.Network
                smtpClient.Timeout = 10000 ' 10 ثوانٍ كمهلة زمنية
                
                ' إنشاء رسالة اختبار
                Dim testMessage As New MailMessage()
                
                ' استخدام عنوان البريد الإلكتروني المناسب للمرسل
                If String.IsNullOrEmpty(fromEmail) Then
                    fromEmail = username
                End If
                
                testMessage.From = New MailAddress(fromEmail)
                testMessage.To.Add(New MailAddress("<EMAIL>")) ' لن يتم إرسال الرسالة فعليًا
                testMessage.Subject = "SMTP Test"
                testMessage.Body = "This is a test message to verify SMTP settings."
                
                ' محاولة الاتصال بالخادم فقط دون إرسال الرسالة
                Await Task.Run(Sub()
                    Try
                        ' استخدام EHLO بدلاً من إرسال رسالة كاملة
                        Dim client = DirectCast(smtpClient.ServicePoint.GetConnection(Nothing), System.Net.Mail.SmtpConnection)
                        client.GetConnection(Nothing)
                        client.Ehlo()
                        
                        result.IsSuccess = True
                    Catch ex As Exception
                        ' محاولة إرسال الرسالة كخيار احتياطي
                        Try
                            smtpClient.Send(testMessage)
                            result.IsSuccess = True
                        Catch innerEx As Exception
                            result.ErrorMessage = innerEx.Message
                        End Try
                    End Try
                End Sub, cancellationToken)
            End Using
            
            Return result
        Catch ex As Exception When TypeOf ex Is TaskCanceledException OrElse cancellationToken.IsCancellationRequested
            result.ErrorMessage = "Operation cancelled"
            Return result
        Catch ex As Exception
            result.ErrorMessage = ex.Message
            Return result
        Finally
            ' إعادة تعيين التحقق من شهادة SSL إلى الإعداد الافتراضي
            ServicePointManager.ServerCertificateValidationCallback = Nothing
        End Try
    End Function
    
    ' دالة للتحقق من شهادة SSL
    Private Shared Function ValidateRemoteCertificate(sender As Object, certificate As X509Certificate, chain As X509Chain, sslPolicyErrors As SslPolicyErrors) As Boolean
        ' قبول جميع الشهادات للتبسيط
        Return True
    End Function
End Class

' فئة لتخزين نتيجة التحقق من SMTP
Public Class SmtpVerificationResult
    Public Property IsSuccess As Boolean
    Public Property ErrorMessage As String
    Public Property Provider As SmtpProviderInfo
    Public Property ConnectionDetails As SmtpConnectionDetails
    Public Property VerificationMethod As String ' "API" أو "SMTP" أو "Cancelled"
End Class

' فئة لتخزين تفاصيل اتصال SMTP
Public Class SmtpConnectionDetails
    Public Property Host As String
    Public Property Username As String
    Public Property Port As Integer
    Public Property UseSSL As Boolean
End Class
