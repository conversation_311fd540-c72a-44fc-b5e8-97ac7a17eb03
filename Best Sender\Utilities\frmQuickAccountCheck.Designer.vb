﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmQuickAccountCheck
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmQuickAccountCheck))
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
        Me.PnlWait = New System.Windows.Forms.Panel()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.BntStop = New DevExpress.XtraEditors.SimpleButton()
        Me.BntResetAll = New DevExpress.XtraEditors.SimpleButton()
        Me.BntTest = New DevExpress.XtraEditors.SimpleButton()
        Me.CheckSSL = New DevExpress.XtraEditors.CheckEdit()
        Me.TxtSMTPServer = New DevExpress.XtraEditors.TextEdit()
        Me.TxtPort = New DevExpress.XtraEditors.TextEdit()
        Me.TxtEmail = New DevExpress.XtraEditors.TextEdit()
        Me.TxtPassword = New DevExpress.XtraEditors.TextEdit()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.BntAddAccount = New DevExpress.XtraEditors.SimpleButton()
        Me.ListBoxControl1 = New DevExpress.XtraEditors.ListBoxControl()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PnlWait.SuspendLayout()
        CType(Me.CheckSSL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtSMTPServer.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtPort.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtEmail.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtPassword.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ListBoxControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'CheckEdit1
        '
        Me.CheckEdit1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.CheckEdit1.Location = New System.Drawing.Point(1285, 324)
        Me.CheckEdit1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.CheckEdit1.Name = "CheckEdit1"
        Me.CheckEdit1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.CheckEdit1.Properties.Appearance.ForeColor = System.Drawing.Color.Transparent
        Me.CheckEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.CheckEdit1.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEdit1.Properties.Caption = "View password"
        Me.CheckEdit1.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style15
        Me.CheckEdit1.Size = New System.Drawing.Size(19, 22)
        Me.CheckEdit1.TabIndex = 10
        '
        'PnlWait
        '
        Me.PnlWait.Controls.Add(Me.ProgressPanel1)
        Me.PnlWait.Location = New System.Drawing.Point(292, 58)
        Me.PnlWait.Name = "PnlWait"
        Me.PnlWait.Size = New System.Drawing.Size(391, 88)
        Me.PnlWait.TabIndex = 506
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.AnimationToTextDistance = 10
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.Appearance.Options.UseFont = True
        Me.ProgressPanel1.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel1.AppearanceCaption.Options.UseFont = True
        Me.ProgressPanel1.AppearanceDescription.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel1.AppearanceDescription.Options.UseFont = True
        Me.ProgressPanel1.BarAnimationElementThickness = 5
        Me.ProgressPanel1.Caption = "Please Wait..."
        Me.ProgressPanel1.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
        Me.ProgressPanel1.Description = "Testing SMTP account.."
        Me.ProgressPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ProgressPanel1.Location = New System.Drawing.Point(0, 0)
        Me.ProgressPanel1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(391, 88)
        Me.ProgressPanel1.TabIndex = 492
        Me.ProgressPanel1.Text = "picWait"
        '
        'BntStop
        '
        Me.BntStop.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntStop.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntStop.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntStop.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntStop.Appearance.Options.UseBackColor = True
        Me.BntStop.Appearance.Options.UseBorderColor = True
        Me.BntStop.Appearance.Options.UseFont = True
        Me.BntStop.Appearance.Options.UseForeColor = True
        Me.BntStop.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntStop.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntStop.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntStop.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntStop.AppearanceDisabled.Options.UseBackColor = True
        Me.BntStop.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntStop.AppearanceDisabled.Options.UseFont = True
        Me.BntStop.AppearanceDisabled.Options.UseForeColor = True
        Me.BntStop.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntStop.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntStop.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntStop.AppearanceHovered.Options.UseBackColor = True
        Me.BntStop.AppearanceHovered.Options.UseBorderColor = True
        Me.BntStop.AppearanceHovered.Options.UseForeColor = True
        Me.BntStop.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntStop.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntStop.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntStop.AppearancePressed.Options.UseBackColor = True
        Me.BntStop.AppearancePressed.Options.UseBorderColor = True
        Me.BntStop.AppearancePressed.Options.UseForeColor = True
        Me.BntStop.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.BntStop.Location = New System.Drawing.Point(539, 386)
        Me.BntStop.Name = "BntStop"
        Me.BntStop.Size = New System.Drawing.Size(127, 38)
        Me.BntStop.TabIndex = 505
        Me.BntStop.Text = "&Cancel"
        '
        'BntResetAll
        '
        Me.BntResetAll.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetAll.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetAll.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntResetAll.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetAll.Appearance.Options.UseBackColor = True
        Me.BntResetAll.Appearance.Options.UseBorderColor = True
        Me.BntResetAll.Appearance.Options.UseFont = True
        Me.BntResetAll.Appearance.Options.UseForeColor = True
        Me.BntResetAll.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntResetAll.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntResetAll.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntResetAll.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntResetAll.AppearanceDisabled.Options.UseBackColor = True
        Me.BntResetAll.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntResetAll.AppearanceDisabled.Options.UseFont = True
        Me.BntResetAll.AppearanceDisabled.Options.UseForeColor = True
        Me.BntResetAll.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntResetAll.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetAll.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntResetAll.AppearanceHovered.Options.UseBackColor = True
        Me.BntResetAll.AppearanceHovered.Options.UseBorderColor = True
        Me.BntResetAll.AppearanceHovered.Options.UseForeColor = True
        Me.BntResetAll.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetAll.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetAll.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntResetAll.AppearancePressed.Options.UseBackColor = True
        Me.BntResetAll.AppearancePressed.Options.UseBorderColor = True
        Me.BntResetAll.AppearancePressed.Options.UseForeColor = True
        Me.BntResetAll.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntResetAll.Location = New System.Drawing.Point(672, 388)
        Me.BntResetAll.Name = "BntResetAll"
        Me.BntResetAll.Size = New System.Drawing.Size(127, 38)
        Me.BntResetAll.TabIndex = 504
        Me.BntResetAll.Text = "Reset All"
        '
        'BntTest
        '
        Me.BntTest.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTest.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTest.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntTest.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTest.Appearance.Options.UseBackColor = True
        Me.BntTest.Appearance.Options.UseBorderColor = True
        Me.BntTest.Appearance.Options.UseFont = True
        Me.BntTest.Appearance.Options.UseForeColor = True
        Me.BntTest.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntTest.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntTest.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntTest.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntTest.AppearanceDisabled.Options.UseBackColor = True
        Me.BntTest.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntTest.AppearanceDisabled.Options.UseFont = True
        Me.BntTest.AppearanceDisabled.Options.UseForeColor = True
        Me.BntTest.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntTest.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTest.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntTest.AppearanceHovered.Options.UseBackColor = True
        Me.BntTest.AppearanceHovered.Options.UseBorderColor = True
        Me.BntTest.AppearanceHovered.Options.UseForeColor = True
        Me.BntTest.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTest.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntTest.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntTest.AppearancePressed.Options.UseBackColor = True
        Me.BntTest.AppearancePressed.Options.UseBorderColor = True
        Me.BntTest.AppearancePressed.Options.UseForeColor = True
        Me.BntTest.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.BntTest.Location = New System.Drawing.Point(406, 386)
        Me.BntTest.Name = "BntTest"
        Me.BntTest.Size = New System.Drawing.Size(127, 38)
        Me.BntTest.TabIndex = 503
        Me.BntTest.Text = "Start"
        '
        'CheckSSL
        '
        Me.CheckSSL.Location = New System.Drawing.Point(177, 396)
        Me.CheckSSL.Name = "CheckSSL"
        Me.CheckSSL.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.CheckSSL.Properties.Appearance.Options.UseFont = True
        Me.CheckSSL.Properties.Caption = "Enable SSL"
        Me.CheckSSL.Size = New System.Drawing.Size(98, 23)
        Me.CheckSSL.TabIndex = 502
        '
        'TxtSMTPServer
        '
        Me.TxtSMTPServer.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtSMTPServer.EditValue = ""
        Me.TxtSMTPServer.Location = New System.Drawing.Point(173, 179)
        Me.TxtSMTPServer.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtSMTPServer.Name = "TxtSMTPServer"
        Me.TxtSMTPServer.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtSMTPServer.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtSMTPServer.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtSMTPServer.Properties.Appearance.Options.UseBackColor = True
        Me.TxtSMTPServer.Properties.Appearance.Options.UseFont = True
        Me.TxtSMTPServer.Properties.Appearance.Options.UseForeColor = True
        Me.TxtSMTPServer.Properties.NullValuePrompt = "Enter your Smtp server host name..."
        Me.TxtSMTPServer.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtSMTPServer.Size = New System.Drawing.Size(624, 30)
        Me.TxtSMTPServer.TabIndex = 498
        '
        'TxtPort
        '
        Me.TxtPort.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtPort.EditValue = ""
        Me.TxtPort.Location = New System.Drawing.Point(173, 350)
        Me.TxtPort.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtPort.Name = "TxtPort"
        Me.TxtPort.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtPort.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtPort.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtPort.Properties.Appearance.Options.UseBackColor = True
        Me.TxtPort.Properties.Appearance.Options.UseFont = True
        Me.TxtPort.Properties.Appearance.Options.UseForeColor = True
        Me.TxtPort.Properties.NullValuePrompt = "Enter your Smtp host port no..."
        Me.TxtPort.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtPort.Size = New System.Drawing.Size(624, 30)
        Me.TxtPort.TabIndex = 499
        '
        'TxtEmail
        '
        Me.TxtEmail.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtEmail.EditValue = ""
        Me.TxtEmail.Location = New System.Drawing.Point(173, 236)
        Me.TxtEmail.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtEmail.Name = "TxtEmail"
        Me.TxtEmail.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtEmail.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtEmail.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtEmail.Properties.Appearance.Options.UseBackColor = True
        Me.TxtEmail.Properties.Appearance.Options.UseFont = True
        Me.TxtEmail.Properties.Appearance.Options.UseForeColor = True
        Me.TxtEmail.Properties.NullValuePrompt = "Enter a vaild email address..."
        Me.TxtEmail.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtEmail.Size = New System.Drawing.Size(624, 30)
        Me.TxtEmail.TabIndex = 500
        '
        'TxtPassword
        '
        Me.TxtPassword.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtPassword.EditValue = ""
        Me.TxtPassword.Location = New System.Drawing.Point(173, 293)
        Me.TxtPassword.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtPassword.Name = "TxtPassword"
        Me.TxtPassword.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtPassword.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtPassword.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtPassword.Properties.Appearance.Options.UseBackColor = True
        Me.TxtPassword.Properties.Appearance.Options.UseFont = True
        Me.TxtPassword.Properties.Appearance.Options.UseForeColor = True
        Me.TxtPassword.Properties.NullValuePrompt = "Enter your email password..."
        Me.TxtPassword.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtPassword.Properties.PasswordChar = Global.Microsoft.VisualBasic.ChrW(42)
        Me.TxtPassword.Size = New System.Drawing.Size(624, 30)
        Me.TxtPassword.TabIndex = 501
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label4.Location = New System.Drawing.Point(173, 326)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(38, 21)
        Me.Label4.TabIndex = 494
        Me.Label4.Text = "Port"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label3.Location = New System.Drawing.Point(173, 269)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(74, 21)
        Me.Label3.TabIndex = 495
        Me.Label3.Text = "Password"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(173, 212)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(106, 21)
        Me.Label2.TabIndex = 496
        Me.Label2.Text = "Email Address"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label1.Location = New System.Drawing.Point(173, 155)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(86, 21)
        Me.Label1.TabIndex = 497
        Me.Label1.Text = "Host Name"
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Bold)
        Me.Label5.Location = New System.Drawing.Point(173, 430)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(0, 23)
        Me.Label5.TabIndex = 509
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'BntAddAccount
        '
        Me.BntAddAccount.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAddAccount.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAddAccount.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntAddAccount.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAddAccount.Appearance.Options.UseBackColor = True
        Me.BntAddAccount.Appearance.Options.UseBorderColor = True
        Me.BntAddAccount.Appearance.Options.UseFont = True
        Me.BntAddAccount.Appearance.Options.UseForeColor = True
        Me.BntAddAccount.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntAddAccount.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntAddAccount.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntAddAccount.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntAddAccount.AppearanceDisabled.Options.UseBackColor = True
        Me.BntAddAccount.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntAddAccount.AppearanceDisabled.Options.UseFont = True
        Me.BntAddAccount.AppearanceDisabled.Options.UseForeColor = True
        Me.BntAddAccount.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntAddAccount.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAddAccount.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntAddAccount.AppearanceHovered.Options.UseBackColor = True
        Me.BntAddAccount.AppearanceHovered.Options.UseBorderColor = True
        Me.BntAddAccount.AppearanceHovered.Options.UseForeColor = True
        Me.BntAddAccount.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAddAccount.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAddAccount.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntAddAccount.AppearancePressed.Options.UseBackColor = True
        Me.BntAddAccount.AppearancePressed.Options.UseBorderColor = True
        Me.BntAddAccount.AppearancePressed.Options.UseForeColor = True
        Me.BntAddAccount.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BntAddAccount.Location = New System.Drawing.Point(273, 386)
        Me.BntAddAccount.Name = "BntAddAccount"
        Me.BntAddAccount.Size = New System.Drawing.Size(127, 38)
        Me.BntAddAccount.TabIndex = 507
        Me.BntAddAccount.Text = "Add Account"
        '
        'ListBoxControl1
        '
        Me.ListBoxControl1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.ListBoxControl1.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Bold)
        Me.ListBoxControl1.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.ListBoxControl1.Appearance.Options.UseBackColor = True
        Me.ListBoxControl1.Appearance.Options.UseFont = True
        Me.ListBoxControl1.Appearance.Options.UseForeColor = True
        Me.ListBoxControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
        Me.ListBoxControl1.Location = New System.Drawing.Point(173, 155)
        Me.ListBoxControl1.Name = "ListBoxControl1"
        Me.ListBoxControl1.Size = New System.Drawing.Size(624, 200)
        Me.ListBoxControl1.TabIndex = 508
        Me.ListBoxControl1.Visible = False
        '
        'frmQuickAccountCheck
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.BntAddAccount)
        Me.Controls.Add(Me.ListBoxControl1)
        Me.Controls.Add(Me.PnlWait)
        Me.Controls.Add(Me.BntStop)
        Me.Controls.Add(Me.BntResetAll)
        Me.Controls.Add(Me.BntTest)
        Me.Controls.Add(Me.CheckSSL)
        Me.Controls.Add(Me.TxtSMTPServer)
        Me.Controls.Add(Me.TxtPort)
        Me.Controls.Add(Me.TxtEmail)
        Me.Controls.Add(Me.TxtPassword)
        Me.Controls.Add(Me.Label5)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.CheckEdit1)
        Me.IconOptions.Icon = CType(resources.GetObject("frmQuickAccountCheck.IconOptions.Icon"), System.Drawing.Icon)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Logo_NewBestSender
        Me.IconOptions.ShowIcon = False
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Name = "frmQuickAccountCheck"
        Me.Opacity = 0.99R
        Me.ShowInTaskbar = False
        Me.Text = "Test account SMTP"
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PnlWait.ResumeLayout(False)
        CType(Me.CheckSSL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtSMTPServer.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtPort.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtEmail.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtPassword.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ListBoxControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents PnlWait As Panel
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents BntStop As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntResetAll As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntTest As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents CheckSSL As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TxtSMTPServer As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtPort As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtEmail As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtPassword As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label4 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents Label5 As Label
    Friend WithEvents BntAddAccount As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ListBoxControl1 As DevExpress.XtraEditors.ListBoxControl
End Class
