﻿Imports System.IO
Imports System.ComponentModel
Imports OpenQA.Selenium
Imports OpenQA.Selenium.Chrome
Imports System.Linq
Imports DevExpress.XtraEditors
Imports OpenQA.Selenium.Support.UI
Imports System.Management
Imports System.Threading
Public Class YahooVerify
    Private WithEvents BgWorker As New BackgroundWorker()
    Private _virtualModeEmails As New List(Of (String, String, String))() ' (Email, Status, Time)
    Private _isVirtualModeActive As Boolean
    Private lastCheckedIndex As Integer
    Private validEmailCount As Integer
    Private skippedEmailCount As Integer
    Private invalidEmailCount As Integer
    Private driver As IWebDriver
    Private tempResultsFile As String = Path.Combine(Path.GetTempPath(), "YahooVerifyTempResults.txt")
    Public Sub New()
        InitializeComponent()
        ' Setup BackgroundWorker
        BgWorker.WorkerReportsProgress = True
        BgWorker.WorkerSupportsCancellation = True
        ' Setup ListView for virtual mode
        With ListView1
            .View = View.Details
            .Columns.Add("Email", 350, HorizontalAlignment.Left)
            .Columns.Add("Status", 300, HorizontalAlignment.Center)
            .Columns.Add("Time", 175, HorizontalAlignment.Center)
            .FullRowSelect = True
            .GridLines = True
            .VirtualMode = True
        End With
        ' Setup event handlers for virtual mode
        AddHandler ListView1.RetrieveVirtualItem, AddressOf ListView1_RetrieveVirtualItem
        AddHandler ListView1.CacheVirtualItems, AddressOf ListView1_CacheVirtualItems
        ' Setup ProgressPanel
        ProgressPanel1.Visible = False
        ' Delete temporary results file if exists
        If File.Exists(tempResultsFile) Then
            File.Delete(tempResultsFile)
        End If
    End Sub
    Private Sub BtnSelectFile_Click(sender As Object, e As EventArgs) Handles btnSelectFile.Click
        Dim openFileDialog As New OpenFileDialog With {
            .Filter = "Text Files (*.txt)|*.txt",
            .Title = "Select Email List File"
        }
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            ProgressPanel1.Visible = True
            ' Temporarily disable virtual mode during loading
            _isVirtualModeActive = False
            ListView1.VirtualMode = False
            ListView1.Items.Clear()
            ' Reset counters
            lastCheckedIndex = 0
            validEmailCount = 0
            skippedEmailCount = 0
            invalidEmailCount = 0
            _virtualModeEmails.Clear()
            ' Read file line by line
            Dim count As Integer = 0
            Using reader As New StreamReader(openFileDialog.FileName)
                While Not reader.EndOfStream
                    Dim email = reader.ReadLine()
                    If Not String.IsNullOrWhiteSpace(email) Then
                        _virtualModeEmails.Add((email, String.Empty, String.Empty))
                        count += 1
                        ' Show progress every 1000 items
                        If count Mod 1000 = 0 Then
                            Label1.Text = $"Loading... {count} emails"
                            Application.DoEvents()
                        End If
                    End If
                End While
            End Using
            ' Enable virtual mode after loading
            _isVirtualModeActive = True
            ListView1.VirtualMode = True
            ListView1.VirtualListSize = _virtualModeEmails.Count
            Label1.Text = $"Total Emails: {_virtualModeEmails.Count}"
            XtraMessageBox.Show($"Successfully loaded {_virtualModeEmails.Count} emails.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            btnStartCheck.Enabled = True
            ProgressPanel1.Visible = False
            btnStopCheck.Enabled = False
            btnSaveResults.Enabled = False
            btnClearList.Enabled = False
        End If
    End Sub
    Private Sub BtnStartCheck_Click(sender As Object, e As EventArgs) Handles btnStartCheck.Click
        If _virtualModeEmails.Count = 0 Then
            XtraMessageBox.Show("Please select a file that contains the email first!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        If Not BgWorker.IsBusy Then
            ' Reset only if it's a new scan
            If lastCheckedIndex = 0 Then
                validEmailCount = 0
                skippedEmailCount = 0
                invalidEmailCount = 0
                lblTotal.Text = "Valid Emails: 0"
                lblTotalSkipped.Text = "Skipped Emails: 0"
                lblInvalid.Text = "Invalid Emails: 0"
                ' Delete temporary results file if exists
                If File.Exists(tempResultsFile) Then
                    File.Delete(tempResultsFile)
                End If
            End If
            ' Setup and show ProgressPanel
            ProgressPanel1.Visible = True
            btnSelectFile.Enabled = False
            btnSaveResults.Enabled = False
            btnClearList.Enabled = False
            btnStopCheck.Enabled = True
            BgWorker.RunWorkerAsync()
            btnStopCheck.Enabled = True
            btnStartCheck.Enabled = False
        End If
    End Sub
    Private Sub BtnStopCheck_Click(sender As Object, e As EventArgs) Handles btnStopCheck.Click
        btnStopCheck.Enabled = False
        ' Close ChromeDriver process if running
        Dim processes() As Process = Process.GetProcessesByName("chromedriver")
        For Each p As Process In processes
            p.Kill()
        Next
        btnClearList.Enabled = False
        btnSelectFile.Enabled = False
        If BgWorker.IsBusy Then
            ' Force immediate cancellation
            BgWorker.CancelAsync()
            ' Close the Selenium driver immediately
            If driver IsNot Nothing Then
                Try
                    driver.Quit()
                Catch
                End Try
                driver = Nothing
            End If
            btnStartCheck.Enabled = False
            btnSelectFile.Enabled = True
            btnSaveResults.Enabled = True
            btnClearList.Enabled = True
            ProgressPanel1.Visible = False
            ' Update UI immediately
            XtraMessageBox.Show("The scan has been stopped immediately!", "Stop", MessageBoxButtons.OK, MessageBoxIcon.Information)
            btnSelectFile.Enabled = True
            btnSaveResults.Enabled = True
            btnClearList.Enabled = True
        End If
    End Sub
    Private Sub BgWorker_DoWork(sender As Object, e As DoWorkEventArgs) Handles BgWorker.DoWork
        Dim worker As BackgroundWorker = CType(sender, BackgroundWorker)
        Dim totalEmails As Integer = _virtualModeEmails.Count
        Dim ct As CancellationToken = CancellationToken.None ' Create cancellation token
        ' Split work into batches
        Dim batchSize As Integer = 1 ' Reduce batch size for more stability
        Dim batchResults As New List(Of (String, String))
        For i As Integer = lastCheckedIndex To totalEmails - 1
            If worker.CancellationPending OrElse ct.IsCancellationRequested Then
                lastCheckedIndex = i
                e.Cancel = True
                Exit For
            End If
            Dim result As (String, String) = (String.Empty, String.Empty)
            ' Try up to 3 times for each email
            Dim attempts = 0
            Dim success = False
            While attempts < 2 AndAlso Not success
                Try
                    result = CheckEmailSeleniumAsync(_virtualModeEmails(i).Item1, ct).Result
                    success = True
                Catch ex As Exception
                    attempts += 1
                    If attempts = 2 Then
                        result = (_virtualModeEmails(i).Item1, "⚠️ Error after 3 attempts")
                    Else
                        Thread.Sleep(2000) ' Wait 2 seconds before retrying
                    End If
                End Try
            End While
            batchResults.Add(result)
            ' Send results as batch every batchSize
            If batchResults.Count >= batchSize OrElse i = totalEmails - 1 Then
                worker.ReportProgress(CInt((i / totalEmails) * 100), batchResults)
                batchResults = New List(Of (String, String))()
            End If
        Next
        ' Close Selenium browser when done
        If driver IsNot Nothing Then
            Try
                driver.Quit()
            Catch
                ' Ignore any errors during closing
            End Try
            driver = Nothing
        End If
    End Sub
    Private Sub BgWorker_ProgressChanged(sender As Object, e As ProgressChangedEventArgs) Handles BgWorker.ProgressChanged
        ' Process batch results
        Dim batchResults = CType(e.UserState, List(Of (String, String)))
        Using writer As New StreamWriter(tempResultsFile, True)
            For Each result In batchResults
                ' Update data in the list
                Dim index = _virtualModeEmails.FindIndex(Function(x) String.Compare(x.Item1, result.Item1, StringComparison.OrdinalIgnoreCase) = 0)
                If index >= 0 Then
                    _virtualModeEmails(index) = (result.Item1, result.Item2, DateTime.Now.ToString("HH:mm:ss"))
                    ' Update display for the specific item if visible
                    If ListView1.VirtualMode AndAlso ListView1.Items.Count > 0 Then
                        Dim firstVisible = ListView1.TopItem
                        If firstVisible IsNot Nothing Then
                            Dim itemsPerPage = ListView1.ClientSize.Height \ Math.Max(1, ListView1.GetItemRect(0).Height)
                            Dim lastVisibleIndex = Math.Min(firstVisible.Index + itemsPerPage, _virtualModeEmails.Count - 1)
                            If index >= firstVisible.Index AndAlso index <= lastVisibleIndex Then
                                ListView1.RedrawItems(index, index, True)
                            End If
                        End If
                    End If
                End If
                ' Save results to temporary file
                writer.WriteLine($"{result.Item1}|{result.Item2}|{DateTime.Now:HH:mm:ss}")
                ' Update counters
                If result.Item2.Contains("✅") Then
                    validEmailCount += 1
                    lblTotal.Text = $"Valid Emails: {validEmailCount}"
                ElseIf result.Item2.Contains("Skipped") Then
                    skippedEmailCount += 1
                    lblTotalSkipped.Text = $"Skipped Emails: {skippedEmailCount}"
                ElseIf result.Item2.Contains("❌ Invalid") Then
                    invalidEmailCount += 1
                    lblInvalid.Text = $"Invalid Emails: {invalidEmailCount}"
                End If
            Next
        End Using
    End Sub
    Private Async Function CheckEmailSeleniumAsync(email As String, Optional ct As CancellationToken = Nothing) As Task(Of (String, String))
        ' Check if email is from Yahoo
        If Not email.Trim().EndsWith("@yahoo.com", StringComparison.OrdinalIgnoreCase) Then
            Return (email, "Skipped")
        End If
        ' Create new browser if doesn't exist
        If driver Is Nothing Then
            Dim chromeOptions As New ChromeOptions() With {
                .BinaryLocation = "C:\Program Files\Google\Chrome\Application\chrome.exe"
            }
            chromeOptions.AddArguments("--headless", "--disable-gpu", "--no-sandbox", "--disable-dev-shm-usage", "--window-size=1200,900")
            Dim chromeService = ChromeDriverService.CreateDefaultService()
            chromeService.HideCommandPromptWindow = True
            Try
                driver = New ChromeDriver(chromeService, chromeOptions)
            Catch ex As WebDriverException
                Return (email, $"⚠️ WebDriverException: {ex.Message}")
            End Try
        End If
        Try
            ct.ThrowIfCancellationRequested()
            Dim wait As New WebDriverWait(driver, TimeSpan.FromSeconds(15))
            ' Open Yahoo password recovery page
            driver.Navigate().GoToUrl("https://login.yahoo.com/forgot")
            ' Wait for page to load
            Await Task.Delay(3000, ct).ConfigureAwait(False)
            ' Find email input field with better error handling
            Dim emailInput As IWebElement
            Try
                emailInput = wait.Until(Function(d)
                                            ct.ThrowIfCancellationRequested()
                                            Dim inputs = d.FindElements(By.CssSelector("input[name='username'], input[id*='login'], input[type='email'], #username"))
                                            Return inputs.FirstOrDefault(Function(i) i.Displayed AndAlso i.Enabled)
                                        End Function)
                If emailInput Is Nothing Then
                    Return (email, "⚠️ Error: Email field not found")
                End If
                emailInput.Clear()
                emailInput.SendKeys(email)
            Catch ex As Exception
                Return (email, $"⚠️ Error: Email field error - {ex.Message}")
            End Try
            ' Find continue button with multiple options
            Dim continueButton As IWebElement = Nothing
            Try
                ' First try: Find visible button
                continueButton = wait.Until(Function(d)
                                                ct.ThrowIfCancellationRequested()
                                                Dim buttons = d.FindElements(By.CssSelector("button.next, input[type='submit'], button[name='verifyYid'], #verifyYid"))
                                                Return buttons.FirstOrDefault(Function(b) b.Displayed AndAlso b.Enabled)
                                            End Function)
                ' Second try: Use JavaScript if first try failed
                If continueButton Is Nothing Then
                    Dim js As IJavaScriptExecutor = CType(driver, IJavaScriptExecutor)
                    continueButton = CType(js.ExecuteScript("return document.querySelector('button:not([disabled]), input[type=submit]:not([disabled])');"), IWebElement)
                End If
            Catch ex As Exception
                Return (email, "⚠️ Error: Continue button not found")
            End Try
            ' Click continue button if found
            If continueButton IsNot Nothing AndAlso continueButton.Enabled Then
                Try
                    ' Use JavaScript click to avoid clickability issues
                    Dim js As IJavaScriptExecutor = CType(driver, IJavaScriptExecutor)
                    js.ExecuteScript("arguments[0].click();", continueButton)
                    Await Task.Delay(5000, ct).ConfigureAwait(False) ' Increase wait time after click
                Catch ex As Exception
                    Return (email, "⚠️ Error: Button click failed")
                End Try
            Else
                Return (email, "⚠️ Error: Button not available")
            End If
            ' Check for error message (with better exception handling)
            Try
                ' Shorter wait for message (5 seconds)
                Dim shortWait As New WebDriverWait(driver, TimeSpan.FromSeconds(5))
                ' Find any visible error message
                Dim errorMsg = shortWait.Until(Function(d)
                                                   Try
                                                       ct.ThrowIfCancellationRequested()
                                                       Dim errors = d.FindElements(By.CssSelector(".error-msg, .error, .error-message, [role='alert'], .alert-danger, .message-error"))
                                                       Return errors.FirstOrDefault(Function(e) e.Displayed AndAlso e.Text.Trim().Length > 0)
                                                   Catch
                                                       Return Nothing
                                                   End Try
                                               End Function)
                If errorMsg IsNot Nothing Then
                    ' Check error message content
                    Dim errorText = errorMsg.Text.ToLower()
                    If errorText.Contains("not recognize") OrElse
                       errorText.Contains("not found") OrElse
                       errorText.Contains("does not exist") OrElse
                       errorText.Contains("invalid") OrElse
                       errorText.Contains("no account") OrElse
                       errorText.Contains("don't recognize") Then
                        Return (email, "❌ Invalid")
                    End If
                    Return (email, $"⚠️ Error: {errorMsg.Text.Trim()}")
                End If
                Return (email, "✅ Valid")
            Catch ex As WebDriverTimeoutException
                Return (email, "✅ Valid")
            Catch ex As Exception
                Return (email, $"⚠️ Error: {ex.Message}")
            End Try
        Catch ex As Exception
            ' Close browser in case of fatal error
            Try
                driver?.Quit()
            Catch quitEx As Exception
                ' Ignore errors during browser closing
            Finally
                driver = Nothing
            End Try
            Return (email, $"❌ Fatal Error: {ex.Message}")
        End Try
    End Function
    Private Sub ListView1_RetrieveVirtualItem(sender As Object, e As RetrieveVirtualItemEventArgs) Handles ListView1.RetrieveVirtualItem
        If Not _isVirtualModeActive OrElse e.ItemIndex < 0 OrElse e.ItemIndex >= _virtualModeEmails.Count Then
            e.Item = New ListViewItem()
            Return
        End If
        Dim data = _virtualModeEmails(e.ItemIndex)
        Dim item As New ListViewItem(data.Item1) ' Email
        ' Set default white color for unprocessed emails
        item.ForeColor = Color.White
        item.BackColor = Color.FromArgb(48, 54, 74) ' Dark background for contrast
        ' Add status
        Dim statusSubItem As New ListViewItem.ListViewSubItem() With {
            .Text = data.Item2 ' Status
        }
        ' Add time
        Dim timeSubItem As New ListViewItem.ListViewSubItem() With {
            .Text = data.Item3 ' Time
        }
        ' Change text colors based on status (only if email has been processed)
        If Not String.IsNullOrEmpty(data.Item2) Then
            Select Case data.Item2
                Case "✅ Valid"
                    item.ForeColor = Color.FromArgb(34, 203, 121)
                    statusSubItem.ForeColor = Color.Green
                Case "Skipped"
                    item.ForeColor = Color.Orange
                    statusSubItem.ForeColor = Color.Orange
                Case "❌ Invalid"
                    item.ForeColor = Color.Red
                    statusSubItem.ForeColor = Color.Red
                Case Else
                    item.ForeColor = Color.White
                    statusSubItem.ForeColor = Color.White
            End Select
        End If
        item.SubItems.Add(statusSubItem)
        item.SubItems.Add(timeSubItem)
        e.Item = item
    End Sub
    Private Sub ListView1_CacheVirtualItems(sender As Object, e As CacheVirtualItemsEventArgs) Handles ListView1.CacheVirtualItems
        ' Cache optimizations can be implemented here if needed
    End Sub
    Private Sub BgWorker_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BgWorker.RunWorkerCompleted
        ' Stop and hide ProgressPanel
        ProgressPanel1.Visible = False
        btnStopCheck.Enabled = False
        If e.Cancelled Then
            XtraMessageBox.Show("The scan has been stopped! You can continue later.", "Stop", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            btnStopCheck.Enabled = False
            XtraMessageBox.Show("The scan has been completed!", "Completion", MessageBoxButtons.OK, MessageBoxIcon.Information)
            btnStartCheck.Enabled = False
            lastCheckedIndex = 0 ' Reset after complete scan
        End If
    End Sub
    Private Sub BtnSaveResults_Click(sender As Object, e As EventArgs) Handles btnSaveResults.Click
        If Not File.Exists(tempResultsFile) AndAlso _virtualModeEmails.Count = 0 Then
            XtraMessageBox.Show("There is no data to save!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        ' Specify folder on desktop
        Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        Dim resultsFolder As String = Path.Combine(desktopPath, "Yahoo Verify")
        ' Create folder if doesn't exist
        If Not Directory.Exists(resultsFolder) Then
            Directory.CreateDirectory(resultsFolder)
        End If
        ' Create filename with execution timestamp
        Dim timestamp As String = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss")
        Dim validFilePath As String = Path.Combine(resultsFolder, $"valid_{timestamp}.txt")
        Dim invalidFilePath As String = Path.Combine(resultsFolder, $"invalid_{timestamp}.txt")
        Dim skippedFilePath As String = Path.Combine(resultsFolder, $"skipped_{timestamp}.txt")
        ' Extract emails from temporary results file or from _virtualModeEmails
        Dim validEmails As New List(Of String)
        Dim invalidEmails As New List(Of String)
        Dim skippedEmails As New List(Of String)
        If File.Exists(tempResultsFile) Then
            Dim lines = File.ReadAllLines(tempResultsFile)
            For Each line In lines
                Dim parts = line.Split("|"c)
                If parts.Length >= 2 Then
                    Select Case parts(1)
                        Case "✅ Valid"
                            validEmails.Add(parts(0))
                        Case "❌ Invalid"
                            invalidEmails.Add(parts(0))
                        Case "Skipped"
                            skippedEmails.Add(parts(0))
                    End Select
                End If
            Next
        Else
            ' If no temporary file, use _virtualModeEmails
            For Each item In _virtualModeEmails
                Select Case item.Item2
                    Case "✅ Valid"
                        validEmails.Add(item.Item1)
                    Case "❌ Invalid"
                        invalidEmails.Add(item.Item1)
                    Case "Skipped"
                        skippedEmails.Add(item.Item1)
                End Select
            Next
        End If
        ' Save data to files
        If validEmails.Count > 0 Then
            File.WriteAllLines(validFilePath, validEmails)
        End If
        If invalidEmails.Count > 0 Then
            File.WriteAllLines(invalidFilePath, invalidEmails)
        End If
        If skippedEmails.Count > 0 Then
            File.WriteAllLines(skippedFilePath, skippedEmails)
        End If
        ' Show success message without path
        Dim message As New System.Text.StringBuilder()
        message.AppendLine("Results saved successfully!")
        message.AppendLine()
        If validEmails.Count > 0 Then message.AppendLine($"- Valid: {validEmails.Count}")
        If invalidEmails.Count > 0 Then message.AppendLine($"- Invalid: {invalidEmails.Count}")
        If skippedEmails.Count > 0 Then message.AppendLine($"- Skipped: {skippedEmails.Count}")
        XtraMessageBox.Show(message.ToString(), "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        btnStartCheck.Enabled = True
    End Sub
    Private Sub BtnClearList_Click(sender As Object, e As EventArgs) Handles btnClearList.Click
        ' Close ChromeDriver process if running
        Dim processes() As Process = Process.GetProcessesByName("chromedriver")
        For Each p As Process In processes
            p.Kill()
        Next
        Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to delete all data and settings?", "Confirm Deletion", MessageBoxButtons.YesNo, MessageBoxIcon.Warning)
        If result = DialogResult.Yes Then
            ' Clear all data
            _isVirtualModeActive = False
            ListView1.VirtualMode = False
            ListView1.Items.Clear()
            _virtualModeEmails.Clear()
            ListView1.VirtualMode = True
            _isVirtualModeActive = True
            ListView1.VirtualListSize = 0
            ' Reset counters
            validEmailCount = 0
            skippedEmailCount = 0
            invalidEmailCount = 0
            lastCheckedIndex = 0
            ' Update labels
            lblTotal.Text = "Valid Emails: 0"
            lblTotalSkipped.Text = "Skipped Emails: 0"
            lblInvalid.Text = "Invalid Emails: 0"
            Label1.Text = "Total Emails: 0"
            ' Delete temporary results file
            If File.Exists(tempResultsFile) Then
                File.Delete(tempResultsFile)
            End If
            ' Stop scan if running
            If BgWorker.IsBusy Then
                BgWorker.CancelAsync()
                XtraMessageBox.Show("All data has been cleared, and the scan has been stopped!", "Cleared", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                XtraMessageBox.Show("All data has been successfully cleared!", "Cleared", MessageBoxButtons.OK, MessageBoxIcon.Information)
                btnStartCheck.Enabled = True
                btnSelectFile.Enabled = True
                btnSaveResults.Enabled = True
                btnStartCheck.Enabled = False
            End If
        End If
    End Sub
    Private Sub YahooVerify_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        ' Stop any ongoing process
        If BgWorker.IsBusy Then
            BgWorker.CancelAsync()
        End If
        ' Close Selenium browser if open
        If driver IsNot Nothing Then
            Try
                driver.Quit()
                driver.Dispose()
            Catch
            End Try
            driver = Nothing
        End If
        ' Delete temporary results file
        If File.Exists(tempResultsFile) Then
            Try
                File.Delete(tempResultsFile)
            Catch
            End Try
        End If
        ' Clean up other resources
        _virtualModeEmails.Clear()
        ListView1.VirtualListSize = 0
        ListView1.Items.Clear()
    End Sub
    Private Sub YahooVerify_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Dim processes() As Process = Process.GetProcessesByName("chromedriver")
        For Each p As Process In processes
            p.Kill()
        Next
        ' Close conhost.exe process if running
        Dim conhostProcesses() As Process = Process.GetProcessesByName("conhost")
        For Each p As Process In conhostProcesses
            p.Kill()
        Next
        ProgressPanel1.Visible = False
        btnStartCheck.Enabled = False
        btnSelectFile.Enabled = True
        btnStopCheck.Enabled = False
        btnSaveResults.Enabled = False
        btnClearList.Enabled = False
        ' Add options to ComboBox for wait duration
        cmbDelay.Properties.Items.AddRange(New Object() {"1000", "2000", "3000", "5000"})
        cmbDelay.SelectedIndex = 1 ' Default: 2000 milliseconds
    End Sub
End Class