<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Select.HtmlToPdf.NetCore20</name>
    </assembly>
    <members>
        <member name="T:SelectPdf.BeforeCreateNextPageHandler">
            <summary>
            BeforeCreateNextPage event handler
            </summary>
            <param name="bcnpEventArgs">The BeforeCreateNextPage event handler</param>
        </member>
        <member name="T:SelectPdf.BeforeCreateNextPageEventArgs">
            <summary>
            BeforeCreateNextPage event arguments
            </summary>
        </member>
        <member name="P:SelectPdf.BeforeCreateNextPageEventArgs.Cancel">
            <summary>
            Set this property true to cancel automatic creation of next page. 
            This will also end the rendering of the next page.
            </summary>
        </member>
        <member name="T:SelectPdf.BeforeRenderNextPageHandler">
            <summary>
            BeforeRenderNextPage event handler
            </summary>
            <param name="brnpEventArgs">The BeforeRenderNextPage event handler</param>
        </member>
        <member name="T:SelectPdf.BeforeRenderNextPageEventArgs">
            <summary>
            BeforeRenderNextPage event arguments
            </summary>
        </member>
        <member name="M:SelectPdf.BeforeRenderNextPageEventArgs.#ctor(System.Int32,System.Drawing.RectangleF)">
            <summary>
            Constructor
            </summary>
            <param name="pageIndex">The next page index</param>
            <param name="rectangle">The rectangle rendered on the next page</param>
        </member>
        <member name="P:SelectPdf.BeforeRenderNextPageEventArgs.Cancel">
            <summary>
            Set this property true to cancel rendering on next page
            </summary>
        </member>
        <member name="P:SelectPdf.BeforeRenderNextPageEventArgs.Rectangle">
            <summary>
            The rectangle that will be rendered on the next page
            </summary>
        </member>
        <member name="P:SelectPdf.BeforeRenderNextPageEventArgs.PageIndex">
            <summary>
            The index of the next page
            </summary>
        </member>
        <member name="T:SelectPdf.GlobalProperties">
            <summary>
            Global properties for SelectPdf SDK.
            </summary>
        </member>
        <member name="P:SelectPdf.GlobalProperties.HtmlEngineFullPath">
            <summary>
            Gets or sets the full path (including the file name) of the html rendering engine helper file. 
            </summary>
        </member>
        <member name="P:SelectPdf.GlobalProperties.PdfToolsFullPath">
            <summary>
            Gets or sets the full path (including the file name) of the pdf tools engine helper file. 
            </summary>
        </member>
        <member name="P:SelectPdf.GlobalProperties.LFN">
            <summary>
            Internal use only.
            </summary>
        </member>
        <member name="P:SelectPdf.GlobalProperties.ForceDenyLocalFileAccess">
            <summary>
            A flag indicating if local files can be loaded during the conversion. The default value is False and local files can be loaded.
            </summary>
            <remarks>
            This global flag takes priority. If it is set, the corresponding per object flag (DenyLocalFileAccess) is ignored.</remarks>
        </member>
        <member name="P:SelectPdf.GlobalProperties.EnableRestrictedRenderingEngine">
            <summary>
            Enable or disable restricted rendering engine.
            </summary>
            <remarks>
            There are environments, such as Microsoft Azure Web Apps, where GDI calls are restricted and the default rendering engine does not work.
            To enable an alternative rendering engine (with some restrictions - no web fonts for html to pdf conversions, one page PdfHtmlElement objects) set this property to True. 
            The default value for this property is False and the restricted rendering engine is not enabled by default.
            </remarks>
        </member>
        <member name="P:SelectPdf.GlobalProperties.EnableFallbackToRestrictedRenderingEngine">
            <summary>
            Enable or disable fall-back to restricted rendering engine.
            </summary>
            <remarks>
            There are environments, such as Microsoft Azure Web Apps, where GDI calls are restricted and the default rendering engine does not work.
            To enable an alternative rendering engine (with some restrictions - no web fonts for html to pdf conversions, one page PdfHtmlElement objects) 
            set <see cref="P:SelectPdf.GlobalProperties.EnableRestrictedRenderingEngine"/> to True,
            or set this property to True to automatically enable the restricted rendering engine if the main engine fails.
            The default value for this property is True and the restricted rendering engine is activated when main engine fails.
            </remarks>
        </member>
        <member name="T:SelectPdf.ConverterUtils">
            <summary>
            HTML converter utilities
            </summary>
        </member>
        <member name="M:SelectPdf.ConverterUtils.GetHtmlFromURL(System.String)">
            <summary>
            Get the HTML code from the specified URL. Use the autodetermined page encoding
            to create the resulted string object. The default code page is UTF8 if the page
            has no encoding specified
            </summary>
            <param name="url">The URL from where to get the HTML</param>
            <returns>The page HTML string</returns>
        </member>
        <member name="M:SelectPdf.ConverterUtils.GetHtmlFromURL(System.String,System.Text.Encoding)">
            <summary>
            Get the HTML code from the specified URL. Use the specified page encoding
            to create the resulted string object. 
            </summary>
            <param name="url">The URL from where to get the HTML</param>
            <param name="pageEncoding">The encoding used to build the resulted string object</param>
            <returns>The page HTML string</returns>
        </member>
        <member name="M:SelectPdf.ConverterUtils.GetHtmlFromURL(System.Net.HttpWebRequest,System.Text.Encoding)">
            <summary>
            
            </summary>
            <param name="request">The HttpWebRequest object to to make the HTTP request</param>
            <param name="pageEncoding">The encoding used to build the resulted string object</param>
            <returns>The page HTML string</returns>
        </member>
        <member name="T:SelectPdf.HiddenWebElements">
            <summary>
            Helps defining a set of html elements that will not be displayed in the generated pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.HiddenWebElements.CssSelectors">
            <summary>
            This property is used to define an array containing the selectors of the html elements that will not be displayed in the final pdf document. 
            For example, the selector for all the image elements is "img", the selector for all the elements with the CSS class name 'myclass'
            is "*.myclass" and the selector for the elements with the id 'myid' is "*#myid".
            </summary>
        </member>
        <member name="T:SelectPdf.SecureProtocol">
            <summary>
            Protocol used for secure (HTTPS) connections.
            </summary>
        </member>
        <member name="F:SelectPdf.SecureProtocol.Tls11OrNewer">
            <summary>
            TLS 1.1 or newer. Recommended value.
            </summary>
        </member>
        <member name="F:SelectPdf.SecureProtocol.Tls10">
            <summary>
            TLS 1.0 only.
            </summary>
        </member>
        <member name="F:SelectPdf.SecureProtocol.Ssl3">
            <summary>
            SSL v3 only.
            </summary>
        </member>
        <member name="T:SelectPdf.HtmlToImage">
            <summary>
            Html to Image Converter. This class offers the API needed to create images in various formats from
            a specified web page or html string.
            </summary>
        </member>
        <member name="M:SelectPdf.HtmlToImage.#ctor">
            <summary>
            Creates an html to image converter. Width and height of the web page are automatically detected.
            </summary>
        </member>
        <member name="M:SelectPdf.HtmlToImage.#ctor(System.Int32)">
            <summary>
            Creates an html to image converter. The width of the web page is specified. The height of the web page is automatically detected.
            </summary>
            <param name="webPageWidth">The web page width.</param>
        </member>
        <member name="M:SelectPdf.HtmlToImage.#ctor(System.Int32,System.Int32)">
            <summary>
            Creates an html to image converter for a web page with the specified width and height.
            </summary>
            <param name="webPageWidth">The web page width.</param>
            <param name="webPageHeight">The web page height.</param>
        </member>
        <member name="P:SelectPdf.HtmlToImage.LFN">
            <summary>
            Internal use only.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToImage.VisibleWebElementId">
            <summary>
            Use this property to convert only a certain section of the web page, specified by the html element ID.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToImage.StartupMode">
            <summary>
            Use this property to specify how the conversion starts.
            </summary>
            <remarks>
            By default this is set to <see cref="F:SelectPdf.HtmlToPdfStartupMode.Automatic"/> and the conversion is started as soon as the page loads (and <see cref="P:SelectPdf.HtmlToImage.MinPageLoadTime"/> elapses). 
            If set to <see cref="F:SelectPdf.HtmlToPdfStartupMode.Manual"/>, the conversion is started only by a javascript call to <c>SelectPdf.startConversion()</c> from within the web page.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.StartupScript">
            <summary>
            Use this property to specify some JavaScript code that will be injected into the page that is converted. 
            </summary>
            <remarks>The JavaScript code specified here will run before any other script on the page.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.WebPageWidth">
            <summary>
            Gets or sets the web page width.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToImage.WebPageHeight">
            <summary>
            Gets or sets the web page height. If the width was not set, this property has no effect.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToImage.WebPageFixedSize">
            <summary>Controls whether the web page is rendered with a fixed size internal browser or the size automatically extends to make the whole content visible.</summary>
            <remarks>
            If the html content is not entirely visible in the web page viewer (browser) at the specified width, this property will control whether the 
            html content is clipped or the web page browser width is automatically extended to make visible the whole html content. 
            <para></para>
            The html browser width is given by the <see cref="P:SelectPdf.HtmlToImage.WebPageWidth"/> property. 
            <para></para>
            The default value of this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.JavaScriptEnabled">
            <summary>
            Enable scripts when rendering the url or html string. 
            </summary>
            <remarks>The default value of this property is true and the converter will run any javascript available when the page loads.
            <para></para>
            <para>Note: If the javascript requires some time to load, <see cref="P:SelectPdf.HtmlToImage.MinPageLoadTime"/> property should be set to delay the conversion with the specified number of seconds and allow the javascript to run.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.PluginsEnabled">
            <summary>
            A flag indicating if plugins (like Flash players) are enabled in the converter. 
            </summary>
            <remarks>
            The default value for this property is true.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.AllowContentHeightResize">
            <summary>
            A flag used only by WebKitRestricted or Blink engine indicating if the content height can be recalculated to better fit the page. This can introduce certain errors in some cases.
            The default is True. Set it to False if content is split to more pages than expected.
            </summary>
            <remarks>Note: This is used only by the WebKitRestricted rendering engine.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.ProxyOptions">
            <summary>
            Gets a reference to an object containing the proxy settings used to access the web page that is being converted.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToImage.AuthenticationUsername">
            <summary>
            Specifies the username if the web page being converted requires authentication.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToImage.AuthenticationPassword">
            <summary>
            Specifies the password if the web page being converted requires authentication.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToImage.MinPageLoadTime">
            <summary>
            An additional time in seconds to wait for asynchronous items to be loaded before the web page is rendered.
            </summary>
            <remarks>
            A delay of this type could be necessary if javascripts are loaded when the page is rendered or if Flash movies are played.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.MaxPageLoadTime">
            <summary>
            The web page navigation timeout in seconds. 
            </summary>
            <remarks>Default value is 60 seconds. This value should be increased if large web pages are loaded.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.SecureProtocol">
            <summary>
            Protocol used for secure (HTTPS) connections. 
            </summary>
            <remarks>Default and recommended value is Tls11OrNewer. It means that TLS 1.1 or newer is used for secure connections.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.HttpCookies">
            <summary>
            Gets the collection of custom HTTP cookies used for the conversion. 
            </summary>
            <remarks>
            This property returns a reference to the collection of custom HTTP cookies to be sent by the converter to the web server when the web page is rendered. 
            A cookie is defined by a name/value pair that can be added to the collection using the <see cref="M:System.Collections.Specialized.NameValueCollection.Add(System.String,System.String)"/> method of the <see cref="P:SelectPdf.HtmlToImage.HttpCookies"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.HttpHeaders">
            <summary>
            Get the collection of custom HTTP headers used for the conversion.
            </summary>
            <remarks>
            This property returns a reference to the collection of custom HTTP headers to be sent by the converter to the web server when the web page is rendered.
            A custom HTTP header is defined by a name/value pair
            that can be added to the collection using the <see cref="M:System.Collections.Specialized.NameValueCollection.Add(System.String,System.String)"/> method of the <see cref="P:SelectPdf.HtmlToImage.HttpHeaders"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.HttpPostParameters">
            <summary>
            Gets the collection of custom HTTP POST parameters used for the conversion. 
            </summary>
            <remarks>
            This property returns a reference to the collection of custom HTTP POST parameters that will be sent by the converter to the web server when the web page is rendered. 
            A POST parameter is defined by a name/value pair that can be added to the collection using the <see cref="M:System.Collections.Specialized.NameValueCollection.Add(System.String,System.String)"/> method of the <see cref="P:SelectPdf.HtmlToImage.HttpPostParameters"/> property.
            When this collection has at least one parameter, the rendered page is a accessed via POST method. Otherwise the method is GET.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToImage.RenderingEngine">
            <summary>
            Gets or sets the rendering engine used by the converter to load and render the HTML. The possible values are WebKit, WebKitRestricted and Blink. 
            The Webkit rendering engine is internal and renders similar to Apple's Safari. For Blink, Chromium binaries must be also installed.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToImage.DenyLocalFileAccess">
            <summary>
            A flag indicating if local files can be loaded during the conversion. The default value is False and local files can be loaded.
            </summary>
        </member>
        <member name="M:SelectPdf.HtmlToImage.ConvertUrl(System.String)">
            <summary>
            Gets an <see cref="T:System.Drawing.Image"/> object from the specified url. 
            </summary>
            <remarks>The Image object can be further manipulated to have the format changed, saved to disk, etc.</remarks>
            <param name="url">The full url of the web page to be converted to image. The url can be also the full path of a html file from the local file system.</param>
            <returns>The rendered Image object.</returns>
        </member>
        <member name="M:SelectPdf.HtmlToImage.ConvertHtmlString(System.String)">
            <summary>
            Gets an <see cref="T:System.Drawing.Image"/> object from the specified html string.
            </summary>
            <remarks> The Image object can be further manipulated to have the format changed, saved to disk, etc.</remarks>
            <param name="htmlString">The html string to be converted to image.</param>
            <returns>The rendered Image object.</returns>
        </member>
        <member name="M:SelectPdf.HtmlToImage.ConvertHtmlString(System.String,System.String)">
            <summary>
            Gets an <see cref="T:System.Drawing.Image"/> object from the specified html string. 
            </summary>
            <remarks>
            The <c>baseUrl</c> parameter allows the converter to determine the full absolute urls from relative urls for images and css files appearing in the html string. 
            <para></para>
            The Image object can be further manipulated to have the format changed, saved to disk, etc.
            </remarks>
            <param name="htmlString">The html string to be converted to image.</param>
            <param name="baseUrl">This parameter allows the converter to resolve relative urls. Basically, <c>baseUrl + relative image/css url = full absolute url</c>.</param>
            <returns>The rendered Image object.</returns>
        </member>
        <member name="T:SelectPdf.HtmlToPdf">
            <summary>
            Html To Pdf Converter. This class offers the API needed to convert any web page url or html string to pdf. 
            </summary>
            <remarks>
            The result of conversion is a <see cref="T:SelectPdf.PdfDocument"/> object that can be further modified, saved to memory or into a file on the disk.
            </remarks>
            <example>
            The following sample code shows how an HtmlToPdf Converter can be instantiated and a basic conversion performed.
            <code language="cs">
            using SelectPdf;
            HtmlToPdf converter = new HtmlToPdf();
            PdfDocument doc = converter.ConvertUrl(url);
            doc.Save(file);
            doc.Close();
            </code>
            <code language="vb">
            Imports SelectPdf
            Dim converter as HtmlToPdf  = New HtmlToPdf()
            Dim doc as PdfDocument = converter.ConvertUrl(url)
            doc.Save(file)
            doc.Close()
            </code>
            </example>
        </member>
        <member name="M:SelectPdf.HtmlToPdf.#ctor">
            <summary>
            Initializes an instance of the HtmlToPdf Converter. 
            </summary>
            <remarks>
            The web page is rendered by an internal browser having the default width of 1024px.
            The height of the page is automatically calculated. The width and height can be set using <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageWidth"/> and <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/> properties of <see cref="P:SelectPdf.HtmlToPdf.Options"/> property.
            <para>Usually, the web page width and height do not need to be set and the converter automatically calculates them, 
            but there are some situations (web pages with frames for example) when the converter cannot calculate the page size correctly. 
            In this case, the web page width and height needs to be set, otherwise no content will appear in the generated pdf document.</para>
            </remarks>
            <example>
            The following sample code shows how an HtmlToPdf Converter can be instantiated and a basic conversion performed.
            <code language="cs">
            HtmlToPdf converter = new HtmlToPdf();
            PdfDocument doc = converter.ConvertUrl(url);
            doc.Save(file);
            doc.Close();
            </code>
            <code language="vb">
            Dim converter as HtmlToPdf  = New HtmlToPdf()
            Dim doc as PdfDocument = converter.ConvertUrl(url)
            doc.Save(file)
            doc.Close()
            </code>
            </example>
            <seealso cref="P:SelectPdf.HtmlToPdfOptions.WebPageWidth"/>
            <seealso cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/>
        </member>
        <member name="M:SelectPdf.HtmlToPdf.#ctor(System.Int32)">
            <summary>
            Initializes an instance of the HtmlToPdf Converter. 
            </summary>
            <remarks>
            The web page is rendered by an internal browser having the width specified by the <c>webPageWidth</c> parameter and 
            the height automatically calculated. 
            The width and height can be also set using <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageWidth"/> and <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/> properties of <see cref="P:SelectPdf.HtmlToPdf.Options"/> property.
            <para>Usually, the web page height does not need to be set and the converter automatically calculates it, 
            but there are some situations (web pages with frames for example) when the converter cannot calculate the page height correctly. 
            In this case, the web page needs to be set, otherwise no content will appear in the generated pdf document.</para>
            </remarks>
            <param name="webPageWidth">Width in pixels of the internal browser that renders the web page.</param>
            <example>
            The following sample code shows how an HtmlToPdf Converter can be instantiated and a basic conversion performed.
            <code language="cs">
            HtmlToPdf converter = new HtmlToPdf(width);
            PdfDocument doc = converter.ConvertUrl(url);
            doc.Save(file);
            doc.Close();
            </code>
            <code language="vb">
            Dim converter as HtmlToPdf  = New HtmlToPdf(width)
            Dim doc as PdfDocument = converter.ConvertUrl(url)
            doc.Save(file)
            doc.Close()
            </code>
            </example>
            <seealso cref="P:SelectPdf.HtmlToPdfOptions.WebPageWidth"/>
            <seealso cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/>
        </member>
        <member name="M:SelectPdf.HtmlToPdf.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes an instance of the HtmlToPdf Converter. 
            </summary>
            <remarks>
            The web page is rendered by an internal browser having the width specified by the <c>webPageWidth</c> parameter and 
            the height of the page specified by the <c>webPageHeight</c> parameter. 
            The width and height can be also set using <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageWidth"/> and <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/> properties of <see cref="P:SelectPdf.HtmlToPdf.Options"/> property.
            <para>If <c>webPageHeight</c> is set, the web page will be truncated to the specified height. 
            Usually, <c>webPageHeight</c> should be set to 0 to determine the converter to automatically calculate the page height, 
            but there are some situations (web pages with frames for example) when the converter cannot calculate the page height correctly. 
            In this case, <c>webPageHeight</c> needs to be set, otherwise no content will appear in the generated pdf document.</para>
            </remarks>
            <param name="webPageWidth">Width in pixels of the internal browser that renders the web page.</param>
            <param name="webPageHeight">Height in pixels of the internal browser that renders the web page.</param>
            <example>
            The following sample code shows how an HtmlToPdf Converter can be instantiated and a basic conversion performed.
            <code language="cs">
            HtmlToPdf converter = new HtmlToPdf(width, height);
            PdfDocument doc = converter.ConvertUrl(url);
            doc.Save(file);
            doc.Close();
            </code>
            <code language="vb">
            Dim converter as HtmlToPdf  = New HtmlToPdf(width, height)
            Dim doc as PdfDocument = converter.ConvertUrl(url)
            doc.Save(file)
            doc.Close()
            </code>
            </example>
            <seealso cref="P:SelectPdf.HtmlToPdfOptions.WebPageWidth"/>
            <seealso cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/>
        </member>
        <member name="P:SelectPdf.HtmlToPdf.Options">
            <summary>
            Gets a reference to an object that controls the html to pdf conversion process and the generated pdf document properties. 
            </summary>
            <remarks>
            This property can be used to control the generated pdf header, footer, margins, page size and orientation, compression level, content resizing and many more. 
            Check <see cref="T:SelectPdf.HtmlToPdfOptions"/> for complete details.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdf.Header">
            <summary>
            Gets a reference to an object that controls the properties of the custom header of the generated pdf document. 
            </summary>
            <remarks>The header is displayed in the generated pdf document only if the <see cref="P:SelectPdf.HtmlToPdfOptions.DisplayHeader"/> property is set to true.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdf.Footer">
            <summary>
            Gets a reference to an object that controls the properties of the custom footer of the generated pdf document. 
            </summary>
            <remarks>The footer is displayed in the generated pdf document only if the <see cref="P:SelectPdf.HtmlToPdfOptions.DisplayFooter"/> property is set to true.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdf.ConversionResult">
            <summary>
            Gets a reference to an object that offers information about the result of the html to pdf conversion process. 
            </summary>
            <remarks>
            After a successful conversion this property is initialized with an instance of the <see cref="T:SelectPdf.HtmlToPdfResult"/> class. 
            If the conversion failed this property returns a null value.
            </remarks>
        </member>
        <member name="M:SelectPdf.HtmlToPdf.ConvertUrl(System.String)">
            <summary>
            Gets a <see cref="T:SelectPdf.PdfDocument"/> object representing the result of the conversion of the specified url to pdf.
            </summary>
            <remarks>
            The resulted <see cref="T:SelectPdf.PdfDocument"/> object can be modified and saved to a file, to a stream, to a memory buffer or to a HTTP response.
            The <see cref="T:SelectPdf.PdfDocument"/> object must be closed after save to release the system resources used during conversion.
            </remarks>
            <param name="url">The full url of the web page to be converted to pdf. The url can be also the full path of a html file from the local file system.</param>
            <returns>The <see cref="T:SelectPdf.PdfDocument"/> object that can be modified and saved to a file, to a stream, to a memory buffer or to a HTTP response.
            </returns>
            <example>
            The following sample code shows how an HtmlToPdf Converter can be instantiated and a basic conversion performed.
            <code language="cs">
            HtmlToPdf converter = new HtmlToPdf();
            PdfDocument doc = converter.ConvertUrl(url);
            doc.Save(file);
            doc.Close();
            </code>
            <code language="vb">
            Dim converter as HtmlToPdf  = New HtmlToPdf()
            Dim doc as PdfDocument = converter.ConvertUrl(url)
            doc.Save(file)
            doc.Close()
            </code>
            </example>
        </member>
        <member name="M:SelectPdf.HtmlToPdf.ConvertHtmlString(System.String)">
            <summary>
            Gets a <see cref="T:SelectPdf.PdfDocument"/> object representing the result of the conversion of the specified html string to pdf. 
            </summary>
            <remarks>
            The resulted <see cref="T:SelectPdf.PdfDocument"/> object can be modified and saved to a file, to a stream, to a memory buffer or to a HTTP response.
            The <see cref="T:SelectPdf.PdfDocument"/> object must be closed after save to release the system resources used during conversion.
            <para>
            This method does not use any base url and therefore the images or css files referenced by relative urls in the html string cannot be resolved by the converter to an absolute url. 
            If there are external resources referenced by relative urls, the <see cref="M:SelectPdf.HtmlToPdf.ConvertHtmlString(System.String,System.String)"/>  method with the <c>baseUrl</c> additional parameter should be used.
            </para>
            </remarks>
            <param name="htmlString">The html string to be converted to pdf.</param>
            <returns>The <see cref="T:SelectPdf.PdfDocument"/> object that can be modified and saved to a file, to a stream, to a memory buffer or to a HTTP response.
            </returns>
            <example>
            The following sample code shows how an HtmlToPdf Converter can be instantiated and a basic conversion performed.
            <code language="cs">
            HtmlToPdf converter = new HtmlToPdf();
            PdfDocument doc = converter.ConvertHtmlString(html);
            doc.Save(file);
            doc.Close();
            </code>
            <code language="vb">
            Dim converter as HtmlToPdf  = New HtmlToPdf()
            Dim doc as PdfDocument = converter.ConvertString(html)
            doc.Save(file)
            doc.Close()
            </code>
            </example>
        </member>
        <member name="M:SelectPdf.HtmlToPdf.ConvertHtmlString(System.String,System.String)">
            <summary>
            Gets a <see cref="T:SelectPdf.PdfDocument"/> object representing the result of the conversion of the specified html string to pdf. 
            </summary>
            <remarks>
            The resulted <see cref="T:SelectPdf.PdfDocument"/> object can be modified and saved to a file, to a stream, to a memory buffer or to a HTTP response.
            The <see cref="T:SelectPdf.PdfDocument"/> object must be closed after save to release the system resources used during conversion.
            <para>
            The <c>baseUrl</c> parameter allows the converter to determine the full absolute urls from relative urls for images and css files appearing in the html string. 
            </para>
            </remarks>
            <param name="htmlString">The html string to be converted to pdf.</param>
            <param name="baseUrl">This parameter allows the converter to resolve relative urls. Basically, <c>baseUrl + relative image/css url = full absolute url</c>.</param>
            <returns>The <see cref="T:SelectPdf.PdfDocument"/> object that can be modified and saved to a file, to a stream, to a memory buffer or to a HTTP response.
            </returns>
            <example>
            The following sample code shows how an HtmlToPdf Converter can be instantiated and a basic conversion performed.
            <code language="cs">
            HtmlToPdf converter = new HtmlToPdf();
            PdfDocument doc = converter.ConvertHtmlString(html, baseUrl);
            doc.Save(file);
            doc.Close();
            </code>
            <code language="vb">
            Dim converter as HtmlToPdf  = New HtmlToPdf()
            Dim doc as PdfDocument = converter.ConvertString(html, baseUrl)
            doc.Save(file)
            doc.Close()
            </code>
            </example>
        </member>
        <member name="T:SelectPdf.HtmlToPdfAuthenticationOptions">
            <summary>
            Authentication options needed when the web page to be converted requires authentication.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfAuthenticationOptions.Username">
            <summary>
            Specifies the user name if the web page to be converted requires authentication.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfAuthenticationOptions.Password">
            <summary>
            Specifies the password if the web page to be converted requires authentication.
            </summary>
        </member>
        <member name="T:SelectPdf.HtmlToPdfCssMediaType">
            <summary>
            Specifies what css styles are loaded when the html is rendered
            </summary>
        </member>
        <member name="F:SelectPdf.HtmlToPdfCssMediaType.Screen">
            <summary>
            Default (screen) css styles are loaded
            </summary>
        </member>
        <member name="F:SelectPdf.HtmlToPdfCssMediaType.Print">
            <summary>
            Print css styles are loaded
            </summary>
        </member>
        <member name="T:SelectPdf.HtmlToPdfDocumentInformation">
            <summary>
            This class provides the possibility to control the pdf document properties.
            </summary>
            <remarks>
            Using this class, the pdf document properties (author, title, keywords, etc) can be controlled. The <see cref="T:SelectPdf.HtmlToPdfOptions"/> class defines 
            a reference to an object of this type.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfDocumentInformation.Author">
            <summary>
            Gets or sets the name of the pdf document author.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfDocumentInformation.CreationDate">
            <summary>
            Gets or sets the date and time when the pdf document was created.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfDocumentInformation.Keywords">
            <summary>
            Gets or sets the pdf document keywords.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfDocumentInformation.Producer">
            <summary>
            Gets the name of the application that generated the pdf document (Select.Pdf SDK).
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfDocumentInformation.Subject">
            <summary>
            Gets or sets the subject of the pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfDocumentInformation.Title">
            <summary>
            Gets or sets the pdf document title.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfDocumentInformation.Language">
            <summary>
            Gets or sets the default language of the pdf document.
            </summary>
        </member>
        <member name="T:SelectPdf.HtmlToPdfException">
            <summary>
            Exception thrown by the html to pdf converter related objects.
            </summary>
        </member>
        <member name="M:SelectPdf.HtmlToPdfException.#ctor(System.String)">
            <summary>
            Initializes a <see cref="T:SelectPdf.HtmlToPdfException"/> object.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:SelectPdf.HtmlToPdfException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a <see cref="T:SelectPdf.HtmlToPdfException"/> object.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception that triggered this exception.</param>
        </member>
        <member name="T:SelectPdf.HtmlToPdfOptions">
            <summary>
            This class contains various options used for the html to pdf conversion. 
            </summary>
            <remarks>
            The <see cref="T:SelectPdf.HtmlToPdf"/> class defines a reference to an object of this type.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.MarginLeft">
            <summary>
            The left margin of the pdf document.
            </summary>
            <remarks>
            The margin is specified in points. 1 point is 1/72 inch.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.MarginRight">
            <summary>
            The right margin of the pdf document.
            </summary>
            <remarks>
            The margin is specified in points. 1 point is 1/72 inch.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.MarginTop">
            <summary>
            The top margin of the pdf document.
            </summary>
            <remarks>
            The margin is specified in points. 1 point is 1/72 inch.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.MarginBottom">
            <summary>
            The bottom margin of the pdf document.
            </summary>
            <remarks>
            The margin is specified in points. 1 point is 1/72 inch.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.PdfCompressionLevel">
            <summary>
            This property controls the compression level of the generated pdf document.
            </summary>
            <remarks>The default value is <see cref="F:SelectPdf.PdfCompressionLevel.Normal"/>.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.JpegCompressionEnabled">
            <summary>
            Gets or sets a flag indicating if the JPEG compression is enabled or not for the images in the generated pdf document.
            </summary>
            <remarks>
            By default the JPEG compression is enabled to reduce the size of the generated pdf document. When the JPEG compression is enabled
            the quality of the images in the generated pdf is lower than the quality of the original images from the web page,
            and this quality depends on the <see cref="P:SelectPdf.HtmlToPdfOptions.JpegCompressionLevel"/> parameter. When the <see cref="P:SelectPdf.HtmlToPdfOptions.JpegCompressionLevel"/> is increased, the quality of the images in pdf decreases.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.JpegCompressionLevel">
            <summary>
            Gets or sets the compression level of images in the generated pdf document.
            </summary>
            <remarks>
            This property can take values between 0 and 100. It has effect only when the <see cref="P:SelectPdf.HtmlToPdfOptions.JpegCompressionEnabled"/> is set to true.
            When the <see cref="P:SelectPdf.HtmlToPdfOptions.JpegCompressionLevel"/> is 0, the compression rate is the lowest and the quality of the images is the best.
            When the <see cref="P:SelectPdf.HtmlToPdfOptions.JpegCompressionLevel"/> is 100, the compression rate is the highest and quality of the images in PDF is the worst.
            <para></para>
            The default JPEG compression level is 10, which should offer a good balance between the compression rate and 
            the quality of the images in pdf.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.PdfPageSize">
            <summary>
            This property controls the size of the generated document pages. 
            </summary>
            <remarks>The default size of the pdf document pages is <see cref="F:SelectPdf.PdfPageSize.A4"/>. When this property is set to <see cref="F:SelectPdf.PdfPageSize.Custom"/>, 
            a custom size can be specified for the generated pdf document pages using the <see cref="P:SelectPdf.HtmlToPdfOptions.PdfPageCustomSize"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.PdfPageCustomSize">
            <summary>
            This property can be used to specify a custom pdf document page size.
            </summary>
            <remarks>
            The property is taken into account only when <see cref="P:SelectPdf.HtmlToPdfOptions.PdfPageSize"/> property is set to <see cref="F:SelectPdf.PdfPageSize.Custom"/>. 
            The custom width and height of the pdf page is specified in points. 1 point is 1/72 inch.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.AutoFitWidth">
            <summary>Specifies the html content horizontal auto fit mode.</summary>
            <remarks>
            The converter considers both <see cref="P:SelectPdf.HtmlToPdfOptions.AutoFitWidth"/> and <see cref="P:SelectPdf.HtmlToPdfOptions.AutoFitHeight"/> when the html content is rendered in the pdf page or specified rectangle.
            <para></para>
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.NoAdjustment"/>, the html content is not resized horizontally in any way to fit the available space. If the content is larger,
            it will be cut and not all of it will be displayed in the generated pdf file.
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.ShrinkOnly"/>, the html content is resized only if the content width is larger than the destination space 
            (pdf page or rectangle) width.
            In this case, the content is shrunk to fit the destination space width and the elements that it contains (texts, images) will appear smaller in the generated 
            pdf document than in the original web page.
            If the original content width is smaller than the destination width, no adjustments will be done and the content will be rendered exactly as it is, 
            even though some additional white space might appear to its right.
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.AutoFit"/>, the html content is resized to fit the available width of the destination space. 
            If the original content width is smaller than the destination width, 
            the elements rendered (texts, images) will appear larger in the generated pdf document. If the original content width is larger than the destination width, 
            the elements rendered (texts, images) will appear smaller in the generated pdf document.
            <para>The default value of this property is <see cref="F:SelectPdf.HtmlToPdfPageFitMode.ShrinkOnly"/>.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.AutoFitHeight">
            <summary>Specifies the html content vertical auto fit mode.</summary>
            <remarks>
            The converter considers both <see cref="P:SelectPdf.HtmlToPdfOptions.AutoFitWidth"/> and <see cref="P:SelectPdf.HtmlToPdfOptions.AutoFitHeight"/> when the html content is rendered in the pdf page or specified rectangle.
            <para></para>
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.NoAdjustment"/>, the html content is not resized vertically in any way to fit the available space. If the content is larger,
            it will be cut and not all of it will be displayed in the generated pdf file.
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.ShrinkOnly"/>, the html content is resized only if the content height is larger than the destination space 
            (pdf page or rectangle) height.
            In this case, the content is shrunk to fit the destination space height and the elements that it contains (texts, images) will appear smaller in the generated 
            pdf document than in the original web page.
            If the original content height is smaller than the destination height, no adjustments will be done and the content will be rendered exactly as it is, 
            even though some additional white space might appear at the bottom.
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.AutoFit"/>, the converter will treat it like <see cref="F:SelectPdf.HtmlToPdfPageFitMode.ShrinkOnly"/>.
            <para>The default value of this property is <see cref="F:SelectPdf.HtmlToPdfPageFitMode.NoAdjustment"/> and for rendering, the converter will only take <see cref="P:SelectPdf.HtmlToPdfOptions.AutoFitWidth"/> into consideration.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.PdfPageOrientation">
            <summary>
            This property controls the page orientation of the generated pdf document pages. 
            </summary>
            <remarks>The default pdf page orientation is <see cref="F:SelectPdf.PdfPageOrientation.Portrait"/>.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.DisplayHeader">
            <summary>
            Controls if a custom header is displayed in the generated pdf document.
            </summary>
            <remarks>
            The header properties can be customized using the <see cref="T:SelectPdf.PdfHeader"/> object exposed by <see cref="P:SelectPdf.HtmlToPdf.Header"/> property of the <see cref="T:SelectPdf.HtmlToPdf"/> converter class.
            <para>Note: The default value of this property is false and the generated pdf document will not have a custom header.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.DisplayFooter">
            <summary>
            Controls if a custom footer is displayed in the generated pdf document.
            </summary>
            <remarks>
            The footer properties can be customized using the <see cref="T:SelectPdf.PdfFooter"/> object exposed by <see cref="P:SelectPdf.HtmlToPdf.Footer"/> property of the <see cref="T:SelectPdf.HtmlToPdf"/> converter class.
            <para>Note: The default value of this property is false and the generated pdf document will not have a custom footer.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.EmbedFonts">
            <summary>
            Instructs the converter to embed all the needed fonts into the pdf document or not.
            </summary>
            <remarks>
            The default value for this property is false and the fonts are not automatically embedded.
            <para>
            This property does not control the web fonts (fonts that are downloaded with the web page). The web fonts are always embedded because they are not installed on the machine.
            </para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.ExternalLinksEnabled">
            <summary>
            Controls the rendering of external hyperlinks in pdf.
            </summary>
            <remarks>
            The default value for this property is true and in this case all external hyperlinks from the web page will be rendered in the generated pdf document.
            <para>When this property is set to false, external links are ignore and they do not appear in the generated pdf document.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.InternalLinksEnabled">
            <summary>
            Controls the conversion of internal html links to internal pdf links.
            </summary>
            <remarks>
            The default value for this property is true and in this case all internal hyperlinks from the web page (links that point within the web page) will be rendered in the 
            generated pdf document as internal pdf links (clicking one of them will jump within the pdf document).
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.WebPageWidth">
            <summary>Gets or sets the width of the converted web page as it would appear in the internal browser used to render the html.</summary>
            <remarks>The web page width is specified in pixels and the default value is 1024px. 
            The page width is only an indication of the minimum page width recommended for conversion. If the content does not fit this width, 
            the converter will automatically resize the internal browser to fit the whole html content. To avoid this, the <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageFixedSize"/> property needs to be set to true.
            When <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageFixedSize"/> is true, the web page will be rendered with the specified <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageWidth"/> and <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/> even though the content might be truncated.
            <para></para>
            <para></para>
            If <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageWidth"/> is set to 0, the converter will automatically determine the page width, finding the width that will fit the html content.
            <para>This property can also be set directly in the constructor of <see cref="T:SelectPdf.HtmlToPdf"/> class.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.WebPageHeight">
            <summary>Gets or sets the height of the converted web page as it would appear in the internal browser used to render the html.</summary>
            <remarks>
            The web page height is specified in pixels and the default value is 0px. This means that the converter will automatically calculate the page height.
            <para>Generally this property does not need to be changed, but there are situations when the converter cannot calculate correctly the web page height (for example for web pages with frames) and
            in that case, <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/> needs to be set, otherwise no content might appear in the generated pdf.
            </para>
            <para>
            Note: If the <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/> is set, the content that exceeds this page height is truncated and will not appear in the generated pdf document.
            Only using the default 0 value will allow the whole page content to be rendered all the time in the generated pdf document.
            </para>
            <para>This property can also be set directly in the constructor of <see cref="T:SelectPdf.HtmlToPdf"/> class.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.WebPageFixedSize">
            <summary>Controls whether the web page is rendered with a fixed size internal browser or the size automatically extends to make the whole content visible.</summary>
            <remarks>
            The default value of this property is false.
            <para></para>
            <para></para>
            If the web page does not fit the dimensions specified by the <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageWidth"/> and <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/> properties and <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageFixedSize"/> 
            is set to false, the converter will try to automatically calculate a larger width and height to be able to display the whole html content.
            <para>
            If <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageFixedSize"/> is set to true, this will indicate the converter not to calculate another width and height, but use width and height specified by <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageWidth"/> and <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/>.
            These dimensions with be used even though the content will not fit the available space and it will be truncated.
            </para>
            <para>
            Note: If <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageFixedSize"/> is set to true, a page height needs to be set using <see cref="P:SelectPdf.HtmlToPdfOptions.WebPageHeight"/>, because the default value (0) will make the converter fail (cannot render a web page with no height).
            </para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.VisibleWebElementId">
            <summary>
            Use this property to convert only a certain section of the page, specified by the html element ID.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.StartupMode">
            <summary>
            Use this property to specify how the conversion starts.
            </summary>
            <remarks>
            By default this is set to <see cref="F:SelectPdf.HtmlToPdfStartupMode.Automatic"/> and the conversion is started as soon as the page loads (and <see cref="P:SelectPdf.HtmlToPdfOptions.MinPageLoadTime"/> elapses). 
            If set to <see cref="F:SelectPdf.HtmlToPdfStartupMode.Manual"/>, the conversion is started only by a javascript call to <c>SelectPdf.startConversion()</c> from within the web page.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.StartupScript">
            <summary>
            Use this property to specify some JavaScript code that will be injected into the page that is converted. 
            </summary>
            <remarks>The JavaScript code specified here will run before any other script on the page.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.KeepTextsTogether">
            <summary>
            This property instructs the converter whether to try to avoid cutting off the text lines between pdf pages or not.
            </summary>
            <remarks>The default value is true and the converter tries to avoid text cutting between pdf pages.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.KeepImagesTogether">
            <summary>
            This property instructs the converter whether to try to avoid cutting off the images between pdf pages or not.
            </summary>
            <remarks>The default value is false and the converter does not try to avoid images cutting between pdf pages.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.JavaScriptEnabled">
            <summary>
            Enable scripts when rendering the url or html string. 
            </summary>
            <remarks>The default value of this property is true and the converter will run any javascript available when the page loads.
            <para></para>
            <para>Note: If the javascript requires some time to load, <see cref="P:SelectPdf.HtmlToPdfOptions.MinPageLoadTime"/> property should be set to delay the conversion 
            with the specified number of seconds and allow the javascript to run.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.PluginsEnabled">
            <summary>
            A flag indicating if plugins (like Flash players) are enabled in the converter. 
            </summary>
            <remarks>
            The default value for this property is true.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.CssMediaType">
            <summary>
            Indicates what css styles are used when the web page is rendered.
            </summary>
            <remarks>The default value is <see cref="F:SelectPdf.HtmlToPdfCssMediaType.Screen"/> and the page is rendered like in a regular web browser. 
            If the page contains printer specific styles, the property should be set to <see cref="F:SelectPdf.HtmlToPdfCssMediaType.Print"/> to have the page rendered using those css styles.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.RenderPageOnTimeout">
            <summary>
            A flag indicating if the page is rendered even if a navigation timeout occurs. The default value is False and a navigation timeout exception is raised.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.ScaleImages">
            <summary>
            A flag indicating if the images from the page are scaled during the conversion process. The default value is False and images are not scaled.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.DenyLocalFileAccess">
            <summary>
            A flag indicating if local files can be loaded during the conversion. The default value is False and local files can be loaded.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.DrawBackground">
            <summary>
            Gets or sets a flag indicating if the web page background is rendered in pdf.
            </summary>
            <remarks>
            The default value for this property is true and the page background is rendered into the generated pdf. 
            Even though it might be white, it will still make invisible any pdf element that was rendered behind it.
            Setting this property to false can make visible the existing pdf elements behind this html element.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.HttpCookies">
            <summary>
            Gets the collection of custom HTTP cookies used for the conversion. 
            </summary>
            <remarks>
            This property returns a reference to the collection of custom HTTP cookies to be sent by the converter to the web server when the web page is rendered. 
            A cookie is defined by a name/value pair that can be added to the collection using the <see cref="M:System.Collections.Specialized.NameValueCollection.Add(System.String,System.String)"/> method of the <see cref="P:SelectPdf.HtmlToPdfOptions.HttpCookies"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.HttpHeaders">
            <summary>
            Get the collection of custom HTTP headers used for the conversion.
            </summary>
            <remarks>
            This property returns a reference to the collection of custom HTTP headers to be sent by the converter to the web server when the web page is rendered.
            A custom HTTP header is defined by a name/value pair
            that can be added to the collection using the <see cref="M:System.Collections.Specialized.NameValueCollection.Add(System.String,System.String)"/> method of the <see cref="P:SelectPdf.HtmlToPdfOptions.HttpHeaders"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.HttpPostParameters">
            <summary>
            Gets the collection of custom HTTP POST parameters used for the conversion. 
            </summary>
            <remarks>
            This property returns a reference to the collection of custom HTTP POST parameters that will be sent by the converter to the web server when the web page is rendered. 
            A POST parameter is defined by a name/value pair that can be added to the collection using the <see cref="M:System.Collections.Specialized.NameValueCollection.Add(System.String,System.String)"/> method of the <see cref="P:SelectPdf.HtmlToPdfOptions.HttpPostParameters"/> property.
            When this collection has at least one parameter, the rendered page is a accessed via POST method. Otherwise the method is GET.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.MinPageLoadTime">
            <summary>
            An additional time in seconds to wait for asynchronous items to be loaded before the web page is rendered.
            </summary>
            <remarks>
            A delay of this type could be necessary if javascripts are loaded when the page is rendered or if Flash movies are played.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.MaxPageLoadTime">
            <summary>
            The web page navigation timeout in seconds. 
            </summary>
            <remarks>Default value is 60 seconds. This value should be increased if large web pages are loaded.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.SecureProtocol">
            <summary>
            Protocol used for secure (HTTPS) connections. 
            </summary>
            <remarks>Default and recommended value is Tls11OrNewer. It means that TLS 1.1 or newer is used for secure connections.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.Authentication">
            <summary>
            Handles authentication options if the web page being converted requires authentication.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.PdfDocumentInformation">
            <summary>
            Gets a reference to the object that specifies the generated pdf document properties.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.SecurityOptions">
            <summary>
            Gets a reference to the object that specifies the generated pdf document security settings.
            </summary>
            <remarks>
            This property returns a reference to a <see cref="T:SelectPdf.PdfSecurityOptions"/> object that can be used to control the generated pdf 
            document security settings (like password, printing or editing restrictions).
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.PdfBookmarkOptions">
            <summary>
            Gets a reference to the object that controls the bookmarks creation for the generated pdf document.
            </summary>
            <remarks>
            This property needs to be set to instruct the html to pdf converter to automatically generate some bookmarks in the pdf document. The converter will create bookmarks for the web elements
            specified by the <see cref="P:SelectPdf.PdfBookmarkOptions.CssSelectors"/> of the <see cref="P:SelectPdf.HtmlToPdfOptions.PdfBookmarkOptions"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.HiddenWebElements">
            <summary>
            Gets a reference to the object that controls the visibility of some web elements in the generated pdf document.
            </summary>
            <remarks>
            This property needs to be set to instruct the html to pdf converter to hide some elements from the web page and not render them in the pdf document. 
            The converter will not render the web elements specified by the <see cref="P:SelectPdf.HiddenWebElements.CssSelectors"/> of the <see cref="P:SelectPdf.HtmlToPdfOptions.HiddenWebElements"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.ViewerPreferences">
            <summary>
            Controls how the pdf document will appear in a pdf viewer (like Adobe Reader).
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.ProxyOptions">
            <summary>
            Gets a reference to an object containing the proxy settings used to access the web page that is being converted.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.PdfStandard">
            <summary>
            Defines the pdf standard used by the generated pdf document. 
            </summary>
            <remarks>
            The default value is <see cref="F:SelectPdf.PdfStandard.Full"/> and in this case the full set of pdf features can be used in the generated document.
            This property can be used to instruct the converter to generate PDF/A, PDF/X or PDF/SiqQ compliant documents.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.ColorSpace">
            <summary>
            Gets or sets the color space of the generated pdf document. 
            </summary>
            <remarks>
            The possible values are <see cref="F:SelectPdf.PdfColorSpace.RGB"/>, <see cref="F:SelectPdf.PdfColorSpace.CMYK"/> or <see cref="F:SelectPdf.PdfColorSpace.GrayScale"/>. The default
            color space is <see cref="F:SelectPdf.PdfColorSpace.RGB"/>.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.MaximumConcurrentConversions">
            <summary>
            Maximum number of html to pdf conversions that can be performed simultaneously.
            </summary>
            <remarks>
            This parameter must be set before the first conversion performed by the application.
            When this property is set with a negative value or zero, the concurrency level is maximum. The default value is 4.
            <para>If the maximum number is reached and more conversion requests arrive, they will wait in a queue for an available spot.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.GenerateSinglePagePdf">
            <summary>
            Generate a single page PDF. The converter will automatically resize the PDF page to fit all the content in a single page.
            </summary>
            <remarks>The default value of this property is False.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.WebElementsMappingOptions">
            <summary>
            Gets a reference to the object controlling the retrieval of HTML elements position in the generated PDF.
            In order to retrieve the position in PDF for a list of HTML elements, the <see cref="P:SelectPdf.WebElementsMappingOptions.CssSelectors"/> property of 
            <see cref="T:SelectPdf.WebElementsMappingOptions"/> must be set with an array of strings containing the selectors of the HTML elements.
            The positions of the HTML elements in the generated PDF document will be returned in the 
            <see cref="P:SelectPdf.WebElementsMappingOptions.Result"/> object.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.PageBreaksEnhancedAlgorithm">
            <summary>
            Gets or sets a flag indicating if an enhanced custom page breaks algorithm is used. 
            The enhanced algorithm is a little bit slower but it will prevent the appearance of hidden text in the PDF when custom page breaks are used.
            </summary>
            <remarks>The default value for this property is False.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.DisplayCutText">
            <summary>
            Gets or sets a flag indicating if the text that is out of the calculated rendering rectangle is displayed or not.
            </summary>
            <remarks>The default value for this property is False.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.RenderingEngine">
            <summary>
            Gets or sets the rendering engine used by the converter to load and render the HTML. The possible values are WebKit, WebKitRestricted and Blink. 
            The Webkit rendering engine is internal and renders similar to Apple's Safari. For Blink, Chromium binaries must be also installed.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.LFN">
            <summary>
            Internal use only.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.LFNB">
            <summary>
            Internal use only.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfOptions.AllowContentHeightResize">
            <summary>
            A flag used only by WebKitRestricted or Blink engine indicating if the content height can be recalculated to better fit the page. This can introduce certain errors in some cases.
            The default is True. Set it to False if content is split to more pages than expected.
            </summary>
            <remarks>Note: This is used only by the WebKitRestricted rendering engine.</remarks>
        </member>
        <member name="T:SelectPdf.HtmlToPdfPageFitMode">
            <summary>
            Specifies how the html content is rendered in a pdf page.
            </summary>
        </member>
        <member name="F:SelectPdf.HtmlToPdfPageFitMode.NoAdjustment">
            <summary>
            The html content is rendered as it is. No size adjustments are performed, even though the destination space might be smaller than needed.
            </summary>
        </member>
        <member name="F:SelectPdf.HtmlToPdfPageFitMode.ShrinkOnly">
            <summary>
            The html content is shrunk if necessary to fit the available destination space. This option does not expand the content if it's smaller than the available space.
            </summary>
        </member>
        <member name="F:SelectPdf.HtmlToPdfPageFitMode.AutoFit">
            <summary>
            The html content is automatically adjusted to fit the entire available space. Unlike <see cref="F:SelectPdf.HtmlToPdfPageFitMode.ShrinkOnly"/>, the content can be either shrunk or stretched.
            </summary>
        </member>
        <member name="T:SelectPdf.HtmlToPdfProxyOptions">
            <summary>
            The proxy settings used to resolve the HTTP requests.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfProxyOptions.HostName">
            <summary>
            Gets or sets the host name of the proxy host. 
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfProxyOptions.Password">
            <summary>
            Gets or sets the password used for authentication. 
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfProxyOptions.PortNumber">
            <summary>
            Gets or sets the port number of the proxy host. 
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfProxyOptions.Type">
            <summary>
            Gets or sets the proxy type.  
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfProxyOptions.Username">
            <summary>
            Gets or sets the user name used for authentication. 
            </summary>
        </member>
        <member name="T:SelectPdf.NetworkProxyType">
            <summary>
            The list of available proxy types used by the converter to resolve HTTP requests. 
            </summary>
        </member>
        <member name="F:SelectPdf.NetworkProxyType.Socks5">
            <summary>
            Socks5
            </summary>
        </member>
        <member name="F:SelectPdf.NetworkProxyType.None">
            <summary>
            None
            </summary>
        </member>
        <member name="F:SelectPdf.NetworkProxyType.Http">
            <summary>
            Http
            </summary>
        </member>
        <member name="F:SelectPdf.NetworkProxyType.HttpCaching">
            <summary>
            HttpCaching
            </summary>
        </member>
        <member name="T:SelectPdf.HtmlToPdfResult">
            <summary>
            Offers information about the html to pdf conversion process. 
            </summary>
            <remarks>
            After a successful conversion the <see cref="P:SelectPdf.HtmlToPdf.ConversionResult"/> property is initialized with an instance of this class.
            </remarks>
        </member>
        <member name="M:SelectPdf.HtmlToPdfResult.#ctor(System.Int32,System.Drawing.SizeF,System.Drawing.SizeF,System.Drawing.SizeF,System.Single,System.Int32,System.Drawing.RectangleF,System.Drawing.RectangleF[],SelectPdf.WebPageInformation,System.String)">
            <summary>
            Creates a HtmlToPdfResult object.
            </summary>
            <param name="pdfPageCount"></param>
            <param name="htmlContentSizePixels"></param>
            <param name="htmlContentSizePoints"></param>
            <param name="pdfPageSizePoints"></param>
            <param name="htmlContentWidthResizeFactor"></param>
            <param name="lastPageIndex"></param>
            <param name="lastPageRectangle"></param>
            <param name="renderedPagesRectangles"></param>
        </member>
        <member name="P:SelectPdf.HtmlToPdfResult.PdfPageCount">
            <summary>
            The number of pages in the generated pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfResult.WebPageSize">
            <summary>
            The size of the web page in pixels.
            </summary>
            <remarks>
            1px is 1/96 inch at 96 DPI.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfResult.PdfPageSize">
            <summary>
            The size of the page in the generated pdf document.
            </summary>
            <remarks>This size is in points. 1 point is 1/72 inch.</remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfResult.PdfPageScaleFactor">
            <summary>
            The factor used to scale the html content to fit the pdf page.
            </summary>
            <remarks>
            This property offers the factor used to scale the html content when rendered into the pdf document. 
            When <see cref="P:SelectPdf.HtmlToPdfOptions.AutoFitWidth"/> is <see cref="F:SelectPdf.HtmlToPdfPageFitMode.ShrinkOnly"/> or <see cref="F:SelectPdf.HtmlToPdfPageFitMode.AutoFit"/>,
            the html content might be resized to fit the pdf page width if necessary.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfResult.PdfPageLastIndex">
            <summary>
            The index of the pdf page where the rendering of the html content ended in the generated pdf document.
            </summary>
            <remarks>
            This information is useful when other pdf elements are added after conversion using the <see cref="T:SelectPdf.PdfDocument"/> object.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfResult.PdfPageLastRectangle">
            <summary>
            The size and position of the html content rendered on the last page in the generated pdf document.
            </summary>
            <remarks>
            This information is useful when other pdf elements are added after conversion using the <see cref="T:SelectPdf.PdfDocument"/> object.
            <para>
            Note: The last page is specified by the <see cref="P:SelectPdf.HtmlToPdfResult.PdfPageLastIndex"/> property.
            </para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfResult.PdfPagesRectangles">
            <summary>
            The size and position of the html content rendered in all pages of the pdf document.
            </summary>
            <remarks>
            This information is useful when other pdf elements are added after conversion using the <see cref="T:SelectPdf.PdfDocument"/> object.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfResult.WebPageInformation">
            <summary>
            Gets an object populated after conversion with the title, keywords and description of the converted web page.
            </summary>
            <remarks>
            This information can be used to set the properties of the pdf document after the conversion.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfResult.ConsoleLog">
            <summary>
            Returns the console log of the browser used to render the web page.
            </summary>
            <remarks>
            This information can be used to debug javascript errors.
            </remarks>
        </member>
        <member name="T:SelectPdf.HtmlToPdfStartupMode">
            <summary>
            Specifies the converter startup mode. 
            </summary>
        </member>
        <member name="F:SelectPdf.HtmlToPdfStartupMode.Automatic">
            <summary>
            The conversion starts right after the page loads.
            </summary>
        </member>
        <member name="F:SelectPdf.HtmlToPdfStartupMode.Manual">
            <summary>
            The conversion starts only when called from JavaScript.
            </summary>
        </member>
        <member name="T:SelectPdf.HtmlToPdfViewerPreferences">
            <summary>
            The pdf viewer preferences. Controls how the pdf document appears in a pdf viewer (like Adobe Reader).
            </summary>
        </member>
        <member name="P:SelectPdf.HtmlToPdfViewerPreferences.HideToolbar">
            <summary>
            Gets or sets a flag specifying whether to hide the pdf viewer application's tool bars when the document is active. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfViewerPreferences.HideMenuBar">
            <summary>
            Gets or sets a flag specifying whether to hide the pdf viewer application's menu bar when the document is active. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfViewerPreferences.HideWindowUI">
            <summary>
            Gets or sets a flag specifying whether to hide user interface elements in the document's window (such as scroll bars and navigation controls), leaving only the document's contents displayed. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfViewerPreferences.FitWindow">
            <summary>
            Gets or sets a flag specifying whether to resize the document's window to fit the size of the first displayed page. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfViewerPreferences.CenterWindow">
            <summary>
            Gets or sets a flag specifying whether to position the document's window in the center of the screen. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfViewerPreferences.DisplayDocTitle">
            <summary>
            Gets or sets a flag specifying whether the window's title bar should display the document title taken from <see cref="P:SelectPdf.HtmlToPdfOptions.PdfDocumentInformation"/> property. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfViewerPreferences.NonFullScreenPageMode">
            <summary>
            Gets or sets the document page mode when the pdf viewer application exits the full screen mode. 
            </summary>
            <remarks>
            This property is taken into account only if <see cref="P:SelectPdf.HtmlToPdfViewerPreferences.PageMode"/> is set to <see cref="F:SelectPdf.PdfViewerPageMode.FullScreen"/>. Otherwise it is ignored.
            <para>
            The default value for this property is <see cref="F:SelectPdf.PdfViewerFullScreenExitMode.UseNone"/>.
            </para>
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfViewerPreferences.PageMode">
            <summary>
            Gets or sets the document page mode when the pdf document is opened in a pdf viewer. 
            </summary>
            <remarks>
            The default value for this property is <see cref="F:SelectPdf.PdfViewerPageMode.UseNone"/>.
            </remarks>
        </member>
        <member name="P:SelectPdf.HtmlToPdfViewerPreferences.PageLayout">
            <summary>
            Gets or sets the page layout to be used when the document is opened. 
            </summary>
            <remarks>
            The default value for this property is <see cref="F:SelectPdf.PdfViewerPageLayout.OneColumn"/>.
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfAction">
            <summary>
            Base class for the pdf actions.
            </summary>
            <remarks>
            The pdf document can perform 2 types of actions when it is opened:
            <list type="bullet">
            <item>
            <description><see cref="T:SelectPdf.PdfActionGoTo"/> - The pdf document opens at a specific <see cref="T:SelectPdf.PdfDestination"/>.</description>
            </item>
            <item>
            <description><see cref="T:SelectPdf.PdfActionJavaScript"/> - The pdf document will run a specific javascript.</description>
            </item>
            </list>
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfActionGoTo">
            <summary>
            Represents a Go To action in a pdf document.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfActionGoTo.#ctor(SelectPdf.PdfDestination)">
            <summary>
            Creates a Go To action based on the specified pdf destination.
            </summary>
            <param name="destination">The destination of the Go To action.</param>
        </member>
        <member name="P:SelectPdf.PdfActionGoTo.Destination">
            <summary>
            Gets or sets the pdf destination of the Go To action.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfActionJavaScript">
            <summary>
            Represents a JavaScript action in a pdf document.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfActionJavaScript.#ctor(System.String)">
            <summary>
            Creates a JavaScript action based on a JavaScript code.
            </summary>
            <param name="javaScript">The JavaScript code.</param>
        </member>
        <member name="P:SelectPdf.PdfActionJavaScript.JavaScript">
            <summary>
            Gets or sets the JavaScript code executed by this action.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfBookmark">
            <summary>
            Represents a bookmark in a pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfBookmark.Text">
            <summary>
            Gets or sets the bookmark text.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfBookmark.Color">
            <summary>
            Gets or sets the bookmark text color.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfBookmark.Style">
            <summary>
            Gets or sets the bookmark text style.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfBookmark.Destination">
            <summary>
            Gets or sets the bookmark destination.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfBookmark.Parent">
            <summary>
            Gets the parent of the current bookmark or null if the bookmark has no parent.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfBookmark.ChildNodes">
            <summary>
            Gets the child bookmark collection. 
            </summary>
        </member>
        <member name="T:SelectPdf.PdfBookmarkOptions">
            <summary>
            Helps defining a set of html elements that will be associated with bookmarks in the generated pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfBookmarkOptions.CssSelectors">
            <summary>
            Gets or sets the selectors of the html elements to be bookmarked.
            For example, the selector for all the H1 elements is "H1", the selector for all the elements with the CSS class name 'myclass'
            is "*.myclass" and the selector for the elements with the id 'myid' is "*#myid".
            </summary>
        </member>
        <member name="T:SelectPdf.PdfBookmarksCollection">
            <summary>
            Represents the collection of bookmarks in a pdf document.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfBookmarksCollection.document">
            <summary>
            The parent PDF Document.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfBookmarksCollection.#ctor(SelectPdf.PdfDocument)">
            <summary>
            Creates a collection of bookmarks for this document.
            </summary>
            <param name="document">The PDF document for which this collection is created.</param>
        </member>
        <member name="M:SelectPdf.PdfBookmarksCollection.GetEnumerator">
            <summary>
            Gets the collection enumerator.
            </summary>
            <returns>The collection enumerator.</returns>
        </member>
        <member name="P:SelectPdf.PdfBookmarksCollection.Document">
            <summary>
            The parent pdf document of the bookmarks collection.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfBookmarksCollection.Item(System.Int32)">
            <summary>
            Gets or sets the bookmark at the specified zero based index in the bookmarks collection.
            </summary>
            <param name="index">The zero based bookmark index in the pdf bookmarks collection.</param>
            <returns>The pdf bookmark at the specified index.</returns>
        </member>
        <member name="M:SelectPdf.PdfBookmarksCollection.Add(System.String,SelectPdf.PdfDestination)">
            <summary>
            Adds a new bookmark to the bookmarks collection.
            </summary>
            <param name="text">The bookmark text.</param>
            <param name="destination">The bookmark destination.</param>
            <returns>The newly created pdf bookmark.</returns>
        </member>
        <member name="M:SelectPdf.PdfBookmarksCollection.Insert(System.Int32,System.String,SelectPdf.PdfDestination)">
            <summary>
            Creates a new bookmark and inserts it at the specified zero based index in the bookmarks collection.
            </summary>
            <param name="index">The zero based index where to insert the bookmark.</param>
            <param name="text">The bookmark text.</param>
            <param name="destination">The bookmark destination.</param>
            <returns>The newly created pdf bookmark.</returns>
        </member>
        <member name="M:SelectPdf.PdfBookmarksCollection.Remove(SelectPdf.PdfBookmark)">
            <summary>
            Removes the specified pdf bookmark.
            </summary>
            <param name="bookmark">The pdf bookmark to be removed.</param>
        </member>
        <member name="M:SelectPdf.PdfBookmarksCollection.RemoveAt(System.Int32)">
            <summary>
            Removes the pdf bookmark at the specified index in the bookmarks collection.
            </summary>
            <param name="index">The zero based index of the pdf bookmark to be removed.</param>
        </member>
        <member name="P:SelectPdf.PdfBookmarksCollection.Count">
            <summary>
            The number of bookmarks in the pdf bookmarks collection.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfBookmarkStyle">
            <summary>
            The pdf bookmark text style.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfBookmarkStyle.Normal">
            <summary>
            Normal text style.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfBookmarkStyle.Italic">
            <summary>
            The text is italic.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfBookmarkStyle.Bold">
            <summary>
            The text is bold.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfCanvas">
            <summary>
            Base class for classes that can render pdf page elements.
            </summary>
            <remarks>
            <see cref="T:SelectPdf.PdfCanvas"/> is the base class for <see cref="T:SelectPdf.PdfPage"/> and <see cref="T:SelectPdf.PdfTemplate"/>. 
            <see cref="T:SelectPdf.PdfPage"/> should be used to add elements to the pdf document only in a certain page at a certain position.
            <see cref="T:SelectPdf.PdfTemplate"/> should be used to add elements to the pdf document that will appear in more than one page.
            </remarks>
        </member>
        <member name="M:SelectPdf.PdfCanvas.Add(SelectPdf.PdfPageElement)">
            <summary>
            Adds a pdf page element to the canvas.
            </summary>
            <param name="element">The page element to be rendered</param>
            <returns>The result of adding the element</returns>
        </member>
        <member name="P:SelectPdf.PdfCanvas.ClientRectangle">
            <summary>
            Gets the drawable area of the canvas. The margins, pdf document header and footer are excluded.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfCanvas.MeasureString(System.String,SelectPdf.PdfFont)">
            <summary>
            Measures the size of the specified string when rendered with the specified font.
            </summary>
            <param name="text">String to measure.</param>
            <param name="font">The font used to render the string.</param>
            <returns>The size of the string.</returns>
        </member>
        <member name="T:SelectPdf.PdfColor">
            <summary>
            Represents a pdf color.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfColor.#ctor(System.Drawing.Color)">
            <summary>
            Creates a pdf color based on a .NET system color.
            </summary>
            <param name="color">The .NET system color.</param>
        </member>
        <member name="M:SelectPdf.PdfColor.#ctor(System.Single)">
            <summary>
            Creates a Gray-scale color based on a gray level.
            </summary>
            <param name="gray">A value between 0 and 1 to specify the gray level.</param>
        </member>
        <member name="M:SelectPdf.PdfColor.#ctor(System.Byte,System.Byte,System.Byte)">
            <summary>
            Creates a pdf color based on RGB values.
            </summary>
            <param name="red">Red value.</param>
            <param name="green">Green value.</param>
            <param name="blue">Blue value.</param>
        </member>
        <member name="M:SelectPdf.PdfColor.#ctor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a pdf color based on GMYK values.
            </summary>
            <param name="cyan">Cyan value.</param>
            <param name="magenta">Magenta value.</param>
            <param name="yellow">Yellow value.</param>
            <param name="key">Key value.</param>
        </member>
        <member name="P:SelectPdf.PdfColor.Empty">
            <summary>
            An empty color.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfColor.Red">
            <summary>
            Gets or sets the Red value of this pdf color.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfColor.Blue">
            <summary>
            Gets or sets the Blue value of this pdf color.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfColor.Green">
            <summary>
            Gets or sets the Green value of this pdf color.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfColor.Gray">
            <summary>
            Gets or sets the Gray level of this pdf color.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfColor.Cyan">
            <summary>
            Gets or sets the Cyan value of this pdf color.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfColor.Magenta">
            <summary>
            Gets or sets the Magenta value of this pdf color.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfColor.Yellow">
            <summary>
            Gets or sets the Yellow value of this pdf color.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfColor.Key">
            <summary>
            Gets or sets the Key value of this pdf color.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfColor.op_Implicit(System.Drawing.Color)~SelectPdf.PdfColor">
            <summary>
            Implicit operator to convert a .NET system color to a pdf color.
            </summary>
            <param name="color">The .NET system color.</param>
            <returns>The pdf color.</returns>
        </member>
        <member name="M:SelectPdf.PdfColor.op_Implicit(SelectPdf.PdfColor)~System.Drawing.Color">
            <summary>
            Implicit operator to convert a pdf color to a .NET system color.
            </summary>
            <param name="color">The pdf color.</param>
            <returns>The .NET system color.</returns>
        </member>
        <member name="T:SelectPdf.PdfColorSpace">
            <summary>
            The list of possible color spaces for the pdf document.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfColorSpace.RGB">
            <summary>
            RGB color space.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfColorSpace.CMYK">
            <summary>
            CMYK color space.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfColorSpace.GrayScale">
            <summary>
            Gray scale color space.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfCompressionLevel">
            <summary>
            The pdf document compression level.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCompressionLevel.NoCompression">
            <summary>
            No compression.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCompressionLevel.Normal">
            <summary>
            Normal compression level. 
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCompressionLevel.Best">
            <summary>
            Best compression. Produces the smallest pdf document but it takes longer to compress it.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfCustomPageSize">
            <summary>
            The pdf page size expressed in points.
            </summary>
            <remarks>
            1 point is 1/72 inch.
            </remarks>
        </member>
        <member name="M:SelectPdf.PdfCustomPageSize.#ctor">
            <summary>
            Default constructor constructs a A4 page size
            </summary>
        </member>
        <member name="M:SelectPdf.PdfCustomPageSize.#ctor(System.Single,System.Single)">
            <summary>
            Constructs a custom page size
            </summary>
            <param name="width">The page width in points</param>
            <param name="height">The page height in points</param>
        </member>
        <member name="P:SelectPdf.PdfCustomPageSize.Width">
            <summary>
            Gets or sets the page width
            </summary>
        </member>
        <member name="P:SelectPdf.PdfCustomPageSize.Height">
            <summary>
            Gets or sets the page height
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.Letter">
            <summary>
            Letter
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.Note">
            <summary>
            Note
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.Legal">
            <summary>
            Legal
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.A0">
            <summary>
            A0
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.A1">
            <summary>
            A1
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.A2">
            <summary>
            A2
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.A3">
            <summary>
            A3
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.A4">
            <summary>
            A4
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.A5">
            <summary>
            A5
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.A6">
            <summary>
            A6
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.A7">
            <summary>
            A7
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.A8">
            <summary>
            A8
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.A9">
            <summary>
            A9
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.A10">
            <summary>
            A10
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.B0">
            <summary>
            B0
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.B1">
            <summary>
            B1
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.B2">
            <summary>
            B2
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.B3">
            <summary>
            B3
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.B4">
            <summary>
            B4
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.B5">
            <summary>
            B5
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.ArchE">
            <summary>
            ArchE
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.ArchD">
            <summary>
            ArchD
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.ArchC">
            <summary>
            ArchC
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.ArchB">
            <summary>
            ArchB
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.ArchA">
            <summary>
            ArchA
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.Flsa">
            <summary>
            Flsa
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.HalfLetter">
            <summary>
            HalfLetter
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.Letter11x17">
            <summary>
            11x17
            </summary>
        </member>
        <member name="F:SelectPdf.PdfCustomPageSize.Ledger">
            <summary>
            Ledger
            </summary>
        </member>
        <member name="T:SelectPdf.PdfDestination">
            <summary>
            Represents a destination in a pdf document.
            </summary>
            <remarks>The pdf destination is used by links, bookmarks or open actions to jump at a specified position in the pdf document.</remarks>
        </member>
        <member name="M:SelectPdf.PdfDestination.#ctor(SelectPdf.PdfPage)">
            <summary>
            Constructs a pdf destination to the specified pdf page.
            </summary>
            <param name="page">The destination page.</param>
        </member>
        <member name="M:SelectPdf.PdfDestination.#ctor(SelectPdf.PdfPage,System.Drawing.PointF)">
            <summary>
            Constructs a pdf destination to the specified location in the specified page.
            </summary>
            <param name="page">The destination page.</param>
            <param name="location">The location in the destination pdf page.</param>
        </member>
        <member name="M:SelectPdf.PdfDestination.#ctor(SelectPdf.PdfPage,System.Drawing.PointF,SelectPdf.PdfDestinationFitMode)">
            <summary>
            Constructs a destination to the specified location in the specified page and with the specified fit mode
            in the destination page.
            </summary>
            <param name="page">The destination page.</param>
            <param name="location">The location in the destination page.</param>
            <param name="fitMode">The destination fit mode.</param>
        </member>
        <member name="P:SelectPdf.PdfDestination.Location">
            <summary>
            Gets or sets the location point in the destination page.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDestination.ZoomFactor">
            <summary>
            Gets or sets the zoom factor of the destination page when viewed in a pdf viewer.
            </summary>
            <remarks> This only works when <see cref="P:SelectPdf.PdfDestination.FitMode"/> is <see cref="F:SelectPdf.PdfDestinationFitMode.None"/>.
            <para></para>
            This zoom factor is a percentage. A value of 100 will not zoom the page.</remarks>
        </member>
        <member name="P:SelectPdf.PdfDestination.Page">
            <summary>
            Gets or sets the destination page.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDestination.FitMode">
            <summary>
            Gets or sets the auto-fit mode in the destination page.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfDestinationFitMode">
            <summary>
            Specifies the auto-fit mode for a <see cref="T:SelectPdf.PdfDestination"/> object.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfDestinationFitMode.None">
            <summary>
            Does not try to automatically fit the page. 
            </summary>
            <remarks>
            Displays the page specified, with the coordinates (left, top) positioned 
            at the upper-left corner of the window and the contents of the page magnified by the factor zoom. 
            A null value for any of the parameters left, top, or zoom specifies that the current value of that parameter is to be retained unchanged.
            A zoom value of 0 has the same meaning as a null value.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfDestinationFitMode.EntirePage">
            <summary>
            Automatically fits the entire page. 
            </summary>
            <remarks>
            Displays the page specified, with its contents magnified just enough to fit the entire page 
            within the window both horizontally and vertically. 
            If the required horizontal and vertical magnification factors are different, 
            use the smaller of the two, centering the page within the window in the other dimension.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfDestinationFitMode.EntirePageWidth">
            <summary>
            Automatically fits the entire width of the page. 
            </summary>
            <remarks>
            Displays the page specified, with the vertical coordinate top positioned
            at the top edge of the window and the contents of the page magnified
            just enough to fit the entire width of the page within the window.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfDestinationFitMode.EntirePageHeight">
            <summary>
            Automatically fits the entire height of the page. 
            </summary>
            <remarks>
            Displays the page specified, with the horizontal coordinate left positioned
            at the left edge of the window and the contents of the page magnified
            just enough to fit the entire height of the page within the window.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfDestinationFitMode.BoundingBox">
            <summary>
            Automatically fits the bounding box. 
            </summary>
            <remarks>
            Displays the page specified, with its contents magnified
            just enough to fit its bounding box entirely within the window both horizontally
            and vertically. If the required horizontal and vertical magnification
            factors are different, use the smaller of the two, centering the bounding box
            within the window in the other dimension.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfDestinationFitMode.BoundingBoxWidth">
            <summary>
            Automatically fits the entire bounding box width of the page. 
            </summary>
            <remarks>
            Displays the page specified, with the vertical coordinate top positioned at the top edge 
            of the window and the contents of the page magnified just enough to fit the entire width of its 
            bounding box within the window. A null value for top specifies that the current value of that 
            parameter is to be retained unchanged.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfDestinationFitMode.BoundingBoxHeight">
            <summary>
            Automatically fits the entire bounding box height of the page. 
            </summary>
            <remarks>
            Displays the page specified, with the horizontal coordinate left positioned at the left 
            edge of the window and the contents of the page magnified just enough to fit the entire height 
            of its bounding box within the window. A null value for left specifies that the current value of that 
            parameter is to be retained unchanged.
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfDocument">
            <summary>
            Represents a pdf document.
            </summary>
            <remarks>
            This class provides the API to create, load, modify and save a pdf document.
            </remarks>
        </member>
        <member name="M:SelectPdf.PdfDocument.#ctor">
            <summary>
            Creates an empty pdf document object. 
            </summary>
            <remarks>New pdf pages can be added to the document and new pdf elements can be added to these new document pages.</remarks>
        </member>
        <member name="M:SelectPdf.PdfDocument.#ctor(SelectPdf.PdfStandard)">
            <summary>
            Creates an empty pdf document object. 
            </summary>
            <param name="pdfStandard">Defines the pdf standard used by the generated pdf document. 
            The default value is <see cref="F:SelectPdf.PdfStandard.Full"/> and in this case the full set of pdf features can be used in the generated document.
            This parameter can be used to generate PDF/A, PDF/X or PDF/SiqQ compliant documents.
            </param>
            <remarks>New pdf pages can be added to the document and new pdf elements can be added to these new document pages.</remarks>
        </member>
        <member name="M:SelectPdf.PdfDocument.#ctor(System.IO.Stream)">
            <summary>
            Loads a pdf document from the specified stream.
            </summary>
            <param name="stream">The pdf document stream.</param>
        </member>
        <member name="M:SelectPdf.PdfDocument.#ctor(System.String)">
            <summary>
            Loads a pdf document from an existing pdf file.
            </summary>
            <param name="fileName">
            The full path to the existing pdf document file.
            </param>
        </member>
        <member name="M:SelectPdf.PdfDocument.#ctor(System.IO.Stream,System.String)">
            <summary>
            Loads a pdf document from a stream containing a password protected pdf document. 
            </summary>
            <remarks>
            The specified password can be a user or owner password.
            </remarks>
            <param name="stream">The stream containing the pdf document.</param>
            <param name="password">The password to open the specified pdf document.</param>
        </member>
        <member name="M:SelectPdf.PdfDocument.#ctor(System.String,System.String)">
            <summary>
            Loads a pdf document from an existing password protected pdf file.
            </summary>
            <param name="fileName">The full path to the existing pdf file.</param>
            <param name="password">The password to open the specified pdf document.</param>
        </member>
        <member name="M:SelectPdf.PdfDocument.Close">
            <summary>
            Closes the current pdf document.
            </summary>
            <remarks>
            Calling this method will release all the resources allocated by the pdf document. 
            <para></para>
            <note type="important">
            Always call this method after the document was saved.
            </note>
            <para>
            </para>
            <note type="note">
            If the document was created from a stream, the stream will be closed if it was not explicitly detached 
            by calling the <see cref="M:SelectPdf.PdfDocument.DetachStream"/> before the document is closed.
            </note>
            </remarks>
        </member>
        <member name="M:SelectPdf.PdfDocument.DetachStream">
            <summary>
            Detaches the stream that was used to load the pdf document, leaving it open in case the current pdf document is closed.
            </summary>
            <remarks>
            Call this method to detach the stream from which the pdf document was created. When this method is called before 
            calling <see cref="M:SelectPdf.PdfDocument.Close"/>, the <see cref="M:SelectPdf.PdfDocument.Close"/> method will not close the detached stream.
            </remarks>
        </member>
        <member name="M:SelectPdf.PdfDocument.Save(System.String)">
            <summary>
            Saves the pdf document to the specified file.
            </summary>
            <param name="fileName">The name of the file where to save the document. It can be a full path or a relative path.</param>
        </member>
        <member name="M:SelectPdf.PdfDocument.Save(System.IO.Stream)">
            <summary>
            Saves the pdf document to the specified stream.
            </summary>
            <param name="stream">The stream where to save the pdf document.</param>
        </member>
        <member name="M:SelectPdf.PdfDocument.Save">
            <summary>
            Saves the pdf document as byte array.
            </summary>
            <remarks>
            The byte array can be further saved to a file or sent over HTTP as a response to the browser.
            </remarks>
            <returns>The byte array representation of the document.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.Save(System.Web.HttpResponse,System.Boolean,System.String)">
            <summary>
            Saves the pdf document to a HTTP stream.
            </summary>
            <param name="httpResponse">The HTTP response object.</param>
            <param name="displayInline">If this is true, the pdf document will be automatically opened in browser, otherwise the 
            user will be asked to save or open the downloaded pdf document.</param>
            <param name="fileName">The name of the pdf document. This name is used by the browser when it opens the pdf document inline 
            or the name suggested for saving the document when the document is sent as an attachment.</param>
        </member>
        <member name="P:SelectPdf.PdfDocument.Pages">
            <summary>
            Gets the pdf document pages collection.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocument.Fonts">
            <summary>
            Gets the pdf document fonts collection.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocument.Templates">
            <summary>
            Gets the pdf document templates collection.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocument.Bookmarks">
            <summary>
            Gets the bookmarks collection of the pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocument.Margins">
            <summary>
            The default pdf document margins. 
            </summary>
            <remarks>
            A new pages will use the default margins if no margins are specified when the page is added.</remarks>
        </member>
        <member name="P:SelectPdf.PdfDocument.LFN">
            <summary>
            Internal use only.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocument.Header">
            <summary>
            Represents the default pdf document header template. 
            </summary>
            <remarks>
            The header space is automatically taken into account when calculating the available space for rendering in a pdf page. 
            <para></para>
            This template is automatically docked to the top of the pdf page.</remarks>
        </member>
        <member name="P:SelectPdf.PdfDocument.Footer">
            <summary>
            Represents the default pdf document footer template.
            </summary>
            <remarks>
            The footer space is automatically taken into account when calculating the available space for rendering in a pdf page. 
            <para></para>
            This template is automatically docked to the bottom of the pdf page.</remarks>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddTemplate(System.Single,System.Single)">
            <summary>
            Adds a new template with the specified width and height to the pdf document templates collection.
            </summary>
            <remarks>
            The template location is top left corner of the pdf page. Use <see cref="M:SelectPdf.PdfDocument.AddTemplate(System.Drawing.RectangleF)"/> to specify another location for the template.
            </remarks>
            <param name="width">The pdf template width.</param>
            <param name="height">The pdf template height.</param>
            <returns>The newly added pdf template.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddTemplate(System.Drawing.RectangleF)">
            <summary>
            Adds a new template with the specified bounds to the pdf document templates collection.
            </summary>
            <param name="bounds">The template element bounds.</param>
            <returns>The newly added pdf template.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddPage">
            <summary>
            Creates a new pdf page and adds it to the pdf document pages collection. 
            </summary>
            <remarks>
            If there is a previous page in the pages collection, the orientation and size are inherited from that page, otherwise a first page with
            the default <see cref="F:SelectPdf.PdfPageSize.A4"/> size and <see cref="F:SelectPdf.PdfPageOrientation.Portrait"/> orientation is created. 
            <para></para>
            The page margins are inherited from the default pdf document margins specified in <see cref="P:SelectPdf.PdfDocument.Margins"/> property.</remarks>
            <returns>The newly created pdf page.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddPage(SelectPdf.PdfMargins)">
            <summary>
            Creates a new pdf page with the specified margins and adds it to the pdf document pages collection. 
            </summary>
            <remarks>
            If there is a previous page in the pages collection, the orientation and size are inherited from that page, otherwise a first page with
            the default <see cref="F:SelectPdf.PdfPageSize.A4"/> size and <see cref="F:SelectPdf.PdfPageOrientation.Portrait"/> orientation is created.</remarks>
            <param name="pageMargins">The pdf page margins.</param>
            <returns>The newly created pdf page.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddPage(SelectPdf.PdfCustomPageSize,SelectPdf.PdfMargins)">
            <summary>
            Creates a new pdf page with the specified size and margins and adds it to the pdf document pages collection. 
            </summary>
            <remarks>
            If there is a previous page in the pages collection, the orientation is inherited from that page, otherwise a first page with
            the default <see cref="F:SelectPdf.PdfPageOrientation.Portrait"/> orientation is created.</remarks>
            <param name="pageSize">The pdf page size.</param>
            <param name="pageMargins">The pdf page margins.</param>
            <returns>The newly created pdf page.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddPage(SelectPdf.PdfCustomPageSize,SelectPdf.PdfMargins,SelectPdf.PdfPageOrientation)">
            <summary>
            Creates a new pdf page with the specified size, margins and orientation and adds it to the pdf document pages collection. 
            </summary>
            <param name="pageSize">The pdf page size.</param>
            <param name="pageMargins">The pdf page margins.</param>
            <param name="pageOrientation">The pdf page orientation.</param>
            <returns>The newly created pdf page.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddPage(SelectPdf.PdfPage)">
            <summary>
            Adds the specified page to the pdf document pages collection. 
            </summary>
            <remarks>
            The specified page can belong to another pdf document. 
            <para></para>
            <note type="important">The source pdf document must remain opened until the current document is saved.</note>
            </remarks>
            <param name="pdfPage">The page to be added to the collection.</param>
        </member>
        <member name="M:SelectPdf.PdfDocument.InsertPage(System.Int32,SelectPdf.PdfCustomPageSize,SelectPdf.PdfMargins,SelectPdf.PdfPageOrientation)">
            <summary>
            Creates a new pdf page with the specified size, margins and orientation and inserts it into the pdf document pages collection
            at the specified index.
            </summary>
            <param name="index">The index where the new pdf page will be inserted.</param>
            <param name="pageSize">The pdf page size.</param>
            <param name="pageMargins">The pdf page margins.</param>
            <param name="pageOrientation">The pdf page orientation.</param>
            <returns>The newly created pdf page.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.InsertPage(System.Int32,SelectPdf.PdfPage)">
            <summary>
            Inserts the specified page into the pdf document pages collection at the specified index. 
            </summary>
            <remarks>
            The specified page can belong to another pdf document. 
            <para></para>
            <note>The source pdf document must remain opened until the current document is saved.</note>
            </remarks>
            <param name="pageIndex">The index where the pdf page will be inserted.</param>
            <param name="pdfPage">The pdf page to be inserted into the collection.</param>
        </member>
        <member name="M:SelectPdf.PdfDocument.Append(SelectPdf.PdfDocument)">
            <summary>
            Appends all pages from another pdf document to the current pdf document. 
            </summary>
            <remarks>
            A custom range of pages from another document can be appended to the current document calling the
            <see cref="M:SelectPdf.PdfPageCollection.Add(SelectPdf.PdfPage)"/> method on the <see cref="P:SelectPdf.PdfDocument.Pages"/> collection for each page to be appended.
            <para></para>
            <note type="important"></note>
            The appended document must remain opened until the current document is saved.</remarks>
            <param name="doc">The document to be appended.</param>
        </member>
        <member name="M:SelectPdf.PdfDocument.RemovePage(SelectPdf.PdfPage)">
            <summary>
            Removes the specified page from the pdf document pages collection.
            </summary>
            <param name="pdfPage">The pdf page to be removed.</param>
        </member>
        <member name="M:SelectPdf.PdfDocument.RemovePageAt(System.Int32)">
            <summary>
            Removes the pdf page at the specified index in pdf document pages collection.
            </summary>
            <param name="index">The zero based index of the pdf page to be removed.</param>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddFont(System.Drawing.Font)">
            <summary>
            Adds a system font to the pdf fonts collection.
            </summary>
            <remarks> 
            If the font was already added, the method returns the existing font. 
            By default the font is embedded into the pdf document in order to support Unicode texts.
            </remarks>
            <param name="font">The system font to be added to the fonts collection.</param>
            <returns>The pdf font added to the fonts collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddFont(System.Drawing.Font,System.Boolean)">
            <summary>
            Adds a system font to the pdf fonts collection. 
            </summary>
            <remarks>
            If the font was already added, the method returns the existing font. The <c>embedFont</c> parameter indicates if the added font 
            will be embedded into the pdf document or not.</remarks>
            <param name="font">Font for adding.The system font to be added to the fonts collection.</param>
            <param name="embedFont">Specifies if the font will be embedded into the pdf document or not.</param>
            <returns>The pdf font added to the fonts collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddFont(System.String)">
            <summary>
            Adds a system font to the pdf fonts collection. The font is loaded from the specified file.
            </summary>
            <remarks>
            If the font was already added, the method returns the existing pdf font. 
            </remarks>
            <param name="fontFileName">The file name of the font to be added.</param>
            <returns>The pdf font added to the fonts collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddFont(SelectPdf.PdfStandardFont)">
            <summary>
            Adds a standard font to the pdf fonts collection.
            </summary>
            <remarks>
            If the font was already added, the method returns the existing pdf font. 
            </remarks>
            <param name="stdFont">The standard font family.</param>
            <returns>The pdf font added to the fonts collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddFont(SelectPdf.PdfStandardCJKFont)">
            <summary>
            Adds a standard CJK font to the pdf fonts collection.
            </summary>
            <remarks>
            If the font was already added, the method returns the existing pdf font. 
            </remarks>
            <param name="stdCJKFont">The standard CJK font to add to the pdf document fonts collection.</param>
            <returns>The pdf font added to the fonts collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.GetPagesCount(System.String)">
            <summary>
            Returns the number of pages in a specified pdf document.
            </summary>
            <param name="fileName">The pdf file.</param>
            <returns>The number of pages in the specified pdf document.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.GetPagesCount(System.IO.Stream)">
            <summary>
            Returns the number of pages in a specified pdf document.
            </summary>
            <param name="stream">The stream containing the pdf document.</param>
            <returns>The number of pages in the specified pdf document.</returns>
        </member>
        <member name="P:SelectPdf.PdfDocument.DocumentInformation">
            <summary>
            Gets a reference to the object that specifies the pdf document properties.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocument.Security">
            <summary>
            Gets a reference to the object that specifies the pdf document security settings.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocument.ViewerPreferences">
            <summary>
            Controls how the pdf document will appear in a pdf viewer (like Adobe Reader).
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocument.OpenAction">
            <summary>
            The action to be performed when the pdf document is opened.
            </summary>
            <remarks>
            The pdf document can perform 2 types of actions when it is opened:
            <list type="bullet">
            <item>
            <description><see cref="T:SelectPdf.PdfActionGoTo"/> - The pdf document opens at a specific <see cref="T:SelectPdf.PdfDestination"/>.</description>
            </item>
            <item>
            <description><see cref="T:SelectPdf.PdfActionJavaScript"/> - The pdf document will run a specific javascript.</description>
            </item>
            </list>
            </remarks>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddBookmark(System.String,SelectPdf.PdfDestination)">
            <summary>
            Creates a root bookmark in the current pdf document.
            </summary>
            <param name="text">The bookmark text.</param>
            <param name="destination">The bookmark destination.</param>
            <returns>The newly added bookmark.</returns>
        </member>
        <member name="M:SelectPdf.PdfDocument.AddBookmark(System.String,SelectPdf.PdfDestination,SelectPdf.PdfBookmark)">
            <summary>
            Creates a child bookmark in the current pdf document under the specified parent bookmark.
            </summary>
            <param name="text">The bookmark text.</param>
            <param name="destination">The bookmark destination.</param>
            <param name="parent">The parent bookmark.</param>
            <returns>The newly added bookmark.</returns>
        </member>
        <member name="P:SelectPdf.PdfDocument.CompressionLevel">
            <summary>
            Gets or sets the pdf document compression level.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocument.JpegCompressionEnabled">
            <summary>
            Gets or sets a flag indicating if the JPEG compression is enabled or not for the images in the generated pdf document.
            </summary>
            <remarks>
            By default the JPEG compression is enabled to reduce the size of the generated pdf document. When the JPEG compression is enabled
            the quality of the images in the generated pdf is lower than the quality of the original images from the web page,
            and this quality depends on the <see cref="P:SelectPdf.PdfDocument.JpegCompressionLevel"/> parameter. When the <see cref="P:SelectPdf.PdfDocument.JpegCompressionLevel"/> is increased, the quality of the images in pdf decreases.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfDocument.JpegCompressionLevel">
            <summary>
            Gets or sets the compression level of images in the generated pdf document.
            </summary>
            <remarks>
            This property can take values between 0 and 100. It has effect only when the <see cref="P:SelectPdf.PdfDocument.JpegCompressionEnabled"/> is set to true.
            When the <see cref="P:SelectPdf.PdfDocument.JpegCompressionLevel"/> is 0, the compression rate is the lowest and the quality of the images is the best.
            When the <see cref="P:SelectPdf.PdfDocument.JpegCompressionLevel"/> is 100, the compression rate is the highest and quality of the images in PDF is the worst.
            <para></para>
            The default JPEG compression level is 10, which should offer a good balance between the compression rate and 
            the quality of the images in pdf.
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfDocumentException">
            <summary>
            Exception thrown by the pdf document related objects.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfDocumentException.#ctor(System.String)">
            <summary>
            Constructor.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:SelectPdf.PdfDocumentException.#ctor(System.String,System.Exception)">
            <summary>
            Constructor.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception that triggered this exception.</param>
        </member>
        <member name="T:SelectPdf.PdfDocumentInformation">
            <summary>
            This class provides the possibility to control the pdf document properties.
            </summary>
            <remarks>
            Using this class, the pdf document properties (author, title, keywords, etc) can be controlled. 
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfDocumentInformation.Language">
            <summary>
            Gets or sets the default language of the pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentInformation.Title">
            <summary>
            Gets or sets the pdf document title.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentInformation.Author">
            <summary>
            Gets or sets the name of the pdf document author.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentInformation.Subject">
            <summary>
            Gets or sets the subject of the pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentInformation.Keywords">
            <summary>
            Gets or sets the pdf document keywords.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentInformation.Producer">
            <summary>
            Gets or sets the name of the application that generated the pdf document (Select.Pdf SDK).
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentInformation.CreationDate">
            <summary>
            Gets or sets the date and time when the pdf document was created.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfDocumentOpenAction">
            <summary>
            Encapsulates an action to be performed when the pdf document is opened.
            </summary>
            <remarks>
            The pdf document can perform 2 types of actions when it is opened:
            <list type="bullet">
            <item>
            <description><see cref="T:SelectPdf.PdfActionGoTo"/> - The pdf document opens at a specific <see cref="T:SelectPdf.PdfDestination"/>.</description>
            </item>
            <item>
            <description><see cref="T:SelectPdf.PdfActionJavaScript"/> - The pdf document will run a specific javascript.</description>
            </item>
            </list>
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfDocumentOpenAction.Action">
            <summary>
            The pdf document open action as an instance of the <see cref="T:SelectPdf.PdfActionGoTo"/> or <see cref="T:SelectPdf.PdfActionJavaScript"/> classes. 
            </summary>
        </member>
        <member name="T:SelectPdf.PdfDocumentSecurity">
            <summary>
            This class provides the options to control the pdf document security. 
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentSecurity.CanAssembleDocument">
            <summary>
            Allows inserting, rotating or deleting pages or bookmarks. 
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentSecurity.CanCopyContent">
            <summary>
            Allows copying page contents such as text and images.  
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentSecurity.CanAccessibilityCopyContent">
            <summary>
            Allows copying page accessibility contents such as text and images.  
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentSecurity.CanEditAnnotations">
            <summary>
            Allows document annotations editing.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentSecurity.CanEditContent">
            <summary>
            Allows modifying document content. 
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentSecurity.CanFillFormFields">
            <summary>
            Allows filling in form fields. 
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentSecurity.CanPrint">
            <summary>
            Allows printing.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfDocumentSecurity.OwnerPassword">
            <summary>Sets or gets the owner password.</summary>
            <remarks>
            This password is required to change permissions of the pdf document (like printing or editing).
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfDocumentSecurity.UserPassword">
            <summary>Gets or sets the user password. </summary>
            <remarks>
            This password is required to open the pdf document. 
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfEncryptionKeySize">
            <summary>
            The length of the pdf encryption key.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfEncryptionKeySize.EncryptKey256Bit">
            <summary>
            246 bit length.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfEncryptionKeySize.EncryptKey128Bit">
            <summary>
            128 bit length.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfEncryptionKeySize.EncryptKey40Bit">
            <summary>
            40 bit length.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfEncryptionAlgorithm">
            <summary>
            The encryption algorithm.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfEncryptionAlgorithm.RC4">
            <summary>
            RC4
            </summary>
        </member>
        <member name="F:SelectPdf.PdfEncryptionAlgorithm.AES">
            <summary>
            AES
            </summary>
        </member>
        <member name="T:SelectPdf.PdfFont">
            <summary>
            Represents a font in a pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfFont.FontFamily">
            <summary>
            Gets the name of the font family.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfFont.Height">
            <summary>
            Gets the font height.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfFont.Size">
            <summary>
            Gets or sets the font size in points.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfFont.FontBaseFamily">
            <summary>
            Gets the predefined family name.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfFont.IsUnderline">
            <summary>
            A flag to indicate if the font is underline.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfFont.IsStrikeout">
            <summary>
            A flag to indicate if the font is strikeout.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfFont.GetCharWidth(System.Char)">
            <summary>
            Gets the width of the specified character in points.
            </summary>
            <param name="c">The character to be measured.</param>
            <returns>The character width in points.</returns>
        </member>
        <member name="M:SelectPdf.PdfFont.GetTextWidth(System.String)">
            <summary>
            Get the width in points of the specified string.
            </summary>
            <param name="text">The string to be measured.</param>
            <returns>The width of the string in points.</returns>
        </member>
        <member name="M:SelectPdf.PdfFont.GetTextSize(System.String)">
            <summary>
            Gets the size of the specified string in points.
            </summary>
            <param name="text">The string to be measured.</param>
            <returns>The size of the string in points.</returns>
        </member>
        <member name="M:SelectPdf.PdfFont.GetSystemFont">
            <summary>
            Converts the current pdf font to a corresponding system font.
            </summary>
            <returns>A system font corresponding to the current pdf font object.</returns>
        </member>
        <member name="M:SelectPdf.PdfFont.Clone(SelectPdf.PdfDocument)">
            <summary>
            Creates a clone of this font in the specified pdf document.
            </summary>
            <param name="document">The pdf document where the font is cloned.</param>
            <returns>The font object clone.</returns>
        </member>
        <member name="T:SelectPdf.PdfFontCollection">
            <summary>
            Represents a collection of fonts in a pdf document.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontCollection.document">
            <summary>
            The parent PDF Document
            </summary>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.#ctor(SelectPdf.PdfDocument)">
            <summary>
            Creates a font collection for the specified PDF document
            </summary>
            <param name="document"></param>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.GetEnumerator">
            <summary>
            Gets the collection enumerator.
            </summary>
            <returns>The collection enumerator.</returns>
        </member>
        <member name="P:SelectPdf.PdfFontCollection.Item(System.Int32)">
            <summary>
            Gets or sets the font at the specified zero based index in the fonts collection.
            </summary>
            <param name="index">The zero based font index in the fonts pages collection.</param>
            <returns>The pdf font at the specified index.</returns>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.Add(System.Drawing.Font)">
            <summary>
            Adds a system font to the pdf fonts collection.
            </summary>
            <remarks> 
            If the font was already added, the method returns the existing font. 
            By default the font is embedded into the pdf document in order to support Unicode texts.
            </remarks>
            <param name="font">The system font to be added to the fonts collection.</param>
            <returns>The pdf font added to the fonts collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.Add(System.Drawing.Font,System.Boolean)">
            <summary>
            Adds a system font to the pdf fonts collection. 
            </summary>
            <remarks>
            If the font was already added, the method returns the existing font. The <c>embedFont</c> parameter indicates if the added font 
            will be embedded into the pdf document or not.</remarks>
            <param name="font">Font for adding.The system font to be added to the fonts collection.</param>
            <param name="embedFont">Specifies if the font will be embedded into the pdf document or not.</param>
            <returns>The pdf font added to the fonts collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.Add(System.String)">
            <summary>
            Adds a system font to the pdf fonts collection. The font is loaded from the specified file.
            </summary>
            <remarks>
            If the font was already added, the method returns the existing pdf font. 
            </remarks>
            <param name="fontFileName">The file name of the font to be added.</param>
            <returns>The pdf font added to the fonts collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.Add(SelectPdf.PdfStandardFont)">
            <summary>
            Adds a standard font to the pdf fonts collection.
            </summary>
            <remarks>
            If the font was already added, the method returns the existing pdf font. 
            </remarks>
            <param name="stdFont">The standard font family.</param>
            <returns>The pdf font added to the fonts collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.Add(SelectPdf.PdfStandardCJKFont)">
            <summary>
            Adds a standard CJK font to the pdf fonts collection.
            </summary>
            <remarks>
            If the font was already added, the method returns the existing pdf font. 
            </remarks>
            <param name="stdCJKFont">The standard CJK font to add to the pdf document fonts collection.</param>
            <returns>The pdf font added to the fonts collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.Add(SelectPdf.PdfFont)">
            <summary>
            Adds a pdf font to the pdf fonts collection.
            </summary>
            <remarks>
            If the font was already added, the method returns the existing pdf font.
            </remarks>
            <param name="font">The pdf font object to be add to the collection.</param>
            <returns>The pdf font added to the fonts collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.Add(SelectPdf.PdfFont[])">
            <summary>
            Adds the specified array of pdf fonts to the pdf document fonts collection. 
            </summary>
            <param name="fonts">The pdf fonts to be added to the collection.</param>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.Contains(SelectPdf.PdfFont)">
            <summary>
            Checks if the specified font belongs to the fonts collection.
            </summary>
            <param name="font">The specified pdf font.</param>
            <returns>True if the font is found in the collection, otherwise returns false.</returns>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.IndexOf(SelectPdf.PdfFont)">
            <summary>
            Returns the index of the specified font in the fonts collection.
            </summary>
            <param name="font">The font to search in collection.</param>
            <returns>The index of font or -1 if the font does not exist.</returns>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.Remove(SelectPdf.PdfFont)">
            <summary>
            Removes the specified pdf font from the fonts collection.
            </summary>
            <param name="font">The font to be removed from the pdf fonts collection.</param>
        </member>
        <member name="M:SelectPdf.PdfFontCollection.Insert(System.Int32,SelectPdf.PdfFont)">
            <summary>
            Inserts a font to the specified index in the pdf fonts collection.
            </summary>
            <param name="font">The font to be inserted in the collection.</param>
            <param name="index">Index in collection where to insert the font.</param>
        </member>
        <member name="T:SelectPdf.PdfFontType">
            <summary>
            The list of supported fonts for the header and footer
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.None">
            <summary>
            None
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.Helvetica">
            <summary>
            Helvetica
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.HelveticaBold">
            <summary>
            HelveticaBold
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.HelveticaOblique">
            <summary>
            HelveticaOblique
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.HelvetivaBoldOblique">
            <summary>
            HelvetivaBoldOblique
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.Courier">
            <summary>
            Courier
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.CourierBold">
            <summary>
            CourierBold
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.CourierOblique">
            <summary>
            CourierOblique
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.CourierBoldOblique">
            <summary>
            CourierBoldOblique
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.TimesRoman">
            <summary>
            TimesRoman
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.TimesBold">
            <summary>
            TimesBold
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.TimesBoldItalic">
            <summary>
            TimesBoldItalic
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.TimesItalic">
            <summary>
            TimesItalic
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.Symbol">
            <summary>
            Symbol
            </summary>
        </member>
        <member name="F:SelectPdf.PdfFontType.ZapfDingbats">
            <summary>
            ZapfDingbats
            </summary>
        </member>
        <member name="T:SelectPdf.PdfFooter">
            <summary>
            Handles the properties of the footer of the generated pdf document.
            </summary>
            <remarks>
            This class encapsulates the options to control the footer in the rendered pdf document. The <see cref="T:SelectPdf.HtmlToPdf"/> class contains a 
            reference to an object of this type. 
            <para></para>
            The footer is displayed only if <see cref="P:SelectPdf.HtmlToPdfOptions.DisplayFooter"/> property of the <see cref="P:SelectPdf.HtmlToPdf.Options"/> object is true.
            If the <see cref="P:SelectPdf.HtmlToPdfOptions.DisplayFooter"/> property of the <see cref="P:SelectPdf.HtmlToPdf.Options"/> 
            object is false, the footer will not be displayed and all the options set here will have no effect.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfFooter.Height">
            <summary>
            The height of the pdf document footer.
            </summary>
            <remarks>
            This height is specified in points. 1 point is 1/72 inch. The default value is 35pt.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfFooter.FirstPageNumber">
            <summary>
            Controls the page number for the first page being rendered.
            </summary>
            <remarks>
            The page number is displayed setting a <c>{page_number}</c> placeholder in the <see cref="P:SelectPdf.PdfTextSection.Text"/> property.
            By default the page numbers start with 1. This can be changed using the <see cref="P:SelectPdf.PdfFooter.FirstPageNumber"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfFooter.TotalPagesOffset">
            <summary>
            Controls the total number of pages offset in the generated pdf document.
            </summary>
            <remarks>
            The total number of pages is displayed setting a <c>{total_pages}</c> placeholder in the <see cref="P:SelectPdf.PdfTextSection.Text"/> property.
            The total number of pages can be incremented with a value specified by the <see cref="P:SelectPdf.PdfFooter.TotalPagesOffset"/> property. 
            This could be useful when the generated pdf will be merged with other documents.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfFooter.DisplayOnFirstPage">
            <summary>
            Controls the visibility of the footer on the first page of the generated pdf document.
            </summary>
            <remarks>
            When this property is true the footer will be displayed on the first page of the pdf document, otherwise it will not be visible. 
            This property has priority over <see cref="P:SelectPdf.PdfFooter.DisplayOnOddPages"/>. The default value is true.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfFooter.DisplayOnOddPages">
            <summary>
            Controls the visibility of the footer on the odd numbered pages of the generated pdf document.
            </summary>
            <remarks>
            By default the footer is displayed on both odd and even numbered pages.
            The <see cref="P:SelectPdf.PdfFooter.DisplayOnFirstPage"/> property has priority over <see cref="P:SelectPdf.PdfFooter.DisplayOnOddPages"/>.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfFooter.DisplayOnEvenPages">
            <summary>
            Controls the visibility of the footer on the even numbered pages of the generated pdf document.
            </summary>
            <remarks>
            By default the footer is displayed on both odd and even numbered pages.
            </remarks>
        </member>
        <member name="M:SelectPdf.PdfFooter.Add(SelectPdf.PdfSectionElement)">
            <summary>
            Adds an additional element to the footer of the pdf document.
            </summary>
            <remarks>
            There are 3 section elements that can be added to the footer of the pdf document: <see cref="T:SelectPdf.PdfHtmlSection"/>, <see cref="T:SelectPdf.PdfTextSection"/> and <see cref="T:SelectPdf.PdfImageSection"/>.
            </remarks>
            <param name="element">The element that will be rendered in the pdf footer.</param>
        </member>
        <member name="M:SelectPdf.PdfFooter.AddTextArea(SelectPdf.PdfTextSection)">
            <summary>
            Adds a TextArea element to the additional elements list rendered after the 
            predefined footer elements. The additional elements are rendered in the order they 
            are added to the list.
            </summary>
            <param name="textArea">The TextArea object to be rendered.</param>
        </member>
        <member name="M:SelectPdf.PdfFooter.AddImageArea(SelectPdf.PdfImageSection)">
            <summary>
            Adds an ImageArea element to the additional elements list rendered after the 
            predefined footer elements. The additional elements are rendered in the order they 
            are added to the list.
            </summary>
            <param name="imageArea">The ImageArea object to be rendered.</param>
        </member>
        <member name="M:SelectPdf.PdfFooter.AddHtmlToPdfArea(SelectPdf.PdfHtmlSection)">
            <summary>
            Adds a PdfHtmlSection element to the additional elements list rendered after the 
            predefined footer elements. The additional elements are rendered in the order they 
            are added to the list.
            </summary>
            <param name="htmlArea">The PdfHtmlSection object to be rendered.</param>
        </member>
        <member name="T:SelectPdf.PdfHeader">
            <summary>
            Handles the properties of the header of the generated pdf document.
            </summary>
            <remarks>
            This class encapsulates the options to control the header in the rendered pdf document. The <see cref="T:SelectPdf.HtmlToPdf"/> class contains a 
            reference to an object of this type. 
            <para></para>
            The header is displayed only if <see cref="P:SelectPdf.HtmlToPdfOptions.DisplayHeader"/> property of the <see cref="P:SelectPdf.HtmlToPdf.Options"/> object is true.
            If the <see cref="P:SelectPdf.HtmlToPdfOptions.DisplayHeader"/> property of the <see cref="P:SelectPdf.HtmlToPdf.Options"/> 
            object is false, the header will not be displayed and all the options set here will have no effect.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHeader.Height">
            <summary>
            The height of the pdf document header.
            </summary>
            <remarks>
            This height is specified in points. 1 point is 1/72 inch. The default value is 45pt.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHeader.FirstPageNumber">
            <summary>
            Controls the page number for the first page being rendered.
            </summary>
            <remarks>
            The page number is displayed setting a <c>{page_number}</c> placeholder in the <see cref="P:SelectPdf.PdfTextSection.Text"/> property.
            By default the page numbers start with 1. This can be changed using the <see cref="P:SelectPdf.PdfHeader.FirstPageNumber"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHeader.TotalPagesOffset">
            <summary>
            Controls the total number of pages offset in the generated pdf document.
            </summary>
            <remarks>
            The total number of pages is displayed setting a <c>{total_pages}</c> placeholder in the <see cref="P:SelectPdf.PdfTextSection.Text"/> property.
            The total number of pages can be incremented with a value specified by the <see cref="P:SelectPdf.PdfHeader.TotalPagesOffset"/> property. 
            This could be useful when the generated pdf will be merged with other documents.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHeader.DisplayOnFirstPage">
            <summary>
            Controls the visibility of the header on the first page of the generated pdf document.
            </summary>
            <remarks>
            When this property is true the header will be displayed on the first page of the pdf document, otherwise it will not be visible. 
            This property has priority over <see cref="P:SelectPdf.PdfHeader.DisplayOnOddPages"/>. The default value is true.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHeader.DisplayOnOddPages">
            <summary>
            Controls the visibility of the header on the odd numbered pages of the generated pdf document.
            </summary>
            <remarks>
            By default the header is displayed on both odd and even numbered pages.
            The <see cref="P:SelectPdf.PdfHeader.DisplayOnFirstPage"/> property has priority over <see cref="P:SelectPdf.PdfHeader.DisplayOnOddPages"/>.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHeader.DisplayOnEvenPages">
            <summary>
            Controls the visibility of the header on the even numbered pages of the generated pdf document.
            </summary>
            <remarks>
            By default the header is displayed on both odd and even numbered pages.
            </remarks>
        </member>
        <member name="M:SelectPdf.PdfHeader.Add(SelectPdf.PdfSectionElement)">
            <summary>
            Adds an additional element to the header of the pdf document.
            </summary>
            <remarks>
            There are 3 section elements that can be added to the header of the pdf document: <see cref="T:SelectPdf.PdfHtmlSection"/>, <see cref="T:SelectPdf.PdfTextSection"/> and <see cref="T:SelectPdf.PdfImageSection"/>.
            </remarks>
            <param name="element">The element that will be rendered in the pdf header.</param>
        </member>
        <member name="M:SelectPdf.PdfHeader.AddTextArea(SelectPdf.PdfTextSection)">
            <summary>
            Adds a TextArea element to the additional elements list rendered after the 
            predefined header elements. The additional elements are rendered in the order they 
            are added to the list.
            </summary>
            <param name="textArea">The TextArea object to be rendered.</param>
        </member>
        <member name="M:SelectPdf.PdfHeader.AddImageArea(SelectPdf.PdfImageSection)">
            <summary>
            Adds an ImageArea element to the additional elements list rendered after the 
            predefined header elements. The additional elements are rendered in the order they 
            are added to the list.
            </summary>
            <param name="imageArea">The ImageArea object to be rendered.</param>
        </member>
        <member name="M:SelectPdf.PdfHeader.AddHtmlToPdfArea(SelectPdf.PdfHtmlSection)">
            <summary>
            Adds a PdfHtmlSection element to the additional elements list rendered after the 
            predefined header elements. The additional elements are rendered in the order they 
            are added to the list.
            </summary>
            <param name="htmlArea">The PdfHtmlSection object to be rendered.</param>
        </member>
        <member name="T:SelectPdf.PdfHtmlSection">
            <summary>
            Represents a html element that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html can be specified by either an url or a html string.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfHtmlSection.DEFAULT_HTML_VIEWER_WIDTH_PX">
            <summary>
            The default virtual browser width in pixels
            </summary>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.String,System.String)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by a html string. The html will be converted and rendered at the (0,0) location in the pdf header or footer. 
            The width of the element in pdf is the available width in pdf page and the height is auto determined.
            <para>
            The web page is rendered in an internal browser with the default width of 1024px and height auto determined. 
            This can be changed using the <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> properties.
            </para>
            </remarks>
            <param name="htmlString">The html string to be converted to pdf.</param>
            <param name="baseUrl">This parameter allows the converter to resolve relative urls. Basically, <c>baseUrl + relative image/css url = full absolute url</c>.</param>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.String)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by an url. The html will be converted and rendered at the (0,0) location in the pdf header or footer. 
            The width of the element in pdf is the available width in pdf page and the height is auto determined.
            <para>
            The web page is rendered in an internal browser with the default width of 1024px and height auto determined. 
            This can be changed using the <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> properties.
            </para>
            </remarks>
            <param name="url">The full url of the web page to be converted to pdf. The url can be also the full path of a html file from the local file system.</param>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.Single,System.Single,System.String,System.String)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by a html string. The html will be converted and rendered at the (x,y) location in the pdf header or footer. 
            The width of the element in pdf is the available width in pdf page and the height is auto determined.
            <para>
            The web page is rendered in an internal browser with the default width of 1024px and height auto determined. 
            This can be changed using the <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> properties.
            </para>
            </remarks>
            <param name="x">The position in points on the X axis where the element will be rendered.</param>
            <param name="y">The position in points on the Y axis where the element will be rendered.</param>
            <param name="htmlString">The html string to be converted to pdf.</param>
            <param name="baseUrl">This parameter allows the converter to resolve relative urls. Basically, <c>baseUrl + relative image/css url = full absolute url</c>.</param>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.Single,System.Single,System.String)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by an url. The html will be converted and rendered at the (x,y) location in the pdf header or footer. 
            The width of the element in pdf is the available width in pdf page and the height is auto determined.
            <para>
            The web page is rendered in an internal browser with the default width of 1024px and height auto determined. 
            This can be changed using the <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> properties.
            </para>
            </remarks>
            <param name="x">The position in points on the X axis where the element will be rendered.</param>
            <param name="y">The position in points on the Y axis where the element will be rendered.</param>
            <param name="url">The full url of the web page to be converted to pdf. The url can be also the full path of a html file from the local file system.</param>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.Single,System.Single,System.Single,System.String,System.String)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by a html string. The html will be converted and rendered at the (x,y) location in the pdf header or footer. 
            The width of the element in pdf is specified as parameter and the height is auto determined.
            <para>
            The web page is rendered in an internal browser with the default width of 1024px and height auto determined. 
            This can be changed using the <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> properties.
            </para>
            </remarks>
            <param name="x">The position in points on the X axis where the element will be rendered.</param>
            <param name="y">The position in points on the Y axis where the element will be rendered.</param>
            <param name="width">The destination width in points for the rendered content.</param>
            <param name="htmlString">The html string to be converted to pdf.</param>
            <param name="baseUrl">This parameter allows the converter to resolve relative urls. Basically, <c>baseUrl + relative image/css url = full absolute url</c>.</param>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by an url. The html will be converted and rendered at the (x,y) location in the pdf header or footer. 
            The width of the element in pdf is specified as parameter and the height is auto determined.
            <para>
            The web page is rendered in an internal browser with the default width of 1024px and height auto determined. 
            This can be changed using the <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> properties.
            </para>
            </remarks>
            <param name="x">The position in points on the X axis where the element will be rendered.</param>
            <param name="y">The position in points on the Y axis where the element will be rendered.</param>
            <param name="width">The destination width in points for the rendered content.</param>
            <param name="url">The full url of the web page to be converted to pdf. The url can be also the full path of a html file from the local file system.</param>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.Single,System.Single,System.Single,System.String,System.String,System.Int32)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by a html string. The html will be converted and rendered at the (x,y) location in the pdf header or footer. 
            The width of the element in pdf is specified as parameter and the height is auto determined.
            <para>
            The web page is rendered in an internal browser with the specified width and with height auto determined. 
            This can be changed using the <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> properties.
            </para>
            </remarks>
            <param name="x">The position in points on the X axis where the element will be rendered.</param>
            <param name="y">The position in points on the Y axis where the element will be rendered.</param>
            <param name="width">The destination width in points for the rendered content.</param>
            <param name="htmlString">The html string to be converted to pdf.</param>
            <param name="baseUrl">This parameter allows the converter to resolve relative urls. Basically, <c>baseUrl + relative image/css url = full absolute url</c>.</param>
            <param name="webPageWidth">The virtual browser width in pixels. The default value is 1024 pixels. The effect of this parameter
            is similar with viewing the web page in a browser window with the specified width. When this parameter is less than or equal to 0, the
            converter will try to auto-determine the web page width from the html body element width.</param>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.Single,System.Single,System.Single,System.String,System.Int32)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by an url. The html will be converted and rendered at the (x,y) location in the pdf header or footer. 
            The width of the element in pdf is specified as parameter and the height is auto determined.
            <para>
            The web page is rendered in an internal browser with the specified width and with height auto determined. 
            This can be changed using the <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> properties.
            </para>
            </remarks>
            <param name="x">The position in points on the X axis where the element will be rendered.</param>
            <param name="y">The position in points on the Y axis where the element will be rendered.</param>
            <param name="width">The destination width in points for the rendered content.</param>
            <param name="url">The full url of the web page to be converted to pdf. The url can be also the full path of a html file from the local file system.</param>
            <param name="webPageWidth">The virtual browser width in pixels. The default value is 1024 pixels. The effect of this parameter
            is similar with viewing the web page in a browser window with the specified width. When this parameter is less than or equal to 0, the
            converter will try to auto-determine the web page width from the html body element width.</param>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.Single,System.Single,System.Single,System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by a html string. The html will be converted and rendered at the (x,y) location in the pdf header or footer. 
            The width of the element in pdf is specified as parameter and the height is auto determined.
            <para>
            The web page is rendered in an internal browser with the specified width and height. 
            </para>
            </remarks>
            <param name="x">The position in points on the X axis where the element will be rendered.</param>
            <param name="y">The position in points on the Y axis where the element will be rendered.</param>
            <param name="width">The destination width in points for the rendered content.</param>
            <param name="htmlString">The html string to be converted to pdf.</param>
            <param name="baseUrl">This parameter allows the converter to resolve relative urls. Basically, <c>baseUrl + relative image/css url = full absolute url</c>.</param>
            <param name="webPageWidth">The virtual browser width in pixels. The default value is 1024 pixels. The effect of this parameter
            is similar with viewing the web page in a browser window with the specified width. When this parameter is less than or equal to 0, the
            converter will try to auto-determine the web page width from the html body element width.</param>
            <param name="webPageHeight">The virtual browser height in pixels. The default value is 0 which means the height will
            be auto-determined. The effect of this parameter is similar with viewing the web page in a browser window
            with the specified height.</param>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.Single,System.Single,System.Single,System.Single,System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by a html string. The html will be converted and rendered at the (x,y) location in the pdf header or footer. 
            The width and height of the element in pdf are also specified as parameters.
            <para>
            The web page is rendered in an internal browser with the specified width and height. 
            </para>
            </remarks>
            <param name="x">The position in points on the X axis where the element will be rendered.</param>
            <param name="y">The position in points on the Y axis where the element will be rendered.</param>
            <param name="width">The destination width in points for the rendered content.</param>
            <param name="height">The destination height in points for the rendered content.</param>
            <param name="htmlString">The html string to be converted to pdf.</param>
            <param name="baseUrl">This parameter allows the converter to resolve relative urls. Basically, <c>baseUrl + relative image/css url = full absolute url</c>.</param>
            <param name="webPageWidth">The virtual browser width in pixels. The default value is 1024 pixels. The effect of this parameter
            is similar with viewing the web page in a browser window with the specified width. When this parameter is less than or equal to 0, the
            converter will try to auto-determine the web page width from the html body element width.</param>
            <param name="webPageHeight">The virtual browser height in pixels. The default value is 0 which means the height will
            be auto-determined. The effect of this parameter is similar with viewing the web page in a browser window
            with the specified height.</param>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.Single,System.Single,System.Single,System.String,System.Int32,System.Int32)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by an url. The html will be converted and rendered at the (x,y) location in the pdf header or footer. 
            The width of the element in pdf is specified as parameter and the height is auto determined.
            <para>
            The web page is rendered in an internal browser with the specified width and height. 
            </para>
            </remarks>
            <param name="x">The position in points on the X axis where the element will be rendered.</param>
            <param name="y">The position in points on the Y axis where the element will be rendered.</param>
            <param name="width">The destination width in points for the rendered content.</param>
            <param name="url">The full url of the web page to be converted to pdf. The url can be also the full path of a html file from the local file system.</param>
            <param name="webPageWidth">The virtual browser width in pixels. The default value is 1024 pixels. The effect of this parameter
            is similar with viewing the web page in a browser window with the specified width. When this parameter is less than or equal to 0, the
            converter will try to auto-determine the web page width from the html body element width.</param>
            <param name="webPageHeight">The virtual browser height in pixels. The default value is 0 which means the height will
            be auto-determined. The effect of this parameter is similar with viewing the web page in a browser window
            with the specified height.</param>
        </member>
        <member name="M:SelectPdf.PdfHtmlSection.#ctor(System.Single,System.Single,System.Single,System.Single,System.String,System.Int32,System.Int32)">
            <summary>
            Creates a html object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The html element is specified in this case by an url. The html will be converted and rendered at the (x,y) location in the pdf header or footer. 
            The width and height of the element in pdf are also specified as parameters.
            <para>
            The web page is rendered in an internal browser with the specified width and height. 
            </para>
            </remarks>
            <param name="x">The position in points on the X axis where the element will be rendered.</param>
            <param name="y">The position in points on the Y axis where the element will be rendered.</param>
            <param name="width">The destination width in points for the rendered content.</param>
            <param name="height">The destination height in points for the rendered content.</param>
            <param name="url">The full url of the web page to be converted to pdf. The url can be also the full path of a html file from the local file system.</param>
            <param name="webPageWidth">The virtual browser width in pixels. The default value is 1024 pixels. The effect of this parameter
            is similar with viewing the web page in a browser window with the specified width. When this parameter is less than or equal to 0, the
            converter will try to auto-determine the web page width from the html body element width.</param>
            <param name="webPageHeight">The virtual browser height in pixels. The default value is 0 which means the height will
            be auto-determined. The effect of this parameter is similar with viewing the web page in a browser window
            with the specified height.</param>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.WebPageWidth">
            <summary>Gets or sets the width of the converted web page as it would appear in the internal browser used to render the html.</summary>
            <remarks>The web page width is specified in pixels and the default value is 1024px. 
            The page width is only an indication of the minimum page width recommended for conversion. If the content does not fit this width, 
            the converter will automatically resize the internal browser to fit the whole html content. To avoid this, the <see cref="P:SelectPdf.PdfHtmlSection.WebPageFixedSize"/> property needs to be set to true.
            When <see cref="P:SelectPdf.PdfHtmlSection.WebPageFixedSize"/> is true, the web page will be rendered with the specified <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> even though the content might be truncated.
            <para></para>
            <para></para>
            If <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> is set to 0, the converter will automatically determine the page width, finding the width that will fit the html content.
            <para>This property can also be set directly in the constructor of <see cref="T:SelectPdf.PdfHtmlSection"/> class.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.WebPageHeight">
            <summary>Gets or sets the height of the converted web page as it would appear in the internal browser used to render the html.</summary>
            <remarks>
            The web page height is specified in pixels and the default value is 0px. This means that the converter will automatically calculate the page height.
            <para>Generally this property does not need to be changed, but there are situations when the converter cannot calculate correctly the web page height (for example for web pages with frames) and
            in that case, <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> needs to be set, otherwise no content might appear in the generated pdf.
            </para>
            <para>
            Note: If the <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> is set, the content that exceeds this page height is truncated and will not appear in the generated pdf document.
            Only using the default 0 value will allow the whole page content to be rendered all the time in the generated pdf document.
            </para>
            <para>This property can also be set directly in the constructor of <see cref="T:SelectPdf.PdfHtmlSection"/> class.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.WebPageFixedSize">
            <summary>Controls whether the web page is rendered with a fixed size internal browser or the size automatically extends to make the whole content visible.</summary>
            <remarks>
            The default value of this property is false.
            <para></para>
            <para></para>
            If the web page does not fit the dimensions specified by the <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/> properties and <see cref="P:SelectPdf.PdfHtmlSection.WebPageFixedSize"/> 
            is set to false, the converter will try to automatically calculate a larger width and height to be able to display the whole html content.
            <para>
            If <see cref="P:SelectPdf.PdfHtmlSection.WebPageFixedSize"/> is set to true, this will indicate the converter not to calculate another width and height, but use width and height specified by <see cref="P:SelectPdf.PdfHtmlSection.WebPageWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/>.
            These dimensions with be used even though the content will not fit the available space and it will be truncated.
            </para>
            <para>
            Note: If <see cref="P:SelectPdf.PdfHtmlSection.WebPageFixedSize"/> is set to true, a page height needs to be set using <see cref="P:SelectPdf.PdfHtmlSection.WebPageHeight"/>, because the default value (0) will make the converter fail (cannot render a web page with no height).
            </para>
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.VisibleWebElementId">
            <summary>
            Use this property to convert only a certain section of the page, specified by the html element ID.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.StartupMode">
            <summary>
            Use this property to specify how the conversion starts.
            </summary>
            <remarks>
            By default this is set to <see cref="F:SelectPdf.HtmlToPdfStartupMode.Automatic"/> and the conversion is started as soon as the page loads (and <see cref="P:SelectPdf.PdfHtmlSection.MinPageLoadTime"/> elapses). 
            If set to <see cref="F:SelectPdf.HtmlToPdfStartupMode.Manual"/>, the conversion is started only by a javascript call to <c>SelectPdf.startConversion()</c> from within the web page.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.StartupScript">
            <summary>
            Use this property to specify some JavaScript code that will be injected into the page that is converted. 
            </summary>
            <remarks>The JavaScript code specified here will run before any other script on the page.</remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.KeepTextsTogether">
            <summary>
            This property instructs the converter whether to try to avoid cutting off the text lines between pdf pages or not.
            </summary>
            <remarks>The default value is true and the converter tries to avoid text cutting between pdf pages.</remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.KeepImagesTogether">
            <summary>
            This property instructs the converter whether to try to avoid cutting off the images between pdf pages or not.
            </summary>
            <remarks>The default value is false and the converter does not try to avoid images cutting between pdf pages.</remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.AutoFitWidth">
            <summary>Specifies the horizontal auto fit mode.</summary>
            <remarks>
            The converter considers both <see cref="P:SelectPdf.PdfHtmlSection.AutoFitWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.AutoFitHeight"/> when the html content is rendered in the pdf page or specified rectangle.
            <para></para>
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.NoAdjustment"/>, the html content is not resized horizontally in any way to fit the available space. If the content is larger,
            it will be cut and not all of it will be displayed in the generated pdf file.
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.ShrinkOnly"/>, the html content is resized only if the content width is larger than the destination space 
            (pdf page or rectangle) width.
            In this case, the content is shrunk to fit the destination space width and the elements that it contains (texts, images) will appear smaller in the generated 
            pdf document than in the original web page.
            If the original content width is smaller than the destination width, no adjustments will be done and the content will be rendered exactly as it is, 
            even though some additional white space might appear to its right.
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.AutoFit"/>, the html content is resized to fit the available width of the destination space. 
            If the original content width is smaller than the destination width, 
            the elements rendered (texts, images) will appear larger in the generated pdf document. If the original content width is larger than the destination width, 
            the elements rendered (texts, images) will appear smaller in the generated pdf document.
            <para>The default value of this property is <see cref="F:SelectPdf.HtmlToPdfPageFitMode.ShrinkOnly"/>.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.AutoFitHeight">
            <summary>Specifies the vertical auto fit mode.</summary>
            <remarks>
            The converter considers both <see cref="P:SelectPdf.PdfHtmlSection.AutoFitWidth"/> and <see cref="P:SelectPdf.PdfHtmlSection.AutoFitHeight"/> when the html content is rendered in the pdf page or specified rectangle.
            <para></para>
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.NoAdjustment"/>, the html content is not resized vertically in any way to fit the available space. If the content is larger,
            it will be cut and not all of it will be displayed in the generated pdf file.
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.ShrinkOnly"/>, the html content is resized only if the content height is larger than the destination space 
            (pdf page or rectangle) height.
            In this case, the content is shrunk to fit the destination space height and the elements that it contains (texts, images) will appear smaller in the generated 
            pdf document than in the original web page.
            If the original content height is smaller than the destination height, no adjustments will be done and the content will be rendered exactly as it is, 
            even though some additional white space might appear at the bottom.
            <para></para>
            If this property is set to <see cref="F:SelectPdf.HtmlToPdfPageFitMode.AutoFit"/>, the converter will treat it like <see cref="F:SelectPdf.HtmlToPdfPageFitMode.ShrinkOnly"/>.
            <para>The default value of this property is <see cref="F:SelectPdf.HtmlToPdfPageFitMode.NoAdjustment"/> and for rendering, the converter will only take <see cref="P:SelectPdf.PdfHtmlSection.AutoFitWidth"/> into consideration.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.DrawBackground">
            <summary>
            Gets or sets a flag indicating if the web page background is rendered in pdf.
            </summary>
            <remarks>
            The default value for this property is true and the page background is rendered into the generated pdf. 
            Even though it might be white, it will still make invisible any pdf element that was rendered behind it.
            Setting this property to false can make visible the existing pdf elements behind this html element.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.JavaScriptEnabled">
            <summary>
            Enable scripts when rendering the url or html string. 
            </summary>
            <remarks>The default value of this property is true and the converter will run any javascript available when the page loads.
            <para></para>
            <para>Note: If the javascript requires some time to load, <see cref="P:SelectPdf.PdfHtmlSection.MinPageLoadTime"/> property should be set to delay the conversion 
            with the specified number of seconds and allow the javascript to run.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.PluginsEnabled">
            <summary>
            A flag indicating if plugins (like Flash players) are enabled in the converter. 
            </summary>
            <remarks>
            The default value for this property is true.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.CssMediaType">
            <summary>
            Indicates what css styles are used when the web page is rendered.
            </summary>
            <remarks>The default value is <see cref="F:SelectPdf.HtmlToPdfCssMediaType.Screen"/> and the page is rendered like in a regular web browser. 
            If the page contains printer specific styles, the property should be set to <see cref="F:SelectPdf.HtmlToPdfCssMediaType.Print"/> to have the page rendered using those css styles.</remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.EmbedFonts">
            <summary>
            Instructs the converter to embed all the needed fonts into the pdf document or not.
            </summary>
            <remarks>
            The default value for this property is false and the fonts are not automatically embedded.
            <para>
            This property does not control the web fonts (fonts that are downloaded with the web page). The web fonts are always embedded because they are not installed on the machine.
            </para>
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.MinPageLoadTime">
            <summary>
            An additional time in seconds to wait for asynchronous items to be loaded before the web page is rendered.
            </summary>
            <remarks>
            A delay of this type could be necessary if javascripts are loaded when the page is rendered or if Flash movies are played.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.MaxPageLoadTime">
            <summary>
            The web page navigation timeout in seconds. 
            </summary>
            <remarks>Default value is 60 seconds. This value should be increased if large web pages are loaded.</remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.SecureProtocol">
            <summary>
            Protocol used for secure (HTTPS) connections. 
            </summary>
            <remarks>Default and recommended value is Tls11OrNewer. It means that TLS 1.1 or newer is used for secure connections.</remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.HttpCookies">
            <summary>
            Gets the collection of custom HTTP cookies used for the conversion. 
            </summary>
            <remarks>
            This property returns a reference to the collection of custom HTTP cookies to be sent by the converter to the web server when the web page is rendered. 
            A cookie is defined by a name/value pair that can be added to the collection using the <see cref="M:System.Collections.Specialized.NameValueCollection.Add(System.String,System.String)"/> method of the <see cref="P:SelectPdf.PdfHtmlSection.HttpCookies"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.HttpHeaders">
            <summary>
            Get the collection of custom HTTP headers used for the conversion.
            </summary>
            <remarks>
            This property returns a reference to the collection of custom HTTP headers to be sent by the converter to the web server when the web page is rendered.
            A custom HTTP header is defined by a name/value pair
            that can be added to the collection using the <see cref="M:System.Collections.Specialized.NameValueCollection.Add(System.String,System.String)"/> method of the <see cref="P:SelectPdf.PdfHtmlSection.HttpHeaders"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.HttpPostParameters">
            <summary>
            Gets the collection of custom HTTP POST parameters used for the conversion. 
            </summary>
            <remarks>
            This property returns a reference to the collection of custom HTTP POST parameters that will be sent by the converter to the web server when the web page is rendered. 
            A POST parameter is defined by a name/value pair that can be added to the collection using the <see cref="M:System.Collections.Specialized.NameValueCollection.Add(System.String,System.String)"/> method of the <see cref="P:SelectPdf.PdfHtmlSection.HttpPostParameters"/> property.
            When this collection has at least one parameter, the rendered page is a accessed via POST method. Otherwise the method is GET.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.ProxyOptions">
            <summary>
            Gets a reference to an object containing the proxy settings used to access the web page that is being converted.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.ExternalLinksEnabled">
            <summary>
            Controls the rendering of external hyperlinks in pdf.
            </summary>
            <remarks>
            The default value for this property is true and in this case all external hyperlinks from the web page will be rendered in the generated pdf document.
            <para>When this property is set to false, external links are ignore and they do not appear in the generated pdf document.</para>
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.InternalLinksEnabled">
            <summary>
            Controls the conversion of internal html links to internal pdf links.
            </summary>
            <remarks>
            The default value for this property is true and in this case all internal hyperlinks from the web page (links that point within the web page) will be rendered in the 
            generated pdf document as internal pdf links (clicking one of them will jump within the pdf document).
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.Authentication">
            <summary>
            Handles authentication options if the web page being converted requires authentication.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.RenderPageOnTimeout">
            <summary>
            A flag indicating if the page is rendered even if a navigation timeout occurs. The default value is False and a navigation timeout exception is raised.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.ScaleImages">
            <summary>
            A flag indicating if the images from the page are scaled during the conversion process. The default value is False and images are not scaled.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.DenyLocalFileAccess">
            <summary>
            A flag indicating if local files can be loaded during the conversion. The default value is False and local files can be loaded.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.DisplayCutText">
            <summary>
            Gets or sets a flag indicating if the text that is out of the calculated rendering rectangle is displayed or not.
            </summary>
            <remarks>The default value for this property is False.</remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.HiddenWebElements">
            <summary>
            Gets a reference to the object that controls the visibility of some web elements in the generated pdf document.
            </summary>
            <remarks>
            This property needs to be set to instruct the html to pdf converter to hide some elements from the web page and not render them in the pdf document. 
            The converter will not render the web elements specified by the <see cref="P:SelectPdf.HiddenWebElements.CssSelectors"/> of the <see cref="P:SelectPdf.PdfHtmlSection.HiddenWebElements"/> property.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.RenderingEngine">
            <summary>
            Gets or sets the rendering engine used by the converter to load and render the HTML. The possible values are WebKit, WebKitRestricted and Blink. 
            The Webkit rendering engine is internal and renders similar to Apple's Safari. For Blink, Chromium binaries must be also installed.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.LFN">
            <summary>
            Internal use only.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.ConsoleLog">
            <summary>
            Returns the console log of the browser used to render the web page.
            </summary>
            <remarks>
            This information can be used to debug javascript errors.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfHtmlSection.AllowContentHeightResize">
            <summary>
            A flag used only by WebKitRestricted or Blink engine indicating if the content height can be recalculated to better fit the page. This can introduce certain errors in some cases.
            The default is True. Set it to False if content is split to more pages than expected.
            </summary>
            <remarks>Note: This is used only by the WebKitRestricted rendering engine.</remarks>
        </member>
        <member name="T:SelectPdf.PdfImageSection">
            <summary>
            Represents an image that can be added to the header or footer templates of a pdf document.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfImageSection.#ctor(System.Single,System.Single,System.Single,System.String)">
            <summary>
            Creates an image object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The image object is created from the specified file that will be rendered at the position (x,y) in the pdf header or footer
            with the specified destination width and the height auto determined to keep the initial aspect ratio.
            </remarks>
            <param name="x">The X position where this element will be rendered.</param>
            <param name="y">The Y position where this element will be rendered.</param>
            <param name="width">The destination rectangle width.</param>
            <param name="fileName">The image file path.</param>
        </member>
        <member name="M:SelectPdf.PdfImageSection.#ctor(System.Single,System.Single,System.Single,System.Single,System.String)">
            <summary>
            Creates an image object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The image object is created from the specified file that will be rendered at the position (x,y) in the pdf header or footer
            with the specified destination width and height size.
            </remarks>
            <param name="x">The X position where this element will be rendered.</param>
            <param name="y">The Y position where this element will be rendered.</param>
            <param name="width">The destination rectangle width.</param>
            <param name="height">The destination rectangle height.</param>
            <param name="fileName">The image file path.</param>
        </member>
        <member name="M:SelectPdf.PdfImageSection.#ctor(System.Single,System.Single,System.Single,System.Drawing.Image)">
            <summary>
            Creates an image object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The image object is created from the specified <see cref="T:System.Drawing.Image"/> object that will be rendered at the position (x,y) in the pdf header or footer
            with the specified destination width and the height auto determined to keep the aspect ratio.
            </remarks>
            <param name="x">The X position where this element will be rendered.</param>
            <param name="y">The Y position where this element will be rendered.</param>
            <param name="width">The destination rectangle width.</param>
            <param name="imageObj">The system image object.</param>
        </member>
        <member name="M:SelectPdf.PdfImageSection.#ctor(System.Single,System.Single,System.Single,System.Single,System.Drawing.Image)">
            <summary>
            Creates an image object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The image object is created from the specified <see cref="T:System.Drawing.Image"/> object that will be rendered at the position (x,y) in the pdf header or footer
            with the specified destination width and height size.
            </remarks>
            <param name="x">The X position where this element will be rendered.</param>
            <param name="y">The Y position where this element will be rendered.</param>
            <param name="width">The destination rectangle width.</param>
            <param name="height">The destination rectangle height.</param>
            <param name="imageObj">The system image object.</param>
        </member>
        <member name="P:SelectPdf.PdfImageSection.X">
            <summary>
            Gets or sets the image position on the X axis.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfImageSection.Y">
            <summary>
            Gets or sets the image position on the Y axis.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfImageSection.Width">
            <summary>
            Gets or sets the image width in the destination pdf page.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfImageSection.Height">
            <summary>
            Gets or sets the image height in the destination pdf page.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfImageSection.Image">
            <summary>
            Gets the underlying system image object.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfImageSection.Transparency">
            <summary>
            Gets or sets the pdf element transparency. 
            </summary>
            <remarks>The transparency is expressed as a value between 0 and 100. 
            <para></para>
            The value 0 means completely transparent and 100 means completely opaque.
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfMargins">
            <summary>
            Represents the settings for a pdf page margins.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfMargins.#ctor">
            <summary>
            Initializes all margins with 0
            </summary>
        </member>
        <member name="M:SelectPdf.PdfMargins.#ctor(System.Single)">
            <summary>
            Initializes all margins with the same value
            </summary>
            <param name="all">The value for all document margins.</param>
        </member>
        <member name="M:SelectPdf.PdfMargins.#ctor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Default constructor initializes with 0 all the margins
            </summary>
            <param name="left">The left margin</param>
            <param name="right">The right margin</param>
            <param name="top">The top margin</param>
            <param name="bottom">The bottom margin</param>
        </member>
        <member name="P:SelectPdf.PdfMargins.Empty">
            <summary>
            Empty margins object
            </summary>
        </member>
        <member name="P:SelectPdf.PdfMargins.Left">
            <summary>
            PDF page left margin.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfMargins.Right">
            <summary>
            PDF page right margin.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfMargins.Bottom">
            <summary>
            PDF page bottom margin.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfMargins.Top">
            <summary>
            PDF page top margin.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfPage">
            <summary>
            Represents a page in a pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfPage.Orientation">
            <summary>
            Pdf page orientation. 
            </summary>
            <remarks>The default page orientation is <see cref="F:SelectPdf.PdfPageOrientation.Portrait"/>.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfPage.Rotation">
            <summary>
            Pdf page rotation.
            </summary>
            <remarks>
            The pdf page can be rotated with 90, 180 or 270 degrees from the original position.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfPage.CustomHeader">
            <summary>
            Represents the custom header template for the pdf page. 
            </summary>
            <remarks>It will replace the default pdf document header template specified by <see cref="P:SelectPdf.PdfDocument.Header"/>.</remarks>
        </member>
        <member name="P:SelectPdf.PdfPage.CustomFooter">
            <summary>
            Represents the custom footer template for the pdf page. 
            </summary>
            <remarks>It will replace the default pdf document footer template specified by <see cref="P:SelectPdf.PdfDocument.Footer"/>.</remarks>
        </member>
        <member name="P:SelectPdf.PdfPage.DisplayHeader">
            <summary>
            Controls if a custom header is displayed in the current pdf page.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfPage.DisplayFooter">
            <summary>
            Controls if a custom footer is displayed in the current pdf page.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfPage.Margins">
            <summary>
            Gets the pdf page margins.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfPage.PageSize">
            <summary>
            Gets the pdf page size in points.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfPage.Add(SelectPdf.PdfPageElement)">
            <summary>
            Adds the specified pdf element to the current page.
            </summary>
            <param name="element">The element to be added to the current pdf page.</param>
            <returns>The result of adding the element to the page.</returns>
        </member>
        <member name="P:SelectPdf.PdfPage.Document">
            <summary>
            The parent pdf document of the current page.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfPage.PageIndex">
            <summary>
            The index of the current page in the pdf pages collection.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfPageCollection">
            <summary>
            Represents the collection of pages in a pdf document.
            </summary>
            <remarks>
            <see cref="T:SelectPdf.PdfPageCollection"/> is a collection of <see cref="T:SelectPdf.PdfPage"/> objects.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfPageCollection.document">
            <summary>
            The parent PDF Document.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.#ctor(SelectPdf.PdfDocument)">
            <summary>
            Creates a collection of pages for this document.
            </summary>
            <param name="document">The PDF document for which this collection is created.</param>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.GetEnumerator">
            <summary>
            Gets the collection enumerator.
            </summary>
            <returns>The collection enumerator.</returns>
        </member>
        <member name="P:SelectPdf.PdfPageCollection.Document">
            <summary>
            The parent pdf document of the pages collection.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfPageCollection.Item(System.Int32)">
            <summary>
            Gets or sets the page at the specified zero based index in the pages collection.
            </summary>
            <param name="index">The zero based page index in the pdf pages collection.</param>
            <returns>The pdf page at the specified index.</returns>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.Add">
            <summary>
            Creates a new pdf page and adds it to the pdf document pages collection. 
            </summary>
            <remarks>
            If there is a previous page in the pages collection, the orientation and size are inherited from that page, otherwise a first page with
            the default <see cref="F:SelectPdf.PdfPageSize.A4"/> size and <see cref="F:SelectPdf.PdfPageOrientation.Portrait"/> orientation is created. 
            <para></para>
            The page margins are inherited from the default pdf document margins specified in <see cref="P:SelectPdf.PdfDocument.Margins"/> property.</remarks>
            <returns>The newly created pdf page.</returns>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.Add(SelectPdf.PdfMargins)">
            <summary>
            Creates a new pdf page with the specified margins and adds it to the pdf document pages collection. 
            </summary>
            <remarks>
            If there is a previous page in the pages collection, the orientation and size are inherited from that page, otherwise a first page with
            the default <see cref="F:SelectPdf.PdfPageSize.A4"/> size and <see cref="F:SelectPdf.PdfPageOrientation.Portrait"/> orientation is created.</remarks>
            <param name="pageMargins">The pdf page margins.</param>
            <returns>The newly created pdf page.</returns>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.Add(SelectPdf.PdfCustomPageSize,SelectPdf.PdfMargins)">
            <summary>
            Creates a new pdf page with the specified size and margins and adds it to the pdf document pages collection. 
            </summary>
            <remarks>
            If there is a previous page in the pages collection, the orientation is inherited from that page, otherwise a first page with
            the default <see cref="F:SelectPdf.PdfPageOrientation.Portrait"/> orientation is created.</remarks>
            <param name="pageSize">The pdf page size.</param>
            <param name="pageMargins">The pdf page margins.</param>
            <returns>The newly created pdf page.</returns>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.Add(SelectPdf.PdfCustomPageSize,SelectPdf.PdfMargins,SelectPdf.PdfPageOrientation)">
            <summary>
            Creates a new pdf page with the specified size, margins and orientation and adds it to the pdf document pages collection. 
            </summary>
            <param name="pageSize">The pdf page size.</param>
            <param name="pageMargins">The pdf page margins.</param>
            <param name="pageOrientation">The pdf page orientation.</param>
            <returns>The newly created pdf page.</returns>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.Insert(System.Int32,SelectPdf.PdfCustomPageSize,SelectPdf.PdfMargins,SelectPdf.PdfPageOrientation)">
            <summary>
            Creates a new pdf page with the specified size, margins and orientation and inserts it into the pdf document pages collection
            at the specified index.
            </summary>
            <param name="index">The index where the new pdf page will be inserted.</param>
            <param name="pageSize">The pdf page size.</param>
            <param name="pageMargins">The pdf page margins.</param>
            <param name="pageOrientation">The pdf page orientation.</param>
            <returns>The newly created pdf page.</returns>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.Add(SelectPdf.PdfPage)">
            <summary>
            Adds the specified page to the pdf document pages collection. 
            </summary>
            <remarks>
            The specified page can belong to another pdf document. 
            <para></para>
            <note type="important">The source pdf document must remain opened until the current document is saved.</note>
            </remarks>
            <param name="pdfPage">The page to be added to the collection.</param>
            <returns>The index of the page in collection. If the page already exists, this method returns the index of the existing page.</returns>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.Add(SelectPdf.PdfPage[])">
            <summary>
            Adds the specified array of pages to the pdf document pages collection. 
            </summary>
            <remarks>
            The specified pages can belong to another pdf document. 
            <para></para>
            <note type="important">The source pdf document must remain opened until the current document is saved.</note>
            </remarks>
            <param name="pages">The pdf pages to be added to the collection.</param>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.Insert(System.Int32,SelectPdf.PdfPage)">
            <summary>
            Inserts the specified page into the pdf document pages collection at the specified index. 
            </summary>
            <remarks>
            The specified page can belong to another pdf document. 
            <para></para>
            <note>The source pdf document must remain opened until the current document is saved.</note>
            </remarks>
            <param name="pageIndex">The index where the pdf page will be inserted.</param>
            <param name="pdfPage">The pdf page to be inserted into the collection.</param>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.Contains(SelectPdf.PdfPage)">
            <summary>
            Indicates if the collection contains the specified page.
            </summary>
            <param name="pdfPage">The specified pdf page.</param>
            <returns>True if the collection contains the specified page.</returns>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.IndexOf(SelectPdf.PdfPage)">
            <summary>
            Returns the index of specified page in collection.
            </summary>
            <param name="pdfPage">The specified pdf page.</param>
            <returns>The index of the specified page in collection.</returns>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.Remove(SelectPdf.PdfPage)">
            <summary>
            Removes the specified page from the pdf document pages collection.
            </summary>
            <param name="pdfPage">The pdf page to be removed.</param>
        </member>
        <member name="M:SelectPdf.PdfPageCollection.RemoveAt(System.Int32)">
            <summary>
            Removes the pdf page at the specified index in pdf document pages collection.
            </summary>
            <param name="index">The zero based index of the pdf page to be removed.</param>
        </member>
        <member name="P:SelectPdf.PdfPageCollection.Count">
            <summary>
            The number of pages in the pdf pages collection.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfPageElement">
            <summary>
            The base class for all graphic elements from a pdf document.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfPageElement.Render(SelectPdf.PdfCanvas)">
            <summary>
            Renders the current pdf element on the specified canvas. 
            </summary>
            <remarks>
            The canvas can be a <see cref="T:SelectPdf.PdfPage"/> or a <see cref="T:SelectPdf.PdfTemplate"/>.
            </remarks>
            <param name="canvas">The canvas where the current pdf element will be rendered. Can be a pdf page or template.</param>
            <returns>The result of adding the current pdf element to the specified canvas.</returns>
        </member>
        <member name="T:SelectPdf.PdfPageOrientation">
            <summary>
            The list of pdf pages available orientations.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageOrientation.Portrait">
            <summary>
            Portrait
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageOrientation.Landscape">
            <summary>
            Landscape
            </summary>
        </member>
        <member name="T:SelectPdf.PdfPageSize">
            <summary>
            The list of pdf standard page sizes.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.Custom">
            <summary>
            Use specified page size
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.Letter">
            <summary>
            Letter format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.Note">
            <summary>
            Note format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.Legal">
            <summary>
            Legal format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.A0">
            <summary>
            A0 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.A1">
            <summary>
            A1 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.A2">
            <summary>
            A2 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.A3">
            <summary>
            A3 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.A4">
            <summary>
            A4 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.A5">
            <summary>
            A5 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.A6">
            <summary>
            A6 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.A7">
            <summary>
            A7 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.A8">
            <summary>
            A8 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.A9">
            <summary>
            A9 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.A10">
            <summary>
            A0 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.B0">
            <summary>
            B0 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.B1">
            <summary>
            B1 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.B2">
            <summary>
            B2 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.B3">
            <summary>
            B3 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.B4">
            <summary>
            B4 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.B5">
            <summary>
            B5 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.ArchE">
            <summary>
            ArchE format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.ArchD">
            <summary>
            ArchD format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.ArchC">
            <summary>
            ArchC format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.ArchB">
            <summary>
            ArchB format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.ArchA">
            <summary>
            ArchA format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.Flsa">
            <summary>
            Flsa format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.HalfLetter">
            <summary>
            HalfLetter format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.Letter11x17">
            <summary>
            11x17 format
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageSize.Ledger">
            <summary>
            Ledger format
            </summary>
        </member>
        <member name="T:SelectPdf.PdfRenderingResult">
            <summary>
            The result of adding a pdf element to a pdf page or template.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfRenderingResult.pageIndex">
            <summary>
            The end page index
            </summary>
        </member>
        <member name="F:SelectPdf.PdfRenderingResult.pageBounds">
            <summary>
            The bounds inside the end page
            </summary>
        </member>
        <member name="M:SelectPdf.PdfRenderingResult.#ctor">
            <summary>
            Construct a PdfRenderingResult object with no page information
            </summary>
        </member>
        <member name="M:SelectPdf.PdfRenderingResult.#ctor(System.Int32,System.Drawing.RectangleF)">
            <summary>
            Constructs PdfRenderingResult object based on the page index and the bounds
            inside the page where the element rendering ended.
            </summary>
            <param name="endPageIndex"></param>
            <param name="endPageBounds"></param>
        </member>
        <member name="P:SelectPdf.PdfRenderingResult.PdfPageLastIndex">
            <summary>
            The index of the pdf page where the rendering of the page element ended.
            </summary>
            <remarks>
            This information is useful when other pdf elements are added to the <see cref="T:SelectPdf.PdfDocument"/> object.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfRenderingResult.PdfPageLastRectangle">
            <summary>
            The bounds inside the pdf page where the rendering of the page element ended.
            </summary>
            <remarks>
            This information is useful when other pdf elements are added to the <see cref="T:SelectPdf.PdfDocument"/> object.
            <para>
            Note: The last page is specified by the <see cref="P:SelectPdf.PdfRenderingResult.PdfPageLastIndex"/> property.
            </para>
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfPageRotation">
            <summary>
            Standard rotation angles for pdf pages.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageRotation.Rotate_0">
            <summary>
            0 degrees rotation.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageRotation.Rotate_90">
            <summary>
            90 degrees rotation.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageRotation.Rotate_180">
            <summary>
            180 degrees rotation.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfPageRotation.Rotate_270">
            <summary>
            270 degrees rotation.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfSectionElement">
            <summary>
            The base class for pdf elements that can be added to header and footer of a pdf document.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfSectionElement.Render(System.Object,System.Object)">
            <summary>
            Render this page element using the specified canvas.
            </summary>
            <param name="canvas">The canvas where the current pdf element is rendered. 
            A canvas can be a pdf page or a template (like header, footer, etc).
            </param>
            <param name="param">An optional parameter.</param>
        </member>
        <member name="T:SelectPdf.PdfSecurityOptions">
            <summary>
            This class provides the options to control the pdf document security. 
            </summary>
            <remarks>
            The <see cref="T:SelectPdf.HtmlToPdfOptions"/> class defines a reference to an object of this type.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfSecurityOptions.CanAssembleDocument">
            <summary>
            Allows inserting, rotating or deleting pages or bookmarks. 
            </summary>
        </member>
        <member name="P:SelectPdf.PdfSecurityOptions.CanCopyContent">
            <summary>
            Allows copying page contents such as text and images.  
            </summary>
        </member>
        <member name="P:SelectPdf.PdfSecurityOptions.CanAccessibilityCopyContent">
            <summary>
            Allows copying page accessibility contents such as text and images.  
            </summary>
        </member>
        <member name="P:SelectPdf.PdfSecurityOptions.CanEditAnnotations">
            <summary>
            Allows document annotations editing.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfSecurityOptions.CanEditContent">
            <summary>
            Allows modifying document content. 
            </summary>
        </member>
        <member name="P:SelectPdf.PdfSecurityOptions.CanFillFormFields">
            <summary>
            Allows filling in form fields. 
            </summary>
        </member>
        <member name="P:SelectPdf.PdfSecurityOptions.CanPrint">
            <summary>
            Allows printing.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfSecurityOptions.OwnerPassword">
            <summary>Sets or gets the owner password.</summary>
            <remarks>
            This password is required to change permissions of the pdf document (like printing or editing).
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfSecurityOptions.UserPassword">
            <summary>Gets or sets the user password. </summary>
            <remarks>
            This password is required to open the pdf document. 
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfStandardCJKFont">
            <summary>
            The Korean-Japanesse-Chinese (CJK) predefined fonts.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardCJKFont.HeiseiKakuGothicW5">
            <summary>
            Heisei Kaku Gothic W5 Japanese font.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardCJKFont.HeiseiMinchoW3">
            <summary>
            Heisei Mincho W3 Japanese font.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardCJKFont.HanyangSystemsGothicMedium">
            <summary>
            Hanyang Systems Gothic Medium Korean font.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardCJKFont.HanyangSystemsShinMyeongJoMedium">
            <summary>
            Hanyang Systems Shin MyeongJo Medium Korean font.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardCJKFont.MonotypeHeiMedium">
            <summary>
            Monotype Hei Medium Chinese traditional font.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardCJKFont.MonotypeSungLight">
            <summary>
            Monotype Sung Light Chinese traditional font.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardCJKFont.SinoTypeSongLight">
            <summary>
            SinoType Song Light Chinese simplified font.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfStandardFont">
            <summary>
            Standard PDF font families.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.Custom">
            <summary>
            Custom font.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.Helvetica">
            <summary>
            Helvetica
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.HelveticaBold">
            <summary>
            Helvetica Bold
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.HelveticaOblique">
            <summary>
            Helvetica Oblique
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.HelveticaBoldOblique">
            <summary>
            Helvetica Bold Oblique
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.Courier">
            <summary>
            Courier
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.CourierBold">
            <summary>
            Courier Bold
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.CourierOblique">
            <summary>
            Courier Oblique
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.CourierBoldOblique">
            <summary>
            Courier Bold Oblique
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.TimesRoman">
            <summary>
            Times Roman
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.TimesBold">
            <summary>
            Times Bold
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.TimesBoldItalic">
            <summary>
            Times Bold Italic
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.TimesItalic">
            <summary>
            Times Italic
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.Symbol">
            <summary>
            Symbol
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandardFont.ZapfDingbats">
            <summary>
            ZapfDingbats
            </summary>
        </member>
        <member name="T:SelectPdf.PdfStandard">
            <summary>
            The list of pdf standards available.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandard.Full">
            <summary>
            The full pdf features can be used in the generated pdf document.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfStandard.PdfA">
            <summary>
            The generated pdf is in conformance with PDF/A standard which makes the document
            suitable for long term archiving. 
            </summary>
            <remarks>
            All the true type fonts are embedded in the pdf document,
            transparencies are disabled, and some special metadata to identify the document as PDF/A compliant 
            is inserted in the generated pdf document.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfStandard.PdfX">
            <summary>
            The generated PDF is in conformance with PDF/X standard which makes the document 
            suitable for graphics exchange. 
            </summary>
            <remarks>
            In PDF/X all the true type fonts are embedded 
            in the generated pdf document.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfStandard.PdfSiqQLevelA">
            <summary>
            The generated PDF is in conformance with PDF/SiqQ Level A standard which makes the document 
            suitable for being digitally signed. 
            </summary>
            <remarks>
            In PDF/SiqQ Level A all the true type fonts are embedded in the pdf document and the 
            external HTTP links are disabled.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfStandard.PdfSiqQLevelB">
            <summary>
            The generated PDF is in conformance with PDF/SiqQ Level B standard which makes the document 
            suitable for being digitally signed. 
            </summary>
            <remarks>
            In PDF/SiqQ Level A all the true type fonts are embedded 
            in the generated pdf document.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfStandard.PdfA2B">
            <summary>
            The generated pdf is in conformance with PDF/A-2B standard which makes the document
            suitable for long term archiving. 
            </summary>
            <remarks>
            All the true type fonts are embedded in the pdf document,
            transparencies are enabled, and some special metadata to identify the document as PDF/A-2B compliant 
            is inserted in the generated pdf document.
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfTemplate">
            <summary>
            Represents a template in a pdf document. A template contains elements that are repeated on each page of the pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTemplate.Width">
            <summary>
            Gets or sets the width for a custom template.
            </summary>
            <remarks>
            This property has no effect for a default document template.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfTemplate.Height">
            <summary>
            Gets or sets the height for a custom template.
            </summary>
            <remarks>
            This property has no effect for a default document template.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfTemplate.Bounds">
            <summary>
            Gets or sets the template bounds rectangle. 
            </summary>
            <remarks>
            This property has no effect for a default document template.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfTemplate.Docking">
            <summary>
            Gets or sets the template docking style inside the pdf page.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTemplate.Anchoring">
            <summary>
            Gets or sets the template anchoring settings inside the pdf page.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTemplate.DisplayOnOddPages">
            <summary>
            Controls the visibility of the template on the odd numbered pages of the pdf document.
            </summary>
            <remarks>
            By default the template is displayed on both odd and even numbered pages.
            The <see cref="P:SelectPdf.PdfTemplate.DisplayOnFirstPage"/> property has priority over <see cref="P:SelectPdf.PdfTemplate.DisplayOnOddPages"/>.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfTemplate.DisplayOnEvenPages">
            <summary>
            Controls the visibility of the template on the even numbered pages of the pdf document.
            </summary>
            <remarks>
            By default the template is displayed on both odd and even numbered pages.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfTemplate.DisplayOnFirstPage">
            <summary>
            Controls the visibility of the template on the first page of the generated pdf document.
            </summary>
            <remarks>
            When this property is true the template will be displayed on the first page of the pdf document, otherwise it will not be visible. 
            This property has priority over <see cref="P:SelectPdf.PdfTemplate.DisplayOnOddPages"/>. The default value is true.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfTemplate.FirstPageNumber">
            <summary>
            Controls the page number for the first page being rendered.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTemplate.TotalPagesOffset">
            <summary>
            Controls the total number of pages offset in the pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTemplate.Foreground">
            <summary>
            Controls whether the template is rendered in front of the page main content or not.
            </summary>
            <remarks>
            The default value is true. Elements from the template will be displayed in front of the other elements from the page.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfTemplate.Background">
            <summary>
            Controls whether the template is rendered behind the page main content or not.
            </summary>
            <remarks>
            The default value is false. Elements from the template will not be displayed behind the other elements from the page.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfTemplate.LFN">
            <summary>
            Internal use only.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfTemplate.Add(SelectPdf.PdfPageElement)">
            <summary>
            Adds a pdf element to the template.
            </summary>
            <param name="element">The page element to be added.</param>
            <returns>The result of element rendering.</returns>
        </member>
        <member name="T:SelectPdf.PdfTemplateAnchoring">
            <summary>
            The anchoring position of a pdf template.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTemplateAnchoring.NotAnchored">
            <summary>
            The template is not anchored.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTemplateAnchoring.Center">
            <summary>
            The template will be anchored in the center of the pdf page.
            </summary>
            <remarks>
            The center of the template rectangle will be same with the center of the page. 
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfTemplateAnchoring.TopLeft">
            <summary>
            The template will be anchored in the top left corner of the pdf page.
            </summary>
            <remarks>
            The template location will be the top left corner of the page.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfTemplateAnchoring.TopRight">
            <summary>
            The template will be anchored in the top right corner of the pdf page.
            </summary>
            <remarks>
            The right side of the template will be aligned with the right side of the page,
            and the top side of the template will be aligned with the top side of the page
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfTemplateAnchoring.BottomLeft">
            <summary>
            The template will be anchored in the bottom left corner of the pdf page.
            </summary>
            <remarks>
            The left side of the template will be aligned with the left side of the 
            page and the bottom side of the document will be aligned with the bottom
            side of the page.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfTemplateAnchoring.BottomRight">
            <summary>
            The template will be anchored in the bottom left corner of the pdf page.
            </summary>
            <remarks>
            The right side of the document will be aligned with the right side of the 
            pdf page and the bottom side of the document will be aligned with the bottom 
            side of the PDF page
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfTemplateCollection">
            <summary>
            Represents the collection of pages in a pdf document. A pdf template is repeated on each page of the pdf document.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfTemplateCollection.GetEnumerator">
            <summary>
            Gets the collection enumerator.
            </summary>
            <returns>The collection enumerator.</returns>
        </member>
        <member name="P:SelectPdf.PdfTemplateCollection.Item(System.Int32)">
            <summary>
            Gets or sets the template at the specified zero based index in the templates collection.
            </summary>
            <param name="index">The zero based template index in the pdf templates collection.</param>
            <returns>The pdf template at the specified index.</returns>
        </member>
        <member name="P:SelectPdf.PdfTemplateCollection.Count">
            <summary>
            The number of templates in pdf templates collection.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTemplateCollection.Header">
            <summary>
            Represents the default pdf document header template. 
            </summary>
            <remarks>
            The header space is automatically taken into account when calculating the available space for rendering in a pdf page. 
            <para></para>
            This template is automatically docked to the top of the pdf page.</remarks>
        </member>
        <member name="P:SelectPdf.PdfTemplateCollection.Footer">
            <summary>
            Represents the default pdf document footer template.
            </summary>
            <remarks>
            The footer space is automatically taken into account when calculating the available space for rendering in a pdf page. 
            <para></para>
            This template is automatically docked to the bottom of the pdf page.</remarks>
        </member>
        <member name="M:SelectPdf.PdfTemplateCollection.Add(System.Single,System.Single)">
            <summary>
            Adds a new template with the specified width and height to the pdf document templates collection.
            </summary>
            <remarks>
            The template location is top left corner of the pdf page. Use <see cref="M:SelectPdf.PdfTemplateCollection.Add(System.Drawing.RectangleF)"/> to specify another location for the template.
            </remarks>
            <param name="width">The pdf template width.</param>
            <param name="height">The pdf template height.</param>
            <returns>The newly added pdf template.</returns>
        </member>
        <member name="M:SelectPdf.PdfTemplateCollection.Add(System.Drawing.RectangleF)">
            <summary>
            Adds a new template with the specified bounds to the pdf document templates collection.
            </summary>
            <param name="bounds">The template element bounds.</param>
            <returns>The newly added pdf template.</returns>
        </member>
        <member name="T:SelectPdf.PdfTemplateDocking">
            <summary>
            The docking style of a pdf template.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTemplateDocking.NotDocked">
            <summary>
            The template is not docked in the pdf page.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTemplateDocking.Top">
            <summary>
            The template is docked to the top of the pdf page. 
            </summary>
            <remarks>
            The template width and location will be adjusted for docking.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfTemplateDocking.Bottom">
            <summary>
            The template is docked to the bottom of the pdf page. 
            </summary>
            <remarks>
            The template width and location will be adjusted for docking.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfTemplateDocking.Left">
            <summary>
            The template is docked to the left of the pdf page.
            </summary>
            <remarks>
            The template location and height will be adjusted for docking.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfTemplateDocking.Right">
            <summary>
            The template is docked to the right of the pdf page.
            </summary>
            <remarks>
            The template location and height will be adjusted for docking.
            </remarks>
        </member>
        <member name="F:SelectPdf.PdfTemplateDocking.Fill">
            <summary>
            The template fills the pdf page. 
            </summary>
            <remarks>
            The template location will be the top left corner and the size will be the size of the pdf page. The docking
            will take into account the existing document docked templates (like header, footer).
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfTextHorizontalAlign">
            <summary>
            Specifies horizontal alignment of text.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextHorizontalAlign.Default">
            <summary>
            Default alignment.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextHorizontalAlign.Left">
            <summary>
            Align left.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextHorizontalAlign.Center">
            <summary>
            Center text.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextHorizontalAlign.Right">
            <summary>
            Align right.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextHorizontalAlign.Justify">
            <summary>
            Justify text.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfTextSection">
            <summary>
            Represents a text that can be added to the header or footer templates of a pdf document.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.text">
            <summary>
            The string to be written
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.x">
            <summary>
            The start x position
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.y">
            <summary>
            The start y position
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.width">
            <summary>
            The width of destination rectangle
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.height">
            <summary>
            The height of the destination rectangle 
            Is optional
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.font">
            <summary>
            The text font
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.multiPageText">
            <summary>
            When true a paginable text is rendered
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.paginate">
            <summary>
            When true the text can be paginated
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.textAlign">
            <summary>
            Horizontal text alignment.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.verticalTextAlign">
            <summary>
            Vertical text alignment.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.rightToLeft">
            <summary>
            Indicates whether RTL should be checked.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.characterSpacing">
            <summary>
            Character spacing value.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.wordSpacing">
            <summary>
            Word spacing value.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextSection.leading">
            <summary>
            Text leading.
            </summary>
        </member>
        <member name="M:SelectPdf.PdfTextSection.#ctor(System.Single,System.Single,System.String,System.Drawing.Font)">
            <summary>
            Creates a text object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The text element that will be rendered at the specified (x,y) position in the pdf header or footer using the specified font. 
            The height of the rendered text will be auto-determined. The width is the available width in page.
            </remarks>
            <param name="x">The start X coordinate where the text will be rendered.</param>
            <param name="y">The start Y coordinate where the text will be rendered.</param>
            <param name="text">The text to be rendered.</param>
            <param name="font">The text font.</param>
        </member>
        <member name="M:SelectPdf.PdfTextSection.#ctor(System.Single,System.Single,System.Single,System.String,System.Drawing.Font)">
            <summary>
            Creates a text object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The text element that will be rendered at the specified (x,y) position in the pdf header or footer using the specified font. 
            The width of the text is specified as parameter. The height of the rendered text will be auto-determined. 
            </remarks>
            <param name="x">The start X coordinate where the text will be rendered.</param>
            <param name="y">The start Y coordinate where the text will be rendered.</param>
            <param name="width">The width of the destination rectangle.</param>
            <param name="text">The text to be rendered.</param>
            <param name="font">The text font.</param>
        </member>
        <member name="M:SelectPdf.PdfTextSection.#ctor(System.Single,System.Single,System.Single,System.Single,System.String,System.Drawing.Font)">
            <summary>
            Creates a text object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The text element that will be rendered at the specified (x,y) position in the pdf header or footer using the specified font. 
            The width and height of the text on the destination page are also specified.
            </remarks>
            <param name="x">The start X coordinate where the text will be rendered.</param>
            <param name="y">The start Y coordinate where the text will be rendered.</param>
            <param name="width">The width of the destination rectangle.</param>
            <param name="height">The height of the destination rectangle.</param>
            <param name="text">The text to be rendered.</param>
            <param name="font">The text font.</param>
        </member>
        <member name="M:SelectPdf.PdfTextSection.#ctor(System.Single,System.Single,System.Single,System.String,System.Drawing.Font,System.Drawing.Color)">
            <summary>
            Creates a text object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The text element that will be rendered at the specified (x,y) position in the pdf header or footer using the specified font and color. 
            The width of the text is specified as parameter. The height of the rendered text will be auto-determined. 
            </remarks>
            <param name="x">The start X coordinate where the text will be rendered.</param>
            <param name="y">The start Y coordinate where the text will be rendered.</param>
            <param name="width">The width of the destination rectangle.</param>
            <param name="text">The text to be rendered.</param>
            <param name="font">The text font.</param>
            <param name="color">The text color.</param>
        </member>
        <member name="M:SelectPdf.PdfTextSection.#ctor(System.Single,System.Single,System.Single,System.Single,System.String,System.Drawing.Font,System.Drawing.Color)">
            <summary>
            Creates a text object that can be added to the header or footer templates of a pdf document.
            </summary>
            <remarks>
            The text element that will be rendered at the specified (x,y) position in the pdf header or footer using the specified font and color. 
            The width and height of the text on the destination page are also specified.
            </remarks>
            <param name="x">The start X coordinate where the text will be rendered.</param>
            <param name="y">The start Y coordinate where the text will be rendered.</param>
            <param name="width">The width of the destination rectangle.</param>
            <param name="height">The height of the destination rectangle.</param>
            <param name="text">The text to be rendered.</param>
            <param name="font">The text font.</param>
            <param name="color">The text color.</param>
        </member>
        <member name="P:SelectPdf.PdfTextSection.HorizontalAlign">
            <summary>
            Gets or sets the horizontal text alignment.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.VerticalAlign">
            <summary>
            Gets or sets the vertical text alignment.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.RightToLeft">
            <summary>
            Indicates if the text is in a right-to-left language.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.CharacterSpacing">
            <summary>
            Gets or sets an additional spacing between characters.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.WordSpacing">
            <summary>
            Gets or sets an additional space between words. 
            </summary>
            <remarks>The words separator is consider the space character with code 32.</remarks>
        </member>
        <member name="P:SelectPdf.PdfTextSection.Leading">
            <summary>
            Gets or sets a value that indicates the vertical distance between the baselines of adjacent lines of text.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.Direction">
            <summary>
            Gets or sets an angle to control the text direction. 
            </summary>
            <remarks>
            The default value is 0. Adding a value in degrees will rotate the text counter-clockwise.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfTextSection.TextRise">
            <summary>
            Gets or sets the distance to move the text baseline up or down from its default location.
            </summary>
            <remarks>
            Adjustments to the baseline are useful for drawing superscripts or subscripts.
            <para></para>
            Positive values of text rise move the baseline up and creates a superscript text. 
            <para></para>
            Negative values of text rise move the baseline down and creates a subscript text. 
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfTextSection.BackColor">
            <summary>
            Gets or sets the element background color.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.ForeColor">
            <summary>
            Gets or sets the element foreground color.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.Text">
            <summary>
            The text string that will be added to the pdf document header or footer.
            </summary>
            <remarks>
            If <c>{page_number}</c> placeholder appears in this property, it will be replaced with the current pdf page number.
            By default the page numbering starts with 1. This can be changed using the <see cref="P:SelectPdf.PdfFooter.FirstPageNumber"/> property.
            <para></para>
            <para></para>
            If <c>{total_pages}</c> placeholder appears in this property, it will be replaced with the total number of pages in the generated pdf document.
            The total number of pages can be incremented with a value specified by the <see cref="P:SelectPdf.PdfFooter.TotalPagesOffset"/> property. 
            This could be useful when the generated pdf will be merged with other documents.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfTextSection.X">
            <summary>
            Gets or sets the text start position on the X axis.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.Y">
            <summary>
            Gets or sets the text start position on the Y axis.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.Width">
            <summary>
            The width of destination rectangle where the text will be rendered.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.Height">
            <summary>
            The height of the destination rectangle where the text will be rendered.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.Font">
            <summary>
            The text font.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.EmbedFont">
            <summary>
            A flag indicating if the text font will be embedded in the pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.PdfTextSection.Transparency">
            <summary>
            Gets or sets the pdf element transparency. 
            </summary>
            <remarks>The transparency is expressed as a value between 0 and 100. 
            <para></para>
            The value 0 means completely transparent and 100 means completely opaque.
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfTextVerticalAlign">
            <summary>
            Specifies vertical alignment of text.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextVerticalAlign.Top">
            <summary>
            Align top.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextVerticalAlign.Middle">
            <summary>
            Align center.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfTextVerticalAlign.Bottom">
            <summary>
            Align bottom.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfViewerFullScreenExitMode">
            <summary>
            Specifies how to display the pdf document on exiting full-screen mode.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerFullScreenExitMode.UseNone">
            <summary>
            Neither document outline (bookmarks) nor thumbnail images are visible.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerFullScreenExitMode.UseOutlines">
            <summary>
            Document outline (bookmarks) are visible.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerFullScreenExitMode.UseThumbs">
            <summary>
            Thumbnail images are visible.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerFullScreenExitMode.UseOC">
            <summary>
            Optional content group panel is visible.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfViewerPageLayout">
            <summary>
            Specifies the page layout to be used when the pdf document is opened in a viewer. 
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerPageLayout.SinglePage">
            <summary>
            Displays one page at a time.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerPageLayout.OneColumn">
            <summary>
            Displays the pages in one column.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerPageLayout.TwoColumnLeft">
            <summary>
            Displays the pages in two columns, with odd-numbered pages on the left.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerPageLayout.TwoColumnRight">
            <summary>
            Displays the pages in two columns, with odd-numbered pages on the right.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfViewerPageMode">
            <summary>
            Specifies the pdf document's page mode. 
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerPageMode.UseNone">
            <summary>
            Neither document outline (bookmarks) nor thumbnail images are visible. 
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerPageMode.UseOutlines">
            <summary>
            Document outline (bookmarks) are visible.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerPageMode.UseThumbs">
            <summary>
            Thumbnail images are visible.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerPageMode.FullScreen">
            <summary>
            Full-screen mode, with no menu bar, window controls or any other window visible.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerPageMode.UseOC">
            <summary>
            Optional content group panel is visible.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerPageMode.UseAttachments">
            <summary>
            Document attachments are visible.
            </summary>
        </member>
        <member name="T:SelectPdf.PdfViewerPreferences">
            <summary>
            The pdf viewer preferences. Controls how the pdf document appears in a pdf viewer (like Adobe Reader).
            </summary>
        </member>
        <member name="P:SelectPdf.PdfViewerPreferences.HideToolbar">
            <summary>
            Gets or sets a flag specifying whether to hide the pdf viewer application's tool bars when the document is active. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfViewerPreferences.HideMenuBar">
            <summary>
            Gets or sets a flag specifying whether to hide the pdf viewer application's menu bar when the document is active. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfViewerPreferences.HideWindowUI">
            <summary>
            Gets or sets a flag specifying whether to hide user interface elements in the document's window (such as scroll bars and navigation controls), leaving only the document's contents displayed. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfViewerPreferences.FitWindow">
            <summary>
            Gets or sets a flag specifying whether to resize the document's window to fit the size of the first displayed page. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfViewerPreferences.CenterWindow">
            <summary>
            Gets or sets a flag specifying whether to position the document's window in the center of the screen. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfViewerPreferences.DisplayDocTitle">
            <summary>
            Gets or sets a flag specifying whether the window's title bar should display the document title. 
            </summary>
            <remarks>
            The default value for this property is false.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfViewerPreferences.NonFullScreenPageMode">
            <summary>
            Gets or sets the document page mode when the pdf viewer application exits the full screen mode. 
            </summary>
            <remarks>
            This property is taken into account only if <see cref="P:SelectPdf.PdfViewerPreferences.PageMode"/> is set to <see cref="F:SelectPdf.PdfViewerPageMode.FullScreen"/>. Otherwise it is ignored.
            <para>
            The default value for this property is <see cref="F:SelectPdf.PdfViewerFullScreenExitMode.UseNone"/>.
            </para>
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfViewerPreferences.PageMode">
            <summary>
            Gets or sets the document page mode when the pdf document is opened in a pdf viewer. 
            </summary>
            <remarks>
            The default value for this property is <see cref="F:SelectPdf.PdfViewerPageMode.UseNone"/>.
            </remarks>
        </member>
        <member name="P:SelectPdf.PdfViewerPreferences.PageLayout">
            <summary>
            Gets or sets the page layout to be used when the document is opened. 
            </summary>
            <remarks>
            The default value for this property is <see cref="F:SelectPdf.PdfViewerPageLayout.OneColumn"/>.
            </remarks>
        </member>
        <member name="T:SelectPdf.PdfViewerTextOrder">
            <summary>
            Used in pdf document viewer preferences to specify the reading order for texts.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerTextOrder.L2R">
            <summary>
            Left to right.
            </summary>
        </member>
        <member name="F:SelectPdf.PdfViewerTextOrder.R2L">
            <summary>
            Right to left.
            </summary>
        </member>
        <member name="T:SelectPdf.PostConvertActionHandler">
            <summary>
            The delegate type for handling the PostConvertAction event of PdfConverter class.
            </summary>
            <param name="pcaEventArgs">The argument for the PostConvertAction event handler.</param>
        </member>
        <member name="T:SelectPdf.PostConvertActionEventArgs">
            <summary>
            This class encapsulates the arguments for the PostConvertAction event handler.
            </summary>
        </member>
        <member name="P:SelectPdf.PostConvertActionEventArgs.PdfDocument">
            <summary>
            The PDF document created by converter.
            </summary>
        </member>
        <member name="P:SelectPdf.PostConvertActionEventArgs.PdfConverter">
            <summary>
            The PdfConverter object that triggered the PostConvertActionEvent.
            </summary>
        </member>
        <member name="P:SelectPdf.PostConvertActionEventArgs.ConversionResult">
            <summary>
            The conversion summary including the rectangle rendered on the last page and the last page index.
            This information is useful when new elements are added after conversion in PostConvertActionEvent handler.
            </summary>
        </member>
        <member name="M:SelectPdf.PostConvertActionEventArgs.#ctor(SelectPdf.HtmlToPdf,SelectPdf.PdfDocument,SelectPdf.HtmlToPdfResult)">
            <summary>
            Constructs the argument for the PostConvertAction event handler. 
            </summary>
            <param name="pdfConverter">The PdfConverter object that triggered the PostConvertActionEven.</param>
            <param name="pdfDocument">The PDF document created by the converter.</param>
            <param name="conversionResult">The conversion summary offering information like the number of pages converted or
            the last drawn region to inform from where to continue drawing.</param>
        </member>
        <member name="T:SelectPdf.RenderingEngine">
            <summary>
            Specifies the rendering engine used by the converter to load and render the HTML.
            </summary>
        </member>
        <member name="F:SelectPdf.RenderingEngine.WebKit">
            <summary>
            The WebKit rendering engine, used in Apple's Safari (heavily customized).
            </summary>
        </member>
        <member name="F:SelectPdf.RenderingEngine.WebKitRestricted">
            <summary>
            The WebKit rendering engine in a simplified form to allow running on Azure Web Apps environment.
            </summary>
            <remarks>
            The simplified rendering engine implies some limitations and lack of features.
            </remarks>
        </member>
        <member name="T:SelectPdf.UnitsConverter">
            <summary>
            Converts between various graphic units (pixels, points, etc).
            </summary>
        </member>
        <member name="M:SelectPdf.UnitsConverter.PointsToPixels(System.Single)">
            <summary>
            Converts from points to pixels. The result of this method takes into account the 
            current screen resolution.
            </summary>
            <param name="points">The value in points to convert.</param>
            <returns>The value in pixels.</returns>
        </member>
        <member name="M:SelectPdf.UnitsConverter.PixelsToPoints(System.Single)">
            <summary>
            Converts from pixels to points. The result of this method takes into account the 
            current screen resolution.
            </summary>
            <param name="pixels">The value in pixels to convert.</param>
            <returns>The value in points.</returns>
        </member>
        <member name="T:SelectPdf.WebElement">
            <summary>
            Represents the mapping of a HTML element in the PDF document as collection of PDF 
            rectangles. A HTML element can span on many pages in the generated PDF document 
            and therefore, in general, many PDF rectangles are necessary to completely describe 
            the mapping of a HTML element in PDF.
            </summary>
        </member>
        <member name="P:SelectPdf.WebElement.HtmlElementId">
            <summary>
            The ID in HTML of the HTML element.
            </summary>
        </member>
        <member name="P:SelectPdf.WebElement.PdfRectangles">
            <summary>
            The rectangles occupied by the HTML element in the generated PDF document.
            A HTML element can span on many pages in the generated PDF document 
            and therefore, in general, many PDF rectangles are necessary to completely describe 
            the mapping of a HTML element in PDF.
            </summary>
        </member>
        <member name="P:SelectPdf.WebElement.HtmlElementText">
            <summary>
            The text of the HTML element. If the element does not have an
            associated text, this property returns null.
            </summary>
        </member>
        <member name="P:SelectPdf.WebElement.HtmlElementOuterHtml">
            <summary>
            The HTML code of the HTML element. 
            </summary>
        </member>
        <member name="P:SelectPdf.WebElement.HtmlElementTagName">
            <summary>
            The HTML tag name of the HTML element. 
            </summary>
        </member>
        <member name="P:SelectPdf.WebElement.HtmlElementCssClassName">
            <summary>
            The CSS class name of the HTML element. 
            </summary>
        </member>
        <member name="T:SelectPdf.WebElementPdfRectangle">
            <summary>
            Represents the rectangle occupied by a HTML element in a page of the generated PDF document.
            </summary>
        </member>
        <member name="P:SelectPdf.WebElementPdfRectangle.PageIndex">
            <summary>
            The zero based index of the PDF page containing this rectangle. 
            </summary>
        </member>
        <member name="P:SelectPdf.WebElementPdfRectangle.Rectangle">
            <summary>
            The rectangle position inside the PDF page drawing area. The drawing area of the PDF page 
            does not include the page margins, header or footer. The rectangle dimensions are expressed 
            in points (1 point is 1/72 inch).
            </summary>
        </member>
        <member name="T:SelectPdf.WebElementsCollection">
            <summary>
            Represents a collection of mappings of HTML elements in PDF.
            </summary>
        </member>
        <member name="M:SelectPdf.WebElementsCollection.GetEnumerator">
            <summary>
            Gets the collection enumerator
            </summary>
            <returns>The collection enumerator</returns>
        </member>
        <member name="P:SelectPdf.WebElementsCollection.Item(System.Int32)">
            <summary>
            Gets the mapping at the specified zero based index from collection.
            </summary>
            <param name="index">The zero based index of the mapping.</param>
            <returns>The mapping at the specified index in collection. Returns null 
            if the index is out of collection bounds.</returns>
        </member>
        <member name="M:SelectPdf.WebElementsCollection.GetElementByHtmlId(System.String)">
            <summary>
            Gets the mapping in PDF of the HTML element with the specified HTML ID.
            The search is case insensitive. 
            </summary>
            <param name="htmlElementId">The HTML ID of the element.</param>
            <returns>The mapping in PDF of the specified HTML element or null if no element 
            with the specified ID was found.</returns>
        </member>
        <member name="M:SelectPdf.WebElementsCollection.GetElementsByTagName(System.String)">
            <summary>
            Gets the mapping in PDF of the HTML elements with the specified HTML tag name.
            The search is case insensitive.
            </summary>
            <param name="htmlElementTagName">The HTML tag name of the element.</param>
            <returns>The mappings in PDF of the specified HTML elements or null if no element 
            with the specified tag name was found.</returns>
        </member>
        <member name="P:SelectPdf.WebElementsCollection.Count">
            <summary>
            Gets the number of mappings in collection.
            </summary>
        </member>
        <member name="T:SelectPdf.WebElementsMappingOptions">
            <summary>
            Helps retrieving the positions of a list of html elements in the generated pdf document.
            </summary>
        </member>
        <member name="P:SelectPdf.WebElementsMappingOptions.CssSelectors">
            <summary>
            This property is used to set an array containing the selectors of the HTML elements for which
            to retrieve the position in PDF. 
            For example, the selector for all the image elements is "img", the selector for all the elements with the CSS class name 'myclass'
            is "*.myclass" and the selector for the elements with the id 'myid' is "*#myid".
            The elements mapping will be retrieved in the Result property after conversion.
            </summary>
        </member>
        <member name="P:SelectPdf.WebElementsMappingOptions.Result">
            <summary>
            This property is set automatically after conversion and returns the mapping into the generated pdf document of the html elements 
            specified by the CssSelectors. 
            </summary>
        </member>
        <member name="T:SelectPdf.WebPageInformation">
            <summary>
            This class provides information about the converted web page (title, description, keywords).
            </summary>
        </member>
        <member name="P:SelectPdf.WebPageInformation.Title">
            <summary>
            The web page title.
            </summary>
        </member>
        <member name="P:SelectPdf.WebPageInformation.Keywords">
            <summary>
            The web page keywords.
            </summary>
        </member>
        <member name="P:SelectPdf.WebPageInformation.Description">
            <summary>
            The web page description.
            </summary>
        </member>
    </members>
</doc>
