﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Reflection</name>
  </assembly>
  <members>
    <member name="T:System.Reflection.AmbiguousMatchException">
      <summary>멤버에 바인딩할 때 바인딩 기준에 일치하는 멤버가 여러 개인 경우 throw되는 예외입니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Reflection.AmbiguousMatchException.#ctor">
      <summary>빈 메시지 문자열과 null로 설정된 근본 원인 예외를 사용하여 <see cref="T:System.Reflection.AmbiguousMatchException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Reflection.AmbiguousMatchException.#ctor(System.String)">
      <summary>메시지 문자열을 제공된 메시지로 설정하고 근본 원인 예외를 null로 설정하여 <see cref="T:System.Reflection.AmbiguousMatchException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">이 예외가 throw된 원인을 나타내는 문자열입니다. </param>
    </member>
    <member name="M:System.Reflection.AmbiguousMatchException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Reflection.AmbiguousMatchException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="T:System.Reflection.Assembly">
      <summary>다시 사용 및 버전 지정이 가능한, 공용 언어 런타임 응용 프로그램의 자체 설명 빌딩 블록인 어셈블리를 나타냅니다.</summary>
    </member>
    <member name="P:System.Reflection.Assembly.CustomAttributes">
      <summary>이 어셈블리의 사용자 지정 특성을 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>이 어셈블리의 사용자 지정 특성을 포함하는 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.Assembly.DefinedTypes">
      <summary>이 어셈블리에 정의된 형식의 컬렉션을 가져옵니다.</summary>
      <returns>이 어셈블리에 정의된 형식의 컬렉션입니다.</returns>
    </member>
    <member name="M:System.Reflection.Assembly.Equals(System.Object)">
      <summary>이 어셈블리와 지정된 개체가 서로 같은지 확인합니다.</summary>
      <returns>true가 이 인스턴스와 같으면 <paramref name="o" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">이 인스턴스와 비교할 개체입니다. </param>
    </member>
    <member name="P:System.Reflection.Assembly.ExportedTypes">
      <summary>이 어셈블리에 정의된 형식 중 어셈블리 외부에서 볼 수 있는 public 형식의 컬렉션을 가져옵니다.</summary>
      <returns>이 어셈블리에 정의된 형식 중 어셈블리 외부에서 볼 수 있는 public 형식의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.Assembly.FullName">
      <summary>어셈블리의 표시 이름을 가져옵니다.</summary>
      <returns>어셈블리의 표시 이름입니다.</returns>
    </member>
    <member name="M:System.Reflection.Assembly.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 반환합니다.</summary>
      <returns>부호 있는 32비트 정수 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Reflection.Assembly.GetManifestResourceInfo(System.String)">
      <summary>지정된 리소스가 지속되는 방법에 대한 정보를 반환합니다.</summary>
      <returns>리소스의 토폴로지에 대한 정보로 채워진 개체이거나, 리소스를 찾을 수 없는 경우 null입니다.</returns>
      <param name="resourceName">리소스의 이름(대/소문자 구분)입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="resourceName" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="resourceName" /> 매개 변수가 빈 문자열("")인 경우 </exception>
    </member>
    <member name="M:System.Reflection.Assembly.GetManifestResourceNames">
      <summary>이 어셈블리에 있는 모든 리소스의 이름을 반환합니다.</summary>
      <returns>모든 리소스의 이름이 포함된 배열입니다.</returns>
    </member>
    <member name="M:System.Reflection.Assembly.GetManifestResourceStream(System.String)">
      <summary>지정된 매니페스트 리소스를 이 어셈블리에서 로드합니다.</summary>
      <returns>매니페스트 리소스이거나 컴파일하는 동안 리소스가 지정되지 않은 경우 또는 호출자가 리소스를 볼 수 없는 경우에는 null입니다.</returns>
      <param name="name">요청된 매니페스트 리소스의 이름(대/소문자 구분)입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> 매개 변수가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> 매개 변수가 빈 문자열("")인 경우 </exception>
      <exception cref="T:System.IO.FileLoadException">에 .NET for Windows Store apps 또는 이식 가능한 클래스 라이브러리, 기본 클래스 예외를 catch <see cref="T:System.IO.IOException" />, 대신 합니다.찾은 파일을 로드할 수 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="name" />을(를) 찾을 수 없습니다. </exception>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="name" />가 올바른 어셈블리가 아닌 경우 </exception>
      <exception cref="T:System.NotImplementedException">리소스 길이가 <see cref="F:System.Int64.MaxValue" />보다 큰 경우</exception>
    </member>
    <member name="M:System.Reflection.Assembly.GetName">
      <summary>이 어셈블리에 대한 <see cref="T:System.Reflection.AssemblyName" />을 가져옵니다.</summary>
      <returns>이 어셈블리에 대해 완전히 구문 분석된 표시 이름을 포함하는 개체입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Reflection.Assembly.GetType(System.String)">
      <summary>어셈블리 인스턴스에서 지정된 이름을 가진 <see cref="T:System.Type" /> 개체를 가져옵니다.</summary>
      <returns>지정된 클래스를 나타내는 개체이며 클래스가 없는 경우 null을 반환합니다.</returns>
      <param name="name">형식의 전체 이름입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" />이 잘못되었습니다. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null인 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="name" />에 찾을 수 없는 종속 어셈블리가 필요한 경우 </exception>
      <exception cref="T:System.IO.FileLoadException">에 .NET for Windows Store apps 또는 이식 가능한 클래스 라이브러리, 기본 클래스 예외를 catch <see cref="T:System.IO.IOException" />, 대신 합니다.<paramref name="name" />에 찾았지만 로드할 수 없는 종속 어셈블리가 필요한 경우또는현재 어셈블리를 리플렉션 전용 컨텍스트로 로드했으며 <paramref name="name" />에 미리 로드되지 않은 종속 어셈블리가 필요한 경우 </exception>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="name" />에 종속 어셈블리가 필요하지만 해당 파일이 올바른 어셈블리가 아닌 경우 또는<paramref name="name" />에 현재 로드된 버전보다 높은 버전의 런타임용으로 컴파일된 종속 어셈블리가 필요한 경우 </exception>
    </member>
    <member name="M:System.Reflection.Assembly.GetType(System.String,System.Boolean,System.Boolean)">
      <summary>대/소문자를 무시할지 여부와 형식이 없으면 예외를 throw할지를 선택적으로 지정하여 어셈블리 인스턴스에서 지정된 이름을 가진 <see cref="T:System.Type" /> 개체를 가져옵니다.</summary>
      <returns>지정된 클래스를 나타내는 개체입니다.</returns>
      <param name="name">형식의 전체 이름입니다. </param>
      <param name="throwOnError">형식을 찾을 수 없는 경우 예외를 throw하려면 true이고, false을 반환하려면 null입니다. </param>
      <param name="ignoreCase">형식 이름의 대/소문자를 무시하면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" />이 잘못되었습니다.또는 <paramref name="name" />의 길이가 1024자를 초과하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null인 경우 </exception>
      <exception cref="T:System.TypeLoadException">
        <paramref name="throwOnError" />가 true이고 형식을 찾을 수 없는 경우</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="name" />에 찾을 수 없는 종속 어셈블리가 필요한 경우 </exception>
      <exception cref="T:System.IO.FileLoadException">
        <paramref name="name" />에 찾았지만 로드할 수 없는 종속 어셈블리가 필요한 경우또는현재 어셈블리를 리플렉션 전용 컨텍스트로 로드했으며 <paramref name="name" />에 미리 로드되지 않은 종속 어셈블리가 필요한 경우 </exception>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="name" />에 종속 어셈블리가 필요하지만 해당 파일이 올바른 어셈블리가 아닌 경우 또는<paramref name="name" />에 현재 로드된 버전보다 높은 버전의 런타임용으로 컴파일된 종속 어셈블리가 필요한 경우</exception>
    </member>
    <member name="P:System.Reflection.Assembly.IsDynamic">
      <summary>현재 어셈블리가 현재 프로세스에서 리플렉션 내보내기를 사용하여 동적으로 생성되었는지를 나타내는 값을 가져옵니다.</summary>
      <returns>현재 어셈블리가 현재 프로세스에서 동적으로 생성되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Reflection.Assembly.Load(System.Reflection.AssemblyName)">
      <summary>해당 <see cref="T:System.Reflection.AssemblyName" />이 지정된 어셈블리를 로드합니다.</summary>
      <returns>로드된 어셈블리입니다.</returns>
      <param name="assemblyRef">로드될 어셈블리를 설명하는 개체입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="assemblyRef" />가 null인 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="assemblyRef" />가 없는 경우 </exception>
      <exception cref="T:System.IO.FileLoadException">에 .NET for Windows Store apps 또는 이식 가능한 클래스 라이브러리, 기본 클래스 예외를 catch <see cref="T:System.IO.IOException" />, 대신 합니다.찾은 파일을 로드할 수 없는 경우 </exception>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="assemblyRef" />가 올바른 어셈블리가 아닌 경우또는버전 2.0 이상의 공용 언어 런타임이 현재 로드되어 있으며 <paramref name="assemblyRef" />가 이후 버전으로 컴파일된 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="*AllFiles*" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Reflection.Assembly.ManifestModule">
      <summary>현재 어셈블리의 매니페스트가 포함된 모듈을 가져옵니다. </summary>
      <returns>어셈블리의 매니페스트가 포함된 모듈입니다. </returns>
    </member>
    <member name="P:System.Reflection.Assembly.Modules">
      <summary>이 어셈블리의 모듈을 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>이 어셈블리의 모듈을 포함하는 컬렉션입니다.</returns>
    </member>
    <member name="M:System.Reflection.Assembly.ToString">
      <summary>표시 이름이라고도 하는 어셈블리의 전체 이름을 반환합니다.</summary>
      <returns>어셈블리의 전체 이름입니다. 어셈블리의 전체 이름을 확인할 수 없는 경우에는 클래스 이름입니다.</returns>
    </member>
    <member name="T:System.Reflection.AssemblyContentType">
      <summary>어셈블리에 포함된 코드의 종류에 대한 정보를 제공합니다.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyContentType.Default">
      <summary>어셈블리에는 .NET Framework 코드가 포함되어 있습니다.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyContentType.WindowsRuntime">
      <summary>어셈블리에는 Windows 런타임 코드가 포함되어 있습니다.</summary>
    </member>
    <member name="T:System.Reflection.AssemblyName">
      <summary>어셈블리의 고유 ID에 대해 자세히 설명합니다.</summary>
    </member>
    <member name="M:System.Reflection.AssemblyName.#ctor">
      <summary>
        <see cref="T:System.Reflection.AssemblyName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Reflection.AssemblyName.#ctor(System.String)">
      <summary>지정된 표시 이름을 사용하여 <see cref="T:System.Reflection.AssemblyName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="assemblyName">
        <see cref="P:System.Reflection.AssemblyName.FullName" /> 속성에서 반환된 어셈블리의 표시 이름입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="assemblyName" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="assemblyName" />이 길이가 0인 문자열인 경우 </exception>
      <exception cref="T:System.IO.FileLoadException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.IO.IOException" />를 catch합니다.참조된 어셈블리를 찾을 수 없거나 로드할 수 없는 경우</exception>
    </member>
    <member name="P:System.Reflection.AssemblyName.ContentType">
      <summary>어셈블리가 포함하는 콘텐츠의 형식을 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>어셈블리가 포함하는 콘텐츠의 형식을 나타내는 값입니다.</returns>
    </member>
    <member name="P:System.Reflection.AssemblyName.CultureName">
      <summary>어셈블리와 연결된 문화권의 이름을 가져오거나 설정합니다.</summary>
      <returns>문화권 이름입니다.</returns>
    </member>
    <member name="P:System.Reflection.AssemblyName.Flags">
      <summary>어셈블리의 특성을 가져오거나 설정합니다.</summary>
      <returns>어셈블리의 특성을 나타내는 값입니다.</returns>
    </member>
    <member name="P:System.Reflection.AssemblyName.FullName">
      <summary>표시 이름이라고도 하는 어셈블리의 전체 이름을 가져옵니다.</summary>
      <returns>표시 이름이라고도 하는 어셈블리의 전체 이름인 문자열입니다.</returns>
    </member>
    <member name="M:System.Reflection.AssemblyName.GetPublicKey">
      <summary>어셈블리의 공개 키를 가져옵니다.</summary>
      <returns>어셈블리의 공개 키가 포함된 바이트 배열입니다.</returns>
      <exception cref="T:System.Security.SecurityException">공개 키가 제공되지만(예: <see cref="M:System.Reflection.AssemblyName.SetPublicKey(System.Byte[])" /> 메서드 사용) 공개 키 토큰은 제공되지 않습니다. </exception>
    </member>
    <member name="M:System.Reflection.AssemblyName.GetPublicKeyToken">
      <summary>응용 프로그램이나 어셈블리 서명에 사용된 공개 키 SHA-1 해시의 마지막 8자인 공개 키 토큰을 가져옵니다.</summary>
      <returns>공개 키 토큰이 들어 있는 바이트 배열입니다.</returns>
    </member>
    <member name="P:System.Reflection.AssemblyName.Name">
      <summary>어셈블리의 단순한 이름을 가져오거나 설정합니다.어셈블리의 단순한 이름은 대개 어셈블리의 매니페스트 파일 이름에서 확장명을 제외한 것입니다.</summary>
      <returns>어셈블리의 단순한 이름입니다.</returns>
    </member>
    <member name="P:System.Reflection.AssemblyName.ProcessorArchitecture">
      <summary>실행 파일의 대상이 되는 플랫폼의 프로세서 및 워드 당 비트 수를 식별하는 값을 가져오거나 설정합니다.</summary>
      <returns>실행 파일의 대상이 되는 플랫폼의 프로세서 및 워드 당 비트 수를 식별하는 열거형 값 중 하나입니다.</returns>
    </member>
    <member name="M:System.Reflection.AssemblyName.SetPublicKey(System.Byte[])">
      <summary>어셈블리를 식별하는 공개 키를 설정합니다.</summary>
      <param name="publicKey">어셈블리의 공개 키가 포함된 바이트 배열입니다. </param>
    </member>
    <member name="M:System.Reflection.AssemblyName.SetPublicKeyToken(System.Byte[])">
      <summary>응용 프로그램이나 어셈블리 서명에 사용된 공개 키 SHA-1 해시의 마지막 8바이트인 공개 키 토큰을 설정합니다.</summary>
      <param name="publicKeyToken">어셈블리의 공개 키 토큰이 포함된 바이트 배열입니다. </param>
    </member>
    <member name="M:System.Reflection.AssemblyName.ToString">
      <summary>표시 이름이라고도 하는 어셈블리의 전체 이름을 반환합니다.</summary>
      <returns>어셈블리의 전체 이름입니다. 어셈블리의 전체 이름을 확인할 수 없는 경우에는 클래스 이름입니다.</returns>
    </member>
    <member name="P:System.Reflection.AssemblyName.Version">
      <summary>어셈블리의 주 번호, 부 번호, 빌드 번호 및 수정 번호를 가져오거나 설정합니다.</summary>
      <returns>어셈블리의 주 번호, 부 번호, 빌드 번호 및 수정 번호를 나타내는 개체입니다.</returns>
    </member>
    <member name="T:System.Reflection.ConstructorInfo">
      <summary>클래스 생성자의 특성을 검색하고 생성자 메타데이터에 대한 액세스를 제공합니다. </summary>
    </member>
    <member name="F:System.Reflection.ConstructorInfo.ConstructorName">
      <summary>클래스 생성자 메서드가 메타데이터에 저장되면 해당 메서드의 이름을 나타냅니다.이 이름은 항상 ".ctor"입니다.이 필드는 읽기 전용입니다.</summary>
    </member>
    <member name="M:System.Reflection.ConstructorInfo.Equals(System.Object)">
      <summary>이 인스턴스가 지정된 개체와 같은지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="obj" />가 이 인스턴스의 형식 및 값과 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">이 인스턴스와 비교할 개체 또는 null입니다.</param>
    </member>
    <member name="M:System.Reflection.ConstructorInfo.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 반환합니다.</summary>
      <returns>32비트 부호 있는 정수 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Reflection.ConstructorInfo.Invoke(System.Object[])">
      <summary>지정된 매개 변수가 있는 인스턴스에서 리플렉션된 생성자를 호출하여 일반적으로 사용되지 않는 매개 변수에 대한 기본값을 제공합니다.</summary>
      <returns>생성자와 관련된 클래스의 인스턴스입니다.</returns>
      <param name="parameters">기본 바인더의 제약 조건에서 이 생성자에 대한 매개 변수의 개수, 순서 및 형식과 일치하는 값의 배열입니다.이 생성자가 매개 변수를 사용하지 않으면 요소가 0인 배열이나 null을 Object[] parameters = new Object[0]와 같이 사용합니다.이 배열에서 명시적으로 값을 사용하여 초기화되지 않은 개체는 모두 해당 개체 형식에 대한 기본값을 포함합니다.참조 형식 요소의 경우 이 값은 null입니다.값 형식 요소의 경우 특정 요소 형식에 따라 이 값은 0, 0.0 또는 false입니다.</param>
      <exception cref="T:System.MemberAccessException">클래스가 추상 클래스인 경우또는 생성자가 클래스 이니셜라이저인 경우 </exception>
      <exception cref="T:System.MethodAccessException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.MemberAccessException" />를 catch합니다.생성자가 전용 또는 보호되는 생성자이거나 호출자에게 <see cref="F:System.Security.Permissions.ReflectionPermissionFlag.MemberAccess" />가 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="parameters" /> 배열에 이 생성자에서 적용하는 형식과 일치하는 값이 없는 경우 </exception>
      <exception cref="T:System.Reflection.TargetInvocationException">호출된 생성자에서 예외를 throw하는 경우 </exception>
      <exception cref="T:System.Reflection.TargetParameterCountException">잘못된 수의 매개 변수가 전달된 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.TypedReference" />, <see cref="T:System.ArgIterator" /> 및 <see cref="T:System.RuntimeArgumentHandle" /> 형식을 만들 수 없는 경우</exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 코드 액세스 권한이 없는 경우</exception>
    </member>
    <member name="F:System.Reflection.ConstructorInfo.TypeConstructorName">
      <summary>메타데이터에 저장된 형식 생성자 메서드의 이름을 나타냅니다.이 이름은 항상 ".cctor"입니다.이 속성은 읽기 전용입니다.</summary>
    </member>
    <member name="T:System.Reflection.CustomAttributeData">
      <summary>리플렉션 전용 컨텍스트에 로드된 어셈블리, 모듈, 형식, 멤버 및 매개 변수에 대한 사용자 지정 특성 데이터에 대한 액세스를 제공합니다.</summary>
    </member>
    <member name="P:System.Reflection.CustomAttributeData.AttributeType">
      <summary>특성의 형식을 가져옵니다.</summary>
      <returns>특성의 형식입니다.</returns>
    </member>
    <member name="P:System.Reflection.CustomAttributeData.ConstructorArguments">
      <summary>
        <see cref="T:System.Reflection.CustomAttributeData" /> 개체가 나타내는 특성 인스턴스에 지정된 위치 인수 목록을 가져옵니다.</summary>
      <returns>사용자 지정 특성 인스턴스에 지정된 위치 인수를 나타내는 구조체의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.CustomAttributeData.NamedArguments">
      <summary>
        <see cref="T:System.Reflection.CustomAttributeData" /> 개체가 나타내는 특성 인스턴스에 지정된 명명된 인수 목록을 가져옵니다.</summary>
      <returns>사용자 지정 특성 인스턴스에 지정된 명명된 인수를 나타내는 구조체의 컬렉션입니다.</returns>
    </member>
    <member name="T:System.Reflection.CustomAttributeNamedArgument">
      <summary>리플렉션 전용 컨텍스트에서 사용자 지정 특성의 명명된 인수를 나타냅니다.</summary>
    </member>
    <member name="P:System.Reflection.CustomAttributeNamedArgument.IsField">
      <summary>명명된 인수가 필드인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>명명된 인수가 필드이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.CustomAttributeNamedArgument.MemberName">
      <summary>명명된 인수를 설정하는 데 사용할 특성 멤버의 이름을 가져옵니다.</summary>
      <returns>명명된 인수를 설정하는 데 사용할 특성 멤버의 이름입니다.</returns>
    </member>
    <member name="P:System.Reflection.CustomAttributeNamedArgument.TypedValue">
      <summary>명명된 현재 인수의 형식과 값을 가져오는 데 사용할 수 있는 <see cref="T:System.Reflection.CustomAttributeTypedArgument" /> 구조체를 가져옵니다.</summary>
      <returns>명명된 현재 인수의 형식과 값을 가져오는 데 사용할 수 있는 구조체입니다.</returns>
    </member>
    <member name="T:System.Reflection.CustomAttributeTypedArgument">
      <summary>리플렉션 전용 컨텍스트에서 사용자 지정 특성의 인수 또는 배열 인수의 요소를 나타냅니다.</summary>
    </member>
    <member name="P:System.Reflection.CustomAttributeTypedArgument.ArgumentType">
      <summary>인수의 형식 또는 배열 인수 요소의 형식을 가져옵니다.</summary>
      <returns>인수의 형식 또는 배열 요소의 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Reflection.CustomAttributeTypedArgument.Value">
      <summary>간단한 인수 또는 배열 인수 요소의 경우에는 인수의 값을 가져오고, 배열 인수의 경우에는 값 컬렉션을 가져옵니다.</summary>
      <returns>인수 또는 요소의 값을 나타내는 개체이거나, 배열 형식 인수의 값을 나타내는 <see cref="T:System.Reflection.CustomAttributeTypedArgument" /> 개체의 제네릭 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />입니다.</returns>
    </member>
    <member name="T:System.Reflection.EventInfo">
      <summary>이벤트의 특성을 검색하고 이벤트 메타데이터에 대한 액세스를 제공합니다.</summary>
    </member>
    <member name="M:System.Reflection.EventInfo.AddEventHandler(System.Object,System.Delegate)">
      <summary>이벤트 소스에 이벤트 처리기를 추가합니다.</summary>
      <param name="target">이벤트 소스입니다. </param>
      <param name="handler">대상에서 이벤트를 발생시킬 때 호출할 메서드를 캡슐화합니다. </param>
      <exception cref="T:System.InvalidOperationException">이벤트에 public add 접근자가 없는 경우</exception>
      <exception cref="T:System.ArgumentException">전달된 처리기를 사용할 수 없는 경우 </exception>
      <exception cref="T:System.MethodAccessException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.MemberAccessException" />를 catch합니다.호출자에게 멤버에 액세스할 권한이 없는 경우 </exception>
      <exception cref="T:System.Reflection.TargetException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 <see cref="T:System.Exception" />를 catch합니다.<paramref name="target" /> 매개 변수가 null이고 이벤트가 정적이 아닌 경우또는 <see cref="T:System.Reflection.EventInfo" />가 대상에 선언되지 않은 경우 </exception>
    </member>
    <member name="P:System.Reflection.EventInfo.AddMethod">
      <summary>비공용 메서드를 포함하여 이벤트의 <see cref="M:System.Reflection.EventInfo.AddEventHandler(System.Object,System.Delegate)" /> 메서드에 대한 <see cref="T:System.Reflection.MethodInfo" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="M:System.Reflection.EventInfo.AddEventHandler(System.Object,System.Delegate)" /> 메서드의 <see cref="T:System.Reflection.MethodInfo" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Reflection.EventInfo.Attributes">
      <summary>이 이벤트에 대한 특성을 가져옵니다.</summary>
      <returns>이 이벤트에 대한 읽기 전용 특성입니다.</returns>
    </member>
    <member name="M:System.Reflection.EventInfo.Equals(System.Object)">
      <summary>이 인스턴스가 지정된 개체와 같은지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="obj" />가 이 인스턴스의 형식 및 값과 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">이 인스턴스와 비교할 개체 또는 null입니다.</param>
    </member>
    <member name="P:System.Reflection.EventInfo.EventHandlerType">
      <summary>이 이벤트와 연결된 내부 이벤트 처리기 대리자의 Type 개체를 가져옵니다.</summary>
      <returns>대리자 이벤트 처리기를 나타내는 읽기 전용 Type 개체입니다.</returns>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
    </member>
    <member name="M:System.Reflection.EventInfo.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 반환합니다.</summary>
      <returns>32비트 부호 있는 정수 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Reflection.EventInfo.IsSpecialName">
      <summary>EventInfo의 이름에 특수한 의미가 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이벤트에 특수한 이름이 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.EventInfo.RaiseMethod">
      <summary>이벤트가 발생할 때 호출되는 메서드(public이 아닌 메서드 포함)를 가져옵니다.</summary>
      <returns>이벤트가 발생할 때 호출되는 메서드입니다.</returns>
    </member>
    <member name="M:System.Reflection.EventInfo.RemoveEventHandler(System.Object,System.Delegate)">
      <summary>이벤트 소스에서 이벤트 처리기를 제거합니다.</summary>
      <param name="target">이벤트 소스입니다. </param>
      <param name="handler">대상에서 발생시킨 이벤트로부터 연관을 해제할 대리자입니다. </param>
      <exception cref="T:System.InvalidOperationException">이벤트에 public remove 접근자가 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">전달된 처리기를 사용할 수 없는 경우 </exception>
      <exception cref="T:System.Reflection.TargetException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 <see cref="T:System.Exception" />를 catch합니다.<paramref name="target" /> 매개 변수가 null이고 이벤트가 정적이 아닌 경우또는 <see cref="T:System.Reflection.EventInfo" />가 대상에 선언되지 않은 경우 </exception>
      <exception cref="T:System.MethodAccessException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.MemberAccessException" />를 catch합니다.호출자에게 멤버에 액세스할 권한이 없는 경우 </exception>
    </member>
    <member name="P:System.Reflection.EventInfo.RemoveMethod">
      <summary>비공용 메서드를 포함하여 이벤트 메서드를 제거하기 위한 MethodInfo 개체를 가져옵니다.</summary>
      <returns>이벤트의 메서드를 제거하는 MethodInfo 개체입니다.</returns>
    </member>
    <member name="T:System.Reflection.FieldInfo">
      <summary>필드의 특성을 검색하고 필드 메타데이터에 대한 액세스를 제공합니다. </summary>
    </member>
    <member name="P:System.Reflection.FieldInfo.Attributes">
      <summary>이 필드와 관련된 특성을 가져옵니다.</summary>
      <returns>이 필드에 대한 FieldAttributes입니다.</returns>
    </member>
    <member name="M:System.Reflection.FieldInfo.Equals(System.Object)">
      <summary>이 인스턴스가 지정된 개체와 같은지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="obj" />가 이 인스턴스의 형식 및 값과 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">이 인스턴스와 비교할 개체 또는 null입니다.</param>
    </member>
    <member name="P:System.Reflection.FieldInfo.FieldType">
      <summary>이 필드 개체의 형식을 가져옵니다.</summary>
      <returns>이 필드 개체의 형식입니다.</returns>
    </member>
    <member name="M:System.Reflection.FieldInfo.GetFieldFromHandle(System.RuntimeFieldHandle)">
      <summary>지정된 핸들이 나타내는 필드의 <see cref="T:System.Reflection.FieldInfo" />를 가져옵니다.</summary>
      <returns>
        <paramref name="handle" />로 지정된 필드를 나타내는 <see cref="T:System.Reflection.FieldInfo" /> 개체입니다.</returns>
      <param name="handle">필드의 내부 메타데이터 표현에 대한 핸들이 들어 있는 <see cref="T:System.RuntimeFieldHandle" /> 구조체입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" />이(가) 잘못되었습니다.</exception>
    </member>
    <member name="M:System.Reflection.FieldInfo.GetFieldFromHandle(System.RuntimeFieldHandle,System.RuntimeTypeHandle)">
      <summary>지정된 제네릭 형식에 대해 지정된 핸들이 나타내는 필드의 <see cref="T:System.Reflection.FieldInfo" />를 가져옵니다.</summary>
      <returns>
        <paramref name="declaringType" />으로 지정된 제네릭 형식에서 <paramref name="handle" />로 지정된 필드를 나타내는 <see cref="T:System.Reflection.FieldInfo" /> 개체입니다.</returns>
      <param name="handle">필드의 내부 메타데이터 표현에 대한 핸들이 들어 있는 <see cref="T:System.RuntimeFieldHandle" /> 구조체입니다.</param>
      <param name="declaringType">필드를 정의하는 제네릭 형식에 대한 핸들이 들어 있는 <see cref="T:System.RuntimeTypeHandle" /> 구조체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" />이(가) 잘못되었습니다.또는<paramref name="declaringType" />이 <paramref name="handle" />과 호환되지 않는 경우.예를 들어 <paramref name="declaringType" />은 제네렉 형식 정의의 런타임 형식 핸들이며 <paramref name="handle" />은 생성된 형식에서 만들어집니다.설명 부분을 참조하십시오.</exception>
    </member>
    <member name="M:System.Reflection.FieldInfo.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 반환합니다.</summary>
      <returns>32비트 부호 있는 정수 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Reflection.FieldInfo.GetValue(System.Object)">
      <summary>파생 클래스에 재정의할 때 지정된 개체에서 지원하는 필드의 값을 반환합니다.</summary>
      <returns>이 인스턴스에서 리플렉션된 필드 값을 포함하는 개체입니다.</returns>
      <param name="obj">필드 값이 반환될 개체입니다. </param>
      <exception cref="T:System.Reflection.TargetException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 <see cref="T:System.Exception" />를 catch합니다.정적 필드가 아니고 <paramref name="obj" />가 null인 경우 </exception>
      <exception cref="T:System.NotSupportedException">리터럴로 표시된 필드에 허용되는 리터럴 형식이 들어 있지 않은 경우 </exception>
      <exception cref="T:System.FieldAccessException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.MemberAccessException" />를 catch합니다.호출자에 이 필드에 액세스할 권한이 없는 경우 </exception>
      <exception cref="T:System.ArgumentException">메서드를 선언하거나 <paramref name="obj" />의 클래스에서 메서드를 상속하지 않은 경우 </exception>
    </member>
    <member name="P:System.Reflection.FieldInfo.IsAssembly">
      <summary>
        <see cref="F:System.Reflection.FieldAttributes.Assembly" />에서 이 필드의 잠재적 표시 유형을 설명하는지 여부를 나타내는 값을 가져옵니다. 즉, 이 필드는 같은 어셈블리의 다른 형식에만 표시되고 어셈블리 외부의 파생 형식에는 표시되지 않습니다.</summary>
      <returns>
        <see cref="F:System.Reflection.FieldAttributes.Assembly" />에서 이 필드의 표시 유형을 정확하게 설명하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.FieldInfo.IsFamily">
      <summary>
        <see cref="F:System.Reflection.FieldAttributes.Family" />에서 이 필드의 표시 유형을 설명하는지 여부를 나타내는 값을 가져옵니다. 즉, 이 필드는 해당 클래스 및 파생 클래스에만 표시됩니다.</summary>
      <returns>
        <see cref="F:System.Reflection.FieldAttributes.Family" />에서 이 필드에 대한 액세스를 정확하게 설명하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.FieldInfo.IsFamilyAndAssembly">
      <summary>
        <see cref="F:System.Reflection.FieldAttributes.FamANDAssem" />에서 이 필드의 표시 유형을 설명하는지 여부를 나타내는 값을 가져옵니다. 즉, 이 필드는 같은 어셈블리에 있는 경우에만 파생 클래스에서 액세스할 수 있습니다.</summary>
      <returns>
        <see cref="F:System.Reflection.FieldAttributes.FamANDAssem" />에서 이 필드에 대한 액세스를 정확하게 설명하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.FieldInfo.IsFamilyOrAssembly">
      <summary>
        <see cref="F:System.Reflection.FieldAttributes.FamORAssem" />에서 이 필드의 잠재적 표시 유형을 설명하는지 여부를 나타내는 값을 가져옵니다. 즉, 이 필드는 파생 클래스(있는 경우) 및 같은 어셈블리의 클래스에서 액세스할 수 있습니다.</summary>
      <returns>
        <see cref="F:System.Reflection.FieldAttributes.FamORAssem" />에서 이 필드에 대한 액세스를 정확하게 설명하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.FieldInfo.IsInitOnly">
      <summary>필드가 생성자의 본문에만 적용될 수 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>필드에 InitOnly 특성 집합이 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.FieldInfo.IsLiteral">
      <summary>값을 컴파일 타임에만 쓰고 변경할 수 없는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>필드에 Literal 특성 집합이 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.FieldInfo.IsPrivate">
      <summary>private 필드인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>private 필드이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.FieldInfo.IsPublic">
      <summary>public 필드인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>public 필드이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.FieldInfo.IsSpecialName">
      <summary>해당 SpecialName 특성이 <see cref="T:System.Reflection.FieldAttributes" /> 열거자에 설정되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>SpecialName 특성이 <see cref="T:System.Reflection.FieldAttributes" />에 설정되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.FieldInfo.IsStatic">
      <summary>정적 필드인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이 필드가 정적 필드이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Reflection.FieldInfo.SetValue(System.Object,System.Object)">
      <summary>지정된 개체에서 지원하는 필드의 값을 설정합니다.</summary>
      <param name="obj">필드 값을 설정할 개체입니다. </param>
      <param name="value">필드에 할당할 값입니다. </param>
      <exception cref="T:System.FieldAccessException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.MemberAccessException" />를 catch합니다.호출자에 이 필드에 액세스할 권한이 없는 경우 </exception>
      <exception cref="T:System.Reflection.TargetException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 <see cref="T:System.Exception" />를 catch합니다.<paramref name="obj" /> 매개 변수가 null이고 필드가 인스턴스 필드인 경우 </exception>
      <exception cref="T:System.ArgumentException">개체에 필드가 없는 경우또는 <paramref name="value" /> 매개 변수를 변환하여 필드에 저장할 수 없는 경우 </exception>
    </member>
    <member name="T:System.Reflection.IntrospectionExtensions">
      <summary>
        <see cref="T:System.Type" /> 개체를 변환하기 위한 메서드를 포함합니다.</summary>
    </member>
    <member name="M:System.Reflection.IntrospectionExtensions.GetTypeInfo(System.Type)">
      <summary>지정된 형식의 <see cref="T:System.Reflection.TypeInfo" /> 표현을 반환합니다.</summary>
      <returns>변환된 개체입니다.</returns>
      <param name="type">변환할 형식입니다.</param>
    </member>
    <member name="T:System.Reflection.IReflectableType">
      <summary>반영할 수 있는 형식을 나타냅니다.</summary>
    </member>
    <member name="M:System.Reflection.IReflectableType.GetTypeInfo">
      <summary>이 형식을 나타내는 개체를 검색합니다.</summary>
      <returns>이 형식을 나타내는 개체입니다.</returns>
    </member>
    <member name="T:System.Reflection.LocalVariableInfo">
      <summary>지역 변수의 특성을 검색하고 지역 변수 메타데이터에 대한 액세스를 제공합니다.</summary>
    </member>
    <member name="M:System.Reflection.LocalVariableInfo.#ctor">
      <summary>
        <see cref="T:System.Reflection.LocalVariableInfo" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.Reflection.LocalVariableInfo.IsPinned">
      <summary>지역 변수에서 참조하는 개체가 메모리에 고정되어 있는지 여부를 나타내는 <see cref="T:System.Boolean" /> 값을 가져옵니다.</summary>
      <returns>변수에서 참조하는 개체가 메모리에 고정되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.LocalVariableInfo.LocalIndex">
      <summary>메서드 본문 내의 지역 변수 인덱스를 가져옵니다.</summary>
      <returns>메서드 본문 내에서 지역 변수의 선언 순서를 나타내는 정수 값입니다.</returns>
    </member>
    <member name="P:System.Reflection.LocalVariableInfo.LocalType">
      <summary>지역 변수의 형식을 가져옵니다.</summary>
      <returns>지역 변수의 형식입니다.</returns>
    </member>
    <member name="M:System.Reflection.LocalVariableInfo.ToString">
      <summary>지역 변수를 설명하며 사용자가 읽을 수 있는 문자열을 반환합니다.</summary>
      <returns>형식 이름, 인덱스 및 고정 상태를 포함하여 지역 변수에 대한 정보를 표시하는 문자열입니다.</returns>
    </member>
    <member name="T:System.Reflection.ManifestResourceInfo">
      <summary>응용 프로그램 종속성을 설명하는 XML 파일인 매니페스트 리소스에 대한 액세스를 제공합니다.  </summary>
    </member>
    <member name="M:System.Reflection.ManifestResourceInfo.#ctor(System.Reflection.Assembly,System.String,System.Reflection.ResourceLocation)">
      <summary>지정된 어셈블리 및 파일에 포함되고 지정된 위치가 있는 리소스에 대해 <see cref="T:System.Reflection.ManifestResourceInfo" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="containingAssembly">매니페스트 리소스가 포함되는 어셈블리입니다.</param>
      <param name="containingFileName">파일이 매니페스트 파일과 다른 경우 매니페스트 리소스가 들어 있는 파일의 이름입니다.</param>
      <param name="resourceLocation">매니페스트 리소스의 위치에 대한 정보를 제공하는 열거형 값의 비트 조합입니다. </param>
    </member>
    <member name="P:System.Reflection.ManifestResourceInfo.FileName">
      <summary>매니페스트 파일과 다른 경우 매니페스트 리소스가 들어 있는 파일의 이름을 가져옵니다.  </summary>
      <returns>매니페스트 리소스의 파일 이름입니다.</returns>
    </member>
    <member name="P:System.Reflection.ManifestResourceInfo.ReferencedAssembly">
      <summary>매니페스트 리소스에 대한 포함 어셈블리를 가져옵니다. </summary>
      <returns>매니페스트 리소스의 포함 어셈블리입니다.</returns>
    </member>
    <member name="P:System.Reflection.ManifestResourceInfo.ResourceLocation">
      <summary>매니페스트 리소스의 위치를 가져옵니다. </summary>
      <returns>매니페스트 리소스의 위치를 나타내는 <see cref="T:System.Reflection.ResourceLocation" /> 플래그의 비트 조합입니다. </returns>
    </member>
    <member name="T:System.Reflection.MemberInfo">
      <summary>멤버의 특성에 대한 정보를 가져오고 멤버 메타데이터에 대한 액세스를 제공합니다.</summary>
    </member>
    <member name="P:System.Reflection.MemberInfo.CustomAttributes">
      <summary>이 멤버의 사용자 지정 특성을 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>이 멤버의 사용자 지정 특성을 포함하는 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.MemberInfo.DeclaringType">
      <summary>이 멤버를 선언하는 클래스를 가져옵니다.</summary>
      <returns>이 멤버를 선언하는 클래스에 대한 Type 개체입니다.</returns>
    </member>
    <member name="M:System.Reflection.MemberInfo.Equals(System.Object)">
      <summary>이 인스턴스가 지정된 개체와 같은지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="obj" />가 이 인스턴스의 형식 및 값과 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">이 인스턴스와 비교할 개체 또는 null입니다.</param>
    </member>
    <member name="M:System.Reflection.MemberInfo.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 반환합니다.</summary>
      <returns>32비트 부호 있는 정수 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Reflection.MemberInfo.Module">
      <summary>현재 <see cref="T:System.Reflection.MemberInfo" />가 나타내는 멤버를 선언하는 형식이 정의된 모듈을 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Reflection.MemberInfo" />가 나타내는 멤버를 선언하는 형식이 정의된 <see cref="T:System.Reflection.Module" />입니다.</returns>
      <exception cref="T:System.NotImplementedException">이 메서드가 구현되지 않은 경우</exception>
    </member>
    <member name="P:System.Reflection.MemberInfo.Name">
      <summary>현재 멤버의 이름을 가져옵니다.</summary>
      <returns>이 멤버의 이름이 포함된 <see cref="T:System.String" />입니다.</returns>
    </member>
    <member name="T:System.Reflection.MethodBase">
      <summary>메서드와 생성자에 대한 정보를 제공합니다. </summary>
    </member>
    <member name="P:System.Reflection.MethodBase.Attributes">
      <summary>이 메서드와 관련된 특성을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Reflection.MethodAttributes" /> 값 중 하나입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.CallingConvention">
      <summary>이 메서드에 대한 호출 규칙을 나타내는 값을 가져옵니다.</summary>
      <returns>이 메서드에 대한 <see cref="T:System.Reflection.CallingConventions" />입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.ContainsGenericParameters">
      <summary>제네릭 메서드에 할당되지 않은 제네릭 형식 매개 변수가 포함되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Reflection.MethodBase" /> 개체가 할당되지 않은 제네릭 형식 매개 변수를 포함하는 제네릭 메서드를 나타내면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Reflection.MethodBase.Equals(System.Object)">
      <summary>이 인스턴스가 지정된 개체와 같은지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="obj" />가 이 인스턴스의 형식 및 값과 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">이 인스턴스와 비교할 개체 또는 null입니다.</param>
    </member>
    <member name="M:System.Reflection.MethodBase.GetGenericArguments">
      <summary>제네릭 메서드의 형식 인수나 제네릭 메서드 정의의 형식 매개 변수를 나타내는 <see cref="T:System.Type" /> 개체의 배열을 반환합니다.</summary>
      <returns>제네릭 메서드의 형식 인수나 제네릭 메서드 정의의 형식 매개 변수를 나타내는 <see cref="T:System.Type" /> 개체의 배열입니다.현재 메서드가 제네릭 메서드가 아니면 빈 배열을 반환합니다.</returns>
      <exception cref="T:System.NotSupportedException">현재 개체가 <see cref="T:System.Reflection.ConstructorInfo" />인 경우..NET Framework 버전 2.0에서는 제네릭 생성자가 지원되지 않습니다.이 예외는 이 메서드가 파생 클래스에서 재정의되지 않은 경우의 기본 동작입니다.</exception>
    </member>
    <member name="M:System.Reflection.MethodBase.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 반환합니다.</summary>
      <returns>32비트 부호 있는 정수 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Reflection.MethodBase.GetMethodFromHandle(System.RuntimeMethodHandle)">
      <summary>메서드의 내부 메타데이터 표현(핸들)을 사용하여 메서드 정보를 가져옵니다.</summary>
      <returns>메서드에 대한 정보가 들어 있는 MethodBase입니다.</returns>
      <param name="handle">메서드의 핸들입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" />이(가) 잘못되었습니다.</exception>
    </member>
    <member name="M:System.Reflection.MethodBase.GetMethodFromHandle(System.RuntimeMethodHandle,System.RuntimeTypeHandle)">
      <summary>지정된 제네릭 형식에 대해 지정된 핸들이 나타내는 생성자 또는 메서드의 <see cref="T:System.Reflection.MethodBase" /> 개체를 가져옵니다.</summary>
      <returns>
        <paramref name="declaringType" />으로 지정된 제네릭 형식에서 <paramref name="handle" />로 지정된 메서드 또는 생성자를 나타내는 <see cref="T:System.Reflection.MethodBase" /> 개체입니다.</returns>
      <param name="handle">생성자 또는 메서드의 내부 메타데이터 표현에 대한 핸들입니다.</param>
      <param name="declaringType">생성자 또는 메서드를 정의하는 제네릭 형식에 대한 핸들입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" />이(가) 잘못되었습니다.</exception>
    </member>
    <member name="M:System.Reflection.MethodBase.GetParameters">
      <summary>파생 클래스에서 재정의되면 지정한 메서드나 생성자의 매개 변수를 가져옵니다.</summary>
      <returns>이 MethodBase 인스턴스에 의해 리플렉션된 메서드나 생성자의 시그니처와 일치하는 정보가 포함된 ParameterInfo 형식의 배열입니다.</returns>
    </member>
    <member name="M:System.Reflection.MethodBase.Invoke(System.Object,System.Object[])">
      <summary>지정된 매개 변수를 사용하여 현재 인스턴스가 나타내는 메서드나 생성자를 호출합니다.</summary>
      <returns>호출된 메서드의 반환 값이 들어 있는 개체이거나, 생성자일 경우 null입니다.주의ref 또는 out 키워드를 사용하여 선언한 매개 변수를 나타내는 <paramref name="parameters" /> 배열의 요소도 수정할 수 있습니다.</returns>
      <param name="obj">메서드나 생성자가 호출될 개체입니다.정적 메서드인 경우 이 인수는 무시됩니다.정적 생성자인 경우 이 인수는 null이거나 해당 생성자를 정의하는 클래스의 인스턴스여야 합니다.</param>
      <param name="parameters">호출되는 메서드나 생성자에 대해 사용하는 인수 목록입니다.이 목록은 호출할 메서드나 생성자의 매개 변수와 동일한 개수, 순서, 형식인 개체의 배열입니다.매개 변수가 없으면 <paramref name="parameters" />는 null이어야 합니다.이 인스턴스가 나타내는 메서드나 생성자에 ref 매개 변수(Visual Basic의 경우 ByRef)가 있으면 해당 매개 변수에 특수한 특성이 없어도 이 함수를 사용하여 메서드나 생성자를 호출할 수 있습니다.이 배열에서 명시적으로 값을 사용하여 초기화되지 않은 개체는 모두 해당 개체 형식에 대한 기본값을 포함합니다.참조 형식 요소의 경우 이 값은 null입니다.값 형식 요소의 경우 특정 요소 형식에 따라 이 값은 0, 0.0 또는 false입니다.</param>
      <exception cref="T:System.Reflection.TargetException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 <see cref="T:System.Exception" />를 catch합니다.<paramref name="obj" /> 매개 변수가 null이고 메서드가 정적이 아닌 경우또는 메서드가 <paramref name="obj" />에서 선언되지 않았거나 상속되지 않은 경우 또는정적 생성자가 호출되었고 <paramref name="obj" />가 null 또는 해당 생성자가 선언된 클래스의 인스턴스가 아닌 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="parameters" /> 배열의 요소가 이 인스턴스에 의해 리플렉션된 메서드 또는 생성자의 시그니처와 일치하지 않는 경우 </exception>
      <exception cref="T:System.Reflection.TargetInvocationException">호출된 메서드나 생성자가 예외를 throw하는 경우 또는현재 인스턴스는 확인할 수 없는 코드를 포함하는 <see cref="T:System.Reflection.Emit.DynamicMethod" />입니다.<see cref="T:System.Reflection.Emit.DynamicMethod" />에 대한 설명의 "확인" 단원을 참조하십시오.</exception>
      <exception cref="T:System.Reflection.TargetParameterCountException">
        <paramref name="parameters" /> 배열에 올바른 수의 인수가 포함되지 않은 경우 </exception>
      <exception cref="T:System.MethodAccessException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.MemberAccessException" />를 catch합니다.호출자가 현재 인스턴스가 나타내는 메서드 또는 생성자를 실행할 권한이 없습니다. </exception>
      <exception cref="T:System.InvalidOperationException">메서드를 선언하는 형식이 개방형 제네릭 형식인 경우.즉, <see cref="P:System.Type.ContainsGenericParameters" /> 속성이 선언 형식에 대해 true를 반환하는 경우</exception>
      <exception cref="T:System.NotSupportedException">현재 인스턴스는 <see cref="T:System.Reflection.Emit.MethodBuilder" />입니다.</exception>
    </member>
    <member name="P:System.Reflection.MethodBase.IsAbstract">
      <summary>이 메서드가 추상 메서드인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>해당 메서드가 추상 메서드이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsAssembly">
      <summary>
        <see cref="F:System.Reflection.MethodAttributes.Assembly" />에서 이 메서드나 생성자의 잠재적 표시 유형을 설명하는지 여부를 나타내는 값을 가져옵니다. 즉, 이 메서드나 생성자는 같은 어셈블리의 다른 형식에만 표시되고 어셈블리 외부의 파생 형식에는 표시되지 않습니다.</summary>
      <returns>
        <see cref="F:System.Reflection.MethodAttributes.Assembly" />에서 이 메서드나 생성자의 표시 유형을 정확하게 설명하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsConstructor">
      <summary>메서드가 생성자인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이 메서드가 <see cref="T:System.Reflection.ConstructorInfo" /> 개체가 나타내는 생성자이면 true이고(<see cref="T:System.Reflection.Emit.ConstructorBuilder" /> 개체에 대한 설명 부분 참조), 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsFamily">
      <summary>
        <see cref="F:System.Reflection.MethodAttributes.Family" />에서 이 메서드나 생성자의 표시 유형을 설명하는지 여부를 나타내는 값을 가져옵니다. 즉, 이 메서드나 생성자는 해당 클래스 및 파생 클래스에만 표시됩니다.</summary>
      <returns>
        <see cref="F:System.Reflection.MethodAttributes.Family" />에서 이 메서드나 생성자에 대한 액세스를 정확하게 설명하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsFamilyAndAssembly">
      <summary>
        <see cref="F:System.Reflection.MethodAttributes.FamANDAssem" />에서 이 메서드나 생성자의 표시 유형을 설명하는지 여부를 나타내는 값을 가져옵니다. 즉, 이 메서드나 생성자는 같은 어셈블리에 있는 경우에만 파생 클래스에서 호출할 수 있습니다.</summary>
      <returns>
        <see cref="F:System.Reflection.MethodAttributes.FamANDAssem" />에서 이 메서드나 생성자에 대한 액세스를 정확하게 설명하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsFamilyOrAssembly">
      <summary>
        <see cref="F:System.Reflection.MethodAttributes.FamORAssem" />에서 이 메서드나 생성자의 잠재적 표시 유형을 설명하는지 여부를 나타내는 값을 가져옵니다. 즉, 이 메서드나 생성자는 파생 클래스(있는 경우) 및 같은 어셈블리의 클래스에서 호출할 수 있습니다.</summary>
      <returns>
        <see cref="F:System.Reflection.MethodAttributes.FamORAssem" />에서 이 메서드나 생성자에 대한 액세스를 정확하게 설명하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsFinal">
      <summary>이 메서드가 final인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이 메서드가 final이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsGenericMethod">
      <summary>메서드가 제네릭 메서드인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Reflection.MethodBase" />가 제네릭 메서드를 나타내면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsGenericMethodDefinition">
      <summary>메서드가 제네릭 메서드 정의인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Reflection.MethodBase" /> 개체가 제네릭 메서드의 정의를 나타내면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsHideBySig">
      <summary>동일한 시그니처가 있는 동일한 종류의 멤버만을 파생 클래스에서 숨길 수 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>멤버를 시그니처로 숨길 수 있으면 true이고 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsPrivate">
      <summary>이 멤버가 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이 메서드에 대한 액세스가 클래스 자신의 다른 멤버로 제한되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsPublic">
      <summary>이 메서드가 public 메서드인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이 메서드가 public 메서드이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsSpecialName">
      <summary>이 메서드의 이름이 특수한지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이 메서드의 이름이 특수하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsStatic">
      <summary>메서드가 static인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이 메서드가 static이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.IsVirtual">
      <summary>메서드가 virtual인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이 메서드가 virtual이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.MethodBase.MethodImplementationFlags">
      <summary>메서드 구현의 특성을 지정하는 <see cref="T:System.Reflection.MethodImplAttributes" /> 플래그를 가져옵니다.</summary>
      <returns>메서드 구현 플래그입니다.</returns>
    </member>
    <member name="T:System.Reflection.MethodInfo">
      <summary>메서드의 특성을 검색하고 메서드 메타데이터에 대한 액세스를 제공합니다.</summary>
    </member>
    <member name="M:System.Reflection.MethodInfo.CreateDelegate(System.Type)">
      <summary>이 메서드로부터 지정된 형식의 대리자를 만듭니다.</summary>
      <returns>이 메서드의 대리자입니다.</returns>
      <param name="delegateType">만들 대리자의 형식입니다.</param>
    </member>
    <member name="M:System.Reflection.MethodInfo.CreateDelegate(System.Type,System.Object)">
      <summary>이 메서드로부터 지정된 대상으로 지정된 형식의 대리자를 만듭니다.</summary>
      <returns>이 메서드의 대리자입니다.</returns>
      <param name="delegateType">만들 대리자의 형식입니다.</param>
      <param name="target">대리자의 대상이 되는 개체입니다.</param>
    </member>
    <member name="M:System.Reflection.MethodInfo.Equals(System.Object)">
      <summary>@FSHO2@이 인스턴스가 지정한 개체와 같은지를 나타내는 값을 반환합니다.</summary>
      <returns>true가 이 인스턴스의 형식 및 값과 같으면 <paramref name="obj" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">이 인스턴스와 비교할 개체 또는 null입니다.</param>
    </member>
    <member name="M:System.Reflection.MethodInfo.GetGenericArguments">
      <summary>제네릭 메서드의 형식 인수나 제네릭 메서드 정의의 형식 매개 변수를 나타내는 <see cref="T:System.Type" /> 개체의 배열을 반환합니다.</summary>
      <returns>제네릭 메서드의 형식 인수나 제네릭 메서드 정의의 형식 매개 변수를 나타내는 <see cref="T:System.Type" /> 개체의 배열입니다.현재 메서드가 제네릭 메서드가 아니면 빈 배열을 반환합니다.</returns>
      <exception cref="T:System.NotSupportedException">이 메서드는 지원되지 않습니다.</exception>
    </member>
    <member name="M:System.Reflection.MethodInfo.GetGenericMethodDefinition">
      <summary>현재 메서드를 생성하는 데 사용할 수 있는 제네릭 메서드 정의를 나타내는 <see cref="T:System.Reflection.MethodInfo" /> 개체를 반환합니다.</summary>
      <returns>현재 메서드를 생성하는 데 사용할 수 있는 제네릭 메서드 정의를 나타내는 <see cref="T:System.Reflection.MethodInfo" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">현재 메서드가 제네릭 메서드가 아닌 경우.즉, <see cref="P:System.Reflection.MethodInfo.IsGenericMethod" />이 false를 반환하는 경우</exception>
      <exception cref="T:System.NotSupportedException">이 메서드는 지원되지 않습니다.</exception>
    </member>
    <member name="M:System.Reflection.MethodInfo.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 반환합니다.</summary>
      <returns>부호 있는 32비트 정수 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Reflection.MethodInfo.MakeGenericMethod(System.Type[])">
      <summary>현재 제네릭 메서드 정의의 형식 매개 변수를 형식 배열의 요소로 대체하고, 결과로 생성된 메서드를 나타내는 <see cref="T:System.Reflection.MethodInfo" /> 개체를 반환합니다.</summary>
      <returns>현재 제네릭 메서드 정의의 형식 매개 변수를 <paramref name="typeArguments" />의 요소로 대체하여 생성된 메서드를 나타내는 <see cref="T:System.Reflection.MethodInfo" /> 개체입니다.</returns>
      <param name="typeArguments">현재 제네릭 메서드 정의의 형식 매개 변수를 대체할 형식 배열입니다.</param>
      <exception cref="T:System.InvalidOperationException">현재 <see cref="T:System.Reflection.MethodInfo" />가 제네릭 메서드 정의를 나타내지 않는 경우.즉, <see cref="P:System.Reflection.MethodInfo.IsGenericMethodDefinition" />이 false를 반환하는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeArguments" />가 null인 경우또는 <paramref name="typeArguments" />의 요소가 null인 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="typeArguments" />의 요소 수가 현재 제네릭 메서드 정의의 형식 매개 변수 수와 같지 않은 경우또는 <paramref name="typeArguments" />의 요소가 현재 제네릭 메서드 정의의 해당 형식 매개 변수에 지정된 제약 조건을 충족하지 않는 경우 </exception>
      <exception cref="T:System.NotSupportedException">이 메서드는 지원되지 않습니다.</exception>
    </member>
    <member name="P:System.Reflection.MethodInfo.ReturnParameter">
      <summary>메서드의 반환 형식에 대한 정보(예: 반환 형식에 사용자 지정 한정자가 포함되는지 여부)가 포함된 <see cref="T:System.Reflection.ParameterInfo" /> 개체를 가져옵니다. </summary>
      <returns>반환 형식에 대한 정보가 포함된 <see cref="T:System.Reflection.ParameterInfo" /> 개체입니다.</returns>
      <exception cref="T:System.NotImplementedException">이 메서드가 구현되지 않은 경우</exception>
    </member>
    <member name="P:System.Reflection.MethodInfo.ReturnType">
      <summary>이 메서드의 반환 형식을 가져옵니다.</summary>
      <returns>이 메서드의 반환 형식입니다.</returns>
    </member>
    <member name="T:System.Reflection.Module">
      <summary>모듈에 리플렉션을 실행합니다.</summary>
    </member>
    <member name="P:System.Reflection.Module.Assembly">
      <summary>
        <see cref="T:System.Reflection.Module" />의 이 인스턴스에 적합한 <see cref="T:System.Reflection.Assembly" />를 가져옵니다.</summary>
      <returns>Assembly 개체</returns>
    </member>
    <member name="P:System.Reflection.Module.CustomAttributes">
      <summary>이 모듈의 사용자 지정 특성을 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>이 모듈의 사용자 지정 특성을 포함하는 컬렉션입니다.</returns>
    </member>
    <member name="M:System.Reflection.Module.Equals(System.Object)">
      <summary>이 모듈과 지정된 개체가 서로 같은지 여부를 확인합니다.</summary>
      <returns>
        <paramref name="o" />가 이 인스턴스와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">이 인스턴스와 비교할 개체입니다. </param>
    </member>
    <member name="P:System.Reflection.Module.FullyQualifiedName">
      <summary>이 모듈의 정규화된 이름과 경로를 나타내는 문자열을 가져옵니다.</summary>
      <returns>정규화된 모듈 이름입니다.</returns>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Reflection.Module.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 반환합니다.</summary>
      <returns>32비트 부호 있는 정수 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Reflection.Module.GetType(System.String,System.Boolean,System.Boolean)">
      <summary>모듈을 검색할 때 대/소문자를 구분할지 여부와 형식을 찾을 수 없을 때 예외를 throw할지 여부를 지정하여 지정된 형식을 반환합니다.</summary>
      <returns>지정된 형식이 이 모듈에 선언된 경우 해당 형식을 나타내는 <see cref="T:System.Type" /> 개체이고, 그렇지 않으면 null입니다.</returns>
      <param name="className">찾을 형식의 이름입니다.이름은 네임스페이스를 사용하여 정규화되어야 합니다.</param>
      <param name="throwOnError">형식을 찾을 수 없는 경우 예외를 throw하려면 true이고, null을 반환하려면 false입니다. </param>
      <param name="ignoreCase">대소문자를 구분하지 않고 검색하면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="className" />가 null입니다. </exception>
      <exception cref="T:System.Reflection.TargetInvocationException">클래스 이니셜라이저가 호출되고 예외가 throw되는 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="className" />이 길이가 0인 문자열인 경우 </exception>
      <exception cref="T:System.TypeLoadException">
        <paramref name="throwOnError" />가 true이고 형식을 찾을 수 없는 경우 </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="className" />에 찾을 수 없는 종속 어셈블리가 필요한 경우 </exception>
      <exception cref="T:System.IO.FileLoadException">
        <paramref name="className" />에 찾았지만 로드할 수 없는 종속 어셈블리가 필요한 경우또는현재 어셈블리를 리플렉션 전용 컨텍스트로 로드했으며 <paramref name="className" />에 미리 로드되지 않은 종속 어셈블리가 필요한 경우 </exception>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="className" />에 종속 어셈블리가 필요하지만 해당 파일이 올바른 어셈블리가 아닌 경우 또는<paramref name="className" />에 현재 로드된 버전보다 높은 버전의 런타임용으로 컴파일된 종속 어셈블리가 필요한 경우</exception>
    </member>
    <member name="P:System.Reflection.Module.Name">
      <summary>경로가 제거된 모듈의 이름을 나타내는 String을 가져옵니다.</summary>
      <returns>경로가 없는 모듈 이름입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Reflection.Module.ToString">
      <summary>모듈의 이름을 반환합니다.</summary>
      <returns>이 모듈의 이름을 나타내는 String입니다.</returns>
    </member>
    <member name="T:System.Reflection.ParameterInfo">
      <summary>매개 변수의 특성을 검색하고 매개 변수 메타데이터에 대한 액세스를 제공합니다.</summary>
    </member>
    <member name="P:System.Reflection.ParameterInfo.Attributes">
      <summary>이 매개 변수에 대한 특성을 가져옵니다</summary>
      <returns>이 매개 변수에 대한 특성을 나타내는 ParameterAttributes 개체입니다.</returns>
    </member>
    <member name="P:System.Reflection.ParameterInfo.CustomAttributes">
      <summary>이 매개 변수의 사용자 지정 특성을 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>이 매개 변수의 사용자 지정 특성을 포함하는 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.ParameterInfo.DefaultValue">
      <summary>매개 변수가 기본값을 가지면 기본값임을 나타내는 값을 가져옵니다.</summary>
      <returns>매개 변수가 기본값을 가지면 해당 기본값이고, 그렇지 않으면 <see cref="F:System.DBNull.Value" />입니다.</returns>
    </member>
    <member name="P:System.Reflection.ParameterInfo.HasDefaultValue">
      <summary>이 매개 변수에 기본값이 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이 매개 변수에 기본값이 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.ParameterInfo.IsIn">
      <summary>입력 매개 변수인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>매개 변수가 입력 매개 변수이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.ParameterInfo.IsOptional">
      <summary>이 매개 변수가 선택적 요소인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>매개 변수가 선택적 요소이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.ParameterInfo.IsOut">
      <summary>이 매개 변수가 출력 매개 변수인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>매개 변수가 출력 매개 변수이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.ParameterInfo.IsRetval">
      <summary>이 매개 변수가 Retval 매개 변수인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>매개 변수가 Retval이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.ParameterInfo.Member">
      <summary>매개 변수가 구현된 멤버를 나타내는 값을 가져옵니다.</summary>
      <returns>이 <see cref="T:System.Reflection.ParameterInfo" />로 표현된 매개 변수를 주입한 멤버입니다.</returns>
    </member>
    <member name="P:System.Reflection.ParameterInfo.Name">
      <summary>매개 변수의 이름을 가져옵니다.</summary>
      <returns>이 매개 변수의 단순한 이름입니다.</returns>
    </member>
    <member name="P:System.Reflection.ParameterInfo.ParameterType">
      <summary>이 매개 변수의 Type을 가져옵니다.</summary>
      <returns>이 매개 변수의 Type을 나타내는 Type 개체입니다.</returns>
    </member>
    <member name="P:System.Reflection.ParameterInfo.Position">
      <summary>정식 매개 변수 목록에서 0부터 시작하는 매개 변수의 위치를 가져옵니다.</summary>
      <returns>매개 변수 목록에서 이 매개 변수가 있는 위치를 나타내는 정수입니다.</returns>
    </member>
    <member name="T:System.Reflection.PropertyInfo">
      <summary>속성의 특성을 검색하고 속성 메타데이터에 대한 액세스를 제공합니다.</summary>
    </member>
    <member name="P:System.Reflection.PropertyInfo.Attributes">
      <summary>이 속성의 특성을 가져옵니다.</summary>
      <returns>이 속성의 특성입니다.</returns>
    </member>
    <member name="P:System.Reflection.PropertyInfo.CanRead">
      <summary>속성을 읽을 수 있는지를 나타내는 값을 가져옵니다.</summary>
      <returns>이 속성을 읽을 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.PropertyInfo.CanWrite">
      <summary>속성에 쓸 수 있는지를 나타내는 값을 가져옵니다.</summary>
      <returns>이 속성에 쓸 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Reflection.PropertyInfo.Equals(System.Object)">
      <summary>@FSHO2@이 인스턴스가 지정한 개체와 같은지를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="obj" />가 이 인스턴스의 형식 및 값과 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">이 인스턴스와 비교할 개체 또는 null입니다.</param>
    </member>
    <member name="M:System.Reflection.PropertyInfo.GetConstantValue">
      <summary>컴파일러에서 속성과 연결한 리터럴 값을 반환합니다. </summary>
      <returns>속성과 연결된 리터럴 값이 들어 있는 <see cref="T:System.Object" />입니다.리터럴 값이 요소 값 0인 클래스 형식인 경우 반환 값은 null입니다.</returns>
      <exception cref="T:System.InvalidOperationException">관리되지 않는 메타데이터의 Constant 테이블에 현재 속성에 대한 상수 값이 들어 있지 않은 경우</exception>
      <exception cref="T:System.FormatException">값 형식이 CLS(Common Language Specification)에서 허용되는 형식 중 하나가 아닌 경우.자세한 내용은 ECMA Partition II 사양, Metadata를 참조하십시오.</exception>
    </member>
    <member name="M:System.Reflection.PropertyInfo.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 반환합니다.</summary>
      <returns>부호 있는 32비트 정수 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Reflection.PropertyInfo.GetIndexParameters">
      <summary>파생 클래스에 재정의할 때 속성에 대한 모든 인덱스 매개 변수의 배열을 반환합니다.</summary>
      <returns>인덱스에 대한 매개 변수가 들어 있는 ParameterInfo 형식의 배열입니다.속성이 인덱싱되지 않은 경우 배열에는 요소가 없습니다.</returns>
    </member>
    <member name="P:System.Reflection.PropertyInfo.GetMethod">
      <summary>이 속성의 get 접근자를 가져옵니다.</summary>
      <returns>이 속성의 get 접근자</returns>
    </member>
    <member name="M:System.Reflection.PropertyInfo.GetValue(System.Object)">
      <summary>지정된 개체의 속성 값을 반환합니다.</summary>
      <returns>지정된 개체의 속성 값입니다.</returns>
      <param name="obj">속성 값이 반환될 개체입니다.</param>
    </member>
    <member name="M:System.Reflection.PropertyInfo.GetValue(System.Object,System.Object[])">
      <summary>인덱싱된 속성에 대해 선택적인 인덱스 값이 있는 지정된 개체의 속성 값을 반환합니다.</summary>
      <returns>지정된 개체의 속성 값입니다.</returns>
      <param name="obj">속성 값이 반환될 개체입니다. </param>
      <param name="index">인덱싱된 속성에 대한 선택적 인덱스 값입니다.인덱싱된 속성에 대한 인덱스는 0부터 시작합니다.인덱싱되지 않은 속성에 대해서는 이 값이 null이어야 합니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 배열에 필요한 인수의 형식이 포함되지 않은 경우또는 속성의 get 접근자가 없는 경우 </exception>
      <exception cref="T:System.Reflection.TargetException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 <see cref="T:System.Exception" />를 catch합니다.해당 개체가 대상 형식과 일치하지 않거나 해당 속성이 인스턴스 속성이지만 <paramref name="obj" />가 null인 경우 </exception>
      <exception cref="T:System.Reflection.TargetParameterCountException">
        <paramref name="index" />에 있는 매개 변수의 수가 인덱싱된 속성이 사용하는 매개 변수의 수와 일치하지 않는 경우 </exception>
      <exception cref="T:System.MethodAccessException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.MemberAccessException" />를 catch합니다.클래스 내부의 전용 또는 보호된 메서드에 잘못 액세스하려고 한 경우 </exception>
      <exception cref="T:System.Reflection.TargetInvocationException">속성 값을 검색하는 동안 오류가 발생한 경우예를 들어, 인덱싱된 속성에 대해 지정된 인덱스 값이 범위를 벗어난 경우가 해당됩니다.<see cref="P:System.Exception.InnerException" /> 속성은 오류에 대한 이유를 나타냅니다.</exception>
    </member>
    <member name="P:System.Reflection.PropertyInfo.IsSpecialName">
      <summary>속성의 이름이 특수한지를 나타내는 값을 가져옵니다.</summary>
      <returns>이 속성에 특수한 이름이 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Reflection.PropertyInfo.PropertyType">
      <summary>이 속성의 형식을 가져옵니다.</summary>
      <returns>이 속성의 형식입니다.</returns>
    </member>
    <member name="P:System.Reflection.PropertyInfo.SetMethod">
      <summary>이 속성의 set 접근자를 가져옵니다.</summary>
      <returns>set 이 속성에 대 한 접근자 또는 null 속성은 읽기 전용으로 설정 하는 경우.</returns>
    </member>
    <member name="M:System.Reflection.PropertyInfo.SetValue(System.Object,System.Object)">
      <summary>지정된 개체의 속성 값을 설정합니다.</summary>
      <param name="obj">속성 값이 설정될 개체입니다.</param>
      <param name="value">새 속성 값입니다.</param>
      <exception cref="T:System.ArgumentException">속성의 set 접근자가 없는 경우 또는<paramref name="value" />형식으로 변환할 수 없는 <see cref="P:System.Reflection.PropertyInfo.PropertyType" />. </exception>
      <exception cref="T:System.Reflection.TargetException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 <see cref="T:System.Exception" />를 catch합니다.유형의 <paramref name="obj" /> 대상 형식과 일치 하지 않습니다 속성은 인스턴스 속성 또는 하지만 <paramref name="obj" /> 은 null. </exception>
      <exception cref="T:System.MethodAccessException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.MemberAccessException" />를 catch합니다. 클래스 내부의 전용 또는 보호된 메서드에 잘못 액세스하려고 한 경우 </exception>
      <exception cref="T:System.Reflection.TargetInvocationException">속성 값을 설정하는 동안 오류가 발생한 경우<see cref="P:System.Exception.InnerException" /> 속성은 오류에 대한 이유를 나타냅니다.</exception>
    </member>
    <member name="M:System.Reflection.PropertyInfo.SetValue(System.Object,System.Object,System.Object[])">
      <summary>인덱스 속성에 대해 선택적인 인덱스 값이 있는 지정된 개체의 속성 값을 설정합니다.</summary>
      <param name="obj">속성 값이 설정될 개체입니다. </param>
      <param name="value">새 속성 값입니다. </param>
      <param name="index">인덱싱된 속성에 대한 선택적 인덱스 값입니다.인덱싱되지 않은 속성에 대해서는 이 값이 null이어야 합니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 배열에 필요한 인수의 형식이 포함되지 않은 경우또는 속성의 set 접근자가 없는 경우 또는<paramref name="value" />형식으로 변환할 수 없는 <see cref="P:System.Reflection.PropertyInfo.PropertyType" />.</exception>
      <exception cref="T:System.Reflection.TargetException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 <see cref="T:System.Exception" />를 catch합니다.해당 개체가 대상 형식과 일치하지 않거나 해당 속성이 인스턴스 속성이지만 <paramref name="obj" />가 null인 경우 </exception>
      <exception cref="T:System.Reflection.TargetParameterCountException">
        <paramref name="index" />에 있는 매개 변수의 수가 인덱싱된 속성이 사용하는 매개 변수의 수와 일치하지 않는 경우 </exception>
      <exception cref="T:System.MethodAccessException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.MemberAccessException" />를 catch합니다.클래스 내부의 전용 또는 보호된 메서드에 잘못 액세스하려고 한 경우 </exception>
      <exception cref="T:System.Reflection.TargetInvocationException">속성 값을 설정하는 동안 오류가 발생한 경우예를 들어, 인덱싱된 속성에 대해 지정된 인덱스 값이 범위를 벗어난 경우가 해당됩니다.<see cref="P:System.Exception.InnerException" /> 속성은 오류에 대한 이유를 나타냅니다.</exception>
    </member>
    <member name="T:System.Reflection.ReflectionContext">
      <summary>리플렉션 개체에 제공할 수 있는 컨텍스트를 나타냅니다.</summary>
    </member>
    <member name="M:System.Reflection.ReflectionContext.#ctor">
      <summary>
        <see cref="T:System.Reflection.ReflectionContext" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Reflection.ReflectionContext.GetTypeForObject(System.Object)">
      <summary>이 리플렉션 컨텍스트에 있는 지정된 개체의 형식의 표현을 가져옵니다.</summary>
      <returns>지정된 개체의 형식을 나타내는 개체입니다.</returns>
      <param name="value">나타낼 개체입니다.</param>
    </member>
    <member name="M:System.Reflection.ReflectionContext.MapAssembly(System.Reflection.Assembly)">
      <summary>다른 리플렉션 컨텍스트의 개체가 나타내는 어셈블리의 표현을 이 리플렉션 컨텍스트에서 가져옵니다.</summary>
      <returns>이 리플렉션 컨텍스트의 어셈블리 표현입니다.</returns>
      <param name="assembly">이 컨텍스트에서 표시할 어셈블리의 외부 표현입니다.</param>
    </member>
    <member name="M:System.Reflection.ReflectionContext.MapType(System.Reflection.TypeInfo)">
      <summary>다른 리플렉션 컨텍스트의 개체가 나타내는 형식의 표현을 이 리플렉션 컨텍스트에서 가져옵니다.</summary>
      <returns>이 리플렉션 컨텍스트의 형식 표현입니다.</returns>
      <param name="type">이 컨텍스트에서 표시할 형식의 외부 표현입니다.</param>
    </member>
    <member name="T:System.Reflection.ReflectionTypeLoadException">
      <summary>모듈에 있는 클래스를 로드할 수 없는 경우 <see cref="M:System.Reflection.Module.GetTypes" /> 메서드에서 throw되는 예외입니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Reflection.ReflectionTypeLoadException.#ctor(System.Type[],System.Exception[])">
      <summary>지정된 클래스 및 이와 관련된 예외를 사용하여 <see cref="T:System.Reflection.ReflectionTypeLoadException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="classes">모듈에서 정의되어 로드된 클래스를 포함하는 Type 형식 배열입니다.이 배열은 null 참조(Visual Basic에서는 Nothing) 값을 포함할 수 있습니다.</param>
      <param name="exceptions">클래스 로더에서 throw된 예외를 포함하는 Exception 형식의 배열입니다.<paramref name="classes" /> 배열의 null 참조(Visual Basic에서는 Nothing) 값은 이 <paramref name="exceptions" /> 배열의 예외와 일치합니다.</param>
    </member>
    <member name="M:System.Reflection.ReflectionTypeLoadException.#ctor(System.Type[],System.Exception[],System.String)">
      <summary>지정된 클래스, 관련 예외 및 그에 대한 설명을 사용하여 <see cref="T:System.Reflection.ReflectionTypeLoadException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="classes">모듈에서 정의되어 로드된 클래스를 포함하는 Type 형식 배열입니다.이 배열은 null 참조(Visual Basic에서는 Nothing) 값을 포함할 수 있습니다.</param>
      <param name="exceptions">클래스 로더에서 throw된 예외를 포함하는 Exception 형식의 배열입니다.<paramref name="classes" /> 배열의 null 참조(Visual Basic에서는 Nothing) 값은 이 <paramref name="exceptions" /> 배열의 예외와 일치합니다.</param>
      <param name="message">에외가 throw된 원인을 설명하는 String입니다. </param>
    </member>
    <member name="P:System.Reflection.ReflectionTypeLoadException.LoaderExceptions">
      <summary>클래스 로더에서 throw된 예외의 배열을 가져옵니다.</summary>
      <returns>클래스 로더에서 throw된 예외를 포함하는 Exception 형식의 배열입니다.이 인스턴스의 <paramref name="classes" /> 배열에 있는 null 값은 이 배열의 예외와 평행합니다.</returns>
    </member>
    <member name="P:System.Reflection.ReflectionTypeLoadException.Types">
      <summary>모듈에서 정의되고 로드된 클래스의 배열을 가져옵니다.</summary>
      <returns>모듈에서 정의되어 로드된 클래스를 포함하는 Type 형식 배열입니다.이 배열은 null 값을 포함할 수 있습니다.</returns>
    </member>
    <member name="T:System.Reflection.ResourceLocation">
      <summary>리소스 위치를 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.ResourceLocation.ContainedInAnotherAssembly">
      <summary>리소스가 다른 어셈블리에 포함되도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.ResourceLocation.ContainedInManifestFile">
      <summary>리소스가 매니페스트 파일에 포함되도록 지정합니다.</summary>
    </member>
    <member name="F:System.Reflection.ResourceLocation.Embedded">
      <summary>포함된 즉, 링크되지 않은 리소스를 지정합니다.</summary>
    </member>
    <member name="T:System.Reflection.TargetInvocationException">
      <summary>리플렉션을 통해 호출된 메서드에서 throw된 예외입니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Reflection.TargetInvocationException.#ctor(System.Exception)">
      <summary>이 예외의 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Reflection.TargetInvocationException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="M:System.Reflection.TargetInvocationException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Reflection.TargetInvocationException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="T:System.Reflection.TargetParameterCountException">
      <summary>호출에 사용하는 매개 변수의 개수가 예상했던 개수와 일치하지 않을 때 throw된 예외입니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Reflection.TargetParameterCountException.#ctor">
      <summary>빈 메시지 문자열과 예외의 근본 원인을 사용하여 <see cref="T:System.Reflection.TargetParameterCountException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Reflection.TargetParameterCountException.#ctor(System.String)">
      <summary>지정된 메시지로 설정된 메시지 문자열과 근본 원인 예외를 사용하여 <see cref="T:System.Reflection.TargetParameterCountException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">이 예외가 throw된 원인을 설명하는 String입니다. </param>
    </member>
    <member name="M:System.Reflection.TargetParameterCountException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Reflection.TargetParameterCountException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="T:System.Reflection.TypeInfo">
      <summary>클래스 형식, 인터페이스 형식, 배열 형식, 값 형식, 열거형 형식, 형식 매개 변수, 제네릭 형식 정의 및 개방형 생성 제네릭 형식이나 폐쇄형 생성 제네릭 형식에 대한 형식 선언을 나타냅니다. </summary>
    </member>
    <member name="P:System.Reflection.TypeInfo.Assembly"></member>
    <member name="P:System.Reflection.TypeInfo.AssemblyQualifiedName"></member>
    <member name="M:System.Reflection.TypeInfo.AsType">
      <summary>현재 형식을 <see cref="T:System.Type" /> 개체로 반환합니다.</summary>
      <returns>현재 형식입니다.</returns>
    </member>
    <member name="P:System.Reflection.TypeInfo.Attributes"></member>
    <member name="P:System.Reflection.TypeInfo.BaseType"></member>
    <member name="P:System.Reflection.TypeInfo.ContainsGenericParameters"></member>
    <member name="P:System.Reflection.TypeInfo.DeclaredConstructors">
      <summary>현재 형식이 선언하는 생성자의 컬렉션을 가져옵니다.</summary>
      <returns>현재 형식이 선언하는 생성자의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.TypeInfo.DeclaredEvents">
      <summary>현재 형식이 정의하는 이벤트의 컬렉션을 가져옵니다.</summary>
      <returns>현재 형식이 정의하는 이벤트의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.TypeInfo.DeclaredFields">
      <summary>현재 형식이 정의하는 필드의 컬렉션을 가져옵니다.</summary>
      <returns>현재 형식이 정의하는 필드의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.TypeInfo.DeclaredMembers">
      <summary>현재 형식이 정의하는 멤버의 컬렉션을 가져옵니다.</summary>
      <returns>현재 형식이 정의하는 멤버의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.TypeInfo.DeclaredMethods">
      <summary>현재 형식이 정의하는 메서드의 컬렉션을 가져옵니다.</summary>
      <returns>현재 형식이 정의하는 메서드의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.TypeInfo.DeclaredNestedTypes">
      <summary>현재 형식이 정의하는 중첩 형식의 컬렉션을 가져옵니다.</summary>
      <returns>현재 형식이 정의하는 중첩 형식의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.TypeInfo.DeclaredProperties">
      <summary>현재 형식이 정의하는 속성의 컬렉션을 가져옵니다. </summary>
      <returns>현재 형식이 정의하는 속성의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.TypeInfo.DeclaringMethod"></member>
    <member name="P:System.Reflection.TypeInfo.FullName"></member>
    <member name="P:System.Reflection.TypeInfo.GenericParameterAttributes"></member>
    <member name="P:System.Reflection.TypeInfo.GenericParameterPosition"></member>
    <member name="P:System.Reflection.TypeInfo.GenericTypeArguments"></member>
    <member name="P:System.Reflection.TypeInfo.GenericTypeParameters">
      <summary>현재 인스턴스의 제네릭 형식 매개 변수 배열을 가져옵니다. </summary>
      <returns>현재 인스턴스의 제네릭 형식 매개 변수를 포함 하는 배열 또는 현재 인스턴스에 제네릭 형식 매개 변수가 없는 경우 <see cref="P:System.Array.Length" /> 0의 배열입니다. </returns>
    </member>
    <member name="M:System.Reflection.TypeInfo.GetArrayRank"></member>
    <member name="M:System.Reflection.TypeInfo.GetDeclaredEvent(System.String)">
      <summary>현재 형식에서 선언한 지정된 공용 이벤트를 나타내는 개체를 반환합니다.</summary>
      <returns>해당 항목이 있으면 지정된 이벤트를 나타내는 개체이며, 그렇지 않으면 null입니다.</returns>
      <param name="name">이벤트의 이름입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null인 경우 </exception>
    </member>
    <member name="M:System.Reflection.TypeInfo.GetDeclaredField(System.String)">
      <summary>현재 형식에서 선언한 지정된 공용 필드를 나타내는 개체를 반환합니다.</summary>
      <returns>해당 항목이 있으면 지정된 필드를 나타내는 개체이며, 그렇지 않으면 null입니다.</returns>
      <param name="name">@FSHO2@필드의 이름입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null인 경우 </exception>
    </member>
    <member name="M:System.Reflection.TypeInfo.GetDeclaredMethod(System.String)">
      <summary>현재 형식에서 선언한 지정된 공용 메서드를 나타내는 개체를 반환합니다.</summary>
      <returns>해당 항목이 있으면 지정된 메서드를 나타내는 개체이며, 그렇지 않으면 null입니다.</returns>
      <param name="name">메서드의 이름입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null인 경우 </exception>
    </member>
    <member name="M:System.Reflection.TypeInfo.GetDeclaredMethods(System.String)">
      <summary>지정된 이름과 일치하며 현재 형식에 선언된 모든 공용 메서드를 포함하는 컬렉션을 반환합니다.</summary>
      <returns>
        <paramref name="name" />과 일치하는 메서드를 포함하는 컬렉션입니다.</returns>
      <param name="name">검색할 메서드 이름입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null인 경우 </exception>
    </member>
    <member name="M:System.Reflection.TypeInfo.GetDeclaredNestedType(System.String)">
      <summary>현재 형식에서 선언한 지정된 공용 중첩 형식을 나타내는 개체를 반환합니다.</summary>
      <returns>해당 항목이 있으면 지정된 중첩 형식을 나타내는 개체이며, 그렇지 않으면 null입니다.</returns>
      <param name="name">중첩 형식의 이름입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null인 경우 </exception>
    </member>
    <member name="M:System.Reflection.TypeInfo.GetDeclaredProperty(System.String)">
      <summary>현재 형식에서 선언한 지정된 공용 속성을 나타내는 개체를 반환합니다.</summary>
      <returns>해당 항목이 있으면 지정된 속성을 나타내는 개체이며, 그렇지 않으면 null입니다.</returns>
      <param name="name">속성의 이름입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null인 경우 </exception>
    </member>
    <member name="M:System.Reflection.TypeInfo.GetElementType"></member>
    <member name="M:System.Reflection.TypeInfo.GetGenericParameterConstraints"></member>
    <member name="M:System.Reflection.TypeInfo.GetGenericTypeDefinition"></member>
    <member name="P:System.Reflection.TypeInfo.GUID"></member>
    <member name="P:System.Reflection.TypeInfo.HasElementType"></member>
    <member name="P:System.Reflection.TypeInfo.ImplementedInterfaces">
      <summary>현재 형식에 의해 구현된 인터페이스의 컬렉션을 가져옵니다.</summary>
      <returns>현재 형식에 의해 구현된 인터페이스의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Reflection.TypeInfo.IsAbstract"></member>
    <member name="P:System.Reflection.TypeInfo.IsAnsiClass"></member>
    <member name="P:System.Reflection.TypeInfo.IsArray"></member>
    <member name="M:System.Reflection.TypeInfo.IsAssignableFrom(System.Reflection.TypeInfo)">
      <summary>지정한 형식을 현재 형식으로 할당할 수 있는지를 나타내는 값을 반환합니다.</summary>
      <returns>지정된 형식을 이 형식에 할당할 수 있으면 true이고, 할당할 수 없으면 false입니다.</returns>
      <param name="typeInfo">확인할 형식입니다.</param>
    </member>
    <member name="P:System.Reflection.TypeInfo.IsAutoClass"></member>
    <member name="P:System.Reflection.TypeInfo.IsAutoLayout"></member>
    <member name="P:System.Reflection.TypeInfo.IsByRef"></member>
    <member name="P:System.Reflection.TypeInfo.IsClass"></member>
    <member name="P:System.Reflection.TypeInfo.IsEnum"></member>
    <member name="P:System.Reflection.TypeInfo.IsExplicitLayout"></member>
    <member name="P:System.Reflection.TypeInfo.IsGenericParameter"></member>
    <member name="P:System.Reflection.TypeInfo.IsGenericType"></member>
    <member name="P:System.Reflection.TypeInfo.IsGenericTypeDefinition"></member>
    <member name="P:System.Reflection.TypeInfo.IsImport"></member>
    <member name="P:System.Reflection.TypeInfo.IsInterface"></member>
    <member name="P:System.Reflection.TypeInfo.IsLayoutSequential"></member>
    <member name="P:System.Reflection.TypeInfo.IsMarshalByRef"></member>
    <member name="P:System.Reflection.TypeInfo.IsNested"></member>
    <member name="P:System.Reflection.TypeInfo.IsNestedAssembly"></member>
    <member name="P:System.Reflection.TypeInfo.IsNestedFamANDAssem"></member>
    <member name="P:System.Reflection.TypeInfo.IsNestedFamily"></member>
    <member name="P:System.Reflection.TypeInfo.IsNestedFamORAssem"></member>
    <member name="P:System.Reflection.TypeInfo.IsNestedPrivate"></member>
    <member name="P:System.Reflection.TypeInfo.IsNestedPublic"></member>
    <member name="P:System.Reflection.TypeInfo.IsNotPublic"></member>
    <member name="P:System.Reflection.TypeInfo.IsPointer"></member>
    <member name="P:System.Reflection.TypeInfo.IsPrimitive"></member>
    <member name="P:System.Reflection.TypeInfo.IsPublic"></member>
    <member name="P:System.Reflection.TypeInfo.IsSealed"></member>
    <member name="P:System.Reflection.TypeInfo.IsSerializable"></member>
    <member name="P:System.Reflection.TypeInfo.IsSpecialName"></member>
    <member name="M:System.Reflection.TypeInfo.IsSubclassOf(System.Type)"></member>
    <member name="P:System.Reflection.TypeInfo.IsUnicodeClass"></member>
    <member name="P:System.Reflection.TypeInfo.IsValueType"></member>
    <member name="P:System.Reflection.TypeInfo.IsVisible"></member>
    <member name="M:System.Reflection.TypeInfo.MakeArrayType"></member>
    <member name="M:System.Reflection.TypeInfo.MakeArrayType(System.Int32)"></member>
    <member name="M:System.Reflection.TypeInfo.MakeByRefType"></member>
    <member name="M:System.Reflection.TypeInfo.MakeGenericType(System.Type[])"></member>
    <member name="M:System.Reflection.TypeInfo.MakePointerType"></member>
    <member name="P:System.Reflection.TypeInfo.Namespace"></member>
    <member name="M:System.Reflection.TypeInfo.System#Reflection#IReflectableType#GetTypeInfo">
      <summary>현재 형식의 표현을 <see cref="T:System.Reflection.TypeInfo" /> 개체로 반환합니다.</summary>
      <returns>현재 형식에 대한 참조입니다.</returns>
    </member>
  </members>
</doc>