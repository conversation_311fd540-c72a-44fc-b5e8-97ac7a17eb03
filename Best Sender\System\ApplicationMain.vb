Imports System.Threading
Imports System.Windows.Forms

''' <summary>
''' Custom application entry point with STA threading for ActiveX controls
''' </summary>
Module ApplicationMain

    ''' <summary>
    ''' Main entry point of the application with STA thread attribute
    ''' This is required for ActiveX controls like WebBrowser to work properly
    ''' </summary>
    <STAThread>
    Public Sub Main()
        Try
            ' Set the thread apartment state to STA for ActiveX controls
            If Thread.CurrentThread.GetApartmentState() <> ApartmentState.STA Then
                Thread.CurrentThread.SetApartmentState(ApartmentState.STA)
            End If

            ' Enable visual styles
            Application.EnableVisualStyles()
            Application.SetCompatibleTextRenderingDefault(False)

            ' Set security protocol for HTTPS connections
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12

            ' Start the VB.NET Application Framework
            My.Application.Run(Environment.GetCommandLineArgs())

        Catch ex As Exception
            ' Handle any startup errors
            MessageBox.Show($"Application startup error: {ex.Message}", "Startup Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

End Module
