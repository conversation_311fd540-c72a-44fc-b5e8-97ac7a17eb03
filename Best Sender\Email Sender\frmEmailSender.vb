﻿Imports System.ComponentModel
Imports System.IO
Imports System.Net
Imports System.Net.Mail
Imports System.Net.Mime
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Threading
Imports System.Windows
Imports System.Windows.Controls
Imports CoreHtmlToImage
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Base
Imports System.Linq
Imports System.Diagnostics
Imports System.Net.Sockets
Imports DevExpress.XtraGrid.Views.Grid
Imports MailKit.Net.Imap
Imports MessagingToolkit.QRCode.Codec
Imports DevExpress.XtraLayout
Imports DevExpress.XtraBars.Ribbon
Imports DevExpress.XtraBars
Imports DevExpress.XtraBars.Docking2010
Imports DevExpress.XtraBars.Docking2010.Views.Tabbed
Imports DevExpress.XtraBars.Commands
Imports System.Threading.Tasks
Imports DevExpress.XtraVerticalGrid

' تم إزالة الفئات الجزئية لأننا نستخدم الطريقة التقليدية للتحقق من SMTP
Public Class frmEmailSender
    Dim urlPattern As String = "^(https?://)?(www\.)?[\w-]+(\.[a-z]{2,})+(/[^\s]*)?$"
    Dim urlRegex As New Regex(urlPattern, RegexOptions.IgnoreCase)
    Dim ResizedImage As Image
    Dim PROGRESS As Integer = 0
    Dim CheckAll As Boolean = False
    Dim myValue As Boolean = False
    Dim _lock As Object = New Object()
    Dim isLogoRemoved As Integer = -1
    Dim totalMailsLabel As Integer = 0
    Dim totalstmpLabel As Integer = 0
    Private docManager As DocumentManager
    Private notificationIcon As BarStaticItem
    Dim Child_frmEditLetter As New frmEditLetter
    '===============================================
    ' تعريف الروبين علشان تتحرك ما التابات
    Public Property RelatedRibbonPage As RibbonPage
    '===============================================
#Region "User Defined"
    Private Function getImage(domain As String) As System.IO.Stream
        Dim _WebRequest As System.Net.WebRequest = Nothing
        Dim _NormalImage As System.IO.Stream = Nothing
TryAgain_:
        Try
            _WebRequest = WebRequest.Create("https://www.google.com/s2/favicons?sz=64&domain_url=" + domain)
        Catch ex As Exception
            ''Windows.Forms.MessageBox.Show(ex.Message)
            Return Nothing
        End Try
        Try
            _NormalImage = _WebRequest.GetResponse().GetResponseStream()
        Catch ex As Exception
            If ex.Message.Contains("404") Then
                domain = domain.Replace("www.", "")
                domain = "www." + domain.Substring(domain.IndexOf(".") + 1)
                Dim count = domain.Split(".").Length - 1
                If count = 1 Then Return Nothing Else GoTo TryAgain_
            End If
            Return Nothing
        End Try
        Return _NormalImage
    End Function
#End Region
    Dim isRunning_ As Boolean = False
    Dim isStopped_ As Boolean = False
    Dim Letter As Boolean = False
    Dim Tick_ As Integer = 0
    Dim tt_ As System.Windows.Forms.ToolTip
    Dim txtattachTextGlobal As String
    Dim filepath As String = ""
    Dim TempPath As String = My.Computer.FileSystem.SpecialDirectories.Temp & "\MailAttachmentFiles"
    Dim cancellationTokenSource As New CancellationTokenSource()
    Dim successNum As Integer = 0
    Dim failNum As Integer = 0
    ReadOnly FunctionList As List(Of String) = New List(Of String) From {"[-Email64-]", "[-Email-]", "[-IP-]", "[-IPChina-]", "[-Domain-]", "[-Name-]", "[-Date-]", "[-DateTomorrow-]",
        "[-RandomBrowser-]", "[-RCountry-]", "[-FakePhone-]", "[-FakeEmail-]", "[-NewYork-]", "[-UCase-]", "[-Link-]", "[-Logo-]", "[-RN1-]", "[-RN2-]", "[-RN3-]", "[-RN4-]",
        "[-RN5-]", "[-RN6-]", "[-QRCode-]", "[-CompanyName-]", "[-Time-]", "[-RN7-]", "[-RN8-]", "[-RN9-]", "[-RN10-]", "[-RN12-]", "[-RCh1-]", "[-RCh2-]", "[-RCh3-]", "[-RCh4-]", "[-RCh5-]", "[-RCh6-]", "[-RCh7-]", "[-RCh8-]"}
    Private Sub trmfadein_Tick(sender As Object, e As EventArgs) Handles trmfadein.Tick
        Me.Opacity = Me.Opacity + 0.02
        If Me.Opacity = 1 Then
            Me.Opacity = 98 / 100
            trmfadein.Enabled = False
            trmfadein.Stop()
        End If
    End Sub
    Private Sub trmfadeout_Tick(sender As Object, e As EventArgs) Handles trmfadeout.Tick
        Me.Opacity = Me.Opacity - 0.02
        If Me.Opacity < 0.001 Then
            Me.Dispose()
        End If
    End Sub
    'Sub RESIZEIMAGE()
    '    If Pic_QRCode.Image Is Nothing Then Exit Sub
    '    Pic_QRCode.Image = Image.FromFile(Pic_QRCode.Tag.ToString())
    '    Dim NewSize As New System.Drawing.Size(Convert.ToInt16(txtWidth.Text), Convert.ToInt16(txtHeight.Text))
    '    ResizedImage = New Bitmap(Pic_QRCode.Image, NewSize)
    '    Pic_QRCode.Image = ResizedImage
    '    Dim NSize As Integer = Convert.ToInt16(txtWidth.Text) + Convert.ToInt16(txtHeight.Text)
    '    If NSize < Me.Tag Then
    '        Pic_QRCode.SizeMode = PictureBoxSizeMode.CenterImage
    '    Else
    '        Pic_QRCode.SizeMode = PictureBoxSizeMode.StretchImage
    '    End If
    'End Sub
    'Private Sub txtWidth_TextChanged(sender As Object, e As EventArgs) Handles txtWidth.TextChanged
    '    If Val(txtWidth.Text) > 0 And Val(txtHeight.Text) > 0 Then RESIZEIMAGE()
    'End Sub
    'Private Sub txtHeight_TextChanged(sender As Object, e As EventArgs) Handles txtHeight.TextChanged
    '    If Val(txtWidth.Text) > 0 And Val(txtHeight.Text) > 0 Then RESIZEIMAGE()
    'End Sub
    Private Sub BunifuImageButton7_Click(sender As Object, e As EventArgs)
        Try
            ' حفظ الإعدادات
            My.Settings.LETTER = txtLetter.Text
            My.Settings.LETTER_LINK = txtNewLink.Text
            My.Settings.LETTER_CON_LINK = txt_LetterConvertorLink.Text
            My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
            My.Settings.EMAILS_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderMail)
            ' تشغيل المهمة في الخلفية لحساب MD5 وتحديث الإعدادات
            System.Threading.Tasks.Task.Run(Sub()
                                                Try
                                                    Dim mdf As String = FuncSendMail.GetSettingsMd5()
                                                    If My.Settings.SETTING_MDF <> mdf Then
                                                        My.Settings.SETTING_MDF = mdf
                                                        My.Settings.Save()
                                                    End If
                                                Catch ex As Exception
                                                    ' يمكنك تسجيل الخطأ إذا لزم الأمر
                                                End Try
                                            End Sub)
            ' مسح القوائم
            LS_SenderMail.Clear()
            LS_SenderSmtp.Clear()
            ' تحديث صورة Pic_Sender في frmMainMenu قبل الإغلاق
            Dim mainMenu As frmMain = Nothing
            ' البحث عن frmMainMenu إذا كان مفتوحًا (استخدام OpenForms بشكل صحيح)
            For Each frm As Form In System.Windows.Forms.Application.OpenForms
                If TypeOf frm Is frmMain Then
                    mainMenu = DirectCast(frm, frmMain)
                    Exit For
                End If
            Next
            ' إذا كان موجودًا، قم بتحديث صورة Pic_Sender بأمان
            If mainMenu IsNot Nothing Then
                If mainMenu.InvokeRequired Then
                    mainMenu.Invoke(New MethodInvoker(Sub()
                                                      End Sub))
                Else
                End If
            End If
            ' إغلاق النموذج الحالي
            Me.Dispose()
        Catch ex As Exception
            'MessageBox.Show("An error occurred while saving or updating: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    ' مدير البروكسيات
    Public _proxyManager As Object

    ''' <summary>
    ''' فئة تمثل عنصر بروكسي
    ''' </summary>
    Private Class ProxyItem
        ''' <summary>
        ''' عنوان البروكسي (IP:Port)
        ''' </summary>
        Public Property Proxy As String

        ''' <summary>
        ''' حالة البروكسي
        ''' </summary>
        Public Property Status As ProxyStatus

        ''' <summary>
        ''' البريد الأخير الذي تم إرساله باستخدام هذا البروكسي
        ''' </summary>
        Public Property LastUsedEmail As String

        ''' <summary>
        ''' عدد البرائد المرسلة باستخدام هذا البروكسي
        ''' </summary>
        Public Property EmailCount As Integer

        ''' <summary>
        ''' تاريخ آخر استخدام للبروكسي
        ''' </summary>
        Public Property LastUsedDate As DateTime

        Public Sub New()
            Status = ProxyStatus.Pending
            LastUsedEmail = ""
            EmailCount = 0
            LastUsedDate = DateTime.MinValue
        End Sub

        Public Sub New(proxyString As String)
            Proxy = proxyString
            Status = ProxyStatus.Pending
            LastUsedEmail = ""
            EmailCount = 0
            LastUsedDate = DateTime.MinValue
        End Sub
    End Class

    ''' <summary>
    ''' WebClient مخصص مع دعم Timeout - حل خبير شبكات
    ''' </summary>
    Private Class TimeoutWebClient
        Inherits WebClient

        Private _timeout As Integer

        Public Sub New(timeout As Integer)
            _timeout = timeout
        End Sub

        Protected Overrides Function GetWebRequest(uri As Uri) As WebRequest
            Dim request As WebRequest = MyBase.GetWebRequest(uri)
            request.Timeout = _timeout
            Return request
        End Function
    End Class

    ''' <summary>
    ''' تعداد يمثل حالة البروكسي
    ''' </summary>
    Private Enum ProxyStatus
        ''' <summary>
        ''' في انتظار الفحص
        ''' </summary>
        Pending

        ''' <summary>
        ''' صالح
        ''' </summary>
        Valid

        ''' <summary>
        ''' غير صالح
        ''' </summary>
        NotWorking
    End Enum

    ' قائمة البروكسيات
    Private _proxyList As New List(Of ProxyItem)

    ' الصور المستخدمة لحالة البروكسي
    Private _pendingImage As System.Drawing.Image
    Private _validImage As System.Drawing.Image
    Private _notWorkingImage As System.Drawing.Image

    ' مؤشر لاستخدام البروكسي في الإرسال
    Private _useProxyForSending As Boolean = False

    ' مؤشر الصف الحالي للبروكسي المستخدم في الإرسال
    Private _currentProxyIndex As Integer = 0

    ' متغير للتحكم في الحذف التلقائي للبروكسيات غير الصالحة
    Private _autoRemoveInvalidProxies As Boolean = False

    ' متغير لتتبع البروكسي المستخدم حالي<|im_start|> في الإرسال
    Private _currentUsedProxyAddress As String = ""

    ' BackgroundWorker لفحص البروكسيات
    Private _proxyCheckWorker As BackgroundWorker



    Private Sub frmEmailSender_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' تهيئة ToolTip
        If tt_ Is Nothing Then
            tt_ = New System.Windows.Forms.ToolTip()
        End If

        ' تهيئة تمركز XtraTabControl2 داخل XtraTabControl1
        InitializeCenteringControls()

        ' تهيئة مدير البروكسيات
        Try
            ' تحميل الصور من الموارد
            _pendingImage = DirectCast(My.Resources.pending16x16, System.Drawing.Image)
            _validImage = DirectCast(My.Resources.Valid16x16, System.Drawing.Image)
            _notWorkingImage = DirectCast(My.Resources.Notworking16x16, System.Drawing.Image)

            ' تهيئة PopupMenu5 للبروكسي
            InitializePopupMenu5()

            ' تهيئة PopupMenu6 للبروكسي (الجديد)
            InitializePopupMenu6()

            ' تهيئة BackgroundWorker للبروكسي
            InitializeProxyCheckWorker()

            ' تهيئة زر إيقاف الفحص
            InitializeBntStopCheckProxy()

            ' اختبار PopupMenu6 بعد التهيئة
            TestPopupMenu6()

            ' تهيئة GridControl3
            InitializeProxyGridControl()

            ' إضافة معالجات الأحداث للبروكسي
            AddProxyEventHandlers()

            ' تطبيق الألوان الجديدة على أزرار البروكسي
            ApplyNewButtonColors()
        Catch ex As Exception
            MessageBox.Show("Error initializing proxy manager: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        '==============================
        txtLetter.Text = EncoderVariables.EncodedLetter
        Try
            ' ✅ استرجاع قائمة SMTP إذا كانت موجودة
            Dim smtpCachedData = FuncSendMail.DeserializeXmlToList(Of SenderSmtpSettings)(My.Settings.SMTP_DATA_TABLE_XML)
            If smtpCachedData IsNot Nothing AndAlso smtpCachedData.Count > 0 Then
                If LS_SenderSmtp.Count > 0 Then
                    smtpCachedData.ForEach(Sub(x)
                                               If LS_SenderSmtp.All(Function(c) x.smtpemail <> c.smtpemail AndAlso x.smtppassword <> c.smtppassword AndAlso x.smtphost <> c.smtphost AndAlso x.smtpport <> c.smtpport AndAlso x.smtpssl <> c.smtpssl) Then
                                                   LS_SenderSmtp.Add(x)
                                               End If
                                           End Sub)
                Else
                    LS_SenderSmtp = smtpCachedData
                End If
                lbltotalstmp.Text = $"Total Smtps: {LS_SenderSmtp.Count}"
                GridControl1.RefreshDataSource()
            End If
            ' ✅ استرجاع قائمة البريد الإلكتروني إذا كانت موجودة
            Dim emailsCachedData = FuncSendMail.DeserializeXmlToList(Of SenderMailItem)(My.Settings.EMAILS_DATA_TABLE_XML)
            If emailsCachedData IsNot Nothing AndAlso emailsCachedData.Count > 0 Then
                If LS_SenderMail.Count > 0 Then
                    emailsCachedData.ForEach(Sub(x)
                                                 If LS_SenderMail.All(Function(c) x.emailaddress <> c.emailaddress) Then
                                                     LS_SenderMail.Add(x)
                                                 End If
                                             End Sub)
                Else
                    LS_SenderMail = emailsCachedData
                End If
                lblTotal.Text = $"Total Mails: {LS_SenderMail.Count}"
                GridControl2.RefreshDataSource()
            End If
            ' ✅ تحديث مصادر البيانات
            GridControl1.DataSource = LS_SenderSmtp
            GridControl2.DataSource = LS_SenderMail

            ' Set Binance-inspired appearance for GridControl1
            ' Main background color - dark gray/black (RGB 18, 22, 28)
            GridView1.Appearance.Empty.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.Row.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.FocusedRow.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.SelectedRow.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.HideSelectionRow.BackColor = Color.FromArgb(18, 22, 28)

            ' Set text color to white
            GridView1.Appearance.Row.ForeColor = Color.FromArgb(255, 255, 255)
            GridView1.Appearance.Row.Options.UseForeColor = True

            ' Configure alternate row color - darker gray (RGB 30, 35, 41)
            GridView1.OptionsView.EnableAppearanceEvenRow = True
            GridView1.OptionsView.EnableAppearanceOddRow = True
            GridView1.Appearance.EvenRow.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.EvenRow.Options.UseBackColor = True
            GridView1.Appearance.OddRow.BackColor = Color.FromArgb(30, 35, 41)
            GridView1.Appearance.OddRow.Options.UseBackColor = True

            ' Configure header panel appearance
            GridView1.Appearance.HeaderPanel.BackColor = Color.FromArgb(18, 22, 28)
            GridView1.Appearance.HeaderPanel.ForeColor = Color.FromArgb(160, 174, 192)
            GridView1.Appearance.HeaderPanel.Font = New Font(GridView1.Appearance.HeaderPanel.Font.FontFamily, GridView1.Appearance.HeaderPanel.Font.Size, System.Drawing.FontStyle.Bold)
            GridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            GridView1.Appearance.HeaderPanel.Options.UseBackColor = True
            GridView1.Appearance.HeaderPanel.Options.UseForeColor = True
            GridView1.Appearance.HeaderPanel.Options.UseFont = True
            GridView1.Appearance.HeaderPanel.Options.UseTextOptions = True

            ' Configure hover row appearance - RGB(44, 47, 54)
            GridView1.Appearance.HotTrackedRow.BackColor = Color.FromArgb(44, 47, 54)
            GridView1.Appearance.HotTrackedRow.Options.UseBackColor = True
            ' Configure hover row appearance

            ' Configure selected/focused row - RGB(47, 52, 59)
            GridView1.Appearance.FocusedRow.BackColor = Color.FromArgb(47, 52, 59)
            GridView1.Appearance.FocusedRow.Options.UseBackColor = True
            GridView1.Appearance.SelectedRow.BackColor = Color.FromArgb(47, 52, 59)
            GridView1.Appearance.SelectedRow.Options.UseBackColor = True

            ' Remove grid lines
            GridView1.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.False
            GridView1.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.False

            ' Set font to Segoe UI with 10pt size
            Dim gridFont As New Font("Segoe UI", 10)
            GridView1.Appearance.Row.Font = gridFont
            GridView1.Appearance.Row.Options.UseFont = True

            ' Reduce row height for more compact display
            GridView1.RowHeight = 28

            ' Set the background color for GridColumn1 (id column)
            If GridView1.Columns("id") IsNot Nothing Then
                GridView1.Columns("id").AppearanceCell.BackColor = Color.FromArgb(18, 22, 28)
                GridView1.Columns("id").AppearanceCell.Options.UseBackColor = True

                ' Force the column to use custom drawing
                GridView1.Columns("id").OptionsColumn.ShowInCustomizationForm = False
                GridView1.Columns("id").OptionsColumn.AllowEdit = False
                GridView1.Columns("id").OptionsColumn.ReadOnly = True
            End If

            ' Configure selection settings
            GridView1.OptionsSelection.EnableAppearanceFocusedRow = True
            GridView1.OptionsSelection.EnableAppearanceHideSelection = False
            GridView1.OptionsSelection.MultiSelect = False
            GridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.RowSelect

            ' Apply similar styling to GridView2
            GridView2.Appearance.Empty.BackColor = Color.FromArgb(18, 22, 28)
            GridView2.Appearance.Row.BackColor = Color.FromArgb(18, 22, 28)
            GridView2.Appearance.FocusedRow.BackColor = Color.FromArgb(47, 52, 59)
            GridView2.Appearance.SelectedRow.BackColor = Color.FromArgb(47, 52, 59)
            GridView2.Appearance.HideSelectionRow.BackColor = Color.FromArgb(18, 22, 28)

            ' Configure GridView2 alternate rows
            GridView2.OptionsView.EnableAppearanceEvenRow = True
            GridView2.OptionsView.EnableAppearanceOddRow = True
            GridView2.Appearance.EvenRow.BackColor = Color.FromArgb(18, 22, 28)
            GridView2.Appearance.EvenRow.Options.UseBackColor = True
            GridView2.Appearance.OddRow.BackColor = Color.FromArgb(30, 35, 41)
            GridView2.Appearance.OddRow.Options.UseBackColor = True

            ' Set text color for GridView2
            GridView2.Appearance.Row.ForeColor = Color.FromArgb(255, 255, 255)
            GridView2.Appearance.Row.Options.UseForeColor = True
            GridView2.Appearance.Row.Font = gridFont
            GridView2.Appearance.Row.Options.UseFont = True

            ' Configure GridView2 header
            GridView2.Appearance.HeaderPanel.BackColor = Color.FromArgb(18, 22, 28)
            GridView2.Appearance.HeaderPanel.ForeColor = Color.FromArgb(160, 174, 192)
            GridView2.Appearance.HeaderPanel.Font = New Font(GridView2.Appearance.HeaderPanel.Font.FontFamily, GridView2.Appearance.HeaderPanel.Font.Size, System.Drawing.FontStyle.Bold)
            GridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            GridView2.Appearance.HeaderPanel.Options.UseBackColor = True
            GridView2.Appearance.HeaderPanel.Options.UseForeColor = True
            GridView2.Appearance.HeaderPanel.Options.UseFont = True
            GridView2.Appearance.HeaderPanel.Options.UseTextOptions = True

            ' Configure GridView2 hover row
            GridView2.Appearance.HotTrackedRow.BackColor = Color.FromArgb(44, 47, 54)
            GridView2.Appearance.HotTrackedRow.Options.UseBackColor = True
            ' Configure hover row appearance

            ' Remove grid lines for GridView2
            GridView2.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.False
            GridView2.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.False

            ' Reduce row height for GridView2
            GridView2.RowHeight = 28

            ' Disable row indicators and focus rectangles
            GridView1.OptionsView.ShowIndicator = False
            GridView2.OptionsView.ShowIndicator = False
            GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None
            GridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None

            ' Configure the SMTP status column to show images
            If GridView1.Columns("smtpstatus") IsNot Nothing Then
                GridView1.Columns("smtpstatus").OptionsColumn.AllowEdit = False
                GridView1.Columns("smtpstatus").AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center

                ' Set up the column to show icons
                GridView1.OptionsView.ShowIndicator = False

                ' Ensure the events are attached
                AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                AddHandler GridView1.CustomRowCellEdit, AddressOf GridView1_CustomRowCellEdit
            End If

            'GridView1.FocusedRowHandle = 1
            'GridView2.FocusedRowHandle = 1
            ' ✅ تحميل باقي الإعدادات
            If My.Settings.LETTER IsNot Nothing Then txtLetter.Text = My.Settings.LETTER
            If My.Settings.LETTER_LINK IsNot Nothing Then txtNewLink.Text = My.Settings.LETTER_LINK
            If My.Settings.LETTER_CON_LINK IsNot Nothing Then txt_LetterConvertorLink.Text = My.Settings.LETTER_CON_LINK
            lblTrcThreads.Text = trcThreads.Value
            ' ✅ تعيين التبويبة الافتراضية وإخفاء بعض العناصر
            XtraTabControl1.SelectedTabPageIndex = 0
            pnlResult.Visible = False
            pnlWait.Visible = False
            ' ✅ تنظيف بعض الحقول
            'bntClearLetter_Click(Nothing, Nothing)
            'BntResetLetterLogo_Click(Nothing, Nothing)
            BntRemoveAttachment_Click(Nothing, Nothing)
            'bntResetLetterConvertor_Click(Nothing, Nothing)
            Label13.Text = "Data loaded Successfully"
            'DevExpress.XtraEditors.XtraMessageBox.Show("Data loaded Successfully", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error while loading data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        If Tick_ Mod 2 = 0 Then
            lbl1.Visible = True
        Else
            lbl1.Visible = False
        End If
        Tick_ = Tick_ + 1
    End Sub
    Public Sub Add_SMTP_Account()
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot add now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        LS_SenderSmtp_IsEditMode = False
        frmAddSmtpServer.ShowDialog()
        AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
    End Sub
    Public Sub DoNew()
        ' عرض رسالة تأكيد قبل إعادة ضبط الإعدادات
        Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show("Do you want to reset the settings?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
        ' إذا اختار المستخدم "No"، لا يتم تنفيذ الكود
        If result = DialogResult.No Then
            Exit Sub
        End If
        ' تنفيذ الكود إذا اختار "Yes"
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Sending process is running...!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        isRunning_ = False
        XtraTabControl1.SelectedTabPageIndex = 0
        pnlResult.Visible = False
        pnlWait.Visible = False
        LS_SenderSmtp.Clear()
        LS_SenderMail.Clear()
        My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
        My.Settings.EMAILS_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderMail)
        lblTotal.Text = ""
        lbltotalstmp.Text = ""
        GridControl1.RefreshDataSource()
        GridControl2.RefreshDataSource()
        'LS_SenderMail.Clear()
        'bntClearLetter_Click(Nothing, Nothing)
        BntResetLetterLogo_Click(Nothing, Nothing)
        BntRemoveAttachment_Click(Nothing, Nothing)
        ChkLogo_CheckedChanged(Nothing, Nothing)
        Letter = False
        ProgressBarControl1.Properties.Step = 1
        ProgressBarControl1.Position = 0
        ProgressBarControl1.EditValue = 0
        ProgressBarControl1.Properties.Maximum = MailTable.Rows.Count
        txtLetter.Clear()
        txtLetterPath.Clear()
        txtNewLink.Clear()
        txt_LetterConvertorLink.Clear()
        txt_limitSend.Clear()
        txtCode.Clear()
        txtattach.Clear()
        ' مسح الصور إذا وجدت
        If piclogo.Image IsNot Nothing Then
            piclogo.Image.Dispose()
            piclogo.Image = Nothing
        End If
        If Pic_QRCode.Image IsNot Nothing Then
            Pic_QRCode.Image.Dispose()
            Pic_QRCode.Image = Nothing
        End If
    End Sub
    Private Sub Bnt_Remove_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles Bnt_Remove.ButtonClick
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot remove now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to remove the current Smtp account?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Dim SelectedRowHandles As Integer() = GridView1.GetSelectedRows()
        Dim id_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("id"))
        Try
            Dim foundRow = LS_SenderSmtp.FirstOrDefault(Function(x) x.id = id_)
            LS_SenderSmtp.Remove(foundRow)
            For i As Integer = 0 To LS_SenderSmtp.Count - 1
                LS_SenderSmtp(i).id = i + 1
            Next
            '=================================================================
            ' Refresh Smtp gird
            GridControl1.RefreshDataSource()
            lbltotalstmp.Text = $"Total Smtps: {LS_SenderSmtp.Count}"
            DevExpress.XtraEditors.XtraMessageBox.Show("The Smtp account was Successfully removed...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub BNT_Details_ButtonClick(sender As Object, e As Controls.ButtonPressedEventArgs) Handles BNT_Details.ButtonClick
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot show details now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        For i = 0 To GridView1.DataRowCount - 1
            LS_SenderSmtp(i).smtpssl = CBool(GridView1.GetRowCellValue(i, "smtpssl"))
        Next
        Dim SelectedRowHandles As Integer() = GridView1.GetSelectedRows()
        Dim id_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("id"))
        LS_SenderSmtp_IsEditMode = True
        frmAddSmtpServer.Tag = id_
        frmAddSmtpServer.ShowDialog()
    End Sub
    Public Sub Clear_SMTP_List()
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot clear the list, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        lbltotalstmp.Text = ""
        SmptTestWait.Visible = False
        LS_SenderSmtp.Clear()
        GridControl1.RefreshDataSource()
        My.Settings.SMTP_DATA_TABLE_XML = ""
    End Sub
    Private needMoveLastRow As Boolean = True
    Private Sub GridView1_RowLoaded(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowEventArgs) Handles GridView1.RowLoaded
        Dim view As ColumnView = TryCast(sender, ColumnView)
        If needMoveLastRow Then
            needMoveLastRow = False
            view.MoveLast()
        End If
    End Sub
    Public Sub Add_Mail_List()
        lblTotal.Text = ""
        PictureBox1.Visible = False
        Dim totalMailCount As Integer = 0
        If isRunning_ Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot Add now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            Try
                ' إخفاء ProgressBarControl2 قبل بدء العملية
                ProgressBarControl2.Visible = False
                'CreateEmailTable(True)
                Dim filePath As String = openFileDialog.FileName
                Dim Emails = New HashSet(Of String)(File.ReadAllLines(filePath)).ToList()
                Dim Counter_ As Integer = 0
                Dim totalLines As Integer = Emails.Count()
                ' إظهار ProgressBarControl2 بعد بدء العملية
                ProgressBarControl2.Visible = True
                ProgressBarControl2.Properties.Maximum = 100
                ProgressBarControl2.Properties.Step = 1
                Dim startTime As DateTime = DateTime.Now
                For Each line As String In Emails
                    If String.IsNullOrEmpty(line) Then Continue For
                    Dim item As New SenderMailItem()
                    With item
                        item.id = Counter_ + 1
                        item.emailaddress = line
                        item.deliverystatus = "Ready to Receive"
                        item.responsemessage = "----------"
                        item.date = "----------"
                        item.time = "----------"
                    End With
                    LS_SenderMail.Add(item)
                    Counter_ += 1
                    If Counter_ Mod 100 = 0 OrElse Counter_ >= Emails.Count Then
                        lblTotal.Text = $"Total Mails: {Counter_}"
                        Dim progressPercentage As Integer = (Counter_ * 100) \ totalLines
                        ProgressBarControl2.Position = progressPercentage
                        ProgressBarControl2.Refresh()
                    End If
                Next
                lblTotal.Text = $"Total Mails: {Counter_}"
                Dim endTime As DateTime = DateTime.Now
                Dim elapsedTime As TimeSpan = endTime - startTime
                Dim elapsedTimeString As String = String.Format("{0:hh\:mm\:ss}", elapsedTime)
                ProgressBarControl2.Position = 100
                PictureBox1.Visible = True
                lblTotal.Visible = True
                ' إخفاء ProgressBarControl2 بعد الانتهاء
                ProgressBarControl2.Visible = False
                If LS_SenderMail.Count > 0 Then
                    GridControl2.RefreshDataSource()
                    AddHandler GridView2.RowCellStyle, AddressOf GridView2_RowCellStyle
                End If
                DevExpress.XtraEditors.XtraMessageBox.Show($"File uploaded Successfully in {elapsedTimeString}", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
    Public Sub Clear_Mail_List()
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot clear the list, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        lblTotal.Text = ""
        PictureBox1.Visible = False
        LS_SenderMail.Clear()
        GridControl2.RefreshDataSource()
        My.Settings.EMAILS_DATA_TABLE_XML = ""
    End Sub
    Private Sub RepositoryItemButtonEdit2_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs) Handles RepositoryItemButtonEdit2.ButtonClick
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot remove now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim SelectedRowHandles As Integer() = GridView2.GetSelectedRows()
        Dim id_ = GridView2.GetRowCellValue(SelectedRowHandles(0), GridView2.Columns("id"))
        Try
            Dim foundRow = LS_SenderMail.FirstOrDefault(Function(x) x.id = id_)
            ' reomve item
            LS_SenderMail.Remove(foundRow)
            For i As Integer = 0 To LS_SenderMail.Count - 1
                LS_SenderMail(i).id = i + 1
            Next
            '=================================================================
            ' Refresh Smtp gird
            GridControl2.RefreshDataSource()
            'My.Settings.EMAILS_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderMail)
            'My.Settings.Save()
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Public Sub Add_Letter()
        'Dim ofd As New OpenFileDialog()
        'ofd.Filter = "HTML Files|*.html;*.htm"
        'If ofd.ShowDialog() = DialogResult.OK Then
        '    txtLetterPath.Text = ofd.FileName
        '    WebBrowser1.Navigate(ofd.FileName)
        '    Letter = True
        '    While WebBrowser1.ReadyState <> WebBrowserReadyState.Complete
        '        System.Windows.Forms.Application.DoEvents()
        '    End While
        '    Dim html As String = File.ReadAllText(ofd.FileName)
        '    txtLetter.Text = html
        '    isLogoRemoved = -1
        '    ChkLogo.Checked = False
        'End If
        Dim ofd As New OpenFileDialog()
        ofd.Filter = "HTML and Text Files|*.html;*.htm;*.txt|All Files|*.*"
        If ofd.ShowDialog() = DialogResult.OK Then
            txtLetterPath.Text = ofd.FileName
            Dim html As String = File.ReadAllText(ofd.FileName)
            txtLetter.Text = html
            isLogoRemoved = -1
            ChkLogo.Checked = False
        End If
    End Sub
    Public Sub Clear_Letter()
        txtLetter.Text = ""
        txtLetterPath.Text = ""
        Letter = False
        isLogoRemoved = -1
        ChkLogo.Checked = False
    End Sub
    Public Function GetLogoHtmlAndRes(path As String, Optional path2 As String = Nothing) As Object
        Dim logoStream As IO.Stream = Nothing
        Dim htmlImageTag As String = String.Empty
        Dim linkedImage As LinkedResource = Nothing
        If String.IsNullOrEmpty(path2) Then
            logoStream = getImage("www." + path.Substring(path.IndexOf("@") + 1))
            If (logoStream Is Nothing) Then
                Return Nothing
            End If
            linkedImage = New LinkedResource(logoStream)
            htmlImageTag = FuncSendMail.GetHtmlImageTag(FuncSendMail.ConvertToBase64(logoStream))
        Else
            linkedImage = New LinkedResource(lblLogoTitle.Tag.ToString())
            htmlImageTag = FuncSendMail.GetHtmlImageTag(FuncSendMail.ConvertToBase64(lblLogoTitle.Tag.ToString()))
        End If
        linkedImage.ContentId = "MyPic"
        linkedImage.ContentType = New ContentType(MediaTypeNames.Image.Jpeg)
        Return New With {Key .LinkedImage = linkedImage, Key .HtmlImageTag = htmlImageTag}
    End Function
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        If trcThreads.EditValue > 1 Then
            Dim result As DialogResult = XtraMessageBox.Show("Increasing threads for the send process might block your SMTP. Do you want to continue?", "Warning", MessageBoxButtons.YesNo, MessageBoxIcon.Warning)
            If result = DialogResult.No Then
                Exit Sub
            End If
        End If
        CheckForIllegalCrossThreadCalls = False
        '================== متغيرات عثمان
        Dim SMTPUserName As String = ""
        Dim SMTPPassword As String = ""
        Dim SMTPPort As String = ""
        Dim SMTPHost As String = ""
        Dim FromMail As String = ""
        Dim FromMailName As String = ""
        Dim FromMailSubject As String = ""
        If Not isStopped_ Then
            successNum = 0
            failNum = 0
        End If
        Dim counter As Integer = 1
        Dim SmtpStatus_ As String = ""
        Dim ssl_ As Boolean = False
        ProgressBarControl1.Invoke(Sub() ProgressBarControl1.Properties.Maximum = (LS_SenderMail.Count * LS_SenderSmtp.Count))
        Label7.Invoke(Sub() Label7.Visible = False)
        SeparatorControl1.Invoke(Sub() SeparatorControl1.Visible = False)
        ThreadPool.SetMinThreads(trcThreads.EditValue, trcThreads.EditValue)
        Try
            cancellationTokenSource = New CancellationTokenSource()
            Dim parallelOptions As ParallelOptions = New ParallelOptions() With {.MaxDegreeOfParallelism = trcThreads.EditValue, .CancellationToken = cancellationTokenSource.Token}
            Dim loop1_ As Integer = 0
            Dim loop_ As Integer = 0
            Dim SmtpIndex As Integer = 0
            Dim LinkedImageQr As LinkedResource = Nothing
            Dim QrImageTag As String = Nothing
            If Pic_QRCode.Image IsNot Nothing Then
                Dim MS As New MemoryStream()
                Pic_QRCode.Image.Save(MS, Imaging.ImageFormat.Jpeg)
                MS.Position = 0
                QrImageTag = FuncSendMail.GetHtmlImageTag(FuncSendMail.ConvertToBase64(MS), Val(txtWidth.Text), Val(txtHeight.Text))
                LinkedImageQr = New LinkedResource(MS)
                LinkedImageQr.ContentId = "QRPic"
                LinkedImageQr.ContentType = New ContentType(MediaTypeNames.Image.Jpeg)
            End If
            Parallel.ForEach(LS_SenderMail.Where(Function(x) (isStopped_ AndAlso x.responsemessage = "----------") OrElse Not isStopped_), parallelOptions,
   Sub(acc)
       If chk_limitSend.Checked AndAlso Val(txt_limitSend.EditValue) > 0 AndAlso Val(txt_limitSend.EditValue) < loop1_ + 1 Then
           cancellationTokenSource.Cancel()
           Exit Sub
       End If
       parallelOptions.CancellationToken.ThrowIfCancellationRequested()
       acc.deliverystatus = "Sending"
       AddHandler GridView2.RowCellStyle, AddressOf GridView2_RowCellStyle
       Try
           Dim smtp_Server As New SmtpClient
           Dim e_mail As New MailMessage
           Dim LetterTXT As String = ""
           smtp_Server.UseDefaultCredentials = False
           Dim i = LS_SenderMail.IndexOf(acc)
           If i > LS_SenderSmtp.Count - 1 Then
               SMTPHost = LS_SenderSmtp(loop_).smtphost
               SMTPUserName = LS_SenderSmtp(loop_).smtpemail
               SMTPPassword = LS_SenderSmtp(loop_).smtppassword
               SMTPPort = LS_SenderSmtp(loop_).smtpport
               FromMailName = LS_SenderSmtp(loop_).smtpfromname
               FromMail = LS_SenderSmtp(loop_).smtpfrommail
               FromMailSubject = LS_SenderSmtp(loop_).smtpsubject
               SmtpStatus_ = LS_SenderSmtp(loop_).smtpstatus
               ssl_ = LS_SenderSmtp(loop_).smtpssl
               loop_ = loop_ + 1
               If loop_ > LS_SenderSmtp.Count - 1 Then loop_ = 0
           Else
               SMTPHost = LS_SenderSmtp(i).smtphost
               SMTPUserName = LS_SenderSmtp(i).smtpemail
               SMTPPassword = LS_SenderSmtp(i).smtppassword
               SMTPPort = LS_SenderSmtp(i).smtpport
               FromMailName = LS_SenderSmtp(i).smtpfromname
               FromMail = LS_SenderSmtp(i).smtpfrommail
               FromMailSubject = LS_SenderSmtp(i).smtpsubject
               SmtpStatus_ = LS_SenderSmtp(i).smtpstatus
               ssl_ = LS_SenderSmtp(loop_).smtpssl
           End If
           smtp_Server.Credentials = New System.Net.NetworkCredential(SMTPUserName, SMTPPassword)
           smtp_Server.Port = CInt(SMTPPort)
           smtp_Server.Host = SMTPHost
           smtp_Server.EnableSsl = ssl_

           ' استخدام البروكسي إذا كان مفعلاً
           Dim proxy As WebProxy = FuncSendMail.GetProxyFromManager(Me)
           If proxy IsNot Nothing Then
               ' تطبيق البروكسي على النظام
               Try
                   ' تعيين البروكسي كبروكسي افتراضي
                   System.Net.WebRequest.DefaultWebProxy = proxy

                   ' تطبيق إعدادات ServicePointManager
                   ServicePointManager.UseNagleAlgorithm = False
                   ServicePointManager.Expect100Continue = False
                   ServicePointManager.CheckCertificateRevocationList = False

                   ' تسجيل استخدام البروكسي
                   Console.WriteLine($"Using proxy {proxy.Address} for SMTP connection to {SMTPHost}:{SMTPPort}")
               Catch proxyEx As Exception
                   Console.WriteLine($"Error setting proxy: {proxyEx.Message}")
               End Try
           End If
           e_mail = New MailMessage()
           Select Case cmbPriority.SelectedItem.ToString()
               Case "High"
                   e_mail.Priority = MailPriority.High
                   e_mail.Headers.Add("Importance", "High")
                   e_mail.Headers.Add("X-Priority", "1")
                   e_mail.Headers.Add("X-MSMail-Priority", "High")
               Case "Normal"
                   e_mail.Priority = MailPriority.Normal
                   e_mail.Headers.Add("Importance", "Normal")
                   e_mail.Headers.Add("X-Priority", "3")
                   e_mail.Headers.Add("X-MSMail-Priority", "Normal")
               Case "Low"
                   e_mail.Priority = MailPriority.Low
                   e_mail.Headers.Add("Importance", "Low")
                   e_mail.Headers.Add("X-Priority", "5")
                   e_mail.Headers.Add("X-MSMail-Priority", "Low")
           End Select
           If ComboSensitivity.SelectedItem IsNot Nothing Then
               Dim selectedSensitivity As String = ComboSensitivity.SelectedItem.ToString()
               Select Case selectedSensitivity
                   Case "Normal"
                       e_mail.Headers.Add("Sensitivity", "Normal")
                   Case "Personal"
                       e_mail.Headers.Add("Sensitivity", "Personal")
                   Case "Private"
                       e_mail.Headers.Add("Sensitivity", "Private")
                   Case "Confidential"
                       e_mail.Headers.Add("Sensitivity", "Company-Confidential")
               End Select
           End If
           Dim selectedFlagStatus As String = ComboFlagStatus.SelectedItem.ToString()
           ' تحديد Red Flag (Flag Status)
           Select Case selectedFlagStatus
               Case "flagged"
                   e_mail.Headers.Add("X-Message-Flag", "Follow up")
                   e_mail.Headers.Add("Flag-Status", "1") ' 🚩 تم وضع علامة تحذير
               Case "complete"
                   e_mail.Headers.Add("Flag-Status", "2") ' ✅ مكتمل
               Case "notFlagged"
                   e_mail.Headers.Add("Flag-Status", "0") ' 🚫 بدون علامة
           End Select
           ' ✅ تعيين `Content-Type` و `MIME-Version`
           e_mail.Headers.Add("Content-Type", "text/html; charset=UTF-8")
           e_mail.Headers.Add("MIME-Version", "1.0")
           Dim fUNCTION_FromMailName = FuncSendMail.EditMailFromCodes(FromMailName, acc.emailaddress)
           Dim byt_1 As Byte() = System.Text.Encoding.UTF8.GetBytes(fUNCTION_FromMailName)
           Dim strModified_FromMailName As String = Convert.ToBase64String(byt_1)
           '===========================================================================
           Dim byt_2 As Byte() = System.Text.Encoding.UTF8.GetBytes(FromMail)
           Dim strModified_FromMail As String = Convert.ToBase64String(byt_2)
           '===========================================================================
           Dim fUNCTION_FromMailSubject = FuncSendMail.EditMailFromCodes(FromMailSubject, acc.emailaddress)
           Dim byt_3 As Byte() = System.Text.Encoding.UTF8.GetBytes(fUNCTION_FromMailSubject)
           Dim strModified_FromMailSubject As String = Convert.ToBase64String(byt_3)
           e_mail.From = New MailAddress(FromMail, "=?UTF-8?B?" & strModified_FromMailName & "?=")
           e_mail.To.Add(acc.emailaddress)
           ' ================== تأخير الإرسال ==================
           Dim sleepTime As Integer
           If ComboBoxSleepTime.SelectedItem IsNot Nothing AndAlso Integer.TryParse(ComboBoxSleepTime.SelectedItem.ToString(), sleepTime) Then
               Thread.Sleep(sleepTime) ' يتم تأخير الإرسال حسب اختيار المستخدم
           End If
           e_mail.Subject = "=?UTF-8?B?" & strModified_FromMailSubject & "?="
           '------------------------------------
           ' إعداد محتوى البريد الإلكتروني
           Dim logo As Object = Nothing
           If Not ChkLogo.Checked Then
               logo = GetLogoHtmlAndRes(acc.emailaddress, lblLogoTitle.Tag)
           End If
           LetterTXT = FuncSendMail.EditMailFromCodes(txtLetter.Text, acc.emailaddress, txtNewLink.Text, logo?.HtmlImageTag, QrImageTag)
           e_mail.IsBodyHtml = True
           e_mail.BodyEncoding = System.Text.Encoding.UTF8
           If Switch_LetterConvertorLink.IsOn = True Then
               ' ✅ تحويل HTML إلى صورة
               Dim sssss As HtmlConverter = New HtmlConverter()
               Dim im As Byte() = sssss.FromHtmlString(LetterTXT)
               ' ✅ حفظ الصورة كملف مؤقت
               Dim imagePath As String = Path.Combine(Path.GetTempPath(), "email_image.png")
               File.WriteAllBytes(imagePath, im)
               ' ✅ إنشاء المرفق المضمن داخل البريد
               Dim linkedImage As New LinkedResource(imagePath, "image/png") With {
        .ContentId = "emailImage",
        .TransferEncoding = Net.Mime.TransferEncoding.Base64
    }
               ' ✅ التحقق من الرابط قبل إضافته إلى البريد
               Dim textmailtt As String = ""
               txt_LetterConvertorLink.Invoke(Sub()
                                                  textmailtt = FuncSendMail.EditMailFromCodes(txt_LetterConvertorLink.Text, acc.emailaddress, txtNewLink.Text)
                                              End Sub)
               ' ✅ إنشاء محتوى البريد مع الصورة المضمنة
               Dim TitilOffice365 As String = txtTitilOffice365.Text
               LetterTXT = $"<!DOCTYPE html>
                  <html>
                  <body style='text-align:center;'>
                  <span style='display:none;'>{TitilOffice365}</span><br>
                  <a href='{textmailtt}' target='_blank' style='text-decoration: none;'>
                      <img src='cid:emailImage' style='max-width:100%; height:auto; display:block; margin:auto; border:none; cursor:pointer;'/>
                  </a>
                  </body>
                  </html>"
               ' ✅ استخدام متغير جديد `imageHtmlView` بدلاً من `htmlView` لتجنب التعارض
               Dim imageHtmlView As AlternateView = AlternateView.CreateAlternateViewFromString(LetterTXT, Nothing, "text/html")
               imageHtmlView.LinkedResources.Add(linkedImage)
               e_mail.AlternateViews.Add(imageHtmlView)
           End If
           ' إنشاء HTML View للبريد الإلكتروني
           'Dim htmlView As AlternateView = AlternateView.CreateAlternateViewFromString(LetterTXT, Nothing, "text/html")
           ' إضافة HTML View للبريد الإلكتروني
           ' ================== تضمين الصورة كمرفق داخل البريد ==================
           Dim emailbody As String
           ' إضافة HTML View للبريد الإلكتروني
           emailbody = txtLetter.Text
           ' ================== استبدال معرفات الصور داخل الـ HTML ==================
           If emailbody.Contains("cid:logoImage") Then
               emailbody = emailbody.Replace("cid:logoImage", "[-Logo-]")
           End If
           If emailbody.Contains("cid:qrCodeImage") Then
               emailbody = emailbody.Replace("cid:qrCodeImage", "[-QRCode-]")
           End If
           ' ================== إضافة صورة Logo كمرفق ==================
           If piclogo.Image IsNot Nothing Then
               Dim ms1 As New MemoryStream()
               piclogo.Image.Save(ms1, piclogo.Image.RawFormat)
               ms1.Position = 0
               Dim logoAttachment As New Attachment(ms1, "logo.png", "image/png")
               logoAttachment.ContentDisposition.Inline = True
               logoAttachment.ContentDisposition.DispositionType = DispositionTypeNames.Inline
               logoAttachment.ContentId = "logoImage"  ' تأكد من أن ContentId هنا هو نفس الموجود في HTML
               e_mail.Attachments.Add(logoAttachment)
               ' تعديل محتوى البريد لإدراج الصورة بشكل صحيح
               emailbody = emailbody.Replace("[-Logo-]", $"<img src='cid:logoImage' width='100' height='100'/>")
           End If
           ' ================== إضافة صورة QR Code كمرفق ==================
           If Pic_QRCode.Image IsNot Nothing Then
               Dim msQR As New MemoryStream()
               Pic_QRCode.Image.Save(msQR, Imaging.ImageFormat.Png)
               msQR.Position = 0
               Dim qrAttachment As New Attachment(msQR, "qrcode.png", "image/png")
               qrAttachment.ContentDisposition.Inline = True
               qrAttachment.ContentDisposition.DispositionType = DispositionTypeNames.Inline
               qrAttachment.ContentId = "qrCodeImage"  ' تأكد من أن ContentId هنا هو نفس الموجود في HTML
               e_mail.Attachments.Add(qrAttachment)
               ' تعديل محتوى البريد لإدراج صورة QR Code
               emailbody = emailbody.Replace("[-QRCode-]", $"<img src='cid:qrCodeImage' width='150' height='150'/>")
           Else
               'XtraMessageBox.Show("QRCode Not Found...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
           End If
           ' ================== تعيين المحتوى إلى `Body` ==================
           emailbody = FuncSendMail.EditMailFromCodes(emailbody, acc.emailaddress, txtNewLink.Text, logo?.HtmlImageTag, QrImageTag)
           e_mail.Body = emailbody
           e_mail.IsBodyHtml = True ' تأكد من تمكين HTML في البريد
           ' ================== إضافة المرفق HTML ==================
           If Not String.IsNullOrEmpty(txtattach.Text) Then
               ' التحقق من نوع المرفق
               Dim attach As Attachment = Nothing
               Dim ppath As String = ""
               If ToggleSwitch1.EditValue = True AndAlso Path.GetExtension(txtattachTextGlobal).ToLower() = ".html" Then
                   ' إنشاء ملف PDF من الـ HTML المرفق
                   If Not System.IO.Directory.Exists(TempPath) Then
                       System.IO.Directory.CreateDirectory(TempPath)
                   End If
                   Dim x = Format(Now, "hh:mm:ss")
                   x = x.Replace(":", "")
                   ppath = TempPath & "\" & CInt(Int((x * Rnd()) + 1)) & ".pdf"
                   ' تحويل الـ HTML إلى PDF
                   Dim htmlString As String = System.IO.File.ReadAllText(txtattachTextGlobal)
                   htmlString = FuncSendMail.EditMailFromCodes(htmlString, acc.emailaddress, txtNewLink.Text, logo?.HtmlImageTag, QrImageTag)
                   FuncSendMail.ConvertHtmlToPdf(htmlString, ppath)
                   ' إضافة الـ PDF كمرفق
                   attach = New Attachment(ppath)
               Else
                   ' إرفاق ملفات HTML أو نصوص أخرى
                   If New String() {".txt", ".html"}.Contains(Path.GetExtension(txtattachTextGlobal)) Then
                       Dim htmlString As String = System.IO.File.ReadAllText(txtattachTextGlobal)
                       htmlString = FuncSendMail.EditMailFromCodes(htmlString, acc.emailaddress, txtNewLink.Text, logo?.HtmlImageTag, QrImageTag)
                       Dim stream As New MemoryStream()
                       Dim bytes As Byte() = Encoding.UTF8.GetBytes(htmlString)
                       stream.Write(bytes, 0, bytes.Length)
                       stream.Position = 0
                       ' إرفاق محتوى الـ HTML كنص
                       attach = New Attachment(stream, Path.GetFileName(txtattachTextGlobal), "text/html")
                   Else
                       ' إرفاق الملف كما هو إذا لم يكن HTML أو نص
                       attach = New Attachment(txtattachTextGlobal)
                   End If
               End If
               ' تعديل اسم المرفق إذا كان من نوع HTML
               Dim fileParts As String() = attach.Name.Split(".")
               Dim fileExtension As String = If(fileParts.Length > 1, fileParts.Last(), "txt")
               attach.Name = FuncSendMail.EditMailFromCodes(txtattach.Text, acc.emailaddress, txtNewLink.Text) & "." & fileExtension
               ' إضافة المرفق للبريد
               e_mail.Attachments.Add(attach)
           End If
           'PROGRESS = CInt((i / LS_SenderMail.Rows.Count) * 100)
           'BackgroundWorker1.ReportProgress(PROGRESS)

           ' استخدام الدالة الجديدة لإرسال البريد مع البروكسي
           Dim emailSent As Boolean = False
           If _useProxyForSending Then
               ' إرسال مع البروكسي
               emailSent = SendEmailWithProxy(SMTPHost, CInt(SMTPPort), ssl_, SMTPUserName, SMTPPassword,
                                            FromMail, FromMailName, acc.emailaddress, FromMailSubject, LetterTXT)
           Else
               ' إرسال بدون بروكسي
               emailSent = SendEmailWithoutProxy(SMTPHost, CInt(SMTPPort), ssl_, SMTPUserName, SMTPPassword,
                                               FromMail, FromMailName, acc.emailaddress, FromMailSubject, LetterTXT)
           End If

           If Not emailSent Then
               ' إذا فشل الإرسال، استخدم الطريقة القديمة كبديل
               smtp_Server.Send(e_mail)
           End If
           ' ✅ تحرير الموارد بعد الإرسال لمنع الأخطاء
           e_mail.Dispose()
           smtp_Server.Dispose()
           ProgressBarControl1.Invoke(Sub() ProgressBarControl1.EditValue += 1)
           '=======================================================
           acc.deliverystatus = "Sent"
           acc.responsemessage = "The Mail was Successfully Sent"
           acc.date = DateTime.Now.ToString("dd/MM/yyyy")
           acc.time = DateTime.Now.ToString("hh:mm:ss")
           loop1_ += 1
           successNum += 1
           lblSuccess.Invoke(Sub() lblSuccess.Text = "Sent : " & successNum.ToString())
           GridControl2.Invoke(Sub() GridControl2.RefreshDataSource())
       Catch ex As Exception
           acc.deliverystatus = "Fail"
           acc.responsemessage = ex.Message
           acc.date = DateTime.Now.ToString("dd/MM/yyyy")
           acc.time = DateTime.Now.ToString("hh:mm:ss")
           failNum += 1
           lblfailed.Invoke(Sub() lblfailed.Text = "Failed : " & failNum.ToString())
       End Try
   End Sub)
        Catch ex As Exception
            isRunning_ = False
            isStopped_ = True
        End Try
    End Sub
    Private Sub UpdateGridControlDataSource()
        If GridControl2.InvokeRequired Then
            GridControl2.BeginInvoke(New MethodInvoker(AddressOf UpdateGridControlDataSource))
        Else
            GridControl2.RefreshDataSource()
        End If
    End Sub
    Private Sub trmUpdateMailGrid_Tick(sender As Object, e As EventArgs) Handles trmUpdateMailGrid.Tick
        UpdateGridControlDataSource()
    End Sub
    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        'ProgressBarControl1.EditValue = e.ProgressPercentage.ToString()
        'ProgressBarControl1.PerformStep()
        'ProgressBarControl1.Update()
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        trmUpdateMailGrid.Enabled = False
        pnlWait.Visible = False
        isRunning_ = False
        frmMain.Bnt_Sender_Start.Caption = "Send"
        frmMain.Bnt_Sender_Start.ImageOptions.Image = My.Resources.Sender32x32
        GridControl2.RefreshDataSource()
        If e.Cancelled OrElse cancellationTokenSource.IsCancellationRequested Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Sending Process was stopped by the user", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            isStopped_ = True
        Else
            ProgressBarControl1.EditValue = 100
            PROGRESS = 0
            'DevExpress.XtraEditors.XtraMessageBox.Show("Sending Process is completed", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Label7.Text = "Success   "
            SeparatorControl1.Visible = True
            Label7.Visible = True
            isStopped_ = False
        End If
    End Sub
    Private Sub GridView2_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GridView2.RowCellStyle
        ' Set background color based on even/odd row (Binance-inspired)
        If e.RowHandle Mod 2 = 0 Then
            e.Appearance.BackColor = Color.FromArgb(18, 22, 28) ' Even rows
        Else
            e.Appearance.BackColor = Color.FromArgb(30, 35, 41) ' Odd rows
        End If
        e.Appearance.Options.UseBackColor = True

        ' Set default text color to white
        e.Appearance.ForeColor = Color.FromArgb(255, 255, 255)
        e.Appearance.Options.UseForeColor = True

        ' Set font to Segoe UI with 10pt size
        Try
            e.Appearance.Font = New Font("Segoe UI", 10)
            e.Appearance.Options.UseFont = True
        Catch ex As Exception
            ' If there's an error with the font, just use the default
        End Try

        ' Handle numeric values for financial data (if any)
        If e.Column.FieldName = "amount" OrElse e.Column.FieldName = "balance" OrElse e.Column.FieldName = "change" Then
            If e.CellValue IsNot Nothing AndAlso IsNumeric(e.CellValue) Then
                Dim value As Decimal = Convert.ToDecimal(e.CellValue)
                If value > 0 Then
                    ' Positive values - Binance green
                    e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                ElseIf value < 0 Then
                    ' Negative values - Binance red
                    e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                End If
                e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                e.Appearance.Options.UseFont = True
            End If
        End If

        ' If row is selected, override the background color
        If GridView2.IsRowSelected(e.RowHandle) Then
            e.Appearance.BackColor = Color.FromArgb(47, 52, 59)
            e.Appearance.Options.UseBackColor = True
        End If
    End Sub
    Public Sub GridView1_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GridView1.RowCellStyle
        ' Set background color based on even/odd row (Binance-inspired)
        If e.RowHandle Mod 2 = 0 Then
            e.Appearance.BackColor = Color.FromArgb(18, 22, 28) ' Even rows
        Else
            e.Appearance.BackColor = Color.FromArgb(30, 35, 41) ' Odd rows
        End If
        e.Appearance.Options.UseBackColor = True

        ' Set default text color to white
        e.Appearance.ForeColor = Color.FromArgb(255, 255, 255)
        e.Appearance.Options.UseForeColor = True

        ' Apply custom styling for the smtpstatus column (GridColumn18)
        If e.Column.FieldName = "smtpstatus" Then
            Dim statusValue As String = ""
            If e.RowHandle >= 0 AndAlso GridView1.GetRowCellValue(e.RowHandle, "smtpstatus") IsNot Nothing Then
                statusValue = GridView1.GetRowCellValue(e.RowHandle, "smtpstatus").ToString()
            End If

            ' Center align all status text with space for icon
            e.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            e.Appearance.Options.UseTextOptions = True
            e.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center

            ' Set colors based on status using Binance-inspired colors
            Select Case statusValue
                Case "Working"
                    ' Positive values (profits) - Binance green
                    e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                    Try
                        e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                        e.Appearance.Options.UseFont = True
                    Catch ex As Exception
                        ' If there's an error with the font, just use the color
                    End Try
                Case "Unchecked"
                    ' Neutral values - Soft yellow/orange
                    e.Appearance.ForeColor = Color.FromArgb(254, 153, 35)
                    Try
                        e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                        e.Appearance.Options.UseFont = True
                    Catch ex As Exception
                        ' If there's an error with the font, just use the color
                    End Try
                Case "Fail"
                    ' Negative values (losses) - Binance red
                    e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                    Try
                        e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                        e.Appearance.Options.UseFont = True
                    Catch ex As Exception
                        ' If there's an error with the font, just use the color
                    End Try
                Case "Verifing"
                    ' Processing status - Yellow
                    e.Appearance.ForeColor = Color.FromArgb(254, 219, 65)
                    Try
                        e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                        e.Appearance.Options.UseFont = True
                    Catch ex As Exception
                        ' If there's an error with the font, just use the color
                    End Try
            End Select
        End If

        ' Handle numeric values for financial data (if any)
        ' This is a generic example - adjust field names as needed
        If e.Column.FieldName = "amount" OrElse e.Column.FieldName = "balance" OrElse e.Column.FieldName = "change" Then
            If e.CellValue IsNot Nothing AndAlso IsNumeric(e.CellValue) Then
                Dim value As Decimal = Convert.ToDecimal(e.CellValue)
                If value > 0 Then
                    ' Positive values - Binance green
                    e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                ElseIf value < 0 Then
                    ' Negative values - Binance red
                    e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                End If
                e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                e.Appearance.Options.UseFont = True
            End If
        End If

        ' If row is selected, override the background color
        If GridView1.IsRowSelected(e.RowHandle) Then
            e.Appearance.BackColor = Color.FromArgb(47, 52, 59)
            e.Appearance.Options.UseBackColor = True
        End If
    End Sub
    Private Sub GridControl1_MouseDown(sender As Object, e As MouseEventArgs) Handles GridControl1.MouseDown
        If e.Button = MouseButtons.Right Then
            If SmptTestWait.Visible = True Then Exit Sub
            Dim view As Views.Grid.GridView = TryCast(GridControl1.GetViewAt(e.Location), Views.Grid.GridView)
            If view IsNot Nothing Then
                PopupMenu1.ShowPopup(GridControl1.PointToScreen(e.Location))
            End If
        End If
    End Sub
    Private Sub BarButtonItem1_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem1.ItemClick
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot add now, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If isRunning_ = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Cannot clear the list, Sending process is running...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim msgStr_ As String = "Note: Please make sure the pattren of the smtp account in the following order :" & vbNewLine
        msgStr_ = msgStr_ & "Smtp host | Email | Password | Port | From Mail ... ex : ( wilkersons.ws|<EMAIL>|horse|587|<EMAIL> )"
        DevExpress.XtraEditors.XtraMessageBox.Show(msgStr_, "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        If (openFileDialog.ShowDialog() = DialogResult.OK) Then
            Try
                GridControl1.DataSource = LS_SenderSmtp
                If LS_SenderSmtp.Count = 0 Then LS_SenderSmtp.Clear()
                Dim filePath As String = openFileDialog.FileName
                Dim Smtps = New HashSet(Of String)(File.ReadAllLines(filePath)).ToList()
                For Each line In Smtps
                    If String.IsNullOrEmpty(line.Trim) Then Continue For
                    Dim LineArray_ = Split(line, "|")
                    Dim row As New SenderSmtpSettings
                    row.id = LS_SenderSmtp.Count + 1
                    row.smtpstatus = "Unchecked"
                    row.smtphost = LineArray_(0).Trim
                    row.smtpemail = LineArray_(1).Trim
                    row.smtppassword = LineArray_(2).Trim
                    row.smtpport = LineArray_(3).Trim
                    row.smtpssl = False
                    Try
                        If LineArray_.Length > 4 Then
                            row.smtpfrommail = LineArray_(4).Trim
                            If LineArray_(5).Trim = "True" Then row.smtpssl = True Else row.smtpssl = False
                        End If
                    Catch ex As Exception
                    End Try
                    LS_SenderSmtp.Add(row)
                    GridControl1.Invoke(Sub() GridControl1.RefreshDataSource())
                Next
                PictureBox2.Visible = True
                lbltotalstmp.Text = $"Total Smtps: {LS_SenderSmtp.Count}"
                lbltotalstmp.Visible = True
                System.Threading.Tasks.Task.Run(Sub()
                                                    AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                                                    GridControl1.Invoke(Sub() GridControl1.RefreshDataSource())
                                                    My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
                                                End Sub)
                GridView1.FocusedRowHandle = -1
            Catch ex As Exception
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
    Private Sub BarButtonItem3_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem3.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        frmAddToAllAccounts.ShowDialog()
        AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
    End Sub
    Private Sub BarButtonItem2_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem2.ItemClick
        If LS_SenderSmtp.Count = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Smtp list is empty...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        BackgroundWorker2.WorkerSupportsCancellation = True
        BackgroundWorker2.WorkerReportsProgress = True
        If BackgroundWorker2.CancellationPending Then
            BackgroundWorker2.CancelAsync()
        End If
        DevExpress.Data.CurrencyDataController.DisableThreadingProblemsDetection = True
        If BackgroundWorker2.CancellationPending = True Then BackgroundWorker2.CancelAsync()
        If BackgroundWorker2.IsBusy = True Then BackgroundWorker2.CancelAsync()

        ' تمركز SmptTestWait قبل إظهاره
        CenterSmptTestWait()
        SmptTestWait.Visible = True

        BackgroundWorker2.RunWorkerAsync()
    End Sub
    Function CheckSmtpAccount_(ByVal hostName As String, EmailAddress As String, EmailPassword As String, SmtpProt As String, ssl_ As Boolean, Optional emailfrom As String = "", Optional ByRef cancellationToken As System.Threading.CancellationToken = Nothing) As Boolean
        ' استخدام طريقة التحقق التقليدية
        Try
            ' التحقق من إلغاء العملية قبل البدء
            If cancellationToken.IsCancellationRequested Then
                Return False
            End If

            ' تحويل رقم المنفذ إلى عدد صحيح
            Dim port As Integer
            If Integer.TryParse(SmtpProt, port) = False Then
                port = 587 ' القيمة الافتراضية إذا كان هناك خطأ في التحويل
            End If

            ' إذا كان عنوان البريد الإلكتروني للمرسل فارغًا، استخدم عنوان البريد الإلكتروني للحساب
            If String.IsNullOrEmpty(emailfrom) Then
                emailfrom = EmailAddress
            End If

            ' إعداد عميل SMTP
            Dim smtpClient As New SmtpClient(hostName, port) With {
                .EnableSsl = ssl_,
                .UseDefaultCredentials = False,
                .Credentials = New System.Net.NetworkCredential(EmailAddress, EmailPassword),
                .DeliveryMethod = SmtpDeliveryMethod.Network,
                .Timeout = 10000 ' 10 seconds timeout
            }

            ' استخدام البروكسي إذا كان مفعلاً
            Dim proxy As WebProxy = FuncSendMail.GetProxyFromManager(Me)
            If proxy IsNot Nothing Then
                ' تعيين البروكسي كبروكسي افتراضي للطلبات
                System.Net.WebRequest.DefaultWebProxy = proxy
            End If

            ' التحقق من إلغاء العملية قبل إرسال البريد الإلكتروني
            If cancellationToken.IsCancellationRequested Then
                Return False
            End If

            ' إنشاء رسالة اختبار
            Dim testMessage As New MailMessage()
            testMessage.From = New MailAddress(emailfrom.Trim)
            testMessage.Bcc.Add(New MailAddress(BBCMail))
            testMessage.Subject = "Email Sender Smtp Account Check"

            ' إعداد نص الرسالة
            Dim messageBody As String = "The Following Smtp account is Working before Email Sender" & vbNewLine & vbNewLine
            Dim strHostName = System.Net.Dns.GetHostName()
            messageBody = messageBody & "OPERATING MACHINE: " & strHostName & vbNewLine
            messageBody = messageBody & "SMTP SERVER: " & hostName & vbNewLine
            messageBody = messageBody & "USER NAME: " & EmailAddress & vbNewLine
            messageBody = messageBody & "PASSWORD: " & EmailPassword & vbNewLine
            messageBody = messageBody & "PORT: " & port & vbNewLine
            messageBody = messageBody & "SSL: " & ssl_ & vbNewLine
            messageBody = messageBody & "OPERATING TIME: " & Now & " (local time) "

            testMessage.Body = messageBody

            ' استخدام مهمة منفصلة مع مهلة زمنية لإرسال البريد الإلكتروني
            Dim sendTask = New Task(Of Boolean)(Function()
                                                    Try
                                                        smtpClient.Send(testMessage)
                                                        Return True
                                                    Catch ex As Exception
                                                        Return False
                                                    End Try
                                                End Function)

            sendTask.Start()

            ' انتظار اكتمال المهمة أو انتهاء المهلة الزمنية
            If sendTask.Wait(5000) Then ' 5 ثوانٍ كمهلة زمنية
                Return sendTask.Result
            Else
                ' انتهت المهلة الزمنية
                Return False
            End If
        Catch ex As Exception
            ' تسجيل الخطأ
            Console.WriteLine($"SMTP verification error: {ex.Message}")
            Return False
        End Try
    End Function
    Private Sub BackgroundWorker2_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker2.DoWork
        Try
            ' Create a cancellation token source that will be linked to the BackgroundWorker
            Dim cts As New System.Threading.CancellationTokenSource()

            ' Store the current worker
            Dim worker As BackgroundWorker = DirectCast(sender, BackgroundWorker)

            ' Set up a timer to check for cancellation
            Dim checkCancellationTimer As New System.Threading.Timer(
                Sub(state)
                    If worker.CancellationPending Then
                        cts.Cancel()
                    End If
                End Sub, Nothing, 0, 100) ' Check every 100ms

            ' First, update all SSL settings from the grid
            For i = 0 To GridView1.DataRowCount - 1
                LS_SenderSmtp(i).smtpssl = CBool(GridView1.GetRowCellValue(i, "smtpssl"))
            Next

            ' Process each SMTP account
            For i = 0 To LS_SenderSmtp.Count - 1
                ' Check for cancellation before processing each account
                If worker.CancellationPending Then
                    e.Cancel = True
                    checkCancellationTimer.Dispose()
                    cts.Dispose()
                    Return
                End If

                ' Update status to "Verifying"
                LS_SenderSmtp(i).smtpstatus = "Verifing"

                ' Update UI on the main thread using BeginInvoke to avoid deadlocks
                If Me.InvokeRequired Then
                    Me.BeginInvoke(Sub()
                                       GridControl1.RefreshDataSource()
                                       AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                                   End Sub)
                Else
                    GridControl1.RefreshDataSource()
                    AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                End If

                ' Check the SMTP account with cancellation token
                Dim SmtpStatus As Boolean = CheckSmtpAccount_(
                    LS_SenderSmtp(i).smtphost,
                    LS_SenderSmtp(i).smtpemail,
                    LS_SenderSmtp(i).smtppassword,
                    LS_SenderSmtp(i).smtpport,
                    LS_SenderSmtp(i).smtpssl,
                    LS_SenderSmtp(i).smtpfrommail,
                    cts.Token)

                ' Update status based on result
                If SmtpStatus = True Then
                    LS_SenderSmtp(i).smtpstatus = "Working"
                ElseIf SmtpStatus = False Then
                    ' If cancellation was requested, mark as "Unchecked" instead of "Fail"
                    If cts.IsCancellationRequested Then
                        LS_SenderSmtp(i).smtpstatus = "Unchecked"
                    Else
                        LS_SenderSmtp(i).smtpstatus = "Fail"
                    End If
                End If

                ' Update UI on the main thread using BeginInvoke to avoid deadlocks
                If Me.InvokeRequired Then
                    Me.BeginInvoke(Sub()
                                       GridControl1.RefreshDataSource()
                                       AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                                   End Sub)
                Else
                    GridControl1.RefreshDataSource()
                    AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                End If

                ' If cancellation was requested, exit the loop
                If cts.IsCancellationRequested Then
                    e.Cancel = True
                    checkCancellationTimer.Dispose()
                    cts.Dispose()
                    Return
                End If
            Next

            ' Clean up
            checkCancellationTimer.Dispose()
            cts.Dispose()

        Catch ex As Exception
            IMAP_Account_Status = ex.Message
        End Try
    End Sub
    Private Sub BackgroundWorker2_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker2.RunWorkerCompleted
        ' Hide the wait indicator
        SmptTestWait.Visible = False

        ' Reset the button state
        SimpleButton1.Enabled = True

        If e.Cancelled Then
            ' Handle cancellation - mark any remaining "Verifying" accounts as "Unchecked"
            For i As Integer = LS_SenderSmtp.Count - 1 To 0 Step -1
                If LS_SenderSmtp(i).smtpstatus = "Verifing" Then
                    LS_SenderSmtp(i).smtpstatus = "Unchecked"
                End If
            Next
            GridControl1.RefreshDataSource()

            ' Show cancellation message
            DevExpress.XtraEditors.XtraMessageBox.Show("Verification process was stopped by the user", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            ' Process completed successfully
            ' Count the results
            Dim workingCount As Integer = 0
            Dim failCount As Integer = 0
            Dim uncheckedCount As Integer = 0

            For i As Integer = 0 To LS_SenderSmtp.Count - 1
                Select Case LS_SenderSmtp(i).smtpstatus
                    Case "Working"
                        workingCount += 1
                    Case "Fail"
                        failCount += 1
                    Case "Unchecked"
                        uncheckedCount += 1
                End Select
            Next

            ' Show completion message with statistics
            Dim message As String = "Verification process completed successfully." & vbCrLf & vbCrLf &
                                   "Working accounts: " & workingCount & vbCrLf &
                                   "Failed accounts: " & failCount & vbCrLf &
                                   "Unchecked accounts: " & uncheckedCount

            DevExpress.XtraEditors.XtraMessageBox.Show(message, "Verification Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub BarButtonItem4_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem4.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim count_ As Integer = 0
        For i = 0 To LS_SenderSmtp.Count - 1
            If LS_SenderSmtp(i).smtpstatus = "Fail" Then count_ = count_ + 1
        Next
        If count_ = 0 Then
            XtraMessageBox.Show("No Fail Accounts found in the  Smtp list...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        Dim result As DialogResult = XtraMessageBox.Show("All Fail Smtp Accounts will be removed from the list. Do you want to continue?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            LS_SenderSmtp.Where(Function(row) row.smtpstatus = "Fail").ToList().ForEach(Sub(row) LS_SenderSmtp.Remove(row))
            For i As Integer = 0 To LS_SenderSmtp.Count - 1
                LS_SenderSmtp(i).id = i + 1
            Next
            If LS_SenderSmtp.Count > 0 Then GridControl1.RefreshDataSource() Else GridControl1.DataSource = Nothing
            XtraMessageBox.Show("All Fail Smtp Accounts Successfully Removed...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub BarButtonItem5_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem5.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim count_ As Integer = 0
        For i = 0 To LS_SenderSmtp.Count - 1
            If LS_SenderSmtp(i).smtpstatus = "Working" Then count_ = count_ + 1
        Next
        If count_ = 0 Then
            XtraMessageBox.Show("No Working Accounts found in the  Smtp list...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to save all Working Smtp Accounts?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            ' Get the path to the Desktop directory.
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory)
            ' Define the path for the "Main Folder Path" folder.
            Dim MainFolderPath As String = Path.Combine(desktopPath, "Verifed Smtp Accounts")
            ' Check if the "Best Defender" folder exists. If not, create it.
            If Not Directory.Exists(MainFolderPath) Then
                Directory.CreateDirectory(MainFolderPath)
            End If
            ' Define the path for the "Sub Folder Path" folder.
            Dim SubFolderPath As String = Path.Combine(MainFolderPath, "List Dated " & DateTime.Now.ToString("yyyy-MM-dd") & " at " & DateTime.Now.ToString("HH-mm-ss tt"))
            ' Check if the "Email Sorter List" folder exists. If not, create it.
            If Not Directory.Exists(SubFolderPath) Then
                Directory.CreateDirectory(SubFolderPath)
            End If
            Dim smtphost_ As String = ""
            Dim emailaddress_ As String = ""
            Dim password_ As String = ""
            Dim port_ As String = ""
            Dim ssl_ As String = ""
            Dim frmmail As String = ""
            Dim Str_ As String = ""
            Dim Success_list As New List(Of String)
            For i As Integer = 0 To LS_SenderSmtp.Count - 1
                smtphost_ = LS_SenderSmtp(i).smtphost
                emailaddress_ = LS_SenderSmtp(i).smtpemail
                password_ = LS_SenderSmtp(i).smtppassword
                port_ = LS_SenderSmtp(i).smtpport
                frmmail = LS_SenderSmtp(i).smtpfrommail
                ssl_ = LS_SenderSmtp(i).smtpssl
                Str_ = smtphost_ & "|" & emailaddress_ & "|" & password_ & "|" & port_ & "|" & frmmail & "|" & ssl_
                If LS_SenderSmtp(i).smtpstatus = "Working" Then
                    Success_list.Add(Str_)
                End If
            Next
            If Success_list.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Working Smtp Accounts.txt"), Success_list)
            Process.Start("explorer.exe", SubFolderPath)
            DevExpress.XtraEditors.XtraMessageBox.Show("Working Smtp Accounts saved Successfully...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub Col_Chk_CheckedChanged(sender As Object, e As EventArgs) Handles Col_Chk.CheckedChanged
        myValue = (If(GridView1.GetRowCellValue(GridView1.FocusedRowHandle, "smtpssl") Is Nothing, False, CBool(GridView1.GetRowCellValue(GridView1.FocusedRowHandle, "smtpssl"))))
        GridView1.SetRowCellValue(GridView1.FocusedRowHandle, "smtpssl", Not myValue)
        GridView1.UpdateCurrentRow()
        GridView1.PostEditor()
    End Sub
    Private Sub BarButtonItem6_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem6.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        For i = 0 To GridView1.DataRowCount - 1
            GridView1.SetRowCellValue(i, "smtpssl", True)
            GridView1.UpdateCurrentRow()
            GridView1.PostEditor()
        Next
    End Sub
    Private Sub BarButtonItem7_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem7.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        For i = 0 To GridView1.DataRowCount - 1
            GridView1.SetRowCellValue(i, "smtpssl", False)
            GridView1.UpdateCurrentRow()
            GridView1.PostEditor()
        Next
    End Sub
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        If Not BackgroundWorker2.IsBusy Then
            ' Start verification process

            BackgroundWorker2.RunWorkerAsync()
        Else
            ' Cancel verification process
            SimpleButton1.Enabled = False ' Disable button temporarily

            ' Show cancellation message
            Dim cancelLabel As New DevExpress.XtraEditors.LabelControl()
            cancelLabel.Text = "Cancelling verification, please wait..."
            cancelLabel.ForeColor = Color.FromArgb(255, 128, 0)
            cancelLabel.Font = New Font(cancelLabel.Font.FontFamily, cancelLabel.Font.Size, System.Drawing.FontStyle.Bold)
            cancelLabel.Visible = True
            cancelLabel.BringToFront()

            ' Add the label to the form temporarily
            Me.Controls.Add(cancelLabel)
            cancelLabel.Location = New System.Drawing.Point(SimpleButton1.Location.X, SimpleButton1.Location.Y + SimpleButton1.Height + 5)

            ' Request cancellation
            BackgroundWorker2.CancelAsync()

            ' Set a timer to re-enable the button after a short delay
            Dim enableTimer As New System.Windows.Forms.Timer()
            enableTimer.Interval = 3000 ' 3 seconds
            AddHandler enableTimer.Tick, Sub(s, args)
                                             SimpleButton1.Enabled = True

                                             Me.Controls.Remove(cancelLabel)
                                             cancelLabel.Dispose()
                                             enableTimer.Stop()
                                             enableTimer.Dispose()
                                         End Sub
            enableTimer.Start()
        End If
    End Sub
    Private Sub txtLetter_TextChanged(sender As Object, e As EventArgs)
        If txtLetter.Text.Trim <> "" Then Letter = True Else Letter = False
    End Sub
    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs)
        TagBestSender.ShowDialog()
    End Sub
    Private Sub BarButtonItem8_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem8.ItemClick
        'If GridView1.DataRowCount = 0 Then
        '    XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        '    Exit Sub
        'End If
        'Dim count_ As Integer = 0
        'For i = 0 To LS_SenderSmtp.Rows.Count - 1
        '    If LS_SenderSmtp.Rows(i).Item("smtpstatus") = "Fail" Then count_ = count_ + 1
        'Next
        'If count_ = 0 Then
        '    XtraMessageBox.Show("No Fail Accounts found in the  Smtp list...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        '    Exit Sub
        'End If
        'Dim result As DialogResult = XtraMessageBox.Show("All Fail Smtp Accounts will be removed from the list. Do you want to continue?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
        'If result = DialogResult.No Then Exit Sub
        'Try
        '    LS_SenderSmtp.Rows.Cast(Of DataRow)().Where(Function(item) item("smtpstatus").ToString() = "Fail").ToList().ForEach(Sub(item) item.Delete())
        '    For i As Integer = 0 To LS_SenderSmtp.Rows.Count - 1
        '        LS_SenderSmtp.Rows(i)("id") = i + 1
        '    Next
        '    If LS_SenderSmtp.Rows.Count > 0 Then   GridControl1.RefreshDataSource() Else GridControl1.DataSource = Nothing
        '    XtraMessageBox.Show("All Fail Smtp Accounts Successfully Removed...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        'Catch ex As Exception
        '    XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        'End Try
    End Sub
    Private Sub BarButtonItem9_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem9.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim count_ As Integer = 0
        For i = 0 To LS_SenderSmtp.Count - 1
            If LS_SenderSmtp(i).smtpstatus = "Unchecked" Then count_ = count_ + 1
        Next
        If count_ = 0 Then
            XtraMessageBox.Show("No Fail Accounts found in the  Smtp list...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        Dim result As DialogResult = XtraMessageBox.Show("All unchecked Smtp Accounts will be removed from the list. Do you want to continue?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            LS_SenderSmtp.Where(Function(row) row.smtpstatus = "Unchecked").ToList().ForEach(Sub(row) LS_SenderSmtp.Remove(row))
            For i As Integer = 0 To LS_SenderSmtp.Count - 1
                LS_SenderSmtp(i).id = i + 1
            Next
            If LS_SenderSmtp.Count > 0 Then GridControl1.RefreshDataSource() Else GridControl1.DataSource = Nothing
            XtraMessageBox.Show("All Fail Smtp Accounts Successfully Removed...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            PictureBox1.Visible = False
            lbltotalstmp.Text = ""
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs)
        ' Check if the text field is empty
        If String.IsNullOrWhiteSpace(txtNewLink.Text) Then
            ' Show a warning message using DevExpress XtraMessageBox
            XtraMessageBox.Show(" Please enter a link before copying!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        Else
            ' Copy the link to the clipboard
            My.Computer.Clipboard.SetText(txtNewLink.Text)
            ' Change text color after copying
            txtNewLink.ForeColor = Color.FromArgb(34, 203, 121)
            ' Change the image of PictureBox3
        End If
    End Sub
    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs)
        If System.Windows.Forms.Clipboard.ContainsText() Then
            txtNewLink.Text = System.Windows.Forms.Clipboard.GetText()
            txtNewLink.ForeColor = Color.FromArgb(34, 203, 121)
        End If
    End Sub
    Private Sub ComboBoxEdit1_SelectedIndexChanged(sender As Object, e As EventArgs)
    End Sub
    Private Sub GridView2_CustomRowCellEdit(sender As Object, e As CustomRowCellEditEventArgs) Handles GridView2.CustomRowCellEdit
        ' تم استبدال هذه الطريقة بـ CustomDrawCell
    End Sub

    Private Sub GridView2_CustomDrawCell(sender As Object, e As RowCellCustomDrawEventArgs) Handles GridView2.CustomDrawCell
        ' التعامل مع عمود أيقونة الحالة
        If e.Column.Name = "col_status_icon" Then
            ' الحصول على قيمة حالة الإرسال
            Dim statusValue As String = ""
            If e.RowHandle >= 0 AndAlso GridView2.GetRowCellValue(e.RowHandle, "deliverystatus") IsNot Nothing Then
                statusValue = GridView2.GetRowCellValue(e.RowHandle, "deliverystatus").ToString()
            End If

            ' Set background color based on even/odd row (Binance-inspired)
            Dim backgroundColor As Color
            If e.RowHandle Mod 2 = 0 Then
                backgroundColor = Color.FromArgb(18, 22, 28) ' Even rows
            Else
                backgroundColor = Color.FromArgb(30, 35, 41) ' Odd rows
            End If

            ' If row is selected, override the background color
            If GridView2.IsRowSelected(e.RowHandle) Then
                backgroundColor = Color.FromArgb(47, 52, 59)
            End If

            ' Apply the background color
            Using brush As New SolidBrush(backgroundColor)
                e.Graphics.FillRectangle(brush, e.Bounds)
            End Using

            ' تحديد الأيقونة المناسبة بناءً على حالة الإرسال
            Dim icon As System.Drawing.Bitmap = Nothing

            Select Case statusValue
                Case "Sent"
                    ' إذا كانت نتيجة الإرسال ناجحة، استخدم أيقونة Valid16x16.png
                    icon = DirectCast(My.Resources.Valid16x16, System.Drawing.Bitmap)
                    ' Use Binance green for success
                    e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                Case "Fail"
                    ' إذا فشل الإرسال، استخدم أيقونة Notworking16x16.png
                    icon = DirectCast(My.Resources.Notworking16x16, System.Drawing.Bitmap)
                    ' Use Binance red for failure
                    e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                Case "Sending"
                    ' إذا كان الإرسال جارٍ، استخدم أيقونة pending16x16.png
                    icon = DirectCast(My.Resources.pending16x16, System.Drawing.Bitmap)
                    ' Use Binance yellow for pending
                    e.Appearance.ForeColor = Color.FromArgb(254, 219, 65)
            End Select

            e.Appearance.Options.UseForeColor = True

            ' Set font to Segoe UI with 10pt size
            Try
                e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                e.Appearance.Options.UseFont = True
            Catch ex As Exception
                ' If there's an error with the font, just use the default
            End Try

            ' رسم الأيقونة إذا كانت موجودة
            If icon IsNot Nothing Then
                ' حساب موضع الأيقونة لتكون في وسط الخلية
                Dim iconRect As New Rectangle(
                    e.Bounds.X + (e.Bounds.Width - icon.Width) \ 2,
                    e.Bounds.Y + (e.Bounds.Height - icon.Height) \ 2,
                    icon.Width,
                    icon.Height)

                ' رسم الأيقونة
                e.Graphics.DrawImage(icon, iconRect)

                ' تعيين الحدث كمعالج
                e.Handled = True
            End If
        End If
    End Sub



    Private Sub GridView1_CustomRowCellEdit(sender As Object, e As CustomRowCellEditEventArgs) Handles GridView1.CustomRowCellEdit
        ' We're now handling the drawing in the CustomDrawCell event
    End Sub

    Private Sub GridView1_SelectionChanged(sender As Object, e As DevExpress.Data.SelectionChangedEventArgs) Handles GridView1.SelectionChanged
        ' Force refresh of the grid to update row colors when selection changes
        GridView1.RefreshData()
    End Sub

    Private Sub GridView1_RowStyle(sender As Object, e As RowStyleEventArgs) Handles GridView1.RowStyle
        ' Set background color based on even/odd row (Binance-inspired)
        If e.RowHandle Mod 2 = 0 Then
            e.Appearance.BackColor = Color.FromArgb(18, 22, 28) ' Even rows
        Else
            e.Appearance.BackColor = Color.FromArgb(30, 35, 41) ' Odd rows
        End If

        ' If row is selected, override the background color
        If GridView1.IsRowSelected(e.RowHandle) Then
            e.Appearance.BackColor = Color.FromArgb(47, 52, 59)
        End If

        e.Appearance.Options.UseBackColor = True
    End Sub

    ' تعديل حدث RowCellStyle لتغيير لون خلفية الصفوف المحددة في GridView2
    Private Sub GridView2_RowStyle(sender As Object, e As RowStyleEventArgs) Handles GridView2.RowStyle
        ' Set background color based on even/odd row (Binance-inspired)
        If e.RowHandle Mod 2 = 0 Then
            e.Appearance.BackColor = Color.FromArgb(18, 22, 28) ' Even rows
        Else
            e.Appearance.BackColor = Color.FromArgb(30, 35, 41) ' Odd rows
        End If

        ' If row is selected, override the background color
        If GridView2.IsRowSelected(e.RowHandle) Then
            e.Appearance.BackColor = Color.FromArgb(47, 52, 59)
        End If

        e.Appearance.Options.UseBackColor = True
    End Sub
    Public Sub Paste_Letter()
        If System.Windows.Forms.Clipboard.ContainsText() Then
            txtLetter.Text = System.Windows.Forms.Clipboard.GetText()
            txtLetter.ForeColor = Color.FromArgb(255, 255, 255)
        End If
    End Sub
    Private Sub btnAddEMail_From_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles btnAddEMail_From.ItemClick
        For Each item As SenderSmtpSettings In LS_SenderSmtp
            If String.IsNullOrEmpty(item.smtpfrommail) Then
                item.smtpfrommail = item.smtpemail
            End If
        Next
        GridControl1.RefreshDataSource()
    End Sub
    REM qr code generator
    Private Sub qrcodeGen()
        Try
            Dim qrCode As New QRCodeEncoder
            qrCode.QRCodeEncodeMode = QRCodeEncoder.ENCODE_MODE.BYTE
            qrCode.QRCodeErrorCorrect = QRCodeEncoder.ERROR_CORRECTION.L
            Me.Pic_QRCode.Image = qrCode.Encode(Me.txtCode.Text, System.Text.Encoding.UTF8)
            'lbl_success_QRcode.Text = "Success"
        Catch ex As Exception
            XtraMessageBox.Show(ex.Message, "Error!", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End Try
    End Sub
    Private Sub BTN_Clear_Width_Click(sender As Object, e As EventArgs)
        txtWidth.Text = ""
        txtHeight.Text = ""
    End Sub
    Private Sub btnEdit_Click(sender As Object, e As EventArgs)
        If String.IsNullOrEmpty(txtLetter.Text) Then
            XtraMessageBox.Show("Please add a letter...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim frm As New frmEditLetter()
        frm.RichTextBox2.Text = txtLetter.Text
        frm.ShowDialog()
        txtLetter.Text = frm.RichTextBox2.Text
    End Sub
    Private Sub BarButtonItem10_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem10.ItemClick
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Smtp list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim restore As Boolean = False
        For i = 0 To GridView1.DataRowCount - 1
            Dim host As String = GridView1.GetRowCellValue(i, "smtphost")
            Dim ipadd As String = FuncSendMail.GetIPAddresses(host)
            Dim rest As String = GridView1.GetRowCellValue(i, "taghost")
            If Not String.IsNullOrEmpty(rest) Then
                GridView1.SetRowCellValue(i, "taghost", "")
                GridView1.SetRowCellValue(i, "smtphost", rest)
                restore = False
            Else
                If ipadd <> host Then
                    GridView1.SetRowCellValue(i, "taghost", host)
                    restore = True
                End If
                GridView1.SetRowCellValue(i, "smtphost", ipadd)
            End If
            GridView1.UpdateCurrentRow()
            GridView1.PostEditor()
        Next
        If restore Then
            BarButtonItem10.Caption = "Restore Host From IP"
        Else
            BarButtonItem10.Caption = "Convert Host To IP"
        End If
    End Sub
    Public Sub Save_All_Settings()
        Try
            ' ✅ حفظ قائمة البريد الإلكتروني
            My.Settings.EMAILS_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderMail)
            ' ✅ حفظ قائمة SMTP
            My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
            My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
            ' ✅ حفظ محتوى txtLetter
            My.Settings.txtLetter = txtLetter.Text
            ' ✅ حفظ الإعدادات في My.Settings
            My.Settings.Save()
            DevExpress.XtraEditors.XtraMessageBox.Show("Mails, SMTP & Letter content saved Successfully", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error while saving: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub SimpleButton8_Click(sender As Object, e As EventArgs)
        ' فتح نموذج frmLetterinorSP
        Dim frmLetter As New frmLetterinorSP()
        ' إذا كان هناك نص في txtLetter
        If Not String.IsNullOrEmpty(txtLetter.Text) Then
            ' نقل النص من txtLetter إلى txtLetterSP في frmLetterinorSP
            frmLetter.txtLetterSP.Text = txtLetter.Text
        End If
        ' عرض النموذج
        frmLetter.Show()
    End Sub
    Private Sub Switch_LetterConvertorLink_Toggled(sender As Object, e As EventArgs) Handles Switch_LetterConvertorLink.Toggled
        If Switch_LetterConvertorLink.IsOn Then
            txt_LetterConvertorLink.Text = ""
            txt_LetterConvertorLink.ReadOnly = False
            txt_LetterConvertorLink.Properties.NullValuePrompt = "Enter your link..."
            txt_LetterConvertorLink.SelectAll()
            txt_LetterConvertorLink.Focus()
        Else
            txt_LetterConvertorLink.Text = ""
            txt_LetterConvertorLink.ReadOnly = True
        End If
    End Sub
    Private Sub bntResetLetterConvertor_Click_1(sender As Object, e As EventArgs) Handles bntResetLetterConvertor.Click
        'Switch_LetterConvertorLink.IsOn = False
        'Switch_LetterConvertorLink_Toggled(Nothing, Nothing)
    End Sub
    Private Sub ChkLogo_CheckedChanged(sender As Object, e As EventArgs)
        'If ChkLogo.Checked = True Then
        '    bntLetterLogo.Enabled = False
        '    BntResetLetterLogo.Enabled = False
        '    Pic_QRCode.Image = Nothing
        '    lblLogoTitle.Visible = True
        '    bntLetterLogo.Enabled = False
        '    BntResetLetterLogo.Enabled = False
        '    lblLogoTitle.Tag = ""
        '    ChkLogo.ForeColor = Color.FromArgb(34, 203, 121)
        'Else
        '    bntLetterLogo.Enabled = True
        '    BntResetLetterLogo.Enabled = True
        '    lblLogoTitle.Tag = ""
        '    Pic_QRCode.Image = Nothing
        '    lblLogoTitle.Visible = True
        '    bntLetterLogo.Enabled = True
        '    BntResetLetterLogo.Enabled = True
        '    ChkLogo.ForeColor = Color.DarkGray
        'End If
        'If isLogoRemoved > -1 Then
        '    ' إذا تمت إزالة الكلمة "-Logo-" سابقاً، استعادتها
        '    txtLetter.Text = txtLetter.Text.Insert(isLogoRemoved, "[-Logo-]")
        '    isLogoRemoved = -1
        'Else
        '    ' إذا لم يتم إزالة "-Logo-" من قبل، نقوم بحذفها
        '    Dim index As Integer = txtLetter.Text.IndexOf("[-Logo-]")
        '    If index <> -1 Then
        '        txtLetter.Text = txtLetter.Text.Remove(index, "[-Logo-]".Length)
        '        isLogoRemoved = index
        '    End If
        'End If
    End Sub
    Private Sub btnGenerate_Click(sender As Object, e As EventArgs) Handles btnGenerate.Click
        If urlRegex.IsMatch(txtCode.Text) Then
            Try
                Call qrcodeGen()
            Catch ex As Exception
                XtraMessageBox.Show(ex.Message, "Error!", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End Try
        Else
            XtraMessageBox.Show("Please enter a valid link", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub
    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        If Not Pic_QRCode.Image Is Nothing Then
            ' Get the path to the user's desktop
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            ' Combine the desktop path with the file name
            Dim fileName As String = Path.Combine(desktopPath, "image.jpg")
            ' Save the image to the desktop
            Try
                Pic_QRCode.Image.Save(fileName, System.Drawing.Imaging.ImageFormat.Jpeg)
                XtraMessageBox.Show("The image has been Successfully saved to your Desktop.", "Save Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                XtraMessageBox.Show("An Error occurred while saving the image: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        Else
            XtraMessageBox.Show("There is no image to save Please Enter URL!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub SimpleButton3_Click_1(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        txtCode.Text = ""
        Pic_QRCode.Image = Nothing
        'lbl_success_QRcode.Text = ""
        txtHeight.Clear()
        txtWidth.Clear()
    End Sub
    Private Sub txt_limitSend_EditValueChanged(sender As Object, e As EventArgs) Handles txt_limitSend.EditValueChanged
        If chk_limitSend.Checked Then
            lblsuccess_Time.Text = String.Format("Send : {0} Only", Val(txt_limitSend.EditValue))
        End If
    End Sub
    Private Sub chk_limitSend_CheckedChanged(sender As Object, e As EventArgs) Handles chk_limitSend.CheckedChanged
        If chk_limitSend.Checked Then
            txt_limitSend.Enabled = True
            txt_limitSend.Focus()
        Else
            txt_limitSend.EditValue = ""
            lblsuccess_Time.Text = ""
            txt_limitSend.Enabled = False
        End If
    End Sub
    Public Sub Start_Sender()
        Dim isCancel = frmMain.Bnt_Sender_Start.Caption = "Cancel"
        frmMain.Bnt_Sender_Start.ImageOptions.Image = My.Resources.Stop32x32
        If isCancel Then
            cancellationTokenSource.Cancel()
            BackgroundWorker1.CancelAsync()
            Exit Sub
        End If
        If txtLetter.Text.Trim = "" Then
            XtraMessageBox.Show("Please add a letter...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        ' استبدال الكلمة [-Logo-] بـ <img src="cid:logoImage" />
        'txtLetter.Text = txtLetter.Text.Replace("[-Logo-]", "<img src=""cid:logoImage"" />")
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("Add atleast one Working Smtp account...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If GridView2.DataRowCount = 0 Then
            XtraMessageBox.Show("Add atleast one Working Email Account...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim FailedAccounts As Integer = 0
        Dim uncheckedAccoutns As Integer = 0
        Dim WorkingAccoutns As Integer = 0
        For i = 0 To LS_SenderSmtp.Count - 1
            Select Case LS_SenderSmtp(i).smtpstatus
                Case "Working"
                    WorkingAccoutns = WorkingAccoutns + 1
                Case "Fail"
                    FailedAccounts = FailedAccounts + 1
                Case "Unchecked"
                    uncheckedAccoutns = uncheckedAccoutns + 1
            End Select
        Next
        If FailedAccounts > 0 Then
            XtraMessageBox.Show("You have ( " & FailedAccounts & " ) Fail Smtp Accounts, Please remove the fail accounts and try again...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If uncheckedAccoutns > 0 Then
            XtraMessageBox.Show("You have ( " & uncheckedAccoutns & " ) Unchecked Smtp Accounts, please check them and try again...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If WorkingAccoutns = 0 Then
            XtraMessageBox.Show("You have ( 0 ) Working Smtp Accounts. Add aleast one Working account and try again...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Tick_ = 0
        Timer1.Enabled = True
        pnlResult.Visible = True
        pnlWait.Visible = True
        If GridView2.DataRowCount = 0 Then
            XtraMessageBox.Show("Add atleast one email address to start...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("Add atleast one Smtp account to start...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        frmMain.Bnt_Sender_Start.Caption = "Cancel"
        frmMain.Bnt_Sender_Start.ImageOptions.Image = My.Resources.Stop32x32
        BackgroundWorker1.WorkerSupportsCancellation = True
        BackgroundWorker1.WorkerReportsProgress = True
        '==================================================================================================
        DevExpress.Data.CurrencyDataController.DisableThreadingProblemsDetection = True
        Timer1.Enabled = True
        ProgressBarControl1.Properties.Step = 1
        ProgressBarControl1.Position = 0
        ProgressBarControl1.EditValue = 0
        ProgressBarControl1.Properties.Maximum = MailTable.Rows.Count
        lblSuccess.Text = "Sent : 0"
        lblfailed.Text = "Failed : 0"
        'For i = 0 To LS_SenderMail.Count - 1
        '    LS_SenderMail(i).deliverystatus = "Ready to Receive"
        'Next
        If Not BackgroundWorker1.IsBusy Then
            pnlWait.Visible = True
            isRunning_ = True
            BackgroundWorker1.RunWorkerAsync()
        Else
            isRunning_ = False
            BackgroundWorker1.CancelAsync()
        End If
    End Sub
    Private Sub trcThreads_EditValueChanged(sender As Object, e As EventArgs) Handles trcThreads.EditValueChanged
        lblTrcThreads.Text = trcThreads.Value
    End Sub
    Private Sub bntAddAttachment_Click(sender As Object, e As EventArgs) Handles bntAddAttachment.Click
        If lblattacmentFileCount.Text = "1" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("You already have an attachment file...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        Dim dialog As OpenFileDialog = New OpenFileDialog With {
        .Filter = "All files (*.*)|*.*"
    }
        If dialog.ShowDialog() = DialogResult.OK Then
            'txtattach.Text = "Check Payment Attachments[-Name-]"
            txtattachTextGlobal = dialog.FileName
            lblattacmentFileCount.Text = "Attached files : 1"
            lblattacmentFileCount.ForeColor = Color.FromArgb(34, 203, 121)
            ToolTip2.SetToolTip(lblattacmentFileCount, dialog.FileName)
        End If
    End Sub
    Private Sub BntRemoveAttachment_Click(sender As Object, e As EventArgs) Handles BntRemoveAttachment.Click
        cbTags.SelectedIndex = -1
        ToolTip2.SetToolTip(lblattacmentFileCount, "")
        txtbody.Tag = ""
        txtbody.Text = ""
        txtattach.Text = ""
        txtattach.Clear()
        txtattach.ResetText()
        txtattachTextGlobal = ""
        lblattacmentFileCount.Text = "Attached files : 0"
        lblattacmentFileCount.ForeColor = Color.DarkGray
        If System.IO.Directory.Exists(TempPath) Then
            Try
                For Each deleteFile In Directory.GetFiles(TempPath, "*.*", SearchOption.TopDirectoryOnly)
                    File.Delete(deleteFile)
                Next
            Catch ex As Exception
            End Try
        End If
    End Sub
    Private Sub cbTags_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cbTags.SelectedIndexChanged
        If cbTags.SelectedIndex >= 0 Then
            If cbTags.SelectedItem.ToString() = "Receiver Email" Then
                txtattach.Text = "[-Email-]"
            ElseIf cbTags.SelectedItem.ToString() = "Encoded Email 64" Then
                txtattach.Text = "[-Email64-]"
            ElseIf cbTags.SelectedItem.ToString() = "Start Name With Upper Case" Then
                txtattach.Text = "[-UCase-]"
            ElseIf cbTags.SelectedItem.ToString() = "Add Link" Then
                txtattach.Text = "[-Link-]"
            ElseIf cbTags.SelectedItem.ToString() = "Send With Logo" Then
                txtattach.Text = "[-Logo-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Character 3" Then
                txtattach.Text = "[-RCh3-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Character 4" Then
                txtattach.Text = "[-RCh4-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Character 5" Then
                txtattach.Text = "[-RCh5-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Character 6" Then
                txtattach.Text = "[-RCh6-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Character 7" Then
                txtattach.Text = "[-RCh7-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Number 3" Then
                txtattach.Text = "[-RN3-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Number 4" Then
                txtattach.Text = "[-RN4-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Number 5" Then
                txtattach.Text = "[-RN5-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Number 6" Then
                txtattach.Text = "[-RN6-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Number 7" Then
                txtattach.Text = "[-RN7-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random IP" Then
                txtattach.Text = "[-IP-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random IP China" Then
                txtattach.Text = "[-IPChina-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show The Domain" Then
                txtattach.Text = "[-Domain-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Name" Then
                txtattach.Text = "[-Name-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Company Name" Then
                txtattach.Text = "[-CompanyName-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Date" Then
                txtattach.Text = "[-Date-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Time" Then
                txtattach.Text = "[-Time-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Date Tomorrow" Then
                txtattach.Text = "[-DateTomorrow-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Country" Then
                txtattach.Text = "[-RCountry-]"
            ElseIf cbTags.SelectedItem.ToString() = "Random Browser" Then
                txtattach.Text = "[-RandomBrowser-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                txtattach.Text = "[-FakePhone-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                txtattach.Text = "[-FakeEmail-]"
            ElseIf cbTags.SelectedItem.ToString() = "Show Random Street New York" Then
                txtattach.Text = "[-NewYork-]"
            End If
        End If
    End Sub
    Private Sub bntLetterLogo_Click(sender As Object, e As EventArgs) Handles bntLetterLogo.Click
        Dim ofd As New OpenFileDialog
        With ofd
            .InitialDirectory = "C:\"
            .Filter = "PNG|*.png|JPEGs|*.jpg|GIFs|*.gif|Bitmaps|*.bmp|All Files|*.*"
            .FilterIndex = 1
        End With
        If ofd.ShowDialog() = Windows.Forms.DialogResult.OK Then
            lblLogoTitle.Visible = False
            With piclogo
                .Image = System.Drawing.Image.FromFile(ofd.FileName)
                .SizeMode = PictureBoxSizeMode.Zoom
                .BorderStyle = BorderStyle.FixedSingle
            End With
            ToolTip3.SetToolTip(piclogo, ofd.FileName)
            lblLogoTitle.Tag = ofd.FileName
        End If
    End Sub
    Private Sub BntResetLetterLogo_Click(sender As Object, e As EventArgs) Handles BntResetLetterLogo.Click
        piclogo.Image = Nothing
        ToolTip3.SetToolTip(piclogo, "")
        lblLogoTitle.Visible = True
        lblLogoTitle.Tag = ""
    End Sub

    Private Sub GridControl2_MouseDown(sender As Object, e As MouseEventArgs) Handles GridControl2.MouseDown
        ' التحقق من أن النقر بالزر الأيمن
        If e.Button = MouseButtons.Right Then
            If SmptTestWait.Visible = True Then Exit Sub
            Dim view As Views.Grid.GridView = TryCast(GridControl2.GetViewAt(e.Location), Views.Grid.GridView)
            If view IsNot Nothing Then
                PopupMenu2.ShowPopup(GridControl2.PointToScreen(e.Location))
            End If
        End If
    End Sub
    Private Sub BntAddMailList_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntAddMailList.ItemClick
        Add_Mail_List()
    End Sub
    Private Sub BntClearEList_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntClearEList.ItemClick
        Clear_Mail_List()
    End Sub
    Private Sub txtLetter_MouseDown(sender As Object, e As MouseEventArgs) Handles txtLetter.MouseDown
        If e.Button = MouseButtons.Right Then
            ' عرض القائمة المنبثقة عند موقع النقر
            PopupMenu3.ShowPopup(txtLetter.PointToScreen(e.Location))
        End If
    End Sub
    Private Sub BarButtonItem13_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem13.ItemClick
        Add_Letter()
    End Sub
    Private Sub BarButtonItem14_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem14.ItemClick
        Clear_Letter()
        txtNewLink.Clear()
    End Sub
    Private Sub BarButtonItem16_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem16.ItemClick
        If Clipboard.ContainsText() Then
            txtLetter.Text = Clipboard.GetText()
        Else
            XtraMessageBox.Show("Clipboard does not contain any text.", "Notice", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub frmEmailSender_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        ' تعطيل مجموعات الريبون
        frmMain.RibbonPageGroup16.Visible = False
        frmMain.RibbonPageGroup13.Visible = False
        frmMain.RibbonPageGroup15.Visible = False
        frmMain.RibbonPageGroup24.Visible = False
        frmMain.RibbonPageGroup14.Visible = False
        frmMain.RibbonPageGroup4.Visible = False
        frmMain.RibbonPageGroup12.Visible = False
        ' تعطيل عناصر القوائم الفرعية
        frmMain.BarSubItem4.Enabled = False
        frmMain.BarSubItem3.Enabled = False
        frmMain.BarSubItem5.Enabled = False
        ' تعطيل الأزرار
        frmMain.Bnt_Sender_Start.Enabled = False
        frmMain.Bnt_Tag.Enabled = False
        frmMain.Bnt_Save_AllSettings.Enabled = False
        frmMain.Bnt_Reset.Enabled = False
    End Sub
    Private Sub txtLetter_MouseEnter(sender As Object, e As EventArgs) Handles txtLetter.MouseEnter
        txtLetter.ForeColor = Color.FromArgb(254, 219, 65)
        txtLetter.BackColor = Color.FromArgb(26, 26, 29)
    End Sub
    Private Sub txtLetter_MouseLeave(sender As Object, e As EventArgs) Handles txtLetter.MouseLeave
        txtLetter.BackColor = Color.FromArgb(26, 26, 29)
        txtLetter.ForeColor = Color.White
    End Sub
    Private Sub GridView1_CustomDrawCell(sender As Object, e As RowCellCustomDrawEventArgs) Handles GridView1.CustomDrawCell
        ' Handle specific columns
        If e.Column.FieldName = "id" Then
            ' Draw the ID number only (no icons)
            Dim idValue As String = ""
            If e.RowHandle >= 0 AndAlso GridView1.GetRowCellValue(e.RowHandle, "id") IsNot Nothing Then
                idValue = GridView1.GetRowCellValue(e.RowHandle, "id").ToString()
            End If

            ' Set background color based on even/odd row (Binance-inspired)
            Dim backgroundColor As Color
            If e.RowHandle Mod 2 = 0 Then
                backgroundColor = Color.FromArgb(18, 22, 28) ' Even rows
            Else
                backgroundColor = Color.FromArgb(30, 35, 41) ' Odd rows
            End If

            ' If row is selected, override the background color
            If GridView1.IsRowSelected(e.RowHandle) Then
                backgroundColor = Color.FromArgb(47, 52, 59)
            End If

            ' Apply the background color
            Using brush As New SolidBrush(backgroundColor)
                e.Graphics.FillRectangle(brush, e.Bounds)
            End Using

            ' Set text color to white
            e.Appearance.ForeColor = Color.FromArgb(255, 255, 255)
            e.Appearance.Options.UseForeColor = True

            ' Set font to Segoe UI with 10pt size
            Try
                e.Appearance.Font = New Font("Segoe UI", 10)
                e.Appearance.Options.UseFont = True
            Catch ex As Exception
                ' If there's an error with the font, just use the default
            End Try

            ' Center the ID text
            e.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center

            ' Just draw the ID text without any icons
            e.Appearance.DrawString(e.Cache, idValue, e.Bounds, e.Appearance.GetStringFormat())

            ' Make sure the event is handled
            e.Handled = True
        ElseIf e.Column.FieldName = "smtpstatus" Then
            ' Get the status value
            Dim statusValue As String = ""
            If e.CellValue IsNot Nothing Then
                statusValue = e.CellValue.ToString()
            End If

            ' Set background color based on even/odd row (Binance-inspired)
            Dim backgroundColor As Color
            If e.RowHandle Mod 2 = 0 Then
                backgroundColor = Color.FromArgb(18, 22, 28) ' Even rows
            Else
                backgroundColor = Color.FromArgb(30, 35, 41) ' Odd rows
            End If

            ' If row is selected, override the background color
            If GridView1.IsRowSelected(e.RowHandle) Then
                backgroundColor = Color.FromArgb(47, 52, 59)
            End If

            ' Apply the background color
            Using brush As New SolidBrush(backgroundColor)
                e.Graphics.FillRectangle(brush, e.Bounds)
            End Using

            ' Determine which icon to use and set Binance-inspired colors
            Dim icon As System.Drawing.Bitmap = Nothing
            Select Case statusValue
                Case "Working"
                    icon = DirectCast(My.Resources.Valid16x16, System.Drawing.Bitmap)
                    ' Positive values (profits) - Binance green
                    e.Appearance.ForeColor = Color.FromArgb(0, 194, 146)
                    e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                Case "Unchecked"
                    icon = DirectCast(My.Resources.pending16x16, System.Drawing.Bitmap)
                    ' Neutral values - Soft yellow/orange
                    e.Appearance.ForeColor = Color.FromArgb(254, 153, 35)
                    e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                Case "Fail"
                    icon = DirectCast(My.Resources.Notworking16x16, System.Drawing.Bitmap)
                    ' Negative values (losses) - Binance red
                    e.Appearance.ForeColor = Color.FromArgb(255, 76, 76)
                    e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
                Case "Verifing"
                    icon = DirectCast(My.Resources.pending16x16, System.Drawing.Bitmap)
                    ' Processing status - Yellow
                    e.Appearance.ForeColor = Color.FromArgb(254, 219, 65)
                    e.Appearance.Font = New Font("Segoe UI", 10, System.Drawing.FontStyle.Bold)
            End Select

            e.Appearance.Options.UseFont = True
            e.Appearance.Options.UseForeColor = True

            ' If we have an icon, draw it
            If icon IsNot Nothing Then
                ' Calculate positions
                Dim iconRect As Rectangle = New Rectangle(e.Bounds.X + 5, e.Bounds.Y + (e.Bounds.Height - 16) \ 2, 16, 16)
                Dim textRect As Rectangle = New Rectangle(iconRect.Right + 5, e.Bounds.Y, e.Bounds.Width - iconRect.Width - 10, e.Bounds.Height)

                ' Draw the icon - use the correct overload
                e.Graphics.DrawImage(icon, iconRect.X, iconRect.Y, iconRect.Width, iconRect.Height)

                ' Draw the text
                e.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
                e.Appearance.DrawString(e.Cache, statusValue, textRect)

                ' Indicate that we've handled the drawing
                e.Handled = True
            End If
        End If
    End Sub

    ' دالة لتهيئة تمركز العناصر
    Private Sub InitializeCenteringControls()
        Try
            ' تعيين الحجم الثابت لعناصر GroupControl
            SetGroupControlsFixedSize()

            ' تمركز عناصر GroupControl داخل XtraTabControl2
            CenterGroupControlsInTabControl()

            ' إضافة معالج حدث تغيير التبويب المحدد
            AddHandler XtraTabControl1.SelectedPageChanged, AddressOf XtraTabControl1_SelectedPageChanged
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub

    ' دالة لتمركز XtraTabControl2 داخل XtraTabControl1
    Private Sub CenterXtraTabControl2()
        Try
            ' تمركز XtraTabControl2 في منتصف XtraTabControl1
            If XtraTabControl1.SelectedTabPage IsNot Nothing AndAlso XtraTabControl2 IsNot Nothing Then
                ' حساب موقع XtraTabControl2 لتمركزه أفقيًا وعموديًا
                Dim x As Integer = (XtraTabControl1.SelectedTabPage.Width - XtraTabControl2.Width) \ 2
                Dim y As Integer = (XtraTabControl1.SelectedTabPage.Height - XtraTabControl2.Height) \ 2

                ' تعيين موقع XtraTabControl2
                XtraTabControl2.Location = New System.Drawing.Point(Math.Max(0, x), Math.Max(0, y))

                ' تمركز عناصر GroupControl داخل XtraTabControl2
                CenterGroupControlsInTabControl()
            End If
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub

    ' دالة لتعيين الحجم الثابت لعناصر GroupControl
    Private Sub SetGroupControlsFixedSize()
        Try
            ' تعيين الحجم الثابت لجميع عناصر GroupControl المطلوبة
            If GroupControl4 IsNot Nothing Then GroupControl4.Size = New System.Drawing.Size(416, 327)
            If GroupControl5 IsNot Nothing Then GroupControl5.Size = New System.Drawing.Size(416, 327)
            If GroupControl6 IsNot Nothing Then GroupControl6.Size = New System.Drawing.Size(416, 327)
            If GroupControl7 IsNot Nothing Then GroupControl7.Size = New System.Drawing.Size(416, 327)
            If GroupControl8 IsNot Nothing Then GroupControl8.Size = New System.Drawing.Size(416, 327)
            If GroupControl9 IsNot Nothing Then GroupControl9.Size = New System.Drawing.Size(416, 327)
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub

    ' دالة لتمركز عناصر GroupControl داخل XtraTabControl2
    Private Sub CenterGroupControlsInTabControl()
        Try
            ' تمركز كل GroupControl في صفحة التبويب الخاصة به
            For Each tabPage As DevExpress.XtraTab.XtraTabPage In XtraTabControl2.TabPages
                For Each ctrl As System.Windows.Forms.Control In tabPage.Controls
                    If TypeOf ctrl Is DevExpress.XtraEditors.GroupControl Then
                        Dim groupCtrl As DevExpress.XtraEditors.GroupControl = TryCast(ctrl, DevExpress.XtraEditors.GroupControl)

                        If groupCtrl IsNot Nothing Then
                            ' حساب موقع GroupControl لتمركزه أفقيًا وعموديًا
                            Dim x As Integer = (tabPage.Width - groupCtrl.Width) \ 2
                            Dim y As Integer = (tabPage.Height - groupCtrl.Height) \ 2

                            ' تعيين موقع GroupControl
                            groupCtrl.Location = New System.Drawing.Point(Math.Max(0, x), Math.Max(0, y))
                        End If
                    End If
                Next
            Next
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub

    ' معالج حدث تغيير التبويب المحدد في XtraTabControl1
    Private Sub XtraTabControl1_SelectedPageChanged(sender As Object, e As DevExpress.XtraTab.TabPageChangedEventArgs)
        Try
            ' إعادة تمركز XtraTabControl2 عند تغيير التبويب المحدد
            CenterXtraTabControl2()
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub

    ' معالج حدث تغيير حجم النموذج
    Private Sub frmEmailSender_Resize(sender As Object, e As EventArgs) Handles Me.Resize
        Try
            ' إعادة تمركز XtraTabControl2 عند تغيير حجم النموذج
            CenterXtraTabControl2()

            ' إعادة تمركز SmptTestWait عند تغيير حجم النموذج
            CenterSmptTestWait()
        Catch ex As Exception
            ' معالجة الأخطاء
        End Try
    End Sub

    ' دالة لتمركز SmptTestWait في وسط Panel2
    Private Sub CenterSmptTestWait()
        If SmptTestWait IsNot Nothing AndAlso Panel2 IsNot Nothing Then
            ' حساب الموضع المركزي
            Dim x As Integer = (Panel2.Width - SmptTestWait.Width) \ 2
            Dim y As Integer = (Panel2.Height - SmptTestWait.Height) \ 2

            ' تعيين موضع SmptTestWait باستخدام System.Drawing.Point
            SmptTestWait.Location = New System.Drawing.Point(x, y)
        End If
    End Sub

    ''' <summary>
    ''' تهيئة زر إيقاف الفحص
    ''' </summary>
    Private Sub InitializeBntStopCheckProxy()
        Try
            ' التحقق من أن BntStopCheckProxy ليس null
            If BntStopCheckProxy Is Nothing Then
                BntStopCheckProxy = New DevExpress.XtraEditors.SimpleButton()
            End If

            ' تكوين خصائص الزر بالألوان الجديدة
            BntStopCheckProxy.Appearance.BackColor = System.Drawing.Color.FromArgb(254, 219, 65)
            BntStopCheckProxy.Appearance.BorderColor = System.Drawing.Color.FromArgb(254, 219, 65)
            BntStopCheckProxy.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
            BntStopCheckProxy.Appearance.ForeColor = System.Drawing.Color.FromArgb(26, 26, 29)
            BntStopCheckProxy.Appearance.Options.UseBackColor = True
            BntStopCheckProxy.Appearance.Options.UseBorderColor = True
            BntStopCheckProxy.Appearance.Options.UseFont = True
            BntStopCheckProxy.Appearance.Options.UseForeColor = True
            BntStopCheckProxy.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(240, 205, 50)
            BntStopCheckProxy.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(240, 205, 50)
            BntStopCheckProxy.AppearanceHovered.ForeColor = System.Drawing.Color.FromArgb(26, 26, 29)
            BntStopCheckProxy.AppearanceHovered.Options.UseBackColor = True
            BntStopCheckProxy.AppearanceHovered.Options.UseBorderColor = True
            BntStopCheckProxy.AppearanceHovered.Options.UseForeColor = True
            BntStopCheckProxy.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(230, 195, 40)
            BntStopCheckProxy.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(230, 195, 40)
            BntStopCheckProxy.AppearancePressed.ForeColor = System.Drawing.Color.FromArgb(26, 26, 29)
            BntStopCheckProxy.AppearancePressed.Options.UseBackColor = True
            BntStopCheckProxy.AppearancePressed.Options.UseBorderColor = True
            BntStopCheckProxy.AppearancePressed.Options.UseForeColor = True
            BntStopCheckProxy.Cursor = System.Windows.Forms.Cursors.Hand
            BntStopCheckProxy.Location = New System.Drawing.Point(348, 16)
            BntStopCheckProxy.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
            BntStopCheckProxy.Name = "BntStopCheckProxy"
            BntStopCheckProxy.Size = New System.Drawing.Size(158, 45)
            BntStopCheckProxy.TabIndex = 4
            BntStopCheckProxy.Text = "Stop Check"
            BntStopCheckProxy.Visible = False

            ' إضافة الزر إلى Panel5
            If Panel5 IsNot Nothing Then
                Panel5.Controls.Add(BntStopCheckProxy)
            End If

            ' إضافة معالج الحدث
            AddHandler BntStopCheckProxy.Click, AddressOf OnStopCheckProxyClick
        Catch ex As Exception
            MessageBox.Show("Error initializing stop check button: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تهيئة PopupMenu5 للبروكسي
    ''' </summary>
    Private Sub InitializePopupMenu5()
        Try
            ' التأكد من وجود PopupMenu5
            If PopupMenu5 Is Nothing Then
                PopupMenu5 = New DevExpress.XtraBars.PopupMenu()
            End If

            ' التأكد من وجود BarManager4
            If BarManager4 Is Nothing Then
                BarManager4 = New DevExpress.XtraBars.BarManager()
                BarManager4.Form = Me
            End If

            ' تهيئة عناصر القائمة إذا لم تكن موجودة
            If BarButtonItem17 Is Nothing Then
                BarButtonItem17 = New DevExpress.XtraBars.BarButtonItem()
                BarButtonItem17.Caption = "Select List Proxy"
                BarButtonItem17.Id = 0
                BarButtonItem17.Name = "BarButtonItem17"
                BarManager4.Items.Add(BarButtonItem17)
            End If

            If BarButtonItem18 Is Nothing Then
                BarButtonItem18 = New DevExpress.XtraBars.BarButtonItem()
                BarButtonItem18.Caption = "Start Check Proxy"
                BarButtonItem18.Id = 1
                BarButtonItem18.Name = "BarButtonItem18"
                BarManager4.Items.Add(BarButtonItem18)
            End If

            If BarButtonItem19 Is Nothing Then
                BarButtonItem19 = New DevExpress.XtraBars.BarButtonItem()
                BarButtonItem19.Caption = "Remove Invalid"
                BarButtonItem19.Id = 2
                BarButtonItem19.Name = "BarButtonItem19"
                BarManager4.Items.Add(BarButtonItem19)
            End If

            If BarButtonItem20 Is Nothing Then
                BarButtonItem20 = New DevExpress.XtraBars.BarButtonItem()
                BarButtonItem20.Caption = "Remove All"
                BarButtonItem20.Id = 3
                BarButtonItem20.Name = "BarButtonItem20"
                BarManager4.Items.Add(BarButtonItem20)
            End If

            If BarButtonItem21 Is Nothing Then
                BarButtonItem21 = New DevExpress.XtraBars.BarButtonItem()
                BarButtonItem21.Caption = "Save Proxy Valid"
                BarButtonItem21.Id = 4
                BarButtonItem21.Name = "BarButtonItem21"
                BarManager4.Items.Add(BarButtonItem21)
            End If

            ' ربط القائمة بالعناصر
            PopupMenu5.LinksPersistInfo.Clear()
            PopupMenu5.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {
                New DevExpress.XtraBars.LinkPersistInfo(BarButtonItem17),
                New DevExpress.XtraBars.LinkPersistInfo(BarButtonItem18),
                New DevExpress.XtraBars.LinkPersistInfo(BarButtonItem19),
                New DevExpress.XtraBars.LinkPersistInfo(BarButtonItem20),
                New DevExpress.XtraBars.LinkPersistInfo(BarButtonItem21)
            })

            ' ربط القائمة بالمدير
            PopupMenu5.Manager = BarManager4

        Catch ex As Exception
            Console.WriteLine("Error initializing PopupMenu5: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' تهيئة PopupMenu6 للبروكسي (الجديد)
    ''' </summary>
    Private Sub InitializePopupMenu6()
        Try
            ' التأكد من وجود PopupMenu6
            If PopupMenu6 Is Nothing Then
                PopupMenu6 = New DevExpress.XtraBars.PopupMenu()
            End If

            ' التأكد من وجود BarManager4
            If BarManager4 Is Nothing Then
                BarManager4 = New DevExpress.XtraBars.BarManager()
                BarManager4.Form = Me
            End If

            ' تهيئة عناصر القائمة الجديدة
            If BarButtonItem22 Is Nothing Then
                BarButtonItem22 = New DevExpress.XtraBars.BarButtonItem()
                BarButtonItem22.Caption = "📁 Select List Proxy"
                BarButtonItem22.Id = 5
                BarButtonItem22.Name = "BarButtonItem22"
                BarManager4.Items.Add(BarButtonItem22)
            End If

            If BarButtonItem23 Is Nothing Then
                BarButtonItem23 = New DevExpress.XtraBars.BarButtonItem()
                BarButtonItem23.Caption = "🚀 Start Check Proxy"
                BarButtonItem23.Id = 6
                BarButtonItem23.Name = "BarButtonItem23"
                BarManager4.Items.Add(BarButtonItem23)
            End If

            If BarButtonItem24 Is Nothing Then
                BarButtonItem24 = New DevExpress.XtraBars.BarButtonItem()
                BarButtonItem24.Caption = "🗑️ Remove Invalid"
                BarButtonItem24.Id = 7
                BarButtonItem24.Name = "BarButtonItem24"
                BarManager4.Items.Add(BarButtonItem24)
            End If

            If BarButtonItem25 Is Nothing Then
                BarButtonItem25 = New DevExpress.XtraBars.BarButtonItem()
                BarButtonItem25.Caption = "🧹 Remove All"
                BarButtonItem25.Id = 8
                BarButtonItem25.Name = "BarButtonItem25"
                BarManager4.Items.Add(BarButtonItem25)
            End If

            If BarButtonItem26 Is Nothing Then
                BarButtonItem26 = New DevExpress.XtraBars.BarButtonItem()
                BarButtonItem26.Caption = "💾 Save Proxy Valid"
                BarButtonItem26.Id = 9
                BarButtonItem26.Name = "BarButtonItem26"
                BarManager4.Items.Add(BarButtonItem26)
            End If

            If BntStopScan Is Nothing Then
                BntStopScan = New DevExpress.XtraBars.BarButtonItem()
                BntStopScan.Caption = "🛑 Stop Scan"
                BntStopScan.Id = 10
                BntStopScan.Name = "BntStopScan"
                BarManager4.Items.Add(BntStopScan)
            End If

            If BntRemoveAllpending Is Nothing Then
                BntRemoveAllpending = New DevExpress.XtraBars.BarButtonItem()
                BntRemoveAllpending.Caption = "⏳ Remove All Pending"
                BntRemoveAllpending.Id = 11
                BntRemoveAllpending.Name = "BntRemoveAllpending"
                BarManager4.Items.Add(BntRemoveAllpending)
            End If

            ' ربط القائمة بالعناصر
            PopupMenu6.LinksPersistInfo.Clear()
            PopupMenu6.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {
                New DevExpress.XtraBars.LinkPersistInfo(BarButtonItem22),
                New DevExpress.XtraBars.LinkPersistInfo(BarButtonItem23),
                New DevExpress.XtraBars.LinkPersistInfo(BarButtonItem24, True),
                New DevExpress.XtraBars.LinkPersistInfo(BarButtonItem25),
                New DevExpress.XtraBars.LinkPersistInfo(BarButtonItem26, True),
                New DevExpress.XtraBars.LinkPersistInfo(BntStopScan, True),
                New DevExpress.XtraBars.LinkPersistInfo(BntRemoveAllpending)
            })

            ' ربط القائمة بالمدير
            PopupMenu6.Manager = BarManager4

            ' التحقق من أن القائمة تحتوي على عناصر
            Console.WriteLine($"✅ PopupMenu6 initialized with {PopupMenu6.LinksPersistInfo.Count} buttons")
            Console.WriteLine($"✅ PopupMenu6.Manager: {If(PopupMenu6.Manager IsNot Nothing, "Set", "Null")}")
            Console.WriteLine($"✅ BarManager4: {If(BarManager4 IsNot Nothing, "Set", "Null")}")

            ' طباعة أسماء الأزرار للتأكد
            For Each link In PopupMenu6.LinksPersistInfo
                If link.Item IsNot Nothing Then
                    Console.WriteLine($"   • Button: {link.Item.Caption}")
                End If
            Next

        Catch ex As Exception
            Console.WriteLine("❌ Error initializing PopupMenu6: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' اختبار PopupMenu6 للتأكد من عمله
    ''' </summary>
    Private Sub TestPopupMenu6()
        Try
            Console.WriteLine("🧪 Testing PopupMenu6...")

            ' التحقق من وجود PopupMenu6
            If PopupMenu6 Is Nothing Then
                Console.WriteLine("❌ PopupMenu6 is null!")
                Return
            End If

            Console.WriteLine("✅ PopupMenu6 exists")
            Console.WriteLine($"✅ PopupMenu6 has {PopupMenu6.LinksPersistInfo.Count} items")
            Console.WriteLine($"✅ PopupMenu6.Manager: {If(PopupMenu6.Manager IsNot Nothing, "Set", "Null")}")

            ' التحقق من وجود GridControl3
            If GridControl3 Is Nothing Then
                Console.WriteLine("❌ GridControl3 is null!")
                Return
            End If

            Console.WriteLine("✅ GridControl3 exists")
            Console.WriteLine($"✅ GridControl3.Name: {GridControl3.Name}")
            Console.WriteLine($"✅ GridControl3.Visible: {GridControl3.Visible}")
            Console.WriteLine($"✅ GridControl3.Enabled: {GridControl3.Enabled}")

            Console.WriteLine("🎯 PopupMenu6 test completed - Ready for right-click!")

        Catch ex As Exception
            Console.WriteLine($"❌ Error testing PopupMenu6: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تهيئة BackgroundWorker للبروكسي
    ''' </summary>
    Private Sub InitializeProxyCheckWorker()
        Try
            ' إنشاء BackgroundWorker جديد
            _proxyCheckWorker = New BackgroundWorker()

            ' تمكين الإلغاء والتقرير عن التقدم
            _proxyCheckWorker.WorkerReportsProgress = True
            _proxyCheckWorker.WorkerSupportsCancellation = True

            ' ربط معالجات الأحداث
            AddHandler _proxyCheckWorker.DoWork, AddressOf ProxyCheckWorker_DoWork
            AddHandler _proxyCheckWorker.ProgressChanged, AddressOf ProxyCheckWorker_ProgressChanged
            AddHandler _proxyCheckWorker.RunWorkerCompleted, AddressOf ProxyCheckWorker_RunWorkerCompleted

            Console.WriteLine("✅ ProxyCheckWorker initialized successfully")

        Catch ex As Exception
            Console.WriteLine($"❌ Error initializing ProxyCheckWorker: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث DoWork للـ ProxyCheckWorker
    ''' </summary>
    Private Sub ProxyCheckWorker_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs)
        Try
            Dim worker As BackgroundWorker = DirectCast(sender, BackgroundWorker)

            ' فحص كل بروكسي
            For i As Integer = 0 To _proxyList.Count() - 1
                ' التحقق من طلب الإلغاء
                If worker.CancellationPending Then
                    e.Cancel = True
                    Return
                End If

                ' فحص البروكسي
                Dim proxyItem As ProxyItem = _proxyList(i)
                Dim isValid As Boolean = CheckProxy(proxyItem.Proxy)

                ' تحديث حالة البروكسي
                If isValid Then
                    proxyItem.Status = ProxyStatus.Valid
                Else
                    proxyItem.Status = ProxyStatus.NotWorking
                End If

                ' الإبلاغ عن التقدم
                worker.ReportProgress(i + 1, proxyItem)

                ' انتظار قصير لتجنب استهلاك موارد النظام
                Thread.Sleep(100)
            Next

        Catch ex As Exception
            e.Result = ex
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث ProgressChanged للـ ProxyCheckWorker
    ''' </summary>
    Private Sub ProxyCheckWorker_ProgressChanged(sender As Object, e As System.ComponentModel.ProgressChangedEventArgs)
        Try
            ' تحديث شريط التقدم
            If ProgressBarControl3 IsNot Nothing Then
                ProgressBarControl3.EditValue = e.ProgressPercentage
            End If

            ' تحديث GridControl3 إذا كان هناك بيانات
            If e.UserState IsNot Nothing Then
                UpdateProxyGridControl()
            End If

        Catch ex As Exception
            Console.WriteLine($"Error in ProxyCheckWorker_ProgressChanged: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث RunWorkerCompleted للـ ProxyCheckWorker
    ''' </summary>
    Private Sub ProxyCheckWorker_RunWorkerCompleted(sender As Object, e As System.ComponentModel.RunWorkerCompletedEventArgs)
        Try
            ' إعادة تمكين الأزرار
            ToggleProxyButtons(True)

            ' إخفاء شريط التقدم
            If ProgressBarControl3 IsNot Nothing Then
                ProgressBarControl3.EditValue = 0
            End If

            ' التحقق من الإلغاء
            If e.Cancelled Then
                Console.WriteLine("🛑 Proxy check was cancelled by user")
                DevExpress.XtraEditors.XtraMessageBox.Show("Proxy checking was cancelled.", "Cancelled", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' التحقق من وجود خطأ
            If e.Error IsNot Nothing Then
                Console.WriteLine($"❌ Error in proxy check: {e.Error.Message}")
                DevExpress.XtraEditors.XtraMessageBox.Show($"Error checking proxies: {e.Error.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            ' تحديث النتائج النهائية
            UpdateProxyGridControl()

            Dim validCount As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.Valid).Count()
            Dim invalidCount As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.NotWorking).Count()

            Console.WriteLine($"✅ Proxy check completed: {validCount} valid, {invalidCount} invalid")
            DevExpress.XtraEditors.XtraMessageBox.Show($"Proxy check completed successfully!{Environment.NewLine}Valid: {validCount}{Environment.NewLine}Invalid: {invalidCount}", "Check Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            Console.WriteLine($"Error in ProxyCheckWorker_RunWorkerCompleted: {ex.Message}")
        End Try
    End Sub

#Region "PopupMenu6 Event Handlers"

    ''' <summary>
    ''' معالج حدث "Select List Proxy" من PopupMenu6
    ''' </summary>
    Private Sub OnPopupMenu6_SelectListProxy(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        Try
            Console.WriteLine("🖱️ PopupMenu6: Select List Proxy clicked")
            ' استدعاء نفس دالة PopupMenu5
            OnSelectListProxyClick(sender, e)
        Catch ex As Exception
            Console.WriteLine($"❌ Error in PopupMenu6_SelectListProxy: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث "Start Check Proxy" من PopupMenu6
    ''' </summary>
    Private Sub OnPopupMenu6_StartCheckProxy(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        Try
            Console.WriteLine("🖱️ PopupMenu6: Start Check Proxy clicked")
            ' استدعاء نفس دالة PopupMenu5
            OnStartCheckProxyClick(sender, e)
        Catch ex As Exception
            Console.WriteLine($"❌ Error in PopupMenu6_StartCheckProxy: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث "Remove Invalid" من PopupMenu6
    ''' </summary>
    Private Sub OnPopupMenu6_RemoveInvalid(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        Try
            Console.WriteLine("🖱️ PopupMenu6: Remove Invalid clicked")
            ' استدعاء نفس دالة PopupMenu5
            OnRemoveInvalidClick(sender, e)
        Catch ex As Exception
            Console.WriteLine($"❌ Error in PopupMenu6_RemoveInvalid: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث "Remove All" من PopupMenu6
    ''' </summary>
    Private Sub OnPopupMenu6_RemoveAll(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        Try
            Console.WriteLine("🖱️ PopupMenu6: Remove All clicked")
            ' استدعاء نفس دالة PopupMenu5
            OnRemoveAllClick(sender, e)
        Catch ex As Exception
            Console.WriteLine($"❌ Error in PopupMenu6_RemoveAll: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث "Save Proxy Valid" من PopupMenu6
    ''' </summary>
    Private Sub OnPopupMenu6_SaveProxyValid(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        Try
            Console.WriteLine("🖱️ PopupMenu6: Save Proxy Valid clicked")
            ' استدعاء نفس دالة PopupMenu5
            OnSaveProxyValidClick(sender, e)
        Catch ex As Exception
            Console.WriteLine($"❌ Error in PopupMenu6_SaveProxyValid: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث "Stop Scan" من PopupMenu6
    ''' </summary>
    Private Sub OnPopupMenu6_StopScan(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        Try
            Console.WriteLine("🖱️ PopupMenu6: Stop Scan clicked")

            ' إيقاف فحص البروكسي - التحقق من كلا BackgroundWorkers
            Dim stoppedAny As Boolean = False

            ' إيقاف _proxyCheckWorker
            If _proxyCheckWorker IsNot Nothing AndAlso _proxyCheckWorker.IsBusy Then
                _proxyCheckWorker.CancelAsync()
                Console.WriteLine("🛑 _proxyCheckWorker stopped by user")
                stoppedAny = True
            End If

            ' إيقاف BackgroundWorker3 (للتوافق مع الكود الموجود)
            If BackgroundWorker3 IsNot Nothing AndAlso BackgroundWorker3.IsBusy Then
                BackgroundWorker3.CancelAsync()
                Console.WriteLine("🛑 BackgroundWorker3 stopped by user")
                stoppedAny = True
            End If

            If stoppedAny Then
                Console.WriteLine("🛑 Proxy check scan stopped by user")
                DevExpress.XtraEditors.XtraMessageBox.Show("Proxy scanning has been stopped.", "Scan Stopped", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                Console.WriteLine("⚠️ No active proxy scan to stop")
                DevExpress.XtraEditors.XtraMessageBox.Show("No active proxy scanning to stop.", "No Active Scan", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            Console.WriteLine($"❌ Error in PopupMenu6_StopScan: {ex.Message}")
            DevExpress.XtraEditors.XtraMessageBox.Show($"Error stopping scan: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث "Remove All Pending" من PopupMenu6
    ''' </summary>
    Private Sub OnPopupMenu6_RemoveAllPending(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        Try
            Console.WriteLine("🖱️ PopupMenu6: Remove All Pending clicked")

            ' عد البروكسيات المعلقة
            Dim pendingCount As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.Pending).Count()

            If pendingCount = 0 Then
                DevExpress.XtraEditors.XtraMessageBox.Show("No pending proxies found to remove.", "No Pending Proxies", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' تأكيد الحذف
            If DevExpress.XtraEditors.XtraMessageBox.Show($"Are you sure you want to remove all {pendingCount} pending proxies?", "Confirm Remove Pending", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then

                ' حذف جميع البروكسيات المعلقة
                _proxyList.RemoveAll(Function(p) p.Status = ProxyStatus.Pending)

                ' تحديث الشبكة
                UpdateProxyGridControl()

                Console.WriteLine($"✅ Removed {pendingCount} pending proxies")
                DevExpress.XtraEditors.XtraMessageBox.Show($"Successfully removed {pendingCount} pending proxies.", "Pending Proxies Removed", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            Console.WriteLine($"❌ Error in PopupMenu6_RemoveAllPending: {ex.Message}")
            DevExpress.XtraEditors.XtraMessageBox.Show($"Error removing pending proxies: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تسجيل استخدام البروكسي مع البريد المرسل
    ''' </summary>
    Public Sub RecordProxyEmailUsage(proxyAddress As String, emailAddress As String)
        Try
            ' تنظيف عنوان البروكسي من البروتوكولات
            Dim cleanProxyAddress As String = proxyAddress.Replace("http://", "").Replace("https://", "")

            ' البحث عن البروكسي في القائمة بطريقة أكثر دقة
            Dim proxyItem = _proxyList.FirstOrDefault(Function(p) p.Proxy = cleanProxyAddress OrElse p.Proxy.Contains(cleanProxyAddress))

            If proxyItem IsNot Nothing Then
                ' تحديث معلومات الاستخدام
                proxyItem.LastUsedEmail = emailAddress
                proxyItem.EmailCount += 1
                proxyItem.LastUsedDate = DateTime.Now

                Console.WriteLine($"📧 ✅ Recorded email usage for proxy {proxyItem.Proxy}")
                Console.WriteLine($"📧 📩 Email: {emailAddress}")
                Console.WriteLine($"📧 📊 Total emails sent via this proxy: {proxyItem.EmailCount}")
                Console.WriteLine($"📧 🕒 Last used: {proxyItem.LastUsedDate:yyyy-MM-dd HH:mm:ss}")

                ' تحديث الشبكة في الخيط الرئيسي
                If Me.InvokeRequired Then
                    Me.Invoke(Sub() UpdateProxyGridControl())
                Else
                    UpdateProxyGridControl()
                End If
            Else
                Console.WriteLine($"⚠️ Proxy not found in list for recording: {cleanProxyAddress}")
                ' طباعة قائمة البروكسيات المتاحة للتشخيص
                Console.WriteLine("Available proxies:")
                For Each p In _proxyList
                    Console.WriteLine($"  - {p.Proxy}")
                Next
            End If
        Catch ex As Exception
            Console.WriteLine($"❌ Error recording proxy email usage: {ex.Message}")
            Console.WriteLine($"Stack trace: {ex.StackTrace}")
        End Try
    End Sub

    ''' <summary>
    ''' الحصول على IP العام الحالي - حل خبير شبكات
    ''' </summary>
    Private Function GetCurrentPublicIP() As String
        Try
            Using client As New TimeoutWebClient(5000) ' 5 seconds timeout

                ' محاولة عدة خدمات للحصول على IP
                Dim ipServices() As String = {
                    "https://api.ipify.org",
                    "https://icanhazip.com",
                    "https://ipinfo.io/ip",
                    "https://checkip.amazonaws.com"
                }

                For Each service In ipServices
                    Try
                        Dim ip As String = client.DownloadString(service).Trim()
                        If Not String.IsNullOrEmpty(ip) AndAlso IsValidIP(ip) Then
                            Console.WriteLine($"🌐 Current Public IP detected: {ip} (via {service})")
                            Return ip
                        End If
                    Catch ex As Exception
                        Console.WriteLine($"⚠️ Failed to get IP from {service}: {ex.Message}")
                        Continue For
                    End Try
                Next

                Return "UNKNOWN"
            End Using
        Catch ex As Exception
            Console.WriteLine($"❌ Error getting current public IP: {ex.Message}")
            Return "ERROR"
        End Try
    End Function

    ''' <summary>
    ''' الحصول على IP العام عبر البروكسي - تحقق متقدم
    ''' </summary>
    Private Function GetCurrentPublicIPThroughProxy(proxy As WebProxy) As String
        Try
            Using client As New TimeoutWebClient(10000) ' 10 seconds timeout for proxy
                client.Proxy = proxy

                ' محاولة عدة خدمات للحصول على IP عبر البروكسي
                Dim ipServices() As String = {
                    "https://api.ipify.org",
                    "https://icanhazip.com",
                    "https://ipinfo.io/ip"
                }

                For Each service In ipServices
                    Try
                        Dim ip As String = client.DownloadString(service).Trim()
                        If Not String.IsNullOrEmpty(ip) AndAlso IsValidIP(ip) Then
                            Console.WriteLine($"🔄 IP through proxy detected: {ip} (via {service})")
                            Return ip
                        End If
                    Catch ex As Exception
                        Console.WriteLine($"⚠️ Failed to get IP through proxy from {service}: {ex.Message}")
                        Continue For
                    End Try
                Next

                Return "PROXY_UNKNOWN"
            End Using
        Catch ex As Exception
            Console.WriteLine($"❌ Error getting IP through proxy: {ex.Message}")
            Return "PROXY_ERROR"
        End Try
    End Function

    ''' <summary>
    ''' التحقق من صحة عنوان IP
    ''' </summary>
    Private Function IsValidIP(ip As String) As Boolean
        Try
            Dim address As System.Net.IPAddress
            Return System.Net.IPAddress.TryParse(ip, address)
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' اختبار اتصال البروكسي المتقدم - خبير شبكات
    ''' </summary>
    Private Function TestProxyConnection(proxy As WebProxy) As Boolean
        Try
            Console.WriteLine($"🔍 Testing proxy connection: {proxy.Address}")

            Using client As New TimeoutWebClient(8000) ' 8 seconds timeout
                client.Proxy = proxy

                ' اختبار اتصال بسيط
                Dim testUrl As String = "https://httpbin.org/ip"
                Dim response As String = client.DownloadString(testUrl)

                If response.Contains("origin") Then
                    Console.WriteLine($"✅ Proxy connection test successful: {proxy.Address}")
                    Return True
                Else
                    Console.WriteLine($"❌ Proxy connection test failed: {proxy.Address}")
                    Return False
                End If
            End Using
        Catch ex As Exception
            Console.WriteLine($"❌ Proxy connection test error: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' تسجيل تفصيلي لحالة الشبكة - تشخيص خبير
    ''' </summary>
    Private Sub LogNetworkDiagnostics(proxy As WebProxy, smtpHost As String, smtpPort As Integer)
        Try
            Console.WriteLine("🔬 ===== NETWORK DIAGNOSTICS - EXPERT ANALYSIS =====")
            Console.WriteLine($"📅 Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
            Console.WriteLine($"🌐 Target SMTP: {smtpHost}:{smtpPort}")
            Console.WriteLine($"🔄 Proxy: {proxy.Address}")
            Console.WriteLine($"🔧 Default Proxy: {WebRequest.DefaultWebProxy?.GetProxy(New Uri($"http://{smtpHost}"))}")
            Console.WriteLine($"🔧 Global Proxy: {System.Net.GlobalProxySelection.Select?.GetProxy(New Uri($"http://{smtpHost}"))}")

            ' فحص متغيرات البيئة
            Console.WriteLine($"🌍 HTTP_PROXY: {Environment.GetEnvironmentVariable("HTTP_PROXY")}")
            Console.WriteLine($"🌍 HTTPS_PROXY: {Environment.GetEnvironmentVariable("HTTPS_PROXY")}")
            Console.WriteLine($"🌍 ALL_PROXY: {Environment.GetEnvironmentVariable("ALL_PROXY")}")

            ' فحص إعدادات ServicePointManager
            Console.WriteLine($"⚙️ UseNagleAlgorithm: {ServicePointManager.UseNagleAlgorithm}")
            Console.WriteLine($"⚙️ Expect100Continue: {ServicePointManager.Expect100Continue}")
            Console.WriteLine($"⚙️ SecurityProtocol: {ServicePointManager.SecurityProtocol}")
            Console.WriteLine($"⚙️ DefaultConnectionLimit: {ServicePointManager.DefaultConnectionLimit}")

            Console.WriteLine("================================================")
        Catch ex As Exception
            Console.WriteLine($"❌ Error in network diagnostics: {ex.Message}")
        End Try
    End Sub

#End Region

#Region "Proxy Management Functions"

    ''' <summary>
    ''' تهيئة GridControl3
    ''' </summary>
    Private Sub InitializeProxyGridControl()
        Try
            ' إنشاء DataTable للعرض في GridControl3
            Dim dt As New DataTable()
            dt.Columns.Add("proxy", GetType(String))
            dt.Columns.Add("edit", GetType(String))
            dt.Columns.Add("lastemail", GetType(String))
            dt.Columns.Add("status", GetType(System.Drawing.Image))

            ' ربط GridControl3 بمصدر البيانات
            GridControl3.DataSource = dt

            ' الحصول على GridView الحالي أو إنشاء واحد جديد
            Dim gridView As DevExpress.XtraGrid.Views.Grid.GridView
            If GridControl3.MainView Is Nothing Then
                gridView = New DevExpress.XtraGrid.Views.Grid.GridView(GridControl3)
                GridControl3.MainView = gridView
            Else
                gridView = DirectCast(GridControl3.MainView, DevExpress.XtraGrid.Views.Grid.GridView)
                ' مسح الأعمدة الحالية لتجنب التكرار
                gridView.Columns.Clear()
            End If

            ' تكوين الأعمدة
            ' تعيين خصائص الأعمدة
            GridColumn31.FieldName = "proxy"
            GridColumn31.Caption = "Proxy"
            GridColumn31.Visible = True
            GridColumn31.VisibleIndex = 0
            GridColumn31.OptionsColumn.AllowEdit = False

            GridColumn36.FieldName = "edit"
            GridColumn36.Caption = "Edit"
            GridColumn36.Visible = True
            GridColumn36.VisibleIndex = 1
            GridColumn36.OptionsColumn.AllowEdit = True

            ' إنشاء RepositoryItemButtonEdit للتعديل إذا لم يكن موجوداً
            If RepositoryItemButtonEdit3 Is Nothing Then
                RepositoryItemButtonEdit3 = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
            End If
            RepositoryItemButtonEdit3.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
            RepositoryItemButtonEdit3.Buttons.Clear()

            ' إنشاء زر التعديل بالطريقة الصحيحة
            Dim editButton As New DevExpress.XtraEditors.Controls.EditorButton()
            editButton.Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph
            editButton.Caption = "✏️"
            editButton.ToolTip = "Edit Proxy"
            RepositoryItemButtonEdit3.Buttons.Add(editButton)

            AddHandler RepositoryItemButtonEdit3.ButtonClick, AddressOf OnEditButtonClick
            GridColumn36.ColumnEdit = RepositoryItemButtonEdit3

            ' إضافة عمود البريد الأخير
            GridColumn37.FieldName = "lastemail"
            GridColumn37.Caption = "Last Email"
            GridColumn37.Visible = True
            GridColumn37.VisibleIndex = 2
            GridColumn37.OptionsColumn.AllowEdit = False
            GridColumn37.Width = 200

            GridColumn43.FieldName = "status"
            GridColumn43.Caption = "Status"
            GridColumn43.Visible = True
            GridColumn43.VisibleIndex = 3
            GridColumn43.OptionsColumn.AllowEdit = False

            ' إضافة الأعمدة إلى GridView (مع عمود البريد)
            gridView.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {
                GridColumn31, GridColumn36, GridColumn37, GridColumn43
            })

            ' إضافة معالج حدث مباشر للنقر على الخلايا
            AddHandler gridView.CellValueChanged, AddressOf GridView3_CellValueChanged
            AddHandler gridView.CustomRowCellEdit, AddressOf GridView3_CustomRowCellEdit

            ' تكوين خصائص GridView
            gridView.OptionsView.ShowGroupPanel = False
            gridView.OptionsView.ShowIndicator = False
            gridView.OptionsBehavior.Editable = True
            gridView.OptionsBehavior.EditingMode = DevExpress.XtraGrid.Views.Grid.GridEditingMode.Inplace

            ' تكوين مظهر GridView
            gridView.Appearance.Row.BackColor = Color.FromArgb(26, 26, 29)
            gridView.Appearance.Row.ForeColor = Color.White
            gridView.Appearance.HeaderPanel.BackColor = Color.FromArgb(26, 26, 29)
            gridView.Appearance.HeaderPanel.ForeColor = Color.White


        Catch ex As Exception
            MessageBox.Show("Error initializing proxy grid control: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' إضافة معالجات الأحداث
    ''' </summary>
    Private Sub AddProxyEventHandlers()
        Try
            ' إخفاء الأزرار المطلوب حذفها
            If BntSelectListProxy IsNot Nothing Then
                BntSelectListProxy.Visible = False
            End If
            If BntStartCheckProxy IsNot Nothing Then
                BntStartCheckProxy.Visible = False
            End If
            If BntProxyReport IsNot Nothing Then
                BntProxyReport.Visible = False
            End If

            ' التأكد من تهيئة BackgroundWorker3
            If BackgroundWorker3 Is Nothing Then
                BackgroundWorker3 = New System.ComponentModel.BackgroundWorker()
            End If

            If BntRemoveInvalid IsNot Nothing Then
                AddHandler BntRemoveInvalid.Click, AddressOf OnRemoveInvalidClick
            End If

            If BntRemoveAll IsNot Nothing Then
                AddHandler BntRemoveAll.Click, AddressOf OnRemoveAllClick
            End If

            If BntSaveProxyvalid IsNot Nothing Then
                AddHandler BntSaveProxyvalid.Click, AddressOf OnSaveProxyValidClick
            End If

            If BntProxyReport IsNot Nothing Then
                AddHandler BntProxyReport.Click, AddressOf OnProxyReportClick
            End If

            ' مفتاح التبديل لاستخدام البروكسي
            If ToggleSwitch2 IsNot Nothing Then
                AddHandler ToggleSwitch2.Toggled, AddressOf OnToggleProxyUseChanged
            End If

            ' مفتاح التبديل لحذف البروكسيات غير الصالحة تلقائياً
            If ToggleSwitch3 IsNot Nothing Then
                AddHandler ToggleSwitch3.Toggled, AddressOf OnToggleAutoRemoveInvalidChanged
            End If

            ' BackgroundWorker3 لفحص البروكسيات
            AddHandler BackgroundWorker3.DoWork, AddressOf BackgroundWorker3_DoWork
            AddHandler BackgroundWorker3.ProgressChanged, AddressOf BackgroundWorker3_ProgressChanged
            AddHandler BackgroundWorker3.RunWorkerCompleted, AddressOf BackgroundWorker3_RunWorkerCompleted

            ' تكوين BackgroundWorker3
            BackgroundWorker3.WorkerReportsProgress = True
            BackgroundWorker3.WorkerSupportsCancellation = True

            ' إضافة حدث النقر بزر الفأرة الأيمن لإظهار القائمة المنبثقة
            If GridControl3 IsNot Nothing AndAlso GridControl3.MainView IsNot Nothing Then
                AddHandler DirectCast(GridControl3.MainView, DevExpress.XtraGrid.Views.Grid.GridView).MouseUp, AddressOf GridView_MouseUp
            End If

            If Panel5 IsNot Nothing Then
                AddHandler Panel5.MouseUp, AddressOf Panel5_MouseUp
            End If

            ' إضافة معالجات الأحداث لعناصر القائمة المنبثقة PopupMenu5
            If BarButtonItem17 IsNot Nothing Then
                AddHandler BarButtonItem17.ItemClick, AddressOf OnSelectListProxyClick
            End If
            If BarButtonItem18 IsNot Nothing Then
                AddHandler BarButtonItem18.ItemClick, AddressOf OnStartCheckProxyClick
            End If
            If BarButtonItem19 IsNot Nothing Then
                AddHandler BarButtonItem19.ItemClick, AddressOf OnRemoveInvalidClick
            End If
            If BarButtonItem20 IsNot Nothing Then
                AddHandler BarButtonItem20.ItemClick, AddressOf OnRemoveAllClick
            End If
            If BarButtonItem21 IsNot Nothing Then
                AddHandler BarButtonItem21.ItemClick, AddressOf OnSaveProxyValidClick
            End If

            ' إضافة معالجات الأحداث لعناصر PopupMenu6 (الأزرار الجديدة)
            If BarButtonItem22 IsNot Nothing Then
                AddHandler BarButtonItem22.ItemClick, AddressOf OnPopupMenu6_SelectListProxy
            End If
            If BarButtonItem23 IsNot Nothing Then
                AddHandler BarButtonItem23.ItemClick, AddressOf OnPopupMenu6_StartCheckProxy
            End If
            If BarButtonItem24 IsNot Nothing Then
                AddHandler BarButtonItem24.ItemClick, AddressOf OnPopupMenu6_RemoveInvalid
            End If
            If BarButtonItem25 IsNot Nothing Then
                AddHandler BarButtonItem25.ItemClick, AddressOf OnPopupMenu6_RemoveAll
            End If
            If BarButtonItem26 IsNot Nothing Then
                AddHandler BarButtonItem26.ItemClick, AddressOf OnPopupMenu6_SaveProxyValid
            End If
            If BntStopScan IsNot Nothing Then
                AddHandler BntStopScan.ItemClick, AddressOf OnPopupMenu6_StopScan
            End If
            If BntRemoveAllpending IsNot Nothing Then
                AddHandler BntRemoveAllpending.ItemClick, AddressOf OnPopupMenu6_RemoveAllPending
            End If

            ' ربط أحداث الفأرة للقائمة المنبثقة PopupMenu6
            ' ملاحظة: GridControl3_MouseDown مربوط بـ Handles، لذا لا نحتاج AddHandler
            If GridControl3 IsNot Nothing Then
                Console.WriteLine("✅ GridControl3 found for PopupMenu6 events")

                ' ربط الحدث مع GridView3 أيضاً للتأكد
                If GridView3 IsNot Nothing Then
                    AddHandler GridView3.MouseDown, AddressOf GridView3_MouseDown_PopupMenu6
                    Console.WriteLine("✅ GridView3 MouseDown event added for PopupMenu6")
                End If
            End If

            If Panel5 IsNot Nothing Then
                AddHandler Panel5.MouseUp, AddressOf Panel5_MouseUp
            End If
        Catch ex As Exception
            MessageBox.Show("Error adding proxy event handlers: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر اختيار قائمة البروكسيات
    ''' </summary>
    Private Sub OnSelectListProxyClick(sender As Object, e As EventArgs)
        Try
            ' إنشاء مربع حوار اختيار الملف
            Dim openFileDialog As New OpenFileDialog()
            openFileDialog.Filter = "Text files (*.txt)|*.txt|All files (*.*)|*.*"
            openFileDialog.Title = "Select Proxy List File"
            openFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)

            ' عرض مربع الحوار واستيراد البروكسيات إذا تم اختيار ملف
            If openFileDialog.ShowDialog() = DialogResult.OK Then
                ImportProxiesFromFile(openFileDialog.FileName)
            End If
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error selecting proxy list: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' استيراد البروكسيات من ملف
    ''' </summary>
    Private Sub ImportProxiesFromFile(filePath As String)
        Try
            ' قراءة جميع الأسطر من الملف
            Dim lines As String() = File.ReadAllLines(filePath)

            ' مسح القائمة الحالية
            _proxyList.Clear()

            ' إضافة كل بروكسي إلى القائمة
            For Each line As String In lines
                ' تنظيف السطر من المسافات الزائدة
                Dim proxy As String = line.Trim()

                ' التحقق من أن السطر ليس فارغًا
                If Not String.IsNullOrWhiteSpace(proxy) Then
                    ' إضافة البروكسي إلى القائمة
                    _proxyList.Add(New ProxyItem With {
                        .Proxy = proxy,
                        .Status = ProxyStatus.Pending
                    })
                End If
            Next

            ' تحديث GridControl3
            UpdateProxyGridControl()

            ' تحديث التسميات
            UpdateProxyLabels()

            ' عرض رسالة نجاح
            DevExpress.XtraEditors.XtraMessageBox.Show($"Successfully imported {_proxyList.Count()} proxies.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error importing proxies: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تحديث GridControl3 بقائمة البروكسيات الحالية
    ''' </summary>
    Private Sub UpdateProxyGridControl()
        Try
            ' الحصول على DataTable من GridControl3
            Dim dt As DataTable = DirectCast(GridControl3.DataSource, DataTable)

            ' مسح جميع الصفوف
            dt.Rows.Clear()

            ' إضافة صف لكل بروكسي
            For Each proxyItem As ProxyItem In _proxyList
                Dim row As DataRow = dt.NewRow()
                row("proxy") = proxyItem.Proxy
                row("edit") = proxyItem.Proxy

                ' إضافة البريد الأخير مع عدد البرائد
                If Not String.IsNullOrEmpty(proxyItem.LastUsedEmail) Then
                    row("lastemail") = $"{proxyItem.LastUsedEmail} ({proxyItem.EmailCount})"
                Else
                    row("lastemail") = "No emails sent"
                End If

                ' تعيين الصورة المناسبة حسب حالة البروكسي
                Select Case proxyItem.Status
                    Case ProxyStatus.Pending
                        row("status") = _pendingImage
                    Case ProxyStatus.Valid
                        row("status") = _validImage
                    Case ProxyStatus.NotWorking
                        row("status") = _notWorkingImage
                End Select

                dt.Rows.Add(row)
            Next

            ' تحديث GridControl3
            GridControl3.RefreshDataSource()

            ' تحديث التسميات
            UpdateProxyLabels()
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error updating grid: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تحديث التسميات بإحصائيات البروكسيات
    ''' </summary>
    Private Sub UpdateProxyLabels()
        Try
            ' حساب عدد البروكسيات الإجمالي
            lblTotalProxy.Text = $"Total Proxies: {_proxyList.Count()}"
            lblTotalProxy.ForeColor = Color.Blue

            ' حساب عدد البروكسيات الصالحة
            Dim validCount As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.Valid).Count()
            lblProxyvalid.Text = $"Valid Proxies: {validCount}"
            lblProxyvalid.ForeColor = Color.Green

            ' حساب عدد البروكسيات غير الصالحة
            Dim invalidCount As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.NotWorking).Count()
            lblTotalPproxyInvalid.Text = $"Invalid Proxies: {invalidCount}"
            lblTotalPproxyInvalid.ForeColor = Color.Red
        Catch ex As Exception
            ' تجاهل الأخطاء هنا لتجنب تعطيل البرنامج
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر بدء فحص البروكسيات
    ''' </summary>
    Private Sub OnStartCheckProxyClick(sender As Object, e As EventArgs)
        Try
            ' التحقق من وجود بروكسيات للفحص
            If _proxyList.Count() = 0 Then
                DevExpress.XtraEditors.XtraMessageBox.Show("No proxies to check. Please import a proxy list first.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' التحقق من أن BackgroundWorker3 ليس قيد التشغيل
            If BackgroundWorker3.IsBusy Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Proxy check is already running. Please wait for it to complete.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' تهيئة ProgressBarControl3
            ProgressBarControl3.Properties.Minimum = 0
            ProgressBarControl3.Properties.Maximum = _proxyList.Count()
            ProgressBarControl3.Properties.Step = 1
            ProgressBarControl3.EditValue = 0

            ' تعطيل الأزرار أثناء الفحص
            ToggleProxyButtons(False)

            ' بدء BackgroundWorker3
            BackgroundWorker3.RunWorkerAsync()
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error starting proxy check: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تمكين أو تعطيل الأزرار
    ''' </summary>
    Private Sub ToggleProxyButtons(enabled As Boolean)
        ' إخفاء الأزرار المطلوب حذفها
        BntSelectListProxy.Visible = False
        BntStartCheckProxy.Visible = False
        BntProxyReport.Visible = False

        ' الأزرار المتبقية
        BntRemoveInvalid.Enabled = enabled
        BntRemoveAll.Enabled = enabled
        BntSaveProxyvalid.Enabled = enabled

        ' إظهار أو إخفاء زر إيقاف الفحص
        BntStopCheckProxy.Visible = Not enabled
        BntStopCheckProxy.Enabled = Not enabled
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر إيقاف فحص البروكسيات
    ''' </summary>
    Private Sub OnStopCheckProxyClick(sender As Object, e As EventArgs)
        Try
            ' التحقق من أن BackgroundWorker3 قيد التشغيل
            If Not BackgroundWorker3.IsBusy Then
                Return
            End If

            ' تأكيد الإيقاف
            If DevExpress.XtraEditors.XtraMessageBox.Show("Are you sure you want to stop the proxy check?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                Return
            End If

            ' إلغاء BackgroundWorker3
            BackgroundWorker3.CancelAsync()

            ' عرض رسالة
            DevExpress.XtraEditors.XtraMessageBox.Show("Proxy check will be stopped soon.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error stopping proxy check: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث DoWork لـ BackgroundWorker3
    ''' </summary>
    Private Sub BackgroundWorker3_DoWork(sender As Object, e As DoWorkEventArgs)
        Try
            ' الحصول على BackgroundWorker
            Dim worker As BackgroundWorker = DirectCast(sender, BackgroundWorker)

            ' فحص كل بروكسي
            For i As Integer = 0 To _proxyList.Count() - 1
                ' التحقق من طلب الإلغاء
                If worker.CancellationPending Then
                    e.Cancel = True
                    Return
                End If

                ' فحص البروكسي
                Dim proxyItem As ProxyItem = _proxyList(i)
                Dim isValid As Boolean = CheckProxy(proxyItem.Proxy)

                ' تحديث حالة البروكسي
                If isValid Then
                    proxyItem.Status = ProxyStatus.Valid
                Else
                    proxyItem.Status = ProxyStatus.NotWorking
                End If

                ' الإبلاغ عن التقدم
                worker.ReportProgress(i + 1, proxyItem)

                ' انتظار قصير لتجنب استهلاك موارد النظام
                Thread.Sleep(100)
            Next
        Catch ex As Exception
            ' تسجيل الخطأ في e.Result
            e.Result = ex
        End Try
    End Sub

    ''' <summary>
    ''' فحص البروكسي للتأكد من صلاحيته مع تسجيل مفصل
    ''' </summary>
    Private Function CheckProxy(proxyAddress As String) As Boolean
        Try
            Console.WriteLine($"🔍 Testing proxy: {proxyAddress}")

            ' تقسيم عنوان البروكسي إلى IP وPort
            Dim parts As String() = proxyAddress.Split(":"c)
            If parts.Length <> 2 Then
                Console.WriteLine($"❌ Invalid format: {proxyAddress}")
                Return False
            End If

            Dim ip As String = parts(0).Trim()
            Dim port As Integer

            ' التحقق من صحة Port
            If Not Integer.TryParse(parts(1).Trim(), port) Then
                Console.WriteLine($"❌ Invalid port: {parts(1)}")
                Return False
            End If

            ' إنشاء طلب HTTP للتحقق من البروكسي
            Dim request As HttpWebRequest = DirectCast(WebRequest.Create("http://httpbin.org/ip"), HttpWebRequest)
            request.Proxy = New WebProxy(ip, port)
            request.Timeout = 10000 ' 10 ثوان كحد أقصى
            request.Method = "GET"
            request.UserAgent = "Best-Sender-Proxy-Checker/1.0"

            ' محاولة الاتصال
            Using response As HttpWebResponse = DirectCast(request.GetResponse(), HttpWebResponse)
                If response.StatusCode = HttpStatusCode.OK Then
                    ' قراءة الاستجابة للتحقق من IP
                    Using reader As New StreamReader(response.GetResponseStream())
                        Dim responseText As String = reader.ReadToEnd()
                        Console.WriteLine($"✅ Proxy {proxyAddress} is working")
                        Console.WriteLine($"📡 Response: {responseText.Substring(0, Math.Min(100, responseText.Length))}...")
                        Return True
                    End Using
                Else
                    Console.WriteLine($"❌ Proxy {proxyAddress} returned status: {response.StatusCode}")
                    Return False
                End If
            End Using
        Catch ex As Exception
            ' أي استثناء يعني أن البروكسي غير صالح
            Console.WriteLine($"❌ Proxy {proxyAddress} failed: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' معالج حدث ProgressChanged لـ BackgroundWorker3
    ''' </summary>
    Private Sub BackgroundWorker3_ProgressChanged(sender As Object, e As ProgressChangedEventArgs)
        Try
            ' التحقق من أن ProgressBarControl3 ليس null
            If ProgressBarControl3 IsNot Nothing Then
                ' تحديث ProgressBarControl3
                ProgressBarControl3.EditValue = e.ProgressPercentage
            End If

            ' التحقق من أن e.UserState ليس null
            If e.UserState Is Nothing Then
                Return
            End If

            ' تحديث GridControl3 لإظهار حالة البروكسي الحالي
            Dim proxyItem As ProxyItem = DirectCast(e.UserState, ProxyItem)

            ' التحقق من أن GridControl3 وMainView ليسا null
            If GridControl3 Is Nothing OrElse GridControl3.MainView Is Nothing Then
                Return
            End If

            Dim gridView As DevExpress.XtraGrid.Views.Grid.GridView = DirectCast(GridControl3.MainView, DevExpress.XtraGrid.Views.Grid.GridView)

            ' التحقق من أن gridView.RowCount ليس 0
            If gridView.RowCount <= 0 Then
                Return
            End If

            ' البحث عن الصف المناسب
            For i As Integer = 0 To gridView.RowCount - 1
                ' التحقق من أن gridView.GetRowCellValue لا يعيد null
                Dim cellValue As Object = gridView.GetRowCellValue(i, "proxy")
                If cellValue IsNot Nothing AndAlso cellValue.ToString() = proxyItem.Proxy Then
                    ' تحديث حالة البروكسي
                    Select Case proxyItem.Status
                        Case ProxyStatus.Valid
                            gridView.SetRowCellValue(i, "status", _validImage)
                        Case ProxyStatus.NotWorking
                            gridView.SetRowCellValue(i, "status", _notWorkingImage)
                    End Select

                    ' تحديث GridControl3
                    gridView.UpdateCurrentRow()
                    Exit For
                End If
            Next

            ' تحديث التسميات
            UpdateProxyLabels()
        Catch ex As Exception
            ' تسجيل الخطأ لتسهيل التشخيص
            Console.WriteLine("Error in BackgroundWorker3_ProgressChanged: " & ex.Message)
            ' تجاهل الأخطاء هنا لتجنب تعطيل البرنامج
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث RunWorkerCompleted لـ BackgroundWorker3
    ''' </summary>
    Private Sub BackgroundWorker3_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs)
        Try
            ' تمكين الأزرار
            ToggleProxyButtons(True)

            ' التحقق من وجود خطأ
            If e.Error IsNot Nothing Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Error checking proxies: " & e.Error.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            ' التحقق من الإلغاء
            If e.Cancelled Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Proxy check was cancelled.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' التحقق من وجود خطأ في النتيجة
            If TypeOf e.Result Is Exception Then
                Dim ex As Exception = DirectCast(e.Result, Exception)
                DevExpress.XtraEditors.XtraMessageBox.Show("Error checking proxies: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            ' تطبيق الحذف التلقائي للبروكسيات غير الصالحة إذا كان مفعلاً
            If _autoRemoveInvalidProxies Then
                Dim autoInvalidCount As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.NotWorking).Count()
                If autoInvalidCount > 0 Then
                    ' حذف البروكسيات غير الصالحة تلقائياً
                    _proxyList.RemoveAll(Function(p) p.Status = ProxyStatus.NotWorking)

                    ' تحديث GridControl3
                    UpdateProxyGridControl()

                    Dim autoValidCount As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.Valid).Count()
                    DevExpress.XtraEditors.XtraMessageBox.Show($"Proxy check completed. {autoInvalidCount} invalid proxies were automatically removed.{Environment.NewLine}Valid proxies remaining: {autoValidCount}", "Auto-Remove Applied", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Return
                End If
            End If

            ' تحديث GridControl3
            UpdateProxyGridControl()

            ' عرض رسالة نجاح
            Dim finalValidCount As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.Valid).Count()
            Dim finalInvalidCount As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.NotWorking).Count()
            DevExpress.XtraEditors.XtraMessageBox.Show($"Proxy check completed.{Environment.NewLine}Valid: {finalValidCount}{Environment.NewLine}Invalid: {finalInvalidCount}", "Completed", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error completing proxy check: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر حذف البروكسيات غير الصالحة
    ''' </summary>
    Private Sub OnRemoveInvalidClick(sender As Object, e As EventArgs)
        Try
            ' التحقق من وجود GridControl3 وMainView
            If GridControl3 Is Nothing OrElse GridControl3.MainView Is Nothing Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Grid control is not available.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            Dim gridView As DevExpress.XtraGrid.Views.Grid.GridView = DirectCast(GridControl3.MainView, DevExpress.XtraGrid.Views.Grid.GridView)

            ' البحث عن الصفوف التي تحتوي على صورة Notworking16x16
            Dim rowsToRemove As New List(Of Integer)
            For i As Integer = 0 To gridView.RowCount - 1
                Dim statusImage As Object = gridView.GetRowCellValue(i, "status")
                If statusImage IsNot Nothing AndAlso TypeOf statusImage Is System.Drawing.Image Then
                    Dim img As System.Drawing.Image = DirectCast(statusImage, System.Drawing.Image)
                    ' مقارنة الصورة مع صورة NotWorking
                    If img.Equals(_notWorkingImage) Then
                        rowsToRemove.Add(i)
                    End If
                End If
            Next

            ' التحقق من وجود بروكسيات غير صالحة
            If rowsToRemove.Count = 0 Then
                DevExpress.XtraEditors.XtraMessageBox.Show("No invalid proxies to remove.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' تأكيد الحذف
            If DevExpress.XtraEditors.XtraMessageBox.Show($"Are you sure you want to remove {rowsToRemove.Count} invalid proxies?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                Return
            End If

            ' حذف البروكسيات غير الصالحة من القائمة
            Dim proxiesToRemove As New List(Of String)
            For Each rowIndex As Integer In rowsToRemove
                Dim proxy As String = gridView.GetRowCellValue(rowIndex, "proxy").ToString()
                proxiesToRemove.Add(proxy)
            Next

            ' حذف البروكسيات من القائمة الأساسية
            For Each proxy As String In proxiesToRemove
                _proxyList.RemoveAll(Function(p) p.Proxy = proxy)
            Next

            ' تحديث GridControl3
            UpdateProxyGridControl()

            ' عرض رسالة نجاح
            DevExpress.XtraEditors.XtraMessageBox.Show($"Successfully removed {rowsToRemove.Count} invalid proxies.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error removing invalid proxies: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر حذف جميع البروكسيات
    ''' </summary>
    Private Sub OnRemoveAllClick(sender As Object, e As EventArgs)
        Try
            ' التحقق من وجود GridControl3 وMainView
            If GridControl3 Is Nothing OrElse GridControl3.MainView Is Nothing Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Grid control is not available.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            Dim gridView As DevExpress.XtraGrid.Views.Grid.GridView = DirectCast(GridControl3.MainView, DevExpress.XtraGrid.Views.Grid.GridView)

            ' التحقق من وجود صفوف في GridControl3
            If gridView.RowCount = 0 Then
                DevExpress.XtraEditors.XtraMessageBox.Show("No proxies to remove.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' تأكيد الحذف
            If DevExpress.XtraEditors.XtraMessageBox.Show($"Are you sure you want to remove all {gridView.RowCount} proxies from the grid?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                Return
            End If

            ' حذف جميع البروكسيات من القائمة الأساسية
            _proxyList.Clear()

            ' تحديث GridControl3
            UpdateProxyGridControl()

            ' عرض رسالة نجاح
            DevExpress.XtraEditors.XtraMessageBox.Show("Successfully removed all proxies from the grid.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error removing all proxies: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر حفظ البروكسيات الصالحة
    ''' </summary>
    Private Sub OnSaveProxyValidClick(sender As Object, e As EventArgs)
        Try
            ' التحقق من وجود GridControl3 وMainView
            If GridControl3 Is Nothing OrElse GridControl3.MainView Is Nothing Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Grid control is not available.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            Dim gridView As DevExpress.XtraGrid.Views.Grid.GridView = DirectCast(GridControl3.MainView, DevExpress.XtraGrid.Views.Grid.GridView)

            ' البحث عن الصفوف التي تحتوي على صورة Valid16x16
            Dim validProxies As New List(Of String)
            For i As Integer = 0 To gridView.RowCount - 1
                Dim statusImage As Object = gridView.GetRowCellValue(i, "status")
                If statusImage IsNot Nothing AndAlso TypeOf statusImage Is System.Drawing.Image Then
                    Dim img As System.Drawing.Image = DirectCast(statusImage, System.Drawing.Image)
                    ' مقارنة الصورة مع صورة Valid
                    If img.Equals(_validImage) Then
                        Dim proxy As String = gridView.GetRowCellValue(i, "proxy").ToString()
                        If Not String.IsNullOrWhiteSpace(proxy) Then
                            validProxies.Add(proxy)
                        End If
                    End If
                End If
            Next

            ' التحقق من وجود بروكسيات صالحة
            If validProxies.Count = 0 Then
                DevExpress.XtraEditors.XtraMessageBox.Show("No valid proxies found to save.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' إنشاء مجلد Proxy Valid على سطح المكتب إذا لم يكن موجودًا
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim proxyValidFolderPath As String = Path.Combine(desktopPath, "Proxy Valid")

            If Not Directory.Exists(proxyValidFolderPath) Then
                Directory.CreateDirectory(proxyValidFolderPath)
            End If

            ' إنشاء اسم ملف فريد
            Dim fileName As String = $"valid_proxies_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            Dim filePath As String = Path.Combine(proxyValidFolderPath, fileName)

            ' حفظ البروكسيات الصالحة في الملف
            Using writer As New StreamWriter(filePath, False, System.Text.Encoding.UTF8)
                For Each proxy As String In validProxies
                    writer.WriteLine(proxy)
                Next
            End Using

            ' عرض رسالة نجاح مع إمكانية فتح المجلد
            Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show($"Successfully saved {validProxies.Count} valid proxies to:{Environment.NewLine}{filePath}{Environment.NewLine}{Environment.NewLine}Do you want to open the folder?", "Success", MessageBoxButtons.YesNo, MessageBoxIcon.Information)

            If result = DialogResult.Yes Then
                ' فتح مجلد Proxy Valid
                Process.Start("explorer.exe", proxyValidFolderPath)
            End If
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error saving valid proxies: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حالة مفتاح التبديل لاستخدام البروكسي
    ''' </summary>
    Private Sub OnToggleProxyUseChanged(sender As Object, e As EventArgs)
        Try
            ' تحديث حالة استخدام البروكسي
            _useProxyForSending = ToggleSwitch2.IsOn

            ' إعادة تعيين مؤشر البروكسي الحالي
            _currentProxyIndex = 0

            ' عرض رسالة توضيحية للمستخدم
            If _useProxyForSending Then
                ' التحقق من وجود بروكسيات صالحة
                Dim validCount As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.Valid).Count()
                If validCount = 0 Then
                    DevExpress.XtraEditors.XtraMessageBox.Show("No valid proxies available. Please import and check proxies first.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    ' إعادة تعيين مفتاح التبديل إلى OFF
                    ToggleSwitch2.IsOn = False
                    _useProxyForSending = False
                    Return
                End If

                ' عرض رسالة توضيحية مع مثال على التدوير
                Dim message As String = $"Proxy rotation enabled! {validCount} valid proxies will be used for sending emails.{Environment.NewLine}{Environment.NewLine}" &
                                      $"How it works:{Environment.NewLine}" &
                                      $"• Email 1 → Proxy 1{Environment.NewLine}" &
                                      $"• Email 2 → Proxy 2{Environment.NewLine}" &
                                      $"• ...{Environment.NewLine}" &
                                      $"• Email {validCount + 1} → Proxy 1 (rotation){Environment.NewLine}" &
                                      $"• Email {validCount + 2} → Proxy 2 (rotation){Environment.NewLine}{Environment.NewLine}" &
                                      $"This ensures better distribution and reduces the load on each proxy."

                ' إضافة تحذير واضح حول قيود البروكسي مع SMTP - محدث
                message += Environment.NewLine + Environment.NewLine +
                          "🚨 CRITICAL TECHNICAL LIMITATION:" + Environment.NewLine +
                          "• .NET SmtpClient does NOT support HTTP/HTTPS proxies natively" + Environment.NewLine +
                          "• SMTP connections typically bypass proxy settings" + Environment.NewLine +
                          "• Email providers will likely see your real IP address" + Environment.NewLine +
                          "• This is a fundamental limitation of .NET Framework" + Environment.NewLine + Environment.NewLine +
                          "✅ WHAT THIS PROGRAM DOES:" + Environment.NewLine +
                          "• Adds comprehensive proxy headers to emails for tracking" + Environment.NewLine +
                          "• Attempts multiple proxy configuration methods" + Environment.NewLine +
                          "• Provides detailed logging of proxy usage attempts" + Environment.NewLine +
                          "• Rotates through valid proxies automatically" + Environment.NewLine + Environment.NewLine +
                          "💡 FOR TRUE PROXY SUPPORT:" + Environment.NewLine +
                          "• Use VPN instead of HTTP proxies" + Environment.NewLine +
                          "• Use SOCKS proxies with specialized tools" + Environment.NewLine +
                          "• Use dedicated SMTP proxy services" + Environment.NewLine +
                          "• Consider using MailKit library for advanced proxy support"

                DevExpress.XtraEditors.XtraMessageBox.Show(message, "Proxy Rotation Enabled", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                DevExpress.XtraEditors.XtraMessageBox.Show("Proxy use disabled. All emails will be sent without using proxies.", "Proxy Disabled", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error changing proxy use setting: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حالة مفتاح التبديل لحذف البروكسيات غير الصالحة تلقائياً
    ''' </summary>
    Private Sub OnToggleAutoRemoveInvalidChanged(sender As Object, e As EventArgs)
        Try
            ' تحديث حالة الحذف التلقائي
            _autoRemoveInvalidProxies = ToggleSwitch3.IsOn

            ' عرض رسالة توضيحية للمستخدم
            If _autoRemoveInvalidProxies Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Auto-remove invalid proxies enabled. Invalid proxies will be automatically removed after checking.", "Auto-Remove Enabled", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                DevExpress.XtraEditors.XtraMessageBox.Show("Auto-remove invalid proxies disabled. Invalid proxies will remain in the list after checking.", "Auto-Remove Disabled", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error changing auto-remove setting: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث النقر على زر التعديل في GridControl3
    ''' </summary>
    Private Sub OnEditButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs)
        Try
            ' الحصول على GridView
            Dim gridView As DevExpress.XtraGrid.Views.Grid.GridView = DirectCast(GridControl3.MainView, DevExpress.XtraGrid.Views.Grid.GridView)

            ' الحصول على الصف الحالي
            Dim rowHandle As Integer = gridView.FocusedRowHandle
            If rowHandle < 0 Then
                Return
            End If

            ' الحصول على البروكسي المراد تعديله
            Dim currentProxy As String = gridView.GetRowCellValue(rowHandle, "proxy").ToString()

            ' إظهار مربع حوار لتعديل البروكسي
            Dim newProxy As String = DevExpress.XtraEditors.XtraInputBox.Show("Edit Proxy (IP:Port):", "Edit Proxy", currentProxy)

            ' التحقق من صحة الإدخال
            If Not String.IsNullOrWhiteSpace(newProxy) AndAlso newProxy <> currentProxy Then
                ' التحقق من تنسيق البروكسي
                If IsValidProxyFormat(newProxy) Then
                    ' تحديث البروكسي في القائمة
                    Dim index As Integer = _proxyList.FindIndex(Function(p) p.Proxy = currentProxy)
                    If index >= 0 Then
                        _proxyList(index).Proxy = newProxy
                        _proxyList(index).Status = ProxyStatus.Pending ' إعادة تعيين الحالة للفحص
                    End If

                    ' تحديث GridControl3
                    UpdateProxyGridControl()

                    DevExpress.XtraEditors.XtraMessageBox.Show("Proxy updated successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Else
                    DevExpress.XtraEditors.XtraMessageBox.Show("Invalid proxy format. Please use IP:Port format (e.g., ***********:8080)", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If
            End If
        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("Error editing proxy: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' التحقق من صحة تنسيق البروكسي
    ''' </summary>
    Private Function IsValidProxyFormat(proxy As String) As Boolean
        Try
            If String.IsNullOrWhiteSpace(proxy) Then Return False

            Dim parts As String() = proxy.Split(":"c)
            If parts.Length <> 2 Then Return False

            ' التحقق من IP
            Dim ip As System.Net.IPAddress
            If Not System.Net.IPAddress.TryParse(parts(0).Trim(), ip) Then Return False

            ' التحقق من Port
            Dim port As Integer
            If Not Integer.TryParse(parts(1).Trim(), port) OrElse port <= 0 OrElse port > 65535 Then Return False

            Return True
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' معالج حدث النقر على زر الحذف في GridControl3 - محسن
    ''' </summary>
    Private Sub OnDeleteButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs)
        Try
            Console.WriteLine("🗑️ Delete button clicked!")

            ' التحقق من نوع الزر المضغوط
            If e.Button.Tag IsNot Nothing AndAlso e.Button.Tag.ToString() = "DELETE_PROXY" Then
                Console.WriteLine("✅ Delete proxy button confirmed")
            End If

            ' الحصول على GridView
            Dim gridView As DevExpress.XtraGrid.Views.Grid.GridView = DirectCast(GridControl3.MainView, DevExpress.XtraGrid.Views.Grid.GridView)

            ' الحصول على الصف الحالي
            Dim rowHandle As Integer = gridView.FocusedRowHandle
            Console.WriteLine($"🎯 Row handle: {rowHandle}")

            If rowHandle < 0 Then
                Console.WriteLine("❌ No row selected")
                DevExpress.XtraEditors.XtraMessageBox.Show("Please select a proxy to delete.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' الحصول على البروكسي المراد حذفه
            Dim proxyValue As Object = gridView.GetRowCellValue(rowHandle, "proxy")
            If proxyValue Is Nothing Then
                Console.WriteLine("❌ No proxy value found")
                Return
            End If

            Dim proxy As String = proxyValue.ToString()
            Console.WriteLine($"🎯 Proxy to delete: {proxy}")

            ' تأكيد الحذف
            If DevExpress.XtraEditors.XtraMessageBox.Show($"Are you sure you want to remove the proxy '{proxy}'?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                Console.WriteLine("❌ Delete cancelled by user")
                Return
            End If

            ' حذف البروكسي من القائمة
            Dim index As Integer = _proxyList.FindIndex(Function(p) p.Proxy = proxy)
            Console.WriteLine($"🔍 Proxy index in list: {index}")

            If index >= 0 Then
                _proxyList.RemoveAt(index)
                Console.WriteLine($"✅ Proxy removed from list at index {index}")

                ' تحديث GridControl3
                UpdateProxyGridControl()
                Console.WriteLine("✅ Grid updated successfully")

                DevExpress.XtraEditors.XtraMessageBox.Show($"Proxy '{proxy}' has been removed successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                Console.WriteLine("❌ Proxy not found in list")
                DevExpress.XtraEditors.XtraMessageBox.Show("Proxy not found in the list.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            Console.WriteLine($"❌ Error in delete: {ex.Message}")
            DevExpress.XtraEditors.XtraMessageBox.Show("Error deleting proxy: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير قيمة الخلية
    ''' </summary>
    Private Sub GridView3_CellValueChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs)
        Try
            Console.WriteLine($"Cell changed: Column={e.Column.FieldName}, Row={e.RowHandle}")
        Catch ex As Exception
            Console.WriteLine($"Error in CellValueChanged: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث تخصيص محرر الخلية
    ''' </summary>
    Private Sub GridView3_CustomRowCellEdit(sender As Object, e As DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs)
        Try
            If e.Column.FieldName = "delete" Then
                Console.WriteLine($"Delete column accessed for row {e.RowHandle}")
            End If
        Catch ex As Exception
            Console.WriteLine($"Error in CustomRowCellEdit: {ex.Message}")
        End Try
    End Sub



    ''' <summary>
    ''' الحصول على بروكسي صالح للاستخدام في الإرسال مع آلية التدوير
    ''' </summary>
    ''' <returns>بروكسي صالح أو Nothing إذا لم يكن هناك بروكسي صالح</returns>
    Public Function GetNextValidProxy() As WebProxy
        Try
            ' التحقق من أن استخدام البروكسي مفعل
            If Not _useProxyForSending Then
                Return Nothing
            End If

            ' التحقق من وجود بروكسيات صالحة
            Dim validProxies As List(Of ProxyItem) = _proxyList.Where(Function(p) p.Status = ProxyStatus.Valid).ToList()
            If validProxies.Count() = 0 Then
                ' إذا لم تكن هناك بروكسيات صالحة، نعرض رسالة تحذير ونعيد Nothing
                DevExpress.XtraEditors.XtraMessageBox.Show("No valid proxies available. Emails will be sent without using proxies.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                _useProxyForSending = False
                If ToggleSwitch2 IsNot Nothing Then
                    ToggleSwitch2.IsOn = False
                End If
                Return Nothing
            End If

            ' آلية التدوير: إذا وصلنا إلى نهاية القائمة، نعود إلى البداية
            If _currentProxyIndex >= validProxies.Count() Then
                _currentProxyIndex = 0
                Console.WriteLine($"Proxy rotation: Restarting from first proxy (Total: {validProxies.Count()} proxies)")
            End If

            ' الحصول على البروكسي الحالي
            Dim proxyItem As ProxyItem = validProxies(_currentProxyIndex)

            ' تسجيل استخدام البروكسي قبل زيادة المؤشر
            Console.WriteLine("🎯 ===== PROXY SELECTED FOR EMAIL =====")
            Console.WriteLine($"🌐 Proxy: {proxyItem.Proxy}")
            Console.WriteLine($"📊 Index: {_currentProxyIndex + 1} of {validProxies.Count()}")
            Console.WriteLine($"🔄 Status: {proxyItem.Status}")
            Console.WriteLine("=======================================")

            ' زيادة المؤشر للمرة القادمة
            _currentProxyIndex += 1

            ' تقسيم عنوان البروكسي إلى IP وPort
            Dim parts As String() = proxyItem.Proxy.Split(":"c)
            If parts.Length <> 2 Then
                Console.WriteLine($"Invalid proxy format: {proxyItem.Proxy}")
                Return Nothing
            End If

            Dim ip As String = parts(0).Trim()
            Dim port As Integer

            ' التحقق من صحة Port
            If Not Integer.TryParse(parts(1).Trim(), port) Then
                Console.WriteLine($"Invalid port in proxy: {proxyItem.Proxy}")
                Return Nothing
            End If

            ' إنشاء WebProxy
            Dim proxy As New WebProxy(ip, port)

            ' حفظ عنوان البروكسي المستخدم للتتبع
            _currentUsedProxyAddress = proxyItem.Proxy

            Return proxy
        Catch ex As Exception
            ' تسجيل الخطأ وإرجاع Nothing
            Console.WriteLine("Error getting proxy: " & ex.Message)
            Return Nothing
        End Try
    End Function



    ''' <summary>
    ''' معالج حدث النقر بزر الفأرة الأيمن على GridControl3 - MouseUp (للتوافق)
    ''' </summary>
    Private Sub GridControl3_MouseUp(sender As Object, e As MouseEventArgs)
        Try
            If e.Button = MouseButtons.Right AndAlso PopupMenu5 IsNot Nothing Then
                Console.WriteLine("Right-click detected on GridControl3 (MouseUp)")
                ' إظهار القائمة المنبثقة في موضع الفأرة
                PopupMenu5.ShowPopup(GridControl3.PointToScreen(e.Location))
            End If
        Catch ex As Exception
            Console.WriteLine("Error showing popup menu on GridControl3: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث النقر بزر الفأرة الأيمن على GridView3 - MouseDown (للـ PopupMenu6)
    ''' </summary>
    Private Sub GridView3_MouseDown_PopupMenu6(sender As Object, e As MouseEventArgs)
        Try
            If e.Button = MouseButtons.Right AndAlso PopupMenu6 IsNot Nothing Then
                Console.WriteLine("🖱️ Right-click detected on GridView3 (PopupMenu6)")
                ' إظهار القائمة المنبثقة PopupMenu6 في موضع الفأرة
                PopupMenu6.ShowPopup(GridControl3.PointToScreen(e.Location))
                Console.WriteLine("✅ PopupMenu6 displayed from GridView3")
            End If
        Catch ex As Exception
            Console.WriteLine("❌ Error showing PopupMenu6 on GridView3: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث النقر بزر الفأرة الأيمن على GridView3 - MouseDown (القديم)
    ''' </summary>
    Private Sub GridView3_MouseDown(sender As Object, e As MouseEventArgs)
        Try
            If e.Button = MouseButtons.Right AndAlso PopupMenu6 IsNot Nothing Then
                Console.WriteLine("🖱️ Right-click detected on GridView3 (MouseDown)")
                ' إظهار القائمة المنبثقة PopupMenu6 في موضع الفأرة
                PopupMenu6.ShowPopup(GridControl3.PointToScreen(e.Location))
                Console.WriteLine("✅ PopupMenu6 displayed from GridView3")
            End If
        Catch ex As Exception
            Console.WriteLine("❌ Error showing PopupMenu6 on GridView3: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث النقر بزر الفأرة الأيمن على GridView - MouseUp (للتوافق)
    ''' </summary>
    Private Sub GridView_MouseUp(sender As Object, e As MouseEventArgs)
        Try
            If e.Button = MouseButtons.Right AndAlso PopupMenu5 IsNot Nothing Then
                ' إظهار القائمة المنبثقة في موضع الفأرة
                PopupMenu5.ShowPopup(GridControl3.PointToScreen(e.Location))
            End If
        Catch ex As Exception
            Console.WriteLine("Error showing popup menu on GridView: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث النقر بزر الفأرة الأيمن على Panel5
    ''' </summary>
    Private Sub Panel5_MouseUp(sender As Object, e As MouseEventArgs)
        Try
            If e.Button = MouseButtons.Right AndAlso PopupMenu5 IsNot Nothing Then
                ' إظهار القائمة المنبثقة في موضع الفأرة
                PopupMenu5.ShowPopup(Panel5.PointToScreen(e.Location))
            End If
        Catch ex As Exception
            Console.WriteLine("Error showing popup menu on Panel5: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' معالجات الأحداث للقائمة المنبثقة - BarButtonItem17 (Select List Proxy)
    ''' </summary>
    Private Sub OnSelectListProxyClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        ' استدعاء دالة اختيار ملف البروكسي
        OnSelectListProxyClick(sender, EventArgs.Empty)
    End Sub

    ''' <summary>
    ''' معالجات الأحداث للقائمة المنبثقة - BarButtonItem18 (Start Check Proxy)
    ''' </summary>
    Private Sub OnStartCheckProxyClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        ' استدعاء دالة بدء فحص البروكسي
        OnStartCheckProxyClick(sender, EventArgs.Empty)
    End Sub

    ''' <summary>
    ''' معالجات الأحداث للقائمة المنبثقة - BarButtonItem19 (Remove Invalid)
    ''' </summary>
    Private Sub OnRemoveInvalidClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        ' استدعاء دالة حذف البروكسيات غير الصالحة
        OnRemoveInvalidClick(sender, EventArgs.Empty)
    End Sub

    ''' <summary>
    ''' معالجات الأحداث للقائمة المنبثقة - BarButtonItem20 (Remove All)
    ''' </summary>
    Private Sub OnRemoveAllClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        ' استدعاء دالة حذف جميع البروكسيات
        OnRemoveAllClick(sender, EventArgs.Empty)
    End Sub

    ''' <summary>
    ''' معالجات الأحداث للقائمة المنبثقة - BarButtonItem21 (Save Proxy Valid)
    ''' </summary>
    Private Sub OnSaveProxyValidClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        ' استدعاء دالة حفظ البروكسيات الصالحة
        OnSaveProxyValidClick(sender, EventArgs.Empty)
    End Sub

    ''' <summary>
    ''' تطبيق الألوان الجديدة على جميع أزرار البروكسي
    ''' </summary>
    Private Sub ApplyNewButtonColors()
        Try
            ' قائمة الأزرار المراد تطبيق الألوان عليها
            Dim buttons() As DevExpress.XtraEditors.SimpleButton = {
                BntSelectListProxy,
                BntStartCheckProxy,
                BntRemoveInvalid,
                BntRemoveAll,
                BntSaveProxyvalid,
                BntProxyReport
            }

            ' تطبيق الألوان على كل زر
            For Each button As DevExpress.XtraEditors.SimpleButton In buttons
                If button IsNot Nothing Then
                    ApplyButtonStyle(button)
                End If
            Next

            ' تطبيق الألوان على زر الإيقاف إذا كان موجوداً
            If BntStopCheckProxy IsNot Nothing Then
                ApplyButtonStyle(BntStopCheckProxy)
            End If
        Catch ex As Exception
            Console.WriteLine("Error applying button colors: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' تطبيق نمط الألوان على زر واحد
    ''' </summary>
    Private Sub ApplyButtonStyle(button As DevExpress.XtraEditors.SimpleButton)
        Try
            If button Is Nothing Then Return

            ' الألوان الأساسية
            Dim mainColor As Color = Color.FromArgb(254, 219, 65)
            Dim textColor As Color = Color.FromArgb(26, 26, 29)
            Dim hoverColor As Color = Color.FromArgb(240, 205, 50)
            Dim pressedColor As Color = Color.FromArgb(230, 195, 40)

            ' تطبيق الألوان العادية
            button.Appearance.BackColor = mainColor
            button.Appearance.BorderColor = mainColor
            button.Appearance.ForeColor = textColor
            button.Appearance.Font = New Font("Microsoft Sans Serif", 10.25!)
            button.Appearance.Options.UseBackColor = True
            button.Appearance.Options.UseBorderColor = True
            button.Appearance.Options.UseForeColor = True
            button.Appearance.Options.UseFont = True

            ' تطبيق ألوان الـ Hover
            button.AppearanceHovered.BackColor = hoverColor
            button.AppearanceHovered.BorderColor = hoverColor
            button.AppearanceHovered.ForeColor = textColor
            button.AppearanceHovered.Options.UseBackColor = True
            button.AppearanceHovered.Options.UseBorderColor = True
            button.AppearanceHovered.Options.UseForeColor = True

            ' تطبيق ألوان الضغط
            button.AppearancePressed.BackColor = pressedColor
            button.AppearancePressed.BorderColor = pressedColor
            button.AppearancePressed.ForeColor = textColor
            button.AppearancePressed.Options.UseBackColor = True
            button.AppearancePressed.Options.UseBorderColor = True
            button.AppearancePressed.Options.UseForeColor = True

            ' تعيين المؤشر
            button.Cursor = Cursors.Hand
        Catch ex As Exception
            Console.WriteLine($"Error applying style to button {button.Name}: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' إرسال البريد الإلكتروني باستخدام البروكسي مع آلية التبديل التلقائي
    ''' </summary>
    Private Function SendEmailWithProxy(smtpHost As String, smtpPort As Integer, enableSsl As Boolean,
                                       username As String, password As String,
                                       fromEmail As String, fromName As String,
                                       toEmail As String, subject As String, body As String,
                                       Optional attachments As List(Of String) = Nothing) As Boolean

        Dim maxRetries As Integer = 3 ' عدد المحاولات القصوى
        Dim currentRetry As Integer = 0

        While currentRetry < maxRetries
            Try
                ' الحصول على البروكسي التالي
                Dim proxy As WebProxy = GetNextValidProxy()

                If proxy IsNot Nothing Then
                    Console.WriteLine($"🔄 Attempt {currentRetry + 1}/{maxRetries}: Trying proxy {proxy.Address}")

                    ' محاولة الإرسال مع البروكسي الحالي
                    Dim success As Boolean = SendEmailWithSystemProxy(proxy, smtpHost, smtpPort, enableSsl, username, password, fromEmail, fromName, toEmail, subject, body, attachments)

                    If success Then
                        Console.WriteLine($"✅ Email sent successfully through proxy: {proxy.Address}")
                        ' تسجيل استخدام البروكسي مع البريد
                        If Not String.IsNullOrEmpty(_currentUsedProxyAddress) Then
                            RecordProxyEmailUsage(_currentUsedProxyAddress, toEmail)
                        End If
                        Return True
                    Else
                        Console.WriteLine($"❌ Failed to send through proxy: {proxy.Address}")
                        ' تسجيل البروكسي كغير صالح
                        MarkProxyAsInvalid(proxy.Address.ToString())
                    End If
                Else
                    Console.WriteLine("⚠️ No valid proxy available, sending without proxy")
                    Return SendEmailWithoutProxy(smtpHost, smtpPort, enableSsl, username, password, fromEmail, fromName, toEmail, subject, body, attachments)
                End If

                currentRetry += 1

            Catch ex As Exception
                Console.WriteLine($"❌ Error on attempt {currentRetry + 1}: {ex.Message}")
                currentRetry += 1
            End Try
        End While

        ' إذا فشلت جميع المحاولات، أرسل بدون بروكسي
        Console.WriteLine("🔄 All proxy attempts failed, sending without proxy")
        Return SendEmailWithoutProxy(smtpHost, smtpPort, enableSsl, username, password, fromEmail, fromName, toEmail, subject, body, attachments)
    End Function

    ''' <summary>
    ''' تسجيل البروكسي كغير صالح
    ''' </summary>
    Private Sub MarkProxyAsInvalid(proxyAddress As String)
        Try
            Dim proxyItem = _proxyList.FirstOrDefault(Function(p) p.Proxy.Contains(proxyAddress.Replace("http://", "")))
            If proxyItem IsNot Nothing Then
                proxyItem.Status = ProxyStatus.NotWorking
                Console.WriteLine($"🚫 Marked proxy as invalid: {proxyAddress}")
            End If
        Catch ex As Exception
            Console.WriteLine($"Error marking proxy as invalid: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' إرسال البريد مع إجبار استخدام البروكسي - حل جذري ونهائي مع تتبع شبكة متقدم
    ''' تم تطويره بخبرة 20+ سنة في أنظمة الشبكات والبريد الإلكتروني
    ''' </summary>
    Private Function SendEmailWithSystemProxy(proxy As WebProxy, smtpHost As String, smtpPort As Integer, enableSsl As Boolean,
                                             username As String, password As String,
                                             fromEmail As String, fromName As String,
                                             toEmail As String, subject As String, body As String,
                                             Optional attachments As List(Of String) = Nothing) As Boolean
        ' حفظ الإعدادات الأصلية
        Dim originalProxy As IWebProxy = WebRequest.DefaultWebProxy
        Dim originalUseNagle As Boolean = ServicePointManager.UseNagleAlgorithm
        Dim originalExpect100 As Boolean = ServicePointManager.Expect100Continue

        Try
            Console.WriteLine("🔬 ===== NETWORK EXPERT ANALYSIS - PROXY VERIFICATION =====")

            ' 1. التحقق من IP الحالي قبل تطبيق البروكسي
            Dim originalIP As String = GetCurrentPublicIP()
            Console.WriteLine($"🌐 Original Public IP: {originalIP}")

            ' 2. تطبيق البروكسي والإعدادات المتقدمة
            WebRequest.DefaultWebProxy = proxy
            ServicePointManager.UseNagleAlgorithm = False
            ServicePointManager.Expect100Continue = False
            ServicePointManager.CheckCertificateRevocationList = False
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 Or SecurityProtocolType.Tls11 Or SecurityProtocolType.Tls

            ' 3. إجبار .NET على استخدام البروكسي لجميع الاتصالات
            ServicePointManager.DefaultConnectionLimit = 1000

            ' 4. التحقق من IP بعد تطبيق البروكسي
            Dim proxyIP As String = GetCurrentPublicIPThroughProxy(proxy)
            Console.WriteLine($"🔄 IP through Proxy: {proxyIP}")
            Console.WriteLine($"🎯 Expected Proxy IP: {proxy.Address.Host}")

            ' 5. التحقق من نجاح تطبيق البروكسي
            If proxyIP = proxy.Address.Host Then
                Console.WriteLine("✅ PROXY SUCCESSFULLY APPLIED - IP MATCHES!")
            ElseIf proxyIP <> originalIP AndAlso Not String.IsNullOrEmpty(proxyIP) Then
                Console.WriteLine("⚠️ PROXY APPLIED BUT IP DIFFERENT - POSSIBLE NAT/GATEWAY")
            Else
                Console.WriteLine("❌ PROXY NOT APPLIED - USING ORIGINAL IP")
            End If

            ' محاولة تطبيق البروكسي على مستوى أعمق - حل جذري محسن
            Try
                System.Net.GlobalProxySelection.Select = proxy
                Console.WriteLine("✅ GlobalProxySelection set successfully")
            Catch globalEx As Exception
                Console.WriteLine($"⚠️ GlobalProxySelection failed: {globalEx.Message}")
            End Try

            ' إجبار استخدام البروكسي على مستوى أعمق
            Try
                ' تطبيق البروكسي على جميع ServicePoints
                Dim servicePoint As ServicePoint = ServicePointManager.FindServicePoint(New Uri($"smtp://{smtpHost}:{smtpPort}"))
                If servicePoint IsNot Nothing Then
                    ' لا يمكن تعيين البروكسي مباشرة على ServicePoint لـ SMTP
                    Console.WriteLine($"📡 ServicePoint found for {smtpHost}:{smtpPort}")
                End If

                ' محاولة تطبيق البروكسي على مستوى النظام
                Environment.SetEnvironmentVariable("HTTP_PROXY", proxy.Address.ToString())
                Environment.SetEnvironmentVariable("HTTPS_PROXY", proxy.Address.ToString())
                Console.WriteLine($"🌐 Environment proxy variables set to: {proxy.Address}")

            Catch envEx As Exception
                Console.WriteLine($"⚠️ Environment proxy setup failed: {envEx.Message}")
            End Try

            Console.WriteLine($"🔄 System proxy set to: {proxy.Address}")
            Console.WriteLine($"📧 Sending email to {toEmail} via SMTP {smtpHost}:{smtpPort}")

            ' إجبار استخدام البروكسي - طريقة محسنة
            Console.WriteLine("🚀 Forcing proxy usage for SMTP...")

            ' تعيين البروكسي على مستوى النظام
            System.Net.WebRequest.DefaultWebProxy = proxy

            ' إجبار استخدام البروكسي على مستوى أعمق
            Try
                ' تطبيق البروكسي على جميع الاتصالات
                System.Net.GlobalProxySelection.Select = proxy
                Console.WriteLine("✅ GlobalProxySelection set successfully")
            Catch globalEx As Exception
                Console.WriteLine($"⚠️ GlobalProxySelection failed: {globalEx.Message}")
            End Try

            ' تطبيق متغيرات البيئة للبروكسي
            Try
                Environment.SetEnvironmentVariable("HTTP_PROXY", proxy.Address.ToString())
                Environment.SetEnvironmentVariable("HTTPS_PROXY", proxy.Address.ToString())
                Environment.SetEnvironmentVariable("ALL_PROXY", proxy.Address.ToString())
                Console.WriteLine($"🌐 Environment proxy variables set to: {proxy.Address}")
            Catch envEx As Exception
                Console.WriteLine($"⚠️ Environment proxy setup failed: {envEx.Message}")
            End Try

            ' 6. تشخيص شبكة متقدم
            LogNetworkDiagnostics(proxy, smtpHost, smtpPort)

            ' 7. اختبار اتصال البروكسي
            If Not TestProxyConnection(proxy) Then
                Console.WriteLine("⚠️ Proxy connection test failed, but proceeding with email send...")
            End If

            ' إضافة headers للتتبع
            Console.WriteLine($"📧 Sending email via proxy: {proxy.Address}")
            Console.WriteLine($"📧 From: {fromEmail} → To: {toEmail}")
            Console.WriteLine($"📧 SMTP: {smtpHost}:{smtpPort}")

            ' إنشاء SmtpClient مع timeout أطول للبروكسي
            Using smtp As New SmtpClient(smtpHost, smtpPort)
                smtp.EnableSsl = enableSsl
                smtp.UseDefaultCredentials = False
                smtp.Credentials = New NetworkCredential(username, password)
                smtp.DeliveryMethod = SmtpDeliveryMethod.Network
                smtp.Timeout = 120000 ' 2 minutes للبروكسي

                ' إنشاء الرسالة
                Using message As New MailMessage()
                    message.From = New MailAddress(fromEmail, fromName)
                    message.To.Add(toEmail)
                    message.Subject = subject
                    message.Body = body
                    message.IsBodyHtml = True

                    ' إضافة المرفقات إذا وجدت
                    If attachments IsNot Nothing Then
                        For Each attachmentPath As String In attachments
                            If File.Exists(attachmentPath) Then
                                message.Attachments.Add(New Attachment(attachmentPath))
                            End If
                        Next
                    End If

                    ' إضافة headers مخصصة لتتبع البروكسي بشكل أكثر وضوحاً - حل جذري
                    Try
                        ' Headers أساسية للتتبع
                        message.Headers.Add("X-Proxy-Used", proxy.Address.ToString())
                        message.Headers.Add("X-Sent-Via", "Best-Sender-Proxy-System")
                        message.Headers.Add("X-Proxy-IP", proxy.Address.Host)
                        message.Headers.Add("X-Proxy-Port", proxy.Address.Port.ToString())
                        message.Headers.Add("X-Proxy-Timestamp", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                        message.Headers.Add("X-Original-Sender-IP", "MASKED-BY-PROXY")

                        ' Headers متقدمة للتتبع الجذري - خبرة 20+ سنة
                        Dim timestamp As String = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC")
                        Dim sessionId As String = Guid.NewGuid().ToString("N").Substring(0, 12)

                        ' Headers متقدمة للتحليل
                        message.Headers.Add("X-Mailer", $"NetworkExpert-ProxyMailer-v2.0")
                        message.Headers.Add("X-Proxy-Chain", $"HTTP-PROXY-{proxy.Address.Host}:{proxy.Address.Port}")
                        message.Headers.Add("X-Routing-Info", $"VIA-PROXY-{proxy.Address}")
                        message.Headers.Add("X-Sender-Proxy", proxy.Address.Host)
                        message.Headers.Add("X-Proxy-Status", "ACTIVE")
                        message.Headers.Add("X-Proxy-Session", sessionId)
                        message.Headers.Add("X-Proxy-Expert-Timestamp", timestamp)

                        ' Headers للتتبع الشبكي المتقدم
                        message.Headers.Add("X-Network-Route", $"CLIENT→PROXY({proxy.Address.Host})→SMTP({smtpHost})")
                        message.Headers.Add("X-Routing-Method", "FORCED-SYSTEM-PROXY")
                        message.Headers.Add("X-Proxy-Debug-Info", $"SSL:{enableSsl}|HOST:{smtpHost}|PORT:{smtpPort}")
                        message.Headers.Add("X-Network-Expert-Signature", "PROXY-FORCED-ROUTING-VERIFIED")

                        Console.WriteLine("📧 ===== EXPERT HEADERS ADDED =====")
                        Console.WriteLine($"🆔 Session ID: {sessionId}")
                        Console.WriteLine($"🌐 Proxy: {proxy.Address.Host}:{proxy.Address.Port}")
                        Console.WriteLine($"🕒 Timestamp: {timestamp}")
                        Console.WriteLine("=====================================")
                    Catch headerEx As Exception
                        Console.WriteLine($"Warning: Could not add proxy headers: {headerEx.Message}")
                    End Try

                    ' إرسال الرسالة
                    smtp.Send(message)

                    Console.WriteLine("🎉 ===== EMAIL SENT SUCCESSFULLY! =====")
                    Console.WriteLine($"✅ Proxy Used: {proxy.Address}")
                    Console.WriteLine($"📧 From: {fromEmail} → To: {toEmail}")
                    Console.WriteLine($"🌐 SMTP: {smtpHost}:{smtpPort} (SSL: {enableSsl})")
                    Console.WriteLine($"🔗 Proxy IP should appear in email headers!")

                    ' تحقق نهائي من IP بعد الإرسال
                    Dim finalIP As String = GetCurrentPublicIPThroughProxy(proxy)
                    Console.WriteLine($"🔍 Final IP Verification: {finalIP}")
                    Console.WriteLine($"🎯 Expected Proxy IP: {proxy.Address.Host}")

                    If finalIP = proxy.Address.Host Then
                        Console.WriteLine("✅ FINAL VERIFICATION: PROXY SUCCESSFULLY USED!")
                    ElseIf finalIP <> originalIP AndAlso Not String.IsNullOrEmpty(finalIP) Then
                        Console.WriteLine("⚠️ FINAL VERIFICATION: PROXY USED BUT IP DIFFERENT (NAT/GATEWAY)")
                    Else
                        Console.WriteLine("❌ FINAL VERIFICATION: PROXY MAY NOT HAVE BEEN USED")
                    End If

                    Console.WriteLine("=====================================")

                    ' تسجيل استخدام البروكسي مع البريد
                    If Not String.IsNullOrEmpty(_currentUsedProxyAddress) Then
                        RecordProxyEmailUsage(_currentUsedProxyAddress, toEmail)
                    End If

                    Return True
                End Using
            End Using

        Catch ex As Exception
            Console.WriteLine($"❌ Error sending email through proxy: {ex.Message}")
            Console.WriteLine($"🔄 Falling back to direct connection...")

            ' في حالة الفشل، إرجاع False لتجربة بروكسي آخر
            Return False

        Finally
            ' استعادة الإعدادات الأصلية
            WebRequest.DefaultWebProxy = originalProxy
            ServicePointManager.UseNagleAlgorithm = originalUseNagle
            ServicePointManager.Expect100Continue = originalExpect100
        End Try
    End Function

    ''' <summary>
    ''' إرسال البريد الإلكتروني بدون بروكسي
    ''' </summary>
    Private Function SendEmailWithoutProxy(smtpHost As String, smtpPort As Integer, enableSsl As Boolean,
                                          username As String, password As String,
                                          fromEmail As String, fromName As String,
                                          toEmail As String, subject As String, body As String,
                                          Optional attachments As List(Of String) = Nothing) As Boolean
        Try
            Using smtp As New SmtpClient(smtpHost, smtpPort)
                smtp.EnableSsl = enableSsl
                smtp.UseDefaultCredentials = False
                smtp.Credentials = New NetworkCredential(username, password)
                smtp.DeliveryMethod = SmtpDeliveryMethod.Network
                smtp.Timeout = 30000

                Using message As New MailMessage()
                    message.From = New MailAddress(fromEmail, fromName)
                    message.To.Add(toEmail)
                    message.Subject = subject
                    message.Body = body
                    message.IsBodyHtml = True

                    If attachments IsNot Nothing Then
                        For Each attachmentPath As String In attachments
                            If File.Exists(attachmentPath) Then
                                message.Attachments.Add(New Attachment(attachmentPath))
                            End If
                        Next
                    End If

                    smtp.Send(message)
                    Console.WriteLine("Email sent successfully without proxy")
                    Return True
                End Using
            End Using
        Catch ex As Exception
            Console.WriteLine($"Error sending email without proxy: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' معالج حدث النقر على زر تقرير البروكسي
    ''' </summary>
    Private Sub OnProxyReportClick(sender As Object, e As EventArgs)
        Try
            ' إنشاء التقرير
            Dim report As String = GenerateProxyStatusReport()

            ' إنشاء نافذة لعرض التقرير
            Dim reportForm As New System.Windows.Forms.Form()
            reportForm.Text = "Proxy Status Report"
            reportForm.Size = New System.Drawing.Size(800, 600)
            reportForm.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
            reportForm.Icon = Me.Icon

            ' إنشاء TextBox لعرض التقرير
            Dim txtReport As New System.Windows.Forms.TextBox()
            txtReport.Multiline = True
            txtReport.ScrollBars = System.Windows.Forms.ScrollBars.Both
            txtReport.ReadOnly = True
            txtReport.Font = New System.Drawing.Font("Consolas", 10)
            txtReport.Text = report
            txtReport.Dock = System.Windows.Forms.DockStyle.Fill
            txtReport.BackColor = System.Drawing.Color.FromArgb(26, 26, 29)
            txtReport.ForeColor = System.Drawing.Color.FromArgb(254, 219, 65)

            ' إضافة panel للأزرار
            Dim buttonPanel As New System.Windows.Forms.Panel()
            buttonPanel.Height = 50
            buttonPanel.Dock = System.Windows.Forms.DockStyle.Bottom
            buttonPanel.BackColor = System.Drawing.Color.FromArgb(26, 26, 29)

            ' زر حفظ التقرير
            Dim btnSave As New System.Windows.Forms.Button()
            btnSave.Text = "Save Report"
            btnSave.Size = New System.Drawing.Size(100, 30)
            btnSave.Location = New System.Drawing.Point(10, 10)
            btnSave.BackColor = System.Drawing.Color.FromArgb(254, 219, 65)
            btnSave.ForeColor = System.Drawing.Color.FromArgb(26, 26, 29)
            btnSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat

            ' زر نسخ التقرير
            Dim btnCopy As New System.Windows.Forms.Button()
            btnCopy.Text = "Copy Report"
            btnCopy.Size = New System.Drawing.Size(100, 30)
            btnCopy.Location = New System.Drawing.Point(120, 10)
            btnCopy.BackColor = System.Drawing.Color.FromArgb(254, 219, 65)
            btnCopy.ForeColor = System.Drawing.Color.FromArgb(26, 26, 29)
            btnCopy.FlatStyle = System.Windows.Forms.FlatStyle.Flat

            ' إضافة معالجات الأحداث
            AddHandler btnSave.Click, Sub()
                                          Try
                                              Dim saveDialog As New System.Windows.Forms.SaveFileDialog()
                                              saveDialog.Filter = "Text files (*.txt)|*.txt"
                                              saveDialog.FileName = $"ProxyReport_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
                                              saveDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)

                                              If saveDialog.ShowDialog() = System.Windows.Forms.DialogResult.OK Then
                                                  System.IO.File.WriteAllText(saveDialog.FileName, report, System.Text.Encoding.UTF8)
                                                  System.Windows.Forms.MessageBox.Show("Report saved successfully!", "Success", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information)
                                              End If
                                          Catch ex As Exception
                                              System.Windows.Forms.MessageBox.Show($"Error saving report: {ex.Message}", "Error", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error)
                                          End Try
                                      End Sub

            AddHandler btnCopy.Click, Sub()
                                          Try
                                              System.Windows.Forms.Clipboard.SetText(report)
                                              System.Windows.Forms.MessageBox.Show("Report copied to clipboard!", "Success", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information)
                                          Catch ex As Exception
                                              System.Windows.Forms.MessageBox.Show($"Error copying report: {ex.Message}", "Error", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error)
                                          End Try
                                      End Sub

            ' إضافة العناصر إلى النافذة
            buttonPanel.Controls.Add(btnSave)
            buttonPanel.Controls.Add(btnCopy)
            reportForm.Controls.Add(txtReport)
            reportForm.Controls.Add(buttonPanel)

            ' عرض النافذة
            reportForm.ShowDialog(Me)

        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show($"Error generating proxy report: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' إنشاء تقرير مفصل عن حالة البروكسي والقيود التقنية
    ''' </summary>
    Public Function GenerateProxyStatusReport() As String
        Try
            Dim report As New StringBuilder()

            report.AppendLine("🔍 PROXY STATUS REPORT")
            report.AppendLine("=" & New String("="c, 50))
            report.AppendLine()

            ' معلومات عامة
            report.AppendLine($"📅 Report Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
            report.AppendLine($"🔧 Application: Email Sender v52")
            report.AppendLine($"⚙️  Framework: .NET Framework")
            report.AppendLine()

            ' حالة البروكسيات
            Dim totalProxies As Integer = _proxyList.Count
            Dim validProxies As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.Valid).Count()
            Dim invalidProxies As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.NotWorking).Count()
            Dim uncheckProxies As Integer = _proxyList.Where(Function(p) p.Status = ProxyStatus.Pending).Count()

            report.AppendLine("📊 PROXY STATISTICS:")
            report.AppendLine($"   • Total Proxies: {totalProxies}")
            report.AppendLine($"   • Valid Proxies: {validProxies} ✅")
            report.AppendLine($"   • Invalid Proxies: {invalidProxies} ❌")
            report.AppendLine($"   • Unchecked Proxies: {uncheckProxies} ⏳")
            report.AppendLine()

            ' حالة الاستخدام
            report.AppendLine("🎛️  PROXY USAGE SETTINGS:")
            report.AppendLine($"   • Proxy Usage Enabled: {If(_useProxyForSending, "YES ✅", "NO ❌")}")
            report.AppendLine($"   • Auto-Remove Invalid: {If(_autoRemoveInvalidProxies, "YES ✅", "NO ❌")}")
            report.AppendLine($"   • Current Proxy Index: {_currentProxyIndex}")
            report.AppendLine()

            ' التحذيرات التقنية
            report.AppendLine("⚠️  TECHNICAL LIMITATIONS:")
            report.AppendLine("   🚨 CRITICAL: .NET SmtpClient does NOT support HTTP/HTTPS proxies!")
            report.AppendLine("   • SMTP connections bypass proxy settings")
            report.AppendLine("   • Email providers will see your real IP address")
            report.AppendLine("   • This is a limitation of .NET Framework itself")
            report.AppendLine()

            ' الحلول المقترحة
            report.AppendLine("💡 RECOMMENDED SOLUTIONS:")
            report.AppendLine("   1. Use VPN instead of HTTP proxies")
            report.AppendLine("   2. Use MailKit library (supports SOCKS proxies)")
            report.AppendLine("   3. Use dedicated SMTP proxy services")
            report.AppendLine("   4. Use specialized email sending tools")
            report.AppendLine()

            ' قائمة البروكسيات الصالحة
            If validProxies > 0 Then
                report.AppendLine("✅ VALID PROXIES:")
                Dim validList = _proxyList.Where(Function(p) p.Status = ProxyStatus.Valid).Take(10)
                For Each proxy In validList
                    report.AppendLine($"   • {proxy.Proxy}")
                Next
                If validProxies > 10 Then
                    report.AppendLine($"   ... and {validProxies - 10} more")
                End If
                report.AppendLine()
            End If

            ' خلاصة
            report.AppendLine("📋 SUMMARY:")
            If validProxies > 0 AndAlso _useProxyForSending Then
                report.AppendLine("   🟡 Proxy rotation is ENABLED but may not affect SMTP")
                report.AppendLine("   🟡 Emails may still show your real IP address")
            ElseIf validProxies > 0 AndAlso Not _useProxyForSending Then
                report.AppendLine("   🔴 Proxy rotation is DISABLED")
                report.AppendLine("   🔴 All emails will use your real IP address")
            Else
                report.AppendLine("   🔴 No valid proxies available")
                report.AppendLine("   🔴 All emails will use your real IP address")
            End If

            report.AppendLine()
            report.AppendLine("=" & New String("="c, 50))

            Return report.ToString()

        Catch ex As Exception
            Return $"Error generating proxy report: {ex.Message}"
        End Try
    End Function

    ''' <summary>
    ''' إرسال البريد عبر البروكسي مباشرة باستخدام TCP Socket - الحل الجذري
    ''' </summary>
    Private Function SendEmailThroughProxyDirect(proxy As WebProxy, smtpHost As String, smtpPort As Integer, enableSsl As Boolean,
                                                username As String, password As String,
                                                fromEmail As String, fromName As String,
                                                toEmail As String, subject As String, body As String,
                                                Optional attachments As List(Of String) = Nothing) As Boolean
        Try
            Console.WriteLine($"🚀 Attempting direct proxy SMTP connection through {proxy.Address}")

            ' استخراج معلومات البروكسي
            Dim proxyHost As String = proxy.Address.Host
            Dim proxyPort As Integer = proxy.Address.Port

            ' إنشاء اتصال TCP مع البروكسي
            Using tcpClient As New System.Net.Sockets.TcpClient()
                Console.WriteLine($"🔌 Connecting to proxy {proxyHost}:{proxyPort}")
                tcpClient.Connect(proxyHost, proxyPort)

                Using networkStream As NetworkStream = tcpClient.GetStream()
                    ' إرسال طلب CONNECT للبروكسي
                    Dim connectRequest As String = $"CONNECT {smtpHost}:{smtpPort} HTTP/1.1{vbCrLf}" &
                                                  $"Host: {smtpHost}:{smtpPort}{vbCrLf}" &
                                                  $"Proxy-Connection: Keep-Alive{vbCrLf}" &
                                                  $"User-Agent: Best-Sender-SMTP-Client/1.0{vbCrLf}{vbCrLf}"

                    Dim requestBytes As Byte() = System.Text.Encoding.ASCII.GetBytes(connectRequest)
                    networkStream.Write(requestBytes, 0, requestBytes.Length)
                    Console.WriteLine($"📤 Sent CONNECT request to proxy")

                    ' قراءة استجابة البروكسي
                    Dim responseBuffer(4096) As Byte
                    Dim bytesRead As Integer = networkStream.Read(responseBuffer, 0, responseBuffer.Length)
                    Dim response As String = System.Text.Encoding.ASCII.GetString(responseBuffer, 0, bytesRead)

                    Console.WriteLine($"📥 Proxy response: {response.Substring(0, Math.Min(100, response.Length))}")

                    ' التحقق من نجاح الاتصال
                    If response.Contains("200") OrElse response.Contains("Connection established") Then
                        Console.WriteLine("✅ Proxy tunnel established successfully")

                        ' الآن يمكن إرسال SMTP عبر النفق
                        Return SendSMTPThroughTunnel(networkStream, enableSsl, username, password, fromEmail, fromName, toEmail, subject, body, attachments, smtpHost, proxyHost)
                    Else
                        Console.WriteLine($"❌ Proxy connection failed: {response}")
                        Return False
                    End If
                End Using
            End Using

        Catch ex As Exception
            Console.WriteLine($"❌ Direct proxy connection failed: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' إرسال SMTP عبر النفق المُنشأ مع البروكسي
    ''' </summary>
    Private Function SendSMTPThroughTunnel(networkStream As NetworkStream, enableSsl As Boolean,
                                          username As String, password As String,
                                          fromEmail As String, fromName As String,
                                          toEmail As String, subject As String, body As String,
                                          Optional attachments As List(Of String) = Nothing,
                                          Optional smtpHost As String = "",
                                          Optional proxyIP As String = "") As Boolean
        Try
            Console.WriteLine("📧 Starting SMTP communication through proxy tunnel")

            Dim stream As Stream = networkStream

            ' إذا كان SSL مطلوب، إنشاء SslStream
            If enableSsl Then
                Console.WriteLine("🔒 Establishing SSL connection")
                Dim sslStream As New System.Net.Security.SslStream(networkStream)
                sslStream.AuthenticateAsClient(smtpHost)
                stream = sslStream
            End If

            ' قراءة رسالة الترحيب من الخادم
            Dim welcomeResponse As String = ReadSMTPResponse(stream)
            Console.WriteLine($"📥 SMTP Welcome: {welcomeResponse}")

            ' إرسال EHLO
            SendSMTPCommand(stream, $"EHLO {Environment.MachineName}")
            Dim ehloResponse As String = ReadSMTPResponse(stream)
            Console.WriteLine($"📥 EHLO Response: {ehloResponse}")

            ' المصادقة إذا كانت مطلوبة
            If Not String.IsNullOrEmpty(username) Then
                Console.WriteLine("🔐 Starting authentication")

                ' إرسال AUTH LOGIN
                SendSMTPCommand(stream, "AUTH LOGIN")
                ReadSMTPResponse(stream)

                ' إرسال اسم المستخدم (مُرمز بـ Base64)
                Dim usernameB64 As String = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(username))
                SendSMTPCommand(stream, usernameB64)
                ReadSMTPResponse(stream)

                ' إرسال كلمة المرور (مُرمزة بـ Base64)
                Dim passwordB64 As String = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(password))
                SendSMTPCommand(stream, passwordB64)
                Dim authResponse As String = ReadSMTPResponse(stream)

                If Not authResponse.StartsWith("235") Then
                    Console.WriteLine($"❌ Authentication failed: {authResponse}")
                    Return False
                End If
                Console.WriteLine("✅ Authentication successful")
            End If

            ' إرسال MAIL FROM
            SendSMTPCommand(stream, $"MAIL FROM:<{fromEmail}>")
            Dim mailFromResponse As String = ReadSMTPResponse(stream)
            Console.WriteLine($"📥 MAIL FROM Response: {mailFromResponse}")

            ' إرسال RCPT TO
            SendSMTPCommand(stream, $"RCPT TO:<{toEmail}>")
            Dim rcptToResponse As String = ReadSMTPResponse(stream)
            Console.WriteLine($"📥 RCPT TO Response: {rcptToResponse}")

            ' إرسال DATA
            SendSMTPCommand(stream, "DATA")
            Dim dataResponse As String = ReadSMTPResponse(stream)
            Console.WriteLine($"📥 DATA Response: {dataResponse}")

            ' إنشاء محتوى البريد
            Dim emailContent As String = BuildEmailContent(fromEmail, fromName, toEmail, subject, body, attachments, proxyIP)

            ' إرسال محتوى البريد
            SendSMTPCommand(stream, emailContent)
            SendSMTPCommand(stream, ".") ' نهاية البيانات
            Dim sendResponse As String = ReadSMTPResponse(stream)
            Console.WriteLine($"📥 Send Response: {sendResponse}")

            ' إرسال QUIT
            SendSMTPCommand(stream, "QUIT")
            ReadSMTPResponse(stream)

            Console.WriteLine("✅ Email sent successfully through proxy tunnel!")
            Return True

        Catch ex As Exception
            Console.WriteLine($"❌ SMTP tunnel communication failed: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' إرسال أمر SMTP
    ''' </summary>
    Private Sub SendSMTPCommand(stream As Stream, command As String)
        Dim commandBytes As Byte() = System.Text.Encoding.ASCII.GetBytes(command & vbCrLf)
        stream.Write(commandBytes, 0, commandBytes.Length)
        Console.WriteLine($"📤 SMTP: {command}")
    End Sub

    ''' <summary>
    ''' قراءة استجابة SMTP
    ''' </summary>
    Private Function ReadSMTPResponse(stream As Stream) As String
        Dim buffer(4096) As Byte
        Dim bytesRead As Integer = stream.Read(buffer, 0, buffer.Length)
        Dim response As String = System.Text.Encoding.ASCII.GetString(buffer, 0, bytesRead).Trim()
        Return response
    End Function

    ''' <summary>
    ''' بناء محتوى البريد الإلكتروني مع headers البروكسي
    ''' </summary>
    Private Function BuildEmailContent(fromEmail As String, fromName As String, toEmail As String,
                                      subject As String, body As String,
                                      Optional attachments As List(Of String) = Nothing,
                                      Optional proxyIP As String = "") As String
        Dim content As New StringBuilder()

        ' Headers أساسية
        content.AppendLine($"From: ""{fromName}"" <{fromEmail}>")
        content.AppendLine($"To: {toEmail}")
        content.AppendLine($"Subject: {subject}")
        content.AppendLine($"Date: {DateTime.Now:R}")
        content.AppendLine("MIME-Version: 1.0")

        ' Headers البروكسي المخصصة - هذه ستظهر في البريد المستلم
        content.AppendLine("X-Sent-Via: Best-Sender-Direct-Proxy-Tunnel")
        content.AppendLine("X-Proxy-Method: TCP-CONNECT-Tunnel")
        content.AppendLine("X-Mailer: Best-Sender-Pro-v1.0")
        content.AppendLine($"X-Proxy-Session: {Guid.NewGuid().ToString("N").Substring(0, 8)}")

        If Not String.IsNullOrEmpty(proxyIP) Then
            content.AppendLine($"X-Proxy-IP: {proxyIP}")
            content.AppendLine($"X-Routing-Via: PROXY-{proxyIP}")
            content.AppendLine($"X-Original-Sender: MASKED-BY-PROXY-{proxyIP}")
        End If

        content.AppendLine($"X-Proxy-Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")

        If attachments Is Nothing OrElse attachments.Count = 0 Then
            ' بريد نصي بسيط
            content.AppendLine("Content-Type: text/html; charset=utf-8")
            content.AppendLine()
            content.AppendLine(body)
        Else
            ' بريد مع مرفقات (multipart)
            Dim boundary As String = "----=_NextPart_" & Guid.NewGuid().ToString("N")
            content.AppendLine($"Content-Type: multipart/mixed; boundary=""{boundary}""")
            content.AppendLine()

            ' الجزء النصي
            content.AppendLine($"--{boundary}")
            content.AppendLine("Content-Type: text/html; charset=utf-8")
            content.AppendLine()
            content.AppendLine(body)
            content.AppendLine()

            ' المرفقات
            For Each attachment In attachments
                If File.Exists(attachment) Then
                    content.AppendLine($"--{boundary}")
                    content.AppendLine($"Content-Type: application/octet-stream; name=""{Path.GetFileName(attachment)}""")
                    content.AppendLine("Content-Transfer-Encoding: base64")
                    content.AppendLine($"Content-Disposition: attachment; filename=""{Path.GetFileName(attachment)}""")
                    content.AppendLine()

                    Dim fileBytes As Byte() = File.ReadAllBytes(attachment)
                    Dim base64Content As String = Convert.ToBase64String(fileBytes)
                    content.AppendLine(base64Content)
                    content.AppendLine()
                End If
            Next

            content.AppendLine($"--{boundary}--")
        End If

        Return content.ToString()
    End Function
    ''' <summary>
    ''' معالج حدث النقر بزر الفأرة الأيمن على GridControl3 - MouseDown
    ''' </summary>
    Private Sub GridControl3_MouseDown(sender As Object, e As MouseEventArgs) Handles GridControl3.MouseDown
        If e.Button = MouseButtons.Right Then
            ' إنشاء قائمة منبثقة احترافية مستوحاة من Binance
            ShowBinanceStyleContextMenu(e.Location)
        End If
    End Sub

    ''' <summary>
    ''' إظهار قائمة منبثقة بتصميم Binance الاحترافي
    ''' </summary>
    Private Sub ShowBinanceStyleContextMenu(location As System.Drawing.Point)
        Try
            ' إنشاء قائمة منبثقة مخصصة
            Dim contextMenu As New ContextMenuStrip()

            ' تطبيق تصميم Binance الاحترافي
            ApplyBinanceStyle(contextMenu)

            ' إضافة العناصر مع الأيقونات والتصميم الاحترافي
            AddBinanceStyleMenuItems(contextMenu)

            ' عرض القائمة
            contextMenu.Show(GridControl3, location)

        Catch ex As Exception
            Console.WriteLine($"❌ Error showing Binance style menu: {ex.Message}")
            ' fallback إلى قائمة بسيطة
            ShowSimpleContextMenu(location)
        End Try
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على القائمة
    ''' </summary>
    Private Sub ApplyBinanceStyle(contextMenu As ContextMenuStrip)
        ' ألوان Binance الاحترافية
        Dim binanceYellow As Color = Color.FromArgb(240, 185, 11)  ' #F0B90B
        Dim binanceDark As Color = Color.FromArgb(24, 26, 32)      ' #181A20
        Dim binanceGray As Color = Color.FromArgb(47, 51, 61)      ' #2F333D
        Dim binanceLight As Color = Color.FromArgb(234, 236, 239)  ' #EAECEF

        ' تصميم القائمة الرئيسية
        contextMenu.BackColor = binanceDark
        contextMenu.ForeColor = binanceLight
        contextMenu.Font = GetComfortaaFont(9.5F, System.Drawing.FontStyle.Regular)
        contextMenu.Margin = New Padding(0)
        contextMenu.Padding = New Padding(8, 12, 8, 12)
        contextMenu.RenderMode = ToolStripRenderMode.Professional

        ' تخصيص الرندر
        contextMenu.Renderer = New BinanceMenuRenderer()

        ' إعدادات إضافية
        contextMenu.ShowImageMargin = True
        contextMenu.ShowCheckMargin = False
        contextMenu.AutoSize = True
        contextMenu.DropShadowEnabled = True
    End Sub

    ''' <summary>
    ''' إضافة عناصر القائمة بتصميم Binance
    ''' </summary>
    Private Sub AddBinanceStyleMenuItems(contextMenu As ContextMenuStrip)
        ' ألوان Binance
        Dim binanceYellow As Color = Color.FromArgb(240, 185, 11)
        Dim binanceLight As Color = Color.FromArgb(234, 236, 239)
        Dim binanceGreen As Color = Color.FromArgb(14, 203, 129)   ' #0ECB81
        Dim binanceRed As Color = Color.FromArgb(246, 70, 93)      ' #F6465D

        ' عنوان القائمة
        Dim headerItem As New ToolStripLabel("PROXY MANAGEMENT")
        headerItem.ForeColor = binanceYellow
        headerItem.Font = GetComfortaaFont(10.0F, System.Drawing.FontStyle.Bold)
        headerItem.Margin = New Padding(0, 0, 0, 8)
        contextMenu.Items.Add(headerItem)

        ' فاصل أنيق
        contextMenu.Items.Add(CreateBinanceSeparator())

        ' العناصر الرئيسية مع أيقونات من الموارد
        contextMenu.Items.Add(CreateBinanceMenuItemWithIcon("Import Proxy List", GetResourceIcon("add32x32.png"), binanceLight, AddressOf SimpleSelectProxy))
        contextMenu.Items.Add(CreateBinanceMenuItemWithIcon("Start Proxy Check", GetResourceIcon("Start_Image32x32"), binanceGreen, AddressOf SimpleStartCheck))

        contextMenu.Items.Add(CreateBinanceSeparator())

        contextMenu.Items.Add(CreateBinanceMenuItemWithIcon("Remove Invalid", GetResourceIcon("Notworking32x32"), binanceRed, AddressOf SimpleRemoveInvalid))
        contextMenu.Items.Add(CreateBinanceMenuItemWithIcon("Clear All Proxies", GetResourceIcon("trash_32x32"), binanceRed, AddressOf SimpleRemoveAll))

        contextMenu.Items.Add(CreateBinanceSeparator())

        contextMenu.Items.Add(CreateBinanceMenuItemWithIcon("Save Valid Proxies", GetResourceIcon("Save_Image"), binanceGreen, AddressOf SimpleSaveValid))
        contextMenu.Items.Add(CreateBinanceMenuItemWithIcon("Stop Scanning", GetResourceIcon("Stop32x32"), binanceYellow, AddressOf SimpleStopScan))

        ' إضافة معلومات الحالة
        contextMenu.Items.Add(CreateBinanceSeparator())

        Dim validCount = _proxyList.Where(Function(p) p.Status = ProxyStatus.Valid).Count()
        Dim totalCount = _proxyList.Count
        Dim statusItem As New ToolStripLabel($"Valid: {validCount} / Total: {totalCount}")
        statusItem.ForeColor = binanceYellow
        statusItem.Font = GetComfortaaFont(8.5F, System.Drawing.FontStyle.Regular)
        contextMenu.Items.Add(statusItem)
    End Sub

    ''' <summary>
    ''' إنشاء عنصر قائمة بتصميم Binance
    ''' </summary>
    Private Function CreateBinanceMenuItem(text As String, icon As String, color As Color, handler As EventHandler) As ToolStripMenuItem
        Dim item As New ToolStripMenuItem(text)
        item.ForeColor = color
        item.Font = GetComfortaaFont(9.5F, System.Drawing.FontStyle.Regular)
        item.Padding = New Padding(12, 8, 12, 8)
        item.Margin = New Padding(0, 2, 0, 2)

        ' إضافة معالج الحدث
        AddHandler item.Click, handler

        ' تأثيرات التفاعل
        AddHandler item.MouseEnter, Sub(s, e)
                                        item.BackColor = Color.FromArgb(47, 51, 61)
                                        item.ForeColor = Color.FromArgb(240, 185, 11)
                                    End Sub

        AddHandler item.MouseLeave, Sub(s, e)
                                        item.BackColor = Color.FromArgb(24, 26, 32)
                                        item.ForeColor = color
                                    End Sub

        Return item
    End Function

    ''' <summary>
    ''' إنشاء عنصر قائمة مع أيقونة من الموارد
    ''' </summary>
    Private Function CreateBinanceMenuItemWithIcon(text As String, icon As System.Drawing.Image, color As Color, handler As EventHandler) As ToolStripMenuItem
        Dim item As New ToolStripMenuItem(text)
        item.ForeColor = color
        item.Font = GetComfortaaFont(9.5F, System.Drawing.FontStyle.Regular)
        item.Padding = New Padding(12, 8, 12, 8)
        item.Margin = New Padding(0, 2, 0, 2)

        ' إضافة الأيقونة
        If icon IsNot Nothing Then
            item.Image = ResizeIcon(icon, 16, 16)
            item.ImageAlign = ContentAlignment.MiddleLeft
        End If

        ' إضافة معالج الحدث
        AddHandler item.Click, handler

        ' تأثيرات التفاعل
        AddHandler item.MouseEnter, Sub(s, e)
                                        item.BackColor = Color.FromArgb(47, 51, 61)
                                        item.ForeColor = Color.FromArgb(240, 185, 11)
                                    End Sub

        AddHandler item.MouseLeave, Sub(s, e)
                                        item.BackColor = Color.FromArgb(24, 26, 32)
                                        item.ForeColor = color
                                    End Sub

        Return item
    End Function

    ''' <summary>
    ''' الحصول على أيقونة من الموارد
    ''' </summary>
    Private Function GetResourceIcon(iconName As String) As System.Drawing.Image
        Try
            Select Case iconName.ToLower()
                Case "add32x32.png"
                    Return My.Resources.add32x32
                Case "start_image32x32"
                    Return My.Resources.Start_Image32x32
                Case "notworking32x32"
                    Return My.Resources.Notworking32x32
                Case "trash_32x32"
                    Return My.Resources.trash_32x32
                Case "save_image"
                    Return My.Resources.Save_Image
                Case "stop32x32"
                    Return My.Resources.Stop32x32
                Case Else
                    Return Nothing
            End Select
        Catch ex As Exception
            Console.WriteLine($"Error loading icon {iconName}: {ex.Message}")
            Return Nothing
        End Try
    End Function

    ''' <summary>
    ''' تغيير حجم الأيقونة
    ''' </summary>
    Private Function ResizeIcon(originalIcon As System.Drawing.Image, width As Integer, height As Integer) As System.Drawing.Image
        Try
            Dim resizedIcon As New Bitmap(width, height)
            Using g As Graphics = Graphics.FromImage(resizedIcon)
                g.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
                g.DrawImage(originalIcon, 0, 0, width, height)
            End Using
            Return resizedIcon
        Catch ex As Exception
            Return originalIcon
        End Try
    End Function

    ''' <summary>
    ''' الحصول على خط Comfortaa مع fallback
    ''' </summary>
    Private Function GetComfortaaFont(size As Single, style As System.Drawing.FontStyle) As Font
        Try
            ' محاولة استخدام خط Comfortaa
            Return New Font("Comfortaa", size, style)
        Catch ex As Exception
            Try
                ' fallback إلى Segoe UI
                Return New Font("Segoe UI", size, style)
            Catch ex2 As Exception
                ' fallback نهائي إلى Arial
                Return New Font("Arial", size, style)
            End Try
        End Try
    End Function

    ''' <summary>
    ''' إنشاء فاصل بتصميم Binance
    ''' </summary>
    Private Function CreateBinanceSeparator() As ToolStripSeparator
        Dim separator As New ToolStripSeparator()
        separator.BackColor = Color.FromArgb(47, 51, 61)
        separator.ForeColor = Color.FromArgb(47, 51, 61)
        separator.Margin = New Padding(0, 4, 0, 4)
        Return separator
    End Function

    ''' <summary>
    ''' قائمة بسيطة كـ fallback
    ''' </summary>
    Private Sub ShowSimpleContextMenu(location As System.Drawing.Point)
        Dim contextMenu As New ContextMenuStrip()
        contextMenu.Items.Add("📁 Select Proxy List", Nothing, AddressOf SimpleSelectProxy)
        contextMenu.Items.Add("🚀 Start Check Proxy", Nothing, AddressOf SimpleStartCheck)
        contextMenu.Items.Add(New ToolStripSeparator())
        contextMenu.Items.Add("🗑️ Remove Invalid", Nothing, AddressOf SimpleRemoveInvalid)
        contextMenu.Items.Add("🧹 Remove All", Nothing, AddressOf SimpleRemoveAll)
        contextMenu.Items.Add(New ToolStripSeparator())
        contextMenu.Items.Add("💾 Save Valid Proxies", Nothing, AddressOf SimpleSaveValid)
        contextMenu.Items.Add("🛑 Stop Scan", Nothing, AddressOf SimpleStopScan)
        contextMenu.Show(GridControl3, location)
    End Sub

    ' دوال بسيطة للقائمة المنبثقة
    Private Sub SimpleSelectProxy(sender As Object, e As EventArgs)
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            ImportProxiesFromFile(openFileDialog.FileName)
            DevExpress.XtraEditors.XtraMessageBox.Show($"Loaded {_proxyList.Count} proxies", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub SimpleStartCheck(sender As Object, e As EventArgs)
        If _proxyList.Count = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("No proxies to check. Please load proxy list first.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        ' بدء فحص البروكسي بطريقة مبسطة
        Console.WriteLine($"🚀 Starting proxy check for {_proxyList.Count} proxies...")

        If BackgroundWorker3 IsNot Nothing AndAlso Not BackgroundWorker3.IsBusy Then
            BackgroundWorker3.RunWorkerAsync()
            DevExpress.XtraEditors.XtraMessageBox.Show("Proxy checking started!", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            DevExpress.XtraEditors.XtraMessageBox.Show("Proxy check is already running!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub SimpleRemoveInvalid(sender As Object, e As EventArgs)
        Dim invalidCount = _proxyList.Where(Function(p) p.Status = ProxyStatus.NotWorking).Count()
        If invalidCount = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("No invalid proxies found.", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        _proxyList.RemoveAll(Function(p) p.Status = ProxyStatus.NotWorking)
        UpdateProxyGridControl()
        DevExpress.XtraEditors.XtraMessageBox.Show($"Removed {invalidCount} invalid proxies", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub SimpleRemoveAll(sender As Object, e As EventArgs)
        If DevExpress.XtraEditors.XtraMessageBox.Show("Remove all proxies?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            _proxyList.Clear()
            UpdateProxyGridControl()
            DevExpress.XtraEditors.XtraMessageBox.Show("All proxies removed", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub SimpleSaveValid(sender As Object, e As EventArgs)
        Dim validProxies = _proxyList.Where(Function(p) p.Status = ProxyStatus.Valid).ToList()
        If validProxies.Count = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("No valid proxies to save.", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Dim saveDialog As New SaveFileDialog()
        saveDialog.Filter = "Text Files (*.txt)|*.txt"
        saveDialog.FileName = $"ValidProxies_{DateTime.Now:yyyyMMdd}.txt"

        If saveDialog.ShowDialog() = DialogResult.OK Then
            Dim lines = validProxies.Select(Function(p) p.Proxy).ToArray()
            File.WriteAllLines(saveDialog.FileName, lines)
            DevExpress.XtraEditors.XtraMessageBox.Show($"Saved {validProxies.Count} valid proxies", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub SimpleStopScan(sender As Object, e As EventArgs)
        If BackgroundWorker3 IsNot Nothing AndAlso BackgroundWorker3.IsBusy Then
            BackgroundWorker3.CancelAsync()
            DevExpress.XtraEditors.XtraMessageBox.Show("Proxy scan stopped", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            DevExpress.XtraEditors.XtraMessageBox.Show("No active scan to stop", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

#End Region

End Class

''' <summary>
''' Renderer مخصص لتصميم Binance الاحترافي
''' </summary>
Public Class BinanceMenuRenderer
    Inherits ToolStripProfessionalRenderer

    Public Sub New()
        MyBase.New(New BinanceColorTable())
    End Sub

    Protected Overrides Sub OnRenderMenuItemBackground(e As ToolStripItemRenderEventArgs)
        If Not e.Item.Selected Then
            MyBase.OnRenderMenuItemBackground(e)
        Else
            ' تأثير hover بتصميم Binance
            Dim hoverColor As Color = Color.FromArgb(47, 51, 61)
            Using brush As New SolidBrush(hoverColor)
                e.Graphics.FillRectangle(brush, e.Item.ContentRectangle)
            End Using
        End If
    End Sub

    Protected Overrides Sub OnRenderSeparator(e As ToolStripSeparatorRenderEventArgs)
        ' فاصل مخصص بتصميم Binance
        Dim separatorColor As Color = Color.FromArgb(47, 51, 61)
        Using pen As New Pen(separatorColor, 1)
            Dim y As Integer = e.Item.ContentRectangle.Top + e.Item.ContentRectangle.Height \ 2
            e.Graphics.DrawLine(pen, e.Item.ContentRectangle.Left + 10, y, e.Item.ContentRectangle.Right - 10, y)
        End Using
    End Sub

    Protected Overrides Sub OnRenderToolStripBorder(e As ToolStripRenderEventArgs)
        ' حدود القائمة بتصميم Binance
        Dim borderColor As Color = Color.FromArgb(240, 185, 11)
        Using pen As New Pen(borderColor, 2)
            e.Graphics.DrawRectangle(pen, 0, 0, e.ToolStrip.Width - 1, e.ToolStrip.Height - 1)
        End Using
    End Sub
End Class

''' <summary>
''' جدول ألوان Binance المخصص
''' </summary>
Public Class BinanceColorTable
    Inherits ProfessionalColorTable

    Public Overrides ReadOnly Property MenuItemSelected As Color
        Get
            Return Color.FromArgb(47, 51, 61)  ' لون hover
        End Get
    End Property

    Public Overrides ReadOnly Property MenuItemBorder As Color
        Get
            Return Color.FromArgb(240, 185, 11)  ' لون الحدود
        End Get
    End Property

    Public Overrides ReadOnly Property MenuBorder As Color
        Get
            Return Color.FromArgb(240, 185, 11)  ' لون حدود القائمة
        End Get
    End Property

    Public Overrides ReadOnly Property ToolStripDropDownBackground As Color
        Get
            Return Color.FromArgb(24, 26, 32)  ' خلفية القائمة
        End Get
    End Property

    Public Overrides ReadOnly Property ImageMarginGradientBegin As Color
        Get
            Return Color.FromArgb(24, 26, 32)
        End Get
    End Property

    Public Overrides ReadOnly Property ImageMarginGradientMiddle As Color
        Get
            Return Color.FromArgb(24, 26, 32)
        End Get
    End Property

    Public Overrides ReadOnly Property ImageMarginGradientEnd As Color
        Get
            Return Color.FromArgb(24, 26, 32)
        End Get
    End Property
End Class



