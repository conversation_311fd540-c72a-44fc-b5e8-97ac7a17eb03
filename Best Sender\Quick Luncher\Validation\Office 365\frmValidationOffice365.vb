﻿Imports System.IO
Imports System.Net.Http
Imports System.Threading.Tasks
Imports System.Collections.Generic
Imports Newtonsoft.Json
Imports System.Threading
Imports System.ComponentModel
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.XtraEditors
Imports DevExpress.XtraEditors.Repository
Imports System.Data
Imports System.Drawing
Imports DevExpress.Utils
Imports DevExpress.XtraEditors.Controls
Public Class frmValidationOffice365
    Private ReadOnly Services As New List(Of ServiceInfo) From {
        New ServiceInfo With {
            .Name = "Office 365",
            .FileName = "Office365_Valid.txt",
            .Url = "https://login.microsoftonline.com/common/GetCredentialType",
            .Method = "POST",
            .Icon = My.Resources.Microsoft_36516x16, ' تأكد من اسم المورد بالضبط
            .ValidIcon = My.Resources.Valid16x16, ' تأكد من اسم المورد
            .InvalidIcon = My.Resources.Error16x16 ' تأكد من اسم المورد
        },
        New ServiceInfo With {
            .Name = "GoDaddy",
            .FileName = "GoDaddy_Valid.txt",
            .Url = "https://email.godaddy.com/autodiscover/autodiscover.xml?Email=",
            .Method = "GET",
            .Icon = My.Resources.GoDaddy16x16, ' تأكد من اسم المورد
            .ValidIcon = My.Resources.Valid16x16, ' تأكد من اسم المورد
             .InvalidIcon = My.Resources.Error16x16 ' تأكد من اسم المورد
        }
    }
    Private ReadOnly ResultsFilePath As String = Path.Combine(Application.StartupPath, "Results.txt")
    Private ReadOnly ErrorLogPath As String = Path.Combine(Application.StartupPath, "errors.log")
    Private LeadsFilePath As String = ""
    Private ReadOnly httpClient As New HttpClient()
    Private currentIndex As Integer = 0
    Private totalEmails As Integer = 0
    Private processedEmails As Integer = 0
    Private isProcessing As Boolean = False
    Private Class ServiceInfo
        Public Property Name As String
        Public Property FileName As String
        Public Property Url As String
        Public Property Method As String
        Public Property Icon As Image
        Public Property ValidIcon As Image
        Public Property InvalidIcon As Image
    End Class
    Private Sub frmValidationOffice365_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        SetupGridControl()
        httpClient.Timeout = TimeSpan.FromSeconds(10)
        httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64)")
        txtSleepTime.Text = "1000"
        Dim resultsFolder = Path.Combine(Application.StartupPath, "Results")
        If Not Directory.Exists(resultsFolder) Then
            Directory.CreateDirectory(resultsFolder)
        End If
        For Each service In Services
            service.FileName = Path.Combine(resultsFolder, service.FileName)
        Next
        BackgroundWorker1.WorkerReportsProgress = True
        BackgroundWorker1.WorkerSupportsCancellation = True
        ' Initialize btnSave visibility
        UpdateSaveButtonVisibility()
    End Sub
    Private Sub SetupGridControl()
        If GridControl1.InvokeRequired Then
            GridControl1.Invoke(Sub() SetupGridControl())
            Return
        End If
        Dim gridView As GridView = TryCast(GridControl1.MainView, GridView)
        If gridView Is Nothing Then Return
        gridView.Columns.Clear()
        ' Add columns in order with adjusted width for "#" column
        Dim colNumber As DevExpress.XtraGrid.Columns.GridColumn = gridView.Columns.AddField("#")
        colNumber.Visible = True
        colNumber.Width = 5 ' تحديد عرض العمود 50 بكسل هنا
        gridView.Columns.AddField("Email").Visible = True
        ' Add Service Icon column
        Dim iconCol As New DevExpress.XtraGrid.Columns.GridColumn()
        iconCol.FieldName = "ServiceIcon"
        iconCol.Caption = "Service"
        iconCol.Visible = True
        iconCol.Width = 60
        Dim riPictureEdit As New RepositoryItemPictureEdit()
        riPictureEdit.SizeMode = PictureSizeMode.Squeeze
        riPictureEdit.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        iconCol.ColumnEdit = riPictureEdit
        gridView.Columns.Add(iconCol)
        gridView.Columns.AddField("Status").Visible = True
        gridView.Columns.AddField("Time").Visible = True
        gridView.Columns.AddField("Date").Visible = True
        ' Add Valid column
        Dim validCol As New DevExpress.XtraGrid.Columns.GridColumn()
        validCol.FieldName = "Valid"
        validCol.Caption = "Valid"
        validCol.Visible = True
        validCol.ColumnEdit = New RepositoryItemCheckEdit()
        gridView.Columns.Add(validCol)
    End Sub
    Private Sub btnSelectFile_Click(sender As Object, e As EventArgs) Handles btnSelectFile.Click
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        openFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        openFileDialog.Title = "Select leads.txt file"
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            LeadsFilePath = openFileDialog.FileName
            txtFilePath.Text = LeadsFilePath
            LoadEmailsToGrid()
            UpdateSaveButtonVisibility()
        End If
    End Sub
    Private Sub LoadEmailsToGrid()
        If GridControl1.InvokeRequired Then
            GridControl1.Invoke(Sub() LoadEmailsToGrid())
            Return
        End If
        Dim gridView As GridView = TryCast(GridControl1.MainView, GridView)
        If gridView Is Nothing Then Return
        gridView.GridControl.DataSource = Nothing
        If File.Exists(LeadsFilePath) Then
            Dim emails = File.ReadLines(LeadsFilePath).
                        Where(Function(emailLine) Not String.IsNullOrWhiteSpace(emailLine)).
                        Select(Function(cleanEmail) cleanEmail.Trim())
            Dim dataTable As New DataTable()
            dataTable.Columns.Add("#", GetType(Integer))
            dataTable.Columns.Add("Email", GetType(String))
            dataTable.Columns.Add("ServiceIcon", GetType(Image))
            dataTable.Columns.Add("Status", GetType(String))
            dataTable.Columns.Add("Time", GetType(String))
            dataTable.Columns.Add("Date", GetType(String))
            dataTable.Columns.Add("Valid", GetType(Boolean))
            Dim index As Integer = 1
            For Each email In emails
                ' Default to Office 365 icon, will be updated during verification
                dataTable.Rows.Add(index, email, My.Resources.pending16x16, "Pending...", "", DateTime.Now.ToString("yyyy-MM-dd"), False)
                index += 1
            Next
            GridControl1.DataSource = dataTable
            totalEmails = dataTable.Rows.Count
            Label2.Text = $"Total Emails: {totalEmails}"
        End If
        UpdateSaveButtonVisibility()
    End Sub
    Private Sub UpdateGridRow(rowHandle As Integer, status As String, isValid As Boolean, service As ServiceInfo)
        If GridControl1.InvokeRequired Then
            GridControl1.Invoke(Sub() UpdateGridRow(rowHandle, status, isValid, service))
            Return
        End If
        Dim gridView As GridView = TryCast(GridControl1.MainView, GridView)
        If gridView Is Nothing Then Return
        Dim icon As Image = If(isValid, service.ValidIcon, service.InvalidIcon)
        gridView.SetRowCellValue(rowHandle, "ServiceIcon", icon)
        gridView.SetRowCellValue(rowHandle, "Status", status)
        gridView.SetRowCellValue(rowHandle, "Time", DateTime.Now.ToString("HH:mm:ss"))
        gridView.SetRowCellValue(rowHandle, "Valid", isValid)
    End Sub
    Private Sub UpdateProgress(progress As Integer)
        If ProgressBarControl1.InvokeRequired Then
            ProgressBarControl1.Invoke(Sub() UpdateProgress(progress))
        Else
            ProgressBarControl1.EditValue = progress
        End If
    End Sub
    Private Sub btnStart_Click(sender As Object, e As EventArgs) Handles btnStart.Click
        If String.IsNullOrEmpty(LeadsFilePath) Then
            XtraMessageBox.Show("Please select leads.txt file first!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If
        Dim gridView As GridView = TryCast(GridControl1.MainView, GridView)
        If gridView Is Nothing OrElse gridView.RowCount = 0 Then
            XtraMessageBox.Show("No emails to process!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If
        isProcessing = True
        ToggleControls(False)
        ProgressBarControl1.Properties.Minimum = 0
        ProgressBarControl1.Properties.Maximum = 100
        ProgressBarControl1.EditValue = 0
        processedEmails = 0
        Label1.Text = $"Processed: {processedEmails}/{totalEmails}"
        Label3.Text = $"Last Check: {DateTime.Now.ToString("HH:mm:ss")}"
        UpdateSaveButtonVisibility()
        If Not BackgroundWorker1.IsBusy Then
            BackgroundWorker1.RunWorkerAsync()
        End If
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Dim gridView As GridView = TryCast(GridControl1.MainView, GridView)
        If gridView Is Nothing Then Return
        For i As Integer = currentIndex To gridView.RowCount - 1
            If BackgroundWorker1.CancellationPending Then
                e.Cancel = True
                Exit For
            End If
            Dim currentEmail As String = gridView.GetRowCellValue(i, "Email").ToString()
            Dim currentStatus As String = gridView.GetRowCellValue(i, "Status").ToString()
            Dim progress As Integer = CInt((i + 1) / gridView.RowCount * 100)
            BackgroundWorker1.ReportProgress(progress, i)
            Dim isValid As Boolean = False
            Dim status As String = "Invalid"
            Dim verifiedService As ServiceInfo = Nothing
            For Each service In Services
                If BackgroundWorker1.CancellationPending Then Exit For
                Try
                    Dim isSuccess As Boolean = False
                    If service.Method = "GET" Then
                        Dim responseData = httpClient.GetStringAsync(service.Url & currentEmail).Result
                        isSuccess = responseData.Contains("AutodiscoverResponse")
                    ElseIf service.Method = "POST" Then
                        Dim postData = New With {.Username = currentEmail, .federationFlags = 0}
                        Dim jsonData = JsonConvert.SerializeObject(postData)
                        Dim content = New StringContent(jsonData, System.Text.Encoding.UTF8, "application/json")
                        Dim response = httpClient.PostAsync(service.Url, content).Result
                        Dim responseData = response.Content.ReadAsStringAsync().Result
                        Dim resultDict = JsonConvert.DeserializeObject(Of Dictionary(Of String, Object))(responseData)
                        isSuccess = resultDict?.ContainsKey("IfExistsResult") AndAlso resultDict("IfExistsResult").ToString() = "0"
                    End If
                    If isSuccess Then
                        verifiedService = service
                        isValid = True
                        status = $"Verified: {service.Name}"
                        Exit For
                    End If
                Catch ex As Exception
                    ' Just log to error log without saving to results file
                    File.AppendAllText(ErrorLogPath, $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {currentEmail} - Error in {service.Name}: {ex.Message}" & Environment.NewLine)
                End Try
            Next
            If verifiedService Is Nothing Then
                verifiedService = Services(0) ' Default to first service for icon
            End If
            BackgroundWorker1.ReportProgress(progress, New Object() {i, status, isValid, verifiedService})
            Dim sleepTime As Integer = 1000
            If Integer.TryParse(txtSleepTime.Text, sleepTime) AndAlso sleepTime > 0 Then
                Threading.Thread.Sleep(sleepTime)
            End If
        Next
    End Sub
    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        ProgressBarControl1.EditValue = e.ProgressPercentage
        If TypeOf e.UserState Is Integer Then
            currentIndex = CInt(e.UserState)
            processedEmails = currentIndex + 1
            Label1.Text = $"Processed: {processedEmails}/{totalEmails}"
            Label3.Text = $"Last Check: {DateTime.Now.ToString("HH:mm:ss")}"
        ElseIf TypeOf e.UserState Is Object() Then
            Dim params As Object() = DirectCast(e.UserState, Object())
            Dim rowIndex As Integer = CInt(params(0))
            Dim status As String = CStr(params(1))
            Dim isValid As Boolean = CBool(params(2))
            Dim service As ServiceInfo = CType(params(3), ServiceInfo)
            UpdateGridRow(rowIndex, status, isValid, service)
            currentIndex = rowIndex
            processedEmails = currentIndex + 1
            Label1.Text = $"Processed: {processedEmails}/{totalEmails}"
            Label3.Text = $"Last Check: {DateTime.Now.ToString("HH:mm:ss")}"
        End If
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        isProcessing = False
        If e.Cancelled Then
            XtraMessageBox.Show("The operation was stopped by the user", "Operation Stopped", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        ElseIf e.Error IsNot Nothing Then
            XtraMessageBox.Show($"An error occurred: {e.Error.Message}", "Processing Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Else
            XtraMessageBox.Show("All email addresses have been processed successfully", "Operation Completed", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
        ToggleControls(True)
        UpdateSaveButtonVisibility()
    End Sub
    Private Sub ToggleControls(enabled As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(Sub() ToggleControls(enabled))
        Else
            btnStart.Enabled = enabled
            btnSelectFile.Enabled = enabled
            btnStop.Enabled = Not enabled
            BntResetAll.Enabled = enabled
            UpdateSaveButtonVisibility()
        End If
    End Sub
    Private Sub SaveToFile(filePath As String, content As String)
        Try
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim validationFolder As String = Path.Combine(desktopPath, "Validation Office365")
            If Not Directory.Exists(validationFolder) Then
                Directory.CreateDirectory(validationFolder)
            End If
            Dim fileName As String = Path.GetFileName(filePath)
            Dim newFilePath As String = Path.Combine(validationFolder, fileName)
            Using writer As New StreamWriter(newFilePath, True, System.Text.Encoding.UTF8)
                writer.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {content}")
            End Using
        Catch ex As Exception
            XtraMessageBox.Show($"Error saving file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub btnStop_Click(sender As Object, e As EventArgs) Handles btnStop.Click
        If BackgroundWorker1.IsBusy Then
            BackgroundWorker1.CancelAsync()
        End If
    End Sub
    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        MyBase.OnFormClosing(e)
        If BackgroundWorker1.IsBusy Then
            BackgroundWorker1.CancelAsync()
        End If
        httpClient.Dispose()
    End Sub
    Private Sub BntResetAll_Click(sender As Object, e As EventArgs) Handles BntResetAll.Click
        Dim result As DialogResult = XtraMessageBox.Show(
           "Are you sure you want to clear all data?",
           "Confirm Clear",
           MessageBoxButtons.YesNo,
           MessageBoxIcon.Question)
        If result = DialogResult.Yes Then
            GridControl1.DataSource = Nothing
            Dim gridView As GridView = TryCast(GridControl1.MainView, GridView)
            If gridView IsNot Nothing Then
                gridView.Columns.Clear()
                SetupGridControl()
            End If
            currentIndex = 0
            processedEmails = 0
            totalEmails = 0
            Label1.Text = "Processed: 0/0"
            Label2.Text = "Total Emails: 0"
            ProgressBarControl1.EditValue = 0
            txtFilePath.Text = ""
            LeadsFilePath = ""
            UpdateSaveButtonVisibility()
            XtraMessageBox.Show(
                "All data has been cleared successfully",
                "Operation Complete",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub UpdateSaveButtonVisibility()
        If Me.InvokeRequired Then
            Me.Invoke(Sub() UpdateSaveButtonVisibility())
            Return
        End If
        Dim gridView As GridView = TryCast(GridControl1.MainView, GridView)
        Dim hasData As Boolean = gridView IsNot Nothing AndAlso gridView.RowCount > 0
        Dim hasValidData As Boolean = False
        If hasData Then
            For i As Integer = 0 To gridView.RowCount - 1
                If Convert.ToBoolean(gridView.GetRowCellValue(i, "Valid")) Then
                    hasValidData = True
                    Exit For
                End If
            Next
        End If
        ' Show button only if not processing and has valid data
        BntSave.Visible = Not isProcessing AndAlso hasValidData
    End Sub
    Private Sub BntSave_Click(sender As Object, e As EventArgs) Handles BntSave.Click
        ' Get the GridView from GridControl
        Dim gridView As GridView = TryCast(GridControl1.MainView, GridView)
        ' Check if there's data to save
        If gridView Is Nothing OrElse gridView.RowCount = 0 Then
            XtraMessageBox.Show("No data to save", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        Try
            ' Create validation folder on desktop
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim validationFolder As String = Path.Combine(desktopPath, "Validation Office365")
            If Not Directory.Exists(validationFolder) Then
                Directory.CreateDirectory(validationFolder)
            End If
            ' Collections to store emails (using HashSet to avoid duplicates)
            Dim validEmails As New HashSet(Of String)(StringComparer.OrdinalIgnoreCase)
            Dim invalidEmails As New HashSet(Of String)(StringComparer.OrdinalIgnoreCase)
            ' Process all rows in the grid
            For i As Integer = 0 To gridView.RowCount - 1
                Dim email As String = gridView.GetRowCellValue(i, "Email").ToString().Trim()
                Dim isValid As Boolean = Convert.ToBoolean(gridView.GetRowCellValue(i, "Valid"))
                ' Categorize email as valid or invalid
                If isValid Then
                    validEmails.Add(email)
                Else
                    invalidEmails.Add(email)
                End If
            Next
            ' Save valid emails (sorted alphabetically)
            If validEmails.Count > 0 Then
                File.WriteAllLines(Path.Combine(validationFolder, "Valid.txt"),
                             validEmails.OrderBy(Function(email) email))
            End If
            ' Save invalid emails (sorted alphabetically)
            If invalidEmails.Count > 0 Then
                File.WriteAllLines(Path.Combine(validationFolder, "Invalid.txt"),
                             invalidEmails.OrderBy(Function(email) email))
            End If
            ' Show success message
            Dim successMessage As String = $"Emails saved successfully to:{Environment.NewLine}{validationFolder}"
            XtraMessageBox.Show(successMessage, "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            ' Show error message if something went wrong
            XtraMessageBox.Show($"Error saving files: {ex.Message}", "Error",
                          MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class