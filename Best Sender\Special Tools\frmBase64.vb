﻿Imports System.IO
Imports System.Net
Imports System.Text.RegularExpressions
Imports DevExpress.XtraEditors
Imports TheArtOfDev.HtmlRenderer.Core.Entities
Imports System.Text
Public Class frmBase64
    Dim originalHtml As String = "" ' تخزين HTML الأصلي
    Dim dynamicKeywords As String() = {
        "[-Email-]", "[-IP-]", "[-IPChina-]", "[-Domain-]", "[-Name-]", "[-Date-]", "[-DateTomorrow-]",
        "[-RandomBrowser-]", "[-RCountry-]", "[-FakePhone-]", "[-FakeEmail-]", "[-NewYork-]", "[-UCase-]",
        "[-Link-]", "[-Logo-]", "[-QRCode-]", "[-RN1-]", "[-RN2-]", "[-RN3-]", "[-RN4-]", "[-RN5-]", "[-RN6-]",
        "[-RN7-]", "[-RN8-]", "[-RN9-]", "[-RN10-]", "[-RN12-]", "[-RCh1-]", "[-RCh2-]", "[-RCh3-]", "[-RCh4-]",
        "[-RCh5-]", "[-RCh6-]", "[-RCh7-]", "[-RCh8-]"
    }
    ' 🔄 دالة تقوم بمعالجة النصوص داخل HTML
    Private Function EncryptHtmlText(m As Match) As String
        Dim textOnly As String = WebUtility.HtmlDecode(m.Groups(2).Value)
        If String.IsNullOrEmpty(textOnly) Then Return m.Value
        Dim newText As New StringBuilder()
        Dim regexDynamic As String = String.Join("|", dynamicKeywords.Select(Function(k) Regex.Escape(k)))
        Dim parts As String() = Regex.Split(textOnly, $"({regexDynamic})") ' ✅ الحفاظ على الكلمات الديناميكية بدون تغيير
        For Each part In parts
            If dynamicKeywords.Contains(part) Then
                newText.Append(part) ' ✅ عدم تشفير الكلمات الديناميكية
            Else
                newText.Append("<span style='font-family: Arial, sans-serif;' data-encrypted='" & EncodeBase64(part) & "'>" & part & "</span>")
            End If
        Next
        Return m.Groups(1).Value & newText.ToString() & m.Groups(3).Value
    End Function
    ' 🔄 دالة التشفير باستخدام Base64
    Private Function EncodeBase64(text As String) As String
        Dim bytes As Byte() = Encoding.UTF8.GetBytes(text)
        Return Convert.ToBase64String(bytes)
    End Function
    Private Sub frmBase64_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' جعل النموذج غير مرئي أثناء تطبيق التصميم
        Me.Opacity = 0

        ' تعطيل زر التشفير في البداية
        LetterEncoder_bntEncrypt.Enabled = False

        ' تعيين عنوان النموذج
        Me.Text = "Base64 Encoded Letter"

        ' تطبيق تصميم Binance مباشرة
        Try
            ApplyBinanceStyle()

            ' إضافة معالج حدث لتغيير حجم النموذج
            AddHandler Me.Resize, AddressOf frmBase64_Resize

            ' وضع العناصر في المنتصف عند تحميل النموذج
            CenterControls()

            ' إضافة عنوان في أعلى النموذج
            AddHeaderLabel()

            ' جعل النموذج مرئي بعد تطبيق التصميم
            Me.Opacity = 1
        Catch ex As Exception
            ' في حالة حدوث خطأ، جعل النموذج مرئي على أي حال
            Me.Opacity = 1
            Debug.WriteLine($"Error in frmBase64_Load: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' إضافة صورة العنوان في أعلى النموذج
    ''' </summary>
    Private Sub AddHeaderLabel()
        Try
            ' إنشاء PictureBox للصورة
            Dim headerPictureBox As New PictureBox()
            headerPictureBox.Name = "HeaderPictureBox"
            headerPictureBox.BackColor = Color.Transparent
            headerPictureBox.SizeMode = PictureBoxSizeMode.Zoom ' جعل الصورة قابلة للتكبير

            ' تحميل الصورة من الموارد
            Try
                headerPictureBox.Image = My.Resources.Base64Image
                Debug.WriteLine("Image loaded from resources successfully")
            Catch ex As Exception
                Debug.WriteLine($"Error loading image from resources: {ex.Message}")
                ' إذا لم يتم العثور على الصورة، نستخدم صورة افتراضية
                headerPictureBox.BackColor = Color.FromArgb(13, 13, 13)
                headerPictureBox.Image = Nothing
            End Try

            ' تعيين حجم وموقع الصورة
            Dim imageHeight As Integer = 50
            headerPictureBox.Size = New Size(200, imageHeight)
            headerPictureBox.Location = New Point((Me.ClientSize.Width - headerPictureBox.Width) \ 2, 0)
            headerPictureBox.Dock = DockStyle.Top ' جعل الصورة في أعلى النموذج

            ' إضافة PictureBox إلى النموذج
            Me.Controls.Add(headerPictureBox)
            headerPictureBox.BringToFront()

            ' تعديل موقع GroupControl1 و GroupControl2 لإفساح المجال للصورة
            GroupControl1.Location = New Point(10, headerPictureBox.Bottom + 5)
            GroupControl2.Location = New Point(GroupControl1.Right + 20, headerPictureBox.Bottom + 5)

            ' تعديل حجم GroupControl1 و GroupControl2
            Dim panelHeight As Integer = Me.ClientSize.Height - headerPictureBox.Height - 15
            GroupControl1.Height = panelHeight
            GroupControl2.Height = panelHeight

            ' إضافة معالج حدث لتغيير حجم النموذج
            AddHandler Me.Resize, Sub(s, e)
                ' تعديل موقع GroupControl1 و GroupControl2
                GroupControl1.Location = New Point(10, headerPictureBox.Bottom + 5)
                GroupControl2.Location = New Point(GroupControl1.Right + 20, headerPictureBox.Bottom + 5)

                ' تعديل حجم GroupControl1 و GroupControl2
                Dim newPanelHeight As Integer = Me.ClientSize.Height - headerPictureBox.Height - 15
                GroupControl1.Height = newPanelHeight
                GroupControl2.Height = newPanelHeight
            End Sub

            Debug.WriteLine("Header image added successfully")
        Catch ex As Exception
            Debug.WriteLine($"Error adding header image: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmBase64_Resize(sender As Object, e As EventArgs)
        ' وضع العناصر في المنتصف عند تغيير حجم النموذج
        CenterControls()
    End Sub

    ''' <summary>
    ''' تنسيق أحجام العناصر وموقعها
    ''' </summary>
    Private Sub CenterControls()
        Try
            ' الحصول على حجم النموذج
            Dim formWidth As Integer = Me.ClientSize.Width
            Dim formHeight As Integer = Me.ClientSize.Height

            ' تنسيق أحجام GroupControl1 و GroupControl2
            Dim panelWidth As Integer = (formWidth - 40) / 2
            Dim panelHeight As Integer = formHeight - 40

            ' تنسيق GroupControl1
            GroupControl1.Size = New Size(panelWidth, panelHeight)
            GroupControl1.Location = New Point(10, 10)

            ' تنسيق GroupControl2
            GroupControl2.Size = New Size(panelWidth, panelHeight)
            GroupControl2.Location = New Point(GroupControl1.Right + 20, 10)

            ' تنسيق LayoutControl2
            LayoutControl2.Dock = DockStyle.Fill

            ' تنسيق أحجام الأزرار
            LetterEncoder_bntBrowse.Size = New Size(100, 30)
            LetterEncoder_bntEncrypt.Size = New Size(100, 30)
            LetterEncoder_bntClearAll.Size = New Size(100, 30)
            LetterEncoder_bntSave.Size = New Size(100, 30)

            ' تنسيق LetterEncoder_txtFilepath
            LetterEncoder_txtFilepath.Size = New Size(300, LetterEncoder_txtFilepath.Height)

            Debug.WriteLine($"Controls resized. Form size: {formWidth}x{formHeight}")
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error resizing controls: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على النموذج
    ''' </summary>
    Private Sub ApplyBinanceStyle()
        Try
            ' تعيين لون خلفية النموذج - لون Binance الأساسي الداكن
            Me.BackColor = Color.FromArgb(13, 13, 13) ' لون أكثر قتامة مثل Binance

            ' تنسيق LayoutControl2
            LayoutControl2.BackColor = Color.FromArgb(13, 13, 13)
            LayoutControl2.ForeColor = Color.White

            ' تنسيق GroupControl1
            GroupControl1.Appearance.BackColor = Color.FromArgb(22, 26, 30) ' لون Binance للبطاقات
            GroupControl1.Appearance.BorderColor = Color.FromArgb(30, 35, 40) ' حدود رمادية داكنة
            GroupControl1.Appearance.ForeColor = Color.White
            GroupControl1.Appearance.Options.UseBackColor = True
            GroupControl1.Appearance.Options.UseBorderColor = True
            GroupControl1.Appearance.Options.UseForeColor = True

            GroupControl1.AppearanceCaption.BackColor = Color.FromArgb(22, 26, 30)
            GroupControl1.AppearanceCaption.BorderColor = Color.FromArgb(30, 35, 40)
            GroupControl1.AppearanceCaption.ForeColor = Color.White
            GroupControl1.AppearanceCaption.Options.UseBackColor = True
            GroupControl1.AppearanceCaption.Options.UseBorderColor = True
            GroupControl1.AppearanceCaption.Options.UseForeColor = True
            GroupControl1.Text = "Normal"

            ' تنسيق GroupControl2
            GroupControl2.Appearance.BackColor = Color.FromArgb(22, 26, 30) ' لون Binance للبطاقات
            GroupControl2.Appearance.BorderColor = Color.FromArgb(30, 35, 40) ' حدود رمادية داكنة
            GroupControl2.Appearance.ForeColor = Color.White
            GroupControl2.Appearance.Options.UseBackColor = True
            GroupControl2.Appearance.Options.UseBorderColor = True
            GroupControl2.Appearance.Options.UseForeColor = True

            GroupControl2.AppearanceCaption.BackColor = Color.FromArgb(22, 26, 30)
            GroupControl2.AppearanceCaption.BorderColor = Color.FromArgb(30, 35, 40)
            GroupControl2.AppearanceCaption.ForeColor = Color.White
            GroupControl2.AppearanceCaption.Options.UseBackColor = True
            GroupControl2.AppearanceCaption.Options.UseBorderColor = True
            GroupControl2.AppearanceCaption.Options.UseForeColor = True
            GroupControl2.Text = "Encoded"

            ' تنسيق LetterEncoder_Richtext - مثل حقول الإدخال في Binance
            LetterEncoder_Richtext.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_Richtext.ForeColor = Color.White
            LetterEncoder_Richtext.BorderStyle = BorderStyle.None
            LetterEncoder_Richtext.Font = New Font("Segoe UI", 9.5F)

            ' تنسيق LetterEncoder_Richtext2 - مع لون النص الأصفر المميز لـ Binance
            LetterEncoder_Richtext2.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_Richtext2.ForeColor = Color.FromArgb(240, 185, 11) ' اللون الأصفر الذهبي لـ Binance
            LetterEncoder_Richtext2.BorderStyle = BorderStyle.None
            LetterEncoder_Richtext2.Font = New Font("Segoe UI", 9.5F)

            ' تنسيق LetterEncoder_txtFilepath - مثل حقول البحث في Binance
            LetterEncoder_txtFilepath.Properties.Appearance.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_txtFilepath.Properties.Appearance.ForeColor = Color.White
            LetterEncoder_txtFilepath.Properties.Appearance.Options.UseBackColor = True
            LetterEncoder_txtFilepath.Properties.Appearance.Options.UseForeColor = True
            LetterEncoder_txtFilepath.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
            LetterEncoder_txtFilepath.Properties.Appearance.Font = New Font("Segoe UI", 9)




            ' تنسيق أحجام العناصر
            CenterControls()

            Debug.WriteLine("Binance style applied successfully")
        Catch ex As Exception
            Debug.WriteLine($"Error applying Binance style: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub



    ''' <summary>
    ''' تنسيق الزر
    ''' </summary>
    Private Sub FormatButton(button As DevExpress.XtraEditors.SimpleButton, tooltipText As String)
        Try
            ' تعيين نص التلميح
            button.ToolTip = tooltipText

            ' تعيين خصائص الزر
            button.Appearance.BackColor = Color.FromArgb(40, 40, 40)
            button.Appearance.ForeColor = Color.White
            button.Cursor = Cursors.Hand
            button.Appearance.Options.UseBackColor = True
            button.Appearance.Options.UseForeColor = True
            button.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder

            ' تعيين خصائص الزر عند المرور فوقه
            button.Appearance.Options.UseFont = True
            button.Appearance.Font = New Font("Segoe UI", 9, FontStyle.Regular)

            ' تعيين خصائص الزر عند الضغط عليه
            button.PaintStyle = DevExpress.XtraEditors.Controls.PaintStyles.Light

            ' تعيين الحواف المنحنية
            button.LookAndFeel.UseDefaultLookAndFeel = False
            button.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat

            ' تعيين خصائص الزر عند المرور فوقه
            button.Appearance.Options.UseBackColor = True
            button.Appearance.Options.UseForeColor = True
            button.Appearance.BackColor2 = Color.FromArgb(50, 50, 50)
            button.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical

            ' تعيين الحواف المستديرة
            button.AppearancePressed.BorderColor = Color.FromArgb(40, 40, 40)
            button.AppearanceHovered.BorderColor = Color.FromArgb(60, 60, 60)
        Catch ex As Exception
            Debug.WriteLine($"Error formatting button: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تنسيق الزر بأسلوب Binance (حواف فقط)
    ''' </summary>
    Private Sub FormatBinanceButton(button As DevExpress.XtraEditors.SimpleButton, tooltipText As String)
        Try
            ' تعيين نص التلميح
            button.ToolTip = tooltipText

            ' تعيين خصائص الزر - أسلوب Binance
            button.Appearance.BackColor = Color.FromArgb(40, 40, 40) ' خلفية داكنة
            button.Appearance.ForeColor = Color.White ' نص أبيض
            button.Cursor = Cursors.Hand
            button.Appearance.Options.UseBackColor = True
            button.Appearance.Options.UseForeColor = True

            ' تعيين حواف الزر باللون الأصفر
            button.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
            button.Appearance.BorderColor = Color.FromArgb(96, 96, 96) ' لون الحواف العادي
            button.Appearance.Options.UseBorderColor = True

            ' تعيين خصائص الزر عند المرور فوقه
            button.Appearance.Options.UseFont = True
            button.Appearance.Font = New Font("Segoe UI", 9, FontStyle.Regular)

            ' تعيين خصائص الزر عند الضغط عليه
            button.PaintStyle = DevExpress.XtraEditors.Controls.PaintStyles.Light

            ' تعيين الحواف المنحنية
            button.LookAndFeel.UseDefaultLookAndFeel = False
            button.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat

            ' تعيين خصائص الزر عند المرور فوقه - تغيير لون الحواف فقط
            button.AppearanceHovered.BorderColor = Color.FromArgb(254, 219, 65) ' لون الحواف الأصفر عند المرور
            button.AppearanceHovered.Options.UseBorderColor = True

            ' تعيين خصائص الزر عند الضغط عليه
            button.AppearancePressed.BorderColor = Color.FromArgb(254, 219, 65) ' لون الحواف الأصفر عند الضغط
            button.AppearancePressed.Options.UseBorderColor = True
        Catch ex As Exception
            Debug.WriteLine($"Error formatting button: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تعيين أيقونة للزر
    ''' </summary>
    Private Sub SetButtonIcon(button As DevExpress.XtraEditors.SimpleButton, iconType As String)
        Try
            ' تعيين الأيقونة حسب النوع
            Select Case iconType.ToLower()
                Case "folder"
                    button.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter
                    button.ImageOptions.ImageToTextIndent = 5
                    button.Text = "Browse..."
                Case "encrypt"
                    button.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter
                    button.ImageOptions.ImageToTextIndent = 5
                    button.Text = "Encrypt"
                Case "reset"
                    button.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter
                    button.ImageOptions.ImageToTextIndent = 5
                    button.Text = "Reset All"
                Case "save"
                    button.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter
                    button.ImageOptions.ImageToTextIndent = 5
                    button.Text = "Save"
            End Select
        Catch ex As Exception
            Debug.WriteLine($"Error setting button icon: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub
    Private Sub LetterEncoder_bntBrowse_Click(sender As Object, e As EventArgs) Handles LetterEncoder_bntBrowse.Click
        LetterEncoder_txtFilepath.ResetText()
        LetterEncoder_Richtext.ResetText()
        LetterEncoder_Richtext2.ResetText()
        LetterEncoder_bntEncrypt.Enabled = True
        Dim openFileDialog As New OpenFileDialog With {
            .Filter = "HTML Files|*.html;*.htm",
            .Title = "Select an HTML File"
        }
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            LetterEncoder_txtFilepath.Text = openFileDialog.FileName
            Using reader As New StreamReader(openFileDialog.FileName, Encoding.Default, True)
                originalHtml = reader.ReadToEnd()
            End Using
            LetterEncoder_Richtext.Text = originalHtml
            LetterEncoder_bntSave.Enabled = False
        End If
    End Sub
    Private Sub LetterEncoder_bntClearAll_Click(sender As Object, e As EventArgs) Handles LetterEncoder_bntClearAll.Click
        LetterEncoder_txtFilepath.ResetText()
        LetterEncoder_Richtext.ResetText()
        LetterEncoder_Richtext2.ResetText()
        LetterEncoder_bntEncrypt.Enabled = True
    End Sub
    Private Sub LetterEncoder_bntSave_Click_1(sender As Object, e As EventArgs) Handles LetterEncoder_bntSave.Click
        If LetterEncoder_Richtext2.Text.Trim = "" Then
            XtraMessageBox.Show("Please Select Your Letter First!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        ' 🔹 قائمة الكلمات غير المرغوب فيها والتي سيتم حذفها
        Dim wordsToRemove As String() = {"<script>", "</script>", "<iframe>", "</iframe>", "<object>", "</object>"}
        ' 🔹 تنظيف النص
        Dim cleanedText As String = LetterEncoder_Richtext2.Text
        For Each word In wordsToRemove
            cleanedText = cleanedText.Replace(word, "")
        Next
        cleanedText = Regex.Replace(cleanedText, "&nbsp;", "")
        ' 🔹 تحديد المسار: سطح المكتب + مجلد "Base64 Letter Encoded"
        Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        Dim saveFolder As String = Path.Combine(desktopPath, "Base64 Letter Encoded")
        If Not Directory.Exists(saveFolder) Then Directory.CreateDirectory(saveFolder)
        ' 🔹 اسم الملف الأساسي
        Dim baseFileName As String = "Base64 Letter Encoded"
        Dim filePath As String = Path.Combine(saveFolder, baseFileName & ".html")
        Dim counter As Integer = 1
        ' 🔹 التأكد من عدم وجود ملف بنفس الاسم، وإنشاء اسم جديد إذا لزم الأمر
        While File.Exists(filePath)
            filePath = Path.Combine(saveFolder, $"{baseFileName}{counter}.html")
            counter += 1
        End While
        ' 🔹 كتابة الملف
        File.WriteAllText(filePath, cleanedText, Encoding.UTF8)
        ' 🔹 عرض رسالة تأكيد
        XtraMessageBox.Show("The file has been successfully saved to:" & Environment.NewLine & filePath, "Saved Successfully", MessageBoxButtons.OK, MessageBoxIcon.Information)
        ' 🔹 إعادة ضبط الحقول
        LetterEncoder_txtFilepath.ResetText()
        LetterEncoder_Richtext.ResetText()
        LetterEncoder_Richtext2.ResetText()
        LetterEncoder_bntEncrypt.Enabled = True
        ' 🔹 فتح المجلد تلقائيًا
        Process.Start("explorer.exe", saveFolder)
    End Sub
    Private Sub LetterEncoder_bntEncrypt_Click(sender As Object, e As EventArgs) Handles LetterEncoder_bntEncrypt.Click
        Dim originalHtml As String = LetterEncoder_Richtext.Text
        If String.IsNullOrWhiteSpace(originalHtml) Then
            XtraMessageBox.Show("Please enter HTML code in RichTextBox1!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        ' 🔹 البحث عن النصوص داخل الوسوم فقط
        Dim pattern As String = "(>)([^<>]+?)(<)"
        Dim encodedHtml As String = Regex.Replace(originalHtml, pattern, AddressOf EncryptHtmlText, RegexOptions.Singleline)
        ' 🔹 عرض النتيجة بعد التشفير
        LetterEncoder_Richtext2.Text = encodedHtml
        XtraMessageBox.Show("Encryption Done!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        LetterEncoder_bntEncrypt.Enabled = False
        LetterEncoder_bntSave.Enabled = True
    End Sub
    Private Sub TimerWatcher_Tick(sender As Object, e As EventArgs) Handles TimerWatcher.Tick
        ' تحقق من محتوى الـ RichTextBox
        If String.IsNullOrWhiteSpace(LetterEncoder_Richtext.Text) AndAlso
           String.IsNullOrWhiteSpace(LetterEncoder_Richtext2.Text) Then
            LetterEncoder_bntClearAll.Enabled = False
        Else
            LetterEncoder_bntClearAll.Enabled = True
        End If
    End Sub
End Class