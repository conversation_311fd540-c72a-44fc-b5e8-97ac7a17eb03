## Html Agility Pack

This is an agile HTML parser that builds a read/write DOM and supports plain XPATH or XSLT (you actually don't HAVE to understand XPATH nor XSLT to use it, don't worry...). It is a .NET code library that allows you to parse "out of the web" HTML files. The parser is very tolerant with "real world" malformed HTML. The object model is very similar to what proposes System.Xml, but for HTML documents (or streams).

---

This library is sponsored by ZZZ Projects:

- https://entityframework-extensions.net/
- https://eval-expression.net/
- https://dapper-plus.net/

[![Entity Framework Extensions](https://raw.githubusercontent.com/zzzprojects/docs/refs/heads/master/images/entityframework-extensions-ads.jpg)](https://entityframework-extensions.net/)

---

HAP is trusted by companies worldwide with over 150 million downloads.