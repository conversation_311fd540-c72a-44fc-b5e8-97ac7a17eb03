<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
	<Target Name="IronPdfModelBuild" BeforeTargets="BeforeBuild">
        <Message Text="Copying PdfModel files from $(MSBuildThisFileDirectory)..\..\runtimes to build directory for target framework $(TargetFramework) ..." Importance="high" />
        <ItemGroup >
            <RuntimesNativeLibsBuild Include="$(MSBuildThisFileDirectory)..\..\runtimes\**\*.*" />
        </ItemGroup>
        <Copy
                SourceFiles="@(RuntimesNativeLibsBuild)"
                DestinationFolder="$(OutputPath)runtimes\%(RecursiveDir)"
                SkipUnchangedFiles="true"
        />	
	</Target>
</Project>