﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmRedirectPuzzle
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmRedirectPuzzle))
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.DxErrorProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(Me.components)
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.SeparatorControl2 = New DevExpress.XtraEditors.SeparatorControl()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.cb_txt_Time = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.btn_Save = New DevExpress.XtraEditors.SimpleButton()
        Me.BntCLear = New DevExpress.XtraEditors.SimpleButton()
        Me.BntBuild = New DevExpress.XtraEditors.SimpleButton()
        Me.txt_type_Puzzle = New DevExpress.XtraEditors.TextEdit()
        Me.txt_Link_Puzzle = New DevExpress.XtraEditors.TextEdit()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_type_Puzzle.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_Link_Puzzle.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'RichTextBox1
        '
        Me.RichTextBox1.Location = New System.Drawing.Point(1108, 368)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(158, 73)
        Me.RichTextBox1.TabIndex = 495
        Me.RichTextBox1.Text = resources.GetString("RichTextBox1.Text")
        Me.RichTextBox1.Visible = False
        '
        'DxErrorProvider1
        '
        Me.DxErrorProvider1.ContainerControl = Me
        '
        'SeparatorControl2
        '
        Me.SeparatorControl2.LineThickness = 1
        Me.SeparatorControl2.Location = New System.Drawing.Point(293, 183)
        Me.SeparatorControl2.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl2.Name = "SeparatorControl2"
        Me.SeparatorControl2.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl2.Size = New System.Drawing.Size(363, 26)
        Me.SeparatorControl2.TabIndex = 528
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.PuzzelPaner
        Me.PictureBox1.Location = New System.Drawing.Point(276, 129)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(393, 75)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 529
        Me.PictureBox1.TabStop = False
        '
        'cb_txt_Time
        '
        Me.cb_txt_Time.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cb_txt_Time.EditValue = "3000"
        Me.cb_txt_Time.Location = New System.Drawing.Point(623, 250)
        Me.cb_txt_Time.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cb_txt_Time.Name = "cb_txt_Time"
        Me.cb_txt_Time.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cb_txt_Time.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cb_txt_Time.Properties.Appearance.Options.UseBackColor = True
        Me.cb_txt_Time.Properties.Appearance.Options.UseFont = True
        Me.cb_txt_Time.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cb_txt_Time.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.time16x16
        Me.cb_txt_Time.Properties.Items.AddRange(New Object() {"1000", "2000", "3000", "4000", "5000", "6000", "7000", "8000", "9000"})
        Me.cb_txt_Time.Size = New System.Drawing.Size(173, 38)
        Me.cb_txt_Time.TabIndex = 527
        '
        'btn_Save
        '
        Me.btn_Save.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Options.UseBackColor = True
        Me.btn_Save.Appearance.Options.UseBorderColor = True
        Me.btn_Save.Appearance.Options.UseFont = True
        Me.btn_Save.Appearance.Options.UseForeColor = True
        Me.btn_Save.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn_Save.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn_Save.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn_Save.AppearanceDisabled.Options.UseBackColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseFont = True
        Me.btn_Save.AppearanceDisabled.Options.UseForeColor = True
        Me.btn_Save.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn_Save.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn_Save.AppearanceHovered.Options.UseBackColor = True
        Me.btn_Save.AppearanceHovered.Options.UseBorderColor = True
        Me.btn_Save.AppearanceHovered.Options.UseForeColor = True
        Me.btn_Save.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn_Save.AppearancePressed.Options.UseBackColor = True
        Me.btn_Save.AppearancePressed.Options.UseBorderColor = True
        Me.btn_Save.AppearancePressed.Options.UseForeColor = True
        Me.btn_Save.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.settings32x32
        Me.btn_Save.Location = New System.Drawing.Point(404, 435)
        Me.btn_Save.Margin = New System.Windows.Forms.Padding(4)
        Me.btn_Save.Name = "btn_Save"
        Me.btn_Save.Size = New System.Drawing.Size(152, 38)
        Me.btn_Save.TabIndex = 526
        Me.btn_Save.Text = "Save Settings"
        '
        'BntCLear
        '
        Me.BntCLear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Options.UseBackColor = True
        Me.BntCLear.Appearance.Options.UseBorderColor = True
        Me.BntCLear.Appearance.Options.UseFont = True
        Me.BntCLear.Appearance.Options.UseForeColor = True
        Me.BntCLear.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntCLear.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntCLear.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntCLear.AppearanceDisabled.Options.UseBackColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseFont = True
        Me.BntCLear.AppearanceDisabled.Options.UseForeColor = True
        Me.BntCLear.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntCLear.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntCLear.AppearanceHovered.Options.UseBackColor = True
        Me.BntCLear.AppearanceHovered.Options.UseBorderColor = True
        Me.BntCLear.AppearanceHovered.Options.UseForeColor = True
        Me.BntCLear.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntCLear.AppearancePressed.Options.UseBackColor = True
        Me.BntCLear.AppearancePressed.Options.UseBorderColor = True
        Me.BntCLear.AppearancePressed.Options.UseForeColor = True
        Me.BntCLear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntCLear.Location = New System.Drawing.Point(564, 435)
        Me.BntCLear.Margin = New System.Windows.Forms.Padding(4)
        Me.BntCLear.Name = "BntCLear"
        Me.BntCLear.Size = New System.Drawing.Size(152, 38)
        Me.BntCLear.TabIndex = 525
        Me.BntCLear.Text = "Reset All"
        '
        'BntBuild
        '
        Me.BntBuild.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntBuild.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntBuild.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntBuild.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntBuild.Appearance.Options.UseBackColor = True
        Me.BntBuild.Appearance.Options.UseBorderColor = True
        Me.BntBuild.Appearance.Options.UseFont = True
        Me.BntBuild.Appearance.Options.UseForeColor = True
        Me.BntBuild.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntBuild.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntBuild.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntBuild.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntBuild.AppearanceDisabled.Options.UseBackColor = True
        Me.BntBuild.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntBuild.AppearanceDisabled.Options.UseFont = True
        Me.BntBuild.AppearanceDisabled.Options.UseForeColor = True
        Me.BntBuild.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntBuild.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntBuild.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntBuild.AppearanceHovered.Options.UseBackColor = True
        Me.BntBuild.AppearanceHovered.Options.UseBorderColor = True
        Me.BntBuild.AppearanceHovered.Options.UseForeColor = True
        Me.BntBuild.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntBuild.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntBuild.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntBuild.AppearancePressed.Options.UseBackColor = True
        Me.BntBuild.AppearancePressed.Options.UseBorderColor = True
        Me.BntBuild.AppearancePressed.Options.UseForeColor = True
        Me.BntBuild.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.BntBuild.Location = New System.Drawing.Point(244, 435)
        Me.BntBuild.Margin = New System.Windows.Forms.Padding(4)
        Me.BntBuild.Name = "BntBuild"
        Me.BntBuild.Size = New System.Drawing.Size(152, 38)
        Me.BntBuild.TabIndex = 524
        Me.BntBuild.Text = "Start / Build"
        '
        'txt_type_Puzzle
        '
        Me.txt_type_Puzzle.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_type_Puzzle.EditValue = "Please solve the puzzle to prove you're not a robot"
        Me.txt_type_Puzzle.Location = New System.Drawing.Point(162, 367)
        Me.txt_type_Puzzle.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_type_Puzzle.Name = "txt_type_Puzzle"
        Me.txt_type_Puzzle.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_type_Puzzle.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_type_Puzzle.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_type_Puzzle.Properties.Appearance.Options.UseBackColor = True
        Me.txt_type_Puzzle.Properties.Appearance.Options.UseFont = True
        Me.txt_type_Puzzle.Properties.Appearance.Options.UseForeColor = True
        Me.txt_type_Puzzle.Properties.NullValuePrompt = "Please solve the puzzle to prove you're not a robot"
        Me.txt_type_Puzzle.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_type_Puzzle.Size = New System.Drawing.Size(649, 30)
        Me.txt_type_Puzzle.TabIndex = 523
        '
        'txt_Link_Puzzle
        '
        Me.txt_Link_Puzzle.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Link_Puzzle.EditValue = "https://www.Example.com/?="
        Me.txt_Link_Puzzle.Location = New System.Drawing.Point(162, 306)
        Me.txt_Link_Puzzle.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Link_Puzzle.Name = "txt_Link_Puzzle"
        Me.txt_Link_Puzzle.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Link_Puzzle.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Link_Puzzle.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Link_Puzzle.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Link_Puzzle.Properties.Appearance.Options.UseFont = True
        Me.txt_Link_Puzzle.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Link_Puzzle.Properties.NullValuePrompt = "https://www.Example.com/?="
        Me.txt_Link_Puzzle.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Link_Puzzle.Size = New System.Drawing.Size(649, 30)
        Me.txt_Link_Puzzle.TabIndex = 522
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label3.Location = New System.Drawing.Point(162, 341)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(113, 21)
        Me.Label3.TabIndex = 519
        Me.Label3.Text = "Enter Your Text"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label6.Location = New System.Drawing.Point(507, 257)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(109, 21)
        Me.Label6.TabIndex = 520
        Me.Label6.Text = "Time Redirect :"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(162, 280)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(110, 21)
        Me.Label2.TabIndex = 521
        Me.Label2.Text = "Enter Your link"
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton1.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton1.Appearance.Options.UseBackColor = True
        Me.SimpleButton1.Appearance.Options.UseBorderColor = True
        Me.SimpleButton1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.SimpleButton1.Location = New System.Drawing.Point(785, 309)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton1.TabIndex = 530
        '
        'SimpleButton2
        '
        Me.SimpleButton2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton2.Appearance.Options.UseBackColor = True
        Me.SimpleButton2.Appearance.Options.UseBorderColor = True
        Me.SimpleButton2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.SimpleButton2.Location = New System.Drawing.Point(784, 371)
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton2.TabIndex = 530
        '
        'frmRedirectPuzzle
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.SimpleButton2)
        Me.Controls.Add(Me.SimpleButton1)
        Me.Controls.Add(Me.SeparatorControl2)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.cb_txt_Time)
        Me.Controls.Add(Me.btn_Save)
        Me.Controls.Add(Me.BntCLear)
        Me.Controls.Add(Me.BntBuild)
        Me.Controls.Add(Me.txt_type_Puzzle)
        Me.Controls.Add(Me.txt_Link_Puzzle)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label6)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.RichTextBox1)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Logo_Puzzle
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmRedirectPuzzle"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = " Redirect With Puzzle"
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_type_Puzzle.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_Link_Puzzle.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Timer1 As Timer
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents DxErrorProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SeparatorControl2 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents cb_txt_Time As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents btn_Save As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntCLear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntBuild As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txt_type_Puzzle As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txt_Link_Puzzle As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label3 As Label
    Friend WithEvents Label6 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton
End Class
