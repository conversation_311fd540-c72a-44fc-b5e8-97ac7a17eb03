﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmAddSmtpServer
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.DxErrorProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(Me.components)
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.TxtFromName = New DevExpress.XtraEditors.TextEdit()
        Me.TxtTag_1 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TxtTag_2 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TxtPort = New DevExpress.XtraEditors.TextEdit()
        Me.TxtSubject = New DevExpress.XtraEditors.TextEdit()
        Me.TxtFromMail = New DevExpress.XtraEditors.TextEdit()
        Me.Bnt_Reset = New DevExpress.XtraEditors.SimpleButton()
        Me.CheckEdit1 = New DevExpress.XtraEditors.CheckEdit()
        Me.TxtEmail = New DevExpress.XtraEditors.TextEdit()
        Me.TxtPassword = New DevExpress.XtraEditors.TextEdit()
        Me.BntTest = New DevExpress.XtraEditors.SimpleButton()
        Me.TxtSMTPServer = New DevExpress.XtraEditors.TextEdit()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.BntAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.CheckSSL = New DevExpress.XtraEditors.CheckEdit()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.PnlWait = New DevExpress.XtraEditors.GroupControl()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.ProgressPanel2 = New DevExpress.XtraWaitForm.ProgressPanel()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtFromName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtTag_1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtTag_2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtPort.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtSubject.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtFromMail.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtEmail.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtPassword.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtSMTPServer.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckSSL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.PnlWait, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PnlWait.SuspendLayout()
        Me.SuspendLayout()
        '
        'DxErrorProvider1
        '
        Me.DxErrorProvider1.ContainerControl = Me
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'TxtFromName
        '
        Me.TxtFromName.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtFromName.EditValue = ""
        Me.TxtFromName.Location = New System.Drawing.Point(165, 432)
        Me.TxtFromName.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtFromName.Name = "TxtFromName"
        Me.TxtFromName.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtFromName.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtFromName.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtFromName.Properties.Appearance.Options.UseBackColor = True
        Me.TxtFromName.Properties.Appearance.Options.UseFont = True
        Me.TxtFromName.Properties.Appearance.Options.UseForeColor = True
        Me.TxtFromName.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtFromName.Size = New System.Drawing.Size(355, 30)
        Me.TxtFromName.TabIndex = 399
        '
        'TxtTag_1
        '
        Me.TxtTag_1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.TxtTag_1.EditValue = ""
        Me.TxtTag_1.Location = New System.Drawing.Point(165, 377)
        Me.TxtTag_1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtTag_1.Name = "TxtTag_1"
        Me.TxtTag_1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TxtTag_1.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.TxtTag_1.Properties.Appearance.Options.UseBackColor = True
        Me.TxtTag_1.Properties.Appearance.Options.UseFont = True
        Me.TxtTag_1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TxtTag_1.Properties.Items.AddRange(New Object() {"Random Browser", "Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Country", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show Random Fake Email Address Office", "Show Random Fake Phone Numbers", "Show Random Street New York", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.TxtTag_1.Size = New System.Drawing.Size(354, 38)
        Me.TxtTag_1.TabIndex = 515
        '
        'TxtTag_2
        '
        Me.TxtTag_2.Cursor = System.Windows.Forms.Cursors.Hand
        Me.TxtTag_2.EditValue = ""
        Me.TxtTag_2.Location = New System.Drawing.Point(165, 275)
        Me.TxtTag_2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtTag_2.Name = "TxtTag_2"
        Me.TxtTag_2.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TxtTag_2.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.TxtTag_2.Properties.Appearance.Options.UseBackColor = True
        Me.TxtTag_2.Properties.Appearance.Options.UseFont = True
        Me.TxtTag_2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TxtTag_2.Properties.Items.AddRange(New Object() {"Random Browser", "Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Country", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show Random Fake Email Address Office", "Show Random Fake Phone Numbers", "Show Random Street New York", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.TxtTag_2.Size = New System.Drawing.Size(355, 38)
        Me.TxtTag_2.TabIndex = 515
        '
        'TxtPort
        '
        Me.TxtPort.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtPort.EditValue = "587"
        Me.TxtPort.Location = New System.Drawing.Point(165, 87)
        Me.TxtPort.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtPort.Name = "TxtPort"
        Me.TxtPort.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtPort.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtPort.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtPort.Properties.Appearance.Options.UseBackColor = True
        Me.TxtPort.Properties.Appearance.Options.UseFont = True
        Me.TxtPort.Properties.Appearance.Options.UseForeColor = True
        Me.TxtPort.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtPort.Size = New System.Drawing.Size(152, 30)
        Me.TxtPort.TabIndex = 399
        '
        'TxtSubject
        '
        Me.TxtSubject.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtSubject.EditValue = ""
        Me.TxtSubject.Location = New System.Drawing.Point(165, 330)
        Me.TxtSubject.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtSubject.Name = "TxtSubject"
        Me.TxtSubject.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtSubject.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtSubject.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtSubject.Properties.Appearance.Options.UseBackColor = True
        Me.TxtSubject.Properties.Appearance.Options.UseFont = True
        Me.TxtSubject.Properties.Appearance.Options.UseForeColor = True
        Me.TxtSubject.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtSubject.Size = New System.Drawing.Size(354, 30)
        Me.TxtSubject.TabIndex = 399
        '
        'TxtFromMail
        '
        Me.TxtFromMail.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtFromMail.EditValue = ""
        Me.TxtFromMail.Location = New System.Drawing.Point(165, 228)
        Me.TxtFromMail.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtFromMail.Name = "TxtFromMail"
        Me.TxtFromMail.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtFromMail.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtFromMail.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtFromMail.Properties.Appearance.Options.UseBackColor = True
        Me.TxtFromMail.Properties.Appearance.Options.UseFont = True
        Me.TxtFromMail.Properties.Appearance.Options.UseForeColor = True
        Me.TxtFromMail.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtFromMail.Size = New System.Drawing.Size(355, 30)
        Me.TxtFromMail.TabIndex = 399
        '
        'Bnt_Reset
        '
        Me.Bnt_Reset.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.Bnt_Reset.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Reset.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_Reset.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Reset.Appearance.Options.UseBackColor = True
        Me.Bnt_Reset.Appearance.Options.UseBorderColor = True
        Me.Bnt_Reset.Appearance.Options.UseFont = True
        Me.Bnt_Reset.Appearance.Options.UseForeColor = True
        Me.Bnt_Reset.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.Bnt_Reset.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.Bnt_Reset.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_Reset.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Bnt_Reset.AppearanceDisabled.Options.UseBackColor = True
        Me.Bnt_Reset.AppearanceDisabled.Options.UseBorderColor = True
        Me.Bnt_Reset.AppearanceDisabled.Options.UseFont = True
        Me.Bnt_Reset.AppearanceDisabled.Options.UseForeColor = True
        Me.Bnt_Reset.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.Bnt_Reset.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.Bnt_Reset.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.Bnt_Reset.AppearanceHovered.Options.UseBackColor = True
        Me.Bnt_Reset.AppearanceHovered.Options.UseBorderColor = True
        Me.Bnt_Reset.AppearanceHovered.Options.UseForeColor = True
        Me.Bnt_Reset.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Reset.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.Bnt_Reset.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.Bnt_Reset.AppearancePressed.Options.UseBackColor = True
        Me.Bnt_Reset.AppearancePressed.Options.UseBorderColor = True
        Me.Bnt_Reset.AppearancePressed.Options.UseForeColor = True
        Me.Bnt_Reset.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.Bnt_Reset.Location = New System.Drawing.Point(125, 498)
        Me.Bnt_Reset.Name = "Bnt_Reset"
        Me.Bnt_Reset.Size = New System.Drawing.Size(121, 39)
        Me.Bnt_Reset.TabIndex = 376
        Me.Bnt_Reset.Text = "Reset"
        '
        'CheckEdit1
        '
        Me.CheckEdit1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.CheckEdit1.Location = New System.Drawing.Point(500, 185)
        Me.CheckEdit1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.CheckEdit1.Name = "CheckEdit1"
        Me.CheckEdit1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.CheckEdit1.Properties.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.CheckEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.CheckEdit1.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEdit1.Properties.Caption = "View password"
        Me.CheckEdit1.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style15
        Me.CheckEdit1.Size = New System.Drawing.Size(19, 22)
        Me.CheckEdit1.TabIndex = 10
        '
        'TxtEmail
        '
        Me.TxtEmail.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtEmail.EditValue = ""
        Me.TxtEmail.Location = New System.Drawing.Point(165, 134)
        Me.TxtEmail.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtEmail.Name = "TxtEmail"
        Me.TxtEmail.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtEmail.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtEmail.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtEmail.Properties.Appearance.Options.UseBackColor = True
        Me.TxtEmail.Properties.Appearance.Options.UseFont = True
        Me.TxtEmail.Properties.Appearance.Options.UseForeColor = True
        Me.TxtEmail.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtEmail.Size = New System.Drawing.Size(355, 30)
        Me.TxtEmail.TabIndex = 399
        '
        'TxtPassword
        '
        Me.TxtPassword.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtPassword.EditValue = ""
        Me.TxtPassword.Location = New System.Drawing.Point(165, 181)
        Me.TxtPassword.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtPassword.Name = "TxtPassword"
        Me.TxtPassword.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtPassword.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtPassword.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtPassword.Properties.Appearance.Options.UseBackColor = True
        Me.TxtPassword.Properties.Appearance.Options.UseFont = True
        Me.TxtPassword.Properties.Appearance.Options.UseForeColor = True
        Me.TxtPassword.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtPassword.Size = New System.Drawing.Size(354, 30)
        Me.TxtPassword.TabIndex = 399
        '
        'BntTest
        '
        Me.BntTest.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntTest.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTest.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntTest.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTest.Appearance.Options.UseBackColor = True
        Me.BntTest.Appearance.Options.UseBorderColor = True
        Me.BntTest.Appearance.Options.UseFont = True
        Me.BntTest.Appearance.Options.UseForeColor = True
        Me.BntTest.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntTest.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntTest.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntTest.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntTest.AppearanceDisabled.Options.UseBackColor = True
        Me.BntTest.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntTest.AppearanceDisabled.Options.UseFont = True
        Me.BntTest.AppearanceDisabled.Options.UseForeColor = True
        Me.BntTest.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntTest.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntTest.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntTest.AppearanceHovered.Options.UseBackColor = True
        Me.BntTest.AppearanceHovered.Options.UseBorderColor = True
        Me.BntTest.AppearanceHovered.Options.UseForeColor = True
        Me.BntTest.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntTest.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntTest.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntTest.AppearancePressed.Options.UseBackColor = True
        Me.BntTest.AppearancePressed.Options.UseBorderColor = True
        Me.BntTest.AppearancePressed.Options.UseForeColor = True
        Me.BntTest.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image
        Me.BntTest.Location = New System.Drawing.Point(263, 498)
        Me.BntTest.Name = "BntTest"
        Me.BntTest.Size = New System.Drawing.Size(121, 39)
        Me.BntTest.TabIndex = 377
        Me.BntTest.Text = "Start"
        '
        'TxtSMTPServer
        '
        Me.TxtSMTPServer.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtSMTPServer.EditValue = ""
        Me.TxtSMTPServer.Location = New System.Drawing.Point(165, 40)
        Me.TxtSMTPServer.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.TxtSMTPServer.Name = "TxtSMTPServer"
        Me.TxtSMTPServer.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TxtSMTPServer.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtSMTPServer.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtSMTPServer.Properties.Appearance.Options.UseBackColor = True
        Me.TxtSMTPServer.Properties.Appearance.Options.UseFont = True
        Me.TxtSMTPServer.Properties.Appearance.Options.UseForeColor = True
        Me.TxtSMTPServer.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtSMTPServer.Size = New System.Drawing.Size(355, 30)
        Me.TxtSMTPServer.TabIndex = 399
        '
        'Label18
        '
        Me.Label18.AutoSize = True
        Me.Label18.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label18.ForeColor = System.Drawing.Color.Crimson
        Me.Label18.Location = New System.Drawing.Point(52, 342)
        Me.Label18.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(13, 15)
        Me.Label18.TabIndex = 375
        Me.Label18.Text = "*"
        '
        'Label17
        '
        Me.Label17.AutoSize = True
        Me.Label17.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label17.ForeColor = System.Drawing.Color.Crimson
        Me.Label17.Location = New System.Drawing.Point(52, 293)
        Me.Label17.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(13, 15)
        Me.Label17.TabIndex = 374
        Me.Label17.Text = "*"
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label16.ForeColor = System.Drawing.Color.Crimson
        Me.Label16.Location = New System.Drawing.Point(52, 244)
        Me.Label16.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(13, 15)
        Me.Label16.TabIndex = 373
        Me.Label16.Text = "*"
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.ForeColor = System.Drawing.Color.Crimson
        Me.Label15.Location = New System.Drawing.Point(52, 440)
        Me.Label15.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(13, 15)
        Me.Label15.TabIndex = 372
        Me.Label15.Text = "*"
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label14.ForeColor = System.Drawing.Color.Crimson
        Me.Label14.Location = New System.Drawing.Point(52, 391)
        Me.Label14.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(13, 15)
        Me.Label14.TabIndex = 371
        Me.Label14.Text = "*"
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.ForeColor = System.Drawing.Color.Crimson
        Me.Label13.Location = New System.Drawing.Point(52, 97)
        Me.Label13.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(13, 15)
        Me.Label13.TabIndex = 370
        Me.Label13.Text = "*"
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label12.ForeColor = System.Drawing.Color.Crimson
        Me.Label12.Location = New System.Drawing.Point(52, 195)
        Me.Label12.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(13, 15)
        Me.Label12.TabIndex = 369
        Me.Label12.Text = "*"
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label11.ForeColor = System.Drawing.Color.Crimson
        Me.Label11.Location = New System.Drawing.Point(52, 146)
        Me.Label11.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(13, 15)
        Me.Label11.TabIndex = 368
        Me.Label11.Text = "*"
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.ForeColor = System.Drawing.Color.Crimson
        Me.Label10.Location = New System.Drawing.Point(52, 48)
        Me.Label10.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(13, 15)
        Me.Label10.TabIndex = 367
        Me.Label10.Text = "*"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.ForeColor = System.Drawing.Color.White
        Me.Label9.Location = New System.Drawing.Point(66, 340)
        Me.Label9.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(65, 19)
        Me.Label9.TabIndex = 366
        Me.Label9.Text = "Subject : "
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.ForeColor = System.Drawing.Color.White
        Me.Label8.Location = New System.Drawing.Point(66, 291)
        Me.Label8.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(91, 19)
        Me.Label8.TabIndex = 364
        Me.Label8.Text = "Subject Tag : "
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.ForeColor = System.Drawing.Color.White
        Me.Label6.Location = New System.Drawing.Point(66, 242)
        Me.Label6.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(84, 19)
        Me.Label6.TabIndex = 362
        Me.Label6.Text = "From Mail : "
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.ForeColor = System.Drawing.Color.White
        Me.Label7.Location = New System.Drawing.Point(66, 438)
        Me.Label7.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(92, 19)
        Me.Label7.TabIndex = 360
        Me.Label7.Text = "From Name : "
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.White
        Me.Label5.Location = New System.Drawing.Point(66, 389)
        Me.Label5.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(81, 19)
        Me.Label5.TabIndex = 358
        Me.Label5.Text = "Name Tag : "
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.White
        Me.Label4.Location = New System.Drawing.Point(66, 95)
        Me.Label4.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(45, 19)
        Me.Label4.TabIndex = 66
        Me.Label4.Text = "Port : "
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.White
        Me.Label2.Location = New System.Drawing.Point(66, 193)
        Me.Label2.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(79, 19)
        Me.Label2.TabIndex = 64
        Me.Label2.Text = "Password : "
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.White
        Me.Label1.Location = New System.Drawing.Point(66, 144)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(58, 19)
        Me.Label1.TabIndex = 62
        Me.Label1.Text = "Email  : "
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.Color.White
        Me.Label3.Location = New System.Drawing.Point(66, 46)
        Me.Label3.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(88, 19)
        Me.Label3.TabIndex = 60
        Me.Label3.Text = "Host SMTP : "
        '
        'BntAdd
        '
        Me.BntAdd.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntAdd.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntAdd.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.Appearance.Options.UseBackColor = True
        Me.BntAdd.Appearance.Options.UseBorderColor = True
        Me.BntAdd.Appearance.Options.UseFont = True
        Me.BntAdd.Appearance.Options.UseForeColor = True
        Me.BntAdd.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntAdd.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntAdd.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntAdd.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntAdd.AppearanceDisabled.Options.UseBackColor = True
        Me.BntAdd.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntAdd.AppearanceDisabled.Options.UseFont = True
        Me.BntAdd.AppearanceDisabled.Options.UseForeColor = True
        Me.BntAdd.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntAdd.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntAdd.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntAdd.AppearanceHovered.Options.UseBackColor = True
        Me.BntAdd.AppearanceHovered.Options.UseBorderColor = True
        Me.BntAdd.AppearanceHovered.Options.UseForeColor = True
        Me.BntAdd.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.BntAdd.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntAdd.AppearancePressed.Options.UseBackColor = True
        Me.BntAdd.AppearancePressed.Options.UseBorderColor = True
        Me.BntAdd.AppearancePressed.Options.UseForeColor = True
        Me.BntAdd.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BntAdd.Location = New System.Drawing.Point(401, 498)
        Me.BntAdd.Name = "BntAdd"
        Me.BntAdd.Size = New System.Drawing.Size(121, 39)
        Me.BntAdd.TabIndex = 378
        Me.BntAdd.Text = "Add"
        '
        'CheckSSL
        '
        Me.CheckSSL.EditValue = True
        Me.CheckSSL.Location = New System.Drawing.Point(378, 90)
        Me.CheckSSL.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.CheckSSL.Name = "CheckSSL"
        Me.CheckSSL.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CheckSSL.Properties.Appearance.Options.UseFont = True
        Me.CheckSSL.Properties.Caption = " Enable SSL"
        Me.CheckSSL.Size = New System.Drawing.Size(131, 27)
        Me.CheckSSL.TabIndex = 516
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.PnlWait)
        Me.GroupControl1.Controls.Add(Me.Bnt_Reset)
        Me.GroupControl1.Controls.Add(Me.TxtFromName)
        Me.GroupControl1.Controls.Add(Me.BntTest)
        Me.GroupControl1.Controls.Add(Me.CheckSSL)
        Me.GroupControl1.Controls.Add(Me.BntAdd)
        Me.GroupControl1.Controls.Add(Me.TxtTag_1)
        Me.GroupControl1.Controls.Add(Me.Label15)
        Me.GroupControl1.Controls.Add(Me.TxtSMTPServer)
        Me.GroupControl1.Controls.Add(Me.Label7)
        Me.GroupControl1.Controls.Add(Me.TxtSubject)
        Me.GroupControl1.Controls.Add(Me.TxtTag_2)
        Me.GroupControl1.Controls.Add(Me.Label14)
        Me.GroupControl1.Controls.Add(Me.Label3)
        Me.GroupControl1.Controls.Add(Me.Label18)
        Me.GroupControl1.Controls.Add(Me.Label5)
        Me.GroupControl1.Controls.Add(Me.Label10)
        Me.GroupControl1.Controls.Add(Me.TxtFromMail)
        Me.GroupControl1.Controls.Add(Me.Label4)
        Me.GroupControl1.Controls.Add(Me.Label9)
        Me.GroupControl1.Controls.Add(Me.Label17)
        Me.GroupControl1.Controls.Add(Me.TxtPort)
        Me.GroupControl1.Controls.Add(Me.CheckEdit1)
        Me.GroupControl1.Controls.Add(Me.Label13)
        Me.GroupControl1.Controls.Add(Me.Label16)
        Me.GroupControl1.Controls.Add(Me.Label8)
        Me.GroupControl1.Controls.Add(Me.TxtPassword)
        Me.GroupControl1.Controls.Add(Me.TxtEmail)
        Me.GroupControl1.Controls.Add(Me.Label1)
        Me.GroupControl1.Controls.Add(Me.Label11)
        Me.GroupControl1.Controls.Add(Me.Label2)
        Me.GroupControl1.Controls.Add(Me.Label6)
        Me.GroupControl1.Controls.Add(Me.Label12)
        Me.GroupControl1.Location = New System.Drawing.Point(18, 2)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(583, 560)
        Me.GroupControl1.TabIndex = 399
        Me.GroupControl1.Text = "Settings SMTP"
        '
        'PnlWait
        '
        Me.PnlWait.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.PnlWait.Appearance.Options.UseBackColor = True
        Me.PnlWait.AppearanceCaption.BackColor = System.Drawing.Color.Transparent
        Me.PnlWait.AppearanceCaption.Options.UseBackColor = True
        Me.PnlWait.Controls.Add(Me.SimpleButton1)
        Me.PnlWait.Controls.Add(Me.ProgressPanel2)
        Me.PnlWait.Location = New System.Drawing.Point(99, 478)
        Me.PnlWait.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.PnlWait.Name = "PnlWait"
        Me.PnlWait.ShowCaption = False
        Me.PnlWait.Size = New System.Drawing.Size(438, 68)
        Me.PnlWait.TabIndex = 517
        Me.PnlWait.Text = "GroupControl2"
        Me.PnlWait.Visible = False
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton1.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton1.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Options.UseBackColor = True
        Me.SimpleButton1.Appearance.Options.UseBorderColor = True
        Me.SimpleButton1.Appearance.Options.UseFont = True
        Me.SimpleButton1.Appearance.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton1.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearanceDisabled.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SimpleButton1.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton1.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton1.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton1.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer), CType(CType(18, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.SimpleButton1.Location = New System.Drawing.Point(288, 14)
        Me.SimpleButton1.LookAndFeel.SkinName = "Visual Studio 2013 Light"
        Me.SimpleButton1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(117, 41)
        Me.SimpleButton1.TabIndex = 392
        Me.SimpleButton1.Text = "&Cancel"
        '
        'ProgressPanel2
        '
        Me.ProgressPanel2.AnimationToTextDistance = 10
        Me.ProgressPanel2.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel2.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.ProgressPanel2.Appearance.Options.UseBackColor = True
        Me.ProgressPanel2.Appearance.Options.UseFont = True
        Me.ProgressPanel2.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel2.AppearanceCaption.Options.UseFont = True
        Me.ProgressPanel2.AppearanceDescription.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel2.AppearanceDescription.Options.UseFont = True
        Me.ProgressPanel2.BarAnimationElementThickness = 5
        Me.ProgressPanel2.Caption = "Please Wait..."
        Me.ProgressPanel2.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
        Me.ProgressPanel2.Description = "Check SMTP.."
        Me.ProgressPanel2.Location = New System.Drawing.Point(8, 10)
        Me.ProgressPanel2.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.ProgressPanel2.Name = "ProgressPanel2"
        Me.ProgressPanel2.Size = New System.Drawing.Size(217, 48)
        Me.ProgressPanel2.TabIndex = 395
        Me.ProgressPanel2.Text = "picWait"
        '
        'frmAddSmtpServer
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(631, 574)
        Me.Controls.Add(Me.GroupControl1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.ShowIcon = False
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmAddSmtpServer"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtFromName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtTag_1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtTag_2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtPort.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtSubject.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtFromMail.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtEmail.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtPassword.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtSMTPServer.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckSSL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        CType(Me.PnlWait, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PnlWait.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents DxErrorProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents Bnt_Reset As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntTest As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label18 As Label
    Friend WithEvents Label17 As Label
    Friend WithEvents Label16 As Label
    Friend WithEvents Label15 As Label
    Friend WithEvents Label14 As Label
    Friend WithEvents Label13 As Label
    Friend WithEvents Label12 As Label
    Friend WithEvents Label11 As Label
    Friend WithEvents Label10 As Label
    Friend WithEvents Label9 As Label
    Friend WithEvents Label8 As Label
    Friend WithEvents Label6 As Label
    Friend WithEvents Label7 As Label
    Friend WithEvents Label5 As Label
    Friend WithEvents CheckEdit1 As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Label4 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents BntAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TxtPort As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtSMTPServer As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CheckSSL As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TxtTag_1 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TxtTag_2 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TxtFromName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtSubject As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtFromMail As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtPassword As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtEmail As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents PnlWait As DevExpress.XtraEditors.GroupControl
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ProgressPanel2 As DevExpress.XtraWaitForm.ProgressPanel
End Class
