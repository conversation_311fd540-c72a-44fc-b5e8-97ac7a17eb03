﻿Imports System.IO
Imports System.Net
Imports System.Net.Mime
Imports System.Net.Sockets
Imports System.Runtime.CompilerServices
Imports System.Security.Cryptography
Imports System.Text
Imports System.Xml.Serialization
Imports DevExpress.DataAccess.Native.Web
Imports DevExpress.Export.Xl
Imports DevExpress.Pdf
Imports IronPdf.Rendering
Imports SelectPdf
Imports PdfDocument = SelectPdf.PdfDocument
Public Class FuncSendMail
    Public Shared Function RandomChar(number As Integer) As String
        Dim validchars As String = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"
        Dim sb As New StringBuilder()
        Dim rand As New Random()
        For i As Integer = 1 To number
            Dim idx As Integer = rand.Next(0, validchars.Length)
            Dim randomCharn As Char = validchars(idx)
            sb.Append(randomCharn)
        Next i
        Return sb.ToString()
    End Function
    Public Shared Function RandomNum(number As Integer) As String
        Dim validchars As String = "1234567890"
        Dim sb As New StringBuilder()
        Dim rand As New Random()
        For i As Integer = 1 To number
            Dim idx As Integer = rand.Next(0, validchars.Length)
            Dim randomCharn As Char = validchars(idx)
            sb.Append(randomCharn)
        Next i
        Return sb.ToString()
    End Function
    Public Shared Function RandomBrowser() As String
        Dim nameArray() As String = {"Google Chrome", "Mozilla Firefox", "Brave  Browser", "Microsoft Edge", "Safari Web Browser", "Puffin Web Browser", "UC Browser"}
        Dim rnd As New Random
        Dim output As String
        output = nameArray(rnd.Next(0, (nameArray.Count - 1)))
        Return output
    End Function
    Public Shared Function RandomCountry() As String
        Dim nameArray() As String = {"Country", "United States", "Afghanistan", "	Nigeria", " Albania ", "Algeria", "American Samoa ", "Andorra", "Angola", "Anguilla", "Antigua and Barbuda", "Argentina", "Armenia", "Aruba", "Australia", "Azerbaijan", "Bahamas", "	Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium", "Belize", "Benin", "Bermuda", "Bhutan", "Bolivia", "Botswana", "Brazil", "Brunei Darussalam", "Bulgaria", "Burkina Faso", "Burundi", "Cambodia", "Cambodia", "Cambodia", "Cape Verde", "Cayman Islands", "Chad", "Chile", "Chile", "	Colombia", "Comoros", "Czech Republic", "	Dominica", "Ecuador", "Gabon", "The Gambia", "Georgia", "	Germany", "Ghana", "Gibraltar", "Greece", "Grenada", "	Guadeloupe", "Guam", "Guam", "Guyana", "Haiti", "Hong Kong", "Iceland", "India", "Italy", "Jamaica", "Japan", "	Kenya", "Korea", "Mexico", "Monaco", "Morocco", "	Netherlands", "	Peru", "Romania", "	Spain", "Sweden", "China", "Taiwan", "Turkey", "Ukraine", "United Kingdom", "	Venezuela"}
        Dim rnd As New Random
        Dim output As String
        output = nameArray(rnd.Next(0, (nameArray.Count - 1)))
        Return output
    End Function
    ' IP
    Public Shared Function IP() As String
        Dim nameArray() As String = {"IP", "***************", "*************", "***************	", " ************** ", "***************", "**************8	  ", "***************", "*************2", "**************", "**************", "***************", "**************", "***************", "***************", "***************", "**************", "	***************", "**************", "***************", "*************5", "***************", "*************", "*************", "***************", "**************", "*************9", "***************", "*************", "*************7", "*************9", "**************", "***************", "***************", "***************", "***************", "***************", "**************", "***************", "***************", "***************", "	*************", "***************", "C162.159.246.190", "***************", "***************", "**************1", "***************", "***********", "	*************", "***********", "*************", "*************", "**************", "*************", "159.8.114.37", "159.8.114.37", "183.88.232.207", "162.159.248.1", "162.159.244.149", "162.159.242.178", "104.25.114.28", "162.159.251.122", "************", "***************", "	***************", "**************", "*************", "***********", "**************", "	************", "************", "**************", "**************", "*************", "**************", "**************", "***************", "************", "**************", "	*************"}
        Dim rnd As New Random
        Dim output As String
        output = nameArray(rnd.Next(0, (nameArray.Count - 1)))
        Return output
    End Function
    Public Shared Function IPChina() As String
        'IP China
        Dim nameArray() As String = {"IP", "*************", "*************", "**************", " ************** ", "**************", "**************", "************", "*************", "*************", "*************", "*************", "**************", "*************", "**************", "**************", "*************", "************", "************", "************", "************", "**************", "*************", "*************", "*************", "***************", "***************", "***************", "***************", "***************", "**************", "**************", "*************", "**************", "***************", "	***************", "***************", "***************", "***************", "***************", "**************", "***************", "**************", "**************", "**************", "**************", "*************", "*************", "	**************", "**************", "************", "	***************", "***************", "***************", "***************", "**************", "121.46.63.255", "121.5.255.255", "	121.58.167.255", "121.59.30.2559", "121.59.16.255", "121.89.255.255", "121.91.111.255", "121.91.191.255	", "123.191.255.255", "	123.197.255.255", "123.199.255.255", "123.202.136.1", "123.202.250.9", "123.203.109.1", "123.203.144.1", "123.203.145.125", "123.203.184.1", "123.203.238.65", "123.207.255.255", "124.167.255.255", "124.175.255.255", "***************", "124.193.255.255", "124.23.255.255", "124.207.255.255"}
        Dim rnd As New Random
        Dim output As String
        output = nameArray(rnd.Next(0, (nameArray.Count - 1)))
        Return output
    End Function
    Public Shared Function FAKEPHONE() As String
        'FAKE_PHONE
        Dim nameArray() As String = {"Phone", "******-555-0181", "******-555-0103", "******-555-0180", " ******-555-0181 ", "******-555-0167", "******-555-0136", "******-555-0110", "******-555-0115", "******-555-0183", "******-555-0126", "******-555-0134", "******-555-0135", "******-555-0187", "******-555-0195", "******-555-0126", "******-555-0111", "******-555-011", "******-555-0121", "******-555-0166", "******-555-0168", "******-555-0185", "******-555-0122", "******-555-0186", "******-585-0122", "******-555-0124", "******-555-1122", "******-555-0151", "******-555-0189", "******-555-0117", "******-555-0110", "******-555-0122", "******-555-0155", "******-555-0138", "******-555-0184", "******-555-0163", "******-555-0100", "******-555-0146", "******-555-0174", "******-555-0192", "******-555-0145", "******-555-0167", "******-555-0127", "******-555-0118", "**************", "******-555-0145", "******-555-0104", "******-555-0187", "******-555-0188", "******-555-0145", "******-555-0181", "******-555-0184", "******-555-0171", "+1-701-555-0113", "***************", "+1-701-555-0191", "+1-701-555-0187", "+1-701-555-0143", "+1-701-555-0113", "121.59.30.2559", "+1-701-555-0182", "+1-919-555-0126", "+1-919-555-0126", "121.91.191.255	", "+1-919-555-0191", "	+1-919-555-0112", "+1-919-555-0164", "+44 1632 960516", "+44 1632 960828", "+44 1632 960523", "123.203.144.1", "+44 1632 960388", "+44 1632 960168", "+44 1632 960133", "518-555-0165", "+1-518-555-0195", "+1-518-555-0107", "+1-518-555-0104", "+1-518-555-0160", "+1-518-555-0184", "+1-518-555-0165"}
        Dim rnd As New Random
        Dim output As String
        output = nameArray(rnd.Next(0, (nameArray.Count - 1)))
        Return output
    End Function
    Public Shared Function FakeEmail() As String
        'FAKE_Email
        Dim nameArray() As String = {"Email", "<EMAIL>", "<EMAIL>", "<EMAIL>", " <EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "******-555-0115", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "******-555-0186", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "******-555-0155", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "******-555-0192", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "+1-919-555-0126", "<EMAIL>", "<EMAIL>", "+1-919-555-0191", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "518-555-0165", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}
        Dim rnd As New Random
        Dim output As String
        output = nameArray(rnd.Next(0, (nameArray.Count - 1)))
        Return output
    End Function
    Public Shared Function NewYork() As String
        'FAKE_adress
        Dim nameArray() As String = {"NewYork", "Street	908 W Aztec Blvd", "Street	905 W Lea St", "Street	5301 Gibson Blvd SE", " Street	1014 S Main St", "Street	Copper King Trail", "Street	5800 College Blvd", "Street	611 Main St", "Street	1700 Eubank Blvd NE", "Street	905 W Lea St", "Street	3522 Zafarano Dr #F1", "Street	1420 Montaño Rd N", "Street	12124 New Mexico 14", "Street	123 Central Park Square", "Street	3700 4th St NW", "Street 400 Eubank Blvd NE", "Street  2901 Candelaria Rd NW", "Street	2901 Candelaria Rd NW", "Street 525 Paseo Del Pueblo Norte", "Street	1615 7th St", "Street	425 San Felipe St NW", "Street	611 Main St", "Street	315 Juan Tabo Blvd NE", "Street	5800 College Blvd", "Street	1519 Phoenix Ave NW", "Street	8201 Golf Course Rd NW", "Street	3101 Southern Blvd SE", "Street	5555 Zuni Rd SE #6", "Street	555 W Cordova Rd", "Street	6400 Golf Course Rd NW ", "Street	925 San Pedro Dr NE", "Street	600 N Guadalupe St", "Street	131 98th St NW", "Street	6417 Menaul Blvd NE", "Street	630 Haines Ave NW", "Street	3157 San Mateo Blvd NE", "Street	1100 2nd St SW", "Street	5702 Bataan Memorial", "Street	1700 Eubank Blvd NE ", " Street	654 Sudderth Dr", "Street	12165 New Mexico 14", "Street	111 Harvard Dr SE"}
        Dim rnd As New Random
        Dim output As String
        output = nameArray(rnd.Next(0, (nameArray.Count - 1)))
        Return output
    End Function
    Public Shared Function EditMailFromCodes(text As String, email As String, Optional link As String = "", Optional imagetag As String = "<img src=cid:MyPic width=64px height=64px>", Optional ByVal qrImageTag As String = "<img src=cid:QRPic width=64px height=64px>") As String
        If imagetag Is Nothing Then
            imagetag = ""
        End If
        If qrImageTag Is Nothing Then
            qrImageTag = ""
        End If
        '============== في حالة تشفير الايميل
        If text.IndexOf("[-Email64-]", StringComparison.OrdinalIgnoreCase) >= 0 Then
            Dim byt As Byte() = System.Text.Encoding.UTF8.GetBytes(email)
            text = ReplaceIgnoreCase(text, "[-Email64-]", Convert.ToBase64String(byt))
        End If
        '======================================================================
        'If Not link.Contains("https://") AndAlso Not link.Contains("http://") Then
        '    link = "https://" & link
        'End If
        '======================================================================
        text = ReplaceIgnoreCase(text, "[-Email-]", email)
        text = ReplaceIgnoreCase(text, "[-RCh1-]", FuncSendMail.RandomChar(1))
        text = ReplaceIgnoreCase(text, "[-RCh2-]", FuncSendMail.RandomChar(2))
        text = ReplaceIgnoreCase(text, "[-RCh3-]", FuncSendMail.RandomChar(3))
        text = ReplaceIgnoreCase(text, "[-RCh4-]", FuncSendMail.RandomChar(4))
        text = ReplaceIgnoreCase(text, "[-RCh5-]", FuncSendMail.RandomChar(5))
        text = ReplaceIgnoreCase(text, "[-RCh6-]", FuncSendMail.RandomChar(6))
        text = ReplaceIgnoreCase(text, "[-RCh7-]", FuncSendMail.RandomChar(7))
        text = ReplaceIgnoreCase(text, "[-RCh8-]", FuncSendMail.RandomChar(8))
        text = ReplaceIgnoreCase(text, "[-RN1-]", FuncSendMail.RandomNum(1))
        text = ReplaceIgnoreCase(text, "[-RN2-]", FuncSendMail.RandomNum(2))
        text = ReplaceIgnoreCase(text, "[-RN3-]", FuncSendMail.RandomNum(3))
        text = ReplaceIgnoreCase(text, "[-RN4-]", FuncSendMail.RandomNum(4))
        text = ReplaceIgnoreCase(text, "[-RN5-]", FuncSendMail.RandomNum(5))
        text = ReplaceIgnoreCase(text, "[-RN6-]", FuncSendMail.RandomNum(6))
        text = ReplaceIgnoreCase(text, "[-RN7-]", FuncSendMail.RandomNum(7))
        text = ReplaceIgnoreCase(text, "[-RN8-]", FuncSendMail.RandomNum(8))
        text = ReplaceIgnoreCase(text, "[-RN9-]", FuncSendMail.RandomNum(9))
        text = ReplaceIgnoreCase(text, "[-RN10-]", FuncSendMail.RandomNum(10))
        text = ReplaceIgnoreCase(text, "[-RN12-]", FuncSendMail.RandomNum(12))
        text = ReplaceIgnoreCase(text, "[-RN14-]", FuncSendMail.RandomNum(14))
        text = ReplaceIgnoreCase(text, "[-IP-]", IP())
        text = ReplaceIgnoreCase(text, "[-IPChina-]", IPChina())
        text = ReplaceIgnoreCase(text, "[-Domain-]", email.Substring(email.IndexOf("@") + 1, email.Length - email.IndexOf("@") - 1))
        text = ReplaceIgnoreCase(text, "[-Name-]", email.Substring(0, email.IndexOf("@")))
        text = ReplaceIgnoreCase(text, "[-Date-]", DateTime.Now.ToString("dd/MM/yyyy"))
        text = ReplaceIgnoreCase(text, "[-Time-]", DateTime.Now.ToString("HH:mm tt"))
        text = ReplaceIgnoreCase(text, "[-DateTomorrow-]", DateTime.Now.AddDays(1).ToString("dd/MM/yyyy"))
        text = ReplaceIgnoreCase(text, "[-RandomBrowser-]", RandomBrowser())
        text = ReplaceIgnoreCase(text, "[-RCountry-]", RandomCountry())
        text = ReplaceIgnoreCase(text, "[-FakePhone-]", FAKEPHONE())
        text = ReplaceIgnoreCase(text, "[-FakeEmail-]", FakeEmail())
        text = ReplaceIgnoreCase(text, "[-NewYork-]", NewYork())
        text = ReplaceIgnoreCase(text, "[-Link-]", link)
        text = ReplaceIgnoreCase(text, "[-UCase-]", UpperCase_FirstChar(email.Substring(0, email.IndexOf("@"))))
        text = ReplaceIgnoreCase(text, "[-CompanyName-]", ExtractCompanyName(email))
        text = ReplaceIgnoreCase(text, "[-Logo-]", imagetag)
        'text = ReplaceIgnoreCase(text, "[-QRCode-]", qrImageTag)
        If String.IsNullOrEmpty(qrImageTag) Then
            qrImageTag = "<img src='cid:QRPic' width=64px height=64px>" ' وضع قيمة افتراضية
        End If
        text = ReplaceIgnoreCase(text, "[-QRCode-]", qrImageTag)
        Return text
    End Function
    Public Shared Function ReplaceIgnoreCase(inputString As String, searchString As String, replecmentString As String) As String
        Return RegularExpressions.Regex.Replace(inputString, RegularExpressions.Regex.Escape(searchString), replecmentString)
    End Function
    Public Shared Function IsValidEmailAddress(ByVal emailAddress As String) As Boolean
        If emailAddress = "" Then
            Return False
        End If
        Dim valid As Boolean = True
        Try
            Dim a = New System.Net.Mail.MailAddress(emailAddress)
        Catch ex As FormatException
            valid = False
        End Try
        Return valid
    End Function
    Public Shared Function SerializeDataTableToXml(ByVal dataTable As DataTable) As String
        Using sw As StringWriter = New StringWriter()
            dataTable.WriteXml(sw, XmlWriteMode.WriteSchema)
            Return sw.ToString()
        End Using
    End Function
    Public Shared Function SerializeListToXml(Of TEntity)(ByVal list As List(Of TEntity)) As String
        Dim xmlData As String = String.Empty
        Try
            Using sw As StringWriter = New StringWriter()
                Dim serializer As XmlSerializer = New XmlSerializer(GetType(List(Of TEntity)))
                serializer.Serialize(sw, list)
                xmlData = sw.ToString()
            End Using
        Catch ex As Exception
            MsgBox(ex.Message)
            Return Nothing
        End Try
        Return xmlData
    End Function
    Public Shared Function DeserializeXmlToDataTable(ByVal xmlData As String) As DataTable
        Dim dataTable As DataTable = New DataTable()
        If String.IsNullOrEmpty(xmlData) Then
            Return Nothing
        End If
        Try
            Using sr As StringReader = New StringReader(xmlData)
                dataTable.ReadXml(sr)
            End Using
        Catch ex As Exception
            MsgBox(ex.Message)
            Return Nothing
        End Try
        Return dataTable
    End Function
    Public Shared Function DeserializeXmlToList(Of TEntity As New)(ByVal xmlData As String) As List(Of TEntity)
        If String.IsNullOrEmpty(xmlData) Then
            Return Nothing
        End If
        Dim list As List(Of TEntity) = Nothing
        Try
            Using sr As StringReader = New StringReader(xmlData)
                Dim serializer As XmlSerializer = New XmlSerializer(GetType(List(Of TEntity)))
                list = DirectCast(serializer.Deserialize(sr), List(Of TEntity))
            End Using
        Catch ex As Exception
            MsgBox(ex.Message)
            Return Nothing
        End Try
        Return list
    End Function
    Public Shared Sub ConvertHtmlToPdf(ByVal htmlString As String, ByVal pdfDistPath As String)
        ' instantiate a html to pdf converter object
        Dim converter As New HtmlToPdf()
        converter.Options.PdfPageSize = PdfPageSize.A4
        converter.Options.RenderingEngine = RenderingEngine.WebKit
        converter.Options.MaxPageLoadTime = 60
        converter.Options.MarginLeft = 5
        converter.Options.MarginRight = 5
        converter.Options.MarginTop = 5
        converter.Options.MarginBottom = 5
        ' create a new pdf document converting an url
        Dim doc As PdfDocument = converter.ConvertHtmlString(htmlString)
        ' save pdf document
        doc.Save(pdfDistPath)
        ' close pdf document
        doc.Close()
    End Sub
    ' Overload 1: Convert image from file path to Base64 string
    Public Shared Function ConvertToBase64(ByVal filePath As String) As String
        Dim imageBytes As Byte() = File.ReadAllBytes(filePath)
        Return Convert.ToBase64String(imageBytes)
    End Function
    ' Overload 2: Convert image from stream to Base64 string
    Public Shared Function ConvertToBase64(ByVal inputStream As Stream) As String
        Dim memoryStream As MemoryStream = If(TypeOf inputStream Is MemoryStream, CType(inputStream, MemoryStream), New MemoryStream())
        If TypeOf inputStream IsNot MemoryStream Then
            inputStream.CopyTo(memoryStream)
        End If
        ' It's important to reset the position to the beginning of the stream
        memoryStream.Position = 0
        Dim bytes As Byte() = memoryStream.ToArray()
        Return Convert.ToBase64String(bytes)
    End Function
    Public Shared Function GetHtmlImageTag(base64Image As String, Optional width As Integer = 64, Optional height As Integer = 64, Optional imageFormat As String = MediaTypeNames.Image.Jpeg, Optional altText As String = "Embedded Image") As String
        Return $"<img width={width}px height={height}px src=""data:{imageFormat};base64,{base64Image}"" alt=""{altText}"">"
    End Function
    Public Shared Function GetSettingsMd5() As String
        ' Serialize settings
        Dim sb As New StringBuilder()
        For Each prop As System.Configuration.SettingsPropertyValue In My.Settings.PropertyValues
            If prop.Name = "SETTING_MDF" Then Continue For
            sb.AppendLine($"{prop.Name}={prop.PropertyValue}")
        Next
        ' Convert the serialized settings to a byte array
        Dim encoding As New UTF8Encoding()
        Dim bytes As Byte() = encoding.GetBytes(sb.ToString())
        ' Use MD5 to compute the hash of the byte array
        Using md5Hash As MD5 = MD5.Create()
            Dim data As Byte() = md5Hash.ComputeHash(bytes)
            ' Convert the byte array to a hex string
            Dim sBuilder As New StringBuilder()
            For i As Integer = 0 To data.Length - 1
                sBuilder.Append(data(i).ToString("x2"))
            Next i
            Return sBuilder.ToString()
        End Using
    End Function
    Public Shared Function GetIPAddresses(hostname As String) As String
        Dim ipAddresses As String = hostname
        Try
            Dim addresses As IPAddress() = Dns.GetHostAddresses(hostname)
            ipAddresses = addresses?.LastOrDefault().ToString()
        Catch ex As SocketException
            Console.WriteLine("Socket error: " & ex.Message)
        Catch ex As Exception
            Console.WriteLine("General error: " & ex.Message)
        End Try
        Return ipAddresses
    End Function

    ''' <summary>
    ''' الحصول على البروكسي من مدير البروكسيات
    ''' </summary>
    ''' <param name="form">النموذج الرئيسي</param>
    ''' <returns>WebProxy أو Nothing إذا لم يكن هناك بروكسي صالح</returns>
    Public Shared Function GetProxyFromManager(form As frmEmailSender) As WebProxy
        Try
            ' التحقق من وجود النموذج
            If form Is Nothing Then
                Return Nothing
            End If

            ' استدعاء دالة GetNextValidProxy مباشرة
            Return form.GetNextValidProxy()
        Catch ex As Exception
            ' تسجيل الخطأ وإرجاع Nothing
            Console.WriteLine("Error getting proxy: " & ex.Message)
            Return Nothing
        End Try
    End Function


End Class
