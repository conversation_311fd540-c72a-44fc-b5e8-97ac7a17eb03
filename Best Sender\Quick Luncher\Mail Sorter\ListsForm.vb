﻿Imports System.IO

Public Class ListsForm
    Dim x, y As Integer
    Dim newpoint As New Point
    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Close()
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Dim path = ""
        Dim dlg As FolderBrowserDialog = New FolderBrowserDialog()
        Dim Result As DialogResult = dlg.ShowDialog()
        If Result = DialogResult.OK Then
            path = dlg.SelectedPath & "\Lists\"

            If Not Directory.Exists(path) Then Directory.CreateDirectory(path)
            Using sw = File.CreateText(path & Text.Replace("Mails", "").Trim() & ".txt")
                sw.WriteLine(TextBox1.Text)
                MessageBox.Show("List Successfully Saved !")
            End Using
        End If
    End Sub
    Private Sub Panel2_MouseDown(sender As Object, e As MouseEventArgs) Handles PanelUp2.MouseDown
        x = Control.MousePosition.X - Me.Location.X
        y = Control.MousePosition.Y - Me.Location.Y

    End Sub

    Private Sub Panel2_MouseMove(sender As Object, e As MouseEventArgs) Handles PanelUp2.MouseMove
        If e.Button = Windows.Forms.MouseButtons.Left Then
            newpoint = Control.MousePosition
            newpoint.X -= x
            newpoint.Y -= y
            Me.Location = newpoint
            Application.DoEvents()
        End If
    End Sub
    Private Sub BtnHide_Click(sender As Object, e As EventArgs) Handles BtnHide.Click
        WindowState = FormWindowState.Minimized
    End Sub

    Private Sub BtnClose_Click(sender As Object, e As EventArgs) Handles BtnClose.Click
        Me.Close()
    End Sub
End Class