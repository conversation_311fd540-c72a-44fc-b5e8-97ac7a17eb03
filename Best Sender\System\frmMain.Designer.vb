﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmMain
    Inherits DevExpress.XtraBars.Ribbon.RibbonForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmMain))
        Me.RibbonControl = New DevExpress.XtraBars.Ribbon.RibbonControl()
        Me.BarStaticItem6 = New DevExpress.XtraBars.BarStaticItem()
        Me.BarSubItem6 = New DevExpress.XtraBars.BarSubItem()
        Me.BarSubItem9 = New DevExpress.XtraBars.BarSubItem()
        Me.BarButtonItem25 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem24 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem21 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem27 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem74 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem75 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem76 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItem10 = New DevExpress.XtraBars.BarSubItem()
        Me.BarButtonItem46 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem39 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem43 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem40 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem42 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem81 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItem14 = New DevExpress.XtraBars.BarSubItem()
        Me.BarButtonItem47 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItem8 = New DevExpress.XtraBars.BarSubItem()
        Me.BntRedirectWithPass = New DevExpress.XtraBars.BarButtonItem()
        Me.RedirectEncodedLinkSpider = New DevExpress.XtraBars.BarButtonItem()
        Me.BntCloudFlar = New DevExpress.XtraBars.BarButtonItem()
        Me.BntGoogleCaptsha = New DevExpress.XtraBars.BarButtonItem()
        Me.BntRedirectPuzzel = New DevExpress.XtraBars.BarButtonItem()
        Me.BntRedirectVIP = New DevExpress.XtraBars.BarButtonItem()
        Me.BntRedirectMicrosoft = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem29 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem30 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItem7 = New DevExpress.XtraBars.BarSubItem()
        Me.BarButtonItem28 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem31 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem48 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItem12 = New DevExpress.XtraBars.BarSubItem()
        Me.BntSMTP = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem52 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem72 = New DevExpress.XtraBars.BarButtonItem()
        Me.BntIMAP = New DevExpress.XtraBars.BarButtonItem()
        Me.INTERNETCONNECTION = New DevExpress.XtraBars.BarStaticItem()
        Me.BarButtonItem1 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem16 = New DevExpress.XtraBars.BarButtonItem()
        Me.lblTrail = New DevExpress.XtraBars.BarStaticItem()
        Me.LBLACTIVATIONOWNER = New DevExpress.XtraBars.BarStaticItem()
        Me.BarButtonItem19 = New DevExpress.XtraBars.BarButtonItem()
        Me.UserInfo = New DevExpress.XtraBars.BarButtonItem()
        Me.BntEmailScanner_StartScan = New DevExpress.XtraBars.BarButtonItem()
        Me.BntEmailScanner_StopScan = New DevExpress.XtraBars.BarButtonItem()
        Me.BntEmailScanner_SaveList = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem17 = New DevExpress.XtraBars.BarButtonItem()
        Me.BntEmailScanner_OpenForm = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItem1 = New DevExpress.XtraBars.BarSubItem()
        Me.BarButtonItem18 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItem2 = New DevExpress.XtraBars.BarSubItem()
        Me.BntSignal = New DevExpress.XtraBars.BarButtonItem()
        Me.BntDiscord = New DevExpress.XtraBars.BarButtonItem()
        Me.BntTlg = New DevExpress.XtraBars.BarButtonItem()
        Me.TLGCh = New DevExpress.XtraBars.BarButtonItem()
        Me.BntYouTube = New DevExpress.XtraBars.BarButtonItem()
        Me.SkinBarSubItem1 = New DevExpress.XtraBars.SkinBarSubItem()
        Me.BarSubItem3 = New DevExpress.XtraBars.BarSubItem()
        Me.bntAddletter = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Clear_Letter = New DevExpress.XtraBars.BarButtonItem()
        Me.btnEdit = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Inbox_Spam = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Paste_Letter = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItem4 = New DevExpress.XtraBars.BarSubItem()
        Me.BarButtonItem26 = New DevExpress.XtraBars.BarButtonItem()
        Me.bnt_ClearMailList = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Save_AllSettings = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItem5 = New DevExpress.XtraBars.BarSubItem()
        Me.Bnt_Add_Acc_SMTP = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Clear_List_SMTP = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Tag = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Reset = New DevExpress.XtraBars.BarButtonItem()
        Me.BntUploadeSMTP = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem20 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem22 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem23 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem32 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem35 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem38 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem45 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem2 = New DevExpress.XtraBars.BarButtonItem()
        Me.BntStartOlderHost = New DevExpress.XtraBars.BarButtonItem()
        Me.BntClear = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_get_Example = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Copy_List = New DevExpress.XtraBars.BarButtonItem()
        Me.BntSaveSMTPList = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Sender_Start = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem51 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem53 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem54 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem55 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem56 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem57 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem58 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem59 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem60 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem61 = New DevExpress.XtraBars.BarButtonItem()
        Me.btnSelectFile = New DevExpress.XtraBars.BarButtonItem()
        Me.btnStart = New DevExpress.XtraBars.BarButtonItem()
        Me.btnStop = New DevExpress.XtraBars.BarButtonItem()
        Me.BntSave = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem64 = New DevExpress.XtraBars.BarButtonItem()
        Me.BntRest = New DevExpress.XtraBars.BarStaticItem()
        Me.ResetAll = New DevExpress.XtraBars.BarButtonItem()
        Me.BntDeleteDuplicatemail = New DevExpress.XtraBars.BarButtonItem()
        Me.Select_File_text_Email = New DevExpress.XtraBars.BarButtonItem()
        Me.Start_Delet_Mail = New DevExpress.XtraBars.BarButtonItem()
        Me.StopProcessing = New DevExpress.XtraBars.BarButtonItem()
        Me.Clear_AllMail = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem62 = New DevExpress.XtraBars.BarButtonItem()
        Me.BntUploadeList = New DevExpress.XtraBars.BarButtonItem()
        Me.BntStart = New DevExpress.XtraBars.BarButtonItem()
        Me.BntSaveList = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem67 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem68 = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Select_PHP = New DevExpress.XtraBars.BarButtonItem()
        Me.BntResetPHP = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_php_obfuscation = New DevExpress.XtraBars.BarButtonItem()
        Me.SavePHPFile = New DevExpress.XtraBars.BarButtonItem()
        Me.CopyPHP = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_Paste = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem69 = New DevExpress.XtraBars.BarButtonItem()
        Me.BntGetLogo = New DevExpress.XtraBars.BarButtonItem()
        Me.BntStartRedirect = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem70 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem71 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem73 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarStaticItem1 = New DevExpress.XtraBars.BarStaticItem()
        Me.BarButtonItem77 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem78 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem79 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarSubItem11 = New DevExpress.XtraBars.BarSubItem()
        Me.BarSubItem13 = New DevExpress.XtraBars.BarSubItem()
        Me.BarButtonItem49 = New DevExpress.XtraBars.BarButtonItem()
        Me.lblDateTime = New DevExpress.XtraBars.BarStaticItem()
        Me.BarButtonItem80 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem82 = New DevExpress.XtraBars.BarButtonItem()
        Me.lblpublicIP = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem33 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem34 = New DevExpress.XtraBars.BarButtonItem()
        Me.BntStop = New DevExpress.XtraBars.BarButtonItem()
        Me.RibbonPage1 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup1 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup62 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup59 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup60 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup61 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup58 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroupCliebtVIP = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroupCLientNormal = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPage2 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup3 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup12 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup4 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup14 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup24 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup13 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup16 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup15 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPage7 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup25 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup26 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup27 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup28 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup29 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup30 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup31 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPage3 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup10 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup17 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup5 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup6 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup8 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPage17 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup9 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup57 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPage6 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup7 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup11 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup19 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup20 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup22 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup23 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup21 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageExtractDomain = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup33 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup2 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup18 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup32 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup34 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup36 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageCleanMail = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup35 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup37 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup38 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup39 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup40 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageZeroPass = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup41 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup42 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup43 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup63 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup44 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup45 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPagePHP = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup46 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.BntSelectPHP = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup48 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup49 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup50 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup51 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup47 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageCloudFlar = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPageGroup52 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup53 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup54 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup55 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonPageGroup56 = New DevExpress.XtraBars.Ribbon.RibbonPageGroup()
        Me.RibbonStatusBar = New DevExpress.XtraBars.Ribbon.RibbonStatusBar()
        Me.notificationIocn = New DevExpress.XtraBars.BarStaticItem()
        Me.BarStaticItem2 = New DevExpress.XtraBars.BarStaticItem()
        Me.lblCureentVersion = New DevExpress.XtraBars.BarStaticItem()
        Me.BarStaticItem3 = New DevExpress.XtraBars.BarStaticItem()
        Me.BarStaticItem4 = New DevExpress.XtraBars.BarStaticItem()
        Me.lblCity = New DevExpress.XtraBars.BarStaticItem()
        Me.BarStaticItem5 = New DevExpress.XtraBars.BarStaticItem()
        Me.lblCountry = New DevExpress.XtraBars.BarStaticItem()
        Me.PictureBox1 = New DevExpress.XtraBars.BarStaticItem()
        Me.DocumentManager1 = New DevExpress.XtraBars.Docking2010.DocumentManager(Me.components)
        Me.NoDocumentsView1 = New DevExpress.XtraBars.Docking2010.Views.NoDocuments.NoDocumentsView(Me.components)
        Me.TabbedView1 = New DevExpress.XtraBars.Docking2010.Views.Tabbed.TabbedView(Me.components)
        Me.DefaultLookAndFeel1 = New DevExpress.LookAndFeel.DefaultLookAndFeel(Me.components)
        Me.Timer3 = New System.Windows.Forms.Timer(Me.components)
        Me.trmfadein = New System.Windows.Forms.Timer(Me.components)
        Me.BunifuDragControl1 = New Bunifu.Framework.UI.BunifuDragControl(Me.components)
        Me.DefaultToolTipController1 = New DevExpress.Utils.DefaultToolTipController(Me.components)
        Me.BackgroundWorker_CheckInternet = New System.ComponentModel.BackgroundWorker()
        Me.Timer_ChcekInternet = New System.Windows.Forms.Timer(Me.components)
        Me.Timer_DateTime = New System.Windows.Forms.Timer(Me.components)
        Me.BarManager1 = New DevExpress.XtraBars.BarManager(Me.components)
        Me.barDockControlTop = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlBottom = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlLeft = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlRight = New DevExpress.XtraBars.BarDockControl()
        Me.BarButtonItem3 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem4 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem5 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem6 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem7 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem8 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem9 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem10 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem11 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem12 = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem13 = New DevExpress.XtraBars.BarButtonItem()
        Me.Email_Extractor_Pro = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem14 = New DevExpress.XtraBars.BarButtonItem()
        Me.EmailExtractor = New DevExpress.XtraBars.BarButtonItem()
        Me.BarButtonItem15 = New DevExpress.XtraBars.BarButtonItem()
        Me.RadialMenu1 = New DevExpress.XtraBars.Ribbon.RadialMenu(Me.components)
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.Background_CheckSystemDate = New System.ComponentModel.BackgroundWorker()
        Me.BackGround_CheckActivationStatus = New System.ComponentModel.BackgroundWorker()
        Me.Timer_CheckActivationStatus = New System.Windows.Forms.Timer(Me.components)
        Me.Timer_CheckRemainTrail = New System.Windows.Forms.Timer(Me.components)
        Me.Timer_CheckPermissionUpdate = New System.Windows.Forms.Timer(Me.components)
        Me.Background_checkPermissionUpdate = New System.ComponentModel.BackgroundWorker()
        Me.ToastNotificationsManager1 = New DevExpress.XtraBars.ToastNotifications.ToastNotificationsManager(Me.components)
        Me.Timer2 = New System.Windows.Forms.Timer(Me.components)
        Me.TrmUpdteChecker = New System.Windows.Forms.Timer(Me.components)
        Me.BKCheckVersion = New System.ComponentModel.BackgroundWorker()
        Me.TrmNotificationIcon = New System.Windows.Forms.Timer(Me.components)
        Me.Timer4 = New System.Windows.Forms.Timer(Me.components)
        Me.RibbonPage12 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPage14 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPage16 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPage18 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        Me.RibbonPage20 = New DevExpress.XtraBars.Ribbon.RibbonPage()
        CType(Me.RibbonControl, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DocumentManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.NoDocumentsView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TabbedView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RadialMenu1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ToastNotificationsManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'RibbonControl
        '
        Me.RibbonControl.CaptionBarItemLinks.Add(Me.BarStaticItem6)
        Me.RibbonControl.CaptionBarItemLinks.Add(Me.BarSubItem6)
        Me.RibbonControl.CaptionBarItemLinks.Add(Me.INTERNETCONNECTION)
        Me.RibbonControl.EmptyAreaImageOptions.ImagePadding = New System.Windows.Forms.Padding(35, 42, 35, 42)
        Me.RibbonControl.ExpandCollapseItem.Id = 0
        Me.RibbonControl.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.BarStaticItem6, Me.BarSubItem6, Me.INTERNETCONNECTION, Me.RibbonControl.ExpandCollapseItem, Me.BarButtonItem1, Me.BarButtonItem16, Me.lblTrail, Me.LBLACTIVATIONOWNER, Me.BarButtonItem19, Me.UserInfo, Me.BntEmailScanner_StartScan, Me.BntEmailScanner_StopScan, Me.BntEmailScanner_SaveList, Me.BarButtonItem17, Me.BntEmailScanner_OpenForm, Me.BarSubItem1, Me.BarButtonItem18, Me.BarSubItem2, Me.BntTlg, Me.BntSignal, Me.BntDiscord, Me.TLGCh, Me.BntYouTube, Me.BarSubItem3, Me.bntAddletter, Me.Bnt_Clear_Letter, Me.btnEdit, Me.Bnt_Inbox_Spam, Me.BarSubItem4, Me.BarButtonItem26, Me.bnt_ClearMailList, Me.Bnt_Save_AllSettings, Me.BarSubItem5, Me.Bnt_Add_Acc_SMTP, Me.Bnt_Clear_List_SMTP, Me.Bnt_Tag, Me.Bnt_Reset, Me.Bnt_Paste_Letter, Me.BntUploadeSMTP, Me.BarSubItem7, Me.BarSubItem8, Me.BarButtonItem20, Me.BarSubItem9, Me.BarButtonItem21, Me.BarButtonItem22, Me.BarButtonItem23, Me.BarButtonItem24, Me.BarButtonItem25, Me.BarButtonItem27, Me.BarButtonItem28, Me.BntRedirectWithPass, Me.RedirectEncodedLinkSpider, Me.BarButtonItem31, Me.BarButtonItem32, Me.BntCloudFlar, Me.BntGoogleCaptsha, Me.BarButtonItem35, Me.BntRedirectPuzzel, Me.BntRedirectVIP, Me.BarButtonItem38, Me.BarSubItem10, Me.BarButtonItem39, Me.BarButtonItem40, Me.BarButtonItem42, Me.BarButtonItem43, Me.BarButtonItem45, Me.BarButtonItem46, Me.BarButtonItem48, Me.BarSubItem12, Me.BntSMTP, Me.BarButtonItem52, Me.BarButtonItem2, Me.BntStartOlderHost, Me.BntClear, Me.Bnt_get_Example, Me.Bnt_Copy_List, Me.BntSaveSMTPList, Me.Bnt_Sender_Start, Me.BarButtonItem51, Me.BarButtonItem53, Me.BarButtonItem54, Me.BarButtonItem55, Me.BarButtonItem56, Me.BarButtonItem57, Me.BarButtonItem58, Me.BarButtonItem59, Me.BarButtonItem60, Me.BarButtonItem61, Me.btnSelectFile, Me.btnStart, Me.btnStop, Me.BntSave, Me.BarButtonItem64, Me.BntRest, Me.ResetAll, Me.BntDeleteDuplicatemail, Me.Select_File_text_Email, Me.Start_Delet_Mail, Me.StopProcessing, Me.Clear_AllMail, Me.BarButtonItem62, Me.BntUploadeList, Me.BntStart, Me.BntSaveList, Me.BarButtonItem67, Me.BarButtonItem68, Me.Bnt_Select_PHP, Me.BntResetPHP, Me.Bnt_php_obfuscation, Me.SavePHPFile, Me.CopyPHP, Me.Bnt_Paste, Me.BarButtonItem69, Me.BntGetLogo, Me.BntStartRedirect, Me.BarButtonItem70, Me.BarButtonItem71, Me.BarButtonItem72, Me.BarButtonItem73, Me.BarStaticItem1, Me.BarButtonItem74, Me.BarButtonItem75, Me.SkinBarSubItem1, Me.BarButtonItem77, Me.BarButtonItem78, Me.BarButtonItem79, Me.BarButtonItem81, Me.BarSubItem11, Me.BarSubItem13, Me.BarSubItem14, Me.BarButtonItem47, Me.BntIMAP, Me.BarButtonItem49, Me.BarButtonItem76, Me.lblDateTime, Me.BarButtonItem80, Me.BarButtonItem82, Me.lblpublicIP, Me.BntRedirectMicrosoft, Me.BarButtonItem29, Me.BarButtonItem30, Me.BarButtonItem33, Me.BarButtonItem34, Me.BntStop})
        Me.RibbonControl.Location = New System.Drawing.Point(0, 0)
        Me.RibbonControl.Margin = New System.Windows.Forms.Padding(4)
        Me.RibbonControl.MaxItemId = 212
        Me.RibbonControl.Name = "RibbonControl"
        Me.RibbonControl.OptionsMenuMinWidth = 385
        Me.RibbonControl.PageHeaderItemLinks.Add(Me.LBLACTIVATIONOWNER)
        Me.RibbonControl.PageHeaderItemLinks.Add(Me.UserInfo)
        Me.RibbonControl.PageHeaderItemLinks.Add(Me.BarSubItem1)
        Me.RibbonControl.Pages.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPage() {Me.RibbonPage1, Me.RibbonPage2, Me.RibbonPage7, Me.RibbonPage3, Me.RibbonPage17, Me.RibbonPage6, Me.RibbonPageExtractDomain, Me.RibbonPageCleanMail, Me.RibbonPageZeroPass, Me.RibbonPagePHP, Me.RibbonPageCloudFlar})
        Me.RibbonControl.QuickToolbarItemLinks.Add(Me.lblTrail)
        Me.RibbonControl.QuickToolbarItemLinks.Add(Me.BarStaticItem1)
        Me.RibbonControl.Size = New System.Drawing.Size(1284, 221)
        Me.RibbonControl.StatusBar = Me.RibbonStatusBar
        '
        'BarStaticItem6
        '
        Me.BarStaticItem6.Caption = "What New"
        Me.BarStaticItem6.Id = 21
        Me.BarStaticItem6.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.New32x32
        Me.BarStaticItem6.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarStaticItem6.ItemAppearance.Normal.Options.UseFont = True
        Me.BarStaticItem6.Name = "BarStaticItem6"
        Me.BarStaticItem6.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph
        '
        'BarSubItem6
        '
        Me.BarSubItem6.Caption = "BarSubItem6"
        Me.BarSubItem6.Id = 72
        Me.BarSubItem6.ImageOptions.DisabledImage = Global.Best_Sender.My.Resources.Resources.EmailValidation32x32
        Me.BarSubItem6.ImageOptions.DisabledLargeImage = Global.Best_Sender.My.Resources.Resources.EmailValidation32x32
        Me.BarSubItem6.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Menu32x32
        Me.BarSubItem6.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.EmailValidation32x32
        Me.BarSubItem6.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BarSubItem9), New DevExpress.XtraBars.LinkPersistInfo(Me.BarSubItem10), New DevExpress.XtraBars.LinkPersistInfo(Me.BarSubItem14), New DevExpress.XtraBars.LinkPersistInfo(Me.BarSubItem8), New DevExpress.XtraBars.LinkPersistInfo(Me.BarSubItem7), New DevExpress.XtraBars.LinkPersistInfo(Me.BarSubItem12), New DevExpress.XtraBars.LinkPersistInfo(Me.BntIMAP)})
        Me.BarSubItem6.Name = "BarSubItem6"
        '
        'BarSubItem9
        '
        Me.BarSubItem9.Caption = "Letter"
        Me.BarSubItem9.Id = 79
        Me.BarSubItem9.ImageOptions.DisabledImage = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarSubItem9.ImageOptions.DisabledLargeImage = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarSubItem9.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarSubItem9.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarSubItem9.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem9.ItemAppearance.Normal.Options.UseFont = True
        Me.BarSubItem9.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem25), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem24), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem21), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem27), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem74), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem75), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem76)})
        Me.BarSubItem9.Name = "BarSubItem9"
        '
        'BarButtonItem25
        '
        Me.BarButtonItem25.Caption = "Ghost Letter Encoded"
        Me.BarButtonItem25.Id = 84
        Me.BarButtonItem25.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarButtonItem25.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem25.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem25.Name = "BarButtonItem25"
        '
        'BarButtonItem24
        '
        Me.BarButtonItem24.Caption = "Base64 Letter Encoded"
        Me.BarButtonItem24.Id = 83
        Me.BarButtonItem24.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarButtonItem24.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem24.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem24.Name = "BarButtonItem24"
        '
        'BarButtonItem21
        '
        Me.BarButtonItem21.Caption = "Venom Letter Encoded"
        Me.BarButtonItem21.Id = 80
        Me.BarButtonItem21.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarButtonItem21.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem21.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem21.Name = "BarButtonItem21"
        '
        'BarButtonItem27
        '
        Me.BarButtonItem27.Caption = "Letter Maker "
        Me.BarButtonItem27.Id = 85
        Me.BarButtonItem27.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarButtonItem27.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem27.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem27.Name = "BarButtonItem27"
        '
        'BarButtonItem74
        '
        Me.BarButtonItem74.Caption = "Dark Letter Encoded"
        Me.BarButtonItem74.Id = 178
        Me.BarButtonItem74.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarButtonItem74.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem74.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem74.Name = "BarButtonItem74"
        '
        'BarButtonItem75
        '
        Me.BarButtonItem75.Caption = "Dragon Letter"
        Me.BarButtonItem75.Id = 179
        Me.BarButtonItem75.ImageOptions.DisabledLargeImage = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarButtonItem75.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarButtonItem75.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem75.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem75.Name = "BarButtonItem75"
        '
        'BarButtonItem76
        '
        Me.BarButtonItem76.Caption = "VIP Letter Encoded"
        Me.BarButtonItem76.Id = 195
        Me.BarButtonItem76.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarButtonItem76.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem76.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem76.Name = "BarButtonItem76"
        '
        'BarSubItem10
        '
        Me.BarSubItem10.Caption = "Email"
        Me.BarSubItem10.Id = 98
        Me.BarSubItem10.ImageOptions.DisabledImage = Global.Best_Sender.My.Resources.Resources.EmailExtractor32x32
        Me.BarSubItem10.ImageOptions.DisabledLargeImage = Global.Best_Sender.My.Resources.Resources.EmailExtractor32x32
        Me.BarSubItem10.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Emailimage
        Me.BarSubItem10.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.EmailExtractor32x32
        Me.BarSubItem10.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem10.ItemAppearance.Normal.Options.UseFont = True
        Me.BarSubItem10.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem46), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem39), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem43), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem40), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem42), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem81)})
        Me.BarSubItem10.Name = "BarSubItem10"
        '
        'BarButtonItem46
        '
        Me.BarButtonItem46.Caption = "Email From Scanner"
        Me.BarButtonItem46.Id = 106
        Me.BarButtonItem46.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EmailFromScanner32x32
        Me.BarButtonItem46.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem46.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem46.Name = "BarButtonItem46"
        '
        'BarButtonItem39
        '
        Me.BarButtonItem39.Caption = "Clean Mail"
        Me.BarButtonItem39.Id = 99
        Me.BarButtonItem39.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.DeleteDuplicatemail32x32
        Me.BarButtonItem39.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem39.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem39.Name = "BarButtonItem39"
        '
        'BarButtonItem43
        '
        Me.BarButtonItem43.Caption = "Email Sorter"
        Me.BarButtonItem43.Id = 103
        Me.BarButtonItem43.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EmailSorter32x32
        Me.BarButtonItem43.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem43.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem43.Name = "BarButtonItem43"
        '
        'BarButtonItem40
        '
        Me.BarButtonItem40.Caption = "Zero Pass"
        Me.BarButtonItem40.Id = 100
        Me.BarButtonItem40.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.RemovePassFromEmail32x32
        Me.BarButtonItem40.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem40.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem40.Name = "BarButtonItem40"
        '
        'BarButtonItem42
        '
        Me.BarButtonItem42.Caption = "Email Extractor"
        Me.BarButtonItem42.Id = 102
        Me.BarButtonItem42.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EmailExtractor32x32
        Me.BarButtonItem42.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem42.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem42.Name = "BarButtonItem42"
        '
        'BarButtonItem81
        '
        Me.BarButtonItem81.Caption = "MailCatcher"
        Me.BarButtonItem81.Id = 186
        Me.BarButtonItem81.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EmailExtractor32x32
        Me.BarButtonItem81.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem81.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem81.Name = "BarButtonItem81"
        '
        'BarSubItem14
        '
        Me.BarSubItem14.Caption = "Validation"
        Me.BarSubItem14.Id = 190
        Me.BarSubItem14.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EmailValidation32x32
        Me.BarSubItem14.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem14.ItemAppearance.Normal.Options.UseFont = True
        Me.BarSubItem14.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem47)})
        Me.BarSubItem14.Name = "BarSubItem14"
        '
        'BarButtonItem47
        '
        Me.BarButtonItem47.Caption = "Office365"
        Me.BarButtonItem47.Id = 191
        Me.BarButtonItem47.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.MS_Office_365
        Me.BarButtonItem47.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem47.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem47.Name = "BarButtonItem47"
        '
        'BarSubItem8
        '
        Me.BarSubItem8.Caption = "Redirect"
        Me.BarSubItem8.Id = 77
        Me.BarSubItem8.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Redirect32x32
        Me.BarSubItem8.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem8.ItemAppearance.Normal.Options.UseFont = True
        Me.BarSubItem8.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BntRedirectWithPass), New DevExpress.XtraBars.LinkPersistInfo(Me.RedirectEncodedLinkSpider), New DevExpress.XtraBars.LinkPersistInfo(Me.BntCloudFlar), New DevExpress.XtraBars.LinkPersistInfo(Me.BntGoogleCaptsha), New DevExpress.XtraBars.LinkPersistInfo(Me.BntRedirectPuzzel), New DevExpress.XtraBars.LinkPersistInfo(Me.BntRedirectVIP), New DevExpress.XtraBars.LinkPersistInfo(Me.BntRedirectMicrosoft), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem29), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem30)})
        Me.BarSubItem8.Name = "BarSubItem8"
        '
        'BntRedirectWithPass
        '
        Me.BntRedirectWithPass.Caption = "With Pass"
        Me.BntRedirectWithPass.Id = 87
        Me.BntRedirectWithPass.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.withPass32x32
        Me.BntRedirectWithPass.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntRedirectWithPass.ItemAppearance.Normal.Options.UseFont = True
        Me.BntRedirectWithPass.Name = "BntRedirectWithPass"
        '
        'RedirectEncodedLinkSpider
        '
        Me.RedirectEncodedLinkSpider.Caption = " Encoded Link Spider"
        Me.RedirectEncodedLinkSpider.Id = 88
        Me.RedirectEncodedLinkSpider.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.SpiderRedirect32x32
        Me.RedirectEncodedLinkSpider.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.RedirectEncodedLinkSpider.ItemAppearance.Normal.Options.UseFont = True
        Me.RedirectEncodedLinkSpider.Name = "RedirectEncodedLinkSpider"
        '
        'BntCloudFlar
        '
        Me.BntCloudFlar.Caption = "CloudFlar"
        Me.BntCloudFlar.Id = 91
        Me.BntCloudFlar.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.cloudflare32x32
        Me.BntCloudFlar.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntCloudFlar.ItemAppearance.Normal.Options.UseFont = True
        Me.BntCloudFlar.Name = "BntCloudFlar"
        '
        'BntGoogleCaptsha
        '
        Me.BntGoogleCaptsha.Caption = " Google Captcha"
        Me.BntGoogleCaptsha.Id = 92
        Me.BntGoogleCaptsha.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.googlecaptcha32x32
        Me.BntGoogleCaptsha.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntGoogleCaptsha.ItemAppearance.Normal.Options.UseFont = True
        Me.BntGoogleCaptsha.Name = "BntGoogleCaptsha"
        '
        'BntRedirectPuzzel
        '
        Me.BntRedirectPuzzel.Caption = "Puzzle"
        Me.BntRedirectPuzzel.Id = 94
        Me.BntRedirectPuzzel.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Puzzle32x32
        Me.BntRedirectPuzzel.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntRedirectPuzzel.ItemAppearance.Normal.Options.UseFont = True
        Me.BntRedirectPuzzel.Name = "BntRedirectPuzzel"
        '
        'BntRedirectVIP
        '
        Me.BntRedirectVIP.Caption = "VIP"
        Me.BntRedirectVIP.Id = 95
        Me.BntRedirectVIP.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.VIPRedirect32x32
        Me.BntRedirectVIP.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntRedirectVIP.ItemAppearance.Normal.Options.UseFont = True
        Me.BntRedirectVIP.Name = "BntRedirectVIP"
        '
        'BntRedirectMicrosoft
        '
        Me.BntRedirectMicrosoft.Caption = "Microsoft"
        Me.BntRedirectMicrosoft.Id = 205
        Me.BntRedirectMicrosoft.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.microsoft_32x32
        Me.BntRedirectMicrosoft.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntRedirectMicrosoft.ItemAppearance.Normal.Options.UseFont = True
        Me.BntRedirectMicrosoft.Name = "BntRedirectMicrosoft"
        '
        'BarButtonItem29
        '
        Me.BarButtonItem29.Caption = "Pro Skip / Anti-Bot"
        Me.BarButtonItem29.Id = 206
        Me.BarButtonItem29.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.bot32x32
        Me.BarButtonItem29.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem29.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem29.Name = "BarButtonItem29"
        '
        'BarButtonItem30
        '
        Me.BarButtonItem30.Caption = "Link / Hide"
        Me.BarButtonItem30.Id = 207
        Me.BarButtonItem30.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.HideRedirect32x32
        Me.BarButtonItem30.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem30.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem30.Name = "BarButtonItem30"
        '
        'BarSubItem7
        '
        Me.BarSubItem7.Caption = "Link"
        Me.BarSubItem7.Id = 76
        Me.BarSubItem7.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.LinkEN32x32
        Me.BarSubItem7.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem7.ItemAppearance.Normal.Options.UseFont = True
        Me.BarSubItem7.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem28), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem31), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem48)})
        Me.BarSubItem7.Name = "BarSubItem7"
        '
        'BarButtonItem28
        '
        Me.BarButtonItem28.Caption = "Wolf Encoded Link"
        Me.BarButtonItem28.Id = 86
        Me.BarButtonItem28.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Encoded_Link32x32
        Me.BarButtonItem28.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem28.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem28.Name = "BarButtonItem28"
        '
        'BarButtonItem31
        '
        Me.BarButtonItem31.Caption = "Hide Link"
        Me.BarButtonItem31.Id = 89
        Me.BarButtonItem31.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.HideLink32x32
        Me.BarButtonItem31.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem31.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem31.Name = "BarButtonItem31"
        '
        'BarButtonItem48
        '
        Me.BarButtonItem48.Caption = "Link To Attachment"
        Me.BarButtonItem48.Id = 108
        Me.BarButtonItem48.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.ConvertLinkToAttachment32x32
        Me.BarButtonItem48.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem48.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem48.Name = "BarButtonItem48"
        '
        'BarSubItem12
        '
        Me.BarSubItem12.Caption = "SMTP"
        Me.BarSubItem12.Id = 115
        Me.BarSubItem12.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.SMTPTester32x32
        Me.BarSubItem12.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.smtptestacc
        Me.BarSubItem12.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem12.ItemAppearance.Normal.Options.UseFont = True
        Me.BarSubItem12.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BntSMTP), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem52), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem72)})
        Me.BarSubItem12.Name = "BarSubItem12"
        '
        'BntSMTP
        '
        Me.BntSMTP.Caption = "SMTP Tester"
        Me.BntSMTP.Id = 116
        Me.BntSMTP.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.SMTPTester32x32
        Me.BntSMTP.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntSMTP.ItemAppearance.Normal.Options.UseFont = True
        Me.BntSMTP.Name = "BntSMTP"
        '
        'BarButtonItem52
        '
        Me.BarButtonItem52.Caption = "SMTP older"
        Me.BarButtonItem52.Id = 117
        Me.BarButtonItem52.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.smtpServerOne
        Me.BarButtonItem52.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem52.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem52.Name = "BarButtonItem52"
        '
        'BarButtonItem72
        '
        Me.BarButtonItem72.Caption = "Test account SMTP"
        Me.BarButtonItem72.Id = 175
        Me.BarButtonItem72.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.smtptestaccount32x32
        Me.BarButtonItem72.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.smtptestaccount32x32
        Me.BarButtonItem72.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem72.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem72.Name = "BarButtonItem72"
        '
        'BntIMAP
        '
        Me.BntIMAP.Caption = "IMAP"
        Me.BntIMAP.Id = 193
        Me.BntIMAP.ImageOptions.Image = CType(resources.GetObject("BntIMAP.ImageOptions.Image"), System.Drawing.Image)
        Me.BntIMAP.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntIMAP.ItemAppearance.Normal.Options.UseFont = True
        Me.BntIMAP.Name = "BntIMAP"
        Me.BntIMAP.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'INTERNETCONNECTION
        '
        Me.INTERNETCONNECTION.Id = 112
        Me.INTERNETCONNECTION.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.pending16x16
        Me.INTERNETCONNECTION.Name = "INTERNETCONNECTION"
        Me.INTERNETCONNECTION.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph
        '
        'BarButtonItem1
        '
        Me.BarButtonItem1.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem1.Caption = "تطبيق تصميم Binance"
        Me.BarButtonItem1.Id = 1
        Me.BarButtonItem1.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem1.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem1.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem1.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem1.Name = "BarButtonItem1"
        Me.BarButtonItem1.VisibleInSearchMenu = False
        '
        'BarButtonItem16
        '
        Me.BarButtonItem16.Caption = "Open Sender"
        Me.BarButtonItem16.Id = 3
        Me.BarButtonItem16.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.BSSenderImage
        Me.BarButtonItem16.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.BSSenderImage
        Me.BarButtonItem16.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem16.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem16.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem16.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem16.Name = "BarButtonItem16"
        '
        'lblTrail
        '
        Me.lblTrail.Caption = "www.BestSenderVIP.com"
        Me.lblTrail.Id = 4
        Me.lblTrail.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.security_key1
        Me.lblTrail.ItemAppearance.Hovered.ForeColor = System.Drawing.Color.FromArgb(CType(CType(239, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.lblTrail.ItemAppearance.Hovered.Options.UseForeColor = True
        Me.lblTrail.ItemAppearance.Normal.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.lblTrail.ItemAppearance.Normal.Options.UseForeColor = True
        Me.lblTrail.ItemAppearance.Pressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.lblTrail.ItemAppearance.Pressed.Options.UseBackColor = True
        Me.lblTrail.Name = "lblTrail"
        Me.lblTrail.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph
        '
        'LBLACTIVATIONOWNER
        '
        Me.LBLACTIVATIONOWNER.Caption = "Name Client"
        Me.LBLACTIVATIONOWNER.Id = 17
        Me.LBLACTIVATIONOWNER.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.employee_16x16
        Me.LBLACTIVATIONOWNER.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.employee_32x32
        Me.LBLACTIVATIONOWNER.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.LBLACTIVATIONOWNER.ItemAppearance.Normal.Options.UseFont = True
        Me.LBLACTIVATIONOWNER.Name = "LBLACTIVATIONOWNER"
        Me.LBLACTIVATIONOWNER.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph
        '
        'BarButtonItem19
        '
        Me.BarButtonItem19.Caption = "Restart"
        Me.BarButtonItem19.Id = 24
        Me.BarButtonItem19.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.support_16x16
        Me.BarButtonItem19.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.support_32x32
        Me.BarButtonItem19.Name = "BarButtonItem19"
        '
        'UserInfo
        '
        Me.UserInfo.Caption = "BarButtonItem21"
        Me.UserInfo.Id = 28
        Me.UserInfo.ImageOptions.DisabledImage = CType(resources.GetObject("UserInfo.ImageOptions.DisabledImage"), System.Drawing.Image)
        Me.UserInfo.ImageOptions.DisabledLargeImage = CType(resources.GetObject("UserInfo.ImageOptions.DisabledLargeImage"), System.Drawing.Image)
        Me.UserInfo.ImageOptions.Image = CType(resources.GetObject("UserInfo.ImageOptions.Image"), System.Drawing.Image)
        Me.UserInfo.Name = "UserInfo"
        '
        'BntEmailScanner_StartScan
        '
        Me.BntEmailScanner_StartScan.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BntEmailScanner_StartScan.Caption = "Start Send"
        Me.BntEmailScanner_StartScan.Id = 38
        Me.BntEmailScanner_StartScan.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image
        Me.BntEmailScanner_StartScan.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Start_Image
        Me.BntEmailScanner_StartScan.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntEmailScanner_StartScan.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntEmailScanner_StartScan.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntEmailScanner_StartScan.ItemAppearance.Normal.Options.UseFont = True
        Me.BntEmailScanner_StartScan.Name = "BntEmailScanner_StartScan"
        '
        'BntEmailScanner_StopScan
        '
        Me.BntEmailScanner_StopScan.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BntEmailScanner_StopScan.Caption = "Stop"
        Me.BntEmailScanner_StopScan.Id = 39
        Me.BntEmailScanner_StopScan.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Stop_Image
        Me.BntEmailScanner_StopScan.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntEmailScanner_StopScan.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntEmailScanner_StopScan.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntEmailScanner_StopScan.ItemAppearance.Normal.Options.UseFont = True
        Me.BntEmailScanner_StopScan.Name = "BntEmailScanner_StopScan"
        '
        'BntEmailScanner_SaveList
        '
        Me.BntEmailScanner_SaveList.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BntEmailScanner_SaveList.Caption = "Save"
        Me.BntEmailScanner_SaveList.Id = 40
        Me.BntEmailScanner_SaveList.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.BntEmailScanner_SaveList.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntEmailScanner_SaveList.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntEmailScanner_SaveList.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntEmailScanner_SaveList.ItemAppearance.Normal.Options.UseFont = True
        Me.BntEmailScanner_SaveList.Name = "BntEmailScanner_SaveList"
        '
        'BarButtonItem17
        '
        Me.BarButtonItem17.Caption = "Encoded Attachment"
        Me.BarButtonItem17.Id = 42
        Me.BarButtonItem17.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.attachments
        Me.BarButtonItem17.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem17.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem17.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem17.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem17.Name = "BarButtonItem17"
        '
        'BntEmailScanner_OpenForm
        '
        Me.BntEmailScanner_OpenForm.Caption = "Open Email From Scanner"
        Me.BntEmailScanner_OpenForm.Id = 43
        Me.BntEmailScanner_OpenForm.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.ScannerImage
        Me.BntEmailScanner_OpenForm.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.ScannerImage
        Me.BntEmailScanner_OpenForm.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntEmailScanner_OpenForm.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntEmailScanner_OpenForm.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntEmailScanner_OpenForm.ItemAppearance.Normal.Options.UseFont = True
        Me.BntEmailScanner_OpenForm.Name = "BntEmailScanner_OpenForm"
        '
        'BarSubItem1
        '
        Me.BarSubItem1.Caption = "Settings"
        Me.BarSubItem1.Id = 44
        Me.BarSubItem1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Menu
        Me.BarSubItem1.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem18), New DevExpress.XtraBars.LinkPersistInfo(Me.BarSubItem2), New DevExpress.XtraBars.LinkPersistInfo(Me.SkinBarSubItem1)})
        Me.BarSubItem1.Name = "BarSubItem1"
        '
        'BarButtonItem18
        '
        Me.BarButtonItem18.Caption = "Restart Sender"
        Me.BarButtonItem18.Id = 45
        Me.BarButtonItem18.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Reset_Image
        Me.BarButtonItem18.ItemAppearance.Disabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BarButtonItem18.ItemAppearance.Disabled.Options.UseFont = True
        Me.BarButtonItem18.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem18.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem18.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem18.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem18.ItemAppearance.Pressed.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.249999!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BarButtonItem18.ItemAppearance.Pressed.Options.UseFont = True
        Me.BarButtonItem18.Name = "BarButtonItem18"
        '
        'BarSubItem2
        '
        Me.BarSubItem2.Caption = "Support"
        Me.BarSubItem2.Id = 46
        Me.BarSubItem2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.SupportImage
        Me.BarSubItem2.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarSubItem2.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarSubItem2.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem2.ItemAppearance.Normal.Options.UseFont = True
        Me.BarSubItem2.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BntSignal), New DevExpress.XtraBars.LinkPersistInfo(Me.BntDiscord), New DevExpress.XtraBars.LinkPersistInfo(Me.BntTlg), New DevExpress.XtraBars.LinkPersistInfo(Me.TLGCh), New DevExpress.XtraBars.LinkPersistInfo(Me.BntYouTube)})
        Me.BarSubItem2.Name = "BarSubItem2"
        '
        'BntSignal
        '
        Me.BntSignal.Caption = "Signal"
        Me.BntSignal.Id = 48
        Me.BntSignal.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.signal
        Me.BntSignal.Name = "BntSignal"
        '
        'BntDiscord
        '
        Me.BntDiscord.Caption = "Discord"
        Me.BntDiscord.Id = 49
        Me.BntDiscord.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.discordImage
        Me.BntDiscord.Name = "BntDiscord"
        '
        'BntTlg
        '
        Me.BntTlg.Caption = "Telegram"
        Me.BntTlg.Id = 47
        Me.BntTlg.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.TelegramImage
        Me.BntTlg.Name = "BntTlg"
        '
        'TLGCh
        '
        Me.TLGCh.Caption = "Telegram Channel"
        Me.TLGCh.Id = 50
        Me.TLGCh.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.TelegramImage
        Me.TLGCh.Name = "TLGCh"
        '
        'BntYouTube
        '
        Me.BntYouTube.Caption = "YouTube"
        Me.BntYouTube.Id = 51
        Me.BntYouTube.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.youtube_16x16
        Me.BntYouTube.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.youtube_32x32
        Me.BntYouTube.Name = "BntYouTube"
        '
        'SkinBarSubItem1
        '
        Me.SkinBarSubItem1.AllowSerializeChildren = DevExpress.Utils.DefaultBoolean.[False]
        Me.SkinBarSubItem1.Caption = "SkinBarSubItem1"
        Me.SkinBarSubItem1.Id = 181
        Me.SkinBarSubItem1.Name = "SkinBarSubItem1"
        '
        'BarSubItem3
        '
        Me.BarSubItem3.Caption = "Letter"
        Me.BarSubItem3.Id = 53
        Me.BarSubItem3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.LetterSettings32x32
        Me.BarSubItem3.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.LetterSettings32x32
        Me.BarSubItem3.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarSubItem3.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarSubItem3.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem3.ItemAppearance.Normal.Options.UseFont = True
        Me.BarSubItem3.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.bntAddletter), New DevExpress.XtraBars.LinkPersistInfo(Me.Bnt_Clear_Letter), New DevExpress.XtraBars.LinkPersistInfo(Me.btnEdit), New DevExpress.XtraBars.LinkPersistInfo(Me.Bnt_Inbox_Spam), New DevExpress.XtraBars.LinkPersistInfo(Me.Bnt_Paste_Letter)})
        Me.BarSubItem3.Name = "BarSubItem3"
        '
        'bntAddletter
        '
        Me.bntAddletter.Caption = "Select Letter"
        Me.bntAddletter.Id = 54
        Me.bntAddletter.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.snapinsertfooter
        Me.bntAddletter.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.bntAddletter.ItemAppearance.Hovered.Options.UseFont = True
        Me.bntAddletter.Name = "bntAddletter"
        '
        'Bnt_Clear_Letter
        '
        Me.Bnt_Clear_Letter.Caption = "Clear Letter"
        Me.Bnt_Clear_Letter.Id = 55
        Me.Bnt_Clear_Letter.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.removeheader
        Me.Bnt_Clear_Letter.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Clear_Letter.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Clear_Letter.Name = "Bnt_Clear_Letter"
        '
        'btnEdit
        '
        Me.btnEdit.Caption = "Edit Letter"
        Me.btnEdit.Id = 56
        Me.btnEdit.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.edittask_16x16
        Me.btnEdit.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.edittask_32x32
        Me.btnEdit.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.btnEdit.ItemAppearance.Hovered.Options.UseFont = True
        Me.btnEdit.Name = "btnEdit"
        '
        'Bnt_Inbox_Spam
        '
        Me.Bnt_Inbox_Spam.Caption = "Check Letter inbox / Spam"
        Me.Bnt_Inbox_Spam.Id = 57
        Me.Bnt_Inbox_Spam.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.inbox_16x16
        Me.Bnt_Inbox_Spam.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.inbox_32x32
        Me.Bnt_Inbox_Spam.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Inbox_Spam.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Inbox_Spam.Name = "Bnt_Inbox_Spam"
        '
        'Bnt_Paste_Letter
        '
        Me.Bnt_Paste_Letter.Caption = "Paste Letter"
        Me.Bnt_Paste_Letter.Id = 69
        Me.Bnt_Paste_Letter.ImageOptions.Image = CType(resources.GetObject("Bnt_Paste_Letter.ImageOptions.Image"), System.Drawing.Image)
        Me.Bnt_Paste_Letter.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Paste_Letter.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Paste_Letter.Name = "Bnt_Paste_Letter"
        '
        'BarSubItem4
        '
        Me.BarSubItem4.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarSubItem4.Caption = "Email"
        Me.BarSubItem4.Id = 60
        Me.BarSubItem4.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.mailmerge
        Me.BarSubItem4.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarSubItem4.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarSubItem4.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem4.ItemAppearance.Normal.Options.UseFont = True
        Me.BarSubItem4.ItemInMenuAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem4.ItemInMenuAppearance.Normal.Options.UseFont = True
        Me.BarSubItem4.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem26), New DevExpress.XtraBars.LinkPersistInfo(Me.bnt_ClearMailList)})
        Me.BarSubItem4.Name = "BarSubItem4"
        '
        'BarButtonItem26
        '
        Me.BarButtonItem26.Caption = "Add List Email"
        Me.BarButtonItem26.Id = 61
        Me.BarButtonItem26.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.usergroup_16x16
        Me.BarButtonItem26.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.usergroup_32x32
        Me.BarButtonItem26.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem26.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem26.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem26.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem26.Name = "BarButtonItem26"
        '
        'bnt_ClearMailList
        '
        Me.bnt_ClearMailList.Caption = "Clear List Email"
        Me.bnt_ClearMailList.Id = 62
        Me.bnt_ClearMailList.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_16x164
        Me.bnt_ClearMailList.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.trash_32x321
        Me.bnt_ClearMailList.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.bnt_ClearMailList.ItemAppearance.Hovered.Options.UseFont = True
        Me.bnt_ClearMailList.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.bnt_ClearMailList.ItemAppearance.Normal.Options.UseFont = True
        Me.bnt_ClearMailList.Name = "bnt_ClearMailList"
        '
        'Bnt_Save_AllSettings
        '
        Me.Bnt_Save_AllSettings.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.Bnt_Save_AllSettings.Caption = "Save All Settings"
        Me.Bnt_Save_AllSettings.Id = 63
        Me.Bnt_Save_AllSettings.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.Bnt_Save_AllSettings.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.Bnt_Save_AllSettings.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Save_AllSettings.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Save_AllSettings.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_Save_AllSettings.ItemAppearance.Normal.Options.UseFont = True
        Me.Bnt_Save_AllSettings.Name = "Bnt_Save_AllSettings"
        '
        'BarSubItem5
        '
        Me.BarSubItem5.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarSubItem5.Caption = "SMTP"
        Me.BarSubItem5.Id = 64
        Me.BarSubItem5.ImageOptions.DisabledLargeImage = Global.Best_Sender.My.Resources.Resources.smtpServerOne
        Me.BarSubItem5.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.smtpServerOne
        Me.BarSubItem5.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.smtpServerOne
        Me.BarSubItem5.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarSubItem5.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarSubItem5.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem5.ItemAppearance.Normal.Options.UseFont = True
        Me.BarSubItem5.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.Bnt_Add_Acc_SMTP), New DevExpress.XtraBars.LinkPersistInfo(Me.Bnt_Clear_List_SMTP)})
        Me.BarSubItem5.Name = "BarSubItem5"
        '
        'Bnt_Add_Acc_SMTP
        '
        Me.Bnt_Add_Acc_SMTP.Caption = "Add Account SMTP"
        Me.Bnt_Add_Acc_SMTP.Id = 65
        Me.Bnt_Add_Acc_SMTP.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.SMTPTester16x16
        Me.Bnt_Add_Acc_SMTP.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Add_Acc_SMTP.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Add_Acc_SMTP.Name = "Bnt_Add_Acc_SMTP"
        '
        'Bnt_Clear_List_SMTP
        '
        Me.Bnt_Clear_List_SMTP.Caption = "Clear List SMTP"
        Me.Bnt_Clear_List_SMTP.Id = 66
        Me.Bnt_Clear_List_SMTP.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.Bnt_Clear_List_SMTP.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Clear_List_SMTP.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Clear_List_SMTP.Name = "Bnt_Clear_List_SMTP"
        '
        'Bnt_Tag
        '
        Me.Bnt_Tag.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.Bnt_Tag.Caption = "Tag Email"
        Me.Bnt_Tag.Id = 67
        Me.Bnt_Tag.ImageOptions.Image = CType(resources.GetObject("Bnt_Tag.ImageOptions.Image"), System.Drawing.Image)
        Me.Bnt_Tag.ImageOptions.LargeImage = CType(resources.GetObject("Bnt_Tag.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.Bnt_Tag.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Tag.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Tag.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_Tag.ItemAppearance.Normal.Options.UseFont = True
        Me.Bnt_Tag.Name = "Bnt_Tag"
        '
        'Bnt_Reset
        '
        Me.Bnt_Reset.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.Bnt_Reset.Caption = "Reset Settings"
        Me.Bnt_Reset.Id = 68
        Me.Bnt_Reset.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.Bnt_Reset.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Reset.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Reset.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_Reset.ItemAppearance.Normal.Options.UseFont = True
        Me.Bnt_Reset.Name = "Bnt_Reset"
        '
        'BntUploadeSMTP
        '
        Me.BntUploadeSMTP.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BntUploadeSMTP.Caption = "Upload SMTP"
        Me.BntUploadeSMTP.Id = 71
        Me.BntUploadeSMTP.ImageOptions.SvgImage = Global.Best_Sender.My.Resources.Resources.snapinsertheader2
        Me.BntUploadeSMTP.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntUploadeSMTP.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntUploadeSMTP.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntUploadeSMTP.ItemAppearance.Normal.Options.UseFont = True
        Me.BntUploadeSMTP.Name = "BntUploadeSMTP"
        '
        'BarButtonItem20
        '
        Me.BarButtonItem20.Caption = "Encoded Link"
        Me.BarButtonItem20.Id = 78
        Me.BarButtonItem20.Name = "BarButtonItem20"
        '
        'BarButtonItem22
        '
        Me.BarButtonItem22.Caption = "Dragon Letter Encoded"
        Me.BarButtonItem22.Id = 81
        Me.BarButtonItem22.Name = "BarButtonItem22"
        '
        'BarButtonItem23
        '
        Me.BarButtonItem23.Caption = "Dark Letter Encoded"
        Me.BarButtonItem23.Id = 82
        Me.BarButtonItem23.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedLetter32x32
        Me.BarButtonItem23.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem23.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem23.Name = "BarButtonItem23"
        '
        'BarButtonItem32
        '
        Me.BarButtonItem32.Caption = "Redirect Pro "
        Me.BarButtonItem32.Id = 90
        Me.BarButtonItem32.Name = "BarButtonItem32"
        '
        'BarButtonItem35
        '
        Me.BarButtonItem35.Caption = "Microsoft Redirect Link"
        Me.BarButtonItem35.Id = 93
        Me.BarButtonItem35.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem35.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem35.Name = "BarButtonItem35"
        '
        'BarButtonItem38
        '
        Me.BarButtonItem38.Caption = "DVL-777 Redirect"
        Me.BarButtonItem38.Id = 96
        Me.BarButtonItem38.Name = "BarButtonItem38"
        '
        'BarButtonItem45
        '
        Me.BarButtonItem45.Caption = "Email Extractor Pro"
        Me.BarButtonItem45.Id = 105
        Me.BarButtonItem45.Name = "BarButtonItem45"
        '
        'BarButtonItem2
        '
        Me.BarButtonItem2.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem2.Caption = "Add List SMTP"
        Me.BarButtonItem2.Id = 119
        Me.BarButtonItem2.ImageOptions.DisabledImage = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BarButtonItem2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BarButtonItem2.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BarButtonItem2.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem2.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem2.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem2.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem2.Name = "BarButtonItem2"
        '
        'BntStartOlderHost
        '
        Me.BntStartOlderHost.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BntStartOlderHost.Caption = "Start"
        Me.BntStartOlderHost.Id = 120
        Me.BntStartOlderHost.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Start_Image
        Me.BntStartOlderHost.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntStartOlderHost.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntStartOlderHost.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntStartOlderHost.ItemAppearance.Normal.Options.UseFont = True
        Me.BntStartOlderHost.Name = "BntStartOlderHost"
        '
        'BntClear
        '
        Me.BntClear.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BntClear.Caption = "Clear"
        Me.BntClear.Id = 122
        Me.BntClear.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntClear.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntClear.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntClear.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntClear.ItemAppearance.Normal.Options.UseFont = True
        Me.BntClear.Name = "BntClear"
        '
        'Bnt_get_Example
        '
        Me.Bnt_get_Example.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.Bnt_get_Example.Caption = "I want an Example"
        Me.Bnt_get_Example.Id = 123
        Me.Bnt_get_Example.ImageOptions.Image = CType(resources.GetObject("Bnt_get_Example.ImageOptions.Image"), System.Drawing.Image)
        Me.Bnt_get_Example.ImageOptions.LargeImage = CType(resources.GetObject("Bnt_get_Example.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.Bnt_get_Example.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_get_Example.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_get_Example.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_get_Example.ItemAppearance.Normal.Options.UseFont = True
        Me.Bnt_get_Example.Name = "Bnt_get_Example"
        '
        'Bnt_Copy_List
        '
        Me.Bnt_Copy_List.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.Bnt_Copy_List.Caption = "Copy"
        Me.Bnt_Copy_List.Id = 124
        Me.Bnt_Copy_List.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.copy32x32
        Me.Bnt_Copy_List.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.copy32x32
        Me.Bnt_Copy_List.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Copy_List.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Copy_List.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_Copy_List.ItemAppearance.Normal.Options.UseFont = True
        Me.Bnt_Copy_List.Name = "Bnt_Copy_List"
        '
        'BntSaveSMTPList
        '
        Me.BntSaveSMTPList.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BntSaveSMTPList.Caption = "Save"
        Me.BntSaveSMTPList.Id = 125
        Me.BntSaveSMTPList.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.BntSaveSMTPList.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntSaveSMTPList.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntSaveSMTPList.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntSaveSMTPList.ItemAppearance.Normal.Options.UseFont = True
        Me.BntSaveSMTPList.Name = "BntSaveSMTPList"
        '
        'Bnt_Sender_Start
        '
        Me.Bnt_Sender_Start.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.Bnt_Sender_Start.Caption = "Start Send"
        Me.Bnt_Sender_Start.Id = 126
        Me.Bnt_Sender_Start.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Start_Image
        Me.Bnt_Sender_Start.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Sender_Start.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Sender_Start.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_Sender_Start.ItemAppearance.Normal.Options.UseFont = True
        Me.Bnt_Sender_Start.Name = "Bnt_Sender_Start"
        '
        'BarButtonItem51
        '
        Me.BarButtonItem51.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem51.Caption = "Open SMTP Older"
        Me.BarButtonItem51.Id = 127
        Me.BarButtonItem51.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.SMTP32x32
        Me.BarButtonItem51.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem51.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem51.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem51.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem51.Name = "BarButtonItem51"
        '
        'BarButtonItem53
        '
        Me.BarButtonItem53.Caption = "Open SMTP Older"
        Me.BarButtonItem53.Id = 129
        Me.BarButtonItem53.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.smtpServerOne
        Me.BarButtonItem53.Name = "BarButtonItem53"
        '
        'BarButtonItem54
        '
        Me.BarButtonItem54.Caption = "SMTP Tester"
        Me.BarButtonItem54.Id = 130
        Me.BarButtonItem54.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.SMTPTester32x32
        Me.BarButtonItem54.Name = "BarButtonItem54"
        '
        'BarButtonItem55
        '
        Me.BarButtonItem55.Caption = "Open Inbox Phantom "
        Me.BarButtonItem55.Id = 136
        Me.BarButtonItem55.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.inbox32x32
        Me.BarButtonItem55.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.inbox32x32
        Me.BarButtonItem55.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem55.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem55.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem55.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem55.Name = "BarButtonItem55"
        '
        'BarButtonItem56
        '
        Me.BarButtonItem56.Caption = "Uploade HTML"
        Me.BarButtonItem56.Id = 137
        Me.BarButtonItem56.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Letter32x32
        Me.BarButtonItem56.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem56.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem56.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem56.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem56.Name = "BarButtonItem56"
        '
        'BarButtonItem57
        '
        Me.BarButtonItem57.Caption = "Start"
        Me.BarButtonItem57.Id = 138
        Me.BarButtonItem57.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Start_Image
        Me.BarButtonItem57.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem57.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem57.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem57.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem57.Name = "BarButtonItem57"
        '
        'BarButtonItem58
        '
        Me.BarButtonItem58.Caption = "Send To Sender"
        Me.BarButtonItem58.Id = 139
        Me.BarButtonItem58.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Transfer32x32
        Me.BarButtonItem58.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem58.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem58.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem58.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem58.Name = "BarButtonItem58"
        '
        'BarButtonItem59
        '
        Me.BarButtonItem59.Caption = "Tag"
        Me.BarButtonItem59.Id = 140
        Me.BarButtonItem59.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Emailimage
        Me.BarButtonItem59.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem59.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem59.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem59.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem59.Name = "BarButtonItem59"
        '
        'BarButtonItem60
        '
        Me.BarButtonItem60.Caption = "CLear"
        Me.BarButtonItem60.Id = 141
        Me.BarButtonItem60.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BarButtonItem60.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem60.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem60.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem60.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem60.Name = "BarButtonItem60"
        '
        'BarButtonItem61
        '
        Me.BarButtonItem61.Caption = "Change Text"
        Me.BarButtonItem61.Id = 142
        Me.BarButtonItem61.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.ChangeText32x32
        Me.BarButtonItem61.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem61.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem61.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem61.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem61.Name = "BarButtonItem61"
        '
        'btnSelectFile
        '
        Me.btnSelectFile.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.btnSelectFile.Caption = "Add Email List"
        Me.btnSelectFile.Id = 143
        Me.btnSelectFile.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.btnSelectFile.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.btnSelectFile.ItemAppearance.Hovered.Options.UseFont = True
        Me.btnSelectFile.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.btnSelectFile.ItemAppearance.Normal.Options.UseFont = True
        Me.btnSelectFile.Name = "btnSelectFile"
        '
        'btnStart
        '
        Me.btnStart.Caption = "Start"
        Me.btnStart.Id = 144
        Me.btnStart.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Start_Image
        Me.btnStart.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.btnStart.ItemAppearance.Hovered.Options.UseFont = True
        Me.btnStart.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.btnStart.ItemAppearance.Normal.Options.UseFont = True
        Me.btnStart.Name = "btnStart"
        '
        'btnStop
        '
        Me.btnStop.Caption = "Stop"
        Me.btnStop.Id = 145
        Me.btnStop.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.btnStop.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.btnStop.ItemAppearance.Hovered.Options.UseFont = True
        Me.btnStop.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.btnStop.ItemAppearance.Normal.Options.UseFont = True
        Me.btnStop.Name = "btnStop"
        '
        'BntSave
        '
        Me.BntSave.Caption = "Save"
        Me.BntSave.Id = 147
        Me.BntSave.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.BntSave.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntSave.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntSave.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntSave.ItemAppearance.Normal.Options.UseFont = True
        Me.BntSave.Name = "BntSave"
        '
        'BarButtonItem64
        '
        Me.BarButtonItem64.Caption = "Open Extract Domain"
        Me.BarButtonItem64.Id = 148
        Me.BarButtonItem64.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.ExtractDomain32x32
        Me.BarButtonItem64.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem64.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem64.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem64.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem64.Name = "BarButtonItem64"
        '
        'BntRest
        '
        Me.BntRest.Caption = "Reset All"
        Me.BntRest.Id = 149
        Me.BntRest.ImageOptions.DisabledImage = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntRest.ImageOptions.DisabledLargeImage = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntRest.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntRest.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntRest.Name = "BntRest"
        Me.BntRest.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph
        '
        'ResetAll
        '
        Me.ResetAll.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.ResetAll.Caption = "Reset All"
        Me.ResetAll.Id = 150
        Me.ResetAll.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.ResetAll.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.ResetAll.ItemAppearance.Hovered.Options.UseFont = True
        Me.ResetAll.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ResetAll.ItemAppearance.Normal.Options.UseFont = True
        Me.ResetAll.Name = "ResetAll"
        '
        'BntDeleteDuplicatemail
        '
        Me.BntDeleteDuplicatemail.Caption = "Open Clean Mail"
        Me.BntDeleteDuplicatemail.Id = 151
        Me.BntDeleteDuplicatemail.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.DeleteDuplicatemail32x32
        Me.BntDeleteDuplicatemail.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntDeleteDuplicatemail.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntDeleteDuplicatemail.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntDeleteDuplicatemail.ItemAppearance.Normal.Options.UseFont = True
        Me.BntDeleteDuplicatemail.Name = "BntDeleteDuplicatemail"
        '
        'Select_File_text_Email
        '
        Me.Select_File_text_Email.Caption = "Select Email List"
        Me.Select_File_text_Email.Id = 152
        Me.Select_File_text_Email.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.Select_File_text_Email.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Select_File_text_Email.ItemAppearance.Hovered.Options.UseFont = True
        Me.Select_File_text_Email.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Select_File_text_Email.ItemAppearance.Normal.Options.UseFont = True
        Me.Select_File_text_Email.Name = "Select_File_text_Email"
        '
        'Start_Delet_Mail
        '
        Me.Start_Delet_Mail.Caption = "Start"
        Me.Start_Delet_Mail.Id = 153
        Me.Start_Delet_Mail.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Start_Image
        Me.Start_Delet_Mail.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Start_Delet_Mail.ItemAppearance.Hovered.Options.UseFont = True
        Me.Start_Delet_Mail.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Start_Delet_Mail.ItemAppearance.Normal.Options.UseFont = True
        Me.Start_Delet_Mail.Name = "Start_Delet_Mail"
        '
        'StopProcessing
        '
        Me.StopProcessing.Caption = "Stop"
        Me.StopProcessing.Id = 154
        Me.StopProcessing.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.StopProcessing.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.StopProcessing.ItemAppearance.Hovered.Options.UseFont = True
        Me.StopProcessing.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.StopProcessing.ItemAppearance.Normal.Options.UseFont = True
        Me.StopProcessing.Name = "StopProcessing"
        '
        'Clear_AllMail
        '
        Me.Clear_AllMail.Caption = "Reset All"
        Me.Clear_AllMail.Id = 155
        Me.Clear_AllMail.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Reset_Image
        Me.Clear_AllMail.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Clear_AllMail.ItemAppearance.Hovered.Options.UseFont = True
        Me.Clear_AllMail.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Clear_AllMail.ItemAppearance.Normal.Options.UseFont = True
        Me.Clear_AllMail.Name = "Clear_AllMail"
        '
        'BarButtonItem62
        '
        Me.BarButtonItem62.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem62.Caption = "Open ZeroPass "
        Me.BarButtonItem62.Id = 157
        Me.BarButtonItem62.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.RemovePassFromEmail32x32
        Me.BarButtonItem62.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.RemovePassFromEmail32x32
        Me.BarButtonItem62.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem62.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem62.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem62.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem62.Name = "BarButtonItem62"
        '
        'BntUploadeList
        '
        Me.BntUploadeList.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BntUploadeList.Caption = "Add Email+Pass List"
        Me.BntUploadeList.Id = 158
        Me.BntUploadeList.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BntUploadeList.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntUploadeList.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntUploadeList.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntUploadeList.ItemAppearance.Normal.Options.UseFont = True
        Me.BntUploadeList.Name = "BntUploadeList"
        '
        'BntStart
        '
        Me.BntStart.Caption = "Start"
        Me.BntStart.Id = 159
        Me.BntStart.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Start_Image
        Me.BntStart.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntStart.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntStart.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntStart.ItemAppearance.Normal.Options.UseFont = True
        Me.BntStart.Name = "BntStart"
        '
        'BntSaveList
        '
        Me.BntSaveList.Caption = "Save"
        Me.BntSaveList.Id = 160
        Me.BntSaveList.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.BntSaveList.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntSaveList.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntSaveList.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntSaveList.ItemAppearance.Normal.Options.UseFont = True
        Me.BntSaveList.Name = "BntSaveList"
        '
        'BarButtonItem67
        '
        Me.BarButtonItem67.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem67.Caption = "Reset All"
        Me.BarButtonItem67.Id = 161
        Me.BarButtonItem67.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BarButtonItem67.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem67.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem67.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem67.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem67.Name = "BarButtonItem67"
        '
        'BarButtonItem68
        '
        Me.BarButtonItem68.Caption = "Open PHP Obfuscation"
        Me.BarButtonItem68.Id = 162
        Me.BarButtonItem68.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.phpLaft32x32
        Me.BarButtonItem68.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem68.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem68.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem68.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem68.Name = "BarButtonItem68"
        '
        'Bnt_Select_PHP
        '
        Me.Bnt_Select_PHP.Caption = "Add File PHP"
        Me.Bnt_Select_PHP.Id = 163
        Me.Bnt_Select_PHP.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.Bnt_Select_PHP.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Select_PHP.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Select_PHP.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_Select_PHP.ItemAppearance.Normal.Options.UseFont = True
        Me.Bnt_Select_PHP.Name = "Bnt_Select_PHP"
        '
        'BntResetPHP
        '
        Me.BntResetPHP.Caption = "Reset All"
        Me.BntResetPHP.Id = 164
        Me.BntResetPHP.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Reset_Image
        Me.BntResetPHP.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntResetPHP.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntResetPHP.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntResetPHP.ItemAppearance.Normal.Options.UseFont = True
        Me.BntResetPHP.Name = "BntResetPHP"
        '
        'Bnt_php_obfuscation
        '
        Me.Bnt_php_obfuscation.Caption = "Create php obfuscation"
        Me.Bnt_php_obfuscation.Id = 165
        Me.Bnt_php_obfuscation.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.LetterTools32x32
        Me.Bnt_php_obfuscation.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_php_obfuscation.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_php_obfuscation.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_php_obfuscation.ItemAppearance.Normal.Options.UseFont = True
        Me.Bnt_php_obfuscation.Name = "Bnt_php_obfuscation"
        '
        'SavePHPFile
        '
        Me.SavePHPFile.Caption = "Save"
        Me.SavePHPFile.Id = 166
        Me.SavePHPFile.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.SavePHPFile.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.SavePHPFile.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.SavePHPFile.ItemAppearance.Hovered.Options.UseFont = True
        Me.SavePHPFile.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.SavePHPFile.ItemAppearance.Normal.Options.UseFont = True
        Me.SavePHPFile.Name = "SavePHPFile"
        '
        'CopyPHP
        '
        Me.CopyPHP.Caption = "Copy"
        Me.CopyPHP.Id = 167
        Me.CopyPHP.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.copy32x32
        Me.CopyPHP.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.CopyPHP.ItemAppearance.Hovered.Options.UseFont = True
        Me.CopyPHP.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.CopyPHP.ItemAppearance.Normal.Options.UseFont = True
        Me.CopyPHP.Name = "CopyPHP"
        '
        'Bnt_Paste
        '
        Me.Bnt_Paste.Caption = "Paste"
        Me.Bnt_Paste.Id = 168
        Me.Bnt_Paste.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.paste_32x32
        Me.Bnt_Paste.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_Paste.ItemAppearance.Hovered.Options.UseFont = True
        Me.Bnt_Paste.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Bnt_Paste.ItemAppearance.Normal.Options.UseFont = True
        Me.Bnt_Paste.Name = "Bnt_Paste"
        '
        'BarButtonItem69
        '
        Me.BarButtonItem69.Caption = "Open Redirect CloudFlar"
        Me.BarButtonItem69.Id = 169
        Me.BarButtonItem69.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.open32x32
        Me.BarButtonItem69.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem69.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem69.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem69.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem69.Name = "BarButtonItem69"
        '
        'BntGetLogo
        '
        Me.BntGetLogo.Caption = "Get Logo"
        Me.BntGetLogo.Id = 170
        Me.BntGetLogo.ImageOptions.Image = CType(resources.GetObject("BntGetLogo.ImageOptions.Image"), System.Drawing.Image)
        Me.BntGetLogo.ImageOptions.LargeImage = CType(resources.GetObject("BntGetLogo.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BntGetLogo.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntGetLogo.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntGetLogo.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntGetLogo.ItemAppearance.Normal.Options.UseFont = True
        Me.BntGetLogo.Name = "BntGetLogo"
        '
        'BntStartRedirect
        '
        Me.BntStartRedirect.Caption = "Start"
        Me.BntStartRedirect.Id = 171
        Me.BntStartRedirect.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Start_Image
        Me.BntStartRedirect.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntStartRedirect.ItemAppearance.Hovered.Options.UseFont = True
        Me.BntStartRedirect.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntStartRedirect.ItemAppearance.Normal.Options.UseFont = True
        Me.BntStartRedirect.Name = "BntStartRedirect"
        '
        'BarButtonItem70
        '
        Me.BarButtonItem70.Caption = "Save"
        Me.BarButtonItem70.Id = 172
        Me.BarButtonItem70.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.settings32x32
        Me.BarButtonItem70.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem70.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem70.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem70.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem70.Name = "BarButtonItem70"
        '
        'BarButtonItem71
        '
        Me.BarButtonItem71.Caption = "Reset All"
        Me.BarButtonItem71.Id = 173
        Me.BarButtonItem71.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BarButtonItem71.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem71.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem71.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem71.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem71.Name = "BarButtonItem71"
        '
        'BarButtonItem73
        '
        Me.BarButtonItem73.Caption = "Image Resizer"
        Me.BarButtonItem73.Id = 176
        Me.BarButtonItem73.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Image32x32
        Me.BarButtonItem73.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Image32x32
        Me.BarButtonItem73.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem73.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem73.Name = "BarButtonItem73"
        '
        'BarStaticItem1
        '
        Me.BarStaticItem1.Caption = "Call Support"
        Me.BarStaticItem1.Id = 177
        Me.BarStaticItem1.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.SupportImage
        Me.BarStaticItem1.Name = "BarStaticItem1"
        Me.BarStaticItem1.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'BarButtonItem77
        '
        Me.BarButtonItem77.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem77.Caption = "Restart Sender"
        Me.BarButtonItem77.Id = 182
        Me.BarButtonItem77.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Restart32x32
        Me.BarButtonItem77.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Restart32x32
        Me.BarButtonItem77.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem77.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem77.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem77.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem77.Name = "BarButtonItem77"
        '
        'BarButtonItem78
        '
        Me.BarButtonItem78.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem78.Caption = "Dowenload AnyDesk"
        Me.BarButtonItem78.Id = 183
        Me.BarButtonItem78.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Anydesk32x32
        Me.BarButtonItem78.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem78.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem78.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem78.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem78.Name = "BarButtonItem78"
        '
        'BarButtonItem79
        '
        Me.BarButtonItem79.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem79.Caption = "Dowenload Rustdesk"
        Me.BarButtonItem79.Id = 184
        Me.BarButtonItem79.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.RustDesk32x32
        Me.BarButtonItem79.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem79.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem79.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem79.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem79.Name = "BarButtonItem79"
        '
        'BarSubItem11
        '
        Me.BarSubItem11.Caption = "Validation"
        Me.BarSubItem11.Id = 188
        Me.BarSubItem11.ImageOptions.DisabledLargeImage = Global.Best_Sender.My.Resources.Resources.EmailValidation32x32
        Me.BarSubItem11.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.EmailValidation32x32
        Me.BarSubItem11.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarSubItem11.ItemAppearance.Normal.Options.UseFont = True
        Me.BarSubItem11.Name = "BarSubItem11"
        '
        'BarSubItem13
        '
        Me.BarSubItem13.Caption = "BarSubItem13"
        Me.BarSubItem13.Id = 189
        Me.BarSubItem13.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EmailValidation32x32
        Me.BarSubItem13.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.EmailValidation32x32
        Me.BarSubItem13.Name = "BarSubItem13"
        '
        'BarButtonItem49
        '
        Me.BarButtonItem49.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem49.Caption = "Dowenload Net Framework"
        Me.BarButtonItem49.Id = 194
        Me.BarButtonItem49.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.net_framework32x32
        Me.BarButtonItem49.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem49.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem49.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem49.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem49.Name = "BarButtonItem49"
        '
        'lblDateTime
        '
        Me.lblDateTime.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right
        Me.lblDateTime.Id = 198
        Me.lblDateTime.Name = "lblDateTime"
        '
        'BarButtonItem80
        '
        Me.BarButtonItem80.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem80.Caption = "VIP Client with a Gold card"
        Me.BarButtonItem80.Id = 202
        Me.BarButtonItem80.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Premium
        Me.BarButtonItem80.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Premium
        Me.BarButtonItem80.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem80.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem80.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem80.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem80.Name = "BarButtonItem80"
        '
        'BarButtonItem82
        '
        Me.BarButtonItem82.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem82.Caption = "Client VIP"
        Me.BarButtonItem82.Id = 203
        Me.BarButtonItem82.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Customized
        Me.BarButtonItem82.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem82.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem82.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem82.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem82.Name = "BarButtonItem82"
        '
        'lblpublicIP
        '
        Me.lblpublicIP.Id = 204
        Me.lblpublicIP.Name = "lblpublicIP"
        '
        'BarButtonItem33
        '
        Me.BarButtonItem33.Caption = "Convert Link To Attachment"
        Me.BarButtonItem33.Id = 208
        Me.BarButtonItem33.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.ConvertLinkToAttachment32x32
        Me.BarButtonItem33.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.ConvertLinkToAttachment32x32
        Me.BarButtonItem33.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem33.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem33.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem33.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem33.Name = "BarButtonItem33"
        '
        'BarButtonItem34
        '
        Me.BarButtonItem34.AllowHtmlText = DevExpress.Utils.DefaultBoolean.[True]
        Me.BarButtonItem34.Caption = "Home Best Sender"
        Me.BarButtonItem34.Id = 209
        Me.BarButtonItem34.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.home_32x32
        Me.BarButtonItem34.ItemAppearance.Hovered.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BarButtonItem34.ItemAppearance.Hovered.Options.UseFont = True
        Me.BarButtonItem34.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BarButtonItem34.ItemAppearance.Normal.Options.UseFont = True
        Me.BarButtonItem34.Name = "BarButtonItem34"
        '
        'BntStop
        '
        Me.BntStop.Caption = "Stop"
        Me.BntStop.Id = 211
        Me.BntStop.ImageOptions.DisabledImage = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.BntStop.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.BntStop.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.BntStop.Name = "BntStop"
        '
        'RibbonPage1
        '
        Me.RibbonPage1.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup1, Me.RibbonPageGroup62, Me.RibbonPageGroup59, Me.RibbonPageGroup60, Me.RibbonPageGroup61, Me.RibbonPageGroup58, Me.RibbonPageGroupCliebtVIP, Me.RibbonPageGroupCLientNormal})
        Me.RibbonPage1.Name = "RibbonPage1"
        Me.RibbonPage1.Text = "Home"
        '
        'RibbonPageGroup1
        '
        Me.RibbonPageGroup1.AllowTextClipping = False
        Me.RibbonPageGroup1.ItemLinks.Add(Me.BarButtonItem1)
        Me.RibbonPageGroup1.Name = "RibbonPageGroup1"
        Me.RibbonPageGroup1.Text = " Home Settings"
        Me.RibbonPageGroup1.Visible = False
        '
        'RibbonPageGroup62
        '
        Me.RibbonPageGroup62.ItemLinks.Add(Me.BarButtonItem34)
        Me.RibbonPageGroup62.Name = "RibbonPageGroup62"
        '
        'RibbonPageGroup59
        '
        Me.RibbonPageGroup59.ItemLinks.Add(Me.BarButtonItem78)
        Me.RibbonPageGroup59.Name = "RibbonPageGroup59"
        '
        'RibbonPageGroup60
        '
        Me.RibbonPageGroup60.ItemLinks.Add(Me.BarButtonItem79)
        Me.RibbonPageGroup60.Name = "RibbonPageGroup60"
        '
        'RibbonPageGroup61
        '
        Me.RibbonPageGroup61.ItemLinks.Add(Me.BarButtonItem49)
        Me.RibbonPageGroup61.Name = "RibbonPageGroup61"
        '
        'RibbonPageGroup58
        '
        Me.RibbonPageGroup58.ItemLinks.Add(Me.BarButtonItem77)
        Me.RibbonPageGroup58.Name = "RibbonPageGroup58"
        '
        'RibbonPageGroupCliebtVIP
        '
        Me.RibbonPageGroupCliebtVIP.ItemLinks.Add(Me.BarButtonItem80)
        Me.RibbonPageGroupCliebtVIP.Name = "RibbonPageGroupCliebtVIP"
        '
        'RibbonPageGroupCLientNormal
        '
        Me.RibbonPageGroupCLientNormal.ItemLinks.Add(Me.BarButtonItem82)
        Me.RibbonPageGroupCLientNormal.Name = "RibbonPageGroupCLientNormal"
        Me.RibbonPageGroupCLientNormal.Text = ":( He does not have a VIP card"
        '
        'RibbonPage2
        '
        Me.RibbonPage2.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup3, Me.RibbonPageGroup12, Me.RibbonPageGroup4, Me.RibbonPageGroup14, Me.RibbonPageGroup24, Me.RibbonPageGroup13, Me.RibbonPageGroup16, Me.RibbonPageGroup15})
        Me.RibbonPage2.Name = "RibbonPage2"
        Me.RibbonPage2.Text = "Sender"
        '
        'RibbonPageGroup3
        '
        Me.RibbonPageGroup3.ItemLinks.Add(Me.BarButtonItem16)
        Me.RibbonPageGroup3.Name = "RibbonPageGroup3"
        '
        'RibbonPageGroup12
        '
        Me.RibbonPageGroup12.ItemLinks.Add(Me.BarSubItem4)
        Me.RibbonPageGroup12.Name = "RibbonPageGroup12"
        '
        'RibbonPageGroup4
        '
        Me.RibbonPageGroup4.ItemLinks.Add(Me.BarSubItem3)
        Me.RibbonPageGroup4.Name = "RibbonPageGroup4"
        '
        'RibbonPageGroup14
        '
        Me.RibbonPageGroup14.ItemLinks.Add(Me.BarSubItem5)
        Me.RibbonPageGroup14.Name = "RibbonPageGroup14"
        '
        'RibbonPageGroup24
        '
        Me.RibbonPageGroup24.ItemLinks.Add(Me.Bnt_Sender_Start)
        Me.RibbonPageGroup24.Name = "RibbonPageGroup24"
        '
        'RibbonPageGroup13
        '
        Me.RibbonPageGroup13.ItemLinks.Add(Me.Bnt_Save_AllSettings)
        Me.RibbonPageGroup13.Name = "RibbonPageGroup13"
        '
        'RibbonPageGroup16
        '
        Me.RibbonPageGroup16.ItemLinks.Add(Me.Bnt_Reset)
        Me.RibbonPageGroup16.Name = "RibbonPageGroup16"
        '
        'RibbonPageGroup15
        '
        Me.RibbonPageGroup15.ItemLinks.Add(Me.Bnt_Tag)
        Me.RibbonPageGroup15.Name = "RibbonPageGroup15"
        '
        'RibbonPage7
        '
        Me.RibbonPage7.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup25, Me.RibbonPageGroup26, Me.RibbonPageGroup27, Me.RibbonPageGroup28, Me.RibbonPageGroup29, Me.RibbonPageGroup30, Me.RibbonPageGroup31})
        Me.RibbonPage7.Name = "RibbonPage7"
        Me.RibbonPage7.Text = "Inbox Phantom "
        '
        'RibbonPageGroup25
        '
        Me.RibbonPageGroup25.ItemLinks.Add(Me.BarButtonItem55)
        Me.RibbonPageGroup25.Name = "RibbonPageGroup25"
        '
        'RibbonPageGroup26
        '
        Me.RibbonPageGroup26.ItemLinks.Add(Me.BarButtonItem56)
        Me.RibbonPageGroup26.Name = "RibbonPageGroup26"
        '
        'RibbonPageGroup27
        '
        Me.RibbonPageGroup27.ItemLinks.Add(Me.BarButtonItem57)
        Me.RibbonPageGroup27.Name = "RibbonPageGroup27"
        '
        'RibbonPageGroup28
        '
        Me.RibbonPageGroup28.ItemLinks.Add(Me.BarButtonItem58)
        Me.RibbonPageGroup28.Name = "RibbonPageGroup28"
        '
        'RibbonPageGroup29
        '
        Me.RibbonPageGroup29.ItemLinks.Add(Me.BarButtonItem59)
        Me.RibbonPageGroup29.Name = "RibbonPageGroup29"
        '
        'RibbonPageGroup30
        '
        Me.RibbonPageGroup30.ItemLinks.Add(Me.BarButtonItem60)
        Me.RibbonPageGroup30.Name = "RibbonPageGroup30"
        '
        'RibbonPageGroup31
        '
        Me.RibbonPageGroup31.ItemLinks.Add(Me.BarButtonItem61)
        Me.RibbonPageGroup31.Name = "RibbonPageGroup31"
        '
        'RibbonPage3
        '
        Me.RibbonPage3.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup10, Me.RibbonPageGroup17, Me.RibbonPageGroup5, Me.RibbonPageGroup6, Me.RibbonPageGroup8})
        Me.RibbonPage3.Name = "RibbonPage3"
        Me.RibbonPage3.Text = "Email Scanner"
        '
        'RibbonPageGroup10
        '
        Me.RibbonPageGroup10.ItemLinks.Add(Me.BntEmailScanner_OpenForm)
        Me.RibbonPageGroup10.Name = "RibbonPageGroup10"
        '
        'RibbonPageGroup17
        '
        Me.RibbonPageGroup17.ItemLinks.Add(Me.BntUploadeSMTP)
        Me.RibbonPageGroup17.Name = "RibbonPageGroup17"
        '
        'RibbonPageGroup5
        '
        Me.RibbonPageGroup5.ItemLinks.Add(Me.BntEmailScanner_StartScan)
        Me.RibbonPageGroup5.Name = "RibbonPageGroup5"
        '
        'RibbonPageGroup6
        '
        Me.RibbonPageGroup6.ItemLinks.Add(Me.BntEmailScanner_StopScan)
        Me.RibbonPageGroup6.Name = "RibbonPageGroup6"
        '
        'RibbonPageGroup8
        '
        Me.RibbonPageGroup8.ItemLinks.Add(Me.BntEmailScanner_SaveList)
        Me.RibbonPageGroup8.Name = "RibbonPageGroup8"
        '
        'RibbonPage17
        '
        Me.RibbonPage17.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup9, Me.RibbonPageGroup57})
        Me.RibbonPage17.Name = "RibbonPage17"
        Me.RibbonPage17.Text = "Attachment"
        '
        'RibbonPageGroup9
        '
        Me.RibbonPageGroup9.ItemLinks.Add(Me.BarButtonItem17)
        Me.RibbonPageGroup9.Name = "RibbonPageGroup9"
        '
        'RibbonPageGroup57
        '
        Me.RibbonPageGroup57.ItemLinks.Add(Me.BarButtonItem33)
        Me.RibbonPageGroup57.Name = "RibbonPageGroup57"
        '
        'RibbonPage6
        '
        Me.RibbonPage6.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup7, Me.RibbonPageGroup11, Me.RibbonPageGroup19, Me.RibbonPageGroup20, Me.RibbonPageGroup22, Me.RibbonPageGroup23, Me.RibbonPageGroup21})
        Me.RibbonPage6.Name = "RibbonPage6"
        Me.RibbonPage6.Text = "SMTP older"
        '
        'RibbonPageGroup7
        '
        Me.RibbonPageGroup7.ItemLinks.Add(Me.BarButtonItem51)
        Me.RibbonPageGroup7.Name = "RibbonPageGroup7"
        '
        'RibbonPageGroup11
        '
        Me.RibbonPageGroup11.AllowTextClipping = False
        Me.RibbonPageGroup11.ItemLinks.Add(Me.BarButtonItem2)
        Me.RibbonPageGroup11.Name = "RibbonPageGroup11"
        '
        'RibbonPageGroup19
        '
        Me.RibbonPageGroup19.ItemLinks.Add(Me.BntStartOlderHost)
        Me.RibbonPageGroup19.Name = "RibbonPageGroup19"
        '
        'RibbonPageGroup20
        '
        Me.RibbonPageGroup20.ItemLinks.Add(Me.BntClear)
        Me.RibbonPageGroup20.Name = "RibbonPageGroup20"
        '
        'RibbonPageGroup22
        '
        Me.RibbonPageGroup22.ItemLinks.Add(Me.Bnt_Copy_List)
        Me.RibbonPageGroup22.Name = "RibbonPageGroup22"
        '
        'RibbonPageGroup23
        '
        Me.RibbonPageGroup23.ItemLinks.Add(Me.BntSaveSMTPList)
        Me.RibbonPageGroup23.Name = "RibbonPageGroup23"
        '
        'RibbonPageGroup21
        '
        Me.RibbonPageGroup21.ItemLinks.Add(Me.Bnt_get_Example)
        Me.RibbonPageGroup21.Name = "RibbonPageGroup21"
        '
        'RibbonPageExtractDomain
        '
        Me.RibbonPageExtractDomain.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup33, Me.RibbonPageGroup2, Me.RibbonPageGroup18, Me.RibbonPageGroup32, Me.RibbonPageGroup34, Me.RibbonPageGroup36})
        Me.RibbonPageExtractDomain.Name = "RibbonPageExtractDomain"
        Me.RibbonPageExtractDomain.Text = "Extract Domain"
        '
        'RibbonPageGroup33
        '
        Me.RibbonPageGroup33.ItemLinks.Add(Me.BarButtonItem64)
        Me.RibbonPageGroup33.Name = "RibbonPageGroup33"
        '
        'RibbonPageGroup2
        '
        Me.RibbonPageGroup2.ItemLinks.Add(Me.btnSelectFile)
        Me.RibbonPageGroup2.Name = "RibbonPageGroup2"
        '
        'RibbonPageGroup18
        '
        Me.RibbonPageGroup18.ItemLinks.Add(Me.btnStart)
        Me.RibbonPageGroup18.Name = "RibbonPageGroup18"
        '
        'RibbonPageGroup32
        '
        Me.RibbonPageGroup32.ItemLinks.Add(Me.btnStop)
        Me.RibbonPageGroup32.Name = "RibbonPageGroup32"
        '
        'RibbonPageGroup34
        '
        Me.RibbonPageGroup34.ItemLinks.Add(Me.BntSave)
        Me.RibbonPageGroup34.Name = "RibbonPageGroup34"
        '
        'RibbonPageGroup36
        '
        Me.RibbonPageGroup36.ItemLinks.Add(Me.ResetAll)
        Me.RibbonPageGroup36.Name = "RibbonPageGroup36"
        '
        'RibbonPageCleanMail
        '
        Me.RibbonPageCleanMail.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup35, Me.RibbonPageGroup37, Me.RibbonPageGroup38, Me.RibbonPageGroup39, Me.RibbonPageGroup40})
        Me.RibbonPageCleanMail.Name = "RibbonPageCleanMail"
        Me.RibbonPageCleanMail.Text = "Clean Mail "
        '
        'RibbonPageGroup35
        '
        Me.RibbonPageGroup35.ItemLinks.Add(Me.BntDeleteDuplicatemail)
        Me.RibbonPageGroup35.Name = "RibbonPageGroup35"
        '
        'RibbonPageGroup37
        '
        Me.RibbonPageGroup37.ItemLinks.Add(Me.Select_File_text_Email)
        Me.RibbonPageGroup37.Name = "RibbonPageGroup37"
        '
        'RibbonPageGroup38
        '
        Me.RibbonPageGroup38.ItemLinks.Add(Me.Start_Delet_Mail)
        Me.RibbonPageGroup38.Name = "RibbonPageGroup38"
        '
        'RibbonPageGroup39
        '
        Me.RibbonPageGroup39.ItemLinks.Add(Me.StopProcessing)
        Me.RibbonPageGroup39.Name = "RibbonPageGroup39"
        '
        'RibbonPageGroup40
        '
        Me.RibbonPageGroup40.ItemLinks.Add(Me.Clear_AllMail)
        Me.RibbonPageGroup40.Name = "RibbonPageGroup40"
        '
        'RibbonPageZeroPass
        '
        Me.RibbonPageZeroPass.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup41, Me.RibbonPageGroup42, Me.RibbonPageGroup43, Me.RibbonPageGroup63, Me.RibbonPageGroup44, Me.RibbonPageGroup45})
        Me.RibbonPageZeroPass.Name = "RibbonPageZeroPass"
        Me.RibbonPageZeroPass.Text = "Zero Pass  "
        '
        'RibbonPageGroup41
        '
        Me.RibbonPageGroup41.ItemLinks.Add(Me.BarButtonItem62)
        Me.RibbonPageGroup41.Name = "RibbonPageGroup41"
        '
        'RibbonPageGroup42
        '
        Me.RibbonPageGroup42.ItemLinks.Add(Me.BntUploadeList)
        Me.RibbonPageGroup42.Name = "RibbonPageGroup42"
        '
        'RibbonPageGroup43
        '
        Me.RibbonPageGroup43.ItemLinks.Add(Me.BntStart)
        Me.RibbonPageGroup43.Name = "RibbonPageGroup43"
        '
        'RibbonPageGroup63
        '
        Me.RibbonPageGroup63.ItemLinks.Add(Me.BntStop)
        Me.RibbonPageGroup63.Name = "RibbonPageGroup63"
        '
        'RibbonPageGroup44
        '
        Me.RibbonPageGroup44.ItemLinks.Add(Me.BntSaveList)
        Me.RibbonPageGroup44.Name = "RibbonPageGroup44"
        '
        'RibbonPageGroup45
        '
        Me.RibbonPageGroup45.ItemLinks.Add(Me.BarButtonItem67)
        Me.RibbonPageGroup45.Name = "RibbonPageGroup45"
        '
        'RibbonPagePHP
        '
        Me.RibbonPagePHP.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup46, Me.BntSelectPHP, Me.RibbonPageGroup48, Me.RibbonPageGroup49, Me.RibbonPageGroup50, Me.RibbonPageGroup51, Me.RibbonPageGroup47})
        Me.RibbonPagePHP.Name = "RibbonPagePHP"
        Me.RibbonPagePHP.Text = "PHP"
        '
        'RibbonPageGroup46
        '
        Me.RibbonPageGroup46.ItemLinks.Add(Me.BarButtonItem68)
        Me.RibbonPageGroup46.Name = "RibbonPageGroup46"
        '
        'BntSelectPHP
        '
        Me.BntSelectPHP.ItemLinks.Add(Me.Bnt_Select_PHP)
        Me.BntSelectPHP.Name = "BntSelectPHP"
        '
        'RibbonPageGroup48
        '
        Me.RibbonPageGroup48.ItemLinks.Add(Me.Bnt_php_obfuscation)
        Me.RibbonPageGroup48.Name = "RibbonPageGroup48"
        '
        'RibbonPageGroup49
        '
        Me.RibbonPageGroup49.ItemLinks.Add(Me.SavePHPFile)
        Me.RibbonPageGroup49.Name = "RibbonPageGroup49"
        '
        'RibbonPageGroup50
        '
        Me.RibbonPageGroup50.ItemLinks.Add(Me.CopyPHP)
        Me.RibbonPageGroup50.Name = "RibbonPageGroup50"
        '
        'RibbonPageGroup51
        '
        Me.RibbonPageGroup51.ItemLinks.Add(Me.Bnt_Paste)
        Me.RibbonPageGroup51.Name = "RibbonPageGroup51"
        '
        'RibbonPageGroup47
        '
        Me.RibbonPageGroup47.ItemLinks.Add(Me.BntResetPHP)
        Me.RibbonPageGroup47.Name = "RibbonPageGroup47"
        '
        'RibbonPageCloudFlar
        '
        Me.RibbonPageCloudFlar.Groups.AddRange(New DevExpress.XtraBars.Ribbon.RibbonPageGroup() {Me.RibbonPageGroup52, Me.RibbonPageGroup53, Me.RibbonPageGroup54, Me.RibbonPageGroup55, Me.RibbonPageGroup56})
        Me.RibbonPageCloudFlar.Name = "RibbonPageCloudFlar"
        Me.RibbonPageCloudFlar.Text = "CloudFlar Redirect"
        Me.RibbonPageCloudFlar.Visible = False
        '
        'RibbonPageGroup52
        '
        Me.RibbonPageGroup52.ItemLinks.Add(Me.BarButtonItem69)
        Me.RibbonPageGroup52.Name = "RibbonPageGroup52"
        '
        'RibbonPageGroup53
        '
        Me.RibbonPageGroup53.ItemLinks.Add(Me.BntGetLogo)
        Me.RibbonPageGroup53.Name = "RibbonPageGroup53"
        '
        'RibbonPageGroup54
        '
        Me.RibbonPageGroup54.ItemLinks.Add(Me.BntStartRedirect)
        Me.RibbonPageGroup54.Name = "RibbonPageGroup54"
        '
        'RibbonPageGroup55
        '
        Me.RibbonPageGroup55.ItemLinks.Add(Me.BarButtonItem70)
        Me.RibbonPageGroup55.Name = "RibbonPageGroup55"
        '
        'RibbonPageGroup56
        '
        Me.RibbonPageGroup56.ItemLinks.Add(Me.BarButtonItem71)
        Me.RibbonPageGroup56.Name = "RibbonPageGroup56"
        '
        'RibbonStatusBar
        '
        Me.RibbonStatusBar.ItemLinks.Add(Me.notificationIocn)
        Me.RibbonStatusBar.ItemLinks.Add(Me.BarStaticItem2)
        Me.RibbonStatusBar.ItemLinks.Add(Me.lblCureentVersion)
        Me.RibbonStatusBar.ItemLinks.Add(Me.BarStaticItem3)
        Me.RibbonStatusBar.ItemLinks.Add(Me.lblpublicIP)
        Me.RibbonStatusBar.ItemLinks.Add(Me.BarStaticItem4)
        Me.RibbonStatusBar.ItemLinks.Add(Me.lblCity)
        Me.RibbonStatusBar.ItemLinks.Add(Me.BarStaticItem5)
        Me.RibbonStatusBar.ItemLinks.Add(Me.lblCountry)
        Me.RibbonStatusBar.ItemLinks.Add(Me.PictureBox1)
        Me.RibbonStatusBar.ItemLinks.Add(Me.lblDateTime)
        Me.RibbonStatusBar.Location = New System.Drawing.Point(0, 859)
        Me.RibbonStatusBar.Margin = New System.Windows.Forms.Padding(4)
        Me.RibbonStatusBar.Name = "RibbonStatusBar"
        Me.RibbonStatusBar.Ribbon = Me.RibbonControl
        Me.RibbonStatusBar.Size = New System.Drawing.Size(1284, 41)
        '
        'notificationIocn
        '
        Me.notificationIocn.Caption = "Please Update"
        Me.notificationIocn.Id = 33
        Me.notificationIocn.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.alarm
        Me.notificationIocn.ImageOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.alarm
        Me.notificationIocn.Name = "notificationIocn"
        Me.notificationIocn.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph
        Me.notificationIocn.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'BarStaticItem2
        '
        Me.BarStaticItem2.Caption = "Current version :"
        Me.BarStaticItem2.Id = 7
        Me.BarStaticItem2.Name = "BarStaticItem2"
        '
        'lblCureentVersion
        '
        Me.lblCureentVersion.Caption = "Version"
        Me.lblCureentVersion.Id = 8
        Me.lblCureentVersion.Name = "lblCureentVersion"
        '
        'BarStaticItem3
        '
        Me.BarStaticItem3.Caption = "Public IP :"
        Me.BarStaticItem3.Id = 9
        Me.BarStaticItem3.Name = "BarStaticItem3"
        '
        'BarStaticItem4
        '
        Me.BarStaticItem4.Caption = "City :"
        Me.BarStaticItem4.Id = 11
        Me.BarStaticItem4.Name = "BarStaticItem4"
        '
        'lblCity
        '
        Me.lblCity.Caption = "---------"
        Me.lblCity.Id = 12
        Me.lblCity.Name = "lblCity"
        '
        'BarStaticItem5
        '
        Me.BarStaticItem5.Caption = "Country : "
        Me.BarStaticItem5.Id = 13
        Me.BarStaticItem5.Name = "BarStaticItem5"
        '
        'lblCountry
        '
        Me.lblCountry.Caption = "---------"
        Me.lblCountry.Id = 14
        Me.lblCountry.Name = "lblCountry"
        '
        'PictureBox1
        '
        Me.PictureBox1.Caption = "Flag"
        Me.PictureBox1.Id = 15
        Me.PictureBox1.Name = "PictureBox1"
        '
        'DocumentManager1
        '
        Me.DocumentManager1.View = Me.NoDocumentsView1
        Me.DocumentManager1.ViewCollection.AddRange(New DevExpress.XtraBars.Docking2010.Views.BaseView() {Me.NoDocumentsView1, Me.TabbedView1})
        '
        'TabbedView1
        '
        Me.TabbedView1.BackgroundImageLayoutMode = DevExpress.Utils.Drawing.ImageLayoutMode.Squeeze
        '
        'DefaultLookAndFeel1
        '
        Me.DefaultLookAndFeel1.LookAndFeel.SkinName = "WXI"
        '
        'trmfadein
        '
        Me.trmfadein.Enabled = True
        Me.trmfadein.Interval = 1
        '
        'BunifuDragControl1
        '
        Me.BunifuDragControl1.Fixed = True
        Me.BunifuDragControl1.Horizontal = True
        Me.BunifuDragControl1.TargetControl = Nothing
        Me.BunifuDragControl1.Vertical = True
        '
        'DefaultToolTipController1
        '
        '
        '
        '
        Me.DefaultToolTipController1.DefaultController.KeepWhileHovered = True
        Me.DefaultToolTipController1.DefaultController.ToolTipType = DevExpress.Utils.ToolTipType.Flyout
        '
        'BackgroundWorker_CheckInternet
        '
        Me.BackgroundWorker_CheckInternet.WorkerReportsProgress = True
        Me.BackgroundWorker_CheckInternet.WorkerSupportsCancellation = True
        '
        'Timer_ChcekInternet
        '
        Me.Timer_ChcekInternet.Enabled = True
        Me.Timer_ChcekInternet.Interval = 5000
        '
        'Timer_DateTime
        '
        Me.Timer_DateTime.Enabled = True
        Me.Timer_DateTime.Interval = 1000
        '
        'BarManager1
        '
        Me.BarManager1.DockControls.Add(Me.barDockControlTop)
        Me.BarManager1.DockControls.Add(Me.barDockControlBottom)
        Me.BarManager1.DockControls.Add(Me.barDockControlLeft)
        Me.BarManager1.DockControls.Add(Me.barDockControlRight)
        Me.BarManager1.Form = Me
        Me.BarManager1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.BarButtonItem3, Me.BarButtonItem4, Me.BarButtonItem5, Me.BarButtonItem6, Me.BarButtonItem7, Me.BarButtonItem8, Me.BarButtonItem9, Me.BarButtonItem10, Me.BarButtonItem11, Me.BarButtonItem12, Me.BarButtonItem13, Me.Email_Extractor_Pro, Me.BarButtonItem14, Me.EmailExtractor, Me.BarButtonItem15})
        Me.BarManager1.MaxItemId = 15
        '
        'barDockControlTop
        '
        Me.barDockControlTop.CausesValidation = False
        Me.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top
        Me.barDockControlTop.Location = New System.Drawing.Point(0, 0)
        Me.barDockControlTop.Manager = Me.BarManager1
        Me.barDockControlTop.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.barDockControlTop.Size = New System.Drawing.Size(1284, 0)
        '
        'barDockControlBottom
        '
        Me.barDockControlBottom.CausesValidation = False
        Me.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.barDockControlBottom.Location = New System.Drawing.Point(0, 900)
        Me.barDockControlBottom.Manager = Me.BarManager1
        Me.barDockControlBottom.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.barDockControlBottom.Size = New System.Drawing.Size(1284, 0)
        '
        'barDockControlLeft
        '
        Me.barDockControlLeft.CausesValidation = False
        Me.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left
        Me.barDockControlLeft.Location = New System.Drawing.Point(0, 0)
        Me.barDockControlLeft.Manager = Me.BarManager1
        Me.barDockControlLeft.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.barDockControlLeft.Size = New System.Drawing.Size(0, 900)
        '
        'barDockControlRight
        '
        Me.barDockControlRight.CausesValidation = False
        Me.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right
        Me.barDockControlRight.Location = New System.Drawing.Point(1284, 0)
        Me.barDockControlRight.Manager = Me.BarManager1
        Me.barDockControlRight.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.barDockControlRight.Size = New System.Drawing.Size(0, 900)
        '
        'BarButtonItem3
        '
        Me.BarButtonItem3.Caption = "Email Extractor"
        Me.BarButtonItem3.Id = 0
        Me.BarButtonItem3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Mailextractpng
        Me.BarButtonItem3.Name = "BarButtonItem3"
        '
        'BarButtonItem4
        '
        Me.BarButtonItem4.Caption = "Email Sorter"
        Me.BarButtonItem4.Id = 1
        Me.BarButtonItem4.ImageOptions.Image = CType(resources.GetObject("BarButtonItem4.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem4.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem4.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem4.Name = "BarButtonItem4"
        '
        'BarButtonItem5
        '
        Me.BarButtonItem5.Caption = "Check SMTP"
        Me.BarButtonItem5.Id = 2
        Me.BarButtonItem5.ImageOptions.Image = CType(resources.GetObject("BarButtonItem5.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem5.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem5.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem5.Name = "BarButtonItem5"
        Me.BarButtonItem5.Visibility = DevExpress.XtraBars.BarItemVisibility.OnlyInCustomizing
        '
        'BarButtonItem6
        '
        Me.BarButtonItem6.Caption = "Email Vaildator"
        Me.BarButtonItem6.Id = 3
        Me.BarButtonItem6.ImageOptions.Image = CType(resources.GetObject("BarButtonItem6.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem6.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem6.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem6.Name = "BarButtonItem6"
        '
        'BarButtonItem7
        '
        Me.BarButtonItem7.Caption = "Email Scanner"
        Me.BarButtonItem7.Id = 4
        Me.BarButtonItem7.ImageOptions.Image = CType(resources.GetObject("BarButtonItem7.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem7.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem7.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem7.Name = "BarButtonItem7"
        '
        'BarButtonItem8
        '
        Me.BarButtonItem8.Caption = "Letter Tags"
        Me.BarButtonItem8.Id = 5
        Me.BarButtonItem8.ImageOptions.DisabledImage = CType(resources.GetObject("BarButtonItem8.ImageOptions.DisabledImage"), System.Drawing.Image)
        Me.BarButtonItem8.ImageOptions.Image = CType(resources.GetObject("BarButtonItem8.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem8.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem8.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem8.Name = "BarButtonItem8"
        Me.BarButtonItem8.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'BarButtonItem9
        '
        Me.BarButtonItem9.Caption = "Letter Designer"
        Me.BarButtonItem9.Id = 6
        Me.BarButtonItem9.ImageOptions.Image = CType(resources.GetObject("BarButtonItem9.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem9.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem9.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem9.Name = "BarButtonItem9"
        '
        'BarButtonItem10
        '
        Me.BarButtonItem10.Caption = "TAG"
        Me.BarButtonItem10.Id = 7
        Me.BarButtonItem10.ImageOptions.Image = CType(resources.GetObject("BarButtonItem10.ImageOptions.Image"), System.Drawing.Image)
        Me.BarButtonItem10.ImageOptions.LargeImage = CType(resources.GetObject("BarButtonItem10.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BarButtonItem10.Name = "BarButtonItem10"
        '
        'BarButtonItem11
        '
        Me.BarButtonItem11.Caption = "Ranking SMTP"
        Me.BarButtonItem11.Id = 8
        Me.BarButtonItem11.ImageOptions.SvgImage = CType(resources.GetObject("BarButtonItem11.ImageOptions.SvgImage"), DevExpress.Utils.Svg.SvgImage)
        Me.BarButtonItem11.Name = "BarButtonItem11"
        '
        'BarButtonItem12
        '
        Me.BarButtonItem12.Caption = "Email Sending format"
        Me.BarButtonItem12.Id = 9
        Me.BarButtonItem12.ImageOptions.SvgImage = CType(resources.GetObject("BarButtonItem12.ImageOptions.SvgImage"), DevExpress.Utils.Svg.SvgImage)
        Me.BarButtonItem12.Name = "BarButtonItem12"
        '
        'BarButtonItem13
        '
        Me.BarButtonItem13.Caption = "Office365"
        Me.BarButtonItem13.Id = 10
        Me.BarButtonItem13.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.MS_Office_365
        Me.BarButtonItem13.Name = "BarButtonItem13"
        '
        'Email_Extractor_Pro
        '
        Me.Email_Extractor_Pro.Caption = "Email Extractor Pro"
        Me.Email_Extractor_Pro.Id = 11
        Me.Email_Extractor_Pro.ImageOptions.Image = CType(resources.GetObject("Email_Extractor_Pro.ImageOptions.Image"), System.Drawing.Image)
        Me.Email_Extractor_Pro.ImageOptions.LargeImage = CType(resources.GetObject("Email_Extractor_Pro.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.Email_Extractor_Pro.Name = "Email_Extractor_Pro"
        '
        'BarButtonItem14
        '
        Me.BarButtonItem14.Caption = "BarButtonItem12"
        Me.BarButtonItem14.Id = 12
        Me.BarButtonItem14.Name = "BarButtonItem14"
        '
        'EmailExtractor
        '
        Me.EmailExtractor.Caption = "Email Extractor"
        Me.EmailExtractor.Id = 13
        Me.EmailExtractor.ImageOptions.SvgImage = CType(resources.GetObject("EmailExtractor.ImageOptions.SvgImage"), DevExpress.Utils.Svg.SvgImage)
        Me.EmailExtractor.Name = "EmailExtractor"
        '
        'BarButtonItem15
        '
        Me.BarButtonItem15.Caption = "Remove Pass"
        Me.BarButtonItem15.Id = 14
        Me.BarButtonItem15.Name = "BarButtonItem15"
        '
        'RadialMenu1
        '
        Me.RadialMenu1.AlphaChannel = CType(245, Byte)
        Me.RadialMenu1.ArcHoveredThickness = 2
        Me.RadialMenu1.AutoExpand = True
        Me.RadialMenu1.ButtonRadius = 30
        Me.RadialMenu1.Glyph = CType(resources.GetObject("RadialMenu1.Glyph"), System.Drawing.Image)
        Me.RadialMenu1.InnerRadius = 0
        Me.RadialMenu1.ItemAutoSize = DevExpress.XtraBars.Ribbon.RadialMenuItemAutoSize.Spring
        Me.RadialMenu1.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.EmailExtractor), New DevExpress.XtraBars.LinkPersistInfo(Me.BarButtonItem15)})
        Me.RadialMenu1.Manager = Me.BarManager1
        Me.RadialMenu1.Name = "RadialMenu1"
        '
        'Timer1
        '
        Me.Timer1.Interval = 10000
        '
        'Background_CheckSystemDate
        '
        Me.Background_CheckSystemDate.WorkerSupportsCancellation = True
        '
        'BackGround_CheckActivationStatus
        '
        Me.BackGround_CheckActivationStatus.WorkerSupportsCancellation = True
        '
        'Timer_CheckActivationStatus
        '
        Me.Timer_CheckActivationStatus.Interval = 1000
        '
        'Timer_CheckRemainTrail
        '
        Me.Timer_CheckRemainTrail.Interval = 1000
        '
        'Timer_CheckPermissionUpdate
        '
        Me.Timer_CheckPermissionUpdate.Interval = 1000
        '
        'Background_checkPermissionUpdate
        '
        Me.Background_checkPermissionUpdate.WorkerReportsProgress = True
        Me.Background_checkPermissionUpdate.WorkerSupportsCancellation = True
        '
        'ToastNotificationsManager1
        '
        Me.ToastNotificationsManager1.ApplicationId = "Best Sender "
        Me.ToastNotificationsManager1.ApplicationName = ""
        Me.ToastNotificationsManager1.Notifications.AddRange(New DevExpress.XtraBars.ToastNotifications.IToastNotificationProperties() {New DevExpress.XtraBars.ToastNotifications.ToastNotification("fe4974cf-42ea-4d8c-b050-5e27a8ee6fa5", Global.Best_Sender.My.Resources.Resources.LogoUpdate, "", "New Permission update found.", "System plan permissions have been updated. For more details go to Activation Info" &
                    " ...", DevExpress.XtraBars.ToastNotifications.ToastNotificationSound.IM, DevExpress.XtraBars.ToastNotifications.ToastNotificationDuration.[Default], DevExpress.XtraBars.ToastNotifications.ToastNotificationTemplate.ImageAndText04)})
        '
        'Timer2
        '
        Me.Timer2.Enabled = True
        Me.Timer2.Interval = 30000
        '
        'TrmUpdteChecker
        '
        Me.TrmUpdteChecker.Interval = 10000
        '
        'BKCheckVersion
        '
        Me.BKCheckVersion.WorkerReportsProgress = True
        '
        'TrmNotificationIcon
        '
        Me.TrmNotificationIcon.Interval = 500
        '
        'Timer4
        '
        Me.Timer4.Enabled = True
        Me.Timer4.Interval = 500
        '
        'RibbonPage12
        '
        Me.RibbonPage12.Name = "RibbonPage12"
        Me.RibbonPage12.Text = "RibbonPage12"
        '
        'RibbonPage14
        '
        Me.RibbonPage14.Name = "RibbonPage14"
        Me.RibbonPage14.Text = "RibbonPage14"
        '
        'RibbonPage16
        '
        Me.RibbonPage16.Name = "RibbonPage16"
        Me.RibbonPage16.Text = "RibbonPage16"
        '
        'RibbonPage18
        '
        Me.RibbonPage18.Name = "RibbonPage18"
        Me.RibbonPage18.Text = "RibbonPage18"
        '
        'RibbonPage20
        '
        Me.RibbonPage20.Name = "RibbonPage20"
        Me.RibbonPage20.Text = "RibbonPage20"
        '
        'frmMain
        '
        Me.DefaultToolTipController1.SetAllowHtmlText(Me, DevExpress.Utils.DefaultBoolean.[Default])
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(30, Byte), Integer), CType(CType(30, Byte), Integer), CType(CType(30, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1284, 900)
        Me.Controls.Add(Me.RibbonStatusBar)
        Me.Controls.Add(Me.RibbonControl)
        Me.Controls.Add(Me.barDockControlLeft)
        Me.Controls.Add(Me.barDockControlRight)
        Me.Controls.Add(Me.barDockControlBottom)
        Me.Controls.Add(Me.barDockControlTop)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.LogoBSV
        Me.IconOptions.LargeImage = Global.Best_Sender.My.Resources.Resources.LogoBSV
        Me.IsMdiContainer = True
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.MinimumSize = New System.Drawing.Size(1284, 886)
        Me.Name = "frmMain"
        Me.Opacity = 0.96R
        Me.Ribbon = Me.RibbonControl
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.StatusBar = Me.RibbonStatusBar
        CType(Me.RibbonControl, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DocumentManager1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.NoDocumentsView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TabbedView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RadialMenu1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ToastNotificationsManager1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents RibbonControl As DevExpress.XtraBars.Ribbon.RibbonControl
    Friend WithEvents RibbonPage1 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup1 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem1 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents NoDocumentsView1 As DevExpress.XtraBars.Docking2010.Views.NoDocuments.NoDocumentsView
    Friend WithEvents TabbedView1 As DevExpress.XtraBars.Docking2010.Views.Tabbed.TabbedView
    Friend WithEvents DefaultLookAndFeel1 As DevExpress.LookAndFeel.DefaultLookAndFeel
    Friend WithEvents Timer3 As Timer
    Friend WithEvents trmfadein As Timer
    Friend WithEvents BunifuDragControl1 As Bunifu.Framework.UI.BunifuDragControl
    Friend WithEvents DefaultToolTipController1 As DevExpress.Utils.DefaultToolTipController
    Friend WithEvents BackgroundWorker_CheckInternet As System.ComponentModel.BackgroundWorker
    Friend WithEvents Timer_ChcekInternet As Timer
    Friend WithEvents Timer_DateTime As Timer
    Friend WithEvents BarManager1 As DevExpress.XtraBars.BarManager
    Friend WithEvents barDockControlTop As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlBottom As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlLeft As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlRight As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarButtonItem3 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem4 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem5 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem6 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem7 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem8 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem9 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem10 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem11 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem12 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem13 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Email_Extractor_Pro As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem14 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents EmailExtractor As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem15 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RadialMenu1 As DevExpress.XtraBars.Ribbon.RadialMenu
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents Timer1 As Timer
    Friend WithEvents Background_CheckSystemDate As System.ComponentModel.BackgroundWorker
    Friend WithEvents BackGround_CheckActivationStatus As System.ComponentModel.BackgroundWorker
    Public WithEvents Timer_CheckActivationStatus As Timer
    Public WithEvents Timer_CheckRemainTrail As Timer
    Public WithEvents Timer_CheckPermissionUpdate As Timer
    Friend WithEvents Background_checkPermissionUpdate As System.ComponentModel.BackgroundWorker
    Friend WithEvents ToastNotificationsManager1 As DevExpress.XtraBars.ToastNotifications.ToastNotificationsManager
    Friend WithEvents Timer2 As Timer
    Friend WithEvents TrmUpdteChecker As Timer
    Friend WithEvents BKCheckVersion As System.ComponentModel.BackgroundWorker
    Friend WithEvents TrmNotificationIcon As Timer
    Friend WithEvents BarButtonItem16 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPage2 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup3 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents lblTrail As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents LBLACTIVATIONOWNER As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents BarStaticItem6 As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents BarButtonItem19 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents UserInfo As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntEmailScanner_StartScan As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPage3 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup5 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BntEmailScanner_StopScan As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntEmailScanner_SaveList As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup6 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup8 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonStatusBar As DevExpress.XtraBars.Ribbon.RibbonStatusBar
    Friend WithEvents notificationIocn As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents BarStaticItem2 As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents lblCureentVersion As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents BarStaticItem3 As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents BarStaticItem4 As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents lblCity As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents BarStaticItem5 As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents lblCountry As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents PictureBox1 As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents BarButtonItem17 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPage17 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup9 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BntEmailScanner_OpenForm As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup10 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarSubItem1 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BarButtonItem18 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarSubItem2 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BntTlg As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntSignal As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntDiscord As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents TLGCh As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntYouTube As DevExpress.XtraBars.BarButtonItem
    Public WithEvents DocumentManager1 As DevExpress.XtraBars.Docking2010.DocumentManager
    Friend WithEvents BarSubItem3 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents RibbonPageGroup4 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents bntAddletter As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Bnt_Clear_Letter As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents btnEdit As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Bnt_Inbox_Spam As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarSubItem4 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BarButtonItem26 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bnt_ClearMailList As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Bnt_Save_AllSettings As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup12 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup13 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarSubItem5 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents RibbonPageGroup14 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents Bnt_Add_Acc_SMTP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Bnt_Clear_List_SMTP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Bnt_Tag As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup15 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents Bnt_Reset As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup16 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents Bnt_Paste_Letter As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntUploadeSMTP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup17 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents Timer4 As Timer
    Friend WithEvents BarSubItem6 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BarSubItem7 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BarButtonItem20 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarSubItem8 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BarSubItem9 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BarButtonItem21 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem22 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem23 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem24 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem25 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem28 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem31 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntRedirectWithPass As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RedirectEncodedLinkSpider As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem32 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntCloudFlar As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntGoogleCaptsha As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem35 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntRedirectPuzzel As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntRedirectVIP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem38 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem27 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarSubItem10 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BarButtonItem39 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem40 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem48 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem42 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem43 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem45 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem46 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents INTERNETCONNECTION As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents BarSubItem12 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BntSMTP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem52 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPage6 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents BarButtonItem2 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntStartOlderHost As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup11 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup19 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BntClear As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup20 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents Bnt_get_Example As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup21 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents Bnt_Copy_List As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntSaveSMTPList As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup22 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup23 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents Bnt_Sender_Start As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup24 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem51 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup7 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem53 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem54 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPage7 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup25 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem55 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem56 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup26 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup27 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem57 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem58 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup28 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem59 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup29 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem60 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup30 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem61 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup31 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageExtractDomain As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup2 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents btnSelectFile As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents btnStart As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup18 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents btnStop As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup32 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BntSave As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup34 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem64 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup33 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BntRest As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents ResetAll As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup36 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BntDeleteDuplicatemail As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Select_File_text_Email As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageCleanMail As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup35 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup37 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents Start_Delet_Mail As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup38 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents StopProcessing As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup39 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents Clear_AllMail As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup40 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem62 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntUploadeList As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntStart As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntSaveList As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem67 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageZeroPass As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup41 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup42 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup43 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup44 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup45 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem68 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPagePHP As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup46 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents Bnt_Select_PHP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntSelectPHP As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BntResetPHP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup47 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents Bnt_php_obfuscation As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup48 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents SavePHPFile As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup49 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents CopyPHP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Bnt_Paste As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup50 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup51 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageCloudFlar As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPageGroup52 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem69 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntGetLogo As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup53 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BntStartRedirect As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup54 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup55 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem70 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem71 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup56 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPage12 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPage14 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPage16 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPage18 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents RibbonPage20 As DevExpress.XtraBars.Ribbon.RibbonPage
    Friend WithEvents BarButtonItem72 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem73 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarStaticItem1 As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents BarButtonItem74 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem75 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents SkinBarSubItem1 As DevExpress.XtraBars.SkinBarSubItem
    Friend WithEvents BarButtonItem77 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup58 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem78 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem79 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup59 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents RibbonPageGroup60 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem81 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarSubItem11 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BarSubItem13 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BarSubItem14 As DevExpress.XtraBars.BarSubItem
    Friend WithEvents BarButtonItem47 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntIMAP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem49 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup61 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem76 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents lblDateTime As DevExpress.XtraBars.BarStaticItem
    Friend WithEvents BarButtonItem80 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroupCliebtVIP As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem82 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroupCLientNormal As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents lblpublicIP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntRedirectMicrosoft As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem29 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem30 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarButtonItem33 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup57 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BarButtonItem34 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup62 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
    Friend WithEvents BntStop As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents RibbonPageGroup63 As DevExpress.XtraBars.Ribbon.RibbonPageGroup
End Class
