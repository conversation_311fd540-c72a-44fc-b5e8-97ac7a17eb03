﻿Imports System.IO
Imports System.IO.Compression
Imports System.Text
Imports System.Net
Imports DevExpress.XtraEditors
Imports DevExpress.XtraBars.Ribbon
Public Class frm_En_PHP
    Public Property RelatedRibbonPage As RibbonPage
    Public Sub Select_File_PHP()
        Dim ofd As New OpenFileDialog()
        ofd.Filter = "PHP Files|*.php"
        If ofd.ShowDialog() = DialogResult.OK Then
            ' تعيين مسار الملف في مربع النص
            txtPath.Text = ofd.FileName
            ' قراءة محتوى الملف PHP
            Dim phpCode As String = File.ReadAllText(ofd.FileName)
            ' عرض المحتوى في مربع النص الغني
            Richtext_Encoded.Text = phpCode
            frmMain.Bnt_php_obfuscation.Enabled = True
            frmMain.SavePHPFile.Enabled = False
            frmMain.CopyPHP.Enabled = False
            frmMain.Bnt_Paste.Enabled = True
            frmMain.RibbonPageGroup47.Visible = True
            frmMain.RibbonPageGroup48.Visible = True
            frmMain.RibbonPageGroup49.Visible = True
            frmMain.RibbonPageGroup50.Visible = True
            frmMain.RibbonPageGroup51.Visible = True
        End If
    End Sub
    Private Function EncodePHPCode(input As String) As String
        ' تحويل النص إلى بايتات
        Dim byteArray As Byte() = Encoding.UTF8.GetBytes(input)
        ' ضغط البيانات باستخدام ZLIB
        Using ms As New MemoryStream()
            Using deflate As New DeflateStream(ms, CompressionMode.Compress)
                deflate.Write(byteArray, 0, byteArray.Length)
            End Using
            ' الحصول على البيانات المضغوطة
            Dim compressedData As Byte() = ms.ToArray()
            ' ترميز البيانات المضغوطة باستخدام Base64
            Return Convert.ToBase64String(compressedData)
        End Using
    End Function
    ' دالة لتوليد Base64 عشوائي ليشبه ما تم في المثال
    Private Function GenerateRandomBase64() As String
        ' مجرد مثال لتوليد نص Base64 عشوائي
        Return "=QOCxhJADcrslLOVRlkyv8MCRddyJ3MSyD1VI7cTMJe5gjCyvE7sci3/XDAKB8v0A0SA"
    End Function
    Public Sub Reset_All()
        Richtext_Encoded.Clear()
        txtPath.Clear()
        frmMain.Bnt_php_obfuscation.Enabled = True
        frmMain.Bnt_Select_PHP.Enabled = True
        frmMain.BntResetPHP.Enabled = False
        frmMain.Bnt_php_obfuscation.Enabled = False
        frmMain.SavePHPFile.Enabled = False
        frmMain.CopyPHP.Enabled = False
        frmMain.Bnt_Paste.Enabled = False
        frmMain.Bnt_Paste.Enabled = True
    End Sub
    Public Sub Create_php_obfuscation()
        If Richtext_Encoded.Text.Trim = "" Then
            XtraMessageBox.Show("First, Choose the .PHP file that will be Encrypted", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim phpCode As String = Richtext_Encoded.Text
        Dim decodedPHPCode As String = WebUtility.HtmlDecode(phpCode)
        Dim encodedCode As String = EncodePHPCode(decodedPHPCode)
        Richtext_Encoded.Text = "<?php" & vbCrLf &
                        "$stt1 = '" & encodedCode & "';" & vbCrLf &
                        "$stt0 = '" & GenerateRandomBase64() & "';" & vbCrLf &
                        "eval('?>'.gzinflate(base64_decode($stt1)).'<?php');" & vbCrLf &
                        "?>"
        frmMain.Bnt_php_obfuscation.Enabled = False
        frmMain.Bnt_Select_PHP.Enabled = False
        frmMain.Bnt_Paste.Enabled = False
        frmMain.SavePHPFile.Enabled = True
        frmMain.CopyPHP.Enabled = True
        frmMain.Bnt_Select_PHP.Enabled = True
        frmMain.RibbonPageGroup49.Visible = True
        frmMain.RibbonPageGroup50.Visible = True
        frmMain.RibbonPageGroup47.Visible = True
    End Sub
    Public Sub SaveToDesktop()
        ' الحصول على مسار سطح المكتب
        Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        ' إنشاء مسار المجلد الجديد
        Dim folderPath As String = System.IO.Path.Combine(desktopPath, "php_obfuscation")
        ' إنشاء المجلد إذا لم يكن موجوداً
        If Not System.IO.Directory.Exists(folderPath) Then
            System.IO.Directory.CreateDirectory(folderPath)
        End If
        ' إنشاء اسم ملف عشوائي أو استخدام اسم ثابت
        Dim fileName As String = "encoded_" & DateTime.Now.ToString("yyyyMMddHHmmss") & ".php"
        Dim fullPath As String = System.IO.Path.Combine(folderPath, fileName)
        ' كتابة المحتوى إلى الملف
        System.IO.File.WriteAllText(fullPath, Richtext_Encoded.Text)
        ' فتح المجلد في مستكشف الملفات
        Process.Start("explorer.exe", folderPath)
        ' إظهار رسالة تأكيد
        XtraMessageBox.Show($"File Saved successfully at:{vbCrLf}{fullPath}", "File Saved", MessageBoxButtons.OK, MessageBoxIcon.Information)
        frmMain.Bnt_Select_PHP.Enabled = True
        frmMain.Bnt_php_obfuscation.Enabled = False
        frmMain.SavePHPFile.Enabled = False
        frmMain.CopyPHP.Enabled = False
        frmMain.Bnt_Paste.Enabled = True
        frmMain.BntResetPHP.Enabled = True
        frmMain.RibbonPageGroup47.Visible = True
    End Sub
    Public Sub Cut_Code_PHP()
        If Not String.IsNullOrWhiteSpace(Richtext_Encoded.Text) Then
            Clipboard.SetText(Richtext_Encoded.Text)
            frmMain.Bnt_php_obfuscation.Enabled = True
            XtraMessageBox.Show("Code Copied", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
            frmMain.RibbonPageGroup47.Visible = True
        Else
            XtraMessageBox.Show("Please select a .php file first", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub
    Public Sub Paste_Code_PHP()
        Richtext_Encoded.Clear()
        If Clipboard.ContainsText() Then
            Richtext_Encoded.Text = Clipboard.GetText()
            frmMain.Bnt_Select_PHP.Enabled = False
            frmMain.SavePHPFile.Enabled = True
            frmMain.CopyPHP.Enabled = True
            frmMain.Bnt_Paste.Enabled = False
            frmMain.BntResetPHP.Enabled = True
            frmMain.Bnt_php_obfuscation.Enabled = True
            frmMain.RibbonPageGroup48.Visible = True
        Else
            XtraMessageBox.Show("There is no text in the clipboard.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub
    Private Sub frm_En_PHP_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        frmMain.BntSelectPHP.Visible = False
        frmMain.RibbonPageGroup47.Visible = False
        frmMain.RibbonPageGroup48.Visible = False
        frmMain.RibbonPageGroup49.Visible = False
        frmMain.RibbonPageGroup50.Visible = False
        frmMain.RibbonPageGroup51.Visible = False
        frmMain.Bnt_Select_PHP.Enabled = False
        frmMain.BntResetPHP.Enabled = False
        frmMain.Bnt_php_obfuscation.Enabled = False
        frmMain.SavePHPFile.Enabled = False
        frmMain.CopyPHP.Enabled = False
        frmMain.Bnt_Paste.Enabled = False
    End Sub
    Private Sub frm_En_PHP_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        frmMain.Bnt_Paste.Enabled = True
    End Sub
End Class