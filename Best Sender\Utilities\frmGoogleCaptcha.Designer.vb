﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmGoogleCaptcha
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmGoogleCaptcha))
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.txt_Link_Google_CAPTCHA = New DevExpress.XtraEditors.TextEdit()
        Me.txt_Site_key = New DevExpress.XtraEditors.TextEdit()
        Me.txt_type_Google_CAPTCHA = New DevExpress.XtraEditors.TextEdit()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.BTN_Redirect_Link_Google_Click = New DevExpress.XtraEditors.SimpleButton()
        Me.BTN_Reset_Google_CAPTCHA = New DevExpress.XtraEditors.SimpleButton()
        Me.btn_Save = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.txt_Link_Google_CAPTCHA.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_Site_key.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_type_Google_CAPTCHA.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'RichTextBox1
        '
        Me.RichTextBox1.Location = New System.Drawing.Point(1135, 13)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(96, 63)
        Me.RichTextBox1.TabIndex = 507
        Me.RichTextBox1.Text = resources.GetString("RichTextBox1.Text")
        Me.RichTextBox1.Visible = False
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label3.Location = New System.Drawing.Point(183, 354)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(117, 21)
        Me.Label3.TabIndex = 515
        Me.Label3.Text = "Enter Your Text "
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(183, 287)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(143, 21)
        Me.Label2.TabIndex = 516
        Me.Label2.Text = "reCAPTCHA Site key"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label1.Location = New System.Drawing.Point(183, 220)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(110, 21)
        Me.Label1.TabIndex = 517
        Me.Label1.Text = "Enter Your link"
        '
        'txt_Link_Google_CAPTCHA
        '
        Me.txt_Link_Google_CAPTCHA.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Link_Google_CAPTCHA.EditValue = ""
        Me.txt_Link_Google_CAPTCHA.Location = New System.Drawing.Point(183, 249)
        Me.txt_Link_Google_CAPTCHA.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Link_Google_CAPTCHA.Name = "txt_Link_Google_CAPTCHA"
        Me.txt_Link_Google_CAPTCHA.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Link_Google_CAPTCHA.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Link_Google_CAPTCHA.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Link_Google_CAPTCHA.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Link_Google_CAPTCHA.Properties.Appearance.Options.UseFont = True
        Me.txt_Link_Google_CAPTCHA.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Link_Google_CAPTCHA.Properties.NullValuePrompt = "https://www.Example.com/?="
        Me.txt_Link_Google_CAPTCHA.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Link_Google_CAPTCHA.Size = New System.Drawing.Size(590, 30)
        Me.txt_Link_Google_CAPTCHA.TabIndex = 512
        '
        'txt_Site_key
        '
        Me.txt_Site_key.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Site_key.EditValue = ""
        Me.txt_Site_key.Location = New System.Drawing.Point(183, 316)
        Me.txt_Site_key.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Site_key.Name = "txt_Site_key"
        Me.txt_Site_key.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Site_key.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Site_key.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Site_key.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Site_key.Properties.Appearance.Options.UseFont = True
        Me.txt_Site_key.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Site_key.Properties.NullValuePrompt = "Your Google reCAPTCHA Site key"
        Me.txt_Site_key.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Site_key.Size = New System.Drawing.Size(590, 30)
        Me.txt_Site_key.TabIndex = 513
        '
        'txt_type_Google_CAPTCHA
        '
        Me.txt_type_Google_CAPTCHA.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_type_Google_CAPTCHA.EditValue = ""
        Me.txt_type_Google_CAPTCHA.Location = New System.Drawing.Point(183, 383)
        Me.txt_type_Google_CAPTCHA.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_type_Google_CAPTCHA.Name = "txt_type_Google_CAPTCHA"
        Me.txt_type_Google_CAPTCHA.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_type_Google_CAPTCHA.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_type_Google_CAPTCHA.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_type_Google_CAPTCHA.Properties.Appearance.Options.UseBackColor = True
        Me.txt_type_Google_CAPTCHA.Properties.Appearance.Options.UseFont = True
        Me.txt_type_Google_CAPTCHA.Properties.Appearance.Options.UseForeColor = True
        Me.txt_type_Google_CAPTCHA.Properties.NullValuePrompt = "Decoding document. Please wait..."
        Me.txt_type_Google_CAPTCHA.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_type_Google_CAPTCHA.Size = New System.Drawing.Size(590, 30)
        Me.txt_type_Google_CAPTCHA.TabIndex = 514
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.Google_Captcha_Logo
        Me.PictureBox1.Location = New System.Drawing.Point(12, 81)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(936, 114)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 511
        Me.PictureBox1.TabStop = False
        '
        'BTN_Redirect_Link_Google_Click
        '
        Me.BTN_Redirect_Link_Google_Click.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Redirect_Link_Google_Click.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Redirect_Link_Google_Click.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_Redirect_Link_Google_Click.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Redirect_Link_Google_Click.Appearance.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Google_Click.Appearance.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Google_Click.Appearance.Options.UseFont = True
        Me.BTN_Redirect_Link_Google_Click.Appearance.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Google_Click.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BTN_Redirect_Link_Google_Click.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BTN_Redirect_Link_Google_Click.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_Redirect_Link_Google_Click.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BTN_Redirect_Link_Google_Click.AppearanceDisabled.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Google_Click.AppearanceDisabled.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Google_Click.AppearanceDisabled.Options.UseFont = True
        Me.BTN_Redirect_Link_Google_Click.AppearanceDisabled.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Google_Click.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BTN_Redirect_Link_Google_Click.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Redirect_Link_Google_Click.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BTN_Redirect_Link_Google_Click.AppearanceHovered.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Google_Click.AppearanceHovered.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Google_Click.AppearanceHovered.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Google_Click.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Redirect_Link_Google_Click.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Redirect_Link_Google_Click.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BTN_Redirect_Link_Google_Click.AppearancePressed.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Google_Click.AppearancePressed.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Google_Click.AppearancePressed.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Google_Click.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.BTN_Redirect_Link_Google_Click.Location = New System.Drawing.Point(262, 444)
        Me.BTN_Redirect_Link_Google_Click.Name = "BTN_Redirect_Link_Google_Click"
        Me.BTN_Redirect_Link_Google_Click.Size = New System.Drawing.Size(131, 38)
        Me.BTN_Redirect_Link_Google_Click.TabIndex = 510
        Me.BTN_Redirect_Link_Google_Click.Text = "Build"
        '
        'BTN_Reset_Google_CAPTCHA
        '
        Me.BTN_Reset_Google_CAPTCHA.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Reset_Google_CAPTCHA.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Reset_Google_CAPTCHA.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_Reset_Google_CAPTCHA.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Reset_Google_CAPTCHA.Appearance.Options.UseBackColor = True
        Me.BTN_Reset_Google_CAPTCHA.Appearance.Options.UseBorderColor = True
        Me.BTN_Reset_Google_CAPTCHA.Appearance.Options.UseFont = True
        Me.BTN_Reset_Google_CAPTCHA.Appearance.Options.UseForeColor = True
        Me.BTN_Reset_Google_CAPTCHA.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BTN_Reset_Google_CAPTCHA.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BTN_Reset_Google_CAPTCHA.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_Reset_Google_CAPTCHA.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BTN_Reset_Google_CAPTCHA.AppearanceDisabled.Options.UseBackColor = True
        Me.BTN_Reset_Google_CAPTCHA.AppearanceDisabled.Options.UseBorderColor = True
        Me.BTN_Reset_Google_CAPTCHA.AppearanceDisabled.Options.UseFont = True
        Me.BTN_Reset_Google_CAPTCHA.AppearanceDisabled.Options.UseForeColor = True
        Me.BTN_Reset_Google_CAPTCHA.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BTN_Reset_Google_CAPTCHA.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Reset_Google_CAPTCHA.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BTN_Reset_Google_CAPTCHA.AppearanceHovered.Options.UseBackColor = True
        Me.BTN_Reset_Google_CAPTCHA.AppearanceHovered.Options.UseBorderColor = True
        Me.BTN_Reset_Google_CAPTCHA.AppearanceHovered.Options.UseForeColor = True
        Me.BTN_Reset_Google_CAPTCHA.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Reset_Google_CAPTCHA.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Reset_Google_CAPTCHA.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BTN_Reset_Google_CAPTCHA.AppearancePressed.Options.UseBackColor = True
        Me.BTN_Reset_Google_CAPTCHA.AppearancePressed.Options.UseBorderColor = True
        Me.BTN_Reset_Google_CAPTCHA.AppearancePressed.Options.UseForeColor = True
        Me.BTN_Reset_Google_CAPTCHA.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BTN_Reset_Google_CAPTCHA.Location = New System.Drawing.Point(562, 444)
        Me.BTN_Reset_Google_CAPTCHA.Name = "BTN_Reset_Google_CAPTCHA"
        Me.BTN_Reset_Google_CAPTCHA.Size = New System.Drawing.Size(131, 38)
        Me.BTN_Reset_Google_CAPTCHA.TabIndex = 509
        Me.BTN_Reset_Google_CAPTCHA.Text = "Reset All"
        '
        'btn_Save
        '
        Me.btn_Save.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Options.UseBackColor = True
        Me.btn_Save.Appearance.Options.UseBorderColor = True
        Me.btn_Save.Appearance.Options.UseFont = True
        Me.btn_Save.Appearance.Options.UseForeColor = True
        Me.btn_Save.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn_Save.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn_Save.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn_Save.AppearanceDisabled.Options.UseBackColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseFont = True
        Me.btn_Save.AppearanceDisabled.Options.UseForeColor = True
        Me.btn_Save.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn_Save.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn_Save.AppearanceHovered.Options.UseBackColor = True
        Me.btn_Save.AppearanceHovered.Options.UseBorderColor = True
        Me.btn_Save.AppearanceHovered.Options.UseForeColor = True
        Me.btn_Save.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn_Save.AppearancePressed.Options.UseBackColor = True
        Me.btn_Save.AppearancePressed.Options.UseBorderColor = True
        Me.btn_Save.AppearancePressed.Options.UseForeColor = True
        Me.btn_Save.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.settings32x32
        Me.btn_Save.Location = New System.Drawing.Point(412, 444)
        Me.btn_Save.Name = "btn_Save"
        Me.btn_Save.Size = New System.Drawing.Size(131, 38)
        Me.btn_Save.TabIndex = 508
        Me.btn_Save.Text = "Save Settings"
        '
        'frmGoogleCaptcha
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.RichTextBox1)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.txt_Link_Google_CAPTCHA)
        Me.Controls.Add(Me.btn_Save)
        Me.Controls.Add(Me.txt_Site_key)
        Me.Controls.Add(Me.BTN_Reset_Google_CAPTCHA)
        Me.Controls.Add(Me.txt_type_Google_CAPTCHA)
        Me.Controls.Add(Me.BTN_Redirect_Link_Google_Click)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.Sizable
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.MinimumSize = New System.Drawing.Size(800, 500)
        Me.Name = "frmGoogleCaptcha"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Redirect link with Google Captcha"
        CType(Me.txt_Link_Google_CAPTCHA.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_Site_key.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_type_Google_CAPTCHA.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents Label3 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents txt_Link_Google_CAPTCHA As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txt_Site_key As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txt_type_Google_CAPTCHA As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents BTN_Redirect_Link_Google_Click As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BTN_Reset_Google_CAPTCHA As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btn_Save As DevExpress.XtraEditors.SimpleButton
End Class
