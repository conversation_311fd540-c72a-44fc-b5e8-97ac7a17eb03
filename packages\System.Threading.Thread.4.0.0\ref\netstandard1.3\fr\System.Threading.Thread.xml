﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Thread</name>
  </assembly>
  <members>
    <member name="T:System.Threading.ParameterizedThreadStart">
      <summary>Représente la méthode qui s'exécute sur <see cref="T:System.Threading.Thread" />.</summary>
      <param name="obj">Objet qui contient des données pour la procédure de thread.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.Thread">
      <summary>Crée et contrôle un thread, définit sa priorité et obtient son état. Pour parcourir le code source de .NET Framework pour ce type, consultez la Source de référence.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.#ctor(System.Threading.ParameterizedThreadStart)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Thread" />, en spécifiant un délégué qui permet à un objet d'être passé au thread quand le thread est démarré.</summary>
      <param name="start">Délégué qui représente les méthodes à appeler au début de l'exécution de ce thread. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="start" /> is null. </exception>
    </member>
    <member name="M:System.Threading.Thread.#ctor(System.Threading.ThreadStart)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.Thread" />.</summary>
      <param name="start">Délégué <see cref="T:System.Threading.ThreadStart" /> qui représente les méthodes à appeler au début de l'exécution de ce thread. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="start" /> parameter is null. </exception>
    </member>
    <member name="P:System.Threading.Thread.CurrentThread">
      <summary>Obtient le thread en cours d'exécution.</summary>
      <returns>
        <see cref="T:System.Threading.Thread" /> constituant la représentation du thread en cours d'exécution.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.IsAlive">
      <summary>Obtient une valeur indiquant l'état de l'exécution du thread actuel.</summary>
      <returns>true si ce thread a été démarré mais ne s'est pas arrêté normalement ou s'il a été abandonné ; sinon, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.IsBackground">
      <summary>Obtient ou définit une valeur indiquant si le thread est un thread d'arrière-plan ou non.</summary>
      <returns>true si ce thread est ou doit devenir un thread d'arrière-plan ; sinon, false.</returns>
      <exception cref="T:System.Threading.ThreadStateException">The thread is dead. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Join">
      <summary>Bloque le thread appelant jusqu'à l'arrêt d'un thread, tout en continuant d'exécuter le pompage COM et SendMessage standard.</summary>
      <exception cref="T:System.Threading.ThreadStateException">The caller attempted to join a thread that is in the <see cref="F:System.Threading.ThreadState.Unstarted" /> state. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">The thread is interrupted while waiting. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Join(System.Int32)">
      <summary>Bloque le thread appelant jusqu'à ce qu'un thread s'arrête ou que la durée spécifiée soit écoulée, tout en continuant d'exécuter le pompage COM et SendMessage standard.</summary>
      <returns>true si le thread s'est arrêté ; false s'il ne s'est pas arrêté après l'expiration du délai spécifié par le paramètre <paramref name="millisecondsTimeout" />.</returns>
      <param name="millisecondsTimeout">Nombre de millisecondes à attendre l'arrêt du thread. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> in milliseconds. </exception>
      <exception cref="T:System.Threading.ThreadStateException">The thread has not been started. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.ManagedThreadId">
      <summary>Obtient un identificateur unique pour le thread managé actuel. </summary>
      <returns>Entier représentant un identificateur unique pour ce thread managé.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.Name">
      <summary>Obtient ou définit le nom du thread.</summary>
      <returns>Chaîne contenant le nom du thread ou null si aucun nom n'a été défini.</returns>
      <exception cref="T:System.InvalidOperationException">A set operation was requested, but the Name property has already been set. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Sleep(System.Int32)">
      <summary>Suspend le thread actuel pendant le nombre spécifié de millisecondes.</summary>
      <param name="millisecondsTimeout">Nombre de millisecondes pendant lesquelles le thread est suspendu.Si la valeur de l'argument <paramref name="millisecondsTimeout" /> est égale à zéro, le thread abandonne le reste de sa tranche de temps à n'importe quel thread de priorité égale prêt à être exécuté.S'il n'existe pas d'autres threads de priorité égale prêts à être exécutés, l'exécution du thread actuel n'est pas suspendue.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The time-out value is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Sleep(System.TimeSpan)">
      <summary>Suspend le thread actuel pendant la durée spécifiée.</summary>
      <param name="timeout">Durée pendant laquelle le thread est suspendu.Si la valeur de l'argument <paramref name="millisecondsTimeout" /> est <see cref="F:System.TimeSpan.Zero" />, le thread abandonne le reste de sa tranche de temps à n'importe quel thread de priorité égale prêt à être exécuté.S'il n'existe pas d'autres threads de priorité égale prêts à être exécutés, l'exécution du thread actuel n'est pas suspendue.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> in milliseconds, or is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Start">
      <summary>Provoque le changement, par le système d'exploitation, de l'état de l'instance actuelle en <see cref="F:System.Threading.ThreadState.Running" />.</summary>
      <exception cref="T:System.Threading.ThreadStateException">The thread has already been started. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available to start this thread. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Start(System.Object)">
      <summary>Provoque le changement, par le système d'exploitation, de l'état de l'instance actuelle en <see cref="F:System.Threading.ThreadState.Running" />, et fournit éventuellement un objet contenant les données que la méthode exécutée par le thread doit utiliser.</summary>
      <param name="parameter">Objet contenant les données que la méthode exécutée par le thread doit utiliser.</param>
      <exception cref="T:System.Threading.ThreadStateException">The thread has already been started. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available to start this thread. </exception>
      <exception cref="T:System.InvalidOperationException">This thread was created using a <see cref="T:System.Threading.ThreadStart" /> delegate instead of a <see cref="T:System.Threading.ParameterizedThreadStart" /> delegate.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.ThreadState">
      <summary>Obtient une valeur contenant les états du thread actuel.</summary>
      <returns>Une des valeurs <see cref="T:System.Threading.ThreadState" /> indiquant l'état du thread actuel.La valeur initiale est Unstarted.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.ThreadStart">
      <summary>Représente la méthode qui s'exécute sur <see cref="T:System.Threading.Thread" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.ThreadStartException">
      <summary>Exception levée lorsqu'une erreur se produit dans un thread managé après le démarrage du thread de système d'exploitation sous-jacent, mais avant que le thread soit prêt à exécuter le code utilisateur.</summary>
    </member>
    <member name="T:System.Threading.ThreadState">
      <summary>Spécifie les états d'exécution de <see cref="T:System.Threading.Thread" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Threading.ThreadState.Aborted">
      <summary>L'état du thread inclut <see cref="F:System.Threading.ThreadState.AbortRequested" /> et le thread est à présent inactif ; toutefois, son état n'est pas encore passé à <see cref="F:System.Threading.ThreadState.Stopped" />.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.AbortRequested">
      <summary>La méthode <see cref="M:System.Threading.Thread.Abort(System.Object)" /> a été appelée sur le thread, mais ce dernier n'a pas encore reçu <see cref="T:System.Threading.ThreadAbortException" /> en attente qui tentera de l'arrêter.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Background">
      <summary>Le thread est exécuté en tant que thread d'arrière-plan, par opposition à un thread de premier plan.Cet état est contrôlé par définition de la propriété <see cref="P:System.Threading.Thread.IsBackground" />.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Running">
      <summary>Le thread a été démarré, il n'est pas bloqué et il n'y a pas de <see cref="T:System.Threading.ThreadAbortException" /> en attente.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Stopped">
      <summary>Le thread s'est arrêté.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.StopRequested">
      <summary>Une demande d'arrêt du thread est en cours.Ceci est exclusivement destiné à un usage interne.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Suspended">
      <summary>Le thread a été suspendu.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.SuspendRequested">
      <summary>Une demande de suspension du thread est en cours.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Unstarted">
      <summary>La méthode <see cref="M:System.Threading.Thread.Start" /> n'a pas été appelée sur le thread.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.WaitSleepJoin">
      <summary>Le thread est bloqué.Cela peut résulter de l'appel de <see cref="M:System.Threading.Thread.Sleep(System.Int32)" /> ou <see cref="M:System.Threading.Thread.Join" />, d'une demande de verrouillage (par exemple l'appel de <see cref="M:System.Threading.Monitor.Enter(System.Object)" /> ou <see cref="M:System.Threading.Monitor.Wait(System.Object,System.Int32,System.Boolean)" />) ou de l'attente d'un objet de synchronisation de threads tel que <see cref="T:System.Threading.ManualResetEvent" />.</summary>
    </member>
    <member name="T:System.Threading.ThreadStateException">
      <summary>Exception qui est levée lorsque <see cref="T:System.Threading.Thread" /> est dans un état <see cref="P:System.Threading.Thread.ThreadState" /> non valide pour l'appel de méthode.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.ThreadStateException" /> avec des propriétés par défaut.</summary>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.ThreadStateException" /> avec un message d'erreur spécifié.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Threading.ThreadStateException" /> avec un message d'erreur spécifié et une référence à l'exception interne qui est à l'origine de cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="innerException">Exception qui constitue la cause de l'exception actuelle.Si le paramètre <paramref name="innerException" /> n'est pas null, l'exception en cours est levée dans un bloc catch qui gère l'exception interne.</param>
    </member>
  </members>
</doc>