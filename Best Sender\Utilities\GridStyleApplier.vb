Imports System.Windows.Forms
Imports DevExpress.XtraGrid
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.Utils

Namespace Best_Sender
    ''' <summary>
    ''' فئة مساعدة لتطبيق تصميم Binance على جميع نماذج GridControl في التطبيق
    ''' </summary>
    Public Class GridStyleApplier
    ''' <summary>
    ''' تطبيق تصميم Binance على جميع عناصر GridControl في النموذج المحدد
    ''' </summary>
    ''' <param name="form">النموذج المراد تطبيق التصميم على عناصر GridControl فيه</param>
    ''' <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
    Public Shared Sub ApplyBinanceStyleToForm(form As Form, Optional useWXITheme As Boolean = True)
        If form Is Nothing Then Return

        ' البحث عن جميع عناصر GridControl في النموذج وتطبيق التصميم عليها
        For Each control As Control In GetAllControls(form)
            If TypeOf control Is GridControl Then
                Dim gridControl As GridControl = DirectCast(control, GridControl)
                BinanceGridStyle.ApplyBinanceStyle(gridControl, useWXITheme)
            End If
        Next
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على جميع عناصر GridControl في جميع النماذج المفتوحة
    ''' </summary>
    ''' <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
    Public Shared Sub ApplyBinanceStyleToAllForms(Optional useWXITheme As Boolean = True)
        ' تطبيق التصميم على جميع النماذج المفتوحة
        For Each form As Form In Application.OpenForms
            ApplyBinanceStyleToForm(form, useWXITheme)
        Next
    End Sub

    ''' <summary>
    ''' تسجيل حدث إنشاء النماذج لتطبيق التصميم تلقائيًا على النماذج الجديدة
    ''' </summary>
    ''' <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
    Public Shared Sub RegisterForFormCreation(Optional useWXITheme As Boolean = True)
        ' إضافة معالج حدث لتطبيق التصميم على النماذج الجديدة عند إنشائها
        AddHandler Application.OpenForms(0).CreateControl, Sub()
            AddHandler Application.OpenForms(0).ControlAdded, Sub(sender As Object, e As ControlEventArgs)
                If TypeOf e.Control Is Form Then
                    Dim newForm As Form = DirectCast(e.Control, Form)
                    ' تأخير التطبيق قليلاً للتأكد من اكتمال تحميل النموذج
                    newForm.BeginInvoke(Sub() ApplyBinanceStyleToForm(newForm, useWXITheme))
                End If
            End Sub
        End Sub
    End Sub

    ''' <summary>
    ''' تهيئة تطبيق تصميم Binance على جميع النماذج (الحالية والمستقبلية)
    ''' </summary>
    ''' <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
    Public Shared Sub Initialize(Optional useWXITheme As Boolean = True)
        ' تطبيق التصميم على النماذج المفتوحة حاليًا
        ApplyBinanceStyleToAllForms(useWXITheme)

        ' تسجيل لتطبيق التصميم على النماذج الجديدة
        RegisterForFormCreation(useWXITheme)
    End Sub

    ''' <summary>
    ''' الحصول على جميع عناصر التحكم في النموذج بما في ذلك العناصر المتداخلة
    ''' </summary>
    ''' <param name="control">عنصر التحكم الأصلي</param>
    ''' <returns>قائمة بجميع عناصر التحكم</returns>
    Private Shared Function GetAllControls(control As Control) As List(Of Control)
        Dim controls As New List(Of Control)
        controls.Add(control)

        ' إضافة جميع عناصر التحكم المباشرة
        controls.AddRange(control.Controls.Cast(Of Control)())

        ' إضافة عناصر التحكم المتداخلة بشكل متكرر
        For i As Integer = 0 To control.Controls.Count - 1
            controls.AddRange(GetAllControls(control.Controls(i)))
        Next

        Return controls
    End Function
End Class
End Namespace
