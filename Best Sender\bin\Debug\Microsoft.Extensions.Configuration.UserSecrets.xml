<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Configuration.UserSecrets</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Configuration.UserSecrets.PathHelper">
            <summary>
            Provides paths for user secrets configuration files.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.UserSecrets.PathHelper.GetSecretsPathFromSecretsId(System.String)">
            <summary>
            <para>
            Returns the path to the JSON file that stores user secrets.
            </para>
            <para>
            This uses the current user profile to locate the secrets file on disk in a location outside of source control.
            </para>
            </summary>
            <param name="userSecretsId">The user secret ID.</param>
            <returns>The full path to the secret file.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute">
            <summary>
            <para>
            Represents the user secrets ID.
            </para>
            <para>
            In most cases, this attribute is automatically generated during compilation by MSBuild targets
            included in the UserSecrets NuGet package. These targets use the MSBuild property 'UserSecretsId'
            to set the value for <see cref="P:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute.UserSecretsId"/>.
            </para>
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute.#ctor(System.String)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute" />.
            </summary>
            <param name="userSecretId">The user secrets ID.</param>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute.UserSecretsId">
            <summary>
            The user secrets ID.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.UserSecretsConfigurationExtensions">
            <summary>
            Configuration extensions for adding user secrets configuration source.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.UserSecretsConfigurationExtensions.AddUserSecrets``1(Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            <para>
            Adds the user secrets configuration source. Searches the assembly that contains type <typeparamref name="T"/>
            for an instance of <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>, which specifies a user secrets ID.
            </para>
            <para>
            A user secrets ID is unique value used to store and identify a collection of secret configuration values.
            </para>
            </summary>
            <param name="configuration">The configuration builder.</param>
            <typeparam name="T">The type from the assembly to search for an instance of <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>.</typeparam>
            <exception cref="T:System.InvalidOperationException">Thrown when the assembly containing <typeparamref name="T"/> does not have <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>.</exception>
            <returns>The configuration builder.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.UserSecretsConfigurationExtensions.AddUserSecrets``1(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.Boolean)">
            <summary>
            <para>
            Adds the user secrets configuration source. Searches the assembly that contains type <typeparamref name="T"/>
            for an instance of <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>, which specifies a user secrets ID.
            </para>
            <para>
            A user secrets ID is unique value used to store and identify a collection of secret configuration values.
            </para>
            </summary>
            <param name="configuration">The configuration builder.</param>
            <param name="optional">Whether loading secrets is optional. When false, this method may throw.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when <paramref name="optional"/> is false and the assembly containing <typeparamref name="T"/> does not have a valid <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>.</exception>
            <typeparam name="T">The type from the assembly to search for an instance of <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>.</typeparam>
            <returns>The configuration builder.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.UserSecretsConfigurationExtensions.AddUserSecrets``1(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.Boolean,System.Boolean)">
            <summary>
            <para>
            Adds the user secrets configuration source. Searches the assembly that contains type <typeparamref name="T"/>
            for an instance of <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>, which specifies a user secrets ID.
            </para>
            <para>
            A user secrets ID is unique value used to store and identify a collection of secret configuration values.
            </para>
            </summary>
            <param name="configuration">The configuration builder.</param>
            <param name="optional">Whether loading secrets is optional. When false, this method may throw.</param>
            <param name="reloadOnChange">Whether the configuration should be reloaded if the file changes.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when <paramref name="optional"/> is false and the assembly containing <typeparamref name="T"/> does not have a valid <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>.</exception>
            <typeparam name="T">The type from the assembly to search for an instance of <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>.</typeparam>
            <returns>The configuration builder.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.UserSecretsConfigurationExtensions.AddUserSecrets(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.Reflection.Assembly)">
            <summary>
            <para>
            Adds the user secrets configuration source. This searches <paramref name="assembly"/> for an instance
            of <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>, which specifies a user secrets ID.
            </para>
            <para>
            A user secrets ID is unique value used to store and identify a collection of secret configuration values.
            </para>
            </summary>
            <param name="configuration">The configuration builder.</param>
            <param name="assembly">The assembly with the <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute" />.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when <paramref name="assembly"/> does not have a valid <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/></exception>
            <returns>The configuration builder.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.UserSecretsConfigurationExtensions.AddUserSecrets(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.Reflection.Assembly,System.Boolean)">
            <summary>
            <para>
            Adds the user secrets configuration source. This searches <paramref name="assembly"/> for an instance
            of <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>, which specifies a user secrets ID.
            </para>
            <para>
            A user secrets ID is unique value used to store and identify a collection of secret configuration values.
            </para>
            </summary>
            <param name="configuration">The configuration builder.</param>
            <param name="assembly">The assembly with the <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute" />.</param>
            <param name="optional">Whether loading secrets is optional. When false, this method may throw.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when <paramref name="optional"/> is false and <paramref name="assembly"/> does not have a valid <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>.</exception>
            <returns>The configuration builder.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.UserSecretsConfigurationExtensions.AddUserSecrets(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.Reflection.Assembly,System.Boolean,System.Boolean)">
            <summary>
            <para>
            Adds the user secrets configuration source. This searches <paramref name="assembly"/> for an instance
            of <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>, which specifies a user secrets ID.
            </para>
            <para>
            A user secrets ID is unique value used to store and identify a collection of secret configuration values.
            </para>
            </summary>
            <param name="configuration">The configuration builder.</param>
            <param name="assembly">The assembly with the <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute" />.</param>
            <param name="optional">Whether loading secrets is optional. When false, this method may throw.</param>
            <param name="reloadOnChange">Whether the configuration should be reloaded if the file changes.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when <paramref name="optional"/> is false and <paramref name="assembly"/> does not have a valid <see cref="T:Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute"/>.</exception>
            <returns>The configuration builder.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.UserSecretsConfigurationExtensions.AddUserSecrets(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.String)">
            <summary>
            <para>
            Adds the user secrets configuration source with specified user secrets ID.
            </para>
            <para>
            A user secrets ID is unique value used to store and identify a collection of secret configuration values.
            </para>
            </summary>
            <param name="configuration">The configuration builder.</param>
            <param name="userSecretsId">The user secrets ID.</param>
            <returns>The configuration builder.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.UserSecretsConfigurationExtensions.AddUserSecrets(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.String,System.Boolean)">
            <summary>
            <para>
            Adds the user secrets configuration source with specified user secrets ID.
            </para>
            <para>
            A user secrets ID is unique value used to store and identify a collection of secret configuration values.
            </para>
            </summary>
            <param name="configuration">The configuration builder.</param>
            <param name="userSecretsId">The user secrets ID.</param>
            <param name="reloadOnChange">Whether the configuration should be reloaded if the file changes.</param>
            <returns>The configuration builder.</returns>
        </member>
        <member name="P:System.SR.Common_StringNullOrEmpty">
            <summary>Value cannot be null or an empty string.</summary>
        </member>
        <member name="P:System.SR.Error_Invalid_Character_In_UserSecrets_Id">
            <summary>Invalid character '{0}' found in the user secrets ID at index '{1}'.</summary>
        </member>
        <member name="P:System.SR.Error_Missing_UserSecretsIdAttribute">
            <summary>Could not find 'UserSecretsIdAttribute' on assembly '{0}'.
            Check that the project for '{0}' has set the 'UserSecretsId' build property.
            If the 'UserSecretsId' property is already set then add a reference to the Microsoft.Extensions.Configuration.UserSecret ...</summary>
        </member>
        <member name="P:System.SR.Error_Missing_UserSecretsLocation">
            <summary>Could not determine an appropriate location for storing user secrets. Set the {0} environment variable to a folder where user secrets should be stored.</summary>
        </member>
    </members>
</doc>
