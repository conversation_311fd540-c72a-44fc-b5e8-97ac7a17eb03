Imports System.Collections.Generic

Public Class SmtpProviders
    ' قائمة بمزودي خدمة SMTP المدعومين
    Public Shared Function GetSupportedProviders() As List(Of SmtpProviderInfo)
        Dim providers As New List(Of SmtpProviderInfo)

        ' Amazon SES
        providers.Add(New SmtpProviderInfo With {
            .Name = "Amazon SES",
            .Hosts = {"email-smtp.us-east-1.amazonaws.com", "email-smtp.us-west-2.amazonaws.com", "email-smtp.eu-west-1.amazonaws.com"},
            .Ports = {25, 587, 2587, 465, 2465},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "AWS SDK",
            .Notes = "Requires AWS credentials (Access Key and Secret Key)"
        })

        ' ActiveCampaign SMTP
        providers.Add(New SmtpProviderInfo With {
            .Name = "ActiveCampaign SMTP",
            .Hosts = {"smtp.activecampaign.com"},
            .Ports = {587, 2525},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "REST API",
            .Notes = "Requires API key for API access"
        })

        ' Gmail SMTP
        providers.Add(New SmtpProviderInfo With {
            .Name = "Gmail SMTP",
            .Hosts = {"smtp.gmail.com"},
            .Ports = {587, 465},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "OAuth2",
            .Notes = "Requires app password or OAuth2 for authentication"
        })

        ' Outlook/Office365 SMTP
        providers.Add(New SmtpProviderInfo With {
            .Name = "Office 365 SMTP",
            .Hosts = {"smtp.office365.com"},
            .Ports = {587},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "Microsoft Graph API",
            .Notes = "Requires OAuth2 for best security"
        })

        ' SendGrid SMTP
        providers.Add(New SmtpProviderInfo With {
            .Name = "SendGrid SMTP",
            .Hosts = {"smtp.sendgrid.net"},
            .Ports = {587, 25, 465},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "REST API",
            .Notes = "API key authentication recommended"
        })

        ' Mailgun SMTP
        providers.Add(New SmtpProviderInfo With {
            .Name = "Mailgun SMTP",
            .Hosts = {"smtp.mailgun.org"},
            .Ports = {587, 465, 2525},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "REST API",
            .Notes = "Domain verification required"
        })

        ' Sendinblue (Brevo) SMTP
        providers.Add(New SmtpProviderInfo With {
            .Name = "Sendinblue (Brevo) SMTP",
            .Hosts = {"smtp-relay.sendinblue.com"},
            .Ports = {587, 465, 25},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "REST API",
            .Notes = "API key authentication available"
        })

        ' Zoho SMTP
        providers.Add(New SmtpProviderInfo With {
            .Name = "Zoho SMTP",
            .Hosts = {"smtp.zoho.com", "smtp.zoho.eu", "smtp.zoho.in", "smtp.zoho.com.au"},
            .Ports = {587, 465, 25},
            .RequiresSSL = True,
            .SupportsAPI = False,
            .ApiType = "",
            .Notes = "Regional servers available"
        })

        ' Yahoo SMTP
        providers.Add(New SmtpProviderInfo With {
            .Name = "Yahoo SMTP",
            .Hosts = {"smtp.mail.yahoo.com"},
            .Ports = {587, 465, 25},
            .RequiresSSL = True,
            .SupportsAPI = False,
            .ApiType = "",
            .Notes = "App password required for authentication"
        })

        ' Elastic Email
        providers.Add(New SmtpProviderInfo With {
            .Name = "Elastic Email",
            .Hosts = {"smtp.elasticemail.com"},
            .Ports = {2525, 587, 25},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "REST API",
            .Notes = "API key authentication available"
        })

        ' Mailjet
        providers.Add(New SmtpProviderInfo With {
            .Name = "Mailjet",
            .Hosts = {"in-v3.mailjet.com", "in.mailjet.com"},
            .Ports = {587, 25, 465},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "REST API",
            .Notes = "API key and secret required for API access"
        })

        ' SparkPost
        providers.Add(New SmtpProviderInfo With {
            .Name = "SparkPost",
            .Hosts = {"smtp.sparkpostmail.com"},
            .Ports = {587, 2525},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "REST API",
            .Notes = "API key authentication available"
        })

        ' Postmark
        providers.Add(New SmtpProviderInfo With {
            .Name = "Postmark",
            .Hosts = {"smtp.postmarkapp.com"},
            .Ports = {587, 2525, 25},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "REST API",
            .Notes = "Server token required for authentication"
        })

        ' Mandrill (Mailchimp)
        providers.Add(New SmtpProviderInfo With {
            .Name = "Mandrill (Mailchimp)",
            .Hosts = {"smtp.mandrillapp.com"},
            .Ports = {587, 2525, 25},
            .RequiresSSL = True,
            .SupportsAPI = True,
            .ApiType = "REST API",
            .Notes = "API key authentication required"
        })

        Return providers
    End Function

    ' دالة للتعرف التلقائي على مزود خدمة SMTP
    Public Shared Function DetectProvider(host As String) As SmtpProviderInfo
        Dim providers = GetSupportedProviders()
        
        For Each provider In providers
            For Each providerHost In provider.Hosts
                If host.ToLower().Contains(providerHost.ToLower()) OrElse providerHost.ToLower().Contains(host.ToLower()) Then
                    Return provider
                End If
            Next
        Next
        
        ' إذا لم يتم العثور على مزود معروف، قم بإرجاع مزود عام
        Return New SmtpProviderInfo With {
            .Name = "Generic SMTP",
            .Hosts = {host},
            .Ports = {25, 587, 465, 2525},
            .RequiresSSL = True,
            .SupportsAPI = False,
            .ApiType = "",
            .Notes = "Unknown provider, using generic SMTP settings"
        }
    End Function
End Class

' فئة لتخزين معلومات مزود خدمة SMTP
Public Class SmtpProviderInfo
    Public Property Name As String
    Public Property Hosts As String()
    Public Property Ports As Integer()
    Public Property RequiresSSL As Boolean
    Public Property SupportsAPI As Boolean
    Public Property ApiType As String
    Public Property Notes As String
End Class
