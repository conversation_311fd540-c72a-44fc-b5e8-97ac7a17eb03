﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Thread</name>
  </assembly>
  <members>
    <member name="T:System.Threading.ParameterizedThreadStart">
      <summary>
        <see cref="T:System.Threading.Thread" />에서 실행되는 메서드를 나타냅니다.</summary>
      <param name="obj">스레드 프로시저에 대한 데이터가 포함된 개체입니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.Thread">
      <summary>스레드를 만들고 제어하며, 해당 속성을 설정하고, 상태를 가져옵니다. 이 형식에 대한 .NET Framework 소스 코드를 찾아보려면 참조 원본을 참조하세요.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.#ctor(System.Threading.ParameterizedThreadStart)">
      <summary>스레드가 시작될 때 개체가 스레드로 전달될 수 있도록 하는 대리자를 지정하여 <see cref="T:System.Threading.Thread" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="start">이 스레드의 실행이 시작될 때 호출될 메서드를 나타내는 대리자입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="start" /> is null. </exception>
    </member>
    <member name="M:System.Threading.Thread.#ctor(System.Threading.ThreadStart)">
      <summary>
        <see cref="T:System.Threading.Thread" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="start">이 스레드의 실행이 시작될 때 호출될 메서드를 나타내는 <see cref="T:System.Threading.ThreadStart" /> 대리자입니다. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="start" /> parameter is null. </exception>
    </member>
    <member name="P:System.Threading.Thread.CurrentThread">
      <summary>현재 실행 중인 스레드를 가져옵니다.</summary>
      <returns>현재 실행 중인 스레드를 나타내는 <see cref="T:System.Threading.Thread" />입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.IsAlive">
      <summary>현재 스레드의 실행 상태를 나타내는 값을 가져옵니다.</summary>
      <returns>이 스레드가 시작되었으며 정상적으로 종료 또는 중단되지 않았으면 true이고, 그렇지 않으면 false입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.IsBackground">
      <summary>스레드가 배경 스레드인지를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>이 스레드가 백그라운드 스레드이거나 백그라운드 스레드가 될 예정이면 true이고, 그렇지 않으면 false입니다.</returns>
      <exception cref="T:System.Threading.ThreadStateException">The thread is dead. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Join">
      <summary>표준 COM 및 SendMessage 펌프를 계속 수행하면서 스레드가 종료될 때까지 호출 스레드를 차단합니다.</summary>
      <exception cref="T:System.Threading.ThreadStateException">The caller attempted to join a thread that is in the <see cref="F:System.Threading.ThreadState.Unstarted" /> state. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">The thread is interrupted while waiting. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Join(System.Int32)">
      <summary>표준 COM 및 SendMessage 펌프를 계속 수행하면서 스레드가 종료되거나 지정된 시간이 경과할 때까지 호출 스레드를 차단합니다.</summary>
      <returns>스레드가 종료되면 true이고, false 매개 변수에서 지정한 기간이 경과된 후에도 스레드가 종료되지 않으면 <paramref name="millisecondsTimeout" />입니다.</returns>
      <param name="millisecondsTimeout">스레드가 종료되기를 기다릴 밀리초 수입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> in milliseconds. </exception>
      <exception cref="T:System.Threading.ThreadStateException">The thread has not been started. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.ManagedThreadId">
      <summary>현재 관리되는 스레드의 고유 식별자를 가져옵니다. </summary>
      <returns>이 관리되는 스레드의 고유 식별자를 나타내는 정수입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.Name">
      <summary>스레드의 이름을 가져오거나 설정합니다.</summary>
      <returns>스레드의 이름을 포함하는 문자열이며 설정된 이름이 없으면 null입니다.</returns>
      <exception cref="T:System.InvalidOperationException">A set operation was requested, but the Name property has already been set. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Sleep(System.Int32)">
      <summary>지정된 시간(밀리초) 동안 현재 스레드를 일시 중단합니다.</summary>
      <param name="millisecondsTimeout">스레드가 일시 중단되는 밀리초 수입니다.<paramref name="millisecondsTimeout" /> 인수의 값이 0이면 스레드는 실행할 준비가 된 우선 순위가 같은 스레드에 나머지 시간 간격을 내어 줍니다.실행할 준비가 된 우선 순위가 같은 스레드가 없으면 현재 스레드의 실행이 일시 중단되지 않습니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The time-out value is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Sleep(System.TimeSpan)">
      <summary>지정된 시간 동안 현재 스레드를 일시 중단합니다.</summary>
      <param name="timeout">스레드가 일시 중단되는 시간입니다.<paramref name="millisecondsTimeout" /> 인수의 값이 <see cref="F:System.TimeSpan.Zero" />이면 스레드는 실행할 준비가 된 우선 순위가 같은 스레드에 나머지 시간 간격을 내어 줍니다.실행할 준비가 된 우선 순위가 같은 스레드가 없으면 현재 스레드의 실행이 일시 중단되지 않습니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> in milliseconds, or is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Start">
      <summary>운영 체제에서 현재 인스턴스의 상태를 <see cref="F:System.Threading.ThreadState.Running" />으로 변경하도록 합니다.</summary>
      <exception cref="T:System.Threading.ThreadStateException">The thread has already been started. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available to start this thread. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Thread.Start(System.Object)">
      <summary>운영 체제에서 현재 인스턴스의 상태를 <see cref="F:System.Threading.ThreadState.Running" />으로 변경하도록 하며 경우에 따라 스레드가 실행하는 메서드에 사용될 데이터가 포함된 개체를 제공합니다.</summary>
      <param name="parameter">스레드가 실행하는 메서드에 사용될 데이터가 포함된 개체입니다.</param>
      <exception cref="T:System.Threading.ThreadStateException">The thread has already been started. </exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available to start this thread. </exception>
      <exception cref="T:System.InvalidOperationException">This thread was created using a <see cref="T:System.Threading.ThreadStart" /> delegate instead of a <see cref="T:System.Threading.ParameterizedThreadStart" /> delegate.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.Thread.ThreadState">
      <summary>현재 스레드의 상태를 포함하는 값을 가져옵니다.</summary>
      <returns>현재 스레드의 상태를 나타내는 <see cref="T:System.Threading.ThreadState" /> 값 중 하나입니다.초기 값은 Unstarted입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.ThreadStart">
      <summary>
        <see cref="T:System.Threading.Thread" />에서 실행되는 메서드를 나타냅니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.ThreadStartException">
      <summary>내부 운영 체제 스레드가 사용자 코드를 실행할 수 없는 상태로 시작된 후 관리되는 스레드에서 실패가 발생한 경우에 throw되는 예외입니다.</summary>
    </member>
    <member name="T:System.Threading.ThreadState">
      <summary>
        <see cref="T:System.Threading.Thread" />의 실행 상태를 지정합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.Threading.ThreadState.Aborted">
      <summary>스레드 상태에 <see cref="F:System.Threading.ThreadState.AbortRequested" />가 포함되어 있고 스레드가 작동하지 않지만 상태가 아직 <see cref="F:System.Threading.ThreadState.Stopped" />로 변경되지 않았습니다.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.AbortRequested">
      <summary>스레드에 <see cref="M:System.Threading.Thread.Abort(System.Object)" /> 메서드가 호출되었지만 해당 스레드는 자신을 종결시키려는 보류된 <see cref="T:System.Threading.ThreadAbortException" />을 받지 못했습니다.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Background">
      <summary>해당 스레드는 전경 스레드와 반대인 배경 스레드로 실행됩니다.이 상태는 <see cref="P:System.Threading.Thread.IsBackground" /> 속성을 설정하여 제어합니다.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Running">
      <summary>해당 스레드가 차단되지 않고 시작되었고 보류된 <see cref="T:System.Threading.ThreadAbortException" />이 없습니다.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Stopped">
      <summary>스레드가 중지되었습니다.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.StopRequested">
      <summary>스레드를 중지하도록 요청했습니다.이는 내부 전용입니다.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Suspended">
      <summary>스레드가 일시 중단되었습니다.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.SuspendRequested">
      <summary>스레드를 일시 중단하도록 요청하고 있습니다.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.Unstarted">
      <summary>스레드에 <see cref="M:System.Threading.Thread.Start" /> 메서드가 호출되지 않았습니다.</summary>
    </member>
    <member name="F:System.Threading.ThreadState.WaitSleepJoin">
      <summary>스레드가 차단되었습니다.이는 <see cref="M:System.Threading.Thread.Sleep(System.Int32)" /> 또는 <see cref="M:System.Threading.Thread.Join" />을 호출하거나, <see cref="M:System.Threading.Monitor.Enter(System.Object)" /> 또는 <see cref="M:System.Threading.Monitor.Wait(System.Object,System.Int32,System.Boolean)" />를 호출하는 등의 방법으로 잠금을 요청하거나, <see cref="T:System.Threading.ManualResetEvent" />와 같은 스레드 동기화 개체를 기다리고 있는 경우 발생할 수 있습니다.</summary>
    </member>
    <member name="T:System.Threading.ThreadStateException">
      <summary>
        <see cref="T:System.Threading.Thread" />가 메서드 호출에 대한 잘못된 <see cref="P:System.Threading.Thread.ThreadState" />인 경우 throw되는 예외입니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor">
      <summary>기본 속성을 사용하여 <see cref="T:System.Threading.ThreadStateException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 <see cref="T:System.Threading.ThreadStateException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
    </member>
    <member name="M:System.Threading.ThreadStateException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Threading.ThreadStateException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="innerException">현재 예외의 원인이 되는 예외입니다.<paramref name="innerException" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
  </members>
</doc>