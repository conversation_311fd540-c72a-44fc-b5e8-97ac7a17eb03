﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Text.Json</name>
  </assembly>
  <members>
    <member name="T:System.Text.Json.JsonCommentHandling">
      <summary>Defines how the <see cref="T:System.Text.Json.Utf8JsonReader" /> struct handles comments.</summary>
    </member>
    <member name="F:System.Text.Json.JsonCommentHandling.Allow">
      <summary>Allows comments within the JSON input and treats them as valid tokens. While reading, the caller can access the comment values.</summary>
    </member>
    <member name="F:System.Text.Json.JsonCommentHandling.Disallow">
      <summary>Doesn't allow comments within the JSON input. Comments are treated as invalid JSON if found, and a <see cref="T:System.Text.Json.JsonException" /> is thrown. This is the default value.</summary>
    </member>
    <member name="F:System.Text.Json.JsonCommentHandling.Skip">
      <summary>Allows comments within the JSON input and ignores them. The <see cref="T:System.Text.Json.Utf8JsonReader" /> behaves as if no comments are present.</summary>
    </member>
    <member name="T:System.Text.Json.JsonDocument">
      <summary>Provides a mechanism for examining the structural content of a JSON value without automatically instantiating data values.</summary>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Dispose">
      <summary>Releases the resources used by this <see cref="T:System.Text.Json.JsonDocument" /> instance.</summary>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Parse(System.Buffers.ReadOnlySequence{System.Byte},System.Text.Json.JsonDocumentOptions)">
      <summary>Parses a sequence as UTF-8-encoded text representing a single JSON byte value into a JsonDocument.</summary>
      <param name="utf8Json">The JSON text to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="options" /> contains unsupported options.</exception>
      <returns>A JsonDocument representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Parse(System.IO.Stream,System.Text.Json.JsonDocumentOptions)">
      <summary>Parses a <see cref="T:System.IO.Stream" /> as UTF-8-encoded data representing a single JSON value into a JsonDocument. The stream is read to completion.</summary>
      <param name="utf8Json">The JSON data to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="options" /> contains unsupported options.</exception>
      <returns>A JsonDocument representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Parse(System.ReadOnlyMemory{System.Byte},System.Text.Json.JsonDocumentOptions)">
      <summary>Parses memory as UTF-8-encoded text representing a single JSON byte value into a JsonDocument.</summary>
      <param name="utf8Json">The JSON text to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="options" /> contains unsupported options.</exception>
      <returns>A JsonDocument representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Parse(System.ReadOnlyMemory{System.Char},System.Text.Json.JsonDocumentOptions)">
      <summary>Parses text representing a single JSON value into a JsonDocument.</summary>
      <param name="json">The JSON text to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="options" /> contains unsupported options.</exception>
      <returns>A JsonDocument representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonDocument.Parse(System.String,System.Text.Json.JsonDocumentOptions)">
      <summary>Parses text representing a single JSON string value into a JsonDocument.</summary>
      <param name="json">The JSON text to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="options" /> contains unsupported options.</exception>
      <returns>A JsonDocument representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonDocument.ParseAsync(System.IO.Stream,System.Text.Json.JsonDocumentOptions,System.Threading.CancellationToken)">
      <summary>Parses a <see cref="T:System.IO.Stream" /> as UTF-8-encoded data representing a single JSON value into a JsonDocument. The stream is read to completion.</summary>
      <param name="utf8Json">The JSON data to parse.</param>
      <param name="options">Options to control the reader behavior during parsing.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="options" /> contains unsupported options.</exception>
      <returns>A task to produce a JsonDocument representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonDocument.ParseValue(System.Text.Json.Utf8JsonReader@)">
      <summary>Parses one JSON value (including objects or arrays) from the provided reader.</summary>
      <param name="reader">The reader to read.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains unsupported options.

-or-

The current <paramref name="reader" /> token does not start or represent a value.</exception>
      <exception cref="T:System.Text.Json.JsonException">A value could not be read from the reader.</exception>
      <returns>A JsonDocument representing the value (and nested values) read from the reader.</returns>
    </member>
    <member name="M:System.Text.Json.JsonDocument.TryParseValue(System.Text.Json.Utf8JsonReader@,System.Text.Json.JsonDocument@)">
      <summary>Attempts to parse one JSON value (including objects or arrays) from the provided reader.</summary>
      <param name="reader">The reader to read.</param>
      <param name="document">When the method returns, contains the parsed document.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains unsupported options.

-or-

The current <paramref name="reader" /> token does not start or represent a value.</exception>
      <exception cref="T:System.Text.Json.JsonException">A value could not be read from the reader.</exception>
      <returns>
        <see langword="true" /> if a value was read and parsed into a JsonDocument; <see langword="false" /> if the reader ran out of data while parsing. All other situations result in an exception being thrown.</returns>
    </member>
    <member name="M:System.Text.Json.JsonDocument.WriteTo(System.Text.Json.Utf8JsonWriter)">
      <summary>Writes the document to the provided writer as a JSON value.</summary>
      <param name="writer">The writer to which to write the document.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Text.Json.JsonElement.ValueKind" /> of this <see cref="P:System.Text.Json.JsonDocument.RootElement" /> would result in invalid JSON.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="P:System.Text.Json.JsonDocument.RootElement">
      <summary>Gets the root element of this JSON document.</summary>
      <returns>A <see cref="T:System.Text.Json.JsonElement" /> representing the value of the document.</returns>
    </member>
    <member name="T:System.Text.Json.JsonDocumentOptions">
      <summary>Provides the ability for the user to define custom behavior when parsing JSON to create a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
    </member>
    <member name="P:System.Text.Json.JsonDocumentOptions.AllowTrailingCommas">
      <summary>Gets or sets a value that indicates whether an extra comma at the end of a list of JSON values in an object or array is allowed (and ignored) within the JSON payload being read.</summary>
      <returns>
        <see langword="true" /> if an extra comma at the end of a list of JSON values in an object or array is allowed; otherwise, <see langword="false" />. Default is <see langword="false" /></returns>
    </member>
    <member name="P:System.Text.Json.JsonDocumentOptions.CommentHandling">
      <summary>Gets or sets a value that determines how the <see cref="T:System.Text.Json.JsonDocument" /> handles comments when reading through the JSON data.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">The comment handling enum is set to a value that is not supported (or not within the <see cref="T:System.Text.Json.JsonCommentHandling" /> enum range).</exception>
      <returns>One of the enumeration values that indicates how comments are handled.</returns>
    </member>
    <member name="P:System.Text.Json.JsonDocumentOptions.MaxDepth">
      <summary>Gets or sets the maximum depth allowed when parsing JSON data, with the default (that is, 0) indicating a maximum depth of 64.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">The max depth is set to a negative value.</exception>
      <returns>The maximum depth allowed when parsing JSON data.</returns>
    </member>
    <member name="T:System.Text.Json.JsonElement">
      <summary>Represents a specific JSON value within a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
    </member>
    <member name="M:System.Text.Json.JsonElement.Clone">
      <summary>Gets a JsonElement that can be safely stored beyond the lifetime of the original <see cref="T:System.Text.Json.JsonDocument" />.</summary>
      <returns>A JsonElement that can be safely stored beyond the lifetime of the original <see cref="T:System.Text.Json.JsonDocument" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.EnumerateArray">
      <summary>Gets an enumerator to enumerate the values in the JSON array represented by this JsonElement.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Array" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>An enumerator to enumerate the values in the JSON array represented by this JsonElement.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.EnumerateObject">
      <summary>Gets an enumerator to enumerate the properties in the JSON object represented by this JsonElement.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>An enumerator to enumerate the properties in the JSON object represented by this JsonElement.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetArrayLength">
      <summary>Gets the number of values contained within the current array value.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Array" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The number of values contained within the current array value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetBoolean">
      <summary>Gets the value of the element as a <see cref="T:System.Boolean" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is neither <see cref="F:System.Text.Json.JsonValueKind.True" /> nor <see cref="F:System.Text.Json.JsonValueKind.False" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The value of the element as a <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetByte">
      <summary>Gets the current JSON number as a <see cref="T:System.Byte" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Byte" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The current JSON number as a <see cref="T:System.Byte" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetBytesFromBase64">
      <summary>Gets the value of the element as a byte array.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.FormatException">The value is not encoded as Base64 text and hence cannot be decoded to bytes.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The value decoded as a byte array.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetDateTime">
      <summary>Gets the value of the element as a <see cref="T:System.DateTime" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be read as a <see cref="T:System.DateTime" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The value of the element as a <see cref="T:System.DateTime" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetDateTimeOffset">
      <summary>Gets the value of the element as a <see cref="T:System.DateTimeOffset" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be read as a <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The value of the element as a <see cref="T:System.DateTimeOffset" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetDecimal">
      <summary>Gets the current JSON number as a <see cref="T:System.Decimal" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The current JSON number as a <see cref="T:System.Decimal" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetDouble">
      <summary>Gets the current JSON number as a <see cref="T:System.Double" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Double" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The current JSON number as a <see cref="T:System.Double" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetGuid">
      <summary>Gets the value of the element as a <see cref="T:System.Guid" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Guid" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The value of the element as a <see cref="T:System.Guid" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetInt16">
      <summary>Gets the current JSON number as an <see cref="T:System.Int16" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as an <see cref="T:System.Int16" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The current JSON number as an <see cref="T:System.Int16" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetInt32">
      <summary>Gets the current JSON number as an <see cref="T:System.Int32" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as an <see cref="T:System.Int32" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The current JSON number as an <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetInt64">
      <summary>Gets the current JSON number as an <see cref="T:System.Int64" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Int64" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The current JSON number as an <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetProperty(System.ReadOnlySpan{System.Byte})">
      <summary>Gets a <see cref="T:System.Text.Json.JsonElement" /> representing the value of a required property identified by <paramref name="utf8PropertyName" />.</summary>
      <param name="utf8PropertyName">The UTF-8 representation (with no Byte-Order-Mark (BOM)) of the name of the property to return.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">No property was found with the requested name.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>A <see cref="T:System.Text.Json.JsonElement" /> representing the value of the requested property.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetProperty(System.ReadOnlySpan{System.Char})">
      <summary>Gets a <see cref="T:System.Text.Json.JsonElement" /> representing the value of a required property identified by <paramref name="propertyName" />.</summary>
      <param name="propertyName">The name of the property whose value is to be returned.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">No property was found with the requested name.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>A <see cref="T:System.Text.Json.JsonElement" /> representing the value of the requested property.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetProperty(System.String)">
      <summary>Gets a <see cref="T:System.Text.Json.JsonElement" /> representing the value of a required property identified by <paramref name="propertyName" />.</summary>
      <param name="propertyName">The name of the property whose value is to be returned.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">No property was found with the requested name.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>A <see cref="T:System.Text.Json.JsonElement" /> representing the value of the requested property.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetRawText">
      <summary>Gets a string that represents the original input data backing this value.</summary>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The original input data backing this value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetSByte">
      <summary>Gets the current JSON number as an <see cref="T:System.SByte" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as an <see cref="T:System.SByte" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The current JSON number as an <see cref="T:System.SByte" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetSingle">
      <summary>Gets the current JSON number as a <see cref="T:System.Single" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.Single" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The current JSON number as a <see cref="T:System.Single" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetString">
      <summary>Gets the value of the element as a <see cref="T:System.String" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is neither <see cref="F:System.Text.Json.JsonValueKind.String" /> nor <see cref="F:System.Text.Json.JsonValueKind.Null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The value of the element as a <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetUInt16">
      <summary>Gets the current JSON number as a <see cref="T:System.UInt16" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.UInt16" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The current JSON number as a <see cref="T:System.UInt16" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetUInt32">
      <summary>Gets the current JSON number as a <see cref="T:System.UInt32" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.UInt32" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The current JSON number as a <see cref="T:System.UInt32" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.GetUInt64">
      <summary>Gets the current JSON number as a <see cref="T:System.UInt64" />.</summary>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.FormatException">The value cannot be represented as a <see cref="T:System.UInt64" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The current JSON number as a <see cref="T:System.UInt64" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ParseValue(System.Text.Json.Utf8JsonReader@)">
      <summary>Parses one JSON value (including objects or arrays) from the provided reader.</summary>
      <param name="reader">The reader to read.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> is using unsupported options.</exception>
      <exception cref="T:System.ArgumentException">The current <paramref name="reader" /> token does not start or represent a value.</exception>
      <exception cref="T:System.Text.Json.JsonException">A value could not be read from the reader.</exception>
      <returns>A JsonElement representing the value (and nested values) read from the reader.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ToString">
      <summary>Gets a string representation for the current value appropriate to the value type.</summary>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>A string representation for the current value appropriate to the value type.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetByte(System.Byte@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.Byte" />.</summary>
      <param name="value">When the method returns, contains the byte equivalent of the current JSON number if the conversion succeeded.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.Byte" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetBytesFromBase64(System.Byte[]@)">
      <summary>Attempts to represent the current JSON string as a byte array, assuming that it is Base64 encoded.</summary>
      <param name="value">If the method succeeds, contains the decoded binary representation of the Base64 text.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the entire token value is encoded as valid Base64 text and can be successfully decoded to bytes; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetDateTime(System.DateTime@)">
      <summary>Attempts to represent the current JSON string as a <see cref="T:System.DateTime" />.</summary>
      <param name="value">When this method returns, contains the date and time value equivalent to the current JSON string.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the string can be represented as a <see cref="T:System.DateTime" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetDateTimeOffset(System.DateTimeOffset@)">
      <summary>Attempts to represent the current JSON string as a <see cref="T:System.DateTimeOffset" />.</summary>
      <param name="value">When this method returns, contains the date and time equivalent to the current JSON string.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the string can be represented as a <see cref="T:System.DateTimeOffset" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetDecimal(System.Decimal@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.Decimal" />.</summary>
      <param name="value">When this method returns, contains the decimal equivalent of the current JSON number.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.Decimal" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetDouble(System.Double@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.Double" />.</summary>
      <param name="value">When this method returns, contains a double-precision floating point value equivalent to the current JSON number.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.Double" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetGuid(System.Guid@)">
      <summary>Attempts to represent the current JSON string as a <see cref="T:System.Guid" />.</summary>
      <param name="value">When this method returns, contains the GUID equivalent to the current JSON string.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the string can be represented as a <see cref="T:System.Guid" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetInt16(System.Int16@)">
      <summary>Attempts to represent the current JSON number as an <see cref="T:System.Int16" />.</summary>
      <param name="value">When the method returns, contains the 16-bit integer equivalent of the current JSON number if the conversion succeeded.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the number can be represented as an <see cref="T:System.Int16" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetInt32(System.Int32@)">
      <summary>Attempts to represent the current JSON number as an <see cref="T:System.Int32" />.</summary>
      <param name="value">When this method returns, contains the 32-bit integer value equivalent to the current JSON number.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the number can be represented as an <see cref="T:System.Int32" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetInt64(System.Int64@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.Int64" />.</summary>
      <param name="value">When this method returns, contains the 64-bit integer value equivalent to the current JSON number.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.Int64" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetProperty(System.ReadOnlySpan{System.Byte},System.Text.Json.JsonElement@)">
      <summary>Looks for a property named <paramref name="utf8PropertyName" /> in the current object, returning a value that indicates whether or not such a property exists. When the property exists, the method assigns its value to the <paramref name="value" /> argument.</summary>
      <param name="utf8PropertyName">The UTF-8 (with no Byte-Order-Mark (BOM)) representation of the name of the property to return.</param>
      <param name="value">Receives the value of the located property.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the property was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetProperty(System.ReadOnlySpan{System.Char},System.Text.Json.JsonElement@)">
      <summary>Looks for a property named <paramref name="propertyName" /> in the current object, returning a value that indicates whether or not such a property exists. When the property exists, the method assigns its value to the <paramref name="value" /> argument.</summary>
      <param name="propertyName">The name of the property to find.</param>
      <param name="value">When this method returns, contains the value of the specified property.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the property was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetProperty(System.String,System.Text.Json.JsonElement@)">
      <summary>Looks for a property named <paramref name="propertyName" /> in the current object, returning a value that indicates whether or not such a property exists. When the property exists, its value is assigned to the <paramref name="value" /> argument.</summary>
      <param name="propertyName">The name of the property to find.</param>
      <param name="value">When this method returns, contains the value of the specified property.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Object" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the property was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetSByte(System.SByte@)">
      <summary>Attempts to represent the current JSON number as an <see cref="T:System.SByte" />.</summary>
      <param name="value">When the method returns, contains the signed byte equivalent of the current JSON number if the conversion succeeded.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the number can be represented as an <see cref="T:System.SByte" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetSingle(System.Single@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.Single" />.</summary>
      <param name="value">When this method returns, contains the single-precision floating point value equivalent to the current JSON number.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.Single" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetUInt16(System.UInt16@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.UInt16" />.</summary>
      <param name="value">When the method returns, contains the unsigned 16-bit integer equivalent of the current JSON number if the conversion succeeded.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.UInt16" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetUInt32(System.UInt32@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.UInt32" />.</summary>
      <param name="value">When this method returns, contains unsigned 32-bit integer value equivalent to the current JSON number.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.UInt32" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryGetUInt64(System.UInt64@)">
      <summary>Attempts to represent the current JSON number as a <see cref="T:System.UInt64" />.</summary>
      <param name="value">When this method returns, contains unsigned 64-bit integer value equivalent to the current JSON number.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Number" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>
        <see langword="true" /> if the number can be represented as a <see cref="T:System.UInt64" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.TryParseValue(System.Text.Json.Utf8JsonReader@,System.Nullable{System.Text.Json.JsonElement}@)">
      <summary>Attempts to parse one JSON value (including objects or arrays) from the provided reader.</summary>
      <param name="reader">The reader to read.</param>
      <param name="element">Receives the parsed element.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> is using unsupported options.</exception>
      <exception cref="T:System.ArgumentException">The current <paramref name="reader" /> token does not start or represent a value.</exception>
      <exception cref="T:System.Text.Json.JsonException">A value could not be read from the reader.</exception>
      <returns>
        <see langword="true" /> if a value was read and parsed into a JsonElement; <see langword="false" /> if the reader ran out of data while parsing.
               All other situations result in an exception being thrown.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ValueEquals(System.ReadOnlySpan{System.Byte})">
      <summary>Compares the text represented by a UTF8-encoded byte span to the string value of this element.</summary>
      <param name="utf8Text">The UTF-8 encoded text to compare against.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <returns>
        <see langword="true" /> if the string value of this element has the same UTF-8 encoding as
              <paramref name="utf8Text" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ValueEquals(System.ReadOnlySpan{System.Char})">
      <summary>Compares a specified read-only character span to the string value of this element.</summary>
      <param name="text">The text to compare against.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <returns>
        <see langword="true" /> if the string value of this element matches <paramref name="text" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ValueEquals(System.String)">
      <summary>Compares a specified string to the string value of this element.</summary>
      <param name="text">The text to compare against.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.String" />.</exception>
      <returns>
        <see langword="true" /> if the string value of this element matches <paramref name="text" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.WriteTo(System.Text.Json.Utf8JsonWriter)">
      <summary>Writes the element to the specified writer as a JSON value.</summary>
      <param name="writer">The writer to which to write the element.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Text.Json.JsonElement.ValueKind" /> of this value is <see cref="F:System.Text.Json.JsonValueKind.Undefined" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="P:System.Text.Json.JsonElement.Item(System.Int32)">
      <summary>Gets the value at the specified index if the current value is an <see cref="F:System.Text.Json.JsonValueKind.Array" />.</summary>
      <param name="index">The item index.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="P:System.Text.Json.JsonElement.ValueKind" /> is not <see cref="F:System.Text.Json.JsonValueKind.Array" />.</exception>
      <exception cref="T:System.IndexOutOfRangeException">
        <paramref name="index" /> is not in the range [0, <see cref="M:System.Text.Json.JsonElement.GetArrayLength" />()).</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The value at the specified index.</returns>
    </member>
    <member name="P:System.Text.Json.JsonElement.ValueKind">
      <summary>Gets the type of the current JSON value.</summary>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
      <returns>The type of the current JSON value.</returns>
    </member>
    <member name="T:System.Text.Json.JsonElement.ArrayEnumerator">
      <summary>Represents an enumerator for the contents of a JSON array.</summary>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.Dispose">
      <summary>Releases the resources used by this <see cref="T:System.Text.Json.JsonElement.ArrayEnumerator" /> instance.</summary>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator that can be used to iterate through the array.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the collection.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.System#Collections#Generic#IEnumerable{System#Text#Json#JsonElement}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator for an array of <see cref="T:System.Text.Json.JsonElement" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ArrayEnumerator.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Text.Json.JsonElement.ArrayEnumerator.Current">
      <summary>Gets the element in the collection at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
    </member>
    <member name="P:System.Text.Json.JsonElement.ArrayEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element in the collection at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
    </member>
    <member name="T:System.Text.Json.JsonElement.ObjectEnumerator">
      <summary>Represents an enumerator for the properties of a JSON object.</summary>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.Dispose">
      <summary>Releases the resources used by this <see cref="T:System.Text.Json.JsonElement.ObjectEnumerator" /> instance.</summary>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.GetEnumerator">
      <summary>Returns an enumerator that iterates the properties of an object.</summary>
      <returns>An enumerator that can be used to iterate through the object.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the collection.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.System#Collections#Generic#IEnumerable{System#Text#Json#JsonProperty}#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator for <see cref="T:System.Text.Json.JsonProperty" /> objects that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Text.Json.JsonElement.ObjectEnumerator.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Text.Json.JsonElement.ObjectEnumerator.Current">
      <summary>Gets the element in the collection at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
    </member>
    <member name="P:System.Text.Json.JsonElement.ObjectEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the element in the collection at the current position of the enumerator.</summary>
      <returns>The element in the collection at the current position of the enumerator.</returns>
    </member>
    <member name="T:System.Text.Json.JsonEncodedText">
      <summary>Provides methods to transform UTF-8 or UTF-16 encoded text into a form that is suitable for JSON.</summary>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.Encode(System.ReadOnlySpan{System.Byte},System.Text.Encodings.Web.JavaScriptEncoder)">
      <summary>Encodes a UTF-8 text value as a JSON string.</summary>
      <param name="utf8Value">The UTF-8 encoded text to convert to JSON encoded text.</param>
      <param name="encoder">The encoder to use when escaping the string, or <see langword="null" /> to use the default encoder.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="utf8Value" /> is too large.

-or-

<paramref name="utf8Value" /> contains invalid UTF-8 bytes.</exception>
      <returns>The encoded JSON text.</returns>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.Encode(System.ReadOnlySpan{System.Char},System.Text.Encodings.Web.JavaScriptEncoder)">
      <summary>Encodes a specified text value as a JSON string.</summary>
      <param name="value">The value to convert to JSON encoded text.</param>
      <param name="encoder">The encoder to use when escaping the string, or <see langword="null" /> to use the default encoder.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is too large.

-or-

<paramref name="value" /> contains invalid UTF-16 characters.</exception>
      <returns>The encoded JSON text.</returns>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.Encode(System.String,System.Text.Encodings.Web.JavaScriptEncoder)">
      <summary>Encodes the string text value as a JSON string.</summary>
      <param name="value">The value to convert to JSON encoded text.</param>
      <param name="encoder">The encoder to use when escaping the string, or <see langword="null" /> to use the default encoder.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is too large.

-or-

<paramref name="value" /> contains invalid UTF-16 characters.</exception>
      <returns>The encoded JSON text.</returns>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.Equals(System.Object)">
      <summary>Determines whether this instance and a specified object, which must also be a <see cref="T:System.Text.Json.JsonEncodedText" /> instance, have the same value.</summary>
      <param name="obj">The object to compare to this instance.</param>
      <returns>
        <see langword="true" /> if the current instance and <paramref name="obj" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.Equals(System.Text.Json.JsonEncodedText)">
      <summary>Determines whether this instance and another specified <see cref="T:System.Text.Json.JsonEncodedText" /> instance have the same value.</summary>
      <param name="other">The object to compare to this instance.</param>
      <returns>
        <see langword="true" /> if this instance and <paramref name="other" /> have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.GetHashCode">
      <summary>Returns the hash code for this <see cref="T:System.Text.Json.JsonEncodedText" />.</summary>
      <returns>The hash code for this instance.</returns>
    </member>
    <member name="M:System.Text.Json.JsonEncodedText.ToString">
      <summary>Converts the value of this instance to a <see cref="T:System.String" />.</summary>
      <returns>The underlying UTF-16 encoded string.</returns>
    </member>
    <member name="P:System.Text.Json.JsonEncodedText.EncodedUtf8Bytes">
      <summary>Gets the UTF-8 encoded representation of the pre-encoded JSON text.</summary>
      <returns>The UTF-8 encoded representation of the pre-encoded JSON text.</returns>
    </member>
    <member name="T:System.Text.Json.JsonException">
      <summary>Defines a custom exception object that is thrown when invalid JSON text is encountered, when the defined maximum depth is passed, or the JSON text is not compatible with the type of a property on an object.</summary>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.JsonException" /> class.</summary>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Creates a new exception object with serialized data.</summary>
      <param name="info">The serialized object data about the exception being thrown.</param>
      <param name="context">An object that contains contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.JsonException" /> class with a specified error message.</summary>
      <param name="message">The context-specific error message.</param>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.JsonException" /> class, with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The context-specific error message.</param>
      <param name="innerException">The exception that caused the current exception.</param>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor(System.String,System.String,System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Creates a new exception object to relay error information to the user.</summary>
      <param name="message">The context-specific error message.</param>
      <param name="path">The path where the invalid JSON was encountered.</param>
      <param name="lineNumber">The line number (starting at 0) at which the invalid JSON was encountered when deserializing.</param>
      <param name="bytePositionInLine">The byte count within the current line (starting at 0) where the invalid JSON was encountered.</param>
    </member>
    <member name="M:System.Text.Json.JsonException.#ctor(System.String,System.String,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Exception)">
      <summary>Creates a new exception object to relay error information to the user that includes a specified inner exception.</summary>
      <param name="message">The context-specific error message.</param>
      <param name="path">The path where the invalid JSON was encountered.</param>
      <param name="lineNumber">The line number (starting at 0) at which the invalid JSON was encountered when deserializing.</param>
      <param name="bytePositionInLine">The byte count (starting at 0) within the current line where the invalid JSON was encountered.</param>
      <param name="innerException">The exception that caused the current exception.</param>
    </member>
    <member name="M:System.Text.Json.JsonException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with information about the exception.</summary>
      <param name="info">The serialized object data about the exception being thrown.</param>
      <param name="context">An object that contains contextual information about the source or destination.</param>
    </member>
    <member name="P:System.Text.Json.JsonException.BytePositionInLine">
      <summary>Gets the zero-based number of bytes read within the current line before the exception.</summary>
      <returns>The zero-based number of bytes read within the current line before the exception.</returns>
    </member>
    <member name="P:System.Text.Json.JsonException.LineNumber">
      <summary>Gets the zero-based number of lines read before the exception.</summary>
      <returns>The zero-based number of lines read before the exception.</returns>
    </member>
    <member name="P:System.Text.Json.JsonException.Message">
      <summary>Gets a message that describes the current exception.</summary>
      <returns>The error message that describes the current exception.</returns>
    </member>
    <member name="P:System.Text.Json.JsonException.Path">
      <summary>Gets The path within the JSON where the exception was encountered.</summary>
      <returns>The path within the JSON where the exception was encountered.</returns>
    </member>
    <member name="T:System.Text.Json.JsonNamingPolicy">
      <summary>Determines the naming policy used to convert a string-based name to another format, such as a camel-casing format.</summary>
    </member>
    <member name="M:System.Text.Json.JsonNamingPolicy.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.JsonNamingPolicy" />.</summary>
    </member>
    <member name="M:System.Text.Json.JsonNamingPolicy.ConvertName(System.String)">
      <summary>When overridden in a derived class, converts the specified name according to the policy.</summary>
      <param name="name">The name to convert.</param>
      <returns>The converted name.</returns>
    </member>
    <member name="P:System.Text.Json.JsonNamingPolicy.CamelCase">
      <summary>Gets the naming policy for camel-casing.</summary>
      <returns>The naming policy for camel-casing.</returns>
    </member>
    <member name="T:System.Text.Json.JsonProperty">
      <summary>Represents a single property for a JSON object.</summary>
    </member>
    <member name="M:System.Text.Json.JsonProperty.NameEquals(System.ReadOnlySpan{System.Byte})">
      <summary>Compares the specified UTF-8 encoded text to the name of this property.</summary>
      <param name="utf8Text">The UTF-8 encoded text to compare against.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="T:System.Type" /> is not <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />.</exception>
      <returns>
        <see langword="true" /> if the name of this property has the same UTF-8 encoding as <paramref name="utf8Text" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonProperty.NameEquals(System.ReadOnlySpan{System.Char})">
      <summary>Compares the specified text as a character span to the name of this property.</summary>
      <param name="text">The text to compare against.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="T:System.Type" /> is not <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />.</exception>
      <returns>
        <see langword="true" /> if the name of this property matches <paramref name="text" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonProperty.NameEquals(System.String)">
      <summary>Compares the specified string to the name of this property.</summary>
      <param name="text">The text to compare against.</param>
      <exception cref="T:System.InvalidOperationException">This value's <see cref="T:System.Type" /> is not <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />.</exception>
      <returns>
        <see langword="true" /> if the name of this property matches <paramref name="text" />; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.JsonProperty.ToString">
      <summary>Provides a string representation of the property for debugging purposes.</summary>
      <returns>A string containing the uninterpreted value of the property, beginning at the declaring open-quote and ending at the last character that is part of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonProperty.WriteTo(System.Text.Json.Utf8JsonWriter)">
      <summary>Writes the property to the provided writer as a named JSON object property.</summary>
      <param name="writer">The writer to which to write the property.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Text.Json.JsonProperty.Name" /> is too large to be a JSON object property.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Text.Json.JsonElement.ValueKind" /> of this JSON property's <see cref="P:System.Text.Json.JsonProperty.Value" /> would result in invalid JSON.</exception>
      <exception cref="T:System.ObjectDisposedException">The parent <see cref="T:System.Text.Json.JsonDocument" /> has been disposed.</exception>
    </member>
    <member name="P:System.Text.Json.JsonProperty.Name">
      <summary>Gets the name of this property.</summary>
      <returns>The name of this property.</returns>
    </member>
    <member name="P:System.Text.Json.JsonProperty.Value">
      <summary>Gets the value of this property.</summary>
      <returns>The value of this property.</returns>
    </member>
    <member name="T:System.Text.Json.JsonReaderOptions">
      <summary>Provides the ability for the user to define custom behavior when reading JSON.</summary>
    </member>
    <member name="P:System.Text.Json.JsonReaderOptions.AllowTrailingCommas">
      <summary>Gets or sets a value that defines whether an extra comma at the end of a list of JSON values in an object or array is allowed (and ignored) within the JSON payload being read.</summary>
      <returns>
        <see langword="true" /> if an extra comma is allowed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Text.Json.JsonReaderOptions.CommentHandling">
      <summary>Gets or sets a value that determines how the <see cref="T:System.Text.Json.Utf8JsonReader" /> handles comments when reading through the JSON data.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is being set to a value that is not a member of the <see cref="T:System.Text.Json.JsonCommentHandling" /> enumeration.</exception>
      <returns>One of the enumeration values that indicates how comments are handled.</returns>
    </member>
    <member name="P:System.Text.Json.JsonReaderOptions.MaxDepth">
      <summary>Gets or sets the maximum depth allowed when reading JSON, with the default (that is, 0) indicating a maximum depth of 64.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">The maximum depth is being set to a negative value.</exception>
      <returns>The maximum depth allowed when reading JSON.</returns>
    </member>
    <member name="T:System.Text.Json.JsonReaderState">
      <summary>Defines an opaque type that holds and saves all the relevant state information, which must be provided to the <see cref="T:System.Text.Json.Utf8JsonReader" /> to continue reading after processing incomplete data.</summary>
    </member>
    <member name="M:System.Text.Json.JsonReaderState.#ctor(System.Text.Json.JsonReaderOptions)">
      <summary>Constructs a new <see cref="T:System.Text.Json.JsonReaderState" /> instance.</summary>
      <param name="options">Defines the customized behavior of the <see cref="T:System.Text.Json.Utf8JsonReader" /> that is different from the JSON RFC (for example how to handle comments, or the maximum depth allowed when reading). By default, the <see cref="T:System.Text.Json.Utf8JsonReader" /> follows the JSON RFC strictly (comments within the JSON are invalid) and reads up to a maximum depth of 64.</param>
      <exception cref="T:System.ArgumentException">The maximum depth is set to a non-positive value (&lt; 0).</exception>
    </member>
    <member name="P:System.Text.Json.JsonReaderState.Options">
      <summary>Gets the custom behavior to use when reading JSON data using the <see cref="T:System.Text.Json.Utf8JsonReader" /> struct that may deviate from strict adherence to the JSON specification, which is the default behavior.</summary>
      <returns>The custom behavior to use when reading JSON data.</returns>
    </member>
    <member name="T:System.Text.Json.JsonSerializer">
      <summary>Provides functionality to serialize objects or value types to JSON and to deserialize JSON into objects or value types.</summary>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.IO.Stream,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Reads the UTF-8 encoded text representing a single JSON value into a <paramref name="returnType" />.
            The Stream will be read to completion.</summary>
      <param name="utf8Json">JSON data to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the behavior during reading.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> or <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid, the <paramref name="returnType" /> is not compatible with the JSON, or there is remaining data in the Stream.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.IO.Stream,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Reads the UTF-8 encoded text representing a single JSON value into a <paramref name="returnType" />.
            The Stream will be read to completion.</summary>
      <param name="utf8Json">JSON data to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" />, <paramref name="returnType" />, or <paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid, the <paramref name="returnType" /> is not compatible with the JSON, or there is remaining data in the Stream.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method on the provided <paramref name="context" /> did not return a compatible <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> for <paramref name="returnType" />.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.ReadOnlySpan{System.Byte},System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Parses the UTF-8 encoded text representing a single JSON value into an instance of a specified type.</summary>
      <param name="utf8Json">The JSON text to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<typeparamref name="returnType" /> is not compatible with the JSON.

-or-

There is remaining data in the span beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.ReadOnlySpan{System.Byte},System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Parses the UTF-8 encoded text representing a single JSON value into a <paramref name="returnType" />.</summary>
      <param name="utf8Json">JSON text to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid, <paramref name="returnType" /> is not compatible with the JSON, or there is remaining data in the Stream.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method on the provided <paramref name="context" /> did not return a compatible <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> for <paramref name="returnType" />.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.ReadOnlySpan{System.Char},System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Parses the text representing a single JSON value into an instance of a specified type.</summary>
      <param name="json">The JSON text to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<paramref name="returnType" /> is not compatible with the JSON.

-or-

There is remaining data in the span beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.ReadOnlySpan{System.Char},System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Parses the text representing a single JSON value into a <paramref name="returnType" />.</summary>
      <param name="json">JSON text to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="json" /> or <paramref name="returnType" /> is <see langword="null" />.

-or-

<paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<paramref name="returnType" /> is not compatible with the JSON.

-or-

There is remaining data in the string beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method of the provided <paramref name="context" /> returns <see langword="null" /> for the type to convert.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.String,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Parses the text representing a single JSON value into an instance of a specified type.</summary>
      <param name="json">The JSON text to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="json" /> or <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<typeparamref name="TValue" /> is not compatible with the JSON.

-or-

There is remaining data in the string beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.String,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Parses the text representing a single JSON value into a <paramref name="returnType" />.</summary>
      <param name="json">JSON text to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="json" /> or <paramref name="returnType" /> is <see langword="null" />.

-or-

<paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<paramref name="returnType" /> is not compatible with the JSON.

-or-

There is remaining data in the string beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method of the provided <paramref name="context" /> returns <see langword="null" /> for the type to convert.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.Text.Json.JsonDocument,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the <see cref="T:System.Text.Json.JsonDocument" /> representing a single JSON value into a <paramref name="returnType" />.</summary>
      <param name="document">The <see cref="T:System.Text.Json.JsonDocument" /> to convert.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="document" /> or <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="returnType" /> is not compatible with the JSON.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.Text.Json.JsonDocument,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Converts the <see cref="T:System.Text.Json.JsonDocument" /> representing a single JSON value into a <paramref name="returnType" />.</summary>
      <param name="document">The <see cref="T:System.Text.Json.JsonDocument" /> to convert.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="document" /> is <see langword="null" />.

-or-

<paramref name="returnType" /> is <see langword="null" />.

-or-

<paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<paramref name="returnType" /> is not compatible with the JSON.

-or-

There is remaining data in the string beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method of the provided <paramref name="context" /> returns <see langword="null" /> for the type to convert.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.Text.Json.JsonElement,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the <see cref="T:System.Text.Json.JsonElement" /> representing a single JSON value into a <paramref name="returnType" />.</summary>
      <param name="element">The <see cref="T:System.Text.Json.JsonElement" /> to convert.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="returnType" /> is not compatible with the JSON.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.Text.Json.JsonElement,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Converts the <see cref="T:System.Text.Json.JsonElement" /> representing a single JSON value into a <paramref name="returnType" />.</summary>
      <param name="element">The <see cref="T:System.Text.Json.JsonElement" /> to convert.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> is <see langword="null" />.

-or-

<paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<paramref name="returnType" /> is not compatible with the JSON.

-or-

There is remaining data in the string beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method of the provided <paramref name="context" /> returns <see langword="null" /> for the type to convert.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.Text.Json.Nodes.JsonNode,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the <see cref="T:System.Text.Json.Nodes.JsonNode" /> representing a single JSON value into a <paramref name="returnType" />.</summary>
      <param name="node">The <see cref="T:System.Text.Json.Nodes.JsonNode" /> to convert.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="returnType" /> is not compatible with the JSON.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.Text.Json.Nodes.JsonNode,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Converts the <see cref="T:System.Text.Json.Nodes.JsonNode" /> representing a single JSON value into a <paramref name="returnType" />.</summary>
      <param name="node">The <see cref="T:System.Text.Json.Nodes.JsonNode" /> to convert.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> is <see langword="null" />.

-or-

<paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<paramref name="returnType" /> is not compatible with the JSON.

-or-

There is remaining data in the string beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method of the provided <paramref name="context" /> returns <see langword="null" /> for the type to convert.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Reads one JSON value (including objects or arrays) from the provided reader and converts it into an instance of  a specified type.</summary>
      <param name="reader">The reader to read the JSON from.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the serializer behavior during reading.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<typeparamref name="returnType" /> is not compatible with the JSON.

-or-

A value could not be read from the reader.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> is using unsupported options.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Reads one JSON value (including objects or arrays) from the provided reader into a <paramref name="returnType" />.</summary>
      <param name="reader">The reader to read.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> or <paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid, <paramref name="returnType" /> is not compatible with the JSON, or a value could not be read from the reader.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> is using unsupported options.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method on the provided <paramref name="context" /> did not return a compatible <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> for <paramref name="returnType" />.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.IO.Stream,System.Text.Json.JsonSerializerOptions)">
      <summary>Reads the UTF-8 encoded text representing a single JSON value into a <typeparamref name="TValue" />.
            The Stream will be read to completion.</summary>
      <param name="utf8Json">JSON data to parse.</param>
      <param name="options">Options to control the behavior during reading.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid, <typeparamref name="TValue" /> is not compatible with the JSON, or there is remaining data in the Stream.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.IO.Stream,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Reads the UTF-8 encoded text representing a single JSON value into a <typeparamref name="TValue" />.
            The Stream will be read to completion.</summary>
      <param name="utf8Json">JSON data to parse.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> or <paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid, <typeparamref name="TValue" /> is not compatible with the JSON, or there is remaining data in the Stream.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.ReadOnlySpan{System.Byte},System.Text.Json.JsonSerializerOptions)">
      <summary>Parses the UTF-8 encoded text representing a single JSON value into an instance of the type specified by a generic type parameter.</summary>
      <param name="utf8Json">The JSON text to parse.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <typeparam name="TValue">The target type of the UTF-8 encoded text.</typeparam>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<typeparamref name="TValue" /> is not compatible with the JSON.

-or-

There is remaining data in the span beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.ReadOnlySpan{System.Byte},System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Parses the UTF-8 encoded text representing a single JSON value into a <typeparamref name="TValue" />.</summary>
      <param name="utf8Json">JSON text to parse.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid, <typeparamref name="TValue" /> is not compatible with the JSON, or there is remaining data in the Stream.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.ReadOnlySpan{System.Char},System.Text.Json.JsonSerializerOptions)">
      <summary>Parses the text representing a single JSON value into an instance of the type specified by a generic type parameter.</summary>
      <param name="json">The JSON text to parse.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<typeparamref name="TValue" /> is not compatible with the JSON.

-or-

There is remaining data in the span beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.ReadOnlySpan{System.Char},System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Parses the text representing a single JSON value into a <typeparamref name="TValue" />.</summary>
      <param name="json">JSON text to parse.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="json" /> is <see langword="null" />.

-or-

<paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<typeparamref name="TValue" /> is not compatible with the JSON.

-or-

There is remaining data in the string beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.String,System.Text.Json.JsonSerializerOptions)">
      <summary>Parses the text representing a single JSON value into an instance of the type specified by a generic type parameter.</summary>
      <param name="json">The JSON text to parse.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <typeparam name="TValue">The target type of the JSON value.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="json" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<typeparamref name="TValue" /> is not compatible with the JSON.

-or-

There is remaining data in the string beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.String,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Parses the text representing a single JSON value into a <typeparamref name="TValue" />.</summary>
      <param name="json">JSON text to parse.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="json" /> is <see langword="null" />.

-or-

<paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<typeparamref name="TValue" /> is not compatible with the JSON.

-or-

There is remaining data in the string beyond a single JSON value.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.Text.Json.JsonDocument,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the <see cref="T:System.Text.Json.JsonDocument" /> representing a single JSON value into a <typeparamref name="TValue" />.</summary>
      <param name="document">The <see cref="T:System.Text.Json.JsonDocument" /> to convert.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="document" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">
        <typeparamref name="TValue" /> is not compatible with the JSON.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.Text.Json.JsonDocument,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Converts the <see cref="T:System.Text.Json.JsonDocument" /> representing a single JSON value into a <typeparamref name="TValue" />.</summary>
      <param name="document">The <see cref="T:System.Text.Json.JsonDocument" /> to convert.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="document" /> is <see langword="null" />.

-or-

<paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">
        <typeparamref name="TValue" /> is not compatible with the JSON.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.Text.Json.JsonElement,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the <see cref="T:System.Text.Json.JsonElement" /> representing a single JSON value into a <typeparamref name="TValue" />.</summary>
      <param name="element">The <see cref="T:System.Text.Json.JsonElement" /> to convert.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.Text.Json.JsonException">
        <typeparamref name="TValue" /> is not compatible with the JSON.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.Text.Json.JsonElement,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Converts the <see cref="T:System.Text.Json.JsonElement" /> representing a single JSON value into a <typeparamref name="TValue" />.</summary>
      <param name="element">The <see cref="T:System.Text.Json.JsonElement" /> to convert.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">
        <typeparamref name="TValue" /> is not compatible with the JSON.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.Text.Json.Nodes.JsonNode,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the <see cref="T:System.Text.Json.Nodes.JsonNode" /> representing a single JSON value into a <typeparamref name="TValue" />.</summary>
      <param name="node">The <see cref="T:System.Text.Json.Nodes.JsonNode" /> to convert.</param>
      <param name="options">Options to control the behavior during parsing.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.Text.Json.JsonException">
        <typeparamref name="TValue" /> is not compatible with the JSON.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.Text.Json.Nodes.JsonNode,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Converts the <see cref="T:System.Text.Json.Nodes.JsonNode" /> representing a single JSON value into a <typeparamref name="TValue" />.</summary>
      <param name="node">The <see cref="T:System.Text.Json.Nodes.JsonNode" /> to convert.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">
        <typeparamref name="TValue" /> is not compatible with the JSON.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.Text.Json.Utf8JsonReader@,System.Text.Json.JsonSerializerOptions)">
      <summary>Reads one JSON value (including objects or arrays) from the provided reader into an instance of the type specified by a generic type parameter.</summary>
      <param name="reader">The reader to read the JSON from.</param>
      <param name="options">Options to control serializer behavior during reading.</param>
      <typeparam name="TValue">The target type of the JSON value.</typeparam>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<typeparamref name="TValue" /> is not compatible with the JSON.

-or-

A value could not be read from the reader.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> uses unsupported options.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Deserialize``1(System.Text.Json.Utf8JsonReader@,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Reads one JSON value (including objects or arrays) from the provided reader into a <typeparamref name="TValue" />.</summary>
      <param name="reader">The reader to read.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid, <typeparamref name="TValue" /> is not compatible with the JSON, or a value could not be read from the reader.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> is using unsupported options.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.DeserializeAsync(System.IO.Stream,System.Type,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Asynchronously reads the UTF-8 encoded text representing a single JSON value into an instance of a specified type. The stream will be read to completion.</summary>
      <param name="utf8Json">The JSON data to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="options">Options to control the behavior during reading.</param>
      <param name="cancellationToken">A cancellation token that may be used to cancel the read operation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> or <paramref name="returnType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<typeparamref name="TValue" /> is not compatible with the JSON.

-or-

There is remaining data in the stream.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.DeserializeAsync(System.IO.Stream,System.Type,System.Text.Json.Serialization.JsonSerializerContext,System.Threading.CancellationToken)">
      <summary>Reads the UTF-8 encoded text representing a single JSON value into a <paramref name="returnType" />.
            The Stream will be read to completion.</summary>
      <param name="utf8Json">JSON data to parse.</param>
      <param name="returnType">The type of the object to convert to and return.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that can be used to cancel the read operation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" />, <paramref name="returnType" />, or <paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid, the <paramref name="returnType" /> is not compatible with the JSON, or there is remaining data in the Stream.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="returnType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method on the provided <paramref name="context" /> did not return a compatible <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> for <paramref name="returnType" />.</exception>
      <returns>A <paramref name="returnType" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.DeserializeAsync``1(System.IO.Stream,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Asynchronously reads the UTF-8 encoded text representing a single JSON value into an instance of a type specified by a generic type parameter. The stream will be read to completion.</summary>
      <param name="utf8Json">The JSON data to parse.</param>
      <param name="options">Options to control the behavior during reading.</param>
      <param name="cancellationToken">A token that may be used to cancel the read operation.</param>
      <typeparam name="TValue">The target type of the JSON value.</typeparam>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid.

-or-

<typeparamref name="TValue" /> is not compatible with the JSON.

-or-

There is remaining data in the stream.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" />is <see langword="null" />.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.DeserializeAsync``1(System.IO.Stream,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Reads the UTF-8 encoded text representing a single JSON value into a <typeparamref name="TValue" />.
            The Stream will be read to completion.</summary>
      <param name="utf8Json">JSON data to parse.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> which may be used to cancel the read operation.</param>
      <typeparam name="TValue">The type to deserialize the JSON value into.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> or <paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">The JSON is invalid, <typeparamref name="TValue" /> is not compatible with the JSON, or there is remaining data in the Stream.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <typeparamref name="TValue" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.DeserializeAsyncEnumerable``1(System.IO.Stream,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Wraps the UTF-8 encoded text into an <see cref="T:System.Collections.Generic.IAsyncEnumerable`1" /> that can be used to deserialize root-level JSON arrays in a streaming manner.</summary>
      <param name="utf8Json">JSON data to parse.</param>
      <param name="options">Options to control the behavior during reading.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> which may be used to cancel the read operation.</param>
      <typeparam name="TValue">The element type to deserialize asynchronously.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Collections.Generic.IAsyncEnumerable`1" /> representation of the provided JSON array.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize(System.IO.Stream,System.Object,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the provided value to UTF-8 encoded JSON text and write it to the <see cref="T:System.IO.Stream" />.</summary>
      <param name="utf8Json">The UTF-8 <see cref="T:System.IO.Stream" /> to write to.</param>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> or <paramref name="inputType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" />  or its serializable members.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize(System.IO.Stream,System.Object,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Converts the provided value to UTF-8 encoded JSON text and write it to the <see cref="T:System.IO.Stream" />.</summary>
      <param name="utf8Json">The UTF-8 <see cref="T:System.IO.Stream" /> to write to.</param>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" />, <paramref name="inputType" />, or <paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" />  or its serializable members.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize(System.Object,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the value of a specified type into a JSON string.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" /> or its serializable members.</exception>
      <returns>The JSON string representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize(System.Object,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Converts the provided value into a <see cref="T:System.String" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method of the provided <paramref name="context" /> returns <see langword="null" /> for the type to convert.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputType" /> or <paramref name="context" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.String" /> representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize(System.Text.Json.Utf8JsonWriter,System.Object,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Writes the JSON representation of the specified type to the provided writer.</summary>
      <param name="writer">The JSON writer to write to.</param>
      <param name="value">The value to convert and write.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control serialization behavior.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" /></exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> or <paramref name="inputType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" /> or its serializable members.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize(System.Text.Json.Utf8JsonWriter,System.Object,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Writes one JSON value (including objects or arrays) to the provided writer.</summary>
      <param name="writer">A JSON writer to write to.</param>
      <param name="value">The value to convert and write.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> or <paramref name="inputType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method of the provided <paramref name="context" /> returns <see langword="null" /> for the type to convert.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize``1(``0,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the value of a type specified by a generic type parameter into a JSON string.</summary>
      <param name="value">The value to convert.</param>
      <param name="options">Options to control serialization behavior.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A JSON string representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize``1(``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Converts the provided value into a <see cref="T:System.String" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.String" /> representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize``1(System.IO.Stream,``0,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the provided value to UTF-8 encoded JSON text and write it to the <see cref="T:System.IO.Stream" />.</summary>
      <param name="utf8Json">The UTF-8 <see cref="T:System.IO.Stream" /> to write to.</param>
      <param name="value">The value to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize``1(System.IO.Stream,``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Converts the provided value to UTF-8 encoded JSON text and write it to the <see cref="T:System.IO.Stream" />.</summary>
      <param name="utf8Json">The UTF-8 <see cref="T:System.IO.Stream" /> to write to.</param>
      <param name="value">The value to convert.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize``1(System.Text.Json.Utf8JsonWriter,``0,System.Text.Json.JsonSerializerOptions)">
      <summary>Writes the JSON representation of a type specified by a generic type parameter to the provided writer.</summary>
      <param name="writer">A JSON writer to write to.</param>
      <param name="value">The value to convert and write.</param>
      <param name="options">Options to control serialization behavior.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.Serialize``1(System.Text.Json.Utf8JsonWriter,``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Writes one JSON value (including objects or arrays) to the provided writer.</summary>
      <param name="writer">The writer to write.</param>
      <param name="value">The value to convert and write.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> or <paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeAsync(System.IO.Stream,System.Object,System.Type,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Asynchronously converts the value of a specified type to UTF-8 encoded JSON text and writes it to the specified stream.</summary>
      <param name="utf8Json">The UTF-8 stream to write to.</param>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control serialization behavior.</param>
      <param name="cancellationToken">A token that may be used to cancel the write operation.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> or <paramref name="inputType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" /> or its serializable members.</exception>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeAsync(System.IO.Stream,System.Object,System.Type,System.Text.Json.Serialization.JsonSerializerContext,System.Threading.CancellationToken)">
      <summary>Converts the provided value to UTF-8 encoded JSON text and write it to the <see cref="T:System.IO.Stream" />.</summary>
      <param name="utf8Json">The UTF-8 <see cref="T:System.IO.Stream" /> to write to.</param>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that can be used to cancel the write operation.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" />, <paramref name="inputType" />, or <paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" />  or its serializable members.</exception>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeAsync``1(System.IO.Stream,``0,System.Text.Json.JsonSerializerOptions,System.Threading.CancellationToken)">
      <summary>Asynchronously converts a value of a type specified by a generic type parameter to UTF-8 encoded JSON text and writes it to a stream.</summary>
      <param name="utf8Json">The UTF-8 stream to write to.</param>
      <param name="value">The value to convert.</param>
      <param name="options">Options to control serialization behavior.</param>
      <param name="cancellationToken">A token that may be used to cancel the write operation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeAsync``1(System.IO.Stream,``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Threading.CancellationToken)">
      <summary>Converts the provided value to UTF-8 encoded JSON text and write it to the <see cref="T:System.IO.Stream" />.</summary>
      <param name="utf8Json">The UTF-8 <see cref="T:System.IO.Stream" /> to write to.</param>
      <param name="value">The value to convert.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> that can be used to cancel the write operation.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToDocument(System.Object,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" />  or its serializable members.</exception>
      <returns>A <see cref="T:System.Text.Json.JsonDocument" /> representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToDocument(System.Object,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method of the provided <paramref name="context" /> returns <see langword="null" /> for the type to convert.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputType" /> or <paramref name="context" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Text.Json.JsonDocument" /> representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToDocument``1(``0,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <see cref="T:System.Text.Json.JsonDocument" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToDocument``1(``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Text.Json.JsonDocument" /> representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToElement(System.Object,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" />  or its serializable members.</exception>
      <returns>A <see cref="T:System.Text.Json.JsonDocument" /> representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToElement(System.Object,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method of the provided <paramref name="context" /> returns <see langword="null" /> for the type to convert.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputType" /> or <paramref name="context" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Text.Json.JsonDocument" /> representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToElement``1(``0,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <see cref="T:System.Text.Json.JsonDocument" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToElement``1(``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.JsonDocument" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Text.Json.JsonDocument" /> representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToNode(System.Object,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" />  or its serializable members.</exception>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToNode(System.Object,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" /> or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method of the provided <paramref name="context" /> returns <see langword="null" /> for the type to convert.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputType" /> or <paramref name="context" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToNode``1(``0,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToNode``1(``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Converts the provided value into a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">The value to convert.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(System.Object,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts a value of the specified type into a JSON string, encoded as UTF-8 bytes.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" /> or its serializable members.</exception>
      <returns>A JSON string representation of the value, encoded as UTF-8 bytes.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(System.Object,System.Type,System.Text.Json.Serialization.JsonSerializerContext)">
      <summary>Converts the provided value into a <see cref="T:System.Byte" /> array.</summary>
      <param name="value">The value to convert.</param>
      <param name="inputType">The type of the <paramref name="value" /> to convert.</param>
      <param name="context">A metadata provider for serializable types.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="inputType" /> is not compatible with <paramref name="value" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <paramref name="inputType" />  or its serializable members.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)" /> method of the provided <paramref name="context" /> returns <see langword="null" /> for the type to convert.</exception>
      <returns>A UTF-8 representation of the value.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToUtf8Bytes``1(``0,System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the value of a type specified by a generic type parameter into a JSON string, encoded as UTF-8 bytes.</summary>
      <param name="value">The value to convert.</param>
      <param name="options">Options to control the conversion behavior.</param>
      <typeparam name="TValue">The type of the value.</typeparam>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <returns>A JSON string representation of the value, encoded as UTF-8 bytes.</returns>
    </member>
    <member name="M:System.Text.Json.JsonSerializer.SerializeToUtf8Bytes``1(``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Converts the provided value into a <see cref="T:System.Byte" /> array.</summary>
      <param name="value">The value to convert.</param>
      <param name="jsonTypeInfo">Metadata about the type to convert.</param>
      <typeparam name="TValue">The type of the value to serialize.</typeparam>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for <typeparamref name="TValue" /> or its serializable members.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="jsonTypeInfo" /> is <see langword="null" />.</exception>
      <returns>A UTF-8 representation of the value.</returns>
    </member>
    <member name="T:System.Text.Json.JsonSerializerDefaults">
      <summary>Specifies scenario-based default serialization options that can be used to construct a <see cref="T:System.Text.Json.JsonSerializerOptions" /> instance.</summary>
    </member>
    <member name="F:System.Text.Json.JsonSerializerDefaults.General">
      <summary>
        <para>General-purpose option values. These are the same settings that are applied if a <see cref="T:System.Text.Json.JsonSerializerDefaults" /> member isn't specified.</para>
        <para>For information about the default property values that are applied, see JsonSerializerOptions properties.</para>
      </summary>
    </member>
    <member name="F:System.Text.Json.JsonSerializerDefaults.Web">
      <summary>
        <para>Option values appropriate to Web-based scenarios.</para>
        <para>This member implies that:</para>
        <para>- Property names are treated as case-insensitive.</para>
        <para>- "camelCase" name formatting should be employed.</para>
        <para>- Quoted numbers (JSON strings for number properties) are allowed.</para>
      </summary>
    </member>
    <member name="T:System.Text.Json.JsonSerializerOptions">
      <summary>Provides options to be used with <see cref="T:System.Text.Json.JsonSerializer" />.</summary>
    </member>
    <member name="M:System.Text.Json.JsonSerializerOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.JsonSerializerOptions" /> class.</summary>
    </member>
    <member name="M:System.Text.Json.JsonSerializerOptions.#ctor(System.Text.Json.JsonSerializerDefaults)">
      <summary>Constructs a new <see cref="T:System.Text.Json.JsonSerializerOptions" /> instance with a predefined set of options determined by the specified <see cref="T:System.Text.Json.JsonSerializerDefaults" />.</summary>
      <param name="defaults">The <see cref="T:System.Text.Json.JsonSerializerDefaults" /> to reason about.</param>
    </member>
    <member name="M:System.Text.Json.JsonSerializerOptions.#ctor(System.Text.Json.JsonSerializerOptions)">
      <summary>Copies the options from a <see cref="T:System.Text.Json.JsonSerializerOptions" /> instance to a new instance.</summary>
      <param name="options">The options instance to copy options from.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.JsonSerializerOptions.AddContext``1">
      <summary>Binds current <see cref="T:System.Text.Json.JsonSerializerOptions" /> instance with a new instance of the specified <see cref="T:System.Text.Json.Serialization.JsonSerializerContext" /> type.</summary>
      <typeparam name="TContext">The generic definition of the specified context type.</typeparam>
    </member>
    <member name="M:System.Text.Json.JsonSerializerOptions.GetConverter(System.Type)">
      <summary>Returns the converter for the specified type.</summary>
      <param name="typeToConvert">The type to return a converter for.</param>
      <exception cref="T:System.InvalidOperationException">The configured <see cref="System.Text.Json.Serialization.JsonConverter" /> for <paramref name="typeToConvert" /> returned an invalid converter.</exception>
      <exception cref="T:System.NotSupportedException">There is no compatible <see cref="System.Text.Json.Serialization.JsonConverter" /> for <paramref name="typeToConvert" /> or its serializable members.</exception>
      <returns>The first converter that supports the given type, or <see langword="null" /> if there is no converter.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.AllowTrailingCommas">
      <summary>Get or sets a value that indicates whether an extra comma at the end of a list of JSON values in an object or array is allowed (and ignored) within the JSON payload being deserialized.</summary>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
      <returns>
        <see langword="true" /> if an extra comma at the end of a list of JSON values in an object or array is allowed (and ignored); <see langword="false" /> otherwise.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.Converters">
      <summary>Gets the list of user-defined converters that were registered.</summary>
      <returns>The list of custom converters.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.DefaultBufferSize">
      <summary>Gets or sets the default buffer size, in bytes, to use when creating temporary buffers.</summary>
      <exception cref="T:System.ArgumentException">The buffer size is less than 1.</exception>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
      <returns>The default buffer size in bytes.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.DefaultIgnoreCondition">
      <summary>Specifies a condition to determine when properties with default values are ignored during serialization or deserialization.
 The default value is <see cref="F:System.Text.Json.Serialization.JsonIgnoreCondition.Never" />.</summary>
      <exception cref="T:System.ArgumentException">This property is set to <see cref="F:System.Text.Json.Serialization.JsonIgnoreCondition.Always" />.</exception>
      <exception cref="T:System.InvalidOperationException">This property is set after serialization or deserialization has occurred.

-or-

<see cref="P:System.Text.Json.JsonSerializerOptions.IgnoreNullValues" /> has been set to <see langword="true" />. These properties cannot be used together.</exception>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.DictionaryKeyPolicy">
      <summary>Gets or sets the policy used to convert a <see cref="T:System.Collections.IDictionary" /> key's name to another format, such as camel-casing.</summary>
      <returns>The policy used to convert a <see cref="T:System.Collections.IDictionary" /> key's name to another format.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.Encoder">
      <summary>Gets or sets the encoder to use when escaping strings, or <see langword="null" /> to use the default encoder.</summary>
      <returns>The JavaScript character encoding.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.IgnoreNullValues">
      <summary>Gets or sets a value that determines whether <see langword="null" /> values are ignored during serialization and deserialization. The default value is <see langword="false" />.</summary>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.

-or-

<see cref="P:System.Text.Json.JsonSerializerOptions.DefaultIgnoreCondition" /> has been set to a non-default value. These properties cannot be used together.</exception>
      <returns>
        <see langword="true" /> to ignore null values during serialization and deserialization; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.IgnoreReadOnlyFields">
      <summary>Determines whether read-only fields are ignored during serialization. A field is read-only if it is marked with the <see langword="readonly" /> keyword. The default value is <see langword="false" />.</summary>
      <exception cref="T:System.InvalidOperationException">This property is set after serialization or deserialization has occurred.</exception>
      <returns>
        <see langword="true" /> if read-only fields should be ignored during serialization; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.IgnoreReadOnlyProperties">
      <summary>Gets a value that determines whether read-only properties are ignored during serialization. The default value is <see langword="false" />.</summary>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
      <returns>
        <see langword="true" /> to ignore read-only properties during serialization; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.IncludeFields">
      <summary>Determines whether fields are handled during serialization and deserialization.
            The default value is <see langword="false" />.</summary>
      <exception cref="T:System.InvalidOperationException">This property is set after serialization or deserialization has occurred.</exception>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.MaxDepth">
      <summary>Gets or sets the maximum depth allowed when serializing or deserializing JSON, with the default value of 0 indicating a maximum depth of 64.</summary>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The max depth is set to a negative value.</exception>
      <returns>The maximum depth allowed when serializing or deserializing JSON.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.NumberHandling">
      <summary>Specifies how number types should be handled when serializing or deserializing.</summary>
      <exception cref="T:System.InvalidOperationException">This property is set after serialization or deserialization has occurred.</exception>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.PropertyNameCaseInsensitive">
      <summary>Gets or sets a value that determines whether a property's name uses a case-insensitive comparison during deserialization. The default value is <see langword="false" />.</summary>
      <returns>
        <see langword="true" /> to compare property names using case-insensitive comparison; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.PropertyNamingPolicy">
      <summary>Gets or sets a value that specifies the policy used to convert a property's name on an object to another format, such as camel-casing, or <see langword="null" /> to leave property names unchanged.</summary>
      <returns>A property naming policy, or <see langword="null" /> to leave property names unchanged.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.ReadCommentHandling">
      <summary>Gets or sets a value that defines how comments are handled during deserialization.</summary>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The comment handling enum is set to a value that is not supported (or not within the <see cref="T:System.Text.Json.JsonCommentHandling" /> enum range).</exception>
      <returns>A value that indicates whether comments are allowed, disallowed, or skipped.</returns>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.ReferenceHandler">
      <summary>Configures how object references are handled when reading and writing JSON.</summary>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.UnknownTypeHandling">
      <summary>Defines how deserializing a type declared as an <see cref="T:System.Object" /> is handled during deserialization.</summary>
    </member>
    <member name="P:System.Text.Json.JsonSerializerOptions.WriteIndented">
      <summary>Gets or sets a value that defines whether JSON should use pretty printing. By default, JSON is serialized without any extra white space.</summary>
      <exception cref="T:System.InvalidOperationException">This property was set after serialization or deserialization has occurred.</exception>
      <returns>
        <see langword="true" /> if JSON should pretty print on serialization; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="T:System.Text.Json.JsonTokenType">
      <summary>Defines the various JSON tokens that make up a JSON text.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.Comment">
      <summary>The token type is a comment string.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.EndArray">
      <summary>The token type is the end of a JSON array.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.EndObject">
      <summary>The token type is the end of a JSON object.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.False">
      <summary>The token type is the JSON literal false.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.None">
      <summary>There is no value (as distinct from <see cref="F:System.Text.Json.JsonTokenType.Null" />). This is the default token type if no data has been read by the <see cref="T:System.Text.Json.Utf8JsonReader" />.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.Null">
      <summary>The token type is the JSON literal null.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.Number">
      <summary>The token type is a JSON number.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.PropertyName">
      <summary>The token type is a JSON property name.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.StartArray">
      <summary>The token type is the start of a JSON array.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.StartObject">
      <summary>The token type is the start of a JSON object.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.String">
      <summary>The token type is a JSON string.</summary>
    </member>
    <member name="F:System.Text.Json.JsonTokenType.True">
      <summary>The token type is the JSON literal true.</summary>
    </member>
    <member name="T:System.Text.Json.JsonValueKind">
      <summary>Specifies the data type of a JSON value.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.Array">
      <summary>A JSON array.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.False">
      <summary>The JSON value false.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.Null">
      <summary>The JSON value null.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.Number">
      <summary>A JSON number.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.Object">
      <summary>A JSON object.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.String">
      <summary>A JSON string.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.True">
      <summary>The JSON value true.</summary>
    </member>
    <member name="F:System.Text.Json.JsonValueKind.Undefined">
      <summary>There is no value (as distinct from <see cref="F:System.Text.Json.JsonValueKind.Null" />).</summary>
    </member>
    <member name="T:System.Text.Json.JsonWriterOptions">
      <summary>Allows the user to define custom behavior when writing JSON using the <see cref="T:System.Text.Json.Utf8JsonWriter" />.</summary>
    </member>
    <member name="P:System.Text.Json.JsonWriterOptions.Encoder">
      <summary>Gets or sets the encoder to use when escaping strings, or <see langword="null" /> to use the default encoder.</summary>
      <returns>The JavaScript character encoder used to override the escaping behavior.</returns>
    </member>
    <member name="P:System.Text.Json.JsonWriterOptions.Indented">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Text.Json.Utf8JsonWriter" /> should format the JSON output, which includes indenting nested JSON tokens, adding new lines, and adding white space between property names and values.</summary>
      <returns>
        <see langword="true" /> to format the JSON output; <see langword="false" /> to write without any extra white space. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Text.Json.JsonWriterOptions.SkipValidation">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Text.Json.Utf8JsonWriter" /> should skip structural validation and allow the user to write invalid JSON.</summary>
      <returns>
        <see langword="true" /> to skip structural validation and allow invalid JSON; <see langword="false" /> to throw an <see cref="T:System.InvalidOperationException" /> on any attempt to write invalid JSON.</returns>
    </member>
    <member name="T:System.Text.Json.Nodes.JsonArray">
      <summary>Represents a mutable JSON array.</summary>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.#ctor(System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonArray" /> class that is empty.</summary>
      <param name="options">Options to control the behavior.</param>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.#ctor(System.Text.Json.Nodes.JsonNode[])">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonArray" /> class that contains items from the specified array.</summary>
      <param name="items">The items to add to the new <see cref="T:System.Text.Json.Nodes.JsonArray" />.</param>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.#ctor(System.Text.Json.Nodes.JsonNodeOptions,System.Text.Json.Nodes.JsonNode[])">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonArray" /> class that contains items from the specified params array.</summary>
      <param name="options">Options to control the behavior.</param>
      <param name="items">The items to add to the new <see cref="T:System.Text.Json.Nodes.JsonArray" />.</param>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.Add(System.Text.Json.Nodes.JsonNode)">
      <summary>Adds a <see cref="T:System.Text.Json.Nodes.JsonNode" /> to the end of the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</summary>
      <param name="item">The <see cref="T:System.Text.Json.Nodes.JsonNode" /> to be added to the end of the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</param>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.Add``1(``0)">
      <summary>Adds an object to the end of the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</summary>
      <param name="value">The object to be added to the end of the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</param>
      <typeparam name="T">The type of object to be added.</typeparam>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.Clear">
      <summary>Removes all elements from the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</summary>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.Contains(System.Text.Json.Nodes.JsonNode)">
      <summary>Determines whether an element is in the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</summary>
      <param name="item">The object to locate in the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> is found in the <see cref="T:System.Text.Json.Nodes.JsonArray" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.Create(System.Text.Json.JsonElement,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonArray" /> class that contains items from the specified <see cref="T:System.Text.Json.JsonElement" />.</summary>
      <param name="element">The <see cref="T:System.Text.Json.JsonElement" />.</param>
      <param name="options">Options to control the behavior.</param>
      <exception cref="T:System.InvalidOperationException">The <paramref name="element" /> is not a <see cref="F:System.Text.Json.JsonValueKind.Array" />.</exception>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonArray" /> class that contains items from the specified <see cref="T:System.Text.Json.JsonElement" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</summary>
      <returns>A <see cref="T:System.Collections.Generic.IEnumerator`1" /> for the <see cref="T:System.Text.Json.Nodes.JsonNode" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.IndexOf(System.Text.Json.Nodes.JsonNode)">
      <summary>The object to locate in the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</summary>
      <param name="item">The <see cref="T:System.Text.Json.Nodes.JsonNode" /> to locate in the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</param>
      <returns>The index of item if found in the list; otherwise, -1.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.Insert(System.Int32,System.Text.Json.Nodes.JsonNode)">
      <summary>Inserts an element into the <see cref="T:System.Text.Json.Nodes.JsonArray" /> at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
      <param name="item">The <see cref="T:System.Text.Json.Nodes.JsonNode" /> to insert.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0 or <paramref name="index" /> is greater than <see cref="P:System.Text.Json.Nodes.JsonArray.Count" />.</exception>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.Remove(System.Text.Json.Nodes.JsonNode)">
      <summary>Removes the first occurrence of a specific <see cref="T:System.Text.Json.Nodes.JsonNode" /> from the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</summary>
      <param name="item">The <see cref="T:System.Text.Json.Nodes.JsonNode" /> to remove from the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="item" /> is successfully removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.RemoveAt(System.Int32)">
      <summary>Removes the element at the specified index of the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</summary>
      <param name="index">The zero-based index of the element to remove.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0 or <paramref name="index" /> is greater than <see cref="P:System.Text.Json.Nodes.JsonArray.Count" />.</exception>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.System#Collections#Generic#ICollection{System#Text#Json#Nodes#JsonNode}#CopyTo(System.Text.Json.Nodes.JsonNode[],System.Int32)">
      <summary>Copies the entire <see cref="T:System.Array" /> to a compatible one-dimensional array, starting at the specified index of the target array.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Text.Json.Nodes.JsonArray" />. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source ICollection is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</summary>
      <returns>A <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonArray.WriteTo(System.Text.Json.Utf8JsonWriter,System.Text.Json.JsonSerializerOptions)">
      <summary>Writes the <see cref="T:System.Text.Json.Nodes.JsonNode" /> into the provided <see cref="T:System.Text.Json.Utf8JsonWriter" /> as JSON.</summary>
      <param name="writer">The <see cref="T:System.Text.Json.Utf8JsonWriter" />.</param>
      <param name="options">Options to control the serialization behavior.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonArray.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Text.Json.Nodes.JsonArray" />.</summary>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonArray.System#Collections#Generic#ICollection{System#Text#Json#Nodes#JsonNode}#IsReadOnly">
      <summary>Returns <see langword="false" />.</summary>
    </member>
    <member name="T:System.Text.Json.Nodes.JsonNode">
      <summary>The base class that represents a single node within a mutable JSON document.</summary>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.AsArray">
      <summary>Casts to the derived <see cref="T:System.Text.Json.Nodes.JsonArray" /> type.</summary>
      <exception cref="T:System.InvalidOperationException">The node is not a <see cref="T:System.Text.Json.Nodes.JsonArray" />.</exception>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonArray" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.AsObject">
      <summary>Casts to the derived <see cref="T:System.Text.Json.Nodes.JsonObject" /> type.</summary>
      <exception cref="T:System.InvalidOperationException">The node is not a <see cref="T:System.Text.Json.Nodes.JsonObject" />.</exception>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonObject" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.AsValue">
      <summary>Casts to the derived <see cref="T:System.Text.Json.Nodes.JsonValue" /> type.</summary>
      <exception cref="T:System.InvalidOperationException">The node is not a <see cref="T:System.Text.Json.Nodes.JsonValue" />.</exception>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonValue" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.GetPath">
      <summary>Gets the JSON path.</summary>
      <returns>The JSON Path value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.GetValue``1">
      <summary>Gets the value for the current <see cref="T:System.Text.Json.Nodes.JsonValue" />.</summary>
      <typeparam name="T">The type of the value to obtain from the <see cref="T:System.Text.Json.Nodes.JsonValue" />.</typeparam>
      <exception cref="T:System.FormatException">The current <see cref="T:System.Text.Json.Nodes.JsonNode" /> cannot be represented as a {TValue}.</exception>
      <exception cref="T:System.InvalidOperationException">The current <see cref="T:System.Text.Json.Nodes.JsonNode" /> is not a <see cref="T:System.Text.Json.Nodes.JsonValue" /> or is not compatible with {TValue}.</exception>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Boolean">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Boolean" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Boolean" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Byte">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Byte" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Byte" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Char">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Char" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Char" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.DateTime">
      <summary>Defines an explicit conversion of a given <see cref="T:System.DateTime" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.DateTime" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.DateTimeOffset">
      <summary>Defines an explicit conversion of a given <see cref="T:System.DateTimeOffset" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.DateTimeOffset" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Decimal">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Decimal" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Decimal" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Double">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Double" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Double" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Guid">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Guid" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Guid" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Int16">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Int16" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int16" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Int32">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Int32" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int32" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Int64">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Int64" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int64" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.Boolean}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Boolean" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Boolean" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.Byte}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Byte" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Byte" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.Char}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Char" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Char" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.DateTime}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.DateTime" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.DateTime" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.DateTimeOffset}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.DateTimeOffset" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.DateTimeOffset" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.Decimal}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Decimal" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Decimal" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.Double}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Double" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Double" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.Guid}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Guid" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Guid" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.Int16}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Int16" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int16" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.Int32}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Int32" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int32" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.Int64}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Int64" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int64" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.SByte}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.SByte" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.SByte" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.Single}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Single" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Single" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.UInt16}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.UInt16" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt16" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.UInt32}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.UInt32" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt32" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Nullable{System.UInt64}">
      <summary>Defines an explicit conversion of a given <see cref="T:System.UInt64" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt64" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.SByte">
      <summary>Defines an explicit conversion of a given <see cref="T:System.SByte" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.SByte" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.Single">
      <summary>Defines an explicit conversion of a given <see cref="T:System.Single" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Single" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.String">
      <summary>Defines an explicit conversion of a given <see cref="T:System.String" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.String" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.UInt16">
      <summary>Defines an explicit conversion of a given <see cref="T:System.UInt16" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt16" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.UInt32">
      <summary>Defines an explicit conversion of a given <see cref="T:System.UInt32" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt32" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Explicit(System.Text.Json.Nodes.JsonNode)~System.UInt64">
      <summary>Defines an explicit conversion of a given <see cref="T:System.UInt64" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt64" /> to implicitly convert.</param>
      <returns>A value converted from the <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Boolean)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Boolean" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Boolean" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Byte)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Byte" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Byte" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Char)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Char" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Char" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.DateTime)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.DateTime" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.DateTime" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.DateTimeOffset)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.DateTimeOffset" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.DateTimeOffset" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Decimal)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Decimal" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Decimal" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Double)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Double" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Double" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Guid)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Guid" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Guid" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Int16)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Int16" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int16" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Int32)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Int32" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int32" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Int64)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Int64" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int64" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.Boolean})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Boolean" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Boolean" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.Byte})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Byte" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Byte" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.Char})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Char" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Char" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.DateTime})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.DateTime" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.DateTime" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.DateTimeOffset})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.DateTimeOffset" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.DateTimeOffset" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.Decimal})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Decimal" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Decimal" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.Double})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Double" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Double" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.Guid})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Guid" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Guid" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.Int16})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Int16" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int16" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.Int32})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Int32" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int32" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.Int64})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Int64" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Int64" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.SByte})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.SByte" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.SByte" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.Single})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Single" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Single" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.UInt16})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.UInt16" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt16" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.UInt32})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.UInt32" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt32" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Nullable{System.UInt64})~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.UInt64" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt64" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.SByte)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.SByte" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.SByte" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.Single)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.Single" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.Single" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.String)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.String" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.String" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.UInt16)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.UInt16" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt16" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.UInt32)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.UInt32" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt32" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.op_Implicit(System.UInt64)~System.Text.Json.Nodes.JsonNode">
      <summary>Defines an implicit conversion of a given <see cref="T:System.UInt64" /> to a <see cref="T:System.Text.Json.Nodes.JsonNode" />.</summary>
      <param name="value">A <see cref="T:System.UInt64" /> to implicitly convert.</param>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> instance converted from the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.Parse(System.IO.Stream,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions},System.Text.Json.JsonDocumentOptions)">
      <summary>Parse a <see cref="T:System.IO.Stream" /> as UTF-8-encoded data representing a single JSON value into a <see cref="T:System.Text.Json.Nodes.JsonNode" />.  The Stream will be read to completion.</summary>
      <param name="utf8Json">JSON text to parse.</param>
      <param name="nodeOptions">Options to control the node behavior after parsing.</param>
      <param name="documentOptions">Options to control the document behavior during parsing.</param>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.Parse(System.ReadOnlySpan{System.Byte},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions},System.Text.Json.JsonDocumentOptions)">
      <summary>Parses text representing a single JSON value.</summary>
      <param name="utf8Json">JSON text to parse.</param>
      <param name="nodeOptions">Options to control the node behavior after parsing.</param>
      <param name="documentOptions">Options to control the document behavior during parsing.</param>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="utf8Json" /> does not represent a valid single JSON value.</exception>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.Parse(System.String,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions},System.Text.Json.JsonDocumentOptions)">
      <summary>Parses text representing a single JSON value.</summary>
      <param name="json">JSON text to parse.</param>
      <param name="nodeOptions">Options to control the node behavior after parsing.</param>
      <param name="documentOptions">Options to control the document behavior during parsing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="json" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="json" /> does not represent a valid single JSON value.</exception>
      <returns>A <see cref="T:System.Text.Json.Nodes.JsonNode" /> representation of the JSON value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.Parse(System.Text.Json.Utf8JsonReader@,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Parses one JSON value (including objects or arrays) from the provided reader.</summary>
      <param name="reader">The reader to read.</param>
      <param name="nodeOptions">Options to control the behavior.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> is using unsupported options.</exception>
      <exception cref="T:System.ArgumentException">The current <paramref name="reader" /> token does not start or represent a value.</exception>
      <exception cref="T:System.Text.Json.JsonException">A value could not be read from the reader.</exception>
      <returns>The <see cref="T:System.Text.Json.Nodes.JsonNode" /> from the reader.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.ToJsonString(System.Text.Json.JsonSerializerOptions)">
      <summary>Converts the current instance to string in JSON format.</summary>
      <param name="options">Options to control the serialization behavior.</param>
      <returns>JSON representation of current instance.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.ToString">
      <summary>Gets a string representation for the current value appropriate to the node type.</summary>
      <returns>A string representation for the current value appropriate to the node type.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonNode.WriteTo(System.Text.Json.Utf8JsonWriter,System.Text.Json.JsonSerializerOptions)">
      <summary>Write the <see cref="T:System.Text.Json.Nodes.JsonNode" /> into the provided <see cref="T:System.Text.Json.Utf8JsonWriter" /> as JSON.</summary>
      <param name="writer">The <see cref="T:System.Text.Json.Utf8JsonWriter" />.</param>
      <param name="options">Options to control the serialization behavior.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonNode.Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <param name="index">The zero-based index of the element to get or set.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0 or <paramref name="index" /> is greater than the number of properties.</exception>
      <exception cref="T:System.InvalidOperationException">The current <see cref="T:System.Text.Json.Nodes.JsonNode" /> is not a <see cref="T:System.Text.Json.Nodes.JsonArray" />.</exception>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonNode.Item(System.String)">
      <summary>Gets or sets the element with the specified property name.
              If the property is not found, <see langword="null" /> is returned.</summary>
      <param name="propertyName">The name of the property to return.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The current <see cref="T:System.Text.Json.Nodes.JsonNode" /> is not a <see cref="T:System.Text.Json.Nodes.JsonObject" />.</exception>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonNode.Options">
      <summary>Options to control the behavior.</summary>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonNode.Parent">
      <summary>Gets the parent <see cref="T:System.Text.Json.Nodes.JsonNode" />.
              If there is no parent, <see langword="null" /> is returned.
              A parent can either be a <see cref="T:System.Text.Json.Nodes.JsonObject" /> or a <see cref="T:System.Text.Json.Nodes.JsonArray" />.</summary>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonNode.Root">
      <summary>Gets the root <see cref="T:System.Text.Json.Nodes.JsonNode" />.
              If the current <see cref="T:System.Text.Json.Nodes.JsonNode" /> is a root, <see langword="null" /> is returned.</summary>
    </member>
    <member name="T:System.Text.Json.Nodes.JsonNodeOptions">
      <summary>Options to control <see cref="T:System.Text.Json.Nodes.JsonNode" /> behavior.</summary>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonNodeOptions.PropertyNameCaseInsensitive">
      <summary>Specifies whether property names on <see cref="T:System.Text.Json.Nodes.JsonObject" /> are case insensitive.</summary>
    </member>
    <member name="T:System.Text.Json.Nodes.JsonObject">
      <summary>Represents a mutable JSON object.</summary>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Text.Json.Nodes.JsonNode}},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonObject" /> class that contains the specified <paramref name="properties" />.</summary>
      <param name="properties">The properties to be added.</param>
      <param name="options">Options to control the behavior.</param>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.#ctor(System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonObject" /> class that is empty.</summary>
      <param name="options">Options to control the behavior.</param>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.Add(System.Collections.Generic.KeyValuePair{System.String,System.Text.Json.Nodes.JsonNode})">
      <summary>Adds the specified property to the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</summary>
      <param name="property">The KeyValuePair structure representing the property name and value to add to the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</param>
      <exception cref="T:System.ArgumentException">An element with the same property name already exists in the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</exception>
      <exception cref="T:System.ArgumentNullException">The property name of <paramref name="property" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.Add(System.String,System.Text.Json.Nodes.JsonNode)">
      <summary>Adds an element with the provided property name and value to the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</summary>
      <param name="propertyName">The property name of the element to add.</param>
      <param name="value">The value of the element to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyName" />is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">An element with the same property name already exists in the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</exception>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.Clear">
      <summary>Removes all elements from the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</summary>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.ContainsKey(System.String)">
      <summary>Determines whether the <see cref="T:System.Text.Json.Nodes.JsonObject" /> contains an element with the specified property name.</summary>
      <param name="propertyName">The property name to locate in the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyName" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Text.Json.Nodes.JsonObject" /> contains an element with the specified property name; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.Create(System.Text.Json.JsonElement,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonObject" /> class that contains properties from the specified <see cref="T:System.Text.Json.JsonElement" />.</summary>
      <param name="element">The <see cref="T:System.Text.Json.JsonElement" />.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonObject" /> class that contains properties from the specified <see cref="T:System.Text.Json.JsonElement" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</summary>
      <returns>An enumerator that iterates through the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.Remove(System.String)">
      <summary>Removes the element with the specified property name from the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</summary>
      <param name="propertyName">The property name of the element to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyName" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the element is successfully removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{System#String@System#Text#Json#Nodes#JsonNode}}#Contains(System.Collections.Generic.KeyValuePair{System.String,System.Text.Json.Nodes.JsonNode})">
      <summary>Determines whether the <see cref="T:System.Text.Json.Nodes.JsonObject" /> contains a specific property name and <see cref="T:System.Text.Json.Nodes.JsonNode" /> reference.</summary>
      <param name="item">The element to locate in the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Text.Json.Nodes.JsonObject" /> contains an element with the property name; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{System#String@System#Text#Json#Nodes#JsonNode}}#CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Text.Json.Nodes.JsonNode}[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Text.Json.Nodes.JsonObject" /> to an array of type KeyValuePair starting at the specified array index.</summary>
      <param name="array">The one-dimensional Array that is the destination of the elements copied from <see cref="T:System.Text.Json.Nodes.JsonObject" />.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source ICollection is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{System#String@System#Text#Json#Nodes#JsonNode}}#Remove(System.Collections.Generic.KeyValuePair{System.String,System.Text.Json.Nodes.JsonNode})">
      <summary>Removes a key and value from the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</summary>
      <param name="item">The KeyValuePair structure representing the property name and value to remove from the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</param>
      <returns>
        <see langword="true" /> if the element is successfully removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.System#Collections#Generic#IDictionary{System#String@System#Text#Json#Nodes#JsonNode}#TryGetValue(System.String,System.Text.Json.Nodes.JsonNode@)">
      <summary>Gets the value associated with the specified property name.</summary>
      <param name="propertyName">The property name of the value to get.</param>
      <param name="jsonNode">When this method returns, contains the value associated with the specified property name, if the property name is found; otherwise, <see langword="null" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyName" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Text.Json.Nodes.JsonObject" /> contains an element with the specified property name; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</summary>
      <returns>An enumerator that iterates through the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.TryGetPropertyValue(System.String,System.Text.Json.Nodes.JsonNode@)">
      <summary>Returns the value of a property with the specified name.</summary>
      <param name="propertyName">The name of the property to return.</param>
      <param name="jsonNode">The JSON value of the property with the specified name.</param>
      <returns>
        <see langword="true" /> if a property with the specified name was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonObject.WriteTo(System.Text.Json.Utf8JsonWriter,System.Text.Json.JsonSerializerOptions)">
      <summary>Write the <see cref="T:System.Text.Json.Nodes.JsonNode" /> into the provided <see cref="T:System.Text.Json.Utf8JsonWriter" /> as JSON.</summary>
      <param name="writer">The <see cref="T:System.Text.Json.Utf8JsonWriter" />.</param>
      <param name="options">Options to control the serialization behavior.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonObject.Count">
      <summary>Gets the number of elements contained in <see cref="T:System.Text.Json.Nodes.JsonObject" />.</summary>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonObject.System#Collections#Generic#ICollection{System#Collections#Generic#KeyValuePair{System#String@System#Text#Json#Nodes#JsonNode}}#IsReadOnly">
      <summary>Returns <see langword="false" />.</summary>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonObject.System#Collections#Generic#IDictionary{System#String@System#Text#Json#Nodes#JsonNode}#Keys">
      <summary>Gets a collection containing the property names in the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</summary>
    </member>
    <member name="P:System.Text.Json.Nodes.JsonObject.System#Collections#Generic#IDictionary{System#String@System#Text#Json#Nodes#JsonNode}#Values">
      <summary>Gets a collection containing the property values in the <see cref="T:System.Text.Json.Nodes.JsonObject" />.</summary>
    </member>
    <member name="T:System.Text.Json.Nodes.JsonValue">
      <summary>Represents a mutable JSON value.</summary>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Boolean,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Byte,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Char,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.DateTime,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.DateTimeOffset,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Decimal,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Double,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Guid,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Int16,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Int32,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Int64,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.Boolean},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.Byte},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.Char},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.DateTime},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.DateTimeOffset},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.Decimal},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.Double},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.Guid},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.Int16},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.Int32},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.Int64},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.SByte},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.Single},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.Text.Json.JsonElement},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.UInt16},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.UInt32},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Nullable{System.UInt64},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.SByte,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Single,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.String,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.Text.Json.JsonElement,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.UInt16,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.UInt32,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create(System.UInt64,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The underlying value of the new <see cref="T:System.Text.Json.Nodes.JsonValue" /> instance.</param>
      <param name="options">Options to control the behavior.</param>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create``1(``0,System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The value to create.</param>
      <param name="options">Options to control the behavior.</param>
      <typeparam name="T">The type of value to create.</typeparam>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.Create``1(``0,System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0},System.Nullable{System.Text.Json.Nodes.JsonNodeOptions})">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</summary>
      <param name="value">The value to create.</param>
      <param name="jsonTypeInfo">The <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> that will be used to serialize the value.</param>
      <param name="options">Options to control the behavior.</param>
      <typeparam name="T">The type of value to create.</typeparam>
      <returns>The new instance of the <see cref="T:System.Text.Json.Nodes.JsonValue" /> class that contains the specified value.</returns>
    </member>
    <member name="M:System.Text.Json.Nodes.JsonValue.TryGetValue``1(``0@)">
      <summary>Tries to obtain the current JSON value and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <typeparam name="T">The type of value to obtain.</typeparam>
      <returns>
        <see langword="true" /> if the value can be successfully obtained; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.IJsonOnDeserialized">
      <summary>Specifies that the JSON type should have its <see cref="M:System.Text.Json.Serialization.IJsonOnDeserialized.OnDeserialized" /> method called after deserialization occurs.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.IJsonOnDeserialized.OnDeserialized">
      <summary>The method that is called after deserialization.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.IJsonOnDeserializing">
      <summary>Specifies that the type should have its <see cref="M:System.Text.Json.Serialization.IJsonOnDeserializing.OnDeserializing" /> method called before deserialization occurs.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.IJsonOnDeserializing.OnDeserializing">
      <summary>The method that is called before deserialization.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.IJsonOnSerialized">
      <summary>Specifies that the type should have its <see cref="M:System.Text.Json.Serialization.IJsonOnSerialized.OnSerialized" /> method called after serialization occurs.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.IJsonOnSerialized.OnSerialized">
      <summary>The method that is called after serialization.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.IJsonOnSerializing">
      <summary>Specifies that the type should have its <see cref="M:System.Text.Json.Serialization.IJsonOnSerializing.OnSerializing" /> method called before serialization occurs.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.IJsonOnSerializing.OnSerializing">
      <summary>The method that is called before serialization.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonAttribute">
      <summary>Provides the base class for serialization attributes.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonAttribute.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Text.Json.Serialization.JsonAttribute" />.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonConstructorAttribute">
      <summary>When placed on a constructor, indicates that the constructor should be used to create instances of the type on deserialization.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConstructorAttribute.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonConstructorAttribute" />.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonConverter">
      <summary>Converts an object or value to or from JSON.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter.CanConvert(System.Type)">
      <summary>When overridden in a derived class, determines whether the converter instance can convert the specified object type.</summary>
      <param name="typeToConvert">The type of the object to check whether it can be converted by this converter instance.</param>
      <returns>
        <see langword="true" /> if the instance can convert the specified object type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonConverter`1">
      <summary>Converts an object or value to or from JSON.</summary>
      <typeparam name="T">The type of object or value handled by the converter.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter`1.#ctor">
      <summary>Initializes a new <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter`1.CanConvert(System.Type)">
      <summary>Determines whether the specified type can be converted.</summary>
      <param name="typeToConvert">The type to compare against.</param>
      <returns>
        <see langword="true" /> if the type can be converted; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter`1.Read(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Reads and converts the JSON to type <typeparamref name="T" />.</summary>
      <param name="reader">The reader.</param>
      <param name="typeToConvert">The type to convert.</param>
      <param name="options">An object that specifies serialization options to use.</param>
      <returns>The converted value.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter`1.ReadAsPropertyName(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Reads a dictionary key from a JSON property name.</summary>
      <param name="reader">The <see cref="T:System.Text.Json.Utf8JsonReader" /> to read from.</param>
      <param name="typeToConvert">The type to convert.</param>
      <param name="options">The options to use when reading the value.</param>
      <returns>The value that was converted.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter`1.Write(System.Text.Json.Utf8JsonWriter,`0,System.Text.Json.JsonSerializerOptions)">
      <summary>Writes a specified value as JSON.</summary>
      <param name="writer">The writer to write to.</param>
      <param name="value">The value to convert to JSON.</param>
      <param name="options">An object that specifies serialization options to use.</param>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverter`1.WriteAsPropertyName(System.Text.Json.Utf8JsonWriter,`0,System.Text.Json.JsonSerializerOptions)">
      <summary>Writes a dictionary key as a JSON property name.</summary>
      <param name="writer">The <see cref="T:System.Text.Json.Utf8JsonWriter" /> to write to.</param>
      <param name="value">The value to convert. The value of <see cref="P:System.Text.Json.Serialization.JsonConverter`1.HandleNull" /> determines if the converter handles <see langword="null" /> values.</param>
      <param name="options">The options to use when writing the value.</param>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonConverter`1.HandleNull">
      <summary>Indicates whether <see langword="null" /> should be passed to the converter on serialization, and whether <see cref="F:System.Text.Json.JsonTokenType.Null" /> should be passed on deserialization.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonConverterAttribute">
      <summary>When placed on a property or type, specifies the converter type to use.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverterAttribute.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonConverterAttribute" />.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverterAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonConverterAttribute" /> with the specified converter type.</summary>
      <param name="converterType">The type of the converter.</param>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverterAttribute.CreateConverter(System.Type)">
      <summary>When overridden in a derived class and <see cref="P:System.Text.Json.Serialization.JsonConverterAttribute.ConverterType" /> is <see langword="null" />, allows the derived class to create a <see cref="T:System.Text.Json.Serialization.JsonConverter" /> in order to pass additional state.</summary>
      <param name="typeToConvert">The type of the converter.</param>
      <returns>The custom converter.</returns>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonConverterAttribute.ConverterType">
      <summary>Gets the type of the <see cref="T:System.Text.Json.Serialization.JsonConverterAttribute" />, or <see langword="null" /> if it was created without a type.</summary>
      <returns>The type of the <see cref="T:System.Text.Json.Serialization.JsonConverterAttribute" />, or <see langword="null" /> if it was created without a type.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonConverterFactory">
      <summary>Supports converting several types by using a factory pattern.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverterFactory.#ctor">
      <summary>When overidden in a derived class, initializes a new instance of the <see cref="T:System.Text.Json.Serialization.JsonConverterFactory" /> class.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonConverterFactory.CreateConverter(System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Creates a converter for a specified type.</summary>
      <param name="typeToConvert">The type handled by the converter.</param>
      <param name="options">The serialization options to use.</param>
      <returns>A converter for which <typeparamref name="T" /> is compatible with <paramref name="typeToConvert" />.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonExtensionDataAttribute">
      <summary>When placed on a property of type <see cref="T:System.Collections.Generic.IDictionary`2" />, any properties that do not have a matching member are added to that dictionary during deserialization and written during serialization.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonExtensionDataAttribute.#ctor">
      <summary>Instantiates a new instance of the <see cref="T:System.Text.Json.Serialization.JsonExtensionDataAttribute" /> class.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonIgnoreAttribute">
      <summary>Prevents a property from being serialized or deserialized.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonIgnoreAttribute.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonIgnoreAttribute" />.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonIgnoreAttribute.Condition">
      <summary>Specifies the condition that must be met before a property will be ignored.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonIgnoreCondition">
      <summary>Controls how the <see cref="T:System.Text.Json.Serialization.JsonIgnoreAttribute" /> ignores properties on serialization and deserialization.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonIgnoreCondition.Always">
      <summary>Property will always be ignored.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonIgnoreCondition.Never">
      <summary>Property will always be serialized and deserialized, regardless of <see cref="P:System.Text.Json.JsonSerializerOptions.IgnoreNullValues" /> configuration.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingDefault">
      <summary>Property will only be ignored if it is <see langword="null" />.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull">
      <summary>If the value is <see langword="null" />, the property is ignored during serialization. This is applied only to reference-type properties and fields.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonIncludeAttribute">
      <summary>Indicates that the member should be included for serialization and deserialization.</summary>
      <exception cref="T:System.InvalidOperationException">The attribute is applied to a non-public property.</exception>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonIncludeAttribute.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonIncludeAttribute" />.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonKnownNamingPolicy">
      <summary>The <see cref="T:System.Text.Json.JsonNamingPolicy" /> to be used at run time.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonKnownNamingPolicy.CamelCase">
      <summary>Specifies that the built-in <see cref="P:System.Text.Json.JsonNamingPolicy.CamelCase" /> be used to convert JSON property names.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonKnownNamingPolicy.Unspecified">
      <summary>Specifies that JSON property names should not be converted.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonNumberHandling">
      <summary>Determines how <see cref="T:System.Text.Json.JsonSerializer" /> handles numbers when serializing and deserializing.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonNumberHandling.AllowNamedFloatingPointLiterals">
      <summary>The "NaN", "Infinity", and "-Infinity" <see cref="F:System.Text.Json.JsonTokenType.String" /> tokens can be read as floating-point constants, and the <see cref="T:System.Single" /> and <see cref="T:System.Double" /> values for these constants will be written as their corresponding JSON string representations.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString">
      <summary>Numbers can be read from <see cref="F:System.Text.Json.JsonTokenType.String" /> tokens. Does not prevent numbers from being read from <see cref="F:System.Text.Json.JsonTokenType.Number" /> token.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonNumberHandling.Strict">
      <summary>Numbers will only be read from <see cref="F:System.Text.Json.JsonTokenType.Number" /> tokens and will only be written as JSON numbers (without quotes).</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonNumberHandling.WriteAsString">
      <summary>Numbers will be written as JSON strings (with quotes), not as JSON numbers.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonNumberHandlingAttribute">
      <summary>When placed on a type, property, or field, indicates what <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> settings should be used when serializing or deserializing numbers.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonNumberHandlingAttribute.#ctor(System.Text.Json.Serialization.JsonNumberHandling)">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonNumberHandlingAttribute" />.</summary>
      <param name="handling">A bitwise combination of the enumeration values that specify how number types should be handled when serializing or deserializing.</param>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonNumberHandlingAttribute.Handling">
      <summary>Indicates what settings should be used when serializing or deserializing numbers.</summary>
      <returns>An object that determines the number serialization and deserialization settings.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonPropertyNameAttribute">
      <summary>Specifies the property name that is present in the JSON when serializing and deserializing. This overrides any naming policy specified by <see cref="T:System.Text.Json.JsonNamingPolicy" />.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonPropertyNameAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonPropertyNameAttribute" /> with the specified property name.</summary>
      <param name="name">The name of the property.</param>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonPropertyNameAttribute.Name">
      <summary>Gets the name of the property.</summary>
      <returns>The name of the property.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonPropertyOrderAttribute">
      <summary>Specifies the property order that is present in the JSON when serializing. Lower values are serialized first.
            If the attribute is not specified, the default value is 0.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonPropertyOrderAttribute.#ctor(System.Int32)">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonPropertyNameAttribute" /> with the specified order.</summary>
      <param name="order">The order of the property.</param>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonPropertyOrderAttribute.Order">
      <summary>Gets the serialization order of the property.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonSerializableAttribute">
      <summary>Instructs the System.Text.Json source generator to generate source code to help optimize performance when serializing and deserializing instances of the specified type and types in its object graph.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonSerializableAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of <see cref="T:System.Text.Json.Serialization.JsonSerializableAttribute" /> with the specified type.</summary>
      <param name="type">The type to generate source code for.</param>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonSerializableAttribute.GenerationMode">
      <summary>Determines what the source generator should generate for the type. If the value is <see cref="F:System.Text.Json.Serialization.JsonSourceGenerationMode.Default" />, then the setting specified on <see cref="P:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.GenerationMode" /> will be used.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonSerializableAttribute.TypeInfoPropertyName">
      <summary>The name of the property for the generated <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo`1" /> for the type on the generated, derived <see cref="T:System.Text.Json.Serialization.JsonSerializerContext" /> type.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonSerializerContext">
      <summary>Provides metadata about a set of types that is relevant to JSON serialization.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonSerializerContext.#ctor(System.Text.Json.JsonSerializerOptions,System.Text.Json.JsonSerializerOptions)">
      <summary>Creates an instance of <see cref="T:System.Text.Json.Serialization.JsonSerializerContext" /> and binds it with the indicated <see cref="T:System.Text.Json.JsonSerializerOptions" />.</summary>
      <param name="instanceOptions">The run-time provided options for the context instance.</param>
      <param name="defaultOptions">The default run-time options for the context. Its values are defined at design time via <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" />.</param>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonSerializerContext.GetTypeInfo(System.Type)">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the given type.</summary>
      <param name="type">The type to fetch metadata about.</param>
      <returns>The metadata for the specified type, or <see langword="null" /> if the context has no metadata for the type.</returns>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonSerializerContext.Options">
      <summary>Gets the run-time specified options of the context. If no options were passed when instanciating the context, then a new instance is bound and returned.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonSourceGenerationMode">
      <summary>The generation mode for the System.Text.Json source generator.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonSourceGenerationMode.Default">
      <summary>When specified on <see cref="P:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.GenerationMode" />, indicates that both type-metadata initialization logic and optimized serialization logic should be generated for all types. When specified on <see cref="P:System.Text.Json.Serialization.JsonSerializableAttribute.GenerationMode" />, indicates that the setting on <see cref="P:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.GenerationMode" /> should be used.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonSourceGenerationMode.Metadata">
      <summary>Instructs the JSON source generator to generate type-metadata initialization logic.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonSourceGenerationMode.Serialization">
      <summary>Instructs the JSON source generator to generate optimized serialization logic.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute">
      <summary>Instructs the System.Text.Json source generator to assume the specified options will be used at run time via <see cref="T:System.Text.Json.JsonSerializerOptions" />.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.#ctor">
      <summary>Instructs the System.Text.Json source generator to assume the specified options will be used at run time via <see cref="T:System.Text.Json.JsonSerializerOptions" />.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.DefaultIgnoreCondition">
      <summary>Gets or sets the default ignore condition.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.GenerationMode">
      <summary>Gets or sets the source generation mode for types that don't explicitly set the mode with <see cref="P:System.Text.Json.Serialization.JsonSerializableAttribute.GenerationMode" />.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.IgnoreReadOnlyFields">
      <summary>Gets or sets a value that indicates whether to ignore read-only fields.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.IgnoreReadOnlyProperties">
      <summary>Gets or sets a value that indicates whether to ignore read-only properties.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.IgnoreRuntimeCustomConverters">
      <summary>Gets or sets a value that indicates whether to ignore custom converters provided at run time.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.IncludeFields">
      <summary>Gets or sets a value that indicates whether to include fields for serialization and deserialization.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.PropertyNamingPolicy">
      <summary>Gets or sets a built-in naming policy to convert JSON property names with.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute.WriteIndented">
      <summary>Gets or sets a value that indicates whether JSON output should be pretty-printed.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonStringEnumConverter">
      <summary>Converts enumeration values to and from strings.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonStringEnumConverter.#ctor">
      <summary>Initializes an instance of the <see cref="T:System.Text.Json.Serialization.JsonStringEnumConverter" /> class with the default naming policy that allows integer values.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonStringEnumConverter.#ctor(System.Text.Json.JsonNamingPolicy,System.Boolean)">
      <summary>Initializes an instance of the <see cref="T:System.Text.Json.Serialization.JsonStringEnumConverter" /> class with a specified naming policy and a value that indicates whether undefined enumeration values are allowed.</summary>
      <param name="namingPolicy">The optional naming policy for writing enum values.</param>
      <param name="allowIntegerValues">
        <see langword="true" /> to allow undefined enum values; otherwise, <see langword="false" />. When <see langword="true" />, if an enum value isn't defined, it will output as a number rather than a string.</param>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonStringEnumConverter.CanConvert(System.Type)">
      <summary>Determines whether the specified type can be converted to an enum.</summary>
      <param name="typeToConvert">The type to be checked.</param>
      <returns>
        <see langword="true" /> if the type can be converted; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.JsonStringEnumConverter.CreateConverter(System.Type,System.Text.Json.JsonSerializerOptions)">
      <summary>Creates a converter for the specified type.</summary>
      <param name="typeToConvert">The type handled by the converter.</param>
      <param name="options">The serialization options to use.</param>
      <returns>A converter for which <typeparamref name="T" /> is compatible with <paramref name="typeToConvert" />.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.JsonUnknownTypeHandling">
      <summary>Defines how deserializing a type declared as an <see cref="T:System.Object" /> is handled during deserialization.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonUnknownTypeHandling.JsonElement">
      <summary>A type declared as <see cref="T:System.Object" /> is deserialized as a <see cref="F:System.Text.Json.Serialization.JsonUnknownTypeHandling.JsonElement" />.</summary>
    </member>
    <member name="F:System.Text.Json.Serialization.JsonUnknownTypeHandling.JsonNode">
      <summary>A type declared as <see cref="T:System.Object" /> is deserialized as a <see cref="F:System.Text.Json.Serialization.JsonUnknownTypeHandling.JsonNode" />.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.Metadata.JsonMetadataServices">
      <summary>Provides helpers to create and initialize metadata for JSON-serializable types.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateArrayInfo``1(System.Text.Json.JsonSerializerOptions,System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0[]})">
      <summary>Creates metadata for an array.</summary>
      <param name="options">The <see cref="T:System.Text.Json.JsonSerializerOptions" /> to use.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TElement">The generic definition of the element type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateConcurrentQueueInfo``2(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The type of the collection.</typeparam>
      <typeparam name="TElement">The generic definition of the element type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateConcurrentStackInfo``2(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Concurrent.ConcurrentStack`1" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TElement">The generic definition of the element type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateDictionaryInfo``3(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="keyInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the key type.</param>
      <param name="valueInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the value type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TKey">The generic definition of the key type.</typeparam>
      <typeparam name="TValue">The generic definition of the value type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateICollectionInfo``2(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TElement">The generic definition of the element type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateIDictionaryInfo``1(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="stringInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing <see cref="T:System.String" /> instances.</param>
      <param name="objectInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing <see cref="T:System.Object" /> instances.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateIDictionaryInfo``3(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="keyInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the key type.</param>
      <param name="valueInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the value type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TKey">The generic definition of the key type.</typeparam>
      <typeparam name="TValue">The generic definition of the value type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateIEnumerableInfo``1(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.IList" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateIEnumerableInfo``2(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Generic.IEnumerable`1" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TElement">The generic definition of the element type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateIListInfo``1(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.IList" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="objectInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateIListInfo``2(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Generic.IList`1" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TElement">The generic definition of the element type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateImmutableDictionaryInfo``3(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0},System.Func{System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{``1,``2}},``0})">
      <summary>Creates metadata for <see cref="T:System.Collections.Immutable.ImmutableDictionary`2" /> and types assignable to <see cref="T:System.Collections.Immutable.ImmutableDictionary`2" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="keyInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the key type.</param>
      <param name="valueInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the value type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <param name="createRangeFunc">A method to create an immutable dictionary instance.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TKey">The generic definition of the key type.</typeparam>
      <typeparam name="TValue">The generic definition of the value type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateImmutableEnumerableInfo``2(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0},System.Func{System.Collections.Generic.IEnumerable{``1},``0})">
      <summary>Creates metadata for non-dictionary immutable collection types.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <param name="createRangeFunc">A method to create an immutable dictionary instance.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TElement">The generic definition of the element type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateIReadOnlyDictionaryInfo``3(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="keyInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the key type.</param>
      <param name="valueInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the value type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TKey">The generic definition of the key type.</typeparam>
      <typeparam name="TValue">The generic definition of the value type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateISetInfo``2(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Generic.ISet`1" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TElement">The generic definition of the element type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateListInfo``2(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TElement">The generic definition of the element type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateObjectInfo``1(System.Text.Json.JsonSerializerOptions,System.Text.Json.Serialization.Metadata.JsonObjectInfoValues{``0})">
      <summary>Creates metadata for a complex class or struct.</summary>
      <param name="options">The <see cref="T:System.Text.Json.JsonSerializerOptions" /> to initialize the metadata with.</param>
      <param name="objectInfo">Provides serialization metadata about an object type with constructors, properties, and fields.</param>
      <typeparam name="T">The type of the class or struct.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> or <paramref name="objectInfo" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo`1" /> instance representing the class or struct.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreatePropertyInfo``1(System.Text.Json.JsonSerializerOptions,System.Boolean,System.Boolean,System.Boolean,System.Type,System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonConverter{``0},System.Func{System.Object,``0},System.Action{System.Object,``0},System.Nullable{System.Text.Json.Serialization.JsonIgnoreCondition},System.Boolean,System.Nullable{System.Text.Json.Serialization.JsonNumberHandling},System.String,System.String)">
      <summary>Creates metadata for a property or field.</summary>
      <param name="options">The <see cref="T:System.Text.Json.JsonSerializerOptions" /> to initialize the metadata with.</param>
      <param name="isProperty">Whether the CLR member is a property or field.</param>
      <param name="isPublic">Whether the CLR member is public.</param>
      <param name="isVirtual">Whether the CLR member is a virtual property.</param>
      <param name="declaringType">The declaring type of the property or field.</param>
      <param name="propertyTypeInfo">The <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> info for the property or field's type.</param>
      <param name="converter">A <see cref="T:System.Text.Json.Serialization.JsonConverter" /> for the property or field, specified by <see cref="T:System.Text.Json.Serialization.JsonConverterAttribute" />.</param>
      <param name="getter">Provides a mechanism to get the property or field's value.</param>
      <param name="setter">Provides a mechanism to set the property or field's value.</param>
      <param name="ignoreCondition">Specifies a condition for the property to be ignored.</param>
      <param name="hasJsonInclude">Whether the property was annotated with <see cref="T:System.Text.Json.Serialization.JsonIncludeAttribute" />.</param>
      <param name="numberHandling">If the property or field is a number, specifies how it should processed when serializing and deserializing.</param>
      <param name="propertyName">The CLR name of the property or field.</param>
      <param name="jsonPropertyName">The name to be used when processing the property or field, specified by <see cref="T:System.Text.Json.Serialization.JsonPropertyNameAttribute" />.</param>
      <typeparam name="T">The type that the converter for the property returns or accepts when converting JSON data.</typeparam>
      <returns>A <see cref="T:System.Text.Json.Serialization.Metadata.JsonPropertyInfo" /> instance intialized with the provided metadata.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateQueueInfo``2(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TElement">The generic definition of the element type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateStackInfo``2(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
      <typeparam name="TElement">The generic definition of the element type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateStackOrQueueInfo``1(System.Text.Json.JsonSerializerOptions,System.Func{``0},System.Text.Json.Serialization.Metadata.JsonTypeInfo,System.Text.Json.Serialization.JsonNumberHandling,System.Action{System.Text.Json.Utf8JsonWriter,``0},System.Action{``0,System.Object})">
      <summary>Creates metadata for types assignable to <see cref="T:System.Collections.IList" />.</summary>
      <param name="options" />
      <param name="createObjectFunc">A <see cref="T:System.Func`1" /> to create an instance of the list when deserializing.</param>
      <param name="elementInfo">A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo" /> instance representing the element type.</param>
      <param name="numberHandling">The <see cref="T:System.Text.Json.Serialization.JsonNumberHandling" /> option to apply to number collection elements.</param>
      <param name="serializeFunc">An optimized serialization implementation assuming pre-determined <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> defaults.</param>
      <param name="addFunc">A method for adding elements to the collection when using the serializer's code-paths.</param>
      <typeparam name="TCollection">The generic definition of the type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateValueInfo``1(System.Text.Json.JsonSerializerOptions,System.Text.Json.Serialization.JsonConverter)">
      <summary>Creates metadata for a primitive or a type with a custom converter.</summary>
      <param name="options" />
      <param name="converter" />
      <typeparam name="T">The generic type definition.</typeparam>
      <returns>A <see cref="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo`1" /> instance representing the type.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.GetEnumConverter``1(System.Text.Json.JsonSerializerOptions)">
      <summary>Creates a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <typeparamref name="T" /> values.</summary>
      <param name="options">The <see cref="T:System.Text.Json.JsonSerializerOptions" /> to use for serialization and deserialization.</param>
      <typeparam name="T">The generic definition for the enum type.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonMetadataServices.GetNullableConverter``1(System.Text.Json.Serialization.Metadata.JsonTypeInfo{``0})">
      <summary>Creates a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <typeparamref name="T??" /> values.</summary>
      <param name="underlyingTypeInfo">Serialization metadata for the underlying nullable type.</param>
      <typeparam name="T">The generic definition for the underlying nullable type.</typeparam>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.BooleanConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Boolean" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.ByteArrayConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts byte array values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.ByteConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Byte" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.CharConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Char" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.DateTimeConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.DateTime" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.DateTimeOffsetConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.DateTimeOffset" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.DecimalConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Decimal" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.DoubleConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Double" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.GuidConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Guid" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.Int16Converter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Int16" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.Int32Converter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Int32" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.Int64Converter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Int64" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.JsonElementConverter">
      <summary>Gets a JSON converter that converts <see cref="T:System.Text.Json.JsonElement" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.ObjectConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Object" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.SByteConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.SByte" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.SingleConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Single" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.StringConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.String" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.TimeSpanConverter">
      <summary>Gets a JSON converter that converts <see cref="T:System.TimeSpan" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.UInt16Converter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.UInt16" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.UInt32Converter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.UInt32" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.UInt64Converter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.UInt64" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.UriConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Uri" /> values.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonMetadataServices.VersionConverter">
      <summary>Returns a <see cref="T:System.Text.Json.Serialization.JsonConverter`1" /> instance that converts <see cref="T:System.Version" /> values.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.Metadata.JsonObjectInfoValues`1">
      <summary>Provides serialization metadata about an object type with constructors, properties, and fields.</summary>
      <typeparam name="T">The object type to serialize or deserialize.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonObjectInfoValues`1.#ctor" />
    <member name="P:System.Text.Json.Serialization.Metadata.JsonObjectInfoValues`1.ConstructorParameterMetadataInitializer">
      <summary>Provides a mechanism to initialize metadata for a parameterized constructor of the class or struct to be used when deserializing.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonObjectInfoValues`1.NumberHandling">
      <summary>Gets or sets an object that specifies how number properties and fields should be processed when serializing and deserializing.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonObjectInfoValues`1.ObjectCreator">
      <summary>Gets or sets a mechanism to create an instance of the class or struct using a parameterless constructor during deserialization.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonObjectInfoValues`1.ObjectWithParameterizedConstructorCreator">
      <summary>Gets or sets a mechanism to create an instance of the class or struct using a parameterized constructor during deserialization.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonObjectInfoValues`1.PropertyMetadataInitializer">
      <summary>Gets or sets a mechanism to initialize metadata for properties and fields of the class or struct.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonObjectInfoValues`1.SerializeHandler">
      <summary>Gets or sets a serialization implementation for instances of the class or struct that assumes options specified by <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" />.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.Metadata.JsonParameterInfoValues">
      <summary>Provides information about a constructor parameter required for JSON deserialization.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.Metadata.JsonParameterInfoValues.#ctor" />
    <member name="P:System.Text.Json.Serialization.Metadata.JsonParameterInfoValues.DefaultValue">
      <summary>Gets or sets the default value of the parameter.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonParameterInfoValues.HasDefaultValue">
      <summary>Gets or sets a value that specifies whether a default value was specified for the parameter.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonParameterInfoValues.Name">
      <summary>Gets or sets the name of the parameter.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonParameterInfoValues.ParameterType">
      <summary>Gets or sets the type of the parameter.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonParameterInfoValues.Position">
      <summary>Gets or sets the zero-based position of the parameter in the formal parameter list.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.Metadata.JsonPropertyInfo">
      <summary>Provides JSON serialization-related metadata about a property or field.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo">
      <summary>Provides JSON serialization-related metadata about a type.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.Metadata.JsonTypeInfo`1">
      <summary>Provides JSON serialization-related metadata about a type.</summary>
      <typeparam name="T">The generic definition of the type.</typeparam>
    </member>
    <member name="P:System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Serialize">
      <summary>A method that serializes an instance of <typeparamref name="T" /> using <see cref="T:System.Text.Json.Serialization.JsonSourceGenerationOptionsAttribute" /> values specified at design time.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.ReferenceHandler">
      <summary>This class defines how the <see cref="T:System.Text.Json.JsonSerializer" /> deals with references on serialization and deserialization.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.ReferenceHandler.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Serialization.ReferenceHandler" /> class.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.ReferenceHandler.CreateResolver">
      <summary>Returns the <see cref="T:System.Text.Json.Serialization.ReferenceResolver" /> used for each serialization call.</summary>
      <returns>The resolver to use for serialization and deserialization.</returns>
    </member>
    <member name="P:System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles">
      <summary>Ignores an object when a reference cycle is detected during serialization.</summary>
    </member>
    <member name="P:System.Text.Json.Serialization.ReferenceHandler.Preserve">
      <summary>Metadata properties will be honored when deserializing JSON objects and arrays into reference types and written when serializing reference types. This is necessary to create round-trippable JSON from objects that contain cycles or duplicate references.</summary>
    </member>
    <member name="T:System.Text.Json.Serialization.ReferenceHandler`1">
      <summary>This class defines how the <see cref="T:System.Text.Json.JsonSerializer" /> deals with references on serialization and deserialization.</summary>
      <typeparam name="T">The type of the <see cref="T:System.Text.Json.Serialization.ReferenceResolver" /> to create on each serialization or deserialization call.</typeparam>
    </member>
    <member name="M:System.Text.Json.Serialization.ReferenceHandler`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Serialization.ReferenceHandler`1" /> generic class that can create a <see cref="T:System.Text.Json.Serialization.ReferenceResolver" /> instance of the specified type.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.ReferenceHandler`1.CreateResolver">
      <summary>Creates a new <see cref="T:System.Text.Json.Serialization.ReferenceResolver" /> of type <typeparamref name="T" /> used for each serialization call.</summary>
      <returns>The new resolver to use for serialization and deserialization.</returns>
    </member>
    <member name="T:System.Text.Json.Serialization.ReferenceResolver">
      <summary>This class defines how the <see cref="T:System.Text.Json.JsonSerializer" /> deals with references on serialization and deserialization.
 Defines the core behavior of preserving references on serialization and deserialization.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.ReferenceResolver.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Serialization.ReferenceResolver" /> class.</summary>
    </member>
    <member name="M:System.Text.Json.Serialization.ReferenceResolver.AddReference(System.String,System.Object)">
      <summary>Adds an entry to the bag of references using the specified id and value.
 This method gets called when an $id metadata property from a JSON object is read.</summary>
      <param name="referenceId">The identifier of the JSON object or array.</param>
      <param name="value">The value of the CLR reference type object that results from parsing the JSON object.</param>
    </member>
    <member name="M:System.Text.Json.Serialization.ReferenceResolver.GetReference(System.Object,System.Boolean@)">
      <summary>Gets the reference identifier of the specified value if exists; otherwise a new id is assigned.
 This method gets called before a CLR object is written so we can decide whether to write $id and enumerate the rest of its properties or $ref and step into the next object.</summary>
      <param name="value">The value of the CLR reference type object to get an id for.</param>
      <param name="alreadyExists">When this method returns, <see langword="true" /> if a reference to value already exists; otherwise, <see langword="false" />.</param>
      <returns>The reference id for the specified object.</returns>
    </member>
    <member name="M:System.Text.Json.Serialization.ReferenceResolver.ResolveReference(System.String)">
      <summary>Returns the CLR reference type object related to the specified reference id.
 This method gets called when $ref metadata property is read.</summary>
      <param name="referenceId">The reference id related to the returned object.</param>
      <returns>The reference type object related to the specified reference id.</returns>
    </member>
    <member name="T:System.Text.Json.Utf8JsonReader">
      <summary>Provides a high-performance API for forward-only, read-only access to UTF-8 encoded JSON text.</summary>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.#ctor(System.Buffers.ReadOnlySequence{System.Byte},System.Boolean,System.Text.Json.JsonReaderState)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonReader" /> structure that processes a read-only sequence of UTF-8 encoded text and indicates whether the input contains all the text to process.</summary>
      <param name="jsonData">The UTF-8 encoded JSON text to process.</param>
      <param name="isFinalBlock">
        <see langword="true" /> to indicate that the input sequence contains the entire data to process; <see langword="false" /> to indicate that the input span contains partial data with more data to follow.</param>
      <param name="state">An object that contains the reader state. If this is the first call to the constructor, pass the default state; otherwise, pass the value of the <see cref="P:System.Text.Json.Utf8JsonReader.CurrentState" /> property from the previous instance of the <see cref="T:System.Text.Json.Utf8JsonReader" />.</param>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.#ctor(System.Buffers.ReadOnlySequence{System.Byte},System.Text.Json.JsonReaderOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonReader" /> structure that processes a read-only sequence of UTF-8 encoded text using the specified options.</summary>
      <param name="jsonData">The UTF-8 encoded JSON text to process.</param>
      <param name="options">Defines customized behavior of the <see cref="T:System.Text.Json.Utf8JsonReader" /> that differs from the JSON RFC (for example how to handle comments or maximum depth allowed when reading). By default, the <see cref="T:System.Text.Json.Utf8JsonReader" /> follows the JSON RFC strictly; comments within the JSON are invalid, and the maximum depth is 64.</param>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.#ctor(System.ReadOnlySpan{System.Byte},System.Boolean,System.Text.Json.JsonReaderState)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonReader" /> structure that processes a read-only span of UTF-8 encoded text and indicates whether the input contains all the text to process.</summary>
      <param name="jsonData">The UTF-8 encoded JSON text to process.</param>
      <param name="isFinalBlock">
        <see langword="true" /> to indicate that the input sequence contains the entire data to process; <see langword="false" /> to indicate that the input span contains partial data with more data to follow.</param>
      <param name="state">An object that contains the reader state. If this is the first call to the constructor, pass the default state; otherwise, pass the value of the <see cref="P:System.Text.Json.Utf8JsonReader.CurrentState" /> property from the previous instance of the <see cref="T:System.Text.Json.Utf8JsonReader" />.</param>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.#ctor(System.ReadOnlySpan{System.Byte},System.Text.Json.JsonReaderOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonReader" /> structure that processes a read-only span of UTF-8 encoded text using the specified options.</summary>
      <param name="jsonData">The UTF-8 encoded JSON text to process.</param>
      <param name="options">Defines customized behavior of the <see cref="T:System.Text.Json.Utf8JsonReader" /> that differs from the JSON RFC (for example how to handle comments or maximum depth allowed when reading). By default, the <see cref="T:System.Text.Json.Utf8JsonReader" /> follows the JSON RFC strictly; comments within the JSON are invalid, and the maximum depth is 64.</param>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetBoolean">
      <summary>Reads the next JSON token value from the source as a <see cref="T:System.Boolean" />.</summary>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a boolean value (that is, <see cref="F:System.Text.Json.JsonTokenType.True" /> or <see cref="F:System.Text.Json.JsonTokenType.False" />).</exception>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Text.Json.Utf8JsonReader.TokenType" /> is <see cref="F:System.Text.Json.JsonTokenType.True" />; <see langword="false" /> if the <see cref="P:System.Text.Json.Utf8JsonReader.TokenType" /> is <see cref="F:System.Text.Json.JsonTokenType.False" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetByte">
      <summary>Parses the current JSON token value from the source as a <see cref="T:System.Byte" />.</summary>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The numeric format of the JSON token value is incorrect (for example, it contains a fractional value or is written in scientific notation).
          
-or-

The JSON token value represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />.</exception>
      <returns>The value of the UTF-8 encoded token.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetBytesFromBase64">
      <summary>Parses the current JSON token value from the source and decodes the Base64 encoded JSON string as a byte array.</summary>
      <exception cref="T:System.InvalidOperationException">The type of the JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <exception cref="T:System.FormatException">The value is not encoded as Base64 text, so it can't be decoded to bytes.

-or-

The value contains invalid or more than two padding characters.

-or-

The value is incomplete. That is, the JSON string length is not a multiple of 4.</exception>
      <returns>The byte array that represents the current JSON token value.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetComment">
      <summary>Parses the current JSON token value from the source as a comment, transcoded it as a <see cref="T:System.String" />.</summary>
      <exception cref="T:System.InvalidOperationException">The JSON token is not a comment.</exception>
      <returns>The comment that represents the current JSON token value.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetDateTime">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.DateTime" />.</summary>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value cannot be read as a <see cref="T:System.DateTime" />.
        
-or-
        
The entire UTF-8 encoded token value cannot be parsed to a <see cref="T:System.DateTime" /> value.

-or-

The JSON token value is of an unsupported format.</exception>
      <returns>The date and time value, if the entire UTF-8 encoded token value can be successfully parsed.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetDateTimeOffset">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.DateTimeOffset" />.</summary>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value cannot be read as a <see cref="T:System.DateTimeOffset" />.

-or-
        
The entire UTF-8 encoded token value cannot be parsed to a <see cref="T:System.DateTimeOffset" /> value.

-or-

The JSON token value is of an unsupported format.</exception>
      <returns>The date and time offset, if the entire UTF-8 encoded token value can be successfully parsed.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetDecimal">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.Decimal" />.</summary>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />.</exception>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.Decimal" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetDouble">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.Double" />.</summary>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />.</exception>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.Double" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetGuid">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.Guid" />.</summary>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value is in an unsupported format for a Guid.
        
-or-

The entire UTF-8 encoded token value cannot be parsed to a <see cref="T:System.Guid" /> value.</exception>
      <returns>The GUID value, if the entire UTF-8 encoded token value can be successfully parsed.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetInt16">
      <summary>Parses the current JSON token value from the source as a <see cref="T:System.Int16" />.</summary>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The numeric format of the JSON token value is incorrect (for example, it contains a fractional value or is written in scientific notation).
          
-or-

The JSON token value represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />.</exception>
      <returns>The UTF-8 encoded token value parsed to an <see cref="T:System.Int16" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetInt32">
      <summary>Reads the next JSON token value from the source and parses it to an <see cref="T:System.Int32" />.</summary>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value is of the incorrect numeric format. For example, it contains a decimal or is written in scientific notation.
            
-or-

The JSON token value represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
      <returns>The UTF-8 encoded token value parsed to an <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetInt64">
      <summary>Reads the next JSON token value from the source and parses it to an <see cref="T:System.Int64" />.</summary>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value is of the incorrect numeric format. For example, it contains a decimal or is written in scientific notation.
            
-or-

The JSON token value represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />.</exception>
      <returns>The UTF-8 encoded token value parsed to an <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetSByte">
      <summary>Parses the current JSON token value from the source as an <see cref="T:System.SByte" />.</summary>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The numeric format of the JSON token value is incorrect (for example, it contains a fractional value or is written in scientific notation).
          
-or-

The JSON token value represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />.</exception>
      <returns>The UTF-8 encoded token value parsed to an <see cref="T:System.SByte" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetSingle">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.Single" />.</summary>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />.</exception>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.Single" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetString">
      <summary>Reads the next JSON token value from the source, unescaped, and transcoded as a string.</summary>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a string (that is, not a <see cref="F:System.Text.Json.JsonTokenType.String" />, <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />, or <see cref="F:System.Text.Json.JsonTokenType.Null" />).

-or-

The JSON string contains invalid UTF-8 bytes or invalid UTF-16 surrogates.</exception>
      <returns>The token value parsed to a string, or <see langword="null" /> if <see cref="P:System.Text.Json.Utf8JsonReader.TokenType" /> is <see cref="F:System.Text.Json.JsonTokenType.Null" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetUInt16">
      <summary>Parses the current JSON token value from the source as a <see cref="T:System.UInt16" />.</summary>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The numeric format of the JSON token value is incorrect (for example, it contains a fractional value or is written in scientific notation).
          
-or-

The JSON token value represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />.</exception>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.UInt16" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetUInt32">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.UInt32" />.</summary>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value is of the incorrect numeric format. For example, it contains a decimal or is written in scientific notation.

-or-

The JSON token value represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />.</exception>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.UInt32" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.GetUInt64">
      <summary>Reads the next JSON token value from the source and parses it to a <see cref="T:System.UInt64" />.</summary>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <exception cref="T:System.FormatException">The JSON token value is of the incorrect numeric format. For example, it contains a decimal or is written in scientific notation.
            
-or-

The JSON token value represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />.</exception>
      <returns>The UTF-8 encoded token value parsed to a <see cref="T:System.UInt64" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.Read">
      <summary>Reads the next JSON token from the input source.</summary>
      <exception cref="T:System.Text.Json.JsonException">An invalid JSON token according to the JSON RFC is encountered.
        
-or-

The current depth exceeds the recursive limit set by the maximum depth.</exception>
      <returns>
        <see langword="true" /> if the token was read successfully; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.Skip">
      <summary>Skips the children of the current JSON token.</summary>
      <exception cref="T:System.InvalidOperationException">The reader was given partial data with more data to follow (that is, <see cref="P:System.Text.Json.Utf8JsonReader.IsFinalBlock" /> is <see langword="false" />).</exception>
      <exception cref="T:System.Text.Json.JsonException">An invalid JSON token was encountered while skipping, according to the JSON RFC.

-or-

The current depth exceeds the recursive limit set by the maximum depth.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetByte(System.Byte@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.Byte" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.Byte" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetBytesFromBase64(System.Byte[]@)">
      <summary>Tries to parse the current JSON token value from the source and decodes the Base64 encoded JSON string as a byte array and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the decoded binary representation of the Base64 text.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token is not a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <returns>
        <see langword="true" /> if the entire token value is encoded as valid Base64 text and can be successfully decoded to bytes; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetDateTime(System.DateTime@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.DateTime" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.DateTime" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetDateTimeOffset(System.DateTimeOffset@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.DateTimeOffset" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.DateTimeOffset" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetDecimal(System.Decimal@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.Decimal" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.Decimal" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetDouble(System.Double@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.Double" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.Double" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetGuid(System.Guid@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.Guid" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The value of the JSON token isn't a <see cref="F:System.Text.Json.JsonTokenType.String" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.Guid" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetInt16(System.Int16@)">
      <summary>Tries to parse the current JSON token value from the source as an <see cref="T:System.Int16" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.Int16" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetInt32(System.Int32@)">
      <summary>Tries to parse the current JSON token value from the source as an <see cref="T:System.Int32" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to an <see cref="T:System.Int32" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetInt64(System.Int64@)">
      <summary>Tries to parse the current JSON token value from the source as an <see cref="T:System.Int64" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to an <see cref="T:System.Int64" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetSByte(System.SByte@)">
      <summary>Tries to parse the current JSON token value from the source as an <see cref="T:System.SByte" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to an <see cref="T:System.SByte" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetSingle(System.Single@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.Single" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to an <see cref="T:System.Single" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetUInt16(System.UInt16@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.UInt16" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.UInt16" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetUInt32(System.UInt32@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.UInt32" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.UInt32" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TryGetUInt64(System.UInt64@)">
      <summary>Tries to parse the current JSON token value from the source as a <see cref="T:System.UInt64" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="value">When this method returns, contains the parsed value.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token value isn't a <see cref="F:System.Text.Json.JsonTokenType.Number" />.</exception>
      <returns>
        <see langword="true" /> if the entire UTF-8 encoded token value can be successfully parsed to a <see cref="T:System.UInt64" /> value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.TrySkip">
      <summary>Tries to skip the children of the current JSON token.</summary>
      <exception cref="T:System.Text.Json.JsonException">An invalid JSON token was encountered while skipping, according to the JSON RFC.
          
-or -

The current depth exceeds the recursive limit set by the maximum depth.</exception>
      <returns>
        <see langword="true" /> if there was enough data for the children to be skipped successfully; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.ValueTextEquals(System.ReadOnlySpan{System.Byte})">
      <summary>Compares the UTF-8 encoded text in a read-only byte span to the unescaped JSON token value in the source and returns a value that indicates whether they match.</summary>
      <param name="utf8Text">The UTF-8 encoded text to compare against.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token is not a JSON string (that is, it is not <see cref="F:System.Text.Json.JsonTokenType.String" /> or <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />).</exception>
      <returns>
        <see langword="true" /> if the JSON token value in the source matches the UTF-8 encoded lookup text; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.ValueTextEquals(System.ReadOnlySpan{System.Char})">
      <summary>Compares the text in a read-only character span to the unescaped JSON token value in the source and returns a value that indicates whether they match.</summary>
      <param name="text">The text to compare against.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token is not a JSON string (that is, it is not <see cref="F:System.Text.Json.JsonTokenType.String" /> or <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />).</exception>
      <returns>
        <see langword="true" /> if the JSON token value in the source matches the lookup text; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonReader.ValueTextEquals(System.String)">
      <summary>Compares the string text to the unescaped JSON token value in the source and returns a value that indicates whether they match.</summary>
      <param name="text">The text to compare against.</param>
      <exception cref="T:System.InvalidOperationException">The JSON token is not a JSON string (that is, it is not <see cref="F:System.Text.Json.JsonTokenType.String" /> or <see cref="F:System.Text.Json.JsonTokenType.PropertyName" />).</exception>
      <returns>
        <see langword="true" /> if the JSON token value in the source matches the lookup text; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.BytesConsumed">
      <summary>Gets the total number of bytes consumed so far by this instance of the <see cref="T:System.Text.Json.Utf8JsonReader" />.</summary>
      <returns>The total number of bytes consumed so far.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.CurrentDepth">
      <summary>Gets the depth of the current token.</summary>
      <returns>The depth of the current token.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.CurrentState">
      <summary>Gets the current <see cref="T:System.Text.Json.Utf8JsonReader" /> state to pass to a <see cref="T:System.Text.Json.Utf8JsonReader" /> constructor with more data.</summary>
      <returns>The current reader state.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.HasValueSequence">
      <summary>Gets a value that indicates which <c>Value</c> property to use to get the token value.</summary>
      <returns>
        <see langword="true" /> if <see cref="P:System.Text.Json.Utf8JsonReader.ValueSequence" /> should be used to get the token value; <see langword="false" /> if <see cref="P:System.Text.Json.Utf8JsonReader.ValueSpan" /> should be used instead.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.IsFinalBlock">
      <summary>Gets the mode of this instance of the <see cref="T:System.Text.Json.Utf8JsonReader" /> which indicates whether all the JSON data was provided or there is more data to come.</summary>
      <returns>
        <see langword="true" /> if the reader was constructed with the input span or sequence containing the entire JSON data to process; <see langword="false" /> if the reader was constructed with an input span or sequence that may contain partial JSON data with more data to follow.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.Position">
      <summary>Gets the current <see cref="T:System.SequencePosition" /> within the provided UTF-8 encoded input ReadOnlySequence&lt;byte&gt; or a default <see cref="T:System.SequencePosition" /> if the <see cref="T:System.Text.Json.Utf8JsonReader" /> struct was constructed with a ReadOnlySpan&lt;byte&gt;.</summary>
      <returns>The current <see cref="T:System.SequencePosition" /> within the provided UTF-8 encoded input ReadOnlySequence&lt;byte&gt; or a default <see cref="T:System.SequencePosition" /> if the <see cref="T:System.Text.Json.Utf8JsonReader" /> struct was constructed with a ReadOnlySpan&lt;byte&gt;.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.TokenStartIndex">
      <summary>Gets the index that the last processed JSON token starts at (within the given UTF-8 encoded input text), skipping any white space.</summary>
      <returns>The starting index of the last processed JSON token within the given UTF-8 encoded input text.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.TokenType">
      <summary>Gets the type of the last processed JSON token in the UTF-8 encoded JSON text.</summary>
      <returns>The type of the last processed JSON token.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.ValueSequence">
      <summary>Gets the raw value of the last processed token as a ReadOnlySequence&lt;byte&gt; slice of the input payload, only if the token is contained within multiple segments.</summary>
      <returns>A byte read-only sequence.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonReader.ValueSpan">
      <summary>Gets the raw value of the last processed token as a ReadOnlySpan&lt;byte&gt; slice of the input payload, if the token fits in a single segment or if the reader was constructed with a JSON payload contained in a ReadOnlySpan&lt;byte&gt;.</summary>
      <returns>A read-only span of bytes.</returns>
    </member>
    <member name="T:System.Text.Json.Utf8JsonWriter">
      <summary>Provides a high-performance API for forward-only, non-cached writing of UTF-8 encoded JSON text.</summary>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.#ctor(System.Buffers.IBufferWriter{System.Byte},System.Text.Json.JsonWriterOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonWriter" /> class using the specified <see cref="T:System.Buffers.IBufferWriter`1" /> to write the output to and customization options.</summary>
      <param name="bufferWriter">The destination for writing JSON text.</param>
      <param name="options">Defines the customized behavior of the <see cref="T:System.Text.Json.Utf8JsonWriter" />. By default, it writes minimized JSON (with no extra white space) and validates that the JSON being written is structurally valid according to the JSON RFC.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bufferWriter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.#ctor(System.IO.Stream,System.Text.Json.JsonWriterOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Json.Utf8JsonWriter" /> class using the specified stream to write the output to and customization options.</summary>
      <param name="utf8Json">The destination for writing JSON text.</param>
      <param name="options">Defines the customized behavior of the <see cref="T:System.Text.Json.Utf8JsonWriter" />. By default, it writes minimized JSON (with no extra white space) and validates that the JSON being written is structurally valid according to the JSON RFC.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.Dispose">
      <summary>Commits any leftover JSON text that has not yet been flushed and releases all resources used by the current instance.</summary>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.DisposeAsync">
      <summary>Asynchronously commits any leftover JSON text that has not yet been flushed and releases all resources used by the current instance.</summary>
      <returns>A task representing the asynchronous dispose operation.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.Flush">
      <summary>Commits the JSON text written so far, which makes it visible to the output destination.</summary>
      <exception cref="T:System.ObjectDisposedException">This instance has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.FlushAsync(System.Threading.CancellationToken)">
      <summary>Asynchronously commits the JSON text written so far, which makes it visible to the output destination.</summary>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ObjectDisposedException">This instance has been disposed.</exception>
      <returns>A task representing the asynchronous flush operation.</returns>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.Reset">
      <summary>Resets the internal state of this instance so that it can be reused.</summary>
      <exception cref="T:System.ObjectDisposedException">This instance has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.Reset(System.Buffers.IBufferWriter{System.Byte})">
      <summary>Resets the internal state of this instance so that it can be reused with a new instance of <see cref="T:System.Buffers.IBufferWriter`1" />.</summary>
      <param name="bufferWriter">The destination for writing JSON text.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bufferWriter" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">This instance has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.Reset(System.IO.Stream)">
      <summary>Resets the internal state of this instance so that it can be reused with a new instance of <see cref="T:System.IO.Stream" />.</summary>
      <param name="utf8Json">The destination for writing JSON text.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="utf8Json" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">This instance has been disposed.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBase64String(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Writes the property name and raw bytes value (as a Base64 encoded JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded name of the property to write.</param>
      <param name="bytes">The binary data to write as Base64 encoded text.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBase64String(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte})">
      <summary>Writes the property name and raw bytes value (as a Base64 encoded JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="bytes">The binary data to write as Base64 encoded text.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBase64String(System.String,System.ReadOnlySpan{System.Byte})">
      <summary>Writes the property name and raw bytes value (as a Base64 encoded JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="bytes">The binary data to write as Base64 encoded text.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBase64String(System.Text.Json.JsonEncodedText,System.ReadOnlySpan{System.Byte})">
      <summary>Writes the pre-encoded property name and raw bytes value (as a Base64 encoded JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON-encoded name of the property to write.</param>
      <param name="bytes">The binary data to write as Base64 encoded text.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBase64StringValue(System.ReadOnlySpan{System.Byte})">
      <summary>Writes the raw bytes value as a Base64 encoded JSON string as an element of a JSON array.</summary>
      <param name="bytes">The binary data to be written as a Base64 encoded JSON string element of a JSON array.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBoolean(System.ReadOnlySpan{System.Byte},System.Boolean)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.Boolean" /> value (as a JSON literal true or false) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON literal true or false as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBoolean(System.ReadOnlySpan{System.Char},System.Boolean)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.Boolean" /> value (as a JSON literal true or false) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON literal true or false as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBoolean(System.String,System.Boolean)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.Boolean" /> value (as a JSON literal true or false) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON literal true or false as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBoolean(System.Text.Json.JsonEncodedText,System.Boolean)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Boolean" /> value (as a JSON literal true or false) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON literal true or false as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteBooleanValue(System.Boolean)">
      <summary>Writes a <see cref="T:System.Boolean" /> value (as a JSON literal true or false) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON literal true or false as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteCommentValue(System.ReadOnlySpan{System.Byte})">
      <summary>Writes a UTF-8 text value as a JSON comment.</summary>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON comment within <c>/*..*/</c>.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.
        
-or-

<paramref name="utf8Value" /> contains a comment delimiter (that is, <c>*/</c>).</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteCommentValue(System.ReadOnlySpan{System.Char})">
      <summary>Writes a UTF-16 text value as a JSON comment.</summary>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON comment within <c>/*..*/</c>.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.
        
-or-

<paramref name="value" /> contains a comment delimiter (that is, <c>*/</c>).</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteCommentValue(System.String)">
      <summary>Writes a string text value as a JSON comment.</summary>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON comment within <c>/*..*/</c>.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.
        
-or-

<paramref name="value" /> contains a comment delimiter (that is, <c>*/</c>).</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteEndArray">
      <summary>Writes the end of a JSON array.</summary>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteEndObject">
      <summary>Writes the end of a JSON object.</summary>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNull(System.ReadOnlySpan{System.Byte})">
      <summary>Writes a property name specified as a read-only span of bytes and the JSON literal null as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNull(System.ReadOnlySpan{System.Char})">
      <summary>Writes a property name specified as a read-only character span and the JSON literal null as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNull(System.String)">
      <summary>Writes a property name specified as a string and the JSON literal null as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNull(System.Text.Json.JsonEncodedText)">
      <summary>Writes the pre-encoded property name and the JSON literal null as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNullValue">
      <summary>Writes the JSON literal null as an element of a JSON array.</summary>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.Decimal)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.Decimal" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.Double)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.Double" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.Int32)">
      <summary>Writes a property name specified as a read-only span of bytes and an <see cref="T:System.Int32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.Int64)">
      <summary>Writes a property name specified as a read-only span of bytes and an <see cref="T:System.Int64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.Single)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.Single" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.UInt32)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.UInt32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Byte},System.UInt64)">
      <summary>Writes a property name specified as a read-only span of bytes and a <see cref="T:System.UInt64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.Decimal)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.Decimal" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.Double)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.Double" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.Int32)">
      <summary>Writes a property name specified as a read-only character span and an <see cref="T:System.Int32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.Int64)">
      <summary>Writes a property name specified as a read-only character span and an <see cref="T:System.Int64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.Single)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.Single" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.UInt32)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.UInt32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.ReadOnlySpan{System.Char},System.UInt64)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.UInt64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.Decimal)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.Decimal" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.Double)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.Double" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.Int32)">
      <summary>Writes a property name specified as a string and an <see cref="T:System.Int32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.Int64)">
      <summary>Writes a property name specified as a string and an <see cref="T:System.Int64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.Single)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.Single" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.UInt32)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.UInt32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.String,System.UInt64)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.UInt64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.Decimal)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Decimal" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.Double)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Double" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.Int32)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Int32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.Int64)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Int64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.Single)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Single" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.UInt32)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.UInt32" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumber(System.Text.Json.JsonEncodedText,System.UInt64)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.UInt64" /> value (as a JSON number) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON number as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.Decimal)">
      <summary>Writes a <see cref="T:System.Decimal" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.Double)">
      <summary>Writes a <see cref="T:System.Double" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.Int32)">
      <summary>Writes an <see cref="T:System.Int32" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.Int64)">
      <summary>Writes an <see cref="T:System.Int64" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.Single)">
      <summary>Writes a <see cref="T:System.Single" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.UInt32)">
      <summary>Writes a <see cref="T:System.UInt32" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteNumberValue(System.UInt64)">
      <summary>Writes a <see cref="T:System.UInt64" /> value (as a JSON number) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON number as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WritePropertyName(System.ReadOnlySpan{System.Byte})">
      <summary>Writes the UTF-8 property name (as a JSON string) as the first part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WritePropertyName(System.ReadOnlySpan{System.Char})">
      <summary>Writes the property name (as a JSON string) as the first part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WritePropertyName(System.String)">
      <summary>Writes the property name (as a JSON string) as the first part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="propertyName" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WritePropertyName(System.Text.Json.JsonEncodedText)">
      <summary>Writes the pre-encoded property name (as a JSON string) as the first part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteRawValue(System.ReadOnlySpan{System.Byte},System.Boolean)">
      <summary>Writes the input as JSON content. It is expected that the input content is a single complete JSON value.</summary>
      <param name="utf8Json">The raw JSON content to write.</param>
      <param name="skipInputValidation">
        <see langword="false" /> to validate if the input is an RFC 8259-compliant JSON payload; <see langword="true" /> otherwise.</param>
      <exception cref="T:System.ArgumentException">The length of the input is zero or equal to <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="skipInputValidation" /> is <see langword="false" />, and the input is not a valid, complete, single JSON value according to the JSON RFC, or the input JSON exceeds a recursive depth of 64.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteRawValue(System.ReadOnlySpan{System.Char},System.Boolean)">
      <summary>Writes the input as JSON content. It is expected that the input content is a single complete JSON value.</summary>
      <param name="json">The raw JSON content to write.</param>
      <param name="skipInputValidation">
        <see langword="false" /> to validate if the input is an RFC 8259-compliant JSON payload; <see langword="true" /> otherwise.</param>
      <exception cref="T:System.ArgumentException">The length of the input is zero or greater than 715,827,882 (<see cref="F:System.Int32.MaxValue" /> / 3).</exception>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="skipInputValidation" /> is <see langword="false" />, and the input is not a valid, complete, single JSON value according to the JSON RFC, or the input JSON exceeds a recursive depth of 64.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteRawValue(System.String,System.Boolean)">
      <summary>Writes the input as JSON content. It is expected that the input content is a single complete JSON value.</summary>
      <param name="json">The raw JSON content to write.</param>
      <param name="skipInputValidation">
        <see langword="false" /> to validate if the input is an RFC 8259-compliant JSON payload; <see langword="true" /> otherwise.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="json" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The length of the input is zero or greater than 715,827,882 (<see cref="F:System.Int32.MaxValue" /> / 3).</exception>
      <exception cref="T:System.Text.Json.JsonException">
        <paramref name="skipInputValidation" /> is <see langword="false" />, and the input is not a valid, complete, single JSON value according to the JSON RFC, or the input JSON exceeds a recursive depth of 64.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartArray">
      <summary>Writes the beginning of a JSON array.</summary>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.

-or-

Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartArray(System.ReadOnlySpan{System.Byte})">
      <summary>Writes the beginning of a JSON array with a property name specified as a read-only span of bytes as the key.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON array to be written.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.

-or-
       
Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartArray(System.ReadOnlySpan{System.Char})">
      <summary>Writes the beginning of a JSON array with a property name specified as a read-only character span as the key.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON array to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.

-or-
       
Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartArray(System.String)">
      <summary>Writes the beginning of a JSON array with a property name specified as a string as the key.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON array to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.

-or-
       
Validation is enabled, and this write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartArray(System.Text.Json.JsonEncodedText)">
      <summary>Writes the beginning of a JSON array with a pre-encoded property name as the key.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON array to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON has exceeded the maximum depth of 1,000.
    
-or-

Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartObject">
      <summary>Writes the beginning of a JSON object.</summary>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000.

-or-

Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartObject(System.ReadOnlySpan{System.Byte})">
      <summary>Writes the beginning of a JSON object with a property name specified as a read-only span of bytes as the key.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000. 

-or-
       
Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartObject(System.ReadOnlySpan{System.Char})">
      <summary>Writes the beginning of a JSON object with a property name specififed as a read-only character span as the key.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000. 

-or-
       
Validation is enabled, and this write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartObject(System.String)">
      <summary>Writes the beginning of a JSON object with a property name specified as a string as the key.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON exceeds the maximum depth of 1,000. 

-or-
       
Validation is enabled, and this write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStartObject(System.Text.Json.JsonEncodedText)">
      <summary>Writes the beginning of a JSON object with a pre-encoded property name as the key.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <exception cref="T:System.InvalidOperationException">The depth of the JSON has exceeded the maximum depth of 1,000.
          
-or-

Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.DateTime)">
      <summary>Writes a UTF-8 property name and a <see cref="T:System.DateTime" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.DateTimeOffset)">
      <summary>Writes a UTF-8 property name and a <see cref="T:System.DateTimeOffset" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.Guid)">
      <summary>Writes a UTF-8 property name and a <see cref="T:System.Guid" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Writes a UTF-8 property name and UTF-8 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Char})">
      <summary>Writes a UTF-8 property name and UTF-16 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.String)">
      <summary>Writes a UTF-8 property name and string text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Byte},System.Text.Json.JsonEncodedText)">
      <summary>Writes the UTF-8 property name and pre-encoded value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="utf8PropertyName">The UTF-8 encoded property name of the JSON object to be written.</param>
      <param name="value">The JSON encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and this method would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.DateTime)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.DateTime" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.DateTimeOffset)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.DateTimeOffset" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.Guid)">
      <summary>Writes a property name specified as a read-only character span and a <see cref="T:System.Guid" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte})">
      <summary>Writes a UTF-16 property name and UTF-8 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
      <summary>Writes a UTF-16 property name and UTF-16 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.String)">
      <summary>Writes a UTF-16 property name and string text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.ReadOnlySpan{System.Char},System.Text.Json.JsonEncodedText)">
      <summary>Writes the property name and pre-encoded value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The JSON encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.DateTime)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.DateTime" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.DateTimeOffset)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.DateTimeOffset" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.Guid)">
      <summary>Writes a property name specified as a string and a <see cref="T:System.Guid" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.ReadOnlySpan{System.Byte})">
      <summary>Writes a property name specified as a string and a UTF-8 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.ReadOnlySpan{System.Char})">
      <summary>Writes a property name specified as a string and a UTF-16 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.String)">
      <summary>Writes a property name specified as a string and a string text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The UTF-16 encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name or value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.String,System.Text.Json.JsonEncodedText)">
      <summary>Writes the property name and pre-encoded value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The JSON encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified property name is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="propertyName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.DateTime)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.DateTime" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.DateTimeOffset)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.DateTimeOffset" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.Guid)">
      <summary>Writes the pre-encoded property name and <see cref="T:System.Guid" /> value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.ReadOnlySpan{System.Byte})">
      <summary>Writes the pre-encoded property name and UTF-8 text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.ReadOnlySpan{System.Char})">
      <summary>Writes the pre-encoded property name and text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.String)">
      <summary>Writes the pre-encoded property name and string text value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteString(System.Text.Json.JsonEncodedText,System.Text.Json.JsonEncodedText)">
      <summary>Writes the pre-encoded property name and pre-encoded value (as a JSON string) as part of a name/value pair of a JSON object.</summary>
      <param name="propertyName">The JSON encoded property name of the JSON object to be transcoded and written as UTF-8.</param>
      <param name="value">The JSON encoded value to be written as a UTF-8 transcoded JSON string as part of the name/value pair.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.DateTime)">
      <summary>Writes a <see cref="T:System.DateTime" /> value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON string as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.DateTimeOffset)">
      <summary>Writes a <see cref="T:System.DateTimeOffset" /> value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON string as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.Guid)">
      <summary>Writes a <see cref="T:System.Guid" /> value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The value to be written as a JSON string as an element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the operation would result in writing invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.ReadOnlySpan{System.Byte})">
      <summary>Writes a UTF-8 text value (as a JSON string) as an element of a JSON array.</summary>
      <param name="utf8Value">The UTF-8 encoded value to be written as a JSON string element of a JSON array.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.ReadOnlySpan{System.Char})">
      <summary>Writes a UTF-16 text value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string element of a JSON array.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.String)">
      <summary>Writes a string text value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The UTF-16 encoded value to be written as a UTF-8 transcoded JSON string element of a JSON array.</param>
      <exception cref="T:System.ArgumentException">The specified value is too large.</exception>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="M:System.Text.Json.Utf8JsonWriter.WriteStringValue(System.Text.Json.JsonEncodedText)">
      <summary>Writes the pre-encoded text value (as a JSON string) as an element of a JSON array.</summary>
      <param name="value">The JSON encoded value to be written as a UTF-8 transcoded JSON string element of a JSON array.</param>
      <exception cref="T:System.InvalidOperationException">Validation is enabled, and the write operation would produce invalid JSON.</exception>
    </member>
    <member name="P:System.Text.Json.Utf8JsonWriter.BytesCommitted">
      <summary>Gets the total number of bytes committed to the output by the current instance so far.</summary>
      <returns>The total number of bytes committed to the output by the <see cref="T:System.Text.Json.Utf8JsonWriter" /> so far.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonWriter.BytesPending">
      <summary>Gets the number of bytes written by the <see cref="T:System.Text.Json.Utf8JsonWriter" /> so far that have not yet been flushed to the output and committed.</summary>
      <returns>The number of bytes written so far by the <see cref="T:System.Text.Json.Utf8JsonWriter" /> that have not yet been flushed to the output and committed.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonWriter.CurrentDepth">
      <summary>Gets the depth of the current token.</summary>
      <returns>The depth of the current token.</returns>
    </member>
    <member name="P:System.Text.Json.Utf8JsonWriter.Options">
      <summary>Gets the custom behavior when writing JSON using this instance, which indicates whether to format the output while writing, whether to skip structural JSON validation, and which characters to escape.</summary>
      <returns>The custom behavior of this instance of the writer for formatting, validating, and escaping.</returns>
    </member>
  </members>
</doc>