﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmAttPro
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmAttPro))
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.BehaviorManager1 = New DevExpress.Utils.Behaviors.BehaviorManager(Me.components)
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.XtraTabPage1 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.ProgressPanel2 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.cmbEncryption = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Richtext_En_base64 = New System.Windows.Forms.RichTextBox()
        Me.Richtext_NormalBase64 = New System.Windows.Forms.RichTextBox()
        Me.PathLetterNormalBase64 = New DevExpress.XtraEditors.TextEdit()
        Me.lblPath = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.lblResults = New System.Windows.Forms.Label()
        Me.XtraTabPage2 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.PathSaveFile_Encoded = New DevExpress.XtraEditors.TextEdit()
        Me.PathFile_Att_VIP = New DevExpress.XtraEditors.TextEdit()
        Me.LogInNormalTextBox3 = New System.Windows.Forms.TextBox()
        Me.txttitle = New System.Windows.Forms.TextBox()
        Me.LogInCheckBox1 = New System.Windows.Forms.CheckBox()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.lblSaveLocation = New System.Windows.Forms.Label()
        Me.lblVIPPathFile = New System.Windows.Forms.Label()
        Me.ProgressBarControl1 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.BarManager1 = New DevExpress.XtraBars.BarManager(Me.components)
        Me.barDockControlTop = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlBottom = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlLeft = New DevExpress.XtraBars.BarDockControl()
        Me.barDockControlRight = New DevExpress.XtraBars.BarDockControl()
        Me.SelectLetterNormal_Base64 = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_EN_Base64 = New DevExpress.XtraBars.BarButtonItem()
        Me.LetterEncoder_bntClearAll = New DevExpress.XtraBars.BarButtonItem()
        Me.PNLAttachWait = New System.Windows.Forms.Panel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.XtraTabPage3 = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.ProgressPanel3 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.lblGoldResults = New System.Windows.Forms.Label()
        Me.lblGoldTitle = New System.Windows.Forms.Label()
        Me.txtGoldTitle = New DevExpress.XtraEditors.TextEdit()
        Me.txtFilepath_Goldn = New DevExpress.XtraEditors.TextEdit()
        Me.RichTextBox_Goldn_Normal = New System.Windows.Forms.RichTextBox()
        Me.RichTextBox2_Goldn = New System.Windows.Forms.RichTextBox()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControl9 = New DevExpress.XtraLayout.LayoutControl()
        Me.LetterEncoder_txtFilepath_Goldn = New DevExpress.XtraEditors.TextEdit()
        Me.RichTextBox1_Goldn_Normal = New System.Windows.Forms.RichTextBox()
        Me.LayoutControlGroup8 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem29 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.PopupMenu1 = New DevExpress.XtraBars.PopupMenu(Me.components)
        Me.BarManager2 = New DevExpress.XtraBars.BarManager(Me.components)
        Me.BarDockControl1 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl2 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl3 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl4 = New DevExpress.XtraBars.BarDockControl()
        Me.BntSelect_Letter_VIP_Normal = New DevExpress.XtraBars.BarButtonItem()
        Me.Bnt_En_VIP = New DevExpress.XtraBars.BarButtonItem()
        Me.BntSaveVIP = New DevExpress.XtraBars.BarButtonItem()
        Me.bntClearPathVIP = New DevExpress.XtraBars.BarButtonItem()
        Me.PopupMenu2 = New DevExpress.XtraBars.PopupMenu(Me.components)
        Me.PopupMenu3 = New DevExpress.XtraBars.PopupMenu(Me.components)
        Me.BntAttSelect = New DevExpress.XtraBars.BarButtonItem()
        Me.BntEncodedAtt = New DevExpress.XtraBars.BarButtonItem()
        Me.BntRemoveAll = New DevExpress.XtraBars.BarButtonItem()
        Me.BarManager3 = New DevExpress.XtraBars.BarManager(Me.components)
        Me.BarDockControl5 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl6 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl7 = New DevExpress.XtraBars.BarDockControl()
        Me.BarDockControl8 = New DevExpress.XtraBars.BarDockControl()
        Me.RichTextBox_Goldn = New System.Windows.Forms.RichTextBox()
        Me.LayoutControlItem16 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlGroup7 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControl8 = New DevExpress.XtraLayout.LayoutControl()
        CType(Me.BehaviorManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.XtraTabPage1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.cmbEncryption.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PathLetterNormalBase64.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage2.SuspendLayout()
        Me.Panel2.SuspendLayout()
        CType(Me.PathSaveFile_Encoded.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PathFile_Att_VIP.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PNLAttachWait.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage3.SuspendLayout()
        Me.Panel3.SuspendLayout()
        CType(Me.txtGoldTitle.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtFilepath_Goldn.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControl9, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl9.SuspendLayout()
        CType(Me.LetterEncoder_txtFilepath_Goldn.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem29, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PopupMenu1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BarManager2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PopupMenu2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PopupMenu3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BarManager3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControl8, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl8.SuspendLayout()
        Me.SuspendLayout()
        '
        'Timer1
        '
        Me.Timer1.Interval = 30
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.XtraTabControl1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(972, 602)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.XtraTabControl1.Appearance.Options.UseBackColor = True
        Me.XtraTabControl1.AppearancePage.Header.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.XtraTabControl1.AppearancePage.Header.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.AppearancePage.Header.ForeColor = System.Drawing.Color.White
        Me.XtraTabControl1.AppearancePage.Header.Options.UseBackColor = True
        Me.XtraTabControl1.AppearancePage.Header.Options.UseFont = True
        Me.XtraTabControl1.AppearancePage.Header.Options.UseForeColor = True
        Me.XtraTabControl1.AppearancePage.HeaderActive.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.XtraTabControl1.AppearancePage.HeaderActive.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.AppearancePage.HeaderActive.ForeColor = System.Drawing.Color.White
        Me.XtraTabControl1.AppearancePage.HeaderActive.Options.UseBackColor = True
        Me.XtraTabControl1.AppearancePage.HeaderActive.Options.UseFont = True
        Me.XtraTabControl1.AppearancePage.HeaderActive.Options.UseForeColor = True
        Me.XtraTabControl1.HeaderAutoFill = DevExpress.Utils.DefaultBoolean.[True]
        Me.XtraTabControl1.HeaderButtons = DevExpress.XtraTab.TabButtons.None
        Me.XtraTabControl1.HeaderButtonsShowMode = DevExpress.XtraTab.TabButtonShowMode.Never
        Me.XtraTabControl1.HeaderOrientation = DevExpress.XtraTab.TabOrientation.Horizontal
        Me.XtraTabControl1.Location = New System.Drawing.Point(16, 16)
        Me.XtraTabControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.XtraTabPage1
        Me.XtraTabControl1.Size = New System.Drawing.Size(940, 570)
        Me.XtraTabControl1.TabIndex = 4
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.XtraTabPage1, Me.XtraTabPage2, Me.XtraTabPage3})
        '
        'XtraTabPage1
        '
        Me.XtraTabPage1.Controls.Add(Me.Panel1)
        Me.XtraTabPage1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedAttachment32x32
        Me.XtraTabPage1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage1.Name = "XtraTabPage1"
        Me.XtraTabPage1.Size = New System.Drawing.Size(938, 526)
        Me.XtraTabPage1.Text = "Base64 Encoder"
        '
        'Panel1
        '
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(14, Byte), Integer), CType(CType(15, Byte), Integer), CType(CType(20, Byte), Integer))
        Me.Panel1.Controls.Add(Me.ProgressPanel2)
        Me.Panel1.Controls.Add(Me.cmbEncryption)
        Me.Panel1.Controls.Add(Me.Richtext_En_base64)
        Me.Panel1.Controls.Add(Me.Richtext_NormalBase64)
        Me.Panel1.Controls.Add(Me.PathLetterNormalBase64)
        Me.Panel1.Controls.Add(Me.lblPath)
        Me.Panel1.Controls.Add(Me.Label4)
        Me.Panel1.Controls.Add(Me.lblResults)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(938, 526)
        Me.Panel1.TabIndex = 0
        '
        'ProgressPanel2
        '
        Me.ProgressPanel2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.ProgressPanel2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.ProgressPanel2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.ProgressPanel2.Appearance.Font = New System.Drawing.Font("Comfortaa", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel2.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.ProgressPanel2.Appearance.Options.UseBackColor = True
        Me.ProgressPanel2.Appearance.Options.UseBorderColor = True
        Me.ProgressPanel2.Appearance.Options.UseFont = True
        Me.ProgressPanel2.Appearance.Options.UseForeColor = True
        Me.ProgressPanel2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
        Me.ProgressPanel2.Location = New System.Drawing.Point(319, 200)
        Me.ProgressPanel2.Name = "ProgressPanel2"
        Me.ProgressPanel2.Size = New System.Drawing.Size(300, 80)
        Me.ProgressPanel2.TabIndex = 75
        Me.ProgressPanel2.Text = "🔄 Processing... 0%"
        Me.ProgressPanel2.Visible = False
        '
        'cmbEncryption
        '
        Me.cmbEncryption.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.cmbEncryption.EditValue = "Base64"
        Me.cmbEncryption.Location = New System.Drawing.Point(369, 80)
        Me.cmbEncryption.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cmbEncryption.Name = "cmbEncryption"
        Me.cmbEncryption.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.cmbEncryption.Properties.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(76, Byte), Integer), CType(CType(84, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.cmbEncryption.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbEncryption.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.cmbEncryption.Properties.Appearance.Options.UseBackColor = True
        Me.cmbEncryption.Properties.Appearance.Options.UseBorderColor = True
        Me.cmbEncryption.Properties.Appearance.Options.UseFont = True
        Me.cmbEncryption.Properties.Appearance.Options.UseForeColor = True
        Me.cmbEncryption.Properties.AppearanceFocused.BorderColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.cmbEncryption.Properties.AppearanceFocused.Options.UseBorderColor = True
        Me.cmbEncryption.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
        Me.cmbEncryption.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cmbEncryption.Properties.Items.AddRange(New Object() {"Base64"})
        Me.cmbEncryption.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cmbEncryption.Size = New System.Drawing.Size(200, 36)
        Me.cmbEncryption.TabIndex = 73
        '
        'Richtext_En_base64
        '
        Me.Richtext_En_base64.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Richtext_En_base64.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Richtext_En_base64.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Richtext_En_base64.Location = New System.Drawing.Point(21, 349)
        Me.Richtext_En_base64.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Richtext_En_base64.Name = "Richtext_En_base64"
        Me.Richtext_En_base64.Size = New System.Drawing.Size(902, 165)
        Me.Richtext_En_base64.TabIndex = 70
        Me.Richtext_En_base64.Text = ""
        Me.Richtext_En_base64.Visible = False
        '
        'Richtext_NormalBase64
        '
        Me.Richtext_NormalBase64.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Richtext_NormalBase64.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Richtext_NormalBase64.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Richtext_NormalBase64.Font = New System.Drawing.Font("Consolas", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Richtext_NormalBase64.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.Richtext_NormalBase64.Location = New System.Drawing.Point(30, 300)
        Me.Richtext_NormalBase64.Margin = New System.Windows.Forms.Padding(30, 10, 30, 30)
        Me.Richtext_NormalBase64.Name = "Richtext_NormalBase64"
        Me.Richtext_NormalBase64.Size = New System.Drawing.Size(878, 190)
        Me.Richtext_NormalBase64.TabIndex = 69
        Me.Richtext_NormalBase64.Text = ""
        Me.Richtext_NormalBase64.WordWrap = False
        '
        'PathLetterNormalBase64
        '
        Me.PathLetterNormalBase64.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PathLetterNormalBase64.EditValue = ""
        Me.PathLetterNormalBase64.Location = New System.Drawing.Point(30, 150)
        Me.PathLetterNormalBase64.Margin = New System.Windows.Forms.Padding(4, 1, 4, 1)
        Me.PathLetterNormalBase64.Name = "PathLetterNormalBase64"
        Me.PathLetterNormalBase64.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.PathLetterNormalBase64.Properties.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(76, Byte), Integer), CType(CType(84, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.PathLetterNormalBase64.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.PathLetterNormalBase64.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.PathLetterNormalBase64.Properties.Appearance.Options.UseBackColor = True
        Me.PathLetterNormalBase64.Properties.Appearance.Options.UseBorderColor = True
        Me.PathLetterNormalBase64.Properties.Appearance.Options.UseFont = True
        Me.PathLetterNormalBase64.Properties.Appearance.Options.UseForeColor = True
        Me.PathLetterNormalBase64.Properties.AppearanceFocused.BorderColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.PathLetterNormalBase64.Properties.AppearanceFocused.Options.UseBorderColor = True
        Me.PathLetterNormalBase64.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
        Me.PathLetterNormalBase64.Properties.NullValuePrompt = "📁 Select HTML file to encode..."
        Me.PathLetterNormalBase64.Properties.Padding = New System.Windows.Forms.Padding(15, 0, 0, 0)
        Me.PathLetterNormalBase64.Size = New System.Drawing.Size(878, 36)
        Me.PathLetterNormalBase64.TabIndex = 64
        '
        'lblPath
        '
        Me.lblPath.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblPath.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.lblPath.Location = New System.Drawing.Point(30, 120)
        Me.lblPath.Name = "lblPath"
        Me.lblPath.Size = New System.Drawing.Size(200, 25)
        Me.lblPath.TabIndex = 1
        Me.lblPath.Text = "📁 HTML File Path:"
        '
        'Label4
        '
        Me.Label4.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 16.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.Label4.Location = New System.Drawing.Point(344, 26)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(300, 40)
        Me.Label4.TabIndex = 1
        Me.Label4.Text = "🔐 Base64 Encoder"
        '
        'lblResults
        '
        Me.lblResults.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblResults.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.lblResults.Location = New System.Drawing.Point(30, 270)
        Me.lblResults.Name = "lblResults"
        Me.lblResults.Size = New System.Drawing.Size(250, 25)
        Me.lblResults.TabIndex = 2
        Me.lblResults.Text = "📋 Encoding Results:"
        '
        'XtraTabPage2
        '
        Me.XtraTabPage2.Controls.Add(Me.Panel2)
        Me.XtraTabPage2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.VIPRedirect32x32
        Me.XtraTabPage2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage2.Name = "XtraTabPage2"
        Me.XtraTabPage2.Size = New System.Drawing.Size(938, 526)
        Me.XtraTabPage2.Text = "VIP Encoder"
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(14, Byte), Integer), CType(CType(15, Byte), Integer), CType(CType(20, Byte), Integer))
        Me.Panel2.Controls.Add(Me.PathSaveFile_Encoded)
        Me.Panel2.Controls.Add(Me.PathFile_Att_VIP)
        Me.Panel2.Controls.Add(Me.LogInNormalTextBox3)
        Me.Panel2.Controls.Add(Me.txttitle)
        Me.Panel2.Controls.Add(Me.LogInCheckBox1)
        Me.Panel2.Controls.Add(Me.Label5)
        Me.Panel2.Controls.Add(Me.lblSaveLocation)
        Me.Panel2.Controls.Add(Me.lblVIPPathFile)
        Me.Panel2.Controls.Add(Me.ProgressBarControl1)
        Me.Panel2.Controls.Add(Me.PNLAttachWait)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel2.Location = New System.Drawing.Point(0, 0)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(938, 526)
        Me.Panel2.TabIndex = 0
        '
        'PathSaveFile_Encoded
        '
        Me.PathSaveFile_Encoded.EditValue = ""
        Me.PathSaveFile_Encoded.Location = New System.Drawing.Point(30, 216)
        Me.PathSaveFile_Encoded.Margin = New System.Windows.Forms.Padding(4, 1, 4, 1)
        Me.PathSaveFile_Encoded.Name = "PathSaveFile_Encoded"
        Me.PathSaveFile_Encoded.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.PathSaveFile_Encoded.Properties.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(76, Byte), Integer), CType(CType(84, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.PathSaveFile_Encoded.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.PathSaveFile_Encoded.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.PathSaveFile_Encoded.Properties.Appearance.Options.UseBackColor = True
        Me.PathSaveFile_Encoded.Properties.Appearance.Options.UseBorderColor = True
        Me.PathSaveFile_Encoded.Properties.Appearance.Options.UseFont = True
        Me.PathSaveFile_Encoded.Properties.Appearance.Options.UseForeColor = True
        Me.PathSaveFile_Encoded.Properties.AppearanceFocused.BorderColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.PathSaveFile_Encoded.Properties.AppearanceFocused.Options.UseBorderColor = True
        Me.PathSaveFile_Encoded.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
        Me.PathSaveFile_Encoded.Properties.NullValuePrompt = "💾 Save location for encoded file..."
        Me.PathSaveFile_Encoded.Properties.Padding = New System.Windows.Forms.Padding(15, 0, 0, 0)
        Me.PathSaveFile_Encoded.Size = New System.Drawing.Size(878, 36)
        Me.PathSaveFile_Encoded.TabIndex = 303
        '
        'PathFile_Att_VIP
        '
        Me.PathFile_Att_VIP.EditValue = ""
        Me.PathFile_Att_VIP.Location = New System.Drawing.Point(30, 150)
        Me.PathFile_Att_VIP.Margin = New System.Windows.Forms.Padding(4, 1, 4, 1)
        Me.PathFile_Att_VIP.Name = "PathFile_Att_VIP"
        Me.PathFile_Att_VIP.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.PathFile_Att_VIP.Properties.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(76, Byte), Integer), CType(CType(84, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.PathFile_Att_VIP.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.PathFile_Att_VIP.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.PathFile_Att_VIP.Properties.Appearance.Options.UseBackColor = True
        Me.PathFile_Att_VIP.Properties.Appearance.Options.UseBorderColor = True
        Me.PathFile_Att_VIP.Properties.Appearance.Options.UseFont = True
        Me.PathFile_Att_VIP.Properties.Appearance.Options.UseForeColor = True
        Me.PathFile_Att_VIP.Properties.AppearanceFocused.BorderColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.PathFile_Att_VIP.Properties.AppearanceFocused.Options.UseBorderColor = True
        Me.PathFile_Att_VIP.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
        Me.PathFile_Att_VIP.Properties.NullValuePrompt = "📁 Select VIP attachment file..."
        Me.PathFile_Att_VIP.Properties.Padding = New System.Windows.Forms.Padding(15, 0, 0, 0)
        Me.PathFile_Att_VIP.Size = New System.Drawing.Size(878, 36)
        Me.PathFile_Att_VIP.TabIndex = 302
        '
        'LogInNormalTextBox3
        '
        Me.LogInNormalTextBox3.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.0!)
        Me.LogInNormalTextBox3.ForeColor = System.Drawing.Color.FromArgb(CType(CType(8, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(81, Byte), Integer))
        Me.LogInNormalTextBox3.Location = New System.Drawing.Point(742, 114)
        Me.LogInNormalTextBox3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LogInNormalTextBox3.Multiline = True
        Me.LogInNormalTextBox3.Name = "LogInNormalTextBox3"
        Me.LogInNormalTextBox3.Size = New System.Drawing.Size(42, 37)
        Me.LogInNormalTextBox3.TabIndex = 304
        Me.LogInNormalTextBox3.Visible = False
        '
        'txttitle
        '
        Me.txttitle.Font = New System.Drawing.Font("Segoe UI", 15.75!, System.Drawing.FontStyle.Bold)
        Me.txttitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(36, Byte), Integer))
        Me.txttitle.Location = New System.Drawing.Point(692, 114)
        Me.txttitle.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txttitle.Name = "txttitle"
        Me.txttitle.ReadOnly = True
        Me.txttitle.Size = New System.Drawing.Size(42, 35)
        Me.txttitle.TabIndex = 306
        Me.txttitle.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.txttitle.Visible = False
        '
        'LogInCheckBox1
        '
        Me.LogInCheckBox1.AutoSize = True
        Me.LogInCheckBox1.ForeColor = System.Drawing.Color.White
        Me.LogInCheckBox1.Location = New System.Drawing.Point(586, 129)
        Me.LogInCheckBox1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LogInCheckBox1.Name = "LogInCheckBox1"
        Me.LogInCheckBox1.Size = New System.Drawing.Size(86, 22)
        Me.LogInCheckBox1.TabIndex = 305
        Me.LogInCheckBox1.Text = "CheckBox1"
        Me.LogInCheckBox1.UseVisualStyleBackColor = True
        Me.LogInCheckBox1.Visible = False
        '
        'Label5
        '
        Me.Label5.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label5.Font = New System.Drawing.Font("Comfortaa", 16.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.Label5.Location = New System.Drawing.Point(344, 30)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(300, 40)
        Me.Label5.TabIndex = 70
        Me.Label5.Text = "🔐 VIP Attachment"
        '
        'lblSaveLocation
        '
        Me.lblSaveLocation.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblSaveLocation.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.lblSaveLocation.Location = New System.Drawing.Point(30, 186)
        Me.lblSaveLocation.Name = "lblSaveLocation"
        Me.lblSaveLocation.Size = New System.Drawing.Size(200, 25)
        Me.lblSaveLocation.TabIndex = 69
        Me.lblSaveLocation.Text = "💾 Save Location:"
        '
        'lblVIPPathFile
        '
        Me.lblVIPPathFile.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblVIPPathFile.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.lblVIPPathFile.Location = New System.Drawing.Point(30, 120)
        Me.lblVIPPathFile.Name = "lblVIPPathFile"
        Me.lblVIPPathFile.Size = New System.Drawing.Size(200, 25)
        Me.lblVIPPathFile.TabIndex = 69
        Me.lblVIPPathFile.Text = "📁 VIP File Path:"
        '
        'ProgressBarControl1
        '
        Me.ProgressBarControl1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.ProgressBarControl1.Location = New System.Drawing.Point(0, 497)
        Me.ProgressBarControl1.MenuManager = Me.BarManager1
        Me.ProgressBarControl1.Name = "ProgressBarControl1"
        Me.ProgressBarControl1.Properties.ShowTitle = True
        Me.ProgressBarControl1.Size = New System.Drawing.Size(938, 29)
        Me.ProgressBarControl1.TabIndex = 0
        '
        'BarManager1
        '
        Me.BarManager1.DockControls.Add(Me.barDockControlTop)
        Me.BarManager1.DockControls.Add(Me.barDockControlBottom)
        Me.BarManager1.DockControls.Add(Me.barDockControlLeft)
        Me.BarManager1.DockControls.Add(Me.barDockControlRight)
        Me.BarManager1.Form = Me
        Me.BarManager1.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.SelectLetterNormal_Base64, Me.Bnt_EN_Base64, Me.LetterEncoder_bntClearAll})
        Me.BarManager1.MaxItemId = 3
        '
        'barDockControlTop
        '
        Me.barDockControlTop.CausesValidation = False
        Me.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top
        Me.barDockControlTop.Location = New System.Drawing.Point(0, 0)
        Me.barDockControlTop.Manager = Me.BarManager1
        Me.barDockControlTop.Size = New System.Drawing.Size(972, 0)
        '
        'barDockControlBottom
        '
        Me.barDockControlBottom.CausesValidation = False
        Me.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.barDockControlBottom.Location = New System.Drawing.Point(0, 602)
        Me.barDockControlBottom.Manager = Me.BarManager1
        Me.barDockControlBottom.Size = New System.Drawing.Size(972, 0)
        '
        'barDockControlLeft
        '
        Me.barDockControlLeft.CausesValidation = False
        Me.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left
        Me.barDockControlLeft.Location = New System.Drawing.Point(0, 0)
        Me.barDockControlLeft.Manager = Me.BarManager1
        Me.barDockControlLeft.Size = New System.Drawing.Size(0, 602)
        '
        'barDockControlRight
        '
        Me.barDockControlRight.CausesValidation = False
        Me.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right
        Me.barDockControlRight.Location = New System.Drawing.Point(972, 0)
        Me.barDockControlRight.Manager = Me.BarManager1
        Me.barDockControlRight.Size = New System.Drawing.Size(0, 602)
        '
        'SelectLetterNormal_Base64
        '
        Me.SelectLetterNormal_Base64.Caption = "Select Attachment"
        Me.SelectLetterNormal_Base64.Id = 0
        Me.SelectLetterNormal_Base64.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.SelectLetterNormal_Base64.ImageOptions.LargeImage = CType(resources.GetObject("SelectLetterNormal_Base64.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.SelectLetterNormal_Base64.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.SelectLetterNormal_Base64.ItemAppearance.Normal.Options.UseFont = True
        Me.SelectLetterNormal_Base64.Name = "SelectLetterNormal_Base64"
        '
        'Bnt_EN_Base64
        '
        Me.Bnt_EN_Base64.Caption = "Encoded Attachment"
        Me.Bnt_EN_Base64.Id = 1
        Me.Bnt_EN_Base64.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedAttachment32x32
        Me.Bnt_EN_Base64.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_EN_Base64.ItemAppearance.Normal.Options.UseFont = True
        Me.Bnt_EN_Base64.Name = "Bnt_EN_Base64"
        Me.Bnt_EN_Base64.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'LetterEncoder_bntClearAll
        '
        Me.LetterEncoder_bntClearAll.Caption = "Clear"
        Me.LetterEncoder_bntClearAll.Id = 2
        Me.LetterEncoder_bntClearAll.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.LetterEncoder_bntClearAll.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.LetterEncoder_bntClearAll.ItemAppearance.Normal.Options.UseFont = True
        Me.LetterEncoder_bntClearAll.Name = "LetterEncoder_bntClearAll"
        '
        'PNLAttachWait
        '
        Me.PNLAttachWait.Controls.Add(Me.PictureBox1)
        Me.PNLAttachWait.Controls.Add(Me.ProgressPanel1)
        Me.PNLAttachWait.Location = New System.Drawing.Point(80, 320)
        Me.PNLAttachWait.Name = "PNLAttachWait"
        Me.PNLAttachWait.Size = New System.Drawing.Size(778, 80)
        Me.PNLAttachWait.TabIndex = 0
        Me.PNLAttachWait.Visible = False
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.Valid32x32
        Me.PictureBox1.Location = New System.Drawing.Point(266, 15)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(30, 33)
        Me.PictureBox1.TabIndex = 312
        Me.PictureBox1.TabStop = False
        Me.PictureBox1.Visible = False
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.Location = New System.Drawing.Point(10, 10)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(758, 60)
        Me.ProgressPanel1.TabIndex = 0
        Me.ProgressPanel1.Text = "🔄 Processing VIP... 0%"
        '
        'XtraTabPage3
        '
        Me.XtraTabPage3.Controls.Add(Me.Panel3)
        Me.XtraTabPage3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.VIP_PRIME_PNG
        Me.XtraTabPage3.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.XtraTabPage3.Name = "XtraTabPage3"
        Me.XtraTabPage3.Size = New System.Drawing.Size(938, 526)
        Me.XtraTabPage3.Text = "Gold Encoder"
        '
        'Panel3
        '
        Me.Panel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(14, Byte), Integer), CType(CType(15, Byte), Integer), CType(CType(20, Byte), Integer))
        Me.Panel3.Controls.Add(Me.ProgressPanel3)
        Me.Panel3.Controls.Add(Me.lblGoldResults)
        Me.Panel3.Controls.Add(Me.lblGoldTitle)
        Me.Panel3.Controls.Add(Me.txtGoldTitle)
        Me.Panel3.Controls.Add(Me.txtFilepath_Goldn)
        Me.Panel3.Controls.Add(Me.RichTextBox_Goldn_Normal)
        Me.Panel3.Controls.Add(Me.RichTextBox2_Goldn)
        Me.Panel3.Controls.Add(Me.Label7)
        Me.Panel3.Controls.Add(Me.Label3)
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel3.Location = New System.Drawing.Point(0, 0)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(938, 526)
        Me.Panel3.TabIndex = 0
        '
        'ProgressPanel3
        '
        Me.ProgressPanel3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.ProgressPanel3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.ProgressPanel3.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.ProgressPanel3.Appearance.Font = New System.Drawing.Font("Comfortaa", 11.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel3.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.ProgressPanel3.Appearance.Options.UseBackColor = True
        Me.ProgressPanel3.Appearance.Options.UseBorderColor = True
        Me.ProgressPanel3.Appearance.Options.UseFont = True
        Me.ProgressPanel3.Appearance.Options.UseForeColor = True
        Me.ProgressPanel3.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
        Me.ProgressPanel3.Location = New System.Drawing.Point(319, 230)
        Me.ProgressPanel3.Name = "ProgressPanel3"
        Me.ProgressPanel3.Size = New System.Drawing.Size(300, 80)
        Me.ProgressPanel3.TabIndex = 76
        Me.ProgressPanel3.Text = "🔄 Processing Gold... 0%"
        Me.ProgressPanel3.Visible = False
        '
        'lblGoldResults
        '
        Me.lblGoldResults.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblGoldResults.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.lblGoldResults.Location = New System.Drawing.Point(30, 290)
        Me.lblGoldResults.Name = "lblGoldResults"
        Me.lblGoldResults.Size = New System.Drawing.Size(250, 25)
        Me.lblGoldResults.TabIndex = 79
        Me.lblGoldResults.Text = "📋 Gold Encoding Results:"
        '
        'lblGoldTitle
        '
        Me.lblGoldTitle.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblGoldTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.lblGoldTitle.Location = New System.Drawing.Point(30, 70)
        Me.lblGoldTitle.Name = "lblGoldTitle"
        Me.lblGoldTitle.Size = New System.Drawing.Size(200, 25)
        Me.lblGoldTitle.TabIndex = 77
        Me.lblGoldTitle.Text = "📝 Page Title:"
        '
        'txtGoldTitle
        '
        Me.txtGoldTitle.EditValue = "Gold Secure Content"
        Me.txtGoldTitle.Location = New System.Drawing.Point(30, 100)
        Me.txtGoldTitle.Margin = New System.Windows.Forms.Padding(4, 1, 4, 1)
        Me.txtGoldTitle.Name = "txtGoldTitle"
        Me.txtGoldTitle.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtGoldTitle.Properties.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(76, Byte), Integer), CType(CType(84, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.txtGoldTitle.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtGoldTitle.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.txtGoldTitle.Properties.Appearance.Options.UseBackColor = True
        Me.txtGoldTitle.Properties.Appearance.Options.UseBorderColor = True
        Me.txtGoldTitle.Properties.Appearance.Options.UseFont = True
        Me.txtGoldTitle.Properties.Appearance.Options.UseForeColor = True
        Me.txtGoldTitle.Properties.AppearanceFocused.BorderColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.txtGoldTitle.Properties.AppearanceFocused.Options.UseBorderColor = True
        Me.txtGoldTitle.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
        Me.txtGoldTitle.Properties.NullValuePrompt = "📝 Enter page title..."
        Me.txtGoldTitle.Properties.Padding = New System.Windows.Forms.Padding(15, 0, 0, 0)
        Me.txtGoldTitle.Size = New System.Drawing.Size(878, 36)
        Me.txtGoldTitle.TabIndex = 78
        '
        'txtFilepath_Goldn
        '
        Me.txtFilepath_Goldn.EditValue = ""
        Me.txtFilepath_Goldn.Location = New System.Drawing.Point(30, 180)
        Me.txtFilepath_Goldn.Margin = New System.Windows.Forms.Padding(4, 1, 4, 1)
        Me.txtFilepath_Goldn.Name = "txtFilepath_Goldn"
        Me.txtFilepath_Goldn.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtFilepath_Goldn.Properties.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(76, Byte), Integer), CType(CType(84, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.txtFilepath_Goldn.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtFilepath_Goldn.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.txtFilepath_Goldn.Properties.Appearance.Options.UseBackColor = True
        Me.txtFilepath_Goldn.Properties.Appearance.Options.UseBorderColor = True
        Me.txtFilepath_Goldn.Properties.Appearance.Options.UseFont = True
        Me.txtFilepath_Goldn.Properties.Appearance.Options.UseForeColor = True
        Me.txtFilepath_Goldn.Properties.AppearanceFocused.BorderColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.txtFilepath_Goldn.Properties.AppearanceFocused.Options.UseBorderColor = True
        Me.txtFilepath_Goldn.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
        Me.txtFilepath_Goldn.Properties.NullValuePrompt = "📁 Select Gold attachment file..."
        Me.txtFilepath_Goldn.Properties.Padding = New System.Windows.Forms.Padding(15, 0, 0, 0)
        Me.txtFilepath_Goldn.Size = New System.Drawing.Size(878, 36)
        Me.txtFilepath_Goldn.TabIndex = 71
        '
        'RichTextBox_Goldn_Normal
        '
        Me.RichTextBox_Goldn_Normal.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.RichTextBox_Goldn_Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.RichTextBox_Goldn_Normal.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.RichTextBox_Goldn_Normal.Font = New System.Drawing.Font("Consolas", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.RichTextBox_Goldn_Normal.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.RichTextBox_Goldn_Normal.Location = New System.Drawing.Point(30, 320)
        Me.RichTextBox_Goldn_Normal.Margin = New System.Windows.Forms.Padding(30, 10, 30, 30)
        Me.RichTextBox_Goldn_Normal.Name = "RichTextBox_Goldn_Normal"
        Me.RichTextBox_Goldn_Normal.Size = New System.Drawing.Size(878, 190)
        Me.RichTextBox_Goldn_Normal.TabIndex = 69
        Me.RichTextBox_Goldn_Normal.Text = ""
        Me.RichTextBox_Goldn_Normal.WordWrap = False
        '
        'RichTextBox2_Goldn
        '
        Me.RichTextBox2_Goldn.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.RichTextBox2_Goldn.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox2_Goldn.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.RichTextBox2_Goldn.Location = New System.Drawing.Point(39, 205)
        Me.RichTextBox2_Goldn.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.RichTextBox2_Goldn.Name = "RichTextBox2_Goldn"
        Me.RichTextBox2_Goldn.Size = New System.Drawing.Size(870, 107)
        Me.RichTextBox2_Goldn.TabIndex = 70
        Me.RichTextBox2_Goldn.Text = ""
        Me.RichTextBox2_Goldn.Visible = False
        '
        'Label7
        '
        Me.Label7.Font = New System.Drawing.Font("Comfortaa", 10.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.ForeColor = System.Drawing.Color.FromArgb(CType(CType(234, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.Label7.Location = New System.Drawing.Point(30, 150)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(200, 25)
        Me.Label7.TabIndex = 0
        Me.Label7.Text = "📁 Gold File Path:"
        '
        'Label3
        '
        Me.Label3.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 16.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.Color.FromArgb(CType(CType(240, Byte), Integer), CType(CType(185, Byte), Integer), CType(CType(11, Byte), Integer))
        Me.Label3.Location = New System.Drawing.Point(344, 20)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(300, 40)
        Me.Label3.TabIndex = 0
        Me.Label3.Text = "🔐 Gold Attachment"
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(972, 602)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.XtraTabControl1
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(946, 576)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControl9
        '
        Me.LayoutControl9.Controls.Add(Me.LetterEncoder_txtFilepath_Goldn)
        Me.LayoutControl9.Controls.Add(Me.RichTextBox1_Goldn_Normal)
        Me.LayoutControl9.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl9.Location = New System.Drawing.Point(2, 29)
        Me.LayoutControl9.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LayoutControl9.Name = "LayoutControl9"
        Me.LayoutControl9.Root = Me.LayoutControlGroup8
        Me.LayoutControl9.Size = New System.Drawing.Size(902, 199)
        Me.LayoutControl9.TabIndex = 0
        Me.LayoutControl9.Text = "LayoutControl2"
        '
        'LetterEncoder_txtFilepath_Goldn
        '
        Me.LetterEncoder_txtFilepath_Goldn.EditValue = ""
        Me.LetterEncoder_txtFilepath_Goldn.Location = New System.Drawing.Point(156, 16)
        Me.LetterEncoder_txtFilepath_Goldn.Margin = New System.Windows.Forms.Padding(4, 1, 4, 1)
        Me.LetterEncoder_txtFilepath_Goldn.Name = "LetterEncoder_txtFilepath_Goldn"
        Me.LetterEncoder_txtFilepath_Goldn.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.LetterEncoder_txtFilepath_Goldn.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LetterEncoder_txtFilepath_Goldn.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.LetterEncoder_txtFilepath_Goldn.Properties.Appearance.Options.UseBackColor = True
        Me.LetterEncoder_txtFilepath_Goldn.Properties.Appearance.Options.UseFont = True
        Me.LetterEncoder_txtFilepath_Goldn.Properties.Appearance.Options.UseForeColor = True
        Me.LetterEncoder_txtFilepath_Goldn.Properties.NullValuePrompt = "Path...."
        Me.LetterEncoder_txtFilepath_Goldn.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.LetterEncoder_txtFilepath_Goldn.Size = New System.Drawing.Size(730, 30)
        Me.LetterEncoder_txtFilepath_Goldn.StyleController = Me.LayoutControl9
        Me.LetterEncoder_txtFilepath_Goldn.TabIndex = 63
        '
        'RichTextBox1_Goldn_Normal
        '
        Me.RichTextBox1_Goldn_Normal.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.RichTextBox1_Goldn_Normal.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox1_Goldn_Normal.ForeColor = System.Drawing.Color.White
        Me.RichTextBox1_Goldn_Normal.Location = New System.Drawing.Point(16, 52)
        Me.RichTextBox1_Goldn_Normal.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.RichTextBox1_Goldn_Normal.Name = "RichTextBox1_Goldn_Normal"
        Me.RichTextBox1_Goldn_Normal.Size = New System.Drawing.Size(870, 131)
        Me.RichTextBox1_Goldn_Normal.TabIndex = 68
        Me.RichTextBox1_Goldn_Normal.Text = ""
        '
        'LayoutControlGroup8
        '
        Me.LayoutControlGroup8.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup8.GroupBordersVisible = False
        Me.LayoutControlGroup8.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem29, Me.LayoutControlItem11})
        Me.LayoutControlGroup8.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup8.Size = New System.Drawing.Size(902, 199)
        Me.LayoutControlGroup8.TextVisible = False
        '
        'LayoutControlItem29
        '
        Me.LayoutControlItem29.Control = Me.RichTextBox1_Goldn_Normal
        Me.LayoutControlItem29.Location = New System.Drawing.Point(0, 36)
        Me.LayoutControlItem29.Name = "LayoutControlItem7"
        Me.LayoutControlItem29.Size = New System.Drawing.Size(876, 137)
        Me.LayoutControlItem29.TextVisible = False
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.LetterEncoder_txtFilepath_Goldn
        Me.LayoutControlItem11.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(876, 36)
        Me.LayoutControlItem11.Text = "Path File Attachment : "
        Me.LayoutControlItem11.TextSize = New System.Drawing.Size(124, 18)
        '
        'PopupMenu1
        '
        Me.PopupMenu1.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.SelectLetterNormal_Base64), New DevExpress.XtraBars.LinkPersistInfo(Me.Bnt_EN_Base64), New DevExpress.XtraBars.LinkPersistInfo(Me.LetterEncoder_bntClearAll)})
        Me.PopupMenu1.Manager = Me.BarManager1
        Me.PopupMenu1.Name = "PopupMenu1"
        '
        'BarManager2
        '
        Me.BarManager2.DockControls.Add(Me.BarDockControl1)
        Me.BarManager2.DockControls.Add(Me.BarDockControl2)
        Me.BarManager2.DockControls.Add(Me.BarDockControl3)
        Me.BarManager2.DockControls.Add(Me.BarDockControl4)
        Me.BarManager2.Form = Me
        Me.BarManager2.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.BntSelect_Letter_VIP_Normal, Me.Bnt_En_VIP, Me.BntSaveVIP, Me.bntClearPathVIP})
        Me.BarManager2.MaxItemId = 4
        '
        'BarDockControl1
        '
        Me.BarDockControl1.CausesValidation = False
        Me.BarDockControl1.Dock = System.Windows.Forms.DockStyle.Top
        Me.BarDockControl1.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl1.Manager = Me.BarManager2
        Me.BarDockControl1.Size = New System.Drawing.Size(972, 0)
        '
        'BarDockControl2
        '
        Me.BarDockControl2.CausesValidation = False
        Me.BarDockControl2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.BarDockControl2.Location = New System.Drawing.Point(0, 602)
        Me.BarDockControl2.Manager = Me.BarManager2
        Me.BarDockControl2.Size = New System.Drawing.Size(972, 0)
        '
        'BarDockControl3
        '
        Me.BarDockControl3.CausesValidation = False
        Me.BarDockControl3.Dock = System.Windows.Forms.DockStyle.Left
        Me.BarDockControl3.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl3.Manager = Me.BarManager2
        Me.BarDockControl3.Size = New System.Drawing.Size(0, 602)
        '
        'BarDockControl4
        '
        Me.BarDockControl4.CausesValidation = False
        Me.BarDockControl4.Dock = System.Windows.Forms.DockStyle.Right
        Me.BarDockControl4.Location = New System.Drawing.Point(972, 0)
        Me.BarDockControl4.Manager = Me.BarManager2
        Me.BarDockControl4.Size = New System.Drawing.Size(0, 602)
        '
        'BntSelect_Letter_VIP_Normal
        '
        Me.BntSelect_Letter_VIP_Normal.Caption = "Select Attachment"
        Me.BntSelect_Letter_VIP_Normal.Id = 0
        Me.BntSelect_Letter_VIP_Normal.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BntSelect_Letter_VIP_Normal.ImageOptions.LargeImage = CType(resources.GetObject("BntSelect_Letter_VIP_Normal.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BntSelect_Letter_VIP_Normal.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntSelect_Letter_VIP_Normal.ItemAppearance.Normal.Options.UseFont = True
        Me.BntSelect_Letter_VIP_Normal.Name = "BntSelect_Letter_VIP_Normal"
        '
        'Bnt_En_VIP
        '
        Me.Bnt_En_VIP.Caption = "Encoded Attachment"
        Me.Bnt_En_VIP.Id = 1
        Me.Bnt_En_VIP.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedAttachment32x32
        Me.Bnt_En_VIP.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Bnt_En_VIP.ItemAppearance.Normal.Options.UseFont = True
        Me.Bnt_En_VIP.Name = "Bnt_En_VIP"
        Me.Bnt_En_VIP.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'BntSaveVIP
        '
        Me.BntSaveVIP.Caption = "Save Attachment"
        Me.BntSaveVIP.Id = 2
        Me.BntSaveVIP.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.BntSaveVIP.ImageOptions.LargeImage = CType(resources.GetObject("BntSaveVIP.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BntSaveVIP.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntSaveVIP.ItemAppearance.Normal.Options.UseFont = True
        Me.BntSaveVIP.Name = "BntSaveVIP"
        Me.BntSaveVIP.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'bntClearPathVIP
        '
        Me.bntClearPathVIP.Caption = "Clear All"
        Me.bntClearPathVIP.Id = 3
        Me.bntClearPathVIP.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.bntClearPathVIP.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.bntClearPathVIP.ItemAppearance.Normal.Options.UseFont = True
        Me.bntClearPathVIP.Name = "bntClearPathVIP"
        '
        'PopupMenu2
        '
        Me.PopupMenu2.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BntSelect_Letter_VIP_Normal), New DevExpress.XtraBars.LinkPersistInfo(Me.Bnt_En_VIP), New DevExpress.XtraBars.LinkPersistInfo(Me.BntSaveVIP), New DevExpress.XtraBars.LinkPersistInfo(Me.bntClearPathVIP)})
        Me.PopupMenu2.Manager = Me.BarManager2
        Me.PopupMenu2.Name = "PopupMenu2"
        '
        'PopupMenu3
        '
        Me.PopupMenu3.LinksPersistInfo.AddRange(New DevExpress.XtraBars.LinkPersistInfo() {New DevExpress.XtraBars.LinkPersistInfo(Me.BntAttSelect), New DevExpress.XtraBars.LinkPersistInfo(Me.BntEncodedAtt), New DevExpress.XtraBars.LinkPersistInfo(Me.BntRemoveAll)})
        Me.PopupMenu3.Manager = Me.BarManager3
        Me.PopupMenu3.Name = "PopupMenu3"
        '
        'BntAttSelect
        '
        Me.BntAttSelect.Caption = "Select Attachment"
        Me.BntAttSelect.Id = 0
        Me.BntAttSelect.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.BntAttSelect.ImageOptions.LargeImage = CType(resources.GetObject("BntAttSelect.ImageOptions.LargeImage"), System.Drawing.Image)
        Me.BntAttSelect.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntAttSelect.ItemAppearance.Normal.Options.UseFont = True
        Me.BntAttSelect.Name = "BntAttSelect"
        '
        'BntEncodedAtt
        '
        Me.BntEncodedAtt.Caption = "Encoded Attachment"
        Me.BntEncodedAtt.Id = 1
        Me.BntEncodedAtt.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.EncodedAttachment32x32
        Me.BntEncodedAtt.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntEncodedAtt.ItemAppearance.Normal.Options.UseFont = True
        Me.BntEncodedAtt.Name = "BntEncodedAtt"
        Me.BntEncodedAtt.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '
        'BntRemoveAll
        '
        Me.BntRemoveAll.Caption = "Clear All"
        Me.BntRemoveAll.Id = 2
        Me.BntRemoveAll.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntRemoveAll.ItemAppearance.Normal.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.BntRemoveAll.ItemAppearance.Normal.Options.UseFont = True
        Me.BntRemoveAll.Name = "BntRemoveAll"
        '
        'BarManager3
        '
        Me.BarManager3.DockControls.Add(Me.BarDockControl5)
        Me.BarManager3.DockControls.Add(Me.BarDockControl6)
        Me.BarManager3.DockControls.Add(Me.BarDockControl7)
        Me.BarManager3.DockControls.Add(Me.BarDockControl8)
        Me.BarManager3.Form = Me
        Me.BarManager3.Items.AddRange(New DevExpress.XtraBars.BarItem() {Me.BntAttSelect, Me.BntEncodedAtt, Me.BntRemoveAll})
        Me.BarManager3.MaxItemId = 3
        '
        'BarDockControl5
        '
        Me.BarDockControl5.CausesValidation = False
        Me.BarDockControl5.Dock = System.Windows.Forms.DockStyle.Top
        Me.BarDockControl5.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl5.Manager = Me.BarManager3
        Me.BarDockControl5.Size = New System.Drawing.Size(972, 0)
        '
        'BarDockControl6
        '
        Me.BarDockControl6.CausesValidation = False
        Me.BarDockControl6.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.BarDockControl6.Location = New System.Drawing.Point(0, 602)
        Me.BarDockControl6.Manager = Me.BarManager3
        Me.BarDockControl6.Size = New System.Drawing.Size(972, 0)
        '
        'BarDockControl7
        '
        Me.BarDockControl7.CausesValidation = False
        Me.BarDockControl7.Dock = System.Windows.Forms.DockStyle.Left
        Me.BarDockControl7.Location = New System.Drawing.Point(0, 0)
        Me.BarDockControl7.Manager = Me.BarManager3
        Me.BarDockControl7.Size = New System.Drawing.Size(0, 602)
        '
        'BarDockControl8
        '
        Me.BarDockControl8.CausesValidation = False
        Me.BarDockControl8.Dock = System.Windows.Forms.DockStyle.Right
        Me.BarDockControl8.Location = New System.Drawing.Point(972, 0)
        Me.BarDockControl8.Manager = Me.BarManager3
        Me.BarDockControl8.Size = New System.Drawing.Size(0, 602)
        '
        'RichTextBox_Goldn
        '
        Me.RichTextBox_Goldn.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.RichTextBox_Goldn.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox_Goldn.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.RichTextBox_Goldn.Location = New System.Drawing.Point(141, 16)
        Me.RichTextBox_Goldn.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.RichTextBox_Goldn.Name = "RichTextBox_Goldn"
        Me.RichTextBox_Goldn.Size = New System.Drawing.Size(745, 195)
        Me.RichTextBox_Goldn.TabIndex = 68
        Me.RichTextBox_Goldn.Text = ""
        '
        'LayoutControlItem16
        '
        Me.LayoutControlItem16.Control = Me.RichTextBox_Goldn
        Me.LayoutControlItem16.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem16.Name = "LayoutControlItem7"
        Me.LayoutControlItem16.Size = New System.Drawing.Size(876, 201)
        Me.LayoutControlItem16.TextSize = New System.Drawing.Size(109, 18)
        '
        'LayoutControlGroup7
        '
        Me.LayoutControlGroup7.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup7.GroupBordersVisible = False
        Me.LayoutControlGroup7.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem16})
        Me.LayoutControlGroup7.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup7.Size = New System.Drawing.Size(902, 227)
        Me.LayoutControlGroup7.TextVisible = False
        '
        'LayoutControl8
        '
        Me.LayoutControl8.Controls.Add(Me.RichTextBox_Goldn)
        Me.LayoutControl8.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl8.Location = New System.Drawing.Point(2, 29)
        Me.LayoutControl8.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.LayoutControl8.Name = "LayoutControl8"
        Me.LayoutControl8.Root = Me.LayoutControlGroup7
        Me.LayoutControl8.Size = New System.Drawing.Size(902, 227)
        Me.LayoutControl8.TabIndex = 0
        Me.LayoutControl8.Text = "LayoutControl8"
        '
        'frmAttPro
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.LayoutControl1)
        Me.Controls.Add(Me.barDockControlLeft)
        Me.Controls.Add(Me.barDockControlRight)
        Me.Controls.Add(Me.barDockControlBottom)
        Me.Controls.Add(Me.barDockControlTop)
        Me.Controls.Add(Me.BarDockControl3)
        Me.Controls.Add(Me.BarDockControl4)
        Me.Controls.Add(Me.BarDockControl2)
        Me.Controls.Add(Me.BarDockControl1)
        Me.Controls.Add(Me.BarDockControl7)
        Me.Controls.Add(Me.BarDockControl8)
        Me.Controls.Add(Me.BarDockControl6)
        Me.Controls.Add(Me.BarDockControl5)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.LogoBSV
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmAttPro"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Encoded Attachment Pro"
        CType(Me.BehaviorManager1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.XtraTabPage1.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        CType(Me.cmbEncryption.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PathLetterNormalBase64.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage2.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        Me.Panel2.PerformLayout()
        CType(Me.PathSaveFile_Encoded.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PathFile_Att_VIP.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BarManager1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PNLAttachWait.ResumeLayout(False)
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage3.ResumeLayout(False)
        Me.Panel3.ResumeLayout(False)
        CType(Me.txtGoldTitle.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtFilepath_Goldn.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControl9, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl9.ResumeLayout(False)
        CType(Me.LetterEncoder_txtFilepath_Goldn.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem29, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PopupMenu1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BarManager2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PopupMenu2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PopupMenu3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BarManager3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControl8, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl8.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Timer1 As Timer
    Friend WithEvents BehaviorManager1 As DevExpress.Utils.Behaviors.BehaviorManager
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents XtraTabPage2 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents XtraTabPage3 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents BarManager1 As DevExpress.XtraBars.BarManager
    Friend WithEvents barDockControlTop As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlBottom As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlLeft As DevExpress.XtraBars.BarDockControl
    Friend WithEvents barDockControlRight As DevExpress.XtraBars.BarDockControl
    Friend WithEvents PopupMenu1 As DevExpress.XtraBars.PopupMenu
    Friend WithEvents SelectLetterNormal_Base64 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Bnt_EN_Base64 As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents LetterEncoder_bntClearAll As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents XtraTabPage1 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents BarDockControl3 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarManager2 As DevExpress.XtraBars.BarManager
    Friend WithEvents BarDockControl1 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl2 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl4 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BntSelect_Letter_VIP_Normal As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Bnt_En_VIP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntSaveVIP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents bntClearPathVIP As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BarDockControl7 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarManager3 As DevExpress.XtraBars.BarManager
    Friend WithEvents BarDockControl5 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl6 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents BarDockControl8 As DevExpress.XtraBars.BarDockControl
    Friend WithEvents PopupMenu2 As DevExpress.XtraBars.PopupMenu
    Friend WithEvents PopupMenu3 As DevExpress.XtraBars.PopupMenu
    Friend WithEvents BntAttSelect As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntEncodedAtt As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents BntRemoveAll As DevExpress.XtraBars.BarButtonItem
    Friend WithEvents Panel2 As Panel
    Friend WithEvents PNLAttachWait As Panel
    Friend WithEvents ProgressBarControl1 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents lblSaveLocation As Label
    Friend WithEvents lblVIPPathFile As Label
    Friend WithEvents Panel3 As Panel
    Friend WithEvents RichTextBox_Goldn As RichTextBox
    Friend WithEvents LetterEncoder_txtFilepath_Goldn As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControl9 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents RichTextBox1_Goldn_Normal As RichTextBox
    Friend WithEvents LayoutControlGroup8 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem29 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem16 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlGroup7 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControl8 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents Panel1 As Panel
    Friend WithEvents Richtext_En_base64 As RichTextBox
    Friend WithEvents Richtext_NormalBase64 As RichTextBox
    Friend WithEvents PathLetterNormalBase64 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label4 As Label
    Friend WithEvents PathSaveFile_Encoded As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PathFile_Att_VIP As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LogInNormalTextBox3 As TextBox
    Friend WithEvents txttitle As TextBox
    Friend WithEvents LogInCheckBox1 As CheckBox
    Friend WithEvents Label5 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents cmbEncryption As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents txtFilepath_Goldn As DevExpress.XtraEditors.TextEdit
    Friend WithEvents RichTextBox_Goldn_Normal As RichTextBox
    Friend WithEvents RichTextBox2_Goldn As RichTextBox
    Friend WithEvents Label7 As Label
    Friend WithEvents ProgressPanel2 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents lblResults As Label
    Friend WithEvents lblPath As Label
    Friend WithEvents ProgressPanel3 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents lblGoldTitle As Label
    Friend WithEvents txtGoldTitle As DevExpress.XtraEditors.TextEdit
    Friend WithEvents lblGoldResults As Label
    Friend WithEvents PictureBox1 As PictureBox
End Class
