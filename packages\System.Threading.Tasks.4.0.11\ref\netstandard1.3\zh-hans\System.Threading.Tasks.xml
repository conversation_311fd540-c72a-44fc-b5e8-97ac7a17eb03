﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks</name>
  </assembly>
  <members>
    <member name="T:System.AggregateException">
      <summary>表示在应用程序执行期间发生的一个或多个错误。</summary>
    </member>
    <member name="M:System.AggregateException.#ctor">
      <summary>使用由系统提供的用来描述错误的消息初始化 <see cref="T:System.AggregateException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>用对作为此异常原因的内部异常的引用初始化 <see cref="T:System.AggregateException" /> 类的新实例。</summary>
      <param name="innerExceptions">导致当前异常的异常。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> 的元素为 Null。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Exception[])">
      <summary>用对作为此异常原因的内部异常的引用初始化 <see cref="T:System.AggregateException" /> 类的新实例。</summary>
      <param name="innerExceptions">导致当前异常的异常。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> 的元素为 Null。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String)">
      <summary>使用指定的描述错误的消息初始化 <see cref="T:System.AggregateException" /> 类的新实例。</summary>
      <param name="message">描述该异常的消息。此构造函数的调用方需要确保此字符串已针对当前系统区域性进行了本地化。</param>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>使用指定错误信息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.AggregateException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="innerExceptions">导致当前异常的异常。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> 的元素为 Null。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception)">
      <summary>使用指定错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.AggregateException" /> 类的新实例。</summary>
      <param name="message">描述该异常的消息。此构造函数的调用方需要确保此字符串已针对当前系统区域性进行了本地化。</param>
      <param name="innerException">导致当前异常的异常。如果 <paramref name="innerException" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerException" /> 参数为 null。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception[])">
      <summary>使用指定错误信息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.AggregateException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="innerExceptions">导致当前异常的异常。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> 的元素为 Null。</exception>
    </member>
    <member name="M:System.AggregateException.Flatten">
      <summary>将 <see cref="T:System.AggregateException" /> 实例平展入单个新实例。</summary>
      <returns>一个新的平展 <see cref="T:System.AggregateException" />。</returns>
    </member>
    <member name="M:System.AggregateException.GetBaseException">
      <summary>返回 <see cref="T:System.AggregateException" />，它是此异常的根本原因。</summary>
      <returns>返回 <see cref="T:System.AggregateException" />，它是此异常的根本原因。</returns>
    </member>
    <member name="M:System.AggregateException.Handle(System.Func{System.Exception,System.Boolean})">
      <summary>对此 <see cref="T:System.AggregateException" /> 所包含的每个 <see cref="T:System.Exception" /> 调用处理程序。</summary>
      <param name="predicate">要对每个异常执行的谓词。该谓词接受作为参数来处理 <see cref="T:System.Exception" />，并返回指示异常是否已处理的布尔值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="predicate" /> 参数为 null。</exception>
      <exception cref="T:System.AggregateException">未处理此 <see cref="T:System.AggregateException" /> 包含的异常。</exception>
    </member>
    <member name="P:System.AggregateException.InnerExceptions">
      <summary>获取导致当前异常的 <see cref="T:System.Exception" /> 实例的只读集合。</summary>
      <returns>返回导致当前异常的 <see cref="T:System.Exception" /> 实例的只读集合。</returns>
    </member>
    <member name="M:System.AggregateException.ToString">
      <summary>创建并返回当前 <see cref="T:System.AggregateException" /> 的字符串表示形式。</summary>
      <returns>当前异常的字符串表示形式。</returns>
    </member>
    <member name="T:System.OperationCanceledException">
      <summary>取消线程正在执行的操作时在线程中引发的异常。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.OperationCanceledException.#ctor">
      <summary>使用系统提供的错误信息初始化 <see cref="T:System.OperationCanceledException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String)">
      <summary>使用指定的错误信息初始化 <see cref="T:System.OperationCanceledException" /> 类的新实例。</summary>
      <param name="message">描述该错误的 <see cref="T:System.String" />。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception)">
      <summary>使用指定错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.OperationCanceledException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="innerException">导致当前异常的异常。如果 <paramref name="innerException" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception,System.Threading.CancellationToken)">
      <summary>用指定的错误消息、对作为此异常原因的内部异常的引用以及取消令牌初始化 <see cref="T:System.OperationCanceledException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="innerException">导致当前异常的异常。如果 <paramref name="innerException" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
      <param name="token">一个与已取消的操作关联的取消标记。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Threading.CancellationToken)">
      <summary>使用指定的错误信息和取消令牌初始化 <see cref="T:System.OperationCanceledException" /> 类的新实例。</summary>
      <param name="message">解释异常原因的错误信息。</param>
      <param name="token">一个与已取消的操作关联的取消标记。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.Threading.CancellationToken)">
      <summary>用取消令牌初始化 <see cref="T:System.OperationCanceledException" /> 类的新实例。</summary>
      <param name="token">一个与已取消的操作关联的取消标记。</param>
    </member>
    <member name="P:System.OperationCanceledException.CancellationToken">
      <summary>获取与已取消的操作关联的令牌。</summary>
      <returns>与已取消的操作关联的令牌，或默认令牌。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder">
      <summary>表示生成器，用于返回任务的异步方法。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>指定的 awaiter 完成时，安排状态机，以继续下一操作。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">状态机。</param>
      <typeparam name="TAwaiter">Awaiter 的类型。</typeparam>
      <typeparam name="TStateMachine">状态机的类型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>指定的 awaiter 完成时，安排状态机，以继续下一操作。此方法可从部分受信任的代码调用。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">状态机。</param>
      <typeparam name="TAwaiter">Awaiter 的类型。</typeparam>
      <typeparam name="TStateMachine">状态机的类型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Create">
      <summary>创建 <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder" /> 类的实例。</summary>
      <returns>生成器的新实例。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetException(System.Exception)">
      <summary>标记此任务为失败并绑定指定的异常至此任务。</summary>
      <param name="exception">要绑定到任务的异常。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">任务已完成。- 或 -该生成程序未初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult">
      <summary>将任务标记为已成功完成。</summary>
      <exception cref="T:System.InvalidOperationException">任务已完成。- 或 -该生成程序未初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>一个生成器与指定的状态机关联。</summary>
      <param name="stateMachine">要与生成器关联的状态机实例。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">预设置状态机。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Start``1(``0@)">
      <summary>开始运行有关联状态机的生成器。</summary>
      <param name="stateMachine">由引用传递的状态器实例。</param>
      <typeparam name="TStateMachine">状态机的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 为 null。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Task">
      <summary>获取此生成器的任务。</summary>
      <returns>此生成器的任务。</returns>
      <exception cref="T:System.InvalidOperationException">该生成程序未初始化。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1">
      <summary>表示异步方法的生成器，该生成器将返回任务并提供结果的参数。</summary>
      <typeparam name="TResult">用来完成任务的结果。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitOnCompleted``2(``0@,``1@)">
      <summary>指定的 awaiter 完成时，安排状态机，以继续下一操作。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">状态机。</param>
      <typeparam name="TAwaiter">Awaiter 的类型。</typeparam>
      <typeparam name="TStateMachine">状态机的类型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>指定的 awaiter 完成时，安排状态机，以继续下一操作。此方法可从部分受信任的代码调用。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">状态机。</param>
      <typeparam name="TAwaiter">Awaiter 的类型。</typeparam>
      <typeparam name="TStateMachine">状态机的类型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Create">
      <summary>创建 <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1" /> 类的实例。</summary>
      <returns>生成器的新实例。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetException(System.Exception)">
      <summary>标记此任务为失败并绑定指定的异常至此任务。</summary>
      <param name="exception">要绑定到任务的异常。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">任务已完成。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetResult(`0)">
      <summary>将任务标记为已成功完成。</summary>
      <param name="result">用来完成任务的结果。</param>
      <exception cref="T:System.InvalidOperationException">任务已完成。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>一个生成器与指定的状态机关联。</summary>
      <param name="stateMachine">要与生成器关联的状态机实例。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">预设置状态机。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Start``1(``0@)">
      <summary>开始运行有关联状态机的生成器。</summary>
      <param name="stateMachine">由引用传递的状态器实例。</param>
      <typeparam name="TStateMachine">状态机的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 为 null。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Task">
      <summary>获取此生成器的任务。</summary>
      <returns>此生成器的任务。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder">
      <summary>表示生成器，用于不返回值的异步方法。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>指定的 awaiter 完成时，安排状态机，以继续下一操作。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">状态机。</param>
      <typeparam name="TAwaiter">Awaiter 的类型。</typeparam>
      <typeparam name="TStateMachine">状态机的类型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>指定的 awaiter 完成时，安排状态机，以继续下一操作。此方法可从部分受信任的代码调用。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">状态机。</param>
      <typeparam name="TAwaiter">Awaiter 的类型。</typeparam>
      <typeparam name="TStateMachine">状态机的类型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Create">
      <summary>创建 <see cref="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder" /> 类的实例。</summary>
      <returns>生成器的新实例。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetException(System.Exception)">
      <summary>将一个异常绑定到该方法生成器。</summary>
      <param name="exception">要绑定的异常。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">该生成程序未初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetResult">
      <summary>标记此方法生成器为成功完成。</summary>
      <exception cref="T:System.InvalidOperationException">该生成程序未初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>一个生成器与指定的状态机关联。</summary>
      <param name="stateMachine">要与生成器关联的状态机实例。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">预设置状态机。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start``1(``0@)">
      <summary>开始运行有关联状态机的生成器。</summary>
      <param name="stateMachine">由引用传递的状态器实例。</param>
      <typeparam name="TStateMachine">状态机的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 为 null。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable">
      <summary>提供用于启用对任务的已配置等待的可等待对象。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.GetAwaiter">
      <summary>返回此可等待对象的 Awaiter。</summary>
      <returns>awaiter。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1">
      <summary>提供用于启用对任务的已配置等待的可等待对象。</summary>
      <typeparam name="TResult">此 <see cref="T:System.Threading.Tasks.Task`1" /> 生成的结果的类型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.GetAwaiter">
      <summary>返回此可等待对象的 Awaiter。</summary>
      <returns>awaiter。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter">
      <summary>提供可等待对象 (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1" />) 的 Awaiter。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.GetResult">
      <summary>结束对已完成任务的等待。</summary>
      <returns>已完成任务的结果。</returns>
      <exception cref="T:System.NullReferenceException">等待未正确地初始化。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">已取消的任务。</exception>
      <exception cref="T:System.Exception">该任务在出错状态下完成。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.IsCompleted">
      <summary>获取一个值，该值指定等待中的任务是否已完成。</summary>
      <returns>如果已成功完成等待任务，则为 true；否则为 false。</returns>
      <exception cref="T:System.NullReferenceException">等待未正确地初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>为与此 awaiter 关联的任务计划延续操作。</summary>
      <param name="continuation">在等待操作完成时要调用的操作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 参数为 null。</exception>
      <exception cref="T:System.NullReferenceException">等待未正确地初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>为与此 awaiter 关联的任务计划延续操作。</summary>
      <param name="continuation">在等待操作完成时要调用的操作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 参数为 null。</exception>
      <exception cref="T:System.NullReferenceException">等待未正确地初始化。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter">
      <summary>提供可等待 (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable" />) 对象的 Awaiter。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.GetResult">
      <summary>结束对已完成任务的等待。</summary>
      <exception cref="T:System.NullReferenceException">等待未正确地初始化。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">已取消的任务。</exception>
      <exception cref="T:System.Exception">该任务在出错状态下完成。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.IsCompleted">
      <summary>获取指定长期任务是否复杂的值。</summary>
      <returns>如果已成功完成等待任务，则为 true；否则为 false。</returns>
      <exception cref="T:System.NullReferenceException">等待未正确地初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>为与此 awaiter 关联的任务计划延续操作。</summary>
      <param name="continuation">在等待操作完成时要调用的操作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 参数为 null。</exception>
      <exception cref="T:System.NullReferenceException">等待未正确地初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>为与此 awaiter 关联的任务计划延续操作。</summary>
      <param name="continuation">在等待操作完成时要调用的操作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 参数为 null。</exception>
      <exception cref="T:System.NullReferenceException">等待未正确地初始化。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.IAsyncStateMachine">
      <summary>表示为异步方法生成的状态机。此类别仅供编译器使用。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.MoveNext">
      <summary>移动此状态机至其下一个状态。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>使用堆分配的副本配置该状态机。</summary>
      <param name="stateMachine">堆分配的副本。</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.ICriticalNotifyCompletion">
      <summary>表示等候程序，其计划等待操作完成时的后续部分。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ICriticalNotifyCompletion.UnsafeOnCompleted(System.Action)">
      <summary>计划实例完成时调用的延续操作。</summary>
      <param name="continuation">要在操作完成时调用的操作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 参数是 null（在 Visual Basic 中为 Nothing）。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.INotifyCompletion">
      <summary>表示操作，其计划等待操作完成时的后续部分。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.INotifyCompletion.OnCompleted(System.Action)">
      <summary>计划实例完成时调用的延续操作。</summary>
      <param name="continuation">要在操作完成时调用的操作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 参数是 null（在 Visual Basic 中为 Nothing）。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter">
      <summary>提供对象，其等待异步任务的完成。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.GetResult">
      <summary>异步任务完成后关闭等待任务。</summary>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> 对象未正确地初始化。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">已取消的任务。</exception>
      <exception cref="T:System.Exception">在 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状态中完成的任务。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter.IsCompleted">
      <summary>获取一个值，该值指示异步任务是否已完成。</summary>
      <returns>如果该任务已完成，则为 true；否则为 false。</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> 对象未正确地初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.OnCompleted(System.Action)">
      <summary>将操作设置为当 <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> 对象停止等待异步任务完成时执行。</summary>
      <param name="continuation">在等待操作完成时要执行的操作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 为 null。</exception>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> 对象未正确地初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>计划与此 awaiter 相关异步任务的延续操作。</summary>
      <param name="continuation">在等待操作完成时要调用的操作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">等待未正确地初始化。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter`1">
      <summary>表示等待完成的异步任务的对象，并提供结果的参数。</summary>
      <typeparam name="TResult">任务的结果。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.GetResult">
      <summary>异步任务完成后关闭等待任务。</summary>
      <returns>已完成任务的结果。</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> 对象未正确地初始化。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">已取消的任务。</exception>
      <exception cref="T:System.Exception">在 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状态中完成的任务。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter`1.IsCompleted">
      <summary>获取一个值，该值指示异步任务是否已完成。</summary>
      <returns>如果该任务已完成，则为 true；否则为 false。</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> 对象未正确地初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.OnCompleted(System.Action)">
      <summary>将操作设置为当 <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> 对象停止等待异步任务完成时执行。</summary>
      <param name="continuation">在等待操作完成时要执行的操作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 为 null。</exception>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> 对象未正确地初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.UnsafeOnCompleted(System.Action)">
      <summary>计划与此 awaiter 相关异步任务的延续操作。</summary>
      <param name="continuation">在等待操作完成时要调用的操作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">等待未正确地初始化。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable">
      <summary>提供上下文，用于在异步切换到目标环境时等待。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.GetAwaiter">
      <summary>为此类的实例检索 <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> 对象。</summary>
      <returns>用于监视异步操作是否完成的对象。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter">
      <summary>提供等待器，用于切换至目标环境。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.GetResult">
      <summary>结束等待操作。</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.IsCompleted">
      <summary>获取一个值，该值指示是否需要一个 yield。</summary>
      <returns>始终 false，指示 yield 始终是 <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> 所必需的。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.OnCompleted(System.Action)">
      <summary>设置延续以调用。</summary>
      <param name="continuation">要异步调用的调用。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 为 null。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>发送 <paramref name="continuation" /> 回到当前上下文。</summary>
      <param name="continuation">要异步调用的调用。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 参数为 null。</exception>
    </member>
    <member name="T:System.Threading.CancellationToken">
      <summary>传播有关应取消操作的通知。</summary>
    </member>
    <member name="M:System.Threading.CancellationToken.#ctor(System.Boolean)">
      <summary>初始化 <see cref="T:System.Threading.CancellationToken" />。</summary>
      <param name="canceled">标记的已取消状态。</param>
    </member>
    <member name="P:System.Threading.CancellationToken.CanBeCanceled">
      <summary>获取此标记是否能处于已取消状态。</summary>
      <returns>如果此标记能处于已取消状态，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Object)">
      <summary>确定当前的 <see cref="T:System.Threading.CancellationToken" /> 实例是否等于指定的 <see cref="T:System.Object" />。</summary>
      <returns>如果 <paramref name="other" /> 为 <see cref="T:System.Threading.CancellationToken" /> 并且两个实例相等，则为 true；否则为 false。如果两个标记与同一 <see cref="T:System.Threading.CancellationTokenSource" /> 关联，或者它们均是根据公共 CancellationToken 构造函数构造并且其 <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> 值相等，则两个标记相等。</returns>
      <param name="other">要与此实例进行比较的其他对象。</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Threading.CancellationToken)">
      <summary>确定当前的 <see cref="T:System.Threading.CancellationToken" /> 实例是否等于指定的标记。</summary>
      <returns>如果两个实例相等，则为 true；否则为 false。如果两个标记与同一 <see cref="T:System.Threading.CancellationTokenSource" /> 关联，或者它们均是根据公共 CancellationToken 构造函数构造并且其 <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> 值相等，则两个标记相等。</returns>
      <param name="other">要与此实例进行比较的另一个 <see cref="T:System.Threading.CancellationToken" />。</param>
    </member>
    <member name="M:System.Threading.CancellationToken.GetHashCode">
      <summary>作为 <see cref="T:System.Threading.CancellationToken" /> 的哈希函数。</summary>
      <returns>当前 <see cref="T:System.Threading.CancellationToken" /> 实例的哈希代码。</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.IsCancellationRequested">
      <summary>获取是否已请求取消此标记。</summary>
      <returns>如果已请求取消此标记，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.None">
      <summary>返回一个空 <see cref="T:System.Threading.CancellationToken" /> 值。</summary>
      <returns>一个空取消标记。</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Equality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>确定两个 <see cref="T:System.Threading.CancellationToken" /> 实例是否相等。</summary>
      <returns>如果两个实例相等，则为 true；否则为 false。</returns>
      <param name="left">第一个实例。</param>
      <param name="right">第二个实例。</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Inequality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>确定两个 <see cref="T:System.Threading.CancellationToken" /> 实例是否不相等。</summary>
      <returns>如果实例不相等，则为 true；否则为 false。</returns>
      <param name="left">第一个实例。</param>
      <param name="right">第二个实例。</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action)">
      <summary>注册一个将在取消此 <see cref="T:System.Threading.CancellationToken" /> 时调用的委托。</summary>
      <returns>可用于取消注册回调的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 实例。</returns>
      <param name="callback">要在取消 <see cref="T:System.Threading.CancellationToken" /> 时执行的委托。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action,System.Boolean)">
      <summary>注册一个将在取消此 <see cref="T:System.Threading.CancellationToken" /> 时调用的委托。</summary>
      <returns>可用于取消注册回调的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 实例。</returns>
      <param name="callback">要在取消 <see cref="T:System.Threading.CancellationToken" /> 时执行的委托。</param>
      <param name="useSynchronizationContext">一个布尔值，该值指示是否捕获当前 <see cref="T:System.Threading.SynchronizationContext" /> 并在调用 <paramref name="callback" /> 时使用它。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object)">
      <summary>注册一个将在取消此 <see cref="T:System.Threading.CancellationToken" /> 时调用的委托。</summary>
      <returns>可用于取消注册回调的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 实例。</returns>
      <param name="callback">要在取消 <see cref="T:System.Threading.CancellationToken" /> 时执行的委托。</param>
      <param name="state">要在调用委托时传递给 <paramref name="callback" /> 的状态。这可能为 null。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object,System.Boolean)">
      <summary>注册一个将在取消此 <see cref="T:System.Threading.CancellationToken" /> 时调用的委托。</summary>
      <returns>可用于取消注册回调的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 实例。</returns>
      <param name="callback">要在取消 <see cref="T:System.Threading.CancellationToken" /> 时执行的委托。</param>
      <param name="state">要在调用委托时传递给 <paramref name="callback" /> 的状态。这可能为 null。</param>
      <param name="useSynchronizationContext">一个布尔值，该值指示是否捕获当前 <see cref="T:System.Threading.SynchronizationContext" /> 并在调用 <paramref name="callback" /> 时使用它。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.ThrowIfCancellationRequested">
      <summary>如果已请求取消此标记，则引发 <see cref="T:System.OperationCanceledException" />。</summary>
      <exception cref="T:System.OperationCanceledException">The token has had cancellation requested.</exception>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.CancellationToken.WaitHandle">
      <summary>获取在取消标记时收到信号的 <see cref="T:System.Threading.WaitHandle" />。</summary>
      <returns>在取消标记时收到信号的 <see cref="T:System.Threading.WaitHandle" />。</returns>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.CancellationTokenRegistration">
      <summary>表示已向 <see cref="T:System.Threading.CancellationToken" /> 注册的回调委托。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Dispose">
      <summary>释放由 <see cref="T:System.Threading.CancellationTokenRegistration" /> 类的当前实例占用的所有资源。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Object)">
      <summary>确定当前的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 实例是否等于指定的 <see cref="T:System.Threading.CancellationTokenRegistration" />。</summary>
      <returns>如果此实例和 <paramref name="obj" /> 相等，则为 true。否则为 false。如果两个 <see cref="T:System.Threading.CancellationTokenRegistration" /> 实例均引用对相同 <see cref="T:System.Threading.CancellationToken" /> Register 方法的单一调用的输出，则这两个实例相等。</returns>
      <param name="obj">要与此实例进行比较的其他对象。</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Threading.CancellationTokenRegistration)">
      <summary>确定当前的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 实例是否等于指定的 <see cref="T:System.Threading.CancellationTokenRegistration" />。</summary>
      <returns>如果此实例和 <paramref name="other" /> 相等，则为 true。否则为 false。 如果两个 <see cref="T:System.Threading.CancellationTokenRegistration" /> 实例均引用对相同 <see cref="T:System.Threading.CancellationToken" /> Register 方法的单一调用的输出，则这两个实例相等。</returns>
      <param name="other">要与此实例进行比较的其他 <see cref="T:System.Threading.CancellationTokenRegistration" />。</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.GetHashCode">
      <summary>作为 <see cref="T:System.Threading.CancellationTokenRegistration" /> 的哈希函数。</summary>
      <returns>当前 <see cref="T:System.Threading.CancellationTokenRegistration" /> 实例的哈希代码。</returns>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Equality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>确定两个 <see cref="T:System.Threading.CancellationTokenRegistration" /> 实例是否相等。</summary>
      <returns>如果两个实例相等，则为 true；否则为 false。</returns>
      <param name="left">第一个实例。</param>
      <param name="right">第二个实例。</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Inequality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>确定两个 <see cref="T:System.Threading.CancellationTokenRegistration" /> 实例是否不相等。</summary>
      <returns>如果两个实例不相等，则为 true；否则为 false。</returns>
      <param name="left">第一个实例。</param>
      <param name="right">第二个实例。</param>
    </member>
    <member name="T:System.Threading.CancellationTokenSource">
      <summary>向应该被取消的 <see cref="T:System.Threading.CancellationToken" /> 发送信号。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor">
      <summary>初始化 <see cref="T:System.Threading.CancellationTokenSource" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Threading.CancellationTokenSource" /> 类的新实例，在指定的延迟（以毫秒为单位）后将被取消。</summary>
      <param name="millisecondsDelay">取消此 <see cref="T:System.Threading.CancellationTokenSource" /> 前等待的时间间隔（以毫秒为单位）。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsDelay" /> is less than -1. </exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.TimeSpan)">
      <summary>初始化 <see cref="T:System.Threading.CancellationTokenSource" /> 类的新实例，在指定的时间跨度后将被取消。</summary>
      <param name="delay">取消此 <see cref="T:System.Threading.CancellationTokenSource" /> 前等待的时间间隔。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" />.<see cref="P:System.TimeSpan.TotalMilliseconds" /> is less than -1 or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel">
      <summary>传达取消请求。</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel(System.Boolean)">
      <summary>传达对取消的请求，并指定是否应处理其余回调和可取消操作。</summary>
      <param name="throwOnFirstException">如果可以立即传播异常，则为 true；否则为 false。</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.Int32)">
      <summary>在指定的毫秒数后计划对此 <see cref="T:System.Threading.CancellationTokenSource" /> 的取消操作。</summary>
      <param name="millisecondsDelay">取消此 <see cref="T:System.Threading.CancellationTokenSource" /> 前等待的时间范围。</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception thrown when <paramref name="millisecondsDelay" /> is less than -1.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.TimeSpan)">
      <summary>在指定的时间跨度后计划对此 <see cref="T:System.Threading.CancellationTokenSource" /> 的取消操作。</summary>
      <param name="delay">取消此 <see cref="T:System.Threading.CancellationTokenSource" /> 前等待的时间范围。</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when <paramref name="delay" /> is less than -1 or greater than Int32.MaxValue.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>创建一个将在任何源标记处于取消状态时处于取消状态的 <see cref="T:System.Threading.CancellationTokenSource" />。</summary>
      <returns>一个链接到源标记的 <see cref="T:System.Threading.CancellationTokenSource" />。</returns>
      <param name="token1">要观察的第一个取消标记。</param>
      <param name="token2">要观察的第二个取消标记。</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken[])">
      <summary>创建一个将在在指定的数组中任何源标记处于取消状态时处于取消状态的 <see cref="T:System.Threading.CancellationTokenSource" />。</summary>
      <returns>一个链接到源标记的 <see cref="T:System.Threading.CancellationTokenSource" />。</returns>
      <param name="tokens">包含要观察的取消标记实例的数组。</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tokens" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tokens" /> is empty.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose">
      <summary>释放 <see cref="T:System.Threading.CancellationTokenSource" /> 类的当前实例所使用的所有资源。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose(System.Boolean)">
      <summary>释放 <see cref="T:System.Threading.CancellationTokenSource" /> 类使用的非托管资源，并可以选择释放托管资源。</summary>
      <param name="disposing">若要释放托管资源和非托管资源，则为 true；若仅释放非托管资源，则为 false。</param>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.IsCancellationRequested">
      <summary>获取是否已请求取消此 <see cref="T:System.Threading.CancellationTokenSource" />。</summary>
      <returns>如果已请求取消此 <see cref="T:System.Threading.CancellationTokenSource" />，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.Token">
      <summary>获取与此 <see cref="T:System.Threading.CancellationToken" /> 关联的 <see cref="T:System.Threading.CancellationTokenSource" />。</summary>
      <returns>与此 <see cref="T:System.Threading.CancellationToken" /> 关联的 <see cref="T:System.Threading.CancellationTokenSource" />。</returns>
      <exception cref="T:System.ObjectDisposedException">The token source has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair">
      <summary>提供任务计划程序，其用于执行任务，同时确保并发任务可同时运行，而独占任务从不运行。</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor">
      <summary>初始化 <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>用指定的计划程序目标初始化 <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> 类的新实例。</summary>
      <param name="taskScheduler">应执行的目标计划程序。</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32)">
      <summary>初始化针对最大并发级别的指定计划程序 <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> 类的新实例。</summary>
      <param name="taskScheduler">应执行的目标计划程序。</param>
      <param name="maxConcurrencyLevel">并发运行的最大任务数。</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32,System.Int32)">
      <summary>初始化 <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" />  类的新实例，该实例针对最大并发级别和最大计划任务数的指定计划程序，并可能作为一个单元来处理。</summary>
      <param name="taskScheduler">应执行的目标计划程序。</param>
      <param name="maxConcurrencyLevel">并发运行的最大任务数。</param>
      <param name="maxItemsPerTask">要处理的被成对用于每一个基础计划任务的最大任务数。</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Complete">
      <summary>通知不能接受更多任务的计划程序对。</summary>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Completion">
      <summary>当调度程序已经完成处理过程时，获取将要完成的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>计划程序完成处理时将完成的异步操作。</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ConcurrentScheduler">
      <summary>获取可用于调度任务到此添加线对的 <see cref="T:System.Threading.Tasks.TaskScheduler" />，其中的线对可以与此线对上的其他任务同时运行。</summary>
      <returns>可用于当前安排任务的对象。</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ExclusiveScheduler">
      <summary>获取可用于调度任务到此添加线对的 <see cref="T:System.Threading.Tasks.TaskScheduler" />，其中的线对必须独占地运行，不考虑此线对上的其他任务。</summary>
      <returns>可用于安排任务没有与其他任务并发运行的对象。</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task">
      <summary>表示一个异步操作。若要浏览此类型的 .NET Framework 源代码，请参阅引用源。</summary>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action)">
      <summary>使用指定的操作初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">表示要在任务中执行的代码的委托。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken)">
      <summary>使用指定的操作和 <see cref="T:System.Threading.Tasks.Task" /> 初始化新的 <see cref="T:System.Threading.CancellationToken" />。</summary>
      <param name="action">表示要在任务中执行的代码的委托。</param>
      <param name="cancellationToken">新任务将观察的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的操作和创建选项初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">表示要在任务中执行的代码的委托。</param>
      <param name="cancellationToken">新任务将观察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">用于自定义任务的行为的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的操作和创建选项初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">表示要在任务中执行的代码的委托。</param>
      <param name="creationOptions">用于自定义任务的行为的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object)">
      <summary>使用指定的操作和状态初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">表示要在任务中执行的代码的委托。</param>
      <param name="state">一个表示由该操作使用的数据的对象。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>使用指定的操作、状态和选项初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">表示要在任务中执行的代码的委托。</param>
      <param name="state">一个表示由该操作使用的数据的对象。</param>
      <param name="cancellationToken">新任务将观察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的操作、状态和选项初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">表示要在任务中执行的代码的委托。</param>
      <param name="state">一个表示由该操作使用的数据的对象。</param>
      <param name="cancellationToken">新任务将观察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">用于自定义任务的行为的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的操作、状态和选项初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">表示要在任务中执行的代码的委托。</param>
      <param name="state">一个表示由该操作使用的数据的对象。</param>
      <param name="creationOptions">用于自定义任务的行为的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.AsyncState">
      <summary>获取在创建 <see cref="T:System.Threading.Tasks.Task" /> 时提供的状态对象，如果未提供，则为 null。</summary>
      <returns>一个 <see cref="T:System.Object" />，表示在创建任务时传递给该任务的状态数据。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CompletedTask">
      <summary>获取一个已成功完成的任务。</summary>
      <returns>已成功完成的任务。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.ConfigureAwait(System.Boolean)">
      <summary>配置用于等待此 <see cref="T:System.Threading.Tasks.Task" />的 awaiter。</summary>
      <returns>用于的等待此任务的对象。</returns>
      <param name="continueOnCapturedContext">尝试将延续任务封送回原始上下文，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时异步执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的操作。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时可接收取消标记并以异步方式执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的操作。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标任务完成时按照指定的 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 执行的延续任务。延续任务会收到一个取消标记，并使用指定计划程序。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">根据在 <paramref name="continuationOptions" /> 中指定的条件运行的操作。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个在目标任务完成时按照指定的 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">根据在 <paramref name="continuationOptions" /> 中指定的条件运行的操作。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时异步执行的延续任务。延续任务使用指定计划程序。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的操作。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. -or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时接收调用方提供的状态信息并执行的延续任务。</summary>
      <returns>一个新的延续任务。</returns>
      <param name="continuationAction">在任务完成时要运行的操作。运行时，委托作为一个参数传递给完成的任务和调用方提供的状态对象。</param>
      <param name="state">一个表示由该延续操作使用的数据的对象。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时接收调用方提供的状态信息和取消标记，并以异步方式执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的操作。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续操作使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时接收调用方提供的状态信息和取消标记并执行的延续任务。延续任务根据一组指定的条件执行，并使用指定的计划程序。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的操作。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续操作使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时接收调用方提供的状态信息并执行的延续任务。延续任务根据一组指定的条件执行。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的操作。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续操作使用的数据的对象。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时接收调用方提供的状态信息并以异步方式执行的延续任务。延续任务使用指定计划程序。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的操作。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续操作使用的数据的对象。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0})">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时异步执行并返回一个值的延续任务。</summary>
      <returns>一个新的延续任务。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的函数。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <typeparam name="TResult"> 延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时异步执行并返回一个值的延续任务。延续任务收到取消标记。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的函数。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <typeparam name="TResult"> 延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个按照指定延续任务选项执行并返回一个值的延续任务。延续任务被传入一个取消标记，并使用指定计划程序。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">根据 <paramref name="continuationOptions." /> 中指定的条件运行函数。在运行时，委托将作为一个自变量传递给完成的任务。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult"> 延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个按照指定延续任务选项执行并返回一个值的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">根据在 <paramref name="continuationOptions" /> 中指定的条件运行的函数。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <typeparam name="TResult"> 延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时异步执行并返回一个值的延续任务。延续任务使用指定计划程序。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的函数。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult"> 延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成并返回一个值时接收调用方提供的状态信息并以异步方式执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的函数。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续功能使用的数据的对象。</param>
      <typeparam name="TResult">延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成并返回一个值时异步执行的延续任务。延续任务接收调用方提供的状态信息和取消标记。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的函数。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续功能使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TResult">延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成并返回一个值时根据指定的任务延续选项执行的延续任务。延续任务接收调用方提供的状态信息和取消标记，并使用指定计划程序。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的函数。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续功能使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult">延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时根据指定的任务延续选项执行的延续任务。延续任务接收调用方提供的状态信息。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的函数。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续功能使用的数据的对象。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <typeparam name="TResult">延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task" /> 完成时异步执行的延续任务。延续任务接收调用方提供的状态信息，并使用指定计划程序。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task" /> 完成时要运行的函数。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续功能使用的数据的对象。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult">延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.CreationOptions">
      <summary>获取用于创建此任务的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</summary>
      <returns>用于创建此任务的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CurrentId">
      <summary>返回当前正在执行 <see cref="T:System.Threading.Tasks.Task" /> 的唯一 ID。</summary>
      <returns>系统分配给当前正在执行的任务的一个整数。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32)">
      <summary>创建将在时间延迟后完成的任务。</summary>
      <returns>表示时间延迟的任务。</returns>
      <param name="millisecondsDelay">在完成返回的任务前要等待的毫秒数；如果无限期等待，则为 -1。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32,System.Threading.CancellationToken)">
      <summary>创建将在时间延迟后完成的可取消任务。</summary>
      <returns>表示时间延迟的任务。</returns>
      <param name="millisecondsDelay">在完成返回的任务前要等待的毫秒数；如果无限期等待，则为 -1。</param>
      <param name="cancellationToken">将在完成返回的任务之前选中的取消标记。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled. </exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan)">
      <summary>创建一个在指定的时间间隔后完成的任务。</summary>
      <returns>表示时间延迟的任务。</returns>
      <param name="delay">在完成返回的任务前等待的时间跨度；如果无限期等待，则为 TimeSpan.FromMilliseconds(-1)。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>创建一个在指定的时间间隔后完成的可取消任务。</summary>
      <returns>表示时间延迟的任务。</returns>
      <param name="delay">在完成返回的任务前等待的时间跨度；如果无限期等待，则为 TimeSpan.FromMilliseconds(-1)。</param>
      <param name="cancellationToken">将在完成返回的任务之前选中的取消标记。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Exception">
      <summary>获取导致 <see cref="T:System.AggregateException" /> 提前结束的 <see cref="T:System.Threading.Tasks.Task" />。如果 <see cref="T:System.Threading.Tasks.Task" /> 成功完成或尚未引发任何异常，这将返回 null。</summary>
      <returns>导致 <see cref="T:System.AggregateException" /> 提前结束的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Factory">
      <summary>提供对用于创建 <see cref="T:System.Threading.Tasks.Task" /> 和 <see cref="T:System.Threading.Tasks.Task`1" /> 的工厂方法的访问。</summary>
      <returns>一个工厂对象，可创建多种 <see cref="T:System.Threading.Tasks.Task" /> 和 <see cref="T:System.Threading.Tasks.Task`1" /> 对象。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled(System.Threading.CancellationToken)">
      <summary>创建 <see cref="T:System.Threading.Tasks.Task" />，它因指定的取消标记进行的取消操作而完成。</summary>
      <returns>取消的任务。</returns>
      <param name="cancellationToken">完成任务的取消标记。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled``1(System.Threading.CancellationToken)">
      <summary>创建 <see cref="T:System.Threading.Tasks.Task`1" />，它因指定的取消标记进行的取消操作而完成。</summary>
      <returns>取消的任务。</returns>
      <param name="cancellationToken">完成任务的取消标记。</param>
      <typeparam name="TResult">任务返回的结果的类型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException``1(System.Exception)">
      <summary>创建 <see cref="T:System.Threading.Tasks.Task`1" />，它是以指定的异常来完成的。</summary>
      <returns>出错的任务。</returns>
      <param name="exception">完成任务的异常。</param>
      <typeparam name="TResult">任务返回的结果的类型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException(System.Exception)">
      <summary>创建 <see cref="T:System.Threading.Tasks.Task" />，它是以指定的异常来完成的。</summary>
      <returns>出错的任务。</returns>
      <param name="exception">完成任务的异常。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromResult``1(``0)">
      <summary>创建指定结果的、成功完成的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>已成功完成的任务。</returns>
      <param name="result">存储入已完成任务的结果。</param>
      <typeparam name="TResult">任务返回的结果的类型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.GetAwaiter">
      <summary>获取用于等待此 <see cref="T:System.Threading.Tasks.Task" /> 的 awaiter。</summary>
      <returns>一个 awaiter 实例。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Id">
      <summary>获取此 <see cref="T:System.Threading.Tasks.Task" /> 实例的唯一 ID。</summary>
      <returns>系统分配给此任务实例的一个整数。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCanceled">
      <summary>获取此 <see cref="T:System.Threading.Tasks.Task" /> 实例是否由于被取消的原因而已完成执行。</summary>
      <returns>如果任务由于被取消而完成，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCompleted">
      <summary>获取此 <see cref="T:System.Threading.Tasks.Task" /> 是否已完成。</summary>
      <returns>如果任务已完成，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsFaulted">
      <summary>获取 <see cref="T:System.Threading.Tasks.Task" /> 是否由于未经处理异常的原因而完成。</summary>
      <returns>如果任务引发了未经处理的异常，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action)">
      <summary>将在线程池上运行的指定工作排队，并返回该工作的任务句柄。</summary>
      <returns>表示在线程池执行的队列的任务。</returns>
      <param name="action">以异步方式执行的工作量。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action,System.Threading.CancellationToken)">
      <summary>将在线程池上运行的指定工作排队，并返回该工作的任务句柄。</summary>
      <returns>表示在线程池执行的队列的任务。</returns>
      <param name="action">以异步方式执行的工作量。</param>
      <param name="cancellationToken">应用以取消工作的取消标记</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}})">
      <summary>将在线程池上运行的指定工作排队，并返回 Task(TResult) 返回的 <paramref name="function" /> 的代理项。</summary>
      <returns>表示由 Task(TResult) 返回的 Task(TResult) 的代理的 <paramref name="function" />。</returns>
      <param name="function">以异步方式执行的工作量。</param>
      <typeparam name="TResult">代理任务返回的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>将在线程池上运行的指定工作排队，并返回 Task(TResult) 返回的 <paramref name="function" /> 的代理项。</summary>
      <returns>表示由 Task(TResult) 返回的 Task(TResult) 的代理的 <paramref name="function" />。</returns>
      <param name="function">以异步方式执行的工作量。</param>
      <param name="cancellationToken">应用以取消工作的取消标记</param>
      <typeparam name="TResult">代理任务返回的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task})">
      <summary>将在线程池上运行的指定工作排队，并返回 <paramref name="function" /> 返回的任务的代理项。</summary>
      <returns>表示由 <paramref name="function" /> 返回的任务代理的任务。</returns>
      <param name="function">以异步方式执行的工作量。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>将在线程池上运行的指定工作排队，并返回 <paramref name="function" /> 返回的任务的代理项。</summary>
      <returns>表示由 <paramref name="function" /> 返回的任务代理的任务。</returns>
      <param name="function">以异步方式执行的工作。</param>
      <param name="cancellationToken">应用以取消工作的取消标记。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0})">
      <summary>将在线程池上运行的指定工作排队，并返回代表该工作的 <see cref="T:System.Threading.Tasks.Task`1" /> 对象。</summary>
      <returns>表示在线程池中排队执行的工作的任务对象。</returns>
      <param name="function">以异步方式执行的工作。</param>
      <typeparam name="TResult">任务的返回类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>将在线程池上运行的指定工作排队，并返回该工作的 Task(TResult) 句柄。</summary>
      <returns>表示在线程池执行的队列的 Task(TResult)。</returns>
      <param name="function">以异步方式执行的工作量。</param>
      <param name="cancellationToken">应用以取消工作的取消标记</param>
      <typeparam name="TResult">任务的结果类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously">
      <summary>对当前的 <see cref="T:System.Threading.Tasks.Task" /> 同步运行 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously(System.Threading.Tasks.TaskScheduler)">
      <summary>对提供的 <see cref="T:System.Threading.Tasks.Task" /> 同步运行 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</summary>
      <param name="scheduler">尝试对其以内联方式运行此任务的计划程序。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start">
      <summary>启动 <see cref="T:System.Threading.Tasks.Task" />，并将它安排到当前的 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 中执行。</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start(System.Threading.Tasks.TaskScheduler)">
      <summary>启动 <see cref="T:System.Threading.Tasks.Task" />，并将它安排到指定的 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 中执行。</summary>
      <param name="scheduler">要与之关联并执行此任务的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Status">
      <summary>获取此任务的 <see cref="T:System.Threading.Tasks.TaskStatus" />。</summary>
      <returns>此任务实例的当前 <see cref="T:System.Threading.Tasks.TaskStatus" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#AsyncWaitHandle">
      <summary>获取可用于等待任务完成的 <see cref="T:System.Threading.WaitHandle" />。</summary>
      <returns>可用于等待任务完成的 <see cref="T:System.Threading.WaitHandle" />。</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#CompletedSynchronously">
      <summary>获取操作是否已同步完成的指示。</summary>
      <returns>如果操作同步完成，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait">
      <summary>等待 <see cref="T:System.Threading.Tasks.Task" /> 完成执行过程。</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32)">
      <summary>等待 <see cref="T:System.Threading.Tasks.Task" /> 在指定的毫秒数内完成执行。</summary>
      <returns>如果在分配的时间内 true 完成执行，则为 <see cref="T:System.Threading.Tasks.Task" />；否则为 false。</returns>
      <param name="millisecondsTimeout">等待的毫秒数，或为 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)，表示无限期等待。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>等待 <see cref="T:System.Threading.Tasks.Task" /> 完成执行过程。如果在任务完成之前超时间隔结束或取消标记已取消，等待将终止。</summary>
      <returns>如果在分配的时间内 true 完成执行，则为 <see cref="T:System.Threading.Tasks.Task" />；否则为 false。</returns>
      <param name="millisecondsTimeout">等待的毫秒数，或为 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)，表示无限期等待。</param>
      <param name="cancellationToken">等待任务完成期间要观察的取消标记。</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Threading.CancellationToken)">
      <summary>等待 <see cref="T:System.Threading.Tasks.Task" /> 完成执行过程。如果在任务完成之前取消标记已取消，等待将终止。</summary>
      <param name="cancellationToken">等待任务完成期间要观察的取消标记。</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The task has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.TimeSpan)">
      <summary>等待 <see cref="T:System.Threading.Tasks.Task" /> 在指定的时间间隔内完成执行。</summary>
      <returns>如果在分配的时间内 true 完成执行，则为 <see cref="T:System.Threading.Tasks.Task" />；否则为 false。</returns>
      <param name="timeout">表示等待毫秒数的 <see cref="T:System.TimeSpan" />，或表示 -1 毫秒（无限期等待）的 <see cref="T:System.TimeSpan" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[])">
      <summary>等待提供的所有 <see cref="T:System.Threading.Tasks.Task" /> 对象完成执行过程。</summary>
      <param name="tasks">要等待的 <see cref="T:System.Threading.Tasks.Task" /> 实例的数组。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.-or-The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> exception contains an <see cref="T:System.OperationCanceledException" /> exception in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32)">
      <summary>等待所有提供的 <see cref="T:System.Threading.Tasks.Task" /> 在指定的毫秒数内完成执行。</summary>
      <returns>如果在分配的时间内所有 true 实例都已完成执行，则为 <see cref="T:System.Threading.Tasks.Task" />；否则为 false。</returns>
      <param name="tasks">要等待的 <see cref="T:System.Threading.Tasks.Task" /> 实例的数组。</param>
      <param name="millisecondsTimeout">等待的毫秒数，或为 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)，表示无限期等待。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>等待提供的所有 <see cref="T:System.Threading.Tasks.Task" /> 对象在指定的毫秒数内完成执行，或等到取消等待。</summary>
      <returns>如果在分配的时间内所有 true 实例都已完成执行，则为 <see cref="T:System.Threading.Tasks.Task" />；否则为 false。</returns>
      <param name="tasks">要等待的 <see cref="T:System.Threading.Tasks.Task" /> 实例的数组。</param>
      <param name="millisecondsTimeout">等待的毫秒数，或为 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)，表示无限期等待。</param>
      <param name="cancellationToken">等待任务完成期间要观察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>等待提供的所有 <see cref="T:System.Threading.Tasks.Task" /> 对象完成执行过程（除非取消等待）。</summary>
      <param name="tasks">要等待的 <see cref="T:System.Threading.Tasks.Task" /> 实例的数组。</param>
      <param name="cancellationToken">等待任务完成期间要观察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>等待所有提供的可取消 <see cref="T:System.Threading.Tasks.Task" /> 对象在指定的时间间隔内完成执行。</summary>
      <returns>如果在分配的时间内所有 true 实例都已完成执行，则为 <see cref="T:System.Threading.Tasks.Task" />；否则为 false。</returns>
      <param name="tasks">要等待的 <see cref="T:System.Threading.Tasks.Task" /> 实例的数组。</param>
      <param name="timeout">表示等待毫秒数的 <see cref="T:System.TimeSpan" />，或表示 -1 毫秒（无限期等待）的 <see cref="T:System.TimeSpan" />。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null. </exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[])">
      <summary>等待提供的任一 <see cref="T:System.Threading.Tasks.Task" /> 对象完成执行过程。</summary>
      <returns>已完成的任务在 <paramref name="tasks" /> 数组参数中的索引。</returns>
      <param name="tasks">要等待的 <see cref="T:System.Threading.Tasks.Task" /> 实例的数组。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32)">
      <summary>等待任何提供的 <see cref="T:System.Threading.Tasks.Task" /> 对象在指定的毫秒数内完成执行。</summary>
      <returns>已完成的任务在 <paramref name="tasks" /> 数组参数中的索引，如果发生超时，则为 -1。</returns>
      <param name="tasks">要等待的 <see cref="T:System.Threading.Tasks.Task" /> 实例的数组。</param>
      <param name="millisecondsTimeout">等待的毫秒数，或为 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)，表示无限期等待。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>等待提供的任何 <see cref="T:System.Threading.Tasks.Task" /> 对象在指定的毫秒数内完成执行，或等到取消标记取消。</summary>
      <returns>已完成的任务在 <paramref name="tasks" /> 数组参数中的索引，如果发生超时，则为 -1。</returns>
      <param name="tasks">要等待的 <see cref="T:System.Threading.Tasks.Task" /> 实例的数组。</param>
      <param name="millisecondsTimeout">等待的毫秒数，或为 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)，表示无限期等待。</param>
      <param name="cancellationToken">等待任务完成期间要观察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>等待提供的任何 <see cref="T:System.Threading.Tasks.Task" /> 对象完成执行过程（除非取消等待）。</summary>
      <returns>已完成的任务在 <paramref name="tasks" /> 数组参数中的索引。</returns>
      <param name="tasks">要等待的 <see cref="T:System.Threading.Tasks.Task" /> 实例的数组。</param>
      <param name="cancellationToken">等待任务完成期间要观察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>等待任何提供的 <see cref="T:System.Threading.Tasks.Task" /> 对象在指定的时间间隔内完成执行。</summary>
      <returns>已完成的任务在 <paramref name="tasks" /> 数组参数中的索引，如果发生超时，则为 -1。</returns>
      <param name="tasks">要等待的 <see cref="T:System.Threading.Tasks.Task" /> 实例的数组。</param>
      <param name="timeout">表示等待毫秒数的 <see cref="T:System.TimeSpan" />，或表示 -1 毫秒（无限期等待）的 <see cref="T:System.TimeSpan" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>创建一个任务，该任务将在可枚举集合中的所有 <see cref="T:System.Threading.Tasks.Task`1" /> 对象都完成时完成。</summary>
      <returns>表示所有提供的任务的完成情况的任务。</returns>
      <param name="tasks">等待完成的任务。</param>
      <typeparam name="TResult">已完成任务的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>创建一个任务，该任务将在可枚举集合中的所有 <see cref="T:System.Threading.Tasks.Task" /> 对象都完成时完成。</summary>
      <returns>表示所有提供的任务的完成情况的任务。</returns>
      <param name="tasks">等待完成的任务。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Threading.Tasks.Task[])">
      <summary>创建一个任务，该任务将在数组中的所有 <see cref="T:System.Threading.Tasks.Task" /> 对象都完成时完成。</summary>
      <returns>表示所有提供的任务的完成情况的任务。</returns>
      <param name="tasks">等待完成的任务。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Threading.Tasks.Task{``0}[])">
      <summary>创建一个任务，该任务将在数组中的所有 <see cref="T:System.Threading.Tasks.Task`1" /> 对象都完成时完成。</summary>
      <returns>表示所有提供的任务的完成情况的任务。</returns>
      <param name="tasks">等待完成的任务。</param>
      <typeparam name="TResult">已完成任务的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>任何提供的任务已完成时，创建将完成的任务。</summary>
      <returns>表示提供的任务之一已完成的任务。返回任务的结果是完成的任务。</returns>
      <param name="tasks">等待完成的任务。</param>
      <typeparam name="TResult">已完成任务的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>任何提供的任务已完成时，创建将完成的任务。</summary>
      <returns>表示提供的任务之一已完成的任务。返回任务的结果是完成的任务。</returns>
      <param name="tasks">等待完成的任务。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Threading.Tasks.Task[])">
      <summary>任何提供的任务已完成时，创建将完成的任务。</summary>
      <returns>表示提供的任务之一已完成的任务。返回任务的结果是完成的任务。</returns>
      <param name="tasks">等待完成的任务。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Threading.Tasks.Task{``0}[])">
      <summary>任何提供的任务已完成时，创建将完成的任务。</summary>
      <returns>表示提供的任务之一已完成的任务。返回任务的结果是完成的任务。</returns>
      <param name="tasks">等待完成的任务。</param>
      <typeparam name="TResult">已完成任务的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Yield">
      <summary>创建异步产生当前上下文的等待任务。</summary>
      <returns>等待时，上下文将异步转换回等待时的当前上下文。如果当前 <see cref="T:System.Threading.SynchronizationContext" /> 不为 null，则将其视为当前上下文。否则，与当前执行任务关联的任务计划程序将视为当前上下文。</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task`1">
      <summary>表示一个可以返回值的异步操作。</summary>
      <typeparam name="TResult">此 <see cref="T:System.Threading.Tasks.Task`1" /> 生成的结果的类型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0})">
      <summary>使用指定的函数初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">表示要在任务中执行的代码的委托。在完成此函数后，该任务的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 属性将设置为返回此函数的结果值。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken)">
      <summary>使用指定的函数初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">表示要在任务中执行的代码的委托。在完成此函数后，该任务的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 属性将设置为返回此函数的结果值。</param>
      <param name="cancellationToken">将指派给此任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的函数和创建选项初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">表示要在任务中执行的代码的委托。在完成此函数后，该任务的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 属性将设置为返回此函数的结果值。</param>
      <param name="cancellationToken">将指派给新任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="creationOptions">用于自定义任务的行为的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的函数和创建选项初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">表示要在任务中执行的代码的委托。在完成此函数后，该任务的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 属性将设置为返回此函数的结果值。</param>
      <param name="creationOptions">用于自定义任务的行为的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object)">
      <summary>使用指定的函数和状态初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">表示要在任务中执行的代码的委托。在完成此函数后，该任务的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 属性将设置为返回此函数的结果值。</param>
      <param name="state">一个表示由该操作使用的数据的对象。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>使用指定的操作、状态和选项初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">表示要在任务中执行的代码的委托。在完成此函数后，该任务的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 属性将设置为返回此函数的结果值。</param>
      <param name="state">一个表示将由此函数使用的数据的对象。</param>
      <param name="cancellationToken">将指派给此新任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的操作、状态和选项初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">表示要在任务中执行的代码的委托。在完成此函数后，该任务的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 属性将设置为返回此函数的结果值。</param>
      <param name="state">一个表示将由此函数使用的数据的对象。</param>
      <param name="cancellationToken">将指派给此新任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="creationOptions">用于自定义任务的行为的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的操作、状态和选项初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">表示要在任务中执行的代码的委托。在完成此函数后，该任务的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 属性将设置为返回此函数的结果值。</param>
      <param name="state">一个表示将由此函数使用的数据的对象。</param>
      <param name="creationOptions">用于自定义任务的行为的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ConfigureAwait(System.Boolean)">
      <summary>配置用于等待此 <see cref="T:System.Threading.Tasks.Task`1" />的 awaiter。</summary>
      <returns>用于的等待此任务的对象。</returns>
      <param name="continueOnCapturedContext">尝试将延续任务封送回原始上下文，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}})">
      <summary>创建一个在目标任务完成时异步执行的延续任务。</summary>
      <returns>一个新的延续任务。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的操作。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时异步执行的可取消延续任务。</summary>
      <returns>一个新的延续任务。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的操作。在运行时，委托作为一个参数传递给完成的任务。</param>
      <param name="cancellationToken">传递给新的延续任务的取消标记。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建根据 <paramref name="continuationOptions" /> 中指定的条件加以执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">根据在 <paramref name="continuationOptions" /> 中指定的条件运行的操作。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建根据 <paramref name="continuationOptions" /> 中指定的条件加以执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <paramref name="continuationOptions" /> 中指定的条件的操作。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时异步执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的操作。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object)">
      <summary>创建一个传递了状态信息并在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的操作。运行时，委托作为一个参数传递给完成的任务和调用方提供的状态对象。</param>
      <param name="state">一个表示由该延续操作使用的数据的对象。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的操作。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续操作使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的操作。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续操作使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的操作。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续操作使用的数据的对象。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的操作。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续操作使用的数据的对象。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0})">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时异步执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的函数。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <typeparam name="TNewResult"> 延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时异步执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的函数。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="cancellationToken">将指派给新任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TNewResult"> 延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建根据 <paramref name="continuationOptions" /> 中指定的条件加以执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">根据 <paramref name="continuationOptions" /> 中指定的条件运行函数。在运行时，委托将作为一个参数传递给此完成的任务。</param>
      <param name="cancellationToken">将指派给新任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult"> 延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建根据 <paramref name="continuationOptions" /> 中指定的条件加以执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">根据 <paramref name="continuationOptions" /> 中指定的条件运行函数。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <typeparam name="TNewResult"> 延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时异步执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的函数。在运行时，委托将作为一个参数传递给完成的任务。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult"> 延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的函数。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续功能使用的数据的对象。</param>
      <typeparam name="TNewResult">延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的函数。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续功能使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TNewResult">延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的函数。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续功能使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult">延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The  <paramref name="continuationOptions" />  argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的函数。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续功能使用的数据的对象。</param>
      <param name="continuationOptions">用于设置计划延续任务的时间以及延续任务的工作方式的选项。这包括条件（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />）和执行选项（如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />）。</param>
      <typeparam name="TNewResult">延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个在目标 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时执行的延续任务。</summary>
      <returns>一个新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">在 <see cref="T:System.Threading.Tasks.Task`1" /> 完成时要运行的函数。运行时，将传递委托，如完成的任务一样，调用方提供的状态对象（如参数）。</param>
      <param name="state">一个表示由该延续功能使用的数据的对象。</param>
      <param name="scheduler">要与延续任务关联并用于其执行过程的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult">延续任务生成的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Factory">
      <summary>提供对用于创建和配置 <see cref="T:System.Threading.Tasks.Task`1" /> 实例的工厂方法的访问。</summary>
      <returns>一个工厂对象，可创建多种 <see cref="T:System.Threading.Tasks.Task`1" /> 对象。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.GetAwaiter">
      <summary>获取用于等待此 <see cref="T:System.Threading.Tasks.Task`1" /> 的 awaiter。</summary>
      <returns>一个 awaiter 实例。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Result">
      <summary>获取此 <see cref="T:System.Threading.Tasks.Task`1" /> 的结果值。</summary>
      <returns>此 <see cref="T:System.Threading.Tasks.Task`1" /> 的结果值，该值类型与任务参数类型相同。</returns>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskCanceledException">
      <summary>表示一个用于告知任务取消的异常。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor">
      <summary>使用由系统提供的用来描述错误的消息初始化 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String)">
      <summary>使用指定的描述错误的消息初始化 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 类的新实例。</summary>
      <param name="message">描述该异常的消息。此构造函数的调用方需要确保此字符串已针对当前系统区域性进行了本地化。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String,System.Exception)">
      <summary>使用指定错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 类的新实例。</summary>
      <param name="message">描述该异常的消息。此构造函数的调用方需要确保此字符串已针对当前系统区域性进行了本地化。</param>
      <param name="innerException">导致当前异常的异常。如果 <paramref name="innerException" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.Threading.Tasks.Task)">
      <summary>使用对已取消的 <see cref="T:System.Threading.Tasks.Task" /> 的引用初始化 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 类的新实例。</summary>
      <param name="task">已取消的任务。</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskCanceledException.Task">
      <summary>获取与此异常关联的任务。</summary>
      <returns>对与此异常关联的 <see cref="T:System.Threading.Tasks.Task" /> 的引用。</returns>
    </member>
    <member name="T:System.Threading.Tasks.TaskCompletionSource`1">
      <summary>表示未绑定到委托的 <see cref="T:System.Threading.Tasks.Task`1" /> 的制造者方，并通过 <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 属性提供对使用者方的访问。</summary>
      <typeparam name="TResult">与此 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> 关联的结果值的类型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object)">
      <summary>使用指定的状态创建一个 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />。</summary>
      <param name="state">要用作基础 <see cref="T:System.Threading.Tasks.Task`1" /> 的 AsyncState 的状态。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的状态和选项创建一个 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />。</summary>
      <param name="state">要用作基础 <see cref="T:System.Threading.Tasks.Task`1" /> 的 AsyncState 的状态。</param>
      <param name="creationOptions">创建基础 <see cref="T:System.Threading.Tasks.Task`1" /> 时要使用的选项。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 表示与 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> 一起使用时无效的选项。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的选项创建一个 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />。</summary>
      <param name="creationOptions">创建基础 <see cref="T:System.Threading.Tasks.Task`1" /> 时要使用的选项。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 表示与 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> 一起使用时无效的选项。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetCanceled">
      <summary>将基础 <see cref="T:System.Threading.Tasks.Task`1" /> 转换为 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> 状态。</summary>
      <exception cref="T:System.InvalidOperationException">基础 <see cref="T:System.Threading.Tasks.Task`1" /> 已经处于三个最终状态之一： <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 或 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />；或者基础 <see cref="T:System.Threading.Tasks.Task`1" /> 已被释放。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>将基础 <see cref="T:System.Threading.Tasks.Task`1" /> 转换为 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状态。</summary>
      <param name="exceptions">要绑定到此 <see cref="T:System.Threading.Tasks.Task`1" /> 的异常的集合。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 已处理。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exceptions" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">在 <paramref name="exceptions" /> 中有一个或多个 null 元素。</exception>
      <exception cref="T:System.InvalidOperationException">基础 <see cref="T:System.Threading.Tasks.Task`1" /> 已经处于三个最终状态之一： <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 或 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Exception)">
      <summary>将基础 <see cref="T:System.Threading.Tasks.Task`1" /> 转换为 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状态。</summary>
      <param name="exception">要绑定到此 <see cref="T:System.Threading.Tasks.Task`1" /> 的异常。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 已处理。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 参数为 null。</exception>
      <exception cref="T:System.InvalidOperationException">基础 <see cref="T:System.Threading.Tasks.Task`1" /> 已经处于三个最终状态之一： <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 或 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetResult(`0)">
      <summary>将基础 <see cref="T:System.Threading.Tasks.Task`1" /> 转换为 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> 状态。</summary>
      <param name="result">要绑定到此 <see cref="T:System.Threading.Tasks.Task`1" /> 的结果值。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 已处理。</exception>
      <exception cref="T:System.InvalidOperationException">基础 <see cref="T:System.Threading.Tasks.Task`1" /> 已经处于三个最终状态之一： <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 或 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskCompletionSource`1.Task">
      <summary>获取由此 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> 创建的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>返回由此 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> 创建的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled">
      <summary>尝试将基础 <see cref="T:System.Threading.Tasks.Task`1" /> 转换为 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> 状态。</summary>
      <returns>如果操作成功，则为 true；如果操作失败或对象已被释放，则为 false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled(System.Threading.CancellationToken)">
      <summary>尝试，则过渡基础<see cref="T:System.Threading.Tasks.Task`1" />到<see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />状态，并使用于存储在已取消的任务的取消标记。</summary>
      <returns>如果操作成功，则为 true；否则为 false。</returns>
      <param name="cancellationToken">取消标记。 </param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>尝试将基础 <see cref="T:System.Threading.Tasks.Task`1" /> 转换为 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状态。</summary>
      <returns>如果操作成功，则为 true；否则为 false。</returns>
      <param name="exceptions">要绑定到此 <see cref="T:System.Threading.Tasks.Task`1" /> 的异常的集合。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 已处理。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exceptions" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">在 <paramref name="exceptions" /> 中有一个或多个 null 元素。- 或 -<paramref name="exceptions" /> 集合是空的。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Exception)">
      <summary>尝试将基础 <see cref="T:System.Threading.Tasks.Task`1" /> 转换为 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状态。</summary>
      <returns>如果操作成功，则为 true；否则为 false。</returns>
      <param name="exception">要绑定到此 <see cref="T:System.Threading.Tasks.Task`1" /> 的异常。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 已处理。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetResult(`0)">
      <summary>尝试将基础 <see cref="T:System.Threading.Tasks.Task`1" /> 转换为 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> 状态。</summary>
      <returns>如果操作成功，则为 true；否则为 false。</returns>
      <param name="result">要绑定到此 <see cref="T:System.Threading.Tasks.Task`1" /> 的结果值。</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskContinuationOptions">
      <summary>为通过使用 <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)" /> 或 <see cref="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)" /> 方法创建的任务指定行为。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent">
      <summary>如果延续为子任务，则指定将延续附加到任务层次结构中的父级。只有当延续前面的任务也是子任务时，延续才可以是子任务。默认情况下，子任务（即由外部任务创建的内部任务）将独立于其父任务执行。可以使用 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> 选项以便将父任务和子任务同步。请注意，如果使用 <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" /> 选项配置父任务，则子任务中的 <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 选项不起作用，并且子任务将作为分离的子任务执行。有关详细信息，请参阅附加和分离的子任务。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.DenyChildAttach">
      <summary>指定任何使用 <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 选项创建，并尝试作为附加的子任务执行的子任务（即，由此延续创建的任何嵌套内部任务）都无法附加到父任务，会改成作为分离的子任务执行。有关详细信息，请参阅已附加和已分离的子任务。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously">
      <summary>指定应同步执行延续任务。指定此选项后，延续任务在导致前面的任务转换为其最终状态的相同线程上运行。如果在创建延续任务时已经完成前面的任务，则延续任务将在创建此延续任务的线程上运行。如果前面任务的 <see cref="T:System.Threading.CancellationTokenSource" /> 已在一个 finally（在 Visual Basic 中为 Finally）块中释放，则使用此选项的延续任务将在该 finally 块中运行。只应同步执行运行时间非常短的延续任务。由于任务以同步方式执行，因此无需调用诸如 <see cref="M:System.Threading.Tasks.Task.Wait" /> 的方法来确保调用线程等待任务完成。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.HideScheduler">
      <summary>指定由延续通过调用方法 <see cref="M:System.Threading.Tasks.Task.Run(System.Action)" /> 或 <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})" /> 创建的任务视默认计划程序 (<see cref="P:System.Threading.Tasks.TaskScheduler.Default" />) 为当前的计划程序，而不是正在运行该延续的计划程序。 </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LazyCancellation">
      <summary>在延续取消的情况下，防止延续的完成直到完成先前的任务。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LongRunning">
      <summary>指定延续将是长期运行的、粗粒度的操作。它会向 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 提示，过度订阅可能是合理的。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.None">
      <summary>如果未指定延续选项，应在执行延续任务时使用指定的默认行为。延续任务在前面的任务完成后以异步方式运行，与前面任务最终的 <see cref="P:System.Threading.Tasks.Task.Status" /> 属性值无关。如果延续为子任务，则会将其创建为分离的嵌套任务。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnCanceled">
      <summary>指定不应在延续任务前面的任务已取消的情况下安排延续任务。如果前面任务完成的 <see cref="P:System.Threading.Tasks.Task.Status" /> 属性是 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />，则前面的任务会取消。此选项对多任务延续无效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnFaulted">
      <summary>指定不应在延续任务前面的任务引发了未处理异常的情况下安排延续任务。如果前面任务完成的 <see cref="P:System.Threading.Tasks.Task.Status" /> 属性是 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />，则前面的任务会引发未处理的异常。此选项对多任务延续无效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnRanToCompletion">
      <summary>指定不应在延续任务前面的任务已完成运行的情况下安排延续任务。如果前面任务完成的 <see cref="P:System.Threading.Tasks.Task.Status" /> 属性是 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />，则前面的任务会运行直至完成。此选项对多任务延续无效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled">
      <summary>指定只应在延续前面的任务已取消的情况下安排延续任务。如果前面任务完成的 <see cref="P:System.Threading.Tasks.Task.Status" /> 属性是 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />，则前面的任务会取消。此选项对多任务延续无效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted">
      <summary>指定只有在延续任务前面的任务引发了未处理异常的情况下才应安排延续任务。如果前面任务完成的 <see cref="P:System.Threading.Tasks.Task.Status" /> 属性是 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />，则前面的任务会引发未处理的异常。<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted" /> 选项可保证前面任务中的 <see cref="P:System.Threading.Tasks.Task.Exception" /> 属性不是 null。你可以使用该属性来捕获异常，并确定导致任务出错的异常。如果你不访问 <see cref="P:System.Threading.Tasks.Task.Exception" /> 属性，则不会处理异常。此外，如果尝试访问已取消或出错的任务的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 属性，则会引发一个新异常。此选项对多任务延续无效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnRanToCompletion">
      <summary>指定只应在延续任务前面的任务已完成运行的情况下才安排延续任务。如果前面任务完成的 <see cref="P:System.Threading.Tasks.Task.Status" /> 属性是 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />，则前面的任务会运行直至完成。此选项对多任务延续无效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.PreferFairness">
      <summary>提示 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 按任务计划的顺序安排任务，因此较早安排的任务将更可能较早运行，而较晚安排运行的任务将更可能较晚运行。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.RunContinuationsAsynchronously">
      <summary>指定应异步运行延续任务。此选项优先于 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />。</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskCreationOptions">
      <summary>指定用于控制任务的创建和执行的可选行为的标志。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent">
      <summary>指定将任务附加到任务层次结构中的某个父级。默认情况下，子任务（即由外部任务创建的内部任务）将独立于其父任务执行。可以使用 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> 选项以便将父任务和子任务同步。请注意，如果使用 <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" /> 选项配置父任务，则子任务中的 <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 选项不起作用，并且子任务将作为分离的子任务执行。有关详细信息，请参阅附加和分离的子任务。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach">
      <summary>指定任何尝试作为附加的子任务执行（即，使用 <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 选项创建）的子任务都无法附加到父任务，会改成作为分离的子任务执行。有关更多信息，请参见 已附加和已分离的子任务。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.HideScheduler">
      <summary>防止环境计划程序被视为已创建任务的当前计划程序。这意味着像 StartNew 或 ContinueWith 创建任务的执行操作将被视为 <see cref="P:System.Threading.Tasks.TaskScheduler.Default" /> 当前计划程序。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.LongRunning">
      <summary>指定任务将是长时间运行的、粗粒度的操作，涉及比细化的系统更少、更大的组件。它会向 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 提示，过度订阅可能是合理的。可以通过过度订阅创建比可用硬件线程数更多的线程。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.None">
      <summary>指定应使用默认行为。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.PreferFairness">
      <summary>提示 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 以一种尽可能公平的方式安排任务，这意味着较早安排的任务将更可能较早运行，而较晚安排运行的任务将更可能较晚运行。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.RunContinuationsAsynchronously">
      <summary>强制异步执行添加到当前任务的延续任务。</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskExtensions">
      <summary>提供一组用于处理特定类型的 <see cref="T:System.Threading.Tasks.Task" /> 实例的静态方法（在 Visual Basic 中为共享方法）。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap``1(System.Threading.Tasks.Task{System.Threading.Tasks.Task{``0}})">
      <summary>创建一个表示 <see cref="T:System.Threading.Tasks.Task" /> 的异步操作的代理 Task&lt;Task&lt;T&gt;&gt; (C#) or Task (Of Task(Of T)) (Visual Basic)。 </summary>
      <returns>一个 <see cref="T:System.Threading.Tasks.Task" /> 它表示所提供的 Task&lt;Task&lt;T&gt;&gt; (C#) 或 Task (Of Task(Of T)) (Visual Basic) 的异步运算。</returns>
      <param name="task">要解包的 Task&lt;Task&lt;T&gt;&gt; (C#) 或者 Task (Of Task(Of T)) (Visual Basic)。</param>
      <typeparam name="TResult">任务结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap(System.Threading.Tasks.Task{System.Threading.Tasks.Task})">
      <summary>创建一个表示 <see cref="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)" /> 的异步操作的代理 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>表示所提供的 System.Threading.Tasks.Task(Of Task) 的异步操作的 Task。</returns>
      <param name="task">要解包的 Task&lt;Task&gt; (C#) 或者 Task (Of Task) (Visual Basic)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory">
      <summary>提供对创建和计划 <see cref="T:System.Threading.Tasks.Task" /> 对象的支持。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor">
      <summary>使用默认配置初始化 <see cref="T:System.Threading.Tasks.TaskFactory" /> 实例。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken)">
      <summary>使用指定配置初始化 <see cref="T:System.Threading.Tasks.TaskFactory" /> 实例。</summary>
      <param name="cancellationToken">将指派给由此 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> 创建的任务的 <see cref="T:System.Threading.Tasks.TaskFactory" />，除非在调用工厂方法时显式指定另一个 CancellationToken。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>使用指定配置初始化 <see cref="T:System.Threading.Tasks.TaskFactory" /> 实例。</summary>
      <param name="cancellationToken">将指派给由此 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> 创建的任务的 <see cref="T:System.Threading.Tasks.TaskFactory" />，除非在调用工厂方法时显式指定另一个 CancellationToken。</param>
      <param name="creationOptions">在使用此 TaskFactory 创建任务时要使用的默认 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <param name="continuationOptions">在使用此 TaskFactory 创建延续任务时要使用的默认 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />。</param>
      <param name="scheduler">要用于计划使用此 TaskFactory 创建的任何任务的默认 <see cref="T:System.Threading.Tasks.TaskScheduler" />。一个 null 值，该值指示应使用 TaskScheduler.Current。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 值无效。有关详细信息，请参阅的备注部分 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />。- 或 -<paramref name="continuationOptions" /> 参数指定的值无效。 </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>使用指定配置初始化 <see cref="T:System.Threading.Tasks.TaskFactory" /> 实例。</summary>
      <param name="creationOptions">在使用此 TaskFactory 创建任务时要使用的默认 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <param name="continuationOptions">在使用此 TaskFactory 创建延续任务时要使用的默认 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 值无效。有关详细信息，请参阅的备注部分 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />。- 或 -<paramref name="continuationOptions" /> 参数指定的值无效。 </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>使用指定配置初始化 <see cref="T:System.Threading.Tasks.TaskFactory" /> 实例。</summary>
      <param name="scheduler">要用于计划使用此 TaskFactory 创建的任何任务的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。一个 null 值，该值指示应使用当前的 TaskScheduler。</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CancellationToken">
      <summary>获取此任务工厂的默认取消标记。</summary>
      <returns>此任务工厂的默认任务取消标记。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.ContinuationOptions">
      <summary>获取此任务工厂的默认任务继续选项。</summary>
      <returns>此任务工厂的默认任务继续选项。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]})">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的所有任务完成时要执行的操作委托。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationAction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的所有任务完成时要执行的操作委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。- 或 -创建了 <see cref="T:System.Threading.CancellationTokenSource" /> 的 <paramref name="cancellationToken" /> 已经被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationAction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的所有任务完成时要执行的操作委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <param name="continuationOptions">枚举值的按位组合，这些枚举值控制新的延续任务的行为。</param>
      <param name="scheduler">用于计划新的延续任务的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationAction" /> 参数为 null。- 或 -<paramref name="scheduler" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的所有任务完成时要执行的操作委托。</param>
      <param name="continuationOptions">枚举值的按位组合，这些枚举值控制新的延续任务的行为。NotOn* 和 OnlyOn* 成员不受支持。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationAction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的值无效。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0})">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的任务关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的任务关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。- 或 -创建了 <see cref="T:System.Threading.CancellationTokenSource" /> 的 <paramref name="cancellationToken" /> 已经被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <param name="continuationOptions">枚举值的按位组合，这些枚举值控制新的延续任务的行为。NotOn* 和 OnlyOn* 成员不受支持。</param>
      <param name="scheduler">用于计划新的延续任务的对象。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的任务关联的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。- 或 -<paramref name="scheduler" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="continuationOptions">枚举值的按位组合，这些枚举值控制新的延续任务的行为。NotOn* 和 OnlyOn* 成员不受支持。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的任务关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的值无效。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]})">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的所有任务完成时要执行的操作委托。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationAction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的所有任务完成时要执行的操作委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。- 或 -创建了 <see cref="T:System.Threading.CancellationTokenSource" /> 的 <paramref name="cancellationToken" /> 已经被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationAction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的所有任务完成时要执行的操作委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <param name="continuationOptions">枚举值的按位组合，这些枚举值控制新的延续任务的行为。NotOn* 和 OnlyOn* 成员不受支持。</param>
      <param name="scheduler">用于计划新的延续任务的对象。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationAction" /> 参数为 null。- 或 -<paramref name="scheduler" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的所有任务完成时要执行的操作委托。</param>
      <param name="continuationOptions">枚举值的按位组合，这些枚举值控制新的延续任务的行为。NotOn* 和 OnlyOn* 成员不受支持。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationAction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的值无效。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1})">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的任务关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的任务关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。- 或 -<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <param name="continuationOptions">枚举值的按位组合，这些枚举值控制新的延续任务的行为。NotOn* 和 OnlyOn* 成员不受支持。</param>
      <param name="scheduler">用于计划新的延续任务的对象。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的任务关联的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。- 或 -<paramref name="scheduler" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的值无效。</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。- 或 -创建了 <see cref="T:System.Threading.CancellationTokenSource" /> 的 <paramref name="cancellationToken" /> 已经被释放。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续任务，该任务在一组指定的任务完成后开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="continuationOptions">枚举值的按位组合，这些枚举值控制新的延续任务的行为。NotOn* 和 OnlyOn* 成员不受支持。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的任务关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的一个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的值无效。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组为空或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task})">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的一个任务完成时要执行的操作委托。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationAction" /> 参数是 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值。- 或 -<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的一个任务完成时要执行的操作委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。- 或 -<paramref name="cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationAction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值。- 或 -<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的一个任务完成时要执行的操作委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，用于控制所创建的延续 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <param name="scheduler">用于计划所创建的延续 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationAction" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="scheduler" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的一个任务完成时要执行的操作委托。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，用于控制所创建的延续 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <exception cref="T:System.ObjectDisposedException">在 <paramref name="tasks" /> 数组中的元素之一已经被释放时引发的异常。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationAction" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">当 <paramref name="continuationOptions" /> 参数指定无效 TaskContinuationOptions 值时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0})">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task`1" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">在 <paramref name="tasks" /> 数组中的元素之一已经被释放时引发的异常。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationFunction" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task`1" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">在 <paramref name="tasks" /> 数组中的元素之一已经被释放时引发的异常。- 或 -提供的 <see cref="T:System.Threading.CancellationToken" /> 已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationFunction" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task`1" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，用于控制所创建的延续 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <param name="scheduler">用于计划所创建的延续 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 关联的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationFunction" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="scheduler" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task`1" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，用于控制所创建的延续 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">在 <paramref name="tasks" /> 数组中的元素之一已经被释放时引发的异常。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationFunction" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">当 <paramref name="continuationOptions" /> 参数指定无效 TaskContinuationOptions 值时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}})">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的一个任务完成时要执行的操作委托。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">在 <paramref name="tasks" /> 数组中的元素之一已经被释放时引发的异常。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationAction" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的一个任务完成时要执行的操作委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">在 <paramref name="tasks" /> 数组中的元素之一已经被释放时引发的异常。- 或 -提供的 <see cref="T:System.Threading.CancellationToken" /> 已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationAction" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的一个任务完成时要执行的操作委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，用于控制所创建的延续 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <param name="scheduler">用于计划所创建的延续 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationAction" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="scheduler" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationAction">在 <paramref name="tasks" /> 数组中的一个任务完成时要执行的操作委托。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，用于控制所创建的延续 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">在 <paramref name="tasks" /> 数组中的元素之一已经被释放时引发的异常。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationAction" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">当 <paramref name="continuationOptions" /> 参数指定无效 TaskContinuationOptions 值时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1})">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task`1" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">在 <paramref name="tasks" /> 数组中的元素之一已经被释放时引发的异常。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationFunction" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task`1" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">在 <paramref name="tasks" /> 数组中的元素之一已经被释放时引发的异常。- 或 -提供的 <see cref="T:System.Threading.CancellationToken" /> 已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationFunction" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task`1" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，用于控制所创建的延续 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <param name="scheduler">用于计划所创建的延续 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 关联的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationFunction" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="scheduler" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续 <see cref="T:System.Threading.Tasks.Task`1" />，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，用于控制所创建的延续 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委托返回并与创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 关联的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">在 <paramref name="tasks" /> 数组中的元素之一已经被释放时引发的异常。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="tasks" /> 数组为 null 时引发的异常。- 或 -当 <paramref name="continuationFunction" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">当 <paramref name="continuationOptions" /> 参数指定无效 TaskContinuationOptions 值时引发的异常。</exception>
      <exception cref="T:System.ArgumentException">当 <paramref name="tasks" /> 数组包含 null 值时引发的异常。- 或 -当 <paramref name="tasks" /> 数组为空时引发的异常。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CreationOptions">
      <summary>获取此任务工厂的默认任务创建选项。</summary>
      <returns>此任务工厂的默认任务创建选项。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task`1" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task`1" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task`1" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task`1" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task`1" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task`1" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="arg3">传递给 <paramref name="beginMethod" /> 委托的第三个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第三个参数的类型。</typeparam>
      <typeparam name="TArg3">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="arg3">传递给 <paramref name="beginMethod" /> 委托的第三个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第三个参数的类型。</typeparam>
      <typeparam name="TArg3">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task`1" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="arg3">传递给 <paramref name="beginMethod" /> 委托的第三个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第三个参数的类型。</typeparam>
      <typeparam name="TArg3">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task`1" />，表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="arg3">传递给 <paramref name="beginMethod" /> 委托的第三个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第三个参数的类型。</typeparam>
      <typeparam name="TArg3">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="beginMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult})">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task" />，它在指定的 <see cref="T:System.IAsyncResult" /> 完成时执行一个结束方法操作。</summary>
      <returns>一个表示异步操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="asyncResult">IAsyncResult，完成它时将触发对 <paramref name="endMethod" /> 的处理。</param>
      <param name="endMethod">用于处理完成的 <paramref name="asyncResult" /> 的操作委托。</param>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="asyncResult" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task" />，它在指定的 <see cref="T:System.IAsyncResult" /> 完成时执行一个结束方法操作。</summary>
      <returns>一个表示异步操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="asyncResult">IAsyncResult，完成它时将触发对 <paramref name="endMethod" /> 的处理。</param>
      <param name="endMethod">用于处理完成的 <paramref name="asyncResult" /> 的操作委托。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="asyncResult" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task" />，它在指定的 <see cref="T:System.IAsyncResult" /> 完成时执行一个结束方法操作。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="asyncResult">IAsyncResult，完成它时将触发对 <paramref name="endMethod" /> 的处理。</param>
      <param name="endMethod">用于处理完成的 <paramref name="asyncResult" /> 的操作委托。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <param name="scheduler">用于计划将执行结束方法的任务的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="asyncResult" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="scheduler" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0})">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task`1" />，它在指定的 <see cref="T:System.IAsyncResult" /> 完成时执行一个结束方法函数。</summary>
      <returns>一个表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">IAsyncResult，完成它时将触发对 <paramref name="endMethod" /> 的处理。</param>
      <param name="endMethod">用于处理完成的 <paramref name="asyncResult" /> 的函数委托。</param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="asyncResult" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task`1" />，它在指定的 <see cref="T:System.IAsyncResult" /> 完成时执行一个结束方法函数。</summary>
      <returns>一个表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">IAsyncResult，完成它时将触发对 <paramref name="endMethod" /> 的处理。</param>
      <param name="endMethod">用于处理完成的 <paramref name="asyncResult" /> 的函数委托。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="asyncResult" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个 <see cref="T:System.Threading.Tasks.Task`1" />，它在指定的 <see cref="T:System.IAsyncResult" /> 完成时执行一个结束方法函数。</summary>
      <returns>一个表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">IAsyncResult，完成它时将触发对 <paramref name="endMethod" /> 的处理。</param>
      <param name="endMethod">用于处理完成的 <paramref name="asyncResult" /> 的函数委托。</param>
      <param name="creationOptions">TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <param name="scheduler">用于计划将执行结束方法的任务的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="asyncResult" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="endMethod" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="scheduler" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.Scheduler">
      <summary>获取此任务工厂的默认任务计划程序。</summary>
      <returns>此任务工厂的默认任务计划程序。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action)">
      <summary>创建并启动 任务。</summary>
      <returns>已启动的任务。</returns>
      <param name="action">要异步执行的操作委托。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要异步执行的操作委托。</param>
      <param name="cancellationToken">将指派给新任务的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">提供的 <see cref="T:System.Threading.CancellationToken" /> 已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="action" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要异步执行的操作委托。</param>
      <param name="cancellationToken">将指派给新 <see cref="T:System.Threading.Tasks.Task" /> 的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /></param>
      <param name="creationOptions">一个 TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <param name="scheduler">用于计划所创建的 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ObjectDisposedException">提供的 <see cref="T:System.Threading.CancellationToken" /> 已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="action" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="scheduler" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要异步执行的操作委托。</param>
      <param name="creationOptions">一个 TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="action" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要异步执行的操作委托。</param>
      <param name="state">一个包含由 <paramref name="action" /> 委托使用的数据的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要异步执行的操作委托。</param>
      <param name="state">一个包含由 <paramref name="action" /> 委托使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新 <see cref="T:System.Threading.Tasks.Task" /> 的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /></param>
      <exception cref="T:System.ObjectDisposedException">提供的 <see cref="T:System.Threading.CancellationToken" /> 已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="action" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要异步执行的操作委托。</param>
      <param name="state">一个包含由 <paramref name="action" /> 委托使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新任务的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">一个 TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <param name="scheduler">用于计划所创建的 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ObjectDisposedException">提供的 <see cref="T:System.Threading.CancellationToken" /> 已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="action" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="scheduler" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要异步执行的操作委托。</param>
      <param name="state">一个包含由 <paramref name="action" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">一个 TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task" /> 的行为。</param>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="action" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0})">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">一个函数委托，可返回能够通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的将来结果。</param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">一个函数委托，可返回能够通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的将来结果。</param>
      <param name="cancellationToken">将指派给新 <see cref="T:System.Threading.Tasks.Task" /> 的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /></param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">提供的 <see cref="T:System.Threading.CancellationToken" /> 已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="function" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">一个函数委托，可返回能够通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的将来结果。</param>
      <param name="cancellationToken">将指派给新任务的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">一个 TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <param name="scheduler">用于计划所创建的 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">提供的 <see cref="T:System.Threading.CancellationToken" /> 已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="function" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="scheduler" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">一个函数委托，可返回能够通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的将来结果。</param>
      <param name="creationOptions">一个 TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="function" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">一个函数委托，可返回能够通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的将来结果。</param>
      <param name="state">一个包含由 <paramref name="function" /> 委托使用的数据的对象。</param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="function" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">一个函数委托，可返回能够通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的将来结果。</param>
      <param name="state">一个包含由 <paramref name="function" /> 委托使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新 <see cref="T:System.Threading.Tasks.Task" /> 的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /></param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">提供的 <see cref="T:System.Threading.CancellationToken" /> 已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="function" /> 参数为 null 时引发的异常。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">一个函数委托，可返回能够通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的将来结果。</param>
      <param name="state">一个包含由 <paramref name="function" /> 委托使用的数据的对象。</param>
      <param name="cancellationToken">将指派给新任务的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">一个 TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <param name="scheduler">用于计划所创建的 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">提供的 <see cref="T:System.Threading.CancellationToken" /> 已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="function" /> 参数为 null 时引发的异常。- 或 -当 <paramref name="scheduler" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建并启动 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">一个函数委托，可返回能够通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的将来结果。</param>
      <param name="state">一个包含由 <paramref name="function" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">一个 TaskCreationOptions 值，用于控制创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <typeparam name="TResult">可通过 <see cref="T:System.Threading.Tasks.Task`1" /> 获得的结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">当 <paramref name="function" /> 参数为 null 时引发的异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。<paramref name="creationOptions" /> 参数指定无效 TaskCreationOptions 值时引发的异常。有关更多信息，请参见 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的备注</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory`1">
      <summary>提供对创建和计划 <see cref="T:System.Threading.Tasks.Task`1" /> 对象的支持。</summary>
      <typeparam name="TResult">此类的方法创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 对象的返回值。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor">
      <summary>使用默认配置初始化 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 实例。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken)">
      <summary>使用默认配置初始化 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 实例。</summary>
      <param name="cancellationToken">将指派给由此 <see cref="T:System.Threading.Tasks.TaskFactory" /> 创建的任务的默认取消标记（除非在调用工厂方法时显式指定另一个取消标记）。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>使用指定配置初始化 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 实例。</summary>
      <param name="cancellationToken">将指派给由此 <see cref="T:System.Threading.Tasks.TaskFactory" /> 创建的任务的默认取消标记（除非在调用工厂方法时显式指定另一个取消标记）。</param>
      <param name="creationOptions">在使用此 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 创建任务时要使用的默认选项。</param>
      <param name="continuationOptions">在使用此 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 创建任务时要使用的默认选项。</param>
      <param name="scheduler">要用于计划使用此 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 创建的任何任务的默认计划程序。null 值指示应使用 <see cref="P:System.Threading.Tasks.TaskScheduler.Current" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 或 <paramref name="continuationOptions" /> 指定了一个无效值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>使用指定配置初始化 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 实例。</summary>
      <param name="creationOptions">在使用此 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 创建任务时要使用的默认选项。</param>
      <param name="continuationOptions">在使用此 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 创建任务时要使用的默认选项。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 或 <paramref name="continuationOptions" /> 指定了一个无效值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>使用指定配置初始化 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 实例。</summary>
      <param name="scheduler">要用于计划使用此 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 创建的任何任务的计划程序。一个 null 值，该值指示应使用当前 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CancellationToken">
      <summary>获取此任务工厂的默认取消标记。</summary>
      <returns>此任务工厂的默认取消标记。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.ContinuationOptions">
      <summary>获取此任务工厂的 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 枚举值。</summary>
      <returns>指定此任务工厂的默认延续选项的枚举值之一。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0})">
      <summary>创建一个延续任务，它将在提供的一组任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值或为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken)">
      <summary>创建一个延续任务，它将在提供的一组任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。- 或 -<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值或为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续任务，它将在提供的一组任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <param name="continuationOptions">控制所创建的延续任务的行为的枚举值之一。NotOn* 或 OnlyOn* 值均无效。</param>
      <param name="scheduler">用于计划所创建的延续任务的计划程序。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。- 或 -<paramref name="scheduler" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值或为空。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 指定了一个无效值。</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。- 或 -<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续任务，它将在提供的一组任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="continuationOptions">控制所创建的延续任务的行为的枚举值之一。NotOn* 或 OnlyOn* 值均无效。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的值无效。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值或为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0})">
      <summary>创建一个延续任务，它将在提供的一组任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值或为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken)">
      <summary>创建一个延续任务，它将在提供的一组任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。- 或 -<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值或为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续任务，它将在提供的一组任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <param name="continuationOptions">控制所创建的延续任务的行为的枚举值之一。NotOn* 或 OnlyOn* 值均无效。</param>
      <param name="scheduler">用于计划所创建的延续任务的计划程序。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。- 或 -<paramref name="scheduler" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值或为空。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的值无效。</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。- 或 -<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续任务，它将在提供的一组任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的所有任务完成时要异步执行的函数委托。</param>
      <param name="continuationOptions">控制所创建的延续任务的行为的枚举值之一。NotOn* 或 OnlyOn* 值均无效。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的值无效。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值或为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0})">
      <summary>创建一个延续任务，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值或为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken)">
      <summary>创建一个延续任务，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。- 或 -<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组参数为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值。- 或 -<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续任务，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <param name="continuationOptions">控制所创建的延续任务的行为的枚举值之一。NotOn* 或 OnlyOn* 值均无效。</param>
      <param name="scheduler">用于计划所创建的延续任务的任务计划程序。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。- 或 -<paramref name="scheduler" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值。- 或 -<paramref name="tasks" /> 数组为空。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值无效。</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。- 或 -<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续任务，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="continuationOptions">控制所创建的延续任务的行为的枚举值之一。NotOn* 或 OnlyOn* 值均无效。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的枚举值无效。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值。- 或 -<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0})">
      <summary>创建一个延续任务，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值。- 或 -<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken)">
      <summary>创建一个延续任务，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续任务。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。- 或 -<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值。- 或 -<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个延续任务，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="cancellationToken">将指派给新的延续任务的取消标记。</param>
      <param name="continuationOptions">控制所创建的延续任务的行为的枚举值之一。NotOn* 或 OnlyOn* 值均无效。</param>
      <param name="scheduler">用于计划所创建的延续 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。- 或 -<paramref name="scheduler" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值。- 或 -<paramref name="tasks" /> 数组为空。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的 TaskContinuationOptions 值无效。</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。- 或 -<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>创建一个延续任务，它将在提供的组中的任何任务完成后马上开始。</summary>
      <returns>新的延续 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">在一个任务完成时继续执行的任务所在的数组。</param>
      <param name="continuationFunction">在 <paramref name="tasks" /> 数组中的一个任务完成时要异步执行的函数委托。</param>
      <param name="continuationOptions">控制所创建的延续任务的行为的枚举值之一。NotOn* 或 OnlyOn* 值均无效。</param>
      <typeparam name="TAntecedentResult">以前的 <paramref name="tasks" /> 结果的类型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 数组中的某个元素已被释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 数组为 null。- 或 -<paramref name="continuationFunction" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 参数指定的枚举值无效。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值。- 或 -<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CreationOptions">
      <summary>获取此任务工厂的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 枚举值。</summary>
      <returns>指定此任务工厂的默认创建选项的枚举值之一。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object)">
      <summary>创建一个任务，它表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的任务。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 参数为 null。- 或 -<paramref name="endMethod" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个任务，它表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">控制所创建的任务的行为的枚举值之一。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 参数为 null。- 或 -<paramref name="endMethod" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的值无效。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object)">
      <summary>创建一个任务，它表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的任务。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 参数为 null。- 或 -<paramref name="endMethod" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个任务，它表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的任务。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">控制所创建的任务的行为的枚举值之一。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 参数为 null。- 或 -<paramref name="endMethod" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的值无效。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object)">
      <summary>创建一个任务，它表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的任务。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 参数为 null。- 或 -<paramref name="endMethod" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个任务，它表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的任务。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">一个对象，用于控制所创建的 <see cref="T:System.Threading.Tasks.Task`1" /> 的行为。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 参数为 null。- 或 -<paramref name="endMethod" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的值无效。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object)">
      <summary>创建一个任务，它表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的任务。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="arg3">传递给 <paramref name="beginMethod" /> 委托的第三个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第三个参数的类型。</typeparam>
      <typeparam name="TArg3">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 参数为 null。- 或 -<paramref name="endMethod" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个任务，它表示符合异步编程模型模式的成对的开始和结束方法。</summary>
      <returns>创建的表示异步操作的任务。</returns>
      <param name="beginMethod">用于启动异步操作的委托。</param>
      <param name="endMethod">用于结束异步操作的委托。</param>
      <param name="arg1">传递给 <paramref name="beginMethod" /> 委托的第一个参数。</param>
      <param name="arg2">传递给 <paramref name="beginMethod" /> 委托的第二个参数。</param>
      <param name="arg3">传递给 <paramref name="beginMethod" /> 委托的第三个参数。</param>
      <param name="state">一个包含由 <paramref name="beginMethod" /> 委托使用的数据的对象。</param>
      <param name="creationOptions">一个对象，用于控制所创建的任务的行为。</param>
      <typeparam name="TArg1">传递给 <paramref name="beginMethod" /> 委托的第二个参数的类型。</typeparam>
      <typeparam name="TArg2">传递给 <paramref name="beginMethod" /> 委托的第三个参数的类型。</typeparam>
      <typeparam name="TArg3">传递给 <paramref name="beginMethod" /> 委托的第一个参数的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 参数为 null。- 或 -<paramref name="endMethod" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的值无效。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0})">
      <summary>创建一个任务，它在指定的 <see cref="T:System.IAsyncResult" /> 完成时执行一个结束方法函数。</summary>
      <returns>一个表示异步操作的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" />，完成它时将触发对 <paramref name="endMethod" /> 的处理。</param>
      <param name="endMethod">用于处理完成的 <paramref name="asyncResult" /> 的函数委托。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 参数为 null。- 或 -<paramref name="endMethod" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建一个任务，它在指定的 <see cref="T:System.IAsyncResult" /> 完成时执行一个结束方法函数。</summary>
      <returns>表示异步操作的任务。</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" />，完成它时将触发对 <paramref name="endMethod" /> 的处理。</param>
      <param name="endMethod">用于处理完成的 <paramref name="asyncResult" /> 的函数委托。</param>
      <param name="creationOptions">控制所创建的任务的行为的枚举值之一。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 参数为 null。- 或 -<paramref name="endMethod" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的值无效。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建一个任务，它在指定的 <see cref="T:System.IAsyncResult" /> 完成时执行一个结束方法函数。</summary>
      <returns>创建的表示异步操作的任务。</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" />，完成它时将触发对 <paramref name="endMethod" /> 的处理。</param>
      <param name="endMethod">用于处理完成的 <paramref name="asyncResult" /> 的函数委托。</param>
      <param name="creationOptions">控制所创建的任务的行为的枚举值之一。</param>
      <param name="scheduler">用于计划将执行结束方法的任务计划程序。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 参数为 null。- 或 -<paramref name="endMethod" /> 参数为 null。- 或 -<paramref name="scheduler" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的值无效。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.Scheduler">
      <summary>获取此任务工厂的任务计划程序。</summary>
      <returns>此任务工厂的任务计划程序。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0})">
      <summary>创建并启动 任务。</summary>
      <returns>已启动的任务。</returns>
      <param name="function">一个函数委托，可返回能够通过任务获得的将来结果。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken)">
      <summary>创建并启动 任务。</summary>
      <returns>已启动的任务。</returns>
      <param name="function">一个函数委托，可返回能够通过任务获得的将来结果。</param>
      <param name="cancellationToken">将指派给新的任务的取消标记。</param>
      <exception cref="T:System.ObjectDisposedException">已处理创建的 <paramref name="cancellationToken" /> 的取消标记源。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建并启动 任务。</summary>
      <returns>已启动的任务。</returns>
      <param name="function">一个函数委托，可返回能够通过任务获得的将来结果。</param>
      <param name="cancellationToken">将指派给新的任务的取消标记。</param>
      <param name="creationOptions">控制所创建的任务的行为的枚举值之一。</param>
      <param name="scheduler">用于计划所创建的任务的任务计划程序。</param>
      <exception cref="T:System.ObjectDisposedException">已处理创建的 <paramref name="cancellationToken" /> 的取消标记源。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 参数为 null。- 或 -<paramref name="scheduler" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的值无效。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建并启动 任务。</summary>
      <returns>已启动的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">一个函数委托，可返回能够通过任务获得的将来结果。</param>
      <param name="creationOptions">控制所创建的任务的行为的枚举值之一。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的值无效。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object)">
      <summary>创建并启动 任务。</summary>
      <returns>已启动的任务。</returns>
      <param name="function">一个函数委托，可返回能够通过任务获得的将来结果。</param>
      <param name="state">包含 <paramref name="function" /> 委托所用数据的对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>创建并启动 任务。</summary>
      <returns>已启动的任务。</returns>
      <param name="function">一个函数委托，可返回能够通过任务获得的将来结果。</param>
      <param name="state">包含 <paramref name="function" /> 委托所用数据的对象。</param>
      <param name="cancellationToken">将指派给新的任务的取消标记。</param>
      <exception cref="T:System.ObjectDisposedException">已处理创建的 <paramref name="cancellationToken" /> 的取消标记源。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>创建并启动 任务。</summary>
      <returns>已启动的任务。</returns>
      <param name="function">一个函数委托，可返回能够通过任务获得的将来结果。</param>
      <param name="state">包含 <paramref name="function" /> 委托所用数据的对象。</param>
      <param name="cancellationToken">将指派给新的任务的取消标记。</param>
      <param name="creationOptions">控制所创建的任务的行为的枚举值之一。</param>
      <param name="scheduler">用于计划所创建的任务的任务计划程序。</param>
      <exception cref="T:System.ObjectDisposedException">已处理创建的 <paramref name="cancellationToken" /> 的取消标记源。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 参数为 null。- 或 -<paramref name="scheduler" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的值无效。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>创建并启动 任务。</summary>
      <returns>已启动的任务。</returns>
      <param name="function">一个函数委托，可返回能够通过任务获得的将来结果。</param>
      <param name="state">包含 <paramref name="function" /> 委托所用数据的对象。</param>
      <param name="creationOptions">控制所创建的任务的行为的枚举值之一。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 参数为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 参数指定的值无效。</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskScheduler">
      <summary>表示一个处理将任务排队到线程中的低级工作的对象。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.#ctor">
      <summary>初始化 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</summary>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Current">
      <summary>获取与当前正在执行的任务关联的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</summary>
      <returns>返回与当前正在执行的任务关联的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Default">
      <summary>获取由 .NET Framework 提供的默认 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 实例。</summary>
      <returns>返回默认的 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 实例。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext">
      <summary>创建一个与当前 <see cref="T:System.Threading.SynchronizationContext" /> 关联的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</summary>
      <returns>与由 <see cref="P:System.Threading.SynchronizationContext.Current" /> 确定的当前 <see cref="T:System.Threading.SynchronizationContext" /> 关联的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</returns>
      <exception cref="T:System.InvalidOperationException">当前的 SynchronizationContext 不能用作 TaskScheduler。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.GetScheduledTasks">
      <summary>仅对于调试器支持，生成当前排队到计划程序中等待执行的 <see cref="T:System.Threading.Tasks.Task" /> 实例的枚举。</summary>
      <returns>一个允许调试器遍历当前排队到此计划程序中的任务的枚举。</returns>
      <exception cref="T:System.NotSupportedException">此计划程序无法在此时生成排队任务的列表。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Id">
      <summary>获取此 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的唯一 ID。</summary>
      <returns>返回此 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的唯一 ID。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.MaximumConcurrencyLevel">
      <summary>指示此 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 能够支持的最大并发级别。</summary>
      <returns>返回表示最大并发级别的一个整数。默认计划程序返回 <see cref="F:System.Int32.MaxValue" />。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.QueueTask(System.Threading.Tasks.Task)">
      <summary>将 <see cref="T:System.Threading.Tasks.Task" /> 排队到计划程序中。</summary>
      <param name="task">要排队的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryDequeue(System.Threading.Tasks.Task)">
      <summary>尝试将以前排队到此计划程序中的 <see cref="T:System.Threading.Tasks.Task" /> 取消排队。</summary>
      <returns>一个布尔值，该值指示是否已成功地将 <paramref name="task" /> 参数取消排队。</returns>
      <param name="task">要取消排队的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 参数为 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTask(System.Threading.Tasks.Task)">
      <summary>尝试在此计划程序上执行提供的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>一个布尔值，如果成功执行了 <paramref name="task" />，则该值为 true；如果未成功执行，则该值为 false。执行失败的常见原因是，该任务先前已经执行或者位于正在由另一个线程执行的进程中。</returns>
      <param name="task">要执行的 <see cref="T:System.Threading.Tasks.Task" /> 对象。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="task" /> 与此计划程序无关联。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)">
      <summary>确定是否可以在此调用中同步执行提供的 <see cref="T:System.Threading.Tasks.Task" />，如果可以，将执行该任务。</summary>
      <returns>一个布尔值，该值指示是否已以内联方式执行该任务。</returns>
      <param name="task">要执行的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <param name="taskWasPreviouslyQueued">一个布尔值，该值指示任务之前是否已排队。如果此参数为 True，则该任务以前可能已排队（已计划）；如果为 False，则已知该任务尚未排队，此时将执行此调用，以便以内联方式执行该任务，而不用将其排队。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 参数为 null。</exception>
      <exception cref="T:System.InvalidOperationException">已执行的 <paramref name="task" />。</exception>
    </member>
    <member name="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException">
      <summary>当出错的任务的未观察到的异常将要触发异常升级策略时发生，默认情况下，这将终止进程。</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskSchedulerException">
      <summary>表示一个用于告知由 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 计划的某个操作无效的异常。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor">
      <summary>使用由系统提供的用来描述错误的消息初始化 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.Exception)">
      <summary>使用默认的错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 类的新实例。</summary>
      <param name="innerException">导致当前异常的异常。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String)">
      <summary>使用指定的描述错误的消息初始化 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 类的新实例。</summary>
      <param name="message">描述该异常的消息。此构造函数的调用方需要确保此字符串已针对当前系统区域性进行了本地化。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String,System.Exception)">
      <summary>使用指定错误消息和对作为此异常原因的内部异常的引用来初始化 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 类的新实例。</summary>
      <param name="message">描述该异常的消息。此构造函数的调用方需要确保此字符串已针对当前系统区域性进行了本地化。</param>
      <param name="innerException">导致当前异常的异常。如果 <paramref name="innerException" /> 参数不为 null，则当前异常将在处理内部异常的 catch 块中引发。</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskStatus">
      <summary>表示 <see cref="T:System.Threading.Tasks.Task" /> 的生命周期中的当前阶段。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Canceled">
      <summary>该任务已通过对其自身的 CancellationToken 引发 OperationCanceledException 对取消进行了确认，此时该标记处于已发送信号状态；或者在该任务开始执行之前，已向该任务的 CancellationToken 发出了信号。有关详细信息，请参阅任务取消。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Created">
      <summary>该任务已初始化，但尚未被计划。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Faulted">
      <summary>由于未处理异常的原因而完成的任务。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.RanToCompletion">
      <summary>已成功完成执行的任务。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Running">
      <summary>该任务正在运行，但尚未完成。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForActivation">
      <summary>该任务正在等待 .NET Framework 基础结构在内部将其激活并进行计划。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForChildrenToComplete">
      <summary>该任务已完成执行，正在隐式等待附加的子任务完成。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingToRun">
      <summary>该任务已被计划执行，但尚未开始执行。</summary>
    </member>
    <member name="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs">
      <summary>为在出错的 <see cref="T:System.Threading.Tasks.Task" /> 的异常未观察到时引发的事件提供数据。</summary>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.#ctor(System.AggregateException)">
      <summary>使用未观察到的异常初始化 <see cref="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs" /> 类的新实例。</summary>
      <param name="exception">未观察到的异常。</param>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception">
      <summary>未观察到的异常。</summary>
      <returns>未观察到的异常。</returns>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Observed">
      <summary>获取此异常是否已标记为“已观察到”。</summary>
      <returns>如果此异常已标记为“已观察到”，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.SetObserved">
      <summary>将 <see cref="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception" /> 标记为“已观察到”，这样将阻止该异常触发默认情况下会终止进程的异常升级策略。</summary>
    </member>
  </members>
</doc>