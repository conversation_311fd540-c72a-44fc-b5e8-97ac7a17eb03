﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading</name>
  </assembly>
  <members>
    <member name="T:System.Threading.AbandonedMutexException">
      <summary>Excepción que se produce cuando un subproceso adquiere un objeto <see cref="T:System.Threading.Mutex" /> que otro subproceso ha abandonado al salir sin liberarlo.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.AbandonedMutexException" /> con valores predeterminados.</summary>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.Int32,System.Threading.WaitHandle)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.AbandonedMutexException" /> con un índice especificado para la exclusión mutua abandonada, si es aplicable, y un objeto <see cref="T:System.Threading.Mutex" /> que representa la exclusión mutua.</summary>
      <param name="location">Índice de la exclusión mutua abandonada en la matriz de identificadores de espera si la excepción se produce para el método <see cref="Overload:System.Threading.WaitHandle.WaitAny" />, o –1 si la excepción se produce para los métodos <see cref="Overload:System.Threading.WaitHandle.WaitOne" /> o <see cref="Overload:System.Threading.WaitHandle.WaitAll" />.</param>
      <param name="handle">Objeto <see cref="T:System.Threading.Mutex" /> que representa la exclusión mutua abandonada.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.AbandonedMutexException" /> con el mensaje de error especificado.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.AbandonedMutexException" /> con un mensaje de error y una excepción interna especificados. </summary>
      <param name="message">Mensaje de error que explica la razón de la excepción.</param>
      <param name="inner">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="inner" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String,System.Exception,System.Int32,System.Threading.WaitHandle)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.AbandonedMutexException" /> con un mensaje de error especificado, la excepción interna, el índice para la exclusión mutua abandonada, si es aplicable, y un objeto <see cref="T:System.Threading.Mutex" /> que representa la exclusión mutua.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción.</param>
      <param name="inner">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="inner" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
      <param name="location">Índice de la exclusión mutua abandonada en la matriz de identificadores de espera si la excepción se produce para el método <see cref="Overload:System.Threading.WaitHandle.WaitAny" />, o –1 si la excepción se produce para los métodos <see cref="Overload:System.Threading.WaitHandle.WaitOne" /> o <see cref="Overload:System.Threading.WaitHandle.WaitAll" />.</param>
      <param name="handle">Objeto <see cref="T:System.Threading.Mutex" /> que representa la exclusión mutua abandonada.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String,System.Int32,System.Threading.WaitHandle)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.AbandonedMutexException" /> con un mensaje de error especificado, el índice de la exclusión mutua abandonada, si es aplicable, y la exclusión mutua abandonada. </summary>
      <param name="message">Mensaje de error que explica la razón de la excepción.</param>
      <param name="location">Índice de la exclusión mutua abandonada en la matriz de identificadores de espera si la excepción se produce para el método <see cref="Overload:System.Threading.WaitHandle.WaitAny" />, o –1 si la excepción se produce para los métodos <see cref="Overload:System.Threading.WaitHandle.WaitOne" /> o <see cref="Overload:System.Threading.WaitHandle.WaitAll" />.</param>
      <param name="handle">Objeto <see cref="T:System.Threading.Mutex" /> que representa la exclusión mutua abandonada.</param>
    </member>
    <member name="P:System.Threading.AbandonedMutexException.Mutex">
      <summary>Obtiene la exclusión mutua abandonada que produjo la excepción, si se conoce.</summary>
      <returns>Objeto <see cref="T:System.Threading.Mutex" /> que representa la exclusión mutua abandonada o null si no se han podido identificar las exclusiones mutuas abandonadas.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.AbandonedMutexException.MutexIndex">
      <summary>Obtiene el índice de la exclusión mutua abandonada que produjo la excepción, si se conoce.</summary>
      <returns>Índice, en la matriz de identificadores de espera que se ha pasado al método <see cref="Overload:System.Threading.WaitHandle.WaitAny" />, del objeto <see cref="T:System.Threading.Mutex" /> que representa la exclusión mutua abandonada, o –1 si no se puede determinar el índice de la exclusión mutua abandonada.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.AsyncLocal`1">
      <summary>Representa datos ambiente locales de un flujo de control asincrónico determinado, por ejemplo, un método asincrónico. </summary>
      <typeparam name="T">Tipo de los datos ambiente. </typeparam>
    </member>
    <member name="M:System.Threading.AsyncLocal`1.#ctor">
      <summary>Crea una instancia <see cref="T:System.Threading.AsyncLocal`1" /> que no recibe las notificaciones de cambio. </summary>
    </member>
    <member name="M:System.Threading.AsyncLocal`1.#ctor(System.Action{System.Threading.AsyncLocalValueChangedArgs{`0}})">
      <summary>Crea una instancia <see cref="T:System.Threading.AsyncLocal`1" /> local que recibe notificaciones de cambio. </summary>
      <param name="valueChangedHandler">Delegado al que se llama cuando cambia el valor actual en cualquier subproceso. </param>
    </member>
    <member name="P:System.Threading.AsyncLocal`1.Value">
      <summary>Obtiene o establece el valor de los datos ambiente. </summary>
      <returns>Valor de los datos ambiente. </returns>
    </member>
    <member name="T:System.Threading.AsyncLocalValueChangedArgs`1">
      <summary>Clase que proporciona información de cambio de datos a las instancias <see cref="T:System.Threading.AsyncLocal`1" /> que se registran para las notificaciones de cambios. </summary>
      <typeparam name="T">Tipo de los datos. </typeparam>
    </member>
    <member name="P:System.Threading.AsyncLocalValueChangedArgs`1.CurrentValue">
      <summary>Obtiene el valor actual de los datos. </summary>
      <returns>Valor actual de los datos. </returns>
    </member>
    <member name="P:System.Threading.AsyncLocalValueChangedArgs`1.PreviousValue">
      <summary>Obtiene el valor anterior de los datos.</summary>
      <returns>Valor anterior de los datos. </returns>
    </member>
    <member name="P:System.Threading.AsyncLocalValueChangedArgs`1.ThreadContextChanged">
      <summary>Devuelve un valor que indica si el valor cambia debido a un cambio de contexto de ejecución. </summary>
      <returns>true si el valor cambió debido a un cambio de contexto de ejecución; de lo contrario, false. </returns>
    </member>
    <member name="T:System.Threading.AutoResetEvent">
      <summary>Notifica que se ha producido un evento a un subproceso en espera.Esta clase no puede heredarse.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.AutoResetEvent.#ctor(System.Boolean)">
      <summary>Inicializa una instancia de la clase <see cref="T:System.Threading.AutoResetEvent" /> con un valor booleano que indica si hay que establecer el estado inicial en señalado.</summary>
      <param name="initialState">true para establecer el estado inicial en señalado; false para establecer el estado inicial en no señalado. </param>
    </member>
    <member name="T:System.Threading.Barrier">
      <summary>Habilita varias tareas para que cooperen en un algoritmo en paralelo a través de varias fases.</summary>
    </member>
    <member name="M:System.Threading.Barrier.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Barrier" />.</summary>
      <param name="participantCount">Número de subprocesos que participan.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> es menor que 0 o mayor que 32,767.</exception>
    </member>
    <member name="M:System.Threading.Barrier.#ctor(System.Int32,System.Action{System.Threading.Barrier})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Barrier" />.</summary>
      <param name="participantCount">Número de subprocesos que participan.</param>
      <param name="postPhaseAction">
        <see cref="T:System.Action`1" /> que se ejecutará después de cada fase. null (Nothing en Visual Basic) se puede pasar para indicar que no se realiza ninguna acción.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> es menor que 0 o mayor que 32,767.</exception>
    </member>
    <member name="M:System.Threading.Barrier.AddParticipant">
      <summary>Notifica a <see cref="T:System.Threading.Barrier" /> que va a haber un participante adicional.</summary>
      <returns>Número de fase de la barrera en la que primero participarán los nuevos participantes.</returns>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.InvalidOperationException">Agregar un participante haría que el recuento de participantes de la barrera superase los 32.767.O bienEl método se invocó desde dentro de una acción posterior a la fase.</exception>
    </member>
    <member name="M:System.Threading.Barrier.AddParticipants(System.Int32)">
      <summary>Notifica a <see cref="T:System.Threading.Barrier" /> que va a haber participantes adicionales.</summary>
      <returns>Número de fase de la barrera en la que primero participarán los nuevos participantes.</returns>
      <param name="participantCount">Número de participantes adicionales que se van a agregar a la barrera.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> es menor que 0.O bienAgregar <paramref name="participantCount" /> haría que el recuento de participantes de la barrera superase los 32.767.</exception>
      <exception cref="T:System.InvalidOperationException">El método se invocó desde dentro de una acción posterior a la fase.</exception>
    </member>
    <member name="P:System.Threading.Barrier.CurrentPhaseNumber">
      <summary>Obtiene el número de la fase actual de la barrera.</summary>
      <returns>Devuelve el número de la fase actual de la barrera.</returns>
    </member>
    <member name="M:System.Threading.Barrier.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Threading.Barrier" />.</summary>
      <exception cref="T:System.InvalidOperationException">El método se invocó desde dentro de una acción posterior a la fase.</exception>
    </member>
    <member name="M:System.Threading.Barrier.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados utilizados por el objeto <see cref="T:System.Threading.Barrier" /> y, de forma opcional, libera los recursos administrados.</summary>
      <param name="disposing">Es true para liberar tanto los recursos administrados como los no administrados; es false para liberar únicamente los recursos no administrados.</param>
    </member>
    <member name="P:System.Threading.Barrier.ParticipantCount">
      <summary>Obtiene el número total de participantes de la barrera.</summary>
      <returns>Devuelve el número total de participantes de la barrera.</returns>
    </member>
    <member name="P:System.Threading.Barrier.ParticipantsRemaining">
      <summary>Obtiene el número de participantes de la barrera que no aún no se han señalado en la fase actual.</summary>
      <returns>Devuelve el número de participantes de la barrera que no aún no se han señalado en la fase actual.</returns>
    </member>
    <member name="M:System.Threading.Barrier.RemoveParticipant">
      <summary>Notifica a <see cref="T:System.Threading.Barrier" /> que va a haber un participante menos.</summary>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.InvalidOperationException">La barrera ya tiene 0 participantes.O bienEl método se invocó desde dentro de una acción posterior a la fase.</exception>
    </member>
    <member name="M:System.Threading.Barrier.RemoveParticipants(System.Int32)">
      <summary>Notifica a <see cref="T:System.Threading.Barrier" /> que va a haber menos participantes.</summary>
      <param name="participantCount">Número de participantes adicionales que se van a quitar de la barrera.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> es menor que 0.</exception>
      <exception cref="T:System.InvalidOperationException">La barrera ya tiene 0 participantes.O bienEl método se invocó desde dentro de una acción posterior a la fase. O bienel recuento del participante actual es menor que el participantCount especificado</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El recuento del participante total es menor que el <paramref name=" participantCount" /> especificado</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait">
      <summary>Señala que un participante ha alcanzado la barrera y espera a que todos los demás participantes alcancen también la barrera.</summary>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.InvalidOperationException">El método se invocó desde dentro de una acción posterior a la fase, la barrera tiene actualmente 0 participantes, o la barrera la señalan más subprocesos de los que están registrados como participantes.</exception>
      <exception cref="T:System.Threading.BarrierPostPhaseException">Si una excepción se produce de la acción de fase de envío de una barrera después de todos los subprocesos hayan llamado a SignalAndWait, la excepción se ajustará en una BarrierPostPhaseException y se producirá en todos los subprocesos que participan.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.Int32)">
      <summary>Señala que un participante ha alcanzado la barrera y espera a que todos los demás participantes alcancen también la barrera usando un entero de 32 bits con signo para medir el tiempo de espera.</summary>
      <returns>si todos los participantes alcanzaron la barrera dentro del tiempo especificado; de lo contrario, es false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1 que representa un tiempo de espera infinito.</exception>
      <exception cref="T:System.InvalidOperationException">El método se invocó desde dentro de una acción posterior a la fase, la barrera tiene actualmente 0 participantes, o la barrera la señalan más subprocesos de los que están registrados como participantes.</exception>
      <exception cref="T:System.Threading.BarrierPostPhaseException">Si una excepción se produce de la acción de fase de envío de una barrera después de todos los subprocesos hayan llamado a SignalAndWait, la excepción se ajustará en una BarrierPostPhaseException y se producirá en todos los subprocesos que participan.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.Int32,System.Threading.CancellationToken)">
      <summary>Señala que un participante ha alcanzado la barrera y espera a que todos los demás participantes la alcancen también usando un entero de 32 bits con signo para medir el tiempo de espera mientras se observa un token de cancelación.</summary>
      <returns>si todos los participantes alcanzaron la barrera dentro del tiempo especificado; de lo contrario, es false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.OperationCanceledException">Se ha cancelado <paramref name="cancellationToken" />.</exception>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1 que representa un tiempo de espera infinito.</exception>
      <exception cref="T:System.InvalidOperationException">El método se invocó desde dentro de una acción posterior a la fase, la barrera tiene actualmente 0 participantes, o la barrera la señalan más subprocesos de los que están registrados como participantes.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.Threading.CancellationToken)">
      <summary>Señala que un participante ha alcanzado la barrera y espera a que todos los demás participantes alcancen la barrera mientras se observa un token de cancelación.</summary>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.OperationCanceledException">Se ha cancelado <paramref name="cancellationToken" />.</exception>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.InvalidOperationException">El método se invocó desde dentro de una acción posterior a la fase, la barrera tiene actualmente 0 participantes, o la barrera la señalan más subprocesos de los que están registrados como participantes.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.TimeSpan)">
      <summary>Señala que un participante ha alcanzado la barrera y espera a que todos los demás participantes alcancen también la barrera usando un objeto <see cref="T:System.TimeSpan" /> para medir el intervalo de tiempo.</summary>
      <returns>Es true si todos los demás participantes alcanzaron la barrera; de lo contrario, es false.</returns>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> es un número negativo distinto de -1 milisegundo, que representa un tiempo de espera infinito, o es mayor de 32.767.</exception>
      <exception cref="T:System.InvalidOperationException">El método se invocó desde dentro de una acción posterior a la fase, la barrera tiene actualmente 0 participantes, o la barrera la señalan más subprocesos de los que están registrados como participantes.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Señala que un participante ha alcanzado la barrera y espera a que todos los demás participantes la alcancen también usando un objeto <see cref="T:System.TimeSpan" /> para medir el intervalo de tiempo, mientras se observa un token de cancelación.</summary>
      <returns>Es true si todos los demás participantes alcanzaron la barrera; de lo contrario, es false.</returns>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.OperationCanceledException">Se ha cancelado <paramref name="cancellationToken" />.</exception>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> es un número negativo distinto de -1 milisegundo que representa un tiempo de espera infinito.</exception>
      <exception cref="T:System.InvalidOperationException">El método se invocó desde dentro de una acción posterior a la fase, la barrera tiene actualmente 0 participantes, o la barrera la señalan más subprocesos de los que están registrados como participantes.</exception>
    </member>
    <member name="T:System.Threading.BarrierPostPhaseException">
      <summary>Excepción que se inicia cuando se produce un error en la acción posterior a la fase de <see cref="T:System.Threading.Barrier" /></summary>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.BarrierPostPhaseException" /> con un mensaje proporcionado por el sistema que describe el error.</summary>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor(System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.BarrierPostPhaseException" /> con la excepción interna especificada.</summary>
      <param name="innerException">La excepción que es la causa de la excepción actual.</param>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.BarrierPostPhaseException" /> con un mensaje de error especificado que describe el error.</summary>
      <param name="message">Mensaje que describe la excepción.El llamador de este constructor debe asegurarse de que esta cadena se ha traducido para la actual referencia cultural del sistema.</param>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.BarrierPostPhaseException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje que describe la excepción.El llamador de este constructor debe asegurarse de que esta cadena se ha traducido para la actual referencia cultural del sistema.</param>
      <param name="innerException">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="innerException" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="T:System.Threading.ContextCallback">
      <summary>Representa un método al que se va a llamar dentro de un nuevo contexto.  </summary>
      <param name="state">Objeto que contiene la información que va a utilizar el método de devolución de llamadas cada vez que se ejecute.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.CountdownEvent">
      <summary>Representa una primitiva de sincronización que está señalada cuando su recuento alcanza el valor cero.</summary>
    </member>
    <member name="M:System.Threading.CountdownEvent.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.CountdownEvent" /> con el recuento especificado.</summary>
      <param name="initialCount">Número de señales necesarias inicialmente para establecer <see cref="T:System.Threading.CountdownEvent" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialCount" /> es menor que 0.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.AddCount">
      <summary>Incrementa en uno el recuento actual de <see cref="T:System.Threading.CountdownEvent" />.</summary>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.InvalidOperationException">La instancia actual ya está establecida.O bien<see cref="P:System.Threading.CountdownEvent.CurrentCount" /> es mayor o igual que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.AddCount(System.Int32)">
      <summary>Incrementa en un valor especificado el recuento actual de <see cref="T:System.Threading.CountdownEvent" />.</summary>
      <param name="signalCount">Valor en que se va a aumentar <see cref="P:System.Threading.CountdownEvent.CurrentCount" />.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signalCount" /> es menor o igual que 0.</exception>
      <exception cref="T:System.InvalidOperationException">La instancia actual ya está establecida.O bien<see cref="P:System.Threading.CountdownEvent.CurrentCount" /> es igual o mayor que <see cref="F:System.Int32.MaxValue" /> después de incrementar la cuenta en <paramref name="signalCount." /></exception>
    </member>
    <member name="P:System.Threading.CountdownEvent.CurrentCount">
      <summary>Obtiene el número de señales restantes necesario para establecer el evento.</summary>
      <returns> El número de señales restantes necesario para establecer el evento.</returns>
    </member>
    <member name="M:System.Threading.CountdownEvent.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Threading.CountdownEvent" />.</summary>
    </member>
    <member name="M:System.Threading.CountdownEvent.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados utilizados por el objeto <see cref="T:System.Threading.CountdownEvent" /> y, de forma opcional, libera los recursos administrados.</summary>
      <param name="disposing">Es true para liberar tanto los recursos administrados como los no administrados; es false para liberar únicamente los recursos no administrados.</param>
    </member>
    <member name="P:System.Threading.CountdownEvent.InitialCount">
      <summary>Obtiene los números de señales que se necesitan inicialmente para establecer el evento.</summary>
      <returns> El número de señales que se necesitan inicialmente para establecer el evento.</returns>
    </member>
    <member name="P:System.Threading.CountdownEvent.IsSet">
      <summary>Determina si se establece el evento.</summary>
      <returns>Es true si se establece el evento; de lo contrario, es false.</returns>
    </member>
    <member name="M:System.Threading.CountdownEvent.Reset">
      <summary>Restablece <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> en el valor de <see cref="P:System.Threading.CountdownEvent.InitialCount" />.</summary>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Reset(System.Int32)">
      <summary>Restablece la propiedad <see cref="P:System.Threading.CountdownEvent.InitialCount" /> según un valor especificado.</summary>
      <param name="count">Número de señales necesario para establecer <see cref="T:System.Threading.CountdownEvent" />.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="count" /> es menor que 0.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Signal">
      <summary>Registra una señal con <see cref="T:System.Threading.CountdownEvent" /> y disminuye el valor de <see cref="P:System.Threading.CountdownEvent.CurrentCount" />.</summary>
      <returns>Es true si la señal hizo que el recuento alcanzara el valor cero y se estableció el evento; de lo contrario, falso.</returns>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.InvalidOperationException">La instancia actual ya está establecida.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Signal(System.Int32)">
      <summary>Registra varias señales con <see cref="T:System.Threading.CountdownEvent" /> reduciendo el valor de <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> según la cantidad especificada.</summary>
      <returns>Es true si las señales hicieron que el recuento alcanzara el valor cero y se estableció el evento; de lo contrario, falso.</returns>
      <param name="signalCount">Número de señales que se va a registrar.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signalCount" /> es menor que 1.</exception>
      <exception cref="T:System.InvalidOperationException">La instancia actual ya está establecida. -o bien- <paramref name="signalCount" /> es mayor que <see cref="P:System.Threading.CountdownEvent.CurrentCount" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.TryAddCount">
      <summary>Intenta incrementar <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> en uno.</summary>
      <returns>Es true si el incremento se realizó correctamente; en caso contrario, es false.Si <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> ya está en el valor cero, este método devolverá false.</returns>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> es igual a <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.TryAddCount(System.Int32)">
      <summary>Intenta incrementar <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> en un valor especificado.</summary>
      <returns>Es true si el incremento se realizó correctamente; en caso contrario, es false.Si <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> ya está en el valor cero, se devolverá false.</returns>
      <param name="signalCount">Valor en que se va a aumentar <see cref="P:System.Threading.CountdownEvent.CurrentCount" />.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signalCount" /> es menor o igual que 0.</exception>
      <exception cref="T:System.InvalidOperationException">La instancia actual ya está establecida.O bien<see cref="P:System.Threading.CountdownEvent.CurrentCount" /> + <paramref name="signalCount" /> es igual o mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait">
      <summary>Bloquea el subproceso actual hasta que se establezca el objeto <see cref="T:System.Threading.CountdownEvent" />.</summary>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.Int32)">
      <summary>Bloquea el subproceso actual hasta que se establezca el objeto <see cref="T:System.Threading.CountdownEvent" />, usando un entero de 32 bits con signo para medir el tiempo de espera.</summary>
      <returns>Es true si se estableció el objeto <see cref="T:System.Threading.CountdownEvent" />; de lo contrario, es false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1 que representa un tiempo de espera infinito.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Bloquea el subproceso actual hasta que se establezca el objeto <see cref="T:System.Threading.CountdownEvent" />, usando un entero de 32 bits con signo para medir el tiempo de espera, mientras se observa un token <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Es true si se estableció el objeto <see cref="T:System.Threading.CountdownEvent" />; de lo contrario, es false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.OperationCanceledException">Se ha cancelado <paramref name="cancellationToken" />.</exception>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado. o bien, <see cref="T:System.Threading.CancellationTokenSource" /> que creó <paramref name="cancellationToken" /> sido eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1 que representa un tiempo de espera infinito.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.Threading.CancellationToken)">
      <summary>Bloquea el subproceso actual hasta que se establezca el objeto <see cref="T:System.Threading.CountdownEvent" />, mientras se observa un token <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.OperationCanceledException">Se ha cancelado <paramref name="cancellationToken" />.</exception>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado. o bien, <see cref="T:System.Threading.CancellationTokenSource" /> que creó <paramref name="cancellationToken" /> sido eliminado.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.TimeSpan)">
      <summary>Bloquea el subproceso actual hasta que se establezca el objeto <see cref="T:System.Threading.CountdownEvent" />, usando un objeto <see cref="T:System.TimeSpan" /> para medir el tiempo de espera.</summary>
      <returns>Es true si se estableció el objeto <see cref="T:System.Threading.CountdownEvent" />; de lo contrario, es false.</returns>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> es un número negativo distinto de -1 milisegundo, que representa un tiempo de espera infinito, o el tiempo de espera es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Bloquea el subproceso actual hasta que se establezca el objeto <see cref="T:System.Threading.CountdownEvent" />, usando un objeto <see cref="T:System.TimeSpan" /> para medir el tiempo de espera, mientras se observa un token <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Es true si se estableció el objeto <see cref="T:System.Threading.CountdownEvent" />; de lo contrario, es false.</returns>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.OperationCanceledException">Se ha cancelado <paramref name="cancellationToken" />.</exception>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado. o bien, <see cref="T:System.Threading.CancellationTokenSource" /> que creó <paramref name="cancellationToken" /> sido eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> es un número negativo distinto de -1 milisegundo, que representa un tiempo de espera infinito, o el tiempo de espera es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="P:System.Threading.CountdownEvent.WaitHandle">
      <summary>Obtiene un objeto <see cref="T:System.Threading.WaitHandle" /> que se usa para esperar a que se establezca el evento.</summary>
      <returns>Objeto <see cref="T:System.Threading.WaitHandle" /> que se usa para esperar a que se establezca el evento.</returns>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
    </member>
    <member name="T:System.Threading.EventResetMode">
      <summary>Indica si un objeto <see cref="T:System.Threading.EventWaitHandle" /> se restablece automática o manualmente después de recibir una señal.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Threading.EventResetMode.AutoReset">
      <summary>El objeto <see cref="T:System.Threading.EventWaitHandle" />, cuando está señalado, se restablece automáticamente después de haber liberado un único subproceso.Si hay ningún subproceso en espera, el objeto <see cref="T:System.Threading.EventWaitHandle" /> permanece señalado hasta que un subproceso se bloquea y se restablece después de haber liberado el subproceso.</summary>
    </member>
    <member name="F:System.Threading.EventResetMode.ManualReset">
      <summary>El objeto <see cref="T:System.Threading.EventWaitHandle" />, cuando está señalado, libera todos los subprocesos en espera y permanece señalado hasta que se restablece manualmente.</summary>
    </member>
    <member name="T:System.Threading.EventWaitHandle">
      <summary>Representa un evento de sincronización de subprocesos.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.EventWaitHandle.#ctor(System.Boolean,System.Threading.EventResetMode)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.EventWaitHandle" />, especificando si el identificador de espera se señala inicialmente y si se restablece automática o manualmente.</summary>
      <param name="initialState">Es true para establecer el estado inicial en señalado; es false para establecerlo en no señalado.</param>
      <param name="mode">Uno de los valores de <see cref="T:System.Threading.EventResetMode" /> que determina si el evento se restablece de forma automática o manual.</param>
    </member>
    <member name="M:System.Threading.EventWaitHandle.#ctor(System.Boolean,System.Threading.EventResetMode,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.EventWaitHandle" />, especificando si el identificador de espera se señala inicialmente cuando se crea como resultado de esta llamada, si se restablece automática o manualmente y el nombre de un evento de sincronización del sistema.</summary>
      <param name="initialState">Es true para establecer el estado inicial en señalado si el evento con nombre se crea como resultado de esta llamada; es false para establecerlo en no señalado.</param>
      <param name="mode">Uno de los valores de <see cref="T:System.Threading.EventResetMode" /> que determina si el evento se restablece de forma automática o manual.</param>
      <param name="name">Nombre de un evento de sincronización para todo el sistema.</param>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El evento con nombre existe y tiene seguridad de control de acceso, pero el usuario carece de <see cref="F:System.Security.AccessControl.EventWaitHandleRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">No se puede crear el evento con nombre, quizás porque un identificador de espera de un tipo diferente tiene el mismo nombre.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> tiene más de 260 caracteres.</exception>
    </member>
    <member name="M:System.Threading.EventWaitHandle.#ctor(System.Boolean,System.Threading.EventResetMode,System.String,System.Boolean@)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.EventWaitHandle" />, especificando si el identificador de espera se señala inicialmente cuando se crea como resultado de esta llamada, si se restablece automática o manualmente, el nombre de un evento de sincronización del sistema y una variable booleana cuyo valor después de la llamada indica si se ha creado el evento del sistema con nombre.</summary>
      <param name="initialState">Es true para establecer el estado inicial en señalado si el evento con nombre se crea como resultado de esta llamada; es false para establecerlo en no señalado.</param>
      <param name="mode">Uno de los valores de <see cref="T:System.Threading.EventResetMode" /> que determina si el evento se restablece de forma automática o manual.</param>
      <param name="name">Nombre de un evento de sincronización para todo el sistema.</param>
      <param name="createdNew">Cuando este método devuelve un resultado, contiene true si se ha creado un evento local (es decir, si <paramref name="name" /> es null o una cadena vacía) o si se ha creado el evento del sistema con nombre especificado; es false si el evento del sistema con nombre especificado ya existía.Este parámetro se pasa sin inicializar.</param>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El evento con nombre existe y tiene seguridad de control de acceso, pero el usuario carece de <see cref="F:System.Security.AccessControl.EventWaitHandleRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">No se puede crear el evento con nombre, quizás porque un identificador de espera de un tipo diferente tiene el mismo nombre.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> tiene más de 260 caracteres.</exception>
    </member>
    <member name="M:System.Threading.EventWaitHandle.OpenExisting(System.String)">
      <summary>Abre el evento de sincronización con nombre especificado, si ya existe.</summary>
      <returns>Un objeto que representa el evento del sistema con nombre.</returns>
      <param name="name">Nombre del evento de sincronización que se va a abrir.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> es una cadena vacía. O bien<paramref name="name" /> tiene más de 260 caracteres.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> es null.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">El evento del sistema con nombre no existe.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El evento con nombre existe, pero el usuario no tiene el acceso de seguridad exigido para utilizarlo.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Threading.EventWaitHandle.Reset">
      <summary>Establece el estado del evento en no señalado, haciendo que los subprocesos se bloqueen.</summary>
      <returns>true si la operación se realiza correctamente; en caso contrario, false.</returns>
      <exception cref="T:System.ObjectDisposedException">No se ha llamado previamente al método <see cref="M:System.Threading.EventWaitHandle.Close" /> en este <see cref="T:System.Threading.EventWaitHandle" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.EventWaitHandle.Set">
      <summary>Establece el estado del evento en señalado, permitiendo que uno o varios subprocesos en espera continúen.</summary>
      <returns>true si la operación se realiza correctamente; en caso contrario, false.</returns>
      <exception cref="T:System.ObjectDisposedException">No se ha llamado previamente al método <see cref="M:System.Threading.EventWaitHandle.Close" /> en este <see cref="T:System.Threading.EventWaitHandle" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.EventWaitHandle.TryOpenExisting(System.String,System.Threading.EventWaitHandle@)">
      <summary>Abre el evento de sincronización con nombre especificado, si ya existe, y devuelve un valor que indica si la operación se realizó correctamente.</summary>
      <returns>true si el evento de sincronización con nombre se abrió correctamente; si no, false.</returns>
      <param name="name">Nombre del evento de sincronización que se va a abrir.</param>
      <param name="result">Cuando este método vuelve, contiene un objeto <see cref="T:System.Threading.EventWaitHandle" /> que representa el evento de sincronización con nombre si la llamada se realizó correctamente, o null si se produjo un error en la llamada.Este parámetro se trata como sin inicializar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> es una cadena vacía.O bien<paramref name="name" /> tiene más de 260 caracteres.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> es null.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El evento con nombre existe, pero el usuario no tiene el acceso de seguridad deseado.</exception>
    </member>
    <member name="T:System.Threading.ExecutionContext">
      <summary>Administra el contexto de ejecución del subproceso actual.Esta clase no puede heredarse.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ExecutionContext.Capture">
      <summary>Captura el contexto de ejecución del subproceso actual.</summary>
      <returns>Objeto <see cref="T:System.Threading.ExecutionContext" /> que representa el contexto de ejecución del subproceso actual.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.ExecutionContext.Run(System.Threading.ExecutionContext,System.Threading.ContextCallback,System.Object)">
      <summary>Ejecuta un método en un contexto de ejecución especificado en el subproceso actual.</summary>
      <param name="executionContext">Contexto de ejecución <see cref="T:System.Threading.ExecutionContext" /> que se va a establecer.</param>
      <param name="callback">Delegado <see cref="T:System.Threading.ContextCallback" /> que representa el método que se va a ejecutar en el contexto de ejecución proporcionado.</param>
      <param name="state">Objeto que se pasa al método de devolución de llamada.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="executionContext" /> es null.O bien<paramref name="executionContext" /> no se adquirió a través de una operación de captura. O bien<paramref name="executionContext" /> ya se ha utilizado como argumento de una llamada a <see cref="M:System.Threading.ExecutionContext.Run(System.Threading.ExecutionContext,System.Threading.ContextCallback,System.Object)" />.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Infrastructure" />
      </PermissionSet>
    </member>
    <member name="T:System.Threading.Interlocked">
      <summary>Proporciona operaciones atómicas para las variables compartidas por varios subprocesos. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Add(System.Int32@,System.Int32)">
      <summary>Agrega dos enteros de 32 bits y reemplaza el primer entero por la suma, como una operación atómica.</summary>
      <returns>Nuevo valor almacenado en <paramref name="location1" />.</returns>
      <param name="location1">Variable que contiene el primer valor que se va a agregar.La suma de los dos valores se almacena en <paramref name="location1" />.</param>
      <param name="value">Valor que se va a agregar al entero en <paramref name="location1" />.</param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Add(System.Int64@,System.Int64)">
      <summary>Agrega dos enteros de 64 bits y reemplaza el primer entero por la suma, como una operación atómica.</summary>
      <returns>Nuevo valor almacenado en <paramref name="location1" />.</returns>
      <param name="location1">Variable que contiene el primer valor que se va a agregar.La suma de los dos valores se almacena en <paramref name="location1" />.</param>
      <param name="value">Valor que se va a agregar al entero en <paramref name="location1" />.</param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Double@,System.Double,System.Double)">
      <summary>Compara dos números de punto flotante de precisión doble para comprobar si son iguales y, si lo son, reemplaza el primero de los valores.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Destino, cuyo valor se compara con <paramref name="comparand" /> y que posiblemente se reemplace. </param>
      <param name="value">Valor que reemplaza el valor de destino si la comparación da como resultado una igualdad. </param>
      <param name="comparand">Valor que se compara con el valor que hay en <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Int32@,System.Int32,System.Int32)">
      <summary>Compara dos enteros de 32 bits con signo para comprobar si son iguales y, si lo son, reemplaza el primer valor.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Destino, cuyo valor se compara con <paramref name="comparand" /> y que posiblemente se reemplace. </param>
      <param name="value">Valor que reemplaza el valor de destino si la comparación da como resultado una igualdad. </param>
      <param name="comparand">Valor que se compara con el valor que hay en <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Int64@,System.Int64,System.Int64)">
      <summary>Compara dos enteros de 64 bits con signo para comprobar si son iguales y, si lo son, reemplaza el primer valor.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Destino, cuyo valor se compara con <paramref name="comparand" /> y que posiblemente se reemplace. </param>
      <param name="value">Valor que reemplaza el valor de destino si la comparación da como resultado una igualdad. </param>
      <param name="comparand">Valor que se compara con el valor que hay en <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.IntPtr@,System.IntPtr,System.IntPtr)">
      <summary>Compara dos identificadores o punteros específicos de plataforma para comprobar si son iguales y, si lo son, reemplaza el primero.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Estructura <see cref="T:System.IntPtr" /> de destino, cuyo valor se compara con el valor de <paramref name="comparand" /> y que posiblemente se reemplace por <paramref name="value" />. </param>
      <param name="value">Estructura <see cref="T:System.IntPtr" /> que reemplaza el valor de destino si la comparación da como resultado una igualdad. </param>
      <param name="comparand">Estructura <see cref="T:System.IntPtr" /> que se compara con el valor que hay en <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Object@,System.Object,System.Object)">
      <summary>Compara dos objetos para comprobar si sus referencias son iguales y, si lo son, reemplaza el primero de los objetos.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Objeto de destino que se compara con <paramref name="comparand" /> y que posiblemente se reemplace. </param>
      <param name="value">Objeto que reemplaza el objeto de destino si la comparación da como resultado la igualdad de ambos parámetros. </param>
      <param name="comparand">Objeto que se compara con el objeto que hay en <paramref name="location1" />. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Single@,System.Single,System.Single)">
      <summary>Compara dos números de punto flotante de precisión sencilla para comprobar si son iguales y, si lo son, reemplaza el primero de los valores.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Destino, cuyo valor se compara con <paramref name="comparand" /> y que posiblemente se reemplace. </param>
      <param name="value">Valor que reemplaza el valor de destino si la comparación da como resultado una igualdad. </param>
      <param name="comparand">Valor que se compara con el valor que hay en <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange``1(``0@,``0,``0)">
      <summary>Compara dos instancias del tipo de referencia especificado <paramref name="T" /> para comprobar si son iguales y, si lo son, reemplaza la primera.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Destino, cuyo valor se compara con <paramref name="comparand" /> y que posiblemente se reemplace.Este es un parámetro de referencia (ref en C#, ByRef en Visual Basic).</param>
      <param name="value">Valor que reemplaza el valor de destino si la comparación da como resultado una igualdad. </param>
      <param name="comparand">Valor que se compara con el valor que hay en <paramref name="location1" />. </param>
      <typeparam name="T">Tipo que se va a utilizar para <paramref name="location1" />, <paramref name="value" /> y <paramref name="comparand" />.Este tipo debe ser un tipo de referencia.</typeparam>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
    </member>
    <member name="M:System.Threading.Interlocked.Decrement(System.Int32@)">
      <summary>Disminuye el valor de una variable especificada y almacena el resultado, como una operación atómica.</summary>
      <returns>Valor reducido.</returns>
      <param name="location">Variable cuyo valor se va a reducir. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Decrement(System.Int64@)">
      <summary>Disminuye el valor de la variable especificada y almacena el resultado, como una operación atómica.</summary>
      <returns>Valor reducido.</returns>
      <param name="location">Variable cuyo valor se va a reducir. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Double@,System.Double)">
      <summary>Establece un número de punto flotante de precisión doble en un valor especificado y devuelve el valor original, como una operación atómica.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Variable que se va a establecer en el valor especificado. </param>
      <param name="value">Valor en el que está establecido el parámetro <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Int32@,System.Int32)">
      <summary>Establece un entero de 32 bits con signo en un valor especificado y devuelve el valor original, como una operación atómica.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Variable que se va a establecer en el valor especificado. </param>
      <param name="value">Valor en el que está establecido el parámetro <paramref name="location1" />. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Int64@,System.Int64)">
      <summary>Establece un entero de 64 bits con signo en un valor especificado y devuelve el valor original, como una operación atómica.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Variable que se va a establecer en el valor especificado. </param>
      <param name="value">Valor en el que está establecido el parámetro <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.IntPtr@,System.IntPtr)">
      <summary>Establece un puntero o identificador específico de plataforma en un valor especificado y devuelve el valor original, como una operación atómica.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Variable que se va a establecer en el valor especificado. </param>
      <param name="value">Valor en el que está establecido el parámetro <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Object@,System.Object)">
      <summary>Establece un objeto en un valor especificado y devuelve una referencia al objeto original, como una operación atómica.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Variable que se va a establecer en el valor especificado. </param>
      <param name="value">Valor en el que está establecido el parámetro <paramref name="location1" />. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Single@,System.Single)">
      <summary>Establece un número de punto flotante de precisión sencilla en un valor especificado y devuelve el valor original, como una operación atómica.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Variable que se va a establecer en el valor especificado. </param>
      <param name="value">Valor en el que está establecido el parámetro <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange``1(``0@,``0)">
      <summary>Establece una variable del tipo <paramref name="T" /> especificado en un valor determinado y devuelve el valor original, como una operación atómica.</summary>
      <returns>Valor original de <paramref name="location1" />.</returns>
      <param name="location1">Variable que se va a establecer en el valor especificado.Este es un parámetro de referencia (ref en C#, ByRef en Visual Basic).</param>
      <param name="value">Valor en el que está establecido el parámetro <paramref name="location1" />. </param>
      <typeparam name="T">Tipo que se va a utilizar para <paramref name="location1" /> y <paramref name="value" />.Este tipo debe ser un tipo de referencia.</typeparam>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
    </member>
    <member name="M:System.Threading.Interlocked.Increment(System.Int32@)">
      <summary>Aumenta el valor de una variable especificada y almacena el resultado, como una operación atómica.</summary>
      <returns>Valor incrementado.</returns>
      <param name="location">Variable cuyo valor se va a incrementar. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Increment(System.Int64@)">
      <summary>Aumenta el valor de una variable especificada y almacena el resultado, como una operación atómica.</summary>
      <returns>Valor incrementado.</returns>
      <param name="location">Variable cuyo valor se va a incrementar. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.MemoryBarrier">
      <summary>Sincroniza el acceso a la memoria de la siguiente forma: el procesador que ejecuta el subproceso actual no puede reordenar instrucciones de forma que los accesos a la memoria anteriores a la llamada a <see cref="M:System.Threading.Interlocked.MemoryBarrier" /> se ejecuten después de los accesos a memoria que siguen a la llamada a <see cref="M:System.Threading.Interlocked.MemoryBarrier" />.</summary>
    </member>
    <member name="M:System.Threading.Interlocked.Read(System.Int64@)">
      <summary>Devuelve un valor de 64 bits, cargado como una operación atómica.</summary>
      <returns>Valor cargado.</returns>
      <param name="location">Valor de 64 bits que se va a cargar.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.LazyInitializer">
      <summary>Proporciona rutinas de inicialización diferida.</summary>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@)">
      <summary>Inicializa un tipo de referencia de destino con su constructor predeterminado si aún no se ha inicializado el destino.</summary>
      <returns>Referencia de tipo <paramref name="T" /> que se ha inicializado.</returns>
      <param name="target">Referencia de tipo <paramref name="T" /> que se va a inicializar si aún no se ha inicializado.</param>
      <typeparam name="T">Tipo de referencia que se va a inicializar.</typeparam>
      <exception cref="T:System.MemberAccessException">Faltaban los permisos para tener acceso al constructor de tipo <paramref name="T" />.</exception>
      <exception cref="T:System.MissingMemberException">El tipo <paramref name="T" /> no contiene un constructor predeterminado.</exception>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@,System.Boolean@,System.Object@)">
      <summary>Inicializa un tipo de referencia de destino o tipo de valor con su constructor predeterminado si aún no se ha inicializado.</summary>
      <returns>Valor inicializado de tipo <paramref name="T" />.</returns>
      <param name="target">Referencia o valor de tipo <paramref name="T" /> que se va a inicializar si aún no se ha inicializado.</param>
      <param name="initialized">Referencia a un valor booleano que determina si ya se ha inicializado el destino.</param>
      <param name="syncLock">Referencia a un objeto que se usa como bloqueo mutuamente excluyente para la inicialización de <paramref name="target" />.Si <paramref name="syncLock" /> es null, se creará una instancia de un nuevo objeto.</param>
      <typeparam name="T">Tipo de referencia que se va a inicializar.</typeparam>
      <exception cref="T:System.MemberAccessException">Faltaban los permisos para tener acceso al constructor de tipo <paramref name="T" />.</exception>
      <exception cref="T:System.MissingMemberException">El tipo <paramref name="T" /> no contiene un constructor predeterminado.</exception>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@,System.Boolean@,System.Object@,System.Func{``0})">
      <summary>Inicializa un tipo de referencia de destino o tipo de valor utilizando la función especificada si aún no se ha inicializado.</summary>
      <returns>Valor inicializado de tipo <paramref name="T" />.</returns>
      <param name="target">Referencia o valor de tipo <paramref name="T" /> que se va a inicializar si aún no se ha inicializado.</param>
      <param name="initialized">Referencia a un valor booleano que determina si ya se ha inicializado el destino.</param>
      <param name="syncLock">Referencia a un objeto que se usa como bloqueo mutuamente excluyente para la inicialización de <paramref name="target" />.Si <paramref name="syncLock" /> es null, se creará una instancia de un nuevo objeto.</param>
      <param name="valueFactory">Función que se llama para inicializar la referencia o el valor.</param>
      <typeparam name="T">Tipo de referencia que se va a inicializar.</typeparam>
      <exception cref="T:System.MemberAccessException">Faltaban los permisos para tener acceso al constructor de tipo <paramref name="T" />.</exception>
      <exception cref="T:System.MissingMemberException">El tipo <paramref name="T" /> no contiene un constructor predeterminado.</exception>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@,System.Func{``0})">
      <summary>Inicializa un tipo de referencia de destino utilizando la función especificada si aún no se ha inicializado.</summary>
      <returns>Valor inicializado de tipo <paramref name="T" />.</returns>
      <param name="target">Referencia de tipo <paramref name="T" /> que se va a inicializar si aún no se ha inicializado.</param>
      <param name="valueFactory">Función que se llama para inicializar la referencia.</param>
      <typeparam name="T">Tipo de referencia que se va a inicializar.</typeparam>
      <exception cref="T:System.MissingMemberException">El tipo <paramref name="T" /> no contiene un constructor predeterminado.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="valueFactory" /> devuelve un valor NULL (Nothing en Visual Basic).</exception>
    </member>
    <member name="T:System.Threading.LockRecursionException">
      <summary>Excepción que se inicia cuando la entrada recursiva en un bloqueo no es compatible con la directiva de recursividad del bloqueo.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.LockRecursionException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.LockRecursionException" /> con un mensaje proporcionado por el sistema que describe el error.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.LockRecursionException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.LockRecursionException" /> con un mensaje de error especificado que describe el error.</summary>
      <param name="message">Mensaje que describe la excepción.El llamador de este constructor debe asegurarse de que la cadena se ha traducido para la referencia cultural actual del sistema.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.LockRecursionException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.LockRecursionException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje que describe la excepción.El llamador de este constructor debe asegurarse de que la cadena se ha traducido para la referencia cultural actual del sistema.</param>
      <param name="innerException">Excepción que ha producido la excepción actual.Si el parámetro <paramref name="innerException" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.LockRecursionPolicy">
      <summary>Especifica si el mismo subproceso puede entrar varias veces en un bloqueo.</summary>
    </member>
    <member name="F:System.Threading.LockRecursionPolicy.NoRecursion">
      <summary>Si un subproceso intenta entrar en un bloqueo de forma recursiva, se inicia una excepción.Algunas clases pueden permitir cierta recursividad cuando se aplica esta configuración.</summary>
    </member>
    <member name="F:System.Threading.LockRecursionPolicy.SupportsRecursion">
      <summary>Un subproceso puede entrar en un bloqueo de forma recursiva.Algunas clases pueden limitar esta posibilidad.</summary>
    </member>
    <member name="T:System.Threading.ManualResetEvent">
      <summary>Notifica que se ha producido un evento a uno o varios subprocesos en espera.Esta clase no puede heredarse.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ManualResetEvent.#ctor(System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.ManualResetEvent" /> con un valor booleano que indica si hay que establecer el estado inicial en señalado.</summary>
      <param name="initialState">true para establecer el estado inicial de señalado; false para establecer el estado inicial en no señalado. </param>
    </member>
    <member name="T:System.Threading.ManualResetEventSlim">
      <summary>Proporciona una versión reducida de <see cref="T:System.Threading.ManualResetEvent" />.</summary>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.ManualResetEventSlim" /> con el estado inicial establecido en no señalado.</summary>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.#ctor(System.Boolean)">
      <summary>Inicializa una instancia de la clase <see cref="T:System.Threading.ManualResetEventSlim" /> con un valor booleano que indica si hay que establecer el estado inicial en señalado.</summary>
      <param name="initialState">Es true para establecer el estado inicial en señalado; es false para establecer el estado inicial en no señalado.</param>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.#ctor(System.Boolean,System.Int32)">
      <summary>Inicializa una instancia de la clase <see cref="T:System.Threading.ManualResetEventSlim" /> con un valor booleano que indica si hay que establecer el estado inicial en señalado y con el recuento circular especificado.</summary>
      <param name="initialState">Es true para establecer el estado inicial en señalado; es false para establecer el estado inicial en no señalado.</param>
      <param name="spinCount">Número de esperas circulares que se van a producir antes de una operación de espera basada en kernel.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="spinCount" /> is less than 0 or greater than the maximum allowed value.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Threading.ManualResetEventSlim" />.</summary>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Threading.ManualResetEventSlim" /> y, de forma opcional, libera los recursos administrados.</summary>
      <param name="disposing">true para liberar tanto los recursos administrados como los no administrados; false para liberar únicamente los recursos no administrados.</param>
    </member>
    <member name="P:System.Threading.ManualResetEventSlim.IsSet">
      <summary>Obtiene un valor que indica si se ha establecido el evento.</summary>
      <returns>Es true si se ha establecido el evento; de lo contrario, es false.</returns>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Reset">
      <summary>Establece el estado del evento en no señalado, por lo que se bloquean los subprocesos.</summary>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Set">
      <summary>Establece el estado del evento en señalado, lo que permite la continuación de uno o varios subprocesos que están esperando en el evento.</summary>
    </member>
    <member name="P:System.Threading.ManualResetEventSlim.SpinCount">
      <summary>Obtiene el número de esperas circulares que se producirán antes de una operación de espera basada en kernel.</summary>
      <returns>Devuelve el número de esperas circulares que se producirán antes de una operación de espera basada en kernel.</returns>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait">
      <summary>Bloquea el subproceso actual hasta que se establezca el objeto <see cref="T:System.Threading.ManualResetEventSlim" /> actual.</summary>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.Int32)">
      <summary>Bloquea el subproceso actual hasta que se establezca el <see cref="T:System.Threading.ManualResetEventSlim" /> actual, usando un entero de 32 bits con signo para medir el intervalo de tiempo.</summary>
      <returns>Es true si se estableció <see cref="T:System.Threading.ManualResetEventSlim" />; en caso contrario, es false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Bloquea el subproceso actual hasta que se establezca el <see cref="T:System.Threading.ManualResetEventSlim" /> actual, usando un entero de 32 bits con signo para medir el intervalo de tiempo, mientras se observa un token <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>true si se estableció <see cref="T:System.Threading.ManualResetEventSlim" />; en caso contrario, false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed or the <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.Threading.CancellationToken)">
      <summary>Bloquea el subproceso actual hasta que el <see cref="T:System.Threading.ManualResetEventSlim" /> actual reciba una señal, mientras se observa un token <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed or the <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.TimeSpan)">
      <summary>Bloquea el subproceso actual hasta que se establezca el <see cref="T:System.Threading.ManualResetEventSlim" /> actual, utilizando un objeto <see cref="T:System.TimeSpan" /> para medir el intervalo de tiempo.</summary>
      <returns>true si se estableció <see cref="T:System.Threading.ManualResetEventSlim" />; en caso contrario, false.</returns>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-The number of milliseconds in <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Bloquea el subproceso actual hasta que se establezca el <see cref="T:System.Threading.ManualResetEventSlim" />, usando un objeto <see cref="T:System.TimeSpan" /> para medir el intervalo de tiempo, mientras se observa un token <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>true si se estableció <see cref="T:System.Threading.ManualResetEventSlim" />; en caso contrario, false.</returns>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-The number of milliseconds in <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded. </exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed or the <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.ManualResetEventSlim.WaitHandle">
      <summary>Obtiene el objeto <see cref="T:System.Threading.WaitHandle" /> para este <see cref="T:System.Threading.ManualResetEventSlim" />.</summary>
      <returns>Objeto de evento <see cref="T:System.Threading.WaitHandle" /> subyacente de este <see cref="T:System.Threading.ManualResetEventSlim" />.</returns>
    </member>
    <member name="T:System.Threading.Monitor">
      <summary>Proporciona un mecanismo que sincroniza el acceso a los objetos.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Enter(System.Object)">
      <summary>Adquiere un bloqueo exclusivo en el objeto especificado.</summary>
      <param name="obj">Objeto en el que se va a adquirir el bloqueo de monitor. </param>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Enter(System.Object,System.Boolean@)">
      <summary>Adquiere un bloqueo exclusivo en el objeto especificado y establece de forma atómica un valor que indica si se realizó el bloqueo.</summary>
      <param name="obj">Objeto en el que se va a esperar. </param>
      <param name="lockTaken">Resultado del intento de adquirir el bloqueo, pasado por referencia.La entrada debe ser false.El resultado es true si se adquiere el bloqueo; en caso contrario, el resultado es false.El resultado se establece aunque se produzca una excepción durante el intento de adquirir el bloqueo.Nota   Si no se produce ninguna excepción, el resultado de este método siempre es true.</param>
      <exception cref="T:System.ArgumentException">La entrada <paramref name="lockTaken" /> es true.</exception>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
    </member>
    <member name="M:System.Threading.Monitor.Exit(System.Object)">
      <summary>Libera un bloqueo exclusivo en el objeto especificado.</summary>
      <param name="obj">Objeto en el que se va a liberar el bloqueo. </param>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">El subproceso actual no posee el bloqueo para el objeto especificado. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.IsEntered(System.Object)">
      <summary>Determina si el subproceso actual mantiene el bloqueo en el objeto especificado. </summary>
      <returns>Es true si el subproceso actual mantiene el bloqueo en <paramref name="obj" />; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a probar. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="obj" /> es null. </exception>
    </member>
    <member name="M:System.Threading.Monitor.Pulse(System.Object)">
      <summary>Notifica un cambio de estado del objeto bloqueado al subproceso que se encuentra en la cola de espera.</summary>
      <param name="obj">Objeto que está esperando un subproceso. </param>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">El subproceso que realiza la llamada no posee el bloqueo del objeto especificado. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.PulseAll(System.Object)">
      <summary>Notifica un cambio de estado del objeto a todos los subprocesos que se encuentran en espera.</summary>
      <param name="obj">Objeto que envía el pulso. </param>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">El subproceso que realiza la llamada no posee el bloqueo del objeto especificado. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object)">
      <summary>Intenta adquirir un bloqueo exclusivo en el objeto especificado.</summary>
      <returns>Es true si el subproceso actual adquiere el bloqueo; en caso contrario, es false.</returns>
      <param name="obj">Objeto en el que se va a adquirir el bloqueo. </param>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.Boolean@)">
      <summary>Intenta adquirir un bloqueo exclusivo en el objeto especificado y establece de forma atómica un valor que indica si se realizó el bloqueo.</summary>
      <param name="obj">Objeto en el que se va a adquirir el bloqueo. </param>
      <param name="lockTaken">Resultado del intento de adquirir el bloqueo, pasado por referencia.La entrada debe ser false.El resultado es true si se adquiere el bloqueo; en caso contrario, el resultado es false.El resultado se establece aunque se produzca una excepción durante el intento de adquirir el bloqueo.</param>
      <exception cref="T:System.ArgumentException">La entrada <paramref name="lockTaken" /> es true.</exception>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.Int32)">
      <summary>Intenta adquirir un bloqueo exclusivo en el objeto especificado durante el número de segundos especificado.</summary>
      <returns>Es true si el subproceso actual adquiere el bloqueo; en caso contrario, es false.</returns>
      <param name="obj">Objeto en el que se va a adquirir el bloqueo. </param>
      <param name="millisecondsTimeout">Número de milisegundos durante los que se va a esperar para adquirir el bloqueo. </param>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es negativo y no es igual a <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.Int32,System.Boolean@)">
      <summary>Intenta, durante el número especificado de milisegundos, adquirir un bloqueo exclusivo en el objeto especificado y establece de forma atómica un valor que indica si se realizó el bloqueo.</summary>
      <param name="obj">Objeto en el que se va a adquirir el bloqueo. </param>
      <param name="millisecondsTimeout">Número de milisegundos durante los que se va a esperar para adquirir el bloqueo. </param>
      <param name="lockTaken">Resultado del intento de adquirir el bloqueo, pasado por referencia.La entrada debe ser false.El resultado es true si se adquiere el bloqueo; en caso contrario, el resultado es false.El resultado se establece aunque se produzca una excepción durante el intento de adquirir el bloqueo.</param>
      <exception cref="T:System.ArgumentException">La entrada <paramref name="lockTaken" /> es true.</exception>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es negativo y no es igual a <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.TimeSpan)">
      <summary>Intenta adquirir un bloqueo exclusivo en el objeto especificado durante el período de tiempo especificado.</summary>
      <returns>Es true si el subproceso actual adquiere el bloqueo; en caso contrario, es false.</returns>
      <param name="obj">Objeto en el que se va a adquirir el bloqueo. </param>
      <param name="timeout">
        <see cref="T:System.TimeSpan" /> que representa el período de tiempo que se va a esperar para adquirir el bloqueo.Un valor de –1 milisegundo especifica una espera infinita.</param>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="timeout" /> en milisegundos es negativo y no es igual a <see cref="F:System.Threading.Timeout.Infinite" /> (– 1 milisegundo), o es mayor que <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.TimeSpan,System.Boolean@)">
      <summary>Intenta, durante el periodo de tiempo indicado, adquirir un bloqueo exclusivo en el objeto especificado y establece de forma atómica un valor que indica si se realizó el bloqueo.</summary>
      <param name="obj">Objeto en el que se va a adquirir el bloqueo. </param>
      <param name="timeout">Tiempo que se va a esperar el bloqueo.Un valor de –1 milisegundo especifica una espera infinita.</param>
      <param name="lockTaken">Resultado del intento de adquirir el bloqueo, pasado por referencia.La entrada debe ser false.El resultado es true si se adquiere el bloqueo; en caso contrario, el resultado es false.El resultado se establece aunque se produzca una excepción durante el intento de adquirir el bloqueo.</param>
      <exception cref="T:System.ArgumentException">La entrada <paramref name="lockTaken" /> es true.</exception>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="timeout" /> en milisegundos es negativo y no es igual a <see cref="F:System.Threading.Timeout.Infinite" /> (– 1 milisegundo), o es mayor que <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Monitor.Wait(System.Object)">
      <summary>Libera el bloqueo en un objeto y bloquea el subproceso actual hasta que vuelve a adquirir el bloqueo.</summary>
      <returns>Es true si la llamada fue devuelta porque el llamador volvió a adquirir el bloqueo para el objeto especificado.Este método no devuelve ningún resultado si el bloqueo no vuelve a adquirirse.</returns>
      <param name="obj">Objeto en el que se va a esperar. </param>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">El subproceso que realiza la llamada no posee el bloqueo del objeto especificado. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">El subproceso que invoca Wait se interrumpe más adelante desde el estado de espera.Esto sucede cuando otro subproceso llame a este subproceso <see cref="M:System.Threading.Thread.Interrupt" /> método.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Wait(System.Object,System.Int32)">
      <summary>Libera el bloqueo en un objeto y bloquea el subproceso actual hasta que vuelve a adquirir el bloqueo.Si transcurre el intervalo de tiempo de espera especificado, el subproceso entra en la cola de subprocesos listos.</summary>
      <returns>Es true si se volvió a adquirir el bloqueo antes de que transcurriera el período de tiempo especificado; es false si se volvió a adquirir el bloqueo después de que transcurriera el período de tiempo especificado.El método no devuelve ningún resultado hasta que se vuelva a adquirir el bloqueo.</returns>
      <param name="obj">Objeto en el que se va a esperar. </param>
      <param name="millisecondsTimeout">Número de milisegundos que se va a estar a la espera antes de que el subproceso entre en la cola de subprocesos listos. </param>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">El subproceso que realiza la llamada no posee el bloqueo del objeto especificado. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">El subproceso que invoca Wait se interrumpe más adelante desde el estado de espera.Esto sucede cuando otro subproceso llame a este subproceso <see cref="M:System.Threading.Thread.Interrupt" /> método.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de la <paramref name="millisecondsTimeout" /> parámetro es negativo y no es igual a <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Wait(System.Object,System.TimeSpan)">
      <summary>Libera el bloqueo en un objeto y bloquea el subproceso actual hasta que vuelve a adquirir el bloqueo.Si transcurre el intervalo de tiempo de espera especificado, el subproceso entra en la cola de subprocesos listos.</summary>
      <returns>Es true si se volvió a adquirir el bloqueo antes de que transcurriera el período de tiempo especificado; es false si se volvió a adquirir el bloqueo después de que transcurriera el período de tiempo especificado.El método no devuelve ningún resultado hasta que se vuelva a adquirir el bloqueo.</returns>
      <param name="obj">Objeto en el que se va a esperar. </param>
      <param name="timeout">
        <see cref="T:System.TimeSpan" /> que representa la cantidad de tiempo que se va a esperar antes de que el subproceso entre en la cola de subprocesos listos. </param>
      <exception cref="T:System.ArgumentNullException">El parámetro <paramref name="obj" /> es null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">El subproceso que realiza la llamada no posee el bloqueo del objeto especificado. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">El subproceso que invoca Wait se interrumpe más adelante desde el estado de espera.Esto sucede cuando otro subproceso llame a este subproceso <see cref="M:System.Threading.Thread.Interrupt" /> método.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de la <paramref name="timeout" /> parámetro en milisegundos es negativo y no representa <see cref="F:System.Threading.Timeout.Infinite" /> (– 1 milisegundo), o es mayor que <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.Mutex">
      <summary>Primitiva de sincronización que puede usarse también para la sincronización entre procesos. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Mutex.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Mutex" /> con propiedades predeterminadas.</summary>
    </member>
    <member name="M:System.Threading.Mutex.#ctor(System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Mutex" /> con un valor booleano que indica si el subproceso que realiza la llamada debe tener la propiedad inicial de la exclusión mutua.</summary>
      <param name="initiallyOwned">true para otorgar la propiedad inicial de la exclusión mutua al subproceso que realiza la llamada, de lo contrario, false. </param>
    </member>
    <member name="M:System.Threading.Mutex.#ctor(System.Boolean,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Mutex" /> con un valor booleano que indica si el subproceso que realiza la llamada debe tener la propiedad inicial de la exclusión mutua y una cadena que representa el nombre de la exclusión mutua.</summary>
      <param name="initiallyOwned">true para otorgar al subproceso que realiza la llamada la propiedad inicial de la exclusión mutua del sistema con nombre si esta se crea como resultado de dicha llamada; de lo contrario, false. </param>
      <param name="name">Nombre del objeto <see cref="T:System.Threading.Mutex" />.Si el valor es null, <see cref="T:System.Threading.Mutex" /> no tiene nombre.</param>
      <exception cref="T:System.UnauthorizedAccessException">La exclusión mutua con nombre existe y tiene seguridad de control de acceso, pero el usuario no tiene <see cref="F:System.Security.AccessControl.MutexRights.FullControl" />.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">No se puede crear la exclusión mutua con nombre; posiblemente porque un identificador de espera de otro tipo tiene el mismo nombre.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> tiene más de 260 caracteres.</exception>
    </member>
    <member name="M:System.Threading.Mutex.#ctor(System.Boolean,System.String,System.Boolean@)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Mutex" /> con un valor booleano que indica si el subproceso que realiza la llamada debe tener la propiedad inicial de la exclusión mutua, una cadena que es el nombre de la exclusión mutua y un valor booleano que, cuando se devuelva el método, indicará si se concedió la propiedad inicial de la exclusión mutua al subproceso que realiza la llamada.</summary>
      <param name="initiallyOwned">true para otorgar al subproceso que realiza la llamada la propiedad inicial de la exclusión mutua del sistema con nombre si esta se crea como resultado de dicha llamada; de lo contrario, false. </param>
      <param name="name">Nombre del objeto <see cref="T:System.Threading.Mutex" />.Si el valor es null, <see cref="T:System.Threading.Mutex" /> no tiene nombre.</param>
      <param name="createdNew">Cuando se devuelve este método, contiene un valor booleano que es true si se creó una exclusión mutua local (es decir, si <paramref name="name" /> es null o una cadena vacía) o si se creó la exclusión mutua del sistema con nombre especificada; el valor es false si la exclusión mutua del sistema con nombre especificada ya existía.Este parámetro se pasa sin inicializar.</param>
      <exception cref="T:System.UnauthorizedAccessException">La exclusión mutua con nombre existe y tiene seguridad de control de acceso, pero el usuario no tiene <see cref="F:System.Security.AccessControl.MutexRights.FullControl" />.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">No se puede crear la exclusión mutua con nombre; posiblemente porque un identificador de espera de otro tipo tiene el mismo nombre.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> tiene más de 260 caracteres.</exception>
    </member>
    <member name="M:System.Threading.Mutex.OpenExisting(System.String)">
      <summary>Abre la exclusión mutua con nombre especificada, si ya existe.</summary>
      <returns>Objeto que representa la exclusión mutua del sistema con nombre.</returns>
      <param name="name">Nombre de la exclusión mutua del sistema que se va a abrir.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> es una cadena vacía.o bien<paramref name="name" /> tiene más de 260 caracteres.</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="name" /> es null.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">La excepción mutua con nombre no existe.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">La exclusión mutua con nombre existe, pero el usuario no dispone del acceso de seguridad exigido para utilizarla.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Threading.Mutex.ReleaseMutex">
      <summary>Libera una vez la instancia de <see cref="T:System.Threading.Mutex" />.</summary>
      <exception cref="T:System.ApplicationException">El subproceso que realiza la llamada no posee la exclusión mutua. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Mutex.TryOpenExisting(System.String,System.Threading.Mutex@)">
      <summary>Abre la exclusión mutua con nombre especificada, si ya existe, y devuelve un valor que indica si la operación se realizó correctamente.</summary>
      <returns>true si la exclusión mutua con nombre se abrió correctamente; si no, false.</returns>
      <param name="name">Nombre de la exclusión mutua del sistema que se va a abrir.</param>
      <param name="result">Cuando este método vuelve, contiene un objeto <see cref="T:System.Threading.Mutex" /> que representa la exclusión mutua con nombre si la llamada se realizó correctamente, o null si se produjo un error en la llamada.Este parámetro se trata como sin inicializar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> es una cadena vacía.o bien<paramref name="name" /> tiene más de 260 caracteres.</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="name" /> es null.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">La exclusión mutua con nombre existe, pero el usuario no dispone del acceso de seguridad exigido para utilizarla.</exception>
    </member>
    <member name="T:System.Threading.ReaderWriterLockSlim">
      <summary>Representa un bloqueo que se utiliza para administrar el acceso a un recurso y que permite varios subprocesos para la lectura o acceso exclusivo para la escritura.</summary>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.ReaderWriterLockSlim" /> con los valores de propiedad predeterminados.</summary>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.#ctor(System.Threading.LockRecursionPolicy)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.ReaderWriterLockSlim" /> especificando la directiva de recursividad de bloqueo.</summary>
      <param name="recursionPolicy">Uno de los valores de enumeración que especifica la directiva de recursividad de bloqueo. </param>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.CurrentReadCount">
      <summary>Obtiene el número total de subprocesos únicos que han entrado en el bloqueo en modo de lectura.</summary>
      <returns>Número de subprocesos únicos que han entrado en el bloqueo en modo de lectura.</returns>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Threading.ReaderWriterLockSlim" />.</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">
        <see cref="P:System.Threading.ReaderWriterLockSlim.WaitingReadCount" /> is greater than zero. -or-<see cref="P:System.Threading.ReaderWriterLockSlim.WaitingUpgradeCount" /> is greater than zero. -or-<see cref="P:System.Threading.ReaderWriterLockSlim.WaitingWriteCount" /> is greater than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.EnterReadLock">
      <summary>Intenta entrar en el bloqueo en modo de lectura.</summary>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered read mode. -or-The current thread may not acquire the read lock when it already holds the write lock. -or-The recursion number would exceed the capacity of the counter.This limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.EnterUpgradeableReadLock">
      <summary>Intenta entrar en el bloqueo en modo de actualización.</summary>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock in any mode. -or-The current thread has entered read mode, so trying to enter upgradeable mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.EnterWriteLock">
      <summary>Intenta entrar en el bloqueo en modo de escritura.</summary>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock in any mode. -or-The current thread has entered read mode, so trying to enter the lock in write mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.ExitReadLock">
      <summary>Reduce el recuento de recursividad para el modo de lectura y sale del modo de lectura si el recuento resultante es 0 (cero).</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">The current thread has not entered the lock in read mode. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.ExitUpgradeableReadLock">
      <summary>Reduce el recuento de recursividad para el modo de actualización y sale del modo de actualización si el recuento resultante es 0 (cero).</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">The current thread has not entered the lock in upgradeable mode.</exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.ExitWriteLock">
      <summary>Reduce el recuento de recursividad para el modo de escritura y sale del modo de escritura si el recuento resultante es 0 (cero).</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">The current thread has not entered the lock in write mode.</exception>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.IsReadLockHeld">
      <summary>Obtiene un valor que indica si el subproceso actual ha entrado en el bloqueo en modo de lectura.</summary>
      <returns>true si el subproceso actual entró en modo Lectura; en caso contrario, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.IsUpgradeableReadLockHeld">
      <summary>Obtiene un valor que indica si el subproceso actual entró en el bloqueo en modo de actualización. </summary>
      <returns>true si el subproceso actual entró en modo de actualización; en caso contrario, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.IsWriteLockHeld">
      <summary>Obtiene un valor que indica si el subproceso actual ha entrado en el bloqueo en modo de escritura.</summary>
      <returns>true si el subproceso actual entró en modo de escritura; en caso contrario, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy">
      <summary>Obtiene un valor que indica la directiva de recursividad del objeto <see cref="T:System.Threading.ReaderWriterLockSlim" /> actual.</summary>
      <returns>Uno de los valores de enumeración que especifica la directiva de recursividad de bloqueo.</returns>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursiveReadCount">
      <summary>Obtiene el número de veces que el subproceso actual ha entrado en el bloqueo en modo de lectura, como una indicación de recursividad.</summary>
      <returns>0 (cero) si el subproceso actual no entró en modo Lectura, 1 si el subproceso entró en modo Lectura pero no lo hizo de forma recursiva o n si el subproceso entró de forma recursiva en el bloqueo n - 1 veces.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursiveUpgradeCount">
      <summary>Obtiene el número de veces que el subproceso actual ha entrado en el bloqueo en modo de actualización, como una indicación de recursividad.</summary>
      <returns>0 (cero) si el subproceso actual no entró en modo de actualización, 1 si el subproceso entró en modo de actualización pero no lo hizo de forma recursiva o n si el subproceso entró de forma recursiva en el modo de actualización n - 1 veces.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursiveWriteCount">
      <summary>Obtiene el número de veces que el subproceso actual ha entrado en el bloqueo en modo de escritura, como una indicación de recursividad.</summary>
      <returns>0 (cero) si el subproceso actual no entró en modo de escritura, 1 si el subproceso entró en modo de escritura pero no lo hizo de forma recursiva o n si el subproceso entró de forma recursiva en el modo de escritura n - 1 veces.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterReadLock(System.Int32)">
      <summary>Intenta entrar en el bloqueo en modo de lectura, con un tiempo de espera entero opcional.</summary>
      <returns>true si el subproceso que realiza la llamada entró en modo Lectura; en caso contrario, false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o -1 (<see cref="F:System.Threading.Timeout.Infinite" />) para esperar indefinidamente.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative, but it is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> (-1), which is the only negative value allowed. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterReadLock(System.TimeSpan)">
      <summary>Intenta entrar en el bloqueo en modo de lectura, con tiempo de espera opcional.</summary>
      <returns>true si el subproceso que realiza la llamada entró en modo Lectura; en caso contrario, false.</returns>
      <param name="timeout">Intervalo de espera, o -1 milisegundo para esperar indefinidamente. </param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative, but it is not equal to -1 milliseconds, which is the only negative value allowed.-or-The value of <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterUpgradeableReadLock(System.Int32)">
      <summary>Intenta entrar en el bloqueo en modo de actualización, con tiempo de espera opcional.</summary>
      <returns>true si el subproceso que realiza la llamada entró en modo de actualización; en caso contrario, false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o -1 (<see cref="F:System.Threading.Timeout.Infinite" />) para esperar indefinidamente.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter upgradeable mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative, but it is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> (-1), which is the only negative value allowed. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterUpgradeableReadLock(System.TimeSpan)">
      <summary>Intenta entrar en el bloqueo en modo de actualización, con tiempo de espera opcional.</summary>
      <returns>true si el subproceso que realiza la llamada entró en modo de actualización; en caso contrario, false.</returns>
      <param name="timeout">Intervalo de espera, o -1 milisegundo para esperar indefinidamente.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter upgradeable mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative, but it is not equal to -1 milliseconds, which is the only negative value allowed.-or-The value of <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterWriteLock(System.Int32)">
      <summary>Intenta entrar en el bloqueo en modo de escritura, con tiempo de espera opcional.</summary>
      <returns>true si el subproceso que realiza la llamada entró en modo de escritura; en caso contrario, false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o -1 (<see cref="F:System.Threading.Timeout.Infinite" />) para esperar indefinidamente.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter write mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative, but it is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> (-1), which is the only negative value allowed. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterWriteLock(System.TimeSpan)">
      <summary>Intenta entrar en el bloqueo en modo de escritura, con tiempo de espera opcional.</summary>
      <returns>true si el subproceso que realiza la llamada entró en modo de escritura; en caso contrario, false.</returns>
      <param name="timeout">Intervalo de espera, o -1 milisegundo para esperar indefinidamente.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter write mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative, but it is not equal to -1 milliseconds, which is the only negative value allowed.-or-The value of <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.WaitingReadCount">
      <summary>Obtiene el número total de subprocesos que están a la espera de entrar en el bloqueo en modo de lectura.</summary>
      <returns>Número total de subprocesos que están a la espera de entrar en modo de lectura.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.WaitingUpgradeCount">
      <summary>Obtiene el número total de subprocesos que están a la espera de entrar en el bloqueo en modo de actualización.</summary>
      <returns>Número total de subprocesos que están a la espera de entrar en modo de actualización.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.WaitingWriteCount">
      <summary>Obtiene el número total de subprocesos que están a la espera de entrar en el bloqueo en modo de escritura.</summary>
      <returns>Número total de subprocesos que están a la espera de entrar en modo de escritura.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.Semaphore">
      <summary>Limita el número de subprocesos que pueden tener acceso a un recurso o grupo de recursos simultáneamente. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Semaphore.#ctor(System.Int32,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Semaphore" />, que especifica el número inicial de entradas y el número máximo de entradas simultáneas. </summary>
      <param name="initialCount">Número inicial de solicitudes del semáforo que se pueden conceder simultáneamente. </param>
      <param name="maximumCount">Número máximo de solicitudes del semáforo que se pueden conceder simultáneamente. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="initialCount" /> es mayor que <paramref name="maximumCount" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumCount" /> es menor que 1.o bien<paramref name="initialCount" /> es menor que 0.</exception>
    </member>
    <member name="M:System.Threading.Semaphore.#ctor(System.Int32,System.Int32,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Semaphore" />, que especifica el número inicial de entradas y el número máximo de entradas simultáneas, y especificando de forma opcional el nombre de un objeto semáforo de sistema. </summary>
      <param name="initialCount">Número inicial de solicitudes del semáforo que se pueden conceder simultáneamente. </param>
      <param name="maximumCount">Número máximo de solicitudes del semáforo que se pueden conceder simultáneamente.</param>
      <param name="name">Nombre de un objeto de semáforo del sistema con nombre.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="initialCount" /> es mayor que <paramref name="maximumCount" />.o bien<paramref name="name" /> tiene más de 260 caracteres.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumCount" /> es menor que 1.o bien<paramref name="initialCount" /> es menor que 0.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El semáforo con nombre existe y tiene seguridad de control de acceso y el usuario no tiene <see cref="F:System.Security.AccessControl.SemaphoreRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">No se puede crear el semáforo con nombre, probablemente porque tiene el mismo nombre que un identificador de espera de otro tipo.</exception>
    </member>
    <member name="M:System.Threading.Semaphore.#ctor(System.Int32,System.Int32,System.String,System.Boolean@)">
      <summary>Inicializa una instancia nueva de la clase <see cref="T:System.Threading.Semaphore" />, especificando el número inicial de entradas y el número máximo de entradas simultáneas, especificando de forma opcional el nombre de un objeto semáforo de sistema y especificando una variable que recibe un valor que indica si se creó un semáforo del sistema nuevo.</summary>
      <param name="initialCount">Número inicial de solicitudes para el semáforo que se puede satisfacer simultáneamente. </param>
      <param name="maximumCount">Número máximo de solicitudes para el semáforo que se puede satisfacer simultáneamente.</param>
      <param name="name">Nombre de un objeto de semáforo del sistema con nombre.</param>
      <param name="createdNew">Cuando este método devuelve un resultado, contiene true si se creó un semáforo local (es decir, si <paramref name="name" /> es null o una cadena vacía) o si se creó el semáforo del sistema con nombre especificado; es false si el semáforo del sistema con nombre especificado ya existía.Este parámetro se pasa sin inicializar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="initialCount" /> es mayor que <paramref name="maximumCount" />. o bien<paramref name="name" /> tiene más de 260 caracteres.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumCount" /> es menor que 1.o bien<paramref name="initialCount" /> es menor que 0.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El semáforo con nombre existe y tiene seguridad de control de acceso y el usuario no tiene <see cref="F:System.Security.AccessControl.SemaphoreRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">No se puede crear el semáforo con nombre, probablemente porque tiene el mismo nombre que un identificador de espera de otro tipo.</exception>
    </member>
    <member name="M:System.Threading.Semaphore.OpenExisting(System.String)">
      <summary>Abre el semáforo con nombre especificado, si ya existe.</summary>
      <returns>Objeto que representa el semáforo del sistema con nombre.</returns>
      <param name="name">Nombre del semáforo del sistema que se va a abrir.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> es una cadena vacía.o bien<paramref name="name" /> tiene más de 260 caracteres.</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="name" /> es null.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">El semáforo con nombre no existe.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El semáforo con nombre existe, pero el usuario no tiene el acceso de seguridad necesario para utilizarlo. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Threading.Semaphore.Release">
      <summary>Sale del semáforo y devuelve el recuento anterior.</summary>
      <returns>Recuento en el semáforo antes de la llamada al método <see cref="Overload:System.Threading.Semaphore.Release" />. </returns>
      <exception cref="T:System.Threading.SemaphoreFullException">El recuento del semáforo ya está en el valor máximo.</exception>
      <exception cref="T:System.IO.IOException">Error de Win32 con un semáforo con nombre.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El semáforo actual representa un semáforo de sistema con nombre, pero el usuario no tiene <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" />.o bienEl semáforo actual representa un semáforo de sistema con nombre, pero no se abrió con <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Semaphore.Release(System.Int32)">
      <summary>Sale del semáforo un número especificado de veces y devuelve el recuento anterior.</summary>
      <returns>Recuento en el semáforo antes de la llamada al método <see cref="Overload:System.Threading.Semaphore.Release" />. </returns>
      <param name="releaseCount">Número de veces que se abandona el semáforo.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="releaseCount" /> es menor que 1.</exception>
      <exception cref="T:System.Threading.SemaphoreFullException">El recuento del semáforo ya está en el valor máximo.</exception>
      <exception cref="T:System.IO.IOException">Error de Win32 con un semáforo con nombre.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El semáforo actual representa un semáforo de sistema con nombre, pero el usuario no tiene <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" /> derechos.o bienEl semáforo actual representa un semáforo de sistema con nombre, pero no se abrió con <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" /> derechos.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Semaphore.TryOpenExisting(System.String,System.Threading.Semaphore@)">
      <summary>Abre el semáforo con nombre especificado, si ya existe, y devuelve un valor que indica si la operación se realizó correctamente.</summary>
      <returns>true si el semáforo con nombre se abrió correctamente; si no, false.</returns>
      <param name="name">Nombre del semáforo del sistema que se va a abrir.</param>
      <param name="result">Cuando este método vuelve, contiene un objeto <see cref="T:System.Threading.Semaphore" /> que representa el semáforo con nombre si la llamada se realizó correctamente o null si se produjo un error en la misma.Este parámetro se trata como sin inicializar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> es una cadena vacía.o bien<paramref name="name" /> tiene más de 260 caracteres.</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="name" /> es null.</exception>
      <exception cref="T:System.IO.IOException">Se ha producido un error de Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El semáforo con nombre existe, pero el usuario no tiene el acceso de seguridad necesario para utilizarlo. </exception>
    </member>
    <member name="T:System.Threading.SemaphoreFullException">
      <summary>Excepción que se produce cuando se llama al método <see cref="Overload:System.Threading.Semaphore.Release" /> en un semáforo cuyo recuento ya ha alcanzado el valor máximo. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SemaphoreFullException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.SemaphoreFullException" /> con valores predeterminados.</summary>
    </member>
    <member name="M:System.Threading.SemaphoreFullException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.SemaphoreFullException" /> con el mensaje de error especificado.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción.</param>
    </member>
    <member name="M:System.Threading.SemaphoreFullException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.SemaphoreFullException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción.</param>
      <param name="innerException">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="innerException" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="T:System.Threading.SemaphoreSlim">
      <summary>Representa una alternativa ligera a <see cref="T:System.Threading.Semaphore" /> que limita el número de subprocesos que puede obtener acceso a la vez a un recurso o a un grupo de recursos.</summary>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.SemaphoreSlim" />, especificando el número inicial de solicitudes que se pueden conceder simultáneamente.</summary>
      <param name="initialCount">Número inicial de solicitudes del semáforo que se pueden conceder simultáneamente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialCount" /> es menor que 0.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.#ctor(System.Int32,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.SemaphoreSlim" />, especificando el número inicial y máximo de solicitudes que se pueden conceder simultáneamente.</summary>
      <param name="initialCount">Número inicial de solicitudes del semáforo que se pueden conceder simultáneamente.</param>
      <param name="maxCount">Número máximo de solicitudes del semáforo que se pueden conceder simultáneamente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialCount" /> es menor que 0, o <paramref name="initialCount" /> es mayor que <paramref name="maxCount" />, o <paramref name="maxCount" /> es igual o menor que 0.</exception>
    </member>
    <member name="P:System.Threading.SemaphoreSlim.AvailableWaitHandle">
      <summary>Devuelve un objeto <see cref="T:System.Threading.WaitHandle" /> que se puede usar para esperar en el semáforo.</summary>
      <returns>
        <see cref="T:System.Threading.WaitHandle" /> que se puede usar para esperar en el semáforo.</returns>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado <see cref="T:System.Threading.SemaphoreSlim" />.</exception>
    </member>
    <member name="P:System.Threading.SemaphoreSlim.CurrentCount">
      <summary>Obtiene el número de subprocesos restantes que puede introducir el objeto <see cref="T:System.Threading.SemaphoreSlim" />. </summary>
      <returns>Obtiene el número de subprocesos restantes que pueden entrar en el semáforo.</returns>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Threading.SemaphoreSlim" />.</summary>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados utilizados por el objeto <see cref="T:System.Threading.SemaphoreSlim" /> y, de forma opcional, libera los recursos administrados.</summary>
      <param name="disposing">Es true para liberar tanto recursos administrados como no administrados; es false para liberar únicamente recursos no administrados.</param>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Release">
      <summary>Libera una vez el objeto <see cref="T:System.Threading.SemaphoreSlim" />.</summary>
      <returns>Recuento anterior de <see cref="T:System.Threading.SemaphoreSlim" />.</returns>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.Threading.SemaphoreFullException">El <see cref="T:System.Threading.SemaphoreSlim" /> ya se ha alcanzado su tamaño máximo.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Release(System.Int32)">
      <summary>Libera el objeto <see cref="T:System.Threading.SemaphoreSlim" /> un número especificado de veces.</summary>
      <returns>Recuento anterior de <see cref="T:System.Threading.SemaphoreSlim" />.</returns>
      <param name="releaseCount">Número de veces que se abandona el semáforo.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="releaseCount" /> es menor que 1.</exception>
      <exception cref="T:System.Threading.SemaphoreFullException">El <see cref="T:System.Threading.SemaphoreSlim" /> ya se ha alcanzado su tamaño máximo.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait">
      <summary>Bloquea el subproceso actual hasta que pueda introducir <see cref="T:System.Threading.SemaphoreSlim" />.</summary>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.Int32)">
      <summary>Bloquea el subproceso actual hasta que pueda introducir <see cref="T:System.Threading.SemaphoreSlim" />, usando un entero de 32 bits con signo que especifica el tiempo de espera.</summary>
      <returns>true si el subproceso actual introdujo correctamente <see cref="T:System.Threading.SemaphoreSlim" />; de lo contrario, false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1, que representa el tiempo de espera infinito.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Bloquea el subproceso actual hasta que pueda introducir <see cref="T:System.Threading.SemaphoreSlim" />, usando un entero de 32 bits con signo que especifica el tiempo de espera mientras se observa un elemento <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>true si el subproceso actual introdujo correctamente <see cref="T:System.Threading.SemaphoreSlim" />; de lo contrario, false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> se ha cancelado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1, que representa el tiempo de espera infinito.</exception>
      <exception cref="T:System.ObjectDisposedException">El <see cref="T:System.Threading.SemaphoreSlim" /> se ha eliminado la instancia, o la <see cref="T:System.Threading.CancellationTokenSource" /> que creó <paramref name="cancellationToken" /> se ha eliminado.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.Threading.CancellationToken)">
      <summary>Bloquea el subproceso actual hasta que pueda introducir <see cref="T:System.Threading.SemaphoreSlim" />, mientras se observa un elemento <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="cancellationToken">Token <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> se ha cancelado.</exception>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó<paramref name=" cancellationToken" /> ya se ha eliminado.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.TimeSpan)">
      <summary>Bloquea el subproceso actual hasta que pueda introducir <see cref="T:System.Threading.SemaphoreSlim" />, usando <see cref="T:System.TimeSpan" /> para especificar el tiempo de espera.</summary>
      <returns>true si el subproceso actual introdujo correctamente <see cref="T:System.Threading.SemaphoreSlim" />; de lo contrario, false.</returns>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> es un número negativo distinto de-1 milisegundo, que representa un tiempo de espera infinito o - tiempo de espera es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia de semaphoreSlim<paramref name="." /></exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Bloquea el subproceso actual hasta que pueda introducir <see cref="T:System.Threading.SemaphoreSlim" />, usando un <see cref="T:System.TimeSpan" /> que especifica el tiempo de espera mientras se observa un elemento <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>true si el subproceso actual introdujo correctamente <see cref="T:System.Threading.SemaphoreSlim" />; de lo contrario, false.</returns>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> se ha cancelado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> es un número negativo distinto de-1 milisegundo, que representa un tiempo de espera infinito o - tiempo de espera es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia de semaphoreSlim<paramref name="." /><paramref name="-or-" />El <see cref="T:System.Threading.CancellationTokenSource" /> que creó <paramref name="cancellationToken" /> ya se ha eliminado.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync">
      <summary>De forma asincrónica espera que se introduzca <see cref="T:System.Threading.SemaphoreSlim" />. </summary>
      <returns>Tarea que se completará cuando se entre en el semáforo.</returns>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.Int32)">
      <summary>De forma asincrónica espera que se introduzca <see cref="T:System.Threading.SemaphoreSlim" />, usando un entero de 32 bits para medir el intervalo de tiempo. </summary>
      <returns>Tarea que se completará con un resultado true si el subproceso actual introdujo correctamente <see cref="T:System.Threading.SemaphoreSlim" />; de lo contrario, el resultado será false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1, que representa el tiempo de espera infinito.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.Int32,System.Threading.CancellationToken)">
      <summary>De forma asincrónica, espera introducir <see cref="T:System.Threading.SemaphoreSlim" />, usando un entero de 32 bits para medir el intervalo de tiempo, mientras observa un elemento <see cref="T:System.Threading.CancellationToken" />. </summary>
      <returns>Tarea que se completará con un resultado true si el subproceso actual introdujo correctamente <see cref="T:System.Threading.SemaphoreSlim" />; de lo contrario, el resultado será false. </returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <param name="cancellationToken">
        <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1, que representa el tiempo de espera infinito. </exception>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado. </exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> se ha cancelado. </exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.Threading.CancellationToken)">
      <summary>De forma asincrónica, espera introducir <see cref="T:System.Threading.SemaphoreSlim" />, mientras observa un elemento <see cref="T:System.Threading.CancellationToken" />. </summary>
      <returns>Tarea que se completará cuando se entre en el semáforo. </returns>
      <param name="cancellationToken">Token <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> se ha cancelado. </exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.TimeSpan)">
      <summary>De forma asincrónica, espera introducir <see cref="T:System.Threading.SemaphoreSlim" />, usando un <see cref="T:System.TimeSpan" /> para medir el intervalo de tiempo.</summary>
      <returns>Tarea que se completará con un resultado true si el subproceso actual introdujo correctamente <see cref="T:System.Threading.SemaphoreSlim" />; de lo contrario, el resultado será false.</returns>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">La instancia actual ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1, que representa el tiempo de espera infinito o bien tiempo de espera es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>De forma asincrónica, espera introducir <see cref="T:System.Threading.SemaphoreSlim" />, usando un <see cref="T:System.TimeSpan" /> para medir el intervalo de tiempo, mientras observa un elemento <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Tarea que se completará con un resultado true si el subproceso actual introdujo correctamente <see cref="T:System.Threading.SemaphoreSlim" />; de lo contrario, el resultado será false.</returns>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <param name="cancellationToken">Token <see cref="T:System.Threading.CancellationToken" /> que se va a observar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1, que representa el tiempo de espera infinitoo bientiempo de espera es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> se ha cancelado. </exception>
    </member>
    <member name="T:System.Threading.SendOrPostCallback">
      <summary>Representa el método al que hay que llamar cuando se va a enviar un mensaje a un contexto de sincronización.  </summary>
      <param name="state">Objeto que se ha pasado al delegado.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.SpinLock">
      <summary>Proporciona una primitiva de bloqueo de exclusión mutua donde un subproceso que intenta adquirir el bloqueo espera en un bucle repetidamente comprobando hasta que haya un bloqueo disponible.</summary>
    </member>
    <member name="M:System.Threading.SpinLock.#ctor(System.Boolean)">
      <summary>Inicializa una nueva instancia de la estructura <see cref="T:System.Threading.SpinLock" /> con la opción de realizar el seguimiento de los identificadores de subprocesos para mejorar la depuración.</summary>
      <param name="enableThreadOwnerTracking">Indica si se han de capturar y utilizar identificadores de subprocesos con fines de depuración.</param>
    </member>
    <member name="M:System.Threading.SpinLock.Enter(System.Boolean@)">
      <summary>Adquiere el bloqueo de manera confiable de modo que, incluso si se produce una excepción en la llamada al método, se pueda examinar <paramref name="lockTaken" /> de manera confiable para determinar si se adquirió el bloqueo.</summary>
      <param name="lockTaken">Es true si se adquiere el bloqueo; de lo contrario, es false.<paramref name="lockTaken" /> se debe inicializar en false antes de llamar a este método.</param>
      <exception cref="T:System.ArgumentException">El argumento <paramref name="lockTaken" /> se debe inicializar en false antes de llamar a Enter.</exception>
      <exception cref="T:System.Threading.LockRecursionException">El seguimiento de propiedad de subprocesos está habilitado, y el subproceso actual ya ha adquirido este bloqueo.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.Exit">
      <summary>Libera el bloqueo.</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">El seguimiento de propiedad de subprocesos está habilitado, y el subproceso actual no es el propietario de este bloqueo.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.Exit(System.Boolean)">
      <summary>Libera el bloqueo.</summary>
      <param name="useMemoryBarrier">Valor booleano que indica si una barrera de memoria debe emitirse para publicar inmediatamente la operación de salida a otros subprocesos.</param>
      <exception cref="T:System.Threading.SynchronizationLockException">El seguimiento de propiedad de subprocesos está habilitado, y el subproceso actual no es el propietario de este bloqueo.</exception>
    </member>
    <member name="P:System.Threading.SpinLock.IsHeld">
      <summary>Obtiene un valor que indica si un subproceso mantiene actualmente el bloqueo.</summary>
      <returns>Es true si cualquier subproceso mantiene actualmente el bloqueo; de lo contrario, es false.</returns>
    </member>
    <member name="P:System.Threading.SpinLock.IsHeldByCurrentThread">
      <summary>Obtiene un valor que indica si el subproceso actual mantiene actualmente el bloqueo.</summary>
      <returns>Es true si el subproceso actual mantiene el bloqueo; de lo contrario, es false.</returns>
      <exception cref="T:System.InvalidOperationException">El seguimiento de propiedad de subprocesos está deshabilitado.</exception>
    </member>
    <member name="P:System.Threading.SpinLock.IsThreadOwnerTrackingEnabled">
      <summary>Obtiene un valor que indica si el seguimiento de propiedad de subprocesos está habilitado para esta instancia.</summary>
      <returns>Es true si se ha habilitado el seguimiento de propiedad de subprocesos para esta instancia; de lo contrario, es false.</returns>
    </member>
    <member name="M:System.Threading.SpinLock.TryEnter(System.Boolean@)">
      <summary>Intenta adquirir el bloqueo de manera confiable de modo que, incluso si se produce una excepción en la llamada al método, se pueda examinar <paramref name="lockTaken" /> de manera confiable para determinar si se adquirió el bloqueo.</summary>
      <param name="lockTaken">Es true si se adquiere el bloqueo; de lo contrario, es false.<paramref name="lockTaken" /> se debe inicializar en false antes de llamar a este método.</param>
      <exception cref="T:System.ArgumentException">El argumento <paramref name="lockTaken" /> se debe inicializar en false antes de llamar a TryEnter.</exception>
      <exception cref="T:System.Threading.LockRecursionException">El seguimiento de propiedad de subprocesos está habilitado, y el subproceso actual ya ha adquirido este bloqueo.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.TryEnter(System.Int32,System.Boolean@)">
      <summary>Intenta adquirir el bloqueo de manera confiable de modo que, incluso si se produce una excepción en la llamada al método, se pueda examinar <paramref name="lockTaken" /> de manera confiable para determinar si se adquirió el bloqueo.</summary>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <param name="lockTaken">Es true si se adquiere el bloqueo; de lo contrario, es false.<paramref name="lockTaken" /> se debe inicializar en false antes de llamar a este método.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1 que representa un tiempo de espera infinito.</exception>
      <exception cref="T:System.ArgumentException">El argumento <paramref name="lockTaken" /> se debe inicializar en false antes de llamar a TryEnter.</exception>
      <exception cref="T:System.Threading.LockRecursionException">El seguimiento de propiedad de subprocesos está habilitado, y el subproceso actual ya ha adquirido este bloqueo.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.TryEnter(System.TimeSpan,System.Boolean@)">
      <summary>Intenta adquirir el bloqueo de manera confiable de modo que, incluso si se produce una excepción en la llamada al método, se pueda examinar <paramref name="lockTaken" /> de manera confiable para determinar si se adquirió el bloqueo.</summary>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <param name="lockTaken">Es true si se adquiere el bloqueo; de lo contrario, es false.<paramref name="lockTaken" /> se debe inicializar en false antes de llamar a este método.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> es un número negativo distinto de -1 milisegundo, que representa un tiempo de espera infinito, o el tiempo de espera es mayor que <see cref="F:System.Int32.MaxValue" /> milisegundos.</exception>
      <exception cref="T:System.ArgumentException">El argumento <paramref name="lockTaken" /> se debe inicializar en false antes de llamar a TryEnter.</exception>
      <exception cref="T:System.Threading.LockRecursionException">El seguimiento de propiedad de subprocesos está habilitado, y el subproceso actual ya ha adquirido este bloqueo.</exception>
    </member>
    <member name="T:System.Threading.SpinWait">
      <summary>Proporciona compatibilidad con la espera basada en ciclos.</summary>
    </member>
    <member name="P:System.Threading.SpinWait.Count">
      <summary>Obtiene el número de veces que se ha llamado a <see cref="M:System.Threading.SpinWait.SpinOnce" /> en esta instancia.</summary>
      <returns>Devuelve un entero que representa el número de veces que <see cref="M:System.Threading.SpinWait.SpinOnce" /> se ha llamado en esta instancia.</returns>
    </member>
    <member name="P:System.Threading.SpinWait.NextSpinWillYield">
      <summary>Obtiene si la llamada siguiente a <see cref="M:System.Threading.SpinWait.SpinOnce" /> da paso al procesador, lo que activa un cambio de contexto forzado.</summary>
      <returns>Si la llamada siguiente a <see cref="M:System.Threading.SpinWait.SpinOnce" /> da paso al procesador, lo que activa un cambio de contexto forzado.</returns>
    </member>
    <member name="M:System.Threading.SpinWait.Reset">
      <summary>Restablece el contador de ciclos.</summary>
    </member>
    <member name="M:System.Threading.SpinWait.SpinOnce">
      <summary>Realiza un único ciclo.</summary>
    </member>
    <member name="M:System.Threading.SpinWait.SpinUntil(System.Func{System.Boolean})">
      <summary>Itera en ciclos hasta que se satisface la condición especificada.</summary>
      <param name="condition">Delegado que se va a ejecutar una y otra vez hasta que devuelva true.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="condition" /> es nulo.</exception>
    </member>
    <member name="M:System.Threading.SpinWait.SpinUntil(System.Func{System.Boolean},System.Int32)">
      <summary>Itera en ciclos hasta que se satisface la condición especificada o se agota el tiempo de espera indicado.</summary>
      <returns>Es true si la condición se satisface dentro del tiempo de espera; de lo contrario, es false.</returns>
      <param name="condition">Delegado que se va a ejecutar una y otra vez hasta que devuelva true.</param>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="condition" /> es nulo.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> es un número negativo distinto de -1 que representa un tiempo de espera infinito.</exception>
    </member>
    <member name="M:System.Threading.SpinWait.SpinUntil(System.Func{System.Boolean},System.TimeSpan)">
      <summary>Itera en ciclos hasta que se satisface la condición especificada o se agota el tiempo de espera indicado.</summary>
      <returns>Es true si la condición se satisface dentro del tiempo de espera; de lo contrario, es false.</returns>
      <param name="condition">Delegado que se va a ejecutar una y otra vez hasta que devuelva true.</param>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o TimeSpan que representa -1 milisegundo para esperar indefinidamente.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="condition" /> es nulo.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> es un número negativo distinto de -1 milisegundo, que representa un tiempo de espera infinito, o el tiempo de espera es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="T:System.Threading.SynchronizationContext">
      <summary>Proporciona la funcionalidad básica para propagar un contexto de sincronización en varios modelos de sincronización. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.#ctor">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Threading.SynchronizationContext" />.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationContext.CreateCopy">
      <summary>Cuando se invalida en una clase derivada, crea una copia del contexto de sincronización.  </summary>
      <returns>Un nuevo objeto <see cref="T:System.Threading.SynchronizationContext" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.SynchronizationContext.Current">
      <summary>Obtiene el contexto de sincronización del subproceso actual.</summary>
      <returns>Objeto <see cref="T:System.Threading.SynchronizationContext" /> que representa el contexto de sincronización actual.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.OperationCompleted">
      <summary>Cuando se invalida en una clase derivada, responde a la notificación de que se ha completado una operación.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationContext.OperationStarted">
      <summary>Cuando se invalida en una clase derivada, responde a la notificación de que se ha iniciado una operación.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationContext.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Cuando se invalida en una clase derivada, envía un mensaje asincrónico a un contexto de sincronización.</summary>
      <param name="d">Delegado de <see cref="T:System.Threading.SendOrPostCallback" /> al que se va a llamar.</param>
      <param name="state">Objeto que se ha pasado al delegado.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.Send(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Cuando se invalida en una clase derivada, envía un mensaje sincrónico a un contexto de sincronización.</summary>
      <param name="d">Delegado de <see cref="T:System.Threading.SendOrPostCallback" /> al que se va a llamar.</param>
      <param name="state">Objeto que se ha pasado al delegado. </param>
      <exception cref="T:System.NotSupportedException">The method was called in a Windows Store app.The implementation of <see cref="T:System.Threading.SynchronizationContext" /> for Windows Store apps does not support the <see cref="M:System.Threading.SynchronizationContext.Send(System.Threading.SendOrPostCallback,System.Object)" /> method.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.SetSynchronizationContext(System.Threading.SynchronizationContext)">
      <summary>Establece el contexto de sincronización actual.</summary>
      <param name="syncContext">Objeto <see cref="T:System.Threading.SynchronizationContext" /> que se va a establecer.</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence, ControlPolicy" />
      </PermissionSet>
    </member>
    <member name="T:System.Threading.SynchronizationLockException">
      <summary>Excepción que se produce cuando un método requiere que el llamador sea propietario del bloqueo en un Monitor dado y un llamador al que no pertenece ese bloqueo llama al método.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationLockException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.SynchronizationLockException" /> con propiedades predeterminadas.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationLockException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.SynchronizationLockException" /> con el mensaje de error especificado.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
    </member>
    <member name="M:System.Threading.SynchronizationLockException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.SynchronizationLockException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="innerException">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="innerException" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="T:System.Threading.ThreadLocal`1">
      <summary>Proporciona almacenamiento local de los datos de un subproceso.</summary>
      <typeparam name="T">Especifica el tipo de datos que se almacena por subproceso.</typeparam>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor">
      <summary>Inicializa la instancia de <see cref="T:System.Threading.ThreadLocal`1" />.</summary>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor(System.Boolean)">
      <summary>Inicializa la instancia de <see cref="T:System.Threading.ThreadLocal`1" />.</summary>
      <param name="trackAllValues">Si se va a hacer un seguimiento de todos los valores establecidos en la instancia y exponerlos a través de la propiedad <see cref="P:System.Threading.ThreadLocal`1.Values" />.</param>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor(System.Func{`0})">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.ThreadLocal`1" /> con la función especificada por el parámetro <paramref name="valueFactory" />.</summary>
      <param name="valueFactory">
        <see cref="T:System.Func`1" /> que se invoca para generar un valor de inicialización diferida cuando se intenta recuperar <see cref="P:System.Threading.ThreadLocal`1.Value" /> sin que se haya inicializado anteriormente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="valueFactory" /> es una referencia nula (Nothing en Visual Basic).</exception>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor(System.Func{`0},System.Boolean)">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.ThreadLocal`1" /> con la función especificada por el parámetro <paramref name="valueFactory" />.</summary>
      <param name="valueFactory">
        <see cref="T:System.Func`1" /> que se invoca para generar un valor de inicialización diferida cuando se intenta recuperar <see cref="P:System.Threading.ThreadLocal`1.Value" /> sin que se haya inicializado anteriormente.</param>
      <param name="trackAllValues">Si se va a hacer un seguimiento de todos los valores establecidos en la instancia y exponerlos a través de la propiedad <see cref="P:System.Threading.ThreadLocal`1.Values" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="valueFactory" /> es una referencia null (Nothing en Visual Basic).</exception>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Threading.ThreadLocal`1" />.</summary>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.Dispose(System.Boolean)">
      <summary>Libera los recursos utilizados por esta instancia de <see cref="T:System.Threading.ThreadLocal`1" />.</summary>
      <param name="disposing">Valor booleano que indica si se llama a este método debido a una llamada a <see cref="M:System.Threading.ThreadLocal`1.Dispose" />.</param>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.Finalize">
      <summary>Libera los recursos utilizados por esta instancia de <see cref="T:System.Threading.ThreadLocal`1" />.</summary>
    </member>
    <member name="P:System.Threading.ThreadLocal`1.IsValueCreated">
      <summary>Obtiene un valor que indica si <see cref="P:System.Threading.ThreadLocal`1.Value" /> se inicializa en el subproceso actual.</summary>
      <returns>Es true si <see cref="P:System.Threading.ThreadLocal`1.Value" /> se inicializa en el subproceso actual; en caso contrario, es false.</returns>
      <exception cref="T:System.ObjectDisposedException">La instancia de <see cref="T:System.Threading.ThreadLocal`1" /> se ha eliminado.</exception>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.ToString">
      <summary>Crea y devuelve una representación de cadena de esta instancia del subproceso actual.</summary>
      <returns>Resultado de llamar al método <see cref="M:System.Object.ToString" /> en <see cref="P:System.Threading.ThreadLocal`1.Value" />.</returns>
      <exception cref="T:System.ObjectDisposedException">La instancia de <see cref="T:System.Threading.ThreadLocal`1" /> se ha eliminado.</exception>
      <exception cref="T:System.NullReferenceException">La propiedad <see cref="P:System.Threading.ThreadLocal`1.Value" /> del subproceso actual es una referencia nula (Nothing en Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">La función de inicialización intentó hacer referencia de forma recursiva a <see cref="P:System.Threading.ThreadLocal`1.Value" />.</exception>
      <exception cref="T:System.MissingMemberException">No se proporciona ningún constructor predeterminado y no se proporciona ningún generador de valor.</exception>
    </member>
    <member name="P:System.Threading.ThreadLocal`1.Value">
      <summary>Obtiene o establece el valor de esta instancia del subproceso actual.</summary>
      <returns>Devuelve una instancia del objeto que ThreadLocal es responsable de inicializar.</returns>
      <exception cref="T:System.ObjectDisposedException">La instancia de <see cref="T:System.Threading.ThreadLocal`1" /> se ha eliminado.</exception>
      <exception cref="T:System.InvalidOperationException">La función de inicialización intentó hacer referencia de forma recursiva a <see cref="P:System.Threading.ThreadLocal`1.Value" />.</exception>
      <exception cref="T:System.MissingMemberException">No se proporciona ningún constructor predeterminado y no se proporciona ningún generador de valor.</exception>
    </member>
    <member name="P:System.Threading.ThreadLocal`1.Values">
      <summary>Obtiene una lista de todos los valores almacenados actualmente por todos los subprocesos que han tenido acceso a esta instancia.</summary>
      <returns>Lista de todos los valores almacenados actualmente por todos los subprocesos que han tenido acceso a esta instancia.</returns>
      <exception cref="T:System.ObjectDisposedException">La instancia de <see cref="T:System.Threading.ThreadLocal`1" /> se ha eliminado.</exception>
    </member>
    <member name="T:System.Threading.Volatile">
      <summary>Contiene los métodos para realizar operaciones de memoria volátil.</summary>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Boolean@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Byte@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Double@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Int16@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Int32@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Int64@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.IntPtr@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.SByte@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Single@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UInt16@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UInt32@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UInt64@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UIntPtr@)">
      <summary>Lee el valor del campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Valor que se ha leído.El valor es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read``1(``0@)">
      <summary>Lee la referencia al objeto desde el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura tras este método en el código, el procesador no puede moverla antes de este método.</summary>
      <returns>Referencia al <paramref name="T" /> que se ha leído.Esta referencia es el último que haya escrito cualquier procesador del equipo, independientemente del número de procesadores y del estado de la memoria caché del procesador.</returns>
      <param name="location">Campo que se va a leer.</param>
      <typeparam name="T">Tipo del campo que se va a leer.Debe ser un tipo de referencia, no un tipo de valor.</typeparam>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Boolean@,System.Boolean)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Byte@,System.Byte)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Double@,System.Double)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Int16@,System.Int16)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Int32@,System.Int32)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Int64@,System.Int64)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de memoria antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.IntPtr@,System.IntPtr)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.SByte@,System.SByte)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Single@,System.Single)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UInt16@,System.UInt16)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UInt32@,System.UInt32)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UInt64@,System.UInt64)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UIntPtr@,System.UIntPtr)">
      <summary>Escribe el valor especificado en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe el valor.</param>
      <param name="value">Valor que se va a escribir.El valor se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write``1(``0@,``0)">
      <summary>Escribe la referencia de objeto especificada en el campo especificado.En los sistemas que lo requieren, inserta una barrera de memoria que impide que el procesador reordene las operaciones de memoria del modo siguiente: si aparece una operación de lectura o de escritura antes de este método en el código, el procesador no puede moverla después de este método.</summary>
      <param name="location">Campo donde se escribe la referencia de objeto.</param>
      <param name="value">Referencia de objeto que se va a escribir.La referencia se escribe inmediatamente de manera que sea visible para todos los procesadores del equipo.</param>
      <typeparam name="T">Tipo del campo que se va a escribir.Debe ser un tipo de referencia, no un tipo de valor.</typeparam>
    </member>
    <member name="T:System.Threading.WaitHandleCannotBeOpenedException">
      <summary>Excepción que se produce cuando se intenta abrir una exclusión mutua o semáforo del sistema que no existe.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.WaitHandleCannotBeOpenedException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.WaitHandleCannotBeOpenedException" /> con valores predeterminados.</summary>
    </member>
    <member name="M:System.Threading.WaitHandleCannotBeOpenedException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.WaitHandleCannotBeOpenedException" /> con el mensaje de error especificado.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción.</param>
    </member>
    <member name="M:System.Threading.WaitHandleCannotBeOpenedException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.WaitHandleCannotBeOpenedException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción.</param>
      <param name="innerException">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="innerException" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
  </members>
</doc>