﻿Imports System.IO
Imports System.Net
Imports System.Net.Http
Imports System.Text
Imports System.Text.RegularExpressions
Imports DevExpress.LookAndFeel
Imports DevExpress.XtraEditors
Imports DevExpress.XtraRichEdit.Import.Html
Public Class frmMultiLinker
    Private Const VBQuote As String = """"
    ' تعديل دالة ConvertFile لتأخذ النص من RichTextBox1 وتكتب في ملف
    Private Sub ConvertFile(ByVal SourceText As String, ByVal DestFile As String, ByVal Title As String, Optional ByVal DisableClick As Boolean = False)
        Dim sb As New StringBuilder()
        Dim Buffer As String = SourceText
        ' استبدال النصوص بالحقول المدخلة
        If cb_txt_Time.SelectedItem IsNot Nothing AndAlso Not String.IsNullOrEmpty(cb_txt_Time.SelectedItem.ToString()) Then
            Buffer = Buffer.Replace("[-Time-]", cb_txt_Time.SelectedItem.ToString())
        End If
        If RichTextBox1.Text.Contains("[-conversion-]") Then
            If cb_txt_conversion.SelectedItem IsNot Nothing AndAlso Not String.IsNullOrEmpty(cb_txt_conversion.SelectedItem.ToString()) Then
                Buffer = Buffer.Replace("[-conversion-]", cb_txt_conversion.SelectedItem.ToString())
            Else
                XtraMessageBox.Show("Please select a value for conversion.", "Missing Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If
        End If
        If Not String.IsNullOrEmpty(txt_Link_New.Text) Then
            Buffer = Buffer.Replace("[-Link1-]", txt_Link_New.Text)
        End If
        If Not String.IsNullOrEmpty(txt_outlook.Text) Then
            Buffer = Buffer.Replace("[-Link2-]", txt_outlook.Text)
        End If
        If Not String.IsNullOrEmpty(txt_Logo_Page.Text) Then
            Buffer = Buffer.Replace("[-Logo-]", txt_Logo_Page.Text)
        End If
        If Not String.IsNullOrEmpty(txt_Type.Text) Then
            Buffer = Buffer.Replace("[-Type-]", txt_Type.Text)
        End If
        If Not String.IsNullOrEmpty(txt_Logo_Button.Text) Then
            Buffer = Buffer.Replace("[-LogoButton-]", txt_Logo_Button.Text)
        End If
        If Not String.IsNullOrEmpty(txt_Button.Text) Then
            Buffer = Buffer.Replace("[-txtButon-]", txt_Button.Text)
        End If
        If Not String.IsNullOrEmpty(txt_width_Outlook.Text) Then
            Buffer = Buffer.Replace("[-txtwidth-]", txt_width_Outlook.Text)
        End If
        If Not String.IsNullOrEmpty(txt_length_Outlook.Text) Then
            Buffer = Buffer.Replace("[-txtheight-]", txt_length_Outlook.Text)
        End If
        If Not String.IsNullOrEmpty(txt_length_Outlook_B.Text) Then
            Buffer = Buffer.Replace("[-txtwidthB-]", txt_length_Outlook_B.Text)
        End If
        If Not String.IsNullOrEmpty(txt_width_Outlook_B.Text) Then
            Buffer = Buffer.Replace("[-txtheightB-]", txt_width_Outlook_B.Text)
        End If
        ' تحقق من النقر بزر الماوس الأيمن
        If DisableClick Then
            Buffer &= "<script>document.oncontextmenu=new Function(" & VBQuote & "return false" & VBQuote & ")</script>"
        End If
        ' بناء الملف
        sb.AppendLine("<html>")
        sb.AppendLine("<head>")
        sb.AppendLine("<title>" & Title & "</title>")
        sb.AppendLine("<body>")
        sb.AppendLine("<script language=" & VBQuote & "JavaScript" & VBQuote & " type=" & VBQuote & "text/javascript" & VBQuote & ">")
        sb.Append("document.write(unescape('")
        For Each c As Char In Buffer
            sb.Append("%" & Asc(c).ToString("X2"))
        Next c
        sb.Append("'));")
        sb.AppendLine("</script>")
        sb.AppendLine("</body>")
        sb.AppendLine("</html>")
        Dim sw As New StreamWriter(DestFile)
        sw.Write(sb.ToString())
        sw.Close()
        Console.WriteLine("File Successfully written to: " & DestFile)
    End Sub
    Private Sub frmMultiLinker_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Label2.Visible = False
        If My.Settings.Link_txt <> "" Then
            txt_Link.Text = My.Settings.Link_txt
        End If
        If My.Settings.Type_txt <> "" Then
            txt_Type.Text = My.Settings.Type_txt
        End If
        If My.Settings.Outlook_txt <> "" Then
            txt_outlook.Text = My.Settings.Outlook_txt
        End If
        If My.Settings.Button_txt <> "" Then
            txt_Button.Text = My.Settings.Button_txt
        End If
        If My.Settings.Link_New <> "" Then
            txt_Link_New.Text = My.Settings.Link_New
        End If
        If My.Settings.Logo_Button <> "" Then
            txt_Logo_Button.Text = My.Settings.Logo_Button
        End If
        If My.Settings.Logo_Page <> "" Then
            txt_Logo_Page.Text = My.Settings.Logo_Page
        End If

        ' Add event handler for tab page changes
        AddHandler XtraTabControl1.SelectedPageChanged, AddressOf XtraTabControl1_SelectedPageChanged

        ' Trigger the resize event to center controls on initial load
        frmMultiLinker_Resize(Me, New EventArgs())
    End Sub

    Private Sub XtraTabControl1_SelectedPageChanged(sender As Object, e As DevExpress.XtraTab.TabPageChangedEventArgs)
        ' Center all controls in the newly selected tab page
        If e.Page IsNot Nothing Then
            CenterAllControls(e.Page)
        End If
    End Sub

    ' Event handlers for direct tab page clicks
    Private Sub XtraTabPage1_Click(sender As Object, e As EventArgs) Handles XtraTabPage1.Click
        CenterAllControls(XtraTabPage1)
    End Sub

    Private Sub XtraTabPage2_Click(sender As Object, e As EventArgs) Handles XtraTabPage2.Click
        CenterAllControls(XtraTabPage2)
    End Sub

    Private Sub XtraTabPage3_Click(sender As Object, e As EventArgs) Handles XtraTabPage3.Click
        CenterAllControls(XtraTabPage3)
    End Sub

    ' Event handlers for tab page loads
    Private Sub XtraTabPage1_VisibleChanged(sender As Object, e As EventArgs) Handles XtraTabPage1.VisibleChanged
        If XtraTabPage1.Visible Then
            CenterAllControls(XtraTabPage1)
        End If
    End Sub

    Private Sub XtraTabPage2_VisibleChanged(sender As Object, e As EventArgs) Handles XtraTabPage2.VisibleChanged
        If XtraTabPage2.Visible Then
            CenterAllControls(XtraTabPage2)
        End If
    End Sub

    Private Sub XtraTabPage3_VisibleChanged(sender As Object, e As EventArgs) Handles XtraTabPage3.VisibleChanged
        If XtraTabPage3.Visible Then
            CenterAllControls(XtraTabPage3)
        End If
    End Sub
    Dim linkPattern As String = "^(https?)://[^\s/$.?#].[^\s]*$"


    Private Function IsHttpUrl(url As String) As Boolean
        Return url.StartsWith("http://", StringComparison.OrdinalIgnoreCase) OrElse url.StartsWith("https://", StringComparison.OrdinalIgnoreCase)
    End Function
    ' Function to check if the URL points to an image
    Private Function IsImageUrl(url As String) As Boolean
        ' Check if the URL ends with valid image extensions
        Dim extensions As String() = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff"}
        For Each ext As String In extensions
            If url.EndsWith(ext, StringComparison.OrdinalIgnoreCase) Then
                Return True
            End If
        Next
        Return False
    End Function
    ' Function to load image from Base64 string
    Private Sub LoadImageFromBase64(base64String As String)
        Try
            ' Extract the Base64 part of the string (after "data:image/png;base64,")
            Dim base64Data As String = base64String.Substring(base64String.IndexOf(",") + 1)
            Dim imageBytes As Byte() = Convert.FromBase64String(base64Data)
            Using ms As New MemoryStream(imageBytes)
                Dim image As Image = Image.FromStream(ms)
                ' Display the image in the PictureBox
                Pic_L_B.Image = image
                ' Display the image dimensions in the Label
                lbl_S_B.Text = $"Width: {image.Width} px, Height: {image.Height} px"
                ' Set length and width in txt_length_Outlook_B and txt_width_Outlook_B
                txt_length_Outlook_B.Text = image.Height.ToString()
                txt_width_Outlook_B.Text = image.Width.ToString()
            End Using
        Catch ex As Exception
            XtraMessageBox.Show($"An error occurred while processing the Base64 data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    ' Function to load image from URL
    Private Sub LoadImageFromUrl(imageUrl As String)
        Try
            ' Create HTTP request to the image URL
            Dim request As HttpWebRequest = DirectCast(WebRequest.Create(imageUrl), HttpWebRequest)
            request.Method = "GET"
            Using response As HttpWebResponse = DirectCast(request.GetResponse(), HttpWebResponse)
                ' Check for valid response code (200 OK)
                If response.StatusCode = HttpStatusCode.OK Then
                    Using stream As Stream = response.GetResponseStream()
                        ' Load the image from the response stream
                        Dim image As Image = Image.FromStream(stream)
                        ' Display the image in the PictureBox
                        Pic_L_B.Image = image
                        ' Display the image dimensions in the Label
                        lbl_S_B.Text = $"Width: {image.Width} px, Height: {image.Height} px"
                        ' Set length and width in txt_length_Outlook_B and txt_width_Outlook_B
                        txt_length_Outlook_B.Text = image.Height.ToString()
                        txt_width_Outlook_B.Text = image.Width.ToString()
                    End Using
                Else
                    XtraMessageBox.Show("Failed to load the image. The server returned an error.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End If
            End Using
        Catch ex As Exception
            XtraMessageBox.Show($"Failed to load image from URL: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Async Sub txt_Logo_Button_TextChanged(sender As Object, e As EventArgs) Handles txt_Logo_Button.TextChanged
        ' Process image for Logo_Button
        Await ProcessImageUrl(txt_Logo_Button.Text, Pic_L_B, lbl_S_B, txt_length_Outlook_B, txt_width_Outlook_B)
    End Sub
    Private Async Sub txt_Logo_Page_TextChanged(sender As Object, e As EventArgs) Handles txt_Logo_Page.TextChanged
        ' Process image for Logo_Page
        Await ProcessImageUrl(txt_Logo_Page.Text, Pic_Logo_Page, lbl_Size_Logo_Page, txt_length_Outlook, txt_width_Outlook)
    End Sub



    Private Async Function ProcessImageUrl(imageUrl As String, pictureBox As System.Windows.Forms.PictureBox, labelSize As System.Windows.Forms.Label, txtLength As DevExpress.XtraEditors.TextEdit, txtWidth As DevExpress.XtraEditors.TextEdit) As Task
        ' Validate if the URL is not empty and looks like a valid URL
        If Not String.IsNullOrEmpty(imageUrl) AndAlso (Uri.IsWellFormedUriString(imageUrl, UriKind.Absolute) OrElse imageUrl.StartsWith("data:image")) Then
            Try
                ' Check if it's a base64 image
                If imageUrl.StartsWith("data:image") Then
                    ' Extract the Base64 part of the string (after "data:image/png;base64,")
                    Dim base64Data As String = imageUrl.Substring(imageUrl.IndexOf(",") + 1)
                    Dim imageBytes As Byte() = Convert.FromBase64String(base64Data)
                    Using ms As New MemoryStream(imageBytes)
                        Dim image As Image = Image.FromStream(ms)
                        ' Display the image in the PictureBox
                        pictureBox.Image = image
                        ' Display the image dimensions in the Label
                        labelSize.Text = $"Width: {image.Width} px, Height: {image.Height} px"
                        ' Set length and width in the provided TextBoxes
                        txtLength.Text = image.Height.ToString()
                        txtWidth.Text = image.Width.ToString()
                    End Using
                Else
                    ' Check if it's an SVG file
                    If imageUrl.ToLower().EndsWith(".svg") Then
                        ' For SVG files, we'll try to convert to PNG by modifying the URL
                        ' Many services like logo.wine provide both SVG and PNG versions
                        Dim pngUrl As String = imageUrl.ToLower().Replace(".svg", ".png")

                        ' Try to load the PNG version
                        Using client As New HttpClient()
                            Try
                                ' Get the image as byte array from the URL
                                Dim imageBytes As Byte() = Await client.GetByteArrayAsync(pngUrl)
                                ' Convert the byte array to an image
                                Using ms As New IO.MemoryStream(imageBytes)
                                    Dim img As Image = Image.FromStream(ms)
                                    ' Set the image to PictureBox
                                    pictureBox.Image = img
                                    ' Get the width and height of the image
                                    Dim width As Integer = img.Width
                                    Dim height As Integer = img.Height
                                    ' Display the size in label
                                    labelSize.Text = $"Width: {width} px, Height: {height} px"
                                    ' Set length and width in the provided TextBoxes
                                    txtLength.Text = height.ToString()
                                    txtWidth.Text = width.ToString()
                                End Using
                            Catch ex As Exception
                                ' If PNG conversion fails, show a message about SVG
                                labelSize.Text = "SVG files are not directly supported. Try a PNG or JPG URL."
                                pictureBox.Image = Nothing
                            End Try
                        End Using
                    Else
                        ' Create an HttpClient instance to download the image
                        Using client As New HttpClient()
                            ' Get the image as byte array from the URL
                            Dim imageBytes As Byte() = Await client.GetByteArrayAsync(imageUrl)
                            ' Convert the byte array to an image
                            Using ms As New IO.MemoryStream(imageBytes)
                                Dim img As Image = Image.FromStream(ms)
                                ' Set the image to PictureBox
                                pictureBox.Image = img
                                ' Get the width and height of the image
                                Dim width As Integer = img.Width
                                Dim height As Integer = img.Height
                                ' Display the size in label
                                labelSize.Text = $"Width: {width} px, Height: {height} px"
                                ' Set length and width in the provided TextBoxes
                                txtLength.Text = height.ToString()
                                txtWidth.Text = width.ToString()
                            End Using
                        End Using
                    End If
                End If
            Catch ex As Exception
                ' Show an error message if there is an issue downloading or processing the image
                labelSize.Text = "Error: Unable to load the image. " & ex.Message
                pictureBox.Image = Nothing ' Clear the image in PictureBox
            End Try
        Else
            ' Clear the size label and the image if the URL is invalid
            labelSize.Text = ""
            pictureBox.Image = Nothing
        End If
    End Function



    Private Async Sub txt_Link_TextChanged(sender As Object, e As EventArgs) Handles txt_Link.TextChanged
        ' Process image for Link
        Await ProcessImageUrl(txt_Link.Text, PictureBoxSee, lbl_Logo_Example, txt_length_Example, txt_width_Example)
    End Sub
    Private Sub SimpleButton3_Click(sender As Object, e As EventArgs)
        ' التحقق من أن حقل txt_Link غير فارغ
        If txt_Link.Text.Trim = "" Then
            'Pic_Error6.Visible = True
            XtraMessageBox.Show("Choose the logo first.", "Text", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
        ' تغيير لون النص في txt_Link
        txt_Link.ForeColor = Color.FromArgb(34, 203, 121)
        ' تحديث النصوص لعرض رسالة نجاح
        Label11.Text = "The logo has been transferred Successfully. Check the Logo Settings menu"
        ' تفريغ الحقول المرتبطة بعرض مثال الشعار
        lbl_Logo_Example.Text = ""
        txt_length_Example.Text = ""
        txt_width_Example.Text = ""
        PictureBox2.Image = Nothing
        ' التحقق من وجود نص في txt_Logo_Page وتفريغه إذا لزم الأمر
        If txt_Logo_Page.Text <> "" Then
            txt_Logo_Page.Text = "" ' حذف النص الموجود
        End If
        ' نسخ النص من txt_Link إلى txt_Logo_Page
        txt_Logo_Page.Text = txt_Link.Text
        ' التحقق من وجود نص في txt_Logo_Button وتفريغه إذا لزم الأمر
        If txt_Logo_Button.Text <> "" Then
            txt_Logo_Button.Text = ""
        End If
        ' نسخ النص من txt_Link_Icon إلى txt_Logo_Button
        txt_Logo_Button.Text = txt_Link_Icon.Text
    End Sub

    Private Sub BTNSAVEALL_Click(sender As Object, e As EventArgs) Handles BTNSAVEALL.Click
        Try
            ' حفظ القيم في الإعدادات
            My.Settings.Logo_Page = txt_Logo_Page.Text
            My.Settings.Logo_Button = txt_Logo_Button.Text
            My.Settings.Button_txt = txt_Button.Text
            My.Settings.Link_New = txt_Link_New.Text
            My.Settings.Outlook_txt = txt_outlook.Text
            My.Settings.Type = txt_Type.Text
            My.Settings.Link = txt_Link.Text
            ' حفظ التغييرات في الإعدادات
            My.Settings.Save()
            ' إعلام المستخدم بنجاح العملية
            XtraMessageBox.Show("Settings saved Successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            ' معالجة الأخطاء
            XtraMessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub Bnt_Reset_All_Click(sender As Object, e As EventArgs) Handles Bnt_Reset_All.Click
        txt_Logo_Button.Clear()
        lbl_Size_Logo_Page.Text = ""
        txt_Button.Clear()
        txt_Logo_Page.Clear()
        txt_Link_New.Clear()
        txt_outlook.Clear()
        txt_Type.Clear()
        txt_length_Outlook.Clear()
        txt_width_Outlook.Clear()
        txt_length_Outlook_B.Clear()
        txt_width_Outlook_B.Clear()
        txt_length_Example.Clear()
        txt_width_Example.Clear()
        txt_Link.Clear()
        lbl_Logo_Example.Text = ""
        Pic_L_B.Image = Nothing
        Pic_Logo_Page.Image = Nothing
        PictureBox2.Image = Nothing
        PictureBoxSee.Image = Nothing
        Label2.Visible = False
        Label6.Visible = False

    End Sub
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        txt_Logo_Button.Clear()
        lbl_Size_Logo_Page.Text = ""
        txt_Button.Clear()
        txt_Logo_Page.Clear()
        txt_Link_New.Clear()
        txt_outlook.Clear()
        txt_Type.Clear()
        txt_length_Outlook.Clear()
        txt_width_Outlook.Clear()
        txt_length_Outlook_B.Clear()
        txt_width_Outlook_B.Clear()
        txt_length_Example.Clear()
        txt_width_Example.Clear()
        txt_Link.Clear()
        lbl_Logo_Example.Text = ""
        Pic_L_B.Image = Nothing
        Pic_Logo_Page.Image = Nothing
        PictureBox2.Image = Nothing
        PictureBoxSee.Image = Nothing
        Label2.Visible = False
        Label6.Visible = False
        '======================
        txt_Logo_Page.Text = "https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/UHFbanner-MSlogo?fmt=png-alpha&bfc=off&qlt=100,1"
        txt_Logo_Button.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAP1BMVEXzUyWBvAYFpvD/ugj////zTBf3lIGt0n19ugB9w/T/0H0Ao/D/uAD2kX2r0Xn/z3nyRQD5uK7I4K2t1/j/360H9FLlAAAAVUlEQVRIie3LtxGAQBADwAP0lje4/mtlKEDJDaE2XzMmRCDlhaFPUVFRUdEXA3N0YGTKItNP4BqJMTj5Y6Fu4JmVsZ0qwGyUrcwXa9sYRUVFRcV/4wvoSiA+bs30KgAAAABJRU5ErkJggg=="
        txt_Button.Text = "Continue with your account"
        txt_length_Outlook_B.Text = "25"
        txt_width_Outlook_B.Text = "25 ="
        txt_length_Outlook.Text = "46"
        txt_width_Outlook.Text = "216"
        txt_Link.Text = "https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/UHFbanner-MSlogo?fmt=png-alpha&bfc=off&qlt=100,1"
        txt_Link_New.Text = "https://www.google.ru/"
        txt_outlook.Text = "https://www.office.com"
        txt_Type.Text = "Sign in to your Microsoft account to continue "
    End Sub

    Private Sub Timer2_Tick(sender As Object, e As EventArgs) Handles Timer2.Tick

    End Sub

    Private Sub frmMultiLinker_Resize(sender As Object, e As EventArgs) Handles Me.Resize
        ' Resize and center the main XtraTabControl1 when the form is resized
        If XtraTabControl1 IsNot Nothing Then
            ' Set margins (space between form edge and control)
            Const margin As Integer = 12

            ' Calculate new size for XtraTabControl1 based on form size
            Dim newWidth As Integer = Me.ClientSize.Width - (margin * 2)
            Dim newHeight As Integer = Me.ClientSize.Height - (margin * 2)

            ' Resize XtraTabControl1
            XtraTabControl1.Width = newWidth
            XtraTabControl1.Height = newHeight

            ' Center XtraTabControl1 in the form
            XtraTabControl1.Left = margin
            XtraTabControl1.Top = margin

            ' Center all contents of each tab page
            For Each tabPage As DevExpress.XtraTab.XtraTabPage In XtraTabControl1.TabPages
                CenterAllControls(tabPage)
            Next
        End If
    End Sub

    Private Sub CenterAllControls(tabPage As DevExpress.XtraTab.XtraTabPage)
        Try
            ' Get all controls in the tab page
            Dim controls As Control.ControlCollection = tabPage.Controls

            ' Skip if no controls
            If controls.Count = 0 Then
                Return
            End If

            ' Find the bounds of all controls to determine the "content area"
            Dim minLeft As Integer = Integer.MaxValue
            Dim minTop As Integer = Integer.MaxValue
            Dim maxRight As Integer = 0
            Dim maxBottom As Integer = 0

            ' Calculate the bounds of all controls
            For Each ctrl As Control In controls
                If ctrl.Visible Then
                    minLeft = Math.Min(minLeft, ctrl.Left)
                    minTop = Math.Min(minTop, ctrl.Top)
                    maxRight = Math.Max(maxRight, ctrl.Left + ctrl.Width)
                    maxBottom = Math.Max(maxBottom, ctrl.Top + ctrl.Height)
                End If
            Next

            ' If no visible controls or invalid bounds, exit
            If minLeft = Integer.MaxValue Or maxRight = 0 Then
                Return
            End If

            ' Calculate content area dimensions
            Dim contentWidth As Integer = maxRight - minLeft

            ' Ensure we don't have negative or zero width
            If contentWidth <= 0 Or tabPage.Width <= 0 Then
                Return
            End If

            ' Calculate the offset to center the content area horizontally
            ' Use Long to avoid overflow
            Dim offsetX As Long = CLng(tabPage.Width - contentWidth) \ 2 - CLng(minLeft)

            ' Ensure the offset is within reasonable bounds
            If offsetX > Integer.MaxValue Or offsetX < Integer.MinValue Then
                Return
            End If

            ' Apply the horizontal offset to all controls to center them as a group
            For Each ctrl As Control In controls
                ctrl.Left += CInt(offsetX)
            Next
        Catch ex As Exception
            ' Silently handle any exceptions to prevent application crashes
            ' Could log the error here if needed
        End Try
    End Sub

    Private Sub ComboBoxEdit1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBoxEdit1.SelectedIndexChanged
        ' التحقق من العنصر المحدد في ComboBoxEdit1
        Select Case ComboBoxEdit1.SelectedItem.ToString()
            Case "Microsoft"
                txt_Link.Text = "https://cdn-dynmedia-1.microsoft.com/is/image/microsoftcorp/UHFbanner-MSlogo?fmt=png-alpha&bfc=off&qlt=100,1"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAP1BMVEXzUyWBvAYFpvD/ugj////zTBf3lIGt0n19ugB9w/T/0H0Ao/D/uAD2kX2r0Xn/z3nyRQD5uK7I4K2t1/j/360H9FLlAAAAVUlEQVRIie3LtxGAQBADwAP0lje4/mtlKEDJDaE2XzMmRCDlhaFPUVFRUdEXA3N0YGTKItNP4BqJMTj5Y6Fu4JmVsZ0qwGyUrcwXa9sYRUVFRcV/4wvoSiA+bs30KgAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Yahoo"
                txt_Link.Text = "https://download.logo.wine/logo/Yahoo!_Mail/Yahoo!_Mail-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAilBMVEVgAdJfANJXANBVANBdANFmANTFrO3LtO+/oOyBRNuneeX8+f7////dzfVvFtbm3vexkOf28PxzHteDUdqig+Kkh+N7Pdl0LdfTwvGdbuL69f65meqRYd/06/x3KNljCdPk2ffl1PjBp+yiceSEStudduHUtvOia+R6NdnXwfOEU9rfy/aUWuDPu/BV3RqvAAAA40lEQVR4Ac2QRQIDIQwACWR96u5uK/9/Xt3l3sEZgpk/QaxzVn84zw+CMPpubZxAqSzfAyuQVO1trNaez5BTa+MaVOp6c41mq9lWo51j2/C6QPgI7PXBt8YNoN8bjqA/1psUU4LKWCdTmHlzzivv2EVCv+riGkm8rAB1fUjRGnRPh9V0NRqN1u7laRsYhRWIbb2szr19WMIZ2YyYLd/e76pnF8oMGExerW77QNIun2TVvEqZhMB6Ir1pbRe9OiMSAz2VSZR2zBtSzmC3VSOq8i41B5pqvmIHlUoRvcmHdc6p+QMOsc8N//waDKcAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Gmail"
                txt_Link.Text = "https://download.logo.wine/logo/Gmail/Gmail-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAV1BMVEVHcEzZUUXXd2/z9PTeU0fZbmbo5+f0+vrx8vLbQjPaSz7WTkHotLHBOyzDTkPAOCjhhX7ZeHHEo6DqzMrFxcS8cGrae3Ti4uLMdm/p6urQ1NS8OSrl5eWBeqqfAAAAHXRSTlMAc5qd//z//////6Pxof3//+n////y6P/z//9ysazRhhoAAADtSURBVHgB1ZJXgoMwDERTXEUH7wo5uf85Y4mOyQEyCXXevC9uP5L74/klj7v0SmljXRZrtAcmCqWULzPClt4DFDOgIEmyeUrFQM0ES85zgEYAqGtgonUzYp3Mfd3MAHQ9OwAGIewgNYS/FfjHMC4S61rpx4A7gDD0MEkG4B76iDRugEEMQXuWeFDgdYhoTgCG2INYeJ76DEhEksA0R5MbmIi99zxHk+I2oJsAjDEGnvPekMsMTMiF97Qz+MWAfJ33RDlgnKNZQFcAOWJC/FdAaoWQPgcM1zNBR+DFgHTLfwe85KMsHu/LPCrufyEfyewjNsxN1fgAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Outlook"
                txt_Link.Text = "https://www.freepnglogos.com/uploads/logo-outlook-png/microsoft-outlook-amazon-appstore-pour-android-6.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAIAAAD8GO2jAAAABnRSTlMAAAAAAABupgeRAAAAWklEQVR4AWP4FKBEJPrip8wABrV7GOp3EYtGhgWjFoxaMGrBqAXf/OSIRD995CEWVO9hqN1FLBoOYNlnotF3iI7/O4lHQOVL3hONPo9agB2NWjBqwagFoxYAAB2zMZ7bQW2tAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Aol"
                txt_Link.Text = "https://download.logo.wine/logo/AOL/AOL-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAARVBMVEVHcEwAR/8AR/8AR/8AR/8AR/8AR/8ARf8APf8VU/9jhP/m7f+ar/9Pdv/N1//W3/81Zf/////1+P+Iov95l/+yw/8AL/8CDIY3AAAAB3RSTlMAO6DU7/+4Fuj9PwAAANRJREFUeAF9kwsOwyAIQO3HgVqx+Lv/Ucd03dJs+hIk4UViFJWwrNuub+zbuqjGQ//lMXbdLnrIotaxXNU2lpvax3JXesJbAlwZfiRqgyAKjUEEkPhKtM4dCOjJuWDxJFkuCYYFiz7yi3Qy54/ExBy5VGI+cuTob5I4HByNY6o1iYmX7F1LllqTR5cVQWTv+qKQROptS84GmgwciZwU+4EytHw2qUNItfoQ/FmcI4toiSj0nYCIbQWQjC1aSeQduN/thOmTTR97OibTAZuN5nyop9/hCdgvFht1LdBLAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Aliexpress"
                txt_Link.Text = "https://download.logo.wine/logo/AliExpress/AliExpress-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAACqklEQVRYhe2Xz0vTYRzHX8/z3S8T1hyKJSEhBh5kSRjYyDSkg2JQ0MU/IIToFNnu3iajUwQjBQ9Wl26NDCIcIXYIlMybMDqkBIpjY1+duj1Ph21mtE239uPS+/jl+3xer+fh+3yf5yPIRgcZBHwavAKcVCEa4gKWAL8YJwwgsvDHgB+Q1QDniQJ8YpyAyM78Yw3hxyWGpAZfHeBkmT6LAC+ASoL5VZCMSNJx0LqyNCHAcIKjQ9F4WSMdAHiFDqIPt2EnZKBMUVlqgchGjXs0jbUZpErWFg6gTJFhJkEmVmRN4cclEisSmYzUHp5LMiKQ6Vjd+KRjINFFVkBUYHcWq6FF4f3vmnpF68IGVk9f2Wyrp4/W8AauqZcF38kvICWOoTvIpmbs/cNlC9j7h5GuZhxDd0EWQOV9qhQqFgXAaDlftkBurIpFQakSBIDU+jcAbFeuly2QG5urVZLA/uJ7AIz2Tmy9A6XDewcw2jv/qFWSwF5oDrVrIoTAOREAq+30dKsN50QAIQRq12QvNFe6gIpuY84GMvW6enBNToNhORluWHBNTmPt6gHAnA2gotulCwAkZvwcLC8C0DAyhjs4f7SsedntnbiD8zSMjAFwsLxIYsZf1FdseixFD15x1o37eQhb91UAdOqQ/U/v2P/8gdSPCACWCx3Yr93CfmMEYbFm4Gtf2Hkwio7t/JsAAI4GnI+mOHPvPsIwir6q02l237wg/vQJJPdOLH06gWwsl7ppHHuI/eZtZFMLQmR+41prVHSL/YW3mK+fkVpfO23J0gR+jxIYbReRLecAUFs/SW9+L+saVZ5ABVOPy+h/gb8E4nXkxyWZXq1eWZJkesL8h3V1owC/bFtNhcm0Z7WUUICvbTUVPrqRbnosg1kRL1Vqz8l8b0uAPztxfgEVaeU3I5lENgAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Amazon"
                txt_Link.Text = "https://download.logo.wine/logo/Amazon_(company)/Amazon_(company)-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAADsUlEQVRYhbWWT2gcVRzHP8/sBsSkpkLUpdlQRAgptexuqrR6EcxqDipWacCLB4PJwUNpwEUwiAgt4sE/NHdLwIM1NFvw4CUU04JJs7HSRDYbD1VCNiRBiTtJDmnh62F3pjOZyc40ab/wmHlvfu/9vu/7+/3eG6hBUpOkjyXN6+HhlqRBSY/ihqQOSX8+RMdBRNoBjKQm4CbwLBFhWRYLCwtO3xhDIpEgkUhEXQJgFjiOpE+j0h7L59WdzSoWjzutwfXenc1qLJ+/HyXOoAjSVyoVdWezHmf1Wi6Xi0qgQBSr07293l3HYqFkIiqxFUogn88HOkhlMjrd2+sLid1eyWajEFAogQ/6+0MlzuVygSSi4JGwVDXG+MaGhoY8/b6+PgROux8YSaFz3GVnjCGTyfhs4o2Nnr6Au9vboQRiUVg2NzfT1dXl9JeXlymXy/w6OQnAxMRE1akUqNi+CdhOv7t4kR9HR5mbnfV8E2AIDlcooiTKWD6vtmQyMNHqlWMUhObAzMwMJ06edHZpPwEGz54l2d7Okc5OXuvp8akSJQdCFUil076ddWezWlpa8tjtVYG6BEqlUuDChULBY1cul30haXgQ50Bxft6R0y2tuyIAisWi825cz+Xl5dAI1CWwuLjoWdR+tyzLY/fDpUuB829MT4cSqBuC8fFx5/LZ7SgeGRnx2dhhSGcyqlQqdUNQl4A7tjtLL5VOqy2Z3LUM7fELw8N7JyD5r+LdWiqT8Y21JZMqlUr7I2D/jPh2WJO8oRYSy7KcW9FWKMx5JAI2iQvDww6RhlhMqUxG/QMDvpI8d/68+gcGZFmu2G8sSavT0soNadubE96T8I9v4bEkHH47PHujYKsMUx/Cav5eLR/7Bo6ccUy8ZXioB37rh6unYK2wfwKNB+DoJ/D6ErxTgaZO3w+Dl0BLB7xxG7b/hfHnq0T+ugx3NvZGYHUK5s7BT4dgswwbRWh90WNiJN0FGjyjdyy4+Rnc/gpUu4CS70PLMXjypSrReDO+OeslWJ+D1WuwcgW2/6meXCcuQ/wAzH0Or/7invWfkXQLeC5wB2sFmHwPNov3jsOdd6dxje38HXjqLUh/AY93wNp0lURLh9viupH0EfBlIAEbf4/B/Newfo2ge1ly+TbAM4Nw+F1oPV53WWDASGoEpoBUmDVbZVibgs1FWLlac2ggfrAanoNHofUFf3iCcR14ubYDPS3pSpQz4QHhe0lP2II5kHQKeBPoYre82Dt+B2aAUWPMz/bg/5AZ2OKL1i4FAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Cashapp"
                txt_Link.Text = "https://download.logo.wine/logo/Cash_App/Cash_App-Full-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAkFBMVEUA1jIA1TIA1DIA0ycf1T933YVQ22QA0zIA0BCn6q/////c9+AA0jE82FXm+eie6KgAzwBE2Fu978QA0TFk3XUAzBnJ8s7y/PSC4o8AzCUA0DGT5Z7q+u0AzzHT9Nip6rIAzjFG0Vq17LwAzTEd0D8AzDEAyCBX2GsAyzEpzUQAyjBx3IBm2XYAyTAAyDAAxzCY2C3FAAABB0lEQVR4AX3LBZaDQBBF0cahcKkJGkPiyf53NzUN1eNc/L+DWKWtWI/6N5phGLryI5qWZWuf0flCcz0A33WYcAK6y8twQysCiBOdNxEwI41AykxjmUQeBHke0D0EpTDklot8gW8Am8IMy4hiNW+iXrgbgMbNg8qNC1w2FbGlP1O3yuuq4k10i8oDEjU1doqKHW5B2mGt4l7BspXVR15UJBUejkDKimO/qLDr+z0OFI+4bBzRawesqgopehXHUZoaGjPrZPn0PPTj7COex7G/RMB2KDc6xXnWY9PK1Fp4ZuJ8nY/rNFG6DTjxcr6KKxvvFPH6lXgo1/RwuT6+4iirShyfK9bja8U7sxBHp/OyHEQAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Binance"
                txt_Link.Text = "https://download.logo.wine/logo/Binance/Binance-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAQlBMVEXwuQsMDhEAABH0vAv5wAsAAxHksAttVQ8SERG1jA3dqgyyig14XQ+4jg1mTxAKDBEYFRGrhA4hGxHElw1gSxBzWQ946PCfAAABSUlEQVRIie3W25KDIAwAUAmK4qWU7e7//+qqJAIFxTA7+1SeOjqnxCRcmuYz/nXIcZRVThmjKqRUQ9cNfCnVsxOCL1cHQvClVF+740rveJLi5MrY3ZfeAX2nuCMDN88MudUdXd+2/e3cRq5pvDSlvqXv2l0ooTDjAiZwhzQwFWZsd3k4lKtrrxBJdFKiJJfHUvUk8Ye19J3o5lxu13zCw71/uZk0IJAv+oNnKvc6oCRnKEQKWaQS64cSXZQUl96kE0bj6g7f7rmcXFkMWHzwAOqEv4FpqIsLVRdCzSRn2ZKj4+TklklajgXIHeXILq+jATQ1wDS9NcDPyRIhd95yF43u6pc2ub1q8m0SfbasbOVChtJCPts6eJsOw0XSb4+3tuRAMg8eL/mHRyh5x5WX3AOSJP9IdrLmEjBUXR7qryv1F6T6K9ln1I9fSXYOsb/udscAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Facebook"
                txt_Link.Text = "https://download.logo.wine/logo/Facebook/Facebook-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAgVBMVEUAAAAQcP8IZf8IZ/8JZv8HZf8IZv8IZv8IaP8JZ/8HZv8IZv8FZf8YcP9FjP+TvP/g7P/////R4/9Vlf8QYP+Es/9kn/8IZv8nef8JZf8AYP/v9f/Q4v/B2P9GjP8HZv+yz//Q4/83g/8HZv/g6/+Dsv8HZf/n7//////////e6//ZLyHjAAAAK3RSTlMAEGCfz+//XyCQj98w/////////xD//6D/kBD/////7////8///5Cgz+/vONkvXQAAAPJJREFUeAF9kkUCwzAMBGVSGMrM3P//rxBaB+e6s0YREFJpw2y0cgS1cT3DQLmNWPjcwK/XA24RWIuEdg4j7OtHUX0NYedxko5+jCeZMc0En8FsVDDHSd1WDoFdIlogX46awopozWA+ythsd7s9ZxymJBkcs3wcMZC0YHDKhDNbKLowuGYC21zINIWUbQ7EwwJT7YogqgTTKaTY4tIp7HDIRadwwzVlKVyv11HG9cekFBxam8FbTInuQ4LCd3cL2Uzd+4UV/VkHfUIgMLRdQuBi7JsCxh5rQEAfrO9NYSWojruwBOOhDoR8PF+j0fuipNX+AmbCIviMIiwCAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Meta"
                txt_Link.Text = "https://download.logo.wine/logo/Meta_Platforms/Meta_Platforms-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAADn0lEQVR4Ae2WR3bbaBCEG2Bc6gbmEXQEHkE7b52tnLNIDjS6APeTeIPhEbic5eRsM1NZBHPyc7vQIPBkJKct671SFr/6u9F/k2aayVePinP0rLgJ5+lZqQb/D39LT18/oCBp43k6HWfp60nB9HiTtEHi0+AmWIfZdOm+36rPEcQFbs5RZpQDlD19OtE+Fp6zwF4BFMPP4Rfl/8nSJuAnvZ8pMwRozL4hzsa5D8E1mP0CACxWLS9WzBBHnZ8p3Wf6CgG0kQ5rdDZJovyP4JKEsj3JesOfF+fpeckJz9NiMTEN9xDQpoBflDg0tbpx+TeddJnSA8CHujwDDgGcQyiGzRAawrm0WCnQywrTi7LA8dmzXAjwvwEOW35ZZuW4w0rGCNAF3FuA/3wvRMkBryZhpqWaBMDXJZkCHyFA0wBHLK/WWT1uHwVPxyCpmC0SS3ssAZwT+HJdjGo8oiCtNB5EFisCjy5Ovdr4iT4gVKlghzid5MnWxoWOF2Vau2B81ulDSnWSKLsNj5lGiPpDClK6+0jJ9NkOoTGqvHmdpHWANy5ZPm/dZClA8k8aeoixCm9cmPClMsfh2Gr9dwoS7gol3bMDyKQgwCZsgJm24Z3mQvCLDLMoH8Poe6cVW668jVsBFoscXw5un5Lq/iwBDJ9iXGnvNku7dwDfMu3rTIfNhD8ciadwnAABWlpstfqjwJdKHHtZRIiSTpv+D7By0slbbZDxpEO9QHtNgNtmgAAB/DMst5p60rVHCdD/YSsAXPRto5rqaEq6b92YBeMWK8BMRwiAzwFwzYJL+dJdu9Tx1WrSBMNLMCpi/MwzQKanoQJSQTNAqlugY4BTPbjP/htO4JLcGCdyKLpSyUWXKwxLgOhKteQXAAdwBLDgcp3ynPOpxx/+DFun977x0HdAdQmxWuPoWp0jm42suwW9HA4gr2UGyAw02WIwUsHY3/eFDWbBxdrYt7+R5dojgcMRI8B6gyPbV4/eG8NUT5dDWCtayivwsXVP6zIessmQ0IZL+X+WigQovN7ICxgOb15yaOeGwzs32dB+c0E9av+MAIxDm5N09mbh3qKwV6bfGwrPTefVisjmeSmycSEBwtvXDDirB7osLQlgjmDJ+TZKNwO44bAuaT9Skc3afHjrUg9vXZkB9u4kgDoNIGOI5eR+0p0lN/2z/O4TFd88T4S3rwqh3VtHgH6JUoAHrM2E3NGwgL9UuFnDh82kYTp2T89MM830DoNugqFmLgAcAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Coinbase"
                txt_Link.Text = "https://download.logo.wine/logo/Coinbase/Coinbase-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAATlBMVEVHcEz///////////////////////////////////////////8AUP8AUv9/o/8ASf+ivf8SWf9Id//j7P/Ez//b5v/P3P8iZ/9giv9kjP8SuYe/AAAAC3RSTlMAXAqIUrUV4jzFiQJjO6oAAAENSURBVCiRfZPbloMgDEVRq0CTEC7e+v8/2oBa7Dj1vOhik5AcglK7WtNZDaBtZ1r1raGx8JFuhjMzvayleRTNSX57U9lD8k1+XYgZl9VPEvw4Mx+JEYkQmaKv1AhzgYXs4uCElsyDnOdwQ5+P0D5X1UjOgBsJYeMYJHMj/WmYY1mg6Lx3sWzkOIFtlZHAUklwuQlILlMkCTXqCWnlwg4PnCDiNUGnLMxL3hrTAVNk0TKDVRrGUoOr9nmXNYFWACPv5V10DyUt/Ux7W9Deyim0NMqv3Eo2gX6Z0FqYIhc/N/tKBSj26fbe+Psru1w2ni77GBP8d0zqgCEz/R2w+9HMQ62rffZ7qMtzeF6ewxtTQiBf4+Y/uwAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Blockchain"
                txt_Link.Text = "https://cdn.freebiesupply.com/logos/large/2x/blockchain-logo-png-transparent.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAQlBMVEVHcEyFtfiFtfiFtfhonu6FtfiFtfiFtfg9ifU8ivYVVroWVrk9ifU9ifU9ifUWVrkWVrkWVrk9ifU9ifUVVbgqcNhVLvPMAAAAFnRSTlMAsvD/EuVKWb82OK7v/9LS/+ibsjQhIo/mBgAAAIpJREFUeAF1y8URxEAAxMAxM9v5p3pr1Bzp2yV5UZxEqX4XJ3vpf0MJQwlDfxqK/dPIYS+TlWdOcYHA31RWztlFdaNQ23X9w1l+0TCOkzR3XQdDoWYfnaF93QGG9rR0FHQdqVH6H1Oh4GPS9hu3QCiIoSCGgtiuIIaCGApi6IVuVO1Yj6s8dJM2txeqhBGZI+0BzAAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Apple"
                txt_Link.Text = "https://download.logo.wine/logo/Apple_Pay/Apple_Pay-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAC5UlEQVR4Aa1XQ3hkQRjc+ynX2OZtbfu+tm3b1nlt27a9O4qNS5xxbdd+cTKvXydT31fJoPuvmvf6/ejw86dBlX6CwwQXCq6t5cLaz/xV4+ld6F8r9NdgsCAjIwf5+UUoLCwBydf8jN+JNQbBddzjDQM+gocErRSyWm2QgWu4lntq9/q01UAfwYKCgmK43W6ognu4lzEE+6oamCboLC0tR3vBGIwlOF2vgZm5uQWoqamBXrhcLpw5cxZ79uxFKxCxrGBMxpYZ6Eu33KAXNDp+/AQEBgbzv8Y6Kxi7+e1ofuAKVS/7zp27KE7i6dNnem5HAbVaM3CYh0YF/PWRkdEUpxHoQe3BPNTcQJCgTc9pT0tLh8VigdPpBLFv3368evVKBC7A16/fkJmZKX06qCXo39jAej67Wnjx4iVGjBiJ0NBwBAeHYsCAgTh48BCuXLmCKVOmIioqBrwS4eGRGDduPMxmMzyBWtRsbMCglWSePXuOkJAwCuhmnz79YLVaPSUrGjDWGQhgCvWEyspKdOrURUk8JiYO799/0Exg1KQ2DQxjHveEO3fuKomTPBcyUJPaNLCQxcQTNm3arGzAYDBABmoK7UU0sE7rAC5dukxJPCgoRPy6DMhATWpLDWzbtl35Cty//0DBgOQW3LhxU9nAsGEj4HA4dN0CySHkwvy6bKfECRMmISsrS34IZY8hMXnyFAZV5rFjx6WPoa5E9PnzZ2XxpKQUlJaWaiUik1IqXrBgkZKB06fPwBOKiv4fwA3Ni5FdK3NVVFSgd+++usRnzJilXIzII7JynJOTAxaa7t17Yt68+bh37z6+fPmKCxcuYvToMejVqzdWrVrNMi0rx4cVGxIFKDQkCi2ZAhRaMklTavWqeF6epCltxuneasvLyurb8lmqg0lfLw4m/dozmh0RtBUV6R/NuJZ7avf6eGs4ZeIwMoVmZrYcTvkZv+MarlUZTlUZIDi8diRfX8uFtZ8FqMb7Bx+2VJbBTrlcAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Paypal"
                txt_Link.Text = "https://download.logo.wine/logo/PayPal/PayPal-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAApVBMVEX////h5vC2wdm4w9rI0ePo7fUqRZAAB3oAEnxCWZv19/oAI4MALYUAL4YAJoMAAHnb4OwAK4Q4UJYAFHwAMIeSn8IAHoAAKXsBJHENLoFfcagBIGkAKXJvvegOOo0AGWQBVpcAl92j1fEBAFsDjtEAmd1Vs+UBEGAAn+EBB1wES40AnN4AHm0DOX0EfL2t2vIEYqUHVKHn9vwGbLPT6/h7irYnpOHqpTq5AAAA30lEQVR4Ab3QRQKDQBBE0YlbD3FX3F3uf7Q07svkL3lIMeTHDYZpo/GkjdPZPG02W4wbtlwBzWPWDd3Madl2V8fhviA4wLb+***********************************/WK6KM1pBXhCl6tg12jkz+aPUcDTHT2aoaoouPhtjD4aMvU1L0XXWbozdOpZlCaKCpivLCu62FEDBYmk8uDwAhVcCoiiyNSObE9Ctm6AkPTmPVBvhyTI+Yv3n87EAq2Rl0MbwtN2uBB3z2ricYApaRLrzWNwp9eETC8gf+gLSxB5s5mkQCwAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Netflix"
                txt_Link.Text = "https://download.logo.wine/logo/Netflix/Netflix-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAS1BMVEVHcEzVBhLkCBPbBxKwBQ6xBg/FBhCtBQ7oCBPiBxLkCBOlBA7iBxTkCBPxCBSXAw3kCBOUAw3lCBJtAAuDAQynBQ6xBg+xBRDjBxJ6RmETAAAAGXRSTlMA//+Xgf////9NuP9m0xr/gn7zcf/MtjhOS7LcjwAAAMtJREFUeAF10MESgzAIRVFUCVZqMTW2/f8v7RjYtMNjlcUZbgaKGcZrpus5cx/6nTL2wUCWDm4YeGOFgNlXYKD+iTsG/s0NAolvPhBg88ZOFQAtfcWTquRALBpVEYhzH5YDNvYGBNo6WM4mOZDmjdI0B2zqK5ohEA1FQJufe2iSA2lnNDQHbMezAzYIpmgAoPXRwWKSA6nkjaIQ7GMfywFXioZKDmaiLRoQ3L2BAXmDMVj9FALBzVdg4I2x/IPzNc3z+3p+9n1at4F8vruuDABTzZR7AAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "BoA"
                txt_Link.Text = "https://download.logo.wine/logo/Bank_of_America/Bank_of_America-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAllBMVEX////64OPwm6PxpKz87O70trz519vthpDhABjkID7seIXiACrgAADlL0j98PL3yc7oWWm9wdFHVIWRmLPzsbjwmKH2xMnKztsmOHUAAE17g6T3+Pq2u8zmPlPhABTp6/BRXYvf4ukAAFeiqcDiAC3hACDnRloAAGFweZ3iACPqZXSsssUAH2oAAFv63uLrbnziCzHyqrFHeRAjAAAA0UlEQVR4AdXOxWHDQBBA0S95xcxGMZk3/RcX5sQF+N2Gh/ulqAvBv4Qm0A3TUv+pC9txvYVPENo+P0RxAui266aAkoU5H4rlar0h2oLvQ7orS8dWeEFVr+qKJm7bDlj0QznmgP/yW7KaIli37byBtN+bow7oh3LvqnDcUJzOpwIuu2Fv5cAlNPu9lfKieR4DjLI3M8C/ltKRKe82EQhv73gK8LDvpWnwXe6+ZcQ4yGEU/KQOAZAOUg4p/zqYcv8y9g/RO/Llpf+l5T7nJl3o3K0nwX4QD1h8vL0AAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Google"
                txt_Link.Text = "https://download.logo.wine/logo/Google/Google-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAzFBMVEVHcEz////////+/v77+/vx8fL9/f309fX+/v739/f////09PXOz8/5+vr8/P3////////29vf///////84qlf8wAdGiPX8/PzsUUTqQjQsqFLrSj3S3/w6g/TqPCs0gPQgpUf85+bv9P+63sL62Nb+8ef4ycbw+PJkunkeePP81HXwgGv0jhzc5/3o9efX7N5Fr19Uj/WQy562zPr2trL94KDzoJrzoJv80Gjyl5H94qgyh9v7xzihsSp+wYV1sE5ZtXBmmvUynoWKrvzKDGT6AAAAE3RSTlMAW+TTeBLcHLMt1WsKzfUznkBIxSDAuAAAAUZJREFUKJFtktligkAMRUFZxKVuDMOAggpu1apVu+/t//9TkxBU1PsySQ4hlyGadpTd0fWOrV2R3eqyWhe80j1RpYCc7pmcI2tyaZimQw6bOTMplU9hpKIofJSUmgwtTCYq9EFhqKIJ5lbGdGIRAGhUQLNX6wRLOA2Y8vdpuvfVOJtaOjhdhL56yYrjU8cGFsRSLc4/x+DPfxBiSZN6LMlXUYXzVghBT8/7pPkdxFX28yzEO8HYI8U9dlQudMZx3AeInWWe+SrExxrhCLTre3E+M3P7FXznLn887z53a2PwGbjBLLvUP2jcYUC/FYdOA9d1g22SbN1fbizT9bUxXA+QguB4G2GlfbIFqw1i0GCzKmzDDQ1LZgPQLKHk5rAJpmSj0ykH0jxArW4V79yqF1bMkEckjYvFrTWIy0btApFsx7m68Ff1D4OdMHbngtKsAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Paytm"
                txt_Link.Text = "https://download.logo.wine/logo/Paytm/Paytm-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAWlBMVEX////////////4/P7A6f1ueZ+Gj629wtHM0Nzm5+0Ht/U2wPZtzfiY2vqn3/v19fgAAFt5g6XW2eOwtsklPXrP7v1KxPcAsPR/0vkABmZOXo0AIG6Xn7jo9v5O7lBoAAAAAnRSTlMCum8R9SgAAACESURBVHgB3Mm1AcMwEABA+8X8JrH2XzNNmAbwtTdNM/ww3+7Hwh9nTkJfkzDGgTMhuWJUG2qts/6WARdcN47bvuKho0825UTuuRZRW9taLxD9oUcijyzYtiIx1P2W45GoQCJDYLWAcUd6TsIDQOcdOF/BH8ONTPJleP05kiTxJmq82QEACwUMtFXtudUAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Payoneer"
                txt_Link.Text = "https://download.logo.wine/logo/Payoneer/Payoneer-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAABjFBMVEVHcEyX2y3/RwD/SQA4mdP5SSr2UDajj2n/RwH8SRX7SRfrnwAui+jovAEf2Ikm23v9SA1WeejN1Q/+TgAVjPH/RwD/RwD7WwD+TwCPaeLfUbjOV9oYy6C82hn8SA/rnwBYeOh62j3vTFv6SR3uTGB02kD0SkP/RwD+TgD2Sjj1SjgGo9sPjvE4guz8WQDsnwDX1Qnf0gLi0AHxhAD2bwD/RwD2bgDlzAH6YAD/RwD/RwB/beNL21s6224PtsCcZeA0223lT5QRvbSmYt/kUJvhUbDHWd3bUs0i24Ee2okd1pCS2i//SACn2iSb2iu32hyv2h/G2REFnuEHpNni1QHX2QbtTGZs20XouwHukADprQHqTXrk0gELq87puAEOtL/nTojqTXdV21QNssPoToMv23DjUJ8Tv7EWyKQz223/SAC92hjyS0sHk+8Jp9Tk0QEa0JiQ2jD3Si4nh+/6XQDxhABY21KMaeLnTokRvLbfUbir2iFEfuvW2QZqc+bsTXD2bgA422jWVdi9XN5EQd0cAAAAanRSTlMAE7F3ChMMAt0H/fsU/LX9ofEJ6MWEvSTSera20aphnWH3+tdhYZ8YUdGwYZ+sabFrTSDK7TOQkEP26tzb1nY0eDQz+/vZ2ebY8uSx7d6l24nq9t3M6d/jz+vr3OjexX56fIHY2y96+Ks59MWlGwAAAbZJREFUOI19kmdTAkEMhiMc7B0dsdBsDCr2XnFULMjYe+/d0JGOFf+4d4p6wh75tDvPm7xJdgF+Qzk6PHCgVh+ejvRVQmnUDg++JRIJtVqr1YaPR/qLMCc3xeNvgwM9DQ09+9pwOLzT+69Kaw1i3NxdS4QL6e/dfn7211X8cQWLaHKJM/o2/P61X0Urzw3cf8/KOn9upaDgZIjVpKTr9Vx06buPbj6/lANZjUabvwxMuKykzA0VTU17gkk1oovGAZqTyUu+AxbNdA4wkZwgMIoolxJMv78/8A5jQ1KCG71+GmTIchKcMD69DliskSoAoNP7YBxlkpzoIotlBSAIzOUs7BG70CR1j0Iw9sg9yMuMOXOXmYShMos6y2RmgLDIUt5SCHISuuKRHLGeLugIhSZB+G8qlYKWr9kMbWmEU71K1U7ZNlMVDHZ8Sw2xWEuxgpDOYLCz0JyyLRZoL3LRdD0+VjE/N64tEHA4RbOQRuOTiPM1WgKO16Nzz9dOmSmL+8X41CXivKNz9zWdnpu3Wm0272wq9WJsLF6O4uI6PZfNZvP5j9mU26KhDe65tc4vLHhtlilR9ieG108ZJJWvDAAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Payme"
                txt_Link.Text = "https://download.logo.wine/logo/PayMe/PayMe-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAA2ElEQVR4AWJwL/ChKx48Fo5aqJ+5GcBbGRQBDMJAUAAy0FEbaEAFQlCADUzgAg00/XVuEpp2bvrYF8wtDUkZwtowhSpElnCCYEdhCNdLMlvYhCRkq9yvhCCLSuABe7q256vwUMKiY0/+KsxKWMAqMIUFw5xVSNdCEIbQNxQIazhz1qGACJ9u0h4aYlhdjIe6FpJ7cO2/TIO7G9r93YUdmMJigBWwmqLxZXvhIIimkDF7N2fFoCrh9baehMh6nqwmC/73kD/0dGH9UWgPvRfGS9/PjaxW26iFAHxUSDcvYlgOAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Instagram"
                txt_Link.Text = "https://download.logo.wine/logo/Instagram/Instagram-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAABWVBMVEVyHVFHcEx0Fvp8Fv1yFP1/FfyLCOyfBOmtAeS7AuDHAd3UANjZAs7rD6xyFvzjANTqAMHoA7l2F/yPFfx8GPmfD/zFF/XVWujoZuLyZt3pRt/yGdHyA8j1AKTViPj/9Pr////8g9f9A7r3DZz5vO794Pf+ruj9B6uzFPv7JLv+AJr+BI/+YMP/1vD+AIThG+n9GZn/vd3/YZz+AXP+GoT/r8r+AmT/b5z+HHH+I3f+G2P/ur7+FFD+Klb+fYv/wcr+LUf+LWX+AFn+Nzr+Ohz+RT7+inP+Si7/ztD+TxL/zbv+Wy//9fH+WgL+c1X+ZyX+PUv+Bzv+VXD+cxr/6eL+aQT+eAX+nDj+gwD+gxH+fSL+rYn+jwD+iwL+XhX/3rn/1bH9ZR/+lwP+nwH+xHH8ZC3+qAH+sAD+wAP+x1P+zGb+pl79mQn+uAD9J3T+ygD9O1n8pRL9twuaEkMNAAAAc3RSTlMBAF3G///////////GW9j//9nY/1r///////////7//////8b/////////////////////////////////////////////////////////////////////////////////xf///1v////////Y/9j/2VzGSus+GgAAAeNJREFUeAFFjEWiU0EQRc/tqu74V9wZ4TBiB6wG3wpzVoQ7zHCXeELypHH+KVchJAESaEs0IUv0Zv+7fwLgSAOk9ZFghcnfDw4AAg18cyhWCKNVob9NCZjQLWVrEGhJbJGDoCBpMwCrJTETAKBWgdeRGb4dmJW56PdI/KU1K4X15N+AFsW0+3/WVm7Pa0Pmu4Fh7HcbRIjMIhr3FItUGAH4SNZKjK32YjKpK3Iv0yUY0b8g0Vs0oK+8wbeyMBAQwVeo44R1z7XWq6rqpn5jiTmAeQO3iceg9zkYzKvc71iEmMFbqCJJ4tDcKFrLjX5D/MEdq8GpIUUgd/tgGboTgiUDPDZ44SXNpr0gWRcAgqEmEPJRNVPOxYaOth0oAHdCxWoOzslH+RCvlafYMgFUHvAIHoCjz16io80BrH/j0AAcJ9KWKkhn5sRyGjsDtr/sqyrsWNO1cW9/cDNLZgHLSzVvn+wMq9qbKKwy7TiIaKTW56pzW6Oa+N1OKEQdvvl2RyOE4DEOZurd1cHNeab27tyjOPfhFlucXP04ovF97B6jTOzdJwFLaNAff7N6c8incH1VpiSR67q2VgjzUU7JgbMTiQtFKmgAsCAuczlflmVZn7iGBFc1awB/p4tqUS3LDb8OPwBrSrZIO/KHZwAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Bitcoin"
                txt_Link.Text = "https://download.logo.wine/logo/Bitcoin/Bitcoin-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAASFBMVEVHcEz3kxr3kxr3kxr3kxr3kxr3kxr3kxr3kxr3jAD3khX92734pU34nDP////95tL80qz7xZT+8ub5r2T3kQ33kxr3kxr6uXuN5P8aAAAAF3RSTlMAL2qjBor/vtb////////////////2JbbIuikAAAD7SURBVHgBhZJHkoUwDAVNEsjGz9n//jcdTGmIE3rbyiV10vXDRt+pF+NEB9N4dz3d6K9upgfz6RYSmEnTzvLI08TG8Cp2vvezzHAMz5e+o1QMiB4hIWUJbjPLDjk57CTWstEmC4nlCLQAR2KVqiRoAhxrD0Tp2p3r8wcIrBmApPZqOqTfZOYEgL+bDoeMED4iB5HSMhmgLSry2IRTa8ktPxxle9kyOGBl3cZyx43qpWFYyUbAiKxyBL1nCmumRjnOl9nCm9uwk9o4Dm89mCyLo+aOkQzHyFo/XmWWwtqm/HqU40306ZZ/H+yf1xSmQkKZ1JvaT8Mw9VUdfAG5uBUIY0iOdgAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Ethereum"
                txt_Link.Text = "https://download.logo.wine/logo/Ethereum/Ethereum-Portrait-Black-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAvVBMVEVHcEybiuGencgyNrtdV8R0ZcG6n74AAJ3bu75YY8mbzunbu8C7pe+Jd9DPsb+qmOin5O6a0enWt8C2ou1/hd/JrL95otmj3eyxnuug2OvGqr+RxORwjOKnlefJrL93lOZ9m+qokLjLtfaIq/Lzz8K5+/bxzsLatb2r7/R1merDqvK5pPHJs/XmxMC5/Pd/pfG6/vf40sC6//jty8LIq76mnejIsPTCrfKYreSz8/St8/aLlNd0kOOTicm3pcfjWWs4AAAAInRSTlMAg2IdUC58Df49+uv3VLq4/Z/Z4miqTNjRxpR/jp2pwexHbhm6OAAAAQ1JREFUKJF10tdygzAQBdDFdGyMEzu9y6ILYjDFLfn/z8pi8oBA2ifNnFlG3CuA4Wg2yKdy5PYcnl5kpu7D6GhK8JaG0e5JbDMahJHv3wsxC664Etkd7TG/mZq+D3r0j+oE52g0POW4+jA2gwa0yH4OUZn7+Yw3M6NFeI7jJD2syl3E47Kozt/bbZwQktROyd3J/b0gdcgYI03tDAvQrvSPyAuN++7rYLNVOLINMD96JI0HoHC9vs91sD+7C63xsHgbZ7AE2FzSDZgeW49CUDOaGd1BaUk76XSG6VlfbkoYc8eGOWCAFhrxpgZgBYGV4j+KDEvrNhtdiNgMoiI2fGFW+igzMKta9jJxND7wP7tLIiHYH0CqAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Telegram"
                txt_Link.Text = "https://download.logo.wine/logo/Telegram_(software)/Telegram_(software)-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAe1BMVEX////6/f643vZ8xvFItO0kq+t0wvCv2vXz+f2SzvEDpeczruoyq+fX7PrC4vai0/IhpuQxqubq9fwwquVMseZmuuoAn96Bxewtp+Li8fp3vOOYy+mHxOgqo9xuuuUAltooodoAmdUkndVGp9gln9dgsNwAlNCRxuQzodURH7loAAABCklEQVR4AayRVQKEIBRFsRO7u3X/Kxx4z/Z3jnXhSJM/IIiSrCiyqulfZZiKYjFsxZRemqJBFNO5O8e0H5ju5ejhLiue43n+BzPYpeGFF77nRazsxUfDS3lJJAokzcIwyUGKXrbjeSlUpTwbIItdJZEmEMJuEvNiBDKCRklB4c+SvfBfkB6Prs6jk1R8EglIqEnqGrvQm7bkX5rUDJQsdJFLdTFJNJwhSshNx2zCQUfKjv9fQ9aSGukalA3ICofquxM4Dkg9xT+L4ZR9I+w/j+hYaToZ+nIe2Heh53EuaFBz15fkpFxY3cXUF+SGuNzlUpIHwbz0hxopeZOv48LYKvobjrTLwUF8OgcAV58l6Nc1eEoAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "eBay"
                txt_Link.Text = "https://download.logo.wine/logo/EBay/EBay-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAABLUlEQVR4Ae3WJUADYRjG8UMzDpmCJNyl4ZpxSbhrQ9viGu4Oc+8BJ7KCR5y2vXzPrOCypS/8J2e/8zshu63ApXHw3ZpbWkKR08G2trZS1hrrBNl+lzoDBNbMugPU2tYmQjYUw5r/Fewersi0LXha3FUYYh+O3xiGcZjm30D71rx33DDMvtV/Ao1J6XH2xvtrDdWNYyrfuqNYe/cbfo4wHmErx1RBEehH4FVMUjjr6SY6gZA6o8ocWa42CfUnZKnCSCQT6GXLzdL5lmCalPmaR1XBhMZUwU9Afw1qMyopokJDH4GX2140pfCn74Mc5CAHOYinwF9BLOPbIGLQwm9BkSpw5VePp73kYjFgfEeUayQMs1ZxKmGg5HnLXcJAyeW2twQIYls3yN/a/jUOvgKkonKBWnbPogAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Skype"
                txt_Link.Text = "https://download.logo.wine/logo/Skype_Technologies/Skype_Technologies-Logo.wine.png"
                txt_Link_Icon.Text = "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSfhKFkk-qIMl2aJZ7qje20kbD8-ikNruKRoyQbUn4&usqp=CAE&s"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Discord"
                txt_Link.Text = "https://download.logo.wine/logo/Discord_(software)/Discord_(software)-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAASFBMVEVHcExYZPFXZPJXZPFXZPFXZPFXZPFXZPFYZPJYZfJYZPJXZfFWY/JhbfNsd/OSmvbS1fv////x8/6pr/hQXvLl5/2Bi/XAxfpYg8U1AAAADHRSTlMADzmFwOT8YtP/sloPkm6kAAABAklEQVR4AYWTBbLFIAxFcXs4lf3vFEg1X2+9JzITIZco40IqJQVnlGBRbewtoxFmziI59rCPRGhIfn5hiLKDYSkJkemZz98689IB9YlCjPMlxnBgr4ejgTebcqltqJacLFBDCQPHJZWX0gIOjHB4Lusbrgu4ciKQI3IVRALMGGaAkqgZNdQRK4U8foc0MtQw46oDbmC9pBkQomwHlBPOlOsW9lL2sK1XUknETNnKpNOl5slKm1AQfsFaxw2eF+RQBB9HwLq3PedxG3CPMyc7yufDqF6b5Q2+jfoFf5SPaH/WPXhQuCqvn5bNVqEXR1GzkST7e0wwxeyX0fSO/T/Uf65DBzQNHUMopTvLAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Viber"
                txt_Link.Text = "https://download.logo.wine/logo/Viber/Viber-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAJFBMVEVHcEyId/WRg/V6aPODcvR8avN+bfN8avN3ZPKAb/R4ZfNzYPKP7418AAAAC3RSTlMALRfBRZl7rutg1vUAxzQAAAEMSURBVCiRbVJbEgMhCJM36v3vWwS1O9vy40gkgWBrJ6CriKh2aK/oNB9B/QGxR2aY9t5VbcTF+ZbFRfH7FnvguxjmlLeMzlnSNOnym2w+qSTmI7SxiIGFYGcxFSnL8xDrUCilqkxN3bnOBmpMpWr5ZMnkMIHFPOHDkl/XNrIvPbMZG1jO4BdET5qoYAHKjjwJsZzw7QM0u+AoMLSvFfgAy4sgznb5+DZut+vqkzh2YMnENXcWJDpqY6Pal+tQMUuheh3Cs4AkW0tfLkN5exZwuEUw27TTJj1WnSnahdmmyxfGbsHNz4drp2JGw2/DV2ktcf9AJ339ToEmYQEzvOQL1PGbPWD9of8B8gf7ANcwCW1ANp55AAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "LINE"
                txt_Link.Text = "https://download.logo.wine/logo/Line_(software)/Line_(software)-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAANlBMVEX////P4NVuxIkZv1tpv4cAxUYAxlAFyFVe1oi57cvL8tnf9+h63Z3u/PQyz21J0nqh57oXyl9IIyThAAAA3UlEQVR4AX3TBQKDQBAEwcXm3P7/2Qy+lwQ6ghQuwoZx+mkcZGteYH7CMu9m/rbqoK3XQUbcWwJb7ulRpmMMxjofgnfWnDwdCMQkRykCGmGcqJyBRi9dXiGifBVxr5kkcV3Pv+SDc5y8EJnAX6lLDcVm60UyTrQ72tpMtbFEor2wnMh1bbQ1iJQLGxGZc7NEG1t2Iu1EA06FkJhIShwTRzsxJ+lKTSFsjwX62i7Zq/VoChmsT7uFTOuQipYDrfa37OLFS7Cg/SLVXavtOEIr1BTG9wfs9dF8f6hfXocPmPgRopdknbAAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "TencentQQ"
                txt_Link.Text = "https://download.logo.wine/logo/Tencent_QQ/Tencent_QQ-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAA0lBMVEX////o6Oi3t7eioqLe3t5YWFgAAADIyMjh4eEdHR1GRkbU1NSRkZFKSkqGhoZpaWksLCzLzM729vYpIQ1WPwJkRQBLMQCygQb+sAD/tgI/LAK6gwbxqQjs4+MxAAAACAB4VgP3p6fnAADYHR27GRmVFBR4EBBeCQxHAAo2AAjwODjoGhroHx/uICDn0NDUAADoDw9sb28pAACHPT3yhITrR0fuZWXzjo7/8PDzmJgMDAzweHj4v7/S0MsfJCr+4rH/wkHgnQD//PTOjQCBVgBFKQCWWShVAAABRElEQVR4AW2QBYKDQAxFi2yggnu91N3d7f5X2uADuw/L/IclOQKKZhiayv0H+wM+P+xfx0EMl3U8hnkIT3xG4jsLOd7LC/jmtCsCAMOXBKFIM1gWU1L0JCsVCkW+hKWYkgVMZIoDUCgZywLpVPDQdMMwNfBQyfZxbdmOj23hgso0Uq5UbVTVSjnTTK0OSENvNvUGIPUaIVtuu9Pt9QfD4aDf63babouQo/FkSjAZjxI3m0/Hi0nAeLxYjKfzWSyXsFpvFr6ZjLa7/XoFS7LL5cGduMcDgoFEdMpFshW/CYAjBise3AUp4/EKcIKlet7tLmEgYSCEtQx0etQFDOTofzg1NWpvxUUB632FSSQDSy+MoU6gJg+eqFyKK9zChLrBNe3uj6cJosLzigjm83En3cuxbfv9+Wra9/PG0nmRFr1HdAn5BfFwJWD7LmmyAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "WeChat"
                txt_Link.Text = "https://download.logo.wine/logo/WeChat/WeChat-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAilBMVEUY0SEYzyEAzhIYziEAygAo0C983X6Z5JuA3oM70kAYzCFK00/W89f////l+OVk2GiP4ZKv6bF33Hrr+eyi5qQZySG87L4AwQCn5qnP8dA8z0GG3om06LXE7cVr124ZxyFW0VoZxSEawyH0/PQAvAAFvhIawiHY8tlCyUYawCFEyElfzmIbviIbvCIgSWDvAAABBklEQVR4AcSNVWLDMBAFZQip2ZiZ2W56//P1SabQf0asWWDfQFGYIjamgvm5wTSJoh+Op9P5oqvaA4xzTeP6z5UkN0PXxIfcIIFu0oal8w0mnI1Ph5bN3S3zuKfjyw9Coig44Xrh+APcY9iOolmMrk4cYUsCOAmk7tJGdD6lmZUvmhUFaq0cM5I4ZVAASK+kBduhFcObZRHclpop0dkgSgs8AiGrqiripShWiB5RLh5BVbEaFHEzywfcoK4hQXsjlE4eZdJJ2dd9NYxtEFfDg6xqyF7Sdn1fd+NDYotfNj3Q/TaLM1vxfpSwbRI5ztWne/cgH7RgvPdS/n1imuTB/qeLB9BIEgBr4EBeqVVNiwAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "LinkedIn"
                txt_Link.Text = "https://download.logo.wine/logo/LinkedIn/LinkedIn-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABDUlEQVR4AWP4////gOLB44D6nTcsGIo33QHi/zTGd0B2YTiAPpYjHIHNAf/piQk6wGPW8f/rLz8HYRCbXg5AWI4GQGJ0cwDY12gAJDbcHUA4CkZAIqQUK7Ts/m/SfxBMs5RupswBaACr+P47b/5zlG/5DyzZ/r/+8hNF7vuvP//nn3r0X6JhJ+0ccPrR+/+H7735jw9cf/n5v0D1Nuo5gBxQve06zR0AjoL7b7/+//zjN4bc+ScfaOeA33///k9Yfg4mDw7u/Xdeo6uhnQP6D93FMNxlxjF0ZbRzgMXEQ9iyI90cALIMJoccDXRzAK6CZog6YNQBow6gIx54Bwx4x2RAu2bAysoEZu9o7xgAQrvkxt3WZi0AAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Western Union"
                txt_Link.Text = "https://download.logo.wine/logo/Western_Union/Western_Union-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAANlBMVEVHcEz/3QD/3QD/3gD/3QD/3QD/5ABORAAAAADVuQBtXwCPewDlxgAvKQDBpwD01ACpkwAVEgDBJQYuAAAABnRSTlMAHXfb/+csqJz/AAAA4klEQVR4AYXTBbIEIQwEUGamt3G7/2G/EsIqKUuFh4Mx5jgvvIzrPMxv+w1v43Z8aBdx4mOc5voMLvOqSmgY60Z4ekkDGCU3WiWDYkyg1jPlkWayCEBdBm6SF1YrQKuNnEMkegEQ67oO7CIZBCxVIEiemPIAoNWB07pVAUh9VC11U4XsAvBmq20CCLB1XSeCgNdbjS0L0K26Su+WaAM8DqxTDoD1oqqCQgHwr7aaiQlmNXKuqHsokK3mBKDIaU6g6yycK8pQIKI3/m87OhfSBCJ+Q3JJt89++3G2X2/7ebff/xuCARkMw2lkDQAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "HSBC"
                txt_Link.Text = "https://download.logo.wine/logo/HSBC/HSBC-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAY1BMVEX////85+b97ez/+vrxgHj1oJvqAADrFwDxfHXzjof+9/bvW1DtSjzsNCPrHgDyg3vsOCfrIwfuU0ftPS7sKxbsMB3yiILsLRr4wb72raj729n84+H2qaXuTkHwZlv5ycb60c7YnRLdAAAAm0lEQVR4Ad3JhQ2AUAxF0eLufNz2n5K+psFG4EYqh36YZV9Zzsdczw80P4zilyUpeUEm5QWF5aWwqiZWNXKrRhXWGkbRvCPGtleFZUAoDJj1pahTt5kgVAzIavNmZ/2Fw3ihySescdkrDkHACmSbiUSbXnDwsyxYBC+DtoxDkGVQYGUJqK5iqmGgproNfq75XjHTK2c/rnabftcJGa8LyUwUCQcAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Union Bank"
                txt_Link.Text = "https://download.logo.wine/logo/MUFG_Union_Bank/MUFG_Union_Bank-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAArlBMVEX////8/v3z+Prs8vSUttBokrpXgLBdhrN5osTZ5+2dvtUdYaADWJ0AUpoAUJkAU5r47ezy3d359/cAS5YeX6AvaaQAW5301NLYKyHWAADon5qMrcrM3ujmi4bXGg3ssKtHea20zN5Nh7beaGPZODD45eStw9huncLuv7zcUEnXGAmNqcflko7WDwDcSUHutrLjfnk0b6fgdXHwycYARJR7mb7dWFLgbWbljIjcSUPu1gLvAAABH0lEQVR4Ac3OVWLDMBAE0JEZZWZbIXOYk/sfrBwq/bbvU9qdWfwLhMMPeEGUJFlR8Q1NN0xKqaVr91G24xJwomcafhC8TIR3v1GcpCCZYelKTqBmFpVuyQWLB8iHZjjCmzHVR7iYsLJCHdIGqAatC80L62tq13fgptTL4c7mi+rhc1nOV1BFqnOoFv3EhkiDa+yMJS3yNR1ziObz1IV+O8je9AnBiHo1eUllW/A7uieXygNbgFM8Q4V76LvopdLI8GG7mG/AN5bIw+771aXy8tkfQfJaBdI5S0HWdMhfPp0Di228amO22CL3TREXpGBs0lZVuyoZO7nQLFPAlXPuWVyWMWPzjQvwSsbhilSHmL2IyyW+0xabTbF08RuCv/YMRVcbNJhRkZIAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Alibaba Group"
                txt_Link.Text = "https://download.logo.wine/logo/Alibaba_Group/Alibaba_Group-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAS1BMVEVHcEz7eSz4lFT5fDH9bBX9bhn8ciL1nV/8cyT+bBT6gDb/ZQL/YAD/WAD////+aAv8jE/8u5f8mmX97uL92cT8yaz7rYH9fzv9eS3iPrGXAAAAC3RSTlMAWi1d8t21H6L9XviSvTwAAAFJSURBVDiNjVOJsoMgDMS+etAEDIfA/3/pC5e11hmbGRjNJtlNACHe9lpGCSDH5SUubJ5W2G2d5jO+wMmWz/TnGQd4Hoo81m+ceR57/iXOEb3GRf3GcqEPVTY8Kp2PKNrNOedJNU8mmXZcRa/ZgvXathoTB3SFCAw7Q4CoNt9KrEIMPZ04d630ym+dY+gSleV0RmP93htfxFjrRya3ZAMhYNKhtwGjkLWAYzzSioyDrgQlSIqKG629xOJC5xhHREr5V/QCpslSXudJWRNkia8U7LXsBkpM4Ix3LtRyTFFFJtbg/JYleudN2oc9tjZ5yHmVIbyPorTZBhVlilGhipgbKdYGVUfNszNhC2R4bYnIWktYR10OCxkxwRjeeVEOiCVg6seNWQCvukPh6cf9faGPEn+6cveX9vba3z+c+6f3w+PNNvTn/3dw/gOcRyem0Ki7xgAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "WorldPay"
                txt_Link.Text = "https://download.logo.wine/logo/Worldpay/Worldpay-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAY1BMVEX/////4+b/zdP/6u3/09n/kp//AzP/DjX/gpH/d4X/GTr/yc//Ezf/UGb/9/j/LUr/Plb/ACz/8PH/v8b/Hj3/cID/maX/X3H/JEL/N1H/p7D/rbb/aXr/ACP/Y3f/2dz/jJh9GRTWAAAAv0lEQVR4Ad3M1QHDMAwE0ItJjaywsbz/kmUeoU9wf4c/0yilcWUMrrRSDZ6so9U1W+b2mityFk+epeuBZmAeGqDvhD2e9MjTDCwUAi3APPGo8RLFpWsbM7VAchLx5ifJQJGcpQBZJo+3fuCqPbn12pHXlYceH1oSnygYEyh5oRafNhPZLdW+r7S1NG3wqd+FUigCkUoJux5f9hLCdAAOUwiyx7eNC1w1oCsHt8E33TEfAeDI3Gn8mJXBjVEz/s8ZJ+cJytnu80YAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Square"
                txt_Link.Text = "https://download.logo.wine/logo/Square%2C_Inc./Square%2C_Inc.-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABHUlEQVR4AezWA09GYRiA4ZPn7IbckG3b5l9qaka2jSH9pOynO/vjQbq3a8Y5r5V31WEAi5jHnIqWMIx2fMgZcxCdbMENz81DdLaD+wohBmmC0gcxyBRMzv0NDnFio0PcQL6wCmURgvcWEY5AhNgoEOEYgeC9OVMjEAa18sC5tR8QA7XyxZG1HxAHtQrBya/4AAdUoQPN73SgBq5afkApxIxaLT+gG2JGp5Yf0Awxo/n/A371B/RAzOjS8gNyIWZUan0S5qMTze90ohROan3A/10QCbXywZGR7wEvUw+SJQje20ca4uyUhBVTT7JZiEFWofRCDDIKJQtikHLcNwTR2QreNADRySSc8KEc3K4bqmADFbvmG6BmTgRiF2QLAWMdhATUrYGDAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "American Express"
                txt_Link.Text = "https://download.logo.wine/logo/American_Express/American_Express-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAclBMVEUBb9D///8Abc8Aa88AY81OidcAaM4AYczl7PhqmtwAWsurwOgAV8oAX8wAVcoAU8nC0u86f9TJ1/Dc5vbw8vrp8Pn09/xxnt2zyOuOr+Mbc9FXjtnS3fN9pN+Hq+JIhdaZtuUsetMASccATshhlNoARMYCCOobAAACoUlEQVRIie2V246rOBBFs7ExYGPwBYgx927O///iFNBRJzM9D3OkeThStsQuuyorJnZBbrffUpL+HvcG3+Ab/J9AxlhyGEsS9oNuj+xZvuwERVEUAyMrbCz+pp0ufmR3Gm2WBltKFhMCkwF5i4i2hevR5uhaUkfztgGaFoYyDV0YV+Rd7roc9gD5GMrSK1R6QzPpqil0WdaFk7IsI1IBz8pqg5UJgillg+njAm+4pawAeMxyiJg1k8hz3zgdcqNVEZGw4EavB+1ghFgwlxeoRulm2UHPYh11rwns59k4rUYlQxFzqQAUHGKAyQod+i8QA3cq66H7OU7WSALTdFFOSC0K7NFLA3R6GHfZGuktu8sLbHRWVVzQij7j6gRpBdBnpsxjiMiWDiuXtRSzk77Vib7Auc6BUAc5gckufDZ3XdWfu/sckZb5wBDKDyMsfdeiQz1i1fUFOofbDjM6D+/QGnKS7wydgxkp1XWuM61jpjHeIMCYr81Rk+Cr+kGxPwOfD1+EPWf2SG4HKASj7vlJyVdgp9+S72Ty2uQJf4h9D/9FmX3ibP6l9h4fQ++/neKTP4Mo7vd7EfYVjdvNMVEYj+DQnxUo8vXyF1BuQ7r4vgqNHCBjanVv9JLG0g+MKuGu09SO0+HuBdTzcfbU2TGikytgN0dt3Um1HweJuBy+neMXMON1/csDjPq90/a+68lwu0TZDqKuP9Qqq6oKl7+ueKd9oAbL+EDgtiw99fUskgbpRpWGmmO04vAleQUHpdbWVb2pepR0q+hM2XrqxjRSZTTUKHFzh78cB1ikXggJ/ZJJBL66ulKG5wicnkt6N60zvYCW/HD7ehwPrfsZHBfHs7jjBz3t6i3JHhLsCrQ+BZb9U/LpVv+T/rS/gDf4Bv8I8C/uYDfBfENZBwAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Mastercard"
                txt_Link.Text = "https://download.logo.wine/logo/Mastercard/Mastercard-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAk1BMVEX////97O73tLf1n6P2p6v61df84ML70aT948r+9vfwY2rqAADsHC35y9D7z5j2lwD3mgDrAAjrABvrABX6PQD6hQD2oRz3nhv5uGvrABD3RA7/YwD/WQD4lBj7zp7/XwD/XgD+awjuRk/82rfrABn5TAz5jhb6x47zh4zwJhb+8OH5s1/++PD0d3f4qjP4qUDvUltYf4huAAAAqUlEQVR4Ad3QQwLEQBBA0VFs23buf7mwOtjNOn/7mvV6Xu/P94e9lnCCIO9E0cway/GCIIqCSFy3SZK8pqiabqwJ5omsJW/ZjusZu/rIfsxuihOEkQ6KMEYbgyBwjT0xAWTSHbMVc8DiH2SVy7HwouM3JTyoWh4UoAfV6JvoXDdw4aNi80J9QBU3AhPb9qKMpSgM0/WCuJAw7ARR35GNy2XyiTkMJv56XDM4xRahvHcABgAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Chase"
                txt_Link.Text = "https://download.logo.wine/logo/Chase_Bank/Chase_Bank-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAZlBMVEX////3+v1ultBPgshIfMWatt59o9bO3vEAT7UAWLgASbOIqdgAVrfX5fQAXLkATbQjZbxzm9KRsNsAW7kAUrZdi8yivOEbYrusxOTr8/rZ5/Q4dMI8d8MAYrzk7Pa2zOi+0+wmab79g7LrAAAAuUlEQVR4Ac3QRQKDMBBA0cHjKQ1pcbn/JTu47eof56GBN+S43pAfnC2MCB1j/GRCCjWlw71xdlkQNd7alZkNorqrBUwhJmZN29luTPVo7lMXjFpnsBRNmXu2lEssygb06YgwVyRCiKQY9r0jlsOJ6k+REUK02CPqsF83bR9scR38uPCxYqqTPiKZh97xGF1jESKxsGSJWtJ4Z+LBJldvkfiwK9YrloUD+0JtjFE4s8hmcIybaqyG13sADb8XQWs8TWEAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "National Bank of Canada"
                txt_Link.Text = "https://download.logo.wine/logo/National_Bank_of_Canada/National_Bank_of_Canada-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAALVBMVEVHcEzlFR3lFR3kFR3lFR3lFR3lFR3lFR3lEx7lFh3lFR3lFR3lFR3kFB7lFh3LuSV7AAAADnRSTlMAPFOP36UgcgjwYcjDGSaCavYAAABuSURBVCiR7dK7DoAgDEBRKFCe9v8/16iIbcHVyTuRnKEkrTGjzZp1CUImKguxztPVpiRWevKC0JDIiTkKyfI5GgliG+8JeT9+gTWWNXp37hEmzAHSvf+gEMXZZEYtqnPDDn2O6vgUm6OPGF9AtwMaYh3kni7yxgAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Microsoft Wallet"
                txt_Link.Text = "https://download.logo.wine/logo/Microsoft_Pay/Microsoft_Pay-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAIAAAD8GO2jAAAABnRSTlMAAAAAAABupgeRAAAAWklEQVR4AWP4FKBEJPrip8wABrV7GOp3EYtGhgWjFoxaMGrBqAXf/OSIRD995CEWVO9hqN1FLBoOYNlnotF3iI7/O4lHQOVL3hONPo9agB2NWjBqwagFoxYAAB2zMZ7bQW2tAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Docusign"
                txt_Link.Text = "https://docucdn-a.akamaihd.net/olive/images/2.72.0/global-assets/ds-logo-default.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAxUlEQVR4Ae3WsQ2DMBCF4RsiWSIb0KRIQ6RAA4tkD9fZIyk9Q5ZI4wXoqC680hJgPcl6FcXf3iewZdvaZ7eZj4+zD4OTpaXo43hbm8mDJI4ZAjDve31dpCBSgyiqQcc8JYiCteanrbDYU9/7XiQYrTOfl3ytu01uZru9m4YBE0DnwbwPgRJgEdWBxO9NNUFU2khRDQYpiBtECSYcJjIQX6cEAzAVCMwEYPbMyMESyoQDP3tWsCAbbpkMqQEe4AH+KoJzCfwDF1Tj6+YyM5YAAAAASUVORK5CYII="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Amazon Pay"
                txt_Link.Text = "https://download.logo.wine/logo/Amazon_Pay/Amazon_Pay-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAe1BMVEX///////////////////////////////////////////////8AAAAEBQX/+/Pu7u7/qAPMzMzk5OS6urqJior/x2n/58D/8NjZ2dkYGRn/9ub/tSz/0IEpKipeX1+fn59pamp4eXn/1pM9Pj7/3qlMTU339/f/v1OysrJRzGPDAAAAC3RSTlME8bJyR0ts6ahTQ+wGe+8AAAFPSURBVCiRbZPpdoMgEIVpmzY5FUGQRQRU3N//CTugNrXm/sEzn8zmFSH0+bhnF90fNwT6OBDljLGKzwf+gns7o2sYDMa+aflBb+h9ZwJIUt5VO32gt+1B4INhLI6raDt5DlEv1i5SfyTeYQt3TDlntAFo2BlWXTMJmt6CHOUZgmZKKa9ELHqBM2tDM3jzCnIxPNv9B+cuxn0nuhdwjaxhlLZXOMcJfJmWcYF8gNhUpfQXWEU4wOwMus1/97dBOsXZA2MTjtBUp4ZCjOXe77O05/WZFISOAxwNPy+hnCIygvOAw/OrEE1S2bJt11iLM5opuUidYC8VyU4irndWpbS1s1LVfzipRzemm2ATMtpi6ZXWNUir0Vnbjz0BmySDKVcUhV0kaLFFsSiidTTYZk2iHASTrBzrlB2sib424xKteicdZN+rR1MjdHv9O3wj9APKLS4BDycOEwAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Amazon Aws"
                txt_Link.Text = "https://download.logo.wine/logo/Amazon_Web_Services/Amazon_Web_Services-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,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"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Bank of China"
                txt_Link.Text = "https://download.logo.wine/logo/Industrial_and_Commercial_Bank_of_China/Industrial_and_Commercial_Bank_of_China-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAJFBMVEVHcEylASKpCCenBianBSWlASOmBSWmBCSmAiOlACKmAiOlACIkYvMHAAAAC3RSTlMA2Q0bK8ZBWbDtd9vw/mYAAAEDSURBVCiRfVJblsUgCAMRUNn/fidQb18zd/Kh9oRGiCG60JpIa/QHuo8ZwBzeX5QsjmAFUMFL7pyNiLGsi3RbebaLcw71s1rwySdrHOMhJHqyMmK0Ddq7fspXqFAU1jp2Eo5VM3A4gWRgea4gyYNzIi+FPG9VVNdd+IVGCQS7GZro1i1JXDbg2azGSlbRyZYlnwyh0BSfQMzWsABHK40ke03Tm3GSbK3Mx6zyP7lla7ySrTm3bJvze0PnKNY7JLDcRrlMkMwBlrsJX+2Tw3j+anwq6CtWcj5ZPra+H1vvUeBnTPQWoiNgngHzpTg/0lnRnMgm/44mVag5G+Xhb6qwM3bhBw06Cz/s5YyUAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Zelle"
                txt_Link.Text = "https://download.logo.wine/logo/Zelle_(payment_service)/Zelle_(payment_service)-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAYFBMVEX09vj6/fzq6vGVh7qOfraYi7xcPZpiRp1iRZ1mS59lSZ5ZOZhgQ5xrUaL18/iIdrKBbq6wpct4YqlaO5n///9WNJfh3evb1+d7Zqqqn8duVqPp5/Dp5vBGGY+2rM6/t9Tkof/4AAAAwUlEQVR4AaXSBQKDMAwF0A1Lk+BUpnD/Wy7M13b+cR7UF/9lGcnFkjQLkibn//ICghT58oSpwlOIGeZLSahSD6mq6waQ2q4nD2lg1DozQ5l1jYdUWee0ts5m5Uqhh70+Zy1V+rhJtrvdVrAF9HFuKu+t1mOBIUp4kkoNRrHMpFCgKBKKNQqjaHZS4YAhnit0e2ZWIUIjhUpndkmqAizXt0EI/8yuwxcZBLgMPMQH4TRlGEdkBvTw1TJ5ucBeL83fcgDbMBPtHmjYZwAAAABJRU5ErkJggg=="
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "KeyBank"
                txt_Link.Text = "https://download.logo.wine/logo/KeyBank/KeyBank-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAIAAAD9b0jDAAABlUlEQVR4Ae2Ug26tURSE/8e4j9Dw2rZt27Zru1Ft27YblnFS23Y7nZXaDs/KHO/9nVmzoWAdSgVVQaVQUoRkD1y5iw0HcG4vvv9BBdCyGugAUJgHP1vce4E9V3HrskDzalDdslIoiTRlHwO1Y9h2TrTjMk5fwM99yAgAupcPZdf0SOI3E2w5BVNPuCTgiyZ+aOD+JVjpI9pf/LZA6EO9S5EiXtj1qEemmVmFWsDGDcY2/FL8Ml/mUCHQJUrB07fMUfr9+1fodY2SRn2Z2CeXlg9dwIu/7IPeF5exLqXIslCE8jNdd7RAqlu4jn74oSc/LV1XTlJLgnIzMN+ZeniF4txRMSj58pcjNIKX1j63l5beaGuT+vaJmoReuUvU6DovbaH4ZQVmVmImfCK4YUiUuU/06VHcoHsJWyo2cnRLTRZbaZE/5jCyKKYk029pICeFwMU2f1YkZleLjOeWkPF0ZxhIy+TyozSd7LGSYzoKZcpc6JnQ5CJUrOxCGYVywMlN+OYjK84oXv6CooaUUpSoLum1LhV0BLcpLE8+IUz+AAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case "Virgin Money UK"
                txt_Link.Text = "https://download.logo.wine/logo/Virgin_Money_UK/Virgin_Money_UK-Logo.wine.png"
                txt_Link_Icon.Text = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAkFBMVEX//////Pz+8/P73t72vr7zoKDwiYn3wsL74uL1qanoV1fhAADgAADhCgrrYmL1rKz+9vbtdnbiFBThBQXiHh7ugYHsbm7sZ2fiERHufn71r6/kLS3oVVXtbW3jJibnQED3vLzyl5f5zc3xj4/nTEzmOzvvgIDoR0fznJzqWFj4zs751dX86Oj87Oz3xsb62tplqoOdAAABWUlEQVR4AY3RBZLDMAwF0DBUcsB2mOOmDff+t9tksTjbb/YblKS3IyuK/EJUTTcM3bSemGIfALcQx/UezKe7EMKQB/caAmDkxGQLQHJrKTICtgOEMMYAzWvzMiSYF5yRuOQZxeoaaw4MkgiAlbnfVMDbKxSMHrvwQHcKm4TG+hUmULqn6Bz4SWMkQUSZuMIAut4OHDt03eJMkZFr7AnN6yYauGZwYIRR+wpbjpUbsq4for0QwFXpKgfM/OPBIJ2zI0bSdUZGMico8exviPSu+DrFrgTMHCTI0/uG2R3iXnbEarN7bY0MEKETk/yso9akaZPlSe9nTutFXyzb3pa0rK5et9KifqN5ScWlTzfqNe1itIuntdP6jaulr4uoJckViz2K1FTlvp5/sE7muUkt1W28UQhNnWXXlL5jjamiTKppWqOs1Ku5TvLo/aAse9smKd627Y/Py7/5APAHHc7e0PZdAAAAAElFTkSuQmCC"
                txt_Link.ForeColor = Color.FromArgb(224, 224, 224)
                Label11.Text = ""
            Case Else
                txt_Link.Text = "???"
        End Select
    End Sub

    Private Sub btnBuild_Click(sender As Object, e As EventArgs) Handles btnBuild.Click
        ' التحقق من صحة الرابط
        If String.IsNullOrWhiteSpace(txt_Logo_Page.Text) OrElse Not Regex.IsMatch(txt_Logo_Page.Text, linkPattern) Then
            XtraMessageBox.Show("Please enter a valid link starting with http or https.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txt_Logo_Page.Focus()

            Exit Sub
        End If
        If String.IsNullOrWhiteSpace(txt_Link_New.Text) OrElse Not Regex.IsMatch(txt_Link_New.Text, linkPattern) Then
            XtraMessageBox.Show("Enter Your Page Link.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txt_Link_New.Focus()

            Exit Sub
        End If
        If String.IsNullOrWhiteSpace(txt_outlook.Text) OrElse Not Regex.IsMatch(txt_outlook.Text, linkPattern) Then
            XtraMessageBox.Show("Please Enter a Disguise link.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txt_outlook.Focus()

            Exit Sub
        End If

        If txt_Type.Text.Trim = "" Then
            txt_Type.Focus()
            XtraMessageBox.Show("Enter the text that will Appear on the Page.", "Text", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        ' التحقق من وجود نص في RichTextBox1
        If Not String.IsNullOrEmpty(RichTextBox1.Text) Then
            ' حفظ النص الأصلي
            Dim OriginalText As String = RichTextBox1.Text
            ' تحديد مسار المجلد الهدف
            Dim folderPath As String = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "File Multi Linker")
            ' إنشاء المجلد إذا لم يكن موجودًا
            If Not Directory.Exists(folderPath) Then
                Directory.CreateDirectory(folderPath)
            End If
            ' تحديد اسم الملف الهدف
            Dim baseFileName As String = "Multi Linker"
            Dim fileExtension As String = ".html"
            Dim destFile As String = Path.Combine(folderPath, baseFileName & fileExtension)
            Dim counter As Integer = 1
            ' التحقق إذا كان الملف موجودًا وتعديل الاسم إذا لزم الأمر
            While File.Exists(destFile)
                destFile = Path.Combine(folderPath, $"{baseFileName}{counter}{fileExtension}")
                counter += 1
            End While
            ' استدعاء دالة ConvertFile لتحويل النص
            ConvertFile(OriginalText, destFile, "Microsoft")
            ' استرجاع النص الأصلي
            RichTextBox1.Text = OriginalText
            ' عرض رسالة بنجاح العملية
            DevExpress.XtraEditors.XtraMessageBox.Show("File Successfully !", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ' فتح المجلد الذي يحتوي على الملف
            Process.Start("explorer.exe", folderPath)
            ' تحديث الواجهة

            Label2.Visible = True



        Else
            ' عرض رسالة في حالة عدم وجود نص
            DevExpress.XtraEditors.XtraMessageBox.Show("Source text is empty!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub BntClearLink_Click(sender As Object, e As EventArgs) Handles BntClearLink.Click
        txt_Link.Clear()
    End Sub

    Private Sub SimpleButton6_Click(sender As Object, e As EventArgs) Handles SimpleButton6.Click
        txt_Link_New.Clear()
    End Sub

    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        txt_outlook.Clear()
    End Sub

    Private Sub SimpleButton7_Click(sender As Object, e As EventArgs) Handles SimpleButton7.Click
        txt_Type.Clear()
    End Sub

    Private Sub SimpleButton4_Click(sender As Object, e As EventArgs) Handles SimpleButton4.Click
        txt_Logo_Page.Clear()
    End Sub

    Private Sub SimpleButton3_Click_1(sender As Object, e As EventArgs) Handles SimpleButton3.Click
        txt_Logo_Button.Clear()
    End Sub

    Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click
        txt_Button.Clear()
    End Sub

    Private Sub Clear1_Click(sender As Object, e As EventArgs) Handles Clear1.Click
        txt_length_Outlook_B.Clear()
    End Sub

    Private Sub Clear2_Click(sender As Object, e As EventArgs) Handles Clear2.Click
        txt_width_Outlook_B.Clear()
    End Sub

    Private Sub Clear3_Click(sender As Object, e As EventArgs) Handles Clear3.Click
        txt_length_Example.Clear()
    End Sub

    Private Sub Clear4_Click(sender As Object, e As EventArgs) Handles Clear4.Click
        txt_width_Example.Clear()
    End Sub

    Private Sub bntSendLogo_Click(sender As Object, e As EventArgs) Handles bntSendLogo.Click
        ' التحقق من أن حقل txt_Link غير فارغ
        If txt_Link.Text.Trim = "" Then

            XtraMessageBox.Show("Choose the logo first.", "Text", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txt_Link.Focus()
        End If
        ' تغيير لون النص في txt_Link
        txt_Link.ForeColor = Color.FromArgb(34, 203, 121)
        ' تحديث النصوص لعرض رسالة نجاح
        Label11.Text = "The logo has been transferred Successfully. Check the Logo Settings menu"
        ' تفريغ الحقول المرتبطة بعرض مثال الشعار
        lbl_Logo_Example.Text = ""
        txt_length_Example.Text = ""
        txt_width_Example.Text = ""
        PictureBox2.Image = Nothing
        ' التحقق من وجود نص في txt_Logo_Page وتفريغه إذا لزم الأمر
        If txt_Logo_Page.Text <> "" Then
            txt_Logo_Page.Text = "" ' حذف النص الموجود
        End If
        ' نسخ النص من txt_Link إلى txt_Logo_Page
        txt_Logo_Page.Text = txt_Link.Text
        ' التحقق من وجود نص في txt_Logo_Button وتفريغه إذا لزم الأمر
        If txt_Logo_Button.Text <> "" Then
            txt_Logo_Button.Text = ""
        End If
        ' نسخ النص من txt_Link_Icon إلى txt_Logo_Button
        txt_Logo_Button.Text = txt_Link_Icon.Text
        Label6.Visible = True
    End Sub
End Class