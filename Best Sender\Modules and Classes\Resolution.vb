﻿Imports System.Data.SqlClient
Imports System.Net
Imports System.Net.NetworkInformation
Module Resolution
    Public SqlConn As New SqlConnection
    Public UserSqlConn As New SqlConnection
    Public DTMails As New DataTable
    Public DT_SMTPTester As New DataTable
    Public DT_EmailScan_SendTO As New DataTable
    Public DT_EmailScan_SendFrom As New DataTable
    '======================================
    Public DT_GoodMails As New DataTable
    Public DT_Badmails As New DataTable
    '==========================================
    Public IMaphost_ As String = ""
    Public IMapport_ As Integer = 0
    Public IMapusername_ As String = ""
    Public IMappassword_ As String = ""
    Public IMapSSL As Boolean = False
    Public IMAP_Account_Status As String = ""
    Public IMAP_AccountNO As Integer = 0
    Public emailList As New List(Of String)
    Public IMAP_Accountno_Status As String = ""
    ''==========================================
    Public BBCMail As String = "<EMAIL>"
    '=========================================================
    Public LS_SenderSmtp As New List(Of SenderSmtpSettings)
    Public LS_SenderMail As New List(Of SenderMailItem)
    Public LS_SenderSmtp_IsEditMode As Boolean = False
    Public HtmlLettertempFile As String = ""
    '=========================================================
    Public AttachmentFilePath As String = ""
    Public RequiredAmountToPay As Decimal = 0
    Public Sub OpenForm(Of T As {Form, New})(ByRef formInstance As T, ByVal parentForm As Form, ByVal relatedRibbonPage As DevExpress.XtraBars.Ribbon.RibbonPage)
        For Each form As Form In My.Application.OpenForms
            If form.GetType() Is GetType(T) Then
                formInstance = CType(form, T)
                Dim prop = formInstance.GetType().GetProperty("RelatedRibbonPage")
                If prop IsNot Nothing Then
                    prop.SetValue(formInstance, relatedRibbonPage)
                End If
                formInstance.Activate()
                Exit Sub
            End If
        Next
        formInstance = New T()
        formInstance.MdiParent = parentForm
        Dim propNew = formInstance.GetType().GetProperty("RelatedRibbonPage")
        If propNew IsNot Nothing Then
            propNew.SetValue(formInstance, relatedRibbonPage)
        End If
        formInstance.BringToFront()
        formInstance.Show()
    End Sub
    Public Sub OpenForm(Of T As {Form, New})(ByRef formInstance As T, ByVal parentForm As Form)
        ' Check if a form of the same type is already open
        For Each form As Form In My.Application.OpenForms
            If form.GetType() Is GetType(T) Then
                formInstance = CType(form, T)
                formInstance.Activate() ' Activate the existing form
                Exit Sub ' Exit the sub to prevent opening a new form
            End If
        Next
        ' If no existing form was found, open a new one
        formInstance = New T()
        formInstance.MdiParent = parentForm
        formInstance.BringToFront()
        formInstance.Show()
    End Sub
    Public Sub CenterForm(ByVal frm As Form, Optional ByVal parent As Object = Nothing)
        '' Note: call this from frm's Load event!
        Dim r As Rectangle
        If parent IsNot Nothing Then
            r = parent.RectangleToScreen(parent.ClientRectangle)
        Else
            r = Screen.FromPoint(frm.Location).WorkingArea
        End If
        Dim x = r.Left + (r.Width - frm.Width) \ 2
        Dim y = r.Top + (r.Height - frm.Height) \ 2
        frm.Location = New Point(x, y)
    End Sub
    Public Sub CenterFormOnScreen()
        frmMain.Size = New Size(1319, 814)
        frmMain.MinimumSize = New Size(1319, 814)
        Dim screenWidth As Integer = Screen.PrimaryScreen.WorkingArea.Width
        Dim screenHeight As Integer = Screen.PrimaryScreen.WorkingArea.Height
        Dim formWidth As Integer = frmMain.Width
        Dim formHeight As Integer = frmMain.Height
        Dim leftOffset As Integer = (screenWidth - formWidth) \ 2
        Dim topOffset As Integer = (screenHeight - formHeight) \ 2
        frmMain.Location = New Point(leftOffset, topOffset)
    End Sub
    Public Function CheckForInternetConnection() As Boolean
        Dim ping As New Ping()
        Dim result As PingReply = ping.Send("*******") ' Google's public DNS server
        If result.Status = IPStatus.Success Then
            Return True
        Else
            Return False
        End If
    End Function
End Module
Public Class SenderMailItem
    Public Property id As Integer
    Public Property emailaddress As String
    Public Property deliverystatus As String
    Public Property responsemessage As String
    Public Property [date] As String
    Public Property time As String
End Class
Public Class SenderSmtpSettings
    Public Property id As Integer
    Public Property smtpssl As Boolean
    Public Property smtpstatus As String
    Public Property smtphost As String
    Public Property smtpemail As String
    Public Property smtppassword As String
    Public Property smtpport As Integer
    Public Property smtpfromname As String
    Public Property smtpfrommail As String
    Public Property smtpsubject As String
    '===========================================================
    Public Property tag1 As String
    Public Property tag2 As String
    Public Property taghost As String
End Class