﻿<?xml version='1.0' encoding='UTF-8'?>
<svg x="0px" y="0px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" id="Layer_1" style="enable-background:new 0 0 32 32">
  <style type="text/css">
	.Black{fill:#727272;}
	.Yellow{fill:#FFB115;}
	.Blue{fill:#1177D7;}
	.Green{fill:#039C23;}
	.Red{fill:#D11C1C;}
	.White{fill:#FFFFFF;}
	.st0{opacity:0.75;}
	.st1{opacity:0.5;}
	.st2{opacity:0.25;}
</style>
  <g id="HighlightFields">
    <g class="st1">
      <rect x="2" y="22" width="28" height="6" class="Black" />
    </g>
    <path d="M10,15.1L19.1,6H3C2.5,6,2,6.5,2,7v12c0,0.5,0.5,1,1,1h7V15.1z" class="Yellow" />
    <path d="M27,9L17,19l-4-4L23,5L27,9z M28,8l1.7-1.7c0.4-0.4,0.4-1,0-1.3l-2.7-2.7c-0.4-0.4-1-0.4-1.3,0L24,4L28,8z    M12,16v4h4L12,16z" class="Red" />
  </g>
</svg>