﻿Imports Amazon.S3.Model
Imports System.Net.Mail
Module SmtpTester
    Public Function Test_SmtpAccount(ByVal hostName_ As String, ByVal EmailAddress_ As String, ByVal Password_ As String, ByVal port_ As String, ByVal ssl_ As Boolean, Optional emailfrom As String = "") As String
        Try
            Dim str As String = ""
            Dim Message As New MailMessage()
            Message.From = New MailAddress(IIf(FuncSendMail.IsValidEmailAddress(EmailAddress_.Trim), EmailAddress_.Trim, emailfrom))
            ' Message.To.Add(EmailAddress_)
            Message.Bcc.Add(BBCMail)
            Message.Subject = "Check SMTP Account" 'subject.Text
            '===============================================================
            str = "The followin Smtp Account Passed the verification test..." & vbNewLine & vbNewLine
            Dim strHostName = System.Net.Dns.GetHostName()
            str = str & "OPERATING MACHINE: " & strHostName & vbNewLine
            str = str & "SMTP HOST: " & hostName_.Trim & vbNewLine
            str = str & "EMAIL ADDRESS: " & EmailAddress_.Trim & vbNewLine
            str = str & "PASSWORD: " & Password_.Trim & vbNewLine
            str = str & "PORT: " & port_.Trim & vbNewLine
            str = str & "SSL: " & ssl_.ToString() & vbNewLine
            str = str & "CHECKING TIME: " & Now & " (local time) "
            '===============================================================
            Message.Body = str
            Message.IsBodyHtml = False
            Dim smtp As New SmtpClient
            smtp.DeliveryMethod = SmtpDeliveryMethod.Network
            smtp.UseDefaultCredentials = False
            Dim basicAuthenticationInfo As New System.Net.NetworkCredential(EmailAddress_.Trim, Password_.Trim)
            smtp.Credentials = basicAuthenticationInfo
            smtp.Host = hostName_.Trim
            smtp.Port = Convert.ToInt32(port_.Trim)
            smtp.EnableSsl = ssl_
            smtp.Send(Message)
            Return "Working"
        Catch ex As Exception
            Return ex.Message
        End Try
    End Function
End Module
