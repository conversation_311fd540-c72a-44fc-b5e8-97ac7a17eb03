﻿Imports System.IO
Imports System.Text.RegularExpressions
Imports System.ComponentModel
Imports DevExpress.XtraGrid
Imports DevExpress.XtraEditors
Imports System.Data
Imports DevExpress.XtraGrid.Views.Grid
Imports DevExpress.Utils
Imports System.Threading
Imports DevExpress.XtraBars.Ribbon
Public Class frmEMailSorterDomain
    Public Property RelatedRibbonPage As RibbonPage
    Private worker As BackgroundWorker
    Private stopProcess As Boolean = False
    Private totalEmails As Integer = 0
    Private processedEmails As Integer = 0
    Private emailList As New List(Of String)()
    Private filePath As String = String.Empty
    Private dt As New DataTable()
    Private currentIndex As Integer = 0
    Private Sub frmEMailSorterDomain_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InitializeDataTable()
        InitializeBackgroundWorker()
        ' إعداد خصائص ProgressBarControl هنا
        ProgressBarControl1.Properties.Minimum = 0
        ProgressBarControl1.Properties.Maximum = 100
        ProgressBarControl1.Properties.Step = 1
        ProgressBarControl1.Properties.PercentView = True
        ProgressBarControl1.Properties.ShowTitle = True
    End Sub
    Private Sub InitializeDataTable()
        dt.Columns.Add("Email", GetType(String))
        dt.Columns.Add("Host", GetType(String))
        dt.Columns.Add("Domain", GetType(String))
        dt.Columns.Add("Type", GetType(String))
        GridControl1.DataSource = dt
    End Sub
    Private Sub InitializeBackgroundWorker()
        worker = New BackgroundWorker()
        With worker
            .WorkerReportsProgress = True
            .WorkerSupportsCancellation = True
            AddHandler .DoWork, AddressOf worker_DoWork
            AddHandler .ProgressChanged, AddressOf worker_ProgressChanged
            AddHandler .RunWorkerCompleted, AddressOf worker_RunWorkerCompleted
        End With
    End Sub
    Public Sub Select_txt()
        Dim openFileDialog As New OpenFileDialog()
        With openFileDialog
            .Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*"
            .Title = "Select Email List File"
            .Multiselect = False
        End With
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            filePath = openFileDialog.FileName
            txtFilePath.Text = filePath
            Add_Mail_List()
            frmMain.btnStart.Enabled = True
            frmMain.ResetAll.Enabled = True
        End If
    End Sub
    Public Sub Add_Mail_List()
        Try
            Clear_All_Data()
            dt.Rows.Clear()
            emailList.Clear()
            totalEmails = 0
            processedEmails = 0
            ProgressBarControl1.Position = 0
            lblStatus.Text = "Loading file..."
            If Not File.Exists(filePath) Then
                XtraMessageBox.Show("File not found!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If
            ' قراءة الملف مع التحديث التدريجي
            Dim allLines As List(Of String) = New List(Of String)()
            Dim lineCount As Integer = 0
            Dim totalLines As Integer = File.ReadAllLines(filePath).Length
            ' تحديث شريط التقدم لمرحلة الرفع
            ProgressBarControl1.Properties.Maximum = totalLines
            ProgressBarControl1.Position = 0
            Using reader As New StreamReader(filePath)
                While Not reader.EndOfStream
                    Dim line As String = reader.ReadLine().Trim()
                    lineCount += 1
                    ' تحديث شريط التقدم كل 100 سطر أو عند اكتمال الملف
                    If lineCount Mod 100 = 0 OrElse reader.EndOfStream Then
                        ProgressBarControl1.Position = lineCount
                        lblStatus.Text = $"Loading... {lineCount} of {totalLines} lines"
                        Application.DoEvents()
                    End If
                    If Not String.IsNullOrWhiteSpace(line) Then
                        allLines.Add(line)
                    End If
                End While
            End Using
            ' تصفية البريد الإلكتروني الصالح مع التحديث
            Dim validEmails As New List(Of String)()
            ProgressBarControl1.Position = 0
            ProgressBarControl1.Properties.Maximum = allLines.Count
            lblStatus.Text = "Validating emails..."
            For i As Integer = 0 To allLines.Count - 1
                If IsValidEmail(allLines(i)) Then
                    validEmails.Add(allLines(i))
                End If
                ' تحديث شريط التقدم كل 100 سطر
                If i Mod 100 = 0 OrElse i = allLines.Count - 1 Then
                    ProgressBarControl1.Position = i + 1
                    lblStatus.Text = $"Validating... {i + 1} of {allLines.Count} lines"
                    Application.DoEvents()
                End If
            Next
            If validEmails.Count = 0 Then
                XtraMessageBox.Show("No valid email addresses found in the file!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If
            ' ملء DataTable مع التحديث
            ProgressBarControl1.Position = 0
            ProgressBarControl1.Properties.Maximum = validEmails.Count
            lblStatus.Text = "Preparing data..."
            For i As Integer = 0 To validEmails.Count - 1
                dt.Rows.Add(validEmails(i), "", "", "")
                ' تحديث شريط التقدم كل 100 سطر
                If i Mod 100 = 0 OrElse i = validEmails.Count - 1 Then
                    ProgressBarControl1.Position = i + 1
                    lblStatus.Text = $"Preparing... {i + 1} of {validEmails.Count} emails"
                    Application.DoEvents()
                End If
            Next
            ' تعيين الإحصائيات النهائية
            totalEmails = validEmails.Count
            emailList = validEmails
            ProgressBarControl1.Position = totalEmails
            lblStatus.Text = $"Ready: {totalEmails} emails loaded"
            GridView1.PopulateColumns()
            frmMain.btnStart.Enabled = True
        Catch ex As Exception
            XtraMessageBox.Show($"Error loading file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Public Sub Start_Extract()
        If emailList Is Nothing OrElse emailList.Count = 0 Then
            XtraMessageBox.Show("No emails to process", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        If worker.IsBusy Then
            XtraMessageBox.Show("Process is already running", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        stopProcess = False
        processedEmails = 0
        ProgressBarControl1.Position = 0
        lblStatus.Text = "Processing..."
        frmMain.btnStart.Enabled = False
        frmMain.btnStop.Enabled = True
        frmMain.BntSave.Enabled = True
        frmMain.ResetAll.Enabled = True
        worker.RunWorkerAsync()
    End Sub
    Public Sub Stop_Extract()
        stopProcess = True
        If worker IsNot Nothing AndAlso worker.IsBusy Then
            worker.CancelAsync()
        End If
        frmMain.btnStart.Enabled = True
        frmMain.btnStop.Enabled = False
        frmMain.BntSave.Enabled = True ' تأكد من أن زر الحفظ يبقى مفعلاً بعد الإيقاف
        frmMain.ResetAll.Enabled = True
    End Sub
    Public Sub Clear_All_Data()
        dt.Rows.Clear()
        emailList.Clear()
        totalEmails = 0
        processedEmails = 0
        currentIndex = 0
        ProgressBarControl1.Position = 0
        lblStatus.Text = "Ready"
        frmMain.ResetAll.Enabled = False
        frmMain.BntSave.Enabled = False
        frmMain.btnStart.Enabled = False
    End Sub
    Private Sub worker_DoWork(sender As Object, e As DoWorkEventArgs)
        Dim worker As BackgroundWorker = CType(sender, BackgroundWorker)
        For i As Integer = 0 To dt.Rows.Count - 1
            If worker.CancellationPending OrElse stopProcess Then
                e.Cancel = True
                Exit For
            End If
            Dim email As String = dt.Rows(i)("Email").ToString()
            Dim host As String = GetEmailHost(email)
            Dim domain As String = GetEmailDomain(host)
            Dim emailType As String = GetEmailType(host)
            ' تحديث البيانات بطريقة آمنة
            Me.Invoke(Sub()
                          dt.BeginLoadData()
                          Try
                              dt.Rows(i).BeginEdit()
                              dt.Rows(i)("Host") = host
                              dt.Rows(i)("Domain") = domain
                              dt.Rows(i)("Type") = emailType
                              dt.Rows(i).EndEdit()
                          Finally
                              dt.EndLoadData()
                          End Try
                      End Sub)
            processedEmails += 1
            currentIndex = i + 1
            ' تحديث شريط التقدم كل 50 سطر أو عند اكتمال المهمة
            If i Mod 50 = 0 OrElse i = dt.Rows.Count - 1 Then
                Dim progress As Integer = CInt((i + 1) * 100 / dt.Rows.Count)
                worker.ReportProgress(progress, $"Processing {i + 1} of {dt.Rows.Count} emails")
            End If
            Thread.Sleep(1) ' تأخير بسيط للسماح بتحديث الواجهة
        Next
    End Sub
    Private Sub worker_ProgressChanged(sender As Object, e As ProgressChangedEventArgs)
        ProgressBarControl1.Position = e.ProgressPercentage
        lblStatus.Text = e.UserState.ToString()
        Application.DoEvents()
    End Sub
    Private Sub worker_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs)
        frmMain.btnStart.Enabled = True
        frmMain.btnStop.Enabled = False
        frmMain.btnSelectFile.Enabled = True
        frmMain.ResetAll.Enabled = True
        frmMain.BntSave.Enabled = True ' تأكد من أن زر الحفظ يبقى مفعلاً بعد الانتهاء
        If e.Cancelled Then
            lblStatus.Text = $"Paused at {currentIndex} of {totalEmails} emails"
        ElseIf e.Error IsNot Nothing Then
            lblStatus.Text = "Error occurred during processing"
            XtraMessageBox.Show($"Error: {e.Error.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Else
            lblStatus.Text = $"Completed: {processedEmails} emails processed"
            currentIndex = 0
            XtraMessageBox.Show("Done", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Public Sub Save_File_Email()
        If dt.Rows.Count = 0 Then
            XtraMessageBox.Show("No data to save!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        Dim saveFileDialog As New SaveFileDialog()
        With saveFileDialog
            .Filter = "CSV Files (*.csv)|*.csv|Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*"
            .Title = "Save Processed Email List"
            .FileName = "ProcessedEmails_" & DateTime.Now.ToString("yyyyMMdd_HHmmss")
        End With
        If saveFileDialog.ShowDialog() = DialogResult.OK Then
            Try
                If saveFileDialog.FilterIndex = 1 Then
                    GridControl1.ExportToCsv(saveFileDialog.FileName)
                ElseIf saveFileDialog.FilterIndex = 2 Then
                    GridControl1.ExportToXlsx(saveFileDialog.FileName)
                End If
                XtraMessageBox.Show("Data saved successfully", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
                dt.Rows.Clear()
                emailList.Clear()
                totalEmails = 0
                processedEmails = 0
                currentIndex = 0
                ProgressBarControl1.Position = 0
                lblStatus.Text = "Ready"
                frmMain.ResetAll.Enabled = False
                frmMain.BntSave.Enabled = False
                frmMain.btnStart.Enabled = False
            Catch ex As Exception
                XtraMessageBox.Show("Error saving file: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
#Region "Helper Methods"
    Private Function IsValidEmail(email As String) As Boolean
        If String.IsNullOrWhiteSpace(email) OrElse email.IndexOf("@") <= 0 Then Return False
        Try
            Return Regex.IsMatch(email,
                "^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$",
                RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250))
        Catch
            Return False
        End Try
    End Function
    Private Function GetEmailHost(email As String) As String
        If String.IsNullOrEmpty(email) OrElse Not email.Contains("@") Then Return ""
        Return email.Split("@"c)(1).Trim().ToLower()
    End Function
    Private Function GetEmailDomain(host As String) As String
        If String.IsNullOrEmpty(host) Then Return ""
        Dim parts As String() = host.Split("."c)
        If parts.Length >= 2 Then
            Return $"{parts(parts.Length - 2)}.{parts(parts.Length - 1)}"
        End If
        Return host
    End Function
    Private Function GetEmailType(host As String) As String
        If String.IsNullOrEmpty(host) Then Return "Unknown"
        ' قاموس مزودي البريد الإلكتروني
        Dim emailProviders As New Dictionary(Of String, String)(StringComparer.OrdinalIgnoreCase) From {
         {"gmail.com", "Gmail"},
      {"googlemail.com", "Gmail"},
      {"outlook.com", "Outlook"},
      {"office365.com", "Office 365"},
      {"microsoftonline.com", "Office 365"},
      {"yahoo.com", "Yahoo Mail"},
      {"yahoomail.com", "Yahoo Mail"},
      {"ymail.com", "Yahoo Mail"},
      {"icloud.com", "iCloud Mail"},
      {"me.com", "iCloud Mail"},
      {"mac.com", "iCloud Mail"},
      {"hotmail.com", "Hotmail"},
      {"live.com", "Live Mail"},
      {"msn.com", "MSN Mail"},
      {"aol.com", "AOL Mail"},
      {"aim.com", "AOL Mail"},
      {"zoho.com", "Zoho Mail"},
      {"protonmail.com", "ProtonMail"},
      {"proton.me", "ProtonMail"},
      {"yandex.com", "Yandex Mail"},
      {"ya.ru", "Yandex Mail"},
      {"gmx.com", "GMX Mail"},
      {"gmx.net", "GMX Mail"},
      {"gmx.us", "GMX Mail"},
      {"tutanota.com", "Tutanota"},
      {"tuta.io", "Tutanota"},
      {"fastmail.com", "Fastmail"},
      {"mail.com", "Mail.com"},
      {"email.com", "Mail.com"},
      {"hushmail.com", "Hushmail"},
      {"inbox.com", "Inbox.com"},
      {"lycos.com", "Lycos Mail"},
      {"rediffmail.com", "Rediffmail"},
      {"startmail.com", "StartMail"},
      {"runbox.com", "Runbox"},
      {"web.de", "Web.de"},
      {"t-online.de", "T-Online Mail"},
      {"orange.fr", "Orange Mail"},
      {"laposte.net", "Laposte Mail"},
      {"email.it", "Email.it"},
      {"seznam.cz", "Seznam.cz"},
      {"zoznam.sk", "Zoznam.sk"},
      {"mail.ru", "Mail.ru"},
      {"bk.ru", "Mail.ru"},
      {"list.ru", "Mail.ru"},
      {"inbox.ru", "Mail.ru"},
      {"rambler.ru", "Rambler.ru"},
      {"freemail.hu", "Freemail.hu"},
      {"email.cz", "Email.cz"},
      {"virgilio.it", "Virgilio Mail"},
      {"libero.it", "Libero Mail"},
      {"centrum.cz", "Centrum.cz"},
      {"wanadoo.fr", "Wanadoo Mail"},
      {"bluewin.ch", "Bluewin.ch"},
      {"bt.com", "BT Mail"},
      {"sapo.pt", "Sapo.pt"},
      {"walla.com", "Walla Mail"},
      {"terra.com", "Terra Mail"},
      {"uol.com.br", "UOL Mail"},
      {"bol.com.br", "Bol Mail"},
      {"naver.com", "Naver Mail"},
      {"daum.net", "Daum Mail"},
      {"hanmail.net", "Hanmail"},
      {"qq.com", "QQ Mail"},
      {"163.com", "163.com Mail"},
      {"126.com", "126.com Mail"},
      {"sina.com", "Sina Mail"},
      {"sohu.com", "Sohu Mail"},
      {"gawab.com", "Gawab.com"},
      {"care2.com", "Care2 Mail"},
      {"usa.com", "USA.com Mail"},
      {"europe.com", "Europe.com Mail"},
      {"consultant.com", "Consultant.com"},
      {"myway.com", "MyWay Mail"},
      {"zoemail.com", "Zoemail"},
      {"bigpond.com", "BigPond Mail"},
      {"shaw.ca", "Shaw.ca Mail"},
      {"rogers.com", "Rogers.com Mail"},
      {"charter.net", "Charter.net"},
      {"comcast.net", "Comcast.net"},
      {"cox.net", "Cox.net"},
      {"bellsouth.net", "Bellsouth.net"},
      {"att.net", "ATT.net"},
      {"verizon.net", "Verizon.net"},
      {"earthlink.net", "Earthlink.net"},
      {"juno.com", "Juno Mail"},
      {"netzero.net", "NetZero Mail"},
      {"optonline.net", "Optonline.net"},
      {"freeserve.co.uk", "Freeserve.co.uk"},
      {"talktalk.net", "TalkTalk Mail"},
      {"sky.com", "Sky.com Mail"},
      {"virginmedia.com", "Virgin Media Mail"},
      {"plus.net", "Plusnet Mail"},
      {"post.cz", "Post.cz"},
      {"atlas.cz", "Atlas.cz"},
      {"btinternet.com", "Btinternet.com"},
      {"tiscali.co.uk", "Tiscali Mail"},
      {"supanet.com", "Supanet Mail"},
      {"clix.pt", "Clix.pt Mail"},
      {"glay.org", "Glay.org Mail"},
      {"email.ua", "Email.ua"},
      {"hinet.net", "Hinet.net"},
      {"docomo.ne.jp", "Docomo.ne.jp"},
      {"kddi.com", "KDDI Mail"},
      {"rakuten.com", "Rakuten Mail"},
      {"yahoo.co.jp", "Yahoo.co.jp"},
      {"o2.co.uk", "O2.co.uk"},
      {"gmx.fr", "Gmx.fr"},
      {"live.fr", "Live.fr"},
      {"voila.fr", "Voila.fr"},
      {"hot.ee", "Hot.ee"},
      {"mail.bg", "Mail.bg"},
      {"abv.bg", "Abv.bg"},
      {"autistici.org", "Autistici.org"},
      {"riseup.net", "Riseup.net"}
   }
        ' التحقق من المطابقة التامة
        If emailProviders.ContainsKey(host) Then
            Return emailProviders(host)
        End If
        ' التحقق من النطاقات الفرعية
        For Each provider In emailProviders
            If host.EndsWith("." & provider.Key) Then
                Return provider.Value
            End If
        Next
        Return "Custom Domain"
    End Function
    Private Sub frmEMailSorterDomain_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        frmMain.btnStart.Enabled = False
        frmMain.btnStop.Enabled = False
        frmMain.BntSave.Enabled = False ' تأكد من أن زر الحفظ يبقى مفعلاً بعد الإيقاف
        frmMain.ResetAll.Enabled = False
        frmMain.btnSelectFile.Enabled = False
        frmMain.RibbonPageGroup2.Visible = False
        frmMain.RibbonPageGroup18.Visible = False
        frmMain.RibbonPageGroup32.Visible = False
        frmMain.RibbonPageGroup34.Visible = False
        frmMain.RibbonPageGroup36.Visible = False
    End Sub
#End Region
End Class