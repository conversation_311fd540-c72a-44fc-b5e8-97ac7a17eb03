﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmMultiLinker
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmMultiLinker))
        Me.Timer2 = New System.Windows.Forms.Timer(Me.components)
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.XtraTabPage1 = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.Clear2 = New DevExpress.XtraEditors.SimpleButton()
        Me.Clear1 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton3 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton4 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton5 = New DevExpress.XtraEditors.SimpleButton()
        Me.txt_Button = New DevExpress.XtraEditors.TextEdit()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.txt_Logo_Button = New DevExpress.XtraEditors.TextEdit()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.txt_Logo_Page = New DevExpress.XtraEditors.TextEdit()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Pic_L_B = New System.Windows.Forms.PictureBox()
        Me.lbl_S_B = New System.Windows.Forms.Label()
        Me.txt_width_Outlook_B = New DevExpress.XtraEditors.TextEdit()
        Me.txt_length_Outlook_B = New DevExpress.XtraEditors.TextEdit()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.lbl_Size_Logo_Page = New System.Windows.Forms.Label()
        Me.Pic_Logo_Page = New System.Windows.Forms.PictureBox()
        Me.txt_width_Outlook = New DevExpress.XtraEditors.TextEdit()
        Me.txt_length_Outlook = New DevExpress.XtraEditors.TextEdit()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.XtraTabPage2 = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControl4 = New DevExpress.XtraEditors.GroupControl()
        Me.lbl_Logo_Example = New System.Windows.Forms.Label()
        Me.BntClearLink = New DevExpress.XtraEditors.SimpleButton()
        Me.txt_Link = New DevExpress.XtraEditors.TextEdit()
        Me.bntSendLogo = New DevExpress.XtraEditors.SimpleButton()
        Me.PictureBoxSee = New System.Windows.Forms.PictureBox()
        Me.GroupControl3 = New DevExpress.XtraEditors.GroupControl()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Clear4 = New DevExpress.XtraEditors.SimpleButton()
        Me.Clear3 = New DevExpress.XtraEditors.SimpleButton()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.txt_width_Example = New DevExpress.XtraEditors.TextEdit()
        Me.ComboBoxEdit1 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.txt_length_Example = New DevExpress.XtraEditors.TextEdit()
        Me.txt_Link_Icon = New DevExpress.XtraEditors.TextEdit()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.PictureBox4 = New System.Windows.Forms.PictureBox()
        Me.XtraTabPage3 = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControl5 = New DevExpress.XtraEditors.GroupControl()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton6 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton7 = New DevExpress.XtraEditors.SimpleButton()
        Me.txt_Type = New DevExpress.XtraEditors.TextEdit()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.txt_outlook = New DevExpress.XtraEditors.TextEdit()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.txt_Link_New = New DevExpress.XtraEditors.TextEdit()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.SeparatorControl1 = New DevExpress.XtraEditors.SeparatorControl()
        Me.BTNSAVEALL = New DevExpress.XtraEditors.SimpleButton()
        Me.Bnt_Reset_All = New DevExpress.XtraEditors.SimpleButton()
        Me.btnBuild = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupControl6 = New DevExpress.XtraEditors.GroupControl()
        Me.cb_txt_conversion = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.cb_txt_Time = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.PictureBox5 = New System.Windows.Forms.PictureBox()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.XtraTabPage1.SuspendLayout()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        CType(Me.txt_Button.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_Logo_Button.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_Logo_Page.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Pic_L_B, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_width_Outlook_B.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_length_Outlook_B.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.Pic_Logo_Page, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_width_Outlook.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_length_Outlook.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage2.SuspendLayout()
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl4.SuspendLayout()
        CType(Me.txt_Link.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBoxSee, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl3.SuspendLayout()
        CType(Me.txt_width_Example.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_length_Example.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_Link_Icon.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage3.SuspendLayout()
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl5.SuspendLayout()
        CType(Me.txt_Type.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_outlook.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_Link_New.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl6.SuspendLayout()
        CType(Me.cb_txt_conversion.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Timer2
        '
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Location = New System.Drawing.Point(12, 12)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.XtraTabPage1
        Me.XtraTabControl1.Size = New System.Drawing.Size(1199, 574)
        Me.XtraTabControl1.TabIndex = 1
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.XtraTabPage1, Me.XtraTabPage2, Me.XtraTabPage3})
        '
        'XtraTabPage1
        '
        Me.XtraTabPage1.Appearance.Header.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.XtraTabPage1.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage1.Controls.Add(Me.GroupControl2)
        Me.XtraTabPage1.Controls.Add(Me.GroupControl1)
        Me.XtraTabPage1.Controls.Add(Me.SimpleButton1)
        Me.XtraTabPage1.Controls.Add(Me.PictureBox1)
        Me.XtraTabPage1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.settings32x32
        Me.XtraTabPage1.Name = "XtraTabPage1"
        Me.XtraTabPage1.Size = New System.Drawing.Size(1197, 530)
        Me.XtraTabPage1.Text = "Logo Settings"
        '
        'GroupControl2
        '
        Me.GroupControl2.Controls.Add(Me.Clear2)
        Me.GroupControl2.Controls.Add(Me.Clear1)
        Me.GroupControl2.Controls.Add(Me.SimpleButton3)
        Me.GroupControl2.Controls.Add(Me.SimpleButton4)
        Me.GroupControl2.Controls.Add(Me.SimpleButton5)
        Me.GroupControl2.Controls.Add(Me.txt_Button)
        Me.GroupControl2.Controls.Add(Me.Label11)
        Me.GroupControl2.Controls.Add(Me.txt_Logo_Button)
        Me.GroupControl2.Controls.Add(Me.Label10)
        Me.GroupControl2.Controls.Add(Me.txt_Logo_Page)
        Me.GroupControl2.Controls.Add(Me.Label9)
        Me.GroupControl2.Controls.Add(Me.Label4)
        Me.GroupControl2.Controls.Add(Me.Label5)
        Me.GroupControl2.Controls.Add(Me.Pic_L_B)
        Me.GroupControl2.Controls.Add(Me.lbl_S_B)
        Me.GroupControl2.Controls.Add(Me.txt_width_Outlook_B)
        Me.GroupControl2.Controls.Add(Me.txt_length_Outlook_B)
        Me.GroupControl2.Location = New System.Drawing.Point(19, 121)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.Size = New System.Drawing.Size(816, 401)
        Me.GroupControl2.TabIndex = 577
        Me.GroupControl2.Text = "Settings / Link / Button"
        '
        'Clear2
        '
        Me.Clear2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear2.Appearance.Options.UseBackColor = True
        Me.Clear2.Appearance.Options.UseBorderColor = True
        Me.Clear2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.Clear2.Location = New System.Drawing.Point(181, 341)
        Me.Clear2.Name = "Clear2"
        Me.Clear2.Size = New System.Drawing.Size(25, 23)
        Me.Clear2.TabIndex = 594
        '
        'Clear1
        '
        Me.Clear1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear1.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear1.Appearance.Options.UseBackColor = True
        Me.Clear1.Appearance.Options.UseBorderColor = True
        Me.Clear1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.Clear1.Location = New System.Drawing.Point(182, 285)
        Me.Clear1.Name = "Clear1"
        Me.Clear1.Size = New System.Drawing.Size(25, 23)
        Me.Clear1.TabIndex = 595
        '
        'SimpleButton3
        '
        Me.SimpleButton3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton3.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton3.Appearance.Options.UseBackColor = True
        Me.SimpleButton3.Appearance.Options.UseBorderColor = True
        Me.SimpleButton3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.SimpleButton3.Location = New System.Drawing.Point(767, 142)
        Me.SimpleButton3.Name = "SimpleButton3"
        Me.SimpleButton3.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton3.TabIndex = 592
        '
        'SimpleButton4
        '
        Me.SimpleButton4.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton4.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton4.Appearance.Options.UseBackColor = True
        Me.SimpleButton4.Appearance.Options.UseBorderColor = True
        Me.SimpleButton4.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.SimpleButton4.Location = New System.Drawing.Point(767, 69)
        Me.SimpleButton4.Name = "SimpleButton4"
        Me.SimpleButton4.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton4.TabIndex = 593
        '
        'SimpleButton5
        '
        Me.SimpleButton5.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton5.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton5.Appearance.Options.UseBackColor = True
        Me.SimpleButton5.Appearance.Options.UseBorderColor = True
        Me.SimpleButton5.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.SimpleButton5.Location = New System.Drawing.Point(767, 220)
        Me.SimpleButton5.Name = "SimpleButton5"
        Me.SimpleButton5.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton5.TabIndex = 591
        '
        'txt_Button
        '
        Me.txt_Button.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Button.EditValue = ""
        Me.txt_Button.Location = New System.Drawing.Point(6, 215)
        Me.txt_Button.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Button.Name = "txt_Button"
        Me.txt_Button.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Button.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Button.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Button.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Button.Properties.Appearance.Options.UseFont = True
        Me.txt_Button.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Button.Properties.NullValuePrompt = "Continue with your account"
        Me.txt_Button.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Button.Size = New System.Drawing.Size(786, 30)
        Me.txt_Button.TabIndex = 588
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label11.Location = New System.Drawing.Point(6, 189)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(162, 21)
        Me.Label11.TabIndex = 585
        Me.Label11.Text = " Text inside the Button"
        '
        'txt_Logo_Button
        '
        Me.txt_Logo_Button.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Logo_Button.EditValue = ""
        Me.txt_Logo_Button.Location = New System.Drawing.Point(6, 139)
        Me.txt_Logo_Button.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Logo_Button.Name = "txt_Logo_Button"
        Me.txt_Logo_Button.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Logo_Button.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Logo_Button.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Logo_Button.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Logo_Button.Properties.Appearance.Options.UseFont = True
        Me.txt_Logo_Button.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Logo_Button.Properties.NullValuePrompt = "Enter Link Logo Button"
        Me.txt_Logo_Button.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Logo_Button.Size = New System.Drawing.Size(786, 30)
        Me.txt_Logo_Button.TabIndex = 589
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label10.Location = New System.Drawing.Point(6, 113)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(168, 21)
        Me.Label10.TabIndex = 586
        Me.Label10.Text = "Enter Link Logo Button"
        '
        'txt_Logo_Page
        '
        Me.txt_Logo_Page.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Logo_Page.EditValue = ""
        Me.txt_Logo_Page.Location = New System.Drawing.Point(6, 64)
        Me.txt_Logo_Page.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Logo_Page.Name = "txt_Logo_Page"
        Me.txt_Logo_Page.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Logo_Page.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Logo_Page.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Logo_Page.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Logo_Page.Properties.Appearance.Options.UseFont = True
        Me.txt_Logo_Page.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Logo_Page.Properties.NullValuePrompt = "Enter Link Logo Page"
        Me.txt_Logo_Page.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Logo_Page.Size = New System.Drawing.Size(786, 30)
        Me.txt_Logo_Page.TabIndex = 590
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label9.Location = New System.Drawing.Point(6, 38)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(156, 21)
        Me.Label9.TabIndex = 587
        Me.Label9.Text = "Enter Link Logo Page"
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label4.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label4.Location = New System.Drawing.Point(78, 259)
        Me.Label4.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(91, 21)
        Me.Label4.TabIndex = 584
        Me.Label4.Text = "Height logo"
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label5.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label5.Location = New System.Drawing.Point(79, 314)
        Me.Label5.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(88, 21)
        Me.Label5.TabIndex = 583
        Me.Label5.Text = "Width Logo"
        '
        'Pic_L_B
        '
        Me.Pic_L_B.Location = New System.Drawing.Point(445, 252)
        Me.Pic_L_B.Margin = New System.Windows.Forms.Padding(4)
        Me.Pic_L_B.Name = "Pic_L_B"
        Me.Pic_L_B.Size = New System.Drawing.Size(162, 108)
        Me.Pic_L_B.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.Pic_L_B.TabIndex = 579
        Me.Pic_L_B.TabStop = False
        '
        'lbl_S_B
        '
        Me.lbl_S_B.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.lbl_S_B.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(2, Byte), Integer))
        Me.lbl_S_B.Location = New System.Drawing.Point(287, 364)
        Me.lbl_S_B.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbl_S_B.Name = "lbl_S_B"
        Me.lbl_S_B.Size = New System.Drawing.Size(478, 25)
        Me.lbl_S_B.TabIndex = 580
        Me.lbl_S_B.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'txt_width_Outlook_B
        '
        Me.txt_width_Outlook_B.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_width_Outlook_B.EditValue = ""
        Me.txt_width_Outlook_B.Location = New System.Drawing.Point(40, 337)
        Me.txt_width_Outlook_B.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_width_Outlook_B.Name = "txt_width_Outlook_B"
        Me.txt_width_Outlook_B.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_width_Outlook_B.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_width_Outlook_B.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_width_Outlook_B.Properties.Appearance.Options.UseBackColor = True
        Me.txt_width_Outlook_B.Properties.Appearance.Options.UseFont = True
        Me.txt_width_Outlook_B.Properties.Appearance.Options.UseForeColor = True
        Me.txt_width_Outlook_B.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_width_Outlook_B.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_width_Outlook_B.Properties.NullValuePrompt = "Width Logo"
        Me.txt_width_Outlook_B.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_width_Outlook_B.Size = New System.Drawing.Size(167, 30)
        Me.txt_width_Outlook_B.TabIndex = 578
        '
        'txt_length_Outlook_B
        '
        Me.txt_length_Outlook_B.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_length_Outlook_B.EditValue = ""
        Me.txt_length_Outlook_B.Location = New System.Drawing.Point(40, 282)
        Me.txt_length_Outlook_B.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_length_Outlook_B.Name = "txt_length_Outlook_B"
        Me.txt_length_Outlook_B.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_length_Outlook_B.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_length_Outlook_B.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_length_Outlook_B.Properties.Appearance.Options.UseBackColor = True
        Me.txt_length_Outlook_B.Properties.Appearance.Options.UseFont = True
        Me.txt_length_Outlook_B.Properties.Appearance.Options.UseForeColor = True
        Me.txt_length_Outlook_B.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_length_Outlook_B.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_length_Outlook_B.Properties.NullValuePrompt = "Height logo"
        Me.txt_length_Outlook_B.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_length_Outlook_B.Size = New System.Drawing.Size(167, 30)
        Me.txt_length_Outlook_B.TabIndex = 578
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.lbl_Size_Logo_Page)
        Me.GroupControl1.Controls.Add(Me.Pic_Logo_Page)
        Me.GroupControl1.Controls.Add(Me.txt_width_Outlook)
        Me.GroupControl1.Controls.Add(Me.txt_length_Outlook)
        Me.GroupControl1.Location = New System.Drawing.Point(842, 121)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(314, 401)
        Me.GroupControl1.TabIndex = 576
        Me.GroupControl1.Text = " Page Logo Size Settings"
        '
        'lbl_Size_Logo_Page
        '
        Me.lbl_Size_Logo_Page.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.lbl_Size_Logo_Page.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(2, Byte), Integer))
        Me.lbl_Size_Logo_Page.Location = New System.Drawing.Point(6, 362)
        Me.lbl_Size_Logo_Page.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbl_Size_Logo_Page.Name = "lbl_Size_Logo_Page"
        Me.lbl_Size_Logo_Page.Size = New System.Drawing.Size(296, 25)
        Me.lbl_Size_Logo_Page.TabIndex = 580
        Me.lbl_Size_Logo_Page.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Pic_Logo_Page
        '
        Me.Pic_Logo_Page.Location = New System.Drawing.Point(26, 162)
        Me.Pic_Logo_Page.Margin = New System.Windows.Forms.Padding(4)
        Me.Pic_Logo_Page.Name = "Pic_Logo_Page"
        Me.Pic_Logo_Page.Size = New System.Drawing.Size(240, 187)
        Me.Pic_Logo_Page.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.Pic_Logo_Page.TabIndex = 579
        Me.Pic_Logo_Page.TabStop = False
        '
        'txt_width_Outlook
        '
        Me.txt_width_Outlook.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_width_Outlook.EditValue = ""
        Me.txt_width_Outlook.Location = New System.Drawing.Point(68, 120)
        Me.txt_width_Outlook.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_width_Outlook.Name = "txt_width_Outlook"
        Me.txt_width_Outlook.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_width_Outlook.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_width_Outlook.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_width_Outlook.Properties.Appearance.Options.UseBackColor = True
        Me.txt_width_Outlook.Properties.Appearance.Options.UseFont = True
        Me.txt_width_Outlook.Properties.Appearance.Options.UseForeColor = True
        Me.txt_width_Outlook.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_width_Outlook.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_width_Outlook.Properties.NullValuePrompt = "Width Logo"
        Me.txt_width_Outlook.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_width_Outlook.Size = New System.Drawing.Size(167, 30)
        Me.txt_width_Outlook.TabIndex = 578
        '
        'txt_length_Outlook
        '
        Me.txt_length_Outlook.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_length_Outlook.EditValue = ""
        Me.txt_length_Outlook.Location = New System.Drawing.Point(68, 66)
        Me.txt_length_Outlook.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_length_Outlook.Name = "txt_length_Outlook"
        Me.txt_length_Outlook.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_length_Outlook.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_length_Outlook.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_length_Outlook.Properties.Appearance.Options.UseBackColor = True
        Me.txt_length_Outlook.Properties.Appearance.Options.UseFont = True
        Me.txt_length_Outlook.Properties.Appearance.Options.UseForeColor = True
        Me.txt_length_Outlook.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_length_Outlook.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_length_Outlook.Properties.NullValuePrompt = "Height logo"
        Me.txt_length_Outlook.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_length_Outlook.Size = New System.Drawing.Size(167, 30)
        Me.txt_length_Outlook.TabIndex = 578
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton1.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Options.UseBackColor = True
        Me.SimpleButton1.Appearance.Options.UseBorderColor = True
        Me.SimpleButton1.Appearance.Options.UseFont = True
        Me.SimpleButton1.Appearance.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton1.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton1.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton1.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton1.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton1.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.SimpleButton1.Location = New System.Drawing.Point(910, 60)
        Me.SimpleButton1.Margin = New System.Windows.Forms.Padding(4)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(152, 38)
        Me.SimpleButton1.TabIndex = 575
        Me.SimpleButton1.Text = "I want a Sample"
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.AntiBotRedirectPaner
        Me.PictureBox1.Location = New System.Drawing.Point(223, 20)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(561, 78)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 582
        Me.PictureBox1.TabStop = False
        '
        'XtraTabPage2
        '
        Me.XtraTabPage2.Appearance.Header.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.XtraTabPage2.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage2.Controls.Add(Me.GroupControl4)
        Me.XtraTabPage2.Controls.Add(Me.GroupControl3)
        Me.XtraTabPage2.Controls.Add(Me.PictureBox4)
        Me.XtraTabPage2.ImageOptions.Image = CType(resources.GetObject("XtraTabPage2.ImageOptions.Image"), System.Drawing.Image)
        Me.XtraTabPage2.Name = "XtraTabPage2"
        Me.XtraTabPage2.Size = New System.Drawing.Size(1197, 530)
        Me.XtraTabPage2.Text = "Example : Logo"
        '
        'GroupControl4
        '
        Me.GroupControl4.Controls.Add(Me.lbl_Logo_Example)
        Me.GroupControl4.Controls.Add(Me.BntClearLink)
        Me.GroupControl4.Controls.Add(Me.txt_Link)
        Me.GroupControl4.Controls.Add(Me.bntSendLogo)
        Me.GroupControl4.Controls.Add(Me.PictureBoxSee)
        Me.GroupControl4.Location = New System.Drawing.Point(3, 104)
        Me.GroupControl4.Name = "GroupControl4"
        Me.GroupControl4.Size = New System.Drawing.Size(851, 407)
        Me.GroupControl4.TabIndex = 578
        Me.GroupControl4.Text = "Preview"
        '
        'lbl_Logo_Example
        '
        Me.lbl_Logo_Example.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.lbl_Logo_Example.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(2, Byte), Integer))
        Me.lbl_Logo_Example.Location = New System.Drawing.Point(6, 366)
        Me.lbl_Logo_Example.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbl_Logo_Example.Name = "lbl_Logo_Example"
        Me.lbl_Logo_Example.Size = New System.Drawing.Size(825, 25)
        Me.lbl_Logo_Example.TabIndex = 583
        Me.lbl_Logo_Example.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'BntClearLink
        '
        Me.BntClearLink.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.BntClearLink.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.BntClearLink.Appearance.Options.UseBackColor = True
        Me.BntClearLink.Appearance.Options.UseBorderColor = True
        Me.BntClearLink.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.BntClearLink.Location = New System.Drawing.Point(806, 41)
        Me.BntClearLink.Name = "BntClearLink"
        Me.BntClearLink.Size = New System.Drawing.Size(25, 23)
        Me.BntClearLink.TabIndex = 582
        '
        'txt_Link
        '
        Me.txt_Link.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Link.EditValue = ""
        Me.txt_Link.Location = New System.Drawing.Point(167, 38)
        Me.txt_Link.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Link.Name = "txt_Link"
        Me.txt_Link.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Link.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Link.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Link.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Link.Properties.Appearance.Options.UseFont = True
        Me.txt_Link.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Link.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Link.Size = New System.Drawing.Size(666, 30)
        Me.txt_Link.TabIndex = 581
        '
        'bntSendLogo
        '
        Me.bntSendLogo.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSendLogo.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSendLogo.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntSendLogo.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSendLogo.Appearance.Options.UseBackColor = True
        Me.bntSendLogo.Appearance.Options.UseBorderColor = True
        Me.bntSendLogo.Appearance.Options.UseFont = True
        Me.bntSendLogo.Appearance.Options.UseForeColor = True
        Me.bntSendLogo.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.bntSendLogo.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.bntSendLogo.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.bntSendLogo.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.bntSendLogo.AppearanceDisabled.Options.UseBackColor = True
        Me.bntSendLogo.AppearanceDisabled.Options.UseBorderColor = True
        Me.bntSendLogo.AppearanceDisabled.Options.UseFont = True
        Me.bntSendLogo.AppearanceDisabled.Options.UseForeColor = True
        Me.bntSendLogo.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.bntSendLogo.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSendLogo.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.bntSendLogo.AppearanceHovered.Options.UseBackColor = True
        Me.bntSendLogo.AppearanceHovered.Options.UseBorderColor = True
        Me.bntSendLogo.AppearanceHovered.Options.UseForeColor = True
        Me.bntSendLogo.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.bntSendLogo.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.bntSendLogo.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.bntSendLogo.AppearancePressed.Options.UseBackColor = True
        Me.bntSendLogo.AppearancePressed.Options.UseBorderColor = True
        Me.bntSendLogo.AppearancePressed.Options.UseForeColor = True
        Me.bntSendLogo.ImageOptions.Image = CType(resources.GetObject("bntSendLogo.ImageOptions.Image"), System.Drawing.Image)
        Me.bntSendLogo.Location = New System.Drawing.Point(7, 33)
        Me.bntSendLogo.Margin = New System.Windows.Forms.Padding(4)
        Me.bntSendLogo.Name = "bntSendLogo"
        Me.bntSendLogo.Size = New System.Drawing.Size(152, 38)
        Me.bntSendLogo.TabIndex = 580
        Me.bntSendLogo.Text = "Send"
        '
        'PictureBoxSee
        '
        Me.PictureBoxSee.Location = New System.Drawing.Point(7, 78)
        Me.PictureBoxSee.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureBoxSee.Name = "PictureBoxSee"
        Me.PictureBoxSee.Size = New System.Drawing.Size(826, 280)
        Me.PictureBoxSee.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBoxSee.TabIndex = 579
        Me.PictureBoxSee.TabStop = False
        '
        'GroupControl3
        '
        Me.GroupControl3.Controls.Add(Me.Label6)
        Me.GroupControl3.Controls.Add(Me.Clear4)
        Me.GroupControl3.Controls.Add(Me.Clear3)
        Me.GroupControl3.Controls.Add(Me.Label1)
        Me.GroupControl3.Controls.Add(Me.Label3)
        Me.GroupControl3.Controls.Add(Me.txt_width_Example)
        Me.GroupControl3.Controls.Add(Me.ComboBoxEdit1)
        Me.GroupControl3.Controls.Add(Me.txt_length_Example)
        Me.GroupControl3.Controls.Add(Me.txt_Link_Icon)
        Me.GroupControl3.Controls.Add(Me.Label13)
        Me.GroupControl3.Location = New System.Drawing.Point(857, 104)
        Me.GroupControl3.Name = "GroupControl3"
        Me.GroupControl3.Size = New System.Drawing.Size(314, 408)
        Me.GroupControl3.TabIndex = 577
        Me.GroupControl3.Text = " Page Logo Size Settings"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Comfortaa", 14.0!, System.Drawing.FontStyle.Bold)
        Me.Label6.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.Label6.Image = Global.Best_Sender.My.Resources.Resources.Valid32x32
        Me.Label6.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Label6.Location = New System.Drawing.Point(90, 328)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(135, 30)
        Me.Label6.TabIndex = 594
        Me.Label6.Text = "Success       "
        Me.Label6.Visible = False
        '
        'Clear4
        '
        Me.Clear4.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear4.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear4.Appearance.Options.UseBackColor = True
        Me.Clear4.Appearance.Options.UseBorderColor = True
        Me.Clear4.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.Clear4.Location = New System.Drawing.Point(210, 274)
        Me.Clear4.Name = "Clear4"
        Me.Clear4.Size = New System.Drawing.Size(25, 23)
        Me.Clear4.TabIndex = 583
        '
        'Clear3
        '
        Me.Clear3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear3.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear3.Appearance.Options.UseBackColor = True
        Me.Clear3.Appearance.Options.UseBorderColor = True
        Me.Clear3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.Clear3.Location = New System.Drawing.Point(210, 217)
        Me.Clear3.Name = "Clear3"
        Me.Clear3.Size = New System.Drawing.Size(25, 23)
        Me.Clear3.TabIndex = 584
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label1.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label1.Location = New System.Drawing.Point(107, 190)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(91, 21)
        Me.Label1.TabIndex = 582
        Me.Label1.Text = "Height logo"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label3.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label3.Location = New System.Drawing.Point(108, 247)
        Me.Label3.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(88, 21)
        Me.Label3.TabIndex = 581
        Me.Label3.Text = "Width Logo"
        '
        'txt_width_Example
        '
        Me.txt_width_Example.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_width_Example.EditValue = ""
        Me.txt_width_Example.Location = New System.Drawing.Point(69, 271)
        Me.txt_width_Example.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_width_Example.Name = "txt_width_Example"
        Me.txt_width_Example.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_width_Example.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_width_Example.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_width_Example.Properties.Appearance.Options.UseBackColor = True
        Me.txt_width_Example.Properties.Appearance.Options.UseFont = True
        Me.txt_width_Example.Properties.Appearance.Options.UseForeColor = True
        Me.txt_width_Example.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_width_Example.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_width_Example.Properties.NullValuePrompt = "Width Logo"
        Me.txt_width_Example.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_width_Example.Size = New System.Drawing.Size(167, 30)
        Me.txt_width_Example.TabIndex = 579
        '
        'ComboBoxEdit1
        '
        Me.ComboBoxEdit1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.ComboBoxEdit1.EditValue = ""
        Me.ComboBoxEdit1.Location = New System.Drawing.Point(18, 135)
        Me.ComboBoxEdit1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ComboBoxEdit1.Name = "ComboBoxEdit1"
        Me.ComboBoxEdit1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.ComboBoxEdit1.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.ComboBoxEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.ComboBoxEdit1.Properties.Appearance.Options.UseFont = True
        Me.ComboBoxEdit1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.ComboBoxEdit1.Properties.Items.AddRange(New Object() {"Alibaba Group", "Aliexpress", "Amazon", "Amazon Aws", "Amazon Pay", "American Express", "Aol", "Apple", "Bank of China", "Binance", "Bitcoin", "Blockchain", "BoA", "Cashapp", "Chase", "Coinbase", "Discord", "Docusign", "eBay", "Ethereum", "Facebook", "Gmail", "HSBC", "Instagram", "KeyBank", "LINE", "LinkedIn", "Mastercard", "Meta", "Microsoft", "Microsoft Wallet", "National Bank of Canada", "Netflix", "Outlook", "Payme", "Payoneer", "Paypal", "Paytm", "Skype", "Square", "Telegram", "TencentQQ", "Union Bank", "Viber", "Virgin Money UK", "WeChat", "Western Union", "WorldPay", "Yahoo", "Zelle"})
        Me.ComboBoxEdit1.Size = New System.Drawing.Size(265, 38)
        Me.ComboBoxEdit1.TabIndex = 578
        '
        'txt_length_Example
        '
        Me.txt_length_Example.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_length_Example.EditValue = ""
        Me.txt_length_Example.Location = New System.Drawing.Point(69, 214)
        Me.txt_length_Example.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_length_Example.Name = "txt_length_Example"
        Me.txt_length_Example.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_length_Example.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_length_Example.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_length_Example.Properties.Appearance.Options.UseBackColor = True
        Me.txt_length_Example.Properties.Appearance.Options.UseFont = True
        Me.txt_length_Example.Properties.Appearance.Options.UseForeColor = True
        Me.txt_length_Example.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_length_Example.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_length_Example.Properties.NullValuePrompt = "Height logo"
        Me.txt_length_Example.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_length_Example.Size = New System.Drawing.Size(167, 30)
        Me.txt_length_Example.TabIndex = 580
        '
        'txt_Link_Icon
        '
        Me.txt_Link_Icon.EditValue = ""
        Me.txt_Link_Icon.Location = New System.Drawing.Point(240, 36)
        Me.txt_Link_Icon.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Link_Icon.Name = "txt_Link_Icon"
        Me.txt_Link_Icon.Properties.AllowFocused = False
        Me.txt_Link_Icon.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.txt_Link_Icon.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.txt_Link_Icon.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.txt_Link_Icon.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Link_Icon.Properties.Appearance.Options.UseFont = True
        Me.txt_Link_Icon.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Link_Icon.Properties.AppearanceFocused.ForeColor = System.Drawing.Color.White
        Me.txt_Link_Icon.Properties.AppearanceFocused.Options.UseForeColor = True
        Me.txt_Link_Icon.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.txt_Link_Icon.Properties.LookAndFeel.SkinName = "Sharp"
        Me.txt_Link_Icon.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.txt_Link_Icon.Size = New System.Drawing.Size(62, 22)
        Me.txt_Link_Icon.TabIndex = 578
        Me.txt_Link_Icon.Visible = False
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label13.Location = New System.Drawing.Point(107, 88)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(91, 21)
        Me.Label13.TabIndex = 578
        Me.Label13.Text = "Select Logo"
        '
        'PictureBox4
        '
        Me.PictureBox4.Image = Global.Best_Sender.My.Resources.Resources.AntiBotRedirectPaner
        Me.PictureBox4.Location = New System.Drawing.Point(223, 20)
        Me.PictureBox4.Name = "PictureBox4"
        Me.PictureBox4.Size = New System.Drawing.Size(561, 78)
        Me.PictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox4.TabIndex = 583
        Me.PictureBox4.TabStop = False
        '
        'XtraTabPage3
        '
        Me.XtraTabPage3.Appearance.Header.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.XtraTabPage3.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage3.Controls.Add(Me.GroupControl5)
        Me.XtraTabPage3.Controls.Add(Me.GroupControl6)
        Me.XtraTabPage3.Controls.Add(Me.RichTextBox1)
        Me.XtraTabPage3.Controls.Add(Me.PictureBox5)
        Me.XtraTabPage3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.LinkDomain32x32
        Me.XtraTabPage3.Name = "XtraTabPage3"
        Me.XtraTabPage3.Size = New System.Drawing.Size(1197, 530)
        Me.XtraTabPage3.Text = "URL Settings"
        '
        'GroupControl5
        '
        Me.GroupControl5.Controls.Add(Me.SimpleButton2)
        Me.GroupControl5.Controls.Add(Me.SimpleButton6)
        Me.GroupControl5.Controls.Add(Me.SimpleButton7)
        Me.GroupControl5.Controls.Add(Me.txt_Type)
        Me.GroupControl5.Controls.Add(Me.Label2)
        Me.GroupControl5.Controls.Add(Me.Label15)
        Me.GroupControl5.Controls.Add(Me.txt_outlook)
        Me.GroupControl5.Controls.Add(Me.Label16)
        Me.GroupControl5.Controls.Add(Me.txt_Link_New)
        Me.GroupControl5.Controls.Add(Me.Label17)
        Me.GroupControl5.Controls.Add(Me.SeparatorControl1)
        Me.GroupControl5.Controls.Add(Me.BTNSAVEALL)
        Me.GroupControl5.Controls.Add(Me.Bnt_Reset_All)
        Me.GroupControl5.Controls.Add(Me.btnBuild)
        Me.GroupControl5.Location = New System.Drawing.Point(12, 90)
        Me.GroupControl5.Name = "GroupControl5"
        Me.GroupControl5.Size = New System.Drawing.Size(850, 420)
        Me.GroupControl5.TabIndex = 589
        Me.GroupControl5.Text = "Build"
        '
        'SimpleButton2
        '
        Me.SimpleButton2.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton2.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton2.Appearance.Options.UseBackColor = True
        Me.SimpleButton2.Appearance.Options.UseBorderColor = True
        Me.SimpleButton2.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.SimpleButton2.Location = New System.Drawing.Point(802, 143)
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton2.TabIndex = 600
        '
        'SimpleButton6
        '
        Me.SimpleButton6.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton6.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton6.Appearance.Options.UseBackColor = True
        Me.SimpleButton6.Appearance.Options.UseBorderColor = True
        Me.SimpleButton6.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.SimpleButton6.Location = New System.Drawing.Point(802, 68)
        Me.SimpleButton6.Name = "SimpleButton6"
        Me.SimpleButton6.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton6.TabIndex = 601
        '
        'SimpleButton7
        '
        Me.SimpleButton7.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton7.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton7.Appearance.Options.UseBackColor = True
        Me.SimpleButton7.Appearance.Options.UseBorderColor = True
        Me.SimpleButton7.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.SimpleButton7.Location = New System.Drawing.Point(802, 219)
        Me.SimpleButton7.Name = "SimpleButton7"
        Me.SimpleButton7.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton7.TabIndex = 599
        '
        'txt_Type
        '
        Me.txt_Type.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Type.EditValue = ""
        Me.txt_Type.Location = New System.Drawing.Point(6, 216)
        Me.txt_Type.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Type.Name = "txt_Type"
        Me.txt_Type.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Type.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Type.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Type.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Type.Properties.Appearance.Options.UseFont = True
        Me.txt_Type.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Type.Properties.NullValuePrompt = "Sign in to your Microsoft account to continue "
        Me.txt_Type.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Type.Size = New System.Drawing.Size(822, 30)
        Me.txt_Type.TabIndex = 596
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 14.0!, System.Drawing.FontStyle.Bold)
        Me.Label2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.Label2.Image = Global.Best_Sender.My.Resources.Resources.Valid32x32
        Me.Label2.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Label2.Location = New System.Drawing.Point(381, 269)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(135, 30)
        Me.Label2.TabIndex = 593
        Me.Label2.Text = "Success       "
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label15.Location = New System.Drawing.Point(6, 190)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(300, 21)
        Me.Label15.TabIndex = 593
        Me.Label15.Text = "Enter the text that will Appear on the Page"
        '
        'txt_outlook
        '
        Me.txt_outlook.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_outlook.EditValue = ""
        Me.txt_outlook.Location = New System.Drawing.Point(6, 140)
        Me.txt_outlook.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_outlook.Name = "txt_outlook"
        Me.txt_outlook.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_outlook.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_outlook.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_outlook.Properties.Appearance.Options.UseBackColor = True
        Me.txt_outlook.Properties.Appearance.Options.UseFont = True
        Me.txt_outlook.Properties.Appearance.Options.UseForeColor = True
        Me.txt_outlook.Properties.NullValuePrompt = "Enter a Disguise link Example: https://www.office.com"
        Me.txt_outlook.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_outlook.Size = New System.Drawing.Size(822, 30)
        Me.txt_outlook.TabIndex = 597
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label16.Location = New System.Drawing.Point(6, 114)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(150, 21)
        Me.Label16.TabIndex = 594
        Me.Label16.Text = "Enter a Disguise link"
        '
        'txt_Link_New
        '
        Me.txt_Link_New.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Link_New.EditValue = ""
        Me.txt_Link_New.Location = New System.Drawing.Point(6, 65)
        Me.txt_Link_New.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Link_New.Name = "txt_Link_New"
        Me.txt_Link_New.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Link_New.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Link_New.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Link_New.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Link_New.Properties.Appearance.Options.UseFont = True
        Me.txt_Link_New.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Link_New.Properties.NullValuePrompt = "Enter Your Page Link"
        Me.txt_Link_New.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Link_New.Size = New System.Drawing.Size(822, 30)
        Me.txt_Link_New.TabIndex = 598
        '
        'Label17
        '
        Me.Label17.AutoSize = True
        Me.Label17.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label17.Location = New System.Drawing.Point(6, 39)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(152, 21)
        Me.Label17.TabIndex = 595
        Me.Label17.Text = "Enter Your Page Link"
        '
        'SeparatorControl1
        '
        Me.SeparatorControl1.LineThickness = 1
        Me.SeparatorControl1.Location = New System.Drawing.Point(254, 380)
        Me.SeparatorControl1.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl1.Name = "SeparatorControl1"
        Me.SeparatorControl1.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl1.Size = New System.Drawing.Size(363, 26)
        Me.SeparatorControl1.TabIndex = 572
        '
        'BTNSAVEALL
        '
        Me.BTNSAVEALL.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTNSAVEALL.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTNSAVEALL.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTNSAVEALL.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTNSAVEALL.Appearance.Options.UseBackColor = True
        Me.BTNSAVEALL.Appearance.Options.UseBorderColor = True
        Me.BTNSAVEALL.Appearance.Options.UseFont = True
        Me.BTNSAVEALL.Appearance.Options.UseForeColor = True
        Me.BTNSAVEALL.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BTNSAVEALL.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BTNSAVEALL.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTNSAVEALL.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BTNSAVEALL.AppearanceDisabled.Options.UseBackColor = True
        Me.BTNSAVEALL.AppearanceDisabled.Options.UseBorderColor = True
        Me.BTNSAVEALL.AppearanceDisabled.Options.UseFont = True
        Me.BTNSAVEALL.AppearanceDisabled.Options.UseForeColor = True
        Me.BTNSAVEALL.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BTNSAVEALL.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTNSAVEALL.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BTNSAVEALL.AppearanceHovered.Options.UseBackColor = True
        Me.BTNSAVEALL.AppearanceHovered.Options.UseBorderColor = True
        Me.BTNSAVEALL.AppearanceHovered.Options.UseForeColor = True
        Me.BTNSAVEALL.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTNSAVEALL.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTNSAVEALL.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BTNSAVEALL.AppearancePressed.Options.UseBackColor = True
        Me.BTNSAVEALL.AppearancePressed.Options.UseBorderColor = True
        Me.BTNSAVEALL.AppearancePressed.Options.UseForeColor = True
        Me.BTNSAVEALL.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.settings32x32
        Me.BTNSAVEALL.Location = New System.Drawing.Point(364, 319)
        Me.BTNSAVEALL.Margin = New System.Windows.Forms.Padding(4)
        Me.BTNSAVEALL.Name = "BTNSAVEALL"
        Me.BTNSAVEALL.Size = New System.Drawing.Size(152, 38)
        Me.BTNSAVEALL.TabIndex = 571
        Me.BTNSAVEALL.Text = "Save Settings"
        '
        'Bnt_Reset_All
        '
        Me.Bnt_Reset_All.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Bnt_Reset_All.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Reset_All.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.Bnt_Reset_All.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Reset_All.Appearance.Options.UseBackColor = True
        Me.Bnt_Reset_All.Appearance.Options.UseBorderColor = True
        Me.Bnt_Reset_All.Appearance.Options.UseFont = True
        Me.Bnt_Reset_All.Appearance.Options.UseForeColor = True
        Me.Bnt_Reset_All.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.Bnt_Reset_All.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.Bnt_Reset_All.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.Bnt_Reset_All.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Bnt_Reset_All.AppearanceDisabled.Options.UseBackColor = True
        Me.Bnt_Reset_All.AppearanceDisabled.Options.UseBorderColor = True
        Me.Bnt_Reset_All.AppearanceDisabled.Options.UseFont = True
        Me.Bnt_Reset_All.AppearanceDisabled.Options.UseForeColor = True
        Me.Bnt_Reset_All.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.Bnt_Reset_All.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Bnt_Reset_All.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.Bnt_Reset_All.AppearanceHovered.Options.UseBackColor = True
        Me.Bnt_Reset_All.AppearanceHovered.Options.UseBorderColor = True
        Me.Bnt_Reset_All.AppearanceHovered.Options.UseForeColor = True
        Me.Bnt_Reset_All.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Bnt_Reset_All.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.Bnt_Reset_All.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.Bnt_Reset_All.AppearancePressed.Options.UseBackColor = True
        Me.Bnt_Reset_All.AppearancePressed.Options.UseBorderColor = True
        Me.Bnt_Reset_All.AppearancePressed.Options.UseForeColor = True
        Me.Bnt_Reset_All.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.Bnt_Reset_All.Location = New System.Drawing.Point(524, 319)
        Me.Bnt_Reset_All.Margin = New System.Windows.Forms.Padding(4)
        Me.Bnt_Reset_All.Name = "Bnt_Reset_All"
        Me.Bnt_Reset_All.Size = New System.Drawing.Size(152, 38)
        Me.Bnt_Reset_All.TabIndex = 570
        Me.Bnt_Reset_All.Text = "Reset All"
        '
        'btnBuild
        '
        Me.btnBuild.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnBuild.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnBuild.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnBuild.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnBuild.Appearance.Options.UseBackColor = True
        Me.btnBuild.Appearance.Options.UseBorderColor = True
        Me.btnBuild.Appearance.Options.UseFont = True
        Me.btnBuild.Appearance.Options.UseForeColor = True
        Me.btnBuild.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btnBuild.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btnBuild.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnBuild.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnBuild.AppearanceDisabled.Options.UseBackColor = True
        Me.btnBuild.AppearanceDisabled.Options.UseBorderColor = True
        Me.btnBuild.AppearanceDisabled.Options.UseFont = True
        Me.btnBuild.AppearanceDisabled.Options.UseForeColor = True
        Me.btnBuild.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btnBuild.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnBuild.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btnBuild.AppearanceHovered.Options.UseBackColor = True
        Me.btnBuild.AppearanceHovered.Options.UseBorderColor = True
        Me.btnBuild.AppearanceHovered.Options.UseForeColor = True
        Me.btnBuild.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnBuild.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnBuild.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btnBuild.AppearancePressed.Options.UseBackColor = True
        Me.btnBuild.AppearancePressed.Options.UseBorderColor = True
        Me.btnBuild.AppearancePressed.Options.UseForeColor = True
        Me.btnBuild.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.btnBuild.Location = New System.Drawing.Point(204, 319)
        Me.btnBuild.Margin = New System.Windows.Forms.Padding(4)
        Me.btnBuild.Name = "btnBuild"
        Me.btnBuild.Size = New System.Drawing.Size(152, 38)
        Me.btnBuild.TabIndex = 569
        Me.btnBuild.Text = "Encrypter"
        '
        'GroupControl6
        '
        Me.GroupControl6.Controls.Add(Me.cb_txt_conversion)
        Me.GroupControl6.Controls.Add(Me.cb_txt_Time)
        Me.GroupControl6.Controls.Add(Me.Label14)
        Me.GroupControl6.Controls.Add(Me.PictureBox2)
        Me.GroupControl6.Controls.Add(Me.Label19)
        Me.GroupControl6.Controls.Add(Me.Label18)
        Me.GroupControl6.Location = New System.Drawing.Point(868, 87)
        Me.GroupControl6.Name = "GroupControl6"
        Me.GroupControl6.Size = New System.Drawing.Size(314, 423)
        Me.GroupControl6.TabIndex = 588
        Me.GroupControl6.Text = " Page Logo Size Settings"
        '
        'cb_txt_conversion
        '
        Me.cb_txt_conversion.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cb_txt_conversion.EditValue = "1"
        Me.cb_txt_conversion.Location = New System.Drawing.Point(68, 161)
        Me.cb_txt_conversion.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cb_txt_conversion.Name = "cb_txt_conversion"
        Me.cb_txt_conversion.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cb_txt_conversion.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cb_txt_conversion.Properties.Appearance.Options.UseBackColor = True
        Me.cb_txt_conversion.Properties.Appearance.Options.UseFont = True
        Me.cb_txt_conversion.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cb_txt_conversion.Properties.ContextImageOptions.Image = CType(resources.GetObject("cb_txt_conversion.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.cb_txt_conversion.Properties.Items.AddRange(New Object() {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"})
        Me.cb_txt_conversion.Size = New System.Drawing.Size(173, 38)
        Me.cb_txt_conversion.TabIndex = 581
        '
        'cb_txt_Time
        '
        Me.cb_txt_Time.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cb_txt_Time.EditValue = "4"
        Me.cb_txt_Time.Location = New System.Drawing.Point(68, 78)
        Me.cb_txt_Time.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cb_txt_Time.Name = "cb_txt_Time"
        Me.cb_txt_Time.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cb_txt_Time.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cb_txt_Time.Properties.Appearance.Options.UseBackColor = True
        Me.cb_txt_Time.Properties.Appearance.Options.UseFont = True
        Me.cb_txt_Time.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cb_txt_Time.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.time16x16
        Me.cb_txt_Time.Properties.Items.AddRange(New Object() {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"})
        Me.cb_txt_Time.Size = New System.Drawing.Size(173, 38)
        Me.cb_txt_Time.TabIndex = 581
        '
        'Label14
        '
        Me.Label14.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.Label14.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(2, Byte), Integer))
        Me.Label14.Location = New System.Drawing.Point(23, 373)
        Me.Label14.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(296, 25)
        Me.Label14.TabIndex = 580
        Me.Label14.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'PictureBox2
        '
        Me.PictureBox2.Location = New System.Drawing.Point(43, 235)
        Me.PictureBox2.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(235, 125)
        Me.PictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox2.TabIndex = 579
        Me.PictureBox2.TabStop = False
        '
        'Label19
        '
        Me.Label19.AutoSize = True
        Me.Label19.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label19.Location = New System.Drawing.Point(68, 128)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(147, 21)
        Me.Label19.TabIndex = 584
        Me.Label19.Text = "Number of Attempts"
        '
        'Label18
        '
        Me.Label18.AutoSize = True
        Me.Label18.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label18.Location = New System.Drawing.Point(68, 45)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(102, 21)
        Me.Label18.TabIndex = 584
        Me.Label18.Text = "Time Redirect"
        '
        'RichTextBox1
        '
        Me.RichTextBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RichTextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox1.Font = New System.Drawing.Font("Comfortaa", 8.2!, System.Drawing.FontStyle.Bold)
        Me.RichTextBox1.ForeColor = System.Drawing.Color.White
        Me.RichTextBox1.Location = New System.Drawing.Point(967, 12)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(66, 59)
        Me.RichTextBox1.TabIndex = 311
        Me.RichTextBox1.Text = resources.GetString("RichTextBox1.Text")
        Me.RichTextBox1.Visible = False
        '
        'PictureBox5
        '
        Me.PictureBox5.Image = Global.Best_Sender.My.Resources.Resources.AntiBotRedirectPaner
        Me.PictureBox5.Location = New System.Drawing.Point(223, 20)
        Me.PictureBox5.Name = "PictureBox5"
        Me.PictureBox5.Size = New System.Drawing.Size(561, 78)
        Me.PictureBox5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox5.TabIndex = 593
        Me.PictureBox5.TabStop = False
        '
        'frmMultiLinker
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1223, 611)
        Me.Controls.Add(Me.XtraTabControl1)
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmMultiLinker"
        Me.ShowInTaskbar = False
        Me.Text = "Redirect Pro Skip / Anti-Bot"
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.XtraTabPage1.ResumeLayout(False)
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        Me.GroupControl2.PerformLayout()
        CType(Me.txt_Button.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_Logo_Button.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_Logo_Page.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Pic_L_B, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_width_Outlook_B.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_length_Outlook_B.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        CType(Me.Pic_Logo_Page, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_width_Outlook.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_length_Outlook.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage2.ResumeLayout(False)
        CType(Me.GroupControl4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl4.ResumeLayout(False)
        CType(Me.txt_Link.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBoxSee, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl3.ResumeLayout(False)
        Me.GroupControl3.PerformLayout()
        CType(Me.txt_width_Example.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ComboBoxEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_length_Example.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_Link_Icon.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage3.ResumeLayout(False)
        CType(Me.GroupControl5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl5.ResumeLayout(False)
        Me.GroupControl5.PerformLayout()
        CType(Me.txt_Type.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_outlook.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_Link_New.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl6.ResumeLayout(False)
        Me.GroupControl6.PerformLayout()
        CType(Me.cb_txt_conversion.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Timer2 As Timer
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents XtraTabPage1 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Pic_L_B As PictureBox
    Friend WithEvents lbl_S_B As Label
    Friend WithEvents txt_width_Outlook_B As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txt_length_Outlook_B As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents lbl_Size_Logo_Page As Label
    Friend WithEvents Pic_Logo_Page As PictureBox
    Friend WithEvents txt_width_Outlook As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txt_length_Outlook As DevExpress.XtraEditors.TextEdit
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents XtraTabPage2 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupControl4 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents lbl_Logo_Example As Label
    Friend WithEvents BntClearLink As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txt_Link As DevExpress.XtraEditors.TextEdit
    Friend WithEvents bntSendLogo As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents PictureBoxSee As PictureBox
    Friend WithEvents GroupControl3 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents txt_width_Example As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ComboBoxEdit1 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents txt_length_Example As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txt_Link_Icon As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label13 As Label
    Friend WithEvents XtraTabPage3 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupControl5 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents SeparatorControl1 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents BTNSAVEALL As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Bnt_Reset_All As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnBuild As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupControl6 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents cb_txt_conversion As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents cb_txt_Time As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents Label19 As Label
    Friend WithEvents Label18 As Label
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents PictureBox4 As PictureBox
    Friend WithEvents PictureBox5 As PictureBox
    Friend WithEvents Label1 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label14 As Label
    Friend WithEvents PictureBox2 As PictureBox
    Friend WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton6 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton7 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txt_Type As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label15 As Label
    Friend WithEvents txt_outlook As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label16 As Label
    Friend WithEvents txt_Link_New As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label17 As Label
    Friend WithEvents SimpleButton3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton4 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton5 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txt_Button As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label11 As Label
    Friend WithEvents txt_Logo_Button As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label10 As Label
    Friend WithEvents txt_Logo_Page As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label9 As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents Label5 As Label
    Friend WithEvents Clear2 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Clear1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Clear4 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Clear3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label2 As Label
    Friend WithEvents Label6 As Label
End Class
