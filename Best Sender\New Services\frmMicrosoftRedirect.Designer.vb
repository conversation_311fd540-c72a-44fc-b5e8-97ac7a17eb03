﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmMicrosoftRedirect
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmMicrosoftRedirect))
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.btn_Save = New DevExpress.XtraEditors.SimpleButton()
        Me.btn_Reset = New DevExpress.XtraEditors.SimpleButton()
        Me.BTN_Redirect_Link_Microsoft = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.Label22 = New System.Windows.Forms.Label()
        Me.Label21 = New System.Windows.Forms.Label()
        Me.cb_txt_Time = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.lbl_Size_Button_Button = New System.Windows.Forms.Label()
        Me.Pic_Logo_Button_Button = New System.Windows.Forms.PictureBox()
        Me.txt_width = New DevExpress.XtraEditors.TextEdit()
        Me.txt_height = New DevExpress.XtraEditors.TextEdit()
        Me.GroupControl2 = New DevExpress.XtraEditors.GroupControl()
        Me.Clear4 = New DevExpress.XtraEditors.SimpleButton()
        Me.Clear3 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton3 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton5 = New DevExpress.XtraEditors.SimpleButton()
        Me.txt_Logo_URL = New DevExpress.XtraEditors.TextEdit()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.txt_type = New DevExpress.XtraEditors.TextEdit()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.txt_Tag = New DevExpress.XtraEditors.TextEdit()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.txt_Link = New DevExpress.XtraEditors.TextEdit()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.SeparatorControl2 = New DevExpress.XtraEditors.SeparatorControl()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Pic_Logo_Button_Button, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_width.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_height.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl2.SuspendLayout()
        CType(Me.txt_Logo_URL.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_type.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_Tag.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_Link.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'RichTextBox1
        '
        Me.RichTextBox1.Location = New System.Drawing.Point(978, 58)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(109, 62)
        Me.RichTextBox1.TabIndex = 505
        Me.RichTextBox1.Text = resources.GetString("RichTextBox1.Text")
        Me.RichTextBox1.Visible = False
        '
        'btn_Save
        '
        Me.btn_Save.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Options.UseBackColor = True
        Me.btn_Save.Appearance.Options.UseBorderColor = True
        Me.btn_Save.Appearance.Options.UseFont = True
        Me.btn_Save.Appearance.Options.UseForeColor = True
        Me.btn_Save.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn_Save.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn_Save.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn_Save.AppearanceDisabled.Options.UseBackColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseFont = True
        Me.btn_Save.AppearanceDisabled.Options.UseForeColor = True
        Me.btn_Save.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn_Save.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn_Save.AppearanceHovered.Options.UseBackColor = True
        Me.btn_Save.AppearanceHovered.Options.UseBorderColor = True
        Me.btn_Save.AppearanceHovered.Options.UseForeColor = True
        Me.btn_Save.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn_Save.AppearancePressed.Options.UseBackColor = True
        Me.btn_Save.AppearancePressed.Options.UseBorderColor = True
        Me.btn_Save.AppearancePressed.Options.UseForeColor = True
        Me.btn_Save.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.settings32x32
        Me.btn_Save.Location = New System.Drawing.Point(234, 353)
        Me.btn_Save.Margin = New System.Windows.Forms.Padding(4)
        Me.btn_Save.Name = "btn_Save"
        Me.btn_Save.Size = New System.Drawing.Size(152, 38)
        Me.btn_Save.TabIndex = 605
        Me.btn_Save.Text = "Save Settings"
        '
        'btn_Reset
        '
        Me.btn_Reset.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Reset.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Reset.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Reset.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Reset.Appearance.Options.UseBackColor = True
        Me.btn_Reset.Appearance.Options.UseBorderColor = True
        Me.btn_Reset.Appearance.Options.UseFont = True
        Me.btn_Reset.Appearance.Options.UseForeColor = True
        Me.btn_Reset.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn_Reset.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn_Reset.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Reset.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn_Reset.AppearanceDisabled.Options.UseBackColor = True
        Me.btn_Reset.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn_Reset.AppearanceDisabled.Options.UseFont = True
        Me.btn_Reset.AppearanceDisabled.Options.UseForeColor = True
        Me.btn_Reset.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn_Reset.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Reset.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn_Reset.AppearanceHovered.Options.UseBackColor = True
        Me.btn_Reset.AppearanceHovered.Options.UseBorderColor = True
        Me.btn_Reset.AppearanceHovered.Options.UseForeColor = True
        Me.btn_Reset.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Reset.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Reset.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn_Reset.AppearancePressed.Options.UseBackColor = True
        Me.btn_Reset.AppearancePressed.Options.UseBorderColor = True
        Me.btn_Reset.AppearancePressed.Options.UseForeColor = True
        Me.btn_Reset.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.btn_Reset.Location = New System.Drawing.Point(409, 353)
        Me.btn_Reset.Margin = New System.Windows.Forms.Padding(4)
        Me.btn_Reset.Name = "btn_Reset"
        Me.btn_Reset.Size = New System.Drawing.Size(152, 38)
        Me.btn_Reset.TabIndex = 604
        Me.btn_Reset.Text = "Reset All"
        '
        'BTN_Redirect_Link_Microsoft
        '
        Me.BTN_Redirect_Link_Microsoft.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_Redirect_Link_Microsoft.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.Appearance.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Microsoft.Appearance.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Microsoft.Appearance.Options.UseFont = True
        Me.BTN_Redirect_Link_Microsoft.Appearance.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.Options.UseFont = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceDisabled.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearanceHovered.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.Options.UseBackColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.Options.UseBorderColor = True
        Me.BTN_Redirect_Link_Microsoft.AppearancePressed.Options.UseForeColor = True
        Me.BTN_Redirect_Link_Microsoft.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.BTN_Redirect_Link_Microsoft.Location = New System.Drawing.Point(59, 353)
        Me.BTN_Redirect_Link_Microsoft.Margin = New System.Windows.Forms.Padding(4)
        Me.BTN_Redirect_Link_Microsoft.Name = "BTN_Redirect_Link_Microsoft"
        Me.BTN_Redirect_Link_Microsoft.Size = New System.Drawing.Size(152, 38)
        Me.BTN_Redirect_Link_Microsoft.TabIndex = 603
        Me.BTN_Redirect_Link_Microsoft.Text = "Start / Build"
        '
        'GroupControl1
        '
        Me.GroupControl1.Controls.Add(Me.Label20)
        Me.GroupControl1.Controls.Add(Me.Label22)
        Me.GroupControl1.Controls.Add(Me.Label21)
        Me.GroupControl1.Controls.Add(Me.cb_txt_Time)
        Me.GroupControl1.Controls.Add(Me.lbl_Size_Button_Button)
        Me.GroupControl1.Controls.Add(Me.Pic_Logo_Button_Button)
        Me.GroupControl1.Controls.Add(Me.txt_width)
        Me.GroupControl1.Controls.Add(Me.txt_height)
        Me.GroupControl1.Location = New System.Drawing.Point(637, 120)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(314, 456)
        Me.GroupControl1.TabIndex = 607
        Me.GroupControl1.Text = "Settings Time / Logo"
        '
        'Label20
        '
        Me.Label20.AutoSize = True
        Me.Label20.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label20.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label20.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label20.Location = New System.Drawing.Point(110, 159)
        Me.Label20.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(88, 21)
        Me.Label20.TabIndex = 583
        Me.Label20.Text = "Width Logo"
        '
        'Label22
        '
        Me.Label22.AutoSize = True
        Me.Label22.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label22.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label22.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label22.Location = New System.Drawing.Point(103, 37)
        Me.Label22.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(102, 21)
        Me.Label22.TabIndex = 584
        Me.Label22.Text = "Time Redirect"
        '
        'Label21
        '
        Me.Label21.AutoSize = True
        Me.Label21.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label21.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label21.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label21.Location = New System.Drawing.Point(109, 102)
        Me.Label21.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(91, 21)
        Me.Label21.TabIndex = 584
        Me.Label21.Text = "Height logo"
        '
        'cb_txt_Time
        '
        Me.cb_txt_Time.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cb_txt_Time.EditValue = "5000"
        Me.cb_txt_Time.Location = New System.Drawing.Point(68, 61)
        Me.cb_txt_Time.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cb_txt_Time.Name = "cb_txt_Time"
        Me.cb_txt_Time.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cb_txt_Time.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cb_txt_Time.Properties.Appearance.Options.UseBackColor = True
        Me.cb_txt_Time.Properties.Appearance.Options.UseFont = True
        Me.cb_txt_Time.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cb_txt_Time.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.time16x16
        Me.cb_txt_Time.Properties.Items.AddRange(New Object() {"1000", "2000", "3000", "4000", "5000", "6000", "7000", "8000", "9000"})
        Me.cb_txt_Time.Size = New System.Drawing.Size(173, 38)
        Me.cb_txt_Time.TabIndex = 582
        '
        'lbl_Size_Button_Button
        '
        Me.lbl_Size_Button_Button.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.lbl_Size_Button_Button.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(2, Byte), Integer))
        Me.lbl_Size_Button_Button.Location = New System.Drawing.Point(6, 421)
        Me.lbl_Size_Button_Button.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbl_Size_Button_Button.Name = "lbl_Size_Button_Button"
        Me.lbl_Size_Button_Button.Size = New System.Drawing.Size(296, 25)
        Me.lbl_Size_Button_Button.TabIndex = 580
        Me.lbl_Size_Button_Button.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Pic_Logo_Button_Button
        '
        Me.Pic_Logo_Button_Button.Location = New System.Drawing.Point(34, 221)
        Me.Pic_Logo_Button_Button.Margin = New System.Windows.Forms.Padding(4)
        Me.Pic_Logo_Button_Button.Name = "Pic_Logo_Button_Button"
        Me.Pic_Logo_Button_Button.Size = New System.Drawing.Size(240, 187)
        Me.Pic_Logo_Button_Button.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.Pic_Logo_Button_Button.TabIndex = 579
        Me.Pic_Logo_Button_Button.TabStop = False
        '
        'txt_width
        '
        Me.txt_width.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_width.EditValue = ""
        Me.txt_width.Location = New System.Drawing.Point(71, 183)
        Me.txt_width.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_width.Name = "txt_width"
        Me.txt_width.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_width.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_width.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_width.Properties.Appearance.Options.UseBackColor = True
        Me.txt_width.Properties.Appearance.Options.UseFont = True
        Me.txt_width.Properties.Appearance.Options.UseForeColor = True
        Me.txt_width.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_width.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_width.Properties.NullValuePrompt = "Width Logo"
        Me.txt_width.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_width.Size = New System.Drawing.Size(167, 30)
        Me.txt_width.TabIndex = 578
        '
        'txt_height
        '
        Me.txt_height.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_height.EditValue = ""
        Me.txt_height.Location = New System.Drawing.Point(71, 126)
        Me.txt_height.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_height.Name = "txt_height"
        Me.txt_height.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_height.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_height.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_height.Properties.Appearance.Options.UseBackColor = True
        Me.txt_height.Properties.Appearance.Options.UseFont = True
        Me.txt_height.Properties.Appearance.Options.UseForeColor = True
        Me.txt_height.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_height.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_height.Properties.NullValuePrompt = "Height logo"
        Me.txt_height.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_height.Size = New System.Drawing.Size(167, 30)
        Me.txt_height.TabIndex = 578
        '
        'GroupControl2
        '
        Me.GroupControl2.Controls.Add(Me.Clear4)
        Me.GroupControl2.Controls.Add(Me.btn_Save)
        Me.GroupControl2.Controls.Add(Me.Clear3)
        Me.GroupControl2.Controls.Add(Me.BTN_Redirect_Link_Microsoft)
        Me.GroupControl2.Controls.Add(Me.btn_Reset)
        Me.GroupControl2.Controls.Add(Me.SimpleButton3)
        Me.GroupControl2.Controls.Add(Me.SimpleButton5)
        Me.GroupControl2.Controls.Add(Me.txt_Logo_URL)
        Me.GroupControl2.Controls.Add(Me.Label12)
        Me.GroupControl2.Controls.Add(Me.txt_type)
        Me.GroupControl2.Controls.Add(Me.Label11)
        Me.GroupControl2.Controls.Add(Me.txt_Tag)
        Me.GroupControl2.Controls.Add(Me.Label10)
        Me.GroupControl2.Controls.Add(Me.txt_Link)
        Me.GroupControl2.Controls.Add(Me.Label9)
        Me.GroupControl2.Location = New System.Drawing.Point(16, 120)
        Me.GroupControl2.Name = "GroupControl2"
        Me.GroupControl2.Size = New System.Drawing.Size(615, 456)
        Me.GroupControl2.TabIndex = 608
        Me.GroupControl2.Text = "Settings Redirect "
        '
        'Clear4
        '
        Me.Clear4.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear4.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear4.Appearance.Options.UseBackColor = True
        Me.Clear4.Appearance.Options.UseBorderColor = True
        Me.Clear4.ImageOptions.Image = CType(resources.GetObject("Clear4.ImageOptions.Image"), System.Drawing.Image)
        Me.Clear4.Location = New System.Drawing.Point(583, 132)
        Me.Clear4.Name = "Clear4"
        Me.Clear4.Size = New System.Drawing.Size(25, 23)
        Me.Clear4.TabIndex = 589
        '
        'Clear3
        '
        Me.Clear3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear3.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Clear3.Appearance.Options.UseBackColor = True
        Me.Clear3.Appearance.Options.UseBorderColor = True
        Me.Clear3.ImageOptions.Image = CType(resources.GetObject("Clear3.ImageOptions.Image"), System.Drawing.Image)
        Me.Clear3.Location = New System.Drawing.Point(583, 64)
        Me.Clear3.Name = "Clear3"
        Me.Clear3.Size = New System.Drawing.Size(25, 23)
        Me.Clear3.TabIndex = 590
        '
        'SimpleButton3
        '
        Me.SimpleButton3.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton3.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton3.Appearance.Options.UseBackColor = True
        Me.SimpleButton3.Appearance.Options.UseBorderColor = True
        Me.SimpleButton3.ImageOptions.Image = CType(resources.GetObject("SimpleButton3.ImageOptions.Image"), System.Drawing.Image)
        Me.SimpleButton3.Location = New System.Drawing.Point(583, 271)
        Me.SimpleButton3.Name = "SimpleButton3"
        Me.SimpleButton3.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton3.TabIndex = 588
        '
        'SimpleButton5
        '
        Me.SimpleButton5.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton5.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.SimpleButton5.Appearance.Options.UseBackColor = True
        Me.SimpleButton5.Appearance.Options.UseBorderColor = True
        Me.SimpleButton5.ImageOptions.Image = CType(resources.GetObject("SimpleButton5.ImageOptions.Image"), System.Drawing.Image)
        Me.SimpleButton5.Location = New System.Drawing.Point(583, 202)
        Me.SimpleButton5.Name = "SimpleButton5"
        Me.SimpleButton5.Size = New System.Drawing.Size(25, 23)
        Me.SimpleButton5.TabIndex = 588
        '
        'txt_Logo_URL
        '
        Me.txt_Logo_URL.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Logo_URL.EditValue = ""
        Me.txt_Logo_URL.Location = New System.Drawing.Point(6, 268)
        Me.txt_Logo_URL.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Logo_URL.Name = "txt_Logo_URL"
        Me.txt_Logo_URL.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Logo_URL.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Logo_URL.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Logo_URL.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Logo_URL.Properties.Appearance.Options.UseFont = True
        Me.txt_Logo_URL.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Logo_URL.Properties.NullValuePrompt = "Enter Your Link Logo"
        Me.txt_Logo_URL.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Logo_URL.Size = New System.Drawing.Size(603, 30)
        Me.txt_Logo_URL.TabIndex = 585
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label12.Location = New System.Drawing.Point(6, 238)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(153, 21)
        Me.Label12.TabIndex = 582
        Me.Label12.Text = "Enter Your Link Logo"
        '
        'txt_type
        '
        Me.txt_type.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_type.EditValue = ""
        Me.txt_type.Location = New System.Drawing.Point(6, 199)
        Me.txt_type.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_type.Name = "txt_type"
        Me.txt_type.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_type.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_type.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_type.Properties.Appearance.Options.UseBackColor = True
        Me.txt_type.Properties.Appearance.Options.UseFont = True
        Me.txt_type.Properties.Appearance.Options.UseForeColor = True
        Me.txt_type.Properties.NullValuePrompt = "Scan Complete: Safe Link"
        Me.txt_type.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_type.Size = New System.Drawing.Size(603, 30)
        Me.txt_type.TabIndex = 585
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label11.Location = New System.Drawing.Point(6, 169)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(113, 21)
        Me.Label11.TabIndex = 582
        Me.Label11.Text = "Enter Your Text"
        '
        'txt_Tag
        '
        Me.txt_Tag.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Tag.EditValue = ""
        Me.txt_Tag.Location = New System.Drawing.Point(6, 130)
        Me.txt_Tag.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Tag.Name = "txt_Tag"
        Me.txt_Tag.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Tag.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Tag.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Tag.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Tag.Properties.Appearance.Options.UseFont = True
        Me.txt_Tag.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Tag.Properties.NullValuePrompt = "[-Email-]"
        Me.txt_Tag.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Tag.Size = New System.Drawing.Size(603, 30)
        Me.txt_Tag.TabIndex = 586
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label10.Location = New System.Drawing.Point(6, 100)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(35, 21)
        Me.Label10.TabIndex = 583
        Me.Label10.Text = "Tag"
        '
        'txt_Link
        '
        Me.txt_Link.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Link.EditValue = ""
        Me.txt_Link.Location = New System.Drawing.Point(6, 61)
        Me.txt_Link.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Link.Name = "txt_Link"
        Me.txt_Link.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Link.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Link.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Link.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Link.Properties.Appearance.Options.UseFont = True
        Me.txt_Link.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Link.Properties.NullValuePrompt = "Enter Your Link Page Example > https://www.Example.com/?="
        Me.txt_Link.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Link.Size = New System.Drawing.Size(603, 30)
        Me.txt_Link.TabIndex = 587
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label9.Location = New System.Drawing.Point(6, 31)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(110, 21)
        Me.Label9.TabIndex = 584
        Me.Label9.Text = "Enter Your link"
        '
        'SeparatorControl2
        '
        Me.SeparatorControl2.LineThickness = 1
        Me.SeparatorControl2.Location = New System.Drawing.Point(267, 82)
        Me.SeparatorControl2.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl2.Name = "SeparatorControl2"
        Me.SeparatorControl2.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl2.Size = New System.Drawing.Size(363, 26)
        Me.SeparatorControl2.TabIndex = 609
        '
        'PictureBox2
        '
        Me.PictureBox2.Image = Global.Best_Sender.My.Resources.Resources.RedirctMicrosoftPaner
        Me.PictureBox2.Location = New System.Drawing.Point(236, 12)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(434, 96)
        Me.PictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox2.TabIndex = 611
        Me.PictureBox2.TabStop = False
        '
        'SimpleButton1
        '
        Me.SimpleButton1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton1.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.Appearance.Options.UseBackColor = True
        Me.SimpleButton1.Appearance.Options.UseBorderColor = True
        Me.SimpleButton1.Appearance.Options.UseFont = True
        Me.SimpleButton1.Appearance.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.SimpleButton1.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.SimpleButton1.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.SimpleButton1.AppearanceDisabled.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.SimpleButton1.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseBorderColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseForeColor = True
        Me.SimpleButton1.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.SimpleButton1.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseBorderColor = True
        Me.SimpleButton1.AppearancePressed.Options.UseForeColor = True
        Me.SimpleButton1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.SimpleButton1.Location = New System.Drawing.Point(720, 70)
        Me.SimpleButton1.Margin = New System.Windows.Forms.Padding(4)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(152, 38)
        Me.SimpleButton1.TabIndex = 612
        Me.SimpleButton1.Text = "I want a Sample"
        '
        'frmMicrosoftRedirect
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.SimpleButton1)
        Me.Controls.Add(Me.SeparatorControl2)
        Me.Controls.Add(Me.GroupControl1)
        Me.Controls.Add(Me.RichTextBox1)
        Me.Controls.Add(Me.GroupControl2)
        Me.Controls.Add(Me.PictureBox2)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmMicrosoftRedirect"
        Me.ShowInTaskbar = False
        Me.Text = " Microsoft Redirect"
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Pic_Logo_Button_Button, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_width.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_height.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl2.ResumeLayout(False)
        Me.GroupControl2.PerformLayout()
        CType(Me.txt_Logo_URL.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_type.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_Tag.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_Link.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents btn_Save As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btn_Reset As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BTN_Redirect_Link_Microsoft As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Label20 As Label
    Friend WithEvents Label22 As Label
    Friend WithEvents Label21 As Label
    Friend WithEvents cb_txt_Time As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents lbl_Size_Button_Button As Label
    Friend WithEvents Pic_Logo_Button_Button As PictureBox
    Friend WithEvents txt_width As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txt_height As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GroupControl2 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Clear4 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Clear3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton3 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton5 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txt_Logo_URL As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label12 As Label
    Friend WithEvents txt_type As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label11 As Label
    Friend WithEvents txt_Tag As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label10 As Label
    Friend WithEvents txt_Link As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label9 As Label
    Friend WithEvents SeparatorControl2 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents PictureBox2 As PictureBox
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
End Class
