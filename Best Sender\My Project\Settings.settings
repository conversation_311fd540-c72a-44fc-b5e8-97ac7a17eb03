﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="My" GeneratedClassName="MySettings" UseMySettingsClassName="true">
  <Profiles />
  <Settings>
    <Setting Name="prefixes" Type="System.String" Scope="User">
      <Value Profile="(Default)">KS|Kosovo|+381
AL|Albania|+355
AF|Afghanistan|+93
GB|UK|+44
US|USA|+1
DZ|Algeria|+213
AD|Andorra|+376
AO|Angola|+244
AI|Anguilla|+1264
AG|Antigua&amp;Barbuda|+1268
AR|Argentina|+54
AM|Armenia|+374
AW|Aruba|+297
AU|Australia|+61
AT|Austria|+43
AZ|Azerbaijan|+994
BS|Bahamas|+1242
BH|Bahrain|+973
BD|Bangladesh|+880
BB|Barbados|+1246
BY|Belarus|+375
BE|Belgium|+32
BZ|Belize|+501
BJ|Benin|+229
BM|Bermuda|+1441
BT|Bhutan|+975
BO|Bolivia|+591
BA|BosniaHerzegovina|+387
BW|Botswana|+267
BR|Brazil|+55
BN|Brunei|+673
BG|Bulgaria|+359
BF|BurkinaFaso|+226
BI|Burundi|+257
KH|Cambodia|+855
CM|Cameroon|+237
CA|Canada|+1
CV|CapeVerdeIslands|+238
KY|CaymanIslands|+1345
CF|CentralAfricanRepublic|+236
CL|Chile|+56
CN|China|+86
CO|Colombia|+57
KM|Comoros|+269
CG|Congo|+242
CK|CookIslands|+682
CR|CostaRica|+506
HR|Croatia|+385
CU|Cuba|+53
CY|CyprusNorth|+90392
CY|CyprusSouth|+357
CZ|CzechRepublic|+42
DK|Denmark|+45
DJ|Djibouti|+253
DM|Dominica|+1809
DO|DominicanRepublic|+1809
EC|Ecuador|+593
EG|Egypt|+20
SV|ElSalvador|+503
GQ|EquatorialGuinea|+240
ER|Eritrea|+291
EE|Estonia|+372
ET|Ethiopia|+251
FK|FalklandIslands|+500
FO|FaroeIslands|+298
FJ|Fiji|+679
FI|Finland|+358
FR|France|+33
GF|FrenchGuiana|+594
PF|FrenchPolynesia|+689
GA|Gabon|+241
GM|Gambia|+220
GE|Georgia|+7880
DE|Germany|+49
GH|Ghana|+233
GI|Gibraltar|+350
GR|Greece|+30
GL|Greenland|+299
GD|Grenada|+1473
GP|Guadeloupe|+590
GU|Guam|+671
GT|Guatemala|+502
GN|Guinea|+224
GW|Guinea-Bissau|+245
GY|Guyana|+592
HT|Haiti|+509
HN|Honduras|+504
HK|HongKong|+852
HU|Hungary|+36
IS|Iceland|+354
IN|India|+91
ID|Indonesia|+62
IR|Iran|+98
IQ|Iraq|+964
IE|Ireland|+353
IL|Israel|+972
IT|Italy|+39
JM|Jamaica|+1876
JP|Japan|+81
JO|Jordan|+962
KZ|Kazakhstan|+7
KE|Kenya|+254
KI|Kiribati|+686
KP|KoreaNorth|+850
KR|KoreaSouth|+82
KW|Kuwait|+965
KG|Kyrgyzstan|+996
LA|Laos|+856
LV|Latvia|+371
LB|Lebanon|+961
LS|Lesotho|+266
LR|Liberia|+231
LY|Libya|+218
LI|Liechtenstein|+417
LT|Lithuania|+370
LU|Luxembourg|+352
MO|Macao|+853
MK|Macedonia|+389
MG|Madagascar|+261
MW|Malawi|+265
MY|Malaysia|+60
MV|Maldives|+960
ML|Mali|+223
MT|Malta|+356
MH|MarshallIslands|+692
MQ|Martinique|+596
MR|Mauritania|+222
YT|Mayotte|+269
MX|Mexico|+52
FM|Micronesia|+691
MD|Moldova|+373
MC|Monaco|+377
MN|Mongolia|+976
ME|Montenegro|+382
MS|Montserrat|+1664
MA|Morocco|+212
MZ|Mozambique|+258
MN|Myanmar|+95
NA|Namibia|+264
NR|Nauru|+674
NP|Nepal|+977
NL|Netherlands|+31
NC|NewCaledonia|+687
NZ|NewZealand|+64
NI|Nicaragua|+505
NE|Niger|+227
NG|Nigeria|+234
NU|Niue|+683
NF|NorfolkIslands|+672
NP|NorthernMarianas|+670
NO|Norway|+47
OM|Oman|+968
PW|Palau|+680
PA|Panama|+507
PG|PapuaNewGuinea|+675
PY|Paraguay|+595
PE|Peru|+51
PH|Philippines|+63
PL|Poland|+48
PT|Portugal|+351
PR|PuertoRico|+1787
QA|Qatar|+974
RE|Reunion|+262
RO|Romania|+40
RS|Serbia|+381
RU|Russia|+7
RW|Rwanda|+250
SM|SanMarino|+378
ST|SaoTome&amp;Principe|+239
SA|SaudiArabia|+966
SN|Senegal|+221
CS|Serbia|+381
SC|Seychelles|+248
SL|SierraLeone|+232
SG|Singapore|+65
SK|SlovakRepublic|+421
SI|Slovenia|+386
SB|SolomonIslands|+677
SO|Somalia|+252
ZA|SouthAfrica|+27
ES|Spain|+34
LK|SriLanka|+94
SH|St.Helena|+290
KN|St.Kitts|+1869
SC|St.Lucia|+1758
SD|Sudan|+249
SR|Suriname|+597
SZ|Swaziland|+268
SE|Sweden|+46
CH|Switzerland|+41
SI|Syria|+963
TW|Taiwan|+886
TJ|Tajikstan|+7
TH|Thailand|+66
TG|Togo|+228
TO|Tonga|+676
TT|Trinidad&amp;Tobago|+1868
TN|Tunisia|+216
TR|Turkey|+90
TM|Turkmenistan|+7
TM|Turkmenistan|+993
TC|Turks&amp;CaicosIslands|+1649
TV|Tuvalu|+688
UG|Uganda|+256
GB|SelectedUK|+44
UA|Ukraine|+380
AE|UnitedArabEmirates|+971
UY|Uruguay|+598
UZ|Uzbekistan|+7
VU|Vanuatu|+678
VA|VaticanCity|+379
VE|Venezuela|+58
VN|Vietnam|+84
VG|VirginIslands-British|+1284
VI|VirginIslands-US|+1340
WF|Wallis&amp;Futuna|+681
YE|YemenNorth|+969
YE|YemenSouth|+967
ZM|Zambia|+260
ZW|Zimbabwe|+263</Value>
    </Setting>
    <Setting Name="MailSorterPath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Chk_MailSorterPath" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="appver" Type="System.String" Scope="User">
      <Value Profile="(Default)">Data Source=SQL5091.site4now.net;Initial Catalog=db_a8d9ed_bestsender;User Id=db_a8d9ed_bestsender_admin;Password=220Aa0154</Value>
    </Setting>
    <Setting Name="hastring" Type="System.String" Scope="User">
      <Value Profile="(Default)">hkhfhfhdgh</Value>
    </Setting>
    <Setting Name="appname" Type="System.String" Scope="User">
      <Value Profile="(Default)">Best Sender</Value>
    </Setting>
    <Setting Name="PROSTRING" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Fakeuser" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="TrainWarning" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="Setting" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Host" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Email" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Password" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Port" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="FromName" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtSubject" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="SMTP_DATA_TABLE_XML" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="EMAILS_DATA_TABLE_XML" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="DmPath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ExtPath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TrcThread" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="LETTER" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="LETTER_LINK" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="LETTER_CON_LINK" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="SETTING_MDF" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Link" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Tag" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Type" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Logo_URL" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Width" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Length" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="LinkGoogleCaptcha" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TypeGoogleCaptcha" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="SiteKey" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="URL_CloudFlare" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Link_Logo_CludFlare" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Type_CloudFlare" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Width_CloudFlare" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Length_CloudFlare" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="word" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txt_Logo_URL_VIP" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txt_word_VIP" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txt_type_VIP" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txt_Link_VIP" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txt_Link_Puzzle" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txt_type_Puzzle" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Logo_Page" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Logo_Button" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Link_New" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Button_txt" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Outlook_txt" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Type_txt" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Link_txt" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TextBox2LinkPage" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TextBox1MaskLink" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="LogoBKAuthentication" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="LogoUp" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtWord" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtPage" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="URLAuthentication" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtLetter" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Title" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Logo" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="FontSizeMessage" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="FontTitle" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ButtonColor" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TextColor" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TitleColor" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Message" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ButtonName" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Signature" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TextBoxLogoLM" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TextBoxLinklk" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtHttps" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtmyLinkURL" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="txtLinkEncodedWolf" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TitelPass" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="LogoPass" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="PagePass1" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="PagePass2" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="LinkPass" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="EmailTagPass" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="TextBoxpassword" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="UploadCpanel" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="LinkPageCpanel" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="LinkEncodeCpanel" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
  </Settings>
</SettingsFile>