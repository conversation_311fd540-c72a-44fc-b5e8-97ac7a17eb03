﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\IronPdf.Slim.2024.12.9\build\IronPdf.Slim.props" Condition="Exists('..\packages\IronPdf.Slim.2024.12.9\build\IronPdf.Slim.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{F4DDE5F1-6F6D-4C13-8D53-FF5C90E54B15}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>Best_Sender.My.MyApplication</StartupObject>
    <RootNamespace>Best_Sender</RootNamespace>
    <AssemblyName>Best Sender</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>Best Sender.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>Best Sender.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>BSV.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AWSSDK.Core, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.Core.3.7.105.8\lib\net45\AWSSDK.Core.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.S3, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.S3.3.7.103.15\lib\net45\AWSSDK.S3.dll</HintPath>
    </Reference>
    <Reference Include="BitMiracle.LibTiff.NET, Version=2.4.649.0, Culture=neutral, PublicKeyToken=53879b3e20e7a7d6, processorArchitecture=MSIL">
      <HintPath>..\packages\BitMiracle.LibTiff.NET.2.4.649\lib\net40\BitMiracle.LibTiff.NET.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.9.0.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="Bunifu_UI_v1.5.3, Version=1.5.3.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\Downloads\مشروع ولف 11-04-2023   44444444\مشروع ولف 11-04-2023   44444444\WindowsApp1\WindowsApp1\bin\Debug\Bunifu_UI_v1.5.3.dll</HintPath>
    </Reference>
    <Reference Include="ChilkatDotNet47, Version=9.5.0.91, Culture=neutral, PublicKeyToken=eb5fc1fc52ef09bd, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\ChilkatDotNet47.dll</HintPath>
    </Reference>
    <Reference Include="CoreHtmlToImage, Version=1.0.6.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\CoreHtmlToImage.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.BonusSkins.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.CodeParser.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.Desktop.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Drawing.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.DataAccess.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.DataAccess.v24.2.UI, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Diagram.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.ExpressApp.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Images.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Office.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Pdf.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Persistent.Base.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.RichEdit.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Sparkline.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v24.2.UI, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraBars.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraDiagram.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraEditors.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGauges.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraGauges.v24.2.Win, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGrid.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraLayout.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraPrinting.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Charts.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraRichEdit.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraTreeList.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraVerticalGrid.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DnsClient, Version=1.7.0.0, Culture=neutral, PublicKeyToken=4574bb5573c51424, processorArchitecture=MSIL">
      <HintPath>..\packages\DnsClient.1.7.0\lib\net471\DnsClient.dll</HintPath>
    </Reference>
    <Reference Include="Google.Protobuf, Version=3.23.0.0, Culture=neutral, PublicKeyToken=a7d26565bac4d604, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Protobuf.3.23.0\lib\net45\Google.Protobuf.dll</HintPath>
    </Reference>
    <Reference Include="Grpc.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=d754f35622e28bad, processorArchitecture=MSIL">
      <HintPath>..\packages\Grpc.Core.2.46.6\lib\net45\Grpc.Core.dll</HintPath>
    </Reference>
    <Reference Include="Grpc.Core.Api, Version=2.0.0.0, Culture=neutral, PublicKeyToken=d754f35622e28bad, processorArchitecture=MSIL">
      <HintPath>..\packages\Grpc.Core.Api.2.62.0\lib\net462\Grpc.Core.Api.dll</HintPath>
    </Reference>
    <Reference Include="Grpc.Net.Client, Version=2.0.0.0, Culture=neutral, PublicKeyToken=d754f35622e28bad, processorArchitecture=MSIL">
      <HintPath>..\packages\Grpc.Net.Client.2.62.0\lib\net462\Grpc.Net.Client.dll</HintPath>
    </Reference>
    <Reference Include="Grpc.Net.Common, Version=2.0.0.0, Culture=neutral, PublicKeyToken=d754f35622e28bad, processorArchitecture=MSIL">
      <HintPath>..\packages\Grpc.Net.Common.2.62.0\lib\netstandard2.0\Grpc.Net.Common.dll</HintPath>
    </Reference>
    <Reference Include="HtmlAgilityPack, Version=1.12.1.0, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlAgilityPack.1.12.1\lib\Net45\HtmlAgilityPack.dll</HintPath>
    </Reference>
    <Reference Include="HTMLEditor, Version=2.4.8.298, Culture=neutral, PublicKeyToken=7e79dd0f3fedb2ef, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\HTMLEditor.dll</HintPath>
    </Reference>
    <Reference Include="HtmlRenderer, Version=1.5.0.5, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlRenderer.Core.1.5.0.5\lib\net45\HtmlRenderer.dll</HintPath>
    </Reference>
    <Reference Include="IronPdf, Version=2024.12.0.9, Culture=neutral, PublicKeyToken=94e1c31412563c75, processorArchitecture=MSIL">
      <HintPath>..\packages\IronPdf.Slim.2024.12.9\lib\netstandard2.0\IronPdf.dll</HintPath>
    </Reference>
    <Reference Include="IronPdf.Extensions.ASPX, Version=2023.10.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IronPdf.Extensions.ASPX.2023.10.3\lib\net462\IronPdf.Extensions.ASPX.dll</HintPath>
    </Reference>
    <Reference Include="IronSoftware.Abstractions, Version=2024.11.0.8, Culture=neutral, PublicKeyToken=3bd0119b776a4800, processorArchitecture=MSIL">
      <HintPath>..\packages\IronSoftware.Abstractions.2024.11.8\lib\netstandard2.0\IronSoftware.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="IronSoftware.Abstractions.Models, Version=2024.11.0.8, Culture=neutral, PublicKeyToken=3bd0119b776a4800, processorArchitecture=MSIL">
      <HintPath>..\packages\IronSoftware.Abstractions.2024.11.8\lib\netstandard2.0\IronSoftware.Abstractions.Models.dll</HintPath>
    </Reference>
    <Reference Include="IronSoftware.Drawing.Abstractions, Version=2024.11.0.8, Culture=neutral, PublicKeyToken=3bd0119b776a4800, processorArchitecture=MSIL">
      <HintPath>..\packages\IronSoftware.Drawing.Abstractions.2024.11.8\lib\netstandard2.0\IronSoftware.Drawing.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="IronSoftware.Drawing.Common, Version=2024.12.0.3, Culture=neutral, PublicKeyToken=8d7e55c97b3e9835, processorArchitecture=MSIL">
      <HintPath>..\packages\IronSoftware.System.Drawing.2024.12.3\lib\netstandard2.0\IronSoftware.Drawing.Common.dll</HintPath>
    </Reference>
    <Reference Include="IronSoftware.Logger, Version=2024.11.0.2, Culture=neutral, PublicKeyToken=c0ab8f6e415e2702, processorArchitecture=MSIL">
      <HintPath>..\packages\IronSoftware.Common.2024.11.2\lib\netstandard2.0\IronSoftware.Logger.dll</HintPath>
    </Reference>
    <Reference Include="IronSoftware.Shared, Version=2024.11.0.2, Culture=neutral, PublicKeyToken=c0ab8f6e415e2702, processorArchitecture=MSIL">
      <HintPath>..\packages\IronSoftware.Common.2024.11.2\lib\netstandard2.0\IronSoftware.Shared.dll</HintPath>
    </Reference>
    <Reference Include="MailKit, Version=3.5.0.0, Culture=neutral, PublicKeyToken=4e064fe7c44a8f1b, processorArchitecture=MSIL">
      <HintPath>..\packages\MailKit.3.5.0\lib\net47\MailKit.dll</HintPath>
    </Reference>
    <Reference Include="MessagingToolkit.QRCode, Version=1.0.3541.23206, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\MessagingToolkit.QRCode.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.6.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Extensions.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.6.0.0\lib\net461\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.6.0.0\lib\net461\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.6.0.0\lib\net461\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.FileExtensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.FileExtensions.6.0.0\lib\net461\Microsoft.Extensions.Configuration.FileExtensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Json, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Json.6.0.0\lib\net461\Microsoft.Extensions.Configuration.Json.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.UserSecrets, Version=6.0.0.1, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.UserSecrets.6.0.1\lib\net461\Microsoft.Extensions.Configuration.UserSecrets.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Abstractions.6.0.0\lib\net461\Microsoft.Extensions.FileProviders.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Physical, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Physical.6.0.0\lib\net461\Microsoft.Extensions.FileProviders.Physical.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileSystemGlobbing, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileSystemGlobbing.6.0.0\lib\net461\Microsoft.Extensions.FileSystemGlobbing.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\lib\net461\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.6.0.0\lib\net461\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=1*******, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Office.Interop.Excel.15.0.4795.1001\lib\net20\Microsoft.Office.Interop.Excel.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Primitives.4.0.1\lib\net46\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Registry, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Registry.5.0.0\lib\net461\Microsoft.Win32.Registry.dll</HintPath>
    </Reference>
    <Reference Include="MicrovisionActivation, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\MicrovisionActivation.dll</HintPath>
    </Reference>
    <Reference Include="MimeKit, Version=3.5.0.0, Culture=neutral, PublicKeyToken=bede1c8a46c66814, processorArchitecture=MSIL">
      <HintPath>..\packages\MimeKit.3.5.0\lib\net47\MimeKit.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="OpenQA.Selenium.Winium, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\OpenQA.Selenium.Winium.1.0.0.4\lib\OpenQA.Selenium.Winium.dll</HintPath>
    </Reference>
    <Reference Include="PdfSharp, Version=1.32.3057.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp.1.32.3057.0\lib\net20\PdfSharp.dll</HintPath>
    </Reference>
    <Reference Include="PdfSharp.Charting, Version=1.32.3057.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp.1.32.3057.0\lib\net20\PdfSharp.Charting.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="QRCoder, Version=1.4.3.0, Culture=neutral, PublicKeyToken=c4ed5b9ae8358a28, processorArchitecture=MSIL">
      <HintPath>..\packages\QRCoder.1.4.3\lib\net40\QRCoder.dll</HintPath>
    </Reference>
    <Reference Include="Select.HtmlToPdf, Version=23.2.0.0, Culture=neutral, PublicKeyToken=e0ae9f6e27a97018, processorArchitecture=MSIL">
      <HintPath>..\packages\Select.HtmlToPdf.23.2.0\lib\net40\Select.HtmlToPdf.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.Fonts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\packages\SixLabors.Fonts.1.0.0\lib\netstandard2.0\SixLabors.Fonts.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.ImageSharp, Version=2.0.0.0, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\packages\SixLabors.ImageSharp.2.1.9\lib\net472\SixLabors.ImageSharp.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.ImageSharp.Drawing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\packages\SixLabors.ImageSharp.Drawing.1.0.0\lib\net472\SixLabors.ImageSharp.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="SKGL, Version=2.0.2.1, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\SKGL.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.AppContext, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.AppContext.4.1.0\lib\net463\System.AppContext.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.6.0.0\lib\net461\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Console, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Console.4.0.0\lib\net46\System.Console.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Design" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=6.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.6.0.1\lib\net461\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.Tracing.4.1.0\lib\net462\System.Diagnostics.Tracing.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Common, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Drawing.Common.6.0.0\lib\net461\System.Drawing.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Globalization.Calendars.4.0.1\lib\net46\System.Globalization.Calendars.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.1.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.ZipFile.4.0.1\lib\net46\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.4.0.1\lib\net46\System.IO.FileSystem.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.0.1\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.4.1.0\lib\net463\System.Linq.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.Expressions.4.1.0\lib\net463\System.Linq.Expressions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.1.0\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.WinHttpHandler, Version=7.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.WinHttpHandler.7.0.0\lib\net462\System.Net.Http.WinHttpHandler.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Sockets.4.1.0\lib\net46\System.Net.Sockets.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.4.3.0\lib\net462\System.Reflection.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Reflection.TypeExtensions, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.TypeExtensions.4.3.0\lib\net462\System.Reflection.TypeExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Extensions.4.1.0\lib\net462\System.Runtime.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.4.3.0\lib\net463\System.Runtime.InteropServices.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.0.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.6.0.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.1.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.6.0.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encoding.CodePages, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.5.0.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.6.0.0\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.6.0.0\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.RegularExpressions.4.1.0\lib\net463\System.Text.RegularExpressions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Thread.4.0.0\lib\net46\System.Threading.Thread.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="UIAutomationProvider" />
    <Reference Include="WebDriver, Version=3.141.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Selenium.WebDriver.3.141.0\lib\net45\WebDriver.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="WindowsFormsIntegration" />
    <Reference Include="xNet, Version=3.3.3.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\xNet.dll</HintPath>
    </Reference>
    <Reference Include="YamlDotNet, Version=11.0.0.0, Culture=neutral, PublicKeyToken=ec19458f3c15af5e, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\YamlDotNet.dll</HintPath>
    </Reference>
    <Reference Include="zxing, Version=0.16.9.0, Culture=neutral, PublicKeyToken=4e88037ac681fe60, processorArchitecture=MSIL">
      <HintPath>..\packages\ZXing.Net.0.16.9\lib\net47\zxing.dll</HintPath>
    </Reference>
    <Reference Include="zxing.presentation, Version=0.16.9.0, Culture=neutral, PublicKeyToken=4e88037ac681fe60, processorArchitecture=MSIL">
      <HintPath>..\packages\ZXing.Net.0.16.9\lib\net47\zxing.presentation.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Attachment Encoded Pro\frmAttPro.Designer.vb">
      <DependentUpon>frmAttPro.vb</DependentUpon>
    </Compile>
    <Compile Include="Attachment Encoded Pro\frmAttPro.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Email Sender\frmAddToAllAccounts.Designer.vb">
      <DependentUpon>frmAddToAllAccounts.vb</DependentUpon>
    </Compile>
    <Compile Include="Email Sender\frmAddToAllAccounts.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Email Sender\frmEmailSender.Designer.vb">
      <DependentUpon>frmEmailSender.vb</DependentUpon>
    </Compile>
    <Compile Include="Email Sender\frmEmailSender.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Email Sender\frmLetterinorSP.Designer.vb">
      <DependentUpon>frmLetterinorSP.vb</DependentUpon>
    </Compile>
    <Compile Include="Email Sender\frmLetterinorSP.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Email Sender\frmSeeHTML.Designer.vb">
      <DependentUpon>frmSeeHTML.vb</DependentUpon>
    </Compile>
    <Compile Include="Email Sender\frmSeeHTML.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Email Sender\frmTestSmtpAccounts.Designer.vb">
      <DependentUpon>frmTestSmtpAccounts.vb</DependentUpon>
    </Compile>
    <Compile Include="Email Sender\frmTestSmtpAccounts.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Email Sender\FuncSendMail.vb" />
    <Compile Include="Email Sender\OthmanCode_Module.vb" />
    <Compile Include="Email Sender\SuspiciousWordsLoader.vb" />
    <Compile Include="Email Sorter Domain\frmEMailSorterDomain.Designer.vb">
      <DependentUpon>frmEMailSorterDomain.vb</DependentUpon>
    </Compile>
    <Compile Include="Email Sorter Domain\frmEMailSorterDomain.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Email Sorter Domain\Helper.vb" />
    <Compile Include="Email Sorter Domain\LvData.vb" />
    <Compile Include="Email Sorter Domain\Vars.vb" />
    <Compile Include="Encoded Link\frmEncodeLink.Designer.vb">
      <DependentUpon>frmEncodeLink.vb</DependentUpon>
    </Compile>
    <Compile Include="Encoded Link\frmEncodeLink.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Encoded Link\frmlinktoAtt.Designer.vb">
      <DependentUpon>frmlinktoAtt.vb</DependentUpon>
    </Compile>
    <Compile Include="Encoded Link\frmlinktoAtt.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmNew.Designer.vb">
      <DependentUpon>frmNew.vb</DependentUpon>
    </Compile>
    <Compile Include="frmNew.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IMAP\frmIMAP.Designer.vb">
      <DependentUpon>frmIMAP.vb</DependentUpon>
    </Compile>
    <Compile Include="IMAP\frmIMAP.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IMAP\frmDataTransfer_Wait.Designer.vb">
      <DependentUpon>frmDataTransfer_Wait.vb</DependentUpon>
    </Compile>
    <Compile Include="IMAP\frmDataTransfer_Wait.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IMAP\frmIMAP_Wait.Designer.vb">
      <DependentUpon>frmIMAP_Wait.vb</DependentUpon>
    </Compile>
    <Compile Include="IMAP\frmIMAP_Wait.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IMAP\GxIMAPAccount.vb" />
    <Compile Include="IMAP\king Get Mail\IMAP_BSV.Designer.vb">
      <DependentUpon>IMAP_BSV.vb</DependentUpon>
    </Compile>
    <Compile Include="IMAP\king Get Mail\IMAP_BSV.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Letters\EncoderVariablesvb.vb" />
    <Compile Include="Letters\frmLetterEncoddvb.Designer.vb">
      <DependentUpon>frmLetterEncoddvb.vb</DependentUpon>
    </Compile>
    <Compile Include="Letters\frmLetterEncoddvb.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Letters\Module1.vb" />
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="New Activation\frmActivate_New.designer.vb">
      <DependentUpon>frmActivate_New.vb</DependentUpon>
    </Compile>
    <Compile Include="New Activation\frmActivate_New.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="New Services\frmRedirectSpider.Designer.vb">
      <DependentUpon>frmRedirectSpider.vb</DependentUpon>
    </Compile>
    <Compile Include="New Services\frmRedirectSpider.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="New Services\frmRedirectWithPassword.Designer.vb">
      <DependentUpon>frmRedirectWithPassword.vb</DependentUpon>
    </Compile>
    <Compile Include="New Services\frmRedirectWithPassword.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="New Services\frmHideLink.Designer.vb">
      <DependentUpon>frmHideLink.vb</DependentUpon>
    </Compile>
    <Compile Include="New Services\frmHideLink.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="New Services\frmMicrosoftRedirect.Designer.vb">
      <DependentUpon>frmMicrosoftRedirect.vb</DependentUpon>
    </Compile>
    <Compile Include="New Services\frmMicrosoftRedirect.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="New Services\frmMultiLinker.Designer.vb">
      <DependentUpon>frmMultiLinker.vb</DependentUpon>
    </Compile>
    <Compile Include="New Services\frmMultiLinker.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="New Services\frmPassMicrosoft.Designer.vb">
      <DependentUpon>frmPassMicrosoft.vb</DependentUpon>
    </Compile>
    <Compile Include="New Services\frmPassMicrosoft.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="New Services\frmReAuthenticationAtt.Designer.vb">
      <DependentUpon>frmReAuthenticationAtt.vb</DependentUpon>
    </Compile>
    <Compile Include="New Services\frmReAuthenticationAtt.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="New Services\frmRedirectPuzzle.Designer.vb">
      <DependentUpon>frmRedirectPuzzle.vb</DependentUpon>
    </Compile>
    <Compile Include="New Services\frmRedirectPuzzle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Office365\frmCheckOffce356.designer.vb">
      <DependentUpon>frmCheckOffce356.vb</DependentUpon>
    </Compile>
    <Compile Include="Office365\frmCheckOffce356.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\Email Scanner\frmAddAttachment.designer.vb">
      <DependentUpon>frmAddAttachment.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\Email Scanner\frmAddAttachment.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\Email Scanner\Select SMTP\frmaddAttach.Designer.vb">
      <DependentUpon>frmaddAttach.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\Email Scanner\Select SMTP\frmaddAttach.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\Email Scanner\Select SMTP\frmAddSmtpServer.Designer.vb">
      <DependentUpon>frmAddSmtpServer.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\Email Scanner\Select SMTP\frmAddSmtpServer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\Email Scanner\Select SMTP\frmSelectSMTP.Designer.vb">
      <DependentUpon>frmSelectSMTP.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\Email Scanner\Select SMTP\frmSelectSMTP.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\Email Scanner\EmailScanner.Designer.vb">
      <DependentUpon>EmailScanner.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\Email Scanner\EmailScanner.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\Mail Sorter\Email Sorter Pro\frmEmailSorterPro.Designer.vb">
      <DependentUpon>frmEmailSorterPro.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\Mail Sorter\Email Sorter Pro\frmEmailSorterPro.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\Mail Sorter\frmEmailSorter.Designer.vb">
      <DependentUpon>frmEmailSorter.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\Mail Sorter\frmEmailSorter.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\MailCatcher\MailCatcher.Designer.vb">
      <DependentUpon>MailCatcher.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\MailCatcher\MailCatcher.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\ScrapedEmailResult.vb" />
    <Compile Include="Quick Luncher\SMTP Tester\frmSmtpTester.Designer.vb">
      <DependentUpon>frmSmtpTester.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\SMTP Tester\frmSmtpTester.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\Validation\Office 365\frmValidationOffice365.Designer.vb">
      <DependentUpon>frmValidationOffice365.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\Validation\Office 365\frmValidationOffice365.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ranking SMTP\frmAddHostToSMTP.Designer.vb">
      <DependentUpon>frmAddHostToSMTP.vb</DependentUpon>
    </Compile>
    <Compile Include="Ranking SMTP\frmAddHostToSMTP.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Remove.Designer.vb">
      <DependentUpon>Remove.vb</DependentUpon>
    </Compile>
    <Compile Include="Remove.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="remove2.Designer.vb">
      <DependentUpon>remove2.vb</DependentUpon>
    </Compile>
    <Compile Include="remove2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Resources\Class QRCode\QRCodeGenerator.vb" />
    <Compile Include="Resources\frmEditLetter.Designer.vb">
      <DependentUpon>frmEditLetter.vb</DependentUpon>
    </Compile>
    <Compile Include="Resources\frmEditLetter.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special Tools\frmBase64.Designer.vb">
      <DependentUpon>frmBase64.vb</DependentUpon>
    </Compile>
    <Compile Include="Special Tools\frmBase64.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special Tools\frmDakEncryption.Designer.vb">
      <DependentUpon>frmDakEncryption.vb</DependentUpon>
    </Compile>
    <Compile Include="Special Tools\frmDakEncryption.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special Tools\frmDragonLetter.Designer.vb">
      <DependentUpon>frmDragonLetter.vb</DependentUpon>
    </Compile>
    <Compile Include="Special Tools\frmDragonLetter.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special Tools\frmEncodedLinkWolf.Designer.vb">
      <DependentUpon>frmEncodedLinkWolf.vb</DependentUpon>
    </Compile>
    <Compile Include="Special Tools\frmEncodedLinkWolf.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special Tools\frmGhost.Designer.vb">
      <DependentUpon>frmGhost.vb</DependentUpon>
    </Compile>
    <Compile Include="Special Tools\frmGhost.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special Tools\frmLetterMakerST.Designer.vb">
      <DependentUpon>frmLetterMakerST.vb</DependentUpon>
    </Compile>
    <Compile Include="Special Tools\frmLetterMakerST.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special Tools\frmVenomENCLetter.Designer.vb">
      <DependentUpon>frmVenomENCLetter.vb</DependentUpon>
    </Compile>
    <Compile Include="Special Tools\frmVenomENCLetter.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Support\frmSupportBSV.Designer.vb">
      <DependentUpon>frmSupportBSV.vb</DependentUpon>
    </Compile>
    <Compile Include="Support\frmSupportBSV.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="System\frmMain.Designer.vb">
      <DependentUpon>frmMain.vb</DependentUpon>
    </Compile>
    <Compile Include="System\frmMain.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Tag\TagBestSender.Designer.vb">
      <DependentUpon>TagBestSender.vb</DependentUpon>
    </Compile>
    <Compile Include="Tag\TagBestSender.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Update Sender\AutoUpdate.vb" />
    <Compile Include="Update Sender\frmDownloadBSV.Designer.vb">
      <DependentUpon>frmDownloadBSV.vb</DependentUpon>
    </Compile>
    <Compile Include="Update Sender\frmDownloadBSV.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Utilities\DeleteDuplicatemail.Designer.vb">
      <DependentUpon>DeleteDuplicatemail.vb</DependentUpon>
    </Compile>
    <Compile Include="Utilities\DeleteDuplicatemail.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Modules and Classes\IPInfo.vb" />
    <Compile Include="New Activation\frmActivationInfo.designer.vb">
      <DependentUpon>frmActivationInfo.vb</DependentUpon>
    </Compile>
    <Compile Include="New Activation\frmActivationInfo.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="New Activation\GlobalVariables.vb" />
    <Compile Include="Office365\Config.vb" />
    <Compile Include="Office365\Http.vb" />
    <Compile Include="Office365\Stats.vb" />
    <Compile Include="Quick Luncher\Email Verification\EmailValidation.vb" />
    <Compile Include="Quick Luncher\Email Verification\FrmEmailValidation.designer.vb">
      <DependentUpon>FrmEmailValidation.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\Email Verification\FrmEmailValidation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\Mail Sorter\frmMaillist.Designer.vb">
      <DependentUpon>frmMaillist.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\Mail Sorter\frmMaillist.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\Mail Sorter\DataModel.vb" />
    <Compile Include="Quick Luncher\Mail Sorter\functions.vb" />
    <Compile Include="Quick Luncher\SMTP Tester\frmSMTPTester_Details.Designer.vb">
      <DependentUpon>frmSMTPTester_Details.vb</DependentUpon>
    </Compile>
    <Compile Include="Quick Luncher\SMTP Tester\frmSMTPTester_Details.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Quick Luncher\SMTP Tester\SmtpTester.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Modules and Classes\Resolution.vb" />
    <Compile Include="Utilities\frmGoogleCaptcha.Designer.vb">
      <DependentUpon>frmGoogleCaptcha.vb</DependentUpon>
    </Compile>
    <Compile Include="Utilities\frmGoogleCaptcha.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Utilities\frmImageResize.designer.vb">
      <DependentUpon>frmImageResize.vb</DependentUpon>
    </Compile>
    <Compile Include="Utilities\frmImageResize.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Utilities\frmRedirectLink .Designer.vb">
      <DependentUpon>frmRedirectLink .vb</DependentUpon>
    </Compile>
    <Compile Include="Utilities\frmRedirectLink .vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Utilities\frmQuickAccountCheck.Designer.vb">
      <DependentUpon>frmQuickAccountCheck.vb</DependentUpon>
    </Compile>
    <Compile Include="Utilities\frmQuickAccountCheck.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="New Activation\frmSplashScreen_New.designer.vb">
      <DependentUpon>frmSplashScreen_New.vb</DependentUpon>
    </Compile>
    <Compile Include="New Activation\frmSplashScreen_New.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Utilities\frmZeroPass.Designer.vb">
      <DependentUpon>frmZeroPass.vb</DependentUpon>
    </Compile>
    <Compile Include="Utilities\frmZeroPass.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Utilities\frm_En_PHP.Designer.vb">
      <DependentUpon>frm_En_PHP.vb</DependentUpon>
    </Compile>
    <Compile Include="Utilities\frm_En_PHP.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="VIP ULTRA\AolVerify.Designer.vb">
      <DependentUpon>AolVerify.vb</DependentUpon>
    </Compile>
    <Compile Include="VIP ULTRA\AolVerify.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="VIP ULTRA\frmVIPULTRA.Designer.vb">
      <DependentUpon>frmVIPULTRA.vb</DependentUpon>
    </Compile>
    <Compile Include="VIP ULTRA\frmVIPULTRA.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="VIP ULTRA\YahooVerify.Designer.vb">
      <DependentUpon>YahooVerify.vb</DependentUpon>
    </Compile>
    <Compile Include="VIP ULTRA\YahooVerify.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Attachment Encoded Pro\frmAttPro.resx">
      <DependentUpon>frmAttPro.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Email Sender\frmAddToAllAccounts.resx">
      <DependentUpon>frmAddToAllAccounts.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Email Sender\frmEmailSender.resx">
      <DependentUpon>frmEmailSender.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Email Sender\frmLetterinorSP.resx">
      <DependentUpon>frmLetterinorSP.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Email Sender\frmSeeHTML.resx">
      <DependentUpon>frmSeeHTML.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Email Sender\frmTestSmtpAccounts.resx">
      <DependentUpon>frmTestSmtpAccounts.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Email Sorter Domain\frmEMailSorterDomain.resx">
      <DependentUpon>frmEMailSorterDomain.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Encoded Link\frmEncodeLink.resx">
      <DependentUpon>frmEncodeLink.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Encoded Link\frmlinktoAtt.resx">
      <DependentUpon>frmlinktoAtt.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmNew.resx">
      <DependentUpon>frmNew.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IMAP\frmIMAP.resx">
      <DependentUpon>frmIMAP.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IMAP\frmDataTransfer_Wait.resx">
      <DependentUpon>frmDataTransfer_Wait.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IMAP\frmIMAP_Wait.resx">
      <DependentUpon>frmIMAP_Wait.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IMAP\king Get Mail\IMAP_BSV.resx">
      <DependentUpon>IMAP_BSV.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Letters\frmLetterEncoddvb.resx">
      <DependentUpon>frmLetterEncoddvb.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="New Activation\frmActivate_New.resx">
      <DependentUpon>frmActivate_New.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="New Services\frmRedirectSpider.resx">
      <DependentUpon>frmRedirectSpider.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="New Services\frmRedirectWithPassword.resx">
      <DependentUpon>frmRedirectWithPassword.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="New Services\frmHideLink.resx">
      <DependentUpon>frmHideLink.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="New Services\frmMicrosoftRedirect.resx">
      <DependentUpon>frmMicrosoftRedirect.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="New Services\frmMultiLinker.resx">
      <DependentUpon>frmMultiLinker.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="New Services\frmPassMicrosoft.resx">
      <DependentUpon>frmPassMicrosoft.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="New Services\frmReAuthenticationAtt.resx">
      <DependentUpon>frmReAuthenticationAtt.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="New Services\frmRedirectPuzzle.resx">
      <DependentUpon>frmRedirectPuzzle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Office365\frmCheckOffce356.resx">
      <DependentUpon>frmCheckOffce356.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\Email Scanner\frmAddAttachment.resx">
      <DependentUpon>frmAddAttachment.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\Email Scanner\Select SMTP\frmaddAttach.resx">
      <DependentUpon>frmaddAttach.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\Email Scanner\Select SMTP\frmAddSmtpServer.resx">
      <DependentUpon>frmAddSmtpServer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\Email Scanner\Select SMTP\frmSelectSMTP.resx">
      <DependentUpon>frmSelectSMTP.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\Email Scanner\EmailScanner.resx">
      <DependentUpon>EmailScanner.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\Mail Sorter\Email Sorter Pro\frmEmailSorterPro.resx">
      <DependentUpon>frmEmailSorterPro.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\Mail Sorter\frmEmailSorter.resx">
      <DependentUpon>frmEmailSorter.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\MailCatcher\MailCatcher.resx">
      <DependentUpon>MailCatcher.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\SMTP Tester\frmSmtpTester.resx">
      <DependentUpon>frmSmtpTester.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\Validation\Office 365\frmValidationOffice365.resx">
      <DependentUpon>frmValidationOffice365.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ranking SMTP\frmAddHostToSMTP.resx">
      <DependentUpon>frmAddHostToSMTP.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Remove.resx">
      <DependentUpon>Remove.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="remove2.resx">
      <DependentUpon>remove2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\frmEditLetter.resx">
      <DependentUpon>frmEditLetter.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special Tools\frmBase64.resx">
      <DependentUpon>frmBase64.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special Tools\frmDakEncryption.resx">
      <DependentUpon>frmDakEncryption.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special Tools\frmDragonLetter.resx">
      <DependentUpon>frmDragonLetter.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special Tools\frmEncodedLinkWolf.resx">
      <DependentUpon>frmEncodedLinkWolf.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special Tools\frmGhost.resx">
      <DependentUpon>frmGhost.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special Tools\frmLetterMakerST.resx">
      <DependentUpon>frmLetterMakerST.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special Tools\frmVenomENCLetter.resx">
      <DependentUpon>frmVenomENCLetter.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Support\frmSupportBSV.resx">
      <DependentUpon>frmSupportBSV.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="System\frmMain.resx">
      <DependentUpon>frmMain.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Tag\TagBestSender.resx">
      <DependentUpon>TagBestSender.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Update Sender\frmDownloadBSV.resx">
      <DependentUpon>frmDownloadBSV.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Utilities\DeleteDuplicatemail.resx">
      <DependentUpon>DeleteDuplicatemail.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="New Activation\frmActivationInfo.resx">
      <DependentUpon>frmActivationInfo.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\Email Verification\FrmEmailValidation.resx">
      <DependentUpon>FrmEmailValidation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\Mail Sorter\frmMaillist.resx">
      <DependentUpon>frmMaillist.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Quick Luncher\SMTP Tester\frmSMTPTester_Details.resx">
      <DependentUpon>frmSMTPTester_Details.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>PublicVbMyResourcesResXFileCodeGenerator</Generator>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Utilities\frmGoogleCaptcha.resx">
      <DependentUpon>frmGoogleCaptcha.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Utilities\frmImageResize.resx">
      <DependentUpon>frmImageResize.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Utilities\frmRedirectLink .resx">
      <DependentUpon>frmRedirectLink .vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Utilities\frmQuickAccountCheck.resx">
      <DependentUpon>frmQuickAccountCheck.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="New Activation\frmSplashScreen_New.resx">
      <DependentUpon>frmSplashScreen_New.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Utilities\frmZeroPass.resx">
      <DependentUpon>frmZeroPass.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Utilities\frm_En_PHP.resx">
      <DependentUpon>frm_En_PHP.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="VIP ULTRA\AolVerify.resx">
      <DependentUpon>AolVerify.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="VIP ULTRA\frmVIPULTRA.resx">
      <DependentUpon>frmVIPULTRA.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="VIP ULTRA\YahooVerify.resx">
      <DependentUpon>YahooVerify.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\app.manifest" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\TagLinkImage.png" />
    <None Include="Resources\task.svg" />
    <None Include="Resources\sendbackward.svg" />
    <None Include="Resources\updatetableofcontents.svg" />
    <None Include="Resources\viewmergeddata.svg" />
    <None Include="Resources\paste_16x16.png" />
    <None Include="Resources\paste_32x32.png" />
    <None Include="Resources\trash_16x165.png" />
    <None Include="Resources\trash_32x322.png" />
    <None Include="Resources\snaptogglefieldhighlighting.svg" />
    <None Include="Resources\sendmht.svg" />
    <None Include="Resources\gaugestylelinearvertical_16x16.png" />
    <None Include="Resources\gaugestylelinearhorizontal_16x16.png" />
    <None Include="Resources\rulervertical.svg" />
    <None Include="Resources\rulerhorizontal.svg" />
    <None Include="Resources\gettingstarted2.svg" />
    <None Include="Resources\link2.svg" />
    <None Include="Resources\link1.svg" />
    <None Include="Resources\status_16x16.png" />
    <None Include="Resources\status_32x32.png" />
    <None Include="Resources\imageimport.svg" />
    <None Include="Resources\showlegend.svg" />
    <None Include="Resources\snapinsertheader2.svg" />
    <None Include="Resources\StopPoint.svg" />
    <None Include="Resources\stop_16x16.png" />
    <None Include="Resources\stop_32x32.png" />
    <None Include="Resources\gettingstarted1.svg" />
    <None Include="Resources\gettingstarted.svg" />
    <None Include="Resources\reddatabargradient.svg" />
    <None Include="Resources\servermode_32x32.png" />
    <None Include="Resources\servermode_16x16.png" />
    <None Include="Resources\security_key1.svg" />
    <None Include="Resources\trash_32x321.png" />
    <None Include="Resources\trash_16x164.png" />
    <None Include="Resources\usergroup_32x32.png" />
    <None Include="Resources\usergroup_16x16.png" />
    <None Include="Resources\mailmerge1.svg" />
    <None Include="Resources\mailmerge.svg" />
    <None Include="Resources\inbox_32x32.png" />
    <None Include="Resources\inbox_16x16.png" />
    <None Include="Resources\removeheader.svg" />
    <None Include="Resources\snapinsertfooter.svg" />
    <None Include="Resources\trash_16x163.png" />
    <None Include="Resources\trash_16x162.png" />
    <None Include="Resources\showtestreport_16x16.png" />
    <None Include="Resources\trash_16x161.png" />
    <None Include="Resources\trash_16x16.png" />
    <None Include="Resources\trash_32x32.png" />
    <None Include="Resources\youtube_16x16.png" />
    <None Include="Resources\youtube_32x32.png" />
    <None Include="Resources\hyperlink_16x16.png" />
    <None Include="Resources\hyperlink_32x321.png" />
    <None Include="Resources\support_16x16.png" />
    <None Include="Resources\support_32x32.png" />
    <None Include="Resources\security_key.svg" />
    <None Include="Resources\home_32x32.png" />
    <None Include="Resources\open21.svg" />
    <None Include="Resources\open2.svg" />
    <None Include="Resources\trackingchanges_locktracking3.svg" />
    <None Include="Resources\trackingchanges_locktracking2.svg" />
    <None Include="Resources\trackingchanges_locktracking1.svg" />
    <None Include="Resources\trackingchanges_locktracking.svg" />
    <None Include="Resources\viewtablegridlines.svg" />
    <None Include="Resources\insertlistbox.svg" />
    <None Include="Resources\sendpdf_32x32.png" />
    <None Include="Resources\snapinsertheader1.svg" />
    <None Include="Resources\snapinsertheader.svg" />
    <None Include="Resources\opencalendar.svg" />
    <None Include="Resources\saveall_32x32.png" />
    <None Include="Resources\hyperlink_32x32.png" />
    <None Include="Resources\pause_16x16.png" />
    <None Include="Resources\play_16x16.png" />
    <None Include="Resources\refreshpivottable_32x32.png" />
    <None Include="Resources\sendbackward_16x16.png" />
    <None Include="Resources\upload_16x16.png" />
    <None Include="Resources\hyperlink.svg" />
    <None Include="Resources\Link.svg" />
    <None Include="Resources\notifcationAnobis.png" />
    <None Include="icon\QRcodeTelegram1.png" />
    <None Include="icon\QRcodeICQ1.png" />
    <None Include="icon\QRcodeSkype1.png" />
    <None Include="icon\1000.png" />
    <None Include="icon\usernamee.png" />
    <None Include="icon\usernamee2 %281%29.png" />
    <None Include="icon\prossec.gif" />
    <None Include="icon\Mailextractpng.png" />
    <None Include="Resources\Logoicon.png" />
    <None Include="Resources\Best Sender VIP.png" />
    <None Include="icon\Send.png" />
    <None Include="icon\Skype.png" />
    <None Include="icon\PNGSMTPOn.png" />
    <None Include="icon\PNGSMTPONG.png" />
    <None Include="icon\PNGSMTPHide.png" />
    <None Include="Resources\PNGUtilities.png" />
    <None Include="Resources\PNGEncodedLetter.png" />
    <None Include="Resources\PNGIMAP.png" />
    <None Include="Resources\PNGSend.png" />
    <None Include="icon\place_marker_100px.png" />
    <None Include="icon\HIDE_place_marker_100px.png" />
    <None Include="Resources\restart.png" />
    <None Include="icon\txtLetter.txt" />
    <None Include="icon\New.png" />
    <None Include="icon\NewHide.png" />
    <None Include="icon\AMazon.PNG" />
    <None Include="icon\biglobejp.PNG" />
    <None Include="icon\GMX.PNG" />
    <None Include="icon\Godaddy.PNG" />
    <None Include="icon\ionos.PNG" />
    <None Include="icon\Office365.PNG" />
    <None Include="icon\Outlook.png" />
    <None Include="icon\rackspace.PNG" />
    <None Include="icon\zambrea.PNG" />
    <None Include="icon\Yahoo.PNG" />
    <None Include="icon\timeOut.PNG.png" />
    <None Include="icon\bad.PNG.png" />
    <None Include="icon\Good.PNG.png" />
    <None Include="Resources\checked.PNG.png" />
    <None Include="Resources\Images\ovhcloud.png" />
    <None Include="Resources\Utilities_PNG.png" />
    <None Include="Resources\EncodedLetter_PNG.png" />
    <None Include="Resources\IMAP_PNG.png" />
    <None Include="Resources\Send_PNG.png" />
    <None Include="icon\IMAP_New.png" />
    <None Include="icon\Logo Best.png" />
    <None Include="icon\ImageCHeckSMTPHide.png" />
    <None Include="icon\ImageCHeckSMTPHideOn.png" />
    <None Include="icon\image_locked.png" />
    <None Include="icon\IMAGE_Email_Extractor_On.png" />
    <None Include="icon\image_Email_From_Scanner_.png" />
    <None Include="icon\image_Email_From_Scanner_Hide.png" />
    <None Include="icon\IMAGE_Email_Sorter_Hide.png" />
    <None Include="icon\IMAGE_Email_Sorter_On.png" />
    <None Include="icon\image_Email_Vaildator_Hide.png" />
    <None Include="icon\image_Email_Vaildator_On.png" />
    <None Include="icon\IMAGE_SMTP_order_Hide.png" />
    <None Include="icon\IMAGE_SMTP_order_On.png" />
    <None Include="icon\IMAGE_TGB_BSV_Hide.png" />
    <None Include="icon\IMAGE_TGB_BSV_On.png" />
    <None Include="icon\IMAGE_Check_Office365_Hide.png" />
    <None Include="icon\IMAGE_Check_Office365_On.png" />
    <None Include="icon\IMAGE_Email_Extractor_Hide.png" />
    <None Include="icon\IMAGE_Letter_ Designer_On.png" />
    <None Include="icon\IMAGE_Letter_ Designer_Hide.png" />
    <None Include="icon\IMAGE_Email_Extractor_Pro_Hide.png" />
    <None Include="icon\IMAGE_Email_Extractor_Pro_On.png" />
    <None Include="icon\IMAGE_Extract_Domain_Last_Hide.png" />
    <None Include="icon\IMAGE_Extract_Domain_Last_On.png" />
    <None Include="icon\Cloudflare_Logo.png" />
    <None Include="icon\Google Captcha_Logo.png" />
    <None Include="icon\Logo_Microsoft.png" />
    <None Include="icon\Logo_Puzzle.png" />
    <None Include="icon\Redirect_Logo.png" />
    <None Include="icon\Authentication _image.png" />
    <None Include="icon\Redirect_VIP.png" />
    <None Include="icon\LogoBSV.png" />
    <None Include="icon\Logo_VIP_Redirect.png" />
    <None Include="icon\image_Multi Linker.png" />
    <None Include="icon\empty_trash.png" />
    <None Include="Best icon\BKBSV2025.png" />
    <None Include="Best icon\Letter_Encoded_Hide.png" />
    <None Include="Best icon\Letter_Encoded_On.png" />
    <None Include="Best icon\Location_Hide.png" />
    <None Include="Best icon\Location_On.png" />
    <None Include="Best icon\New_Hide.png" />
    <None Include="Best icon\New_On.png" />
    <None Include="Best icon\Redirect_Link_Hide.png" />
    <None Include="Best icon\Redirect_Link_On.png" />
    <None Include="Best icon\Send_Mail_Hide.png" />
    <None Include="Best icon\Send_Mail_On.png" />
    <None Include="Best icon\Services_Hide.png" />
    <None Include="Best icon\Services_On.png" />
    <None Include="Best icon\User_Hide.png" />
    <None Include="Best icon\User_On.png" />
    <None Include="Best icon\red-flag.png" />
    <None Include="Best icon\restart.png" />
    <None Include="Best icon\restartHide.png" />
    <None Include="Best icon\Support_on.png" />
    <None Include="Best icon\Support_Hide.png" />
    <None Include="Best icon\Skype.png" />
    <None Include="Best icon\Skype_Hide.png" />
    <None Include="Best icon\Telegram_Hide.png" />
    <None Include="Best icon\Telegram_On.png" />
    <None Include="Best icon\YouTube_Hide.png" />
    <None Include="Best icon\YouTube_On.png" />
    <None Include="Best icon\Site_Hide.png" />
    <None Include="Best icon\Site_On.png" />
    <None Include="Best icon\Caver.png" />
    <None Include="Best icon\BooCardGold.png" />
    <None Include="Best icon\BestTools_Hide.png" />
    <None Include="Best icon\BestTools_On.png" />
    <None Include="Best icon\Redirect_Logo.png" />
    <None Include="Best icon\Ghost_Image.png" />
    <None Include="Best icon\Gost.png" />
    <None Include="Best icon\ST_1.png" />
    <None Include="Best icon\ST_2.png" />
    <None Include="Best icon\VenomEN.png" />
    <None Include="Best icon\VenomImage.png" />
    <None Include="Best icon\1.html" />
    <None Include="Best icon\LetterPNG.png" />
    <None Include="Best icon\txtC.png" />
    <None Include="Best icon\txtL.png" />
    <None Include="Best icon\txtR.png" />
    <None Include="Best icon\Letter_Maker_PNG.png" />
    <None Include="Best icon\Image_777_Hide.png" />
    <None Include="Best icon\VIP_LEGEND_.png" />
    <None Include="Best icon\VIP_PRIME_PNG.png" />
    <None Include="Best icon\VIP_ULTRA_PNG.png" />
    <None Include="Best icon\Dragon_Image.png" />
    <None Include="Best icon\Dragon.png" />
    <None Include="Best icon\Wolf_image.png" />
    <None Include="Best icon\DarkEN.png" />
    <None Include="Best icon\Base64_image.png" />
    <None Include="Best icon\Base64EN.png" />
    <None Include="Best icon\Wolf_EN.png" />
    <None Include="Best icon\Dark_image_New.png" />
    <None Include="Best icon\Redirect with Pass.png" />
    <None Include="Best icon\Aol_image.png" />
    <None Include="Best icon\Yahoo_image.png" />
    <None Include="Best icon\PrivteToolsBSV.png" />
    <None Include="Best icon\AolPanel.png" />
    <None Include="Best icon\YahooPanel.png" />
    <None Include="Best icon\Spider_Redirect.png" />
    <Content Include="BSV.ico" />
    <None Include="icon\HideLinkHide.png" />
    <None Include="icon\HideLinkOn.png" />
    <None Include="icon\IMAGE_HideLink.png" />
    <None Include="icon\DoneBuild.png" />
    <None Include="icon\linkCool.png" />
    <None Include="icon\CoolLinkHide.png" />
    <None Include="icon\EN_Att_Pro_Hide.png" />
    <None Include="icon\EN_Att_Pro_On.png" />
    <None Include="icon\letter.png" />
    <None Include="icon\int_no.png" />
    <None Include="icon\IMAGE_Encoded_PHP_Hide.png" />
    <None Include="icon\IMAGE_Encoded_PHP_On.png" />
    <None Include="icon\Image_777_Hide.png" />
    <None Include="icon\Image_777_On.png" />
    <None Include="icon\skull.png" />
    <None Include="icon\cloudflare_image.png" />
    <None Include="icon Main\alarm.png" />
    <None Include="icon Main\Attimage.png" />
    <None Include="icon Main\BKSettings.png" />
    <None Include="icon Main\BSSenderImage.png" />
    <None Include="icon Main\BSVNewtools.png" />
    <None Include="icon Main\Emailimage.png" />
    <None Include="icon Main\EncodedLetterIMAGE.png" />
    <None Include="icon Main\LinkEN.png" />
    <None Include="icon Main\mailSort.png" />
    <None Include="icon Main\QrcodeBTN.png" />
    <None Include="icon Main\RedirectBrowser.png" />
    <None Include="icon Main\RestartApp.png" />
    <None Include="icon Main\ServicesBSV.png" />
    <None Include="icon Main\smtpServer.png" />
    <None Include="icon Main\smtpserverOlder.png" />
    <None Include="icon Main\smtpServerOne.png" />
    <None Include="icon Main\smtptestacc.png" />
    <None Include="icon Main\Stop_Image.png" />
    <None Include="icon Main\Reset_Image.png" />
    <None Include="icon Main\Save_Image.png" />
    <None Include="icon Main\Start_Image.png" />
    <None Include="icon Main\trash_Image.png" />
    <None Include="icon Main\ScannerImage.png" />
    <None Include="icon Main\LogoHeader.png" />
    <None Include="icon Main\SupportImage.png" />
    <None Include="icon Main\attached-file.png" />
    <None Include="icon Main\Menu.png" />
    <None Include="icon Main\discordImage.png" />
    <None Include="icon Main\TelegramImage.png" />
    <None Include="icon Main\signal.png" />
    <None Include="icon Main\Header.png" />
    <None Include="icon Main\Start16.png" />
    <None Include="icon Main\trash_32x32.png" />
    <None Include="icon Main\qr_Save.png" />
    <None Include="icon Main\AttachmentPaner.png" />
    <None Include="icon Main\ButtonEncoded16x16.png" />
    <None Include="icon Main\ButtonEncoded32x32.png" />
    <None Include="icon Main\ChangeLetter.png" />
    <None Include="icon Main\ChangeLetter16x16.png" />
    <None Include="icon Main\ConvertLinkToAttachment16x16.png" />
    <None Include="icon Main\ConvertLinkToAttachment32x32.png" />
    <None Include="icon Main\DeleteDuplicatemail16x16.png" />
    <None Include="icon Main\DeleteDuplicatemail32x32.png" />
    <None Include="icon Main\EmailExtractor16x16.png" />
    <None Include="icon Main\EmailExtractor32x32.png" />
    <None Include="icon Main\EmailFromScanner16x16.png" />
    <None Include="icon Main\EmailFromScanner32x32.png" />
    <None Include="icon Main\EmailSendingFormat16x16.png" />
    <None Include="icon Main\EmailSendingFormat32x32.png" />
    <None Include="icon Main\EmailSorter16x16.png" />
    <None Include="icon Main\EmailSorter32x32.png" />
    <None Include="icon Main\EmailValidation16x16.png" />
    <None Include="icon Main\EmailValidation32x32.png" />
    <None Include="icon Main\Encoded Link16x16.png" />
    <None Include="icon Main\Encoded Link32x32.png" />
    <None Include="icon Main\EncodedAttachment16x16.png" />
    <None Include="icon Main\EncodedAttachment32x32.png" />
    <None Include="icon Main\EncodedLetter16x16.png" />
    <None Include="icon Main\EncodedLetter32x32.png" />
    <None Include="icon Main\ExtractDomain16x16.png" />
    <None Include="icon Main\ExtractDomain32x32.png" />
    <None Include="icon Main\HideLink16x16.png" />
    <None Include="icon Main\HideLink32x32.png" />
    <None Include="icon Main\html16x16.png" />
    <None Include="icon Main\html32x32.png" />
    <None Include="icon Main\image16x16.png" />
    <None Include="icon Main\Image32x32.png" />
    <None Include="icon Main\inbox16x16.png" />
    <None Include="icon Main\inbox32x32.png" />
    <None Include="icon Main\Letter16x16.png" />
    <None Include="icon Main\Letter32x32.png" />
    <None Include="icon Main\LetterSettings16x16.png" />
    <None Include="icon Main\LetterSettings32x32.png" />
    <None Include="icon Main\LetterTools16x16.png" />
    <None Include="icon Main\LetterTools32x32.png" />
    <None Include="icon Main\LinkEN16x16.png" />
    <None Include="icon Main\LinkEN32x32.png" />
    <None Include="icon Main\New16x16.png" />
    <None Include="icon Main\New32x32.png" />
    <None Include="icon Main\Office365CheckAcccount16x16.png" />
    <None Include="icon Main\Office365CheckAcccount32x32.png" />
    <None Include="icon Main\PDF16x16.png" />
    <None Include="icon Main\PDF32x32.png" />
    <None Include="icon Main\php16x16.png" />
    <None Include="icon Main\php32x32.png" />
    <None Include="icon Main\Redirect16x16.png" />
    <None Include="icon Main\Redirect32x32.png" />
    <None Include="icon Main\RemovePassFromEmail16x16.png" />
    <None Include="icon Main\RemovePassFromEmail32x32.png" />
    <None Include="icon Main\Send16x16.png" />
    <None Include="icon Main\Sender32x32.png" />
    <None Include="icon Main\SMTPTester16x16.png" />
    <None Include="icon Main\SMTPTester32x32.png" />
    <None Include="icon Main\sql.png" />
    <None Include="icon Main\trash16x16.png" />
    <None Include="icon Main\trash32x32.png" />
    <None Include="icon Main\add32x32.png" />
    <None Include="icon Main\add16x16.png" />
    <None Include="icon Main\Menu16x16.png" />
    <None Include="icon Main\Menu32x32.png" />
    <None Include="icon Main\phpLaft16x16.png" />
    <None Include="icon Main\phpLaft32x32.png" />
    <None Include="icon Main\copy32x32.png" />
    <None Include="icon Main\copy16x16.png" />
    <None Include="icon Main\SMTP16x16.png" />
    <None Include="icon Main\SMTP32x32.png" />
    <None Include="icon Main\Stop16x16.png" />
    <None Include="icon Main\Stop32x32.png" />
    <None Include="icon Main\Transfer16x16.png" />
    <None Include="icon Main\Transfer32x32.png" />
    <None Include="icon Main\Change16x16.png" />
    <None Include="icon Main\Change32x32.png" />
    <None Include="icon Main\ChangeText16x16.png" />
    <None Include="icon Main\ChangeText32x32.png" />
    <None Include="icon Main\settings16x16.png" />
    <None Include="icon Main\settings32x32.png" />
    <None Include="icon Main\open16x16.png" />
    <None Include="icon Main\open32x32.png" />
    <None Include="icon Main\Start_Image16x16.png" />
    <None Include="icon Main\Start_Image32x32.png" />
    <None Include="icon Main\smtptestaccount16x16.png" />
    <None Include="icon Main\smtptestaccount32x32.png" />
    <None Include="icon Main\discord16x16.png" />
    <None Include="icon Main\discord32x32.png" />
    <None Include="icon Main\shopping 16x16.png" />
    <None Include="icon Main\shopping 32x32.png" />
    <None Include="icon Main\signal 32x32.png" />
    <None Include="icon Main\signal16x16.png" />
    <None Include="icon Main\telegram16x16.png" />
    <None Include="icon Main\telegram32x32.png" />
    <None Include="icon Main\web16x16.png" />
    <None Include="icon Main\web23x23.png" />
    <None Include="icon Main\youtube16x16.png" />
    <None Include="icon Main\youtube32x32.png" />
    <None Include="icon Main\default32x32.png" />
    <None Include="icon Main\default16x16.png" />
    <None Include="icon Main\RestartBSV32x32.png" />
    <None Include="icon Main\RestartBSV16x16.png" />
    <None Include="icon Main\LinkDomain16x16.png" />
    <None Include="icon Main\LinkDomain32x32.png" />
    <None Include="icon Main\pending32x32.png" />
    <None Include="icon Main\pending16x16.png" />
    <None Include="icon Main\Error16x16.png" />
    <None Include="icon Main\Error32x32.png" />
    <None Include="icon Main\EmilSorter\16x16\10MinMail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\12616x16.png" />
    <None Include="icon Main\EmilSorter\16x16\16316x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Afterlogic16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Airmail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Alice16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Aliyun16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\amazon16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\anazana16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Aol16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\aruba16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\AT&amp;T16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Autistici16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\bluehost16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Canary16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\chinaemail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\ClubInternet16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\cn4e16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Cock-li16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Comcast16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\CommuniG16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Disroot16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\domainloca-host16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\DreamHost16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Earthlink16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\eM-Client16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\FakeMail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\FastMail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\FirstClass16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Freenet16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Frontapp16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\global-mail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\GMX16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\GoDaddy16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Google Workspace16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\GroupWise16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Guerrilla16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Hey-com16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Hi-Net16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\hiworks16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Hotmail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Hushmail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\IceWarp16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\iCloud Mail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\iinet16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\ionos16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Kerio-mail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\KolabNow16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Laposte16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Libero16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Lotus-Notes16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Luxsci16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Mail.com16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Mailbird16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Mailbox-org16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Mailfence16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\mailinator16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Mailplug16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Mail-ru16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Microsoft 36516x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Mimecast16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Missive16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\mweb16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Namecheap16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\naver16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\NetEase16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Newton16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Openmailbox16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Open-Xchange16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Orange.fr16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Other16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Outlook16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Polarismail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Polymail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Postbox16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Posteo16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\ProtonMail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\QQ16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Rackspace 16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Rainloop16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Rediff16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\register-it16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Riseup16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Roundcube16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\RR16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\RunBox16x1616x16.png" />
    <None Include="icon Main\EmilSorter\16x16\RunBox32x3216x16.png" />
    <None Include="icon Main\EmilSorter\16x16\scalix16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Serverdata16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Seznam16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Sina16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Soverin16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Spark-mail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Spike16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\StartMail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Strato16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Superhuman16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Synaq16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Temp16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Terra16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Thexyz16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Thunderbird16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\T-Online16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Tutanota16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Verizon16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\virgin16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Vivaldi-Mail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\walla16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\web.de16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\yandex16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Ziggo16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Zimbra16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Zoho Mail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\BT16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\citadel16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\CounterMail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\gmail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Horde16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\Squirrelmail16x16.png" />
    <None Include="icon Main\EmilSorter\16x16\yahoo16x16.png" />
    <None Include="icon Main\Path16x16.png" />
    <None Include="icon Main\Path32x32.png" />
    <None Include="icon Main\Valid16x16.png" />
    <None Include="icon Main\Valid32x32.png" />
    <None Include="icon Main\LogoLogin.png" />
    <None Include="icon Main\Connect32x32.png" />
    <None Include="icon Main\connect16x16.png" />
    <None Include="icon Main\RustDesk32x32.png" />
    <None Include="icon Main\Anydesk32x32.png" />
    <None Include="icon Main\net framework16x161.png" />
    <None Include="icon Main\net framework32x32.png" />
    <None Include="icon Main\search 16x16.png" />
    <None Include="icon Main\search 32x32.png" />
    <None Include="icon Main\Restart32x32.png" />
    <None Include="icon Main\Restart16x16.png" />
    <None Include="icon Main\QRcodeSender.png" />
    <None Include="icon Main\LogoUpdate.png" />
    <None Include="icon Main\PassImage.png" />
    <None Include="icon Main\UserNameImage.png" />
    <None Include="icon Main\Notworking16x16.png" />
    <None Include="icon Main\Notworking32x32.png" />
    <None Include="icon Main\Base64Image.png" />
    <None Include="icon Main\DarkImageLeter.png" />
    <None Include="icon Main\DragonImageLetter.png" />
    <None Include="icon Main\GhostImage.png" />
    <None Include="icon Main\Exit16x16.png" />
    <None Include="icon Main\Exit32x32.png" />
    <None Include="icon Main\BkPanelButton.png" />
    <None Include="icon Main\VenomENImage.png" />
    <None Include="icon Main\EncodedLinkWolfImage.png" />
    <None Include="icon Main\EncodedLinkImageNew.png" />
    <None Include="icon Main\time16x16.png" />
    <None Include="icon Main\time32x32.png" />
    <None Include="icon Main\777.png" />
    <None Include="icon Main\cloudflare16x16.png" />
    <None Include="icon Main\cloudflare32x32.png" />
    <None Include="icon Main\googlecaptcha32x32.png" />
    <None Include="icon Main\googlecaptcha16x16.png" />
    <None Include="icon Main\Puzzle32x32.png" />
    <None Include="icon Main\Puzzle16x16.png" />
    <None Include="icon Main\microsoft 32x32.png" />
    <None Include="icon Main\Devl77716x16.png" />
    <None Include="icon Main\Devl77732x32.png" />
    <None Include="icon Main\VIPRedirect16x16.png" />
    <None Include="icon Main\VIPRedirect32x32.png" />
    <None Include="icon Main\SpiderRedirect32x32.png" />
    <None Include="icon Main\withPass16x16.png" />
    <None Include="icon Main\withPass32x32.png" />
    <None Include="icon Main\MicrosoftPaner.png" />
    <None Include="icon Main\PuzzelPaner.png" />
    <None Include="icon Main\PasswordPaner.png" />
    <None Include="icon Main\AntiBotRedirectPaner.png" />
    <None Include="icon Main\bot32x32.png" />
    <None Include="icon Main\bot16x16.png" />
    <None Include="icon Main\HideLinkPaner.png" />
    <None Include="icon Main\HideRedirect16x16.png" />
    <None Include="icon Main\HideRedirect32x32.png" />
    <None Include="icon Main\RedirctMicrosoftPaner.png" />
    <None Include="icon Main\VIPPaner.png" />
    <None Include="icon Main\SPiderPanel.png" />
    <None Include="icon Main\LinktoAttachmentPaner.png" />
    <Content Include="LogoApp.ico" />
    <None Include="Resources\Puzzle_image.png" />
    <None Include="Resources\Google_image.png" />
    <None Include="Resources\Microsoft_image.png" />
    <None Include="Resources\cloudflare_image.png" />
    <None Include="Resources\Redirect_VIP_On.png" />
    <None Include="Shop\iconShop\ProgressDownloadMoney.gif" />
    <None Include="Shop\iconShop\imageBitcoin.png" />
    <None Include="Shop\iconShop\imageUSDT.png" />
    <None Include="Shop\iconShop\imageLitcoin.png" />
    <None Include="Shop\iconShop\ErrorPayment.png" />
    <None Include="Shop\iconShop\Wallet.png" />
    <None Include="Shop\iconShop\shoppingOn.png" />
    <None Include="Shop\iconShop\shopping.png" />
    <None Include="Shop\iconShop\money-bag.png" />
    <None Include="Shop\iconShop\imageEmailFromScanner3MonthHide.png" />
    <None Include="Shop\iconShop\imageEmailFromScanner2MonthHide.png" />
    <None Include="Shop\iconShop\imageEmailFromScanner1MonthOn.png" />
    <None Include="Shop\iconShop\imageEmailFromScanner1MonthHide.png" />
    <None Include="Shop\iconShop\imageEmailFromScanne2MonthrOn.png" />
    <None Include="Shop\iconShop\imageBestSenderPlanMonthOn.png" />
    <None Include="Shop\iconShop\imageBestSenderPlanMonthHide.png" />
    <None Include="Shop\iconShop\imageBestSenderPlanLifetimeOn.png" />
    <None Include="Shop\iconShop\imageBestSenderPlanLifetimeHide.png" />
    <None Include="Shop\iconShop\imageIMAP3MonthOn.png" />
    <None Include="Shop\iconShop\imageIMAP3MonthHide.png" />
    <None Include="Shop\iconShop\imageIMAP2MonthOn.png" />
    <None Include="Shop\iconShop\imageIMAP2MonthHide.png" />
    <None Include="Shop\iconShop\imageIMAP1MonthOn.png" />
    <None Include="Shop\iconShop\imageIMAP1MonthHide.png" />
    <None Include="Shop\iconShop\imageEncodeLetter3MonthOn.png" />
    <None Include="Shop\iconShop\imageEncodeLetter3MonthHide.png" />
    <None Include="Shop\iconShop\imageEncodeLetter2MonthOn.png" />
    <None Include="Shop\iconShop\imageEncodeLetter2MonthHide.png" />
    <None Include="Shop\iconShop\imageEncodeLetter1MonthOn.png" />
    <None Include="Shop\iconShop\imageEncodeLetter1MonthHide.png" />
    <None Include="Shop\iconShop\imageEncodeAttachmentLifetimeOn.png" />
    <None Include="Shop\iconShop\imageEncodeAttachmentLifetimeHide.png" />
    <None Include="Shop\iconShop\imageEncodeAttachment2MonthOn.png" />
    <None Include="Shop\iconShop\imageEncodeAttachment2MonthHide.png" />
    <None Include="Shop\iconShop\imageEncodeAttachment1MonthOn.png" />
    <None Include="Shop\iconShop\imageEncodeAttachment1MonthHide.png" />
    <None Include="Shop\iconShop\imageEmaiVerifier3MonthOne.png" />
    <None Include="Shop\iconShop\imageEmaiVerifier3MonthHode.png" />
    <None Include="Shop\iconShop\imageEmaiVerifier2MonthOne.png" />
    <None Include="Shop\iconShop\imageEmaiVerifier2MonthHode.png" />
    <None Include="Shop\iconShop\imageEmaiVerifier1MonthOne.png" />
    <None Include="Shop\iconShop\imageEmaiVerifier1MonthHode.png" />
    <None Include="Shop\iconShop\imageEmailSorter3MonthOn.png" />
    <None Include="Shop\iconShop\imageEmailSorter3MonthHide.png" />
    <None Include="Shop\iconShop\imageEmailSorter2MonthOn.png" />
    <None Include="Shop\iconShop\imageEmailSorter2MonthHide.png" />
    <None Include="Shop\iconShop\imageEmailSorter1MonthOn.png" />
    <None Include="Shop\iconShop\imageEmailSorter1MonthHide.png" />
    <None Include="Shop\iconShop\imageEmailFromScanner3MonthOn.png" />
    <None Include="Resources\logo.png" />
    <None Include="icon\waitActive.gif" />
    <None Include="icon\Customized.png" />
    <None Include="icon\Premium.png" />
    <None Include="icon\linked_file.png" />
    <None Include="icon\pngAttachment.png" />
    <None Include="icon\pngEncodedLink.png" />
    <None Include="icon\pngRidirectLink.png" />
    <None Include="icon\timeConvert.png" />
    <None Include="icon\Lengthlogo.png" />
    <None Include="icon\widthLogo.png" />
    <None Include="icon\imageAccountTesterOn.png" />
    <None Include="icon\imageAttachmentHide.png" />
    <None Include="icon\imageAttachmentOn.png" />
    <None Include="icon\imageEncodeLinkHide.png" />
    <None Include="icon\imageEncodeLinkOne.png" />
    <None Include="icon\imageKinkToAttachmentHide.png" />
    <None Include="icon\imageKinkToAttachmentOn.png" />
    <None Include="icon\imageRisizerHide.png" />
    <None Include="icon\imageRisizerOne.png" />
    <None Include="icon\imageAccountTesterHide.png" />
    <None Include="icon\CopyLinkEncode.png" />
    <None Include="icon\CopyLinkEncodeSender.png" />
    <None Include="icon\linkURLSender.png" />
    <None Include="icon\IMAPOn.png" />
    <None Include="icon\LetterHide.png" />
    <None Include="icon\letterOn.png" />
    <None Include="icon\SendMailHide.png" />
    <None Include="icon\SendMailOn.png" />
    <None Include="icon\UtilitiesHide.png" />
    <None Include="icon\UtilitiesOn.png" />
    <None Include="icon\IMAPHide.png" />
    <None Include="icon\LinkToAttachmentUpdateOn.png" />
    <None Include="icon\LinkToAttachmentUpdateHide.png" />
    <None Include="icon\imageRemovePassOne.png" />
    <None Include="icon\imageRemovePassHide.png" />
    <None Include="icon\Plesewait.gif" />
    <None Include="icon\Waitforlogin.gif" />
    <None Include="icon\New Picture %2895%29.png" />
    <None Include="icon\New Picture %2896%29.png" />
    <None Include="icon\encoded link.png" />
    <None Include="icon\encrypter attachment.png" />
    <None Include="icon\IMAP.png" />
    <None Include="icon\link To Attachment.png" />
    <None Include="icon\VPSBox.png" />
    <None Include="icon\Delete duplicate mail.png" />
    <None Include="icon\Email From Scanner.png" />
    <None Include="icon\Email Sorter.png" />
    <None Include="icon\Email Vaildator.png" />
    <None Include="icon\Encode Letter.png" />
    <None Include="icon\SenderBox.png" />
    <None Include="icon\Descount.png" />
    <None Include="icon\discountbigNew.png" />
    <None Include="icon\shopping-cart .png" />
    <None Include="icon\Logo NewBestSender.png" />
    <None Include="icon\bksender.png" />
    <None Include="icon\bksenderlast.png" />
    <None Include="icon\media .png" />
    <None Include="icon\live-chat .png" />
    <None Include="Resources\Dark_image.png" />
    <None Include="Resources\existlink_16x16.png" />
    <None Include="Resources\delete_hyperlink_16x16.png" />
    <None Include="Resources\alignfloatingobjecttopleft_16x16.png" />
    <None Include="Resources\exporttohtml_16x16.png" />
    <None Include="Resources\deleteheader_32x32.png" />
    <None Include="Resources\export_16x16.png" />
    <None Include="Resources\download_32x32.png" />
    <None Include="Resources\clearall.svg" />
    <None Include="Resources\add_32x32.png" />
    <None Include="Resources\employee_32x32.png" />
    <None Include="Resources\employee_16x16.png" />
    <None Include="Resources\addheader_32x32.png" />
    <None Include="Resources\addheader_32x321.png" />
    <None Include="Resources\actions_trash.svg" />
    <None Include="Resources\addheader_32x322.png" />
    <None Include="Resources\addgroupfooter_16x16.png" />
    <None Include="Resources\add_16x16.png" />
    <None Include="Resources\exporttohtml.svg" />
    <None Include="Resources\attach_32x32.png" />
    <None Include="Resources\attachments.svg" />
    <None Include="Resources\actions_add.svg" />
    <None Include="Resources\enablesearch.svg" />
    <None Include="Resources\add_16x161.png" />
    <None Include="Resources\addfooter_16x16.png" />
    <None Include="Resources\addheader_16x16.png" />
    <None Include="Resources\alignfloatingobjecttopleft.svg" />
    <None Include="Resources\alignfloatingobjectbottomleft.svg" />
    <None Include="Resources\edittask_16x16.png" />
    <None Include="Resources\edittask_32x32.png" />
    <None Include="Resources\export_16x161.png" />
    <None Include="Resources\export_32x32.png" />
    <None Include="Resources\addgroupheader_32x32.png" />
    <None Include="Resources\clear_16x16.png" />
    <None Include="Resources\actions_trash1.svg" />
    <None Include="Resources\actions_trash2.svg" />
    <None Include="Resources\actions_removecircled.svg" />
    <None Include="Resources\documentpdf.svg" />
    <None Include="Resources\exportfile.svg" />
    <Content Include="Resources\Files\knowndomain.txt" />
    <Content Include="Resources\Files\knowndomains.txt" />
    <Content Include="Resources\Files\knownmx.txt" />
    <None Include="Resources\Images\Zoho.png" />
    <None Include="Resources\Images\Zambriamail.png" />
    <None Include="Resources\Images\yandex.png" />
    <None Include="Resources\Images\Yahoo.png" />
    <None Include="Resources\Images\web.de.png" />
    <None Include="Resources\Images\rackspace-logo.png" />
    <None Include="Resources\Images\QQ.png" />
    <None Include="Resources\Images\pngwing.com.png" />
    <None Include="Resources\Images\place_marker_100px.png" />
    <None Include="Resources\Images\Outlook.png" />
    <None Include="Resources\Images\other.png" />
    <None Include="Resources\Images\orange.fr.png" />
    <None Include="Resources\Images\Office 365.png" />
    <None Include="Resources\Images\namecheap.png" />
    <None Include="Resources\Images\max-loader.gif" />
    <None Include="Resources\Images\Mail.Ru.png" />
    <None Include="Resources\Images\M.png" />
    <None Include="Resources\Images\iinet.png" />
    <None Include="Resources\Images\icloud.png" />
    <None Include="Resources\Images\Hotmail.png" />
    <None Include="Resources\Images\hinet.png" />
    <None Include="Resources\Images\HIDE_place_marker_100px.png" />
    <None Include="Resources\Images\Godaddy.png" />
    <None Include="Resources\Images\GMX.png" />
    <None Include="Resources\Images\gmail.png" />
    <None Include="Resources\Images\free-wifi.gif" />
    <None Include="Resources\Images\finding-signatures.gif" />
    <None Include="Resources\Images\failure-icon-29.png" />
    <None Include="Resources\Images\ezgif.com-crop %282%29.gif" />
    <None Include="Resources\Images\earthlink.png" />
    <None Include="Resources\Images\coremail.png" />
    <None Include="Resources\Images\checkmark_96px.png" />
    <None Include="Resources\Images\Cancel_32x321.png" />
    <None Include="Resources\Images\Cancel_32x32.png" />
    <None Include="Resources\Images\Apply_32x321.png" />
    <None Include="Resources\Images\Apply_32x32.png" />
    <None Include="Resources\Images\anazana.png" />
    <None Include="Resources\Images\amazon.png" />
    <None Include="Resources\Images\aliyun.png" />
    <None Include="Resources\Images\1686538.png" />
    <None Include="Resources\Images\263.png" />
    <None Include="Resources\Images\163.png" />
    <None Include="Resources\Images\60b8f057616cb823051a2fda_blog-listing %2811%29.gif" />
    <None Include="Resources\Images\1and1.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.7.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.7.2 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Grpc.Core.2.46.6\build\net45\Grpc.Core.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Grpc.Core.2.46.6\build\net45\Grpc.Core.targets'))" />
    <Error Condition="!Exists('..\packages\IronPdf.Native.Chrome.Windows.2024.3.24\build\net\IronPdf.Native.Chrome.Windows.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\IronPdf.Native.Chrome.Windows.2024.3.24\build\net\IronPdf.Native.Chrome.Windows.targets'))" />
    <Error Condition="!Exists('..\packages\IronSoftware.Native.PdfModel.2024.3.24\build\net\IronSoftware.Native.PdfModel.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\IronSoftware.Native.PdfModel.2024.3.24\build\net\IronSoftware.Native.PdfModel.targets'))" />
    <Error Condition="!Exists('..\packages\NETStandard.Library.2.0.0\build\netstandard2.0\NETStandard.Library.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NETStandard.Library.2.0.0\build\netstandard2.0\NETStandard.Library.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets'))" />
    <Error Condition="!Exists('..\packages\System.Text.Json.6.0.0\build\System.Text.Json.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\System.Text.Json.6.0.0\build\System.Text.Json.targets'))" />
    <Error Condition="!Exists('..\packages\IronPdf.Slim.2024.12.9\build\IronPdf.Slim.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\IronPdf.Slim.2024.12.9\build\IronPdf.Slim.props'))" />
    <Error Condition="!Exists('..\packages\Selenium.WebDriver.ChromeDriver.134.0.6998.16500\build\Selenium.WebDriver.ChromeDriver.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Selenium.WebDriver.ChromeDriver.134.0.6998.16500\build\Selenium.WebDriver.ChromeDriver.targets'))" />
    <Error Condition="!Exists('..\packages\Selenium.WebDriver.GeckoDriver.0.36.0\build\Selenium.WebDriver.GeckoDriver.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Selenium.WebDriver.GeckoDriver.0.36.0\build\Selenium.WebDriver.GeckoDriver.targets'))" />
    <Error Condition="!Exists('..\packages\Selenium.WebDriver.GeckoDriver.Win64.0.36.0\build\Selenium.WebDriver.GeckoDriver.Win64.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Selenium.WebDriver.GeckoDriver.Win64.0.36.0\build\Selenium.WebDriver.GeckoDriver.Win64.targets'))" />
  </Target>
  <Import Project="..\packages\Grpc.Core.2.46.6\build\net45\Grpc.Core.targets" Condition="Exists('..\packages\Grpc.Core.2.46.6\build\net45\Grpc.Core.targets')" />
  <Import Project="..\packages\IronPdf.Native.Chrome.Windows.2024.3.24\build\net\IronPdf.Native.Chrome.Windows.targets" Condition="Exists('..\packages\IronPdf.Native.Chrome.Windows.2024.3.24\build\net\IronPdf.Native.Chrome.Windows.targets')" />
  <Import Project="..\packages\IronSoftware.Native.PdfModel.2024.3.24\build\net\IronSoftware.Native.PdfModel.targets" Condition="Exists('..\packages\IronSoftware.Native.PdfModel.2024.3.24\build\net\IronSoftware.Native.PdfModel.targets')" />
  <Import Project="..\packages\NETStandard.Library.2.0.0\build\netstandard2.0\NETStandard.Library.targets" Condition="Exists('..\packages\NETStandard.Library.2.0.0\build\netstandard2.0\NETStandard.Library.targets')" />
  <Import Project="..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets')" />
  <Import Project="..\packages\System.Text.Json.6.0.0\build\System.Text.Json.targets" Condition="Exists('..\packages\System.Text.Json.6.0.0\build\System.Text.Json.targets')" />
  <Import Project="..\packages\Selenium.WebDriver.ChromeDriver.134.0.6998.16500\build\Selenium.WebDriver.ChromeDriver.targets" Condition="Exists('..\packages\Selenium.WebDriver.ChromeDriver.134.0.6998.16500\build\Selenium.WebDriver.ChromeDriver.targets')" />
  <Import Project="..\packages\Selenium.WebDriver.GeckoDriver.0.36.0\build\Selenium.WebDriver.GeckoDriver.targets" Condition="Exists('..\packages\Selenium.WebDriver.GeckoDriver.0.36.0\build\Selenium.WebDriver.GeckoDriver.targets')" />
  <Import Project="..\packages\Selenium.WebDriver.GeckoDriver.Win64.0.36.0\build\Selenium.WebDriver.GeckoDriver.Win64.targets" Condition="Exists('..\packages\Selenium.WebDriver.GeckoDriver.Win64.0.36.0\build\Selenium.WebDriver.GeckoDriver.Win64.targets')" />
</Project>