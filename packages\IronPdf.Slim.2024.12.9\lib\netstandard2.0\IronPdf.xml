﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
    <assembly>
        <name>IronPdf</name>
    </assembly>
    <members>
        <member name="T:IronPdf.Annotations.PdfAnnotationCollection">
            <summary>
            Collection of annotations for a PDF document
            </summary>
        </member>
        <member name="M:IronPdf.Annotations.PdfAnnotationCollection.#ctor(IronPdf.PdfDocument)">
            <summary>
            Internal constructor for tying an annotation to a document
            </summary>
            <param name="document">Document containing the annotation</param>
        </member>
        <member name="M:IronPdf.Annotations.PdfAnnotationCollection.ClearItems">
            <summary>
            Override Clear() with Interop RemoveAnnotation() through every annotations in the collection.
            </summary>
        </member>
        <member name="M:IronPdf.Annotations.PdfAnnotationCollection.RemoveAllAnnotationsForPage(System.Int32)">
            <summary>
            Remove all annotations in the specific page
            </summary>
            <param name="pageIndex" />
        </member>
        <member name="T:IronPdf.Annotations.TextAnnotation">
            <summary>
            Defines a Sticky-Note style PDF annotation. <seealso cref="T:IronSoftware.Pdfium.PdfiumInterop.UnmanagedDelegates.AddTextAnnotation" /></summary>
        </member>
        <member name="M:IronPdf.Annotations.TextAnnotation.#ctor(IronSoftware.IDocumentId,System.Int32,System.Int32)">
            <summary>
            Internal text annotation constructor which maps the annotation to the document
            </summary>
            <param name="id">Document identifier</param>
            <param name="pageIndex">Page index</param>
            <param name="annotIndex">Annotation index</param>
        </member>
        <member name="M:IronPdf.Annotations.TextAnnotation.#ctor(IronSoftware.IDocumentId,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Internal text annotation constructor which maps the annotation to the document
            </summary>
            <param name="id">Document identifier</param>
            <param name="pageIndex">Page index</param>
            <param name="annotIndex">Annotation index</param>
            <param name="title">Annotation title</param>
            <param name="contents">Annotation contents</param>
        </member>
        <member name="M:IronPdf.Annotations.TextAnnotation.#ctor(System.Int32,System.String,System.String)">
            <summary>
            Define a new annotation with the specified parameters. Annotation must be added to the document via PdfDocument.Annotations
            </summary>
            <param name="PageIndex">Page index</param>
            <param name="Title">Annotation title</param>
            <param name="Contents">Annotation contents</param>
        </member>
        <member name="M:IronPdf.Annotations.TextAnnotation.#ctor(System.Int32)">
            <summary>
            Define a new annotation with the specified parameters. Annotation must be added to the document via PdfDocument.Annotations
            </summary>
            <param name="PageIndex">Page index</param>
        </member>
        <member name="M:IronPdf.Annotations.TextAnnotation.#ctor(System.String,System.String)">
            <summary>
            Define a new annotation with the specified parameters. Annotation must be added to the document via PdfDocument.Annotations
            </summary>
            <param name="Title">Annotation title</param>
            <param name="Contents">Annotation contents</param>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.DocumentId">
            <summary>
            Internal document identifier
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.PageIndex">
            <summary>
            Index of the page to add the annotation. The first page has a page_index of 0
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.AnnotationIndex">
            <summary>
            Index of the annotation on a page. The first page has a annotation_index of 0
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.X">
            <summary>
            The horizontal X position of the annotation on your page in pixels
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Y">
            <summary>
            The vertical Y position of the annotation on your page in pixels. Measured from bottom upwards.
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Width">
            <summary>
            Annotation visible width
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Height">
            <summary>
            Annotation visible height
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Color">
            <summary>
            The color of the annotation's 'Sticky Note'
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Hidden">
            <summary>
            Hides the annotation from users
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Opacity">
            <summary>
            The opacity of the annotation (valid values are from 0.0 to 1.0)
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.OpenByDefault">
            <summary>
            Sets the annotation to be opened and readable by default, without user interaction
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Printable">
            <summary>
            Allows the annotation to be printed when users print the PDF
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.ReadOnly">
            <summary>
            Allows the annotation to be printed when users print the PDF
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Rotatable">
            <summary>
            Allows the annotation to be rotated.  E.g.  when the containing page os rotated
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Subject">
            <summary>
            The sub title of the annotation as displayed in the header of the 'sticky note'
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Icon">
            <summary>
            An icon to visually represent the 'sticky note' annotation
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Contents">
            <summary>
            The contents of the 'sticky note' annotation
            </summary>
        </member>
        <member name="P:IronPdf.Annotations.TextAnnotation.Title">
            <summary>
            The main title of the annotation as displayed in the header of the 'sticky note'
            </summary>
        </member>
        <member name="M:IronPdf.Annotations.TextAnnotation.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this annotation.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents this instance.
            </returns>
        </member>
        <member name="T:IronPdf.Annotations.TextAnnotation.AnnotationIcon">
            <summary>
            Icons used to interact with (open/close) the annotation with the PDF page.
            </summary>
        </member>
        <member name="F:IronPdf.Annotations.TextAnnotation.AnnotationIcon.NoIcon">
            <summary>
            No icon, this is useful when an annotation will be signified by an existing graphic or object.
            </summary>
        </member>
        <member name="F:IronPdf.Annotations.TextAnnotation.AnnotationIcon.Comment">
            <summary>
            A Comment Icon
            </summary>
        </member>
        <member name="F:IronPdf.Annotations.TextAnnotation.AnnotationIcon.Help">
            <summary>
            A Help Icon
            </summary>
        </member>
        <member name="F:IronPdf.Annotations.TextAnnotation.AnnotationIcon.Insert">
            <summary>
            An 'Insert' Icon
            </summary>
        </member>
        <member name="F:IronPdf.Annotations.TextAnnotation.AnnotationIcon.Key">
            <summary>
            A Key Icon
            </summary>
        </member>
        <member name="F:IronPdf.Annotations.TextAnnotation.AnnotationIcon.NewParagraph">
            <summary>
            A New Paragraph Icon
            </summary>
        </member>
        <member name="F:IronPdf.Annotations.TextAnnotation.AnnotationIcon.Note">
            <summary>
            A Note Icon
            </summary>
        </member>
        <member name="F:IronPdf.Annotations.TextAnnotation.AnnotationIcon.Paragraph">
            <summary>
            A 'Paragraph of Text' Icon
            </summary>
        </member>
        <member name="T:IronPdf.AspxToPdf">
            <summary>
            Renders any .Net Web Page (ASPX) into a PDF Document. Simply add it to the Page_Load event. <para>
            Not available in .Net Core because ASPX is superseded by the MVC model</para>
            Require IronPdf.Extensions.ASPX <see href="https://www.nuget.org/packages/IronPdf.Extensions.ASPX/" /></summary>
        </member>
        <member name="M:IronPdf.AspxToPdf.RenderThisPageAsPdf(IronPdf.AspxToPdf.FileBehavior,System.String,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Automatically renders any ASPX page into PDF instead of Html.  Use it in the Page_Load Event.
            <para>Example:</para><code>protected void Page_Load(object sender, EventArgs e){
            IronPdf.AspxToPdf.RenderThisPageAsPdf(FileBehavior.Attachment, "MyPdf.pdf", new  PdfPrintOptions(){ Dpi =
            300 }); }</code></summary>
            <param name="behavior">
            Specifies if the PDF file should be downloaded as an attachment, or displayed directly in the browser of
            users.
            </param>
            <param name="pdfFileName">
            The file-name of the PDF.  If no name is set, a suitable name will be automatically assigned chosen based on
            the Html title, PrintOptions or name of the ASPX page.
            </param>
            <param name="printOptions">
            Sets PDF output options such as PDF Title, paper-size, DPI, headers and footers.
            </param>
        </member>
        <member name="M:IronPdf.AspxToPdf.RenderThisPageAsPdf(System.Action{IronPdf.PdfDocument},IronPdf.ChromePdfRenderOptions,System.String)">
            <summary>
            Automatically renders this ASPX page into PDF and returns that PDF document in an callback.  Use it in the Page_Load Event.
            </summary>
            <param name="Callback">
            An callback method that handles the rendered <see cref="T:IronPdf.PdfDocument" /> object.
            </param>
            <param name="PrintOptions">
            Sets PDF output options such as PDF Title, paper-size, DPI, headers and footers.
            </param>
            <param name="PdfFileName">
            Output file name
            </param>
        </member>
        <member name="T:IronPdf.AspxToPdf.FileBehavior">
            <summary>
            Determines the web browser behavior towards a PDF document.
            </summary>
        </member>
        <member name="F:IronPdf.AspxToPdf.FileBehavior.Attachment">
            <summary>
            Instructs the user's web-browser to download the PDF as a file.
            </summary>
        </member>
        <member name="F:IronPdf.AspxToPdf.FileBehavior.InBrowser">
            <summary>
            Instructs the user's web-browser to render the PDF wherever possible.
            </summary>
        </member>
        <member name="T:IronPdf.Bookmarks.PdfBookMark">
            <summary>
            Represents a PDF bookmark as seen in the sidebar of PDF reader software to help user's navigate.
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMark.Children">
            <summary>
            Bookmarks contained under this bookmark
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMark.DestinationType">
            <summary>
            Type of destination represented by the bookmark
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMark.NextBookmark">
            <summary>
            Next bookmark at the current level, if any
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMark.Parent">
            <summary>
            Bookmark which contains this bookmark
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMark.ParentText">
            <summary>
            Parent bookmark text
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMark.PreviousText">
            <summary>
            Previous bookmark text
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMark.PageIndex">
            <summary>
            The zero based page number that the bookmark links to.
            </summary>
            <value>
            The index of the page.
            </value>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMark.PreviousBookmark">
            <summary>
            Previous bookmark at the current level, if any
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMark.Siblings">
            <summary>
            Bookmarks contained at the same level and under the same parent as this bookmark
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMark.Text">
            <summary>
            The display text of the bookmark
            </summary>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMark.InsertBookMarkAfter(System.String,System.Int32)">
            <summary>
            Insert a new bookmark after the specified bookmark
            </summary>
            <param name="text">The display text for the link.</param>
            <param name="pageIndex">The zero based page number to link to.  E.g.  Page 1 has a PageIndex of 0</param>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMark.InsertBookMarkBefore(System.String,System.Int32)">
            <summary>
            Insert a new bookmark after the specified bookmark
            </summary>
            <param name="text">The display text for the link.</param>
            <param name="pageIndex">The zero based page number to link to.  E.g.  Page 1 has a PageIndex of 0</param>
        </member>
        <member name="T:IronPdf.Bookmarks.PdfBookMarkCollection">
            <summary>
            Represents a PDF bookmark as seen in the sidebar of PDF reader software to help user's navigate.
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMarkCollection.Count">
            <summary>
            Total number of bookmarks, including all nested bookmarks
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMarkCollection.FirstBookmark">
            <summary>
            First bookmark within the bookmark collection at the current level
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMarkCollection.LastBookmark">
            <summary>
            Last bookmark within the bookmark collection at the current level
            </summary>
        </member>
        <member name="P:IronPdf.Bookmarks.PdfBookMarkCollection.Parent">
            <summary>
            Bookmark which contains this bookmark collection
            </summary>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.GetAllBookmarks">
            <summary>
            Retrieve all bookmarks within this collection, recursively retrieve all children of bookmarks within this collection, and return a flat list
            </summary>
            <returns>A flattened list of all bookmarks in this collection and all of their children</returns>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.GetChildren(System.Int32)">
            <summary>
            Retrieve children of bookmarks within this collection located at the specified depth
            </summary>
            <param name="depth">Depth from which to retrieve children</param>
            <returns>A flattened list of all children of bookmarks contained at the specified depth</returns>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.AddBookMarkAtEnd(System.String,System.Int32)">
            <summary>
            Add a new bookmark at the end of this bookmark collection
            </summary>
            <param name="text">The display text for the link.</param>
            <param name="pageIndex">The zero based page number to link to.  E.g.  Page 1 has a PageIndex of 0</param>
            <returns>Newly added bookmark</returns>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.AddBookMarkAtStart(System.String,System.Int32)">
            <summary>
            Add a new bookmark at the start of this bookmark collection
            </summary>
            <param name="text">The display text for the link.</param>
            <param name="pageIndex">The zero based page number to link to.  E.g.  Page 1 has a PageIndex of 0</param>
            <returns>Newly added bookmark</returns>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.GetBookmarkAfter(IronPdf.Bookmarks.IPdfBookmark)">
            <summary>
            Retrieve the next bookmark after the specified bookmark at the current level
            </summary>
            <param name="previousBookmark">Previous bookmark</param>
            <returns>Next bookmark at the current level</returns>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.GetBookmarkBefore(IronPdf.Bookmarks.IPdfBookmark)">
            <summary>
            Retrieve the previous bookmark before the specified bookmark at the current level
            </summary>
            <param name="nextBookmark">Next bookmark</param>
            <returns>Previous bookmark at the current level</returns>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.InsertBookMarkAfter(System.String,System.Int32,IronPdf.Bookmarks.IPdfBookmark)">
            <summary>
            Insert a new bookmark after the specified bookmark
            </summary>
            <param name="text">The display text for the link.</param>
            <param name="pageIndex">The zero based page number to link to.  E.g.  Page 1 has a PageIndex of 0</param>
            <param name="previousBookmark">Bookmark after which to insert a new bookmark</param>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.InsertBookMarkBefore(System.String,System.Int32,IronPdf.Bookmarks.IPdfBookmark)">
            <summary>
            Insert a new bookmark before the specified bookmark
            </summary>
            <param name="text">The display text for the link.</param>
            <param name="pageIndex">The zero based page number to link to.  E.g.  Page 1 has a PageIndex of 0</param>
            <param name="nextBookmark">Bookmark before which to insert a new bookmark</param>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.AddBookmarkInternal(System.String,System.Int32)">
            <summary>
            Internal method for adding a bookmark to the current bookmark collection, without invoking any Pdfium methods
            </summary>
            <param name="text">Bookmark text</param>
            <param name="pageIndex">Bookmark page index</param>
            <remarks>This method should be invoked when retrieving bookmarks from Pdfium to populate a bookmark collection</remarks>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.RemoveBookMark(IronPdf.Bookmarks.IPdfBookmark)">
            <summary>
            Removes the specified bookmark from this bookmark collection
            </summary>
            <param name="bookmark">The bookmark object to remove.</param>
        </member>
        <member name="M:IronPdf.Bookmarks.PdfBookMarkCollection.RemoveBookMarkAt(System.Int32)">
            <summary>
            Removes a bookmark at the specified index from this bookmark collection or any of its children
            </summary>
            <param name="index">The index of the bookmark to remove.</param>
        </member>
        <member name="T:IronPdf.ChromeClientAccessor">
            <summary>
            Accesses Chrome client implementations
            </summary>
        </member>
        <member name="M:IronPdf.ChromeClientAccessor.#ctor(IronPdf.GrpcLayer.IronPdfConnectionConfiguration)">
            <summary>
            Create a new chrome client accessor
            </summary>
        </member>
        <member name="T:IronPdf.ChromeHttpLoginCredentials">
            <summary>
            Provides credentials for IronPdf's embedded Chrome browser to log-in to an intranet, extranet or website, impersonating a user.   This
            allows a unique ability to render web-pages as PDFs even on secure intranets, extranets and websites.
            </summary>
        </member>
        <member name="M:IronPdf.ChromeHttpLoginCredentials.Finalize">
            <summary>
            Destructor cleans up all temporary cookies files for the  HttpLoginCredentials instance.
            </summary>
        </member>
        <member name="F:IronPdf.ChromeHttpLoginCredentials.CustomCookies">
            <summary>
            OBSOLETE! Use <see cref="F:IronPdf.ChromePdfRenderOptions.CustomCookies" /> instead"
            RenderUriToHml methods.
            </summary>
        </member>
        <member name="P:IronPdf.ChromeHttpLoginCredentials.NetworkPassword">
            <summary>
            Optional: Password credential for Windows / Linux network security authentication.
            </summary>
        </member>
        <member name="P:IronPdf.ChromeHttpLoginCredentials.NetworkUsername">
            <summary>
            Optional: User-name credential for Windows / Linux network security authentication.
            </summary>
        </member>
        <member name="F:IronPdf.ChromeHttpLoginCredentials.CustomHttpHeaders">
            <summary>
            A dictionary of custom HTTP headers to be sent with every HTTP request
            </summary>
            <exclude />
        </member>
        <member name="F:IronPdf.ChromeHttpLoginCredentials.CustomPostFiles">
            <summary>
            Optional, Advanced Usage:  Specifies a collection files to summited on every PDF rendering request.  Allows
            images and documents to be submitted to forms such that the output will be rendered as an PDF. <para>Note:
            The Key is the name of the post field, The value is the full file path.. UrlEncoding is not required.</para></summary>
            <exclude />
        </member>
        <member name="F:IronPdf.ChromeHttpLoginCredentials.CustomPostVariables">
            <summary>
            Optional:  Specifies a collection of HTTP 'POST' variables to submit on every PDF rendering request.  
            <para>Note: The 'key' is the name of the post field, The 'value' is its value.  UrlEncoding is not
            required.</para></summary>
            <exclude />
        </member>
        <member name="F:IronPdf.ChromeHttpLoginCredentials.EnableCookies">
            <summary>
            Enables cookies to be stored and sent when using RenderUriToHtml methods.
            </summary>
            <exclude />
        </member>
        <member name="F:IronPdf.ChromeHttpLoginCredentials.LoginFormPostVariables">
            <summary>
            Optional:  Specifies a collection of HTTP form variables to post/submit to <see cref="P:IronPdf.ChromeHttpLoginCredentials.LoginFormUrl" />.
            </summary>
            <exclude />
        </member>
        <member name="P:IronPdf.ChromeHttpLoginCredentials.LoginFormUrl">
            <summary>
            Optional:  Gives the URL to post website login-form authentication.  Should be the absolute Url which the
            form's action attribute specifies.
            </summary>
            <exclude />
        </member>
        <member name="P:IronPdf.ChromeHttpLoginCredentials.Proxy">
            <summary>
            Specifies an Http proxy server.   Use the pattern: http(s)://user-name:password@host:port/
            </summary>
            <exclude />
        </member>
        <member name="P:IronPdf.ChromeHttpLoginCredentials.UserAgent">
            <summary>
            The Http User-Agent header which will be used to fetch any remote HTML.
            </summary>
            <exclude />
        </member>
        <member name="T:IronPdf.ChromePdfRenderer">
            <summary>
            IronPdf.ChromePdfRenderer allows any web page or Html 'snippet' to be turned into a PDF document using an up-to-date embedded Chrome browser.
            <para> As of 2021 Iron Software recommend <see cref="T:IronPdf.ChromePdfRenderer" /> as our best PDF Renderer and a drop-in replacement for this class.</para></summary>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.#ctor(IronPdf.GrpcLayer.IronPdfConnectionConfiguration)">
            <summary>
            Create a new Chrome Html To PDF renderer.
            </summary>
            <param name="ConnectionConfig">Optional IronPdf host connection configuration</param>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.ApplyCookies(System.String,IronPdf.ChromeHttpLoginCredentials)">
            <summary>
            Apply cookies to the global request context for a specific URL. Consider using <see cref="F:IronPdf.ChromePdfRenderOptions.CustomCookies" /> instead
            <para>
            See <see cref="P:IronPdf.ChromePdfRenderer.RenderingOptions" /> for more information
            </para></summary>
            <param name="Url">Url to apply cookies to</param>
            <param name="Login">Cookies to apply</param>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.ApplyCookies(System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Apply cookies to the global request context for a specific URL. Consider using <see cref="F:IronPdf.ChromePdfRenderOptions.CustomCookies" /> instead.
            <para>
            See <see cref="P:IronPdf.ChromePdfRenderer.RenderingOptions" /> for more information
            </para></summary>
            <param name="url">Url to apply cookies to</param>
            <param name="cookies">Cookies to apply</param>
            <remarks>Automatically sets default <see cref="P:IronPdf.ChromePdfRenderOptions.RequestContext" /> of future renders to <seealso cref="F:IronPdf.Rendering.RequestContexts.Global" /><para>
            Very sensitive to the correct base URL, often times setting <seealso cref="F:IronPdf.ChromePdfRenderOptions.CustomCookies" /> will yield better results
            </para></remarks>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderPdfDocumentFromFile(IronPdf.ChromePdfRenderOptions,IronPdf.ChromeHttpLoginCredentials,System.String)">
            <summary>
            Render a PDF document from the specified HTML file path
            </summary>
            <param name="Options">Rendering options</param>
            <param name="Login">HTTP login credentials</param>
            <param name="filePath">HTML file path</param>
            <returns>PDF document</returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.ClearCookies">
            <summary>
            OBSOLETE! Use <see cref="F:IronPdf.ChromePdfRenderOptions.CustomCookies" /> instead. Previously cleared cookies in the global request context for all URLs.
            <para>
            See <see cref="P:IronPdf.ChromePdfRenderer.RenderingOptions" /> for more information
            </para></summary>
            <remarks>Clearing cookies is no longer necessary as they do not persist in between renders</remarks>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderHtmlAsPdf(System.String,IronPdf.ChromePdfRenderOptions,System.String)">
            <summary>
            Creates a PDF file from an Html string, and returns it as an <see cref="T:IronPdf.PdfDocument" /> object which can be edited and saved to disk or served on a website.
            <para>Static version of the RenderHtmlAsPdf method.</para></summary>
            <param name="Html">The Html to be rendered as a PDF.</param>
            <param name="Options">An instance of <see cref="T:IronPdf.ChromePdfRenderOptions" /> that allows configuration of Chrome "HTML to PDF" rendering parameters. </param>
            <param name="Proxy">Specifies an Http proxy server. Use the pattern: http(s)://user-name:password@host:port</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /> with the Html rendered as its contents.
            </returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderHtmlAsPdf(System.String,System.Uri,IronPdf.ChromePdfRenderOptions,System.String)">
            <summary>
            Creates a PDF file from an Html string, and returns it as an <see cref="T:IronPdf.PdfDocument" /> object which can be edited and saved to disk or served on a website.
            <para>Static version of the RenderHtmlAsPdf method.</para></summary>
            <param name="Html">The Html to be rendered as a PDF.</param>
            <param name="BaseUrl">Setting the BaseURL property gives the relative context for hyper-links, images, CSS and JavaScript files. May be a remote URL or local file path.</param>
            <param name="Options">An instance of <see cref="T:IronPdf.ChromePdfRenderOptions" /> that allows configuration of Chrome "HTML to PDF" rendering parameters. </param>
            <param name="Proxy">Specifies an Http proxy server. Use the pattern: http(s)://user-name:password@host:port</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /> with the Html rendered as its contents.
            </returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderHtmlAsPdf(System.String,System.String,IronPdf.ChromePdfRenderOptions,System.String)">
            <summary>
            Creates a PDF file from an Html string, and returns it as an <see cref="T:IronPdf.PdfDocument" /> object which can be edited and saved to disk or served on a website.
            <para>Static version of the RenderHtmlAsPdf method.</para></summary>
            <param name="Html">The Html to be rendered as a PDF.</param>
            <param name="BaseUrlOrPath">Setting the BaseURL property gives the relative context for hyper-links, images, CSS and JavaScript files. May be a remote URL or local file path.</param>
            <param name="Options">An instance of <see cref="T:IronPdf.ChromePdfRenderOptions" /> that allows configuration of Chrome "HTML to PDF" rendering parameters. </param>
            <param name="Proxy">Specifies an Http proxy server. Use the pattern: http(s)://user-name:password@host:port</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /> with the Html rendered as its contents.
            </returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderHtmlAsPdfAsync(System.String,System.Uri,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Creates a PDF file from an Html string, and returns it as an <see cref="T:IronPdf.PdfDocument" /> object which can be edited and saved to disk or served on a website.
            <para>Static Async version of the RenderHtmlAsPdf method.</para></summary>
            <param name="Html">The Html to be rendered as a PDF.</param>
            <param name="BaseUrl">Setting the BaseURL property gives the relative context for hyper-links, images, CSS and JavaScript files. May be a remote URL or local file path.</param>
            <param name="Options">An instance of <see cref="T:IronPdf.ChromePdfRenderOptions" /> that allows configuration of Chrome "HTML to PDF" rendering parameters. </param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /> with the Html rendered as its contents.
            </returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderHTMLFileAsPdf(System.String,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Renders an HTML file as a PDF file. Returns a <see cref="T:IronPdf.PdfDocument" /> object which may be edited and saved to disk or served on a website.
            <para>Static version of the RenderHTMLFileAsPdf method.</para></summary>
            <param name="FilePath">Path to an Html file.</param>
            <param name="Options">Optional print options and settings as an instance of <see cref="T:IronPdf.ChromePdfRenderOptions" />.  </param>
            <returns>A <see cref="T:IronPdf.PdfDocument" /> with the Html file rendered as its contents..</returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderZipFileAsPdf(System.String,System.String,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Creates a PDF file from a local Zip file, and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            <para>IronPDF is a W3C standards compliant HTML rendering based on Google's Chromium browser.  If your output PDF does not look as expected:
            <br /> - Validate your HTML file using  https://validator.w3.org/ &amp; CSS https://jigsaw.w3.org/css-validator/
            <br /> - To debug HTML, view the file in Chrome web browser's print preview which will work almost exactly as IronPDF.
            <br /> - Read our detailed documentation on pixel perfect HTML to PDF: https://ironpdf.com/tutorials/pixel-perfect-html-to-pdf/
            </para></summary>
            <param name="FilePath">Path to an Zip to be rendered as a PDF.</param>
            <param name="MainFile">Name of the primary HTML file.</param>
            <param name="Options">Optional print options and settings as an instance of <see cref="T:IronPdf.ChromePdfRenderOptions" />. </param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderZipFileAsPdfAsync(System.String,System.String,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Creates a PDF file from a local Zip file, and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            <para>IronPDF is a W3C standards compliant HTML rendering based on Google's Chromium browser.  If your output PDF does not look as expected:
            <br /> - Validate your HTML file using  https://validator.w3.org/ &amp; CSS https://jigsaw.w3.org/css-validator/
            <br /> - To debug HTML, view the file in Chrome web browser's print preview which will work almost exactly as IronPDF.
            <br /> - Read our detailed documentation on pixel perfect HTML to PDF: https://ironpdf.com/tutorials/pixel-perfect-html-to-pdf/
            </para><para>Static Async version of the RenderZipFileAsPdf method.</para></summary>
            <param name="FilePath">Path to a Zip to be rendered as a PDF.</param>
            <param name="MainFile">Name of the primary HTML file.</param>
            <param name="Options">Optional print options and settings as an instance of <see cref="T:IronPdf.ChromePdfRenderOptions" />. </param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderHTMLFileAsPdfAsync(System.String,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Renders an HTML file as a PDF file. Returns a <see cref="T:IronPdf.PdfDocument" /> object which may be edited and saved to disk or served on a website.
            <para>Static Async version of the RenderHTMLFileAsPdf method.</para></summary>
            <param name="FilePath">Path to an Html file.</param>
            <param name="Options">Optional print options and settings as an instance of <see cref="T:IronPdf.ChromePdfRenderOptions" />.  </param>
            <returns>A <see cref="T:IronPdf.PdfDocument" /> with the Html file rendered as its contents.</returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderUrlAsPdf(System.String,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Renders all Html and assets at a given Url into a PDF file using IronPdf's embedded Chrome browser as the rendering engine.
            <para>Static version of the RenderUrlAsPdf method. </para></summary>
            <param name="UrlOrPath">An absolute Url.  Points to the Html document to be rendered as a PDF.</param>
            <param name="Options">Optional print options and settings as an instance of <see cref="T:IronPdf.ChromePdfRenderOptions" />.  </param>&gt;
             <returns>A <see cref="T:IronPdf.PdfDocument" /> with the Url rendered as its contents.</returns></member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderUrlAsPdfAsync(System.String,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Renders all Html and assets at a given Url into a PDF file using IronPdf's embedded Chrome browser as the rendering engine.
            <para>Static Async version of the RenderUrlAsPdf method. </para></summary>
            <param name="UrlOrPath">An absolute Url.  Points to the Html document or local file to be rendered as a PDF.</param>
            <param name="Options">Optional print options and settings as an instance of <see cref="T:IronPdf.ChromePdfRenderOptions" />.  </param>&gt;
             <returns>A <see cref="T:IronPdf.PdfDocument" /> with the Url rendered as its contents.</returns></member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderUrlAsPdf(System.Uri,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Renders all Html and assets at a given Url into a PDF file using IronPdf's embedded Chrome browser as the rendering engine.
            <para>Static version of the RenderUrlAsPdf method. </para></summary>
            <param name="Url">An absolute Uri.  Points to the Html document or local file file to be rendered as a PDF.</param>
            <param name="Options">Optional print options and settings as an instance of <see cref="T:IronPdf.ChromePdfRenderOptions" />.  </param>&gt;
             <returns>A <see cref="T:IronPdf.PdfDocument" /> with the Url rendered as its contents.</returns></member>
        <member name="M:IronPdf.ChromePdfRenderer.StaticRenderUrlAsPdfAsync(System.Uri,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Renders all Html and assets at a given Url into a PDF file using IronPdf's embedded Chrome browser as the rendering engine.
            <para>Static Async version of the RenderUrlAsPdf method. </para></summary>
            <param name="Url">An absolute Uri.  Points to the Html document or local file file to be rendered as a PDF.</param>
            <param name="Options">Optional print options and settings as an instance of <see cref="T:IronPdf.ChromePdfRenderOptions" />.  </param>&gt;
             <returns>A <see cref="T:IronPdf.PdfDocument" /> with the Url rendered as its contents.</returns></member>
        <member name="P:IronPdf.ChromePdfRenderer.RenderingOptions">
            <summary>
            Html to Pdf rendering options
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderer.LoginCredentials">
            <summary>
            Http login credentials
            </summary>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderHtmlAsPdf(System.String,System.Uri,System.String)">
            <summary>
            Creates a PDF file from an Html string, and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            <para>IronPDF is a W3C standards compliant HTML rendering based on Google's Chromium browser.  If your output PDF does not look as expected:
            <br /> - Validate your HTML using  https://validator.w3.org/ &amp; CSS https://jigsaw.w3.org/css-validator/
            <br /> - To debug HTML, Save your HTML string to a file and view in Chrome web browser's print preview which will work almost exactly as IronPDF.
            <br /> - Read our detailed documentation on pixel perfect HTML to PDF: https://ironpdf.com/tutorials/pixel-perfect-html-to-pdf/
            </para></summary>
            <param name="Html">The Html to be rendered as a PDF.</param>
            <param name="BaseUrl">Optional. Setting the BaseURL property gives the relative file path or URL context for hyper-links, images, CSS and JavaScript files.</param>
            <param name="Proxy">Optional. Specifies an Http proxy server. Use the pattern: http(s)://user-name:password@host:port</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderHtmlAsPdf(System.String,System.String,System.String)">
            <summary>
            Creates a PDF file from an Html string, and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            <para>IronPDF is a W3C standards compliant HTML rendering based on Google's Chromium browser.  If your output PDF does not look as expected:
            <br /> - Validate your HTML using  https://validator.w3.org/ &amp; CSS https://jigsaw.w3.org/css-validator/
            <br /> - To debug HTML, Save your HTML string to a file and view in Chrome web browser's print preview which will work almost exactly as IronPDF.
            <br /> - Read our detailed documentation on pixel perfect HTML to PDF: https://ironpdf.com/tutorials/pixel-perfect-html-to-pdf/
            </para></summary>
            <param name="Html">The Html to be rendered as a PDF.</param>
            <param name="BaseUrlOrPath">Optional. Setting the BaseUBaseUrlOrPathRL property gives the relative file path or URL context for hyper-links, images, CSS and JavaScript files.</param>
            <param name="Proxy">Optional. Specifies an Http proxy server. Use the pattern: http(s)://user-name:password@host:port</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderHtmlFileAsPdf(System.String)">
            <summary>
            Creates a PDF file from a local Html file, and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            <para>IronPDF is a W3C standards compliant HTML rendering based on Google's Chromium browser.  If your output PDF does not look as expected:
            <br /> - Validate your HTML file using  https://validator.w3.org/ &amp; CSS https://jigsaw.w3.org/css-validator/
            <br /> - To debug HTML, view the file in Chrome web browser's print preview which will work almost exactly as IronPDF.
            <br /> - Read our detailed documentation on pixel perfect HTML to PDF: https://ironpdf.com/tutorials/pixel-perfect-html-to-pdf/
            </para></summary>
            <param name="FilePath">Path to an Html to be rendered as a PDF.</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderUrlAsPdf(System.String)">
            <summary>
            Creates a PDF file from a URL or local file path and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            </summary>
            <param name="UrlOrPath">Path to an Html to be rendered as a PDF.</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderUrlAsPdf(System.Uri)">
            <summary>
            Creates a PDF file from a URL or local file path and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            </summary>
            <param name="Url"> An absolute (fully formed) Uri.  Points to the Html document to be rendered as a PDF.</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderRtfFileAsPdf(System.String)">
            <summary>
            Creates a PDF file from a RTF (rich text format) file and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            </summary>
            <param name="FilePath">Path to an RTF file to be rendered as a PDF.</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderMarkdownFileAsPdf(System.String)">
            <summary>
            Creates a PDF file from a Markdown file and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            </summary>
            <param name="FilePath">Path to an Markdown file to be rendered as a PDF.</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderRtfStringAsPdf(System.String)">
            <summary>
            Creates a PDF file from an RTF (rich text format) string and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            </summary>
            <param name="RtfString">RTF string to be rendered as a PDF.</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderMarkdownStringAsPdf(System.String)">
            <summary>
            Creates a PDF file from an Markdown string format and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            </summary>
            <param name="markdownString">Markdown string to be rendered as a PDF.</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderHtmlAsPdfAsync(System.String,System.Uri)">
            <summary>
            Asynchronously creates a PDF file from a URL or local file path and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            <para>Asynchronous variant of <see cref="M:IronPdf.ChromePdfRenderer.RenderHtmlAsPdf(System.String,System.Uri,System.String)" /></para></summary>
            <param name="Html">The Html to be rendered as a PDF.</param>
            <param name="BaseUrl">
            Setting the BaseUrl property gives the relative context for hyper-links, images, CSS and JavaScript
            files.   May point to a local directory or a remote URL.
            </param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderHtmlFileAsPdfAsync(System.String)">
            <summary>
            Asynchronously creates a PDF file from a local Html file, and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            <para> Asynchronous variant of <see cref="M:IronPdf.ChromePdfRenderer.RenderHtmlFileAsPdf(System.String)" /></para></summary>
            <param name="FilePath">Path to an Html to be rendered as a PDF.</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderUrlAsPdfAsync(System.Uri)">
            <summary>
            Asynchronously creates a PDF file from a URL or local file path and returns it as a <see cref="T:IronPdf.PdfDocument" />.
             <para> Asynchronous variant of <see cref="M:IronPdf.ChromePdfRenderer.RenderUrlAsPdf(System.Uri)" /></para></summary>
            <param name="Url"> An absolute (fully formed) Uri.  Points to the Html document to be rendered as a PDF.</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderUrlAsPdfAsync(System.String)">
            <summary>
            Asynchronously creates a PDF file from a URL or local file path and returns it as a <see cref="T:IronPdf.PdfDocument" />.
             <para> Asynchronous variant of <see cref="M:IronPdf.ChromePdfRenderer.RenderUrlAsPdf(System.String)" /></para></summary>
            <param name="UrlOrPath">Path or URL to an Html document to be rendered as a PDF.</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderZipFileAsPdf(System.String,System.String)">
            <summary>
            Creates a PDF file from a local Zip file, and returns it as a <see cref="T:IronPdf.PdfDocument" />.
            <para>IronPDF is a W3C standards compliant HTML rendering based on Google's Chromium browser.  If your output PDF does not look as expected:
            <br /> - Validate your HTML file using  https://validator.w3.org/ &amp; CSS https://jigsaw.w3.org/css-validator/
            <br /> - To debug HTML, view the file in Chrome web browser's print preview which will work almost exactly as IronPDF.
            <br /> - Read our detailed documentation on pixel perfect HTML to PDF: https://ironpdf.com/tutorials/pixel-perfect-html-to-pdf/
            </para></summary>
            <param name="FilePath">Path to an Zip to be rendered as a PDF.</param>
            <param name="MainFile">Name of the primary HTML file.</param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderHtmlAsPdfForExtension(System.String)">
            <inheritdoc />
        </member>
        <member name="M:IronPdf.ChromePdfRenderer.RenderHtmlAsPdfForExtensionAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="T:IronPdf.Rendering.RequestContexts">
            <summary>
            Browser request contexts for defining relationship between the browser request contexts of individual renders.
            <para>
            Important for cookies and user preferences.
            </para></summary>
            <remarks>Useful for guaranteeing isolation between individual renders</remarks>
        </member>
        <member name="F:IronPdf.Rendering.RequestContexts.Isolated">
            <summary>
            Creates a new request context which will be isolated from previous or future renders
            </summary>
            <remarks>Recommended, as this will ensure this render is not affected by previous renders</remarks>
        </member>
        <member name="F:IronPdf.Rendering.RequestContexts.Global">
            <summary>
            Uses the global request context which is shared between all renders
            </summary>
            <remarks>Useful in some cases for persisting certain browser states between renders</remarks>
        </member>
        <member name="F:IronPdf.Rendering.RequestContexts.Auto">
            <summary>
            Defaults to <see cref="F:IronPdf.Rendering.RequestContexts.Isolated" /><para>
            Uses <see cref="F:IronPdf.Rendering.RequestContexts.Global" /> if the user has ever invoked <see cref="T:IronPdf.Engines.Chrome.ChromeInterop.UnmanagedDelegates.ApplyCookies" /></para></summary>
        </member>
        <member name="T:IronPdf.Rendering.PdfCssMediaType">
            <summary>
            Defines which style-sheet should be rendered.   'Print' or 'Screen'.  This matches the CSS3 Media Queries
            standard.
            </summary>
        </member>
        <member name="F:IronPdf.Rendering.PdfCssMediaType.Print">
            <summary>
            Use 'print' CSS media styles. Default behavior when viewing print dialog in a browser
            </summary>
        </member>
        <member name="F:IronPdf.Rendering.PdfCssMediaType.Screen">
            <summary>
            Use 'screen' CSS media styles. Emulates the display of a browser window.
            </summary>
        </member>
        <member name="T:IronPdf.Rendering.PdfPaperSize">
            <summary>
             Defines the target virtual paper size the PDF.  Relates to real-world paper-sizes.
             </summary>
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Letter">
            8.5in x 11in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Legal">
            8.5in x 14in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A4">
            210mm x 297mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.CSheet">
            17in x 22in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.DSheet">
            22in x 34in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.ESheet">
            34in x 44in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.LetterSmall">
            8.5in x 11in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Tabloid">
            11in x 17in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Ledger">
            17in x 11in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Statement">
            5.5in x 8.5in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Executive">
            7.25in x 10.5in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A3">
            297mm x 420mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A4Small">
            210mm x 297mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A5">
            148mm x 210mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.B4">
            250mm x 353mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.B5">
            176mm x 250mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Folio">
            8.5in x 13in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Quarto">
            215mm x 275mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Standard10x14">
            10in x 14in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Standard11x17">
            11in x 17in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Note">
            8.5in x 11in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Number9Envelope">
            3.875in x 8.875in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Number10Envelope">
            4.125in x 9.5in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Number11Envelope">
            4.5in x 10.375in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Number12Envelope">
            4.75in x 11in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Number14Envelope">
            5in x 11.5in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.DLEnvelope">
            110mm x 220mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.C5Envelope">
            162mm x 229mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.C3Envelope">
            324mm x 458mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.C4Envelope">
            229mm x 324mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.C6Envelope">
            114mm x 162mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.C65Envelope">
            114mm x 229mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.B4Envelope">
            250mm x 353mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.B5Envelope">
            176mm x 250mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.B6Envelope">
            176mm x 125mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.ItalyEnvelope">
            110mm x 230mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.MonarchEnvelope">
            3.875in x 7.5in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PersonalEnvelope">
            3.625in x 6.5in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.USStandardFanfold">
            14.875in x 11in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.GermanStandardFanfold">
            8.5in x 12in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.GermanLegalFanfold">
            8.5in x 13in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.IsoB4">
            250mm x 353mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.JapanesePostcard">
            100mm x 148mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Standard9x11">
            9in x 11in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Standard10x11">
            10in x 11in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Standard15x11">
            15in x 11in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.InviteEnvelope">
            220mm x 220mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.LetterExtra">
            9.275in x 12in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.LegalExtra">
            9.275in x 15in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.TabloidExtra">
            11.69in x 18in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A4Extra">
            236mm x 322mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.LetterTransverse">
            8.275in x 11in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A4Transverse">
            210mm x 297mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.LetterExtraTransverse">
            9.275in x 12in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.APlus">
            227mm x 356mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.BPlus">
            305mm x 487mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.LetterPlus">
            8.5in x 12.69in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A4Plus">
            210mm x 330mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A5Transverse">
            148mm x 210mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.B5Transverse">
            182mm x 257mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A3Extra">
            322mm x 445mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A5Extra">
            174mm x 235mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.B5Extra">
            201mm x 276mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A2">
            420mm x 594mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A3Transverse">
            297mm x 420mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A3ExtraTransverse">
            322mm x 445mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.JapaneseDoublePostcard">
            200mm x 148mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A6">
            105mm x 148mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.LetterRotated">
            11in x 8.5in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A3Rotated">
            420mm x 297mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A4Rotated">
            297mm x 210mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A5Rotated">
            210mm x 148mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.B4JisRotated">
            364mm x 257mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.B5JisRotated">
            257mm x 182mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.JapanesePostcardRotated">
            148mm x 100mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.JapaneseDoublePostcardRotated">
            148mm x 200mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.A6Rotated">
            148mm x 105mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.B6Jis">
            128mm x 182mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.B6JisRotated">
            182mm x 128mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Standard12x11">
            12in x 11in
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Prc16K">
            146mm x 215mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Prc32K">
            97mm x 151mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Prc32KBig">
            97mm x 151mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber1">
            102mm x 165mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber2">
            102mm x 176mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber3">
            125mm x 176mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber4">
            110mm x 208mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber5">
            110mm x 220mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber6">
            120mm x 230mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber7">
            160mm x 230mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber8">
            120mm x 309mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber9">
            229mm x 324mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber10">
            324mm x 458mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Prc16KRotated">
            146mm x 215mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Prc32KRotated">
            97mm x 151mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Prc32KBigRotated">
            97mm x 151mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber1Rotated">
            165mm x 102mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber2Rotated">
            176mm x 102mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber3Rotated">
            176mm x 125mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber4Rotated">
            208mm x 110mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber5Rotated">
            220mm x 110mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber6Rotated">
            230mm x 120mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber7Rotated">
            230mm x 160mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber8Rotated">
            309mm x 120mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber9Rotated">
            324mm x 229mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.PrcEnvelopeNumber10Rotated">
            458mm x 324mm
        </member>
        <member name="F:IronPdf.Rendering.PdfPaperSize.Custom">
            Set using SetCustomPaperSizeInInches or SetCustomPaperSizeInMillimeters
        </member>
        <member name="T:IronPdf.Rendering.PdfPaperSizeConverter">
            <summary>
            Class for converting PDF paper sizes from the <see cref="T:IronPdf.Rendering.PdfPaperSize" /> enum to millimeters.
            </summary>
        </member>
        <member name="M:IronPdf.Rendering.PdfPaperSizeConverter.ToMillimeters(IronPdf.Rendering.PdfPaperSize)">
            <summary>
            Converts the given <see cref="T:IronPdf.Rendering.PdfPaperSize" /> value to its equivalent size in millimeters.
            </summary>
            <param name="paperSize">The <see cref="T:IronPdf.Rendering.PdfPaperSize" /> value to convert.</param>
            <returns>A tuple that contains the width and height in millimeters for the specified <see cref="T:IronPdf.Rendering.PdfPaperSize" /> enum value.</returns>
        </member>
        <member name="T:IronPdf.Rendering.PdfRenderingEngine">
            <summary>
            Specifies which "HTML to PDF" rendering engine to use.
            </summary>
        </member>
        <member name="F:IronPdf.Rendering.PdfRenderingEngine.Default">
            <summary>Chooses the first renderer in this list which is currently available at runtime.</summary>
        </member>
        <member name="F:IronPdf.Rendering.PdfRenderingEngine.Chrome">
            <summary>Recommended as our most capable, multithreaded and standards compliant rendering engine. Chooses the IronPDf embedded Chrome browser rendering engine.</summary>
        </member>
        <member name="T:IronPdf.UseMargins">
            <summary>
            Margin values which can be copied from the main document to headers and footers applied to the document
            </summary>
        </member>
        <member name="F:IronPdf.UseMargins.None">
            <summary>
            No margins values will be used
            </summary>
        </member>
        <member name="F:IronPdf.UseMargins.Top">
            <summary>
            Top margin value will be used
            </summary>
        </member>
        <member name="F:IronPdf.UseMargins.Bottom">
            <summary>
            Bottom margin value will be used
            </summary>
        </member>
        <member name="F:IronPdf.UseMargins.Left">
            <summary>
            Left margin value will be used
            </summary>
        </member>
        <member name="F:IronPdf.UseMargins.Right">
            <summary>
            Right margin value will be used
            </summary>
        </member>
        <member name="F:IronPdf.UseMargins.TopAndBottom">
            <summary>
            Top and bottom margin values will be used
            </summary>
        </member>
        <member name="F:IronPdf.UseMargins.LeftAndRight">
            <summary>
            Left and right margin values will be used
            </summary>
        </member>
        <member name="F:IronPdf.UseMargins.All">
            <summary>
            All margin values will be used
            </summary>
        </member>
        <member name="T:IronPdf.ChromePdfRenderOptions">
            <summary>
            Html To PDF output options for <see cref="T:IronPdf.ChromePdfRenderer" />. Specifies options such as Paper-Size, DPI, Headers and Footers and other Chromium specific browser setup options.
            </summary>
        </member>
        <member name="M:IronPdf.ChromePdfRenderOptions.#ctor">
            <summary>
            Default Constructor.  Creates a new instance of ChromePdfRenderOptions with default settings.
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.DefaultChrome">
            <summary>
            A <see cref="T:IronPdf.ChromePdfRenderOptions" /> preset with default settings that match the vanilla PDF viewer in Chrome.
            <br /><para>These values are: <br /><see cref="P:IronPdf.ChromePdfRenderOptions.CssMediaType" />: <see cref="F:IronPdf.Rendering.PdfCssMediaType.Print" />, <see cref="P:IronPdf.ChromePdfRenderOptions.FirstPageNumber" />: 1, <see cref="P:IronPdf.ChromePdfRenderOptions.FitToPaperMode" />: <see cref="F:IronPdf.Engines.Chrome.FitToPaperModes.None" />,
            Top and left margin: 0.39 in, Bottom and Right margin: 0.38 in, PaperSize: <see cref="F:IronPdf.Rendering.PdfPaperSize.A4" />,
            <see cref="P:IronPdf.ChromePdfRenderOptions.PaperOrientation" />: <see cref="F:IronPdf.Rendering.PdfPaperOrientation.Portrait" />, <see cref="P:IronPdf.ChromePdfRenderOptions.PrintHtmlBackgrounds" />: false, <see cref="P:IronPdf.ChromePdfRenderOptions.RenderDelay" />: 20
            </para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.UseMarginsOnHeaderAndFooter">
            <summary>
            Use margin values from the main document when rendering headers and footers
            <para>This value is ignored when header and footer heights are autosized. Autosizing happens when a header or footer <see cref="P:IronPdf.HtmlHeaderFooter.MaxHeight">MaxHeight</see> value is null.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.CreatePdfFormsFromHtml">
            <summary>
            Turns all Html forms elements into editable PDF forms.
            <para>Default value is true.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.CssMediaType">
            <summary>
            Enables Media="screen" CSS Styles  and StyleSheets <para>Note: By setting AllowScreenCss=false, IronPdf
            renders PDFs from HTML using CSS for media="print" as if printing a web page in a browser print dialog.</para><para>Default value is PdfCssMediaType.Screen.</para></summary>
        </member>
        <member name="F:IronPdf.ChromePdfRenderOptions.CustomCookies">
            <summary>
            Custom cookies for this HTML render. Cookies do not persist in between renders and must be set each time.
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.CustomCssUrl">
            <summary>
            Allows a custom CSS style-sheet to be applied to Html before rendering. May be a local file path or a remote url.
            </summary>
            <remarks>Only applicable when rendering Html to Pdf</remarks>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.EnableJavaScript">
            <summary>
            Enables JavaScript and Json to be executed  before the page is rendered. Ideal for printing from Ajax /
            Angular Applications. <para>Also see <see cref="P:IronPdf.ChromePdfRenderOptions.RenderDelay" /></para><para>Default value is false.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.EnableMathematicalLaTex">
            <summary>
            Enables Mathematical LaTeX Elements Rendering
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.Javascript">
            <summary>
            A custom javascript string to be executed after all HTML has loaded but before PDf rendering.
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.JavascriptMessageListener">
            <summary>
            Method callback to be invoked whenever a browser JavaScript console message becomes available.
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.FirstPageNumber">
            <summary>
            First page number to be used in PDF Headers and Footers.
            <para><seealso cref="P:IronPdf.ChromePdfRenderOptions.TextHeader" /></para><para><seealso cref="P:IronPdf.ChromePdfRenderOptions.HtmlHeader" /></para><para><seealso cref="P:IronPdf.ChromePdfRenderOptions.TextFooter" /></para><para><seealso cref="P:IronPdf.ChromePdfRenderOptions.HtmlFooter" /></para><para>Default value is 1.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.TableOfContents">
            <summary>
            Generate a table of contents at the location in the HTML document where an element is found with id "ironpdf-toc".
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.GrayScale">
            <summary>
            Outputs a black-and-white PDF
            <para>Default value is false.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.HtmlHeader">
            <summary>
            Sets the header content for every PDF page as Html.  Supports 'mail-merge'.
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.HtmlFooter">
            <summary>
            Sets the footer content for every PDF page as Html.  Supports 'mail-merge'.
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.InputEncoding">
            <summary>
            The input character encoding as a string;
            <para>Default value is Encoding.UTF8.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.MarginBottom">
            <summary>
            Bottom Pdf "paper" margin in millimeters.  Set to zero for border-less and commercial printing applications.
            <para>Default value is 25.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.MarginLeft">
            <summary>
            Left Pdf "paper" margin in millimeters.  Set to zero for border-less and commercial printing applications.
            <para>Default value is 25.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.MarginRight">
            <summary>
            Right Pdf "paper" margin in millimeters.  Set to zero for border-less and commercial printing applications.
            <para>Default value is 25.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.MarginTop">
            <summary>
            Top Pdf "paper" margin in millimeters.  Set to zero for border-less and commercial printing applications.
            <para>Default value is 25.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.PaperOrientation">
            <summary>
            The PDF paper orientation. E.g. Portrait or Landscape.
            <para>Default value is Portrait.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.PaperSize">
            <summary>
            Set an output paper size for PDF pages.  System.Drawing.Printing.PaperKind. <para>Use SetCustomPaperSize(int
            width, int height) for custom sizes.</para><para>Default value is A4.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.PrintHtmlBackgrounds">
            <summary>
            Prints background-colors and images from Html.
            <para>Default value is true.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.RequestContext">
            <summary>
            Request context for this render. Determines isolation of certain resources such as cookies.
            <para>
            See <see cref="T:IronPdf.Rendering.RequestContexts" /> for more information
            </para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.TextFooter">
            <summary>
            Sets the footer content for every PDF page as text.  Supports 'mail-merge' and automatically turns urls into hyperlinks..
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.TextHeader">
            <summary>
            Sets the header content for every PDF page as text.  Supports 'mail-merge' and automatically turns urls into hyperlinks..
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.Timeout">
            <summary>
            Render timeout in seconds
            <para>Default value is 60.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.Title">
            <summary>
            PDF Document Name and Title meta-data.  Not required.  Useful for mail-merge and automatic file naming in the IronPdf MVC and Razor extensions.
            </summary>
        </member>
        <member name="F:IronPdf.ChromePdfRenderOptions.PaperFit">
            <summary>
            A html-to-virtual paper setup manager allowing the developer to control how content will be laid out on PDF "paper" pages.
            <para>Include options for: Deafult Chrome Behavior, Zoomed, Responsive CSS3 Layouts, SCale-To-Page &amp; Continuous Feed (e.g. reciept) style PDF page setups.</para></summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.ForcePaperSize">
            <summary>
            Force page sizes to be exactly what is specified via <see cref="P:IronPdf.ChromePdfRenderOptions.PaperSize" /> by resizing the page after generating a PDF from HTML
            <para>Useful for bypassing CSS rules which specify paper size</para></summary>
            <remarks>Helps correct small errors in page size when rendering HTML to PDF</remarks>
        </member>
        <member name="F:IronPdf.ChromePdfRenderOptions.HttpRequestHeaders">
            <summary>
            Custom HTTP request headers to be sent with the HTML
            </summary>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.WaitFor">
            <summary>
            A wrapper object that holds configuration for wait-for mechanism for user to wait for certain events before rendering.
            </summary>
            <remarks>By default, it will wait for nothing.</remarks>
        </member>
        <member name="M:IronPdf.ChromePdfRenderOptions.Clone">
            <summary>
            Supports <see cref="T:System.ICloneable" />.  Creates a deep copy of this class instance.
            </summary>
            <returns>A deep clone of this instance.  Use explicit casting to convert object back to the intended type. </returns>
        </member>
        <member name="M:IronPdf.ChromePdfRenderOptions.SetCustomPaperSizeinCentimeters(System.Double,System.Double)">
            <summary>
            Set an output paper size for PDF pages.  Dimensions are in Centimeters.
            </summary>
            <param name="width">Custom paper width in cm.</param>
            <param name="height">&gt;Custom paper height in cm.</param>
        </member>
        <member name="M:IronPdf.ChromePdfRenderOptions.SetCustomPaperSizeInInches(System.Double,System.Double)">
            <summary>
            Set an output paper size for PDF pages.  Dimensions are in Inches.
            </summary>
            <param name="width">Custom paper width in Inches.</param>
            <param name="height">&gt;Custom paper height in Inches.</param>
        </member>
        <member name="M:IronPdf.ChromePdfRenderOptions.SetCustomPaperSizeinMilimeters(System.Double,System.Double)">
            <summary>
            Set an output paper size for PDF pages.  Dimensions are in millimeters.
            </summary>
            <param name="width">Custom paper width in millimeters.</param>
            <param name="height">&gt;Custom paper height in millimeters.</param>
        </member>
        <member name="M:IronPdf.ChromePdfRenderOptions.SetCustomPaperSizeinPixelsOrPoints(System.Double,System.Double,System.Int32)">
            <summary>
            Set an output paper size for PDF pages.  Dimensions are in screen Pixels or printer Points.
            </summary>
            <param name="width">Custom paper width in pixels/points.</param>
            <param name="height">&gt;Custom paper height in pixels/points.</param>
            <param name="DPI">&gt;Intended print resolution of the PDF.  To be clear PDFs have no fixed DPI/PPI value for rendering. 72 and 96 are common onscreen values.  300 is a comon value used in commercial printing. </param>
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.RenderDelay">
            <exclude />
            <summary>
            Milliseconds to wait after Html is rendered before printing. This can use useful when considering the
            rendering of JavaScript, Ajax or animations.
            <para>Default value is 0.</para></summary>
            <exclude />
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.ViewPortHeight">
            <exclude />
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.ViewPortWidth">
            <exclude />
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.Zoom">
            <exclude />
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.FitToPaperMode">
            <exclude />
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.ApplyMarginToHeaderAndFooter">
            <summary>
            Apply margin option to Html Headers and Footers. default is false which make Html Headers and Footers have 0 margin.
            Only support ChromeRender
            <para>Default value is false.</para></summary>
            <seealso cref="P:IronPdf.ChromePdfRenderOptions.UseMarginsOnHeaderAndFooter" />
            <exclude />
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.FitToPaperWidth">
            <exclude />
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.DPI">
            <exclude />
        </member>
        <member name="P:IronPdf.ChromePdfRenderOptions.JpegQuality">
            <exclude />
        </member>
        <member name="T:IronPdf.Cleaner">
            <summary>
            The Cleaner class can be used to scan or sanitize (remove any potentially harmful content) PDF document.
            </summary>
        </member>
        <member name="M:IronPdf.Cleaner.SanitizeWithSvg(System.String)">
            <summary>
            Sanitize PDF by convert PDF To an SVG image and convert it back to PDF.
            Returns a new <see cref="T:IronPdf.PdfDocument" /> object.
            <list type="bullet"><item><description>Quicker than <see cref="M:IronPdf.Cleaner.SanitizeWithBitmap(IronPdf.PdfDocument)" /></description></item><item><description>Resulted as a serachable PDF</description></item><item><description>Layout might be inconsistency.</description></item></list></summary>
            <param name="pdfFilePath">A PDF File path.</param>
        </member>
        <member name="M:IronPdf.Cleaner.SanitizeWithSvg(IronPdf.PdfDocument)">
            <summary>
            Sanitize PDF by convert PDF To an SVG image and convert it back to PDF.
            Returns a new <see cref="T:IronPdf.PdfDocument" /> object.
            <list type="bullet"><item><description>Quicker than <see cref="M:IronPdf.Cleaner.SanitizeWithBitmap(IronPdf.PdfDocument)" /></description></item><item><description>Resulted as a serachable PDF</description></item><item><description>Layout might be inconsistency.</description></item></list></summary>
            <param name="pdfDocument">An <see cref="T:IronPdf.PdfDocument" /> of document.</param>
        </member>
        <member name="M:IronPdf.Cleaner.SanitizeWithBitmap(System.String)">
            <summary>
            Sanitize PDF by convert PDF To an Bitmap image and convert it back to PDF.
            Returns a new <see cref="T:IronPdf.PdfDocument" /> object.
            </summary>
            <list type="bullet">
                <item>
                    <description>Slower than <see cref="M:IronPdf.Cleaner.SanitizeWithSvg(IronPdf.PdfDocument)" /></description>
                </item>
                <item>
                    <description>Visually look the same</description>
                </item>
                <item>
                    <description>Resulted as a non-serachable PDF</description>
                </item>
            </list>
            <param name="pdfFilePath">A PDF File path.</param>
        </member>
        <member name="M:IronPdf.Cleaner.SanitizeWithBitmap(IronPdf.PdfDocument)">
            <summary>
            Sanitize PDF by convert PDF To an Bitmap image and convert it back to PDF.
            Returns a new <see cref="T:IronPdf.PdfDocument" /> object.
            </summary>
            <list type="bullet">
                <item>
                    <description>Slower than <see cref="M:IronPdf.Cleaner.SanitizeWithSvg(IronPdf.PdfDocument)" /></description>
                </item>
                <item>
                    <description>Visually look the same</description>
                </item>
                <item>
                    <description>Resulted as a non-serachable PDF</description>
                </item>
            </list>
            <param name="pdfDocument">An <see cref="T:IronPdf.PdfDocument" /> of document.</param>
        </member>
        <member name="M:IronPdf.Cleaner.ScanPdf(System.String,System.String[])">
            <summary>
            Scan for any risk that might exists in a PDF. 
            </summary>
            <param name="pdfFilePath">A PDF File path</param>
            <param name="yaraFiles">Optional external yara rule files, this will override the default yara rules</param>
            <returns>scan result as an <see cref="T:IronPdf.CleanerScanResult" /> object</returns>
        </member>
        <member name="M:IronPdf.Cleaner.ScanPdf(System.Byte[],System.String[])">
            <summary>
            Scan for any risk that might exists in a PDF. 
            </summary>
            <param name="pdfBytes">a PDF bytes</param>
            <param name="yaraFiles">Optional external yara rule files, this will override the default yara rules</param>
            <returns>scan result as an <see cref="T:IronPdf.CleanerScanResult" /> object</returns>
        </member>
        <member name="M:IronPdf.Cleaner.ScanPdf(IronPdf.PdfDocument,System.String[])">
            <summary>
            Scan for any risk that might exists in a PDF. 
            </summary>
            <param name="pdfDocument">a PdfDocument object</param>
            <param name="yaraFiles">Optional external yara rule files, this will override the default yara rules</param>
            <returns>scan result as an <see cref="T:IronPdf.CleanerScanResult" /> object</returns>
        </member>
        <member name="T:IronPdf.CleanerScanResult">
            <summary>
            Result from Cleaner scan functions. E.g. <see cref="M:IronPdf.Cleaner.ScanPdf(IronPdf.PdfDocument,System.String[])" /></summary>
        </member>
        <member name="P:IronPdf.CleanerScanResult.IsDetected">
            <summary>
            Is any risk was found
            </summary>
        </member>
        <member name="P:IronPdf.CleanerScanResult.Risks">
            <summary>
            A list of risk that has been detected.
            </summary>
        </member>
        <member name="M:IronPdf.CleanerScanResult.ToString">
            <summary>
            A text summary of CleanerScanResult 
            </summary>
            <returns>A text summary of CleanerScanResult </returns>
        </member>
        <member name="T:IronPdf.DocxToPdfRenderer">
            <summary>
            IronPdf.DocxToPdfRenderer allows any Docx to be turned into a PDF document using an up-to-date embedded Chrome browser.
            </summary>
        </member>
        <member name="P:IronPdf.DocxToPdfRenderer.RenderingOptions">
            <summary>
            Rendering options
            </summary>
        </member>
        <member name="M:IronPdf.DocxToPdfRenderer.#ctor">
            <summary>
            Create a new Docx To PDF renderer.
            </summary>
        </member>
        <member name="M:IronPdf.DocxToPdfRenderer.#ctor(IronPdf.DocxPdfRenderOptions)">
            <summary>
            Create a new Docx to PDF renderer using the specified options
            </summary>
            <param name="Options">Rendering options</param>
        </member>
        <member name="M:IronPdf.DocxToPdfRenderer.RenderDocxAsPdf(System.String,IronPdf.DocxPdfRenderOptions)">
            <summary>
            Creates a PDF file from an Word document and returns it as an <see cref="T:IronPdf.PdfDocument" /> object which can be edited and saved to disk or served on a website.
            </summary>
            <param name="WordDocumentPath">The word document full path to be rendered as a PDF.</param>
            <param name="Options">An instance of <see cref="T:IronPdf.DocxPdfRenderOptions" /> that allows configuration of DocxRenderer "Docx to PDF" rendering parameters. </param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /> with the Docx rendered as its contents.
            </returns>
        </member>
        <member name="M:IronPdf.DocxToPdfRenderer.RenderDocxAsPdf(System.Byte[],IronPdf.DocxPdfRenderOptions)">
            <summary>
            Creates a PDF file from an Word document and returns it as an <see cref="T:IronPdf.PdfDocument" /> object which can be edited and saved to disk or served on a website.
            </summary>
            <param name="WordDocumentBytes">The word document byte array to be rendered as a PDF.</param>
            <param name="Options">An instance of <see cref="T:IronPdf.DocxPdfRenderOptions" /> that allows configuration of DocxRenderer "Docx to PDF" rendering parameters. </param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /> with the Docx rendered as its contents.
            </returns>
        </member>
        <member name="M:IronPdf.DocxToPdfRenderer.RenderDocxAsPdf(System.IO.Stream,IronPdf.DocxPdfRenderOptions)">
            <summary>
            Creates a PDF file from an Word document and returns it as an <see cref="T:IronPdf.PdfDocument" /> object which can be edited and saved to disk or served on a website.
            </summary>
            <param name="WordDocumentStream">The word document stream to be rendered as a PDF.</param>
            <param name="Options">An instance of <see cref="T:IronPdf.DocxPdfRenderOptions" /> that allows configuration of DocxRenderer "Docx to PDF" rendering parameters. </param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /> with the Docx rendered as its contents.
            </returns>
        </member>
        <member name="M:IronPdf.DocxToPdfRenderer.RenderDocxAsPdfAsync(System.String,IronPdf.DocxPdfRenderOptions)">
            <summary>,
            Creates a PDF file from an Word document and returns it as an <see cref="T:IronPdf.PdfDocument" /> object which can be edited and saved to disk or served on a website.
            <para> Async version of the RenderDocxAsPdf method.</para></summary>
            <param name="WordDocumentPath">The word document full path to be rendered as a PDF.</param>
            <param name="Options">An instance of <see cref="T:IronPdf.DocxPdfRenderOptions" /> that allows configuration of DocxRenderer "Docx to PDF" rendering parameters. </param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /> with the Docx rendered as its contents.
            </returns>
        </member>
        <member name="M:IronPdf.DocxToPdfRenderer.RenderDocxMailMergeAsPdfAsync``1(System.Collections.Generic.List{``0},System.String,IronPdf.DocxPdfRenderOptions)">
            <summary>
            Creates a PDF file from an Word document and returns it as an <see cref="T:IronPdf.PdfDocument" /> object which can be edited and saved to disk or served on a website.
            <para> Async version of the RenderDocxAsPdf method.</para></summary>
            <param name="RecipientsDataList">List of recepient data</param>
            <param name="WordDocumentPath">The word document full path to be rendered as a PDF.</param>
            <param name="Options">An instance of <see cref="T:IronPdf.DocxPdfRenderOptions" /> that allows configuration of DocxRenderer "Docx to PDF" rendering parameters. </param>
            <returns>
            A <see cref="T:IronPdf.PdfDocument" /> with the Docx rendered as its contents.
            </returns>
        </member>
        <member name="T:IronPdf.DocxPdfRenderOptions">
            <summary>
            Margin values which can be copied from the main document to headers and footers applied to the document
            </summary>
            <summary>
            Docx To PDF output options for <see cref="T:IronPdf.DocxToPdfRenderer" />. Specifies options such as Paper-Size, DPI, Headers and Footers and other Chromium specific browser setup options.
            </summary>
        </member>
        <member name="M:IronPdf.DocxPdfRenderOptions.#ctor">
            <summary>
            Default Constructor.  Creates a new instance of DocxPdfRenderOptions with default settings.
            </summary>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.FirstPageNumber">
            <summary>
            Method callback to be invoked whenever a browser JavaScript console message becomes available.
            </summary>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.GrayScale">
            <summary>
            Outputs a black-and-white PDF
            <para>Default value is false.</para></summary>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.InputEncoding">
            <summary>
            The input character encoding as a string;
            <para>Default value is Encoding.UTF8.</para></summary>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.MarginBottom">
            <summary>
            Bottom Pdf "paper" margin in millimeters.  Set to zero for border-less and commercial printing applications.
            <para>Default value is 25.</para></summary>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.MarginLeft">
            <summary>
            Left Pdf "paper" margin in millimeters.  Set to zero for border-less and commercial printing applications.
            <para>Default value is 25.</para></summary>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.MarginRight">
            <summary>
            Right Pdf "paper" margin in millimeters.  Set to zero for border-less and commercial printing applications.
            <para>Default value is 25.</para></summary>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.MarginTop">
            <summary>
            Top Pdf "paper" margin in millimeters.  Set to zero for border-less and commercial printing applications.
            <para>Default value is 25.</para></summary>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.PaperOrientation">
            <summary>
            The PDF paper orientation. E.g. Portrait or Landscape.
            <para>Default value is Portrait.</para></summary>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.PaperSize">
            <summary>
            Set an output paper size for PDF pages.  System.Drawing.Printing.PaperKind. <para>Use SetCustomPaperSize(int
            width, int height) for custom sizes.</para><para>Default value is A4.</para></summary>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.PrintHtmlBackgrounds">
            <summary>
            Prints background-colors and images from Html.
            <para>Default value is true.</para></summary>
        </member>
        <member name="F:IronPdf.DocxPdfRenderOptions.PaperFit">
            <summary>
            A html-to-virtual paper setup manager allowing the developer to control how content will be laid out on PDF "paper" pages.
            <para>Include options for: Deafult Chrome Behavior, Zoomed, Responsive CSS3 Layouts, SCale-To-Page &amp; Continuous Feed (e.g. reciept) style PDF page setups.</para></summary>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.ForcePaperSize">
            <summary>
            Force page sizes to be exactly what is specified via <see cref="P:IronPdf.DocxPdfRenderOptions.PaperSize" /> by resizing the page after generating a PDF from HTML
            <para>Useful for bypassing CSS rules which specify paper size</para></summary>
            <remarks>Helps correct small errors in page size when rendering HTML to PDF</remarks>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.WaitFor">
            <summary>
            A wrapper object that holds configuration for wait-for mechanism for user to wait for certain events before rendering.
            </summary>
            <remarks>By default, it will wait for nothing.</remarks>
        </member>
        <member name="P:IronPdf.DocxPdfRenderOptions.MailMergePrintAllInOnePdfDocument">
            <summary>
            This option enables the renderer to append all merged documents into one PdfDocument. To print separate PdfDocuments corresponding to each contact, this option should be set to false. 
            </summary>
        </member>
        <member name="M:IronPdf.DocxPdfRenderOptions.SetCustomPaperSizeinCentimeters(System.Double,System.Double)">
            <summary>
            Set an output paper size for PDF pages.  Dimensions are in Centimeters.
            </summary>
            <param name="width">Custom paper width in cm.</param>
            <param name="height">&gt;Custom paper height in cm.</param>
        </member>
        <member name="M:IronPdf.DocxPdfRenderOptions.SetCustomPaperSizeInInches(System.Double,System.Double)">
            <summary>
            Set an output paper size for PDF pages.  Dimensions are in Inches.
            </summary>
            <param name="width">Custom paper width in Inches.</param>
            <param name="height">&gt;Custom paper height in Inches.</param>
        </member>
        <member name="M:IronPdf.DocxPdfRenderOptions.SetCustomPaperSizeinMilimeters(System.Double,System.Double)">
            <summary>
            Set an output paper size for PDF pages.  Dimensions are in millimeters.
            </summary>
            <param name="width">Custom paper width in millimeters.</param>
            <param name="height">&gt;Custom paper height in millimeters.</param>
        </member>
        <member name="M:IronPdf.DocxPdfRenderOptions.SetCustomPaperSizeinPixelsOrPoints(System.Double,System.Double,System.Int32)">
            <summary>
            Set an output paper size for PDF pages.  Dimensions are in screen Pixels or printer Points.
            </summary>
            <param name="width">Custom paper width in pixels/points.</param>
            <param name="height">&gt;Custom paper height in pixels/points.</param>
            <param name="DPI">&gt;Intended print resolution of the PDF.  To be clear PDFs have no fixed DPI/PPI value for rendering. 72 and 96 are common onscreen values.  300 is a comon value used in commercial printing. </param>
        </member>
        <member name="T:IronPdf.DocX.MailMergeTemplate">
            <summary>
            Template for mail merge operations and variable insertion
            </summary>
        </member>
        <member name="T:IronPdf.Editing.BarcodeStamper">
            <summary>
            This allows the user to edit an existing PDF by adding Barcode(s) and QR code(s) to PDF documents.<div />
            A subclass of <see cref="T:IronPdf.Editing.Stamper" />. Defines a Barcode PDF Stamper. Can be applied with: <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /><br /><para>To see usage and an example of BarcodeStamper, visit: https://ironpdf.com/tutorials/csharp-edit-pdf-complete-tutorial/#stamp-a-barcode-onto-a-pdf</para></summary>
        </member>
        <member name="P:IronPdf.Editing.BarcodeStamper.Value">
            <summary>
            The value of the barcode as a string.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.BarcodeStamper.BarcodeType">
            <summary>
            Barcode encoding type to use for this Stamper. Supported encoding types include: QRCode, Code128, and Code39. Please see: <see cref="T:IronPdf.Editing.BarcodeEncoding" />.
            <para>Default is QRCode</para></summary>
        </member>
        <member name="P:IronPdf.Editing.BarcodeStamper.Width">
            <summary>
            The width of the rendered barcode in pixels. Default is 250px
            </summary>
        </member>
        <member name="P:IronPdf.Editing.BarcodeStamper.Height">
            <summary>
            The height of the rendered barcode in pixels. Default is 250px
            </summary>
        </member>
        <member name="M:IronPdf.Editing.BarcodeStamper.#ctor(System.String,IronPdf.Editing.BarcodeEncoding)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Editing.BarcodeStamper" /> class.
            <para><see cref="P:IronPdf.Editing.BarcodeStamper.Width" /> and <see cref="P:IronPdf.Editing.BarcodeStamper.Height" /> are 250px each by default unless explicitly set.</para></summary>
            <param name="Value">The value of the barcode as a string.</param>
            <param name="BarcodeType">Barcode encoding type to use for this Stamper. Supported encoding types include: QRCode, Code128, and Code39.
            <seealso cref="T:IronPdf.Editing.BarcodeEncoding" />.</param>
        </member>
        <member name="M:IronPdf.Editing.BarcodeStamper.#ctor(System.String,IronPdf.Editing.BarcodeEncoding,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Editing.BarcodeStamper" /> class.
            </summary>
            <param name="Value">The value of the barcode as a string.</param>
            <param name="BarcodeType">Barcode encoding type to use for this Stamper. Supported encoding types include: QRCode, Code128, and Code39.
            <seealso cref="T:IronPdf.Editing.BarcodeEncoding" />.</param>
            <param name="Width">The width of the rendered barcode in pixels.</param>
            <param name="Height">The height of the rendered barcode in pixels.</param>
        </member>
        <member name="T:IronPdf.Editing.BarcodeEncoding">
            <summary>
            Barcode Encoding Types. Please check the supported characters for each encoding type as some do not support all symbols.
            <para>Setting a <see cref="T:IronPdf.Editing.BarcodeEncoding" /> when rendering Barcodes chooses the type and design of barcode to be generated.</para></summary>
        </member>
        <member name="F:IronPdf.Editing.BarcodeEncoding.Code128">
            <summary>
            Code 128 (1D barcode format). Code 128 is a high-density linear barcode symbology defined in ISO/IEC 15417:2007.
            <div />
            Supported characters include: All alphabetic and numeric characters.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.BarcodeEncoding.Code39">
            <summary>
            Code 39 (1D barcode format). Code 39 is a variable length, discrete barcode symbology. The Code 39 specification defines 43 characters.
            <div />
            Supported characters include: Digits from (0-9), Uppercase (A through Z), and these symbols: (-.$/+% space)
            </summary>
        </member>
        <member name="F:IronPdf.Editing.BarcodeEncoding.QRCode">
            <summary> 
            QR Code (2D barcode format).  QR code (abbreviated from Quick Response Code) is a machine-readable optical label that contains information about the item to which it is attached. A QR code uses four standardized encoding modes to efficiently store data.
            <div />
            Supported characters include: All numeric, alphanumeric, byte/binary, and Japanese kanji.
            </summary>
        </member>
        <member name="T:IronPdf.Editing.HorizontalAlignment">
            <summary>
            The horizontal alignment relative to the page.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.HorizontalAlignment.Left">
            <summary>Alignment along the left side of the page.</summary>
        </member>
        <member name="F:IronPdf.Editing.HorizontalAlignment.Center">
            <summary>Alignment along the center of the page.</summary>
        </member>
        <member name="F:IronPdf.Editing.HorizontalAlignment.Right">
            <summary>Alignment along the right side of the page.</summary>
        </member>
        <member name="T:IronPdf.Editing.HtmlStamper">
            <summary>
            This allows the user to edit an existing PDF by adding an HTML stamp.<div />
            A subclass of <see cref="T:IronPdf.Editing.Stamper" />. Defines an HTML PDF Stamper. Can be applied with: <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /><br /><para>To see usage and an example of HtmlStamper, visit: https://ironpdf.com/tutorials/csharp-edit-pdf-complete-tutorial/#stamp-html-onto-a-pdf</para></summary>
        </member>
        <member name="M:IronPdf.Editing.HtmlStamper.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Editing.HtmlStamper" /> class.
            </summary>
            <param name="html">The HTML string.</param>
        </member>
        <member name="M:IronPdf.Editing.HtmlStamper.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Editing.HtmlStamper" /> class.
            </summary>
            <param name="html">The HTML string.</param>
            <param name="baseUrlString">The HTML base URL for which references to external CSS, Javascript and Image files will be relative.</param>
        </member>
        <member name="M:IronPdf.Editing.HtmlStamper.#ctor(System.String,System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Editing.HtmlStamper" /> class.
            </summary>
            <param name="html">The HTML string.</param>
            <param name="baseUrl">The HTML base URL for which references to external CSS, Javascript and Image files will be relative.</param>
        </member>
        <member name="M:IronPdf.Editing.HtmlStamper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Editing.HtmlStamper" /> class.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.HtmlStamper.HtmlBaseUrl">
            <summary>
            The HTML base URL for which references to external CSS, Javascript and Image files will be relative. <para>A
            trick to make references relative to a your project file is <code>InnerHtmlBaseUrl = new
            Uri(System.Reflection.Assembly.GetEntryAssembly().Location).AbsoluteUri</code></para></summary>
        </member>
        <member name="P:IronPdf.Editing.HtmlStamper.CssMediaType">
            <summary>
            Enables Media="screen" CSS Styles  and StyleSheets <para>Note: By setting AllowScreenCss=false, IronPdf
            renders Stamp from HTML using CSS for media="print" as if printing a web page in a browser print dialog.</para><para>Default value is PdfCssMediaType.Screen.</para></summary>
        </member>
        <member name="T:IronPdf.Editing.ImageStamper">
            <summary>
            This allows the user to edit an existing PDF by adding an image.<div />
            A subclass of <see cref="T:IronPdf.Editing.Stamper" />. Defines an Image PDF Stamper. Can be applied with: <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /><br /><para>To see usage and an example of ImageStamper, visit: https://ironpdf.com/tutorials/csharp-edit-pdf-complete-tutorial/#stamp-an-image-onto-a-pdf</para></summary>
        </member>
        <member name="M:IronPdf.Editing.ImageStamper.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Editing.ImageStamper" /> class.
            </summary>
            <param name="imagePath">The path of the image to be stamped by this stamper</param>
        </member>
        <member name="M:IronPdf.Editing.ImageStamper.#ctor(System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Editing.ImageStamper" /> class.
            </summary>
            <param name="imageUri">The uri of the image to be stamped by this stamper</param>
        </member>
        <member name="M:IronPdf.Editing.ImageStamper.#ctor(IronSoftware.Drawing.AnyBitmap)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Editing.ImageStamper" /> class.
            </summary>
            <param name="bitmap">The image to be stamped by this stamper</param>
        </member>
        <member name="P:IronPdf.Editing.ImageStamper.ImageUri">
            <summary>
            The path of the image to be stamped by this stamper
            </summary>
        </member>
        <member name="T:IronPdf.Editing.Length">
            <summary>
            Represents a value with a specific measurement unit.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Length.Value">
            <summary>
            Gets or sets the value of this length.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Length.Unit">
            <summary>
            Gets or sets the measurement unit of this length.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Length.DPI">
            <summary>
            Gets or sets the DPI of this length.
            </summary>
        </member>
        <member name="M:IronPdf.Editing.Length.#ctor(System.Double,IronPdf.Editing.MeasurementUnit,System.Int32)">
            <summary>
            Initializes a new instance of the Length class with a specified value and measurement unit.
            </summary>
            <param name="value">The value of the length in the specified measurement unit. The default value is 0.</param>
            <param name="unit">The measurement unit of the length. The default value is `MeasurementUnit.Percentage`.</param>
            <param name="dpi">The DPI of the length. The default value is 96.</param>
        </member>
        <member name="M:IronPdf.Editing.Length.ToPoints(System.Nullable{System.Double})">
            <summary>
            Converts the `Length` value to points.
            </summary>
            <param name="refValue">Reference value (points). Required when Unit is <see cref="F:IronPdf.Editing.MeasurementUnit.Percentage" /></param>
            <returns>The converted value in points.</returns>
        </member>
        <member name="M:IronPdf.Editing.Length.ToMillimeter(System.Nullable{System.Double})">
            <summary>
            Converts the `Length` value to millimeters.
            </summary>
            <param name="refValue">Reference value (mm). Required when Unit is <see cref="F:IronPdf.Editing.MeasurementUnit.Percentage" /></param>
            <returns>The converted value in millimeters.</returns>
        </member>
        <member name="M:IronPdf.Editing.Length.ToInch(System.Nullable{System.Double})">
            <summary>
            Converts the `Length` value to inches.
            </summary>
            <param name="refValue">Reference value (inch). Required when Unit is <see cref="F:IronPdf.Editing.MeasurementUnit.Percentage" /></param>
            <returns>The converted value in inches.</returns>
        </member>
        <member name="M:IronPdf.Editing.Length.ToCentimeter(System.Nullable{System.Double})">
            <summary>
            Converts the `Length` value to centimeters.
            </summary>
            <param name="refValue">Reference value (cm). Required when Unit is <see cref="F:IronPdf.Editing.MeasurementUnit.Percentage" /></param>
            <returns>The converted value in centimeters.</returns>
        </member>
        <member name="M:IronPdf.Editing.Length.ToPixel(System.Nullable{System.Double})">
            <summary>
            Converts the `Length` value to pixel.
            </summary>
            <param name="refValue">Reference value (pixel). Required when Unit is <see cref="F:IronPdf.Editing.MeasurementUnit.Percentage" /></param>
            <returns>The converted value in pixel.</returns>
        </member>
        <member name="T:IronPdf.Editing.Stamper">
            <summary>
            Defines a PDF Stamper. <seealso cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /><br /><para>To see a full class walkthrough with diagrams and examples visit: https://ironpdf.com/tutorials/csharp-edit-pdf-complete-tutorial/#stamping-and-watermarking</para><seealso cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /></summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.Html">
            <summary>
            The HTML fragment which will be stamped onto your PDF.  All external references to JavaScript, CSS, and
            image files will be relative to <see cref="P:IronPdf.Editing.Stamper.InnerHtmlBaseUrl" />.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.Opacity">
            <summary>
            Allows the stamp to be transparent. 0 is fully invisible, 100 if fully opaque.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.Rotation">
            <summary>
            Rotates the stamp clockwise from 0 to 360 degrees as specified.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.HorizontalAlignment">
            <summary>
            The horizontal alignment of the stamp relative to the page.
            <para>Documentation: <a href="https://ironpdf.com/tutorials/csharp-edit-pdf-complete-tutorial/#anchor-stamping-and-watermarking">Stamper layout</a></para></summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.VerticalAlignment">
            <summary>
            The vertical alignment of the stamp relative to the page.
            <para>Documentation: <a href="https://ironpdf.com/tutorials/csharp-edit-pdf-complete-tutorial/#anchor-stamping-and-watermarking">Stamper layout</a></para></summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.HorizontalOffset">
            <summary>
                <para>The horizontal offset. Default value is 0, and default unit is <see cref="F:IronPdf.Editing.MeasurementUnit.Percentage" />.</para>
            Value of 0 has no effect.<div />
            Positive indicates an offset to the right direction.<div />
            Negative indicates an offset to the left direction.
            <para>Documentation: <a href="https://ironpdf.com/tutorials/csharp-edit-pdf-complete-tutorial/#anchor-stamping-and-watermarking">Stamper layout</a></para></summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.VerticalOffset">
            <summary>
                <para>The vertical offset. Default value is 0, and default unit is <see cref="F:IronPdf.Editing.MeasurementUnit.Percentage" />.</para>
            Value of 0 has no effect.<div />
            Positive indicates an offset in the downward direction.<div />
            Negative indicates an offset in the upward direction.
            <para>Documentation: <a href="https://ironpdf.com/tutorials/csharp-edit-pdf-complete-tutorial/#anchor-stamping-and-watermarking">Stamper layout</a></para></summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.MaxWidth">
            <summary>
            The maximum width of the output stamp.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.MaxHeight">
            <summary>
            The maximum height of the output stamp.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.MinWidth">
            <summary>
            The minimum width of the output stamp.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.MinHeight">
            <summary>
            The minimum height of the output stamp.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.Hyperlink">
            <summary>
            Makes stamped elements of this Stamper have an on-click hyperlink.<div />
            Note: HTML links created by &lt;a href=''&gt; tags are not reserved by stamping.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.Scale">
            <summary>
            Applies a percentage scale to the stamps to be larger or smaller. <div />
            Default is 100 (Percent) which has no effect.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.IsStampBehindContent">
            <summary>
            Set to true for apply stamp behind the content. If the content is opaque, the stamp may be invisible.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.WaitFor">
            <summary>
            A convenient wrapper to wait for various events, or just wait for amount of time.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.RenderDelay">
            <summary>
            Milliseconds to wait after Html is rendered before printing.  This can use useful when considering the
            rendering of JavaScript, Ajax or animations.
            <para>Default value for HtmlStamper is 100, The other Stamper is 0.</para></summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.Timeout">
            <summary>
            Render timeout in seconds
            <para>Default value is 60.</para></summary>
        </member>
        <member name="P:IronPdf.Editing.Stamper.IsStampAnnotation">
            <summary>
            Set to true to apply the annotation stamp to the PDF.
            <para>Default is fault.</para></summary>
        </member>
        <member name="T:IronPdf.Editing.StampLayer">
            <summary>
            Defines if a stamp should be placed behind or on-top of existing PDF contents.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.StampLayer.BehindExistingPDFContent">
            <summary>
            Stamp in the background
            </summary>
        </member>
        <member name="F:IronPdf.Editing.StampLayer.OnTopOfExistingPDFContent">
            <summary>
            Stamp in the foreground
            </summary>
        </member>
        <member name="T:IronPdf.Editing.StampLocation">
            <exclude />
        </member>
        <member name="F:IronPdf.Editing.StampLocation.TopLeft">
            <summary>
            The top left of the page. Bottom and Right will not be used.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.StampLocation.TopCenter">
            <summary>
            The top center of the page. Bottom, Left and Right will not be used.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.StampLocation.TopRight">
            <summary>
            The top right of the page. Bottom and Left will not be used.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.StampLocation.MiddleLeft">
            <summary>
            The middle left of the page. Top, Bottom and Right will not be used.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.StampLocation.MiddleCenter">
            <summary>
            The dead center of the page Top, Bottom Left and Right will not be used.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.StampLocation.MiddleRight">
            <summary>
            The middle right of the page Top, Bottom and Left will not be used.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.StampLocation.BottomLeft">
            <summary>
            The bottom left of the page. Top and Right will not be used. 
            </summary>
        </member>
        <member name="F:IronPdf.Editing.StampLocation.BottomCenter">
            <summary>
            The bottom center of the page. Top, Left and Right will not be used.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.StampLocation.BottomRight">
            <summary>
            The bottom right of the page. Top and Left will not be used.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.StampLocation.Custom">
            <summary>
            Set using Left or Right and Top or Bottom. 
            If both Left and Right was set, Left we be used. 
            If both Top and Bottom was set, Top will be used. 
            If none of Top, Bottom, Left and Right was set, will apply MiddleCenter.
            </summary>
        </member>
        <member name="T:IronPdf.Editing.TextStamper">
            <summary>
            This allows the user to edit an existing PDF by adding some stamped text.<div />
            A subclass of <see cref="T:IronPdf.Editing.Stamper" />. Defines a Text PDF Stamper. Can be applied with: <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /><br /><para>To see usage and an example of TextStamper, visit: https://ironpdf.com/tutorials/csharp-edit-pdf-complete-tutorial/#stamp-text-onto-a-pdf</para></summary>
        </member>
        <member name="M:IronPdf.Editing.TextStamper.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Editing.TextStamper" /> class.
            This will also automatically wait for all fonts to be loaded before rendering.
            </summary>
            <param name="maxWaitTime">Maximum wait time to wait for all fonts to be loaded before rendering. Default value is 10,000 ms or 10 seconds.</param>
        </member>
        <member name="M:IronPdf.Editing.TextStamper.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Editing.TextStamper" /> class.
            This will also automatically wait for all fonts to be loaded before rendering.
            </summary>
            <param name="text">The text to be stamped by the Stamper</param>
            <param name="maxWaitTime">Maximum wait time to wait for all fonts to be loaded before rendering. Default value is 10,000 ms or 10 seconds.</param>
        </member>
        <member name="P:IronPdf.Editing.TextStamper.Text">
            <summary>
            The text to be stamped by the Stamper
            </summary>
        </member>
        <member name="P:IronPdf.Editing.TextStamper.IsBold">
            <summary>
            Determines if the bold font weight is applied
            </summary>
        </member>
        <member name="P:IronPdf.Editing.TextStamper.IsItalic">
            <summary>
            Determines if the text has the italic font style applied
            </summary>
        </member>
        <member name="P:IronPdf.Editing.TextStamper.IsUnderline">
            <summary>
            Determines if the text has an underline font style applied
            </summary>
        </member>
        <member name="P:IronPdf.Editing.TextStamper.IsStrikethrough">
            <summary>
            Determines if the text has a strike-through applied
            </summary>
        </member>
        <member name="P:IronPdf.Editing.TextStamper.FontSize">
            <summary>
            Font size in px
            </summary>
        </member>
        <member name="P:IronPdf.Editing.TextStamper.Color">
            <summary>
            Font Color. Default is black.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.TextStamper.BackgroundColor">
            <summary>
            Background Color. Default is transparent.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.TextStamper.FontFamily">
            <summary>
            Font family name for the text.
            <div />  
            Note: If using a web font from https://fonts.google.com/ then you must set <see cref="P:IronPdf.Editing.TextStamper.UseGoogleFont" /> property of this TextStamper to true.
            </summary>
        </member>
        <member name="P:IronPdf.Editing.TextStamper.UseGoogleFont">
            <summary>
            Must be set to true, when using <see cref="P:IronPdf.Editing.TextStamper.FontFamily" /> from https://fonts.google.com/ as a web font
            </summary>
        </member>
        <member name="T:IronPdf.Editing.VerticalAlignment">
            <summary>
            The vertical alignment relative to the page.
            </summary>
        </member>
        <member name="F:IronPdf.Editing.VerticalAlignment.Top">
            <summary>The top of the page.</summary>
        </member>
        <member name="F:IronPdf.Editing.VerticalAlignment.Middle">
            <summary>The middle of the page.</summary>
        </member>
        <member name="F:IronPdf.Editing.VerticalAlignment.Bottom">
            <summary>The bottom of the page.</summary>
        </member>
        <member name="T:IronPdf.Editing.WaterMarkLocation">
            <exclude />
        </member>
        <member name="F:IronPdf.Editing.WaterMarkLocation.TopLeft">
            <summary>
            The top left of the page
            </summary>
        </member>
        <member name="F:IronPdf.Editing.WaterMarkLocation.TopCenter">
            <summary>
            The top center of the page
            </summary>
        </member>
        <member name="F:IronPdf.Editing.WaterMarkLocation.TopRight">
            <summary>
            The top right of the page
            </summary>
        </member>
        <member name="F:IronPdf.Editing.WaterMarkLocation.MiddleLeft">
            <summary>
            The middle left of the page
            </summary>
        </member>
        <member name="F:IronPdf.Editing.WaterMarkLocation.MiddleCenter">
            <summary>
            The dead center of the page
            </summary>
        </member>
        <member name="F:IronPdf.Editing.WaterMarkLocation.MiddleRight">
            <summary>
            The middle right of the page
            </summary>
        </member>
        <member name="F:IronPdf.Editing.WaterMarkLocation.BottomLeft">
            <summary>
            The bottom left of the page
            </summary>
        </member>
        <member name="F:IronPdf.Editing.WaterMarkLocation.BottomCenter">
            <summary>
            The bottom center of the page
            </summary>
        </member>
        <member name="F:IronPdf.Editing.WaterMarkLocation.BottomRight">
            <summary>
            The bottom right of the page
            </summary>
        </member>
        <member name="F:IronPdf.Editing.WaterMarkLocation.Custom">
            <summary>
            Set using with WaterMarkPositioning Left or Right and Top or Bottom. 
            If both Left and Right was set, Left we be used. 
            If both Top and Bottom was set, Top will be used. 
            If none of Top, Bottom, Left and Right was set, will apply MiddleCenter.
            </summary>
        </member>
        <member name="T:IronPdf.Editing.WaterMarkPositioning">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.WaterMarkPositioning.Bottom">
            <summary>
            Indent of the watermark from the bottom side of the page in mm.
            </summary>
            /// <remarks>Used when the watermark aligned to the bottom</remarks></member>
        <member name="P:IronPdf.Editing.WaterMarkPositioning.Left">
            <summary>
            Indent of the watermark from the left side of the page in mm.
            </summary>
            <remarks>Used when the watermark aligned to the left or by default</remarks>
        </member>
        <member name="P:IronPdf.Editing.WaterMarkPositioning.Right">
            <summary>
            Indent of the watermark from the right side of the page in mm.
            </summary>
            /// <remarks>Used when the watermark aligned to the right</remarks></member>
        <member name="P:IronPdf.Editing.WaterMarkPositioning.Size">
            <summary>
            Width and height of the watermark
            </summary>
        </member>
        <member name="P:IronPdf.Editing.WaterMarkPositioning.Top">
            <summary>
            Indent of the watermark from the top side of the page in mm.
            </summary>
            /// <remarks>Used when the watermark aligned to the top or by default</remarks></member>
        <member name="T:IronPdf.Editing.HtmlStamp">
            <exclude />
        </member>
        <member name="F:IronPdf.Editing.HtmlStamp.MM_TO_PT">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.AutoCenterStampContentOnStampCanvas">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.Html">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.HtmlBaseUrl">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.Hyperlink">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.Location">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.Opacity">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.Rotation">
            <exclude />
        </member>
        <member name="M:IronPdf.Editing.HtmlStamp.#ctor">
            <exclude />
        </member>
        <member name="M:IronPdf.Editing.HtmlStamp.#ctor(System.String)">
            <exclude />
        </member>
        <member name="M:IronPdf.Editing.HtmlStamp.#ctor(System.String,System.Int32,System.Int32)">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.Bottom">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.Height">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.Left">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.Right">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.Top">
            <exclude />
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.Width">
            <exclude />
        </member>
        <member name="T:IronPdf.Editing.HtmlStamp.StampLayer">
            <exclude />
        </member>
        <member name="F:IronPdf.Editing.HtmlStamp.StampLayer.BehindExistingPDFContent">
            <summary>
            Stamp in the background
            </summary>
        </member>
        <member name="F:IronPdf.Editing.HtmlStamp.StampLayer.OnTopOfExistingPDFContent">
            <summary>
            Stamp in the foreground
            </summary>
        </member>
        <member name="P:IronPdf.Editing.HtmlStamp.ZIndex">
            <exclude />
        </member>
        <member name="T:IronPdf.Engines.Chrome.ChromeInjectionModule">
            <summary>
            Ninject module for determining which Chrome client to use
            </summary>
        </member>
        <member name="T:IronPdf.Engines.Chrome.ChromeInterop">
            <summary>
            External methods from native assemblies
            </summary>
        </member>
        <member name="T:IronPdf.Engines.Chrome.ProcessManager">
            <summary>
            Manages Windows processes
            </summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.ProcessManager.Kill(System.Collections.Generic.List{System.Int32})">
            <summary>
            Kill processes with the specified process ids
            </summary>
            <param name="ids">Process IDs</param>
            <returns>Number of processes killed</returns>
        </member>
        <member name="M:IronPdf.Engines.Chrome.ProcessManager.KillAll(System.String)">
            <summary>
            Kill all processes with the specified name
            </summary>
            <param name="name">Process name</param>
            <returns>Number of processes killed</returns>
        </member>
        <member name="T:IronPdf.Engines.Chrome.ChromeGpuModes">
            <summary>
            Chrome GPU hardware utilization when rendering HTML to PDF
            </summary>
        </member>
        <member name="F:IronPdf.Engines.Chrome.ChromeGpuModes.Disabled">
            <summary>
            Disable GPU hardware utilization
            </summary>
        </member>
        <member name="F:IronPdf.Engines.Chrome.ChromeGpuModes.Software">
            <summary>
            Enable software acceleration
            </summary>
        </member>
        <member name="F:IronPdf.Engines.Chrome.ChromeGpuModes.Hardware">
            <summary>
            Enable hardware acceleration
            </summary>
        </member>
        <member name="F:IronPdf.Engines.Chrome.ChromeGpuModes.HardwareFull">
            <summary>
            Enable hardware acceleration with Vulkan features
            </summary>
        </member>
        <member name="T:IronPdf.Engines.Chrome.FitToPaperModes">
            <exclude />
        </member>
        <member name="F:IronPdf.Engines.Chrome.FitToPaperModes.None">
            <summary>
            Default Chrome PDF printing behavior. Uses <see cref="P:IronPdf.ChromePdfRenderOptions.Zoom" /> to specify zoom level.
            <para><see cref="P:IronPdf.ChromePdfRenderOptions.ViewPortWidth" /> has no effect.
            Instead, Chrome will automatically set the view port based on <see cref="P:IronPdf.ChromePdfRenderOptions.PaperSize" />.</para>
            Use <see cref="P:IronPdf.ChromePdfRenderOptions.CssMediaType" /> to specify CSS media style.
            </summary>
            <remarks>Useful when using 'print' CSS media style or printing documents to match the Chrome browser print preview.</remarks>
        </member>
        <member name="F:IronPdf.Engines.Chrome.FitToPaperModes.Zoom">
            <summary>
            Default Chrome PDF printing behavior. Uses <see cref="P:IronPdf.ChromePdfRenderOptions.Zoom" /> to specify zoom level.
            <para><see cref="P:IronPdf.ChromePdfRenderOptions.ViewPortWidth" /> has no effect.
            Instead, Chrome will automatically set the view port based on <see cref="P:IronPdf.ChromePdfRenderOptions.PaperSize" />.</para>
            Use <see cref="P:IronPdf.ChromePdfRenderOptions.CssMediaType" /> to specify CSS media style.
            </summary>
            <remarks>Useful when using 'print' CSS media style or printing documents to match the Chrome browser print preview.</remarks>
        </member>
        <member name="F:IronPdf.Engines.Chrome.FitToPaperModes.FixedPixelWidth">
            <summary>
            Fit an exact number of pixels onto each PDF page.
            <para>Uses <see cref="P:IronPdf.ChromePdfRenderOptions.ViewPortWidth" /> to specify the pixel width to fit on each PDF page.</para><para><see cref="P:IronPdf.ChromePdfRenderOptions.Zoom" /> has no effect. Instead, IronPdf will calculate the zoom level based on
            <see cref="P:IronPdf.ChromePdfRenderOptions.ViewPortWidth" /> and <see cref="P:IronPdf.ChromePdfRenderOptions.PaperSize" /></para></summary>
            <remarks>Useful when an optimal pixel width is known or printing documents to match a Chrome browser window display</remarks>
        </member>
        <member name="F:IronPdf.Engines.Chrome.FitToPaperModes.Automatic">
            <summary>
            Measures minimum HTML content width after it is rendered by the browser and calculates <see cref="P:IronPdf.ChromePdfRenderOptions.Zoom" />
            based on the width of the content.
            <para><see cref="P:IronPdf.ChromePdfRenderOptions.Zoom" /> and <see cref="P:IronPdf.ChromePdfRenderOptions.ViewPortWidth" /> have no effect and are calculated automatically by IronPdf.</para></summary>
            <remarks>Useful when fitting a wide content or content of unknown width onto a PDF page</remarks>
        </member>
        <member name="F:IronPdf.Engines.Chrome.FitToPaperModes.AutomaticFit">
            <summary>
            Measures minimum HTML content width after it is rendered by the browser using the smallest view port possible, and calculates
            <see cref="P:IronPdf.ChromePdfRenderOptions.Zoom" /> based on the width of the content.
            <para>Use <see cref="P:IronPdf.ChromePdfRenderOptions.ViewPortWidth" /> to specify the minimum number of pixels to be fit on each PDF page.</para><para><see cref="P:IronPdf.ChromePdfRenderOptions.Zoom" /> has no effect and is calculated automatically by IronPdf.</para></summary>
            <remarks>Useful when fitting smaller content onto a wide page</remarks>
        </member>
        <member name="F:IronPdf.Engines.Chrome.FitToPaperModes.ContinuousFeed">
            <summary>
            Creates a single page PDF which will force its entire content's width and height to fit into one page. Can be used for a consumer bill or receipt.
            </summary>
            <remarks>Useful when printing bill or receipt</remarks>
        </member>
        <member name="T:IronPdf.Engines.Chrome.WaitForType">
            <summary>
            Type of wait-for for the user to have a chance to control when to commit rendering.
            </summary>
        </member>
        <member name="F:IronPdf.Engines.Chrome.WaitForType.None">
            <summary>
            Default type, no condition, no special need to do anything prior to rendering.
            </summary>
        </member>
        <member name="F:IronPdf.Engines.Chrome.WaitForType.ManualTrigger">
            <summary>
            This mode will wait until user calls internal IronPdf's JavaScript function <code>window.ironpdf.notifyRender()</code> that will trigger the rendering.
            </summary>
        </member>
        <member name="F:IronPdf.Engines.Chrome.WaitForType.RenderDelay">
            <summary>
            This mode will delay for specified amount of time before begin rendering.
            This option supercedes <see cref="P:IronPdf.ChromePdfRenderOptions.RenderDelay" /></summary>
        </member>
        <member name="F:IronPdf.Engines.Chrome.WaitForType.NetworkIdle0">
            <summary>
            This mode will begin rendering when there is no new network request for 500ms as well as no inflight (outstanding) network requests.
            </summary>
            <remarks>This mode is suitable for SPA (Single-Page Application) or web page that that doesn't have any on-going long polling network request.</remarks>
        </member>
        <member name="F:IronPdf.Engines.Chrome.WaitForType.NetworkIdle2">
            <summary>
            This mode will begin rendering when there is no new network request for 500ms but allowed to have no more than 2 inflight (outstanding)
            network requests.
            </summary>
            <remarks>This mode is suitable for web application or web page that has on-going long-polling or heartbeart ping request.</remarks>
        </member>
        <member name="F:IronPdf.Engines.Chrome.WaitForType.NetworkIdleN">
            <summary>
            This mode will begin rendering when there is no new network request as per specified by user according to networkidle duration (in millisecond),
            and number of maximum allowed inflight (oustanding) network requests.
            </summary>
            <remarks>This mode is suitable for customized type of web application or web page as per user's requirement that doesn't fit by using
            <list type="bullet"><item><see cref="F:IronPdf.Engines.Chrome.WaitForType.NetworkIdle0" /></item><item><see cref="F:IronPdf.Engines.Chrome.WaitForType.NetworkIdle2" /></item></list></remarks>
        </member>
        <member name="F:IronPdf.Engines.Chrome.WaitForType.HtmlElement">
            <summary>
            This mode will begin rendering when the specified HTML element becomes existent.
            </summary>
        </member>
        <member name="F:IronPdf.Engines.Chrome.WaitForType.AllFontsLoaded">
            <summary>
            This mode will begin rendering after all fonts have been loaded; be it local, remote, or google web fonts.
            </summary>
        </member>
        <member name="T:IronPdf.Engines.Chrome.Internal.HtmlValidator">
            <summary>
            A simple HTML related validator.
            </summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.Internal.HtmlValidator.ValidateHtmlNameAndIdToken(System.String)">
            <summary>
            Validate whether the specified HTML name or id is valid according to the following rule.
            <para>Name or ID token begins with a letter ([A-Za-z]), and maybe followed by any number of letters, digits ([0-9]), hyphens ("-"), underscores ("_"), colons (":"), and periods (".").</para></summary>
            <param name="nameOrId">Name of Id to validate according to HTML's Name and Id naming rule.</param>
            <returns>Return true if the specified name or Id is valid, otherwise return false.</returns>
        </member>
        <member name="M:IronPdf.Engines.Chrome.Internal.HtmlValidator.ValidateHtmlClassAttribName(System.String)">
            <summary>
            Validate whether the specified HTML class attribute name is valid according to the following rule.
            <para>Name or ID token begins with a letter ([A-Za-z]), and maybe followed by any number of letters, digits ([0-9]), hyphens ("-"), and underscores ("_").</para></summary>
            <param name="classAttribName">Class attribute name to validate according to HTML's class attribute name naming rule.</param>
            <returns>Return true if the specified class attribute name is valid, otherwise return false.</returns>
        </member>
        <member name="T:IronPdf.Engines.Chrome.WaitFor">
            <summary>
            A managed wrapper of wait-for configurations.
            It also provides factory methods to set all related configurations as per desire waitfor type.
            </summary>
        </member>
        <member name="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime">
            <summary>
            Default maximum wait time in milliseconds
            </summary>
        </member>
        <member name="F:IronPdf.Engines.Chrome.WaitFor.DefaultRenderDelayDuration">
            <summary>
            Default render delay duration in milliseconds.
            </summary>
        </member>
        <member name="P:IronPdf.Engines.Chrome.WaitFor.Type">
            <summary>
            Get type of wait-for.
            </summary>
            <remarks>Read-only, its value is meant to be set via factory method this class provides.</remarks>
        </member>
        <member name="P:IronPdf.Engines.Chrome.WaitFor.Timeout">
            <summary>
            Get timeout duration in milliseconds.
            </summary>
            <remarks>Read-only, its value is meant to be set via factory method this class provides.</remarks>
        </member>
        <member name="P:IronPdf.Engines.Chrome.WaitFor.NetworkIdleDuration">
            <summary>
            Get network idle duration in milliseconds to be regarded as part of network idle event.
            </summary>
            <remarks>Read-only, its value is meant to be set via factory method this class provides.</remarks>
        </member>
        <member name="P:IronPdf.Engines.Chrome.WaitFor.NumAllowedInFlight">
            <summary>
            Get number of allowed inflight network request to be regarded as part of network idle event.
            </summary>
            <remarks>Read-only, its value is meant to be set via factory method this class provides.</remarks>
        </member>
        <member name="P:IronPdf.Engines.Chrome.WaitFor.RenderDelayDuration">
            <summary>
            Get amount of delay in milliseconds before rendering.
            </summary>
            <remarks>Read-only, its value is meant to be set via factory method this class provides.</remarks>
        </member>
        <member name="P:IronPdf.Engines.Chrome.WaitFor.HtmlElementQueryStr">
            <summary>
            Get HTML element query string.
            </summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.#ctor(System.Int32,System.Int32)">
            <summary>
            Default construct WaitFor object.
            Convenient method to construct a WaitFor object with default values.
            </summary>
            <param name="maxWaitTime">Maximum wait time in milliseconds until it forces rendering. Default value is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime" /></param>
            <param name="renderDelay">Render delay in milliseconds. It will delay before rendering. Default value is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultRenderDelayDuration" /></param>
            <remarks>This will set to<see cref="F:IronPdf.Engines.Chrome.WaitForType.RenderDelay" />.</remarks>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.#ctor(IronPdf.Engines.Chrome.WaitForType,System.Int32,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Intended to be used internally as our API recommends user to use convenient methods e.g. <see cref="M:IronPdf.Engines.Chrome.WaitFor.JavaScript(System.Int32)" />,
            <see cref="M:IronPdf.Engines.Chrome.WaitFor.NetworkIdle0(System.Int32)" /> and others to construct the WaitFor object instead of manually setting each data fields.
            </summary>
            <remarks>We use this internally as part of parsing ProtoBuf structure.</remarks>
            <param name="type">WaitFor type</param>
            <param name="maxWaitTime">Maximum wait time in milliseconds until it forces rendering.</param>
            <param name="networkIdleDuration">Network idle duration to be regarded as network idle event.</param>
            <param name="numAllowedInflight">Number of inflight network request allowed to have while still regarded it as network idle event.</param>
            <param name="renderDelayDuration">Amount of time to delay before rendering (in ms)</param>
            <param name="htmlElementQueryStr">HTML element query string that will be passed to JavaScript's document.querySelector().</param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.PageLoad">
            <summary>
            Basically it waits for nothing, but will render as soon as the page loaded.
            <para>There is no need to call this method if user desires to normally render the page. It is mostly useful to reset WaitFor configurations back to wait for nothing.</para><remarks>Page loaded in this case means only loaded in DOM, not the resource loaded.</remarks></summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.RenderDelay(System.Int32)">
            <summary>
            This method proceeds rendering by introducing an initial delay before rendering.
            </summary>
            <param name="delay">Delay time in milliseconds before rendering</param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.JavaScript(System.Int32)">
            <summary>
            This method proceeds rendering by waiting until user calls IronPdf's internal JavaScript function <code>window.ironpdf.notifyRender()</code>.
            </summary>
            <param name="maxWaitTime">Maximum wait time in milliseconds until it forces rendering. Default value is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime" /></param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.NetworkIdle0(System.Int32)">
            <summary>
            This method proceeds rendering by waiting until it internally detects a network idle event when there is no network activity.
            after at least 500ms as well as no inflight (outstanding) network requests.
            </summary>
            <param name="maxWaitTime">Maximum wait time in milliseconds until it forces rendering. Default value is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime" /></param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.NetworkIdle2(System.Int32)">
            <summary>
            This method proceeds rendering by waiting until it internally detects a network idle event when there is no network activity
            after at least 500ms as well as at maximum of 2 inflight (outstanding) network request.
            </summary>
            <param name="maxWaitTime">Maximum wait time in milliseconds until it forces rendering. Default value is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime" /></param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.NetworkIdle(System.Int32,System.Int32,System.Int32)">
            <summary>
            This method proceeds rendering by waiting until it internally detects a network idle event when there is no network activity
            after at least specified <em>networkIdleDuration</em> as well as at maximum of <em>maxNumAllowedInflight</em> inflight (outstanding) network requests.
            </summary>
            <param name="networkIdleDuration">Duration of time in milliseconds to regard as network idle event</param>
            <param name="maxNumAllowedInflight">Maximum number of allowed inflight network requests to not invalidate network idle event</param>
            <param name="maxWaitTime">Maximum wait time in milliseconds until it forces rendering. Default value is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime" /></param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.HtmlQuerySelector(System.String,System.Int32)">
            <summary>
            This method proceeds rendering by waiting until it finds the HTML element via the specified query string which is executed by a JavaScript function <code>document.querySelector()</code>.
            <para><a href="https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector">Read querySelector() documentation</a>.</para></summary>
            <param name="htmlQueryStr">HTML element query string to query for with Javascript's document.querySelector()</param>
            <param name="maxWaitTime">Maximum wait time (in ms) until it forces rendering. Default is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime" />.</param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.HtmlElementById(System.String,System.Int32)">
            <summary>
            This method proceeds rendering by waiting until it finds the HTML element that has the same id as specified one.
            <para>Related JavaScript API that works similarly to this method is <a href="https://developer.mozilla.org/en-US/docs/Web/API/Document/getElementById">getElementById()</a>
            but instead it makes a query directly via <code>document.querySelector()</code> as it calls <see cref="M:IronPdf.Engines.Chrome.WaitFor.HtmlQuerySelector(System.String,System.Int32)" /> internally.</para></summary>
            <exception cref="T:IronSoftware.Exceptions.IronPdfInputException">Thrown if input id is invalid not according to HTML name and Id naming rule.</exception>
            <param name="id">Target element Id. Id token must begin with a letter ([A-Za-z]) and may be followed by any number of letters, digits ([0-9]), hyphens ("-"), underscores ("_"), colons (":"), and periods (".").</param>
            <param name="maxWaitTime">Maximum wait time (in ms) until it forces rendering. Default is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime" />.</param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.HtmlElementByName(System.String,System.Int32)">
            <summary>
            This method proceeds rendering by waiting until it finds the element with the attribute name as of the specified one.
            <para>Related JavaScript API that works similarly to this method is <a href="https://developer.mozilla.org/en-US/docs/Web/API/Document/getElementsByName">getElementsByName()</a>
            but effective only for first found element from the result. Instead it makes a query directly via <code>document.querySelector()</code> as it calls <see cref="M:IronPdf.Engines.Chrome.WaitFor.HtmlQuerySelector(System.String,System.Int32)" /> internally.</para></summary>
            <exception cref="T:IronSoftware.Exceptions.IronPdfInputException">Thrown if input name is invalid not according to HTML name and Id naming rule.</exception>
            <param name="name">Target element name. Name token must begin with a letter ([A-Za-z]) and may be followed by any number of letters, digits ([0-9]), hyphens ("-"), underscores ("_"), colons (":"), and periods (".").</param>
            <param name="maxWaitTime">Maximum wait time (in ms) until it forces rendering. Default is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime" />.</param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.HtmlElementByTagName(System.String,System.Int32)">
            <summary>
            This method proceeds rendering by waiting until it finds the HTML element that itself has the same tag name as of specified one.
            <para>Related JavaScript API that works similarly to this method is <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element/getElementsByTagName">getElementsByTagName()</a>
            but effective only for first found element from the result. Instead it makes a query directly via <code>document.querySelector()</code> as it calls <see cref="M:IronPdf.Engines.Chrome.WaitFor.HtmlQuerySelector(System.String,System.Int32)" /> internally.</para></summary>
            <exception cref="T:IronSoftware.Exceptions.IronPdfInputException">Thrown if input name is invalid not according to HTML name and Id naming rule.</exception>
            <param name="tagName">Target element's tag name. Tag name token must begin with a letter ([A-Za-z]) and may be followed by any number of letters, digits ([0-9]), hyphens ("-"), underscores ("_"), colons (":"), and periods (".").</param>
            <param name="maxWaitTime">Maximum wait time (in ms) until it forces rendering. Default is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime" />.</param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.HtmlElementByClassName(System.String,System.Int32)">
            <summary>
            This method proceeds rendering by waiting until it finds the HTML element whose the class name as of the specified one.
            <para>Related JavaScript API that works similarly to this method is <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element/getElementsByClassName">getElementsByClassName()</a>
            but effective only for first found element from the result. Instead it makes a query directly via <code>document.querySelector()</code> as it calls <see cref="M:IronPdf.Engines.Chrome.WaitFor.HtmlQuerySelector(System.String,System.Int32)" /> internally.</para></summary>
            <exception cref="T:IronSoftware.Exceptions.IronPdfInputException">Thrown if input name is invalid not according to HTML name and Id naming rule.</exception>
            <param name="classAttribName">Target element's class attribute name. Class attribute name token must begin with a letter ([A-Za-z]) and may be followed by any number of letters, digits ([0-9]), hyphens ("-"), and underscores ("_").</param>
            <param name="maxWaitTime">Maximum wait time (in ms) until it forces rendering. Default is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime" />.</param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.WaitFor.AllFontsLoaded(System.Int32)">
            <summary>
            This method proceeds rendering by waiting until all of its fonts have been loaded. Such font types can local, remote, or google web fonts.
            </summary>
            <param name="maxWaitTime">maximum wait time (in ms) until it forces rendering. Default is <see cref="F:IronPdf.Engines.Chrome.WaitFor.DefaultMaxWaitTime" />.</param>
        </member>
        <member name="T:IronPdf.Engines.Chrome.CefBrowserSettings.InteropStruct">
            <summary>
            Cef browser settings struct
            </summary>
            <remarks>Layout must match C++ counterpart</remarks>
        </member>
        <member name="M:IronPdf.Engines.Chrome.CefRenderSettings.TranslateMarginType(System.String)">
            <summary>
            Translate a margin type string into an enum value
            </summary>
            <param name="type">Margin type string</param>
            <returns>Equivalent enum value</returns>
        </member>
        <member name="P:IronPdf.Engines.Chrome.CefRenderSettings.EnableEmulateScreenMediaType">
            <summary>
            Enable emulation of "screen" css media type
            </summary>
        </member>
        <member name="P:IronPdf.Engines.Chrome.CefRenderSettings.EnableLandscapeOrientation">
            <summary>
            Enable landscape orientation
            </summary>
        </member>
        <member name="P:IronPdf.Engines.Chrome.CefRenderSettings.MaxHeaderHeight">
            <summary>
            Max header height in pixels
            </summary>
        </member>
        <member name="P:IronPdf.Engines.Chrome.CefRenderSettings.MaxFooterHeight">
            <summary>
            Max footer height in pixels
            </summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.CefRenderSettings.GetPointer">
            <exclude />
        </member>
        <member name="T:IronPdf.Engines.Chrome.GrpcChromeClient">
            <summary>
            Chrome client implementation which facilitates requests with a remote server via gRPC
            </summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.GrpcChromeClient.#ctor">
            <summary>
            Create a new gRPC Chrome client
            </summary>
        </member>
        <member name="T:IronPdf.Engines.Chrome.IChromeClient">
            <summary>
            Chrome-related functionality definitions
            </summary>
            <remarks>Should contain any functionality directly related to Chrome, and should NOT contain functionality related to Pdfium</remarks>
        </member>
        <member name="T:IronPdf.Engines.Chrome.LocalChromeClient">
            <summary>
            Chrome client implementation which uses interop to satisfy functionality using native code
            </summary>
            <remarks>All HTML-to-PDF related logic should go here</remarks>
        </member>
        <member name="M:IronPdf.Engines.Chrome.LocalChromeClient.#ctor">
            <summary>
            Create a new local chrome client
            </summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.LocalChromeClient.MeasureHTML(System.String,System.Uri,System.String,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Measure the size of an HTML fragment.
            </summary>
            <param name="html">The HTML fragment to measure.</param>
            <param name="baseUrl">Optional: a base URL.</param>
            <param name="proxy">Optional: a proxy.</param>
            <param name="renderOptions">Render options to use when rendering the HTML fragment.</param>
            <returns>The size of the HTML fragment.</returns>
        </member>
        <member name="M:IronPdf.Engines.Chrome.LocalChromeClient.TrySubprocess(System.String)">
            <summary>
            Try to access CEF subprocess executable
            </summary>
            <param name="path">Subprocess executable path</param>
            <returns>True if the subprocess can be successfully executed, false otherwise</returns>
        </member>
        <member name="M:IronPdf.Engines.Chrome.LocalChromeClient.CreateStaticHtmlHeadersAndFooters(IronSoftware.IDocumentId,IronPdf.ChromePdfRenderOptions,IronPdf.Engines.Chrome.CefRenderSettings,IronPdf.Engines.Chrome.CefBrowserSettings,System.Collections.Generic.IEnumerable{System.Int32},System.Int32,System.String,System.String,System.Int32,System.Int32,IronSoftware.Drawing.Color,IronSoftware.Drawing.Color)">
            <summary>
            Creates HTML Headers and Footers that do not change. The method renders the header/footer based on page size, caches it, merges all the renders to their respective pages, then
            destroys the renders.
            </summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.LocalChromeClient.CreateDynamicHtmlHeadersAndFooters(IronSoftware.IDocumentId,IronPdf.ChromePdfRenderOptions,IronPdf.Engines.Chrome.CefRenderSettings,IronPdf.Engines.Chrome.CefBrowserSettings,System.Collections.Generic.IEnumerable{System.Int32},System.Int32,System.String,System.String,System.Int32,System.Int32,IronSoftware.Drawing.Color,IronSoftware.Drawing.Color)">
            <summary>
            Creates HTML Headers and Footers that differ from page to page (e.g. headers/footers with page numbers). This method renders each header/footer, merges them with their respective page,
            then destroys the render.
            </summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.LocalChromeClient.FormatAndRenderHtmlHeaderFooter(IronSoftware.IDocumentId,IronPdf.ChromePdfRenderOptions,IronPdf.Engines.Chrome.CefRenderSettings,IronPdf.Engines.Chrome.CefBrowserSettings,System.Int32,System.String,System.String,System.Int32,System.Int32,IronSoftware.Drawing.Color,IronSoftware.Drawing.Color,System.ValueTuple{System.Double,System.Double},System.Collections.Generic.List{System.Int32})">
            <summary>
            Performs the actual formatting and rendering of a dynamic HTML Header/Footer. Involves setting the correct dimensions on the page, replacing text, and performing the actual rendering.
            </summary>
        </member>
        <member name="T:IronPdf.Engines.Chrome.PersistentThreadLocalChromeClient">
            <summary>
            This is A wrapper of LocalChromeClient That force to invoke any chrome interop as a single persistent thread
            Useful for macOS, because CEF initialize and CEF function (render) in macOS required the same thread.
            </summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.PersistentThreadLocalChromeClient.#ctor">
            <summary>
            internal ctor
            </summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.PersistentThreadLocalChromeClient.ProcessTasks">
            a loop that wait for task that pass to this thread
        </member>
        <member name="M:IronPdf.Engines.Chrome.PersistentThreadLocalChromeClient.InvokeInPersistentThread(System.Action)">
            <summary>
            run action in this thread
            </summary>
            <param name="action" />
            <returns />
        </member>
        <member name="M:IronPdf.Engines.Chrome.PersistentThreadLocalChromeClient.InvokeInPersistentThread``1(System.Func{``0})">
            <summary>
            run function in this thread and get result as a task
            </summary>
            <param name="func" />
            <typeparam name="T" />
            <returns />
        </member>
        <member name="M:IronPdf.Engines.Chrome.PersistentThreadLocalChromeClient.Dispose">
            <summary>
            stop the persistent thread
            </summary>
        </member>
        <member name="T:IronPdf.Engines.Chrome.VirtualPaperLayoutManager">
            <summary>
             Manages HtmlToPdf page layout behavior when rendering HTML to <see cref="P:IronPdf.ChromePdfRenderOptions.PaperSize">virtual PDF paper</see>.
             <para>Provides options for Responsive CSS, Zoom, Fit-To-Page and Continuous feed printing options that are not available in standard Chrome.</para><para>Only one option may be selected at a time.</para></summary>
            <seealso cref="T:IronPdf.ChromePdfRenderOptions" />
            <seealso cref="P:IronPdf.ChromePdfRenderOptions.PaperSize" />
        </member>
        <member name="M:IronPdf.Engines.Chrome.VirtualPaperLayoutManager.UseChromeDefaultRendering">
            <summary>
            Lays out PDF pages in the same way as when viewed from Google Chrome's print preview.
            <para>Responsive CSS viewport is interpreted based on the width of the <see cref="P:IronPdf.ChromePdfRenderOptions.PaperSize">Specified Paper Size</see>.  To change this responsive behavior use <see cref="M:IronPdf.Engines.Chrome.VirtualPaperLayoutManager.UseResponsiveCssRendering(System.Int32)" /></para></summary>
        </member>
        <member name="M:IronPdf.Engines.Chrome.VirtualPaperLayoutManager.UseScaledRendering(System.Int32)">
            <summary>
             Adopts a layout which behaves in the same way the 'Chrome Print Preview' does for a given paper size, with an additional zoom level applied to allow content to be manually scaled by the developer.
            <para>Responsive CSS is interpreted based on the width of the <see cref="P:IronPdf.ChromePdfRenderOptions.PaperSize">Specified Paper Size</see></para></summary>
            <param name="ZoomPercentage">A percentage based scale factor on the HTML document.</param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.VirtualPaperLayoutManager.UseResponsiveCssRendering(System.Int32)">
            <summary>
            Uses Responsive CSS to define the rendering of the HTML based on the ViewPortWidth parameter.
            <para>Content will attempt to scale the rendered content content to fill the width of the <see cref="P:IronPdf.ChromePdfRenderOptions.PaperSize">Specified Paper Size</see>.</para>\
            <para>Set <see cref="P:IronPdf.ChromePdfRenderOptions.CssMediaType" /> to choose between paper and screen CSS interpretations. </para></summary>
            <param name="ViewPortWidth">A pixel based virtual browser viewport for responsive CSS designs.</param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.VirtualPaperLayoutManager.UseFitToPageRendering(System.Int32)">
            <summary>
            Scales content to fit the specified <see cref="P:IronPdf.ChromePdfRenderOptions.PaperSize" />. This mode measures minimum HTML content width after it is rendered by the browser, and then scales that content to fit to 1 sheet of paper wide where possible.
            <para>A minimum width can be set to control scaling and also to ensure that responsive CSS rules are correctly applied.</para></summary>
            <param name="MinimumPixelWidth">A pixel based minimum width for the document. Can help HTML elements to display correctly and respond appropriately to CSS3 responsive layout rules.</param>
        </member>
        <member name="M:IronPdf.Engines.Chrome.VirtualPaperLayoutManager.UseContinuousFeedRendering(System.Double,System.Int32)">
            <summary>
            Creates a single page PDF which will force its entire content's width and height to fit into one page. Can be used for a consumer bill or receipt.
            </summary>
            <param name="width">The width in millimeters to apply to the PDF page. Default is 80</param>
            <param name="margin">The margin in millimeters to apply to the PDF page. Default is 5</param>
        </member>
        <member name="T:IronPdf.Engines.DocumentPostProcessor">
            <summary>
            Various methods useful for post-processing a document which has been recently rendered from HTML
            </summary>
        </member>
        <member name="M:IronPdf.Engines.DocumentPostProcessor.ManageTitleAndApplyHeaderFooters(IronPdf.PdfDocument,IronPdf.ChromePdfRenderOptions,System.String)">
            <summary>
            The Rendered Pdf will have Document.MetaData.Title = htmlTitle so that we can retrieve htmlTitle from C++.
            If user set Options.Title This method will set it to Document.MetaData
            </summary>
        </member>
        <member name="T:IronPdf.Engines.IDocumentPostProcessor">
            <summary>
            Process newly rendered documents
            </summary>
        </member>
        <member name="M:IronPdf.Engines.IDocumentPostProcessor.FixMetaData(IronSoftware.IDocumentId)">
            <summary>
            Fix document metadata
            </summary>
            <param name="id">Document identifier</param>
        </member>
        <member name="M:IronPdf.Engines.IDocumentPostProcessor.FixPageSizes(IronSoftware.IDocumentId,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Fix document page sizes (force to exact size)
            </summary>
            <param name="id">Document identifier</param>
            <param name="options">Render options</param>
        </member>
        <member name="M:IronPdf.Engines.IDocumentPostProcessor.ManageTitleAndApplyHeaderFooters(IronPdf.PdfDocument,IronPdf.ChromePdfRenderOptions,System.String)">
            <summary>
            Apply headers and footers
            </summary>
            <param name="Document">Pdf document</param>
            <param name="Options">Render options</param>
            <param name="url">Url</param>
        </member>
        <member name="M:IronPdf.Engines.IDocumentPostProcessor.ProcessNewDocument(IronPdf.PdfDocument,IronPdf.ChromePdfRenderOptions,System.String)">
            <summary>
            Perform all post-processing tasks
            </summary>
            <param name="document">Pdf document</param>
            <param name="render_options">Render options</param>
            <param name="url">Url</param>
        </member>
        <member name="T:IronPdf.Engines.Marshalers.StringDelegate">
            <summary>
            Callback delegate for a method containing a single string parameter
            </summary>
            <param name="text">Text value</param>
        </member>
        <member name="T:IronPdf.Engines.NinjectKernelFactory">
            <summary>
            Creates kernel singleton for Ninject
            </summary>
        </member>
        <member name="P:IronPdf.Engines.NinjectKernelFactory.Kernel">
            <summary>
            Ninject kernel singleton
            </summary>
        </member>
        <member name="M:IronPdf.Engines.NinjectKernelFactory.Refresh">
            <summary>
            Force refresh of injection bindings
            </summary>
        </member>
        <member name="T:IronPdf.Engines.Pdfium.GrpcPdfClient">
            <summary>
            Pdf client implementation which facilitates requests with a remote server via gRPC
            </summary>
        </member>
        <member name="T:IronPdf.Engines.Pdfium.PdfiumDeployment">
            <summary>
            Deployment class for native Pdfium binaries in IronPdf
            </summary>
        </member>
        <member name="P:IronPdf.Engines.Pdfium.PdfiumDeployment.VersionFactory">
            <inheritdoc />
        </member>
        <member name="M:IronPdf.Engines.Pdfium.PdfiumDeployment.#ctor(System.String)">
            <summary>
            Create a new Pdfium deployment for IronPdf, optionally using the specified custom directory
            </summary>
            <param name="directory">Optional custom directory</param>
            <remarks>Should be instantiated using <seealso cref="T:IronSoftware.Deployment.DeploymentEngine" /></remarks>
        </member>
        <member name="M:IronPdf.Engines.Pdfium.PdfiumDeployment.GetPlatformSpecificErrorMessage(System.Exception)">
            <inheritdoc />
        </member>
        <member name="T:IronPdf.Engines.Pdfium.PdfiumInjectionModule">
            <summary>
            Ninject module for determining which Pdfium client to use
            </summary>
        </member>
        <member name="M:IronPdf.Engines.Pdfium.PdfTextVariableInserter.ReplacePdfVariableText(System.String,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String)">
            <summary>
            Replace pre-defined tokens with their associated values
            </summary>
            <param name="text">String which contains the tokens to replace</param>
            <param name="page_count">Total page count</param>
            <param name="first_page">First page index</param>
            <param name="page_index">Current page index</param>
            <param name="url">Url</param>
            <param name="htmlTitle">Html title</param>
            <param name="pdfTitle">Pdf title</param>
            <returns>String with tokens replaced</returns>
        </member>
        <member name="T:IronPdf.Engines.ISystemClient">
            <summary>
            System client functionality
            </summary>
        </member>
        <member name="M:IronPdf.Engines.ISystemClient.SetLicense(System.String)">
            <summary>
            Apply the specified license key
            </summary>
            <param name="license">License key</param>
        </member>
        <member name="M:IronPdf.Engines.ISystemClient.SetDebugEnable(System.Boolean)">
            <summary>
            Enable debugging
            </summary>
            <param name="value">Debugging value</param>
        </member>
        <member name="T:IronPdf.Engines.SystemClientAccessor">
            <summary>
            Accesses system client implementations
            </summary>
        </member>
        <member name="P:IronPdf.Engines.SystemClientAccessor.SystemClient">
            <summary>
            System client implementation
            </summary>
        </member>
        <member name="M:IronPdf.Engines.SystemClientAccessor.#ctor">
            <summary>
            Create a new PDF client accessor
            </summary>
        </member>
        <member name="T:IronPdf.Engines.SystemInjectionModule">
            <summary>
            Ninject module for determining which System client to use
            </summary>
        </member>
        <member name="T:IronPdf.Exceptions.IronPdfAssemblyVersionMismatchException">
            <summary>
            Represents errors that occur while loading assemblies during IronPdf deployment.
            </summary>
        </member>
        <member name="P:IronPdf.Exceptions.IronPdfAssemblyVersionMismatchException.Message">
            <inheritdoc />
        </member>
        <member name="T:IronPdf.Exceptions.IronPdfNativeException">
            <summary>
            Represents errors that occur in IronPDF native code
            </summary>
        </member>
        <member name="M:IronPdf.Exceptions.IronPdfNativeException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Exceptions.IronPdfNativeException" /> class.
            </summary>
        </member>
        <member name="M:IronPdf.Exceptions.IronPdfNativeException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Exceptions.IronPdfNativeException" /> class with a
            specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:IronPdf.Exceptions.IronPdfNativeException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Exceptions.IronPdfNativeException" /> class with a
            specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">
            The exception that is the cause of the current exception, or a <see langword="null" /> reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified.
            </param>
        </member>
        <member name="T:IronPdf.Exceptions.IronPdfProductException">
            <summary>
            Represents errors that occur during IronPDF execution.
            </summary>
        </member>
        <member name="M:IronPdf.Exceptions.IronPdfProductException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Exceptions.IronPdfProductException" /> class.
            </summary>
        </member>
        <member name="M:IronPdf.Exceptions.IronPdfProductException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Exceptions.IronPdfProductException" /> class with a
            specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:IronPdf.Exceptions.IronPdfProductException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.Exceptions.IronPdfProductException" /> class with a
            specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">
            The exception that is the cause of the current exception, or a <see langword="null" /> reference (<see langword="Nothing" /> in Visual Basic) if no inner exception is specified.
            </param>
        </member>
        <member name="T:IronPdf.Exceptions.PlatformAssertions">
            <summary>
            Platform-specific assertions
            </summary>
            <remarks>These methods throw exceptions</remarks>
        </member>
        <member name="M:IronPdf.Exceptions.PlatformAssertions.AssertNotMacOs(System.String)">
            <summary>
            Checks if running on MacOs and throws an exception if so
            </summary>
            <param name="reason">Additional error information</param>
            <exception cref="T:IronPdf.Exceptions.IronPdfProductException">Throws exception if running on MacOs</exception>
        </member>
        <member name="T:IronPdf.Extensions.Enumeration">
            <exclude />
        </member>
        <member name="P:IronPdf.Extensions.Enumeration.Name">
            <exclude />
        </member>
        <member name="P:IronPdf.Extensions.Enumeration.Id">
            <exclude />
        </member>
        <member name="M:IronPdf.Extensions.Enumeration.ToString">
            <exclude />
        </member>
        <member name="M:IronPdf.Extensions.Enumeration.GetAll``1">
            <exclude />
        </member>
        <member name="M:IronPdf.Extensions.Enumeration.Equals(System.Object)">
            <exclude />
        </member>
        <member name="M:IronPdf.Extensions.Enumeration.CompareTo(System.Object)">
            <exclude />
        </member>
        <member name="M:IronPdf.Extensions.Enumeration.GetHashCode">
            <exclude />
        </member>
        <member name="T:IronPdf.Extensions.MarkdownHelper">
            <summary>
            Methods for Markdown to HTML Conversion
            </summary>
        </member>
        <member name="T:IronPdf.Extensions.RtfHelper">
            <summary>
            Methods for RTF file
            </summary>
        </member>
        <member name="T:IronPdf.Extensions.Util">
            <summary>
            Useful methods for working with HTML and PDF files.
            </summary>
        </member>
        <member name="M:IronPdf.Extensions.Util.ToUri(IronSoftware.Drawing.AnyBitmap,System.Boolean)">
            <summary>
             Turns a <see cref="T:IronSoftware.Drawing.AnyBitmap" /> into a DataUri which can used to embed the Image
             directly into an HTML document. <para>Read standard <see href="https://en.wikipedia.org/wiki/Data_URI_scheme">https://en.wikipedia.org/wiki/Data_URI_scheme</see></para></summary>
            <param name="Image">A non-null <see cref="T:IronSoftware.Drawing.AnyBitmap" /></param>
            <param name="Rasterize">
             Optional flag to flatten the image to raw pixels to increase compatibility with HTML (may affect PDF file
             size and rendering time)
             </param>
            <returns>A data URL which can be used as the src attribute of an HTML &lt;img tag</returns>
        </member>
        <member name="T:IronPdf.GrpcLayer.Bookmark.GrpcBookmark">
            <summary>
                Represents a PDF bookmark as seen in the sidebar of PDF reader software to help user's navigate.
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.Bookmark.GrpcBookmark.ParentBookmarkText">
            <summary>
                Bookmark which contains this bookmark
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.Bookmark.GrpcBookmark.DestinationType">
            <summary>
                Type of destination represented by the bookmark
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.Bookmark.GrpcBookmark.NextBookmarkText">
            <summary>
                Next bookmark at the current level, if any
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.Bookmark.GrpcBookmark.PageIndex">
            <summary>
                The zero based page number that the bookmark links to.
            </summary>
            <value>
                The index of the page.
            </value>
        </member>
        <member name="P:IronPdf.GrpcLayer.Bookmark.GrpcBookmark.PreviousBookmarkText">
            <summary>
                Previous bookmark at the current level, if any
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.Bookmark.GrpcBookmark.Text">
            <summary>
                The display text of the bookmark
            </summary>
        </member>
        <member name="T:IronPdf.GrpcLayer.Bookmark.GrpcBookmarkCollection">
            <summary>
                Represents a PDF bookmark as seen in the sidebar of PDF reader software to help user's navigate.
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.Bookmark.GrpcBookmarkCollection.Bookmarks">
            <summary>
                Retrieve all bookmarks within this collection, recursively retrieve all children of bookmarks within this
                collection, and return a flat list
            </summary>
            <returns>A flattened list of all bookmarks in this collection and all of their children</returns>
        </member>
        <member name="M:IronPdf.GrpcLayer.Bookmark.GrpcBookmarkCollection.GetEnumerator">
            <summary />
            <returns />
        </member>
        <member name="T:IronPdf.GrpcLayer.IronPdfConnectionType">
            <summary>
            Remote connection type, for connecting to IronPdfEngine via remote procedure call
            </summary>
        </member>
        <member name="F:IronPdf.GrpcLayer.IronPdfConnectionType.LocalExecutable">
            <summary>
            Connect to an IronPdfEngine "server" running in an executable on your local machine
            </summary>
        </member>
        <member name="F:IronPdf.GrpcLayer.IronPdfConnectionType.Docker">
            <summary>
            Connect to an IronPdfEngine "server" running in a Docker container directly accessible from your local machine
            </summary>
        </member>
        <member name="F:IronPdf.GrpcLayer.IronPdfConnectionType.RemoteServer">
            <summary>
            Connect to an IronPdfEngine "server" running remotely and/or on the cloud
            </summary>
        </member>
        <member name="F:IronPdf.GrpcLayer.IronPdfConnectionType.CustomChannel">
            <summary>
            Connect to an IronPdfEngine "server" using custom channel
            </summary>
        </member>
        <member name="T:IronPdf.GrpcLayer.IronPdfConnectionConfiguration">
            <summary>
            Custom remote connection configuration, for a client-server model
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.Cloud">
            <summary>
            Default configuration for connecting to IronPdfEngine cloud service
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.Docker">
            <summary>
            Default configuration for connecting to IronPdfEngine docker host service
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.Executable">
            <summary>
            Default configuration for connecting to IronPdfEngine local service executable
            </summary>
        </member>
        <member name="M:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.RemoteServer(System.String,Grpc.Core.Metadata)">
            <summary>
            Custom configuration for connecting to IronPdfEngine remote server
            </summary>
        </member>
        <member name="M:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.WithCustomChannel(Grpc.Core.ChannelBase,Grpc.Core.Metadata)">
            <summary>
            Using your custom gRPC Channel <see cref="T:Grpc.Core.ChannelBase" /><br />
            You can create a channel by either create a new <see cref="T:Grpc.Core.Channel" /> object or using <see cref="M:Grpc.Net.Client.GrpcChannel.ForAddress(System.String)" /> or else to custom your own gRPC channel
            </summary>
            <param name="customChannel">Custom gRPC Channel <see cref="T:Grpc.Core.ChannelBase" /></param>
        </member>
        <member name="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.Metadata">
            <summary>
            Custom <see cref="T:Grpc.Core.Metadata" /> that will be sent with each gRPC request.
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.CustomChannel">
            <summary>
            Custom gRPC Channel <see cref="T:Grpc.Core.ChannelBase" />
            You can create a channel by either create a new <see cref="T:Grpc.Core.Channel" /> object or using <see cref="M:Grpc.Net.Client.GrpcChannel.ForAddress(System.String)" /> or else to custom your own gRPC channel
            </summary>
            <remarks>
            Setting CustomChannel will cause any configuration in this object (<see cref="T:IronPdf.GrpcLayer.IronPdfConnectionConfiguration" />) to be ignored, except for Metadata
            </remarks>
        </member>
        <member name="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.ConnectionType">
            <summary>
            The nature of the client-to-host connection (e.g. executable, Docker, remote, etc.)
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.LicenseKey">
            <summary>
            License key
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.Host">
            <summary>
            Hostname or IP address
            </summary>
        </member>
        <member name="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.Port">
            <summary>
            Host port
            </summary>
        </member>
        <member name="M:IronPdf.GrpcLayer.Utils.Util.Chunk``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
            <summary>
                Split the elements of a sequence into chunks of size at most <paramref name="size" />.
            </summary>
            <remarks>
                Every chunk except the last will be of size <paramref name="size" />.
                The last chunk will contain the remaining elements and may be of a smaller size.
            </remarks>
            <param name="source">
                An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to chunk.
            </param>
            <param name="size">
                Maximum size of each chunk.
            </param>
            <typeparam name="TSource">
                The type of the elements of source.
            </typeparam>
            <returns>
                An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the elements the input sequence split into chunks of size
                <paramref name="size" />.
            </returns>
            <exception cref="T:System.ArgumentNullException">
                <paramref name="source" /> is null.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="size" /> is below 1.
            </exception>
        </member>
        <member name="T:IronPdf.HtmlHeaderFooter">
            <summary>
            A HTML Header or Footer which will be printed onto every page of the PDF. This can be used to override
            <para>When using HtmlHeaderFooter it is important to set <see cref="P:IronPdf.HtmlHeaderFooter.HtmlFragment" /></para><para>Merge meta-data into your HTML using any of these placeholder strings: {page} {total-pages} {url} {date} {time} {html-title} {pdf-title}</para></summary>
        </member>
        <member name="F:IronPdf.HtmlHeaderFooter.FragmentHeight">
            <summary>
            When <see cref="P:IronPdf.HtmlHeaderFooter.MaxHeight" /> is set to this values, the renderer will make sure that the margins of the page are adjusted to fit the HTML fragment.
            </summary>
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.BaseUrl">
            <summary>
            The Base URL all URLS in the <see cref="P:IronPdf.HtmlHeaderFooter.HtmlFragment">HtmlFragment</see> will be relative to. This includes
            'src' attributes on images, scripts, style-sheets and also hrefs on hyper-links. <para>Note: A base URL that
            points to a directory should end with a slash.</para><para>Base URL accepts file paths as well as URLS. If
            no BaseUrl is given, the HtmlHeaderFooter BaseUrl will be inherited from the main HTML document where
            possible.</para></summary>
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.MaxHeight">
            <summary>
            Maximum Height of the HTML Header / Footer in millimeters.  This value must be set sufficiently high to display the
            full HTML header / footer content.
            </summary>
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.HtmlFragment">
            <summary>
            The HTML which will be used to render the Header or Footer should be an HTML snippet rather than a complete
            document. It may contain styles &amp; images. <para>Merge meta-data into the HtmlFragment by putting any of
            these placeholder strings into the text: {page} {total-pages} {url} {date} {time} {html-title} {pdf-title}. 
            An alternative mail-merge style using the pattern &lt;span class='total-pages'&gt;&lt;/span&gt; is also supported.</para><para>HtmlFragment is a stand-alone HTML document which does not inherit styles or settings from your
            main HTML content unless <see cref="P:IronPdf.HtmlHeaderFooter.LoadStylesAndCSSFromMainHtmlDocument" /> is set <c>true</c></para></summary>
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.LoadStylesAndCSSFromMainHtmlDocument">
            <summary>
            Loads style code blocks and links to CSS style sheets from the main HTML document (which provides the PDF
            content) into the <see cref="T:IronPdf.HtmlHeaderFooter" /> . <para>By default, HTML Headers and Footers are stand-
            alone HTML documents with their own default styles.  Setting <see cref="P:IronPdf.HtmlHeaderFooter.LoadStylesAndCSSFromMainHtmlDocument" /> to <c>true</c> will attempt to load all STYLE and LINK tags
            from the main HTML document (which renders teh PDF) into the <see cref="T:IronPdf.HtmlHeaderFooter" />.</para><para>If
            your main HTML document contains complex CSS frameworks,  styles the HEAD or BODY element heavily or loads
            CSS from javascript then this method may not work as intended.</para><para>This feature is not available
            for  RenderUrlAsPdf methods.  It works for RenderHtmlAsPdf, HTMLToPdf
            and HtmlFileToPdf  conversions only.</para><para>It is often preferable to load style sheets explicitly into
            your HTML Headers and Footers as STYLE and LINK tags within the <see cref="P:IronPdf.HtmlHeaderFooter.HtmlFragment" /> for granular
            control</para>
            Not supported in Docker mode.
            </summary>
        </member>
        <member name="M:IronPdf.HtmlHeaderFooter.Clone">
            <summary>
            Supports <see cref="T:System.ICloneable" />.  Creates a deep copy of this class instance.
            </summary>
            <returns>A deep clone of this instance.  Use explicit casting to convert object back to the intended type. </returns>
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.Height">
            <exclude />
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.CenterText">
            <exclude />
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.FontFamily">
            <exclude />
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.Font">
            <exclude />
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.FontSize">
            <exclude />
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.LeftText">
            <exclude />
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.RightText">
            <exclude />
        </member>
        <member name="P:IronPdf.HtmlHeaderFooter.Spacing">
            <exclude />
        </member>
        <member name="T:IronPdf.ImageToPdfConverter">
            <summary>
            The ImageToPdfConverter class can be used to create PDF documents from images.  It accepts both image files and
            Bitmap objects as input. <para>The ImageToPdf static methods create simple PDF
            documents containing each image as one page of the created PDF.</para><para>To rasterize PDF documents as
            images (the reverse operation), see <see cref="!:PdfDocument.ToBitmap(int)" /> and <see cref="!:PdfDocument.RasterizeToImageFiles(string, Imaging.ImageType, int)" /></para></summary>
        </member>
        <member name="M:IronPdf.ImageToPdfConverter.ImageToPdf(System.String,IronPdf.Imaging.ImageBehavior,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Converts a single image file to an identical PDF document of matching dimensions. The default PaperSize is A4. You can set it via ImageToPdfConverter.PaperSize.
            Note: Imaging.ImageBehavior.CropPage will set PaperSize equal to ImageSize.
            </summary>
            <param name="imageFileName">File path of the image file.</param>
            <param name="behavior">Describes how image should be placed on the PDF page</param>
            <param name="options">Rendering options</param>
            <returns>
            Returns a <see cref="T:IronPdf.PdfDocument" /> document which can then be edited, saved or served over the web.
            </returns>
            <exception cref="T:System.NotSupportedException">
            The file does not have a supported image format. Supported files: .apng, .avif, .bmp, .cur, .dib, .gif, .ico, .jfif, .jif,
            .jpe, .jpeg, .jpg, .pjp, .pjpeg, .png, .svg, .tif, .tiff, .webp
            </exception>
            <exception cref="T:System.IO.FileNotFoundException" />
        </member>
        <member name="M:IronPdf.ImageToPdfConverter.ImageToPdf(System.Collections.Generic.IEnumerable{System.String},IronPdf.Imaging.ImageBehavior,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Converts multiple image files to a PDF document.  Each image creates 1 page which matches the image
            dimensions. The default PaperSize is A4. You can set it via ImageToPdfConverter.PaperSize.
            Note: Imaging.ImageBehavior.CropPage will set PaperSize equal to ImageSize.
            </summary>
            <param name="imageFileNames">The image file path names.</param>
            <param name="behavior">Describes how image should be placed on the PDF page</param>
            <param name="options">Rendering options</param>
            <returns>
            Returns a <see cref="T:IronPdf.PdfDocument" /> document which can then be edited, saved or served over the web.
            </returns>
            <exception cref="T:System.NotSupportedException">
            The file does not have a supported image format. Supported files: .apng, .avif, .bmp, .cur, .dib, .gif, .ico, .jfif, .jif,
            .jpe, .jpeg, .jpg, .pjp, .pjpeg, .png, .svg, .tif, .tiff, .webp
            </exception>
            <exception cref="T:System.IO.FileNotFoundException" />
        </member>
        <member name="M:IronPdf.ImageToPdfConverter.ImageToPdf(IronSoftware.Drawing.AnyBitmap,IronPdf.Imaging.ImageBehavior,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Converts a single <see cref="T:IronSoftware.Drawing.AnyBitmap" /> object or Bitmap to a PDF document of matching
            dimensions. The default PaperSize is A4. You can set it via ImageToPdfConverter.PaperSize.
            Note: Imaging.ImageBehavior.CropPage will set PaperSize equal to ImageSize.
            </summary>
            <param name="image">The image object.   Requires a project reference to the IronSoftware.System.Drawing Assembly.</param>
            <param name="behavior">Describes how image should be placed on the PDF page</param>
            <param name="options">Rendering options</param>
            <returns>
            Returns a <see cref="T:IronPdf.PdfDocument" /> document which can then be edited, saved or served over the web.
            </returns>
            <exception cref="T:System.NotSupportedException">
            The file does not have a supported image format. Supported files: .apng, .avif, .bmp, .cur, .dib, .gif, .ico, .jfif, .jif,
            .jpe, .jpeg, .jpg, .pjp, .pjpeg, .png, .svg, .tif, .tiff, .webp
            </exception>
            <exception cref="T:System.IO.FileNotFoundException" />
        </member>
        <member name="M:IronPdf.ImageToPdfConverter.ImageToPdf(System.Collections.Generic.IEnumerable{IronSoftware.Drawing.AnyBitmap},IronPdf.Imaging.ImageBehavior,IronPdf.ChromePdfRenderOptions)">
            <summary>
            Converts multiple  <see cref="T:IronSoftware.Drawing.AnyBitmap" /> objects or Bitmaps  into a PDF document.  Each image
            creates 1 page which matches the image dimensions. The default PaperSize is A4. You can set it via ImageToPdfConverter.PaperSize.
            Note: Imaging.ImageBehavior.CropPage will set PaperSize equal to ImageSize.
            </summary>
            <param name="images">The image objects.   Requires a project reference to the IronSoftware.System.Drawing Assembly.</param>
            <param name="behavior">Describes how image should be placed on the PDF page</param>
            <param name="options">Rendering options</param>
            <returns>
            Returns a <see cref="T:IronPdf.PdfDocument" /> document which can then be edited, saved or served over the web.
            </returns>
            <exception cref="T:System.NotSupportedException">
            The file does not have a supported image format. Supported files: .apng, .avif, .bmp, .cur, .dib, .gif, .ico, .jfif, .jif,
            .jpe, .jpeg, .jpg, .pjp, .pjpeg, .png, .svg, .tif, .tiff, .webp
            </exception>
            <exception cref="T:System.IO.FileNotFoundException" />
        </member>
        <member name="T:IronPdf.Imaging.ImageBehavior">
            E
        </member>
        <member name="F:IronPdf.Imaging.ImageBehavior.CenteredOnPage">
            <summary>
            Image should be placed on center of the page
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageBehavior.FitToPage">
            <summary>
            Image should fit to the page
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageBehavior.FitToPageAndMaintainAspectRatio">
            <summary>
            Image should fit to the page and keep aspect ratio
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageBehavior.CropPage">
            <summary>
            Page should fit to the image
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageBehavior.TopLeftCornerOfPage">
            <summary>
            Image should be placed to the left top corner of the page
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageBehavior.BottomLeftCornerOfPage">
            <summary>
            Image should be placed to the left bottom corner of the page
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageBehavior.TopRightCornerOfPage">
            <summary>
            Image should be placed to the right top corner of the page
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageBehavior.BottomRightCornerOfPage">
            <summary>
            Image should be placed to the right bottom corner of the page
            </summary>
        </member>
        <member name="T:IronPdf.Imaging.ImageType">
            <summary>
            An Image File Type Enumeration used to select image file formats when converting PDF documents to images. Also
            see: <see cref="!:IronPdf.PdfDocument.RasterizeToImageFiles(string,ImageType, int)" /></summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageType.Default">
            <summary>
            Default file type. Will save images in a PNG file type unless a file pattern ends in an image file
            format such as .png or .jpeg
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageType.Bitmap">
            <summary>
            PDF pages will be rendered to windows Bitmap image files.
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageType.Jpeg">
            <summary>
            PDF pages will be rendered to JPEG image files.
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageType.Png">
            <summary>
            PDF pages will be rendered to PNG (Portable Network Graphics) image files.
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageType.Gif">
            <summary>
            PDF pages will be rendered to non-animated GIF image files. The GIF
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageType.Tiff">
            <summary>
            PDF pages will be rendered to TIFF image files.
            </summary>
        </member>
        <member name="F:IronPdf.Imaging.ImageType.MultiPageTiff">
            <summary>
            Every page of the PDF will be compiled into a single multi-page TIFF image file (aka. multi-frame TIFF).
            </summary>
        </member>
        <member name="T:IronPdf.Installation">
            <summary>
            Global installation and setup preferences for all instances of IronPDF
            </summary>
        </member>
        <member name="P:IronPdf.Installation.CustomDeploymentDirectory">
            <summary>
            Custom deployment directory for renderer native binaries.
            </summary>
        </member>
        <member name="P:IronPdf.Installation.SkipShutdown">
            <summary>
            Set to True to skip certain shutdown routines, e.g. Cef shutdown
            </summary>
            <remarks>Useful for certain environments which have persistence, e.g. IIS or web apps</remarks>
        </member>
        <member name="P:IronPdf.Installation.SkipInitialization">
            <summary>
            Set to True to skip certain initialization routines, e.g. Cef initialization
            </summary>
            <remarks>Useful for certain environments which have persistence, e.g. IIS or web apps</remarks>
        </member>
        <member name="P:IronPdf.Installation.AutomaticallyDownloadNativeBinaries">
            <summary>
            Set to True to let IronPDF try to download missing NativeBinaries from the internet at a runtime.
            </summary>
            <remarks>Useful for IronPdf.Slim</remarks>
        </member>
        <member name="P:IronPdf.Installation.ConnectionType">
            <summary>
            Get the current connection type.
            </summary>
        </member>
        <member name="P:IronPdf.Installation.LicenseKey">
            <summary>
            Synonym of IronPdf.License.LicenseKey
            </summary>
        </member>
        <member name="M:IronPdf.Installation.Initialize">
            <summary>
            Initializes IronPDF rendering engines immediately to avoid startup time on your first render.
            </summary>
        </member>
        <member name="P:IronPdf.Installation.SingleProcess">
            <summary>
            Forces Chrome renderer to perform everything in the current process, rather than using subprocesses
            </summary>
            <remarks>Warning! This feature is experimental and unstable. Useful for environments where execution permissions are not possible.</remarks>
        </member>
        <member name="P:IronPdf.Installation.EnableWebSecurity">
            <summary>
            Enable web security for Chrome renderer. Disabled by default.
            <para>
            Enabling web security will disable operations which require local disk access or cross-origin requests.
            </para><para>
            Useful when injecting untrusted HTML from users.
            </para></summary>
        </member>
        <member name="P:IronPdf.Installation.ChromeBrowserCachePath">
            <summary>
            Disk cache path for chrome browser instances
            </summary>
        </member>
        <member name="P:IronPdf.Installation.ChromeBrowserLimit">
            <summary>
            Maximum number of concurrent browsers when using the Chrome renderer
            </summary>
        </member>
        <member name="P:IronPdf.Installation.ChromeGpuMode">
            <summary>
            Chrome renderer GPU compatibility mode. In special environment like Docker or Cloud Service please use ChromeGpuModes.Disabled
            </summary>
        </member>
        <member name="P:IronPdf.Installation.LinuxAndDockerDependenciesAutoConfig">
            <summary>
            If <c>true</c> The necessary package dependencies for IronPDF rendering will we automatically installed to
            Docker and Debian / Ubuntu Linux deployments. <para>Applies only to IronPDF for .Net Core.</para><para>This
            will take a few minutes the next time you run IronPDF. Watch the Visual Studio "Output" window for
            progress.</para><para>Set this to false if manual Docker / Linux setup is more convenient:
            https://ironpdf.com/docs/questions/docker-linux/</para></summary>
            <seealso cref="P:IronSoftware.Deployment.DeploymentEngine.SkipInstallCommands" />
        </member>
        <member name="F:IronPdf.Installation.SendAnonymousAnalyticsAndCrashData">
            <summary>
            Opt in or out of sending anonymous usage statistics about IronPDF usage and performance to help us improve
            the product and developer experience. <para>Usage statistics are never sent in commercially deployed
            projects (when a license key is applied), only during development usage.</para></summary>
        </member>
        <member name="P:IronPdf.Installation.TempFolderPath">
            <summary>
            The temporary folder path which temporary files and rendering engine DLLs will be deployed to where
            necessary. The default location is the system temp folder which is appropriate for most use cases.
            </summary>
            <exception cref="T:System.ArgumentException">
                <paramref name="value" /> is a zero-length string, contains only white space, or contains one or more of the
            invalid characters defined in <see cref="M:System.IO.Path.GetInvalidPathChars" />. -or- The system could not
            retrieve the absolute path.
            </exception>
            <exception cref="T:System.Security.SecurityException">The caller does not have the required permissions.</exception>
            <exception cref="T:System.ArgumentNullException">
                <paramref name="value" /> is <see langword="null" />.</exception>
            <exception cref="T:System.NotSupportedException">
                <paramref name="value" /> contains a colon (":") that is not part of a volume identifier (for example,
            "c:\").
            </exception>
            <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
            <exception cref="T:IronPdf.Exceptions.IronPdfProductException">The property <see cref="M:IronPdf.Installation.TempFolderPath" /> is already initialized.</exception>
        </member>
        <member name="M:IronPdf.Installation.CleanupTempImages(System.Nullable{System.TimeSpan})">
            <summary>
            Method to remove temporary images from <see cref="P:IronPdf.Installation.TempFolderPath" /><para>If you used a
            custom<see cref="P:IronPdf.Installation.TempFolderPath" /> then set <see cref="P:IronPdf.Installation.TempFolderPath" /> to point
            towards your deployment directory before you call this cleanup method.</para></summary>
            <param name="expirationTime">Time from last access for files to delete. All files are deleted by default.</param>
        </member>
        <member name="M:IronPdf.Installation.ConnectToIronPdfHost(IronPdf.GrpcLayer.IronPdfConnectionConfiguration)">
            <summary>
            Connect to an IronPdf server using the specified connection parameters
            </summary>
            <param name="configuration">IronPdf server connection parameters</param>
            <seealso cref="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.Cloud" />
            <seealso cref="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.Docker" />
            <seealso cref="P:IronPdf.GrpcLayer.IronPdfConnectionConfiguration.Executable" />
        </member>
        <member name="P:IronPdf.Installation.IronPdfEngineVersion">
            Gets IronPDF engine version.
        </member>
        <member name="P:IronPdf.IPdfRenderOptions.CustomPaperWidth">
            <summary>
            Custom paper width, in mm
            </summary>
        </member>
        <member name="T:IronPdf.ImageType">
            <exclude />
        </member>
        <member name="F:IronPdf.ImageType.Default">
            <summary>
            Default file type.  WIll save images in a Bitmap file type unless a file pattern ends in an image file
            format such as .png or .jpeg
            </summary>
        </member>
        <member name="F:IronPdf.ImageType.Bitmap">
            <summary>
            PDF pages will be rendered to windows Bitmap image files.
            </summary>
        </member>
        <member name="F:IronPdf.ImageType.Jpeg">
            <summary>
            PDF pages will be rendered to JPEG image files.
            </summary>
        </member>
        <member name="F:IronPdf.ImageType.Png">
            <summary>
            PDF pages will be rendered to PNG (Portable Network Graphics) image files.
            </summary>
        </member>
        <member name="F:IronPdf.ImageType.Gif">
            <summary>
            PDF pages will be rendered to non-animated GIF image files. The GIF
            </summary>
        </member>
        <member name="F:IronPdf.ImageType.Tiff">
            <summary>
            PDF pages will be rendered to TIFF image files.
            </summary>
        </member>
        <member name="F:IronPdf.ImageType.MultiPageTiff">
            <summary>
            Every page of the PDF will be compiled into a single multi-page TIFF image file (aka. multi-frame TIFF).
            </summary>
        </member>
        <member name="T:IronPdf.Font.FontTypes">
            <exclude />
        </member>
        <member name="T:IronPdf.HtmlToPdf">
            <summary>
            IronPdf.HtmlToPdf allows any web page or Html 'snippet' to be turned into a PDF document.
            <para>Legacy support.</para></summary>
        </member>
        <member name="M:IronPdf.HtmlToPdf.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:IronPdf.HtmlToPdf" /> class.
            </summary>
        </member>
        <member name="M:IronPdf.HtmlToPdf.#ctor(IronPdf.PdfPrintOptions)">
            <exclude />
            <seealso cref="M:IronPdf.HtmlToPdf.#ctor" />
        </member>
        <member name="P:IronPdf.HtmlToPdf.PrintOptions">
            <summary>
            An instance of the <see cref="T:IronPdf.PdfPrintOptions" /> class. Sets PDF output options such as Paper-Size, DPI,
            Headers and Footers.
            </summary>
        </member>
        <member name="M:IronPdf.HtmlToPdf.Dispose">
            <summary>
            Dispose HtmlToPdf class
            </summary>
        </member>
        <member name="T:IronPdf.Logging.Logger">
            <summary>
            Provides functionality for developers to view debug logs and export log files from IronPdf.
            </summary>
            <remarks>The properties of this class redirect to <see cref="T:IronSoftware.Logger" /></remarks>
        </member>
        <member name="T:IronPdf.Logging.Logger.LoggingModes">
            <summary>
            Different Ways to log developer notices and warnings 
            </summary>
        </member>
        <member name="F:IronPdf.Logging.Logger.LoggingModes.None">
            <summary>
            Do not log messages
            </summary>
        </member>
        <member name="F:IronPdf.Logging.Logger.LoggingModes.DebugOutputWindow">
            <summary>
            Sends Messages to System.Diagnostics.Debug
            </summary>
        </member>
        <member name="F:IronPdf.Logging.Logger.LoggingModes.Console">
            <summary>
            Sends Messages to System.Console
            </summary>
        </member>
        <member name="F:IronPdf.Logging.Logger.LoggingModes.Custom">
            <summary>
            Send messages to custom logger
            </summary>
        </member>
        <member name="F:IronPdf.Logging.Logger.LoggingModes.File">
            <summary>
            Send messages to file
            </summary>
        </member>
        <member name="F:IronPdf.Logging.Logger.LoggingModes.All">
            <summary>
            Send messages to all available endpoints
            </summary>
        </member>
        <member name="P:IronPdf.Logging.Logger.CustomLogger">
            <summary>
            Custom logger implementation
            </summary>
        </member>
        <member name="P:IronPdf.Logging.Logger.EnableDebugging">
            <summary>
            OBSOLETE. Enable debugging features.
            <para>
            Please use <see cref="P:IronPdf.Logging.Logger.LoggingMode" /> instead.
            </para></summary>
        </member>
        <member name="E:IronPdf.Logging.Logger.MessageLogged">
            <summary>
            Event handler to intercept logged messages.
            </summary>
        </member>
        <member name="P:IronPdf.Logging.Logger.LoggingMode">
            <summary>
            Activate or disable detailed developer messages sent to your choice of location in the IDE or application console. 
            </summary>
        </member>
        <member name="P:IronPdf.Logging.Logger.LogFilePath">
            <summary>
            Log file path
            </summary>
        </member>
        <member name="M:IronPdf.Logging.Logger.ClearLogFiles">
            <summary>
            Removes any Iron Software log files at the <see cref="P:IronPdf.Logging.Logger.LogFilePath" />.
            </summary>
            <seealso cref="P:IronPdf.Logging.Logger.LogFilePath" />
        </member>
        <member name="T:IronPdf.PDFHeaderFooter">
            <exclude />
        </member>
        <member name="T:IronPdf.PdfPrintOptions">
            <exclude />
        </member>
        <member name="T:IronPdf.PdfPrintOptions.PdfCssMediaType">
            <summary>
            Legacy support. Has been replaced by <see cref="T:IronPdf.Rendering.PdfCssMediaType" />.
            </summary>
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfCssMediaType.Screen">
            <summary>
            Screen
            </summary>
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfCssMediaType.Print">
            <summary>
            Print
            </summary>
        </member>
        <member name="T:IronPdf.PdfPrintOptions.PdfPaperSize">
            <summary>
             Legacy support. Has been replaced by <see cref="T:IronPdf.Rendering.PdfCssMediaType" />.
             </summary>
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Letter">
            8.5in x 11in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Legal">
            8.5in x 14in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A4">
            210mm x 297mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.CSheet">
            17in x 22in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.DSheet">
            22in x 34in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.ESheet">
            34in x 44in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.LetterSmall">
            8.5in x 11in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Tabloid">
            11in x 17in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Ledger">
            17in x 11in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Statement">
            5.5in x 8.5in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Executive">
            7.25in x 10.5in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A3">
            297mm x 420mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A4Small">
            210mm x 297mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A5">
            148mm x 210mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.B4">
            250mm x 353mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.B5">
            176mm x 250mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Folio">
            8.5in x 13in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Quarto">
            215mm x 275mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Standard10x14">
            10in x 14in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Standard11x17">
            11in x 17in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Note">
            8.5in x 11in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Number9Envelope">
            3.875in x 8.875in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Number10Envelope">
            4.125in x 9.5in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Number11Envelope">
            4.5in x 10.375in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Number12Envelope">
            4.75in x 11in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Number14Envelope">
            5in x 11.5in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.DLEnvelope">
            110mm x 220mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.C5Envelope">
            162mm x 229mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.C3Envelope">
            324mm x 458mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.C4Envelope">
            229mm x 324mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.C6Envelope">
            114mm x 162mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.C65Envelope">
            114mm x 229mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.B4Envelope">
            250mm x 353mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.B5Envelope">
            176mm x 250mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.B6Envelope">
            176mm x 125mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.ItalyEnvelope">
            110mm x 230mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.MonarchEnvelope">
            3.875in x 7.5in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PersonalEnvelope">
            3.625in x 6.5in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.USStandardFanfold">
            14.875in x 11in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.GermanStandardFanfold">
            8.5in x 12in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.GermanLegalFanfold">
            8.5in x 13in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.IsoB4">
            250mm x 353mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.JapanesePostcard">
            100mm x 148mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Standard9x11">
            9in x 11in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Standard10x11">
            10in x 11in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Standard15x11">
            15in x 11in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.InviteEnvelope">
            220mm x 220mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.LetterExtra">
            9.275in x 12in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.LegalExtra">
            9.275in x 15in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.TabloidExtra">
            11.69in x 18in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A4Extra">
            236mm x 322mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.LetterTransverse">
            8.275in x 11in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A4Transverse">
            210mm x 297mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.LetterExtraTransverse">
            9.275in x 12in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.APlus">
            227mm x 356mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.BPlus">
            305mm x 487mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.LetterPlus">
            8.5in x 12.69in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A4Plus">
            210mm x 330mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A5Transverse">
            148mm x 210mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.B5Transverse">
            182mm x 257mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A3Extra">
            322mm x 445mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A5Extra">
            174mm x 235mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.B5Extra">
            201mm x 276mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A2">
            420mm x 594mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A3Transverse">
            297mm x 420mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A3ExtraTransverse">
            322mm x 445mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.JapaneseDoublePostcard">
            200mm x 148mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A6">
            105mm x 148mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.LetterRotated">
            11in x 8.5in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A3Rotated">
            420mm x 297mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A4Rotated">
            297mm x 210mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A5Rotated">
            210mm x 148mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.B4JisRotated">
            364mm x 257mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.B5JisRotated">
            257mm x 182mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.JapanesePostcardRotated">
            148mm x 100mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.JapaneseDoublePostcardRotated">
            148mm x 200mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.A6Rotated">
            148mm x 105mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.B6Jis">
            128mm x 182mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.B6JisRotated">
            182mm x 128mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Standard12x11">
            12in x 11in
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Prc16K">
            146mm x 215mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Prc32K">
            97mm x 151mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Prc32KBig">
            97mm x 151mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber1">
            102mm x 165mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber2">
            102mm x 176mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber3">
            125mm x 176mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber4">
            110mm x 208mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber5">
            110mm x 220mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber6">
            120mm x 230mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber7">
            160mm x 230mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber8">
            120mm x 309mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber9">
            229mm x 324mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber10">
            324mm x 458mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Prc16KRotated">
            146mm x 215mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Prc32KRotated">
            97mm x 151mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Prc32KBigRotated">
            97mm x 151mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber1Rotated">
            165mm x 102mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber2Rotated">
            176mm x 102mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber3Rotated">
            176mm x 125mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber4Rotated">
            208mm x 110mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber5Rotated">
            220mm x 110mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber6Rotated">
            230mm x 120mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber7Rotated">
            230mm x 160mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber8Rotated">
            309mm x 120mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber9Rotated">
            324mm x 229mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.PrcEnvelopeNumber10Rotated">
            458mm x 324mm
        </member>
        <member name="F:IronPdf.PdfPrintOptions.PdfPaperSize.Custom">
            Set using SetCustomPaperSizeInInches or SetCustomPaperSizeInMillimeters
        </member>
        <member name="T:IronPdf.PdfDocument">
            <summary>
            PdfDocument for IronPdf
            </summary>
        </member>
        <member name="T:IronPdf.PdfDocument.SignaturePermissions">
            <exclude />
        </member>
        <member name="P:IronPdf.PdfDocument.Annotations">
            <summary>
            Annotations collection
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.GetAnnotationCount(System.Int32)">
            <summary>
            Retrieve the number of annotations contained on the specific page
            </summary>
            <param name="page_index">Index of the page to add the annotation. The first page has a page_index of 0</param>
            <returns>Number of annotations contained on the page</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddTextAnnotation(IronPdf.Annotations.TextAnnotation,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Adds an annotation to a page of this <see cref="T:IronPdf.PdfDocument" /></summary>
            <param name="TextAnnotation">The annotation as a <see cref="T:IronPdf.Annotations.TextAnnotation" /> object.</param>
            <param name="PageIndex">Index of the page to add the annotation. The first page has a PageIndex of 0</param>
            <param name="X">The horizontal X position of the annotation on your page in pixels</param>
            <param name="Y">The vertical Y position of the annotation on your page in pixels. Measured from bottom upwards.</param>
            <param name="Width">The width of your annotation's icon and interactive area in pixels</param>
            <param name="Height">The height of your annotation's icon and interactive area in pixels</param>
        </member>
        <member name="M:IronPdf.PdfDocument.GetAnnotations(System.Int32)">
            <summary>
            Retrieve a list of annotations from a given page index.
            </summary>
            <param name="pageIndex">Index of the page for which to retrieve annotations. The index of the first page is 0.</param>
            <returns>A list of annotations.</returns>
        </member>
        <member name="P:IronPdf.PdfDocument.Attachments">
            <summary>
            Collection of attachments contained within the pdf document
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.AddBackgroundPdf(System.String,System.Int32)">
            <summary>
            Adds a background to each page of this PDF. The background is copied from a page in another PDF document.
            </summary>
            <param name="BackgroundPdfPath">The background PDF's file path.</param>
            <param name="BackgroundPdfPageIndex">Index (zero-based page number) to copy from the BackgroundPdf.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddBackgroundPdf(IronPdf.PdfDocument,System.Int32)">
            <summary>
            Adds a background to each page of this PDF. The background is copied from a page in another PDF document.
            </summary>
            <param name="BackgroundPdf">The Background PDF as a <see cref="T:IronPdf.PdfDocument" />.</param>
            <param name="BackgroundPdfPageIndex">Index (zero-based page number) to copy from the BackgroundPdf.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddBackgroundPdfToPage(System.Int32,System.String,System.Int32)">
            <summary>
            Adds the background to one page of this PDF.  The background is copied from a page in another PDF document.
            </summary>
            <param name="ToPageIndex">Index (zero-based page number) of the page of this PDF to which the background will be applied to.</param>
            <param name="BackgroundPdfPath">The background PDF path.</param>
            <param name="BackgroundPdfPageIndex">Index (zero-based page number) to copy from the BackgroundPdf.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddBackgroundPdfToPage(System.Int32,IronPdf.PdfDocument,System.Int32)">
            <summary>
            Adds the background to one page of this PDF.  The background is copied from a page in another PDF document.
            </summary>
            <param name="ToPageIndex">Index (zero-based page number) of the page of this PDF to which the background will be applied to.</param>
            <param name="BackgroundPdf">The Background PDF as a <see cref="T:IronPdf.PdfDocument" />.</param>
            <param name="BackgroundPdfPageIndex">Index (zero-based page number) to copy from the BackgroundPdf.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddBackgroundPdfToPageRange(System.Collections.Generic.IEnumerable{System.Int32},System.String,System.Int32)">
            <summary>
            Adds the background to specified pages of this PDF.  The background is copied from a page in another PDF
            document.
            </summary>
            <param name="ToPageIndexes">A list of Indexes (zero-based page numbers) of pages in this PDF to which the background will be applied to.</param>
            <param name="BackgroundPdfPath">The background PDF path.</param>
            <param name="BackgroundPdfPageIndex">Index (zero-based page number) to copy from the BackgroundPdf.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddBackgroundPdfToPageRange(System.Collections.Generic.IEnumerable{System.Int32},IronPdf.PdfDocument,System.Int32)">
            <summary>
            Adds the background to specified pages of this PDF.  The background is copied from a page in another PDF
            document.
            </summary>
            <param name="ToPageIndexes">A list of Indexes (zero-based page numbers) of pages in this PDF to which the background will be applied to.</param>
            <param name="BackgroundPdf">The Background PDF as a <see cref="T:IronPdf.PdfDocument" />.</param>
            <param name="BackgroundPdfPageIndex">Index (zero-based page number) to copy from the BackgroundPdf.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddBackgroundPdfToPageRange(System.Int32,System.Int32,System.String,System.Int32)">
            <summary>
            Adds the background to a range of pages in this PDF.  The background is copied from a page in another PDF
            document.
            </summary>
            <param name="StartPageIndex">First index  (zero-based page number) to start adding backgrounds to .</param>
            <param name="EndPageIndex">Last index  (zero-based page number) to end adding backgrounds to.</param>
            <param name="BackgroundPdfPath">The background PDF path.</param>
            <param name="BackgroundPdfPageIndex">Index (zero-based page number) to copy from the BackgroundPdf.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddBackgroundPdfToPageRange(System.Int32,System.Int32,IronPdf.PdfDocument,System.Int32)">
            <summary>
            Adds the background to a range of pages in this PDF.  The background is copied from a page in another PDF
            document.
            </summary>
            <param name="StartPageIndex">First index  (zero-based page number) to start adding backgrounds to .</param>
            <param name="EndPageIndex">Last index  (zero-based page number) to end adding backgrounds to.</param>
            <param name="BackgroundPdf">The Background PDF as a <see cref="T:IronPdf.PdfDocument" />.</param>
            <param name="BackgroundPdfPageIndex">Index (zero-based page number) to copy from the BackgroundPdf.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddForegroundOverlayPdf(System.String,System.Int32)">
            <summary>
            Adds an overlay to each page of this PDF. The foreground overlay is copied from a page in another PDF
            document.
            </summary>
            <param name="OverlayPdfPath">The background PDF's file path.</param>
            <param name="OverlayPdfPageIndex">Index (zero-based page number) to copy from the Overlay PDF.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddForegroundOverlayPdf(IronPdf.PdfDocument,System.Int32)">
            <summary>
            Adds an overlay to each page of this PDF. The foreground overlay is copied from a page in another PDF
            document.
            </summary>
            <param name="OverlayPdf">The overlay PDF as a <see cref="T:IronPdf.PdfDocument" />.</param>
            <param name="OverlayPdfPageIndex">Index (zero-based page number) to copy from the Overlay PDF.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddForegroundOverlayPdfToPage(System.Int32,System.String,System.Int32)">
            <summary>
            Adds an overlay to one page of this PDF. The foreground overlay is copied from a page in another PDF
            document.
            </summary>
            <param name="ToPageIndex">Index (zero-based page number) of the page of this PDF to which the foreground will be applied to.</param>
            <param name="OverlayPdfPath">The overlay PDF path.</param>
            <param name="OverlayPdfPageIndex">Index (zero-based page number) to copy from the Overlay PDF.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddForegroundOverlayPdfToPage(System.Int32,IronPdf.PdfDocument,System.Int32)">
            <summary>
            Adds an overlay to one page of this PDF. The foreground overlay is copied from a page in another PDF
            document.
            </summary>
            <param name="ToPageIndex">Index (zero-based page number) of the page of this PDF to which the foreground will be applied to.</param>
            <param name="OverlayPdf">The overlay PDF as a <see cref="T:IronPdf.PdfDocument" />.</param>
            <param name="OverlayPdfPageIndex">Index (zero-based page number) to copy from the Overlay PDF.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddForegroundOverlayPdfToPageRange(System.Collections.Generic.IEnumerable{System.Int32},System.String,System.Int32)">
            <summary>
            Adds an overlay to a range or pages of this PDF. The foreground overlay is copied from a page in another PDF
            document.
            </summary>
            <param name="ToPageIndexes">A list of Indexes (zero-based page numbers) of pages in this PDF to which the overlay will be applied to.</param>
            <param name="OverlayPdfPath">The overlay PDF path.</param>
            <param name="OverlayPdfPageIndex">Index (zero-based page number) to copy from the Overlay PDF.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddForegroundOverlayPdfToPageRange(System.Collections.Generic.IEnumerable{System.Int32},IronPdf.PdfDocument,System.Int32)">
            <summary>
            Adds an overlay to a range or pages of this PDF. The foreground overlay is copied from a page in another PDF
            document.
            </summary>
            <param name="ToPageIndexes">A list of Indexes (zero-based page numbers) of pages in this PDF to which the overlay will be applied to.</param>
            <param name="OverlayPdf">The overlay PDF as a <see cref="T:IronPdf.PdfDocument" />.</param>
            <param name="OverlayPdfPageIndex">Index (zero-based page number) to copy from the Overlay PDF.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddForegroundOverlayPdfToPageRange(System.Int32,System.Int32,System.String,System.Int32)">
            <summary>
            Adds an overlay to a range or pages of this PDF. The foreground overlay is copied from a page in another PDF
            document.
            </summary>
            <param name="StartPageIndex">First index  (zero-based page number) to start adding overlays to .</param>
            <param name="EndPageIndex">Last index  (zero-based page number) to end adding overlays to.</param>
            <param name="OverlayPdfPath">The overlay PDF path.</param>
            <param name="OverlayPdfPageIndex">Index (zero-based page number) to copy from the Overlay PDF.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddForegroundOverlayPdfToPageRange(System.Int32,System.Int32,IronPdf.PdfDocument,System.Int32)">
            <summary>
            Adds an overlay to a range or pages of this PDF. The foreground overlay is copied from a page in another PDF
            document.
            </summary>
            <param name="StartPageIndex">First index  (zero-based page number) to start adding overlays to .</param>
            <param name="EndPageIndex">Last index  (zero-based page number) to end adding overlays to.</param>
            <param name="OverlayPdf">The overlay PDF as a <see cref="T:IronPdf.PdfDocument" />.</param>
            <param name="OverlayPdfPageIndex">Index (zero-based page number) to copy from the Overlay PDF.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="P:IronPdf.PdfDocument.Bookmarks">
            <summary>
            Hierarchy of navigational bookmarks contained at the top level of the document
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.SaveAsSvg(System.String)">
            <summary>
            Converts the PDF document to an SVG format and saves it to the provided file path.
            </summary>
            <param name="fileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_page_*.svg</param>
        </member>
        <member name="M:IronPdf.PdfDocument.ToSvgString(System.Int32)">
            <summary>
            Converts a specific page of the PDF document to an SVG format and returns it as a string.
            </summary>
            <param name="page">The page number to be converted to SVG. Default is 0 (first page).</param>
            <returns>The SVG representation of the specified page as a string.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.SaveAsHtml(System.String,System.Boolean,System.String,System.Collections.Generic.IEnumerable{System.Int32},IronPdf.HtmlFormatOptions)">
            <summary>
            Converts the PDF document to an HTML document and saves it to the provided file path with optional custom formatting.
            </summary>
            <param name="filePath">The file path where the HTML output will be saved.</param>
            <param name="fullContentWidth">If set to true, the pdf content in the HTML will be set to full width.</param>
            <param name="title">Optional title to be used in the HTML output. If not provided, the title from PDF metadata will be used.</param>
            <param name="pageIndexes">Optional set of page indexes to include in the HTML output. If not provided, all pages will be included.</param>
            <param name="htmlFormatOptions">Optional HTML formatting options. If not provided, default formatting will be used.</param>
        </member>
        <member name="M:IronPdf.PdfDocument.ToHtmlString(System.Boolean,System.String,System.Collections.Generic.IEnumerable{System.Int32},IronPdf.HtmlFormatOptions)">
            <summary>
            Converts the PDF document to an HTML string with optional custom formatting.
            </summary>
            <param name="title">Optional title to be used in the HTML output. If not provided, the title from PDF metadata will be used.</param>
            <param name="pageIndexes">Optional set of page indexes to include in the HTML output. If not provided, all pages will be included.</param>
            <param name="htmlFormatOptions">Optional HTML formatting options. If not provided, default formatting will be used.</param>
            <param name="fullContentWidth">If set to true, the pdf content in the HTML will be set to full width.</param>
            <returns>An HTML string content representing the specified pages of the PDF document.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.CopyPage(System.Int32)">
            <summary>
            Creates a new PDF by copying a page from this PdfDocument.
            </summary>
            <param name="PageIndex">Index of the page.  Note: Page 1 has index 0...</param>
            <returns>A new <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.PdfDocument.CopyPages(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Creates a new PDF by copying a range of pages from this PdfDocument.
            </summary>
            <param name="PageIndexes">An IEnumerable of page indexes to copy into the new PDF.</param>
            <returns>A new <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.PdfDocument.CopyPages(System.Int32,System.Int32)">
            <summary>
            Creates a new PDF by copying a range of pages from this <see cref="T:IronPdf.PdfDocument" />.
            </summary>
            <param name="StartIndex">The index of the first PDF page to copy.. Note: Page 1 has index 0</param>
            <param name="EndIndex">The index of the last PDF page to copy.&gt;</param>
            <returns>A new <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="P:IronPdf.PdfDocument.Fonts">
            <summary>
            PDF Document's Font collection management.
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.AddTextFooters(IronPdf.TextHeaderFooter,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Stamps page footers on top of an existing Pdf. 
            <para>Positioning can be adjusted using the <see cref="M:IronPdf.PdfDocument.AddTextFooters(IronPdf.TextHeaderFooter,System.Double,System.Double,System.Double,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})" /> overload of this method.</para></summary>
            <param name="Footer">A new instance of IronPdf.TextHeaderFooter that defines the footer content.</param>
            <param name="FirstPageNumber">Optional. The number of first page for {page} mail-merge.</param>
            <param name="PageIndexesToAddFootersTo">Optional. The PageIndexes (zero-based page numbers) to which the footer will be added.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddTextFooters(IronPdf.TextHeaderFooter,System.Double,System.Double,System.Double,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Stamps page footers on top of an existing Pdf. 
            </summary>
            <param name="Footer">A new instance of IronPdf.SimpleHeaderFooter that defines the footer content and layout.</param>
            <param name="MarginLeft">The left margin of the footer on the page in mm.</param>
            <param name="MarginRight">The right margin of the footer on the page in mm.</param>
            <param name="MarginBottom">The bottom margin of the footer on the page in mm.</param>
            <param name="FirstPageNumber">Optional. The number of first page for {page} mail-merge.</param>
            <param name="PageIndexesToAddFootersTo">Optional. The PageIndexes (zero-based page numbers) to which the footer will be added.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddTextHeaders(IronPdf.TextHeaderFooter,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Stamps page headers on top of an existing Pdf. 
            <para>Positioning can be adjusted using the <see cref="M:IronPdf.PdfDocument.AddTextHeaders(IronPdf.TextHeaderFooter,System.Double,System.Double,System.Double,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})" /> overload of this method.</para></summary>
            <param name="Header">A new instance of IronPdf.TextHeaderFooter that defines the header content and layout.</param>
            <param name="FirstPageNumber">Optional. The number of first page for {page} mail-merge.</param>
            <param name="PageIndexesToAddHeadersTo">Optional. The PageIndexes (zero-based page numbers) to which the header will be added.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddTextHeaders(IronPdf.TextHeaderFooter,System.Double,System.Double,System.Double,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Stamps page headers on top of an existing Pdf. 
            </summary>
            <param name="Header">A new instance of IronPdf.SimpleHeaderFooter that defines the header content and layout.</param>
            <param name="MarginLeft">The left margin of the header on the page in mm.</param>
            <param name="MarginRight">The right margin of the header on the page in mm.</param>
            <param name="MarginTop">The top margin of the header on the page in mm.</param>
            <param name="FirstPageNumber">Optional. The number of first page for {page} mail-merge.</param>
            <param name="PageIndexesToAddHeadersTo">Optional. The PageIndexes (zero-based page numbers) to which the header will be added.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddTextHeadersAndFooters(IronPdf.ChromePdfRenderOptions,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Stamps text headers and footers on top of an existing PDF using the specified render options and optional list of page indices.
            </summary>
            <param name="Options">Render options for the text headers and footers</param>
            <param name="PageIndexes">Optional list of page indices to apply headers and footers to. Defaults to all pages</param>
            <returns>Pdf document</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddHtmlHeadersAndFooters(IronPdf.ChromePdfRenderOptions,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Stamps HTML headers and footers on top of an existing PDF using the specified render options and optional list of page indices.
            </summary>
            <param name="Options">Render options for the HTML headers and footers</param>
            <param name="PageIndexes">Optional list of page indices to apply headers and footers to. Defaults to all pages</param>
            <returns>Pdf document</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddHtmlFooters(IronPdf.HtmlHeaderFooter,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Stamps HTML page footers on top of an existing Pdf.
            <para>Positioning can be adjusted using the <see cref="M:IronPdf.PdfDocument.AddHtmlFooters(IronPdf.HtmlHeaderFooter,System.Double,System.Double,System.Double,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})" /> overload of this method.</para></summary>
            <param name="Footer">A new instance of IronPdf.HtmlHeaderFooter that defines the footer content and layout.</param>
            <param name="FirstPageNumber">Optional. The number of first page for {page} mail-merge.</param>
            <param name="PageIndexesToAddFootersTo">Optional. The PageIndexes (zero-based page numbers) to which the footer will be added.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddHtmlFooters(IronPdf.HtmlHeaderFooter,System.Double,System.Double,System.Double,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Stamps HTML page footers on top of an existing Pdf.
            </summary>
            <param name="Footer">A new instance of IronPdf.HtmlHeaderFooter that defines the footer content and layout.</param>
            <param name="MarginLeft">The left margin of the footer on the page in mm.</param>
            <param name="MarginRight">The right margin of the footer on the page in mm.</param>
            <param name="MarginBottom">The bottom margin of the footer on the page in mm.</param>
            <param name="FirstPageNumber">Optional. The number of first page for {page} mail-merge.</param>
            <param name="PageIndexesToAddFootersTo">Optional. The PageIndexes (zero-based page numbers) to which the footer will be added.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddHtmlHeaders(IronPdf.HtmlHeaderFooter,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Stamps HTML page headers on top of an existing PDF.
            <para>Positioning can be adjusted using the <see cref="M:IronPdf.PdfDocument.AddHtmlHeaders(IronPdf.HtmlHeaderFooter,System.Double,System.Double,System.Double,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})" /> to specify header margins</para></summary>
            <param name="Header">A new instance of IronPdf.HtmlHeaderFooter that defines the header content and layout.</param>
            <param name="FirstPageNumber">Optional. The number of first page for {page} mail-merge.</param>
            <param name="PageIndexesToAddHeadersTo">Optional. The PageIndexes (zero-based page numbers) to which the header will be added.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.AddHtmlHeaders(IronPdf.HtmlHeaderFooter,System.Double,System.Double,System.Double,System.Int32,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Stamps HTML page headers on top of an existing Pdf.
            </summary>
            <param name="Header">A new instance of IronPdf.HtmlHeaderFooter that defines the header content and layout.</param>
            <param name="MarginLeft">The left margin of the header on the page in mm.</param>
            <param name="MarginRight">The right margin of the header on the page in mm.</param>
            <param name="MarginTop">The top margin of the header on the page in mm.</param>
            <param name="FirstPageNumber">Optional. The number of first page for {page} mail-merge.</param>
            <param name="PageIndexesToAddHeadersTo">Optional. The PageIndexes (zero-based page numbers) to which the header will be added.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.DrawText(System.String,System.String,System.Double,System.Int32,System.Double,System.Double,IronSoftware.Drawing.Color,System.Double)">
            <summary>
            Draw text according to the specified parameters
            To draw Unicode (non-latin) text, add a specific Unicode font using PdfDocument.Fonts.Add(byte[]) and pass the font name as `FontName`
            </summary>
            <param name="Text">Text to draw</param>
            <param name="FontName">Font name (must be a system font or already added to the document using Fonts.Add())</param>
            <param name="FontSize">Font size</param>
            <param name="PageIndex">Page index</param>
            <param name="X">X coordinate</param>
            <param name="Y">Y coordinate</param>
            <param name="Color">Desired font color</param>
            <param name="Rotation">Text rotation (degrees, clockwise)</param>
        </member>
        <member name="M:IronPdf.PdfDocument.DrawText(System.String,IronPdf.Fonts.PdfFont,System.Double,System.Int32,System.Double,System.Double,IronSoftware.Drawing.Color,System.Double)">
            <summary>
            Draw text according to the specified parameters
            To draw Unicode (non-latin) text, add a specific Unicode font using PdfDocument.Fonts.Add(byte[]) and pass the font name as `FontName`
            </summary>
            <param name="Text">Text to draw</param>
            <param name="Font">Font (must be a system font or already added to the document using Fonts.Add())</param>
            <param name="FontSize">Font size</param>
            <param name="PageIndex">Page index</param>
            <param name="X">X coordinate</param>
            <param name="Y">Y coordinate</param>
            <param name="Color">Desired font color</param>
            <param name="Rotation">Text rotation (degrees, clockwise)</param>
        </member>
        <member name="M:IronPdf.PdfDocument.DrawBitmap(IronSoftware.Drawing.AnyBitmap,System.Int32,System.Double,System.Double,System.Double,System.Double,IronPdf.PixelFormat)">
            <summary>
            Draw a bitmap a single time according to the specified parameters
            </summary>
            <param name="Bitmap">Bitmap to draw</param>
            <param name="PageIndex">Page index</param>
            <param name="X">X coordinate</param>
            <param name="Y">Y coordinate</param>
            <param name="DesiredWidth">Desired width</param>
            <param name="DesiredHeight">Desired Height</param>
            <param name="PixelFormat">Pixel format</param>
        </member>
        <member name="M:IronPdf.PdfDocument.DrawLine(System.Int32,IronSoftware.Drawing.PointF,IronSoftware.Drawing.PointF,System.Double,IronSoftware.Drawing.Color)">
            <summary>
            Draw a line using the specified parameters
            </summary>
            <param name="PageIndex">Page index</param>
            <param name="Start">Start coordinate</param>
            <param name="End">End coordinate</param>
            <param name="Width">Line width</param>
            <param name="Color">Line color</param>
        </member>
        <member name="M:IronPdf.PdfDocument.DrawRectangle(System.Int32,IronSoftware.Drawing.RectangleF,IronSoftware.Drawing.Color,IronSoftware.Drawing.Color,System.Double)">
            <summary>
            Draw a rectangle using the specified parameters
            </summary>
            <param name="PageIndex">Page index</param>
            <param name="Rect">Rectangle</param>
            <param name="LineColor">Line color</param>
            <param name="FillColor">Fill color</param>
            <param name="Width">Line width</param>
        </member>
        <member name="M:IronPdf.PdfDocument.DrawBitmap(IronSoftware.Drawing.AnyBitmap,System.Collections.Generic.IEnumerable{System.Int32},System.Collections.Generic.IEnumerable{System.Double},System.Collections.Generic.IEnumerable{System.Double},System.Collections.Generic.IEnumerable{System.Double},System.Collections.Generic.IEnumerable{System.Double},IronPdf.PixelFormat)">
            <summary>
            Draw a bitmap multiple times according to the specified parameters; all occurrences of the bitmap will share a single data stream
            </summary>
            <param name="Bitmap">AnyBitmap to draw</param>
            <param name="PageIndices">Target page indices</param>
            <param name="XCoordinates">X coordinates</param>
            <param name="YCoordinates">Y coordinates</param>
            <param name="DesiredWidths">Desired widths</param>
            <param name="DesiredHeights">Desired heights</param>
            <param name="PixelFormat">Pixel format</param>
        </member>
        <member name="M:IronPdf.PdfDocument.Compress(IronPdf.CompressionOptions)">
            <summary>
            Compress the PDF document using the specified options
            </summary>
            <param name="Options">Compression options. See <see cref="T:IronPdf.CompressionOptions" /></param>
            <remarks>Compression can drastically reduce the size of a document by removing unused features or by reducing quality.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.CompressImages(System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Compress existing images using JPG encoding and the specified settings
            </summary>
            <param name="Quality">Quality (1 - 100) to use during compression</param>
            <param name="ShrinkImage">Scale down the image resolution according to its visible size in the PDF document.
            <para>
            This will drastically reduce the size and quality of the images.
            </para></param>
            <param name="HighQualitySubsampling">True to use 444 chroma subsampling for a higher quality image. False to use 411 chrome subsampling to further reduce the image size.
            </param>
            <exception cref="T:System.ArgumentOutOfRangeException">Argument out of range</exception>
        </member>
        <member name="M:IronPdf.PdfDocument.CompressStructTree">
            <summary>
            Remove document struct tree information which describes the logical layout of the document.
            <para>
            Removing the "structure tree" can significantly reduce the disk space used by the document.
            </para><para>
            Removing the "structure tree" of a complicated document can negatively impact text selection.
            </para></summary>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractAllBitmaps">
            <summary>
            Finds all embedded Images from within the PDF and returns then as <see cref="T:IronSoftware.Drawing.AnyBitmap" /> objects
            </summary>
            <returns>The extracted images as <see cref="T:IronSoftware.Drawing.AnyBitmap" /></returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractAllImages">
            <summary>
            Finds all embedded Images from within the PDF and returns then as <see cref="T:IronSoftware.Drawing.AnyBitmap" /> objects
            </summary>
            <returns>The extracted images as <see cref="T:IronSoftware.Drawing.AnyBitmap" /></returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractAllRawImages">
            <summary>
            Extracts all images from a PDf document. Images are returned as their raw data in as Byte Arrays (byte[])
            </summary>
            <returns>An Enumerable of Byte Arrays.  Each Byte Array represents one image.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractBitmapsFromPage(System.Int32)">
            <summary>
            Finds all embedded Images from within one page of the PDF and returns them as <see cref="T:IronSoftware.Drawing.AnyBitmap" /> objects
            </summary>
            <param name="PageIndex">Index of the page.  Note: Page 1 has index 0...</param>
            <returns>The extracted images as <see cref="T:IronSoftware.Drawing.AnyBitmap" /> Objects</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractBitmapsFromPages(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Finds all embedded Images from within a specified  pages in the PDF and returns them as
            <see cref="T:IronSoftware.Drawing.AnyBitmap" /> objects
            </summary>
            <param name="PageIndexes">An IEnumerable list of page indexes.</param>
            <returns>IEnumerable of AnyBitmap.  The extracted images as <see cref="T:IronSoftware.Drawing.AnyBitmap" /> Objects</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractBitmapsFromPages(System.Int32,System.Int32)">
            <summary>
            Finds all embedded Images from within a range of pages in the PDF and returns them as <see cref="T:IronSoftware.Drawing.AnyBitmap" />
            objects
            </summary>
            <param name="StartIndex">The index of the first PDF page  to extract images from.. Note: Page 1 has index 0</param>
            <param name="EndIndex">The index of the last PDF page to extract images from.</param>
            <returns>The extracted images as <see cref="T:IronSoftware.Drawing.AnyBitmap" /> Objects</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractImagesFromPage(System.Int32)">
            <summary>
            Finds all embedded Images from within one page of the PDF and returns them as <see cref="T:IronSoftware.Drawing.AnyBitmap" /> objects
            </summary>
            <param name="PageIndex">Index of the page.  Note: Page 1 has index 0...</param>
            <returns>The extracted images as <see cref="T:IronSoftware.Drawing.AnyBitmap" /> Objects</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractImagesFromPages(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Finds all embedded Images from within a specified  pages in the PDF and returns them as <see cref="T:IronSoftware.Drawing.AnyBitmap" />
            objects
            </summary>
            <param name="PageIndexes">An IEnumerable list of page indexes.</param>
            <returns>The extracted images as <see cref="T:IronSoftware.Drawing.AnyBitmap" /> Objects</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractImagesFromPages(System.Int32,System.Int32)">
            <summary>
            Finds all embedded Images from within a range of pages in the PDF and returns them as <see cref="T:IronSoftware.Drawing.AnyBitmap" />
            objects
            </summary>
            <param name="StartIndex">The index of the first PDF page  to extract images from.. Note: Page 1 has index 0</param>
            <param name="EndIndex">The index of the last PDF page to extract images from.</param>
            <returns>The extracted images as <see cref="T:IronSoftware.Drawing.AnyBitmap" /> Objects</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractRawImagesFromPage(System.Int32)">
            <summary>
            Finds all embedded Images from within one page of the PDF and returns them as raw image byte array objects
            </summary>
            <param name="PageIndex">Index of the page.  Note: Page 1 has index 0...</param>
            <returns>List of raw image byte array.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractRawImagesFromPages(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Finds all embedded Images from specified pages in the PDF and returns them as raw image byte array objects
            objects
            </summary>
            <param name="PageIndexes">An IEnumerable list of page indexes.</param>
            <returns>List of raw image byte array.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractRawImagesFromPages(System.Int32,System.Int32)">
            <summary>
            Finds all embedded Images from within a range of pages in the PDF and returns them as raw image byte array
            objects
            </summary>
            <param name="StartIndex">The index of the first PDF page  to extract images from.. Note: Page 1 has index 0</param>
            <param name="EndIndex">The index of the last PDF page to extract images from.</param>
            <returns>List of raw image byte array.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.op_Implicit(IronSoftware.DocumentId)~IronPdf.PdfDocument">
            <summary>
            Implicit conversion from DocumentId to PdfDocument
            </summary>
            <param name="id">DocumentId to convert</param>
        </member>
        <member name="P:IronPdf.PdfDocument.RevisionCount">
            <summary>
            Number of revisions available in the document. See <seealso cref="M:IronPdf.PdfDocument.SaveAsRevision(System.String)" /></summary>
        </member>
        <member name="M:IronPdf.PdfDocument.GetRevision(System.Int32)">
            <summary>
            Creates a copy of this document at the specified revision number. See <seealso cref="M:IronPdf.PdfDocument.SaveAsRevision(System.String)" /></summary>
            <param name="index">Revision number. Revision 0 represents the original document, revision 1 is the first revision, etc.</param>
            <returns>A copy of the specified revision of this document</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.#ctor(IronSoftware.DocumentId,System.String,System.String)">
            <summary>
            Constructor for converting remote gRPC document into a local document. Not intended for normal use.
            </summary>
            <param name="id">Pdf document identifier which will be converted into a full document</param>
            <param name="Password">Password</param>
            <param name="OwnerPassword">Owner password</param>
            <remarks>DO NOT USE THIS unless you are sure of what you're doing!</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.#ctor(System.Double,System.Double)">
            <summary>
            Create a new blank PDF document
            </summary>
            <param name="Width">Document width (mm)</param>
            <param name="Height">Document height (mm)</param>
        </member>
        <member name="M:IronPdf.PdfDocument.FromJson(System.String)">
            <summary>
            Create a new PDF document from the specified JSON string
            </summary>
            <param name="Json">JSON string</param>
            <returns>PDF document</returns>
            <remarks>Requires the IRON document schema</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.FromJsonFile(System.String)">
            <summary>
            Create a new PDF document from the specified JSON file.
            </summary>
            <param name="Path">JSON file path</param>
            <returns>PDF document</returns>
            <remarks>Requires the IRON document schema</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.#ctor(System.Collections.Generic.IList{IronSoftware.Pdfium.Dom.IPdfPageObjectModel})">
            <summary>
            Create a new PDF document from existing page object models
            </summary>
            <param name="ObjectModels">Object models from which to create pages</param>
        </member>
        <member name="M:IronPdf.PdfDocument.#ctor(IronPdf.PdfDocument,System.String,System.String)">
            <summary>
            Clone a PDF document
            </summary>
            <param name="Document">Document to clone</param>
            <param name="Password">Document password</param>
            <param name="OwnerPassword">Document owner password</param>
        </member>
        <member name="M:IronPdf.PdfDocument.#ctor(System.String,System.String,System.String,IronPdf.Rendering.ChangeTrackingModes)">
            <summary>
            Opens an existing PDF document for editing.
            <br /><para>Adding a using declaration is not required. But can be used if you want to explicitly dispose. See: https://ironpdf.com/troubleshooting/ironpdf-using/</para></summary>
            <param name="PdfFilePath">The PDF file path.</param>
            <param name="Password">Optional user password if the PDF document is encrypted.</param>
            <param name="OwnerPassword">Optional password if the PDF document is protected by owner (printing, modifying restrictions etc..).</param>
            <param name="TrackChanges">Optionally track changes to the document (for use with incremental saves)</param>
            <exception cref="T:System.IO.IOException">Exception thrown if can not be opened.</exception>
            <exception cref="T:System.ArgumentException">
                <paramref name="PdfFilePath" /> is null, empty, or consists only of white-space characters.</exception>
        </member>
        <member name="M:IronPdf.PdfDocument.#ctor(System.Uri,System.String,System.String,IronPdf.Rendering.ChangeTrackingModes)">
            <summary>
            Opens an existing PDF document from the specified Uri for editing.
            <br /><para>Adding a using declaration is not required. But can be used if you want to explicitly dispose. See: https://ironpdf.com/troubleshooting/ironpdf-using/</para></summary>
            <param name="PdfUri">The Uri to the PDF.</param>
            <param name="Password">Optional user password if the PDF document is encrypted.</param>
            <param name="OwnerPassword">Optional password if the PDF document is protected by owner (printing, modifying restrictions etc..).</param>
            <param name="TrackChanges">Optionally track changes to the document (for use with incremental saves)</param>
            <exception cref="T:IronPdf.Exceptions.IronPdfProductException">Exception thrown if PDF cannot be found at URI or if password is incorrect.</exception>
        </member>
        <member name="M:IronPdf.PdfDocument.#ctor(System.IO.Stream,System.String,System.String,IronPdf.Rendering.ChangeTrackingModes)">
            <summary>
            Opens an existing PDF document for editing.
            <br /><para>Adding a using declaration is not required. But can be used if you want to explicitly dispose. See: https://ironpdf.com/troubleshooting/ironpdf-using/</para></summary>
            <param name="PdfDataStream">The PDF file data as a Stream.</param>
            <param name="Password">Optional user password if the PDF document is encrypted.</param>
            <param name="OwnerPassword">Optional password if the PDF document is protected by owner (printing, modifying restrictions etc..).</param>
            <param name="TrackChanges">Optionally track changes to the document (for use with incremental saves)</param>
            <exception cref="T:System.IO.IOException">Exception thrown if can not be opened.</exception>
        </member>
        <member name="M:IronPdf.PdfDocument.#ctor(System.Byte[],System.String,System.String,IronPdf.Rendering.ChangeTrackingModes)">
            <summary>
            Opens an existing PDF document for editing.
            <br /><para>Adding a using declaration is not required. But can be used if you want to explicitly dispose. See: https://ironpdf.com/troubleshooting/ironpdf-using/</para></summary>
            <param name="PdfData">The PDF file data as byte array.</param>
            <param name="Password">Optional user password if the PDF document is encrypted.</param>
            <param name="OwnerPassword">Optional password if the PDF document is protected by owner (printing, modifying restrictions etc..).</param>
            <param name="TrackChanges">Optionally track changes to the document (for use with incremental saves)</param>
            <exception cref="T:System.IO.IOException">Exception thrown if can not be opened.</exception>
        </member>
        <member name="M:IronPdf.PdfDocument.Finalize">
            <summary>
            Finalizer
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.Dispose">
            <summary>
            Releases used resources.
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.FromUrl(System.Uri,System.String,System.String)">
            <summary>
            Opens a PDF stored on a URL for editing.
            <br /><para>Adding a using declaration is not required. But can be used if you want to explicitly dispose. See: https://ironpdf.com/troubleshooting/ironpdf-using/</para></summary>
            <param name="Uri">The URI to the PDF.</param>
            <param name="Password">Optional user password if the PDF document is encrypted.</param>
            <param name="OwnerPassword">Optional password if the PDF document is protected by owner (printing, modifying restrictions etc..).</param>
            <exception cref="T:IronPdf.Exceptions.IronPdfProductException">Exception thrown if cannot read the PDF due to incorrect uri or password.</exception>
            <returns>An IronPdf.PdfDocument object as loaded from the file path.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.FromFile(System.String,System.String,System.String,IronPdf.Rendering.ChangeTrackingModes)">
            <summary>
            Opens an existing PDF document for editing.
            <br /><para>Adding a using declaration is not required. But can be used if you want to explicitly dispose. See: https://ironpdf.com/troubleshooting/ironpdf-using/</para></summary>
            <param name="PdfFilePath">The PDF file path.</param>
            <param name="Password">Optional user password if the PDF document is encrypted.</param>
            <param name="OwnerPassword">Optional password if the PDF document is protected by owner (printing, modifying restrictions etc..).</param>
            <param name="TrackChanges">Optionally track changes (for use with incremental saving)</param>
            <exception cref="T:System.IO.IOException">Exception thrown if can not be opened.</exception>
            <exception cref="T:System.ArgumentException">
                <paramref name="PdfFilePath" /> is null, empty, or consists only of white-space characters.</exception>
            <returns>An IronPdf.PdfDocument object as loaded from the file path.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.SaveAs(System.String,System.Boolean)">
            <summary>
            Saves the PdfDocument to a file.
            </summary>
            <param name="FileName">Target file path</param>
            <param name="SaveAsRevision">Append changes to the end of the PDF rather than overwriting existing objects. See also <seealso cref="M:IronPdf.PdfDocument.SaveAsRevision(System.String)" /></param>
            <returns>This PdfDocument for fluid code notation; if SaveAsRevision is enabled, the returned PdfDocument will be a new revision</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.SaveAsRevision(System.String)">
            <summary>
            Saves current changes as a revision and returns the revised the document, optionally also saving the document to disk
            </summary>
            <param name="FileName">Optional file path</param>
            <returns>The revised PdfDocument, ready for a new set of changes</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.TrySaveAs(System.String)">
            <summary>
            Attempt to save the PdfDocument to a file.
            </summary>
            <param name="FileName">File Path</param>
            <returns>
            }<c>true</c> if the file was saved successfully.  <c>false</c> if an error occurred (e.g. file was locked or
            insufficient permissions).
            </returns>
        </member>
        <member name="P:IronPdf.PdfDocument.BinaryDataIncremental">
            <summary>
            Saves the PDF as byte array with changes appended to the end of the file. For non-incremental saves, see <see cref="P:IronPdf.PdfDocument.BinaryData" /><para>For information on how to enable incremental saves see <seealso cref="M:IronPdf.PdfDocument.SaveAsRevision(System.String)" /></para></summary>
            <value>
            The PDF file as a byte array.
            </value>
        </member>
        <member name="P:IronPdf.PdfDocument.BinaryData">
            <summary>
            Saves the PDF as byte array, including any changes. For incremental saves, see <see cref="P:IronPdf.PdfDocument.BinaryDataIncremental" /></summary>
            <value>
            The PDF file as a byte array.
            </value>
        </member>
        <member name="P:IronPdf.PdfDocument.OwnerPassword">
            <summary>
            Sets the owner password and enables 128-bit encryption of PDF content. An owner password is one used to
            enable and disable all other security settings. <para>OwnerPassword must be set to a non empty string value
            for<see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserCopyPasteContent" />,<see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserAnnotations" />,<see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserFormData" />,<see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserPrinting" /> and<see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserEdits" /> to be
            restricted.</para></summary>
        </member>
        <member name="P:IronPdf.PdfDocument.Password">
            <summary>
            Sets a Password used to protect and encrypt the PDF File. Setting a password will cause IronPDF to
            automatically protect the PDF file content using strong 128 bit encryption. Setting the password to null
            will remove any existing password.
            </summary>
        </member>
        <member name="P:IronPdf.PdfDocument.Stream">
            <summary>
             Gets the binary data for the full PDF file as a Stream.<para>This Stream is <see cref="T:System.IDisposable" /> and should be disposed with "using" or "Dispose()" methods.</para></summary>
            <value>
             The PDF file as a new MemoryStream which is <see cref="T:System.IDisposable" /> and should be disposed with "using" or "Dispose()" methods.
             </value>
        </member>
        <member name="P:IronPdf.PdfDocument.IsValid">
            <summary>
            Document is valid
            </summary>
        </member>
        <member name="P:IronPdf.PdfDocument.NativeId">
            <summary>
            Document local reference identifier
            </summary>
            <remarks>Modifying this property will result in an un-usable document</remarks>
        </member>
        <member name="P:IronPdf.PdfDocument.RemoteId">
            <summary>
            Document remote reference identifier
            </summary>
            <remarks>Modifying this property will result in an un-usable document</remarks>
        </member>
        <member name="P:IronPdf.PdfDocument.ReferenceType">
            <summary>
            Document local/remote type
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.ToString">
            <summary>
            Represent PDF document as a document id string
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.IncrementReference">
            <inheritdoc />
        </member>
        <member name="M:IronPdf.PdfDocument.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:IronPdf.PdfDocument.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:IronPdf.PdfDocument.AppendPdf(IronPdf.PdfDocument)">
            <summary>
            Appends another PDF to the end of the current <see cref="T:IronPdf.PdfDocument" />
            If AnotherPdfFile contains form fields, those fields will be appended with '_' in the resulting PDF. e.g. 'Name' will be 'Name_'
            </summary>
            <param name="AnotherPdfFile">PdfDocument to append.</param>
            <returns>A new <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.PdfDocument.CombinePages(System.Double,System.Double,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Combine all pages of this document into a NEW document with the pages organized into a grid of columns and rows
            </summary>
            <param name="NewWidth">Width of the new document, in millimeters</param>
            <param name="NewHeight">Height of the new document, in millimeters</param>
            <param name="Columns">Number of columns-per-page in the new document</param>
            <param name="Rows">Number of rows-per-page in the new document</param>
            <param name="DrawBorders">Draw borders around each individual page</param>
            <returns>New document with the specified number of columns and rows per page</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.InsertPdf(IronPdf.PdfDocument,System.Int32)">
            <summary>
            Inserts another PDF into the current PdfDocument, starting at a given Page Index.
            If AnotherPdfFile contains form fields, those fields will be appended with '_' in the resulting PDF. e.g. 'Name' will be 'Name_'
            </summary>
            <param name="AnotherPdfFile">Another PdfDocument.</param>
            <param name="AtIndex">Index at which to insert the new content.  Note: Page 1 has index 0...</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.Merge(System.Collections.Generic.IEnumerable{IronPdf.PdfDocument})">
            <summary>
             Static method that joins (concatenates) multiple PDF documents together into one compiled PDF document.
             If the PDF contains form fields the form field in the resulting PDF's name will be appended with '_{index}' e.g. 'Name' will be 'Name_0'
             </summary>
            <param name="Documents">
             A IEnumerable of PdfDocument.  To merge existing PDF files you may use the PdfDocument.FromFile static
             method in conjunction with Merge.
             </param>
            <returns>
             A new, merged <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.PdfDocument.Merge(IronPdf.PdfDocument,IronPdf.PdfDocument)">
            <summary>
            Static method that joins (concatenates) 2 PDF documents together into one PDF document.
            If the PDF contains form fields the form field in the resulting PDF's name will be appended with '_{index}' e.g. 'Name' will be 'Name_0'
            </summary>
            <param name="A">A PDF</param>
            <param name="B">A Seconds PDF</param>
            <returns>
            A new, merged <see cref="T:IronPdf.PdfDocument" /></returns>
        </member>
        <member name="M:IronPdf.PdfDocument.PrependPdf(IronPdf.PdfDocument)">
            <summary>
            Adds another PDF to the beginning of the current PdfDocument
            If AnotherPdfFile contains form fields, those fields will be appended with '_' in the resulting PDF. e.g. 'Name' will be 'Name_'
            </summary>
            <param name="AnotherPdfFile">PdfDocument to prepend.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="P:IronPdf.PdfDocument.MetaData">
            <summary>
            MetaData information settings for the PDF. <para>Allows File meta-data to be read and set including:
            Authors, File Dates, Keywords, Title and Subject</para></summary>
            <value>
            MetaData settings for this PDF as an instance of <see cref="T:IronPdf.MetaData.PdfMetaData" /></value>
        </member>
        <member name="P:IronPdf.PdfDocument.SecuritySettings">
            <summary>
            Advanced security settings for the PDF. <para>Allows the developer to control user access passwords,
            encryption, and also who may edit, print and copy content from the PDF document</para></summary>
            <value>
            Advanced security settings for this PDF as an instance of <see cref="T:IronPdf.Security.PdfSecuritySettings" /></value>
        </member>
        <member name="M:IronPdf.PdfDocument.PerformOcr">
            <summary>
            Perform Optical Character Recognition on this document. Requires IronOcr NuGet package installed.
            </summary>
            <returns>OCR result.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtendPage(System.Int32,System.Double,System.Double,System.Double,System.Double,IronPdf.Editing.MeasurementUnit)">
            <summary>
            Extends page bounds using the specified parameters (in millimeters)
            <para>
            Does not resize page content; results in an empty margin around existing page content
            </para></summary>
            <param name="PageIndex">Page to resize</param>
            <param name="ExtendLeft">Desired amount (mm) to extend page width towards the left</param>
            <param name="ExtendRight">Desired amount (mm) to extend page width towards the right</param>
            <param name="ExtendTop">Desired amount (mm) to extend page height towards the top</param>
            <param name="ExtendBottom">Desired amount (mm) to extend page height towards the bottom</param>
            <param name="Units">Optionally specify units of measurement for input parameters</param>
        </member>
        <member name="M:IronPdf.PdfDocument.Flatten(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Flattens a document (make the fields such as Forms Fields non-editable and non-accessible).
            </summary>
            <param name="Pages">Optional page indices to flatten (defaults to all pages)</param>
        </member>
        <member name="M:IronPdf.PdfDocument.ResizePage(System.Int32,System.Double,System.Double,IronPdf.Editing.MeasurementUnit)">
            <summary>
            Resize a page to the specified dimensions (in millimeters)
            </summary>
            <param name="PageIndex">Page index to resize</param>
            <param name="PageWidth">Desired page width, in millimeters</param>
            <param name="PageHeight">Desired page height, in millimeters</param>
            <param name="Units">Optionally specify units of measurement for input parameters</param>
        </member>
        <member name="P:IronPdf.PdfDocument.Form">
            <summary>
            Gets an object returning any PDF editable form fields which can have their values both read and set
            programmatically.
            </summary>
            <value>
            The form.
            </value>
        </member>
        <member name="P:IronPdf.PdfDocument.PageCount">
            <summary>
            Gets the number of pages in the PDF document.
            </summary>
            <value>
            The page count.
            </value>
        </member>
        <member name="P:IronPdf.PdfDocument.Pages">
            <summary>
            Gets the list of pages in the PDF document.
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.ConvertToPdfA(IronPdf.PdfAVersions,System.String)">
            <summary>
            Convert the current document into the specified PDF/A standard format with selected icc file
            </summary>
            <param name="PdfAVersion">(Optional) PDF/A standard format</param>
            <param name="CustomICC">(Optional) Custom color profile file path</param>
            <returns>A PDF/A compliant reference to this document</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ConvertToPdfA(System.Collections.Generic.IEnumerable{System.String},IronPdf.PdfAVersions,System.String)">
            <summary>
            Convert the current document into the specified PDF/A standard format with embedded files and selected icc file
            </summary>
            <param name="EmbedFilePaths">Collection of path to embedding file to the PDF/A document</param>
            <param name="PdfAVersion">(Optional) PDF/A standard format</param>
            <param name="CustomICC">(Optional) Custom color profile file path</param>
            <returns />
        </member>
        <member name="M:IronPdf.PdfDocument.ConvertToPdfA(System.Collections.Generic.IEnumerable{IronPdf.EmbedFileByte},IronPdf.PdfAVersions,System.String)">
            <summary>
            Convert the current document into the specified PDF/A standard format with embedded files and selected icc file
            </summary>
            <param name="EmbedFileBytes">Collection of byte[] and their file type of embedding file with type of that file to the PDF/A document</param>
            <param name="PdfAVersion">(Optional) PDF/A standard format</param>
            <param name="CustomICC">(Optional) Custom color profile file path</param>
            <returns />
        </member>
        <member name="M:IronPdf.PdfDocument.ConvertToPdfA(System.Collections.Generic.IEnumerable{IronPdf.EmbedFileStream},IronPdf.PdfAVersions,System.String)">
            <summary>
            Convert the current document into the specified PDF/A standard format with embedded files and selected icc file
            </summary>
            <param name="EmbedFileStreams">Collection of Stream and their file type of embedding file with type of that file to the PDF/A document</param>
            <param name="PdfAVersion">(Optional) PDF/A standard format</param>
            <param name="CustomICC">(Optional) Custom color profile file path</param>
            <returns />
        </member>
        <member name="M:IronPdf.PdfDocument.SaveAsPdfA(System.String,IronPdf.PdfAVersions,System.String)">
            <summary>
            Saves the <see cref="T:IronPdf.PdfDocument" /> as a PDF/A Compliant PDF File
            <para>For more information see the: <a href="https://ironpdf.com/how-to/pdfa/">IronPDF PDFA How-To Guide</a><br /><br /></para><para><i>Note: All of non-embedded fonts are embedded as <b>Helvetica</b> Font which is one of 14 standard fonts provided by Adobe.</i></para></summary>
            <param name="FileName">Target file path</param>
            <param name="PdfAVersion">(Optional) PDF/A standard format</param>
            <param name="CustomICC">(Optional) Custom color profile file path</param>
            <returns />
            <remarks>currently only support embedding 3 types of file; .xml, .pdf, and .png</remarks>
            <exception cref="T:System.Exception" />
        </member>
        <member name="M:IronPdf.PdfDocument.SaveAsPdfA(System.String,System.Collections.Generic.IEnumerable{System.String},IronPdf.PdfAVersions,System.String)">
            <summary>
            Saves the <see cref="T:IronPdf.PdfDocument" /> as a PDF/A Compliant PDF File with embedded files
            <para>For more information see the: <a href="https://ironpdf.com/how-to/pdfa/">IronPDF PDFA How-To Guide</a><br /><br /></para><para><i>Note: All of non-embedded fonts are embedded as <b>Helvetica</b> Font which is one of 14 standard fonts provided by Adobe.</i></para></summary>
            <param name="FileName">Target file path</param>
            <param name="EmbedFilePaths">Collection of path to embedding file to the PDF/A document</param>
            <param name="PdfAVersion">(Optional) PDF/A standard format</param>
            <param name="CustomICC">(Optional) Custom color profile file path</param>
            <returns />
            <remarks>currently only support embedding 3 types of file; .xml, .pdf, and .png</remarks>
            <exception cref="T:System.Exception" />
        </member>
        <member name="M:IronPdf.PdfDocument.SaveAsPdfA(System.String,System.Collections.Generic.IEnumerable{IronPdf.EmbedFileByte},IronPdf.PdfAVersions,System.String)">
            <summary>
            Saves the <see cref="T:IronPdf.PdfDocument" /> as a PDF/A Compliant PDF File with embedded files
            <para>For more information see the: <a href="https://ironpdf.com/how-to/pdfa/">IronPDF PDFA How-To Guide</a><br /><br /></para><para><i>Note: All of non-embedded fonts are embedded as <b>Helvetica</b> Font which is one of 14 standard fonts provided by Adobe.</i></para></summary>
            <param name="FileName">Target file path</param>
            <param name="EmbedFileBytes">Collection of byte[] and their file type of embedding file with type of that file to the PDF/A document</param>
            <param name="PdfAVersion">(Optional) PDF/A standard format</param>
            <param name="CustomICC">(Optional) Custom color profile file path</param>
            <remarks>currently only support embedding 3 types of file; .xml, .pdf, and .png</remarks>
            <returns />
        </member>
        <member name="M:IronPdf.PdfDocument.SaveAsPdfA(System.String,System.Collections.Generic.IEnumerable{IronPdf.EmbedFileStream},IronPdf.PdfAVersions,System.String)">
            <summary>
            Saves the <see cref="T:IronPdf.PdfDocument" /> as a PDF/A Compliant PDF File with embedded files
            <para>For more information see the: <a href="https://ironpdf.com/how-to/pdfa/">IronPDF PDFA How-To Guide</a><br /><br /></para><para><i>Note: All of non-embedded fonts are embedded as <b>Helvetica</b> Font which is one of 14 standard fonts provided by Adobe.</i></para></summary>
            <param name="FileName">Target file path</param>
            <param name="EmbedFileStreams">Collection of Stream and their file type of embedding file with type of that file to the PDF/A document</param>
            <param name="PdfAVersion">(Optional) PDF/A standard format</param>
            <param name="CustomICC">(Optional) Custom color profile file path</param>
            <remarks>currently only support embedding 3 types of file; .xml, .pdf, and .png</remarks>
            <returns />
        </member>
        <member name="M:IronPdf.PdfDocument.ConvertToPdfUA(IronPdf.NaturalLanguages,IronPdf.PdfUAVersions)">
            <summary>
            Convert the current document into the specified PDF/UA standard format
            </summary>
            <param name="PdfUAVersion">PDF-UA standard format</param>
            <param name="naturalLanguages">Natural Languages Specification</param>
            <returns>A PDF/A compliant reference to this document</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.SaveAsPdfUA(System.String,IronPdf.PdfUAVersions,IronPdf.NaturalLanguages)">
            <summary>
            Open a PDF File Path and saves a new PDF/UA Compliant PDF File to allow for Section 508 accessibility compliance.
            Please note that this feature supports PDF documents containing the English language only.
            <para>For more information see the: <a href="https://ironpdf.com/how-to/pdfua/">IronPDF PDF/UA How-To Guide</a><br /><br /></para><para><i>Note: All of non-embedded fonts are embedded as <b>Helvetica</b> Font which is one of 14 standard fonts provided by Adobe.</i></para></summary>
            <param name="FileName">Target file path</param>
            <param name="PdfUAVersion">Version of exported PDF/UA</param>
            <param name="naturalLanguages">Natural Languages Specification</param>
            <returns>A PDF/UA compliant reference to this document</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)">
            <summary>
            Returns a <see cref="T:System.Drawing.Printing.PrintDocument" /> for the PDF allowing developers granular
            control over sending the PDF to a Printer. <para>An assembly reference to System.Drawing is required in your
            project.</para><para>Note: '<see cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />' is only supported on: 'Windows'.</para><param name="printerSettings"> Printer settings. Note: If you create a new PrinterSettings() we recommend setting printerSettings.DefaultPageSettings.Margins = new Margins(0, 0, 0, 0); </param><param name="printController"> custom printController such as PreviewPrintController for a GUI print preview</param><param name="Flatten">Flatten the PDF before printing - useful for displaying form field values</param></summary>
            <returns>A <see cref="T:System.Drawing.Printing.PrintDocument" />.</returns>
            <exception cref="T:System.Exception">IronPdf must be licensed to use this feature.</exception>
        </member>
        <member name="M:IronPdf.PdfDocument.Print(System.Boolean)">
            <summary>
            Prints this PDF by sending it to the computer's printer. <para>For advanced real-world printing options
            please see overloads of this method and also <see cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />.</para>
            if <see langword="true" /> print preview dialogs will be displayed to the user. Default
            value is <see langword="false" />.
            <para>Note: '<see cref="!:Print()" />'  is only supported on: 'Windows'.</para></summary>
            <param name="Flatten">Flatten the PDF before printing - useful for displaying form field values</param>
            <returns>The number of printed pages.</returns>
            <seealso cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />
        </member>
        <member name="M:IronPdf.PdfDocument.Print(System.String,System.Boolean)">
            <summary>
            Prints this PDF by sending it to the computer's printer.   <para>For even more advanced real-world printing
            options please see overloads of this method and also <see cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />.</para><para>Note: '<see cref="!:Print(string)" />' is only supported on: 'Windows'.</para><param name="PrinterName">the name of a printer, set<see langword="null" /> or an empty string ("") to use default print. Default value is <see langword="null" />.</param></summary>
            <param name="Flatten">Flatten the PDF before printing - useful for displaying form field values</param>
            <returns>The number of printed pages.</returns>
            <seealso cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />
        </member>
        <member name="M:IronPdf.PdfDocument.Print(System.Int32,System.String,System.String,System.Boolean)">
            <summary>
            Prints this PDF by sending it to the computer's printer.   <para>For even more advanced real-world printing
            options please see overloads of this method and also <see cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />.</para><para>Note: '<see cref="!:Print(int, string, string)" />' is only supported on: 'Windows'.</para><param name="dpi">the horizontal and vertical PDF resolutions, in dots per inch. if <c>-1</c> print with default
            printer resolution. Default value is <c>-1</c>.</param><param name="PrinterName">the name of a printer, set<see langword="null" /> or an empty string ("") to use default print. Default value is <see langword="null" />.</param><param name="FilePath">the file path, when printing to a file, set <see angword="null" /> or an empty string ("") for printing output is sent to a port instead of the 
            <paramref name="FilePath" />. The default value is <see langword="null" />.</param><param name="Flatten">Flatten the PDF before printing - useful for displaying form field values</param></summary>
            <returns>The number of printed pages.</returns>
            <seealso cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />
        </member>
        <member name="M:IronPdf.PdfDocument.Print(System.Int32,System.Int32,System.String,System.String,System.Drawing.Printing.PrintController,System.Boolean)">
            <summary>
            Prints this PDF by sending it to the computer's printer.   <para>For even more advanced real-world printing
            options please see overloads of this method and also <see cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />.</para><para>Note: '<see cref="!:Print(int, int, string, string, System.Drawing.Printing.PrintController)" />' is only supported on: 'Windows'.</para><param name="dpiX"> the horizontal PDF resolution, in dots per inch.</param><param name="dpiY">the vertical PDF resolution, in dots per inch.</param><param name="printerName">the name of a printer, set<see langword="null" /> or an empty string ("") to use default print. Default value is <see langword="null" />.</param><param name="filePath">the file path, when printing to a file, set <see angword="null" /> or an empty string ("") for printing output is sent to a port instead of the 
            <paramref name="filePath" />. The default value is <see langword="null" />.</param><param name="customPrintController"> custom printController such as PreviewPrintController for a GUI print preview</param><param name="Flatten">Flatten the PDF before printing - useful for displaying form field values</param></summary>
            <returns>The number of printed pages.</returns>
            <seealso cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />
        </member>
        <member name="M:IronPdf.PdfDocument.PrintToFile(System.String,System.Boolean)">
            <summary>
            Prints this PDF by sending it to the computer's printer. <para>For advanced real-world printing options
            please see overloads <see cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />.</para><para>Note: '<see cref="!:PrintToFile(string)" />' is only supported on: 'Windows'.</para><param name="filePath">output file path, if set to <see langword="null" /> or an empty string ("") default filePath will be random GUID.</param><param name="Flatten">Flatten the PDF before printing - useful for displaying form field values</param></summary>
            <returns>The number of printed pages.</returns>
            <seealso cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />
        </member>
        <member name="M:IronPdf.PdfDocument.PrintToFile(System.Int32,System.String,System.Boolean)">
            <summary>
            Prints this PDF by sending it to the computer's printer.   <para>For advanced real-world printing options
            please see overloads of this method and also <see cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />.</para><para>Note: '<see cref="!:PrintToFile(int, string)" />' is only supported on: 'Windows'.</para><param name="dpi">the horizontal and vertical PDF resolutions, in dots per inch. if <c>-1</c> print with default printer
            resolution. Default value is <c>-1</c>.</param><param name="filePath">output file path, if set to <see langword="null" /> or an empty string ("") default filePath will be random GUID.</param><param name="Flatten">Flatten the PDF before printing - useful for displaying form field values</param></summary>
            <returns>The number of printed pages.</returns>
            <seealso cref="M:IronPdf.PdfDocument.GetPrintDocument(System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController,System.Boolean)" />
        </member>
        <member name="M:IronPdf.PdfDocument.RemovePage(System.Int32)">
            <summary>
            Removes a page from the PDF at the given index.
            </summary>
            <param name="PageIndex">Index of the page.  Note: Page 1 has index 0...</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.RemovePages(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Removes a range of pages from the PDF
            </summary>
            <param name="PageIndexes">A list of pages indexes to remove.</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.RemovePages(System.Int32,System.Int32)">
            <summary>
            Removes a range of pages from the PDF
            </summary>
            <param name="StartIndex">The start index. Note: Page 1 has index 0</param>
            <param name="EndIndex">The end index. Note: The last page has index <c>PageCount -1</c></param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.Resize(IronPdf.Rendering.PdfPaperSize,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Resizes the PDF document to the specified paper size.
            </summary>
            <param name="PdfPaperSize">The size to resize the PDF document to.</param>
            <param name="Pages">The page(s) to resize, if not specified all pages will be resized</param>
            <remarks>
            The available paper sizes are defined in the <see cref="T:IronPdf.Rendering.PdfPaperSize" /> enumeration.
            </remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.Resize(System.Double,System.Double,IronPdf.Editing.MeasurementUnit,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Resizes the PdfDocument to the specified width, height, and desired measurement unit. 
            This will scale existing page contents to fit the new page dimensions, this may cause distortion in cases where Width-to-height ratio is different.
            </summary>
            <param name="PageWidth">The width of the resized PdfDocument.</param>
            <param name="PageHeight">The height of the resized PdfDocument.</param>
            <param name="MeasurementUnit">The measurement unit for the width and height values.</param>
            <param name="Pages">The page(s) to resize, if not specified all pages will be resized</param>
        </member>
        <member name="M:IronPdf.PdfDocument.Resize(IronPdf.Editing.Length,IronPdf.Editing.Length,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Resizes the PdfDocument to the specified width, height, and desired measurement unit. 
            This will scale existing page contents to fit the new page dimensions, this may cause distortion in cases where Width-to-height ratio is different.
            </summary>
            <param name="PageWidth">The width of the resized PdfDocument.</param>
            <param name="PageHeight">The height of the resized PdfDocument.</param>
            <param name="Pages">The page(s) to resize, if not specified all pages will be resized</param>
        </member>
        <member name="M:IronPdf.PdfDocument.GetPageRotation(System.Int32)">
            <summary>
            Gets the rotation of a PDF page in degrees.
            </summary>
            <param name="PageIndex">Index of the page to inspect. PageIndex is a 'Zero based' page number, the first page being 0.</param>
            <returns>Degrees of rotation</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.SetAllPageRotations(IronPdf.Rendering.PdfPageRotation)">
            <summary>
            Set rotation degree to all pages of the PdfDocument by a specified degree.
            </summary>
            <param name="Rotation">Degrees of rotation</param>
        </member>
        <member name="M:IronPdf.PdfDocument.RotateAllPages(IronPdf.Rendering.PdfPageRotation)">
            <summary>
            Legacy support. Has been replaced by <see cref="M:IronPdf.PdfDocument.SetAllPageRotations(IronPdf.Rendering.PdfPageRotation)" />.
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.SetPageRotation(System.Int32,IronPdf.Rendering.PdfPageRotation)">
            <summary>
            Set rotation degree to one page of the PdfDocument by a specified degree.
            </summary>
            <param name="PageIndex">Index of the page to rotate. PageIndex is a 'Zero based' page number, the first page being 0.</param>
            <param name="Rotation">Degrees of rotation</param>
        </member>
        <member name="M:IronPdf.PdfDocument.RotatePage(System.Int32,IronPdf.Rendering.PdfPageRotation)">
            <summary>
            Legacy support. Has been replaced by <see cref="M:IronPdf.PdfDocument.SetPageRotation(System.Int32,IronPdf.Rendering.PdfPageRotation)" />.
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.SetPageRotations(System.Collections.Generic.IEnumerable{System.Int32},IronPdf.Rendering.PdfPageRotation)">
            <summary>
            Set rotation degree to selected pages of the PdfDocument by a specified degree.
            </summary>
            <param name="PageIndexes">
            Indexes of the pages to rotate in an IEnumerable, list or array. PageIndex is a 'Zero based' page number,
            the first page being 0.
            </param>
            <param name="Rotation">Degrees of rotation</param>
        </member>
        <member name="M:IronPdf.PdfDocument.RotateSelectedPages(System.Collections.Generic.IEnumerable{System.Int32},IronPdf.Rendering.PdfPageRotation)">
            <summary>
            Legacy support. Has been replaced by <see cref="M:IronPdf.PdfDocument.SetPageRotations(System.Collections.Generic.IEnumerable{System.Int32},IronPdf.Rendering.PdfPageRotation)" />.
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.RemoveSignatures">
            <summary>
            Remove all digital signatures from the PDF document
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocument.QuickSignPdfWithDigitalSignatureFile(System.String,System.String,System.String,IronPdf.Signing.SignaturePermissions)">
            <summary>
            Please use <seealso cref="M:IronPdf.PdfDocument.SignWithFile(System.String,System.String,System.String,IronPdf.Signing.SignaturePermissions)" /> instead
            </summary>
            <seealso cref="T:IronPdf.Signing.PdfSignature" />
            <param name="CertificateFilePath">
            The file path to a .pfx or .p12 digital signing certificate which may be generated using Adobe Acrobat
            Viewer.
            </param>
            <param name="Password">The certificate password as a String.</param>
            <param name="TimeStampUrl">Url to use for timestamping</param>
            <param name="Permissions">Permissions regarding modifications to the document after the digital signature is applied</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
            <exclude />
        </member>
        <member name="M:IronPdf.PdfDocument.SignWithFile(System.String,System.String,System.String,IronPdf.Signing.SignaturePermissions)">
            <summary>
            Sign PDF with digital signature certificate.  For more advanced options please see <see cref="M:IronPdf.PdfDocument.SignPdfWithDigitalSignature(IronPdf.Signing.PdfSignature,IronPdf.Signing.SignaturePermissions)" /><para>Note that the PDF will not be fully signed until Saved
            using <see cref="M:IronPdf.PdfDocument.SaveAs(System.String,System.Boolean)" />,  <see cref="M:IronPdf.PdfDocument.TrySaveAs(System.String)" />,  <see cref="P:IronPdf.PdfDocument.Stream" /> or  <see cref="P:IronPdf.PdfDocument.BinaryData" />.  Multiple certificates may
            be used.</para></summary>
            <seealso cref="T:IronPdf.Signing.PdfSignature" />
            <param name="CertificateFilePath">
            The file path to a .pfx or .p12 digital signing certificate which may be generated using Adobe Acrobat
            Viewer.
            </param>
            <param name="Password">The certificate password as a String.</param>
            <param name="TimeStampUrl">Url to use for timestamping</param>
            <param name="Permissions">Permissions regarding modifications to the document after the digital signature is applied</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.SignPdfWithDigitalSignature(IronPdf.Signing.PdfSignature,IronPdf.Signing.SignaturePermissions)">
            <summary>
            Please use <seealso cref="M:IronPdf.PdfDocument.Sign(IronPdf.Signing.PdfSignature,IronPdf.Signing.SignaturePermissions)" /> instead
            </summary>
            <param name="Signature">The PdfSignature.</param>
            <param name="Permissions">Permissions regarding modifications to the document after the digital signature is applied</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.Sign(IronPdf.Signing.PdfSignature,IronPdf.Signing.SignaturePermissions)">
            <summary>
            Signs the PDF with digital signature with advanced options.  <para>Note that the PDF will not be fully
            signed until Saved using <see cref="M:IronPdf.PdfDocument.SaveAs(System.String,System.Boolean)" />,  <see cref="M:IronPdf.PdfDocument.TrySaveAs(System.String)" />,  <see cref="P:IronPdf.PdfDocument.Stream" /> or  <see cref="P:IronPdf.PdfDocument.BinaryData" />.  Multiple certificates may
            be used.</para></summary>
            <param name="Signature">The PdfSignature.</param>
            <param name="Permissions">Permissions regarding modifications to the document after the digital signature is applied</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.SignWithStore(System.String,System.Security.Cryptography.X509Certificates.StoreLocation,IronPdf.Signing.SignaturePermissions)">
            <summary>
            Signs the PDF with digital signature extracted from your computer's signature storage.  <para>Note that the PDF will not be fully
            signed until Saved using <see cref="M:IronPdf.PdfDocument.SaveAs(System.String,System.Boolean)" />,  <see cref="M:IronPdf.PdfDocument.TrySaveAs(System.String)" />,  <see cref="P:IronPdf.PdfDocument.Stream" /> or  <see cref="P:IronPdf.PdfDocument.BinaryData" />.  Multiple certificates may
            be used.</para></summary>
            <param name="Thumbprint">Thumbprint of a signature in your local computer's signature storage</param>
            <param name="StoreLocation">Computer storage location</param>
            <param name="Permissions">Permissions regarding modifications to the document after the digital signature is applied</param>
            <returns>Returns this PdfDocument object, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.GetVerifiedSignatures">
            <summary>
            Returns a list of <see cref="T:IronPdf.Signing.Inspection.VerifiedSignature" />.
            </summary>
            <returns>list of <see cref="T:IronPdf.Signing.Inspection.VerifiedSignature" /></returns>
        </member>
        <member name="M:IronPdf.PdfDocument.VerifyPdfSignatures">
            <summary>
            Verifies all the PDF signatures for this PDF document and returns <c>true</c> if there are no invalid
            signatures.
            </summary>
            <returns>
                <c>true</c> if all digital signatures for this PDF document are currently valid. Editing a PDF document in
            any way will invalidate signatures.
            </returns>
        </member>
        <member name="M:IronPdf.PdfDocument.VerifyPdfSignaturesInFile(System.String)">
            <summary>
            Verifies all the PDF signatures for a PDF file (by file path) and returns <c>true</c> if there are no
            invalid signatures.
            </summary>
            <param name="PdfFilePath">The full or relative PDF file path.</param>
            <returns>
                <c>true</c> if all digital signatures for the PDF document are currently valid. Editing a PDF document in
            any way will invalidate signatures.
            </returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)">
            <summary>
            Edits the PDF by applying the <see cref="T:IronPdf.Editing.Stamper" />'s rendered <see cref="P:IronPdf.Editing.Stamper.Html" /> to every page.
            </summary>
            <param name="Stamper">The <see cref="T:IronPdf.Editing.Stamper" /> object that has the HTML to be stamped onto the PDF.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper,System.Int32)">
            <summary>
            Edits the PDF by applying the <see cref="T:IronPdf.Editing.Stamper" />'s rendered <see cref="P:IronPdf.Editing.Stamper.Html" /> to a single page.
            </summary>
            <param name="Stamper">The <see cref="T:IronPdf.Editing.Stamper" /> object that has the HTML to be stamped onto the PDF.</param>
            <param name="PageIndexToStamp">The page index (zero-based page number) to apply the stamp to.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Edits the PDF by applying the <see cref="T:IronPdf.Editing.Stamper" />'s rendered <see cref="P:IronPdf.Editing.Stamper.Html" /> to only selected page(s).
            </summary>
            <param name="Stamper">The <see cref="T:IronPdf.Editing.Stamper" /> object that has the HTML to be stamped onto the PDF.</param>
            <param name="PageIndexesToStamp">The list of page indexes (zero-based page number) to apply the stamp to.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyStampAsync(IronPdf.Editing.Stamper)">
            <summary>
            Asynchronously edits the PDF by applying the <see cref="T:IronPdf.Editing.Stamper" />'s rendered <see cref="P:IronPdf.Editing.Stamper.Html" /> to every page.
            </summary>
            <remarks>Please wait for the task to be completed before saving to file!</remarks>
            <param name="Stamper">The <see cref="T:IronPdf.Editing.Stamper" /> object that has the HTML to be stamped onto the PDF.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyStampAsync(IronPdf.Editing.Stamper,System.Int32)">
            <summary>
            Asynchronously edits the PDF by applying the <see cref="T:IronPdf.Editing.Stamper" />'s rendered <see cref="P:IronPdf.Editing.Stamper.Html" /> to a single page.
            </summary>
            <remarks>Please wait for the task to be completed before saving to file!</remarks>
            <param name="Stamper">The <see cref="T:IronPdf.Editing.Stamper" /> object that has the HTML to be stamped onto the PDF.</param>
            <param name="PageIndexToStamp">The page index (zero-based page number) to apply the stamp to.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyStampAsync(IronPdf.Editing.Stamper,System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Asynchronously edits the PDF by applying the <see cref="T:IronPdf.Editing.Stamper" />'s rendered <see cref="P:IronPdf.Editing.Stamper.Html" /> to only selected page(s).
            </summary>
            <remarks>Please wait for the task to be completed before saving to file!</remarks>
            <param name="Stamper">The <see cref="T:IronPdf.Editing.Stamper" /> object that has the HTML to be stamped onto the PDF.</param>
            <param name="PageIndexesToStamp">The page indexes (zero-based page number) to apply the stamps to.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyMultipleStamps(System.Collections.Generic.IEnumerable{IronPdf.Editing.Stamper},System.Int32)">
            <summary>
            Edits the PDF by applying multiple <see cref="T:IronPdf.Editing.Stamper" />'s rendered <see cref="P:IronPdf.Editing.Stamper.Html" /> to a single page.
            </summary>
            <param name="Stampers">A list of <see cref="T:IronPdf.Editing.Stamper" /> objects, each with their own HTML stamp, to all be stamped onto the PDF.</param>
            <param name="PageIndexToStamp">The page index (zero-based page number) to apply the stamps to.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
            <remarks>
            Note: This method combines all the provided <see cref="T:IronPdf.Editing.Stamper" /> objects into a single composite stamp before applying it to each page. 
            As a result, the final stamp appearance may differ slightly from the <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> method, which applies a single stamp at a time per page.
            Additionally, this method is generally faster than applying stamps one by one using <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> as it reduces multiple renderings and page processing operations.
            </remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyMultipleStamps(System.Collections.Generic.IEnumerable{IronPdf.Editing.Stamper},System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Edits the PDF by applying multiple <see cref="T:IronPdf.Editing.Stamper" />'s rendered <see cref="P:IronPdf.Editing.Stamper.Html" /> to only selected page(s).
            </summary>
            <param name="Stampers">A list of <see cref="T:IronPdf.Editing.Stamper" /> objects, each with their own HTML stamp, to all be stamped onto the PDF.</param>
            <param name="PageIndexesToStamp">The page indexes (zero-based page number) to apply the stamps to.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
            <remarks>
            Note: This method combines all the provided <see cref="T:IronPdf.Editing.Stamper" /> objects into a single composite stamp before applying it to each page. 
            As a result, the final stamp appearance may differ slightly from the <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> method, which applies a single stamp at a time per page.
            Additionally, this method is generally faster than applying stamps one by one using <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> as it reduces multiple renderings and page processing operations.
            </remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyMultipleStampsAsync(System.Collections.Generic.IEnumerable{IronPdf.Editing.Stamper})">
            <summary>
            Asynchronously edits the PDF by applying multiple <see cref="T:IronPdf.Editing.Stamper" />'s rendered <see cref="P:IronPdf.Editing.Stamper.Html" /> to every page.
            </summary>
            <param name="Stampers">A list of <see cref="T:IronPdf.Editing.Stamper" /> objects, each with their own HTML stamp, to all be stamped onto the PDF.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
            <remarks>
            Note: This method combines all the provided <see cref="T:IronPdf.Editing.Stamper" /> objects into a single composite stamp before applying it to each page. 
            As a result, the final stamp appearance may differ slightly from the <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> method, which applies a single stamp at a time per page.
            Additionally, this method is generally faster than applying stamps one by one using <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> as it reduces multiple renderings and page processing operations.
            </remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyMultipleStampsAsync(System.Collections.Generic.IEnumerable{IronPdf.Editing.Stamper},System.Int32)">
            <summary>
            Asynchronously edits the PDF by applying multiple <see cref="T:IronPdf.Editing.Stamper" />'s rendered <see cref="P:IronPdf.Editing.Stamper.Html" /> to a single page.
            </summary>
            <param name="Stampers">A list of <see cref="T:IronPdf.Editing.Stamper" /> objects, each with their own HTML stamp, to all be stamped onto the PDF.</param>
            <param name="PageIndexToStamp">The page index (zero-based page number) to apply the stamps to.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
            <remarks>
            Note: This method combines all the provided <see cref="T:IronPdf.Editing.Stamper" /> objects into a single composite stamp before applying it to each page. 
            As a result, the final stamp appearance may differ slightly from the <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> method, which applies a single stamp at a time per page.
            Additionally, this method is generally faster than applying stamps one by one using <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> as it reduces multiple renderings and page processing operations.
            </remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyMultipleStampsAsync(System.Collections.Generic.IEnumerable{IronPdf.Editing.Stamper},System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Asynchronously edits the PDF by applying multiple <see cref="T:IronPdf.Editing.Stamper" />'s rendered <see cref="P:IronPdf.Editing.Stamper.Html" /> to only selected page(s).
            </summary>
            <param name="Stampers">A list of <see cref="T:IronPdf.Editing.Stamper" /> objects, each with their own HTML stamp, to all be stamped onto the PDF.</param>
            <param name="PageIndexesToStamp">The page indexes (zero-based page number) to apply the stamps to.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
            <remarks>
            Note: This method combines all the provided <see cref="T:IronPdf.Editing.Stamper" /> objects into a single composite stamp before applying it to each page. 
            As a result, the final stamp appearance may differ slightly from the <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> method, which applies a single stamp at a time per page.
            Additionally, this method is generally faster than applying stamps one by one using <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> as it reduces multiple renderings and page processing operations.
            </remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyWatermark(System.String,System.Int32,IronPdf.Editing.VerticalAlignment,IronPdf.Editing.HorizontalAlignment)">
            <summary>
            Adds Watermark to PDF, Please use <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> for more control.
            <br /><para>For more information and a code example please visit: https://ironpdf.com/tutorials/csharp-edit-pdf-complete-tutorial/#add-a-watermark-to-a-pdf</para></summary>
            <param name="html">The HTML fragment which will be stamped onto your PDF.</param>
            <param name="opacity"> Watermark transparent value. 0 is invisible, 100 if fully opaque. </param>
            <param name="verticalAlignment"> The vertical alignment of the watermark relative to the page. </param>
            <param name="horizontalAlignment"> The horizontal alignment of the watermark relative to the page. </param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ApplyWatermark(System.String,System.Int32,System.Int32,IronPdf.Editing.VerticalAlignment,IronPdf.Editing.HorizontalAlignment)">
            <summary>
            Adds Watermark to PDF, Please use <see cref="M:IronPdf.PdfDocument.ApplyStamp(IronPdf.Editing.Stamper)" /> for more control.
            <br /><para>For more information and a code example please visit: https://ironpdf.com/tutorials/csharp-edit-pdf-complete-tutorial/#add-a-watermark-to-a-pdf</para></summary>
            <param name="html">The HTML fragment which will be stamped onto your PDF.</param>
            <param name="opacity"> Watermark transparent value. 0 is invisible, 100 if fully opaque. </param>
            <param name="verticalAlignment"> The vertical alignment of the watermark relative to the page. </param>
            <param name="horizontalAlignment"> The horizontal alignment of the watermark relative to the page. </param>
            <param name="rotation">Rotates the watermark clockwise from 0 to 360 degrees as specified.</param>
            <returns>Returns this <see cref="T:IronPdf.PdfDocument" />, allowing for a 'fluent' (LINQ like) chained in-line code style</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractAllText(IronPdf.TextExtractionOrder)">
            <summary>
            Extracts the written text content from the PDF and returns it as a string. <para>Pages are be separated by 4
            consecutive Environment.NewLines</para></summary>
            <param name="Order">Determines order of how text is extracted from each page (see <seealso cref="T:IronPdf.TextExtractionOrder" /></param>
            <returns>
            All text in the PDF as a string.
            </returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractTextFromPage(System.Int32)">
            <summary>
            Extracts the text content from one page of the PDF and returns it as a string.
            </summary>
            <param name="PageIndex">Index of the page.  Note: Page 1 has index 0...</param>
            <returns>The text extracted from the PDF page as a string.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractTextFromPage(System.Int32,IronPdf.TextExtractionOrder)">
            <summary>
            Extracts the text content from one page of the PDF and returns it as a string.
            </summary>
            <param name="PageIndex">Index of the page.  Note: Page 1 has index 0...</param>
            <param name="Order">Determines order of how text is extracted from each page (see <seealso cref="T:IronPdf.TextExtractionOrder" /></param>
            <returns>The text extracted from the PDF page as a string.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractTextFromPages(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Extracts the written text content from specified pages in the PDF PDF and returns it as a string.   Pages
            will be separated by 4 consecutive Environment.NewLines
            </summary>
            <param name="PageIndices">An IEnumerable list of page indexes.</param>
            <returns>The text extracted from the PDF pages as a string.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractTextFromPages(System.Collections.Generic.IEnumerable{System.Int32},IronPdf.TextExtractionOrder)">
            <summary>
            Extracts the written text content from specified pages in the PDF PDF and returns it as a string.   Pages
            will be separated by 4 consecutive Environment.NewLines
            </summary>
            <param name="PageIndices">An IEnumerable list of page indexes.</param>
            <param name="Order">Determines order of how text is extracted from each page (see <seealso cref="T:IronPdf.TextExtractionOrder" /></param>
            <returns>The text extracted from the PDF pages as a string.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ExtractTextFromPages(System.Int32,System.Int32,IronPdf.TextExtractionOrder)">
            <summary>
            Extracts the written text content from a range of pages within the PDF and returns it as a string.    Pages
            will be separated by 4 consecutive Environment.NewLines
            </summary>
            <param name="StartIndex">The start page index. Note: Page 1 has index 0</param>
            <param name="EndIndex">The end page index. Note: The last page has index <c>PageCount -1</c></param>
            <param name="Order">Determines order of how text is extracted from each page (see <seealso cref="T:IronPdf.TextExtractionOrder" /></param>
            <returns>The text extracted from the PDF pages as a string.</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.GetInstalledFonts">
            <summary>
            OBSOLETE. Return a list of all fonts referenced in this document
            </summary>
            <para>
            Please use <see cref="P:IronPdf.PdfDocument.Fonts" /> instead
            </para>
            <returns>List of font info structure used in this document</returns>
        </member>
        <member name="M:IronPdf.PdfDocument.ReplaceTextOnPage(System.Int32,System.String,System.String,System.String,System.Nullable{System.Single})">
            <summary>
            Replaces the specified old text with new text on a given page using a custom font.
            </summary>
            <param name="PageIndex">The index of the page to search for old text to replace.</param>
            <param name="OldText">The old text to be replaced.</param>
            <param name="NewText">The new text to replace the old text with.</param>
            <param name="CustomFontName">The name of the custom font for the new text. Must be a system font or added to the document using Fonts.Add().</param>
            <param name="CustomFontSize">The font size of the new text (optional).</param>
        </member>
        <member name="M:IronPdf.PdfDocument.ReplaceTextOnPage(System.Int32,System.String,System.String,IronPdf.Fonts.PdfFont,System.Nullable{System.Single})">
            <summary>
            Replaces the specified old text with new text on a given page using a custom PdfFont.
            </summary>
            <param name="PageIndex">The index of the page to search for old text to replace.</param>
            <param name="OldText">The old text to be replaced.</param>
            <param name="NewText">The new text to replace the old text with.</param>
            <param name="CustomFont">The custom font for the new text (optional).</param>
            <param name="CustomFontSize">The font size of the new text (optional).</param>
        </member>
        <member name="M:IronPdf.PdfDocument.ReplaceTextOnPages(System.Int32[],System.String,System.String,System.String,System.Nullable{System.Single})">
            <summary>
            Replaces the specified old text with new text on a set of pages using a custom font by name.
            </summary>
            <param name="PageIndices">An array of page indices to search for old text to replace.</param>
            <param name="OldText">The old text to be replaced.</param>
            <param name="NewText">The new text to replace the old text with.</param>
            <param name="CustomFontName">The name of the custom font for the new text. Must be a system font or added to the document using Fonts.Add().</param>
            <param name="CustomFontSize">The font size of the new text (optional).</param>
        </member>
        <member name="M:IronPdf.PdfDocument.ReplaceTextOnPages(System.Int32[],System.String,System.String,IronPdf.Fonts.PdfFont,System.Nullable{System.Single})">
            <summary>
            Replaces the specified old text with new text on a set of pages using a custom PdfFont.
            </summary>
            <param name="PageIndices">An array of page indices to search for old text to replace.</param>
            <param name="OldText">The old text to be replaced.</param>
            <param name="NewText">The new text to replace the old text with.</param>
            <param name="CustomFont">The custom font for the new text (optional).</param>
            <param name="CustomFontSize">The font size of the new text (optional).</param>
        </member>
        <member name="M:IronPdf.PdfDocument.ReplaceTextOnAllPages(System.String,System.String,System.String,System.Nullable{System.Single})">
            <summary>
            Replaces the specified old text with new text on all pages using a custom font by name.
            </summary>
            <param name="OldText">The old text to be replaced.</param>
            <param name="NewText">The new text to replace the old text with.</param>
            <param name="CustomFontName">The name of the custom font for the new text. Must be a system font or added to the document using Fonts.Add().</param>
            <param name="CustomFontSize">The font size of the new text (optional).</param>
        </member>
        <member name="M:IronPdf.PdfDocument.ReplaceTextOnAllPages(System.String,System.String,IronPdf.Fonts.PdfFont,System.Nullable{System.Single})">
            <summary>
            Replaces the specified old text with new text on all pages using a custom PdfFont.
            </summary>
            <param name="OldText">The old text to be replaced.</param>
            <param name="NewText">The new text to replace the old text with.</param>
            <param name="CustomFont">The custom font for the new text (optional).</param>
            <param name="CustomFontSize">The font size of the new text (optional).</param>
        </member>
        <member name="M:IronPdf.PdfDocument.RedactTextOnPage(System.Int32,System.String,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            Redacts sensitive text on a specified page within a document.
            </summary>
            <param name="PageIndex">The zero-based index of the page where redaction will occur.</param>
            <param name="ReplaceText">The text string to be redacted</param>
            <param name="CaseSensitive">Match capital or lower-case letters</param>
            <param name="OnlyMatchWholeWords">Only match whole words</param>
            <param name="DrawRectangles">Draw black rectangles around redacted area(s)</param>
            <param name="ReplacementText">Text to be written in place of redacted items</param>
        </member>
        <member name="M:IronPdf.PdfDocument.RedactTextOnPages(System.Int32[],System.String,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            Redacts sensitive text on multiple specified pages within a document.
            </summary>
            <param name="PageIndices">An array of zero-based page indices where redaction will occur.</param>
            <param name="ReplaceText">The text string to be replaced with a black box.</param>
            <param name="CaseSensitive">Match capital or lower-case letters</param>
            <param name="OnlyMatchWholeWords">Only match whole words</param>
            <param name="DrawRectangles">Draw black rectangles around redacted area(s)</param>
            <param name="ReplacementText">Text to be written in place of redacted items</param>
        </member>
        <member name="M:IronPdf.PdfDocument.RedactTextOnAllPages(System.String,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            Redacts sensitive text on the entire document.
            </summary>
            <param name="ReplaceText">The text string to be redacted</param>
            <param name="CaseSensitive">Match capital or lower-case letters</param>
            <param name="OnlyMatchWholeWords">Only match whole words</param>
            <param name="DrawRectangles">Draw black rectangles around redacted area(s)</param>
            <param name="ReplacementText">Text to be written in place of redacted items</param>
        </member>
        <member name="M:IronPdf.PdfDocument.RedactRegionOnPage(System.Int32,IronSoftware.Drawing.RectangleF)">
            <summary>
            Redacts a sensitive region on a specified page within a document.
            </summary>
            <param name="PageIndex">The zero-based index of the page where redaction will occur.</param>
            <param name="Region">The region to be redact.</param>
        </member>
        <member name="M:IronPdf.PdfDocument.RedactRegionsOnPage(System.Int32,System.Collections.Generic.IEnumerable{IronSoftware.Drawing.RectangleF})">
            <summary>
            Redacts sensitive regions on a specified page within a document.
            </summary>
            <param name="PageIndex">The zero-based index of the page where redaction will occur.</param>
            <param name="Regions">The regions to be redact.</param>
        </member>
        <member name="M:IronPdf.PdfDocument.RedactRegionOnPages(System.Int32[],IronSoftware.Drawing.RectangleF)">
            <summary>
            Redacts a sensitive region on multiple specified pages within a document.
            </summary>
            <param name="PageIndices">An array of zero-based page indices where redaction will occur.</param>
            <param name="Region">The region to be redact.</param>
        </member>
        <member name="M:IronPdf.PdfDocument.RedactRegionsOnAllPages(IronSoftware.Drawing.RectangleF)">
            <summary>
            Redacts a sensitive region on the entire document.
            </summary>
            <param name="Region">The region to be redact.</param>
        </member>
        <member name="M:IronPdf.PdfDocument.ToBitmap(System.Int32,System.Boolean)">
            <summary>
            Rasterizes (renders) the PDF into <see cref="T:IronSoftware.Drawing.AnyBitmap" /> objects.  1 Bitmap for each page. <para>Please add
            an assembly reference to <see cref="T:IronSoftware.Drawing.AnyBitmap" /> to use this "PDF To Image" method.</para><para>Please Dispose() each
            Bitmap object after use.</para></summary>
            <param name="DPI">
            The resolution of the output Bitmap in 'Dots Per Inch'.  Higher DPI creates larger bitmap files of higher
            image quality.
            </param>
            <returns>
            An array of <see cref="T:IronSoftware.Drawing.AnyBitmap" /> image objects which can be saved, manipulated, displayed or edited
            programmatically.
            </returns>
            <remarks>The <paramref name="DPI" /> may be ignored on some Linux distros.</remarks>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
        </member>
        <member name="M:IronPdf.PdfDocument.ToBitmap(System.Collections.Generic.IEnumerable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Rasterizes (renders) the PDF into <see cref="T:IronSoftware.Drawing.AnyBitmap" /> objects. <para>Specific pages may be selected using
            the PageIndexes parameter.</para><para>Please add an assembly reference to <see cref="T:IronSoftware.Drawing.AnyBitmap" /> to use this "PDF
            To Image" method</para></summary>
            <param name="pageIndexes">Specific zero based page index may be given to only convert part of the PDF document to images</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of <see cref="T:IronSoftware.Drawing.AnyBitmap" /> image objects which can be saved, manipulated, displayed or edited
            programmatically.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToBitmap(System.Nullable{System.Int32},System.Nullable{System.Int32},System.Collections.Generic.IEnumerable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Rasterizes (renders) the PDF into <see cref="T:IronSoftware.Drawing.AnyBitmap" /> objects. <para>Specific pages may be selected using
            the PageIndexes parameter.  The Widths and Height of the output images may be specified.</para><para>Please
            add an assembly reference to <see cref="T:IronSoftware.Drawing.AnyBitmap" /> to use this "PDF To Image" method</para></summary>
            <param name="imageMaxWidth">The target maximum width(in pixel) of the output images.</param>
            <param name="imageMaxHeight">The target maximum height(in pixel) of the output images.</param>
            <param name="pageIndexes">Specific zero based page index may be given to only convert part of the PDF document to images</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of <see cref="T:IronSoftware.Drawing.AnyBitmap" /> image objects which can be saved, manipulated, displayed or edited
            programmatically.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.PageToBitmap(System.Int32,System.Int32,System.Boolean)">
            <summary>
            Renders a single page of the PDF to a <see cref="T:IronSoftware.Drawing.AnyBitmap" /> object. <para>Please add an assembly reference
            to <see cref="T:IronSoftware.Drawing.AnyBitmap" /> to use this "PDF To Image" method</para></summary>
            <param name="pageIndex">The zero based page number to be converted to an image. E.g. Page 1 has a pageIndex of 0</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>A <see cref="T:IronSoftware.Drawing.AnyBitmap" /> of the rendered PDF page.</returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.PageToBitmap(System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders a single page of the PDF to a <see cref="T:IronSoftware.Drawing.AnyBitmap" /> object. <para>Please add an assembly reference
            to <see cref="T:IronSoftware.Drawing.AnyBitmap" /> to use this "PDF To Image" method</para></summary>
            <param name="pageIndex">The zero based page number to be converted to an image. E.g. Page 1 has a pageIndex of 0</param>
            <param name="imageMaxWidth">The target maximum width of the output images.</param>
            <param name="imageMaxHeight">The target maximum height of the output images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>A <see cref="T:IronSoftware.Drawing.AnyBitmap" /> of the rendered PDF page.</returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.RasterizeToImageFiles(System.String,IronPdf.Imaging.ImageType,System.Int32,System.Boolean)">
            <summary>
            Renders the PDF and exports image Files in convenient formats.  1 image file is created for each
            page.<para>FileNamePattern should normally contain an asterisk (*) character which will be substituted for
            the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_page_*.png</param>
            <param name="ImageFileType">
            Type of the image file.  If not specified, a best guess will be taken from the FileNamePattern file
            extension
            </param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>An array of the file paths of the image files created.</returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.RasterizeToImageFiles(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},IronPdf.Imaging.ImageType,System.Int32,System.Boolean)">
            <summary>
            Renders the PDF and exports image Files in convenient formats.  Image dimensions may be specified.  1 image
            file is created for each page.<para>FileNamePattern should normally contain an asterisk (*) character which
            will be substituted for the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_page_*.png</param>
            <param name="ImageFileType">
            Type of the image file.  If not specified, a best guess will be taken from the FileNamePattern file
            extension
            </param>
            <param name="ImageMaxWidth">The target maximum width(in pixel) of the output images.</param>
            <param name="ImageMaxHeight">The target maximum height(in pixel) of the output images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.RasterizeToImageFiles(System.String,System.Collections.Generic.IEnumerable{System.Int32},IronPdf.Imaging.ImageType,System.Int32,System.Boolean)">
            <summary>
            Renders the PDF and exports image Files in convenient formats.  Page Numbers may be specified.  1 image file
            is created for each page. <para>FileNamePattern should normally contain an asterisk (*) character which will
            be substituted for the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_page_*.png</param>
            <param name="ImageFileType">
            Type of the image file.  If not specified, a best guess will be taken from the FileNamePattern file
            extension
            </param>
            <param name="PageIndexes">A list of the specific zero based page numbe to render as images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.RasterizeToImageFiles(System.String,System.Collections.Generic.IEnumerable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},IronPdf.Imaging.ImageType,System.Int32,System.Boolean)">
            <summary>
            Renders the PDF and exports image Files in convenient formats.  Page Numbers may be specified.  1 image file
            is created for each page. <para>FileNamePattern should normally contain an asterisk (*) character which will
            be substituted for the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_page_*.png</param>
            <param name="ImageFileType">
            Type of the image file.  If not specified, a best guess will be taken from the FileNamePattern file
            extension
            </param>
            <param name="PageIndexes">A list of the specific zero based page numbe to render as images.</param>
            <param name="ImageMaxWidth">The target maximum width(in pixel) of the output images.</param>
            <param name="ImageMaxHeight">The target maximum height(in pixel) of the output images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToJpegImages(System.String,System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as JPEG files and saves them to disk. <para>Specific image dimensions and page
            numbers may be given as optional parameters</para><para>FileNamePattern should normally contain an asterisk
            (*) character which will be substituted for the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_page_*.jpg</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToJpegImages(System.String,System.Collections.Generic.IEnumerable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as JPEG files and saves them to disk. <para>Specific image dimensions and page
            numbers may be given as optional parameters</para><para>FileNamePattern should normally contain an asterisk
            (*) character which will be substituted for the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_page_*.jpg</param>
            <param name="PageIndexes">A list of the specific zero based page numbe to render as images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToJpegImages(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as JPEG files and saves them to disk. <para>Specific image dimensions and page
            numbers may be given as optional parameters</para><para>FileNamePattern should normally contain an asterisk
            (*) character which will be substituted for the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_page_*.jpg</param>
            <param name="ImageMaxWidth">The target maximum width(in pixel) of the output images.</param>
            <param name="ImageMaxHeight">The target maximum height(in pixel) of the output images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToJpegImages(System.String,System.Collections.Generic.IEnumerable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as JPEG files and saves them to disk. <para>Specific image dimensions and page
            numbers may be given as optional parameters</para><para>FileNamePattern should normally contain an asterisk
            (*) character which will be substituted for the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_page_*.jpg</param>
            <param name="PageIndexes">A list of the specific zero based page numbe to render as images.</param>
            <param name="ImageMaxWidth">The target maximum width(in pixel) of the output images.</param>
            <param name="ImageMaxHeight">The target maximum height(in pixel) of the output images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToPngImages(System.String,System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as PNG (Portable Network Graphic) files and saves them to disk. <para>Specific
            image dimensions and page numbers may be given as optional parameters</para><para>FileNamePattern should
            normally contain an asterisk (*) character which will be substituted for the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_pages_*.png</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToPngImages(System.String,System.Collections.Generic.IEnumerable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as PNG (Portable Network Graphic) files and saves them to disk. <para>Specific
            image dimensions and page numbers may be given as optional parameters</para><para>FileNamePattern should
            normally contain an asterisk (*) character which will be substituted for the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_pages_*.png</param>
            <param name="PageIndexes">A list of the specific zero based page numbe to render as images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToPngImages(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as PNG (Portable Network Graphic) files and saves them to disk. <para>Specific
            image dimensions and page numbers may be given as optional parameters</para><para>FileNamePattern should
            normally contain an asterisk (*) character which will be substituted for the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_pages_*.png</param>
            <param name="ImageMaxWidth">The target maximum width(in pixel) of the output images.</param>
            <param name="ImageMaxHeight">The target maximum height(in pixel) of the output images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToPngImages(System.String,System.Collections.Generic.IEnumerable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as PNG (Portable Network Graphic) files and saves them to disk. <para>Specific
            image dimensions and page numbers may be given as optional parameters</para><para>FileNamePattern should
            normally contain an asterisk (*) character which will be substituted for the page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_pages_*.png</param>
            <param name="PageIndexes">A list of the specific zero based page numbe to render</param>
            <param name="ImageMaxWidth">The target maximum width(in pixel) of the output images.</param>
            <param name="ImageMaxHeight">The target maximum height(in pixel) of the output images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToTiffImages(System.String,System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as TIFF (Tagged Image File Format / Tif) files and saves them to disk.
            <para>Specific image dimensions and page numbers may be given as optional parameters</para><para>FileNamePattern should normally contain an asterisk (*) character which will be substituted for the
            page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_pages_*.tiff</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToTiffImages(System.String,System.Collections.Generic.IEnumerable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as TIFF (Tagged Image File Format / Tif) files and saves them to disk.
            <para>Specific image dimensions and page numbers may be given as optional parameters</para><para>FileNamePattern should normally contain an asterisk (*) character which will be substituted for the
            page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_pages_*.tiff</param>
            <param name="PageIndexes">A list of the specific zero based page number to render</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToTiffImages(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as TIFF (Tagged Image File Format / Tif) files and saves them to disk.
            <para>Specific image dimensions and page numbers may be given as optional parameters</para><para>FileNamePattern should normally contain an asterisk (*) character which will be substituted for the
            page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_pages_*.tiff</param>
            <param name="ImageMaxWidth">The target maximum width(in pixel) of the output images.</param>
            <param name="ImageMaxHeight">The target maximum height(in pixel) of the output images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToTiffImages(System.String,System.Collections.Generic.IEnumerable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as TIFF (Tagged Image File Format / Tif) files and saves them to disk.
            <para>Specific image dimensions and page numbers may be given as optional parameters</para><para>FileNamePattern should normally contain an asterisk (*) character which will be substituted for the
            page numbers</para></summary>
            <param name="FileNamePattern">A full or partial file path for the output files containing an asterisk.  E.g.  C:\images\pdf_pages_*.tiff</param>
            <param name="PageIndexes">A list of the specific zero based page number to render</param>
            <param name="ImageMaxWidth">The target maximum width(in pixel) of the output images.</param>
            <param name="ImageMaxHeight">The target maximum height(in pixel) of the output images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToMultiPageTiffImage(System.String,System.Collections.Generic.IEnumerable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as TIFF (Tagged Image File Format / Tif) file and saves it to disk.
            <para>Specific image dimensions and page numbers may be given as optional parameters</para><para>FileNamePattern should normally contain an asterisk (*) character which will be substituted for the
            page numbers</para></summary>
            <param name="FileName">A full file path for the output file.  E.g.  C:\images\pdf_pages.tiff</param>
            <param name="PageIndexes">A list of the specific zero based page number to render</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            A file path of the image file created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToMultiPageTiffImage(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as TIFF (Tagged Image File Format / Tif) file and saves it to disk.
            <para>Specific image dimensions and page numbers may be given as optional parameters</para><para>FileNamePattern should normally contain an asterisk (*) character which will be substituted for the
            page numbers</para></summary>
            <param name="FileName">A full file path for the output file.  E.g.  C:\images\pdf_pages.tiff</param>
            <param name="ImageMaxWidth">The target maximum width(in pixel) of the output images.</param>
            <param name="ImageMaxHeight">The target maximum height(in pixel) of the output images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="M:IronPdf.PdfDocument.ToMultiPageTiffImage(System.String,System.Collections.Generic.IEnumerable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Int32,System.Boolean)">
            <summary>
            Renders the pages of the PDF as TIFF (Tagged Image File Format / Tif) file and saves it to disk.
            <para>Specific image dimensions and page numbers may be given as optional parameters</para><para>FileNamePattern should normally contain an asterisk (*) character which will be substituted for the
            page numbers</para></summary>
            <param name="FileName">A full file path for the output file.  E.g.  C:\images\pdf_pages.tiff</param>
            <param name="PageIndexes">A list of the specific zero based page numbe to number to render</param>
            <param name="ImageMaxWidth">The target maximum width(in pixel) of the output images.</param>
            <param name="ImageMaxHeight">The target maximum height(in pixel) of the output images.</param>
            <param name="DPI">The desired resolution of the output Images.</param>
            <param name="Flatten">Flatten the PDF before rendering the images - useful for displaying form field values</param>
            <returns>
            An array of the file paths of the image files created.
            </returns>
            <remarks>The <paramref name="DPI" /> will be ignored under Linux and macOS.</remarks>
        </member>
        <member name="T:IronPdf.TempFolder">
            <exclude />
        </member>
        <member name="M:IronPdf.TempFolder.CleanupTempImages(System.Nullable{System.TimeSpan})">
            <exclude />
        </member>
        <member name="T:IronPdf.License">
            <summary>
             Allows IronPdf license keys to be applied globally across an application.
             </summary>
        </member>
        <member name="P:IronPdf.License.IsLicensed">
            <summary>
            Determines whether this instance of IronPDF is Licensed. <para>Will return false unless a <see cref="P:IronPdf.License.LicenseKey" /> is set to a valid trial or full license key in IronPdf.License.LicenseKey,
            Web.Config , App.Config or appsettings.json in .Net Core.</para><para>See https://ironpdf.com/licensing/ and https://ironpdf.com/docs/license/license-keys/</para></summary>
            <returns>True if the license key given is valid.</returns>
        </member>
        <member name="P:IronPdf.License.LicenseKey">
            <summary>
             Removes watermarks. Get Licensed at https://ironpdf.com/licensing <para>For .Net framework applications, a
             license key can alternatively be added to Web.Config or App.Config XML file using  &lt;add
             key="IronPdf.LicenseKey" value="IRONPDF-LICENSE-KEY"/&gt; within the appSettings tag. See
             https://ironpdf.com/docs/license/license-keys/</para><para>For .Net Core applications, a license key  may
             be added to appsettings.json where the key name is "IronPdf.LicenseKey" and the value is a valid IronPDF
             trial or full license key.</para><para>See https://ironpdf.com/licensing/ for licensing options.</para></summary>
        </member>
        <member name="M:IronPdf.License.IsValidLicense(System.String)">
            <summary>
            Determines whether a string license key is valid. <para>See https://ironpdf.com/licensing/ for trial,
            development and commercial deployment licensing options.</para></summary>
            <param name="LicenseKey">IronPDF license key as a string</param>
            <returns>True if the license key given is valid.</returns>
        </member>
        <member name="M:IronPdf.License.DisableAppAnalytics">
            <summary>
            Disables AppAnalytics for IronPdf. Limitations apply.
            </summary>
        </member>
        <member name="T:IronPdf.Pages.LineTextObject">
            <summary>
            Represents a line of text within a document
            </summary>
        </member>
        <member name="M:IronPdf.Pages.LineTextObject.#ctor(IronSoftware.IDocumentId,IronSoftware.IPdfTextObject)">
            <summary>
            Create a new line text object and add the specified chunk
            </summary>
            <param name="id">Document identifier</param>
            <param name="chunk">Chunk to add</param>
        </member>
        <member name="M:IronPdf.Pages.LineTextObject.AddChunk(IronSoftware.IPdfTextObject)">
            <summary>
            Add a text object to this line
            </summary>
            <param name="chunk">Text object to add</param>
        </member>
        <member name="M:IronPdf.Pages.LineTextObject.ParseChunks">
            <summary>
            Parse all chunks to generate aggregate properties of the line
            </summary>
        </member>
        <member name="T:IronPdf.Pages.PdfPage">
            <summary>
            A class which represents one page of a PDF Document.
            </summary>
        </member>
        <member name="M:IronPdf.Pages.PdfPage.#ctor(IronSoftware.IDocumentId,System.Int32)">
            <summary>
            Represent an existing PDF page, already attached to a document
            </summary>
            <param name="id_in">Document identifier</param>
            <param name="page_index">Page index</param>
        </member>
        <member name="M:IronPdf.Pages.PdfPage.#ctor(System.Double,System.Double)">
            <summary>
            Create a new PDF page, not yet attached to a document
            </summary>
            <param name="Width">Page width (mm)</param>
            <param name="Height">Page height (mm)</param>
        </member>
        <member name="P:IronPdf.Pages.PdfPage.Height">
            <summary>
            Gets the height of the pdf page in mm.
            </summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPage.PageRotation">
            <summary>
            Gets the page orientation.
            </summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPage.ObjectModel">
            <inheritdoc />
        </member>
        <member name="P:IronPdf.Pages.PdfPage.PrintHeight">
            <summary>
            Gets the height of the pdf page  in printer points.
            </summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPage.PrintWidth">
            <summary>
            Gets the width of the pdf page in printer points.
            </summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPage.Text">
            <summary>
            Page text
            </summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPage.Width">
            <summary>
            Gets the width of the pdf page in mm.
            </summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPage.PageIndex">
            <summary>
            Gets the page index
            </summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPage.Characters">
            <summary>
            A collection of all characters on this page and their position
            <para>
            Also see <seealso cref="P:IronPdf.Pages.PdfPage.TextChunks" /> and <seealso cref="P:IronPdf.Pages.PdfPage.Lines" /></para></summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPage.TextChunks">
            <summary>
            A collection of all text objects on this page and their position
            <para>
            The contents of each text object is rendered as a single unit
            </para><para>
            The contents of each text object share font, origin position, etc.
            </para><para>
            Also see <seealso cref="P:IronPdf.Pages.PdfPage.Characters" /> and <seealso cref="P:IronPdf.Pages.PdfPage.Lines" /></para></summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPage.Lines">
            <summary>
            A collection of all lines of text on this page and their position
            </summary>
            <para>
            Also see <seealso cref="P:IronPdf.Pages.PdfPage.TextChunks" /> and <seealso cref="P:IronPdf.Pages.PdfPage.Characters" /></para>
            <remarks>Derived from <see cref="P:IronPdf.Pages.PdfPage.TextChunks" /> for your convenience</remarks>
        </member>
        <member name="M:IronPdf.Pages.PdfPage.Resize(System.Double,System.Double,IronPdf.Editing.MeasurementUnit)">
            <summary>
            Resize this page to the specified dimensions (in millimeters)
            </summary>
            <param name="PageWidth">Desired page width, in millimeters</param>
            <param name="PageHeight">Desired page height, in millimeters</param>
            <param name="Units">Optionally specify units of measurement for input parameters</param>
        </member>
        <member name="M:IronPdf.Pages.PdfPage.Extend(System.Double,System.Double,System.Double,System.Double,IronPdf.Editing.MeasurementUnit)">
            <summary>
            Extends this page bounds using the specified parameters (in millimeters)
            <para>
            Does not resize page content; results in an empty margin around existing page content
            </para></summary>
            <param name="ExtendLeft">Desired amount (mm) to extend page width towards the left</param>
            <param name="ExtendRight">Desired amount (mm) to extend page width towards the right</param>
            <param name="ExtendTop">Desired amount (mm) to extend page height towards the top</param>
            <param name="ExtendBottom">Desired amount (mm) to extend page height towards the bottom</param>
            <param name="Units">Optionally specify units of measurement for input parameters</param>
        </member>
        <member name="M:IronPdf.Pages.PdfPage.Transform(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Transforms this page contents using the specified parameters
            <para>
            Affects the appearance of all content displayed on the page. Does NOT affect the physical page dimensions.
            </para></summary>
            <param name="MoveX">Move the page contents left (negative) or right (positive), in millimeters</param>
            <param name="MoveY">Move the page contents down (negative) or up (positive), in millimeters</param>
            <param name="ScaleX">Scale the page contents horizontally (0.0 to infinity, where 1.0 is default scaling)</param>
            <param name="ScaleY">Scale the page contents vertically (0.0 to infinity, where 1.0 is default scaling)</param>
        </member>
        <member name="T:IronPdf.Pages.PdfPageModel">
            <summary>
            Document object model for a PDF page
            </summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPageModel.ImageObjects">
            <summary>
            Image objects on this page
            </summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPageModel.PathObjects">
            <summary>
            Path objects on this page
            </summary>
        </member>
        <member name="P:IronPdf.Pages.PdfPageModel.TextObjects">
            <summary>
            Text objects on this page
            </summary>
        </member>
        <member name="M:IronPdf.Pages.PdfPageModel.#ctor">
            <summary>
            Create a new empty PDF page model
            </summary>
        </member>
        <member name="M:IronPdf.Pages.PdfPageModel.#ctor(IronSoftware.IDocumentId,System.Int32)">
            <summary>
            Create a new object model for a PDF page
            </summary>
            <param name="id_in">Document identifier</param>
            <param name="page_index_in">Page index</param>
        </member>
        <member name="M:IronPdf.Pages.PdfPageModel.ToJson">
            <inheritdoc />
        </member>
        <member name="T:IronPdf.Pages.PdfPagesCollection">
            <summary>
            Class for managing the collection of <see cref="T:IronPdf.Pages.PdfPage" /> elements.
            </summary>
        </member>
        <member name="M:IronPdf.Pages.PdfPagesCollection.#ctor(IronSoftware.Pdfium.IPdfClient,IronSoftware.IDocumentId)">
            <summary>
            Create new page collection using the specified client and document identifier
            </summary>
            <param name="client">Pdf client</param>
            <param name="id">Document id</param>
        </member>
        <member name="M:IronPdf.Pages.PdfPagesCollection.AddInternal(IronPdf.Pages.PdfPage)">
            <summary>
            Add to .NET collection without actually sending the item to native code
            </summary>
            <param name="item">Path object item</param>
        </member>
        <member name="M:IronPdf.Pages.PdfPagesCollection.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:IronPdf.CompressionOptions">
            <summary>
            Various compression options for reducing the size of a PDF document
            </summary>
        </member>
        <member name="P:IronPdf.CompressionOptions.CompressImages">
            <summary>
            Compress existing images using JPG encoding and the specified settings
            </summary>
        </member>
        <member name="P:IronPdf.CompressionOptions.RemoveStructureTree">
            <summary>
            Remove document struct tree information which describes the logical layout of the document.
            <para>
            Removing the "structure tree" can significantly reduce the disk space used by the document.
            </para><para>
            Removing the "structure tree" of a complicated document can negatively impact text selection.
            </para></summary>
        </member>
        <member name="P:IronPdf.CompressionOptions.ShrinkImages">
            <summary>
            Scale down the image resolution according to its visible size in the PDF document. This will drastically reduce the size and quality of the images.
            </summary>
        </member>
        <member name="P:IronPdf.CompressionOptions.HighQualityImageSubsampling">
            <summary>
            True to use 444 chroma subsampling for a higher quality image. False to use 411 chrome subsampling to further reduce the image size.
            </summary>
        </member>
        <member name="P:IronPdf.CompressionOptions.JpegQuality">
            <summary>
            JPEG quality (1 - 100) to use during image compression
            </summary>
        </member>
        <member name="T:IronPdf.NaturalLanguages">
            <summary />
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic">
            <summary>
            Arabic Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Bengali">
            <summary>
            Bengali Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Chamorro">
            <summary>
            Chamorro Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Danish">
            <summary>
            Danish Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.German">
            <summary>
            German Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Greek">
            <summary>
            Greek Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English">
            <summary>
            English Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish">
            <summary>
            Spanish Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French">
            <summary>
            French Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Irish">
            <summary>
            Irish Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Croatian">
            <summary>
            Croatian Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Hungarian">
            <summary>
            Hungarian Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Italian">
            <summary>
            Italian Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Korean">
            <summary>
            Korean Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Lingala">
            <summary>
            Lingala Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Malay">
            <summary>
            Malay Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Dutch">
            <summary>
            Dutch Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Portuguese">
            <summary>
            Portuguese Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Sindhi">
            <summary>
            Sindhi Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Serbian">
            <summary>
            Serbian Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Swati">
            <summary>
            Swati Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Swedish">
            <summary>
            Swedish Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Swahili">
            <summary>
            Swahili Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Tamil">
            <summary>
            Tamil Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Tswana">
            <summary>
            Tswana Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Turkish">
            <summary>
            Turkish Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Urdu">
            <summary>
            Urdu Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.MandarinChinese">
            <summary>
            Mandarin Chinese Language
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_UnitedArabEmirates">
            <summary>
            Arabic Language for United Arab Emirates Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Bahrain">
            <summary>
            Arabic Language for Bahrain Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Algeria">
            <summary>
            Arabic Language for Algeria Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Egypt">
            <summary>
            Arabic Language for Egypt Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Israel">
            <summary>
            Arabic Language for Israel Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Iraq">
            <summary>
            Arabic Language for Iraq Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Jordan">
            <summary>
            Arabic Language for Jordan Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Kuwait">
            <summary>
            Arabic Language for Kuwait Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Lebanon">
            <summary>
            Arabic Language for Lebanon Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Libya">
            <summary>
            Arabic Language for Libya Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Morocco">
            <summary>
            Arabic Language for Morocco Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Mauritania">
            <summary>
            Arabic Language for Mauritania Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Oman">
            <summary>
            Arabic Language for Oman Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_PalestinianWestBankAndGaza">
            <summary>
            Arabic Language for Palestinian West Bank and Gaza Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Qatar">
            <summary>
            Arabic Language for Qatar Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_SaudiArabia">
            <summary>
            Arabic Language for Saudi Arabia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Sudan">
            <summary>
            Arabic Language for Sudan Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Somalia">
            <summary>
            Arabic Language for Somalia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Syria">
            <summary>
            Arabic Language for Syria Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Chad">
            <summary>
            Arabic Language for Chad Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Tunisia">
            <summary>
            Arabic Language for Tunisia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Arabic_Yemen">
            <summary>
            Arabic Language for Yemen Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Bengali_Bangladesh">
            <summary>
            Bengali Language for Bangladesh Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Bengali_India">
            <summary>
            Bengali Language for India Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Bengali_Singapore">
            <summary>
            Bengali Language for Singapore Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Chamorro_Guam">
            <summary>
            Chamorro Language for Guam Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Chamorro_NorthernMarianaIslands">
            <summary>
            Chamorro Language for Northern Mariana Islands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Welsh_UnitedKingdom">
            <summary>
            Welsh Language for United Kingdom Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Danish_Denmark">
            <summary>
            Danish Language for Denmark Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Danish_Greenland">
            <summary>
            Danish Language for Greenland Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.German_Austria">
            <summary>
            German Language for Austria Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.German_Belgium">
            <summary>
            German Language for Belgium Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.German_Switzerland">
            <summary>
            German Language for Switzerland Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.German_Germany">
            <summary>
            German Language for Germany Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.German_Denmark">
            <summary>
            German Language for Denmark Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.German_Liechtensein">
            <summary>
            German Language for Liechtensein Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.German_Luxembourg">
            <summary>
            German Language for Luxembourg Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Greek_Cyprus">
            <summary>
            Greek Language for Cyprus Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Greek_Greece">
            <summary>
            Greek Language for Greece Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_AntiguaAndBarbuda">
            <summary>
            English Language for Antigua and Barbuda Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Anguilla">
            <summary>
            English Language for Anguilla Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_AmericanSamoa">
            <summary>
            English Language for American Samoa Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Australia">
            <summary>
            English Language for Australia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Barbados">
            <summary>
            English Language for Barbados Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Bermuda">
            <summary>
            English Language for Bermuda Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Brunei">
            <summary>
            English Language for Brunei Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Bahamas">
            <summary>
            English Language for Bahamas Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Botswana">
            <summary>
            English Language for Botswana Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Belize">
            <summary>
            English Language for Belize Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Canada">
            <summary>
            English Language for Canada Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_CookIslands">
            <summary>
            English Language for Cook Islands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Cameroon">
            <summary>
            English Language for Cameroon Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Dominica">
            <summary>
            English Language for Dominica Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Eritrea">
            <summary>
            English Language for Eritrea Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Ethiopia">
            <summary>
            English Language for Ethiopia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Fiji">
            <summary>
            English Language for Fiji Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_FalklandIslands">
            <summary>
            English Language for Falkland Islands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Micronesia">
            <summary>
            English Language for Micronesia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_UnitedKingdom">
            <summary>
            English Language for United Kingdom Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Grenada">
            <summary>
            English Language for Grenada Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Ghana">
            <summary>
            English Language for Ghana Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Gibraltar">
            <summary>
            English Language for Gibraltar Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Gambia">
            <summary>
            English Language for Gambia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Guam">
            <summary>
            English Language for Guam Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Guyana">
            <summary>
            English Language for Guyana Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Ireland">
            <summary>
            English Language for Ireland Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Israel">
            <summary>
            English Language for Israel Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_BritishIndianOceanTerrit">
            <summary>
            English Language for British Indian Ocean Territ Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Jamaica">
            <summary>
            English Language for Jamaica Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Kenya">
            <summary>
            English Language for Kenya Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Kiribati">
            <summary>
            English Language for Kiribati Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_StKittsNevis">
            <summary>
            English Language for St. Kitts-Nevis Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_CaymanIslands">
            <summary>
            English Language for Cayman Islands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_StLucia">
            <summary>
            English Language for St. Lucia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Liberia">
            <summary>
            English Language for Liberia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Lesotho">
            <summary>
            English Language for Lesotho Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_NorthernMarianaIslands">
            <summary>
            English Language for Northern Mariana Islands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Montserrat">
            <summary>
            English Language for Montserrat Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Malta">
            <summary>
            English Language for Malta Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Mauritius">
            <summary>
            English Language for Mauritius Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Malawi">
            <summary>
            English Language for Malawi Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Namibia">
            <summary>
            English Language for Namibia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_NorfolkIsland">
            <summary>
            English Language for Norfolk Island Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Nigeria">
            <summary>
            English Language for Nigeria Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Nauru">
            <summary>
            English Language for Nauru Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Niue">
            <summary>
            English Language for Niue Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_NewZealand">
            <summary>
            English Language for New Zealand Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_PapuaNewGuinea">
            <summary>
            English Language for Papua New Guinea Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Philippines">
            <summary>
            English Language for Philippines Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Pakistan">
            <summary>
            English Language for Pakistan Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Pitcairn">
            <summary>
            English Language for Pitcairn Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_PuertoRico">
            <summary>
            English Language for Puerto Rico Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Palau">
            <summary>
            English Language for Palau Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Rwanda">
            <summary>
            English Language for Rwanda Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_SolomonIslands">
            <summary>
            English Language for Solomon Islands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Seychelles">
            <summary>
            English Language for Seychelles Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Singapore">
            <summary>
            English Language for Singapore Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_StHelena">
            <summary>
            English Language for St. Helena Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_SierraLeone">
            <summary>
            English Language for Sierra Leone Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Somalia">
            <summary>
            English Language for Somalia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Swaziland">
            <summary>
            English Language for Swaziland Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_TurksAndCaicosIslands">
            <summary>
            English Language for Turks and Caicos Islands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Tokelau">
            <summary>
            English Language for Tokelau Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Tonga">
            <summary>
            English Language for Tonga Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_TrinidadAndTobago">
            <summary>
            English Language for Trinidad and Tobago Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Uganda">
            <summary>
            English Language for Uganda Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_USA">
            <summary>
            English Language for United States of America Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_StVincentAndTheGrenadi">
            <summary>
            English Language for St. Vincent and the Grenadi Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_BritishVirginIslands">
            <summary>
            English Language for British Virgin Islands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_USVirginIslands">
            <summary>
            English Language for U.S. Virgin Islands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Vanuatu">
            <summary>
            English Language for Vanuatu Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_WesternSamoa">
            <summary>
            English Language for Western Samoa Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_SouthAfrica">
            <summary>
            English Language for South Africa Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Zambia">
            <summary>
            English Language for Zambia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.English_Zimbabwe">
            <summary>
            English Language for Zimbabwe Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Argentina">
            <summary>
            Spanish Language for Argentina Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Bolivia">
            <summary>
            Spanish Language for Bolivia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Chile">
            <summary>
            Spanish Language for Chile Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Colombia">
            <summary>
            Spanish Language for Colombia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_CostaRica">
            <summary>
            Spanish Language for Costa Rica Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Cuba">
            <summary>
            Spanish Language for Cuba Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_DominicanRepublic">
            <summary>
            Spanish Language for Dominican Republic Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Ecuador">
            <summary>
            Spanish Language for Ecuador Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Spain">
            <summary>
            Spanish Language for Spain Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_EquatorialGuinea">
            <summary>
            Spanish Language for Equatorial Guinea Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Guatemala">
            <summary>
            Spanish Language for Guatemala Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Honduras">
            <summary>
            Spanish Language for Honduras Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Mexico">
            <summary>
            Spanish Language for Mexico Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Nicaragua">
            <summary>
            Spanish Language for Nicaragua Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Panama">
            <summary>
            Spanish Language for Panama Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Peru">
            <summary>
            Spanish Language for Peru Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_PuertoRico">
            <summary>
            Spanish Language for Puerto Rico Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Paraguay">
            <summary>
            Spanish Language for Paraguay Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_ElSalvador">
            <summary>
            Spanish Language for El Salvador Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_USA">
            <summary>
            Spanish Language for United States of America Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Uruguay">
            <summary>
            Spanish Language for Uruguay Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Spanish_Venezuela">
            <summary>
            Spanish Language for Venezuela Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Andorra">
            <summary>
            French Language for Andorra Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Belgium">
            <summary>
            French Language for Belgium Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_BurkinaFaso">
            <summary>
            French Language for Burkina Faso Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Burundi">
            <summary>
            French Language for Burundi Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Benin">
            <summary>
            French Language for Benin Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Canada">
            <summary>
            French Language for Canada Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_DemocraticRepublicOfCongo">
            <summary>
            French Language for Democratic Republic of Congo Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_CentralAfricanRepublic">
            <summary>
            French Language for Central African Republic Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Congo">
            <summary>
            French Language for Congo Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Switzerland">
            <summary>
            French Language for Switzerland Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_CoteDIvoire">
            <summary>
            French Language for Cote d'Ivoire Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Cameroon">
            <summary>
            French Language for Cameroon Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Djibouti">
            <summary>
            French Language for Djibouti Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_France">
            <summary>
            French Language for France Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Gabon">
            <summary>
            French Language for Gabon Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_UnitedKingdom">
            <summary>
            French Language for United Kingdom Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_FrenchGuiana">
            <summary>
            French Language for French Guiana Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Guinea">
            <summary>
            French Language for Guinea Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Guadeloupe">
            <summary>
            French Language for Guadeloupe Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Haiti">
            <summary>
            French Language for Haiti Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Italy">
            <summary>
            French Language for Italy Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_ComorosIslands">
            <summary>
            French Language for Comoros Islands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Lebanon">
            <summary>
            French Language for Switzerland Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Luxembourg">
            <summary>
            French Language for Luxembourg Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Monaco">
            <summary>
            French Language for Monaco Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Madagascar">
            <summary>
            French Language for Madagascar Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Mali">
            <summary>
            French Language for Mali Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Martinique">
            <summary>
            French Language for Martinique Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_NewCaledonia">
            <summary>
            French Language for New Caledonia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_FrenchPolynesia">
            <summary>
            French Language for French Polynesia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_StPierreAndMiquelon">
            <summary>
            French Language for St. Pierre and Miquelon Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Reunion">
            <summary>
            French Language for Reunion Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Rwanda">
            <summary>
            French Language for Rwanda Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Seychelles">
            <summary>
            French Language for Seychelles Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Chad">
            <summary>
            French Language for Chad Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Togo">
            <summary>
            French Language for Togo Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Vanuatu">
            <summary>
            French Language for Vanuatu Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_WallisAndFutuna">
            <summary>
            French Language for Wallis and Futuna Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.French_Mayotte">
            <summary>
            French Language for Mayotte Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Irish_UnitedKingdom">
            <summary>
            Irish Language for United Kingdom Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Irish_Ireland">
            <summary>
            Irish Language for Ireland Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Gaelic_UnitedKingdom">
            <summary>
            Gaelic Language for United Kingdom Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Manx_UnitedKingdom">
            <summary>
            Manx Language for United Kingdom Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Croatian_BosniaHerzegovina">
            <summary>
            Croatian Language for Bosnia-Herzegovina Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Croatian_Croatia">
            <summary>
            Croatian Language for Croatia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Hungarian_Hungary">
            <summary>
            Hungarian Language for Hungary Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Hungarian_Slovenia">
            <summary>
            Hungarian Language for Slovenia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Italian_Switzerland">
            <summary>
            Italian Language for Switzerland Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Italian_Croatia">
            <summary>
            Italian Language for Croatia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Italian_Italy">
            <summary>
            Italian Language for Italy Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Italian_Slovenia">
            <summary>
            Italian Language for Slovenia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Italian_SanMarino">
            <summary>
            Italian Language for San Marino Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Korean_KoreaNorth">
            <summary>
            Korean Language for Korea, North Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Korean_KoreaSouth">
            <summary>
            Korean Language for Korea, South Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Cornish_UnitedKingdom">
            <summary>
            Cornish Language for United Kingdom Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Lingala_DemocraticRepublicOfCongo">
            <summary>
            Lingala Language for Democratic Republic of Congo Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Lingala_Congo">
            <summary>
            Lingala Language for Congo Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Malay_Brunei">
            <summary>
            Malay Language for Brunei Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Malay_Malaysia">
            <summary>
            Malay Language for Malaysia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Malay_Singapore">
            <summary>
            Malay Language for Singapore Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Dutch_NetherlandsAntilles">
            <summary>
            Dutch Language for Netherlands Antilles Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Dutch_Aruba">
            <summary>
            Dutch Language for Aruba Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Dutch_Belgium">
            <summary>
            Dutch Language for Belgium Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Dutch_Netherlands">
            <summary>
            Dutch Language for Netherlands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Dutch_Suriname">
            <summary>
            Dutch Language for Suriname Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Portuguese_Angola">
            <summary>
            Portuguese Language for Angola Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Portuguese_Brazil">
            <summary>
            Portuguese Language for Brazil Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Portuguese_CapeVerdeIslands">
            <summary>
            Portuguese Language for Cape Verde Islands Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Portuguese_GuineaBissau">
            <summary>
            Portuguese Language for Guinea-Bissau Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Portuguese_Mozambique">
            <summary>
            Portuguese Language for Mozambique Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Portuguese_Portugal">
            <summary>
            Portuguese Language for Portugal Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Portuguese_SaoTomeEPrincipe">
            <summary>
            Portuguese Language for São Tomé e Principe Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Sindhi_India">
            <summary>
            Sindhi Language for India Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Sindhi_Pakistan">
            <summary>
            Sindhi Language for Pakistan Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Serbian_BosniaHerzegovina">
            <summary>
            Serbian Language for Bosnia-Herzegovina Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Serbian_Yugoslavia">
            <summary>
            Serbian Language for Yugoslavia Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Swati_Swaziland">
            <summary>
            Swati Language for Swaziland Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Swati_SouthAfrica">
            <summary>
            Swati Language for South Africa Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Swedish_Finland">
            <summary>
            Swedish Language for Finland Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Swedish_Sweden">
            <summary>
            Swedish Language for Sweden Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Swahili_Kenya">
            <summary>
            Swahili Language for Kenya Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Swahili_Tanzania">
            <summary>
            Swahili Language for Tanzania Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Tamil_India">
            <summary>
            Tamil Language for India Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Tamil_Singapore">
            <summary>
            Tamil Language for Singapore Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Tswana_Botswana">
            <summary>
            Tswana Language for Botswana Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Tswana_SouthAfrica">
            <summary>
            Tswana Language for South Africa Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Turkish_Bulgaria">
            <summary>
            Turkish Language for South Africa Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Turkish_Cyprus">
            <summary>
            Turkish Language for Cyprus Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Turkish_Turkey">
            <summary>
            Turkish Language for Turkey Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Urdu_India">
            <summary>
            Urdu Language for India Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.Urdu_Pakistan">
            <summary>
            Urdu Language for Pakistan Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.MandarinChinese_China">
            <summary>
            Mandarin Chinese Language for China Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.MandarinChinese_Singapore">
            <summary>
            Mandarin Chinese Language for Singapore Region
            </summary>
        </member>
        <member name="F:IronPdf.NaturalLanguages.MandarinChinese_Taiwan">
            <summary>
            Mandarin Chinese Language for Taiwan Region
            </summary>
        </member>
        <member name="T:IronPdf.PdfClientAccessor">
            <summary>
            Accesses PDF client implementations
            </summary>
        </member>
        <member name="M:IronPdf.PdfClientAccessor.#ctor">
            <summary>
            Create a new PDF client accessor
            </summary>
        </member>
        <member name="T:IronPdf.PdfAttachment">
            <summary>
            Pdf document file attachment
            </summary>
        </member>
        <member name="P:IronPdf.PdfAttachment.Data">
            <summary>
            Raw attachment data
            </summary>
        </member>
        <member name="P:IronPdf.PdfAttachment.Index">
            <summary>
            Attachment index within the pdf document
            </summary>
        </member>
        <member name="P:IronPdf.PdfAttachment.Name">
            <summary>
            Attachment name as specified within the pdf document
            </summary>
        </member>
        <member name="T:IronPdf.PdfAttachmentCollection">
            <summary>
            Collection of attachments contained within a pdf document
            </summary>
        </member>
        <member name="P:IronPdf.PdfAttachmentCollection.Item(System.Int32)">
            <summary>
            Retrieve the attachment located at the specified index within this collection
            </summary>
            <param name="index">Index</param>
            <returns>Pdf attachment</returns>
        </member>
        <member name="M:IronPdf.PdfAttachmentCollection.GetEnumerator">
            <summary>
            Retrieve enumerator
            </summary>
            <returns>Enumerator</returns>
        </member>
        <member name="M:IronPdf.PdfAttachmentCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Retrieve enumerator
            </summary>
            <returns>Enumerator</returns>
        </member>
        <member name="M:IronPdf.PdfAttachmentCollection.AddAttachment(System.String,System.Byte[])">
            <summary>
            Add an attachment to the pdf document using the specified name and data
            </summary>
            <param name="name">Attachment name</param>
            <param name="data">Attachment data</param>
            <returns>Pdf attachment</returns>
        </member>
        <member name="M:IronPdf.PdfAttachmentCollection.RemoveAttachment(IronPdf.PdfAttachment)">
            <summary>
            Remove the specified attachment from the pdf document
            </summary>
            <param name="attachment">Attachment to remove</param>
        </member>
        <member name="T:IronPdf.HtmlFormatOptions">
            <summary>
            This class contains properties that define the formatting options for converting PDF to HTML.
            </summary>
        </member>
        <member name="P:IronPdf.HtmlFormatOptions.BackgroundColor">
            <summary>
            The background color of the HTML body. Default is white.
            </summary>
        </member>
        <member name="P:IronPdf.HtmlFormatOptions.PdfPageMargin">
            <summary>
            The margin around each PDF page in the HTML document, specified in pixels. Default is 10.
            </summary>
        </member>
        <member name="P:IronPdf.HtmlFormatOptions.H1Color">
            <summary>
            The color of the title (h1) in the HTML document. Default is black.
            </summary>
        </member>
        <member name="P:IronPdf.HtmlFormatOptions.H1FontSize">
            <summary>
            The font size of the title (h1) in the HTML document, specified in pixels. Default is 20.
            </summary>
        </member>
        <member name="P:IronPdf.HtmlFormatOptions.H1TextAlignment">
            <summary>
            The text alignment of the title (h1) in the HTML document. Default is TextAlignment.Center.
            </summary>
        </member>
        <member name="T:IronPdf.TextAlignment">
            <summary>
            Represents the text alignment options for the title (h1) in the HTML document.
            </summary>
        </member>
        <member name="F:IronPdf.TextAlignment.Left">
            <summary>
            Aligns the text to the left.
            </summary>
        </member>
        <member name="F:IronPdf.TextAlignment.Center">
            <summary>
            Centers the text.
            </summary>
        </member>
        <member name="F:IronPdf.TextAlignment.Right">
            <summary>
            Aligns the text to the right.
            </summary>
        </member>
        <member name="T:IronPdf.PdfAVersions">
            <summary>
            Document PDF/A Version
            </summary>
        </member>
        <member name="F:IronPdf.PdfAVersions.PdfA1b">
            <summary>
            PDF/A-1B Version
            </summary>
        </member>
        <member name="F:IronPdf.PdfAVersions.PdfA2b">
            <summary>
            PDF/A-2B Version
            </summary>
        </member>
        <member name="F:IronPdf.PdfAVersions.PdfA3b">
            <summary>
            PDF/A-3B Version
            </summary>
        </member>
        <member name="F:IronPdf.PdfAVersions.PdfA1a">
            <summary>
            PDF/A-1A Version
            </summary>
        </member>
        <member name="F:IronPdf.PdfAVersions.PdfA2a">
            <summary>
            PDF/A-3B Version
            </summary>
        </member>
        <member name="F:IronPdf.PdfAVersions.PdfA3a">
            <summary>
            PDF/A-3A Version
            </summary>
        </member>
        <member name="T:IronPdf.EmbedFileType">
            <summary>
            Type of supported embedding file to PDF/A document
            </summary>
        </member>
        <member name="F:IronPdf.EmbedFileType.xml">
            <summary>
            XML file of ZUGFeRD invoice
            </summary>
        </member>
        <member name="F:IronPdf.EmbedFileType.pdf">
            <summary>
            PDF file
            </summary>
        </member>
        <member name="F:IronPdf.EmbedFileType.png">
            <summary>
            PNG file
            </summary>
        </member>
        <member name="T:IronPdf.EmbedFileByte">
            <summary>
            Struct for storing byte[] of embedding file and its type
            </summary>
            <param name="FileBytes">byte[] of embedding file</param>
            <param name="FileType">type of embedding file</param>
            <param name="FileName">name of embedding file</param>
            <remarks>currently only support 3 types of file; .xml, .pdf, and .png</remarks>
        </member>
        <member name="M:IronPdf.EmbedFileByte.#ctor(System.Byte[],IronPdf.EmbedFileType,System.String)">
            <summary>
            Struct for storing byte[] of embedding file and its type
            </summary>
            <param name="FileBytes">byte[] of embedding file</param>
            <param name="FileType">type of embedding file</param>
            <param name="FileName">name of embedding file</param>
            <remarks>currently only support 3 types of file; .xml, .pdf, and .png</remarks>
        </member>
        <member name="P:IronPdf.EmbedFileByte.FileBytes">
            <summary>byte[] of embedding file</summary>
        </member>
        <member name="P:IronPdf.EmbedFileByte.FileType">
            <summary>type of embedding file</summary>
        </member>
        <member name="P:IronPdf.EmbedFileByte.FileName">
            <summary>name of embedding file</summary>
        </member>
        <member name="T:IronPdf.EmbedFileStream">
            <summary>
            Struct for storing Stream of embedding file and its type
            </summary>
            <param name="FileStream">Stream of embedding file</param>
            <param name="FileType">type of embedding file</param>
            <param name="FileName">name of embedding file</param>
            <remarks>currently only support 3 types of file; .xml, .pdf, and .png</remarks>
        </member>
        <member name="M:IronPdf.EmbedFileStream.#ctor(System.IO.Stream,IronPdf.EmbedFileType,System.String)">
            <summary>
            Struct for storing Stream of embedding file and its type
            </summary>
            <param name="FileStream">Stream of embedding file</param>
            <param name="FileType">type of embedding file</param>
            <param name="FileName">name of embedding file</param>
            <remarks>currently only support 3 types of file; .xml, .pdf, and .png</remarks>
        </member>
        <member name="P:IronPdf.EmbedFileStream.FileStream">
            <summary>Stream of embedding file</summary>
        </member>
        <member name="P:IronPdf.EmbedFileStream.FileType">
            <summary>type of embedding file</summary>
        </member>
        <member name="P:IronPdf.EmbedFileStream.FileName">
            <summary>name of embedding file</summary>
        </member>
        <member name="T:IronPdf.PdfUAVersions">
            <summary>
            Document PDF/A Version
            </summary>
        </member>
        <member name="F:IronPdf.PdfUAVersions.PdfUA1">
            <summary>
            PDF/UA-1 Version
            </summary>
        </member>
        <member name="T:IronPdf.TextExtractionOrder">
            <summary>
            Strategy for determining word order when extracting text
            </summary>
        </member>
        <member name="F:IronPdf.TextExtractionOrder.VisualOrder">
            <summary>
            Text is extracted in order of appearance on the document, from top left to bottom right
            </summary>
            <remarks>Useful for documents which have been heavily modified after original creation</remarks>
        </member>
        <member name="F:IronPdf.TextExtractionOrder.LogicalOrder">
            <summary>
            Text is extracted in the order it was originally drawn onto the document
            </summary>
            <remarks>Useful for documents which may have complicated layouts which should be retained (e.g. columns)</remarks>
        </member>
        <member name="T:IronPdf.PdfDocumentExtensions">
            <summary>
            Extension methods for PdfDocument class
            </summary>
        </member>
        <member name="M:IronPdf.PdfDocumentExtensions.ToDocument(IronSoftware.IDocumentId,System.String,System.String)">
            <summary>
            Convert a PdfDocument interface to a full PdfDocument
            </summary>
            <param name="id">DocumentId to convert</param>
            <param name="Password">Pdf document password</param>
            <param name="OwnerPassword">Pdf document owner password</param>
            <returns>Full PdfDocument</returns>
            <exception cref="T:System.InvalidCastException">Failed to convert</exception>
        </member>
        <member name="T:IronPdf.PrintDoc.PdfPrintDocument">
            <summary>
            PDF Class enabling developers granular control over sending printable documents to a physical printer.
            <para>An assembly reference to System.Drawing is required in your project.</para><para>Note: '<see cref="T:IronPdf.PrintDoc.PdfPrintDocument" />' is only supported on: 'Windows'.</para></summary>
        </member>
        <member name="P:IronPdf.PrintDoc.PdfPrintDocument.FlattenDocument">
            <summary>
            Flatten document before rendering - useful for displaying form field values of a PDF
            </summary>
        </member>
        <member name="M:IronPdf.PrintDoc.PdfPrintDocument.#ctor(IronPdf.PdfDocument,IronPdf.PrintDoc.PdfPrintSettings,System.Drawing.Printing.PrinterSettings,System.Drawing.Printing.PrintController)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Drawing.Printing.PrintDocument" /> class.
            </summary>
            <param name="pdfDocument">An <see cref="T:IronPdf.PdfDocument" /> of document.</param>
            <param name="settings">A <see cref="T:IronPdf.PrintDoc.PdfPrintSettings" /> that configures the print document.</param>
            <param name="printerSettings">A <see cref="T:System.Drawing.Printing.PrinterSettings" /> that specifies where and how the document is printed.</param>
            <param name="printController">The <see cref="T:System.Drawing.Printing.PrintController" /> that guides the printing process.</param>
        </member>
        <member name="E:IronPdf.PrintDoc.PdfPrintDocument.BeforePrintPage">
            <summary>
            Represents the method that will handle the <see cref="E:System.Drawing.Printing.PrintDocument.PrintPage" /><para>event of a <see cref="T:System.Drawing.Printing.PrintDocument" />.</para></summary>
        </member>
        <member name="E:IronPdf.PrintDoc.PdfPrintDocument.BeforeQueryPageSettings">
            <summary>
            Represents the method that handles the <see cref="E:System.Drawing.Printing.PrintDocument.QueryPageSettings" /><para>event of a <see cref="T:System.Drawing.Printing.PrintDocument" />.</para></summary>
        </member>
        <member name="M:IronPdf.PrintDoc.PdfPrintDocument.OnBeforePrintPage(System.Drawing.Printing.PrintPageEventArgs)">
            <summary>
            Represents the method that will handle the <see cref="E:System.Drawing.Printing.PrintDocument.PrintPage" /><para>event of a <see cref="T:System.Drawing.Printing.PrintDocument" />.</para></summary>
            <param name="e">
                <see cref="T:System.Drawing.Printing.PrintPageEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:IronPdf.PrintDoc.PdfPrintDocument.OnBeforeQueryPageSettings(System.Drawing.Printing.QueryPageSettingsEventArgs)">
            <summary>
            Represents the method that handles the <see cref="E:System.Drawing.Printing.PrintDocument.QueryPageSettings" /><para>event of a <see cref="T:System.Drawing.Printing.PrintDocument" />.</para></summary>
            <param name="e">A <see cref="T:System.Drawing.Printing.QueryPageSettingsEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:IronPdf.PrintDoc.PdfPrintDocument.OnBeginPrint(System.Drawing.Printing.PrintEventArgs)">
            <summary>
            Raises the <see cref="E:System.Drawing.Printing.PrintDocument.BeginPrint" /> event. It is called
            <para>after the <see cref="M:System.Drawing.Printing.PrintDocument.Print" /> method is called and before</para><para>the first page of the document prints.</para></summary>
            <param name="e">A <see cref="T:System.Drawing.Printing.PrintEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:IronPdf.PrintDoc.PdfPrintDocument.OnPrintPage(System.Drawing.Printing.PrintPageEventArgs)">
            <summary>
            Raises the <see cref="E:System.Drawing.Printing.PrintDocument.PrintPage" /> event. It is called
            <para>before a page prints.</para></summary>
            <param name="e">A <see cref="T:System.Drawing.Printing.PrintPageEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:IronPdf.PrintDoc.PdfPrintDocument.OnQueryPageSettings(System.Drawing.Printing.QueryPageSettingsEventArgs)">
            <summary>
            Raises the <see cref="E:System.Drawing.Printing.PrintDocument.QueryPageSettings" /> event. It
            <para>is called immediately before each <see cref="E:System.Drawing.Printing.PrintDocument.PrintPage" /></para><para>event.</para></summary>
            <param name="e">A <see cref="T:System.Drawing.Printing.QueryPageSettingsEventArgs" /> that contains the event data.</param>
        </member>
        <member name="T:IronPdf.PrintDoc.PdfPrintMultiplePages">
            <summary>
            Configuration for printing multiple PDF pages on a single page.
            </summary>
        </member>
        <member name="M:IronPdf.PrintDoc.PdfPrintMultiplePages.#ctor(System.Int32,System.Int32,IronPdf.Rendering.PdfPaperOrientation,System.Single)">
            <summary>
            Creates a new instance of the PdfPrintMultiplePages class.
            </summary>
            <param name="column">The number of pages to print horizontally.</param>
            <param name="row">The number of pages to print vertically.</param>
            <param name="orientation">
            The orientation in which PDF pages are layed out on the physical page.
            </param>
            <param name="margin">The margin between PDF pages in device units.</param>
        </member>
        <member name="P:IronPdf.PrintDoc.PdfPrintMultiplePages.Column">
            <summary>
            Gets the number of pages to print horizontally.
            </summary>
        </member>
        <member name="P:IronPdf.PrintDoc.PdfPrintMultiplePages.Margin">
            <summary>
            Gets the margin between PDF pages in device units.
            </summary>
        </member>
        <member name="P:IronPdf.PrintDoc.PdfPrintMultiplePages.Orientation">
            <summary>
            Gets the orientation in which PDF pages are layed out on the physical page.
            </summary>
        </member>
        <member name="P:IronPdf.PrintDoc.PdfPrintMultiplePages.Row">
            <summary>
            Gets the number of pages to print vertically.
            </summary>
        </member>
        <member name="T:IronPdf.PrintDoc.PdfPrintSettings">
            <summary>
            Configures the print document.
            </summary>
        </member>
        <member name="M:IronPdf.PrintDoc.PdfPrintSettings.#ctor(IronPdf.PrintDoc.PdfPrintMultiplePages)">
            <summary>
            Creates a new instance of the PdfPrintSettings class.
            </summary>
            <param name="multiplePages">
            Configuration for printing multiple PDF pages on a single page.
            </param>
        </member>
        <member name="P:IronPdf.PrintDoc.PdfPrintSettings.MultiplePages">
            <summary>
            Gets configuration for printing multiple PDF pages on a single page.
            </summary>
        </member>
        <member name="T:IronPdf.Security.PdfEditSecurity">
            <summary>
            Enumeration defining levels of PDF user access rights to edit a PDF.  Edit rights may also be limited by
            the User's PDF document client software. <para>See <see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserEdits" /></para></summary>
        </member>
        <member name="F:IronPdf.Security.PdfEditSecurity.NoEdit">
            <summary>
            The user may not edit the PDF unless they have the Owner password.
            </summary>
        </member>
        <member name="F:IronPdf.Security.PdfEditSecurity.EditPages">
            <summary>
            The user may re-arrange pages, rotate pages and manage PDF thumbnails, but may not otherwise edit
            the PDF unless they have the Owner password.
            </summary>
        </member>
        <member name="F:IronPdf.Security.PdfEditSecurity.EditAll">
            <summary>
            The user may edit the PDF as allowed by their PDF client software.
            </summary>
        </member>
        <member name="T:IronPdf.Security.PdfPrintSecurity">
            <summary>
            Enumeration defining levels of PDF user access rights to print a PDF. <para>See <see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserPrinting" /></para></summary>
        </member>
        <member name="F:IronPdf.Security.PdfPrintSecurity.NoPrint">
            <summary>
            The user may not print the PDF unless they have the Owner password.
            </summary>
        </member>
        <member name="F:IronPdf.Security.PdfPrintSecurity.PrintLowQuality">
            <summary>
            The user may only print the PDF at low resolution unless they have the Owner password.
            </summary>
        </member>
        <member name="F:IronPdf.Security.PdfPrintSecurity.FullPrintRights">
            <summary>
            Users may print the PDF without restriction.
            </summary>
        </member>
        <member name="T:IronPdf.Security.PdfSecuritySettings">
            <summary>
            A class defining user security settings for a PDF document. <para>Allows the developer to control user
            access passwords, encryption, and also who may edit, print and copy content from the PDF document</para><para>Implemented in <see cref="P:IronPdf.PdfDocument.SecuritySettings" />.</para></summary>
        </member>
        <member name="M:IronPdf.Security.PdfSecuritySettings.MakePdfDocumentReadOnly(System.String)">
            <summary>
            Makes this PDF document read only such that: <para>Content is encrypted at 128 bit. Copy and paste of
            content is disallowed. Annotations and form editing are disabled.</para></summary>
            <param name="OwnerPassword">
            The owner password for the PDF.  A string for owner password is required to enable PDF encryption and
            all document security options.
            </param>
        </member>
        <member name="M:IronPdf.Security.PdfSecuritySettings.RemovePasswordsAndEncryption">
            <summary>
            Removes all user and owner password security for a PDF document.  Also disables content encryption.
            <para>Content is encrypted at 128 bit. Copy and paste of content is disallowed. Annotations and form
            editing are disabled.</para></summary>
        </member>
        <member name="P:IronPdf.Security.PdfSecuritySettings.AllowUserAnnotations">
            <summary>
            Gets or sets the permissions for users to annotate the PDF document with comments. <para>If
            AllowUserAnnotations is set <c>false</c>, the <see cref="P:IronPdf.Security.PdfSecuritySettings.OwnerPassword" /> must be set for the security
            measure to take effect.</para></summary>
            <value>
                <c>true</c> if users may annotate the PDF document, otherwise <c>false</c>.
            </value>
        </member>
        <member name="P:IronPdf.Security.PdfSecuritySettings.AllowUserCopyPasteContent">
            <summary>
            Gets or sets the permissions for users to extract or 'copy &amp; paste' content (text and images) from
            the PDF document. <para>If AllowUserCopyPasteContent is set <c>false</c>,  the <see cref="P:IronPdf.Security.PdfSecuritySettings.OwnerPassword" /> must also be set for the security measure to take effect.</para></summary>
            <value>
                <c>true</c> if users may 'copy and paste' content out of the PDF otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:IronPdf.Security.PdfSecuritySettings.AllowUserCopyPasteContentForAccessibility">
            <summary>
            Gets or sets the permissions for users to extract or 'copy &amp; paste' content (text and images) from the PDF document for accessibility.
            </summary>
            <value>
                <c>true</c> if users may 'copy and paste' content out of the PDF otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:IronPdf.Security.PdfSecuritySettings.AllowUserEdits">
            <summary>
            Gets or sets the permissions for users edit the PDF document.  The features to edit the document depends
            entirely on the PDF client software used by the end user. <para>If editing rights are restricted, then
            the <see cref="P:IronPdf.Security.PdfSecuritySettings.OwnerPassword" /> must be set for the security measure to take effect.</para></summary>
            <value>
            The <see cref="T:IronPdf.Security.PdfEditSecurity" /> value for user edit security.
            </value>
        </member>
        <member name="P:IronPdf.Security.PdfSecuritySettings.AllowUserFormData">
            <summary>
            Gets or sets the permissions for users to fill-in (enter data into) forms in the PDF document. <para>If
            AllowUserFormData is set <c>false</c>, the <see cref="P:IronPdf.Security.PdfSecuritySettings.OwnerPassword" /> must be set for the security
            measure to take effect.</para><para>Note. If you want to make the form readonly in Adobe Acrobat Reader
            please call <see cref="M:IronPdf.Security.PdfSecuritySettings.MakePdfDocumentReadOnly(System.String)" /> method or set <see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserEdits" /> to <see cref="F:IronPdf.Security.PdfEditSecurity.NoEdit" /> and set <see cref="P:IronPdf.Security.PdfSecuritySettings.OwnerPassword" />.</para></summary>
            <value>
                <c>true</c> if users may annotate the PDF document, otherwise <c>false</c>.  Setting AllowUserFormData
            true will also enable annotations.
            </value>
        </member>
        <member name="P:IronPdf.Security.PdfSecuritySettings.AllowUserPrinting">
            <summary>
            Gets or sets the permissions for users to print the PDF document. <para>If print rights are restricted,
            then the <see cref="P:IronPdf.Security.PdfSecuritySettings.OwnerPassword" /> must be set for the security measure to take effect.</para></summary>
            <value>
            The <see cref="T:IronPdf.Security.PdfPrintSecurity" /> value for user printing.
            </value>
        </member>
        <member name="P:IronPdf.Security.PdfSecuritySettings.OwnerPassword">
            <summary>
            Sets the owner password and enables 128Bit encryption of PDF content. An owner password is one used to
            enable and disable all other security settings. <para>OwnerPassword must be set to a non empty string
            value for <see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserCopyPasteContent" />, <see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserAnnotations" />, <see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserFormData" />,s <see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserPrinting" /> and <see cref="P:IronPdf.Security.PdfSecuritySettings.AllowUserEdits" /> to be
            restricted.</para></summary>
        </member>
        <member name="P:IronPdf.Security.PdfSecuritySettings.UserPassword">
            <summary>
            Sets the user password and enables 128Bit encryption of PDF content . <para>A user password is a
            password that each user must enter to open or print the PDF document.</para></summary>
        </member>
        <member name="T:IronPdf.Signing.PdfSignatureExtensions">
            <summary>
            A class that represents a PDF signing certificate (.PFX or .p12) format which can be used to digitally sign a
            PDF. This protecting it from alteration.
            </summary>
        </member>
        <member name="M:IronPdf.Signing.PdfSignatureExtensions.SignPdfFile(IronPdf.Signing.PdfSignature,System.String,System.Boolean,System.String,System.String)">
            <summary>
            Signs an existing PDF file.
            </summary>
            <param name="signature" />
            <param name="pdfFilePath">The PDF file path.</param>
            <param name="removeOldSignatures">if set to <c>true</c> old digital signatures are removed to ensure signing validity.</param>
            <param name="userPassword">Optional user password if the PDF document is encrypted.</param>
            <param name="ownerPassword">Optional owner password if the PDF document is encrypted.</param>
            <returns>
                <c>true</c> if successful.
            </returns>
        </member>
        <member name="T:IronPdf.TableOfContentsTypes">
            <summary>
            Table of contents layout type
            </summary>
        </member>
        <member name="F:IronPdf.TableOfContentsTypes.None">
            <summary>
            Do not create a table of contents
            </summary>
        </member>
        <member name="F:IronPdf.TableOfContentsTypes.Basic">
            <summary>
            Create a table of contents without page numbers
            </summary>
            <remarks>More performant, but does not include page numbers</remarks>
        </member>
        <member name="F:IronPdf.TableOfContentsTypes.WithPageNumbers">
            <summary>
            Create a table of contents WITH page numbers
            </summary>
        </member>
        <member name="T:IronPdf.TextHeaderFooter">
            <summary>
            Defines PDF Header and Footer display options.  
            <para><see cref="T:IronPdf.TextHeaderFooter" /> uses a logical approach to rendering Headers and Footers for the most common use cases.</para></summary>
            <seealso cref="T:IronPdf.HtmlHeaderFooter" />
        </member>
        <member name="P:IronPdf.TextHeaderFooter.CenterText">
            <summary>
            Sets the centered header text for the PDF document. <para>Merge meta-data into your header using any of
            these placeholder strings: {page} {total-pages} {url} {date} {time} {html-title} {pdf-title}</para></summary>
        </member>
        <member name="P:IronPdf.TextHeaderFooter.DrawDividerLine">
            <summary>
            Adds a horizontal line divider between the header / footer and the page content on every page of the PDF
            document.
            </summary>
        </member>
        <member name="P:IronPdf.TextHeaderFooter.DrawDividerLineColor">
            <summary>
            A Color of divider line <see cref="P:IronPdf.TextHeaderFooter.DrawDividerLine" /></summary>
        </member>
        <member name="P:IronPdf.TextHeaderFooter.Font">
            <summary>
            Font family <see cref="T:IronSoftware.Drawing.FontTypes" />. Default is <see cref="P:IronSoftware.Drawing.FontTypes.Helvetica" /></summary>
        </member>
        <member name="P:IronPdf.TextHeaderFooter.FontSize">
            <summary>
            Font size in px.
            </summary>
        </member>
        <member name="P:IronPdf.TextHeaderFooter.LeftText">
            <summary>
            Sets the left hand side header text for the PDF document. <para>Merge meta-data into your header using any
            of these placeholder strings: {page} {total-pages} {url} {date} {time} {html-title} {pdf-title}</para></summary>
        </member>
        <member name="P:IronPdf.TextHeaderFooter.RightText">
            <summary>
            Sets the right hand side header text for the PDF document. <para>Merge meta-data into your header using any
            of these placeholder strings: {page} {total-pages} {url} {date} {time} {html-title} {pdf-title}</para></summary>
        </member>
        <member name="M:IronPdf.TextHeaderFooter.Clone">
            <summary>
            Clones this instance.
            </summary>
            <returns>System.Object of type SimpleHeaderFooter</returns>
        </member>
        <member name="P:IronPdf.TextHeaderFooter.FontFamily">
            <summary>
            Font used to render the PDF header.
            </summary>
        </member>
    </members>
</doc>