{"RootPath": "C:\\Users\\<USER>\\Desktop\\Best Sender\\Best Sender", "ProjectFileName": "Best Sender.vbproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Attachment Encoded Pro\\frmAttPro.Designer.vb"}, {"SourceFile": "Attachment Encoded Pro\\frmAttPro.vb"}, {"SourceFile": "Email Sender\\frmAddToAllAccounts.Designer.vb"}, {"SourceFile": "Email Sender\\frmAddToAllAccounts.vb"}, {"SourceFile": "Email Sender\\frmEmailSender.Designer.vb"}, {"SourceFile": "Email Sender\\frmEmailSender.vb"}, {"SourceFile": "Email Sender\\frmLetterinorSP.Designer.vb"}, {"SourceFile": "Email Sender\\frmLetterinorSP.vb"}, {"SourceFile": "Email Sender\\frmSeeHTML.Designer.vb"}, {"SourceFile": "Email Sender\\frmSeeHTML.vb"}, {"SourceFile": "Email Sender\\frmTestSmtpAccounts.Designer.vb"}, {"SourceFile": "Email Sender\\frmTestSmtpAccounts.vb"}, {"SourceFile": "Email Sender\\FuncSendMail.vb"}, {"SourceFile": "Email Sender\\OthmanCode_Module.vb"}, {"SourceFile": "Email Sender\\SuspiciousWordsLoader.vb"}, {"SourceFile": "Email Sorter Domain\\frmEMailSorterDomain.Designer.vb"}, {"SourceFile": "Email Sorter Domain\\frmEMailSorterDomain.vb"}, {"SourceFile": "Email Sorter Domain\\Helper.vb"}, {"SourceFile": "Email Sorter Domain\\LvData.vb"}, {"SourceFile": "Email Sorter Domain\\Vars.vb"}, {"SourceFile": "Encoded Link\\frmEncodeLink.Designer.vb"}, {"SourceFile": "Encoded Link\\frmEncodeLink.vb"}, {"SourceFile": "Encoded Link\\frmlinktoAtt.Designer.vb"}, {"SourceFile": "Encoded Link\\frmlinktoAtt.vb"}, {"SourceFile": "frmNew.Designer.vb"}, {"SourceFile": "frmNew.vb"}, {"SourceFile": "IMAP\\frmIMAP.Designer.vb"}, {"SourceFile": "IMAP\\frmIMAP.vb"}, {"SourceFile": "IMAP\\frmDataTransfer_Wait.Designer.vb"}, {"SourceFile": "IMAP\\frmDataTransfer_Wait.vb"}, {"SourceFile": "IMAP\\frmIMAP_Wait.Designer.vb"}, {"SourceFile": "IMAP\\frmIMAP_Wait.vb"}, {"SourceFile": "IMAP\\GxIMAPAccount.vb"}, {"SourceFile": "IMAP\\king Get Mail\\IMAP_BSV.Designer.vb"}, {"SourceFile": "IMAP\\king Get Mail\\IMAP_BSV.vb"}, {"SourceFile": "Letters\\EncoderVariablesvb.vb"}, {"SourceFile": "Letters\\frmLetterEncoddvb.Designer.vb"}, {"SourceFile": "Letters\\frmLetterEncoddvb.vb"}, {"SourceFile": "Letters\\Module1.vb"}, {"SourceFile": "My Project\\Resources.Designer.vb"}, {"SourceFile": "New Activation\\frmActivate_New.designer.vb"}, {"SourceFile": "New Activation\\frmActivate_New.vb"}, {"SourceFile": "New Services\\frmRedirectSpider.Designer.vb"}, {"SourceFile": "New Services\\frmRedirectSpider.vb"}, {"SourceFile": "New Services\\frmRedirectWithPassword.Designer.vb"}, {"SourceFile": "New Services\\frmRedirectWithPassword.vb"}, {"SourceFile": "New Services\\frmHideLink.Designer.vb"}, {"SourceFile": "New Services\\frmHideLink.vb"}, {"SourceFile": "New Services\\frmMicrosoftRedirect.Designer.vb"}, {"SourceFile": "New Services\\frmMicrosoftRedirect.vb"}, {"SourceFile": "New Services\\frmMultiLinker.Designer.vb"}, {"SourceFile": "New Services\\frmMultiLinker.vb"}, {"SourceFile": "New Services\\frmPassMicrosoft.Designer.vb"}, {"SourceFile": "New Services\\frmPassMicrosoft.vb"}, {"SourceFile": "New Services\\frmReAuthenticationAtt.Designer.vb"}, {"SourceFile": "New Services\\frmReAuthenticationAtt.vb"}, {"SourceFile": "New Services\\frmRedirectPuzzle.Designer.vb"}, {"SourceFile": "New Services\\frmRedirectPuzzle.vb"}, {"SourceFile": "Office365\\frmCheckOffce356.designer.vb"}, {"SourceFile": "Office365\\frmCheckOffce356.vb"}, {"SourceFile": "Quick Luncher\\Email <PERSON>anner\\frmAddAttachment.designer.vb"}, {"SourceFile": "Quick Luncher\\Email <PERSON>anner\\frmAddAttachment.vb"}, {"SourceFile": "Quick Luncher\\Email Scanner\\Select SMTP\\frmaddAttach.Designer.vb"}, {"SourceFile": "Quick Luncher\\Email Scanner\\Select SMTP\\frmaddAttach.vb"}, {"SourceFile": "Quick Luncher\\Email Scanner\\Select SMTP\\frmAddSmtpServer.Designer.vb"}, {"SourceFile": "Quick Luncher\\Email Scanner\\Select SMTP\\frmAddSmtpServer.vb"}, {"SourceFile": "Quick Luncher\\Email Scanner\\Select SMTP\\frmSelectSMTP.Designer.vb"}, {"SourceFile": "Quick Luncher\\Email Scanner\\Select SMTP\\frmSelectSMTP.vb"}, {"SourceFile": "Quick Luncher\\Email Scanner\\EmailScanner.Designer.vb"}, {"SourceFile": "Quick Luncher\\Email Scanner\\EmailScanner.vb"}, {"SourceFile": "Quick Luncher\\Mail Sorter\\Email Sorter Pro\\frmEmailSorterPro.Designer.vb"}, {"SourceFile": "Quick Luncher\\Mail Sorter\\Email Sorter Pro\\frmEmailSorterPro.vb"}, {"SourceFile": "Quick Luncher\\Mail Sorter\\frmEmailSorter.Designer.vb"}, {"SourceFile": "Quick Luncher\\Mail Sorter\\frmEmailSorter.vb"}, {"SourceFile": "Quick Luncher\\MailCatcher\\MailCatcher.Designer.vb"}, {"SourceFile": "Quick Luncher\\MailCatcher\\MailCatcher.vb"}, {"SourceFile": "Quick Luncher\\ScrapedEmailResult.vb"}, {"SourceFile": "Quick Luncher\\SMTP Tester\\frmSmtpTester.Designer.vb"}, {"SourceFile": "Quick Luncher\\SMTP Tester\\frmSmtpTester.vb"}, {"SourceFile": "Quick Luncher\\Validation\\Office 365\\frmValidationOffice365.Designer.vb"}, {"SourceFile": "Quick Luncher\\Validation\\Office 365\\frmValidationOffice365.vb"}, {"SourceFile": "Ranking SMTP\\frmAddHostToSMTP.Designer.vb"}, {"SourceFile": "Ranking SMTP\\frmAddHostToSMTP.vb"}, {"SourceFile": "Remove.Designer.vb"}, {"SourceFile": "Remove.vb"}, {"SourceFile": "remove2.Designer.vb"}, {"SourceFile": "remove2.vb"}, {"SourceFile": "Resources\\Class QRCode\\QRCodeGenerator.vb"}, {"SourceFile": "Resources\\frmEditLetter.Designer.vb"}, {"SourceFile": "Resources\\frmEditLetter.vb"}, {"SourceFile": "Special Tools\\frmBase64.Designer.vb"}, {"SourceFile": "Special Tools\\frmBase64.vb"}, {"SourceFile": "Special Tools\\frmDakEncryption.Designer.vb"}, {"SourceFile": "Special Tools\\frmDakEncryption.vb"}, {"SourceFile": "Special Tools\\frmDragonLetter.Designer.vb"}, {"SourceFile": "Special Tools\\frmDragonLetter.vb"}, {"SourceFile": "Special Tools\\frmEncodedLinkWolf.Designer.vb"}, {"SourceFile": "Special Tools\\frmEncodedLinkWolf.vb"}, {"SourceFile": "Special Tools\\frmGhost.Designer.vb"}, {"SourceFile": "Special Tools\\frmGhost.vb"}, {"SourceFile": "Special Tools\\frmLetterMakerST.Designer.vb"}, {"SourceFile": "Special Tools\\frmLetterMakerST.vb"}, {"SourceFile": "Special Tools\\frmVenomENCLetter.Designer.vb"}, {"SourceFile": "Special Tools\\frmVenomENCLetter.vb"}, {"SourceFile": "Support\\frmSupportBSV.Designer.vb"}, {"SourceFile": "Support\\frmSupportBSV.vb"}, {"SourceFile": "System\\frmMain.Designer.vb"}, {"SourceFile": "System\\frmMain.vb"}, {"SourceFile": "Tag\\TagBestSender.Designer.vb"}, {"SourceFile": "Tag\\TagBestSender.vb"}, {"SourceFile": "Update Sender\\AutoUpdate.vb"}, {"SourceFile": "Update Sender\\frmDownloadBSV.Designer.vb"}, {"SourceFile": "Update Sender\\frmDownloadBSV.vb"}, {"SourceFile": "Utilities\\DeleteDuplicatemail.Designer.vb"}, {"SourceFile": "Utilities\\DeleteDuplicatemail.vb"}, {"SourceFile": "Modules and Classes\\IPInfo.vb"}, {"SourceFile": "New Activation\\frmActivationInfo.designer.vb"}, {"SourceFile": "New Activation\\frmActivationInfo.vb"}, {"SourceFile": "New Activation\\GlobalVariables.vb"}, {"SourceFile": "Office365\\Config.vb"}, {"SourceFile": "Office365\\Http.vb"}, {"SourceFile": "Office365\\Stats.vb"}, {"SourceFile": "Quick Luncher\\Email Verification\\EmailValidation.vb"}, {"SourceFile": "Quick Luncher\\Email Verification\\FrmEmailValidation.designer.vb"}, {"SourceFile": "Quick Luncher\\Email Verification\\FrmEmailValidation.vb"}, {"SourceFile": "Quick Luncher\\Mail Sorter\\frmMaillist.Designer.vb"}, {"SourceFile": "Quick Luncher\\Mail Sorter\\frmMaillist.vb"}, {"SourceFile": "Quick Luncher\\Mail Sorter\\DataModel.vb"}, {"SourceFile": "Quick Luncher\\Mail Sorter\\functions.vb"}, {"SourceFile": "Quick Luncher\\SMTP Tester\\frmSMTPTester_Details.Designer.vb"}, {"SourceFile": "Quick Luncher\\SMTP Tester\\frmSMTPTester_Details.vb"}, {"SourceFile": "Quick Luncher\\SMTP Tester\\SmtpTester.vb"}, {"SourceFile": "My Project\\AssemblyInfo.vb"}, {"SourceFile": "My Project\\Application.Designer.vb"}, {"SourceFile": "My Project\\Settings.Designer.vb"}, {"SourceFile": "Modules and Classes\\Resolution.vb"}, {"SourceFile": "Utilities\\frmGoogleCaptcha.Designer.vb"}, {"SourceFile": "Utilities\\frmGoogleCaptcha.vb"}, {"SourceFile": "Utilities\\frmImageResize.designer.vb"}, {"SourceFile": "Utilities\\frmImageResize.vb"}, {"SourceFile": "Utilities\\frmRedirectLink .Designer.vb"}, {"SourceFile": "Utilities\\frmRedirectLink .vb"}, {"SourceFile": "Utilities\\frmQuickAccountCheck.Designer.vb"}, {"SourceFile": "Utilities\\frmQuickAccountCheck.vb"}, {"SourceFile": "New Activation\\frmSplashScreen_New.designer.vb"}, {"SourceFile": "New Activation\\frmSplashScreen_New.vb"}, {"SourceFile": "Utilities\\frmZeroPass.Designer.vb"}, {"SourceFile": "Utilities\\frmZeroPass.vb"}, {"SourceFile": "Utilities\\frm_En_PHP.Designer.vb"}, {"SourceFile": "Utilities\\frm_En_PHP.vb"}, {"SourceFile": "VIP ULTRA\\AolVerify.Designer.vb"}, {"SourceFile": "VIP ULTRA\\AolVerify.vb"}, {"SourceFile": "VIP ULTRA\\frmVIPULTRA.Designer.vb"}, {"SourceFile": "VIP ULTRA\\frmVIPULTRA.vb"}, {"SourceFile": "VIP ULTRA\\YahooVerify.Designer.vb"}, {"SourceFile": "VIP ULTRA\\YahooVerify.vb"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.vb"}], "References": [{"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\AWSSDK.Core.*********\\lib\\net45\\AWSSDK.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\AWSSDK.S3.**********\\lib\\net45\\AWSSDK.S3.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\BitMiracle.LibTiff.NET.2.4.649\\lib\\net40\\BitMiracle.LibTiff.NET.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Portable.BouncyCastle.1.9.0\\lib\\net40\\BouncyCastle.Crypto.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\Best Sender\\bin\\Debug\\Bunifu_UI_v1.5.3.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\Best Sender\\bin\\Debug\\ChilkatDotNet47.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\Best Sender\\bin\\Debug\\CoreHtmlToImage.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.BonusSkins.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Charts.v24.2.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.CodeParser.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Data.Desktop.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Data.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.DataAccess.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.DataAccess.v24.2.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Diagram.v24.2.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Drawing.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.ExpressApp.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Images.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Office.v24.2.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Pdf.v24.2.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Persistent.Base.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Printing.v24.2.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.RichEdit.v24.2.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Sparkline.v24.2.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Utils.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Utils.v24.2.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.Xpo.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraBars.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraCharts.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraDiagram.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraEditors.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraGauges.v24.2.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraGauges.v24.2.Win.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraGrid.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraLayout.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraPrinting.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraReports.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraRichEdit.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraTreeList.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 24.2\\Components\\Bin\\Framework\\DevExpress.XtraVerticalGrid.v24.2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\DnsClient.1.7.0\\lib\\net471\\DnsClient.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Google.Protobuf.3.23.0\\lib\\net45\\Google.Protobuf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Grpc.Core.Api.2.62.0\\lib\\net462\\Grpc.Core.Api.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Grpc.Core.2.46.6\\lib\\net45\\Grpc.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Grpc.Net.Client.2.62.0\\lib\\net462\\Grpc.Net.Client.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Grpc.Net.Common.2.62.0\\lib\\netstandard2.0\\Grpc.Net.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\HtmlAgilityPack.1.12.1\\lib\\Net45\\HtmlAgilityPack.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\Best Sender\\bin\\Debug\\HTMLEditor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\HtmlRenderer.Core.1.5.0.5\\lib\\net45\\HtmlRenderer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\IronPdf.Slim.2024.12.9\\lib\\netstandard2.0\\IronPdf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\IronPdf.Extensions.ASPX.2023.10.3\\lib\\net462\\IronPdf.Extensions.ASPX.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\IronSoftware.Abstractions.2024.11.8\\lib\\netstandard2.0\\IronSoftware.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\IronSoftware.Abstractions.2024.11.8\\lib\\netstandard2.0\\IronSoftware.Abstractions.Models.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\IronSoftware.Drawing.Abstractions.2024.11.8\\lib\\netstandard2.0\\IronSoftware.Drawing.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\IronSoftware.System.Drawing.2024.12.3\\lib\\netstandard2.0\\IronSoftware.Drawing.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\IronSoftware.Common.2024.11.2\\lib\\netstandard2.0\\IronSoftware.Logger.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\IronSoftware.Common.2024.11.2\\lib\\netstandard2.0\\IronSoftware.Shared.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\MailKit.3.5.0\\lib\\net47\\MailKit.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\Best Sender\\bin\\Debug\\MessagingToolkit.QRCode.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Bcl.AsyncInterfaces.6.0.0\\lib\\net461\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Extensions.Configuration.Abstractions.6.0.0\\lib\\net461\\Microsoft.Extensions.Configuration.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Extensions.Configuration.Binder.6.0.0\\lib\\net461\\Microsoft.Extensions.Configuration.Binder.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Extensions.Configuration.6.0.0\\lib\\net461\\Microsoft.Extensions.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Extensions.Configuration.FileExtensions.6.0.0\\lib\\net461\\Microsoft.Extensions.Configuration.FileExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Extensions.Configuration.Json.6.0.0\\lib\\net461\\Microsoft.Extensions.Configuration.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Extensions.Configuration.UserSecrets.6.0.1\\lib\\net461\\Microsoft.Extensions.Configuration.UserSecrets.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Extensions.FileProviders.Abstractions.6.0.0\\lib\\net461\\Microsoft.Extensions.FileProviders.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Extensions.FileProviders.Physical.6.0.0\\lib\\net461\\Microsoft.Extensions.FileProviders.Physical.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Extensions.FileSystemGlobbing.6.0.0\\lib\\net461\\Microsoft.Extensions.FileSystemGlobbing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Extensions.Logging.Abstractions.6.0.0\\lib\\net461\\Microsoft.Extensions.Logging.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Extensions.Primitives.6.0.0\\lib\\net461\\Microsoft.Extensions.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Office.Interop.Excel.15.0.4795.1001\\lib\\net20\\Microsoft.Office.Interop.Excel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\Microsoft.Win32.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Microsoft.Win32.Registry.5.0.0\\lib\\net461\\Microsoft.Win32.Registry.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\Best Sender\\bin\\Debug\\MicrovisionActivation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\MimeKit.3.5.0\\lib\\net47\\MimeKit.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\OpenQA.Selenium.Winium.1.0.0.4\\lib\\OpenQA.Selenium.Winium.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\PDFsharp.1.32.3057.0\\lib\\net20\\PdfSharp.Charting.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\PDFsharp.1.32.3057.0\\lib\\net20\\PdfSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\QRCoder.1.4.3\\lib\\net40\\QRCoder.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Select.HtmlToPdf.23.2.0\\lib\\net40\\Select.HtmlToPdf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\SixLabors.Fonts.1.0.0\\lib\\netstandard2.0\\SixLabors.Fonts.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\SixLabors.ImageSharp.2.1.9\\lib\\net472\\SixLabors.ImageSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\SixLabors.ImageSharp.Drawing.1.0.0\\lib\\net472\\SixLabors.ImageSharp.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\Best Sender\\bin\\Debug\\SKGL.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.AppContext.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Buffers.4.5.1\\lib\\net461\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.Composition.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Configuration.ConfigurationManager.6.0.0\\lib\\net461\\System.Configuration.ConfigurationManager.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Console.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.OracleClient.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Diagnostics.DiagnosticSource.6.0.1\\lib\\net461\\System.Diagnostics.DiagnosticSource.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Diagnostics.Tracing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Drawing.Common.6.0.0\\lib\\net461\\System.Drawing.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Globalization.Calendars.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.IO.Compression.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.IO.Compression.FileSystem.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.IO.Compression.ZipFile.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.IO.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.IO.FileSystem.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.IO.FileSystem.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Linq.Expressions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Memory.4.5.5\\lib\\net461\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Net.Http.WinHttpHandler.7.0.0\\lib\\net462\\System.Net.Http.WinHttpHandler.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Net.Sockets.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Numerics.Vectors.4.5.0\\lib\\net46\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Reflection.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Reflection.TypeExtensions.4.3.0\\lib\\net462\\System.Reflection.TypeExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Runtime.CompilerServices.Unsafe.6.0.0\\lib\\net461\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Runtime.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Runtime.InteropServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Security.AccessControl.6.0.0\\lib\\net461\\System.Security.AccessControl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Security.Cryptography.Algorithms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Security.Cryptography.Encoding.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Security.Cryptography.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Security.Cryptography.X509Certificates.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Security.Permissions.6.0.0\\lib\\net461\\System.Security.Permissions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Security.Principal.Windows.5.0.0\\lib\\net461\\System.Security.Principal.Windows.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ServiceProcess.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Text.Encoding.CodePages.5.0.0\\lib\\net461\\System.Text.Encoding.CodePages.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Text.Encodings.Web.6.0.0\\lib\\net461\\System.Text.Encodings.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Text.Json.6.0.0\\lib\\net461\\System.Text.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Text.RegularExpressions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.Threading.Tasks.Extensions.4.5.4\\lib\\net461\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\System.Threading.Thread.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Transactions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\System.ValueTuple.4.5.0\\lib\\net47\\System.ValueTuple.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xaml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\UIAutomationProvider.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\Selenium.WebDriver.3.141.0\\lib\\net45\\WebDriver.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\WindowsFormsIntegration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\Best Sender\\bin\\Debug\\xNet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\Best Sender\\bin\\Debug\\YamlDotNet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\ZXing.Net.0.16.9\\lib\\net47\\zxing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Best Sender\\packages\\ZXing.Net.0.16.9\\lib\\net47\\zxing.presentation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\Best Sender\\Best Sender\\bin\\Debug\\Best Sender.exe", "OutputItemRelativePath": "Best Sender.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}