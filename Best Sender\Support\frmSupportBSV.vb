﻿Imports System.Diagnostics

Public Class frmSupportBSVBSV
    Private Sub frmSupportBSVBSV_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' إضافة معالج حدث لتغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmSupportBSVBSV_Resize

        ' وضع العناصر في المنتصف عند تحميل النموذج
        CenterControls()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmSupportBSVBSV_Resize(sender As Object, e As EventArgs)
        ' وضع العناصر في المنتصف عند تغيير حجم النموذج
        CenterControls()
    End Sub

    ''' <summary>
    ''' توسيط جميع العناصر في النموذج
    ''' </summary>
    Private Sub CenterControls()
        Try
            ' الحصول على حجم النموذج
            Dim formWidth As Integer = Me.ClientSize.Width
            Dim formHeight As Integer = Me.ClientSize.Height

            ' حساب المسافة لتوسيط PictureBox1
            Dim offsetX As Integer = (formWidth - PictureBox1.Width) \ 2 - PictureBox1.Left
            Dim offsetY As Integer = (formHeight - PictureBox1.Height) \ 2 - PictureBox1.Top

            ' توسيط PictureBox1
            PictureBox1.Left += offsetX
            PictureBox1.Top += offsetY

            ' توسيط جميع العناصر الأخرى بنفس الإزاحة
            MoveControlWithOffset(BntDiscord, offsetX, offsetY)
            MoveControlWithOffset(BntTelegram, offsetX, offsetY)
            MoveControlWithOffset(BntSignal, offsetX, offsetY)
            MoveControlWithOffset(BntWeb, offsetX, offsetY)
            MoveControlWithOffset(BntShopping, offsetX, offsetY)
            MoveControlWithOffset(BntTGChannel, offsetX, offsetY)
            MoveControlWithOffset(BntYouTube, offsetX, offsetY)
            MoveControlWithOffset(BntEmail, offsetX, offsetY)
            MoveControlWithOffset(Label1, offsetX, offsetY)

            ' توسيط SeparatorControl
            MoveControlWithOffset(SeparatorControl1, offsetX, offsetY)
            MoveControlWithOffset(SeparatorControl2, offsetX, offsetY)
            MoveControlWithOffset(SeparatorControl3, offsetX, offsetY)
            MoveControlWithOffset(SeparatorControl4, offsetX, offsetY)
            MoveControlWithOffset(SeparatorControl5, offsetX, offsetY)
            MoveControlWithOffset(SeparatorControl6, offsetX, offsetY)
            MoveControlWithOffset(SeparatorControl7, offsetX, offsetY)
            MoveControlWithOffset(SeparatorControl10, offsetX, offsetY)

            Debug.WriteLine($"Controls centered. Form size: {formWidth}x{formHeight}, Offset: {offsetX},{offsetY}")
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error centering controls: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تحريك عنصر بإزاحة محددة
    ''' </summary>
    Private Sub MoveControlWithOffset(ctrl As Control, offsetX As Integer, offsetY As Integer)
        ctrl.Left += offsetX
        ctrl.Top += offsetY
    End Sub
    Private Sub BntDiscord_Click(sender As Object, e As EventArgs) Handles BntDiscord.Click
        Process.Start("https://discord.com/users/1247383168408092794")
    End Sub
    Private Sub BntTelegram_Click(sender As Object, e As EventArgs) Handles BntTelegram.Click
        Process.Start("https://t.me/BestSenderVIP")
    End Sub
    Private Sub BntSignal_Click(sender As Object, e As EventArgs) Handles BntSignal.Click
        Process.Start("https://signal.me/#eu/N_S6zRFQZxUy7qNMGhjUkDHeWTP5swskArw9cYHyjET-gmX07CtCMq9Al3RPTpEJ")
    End Sub
    Private Sub BntTGChannel_Click(sender As Object, e As EventArgs) Handles BntTGChannel.Click
        Process.Start("https://t.me/bsvtrusted")
    End Sub
    Private Sub BntShopping_Click(sender As Object, e As EventArgs) Handles BntShopping.Click
        Process.Start("https://www.bestsendervip.com/shop")
    End Sub
    Private Sub BntWeb_Click(sender As Object, e As EventArgs) Handles BntWeb.Click
        Process.Start("https://www.bestsendervip.com/")
    End Sub
End Class