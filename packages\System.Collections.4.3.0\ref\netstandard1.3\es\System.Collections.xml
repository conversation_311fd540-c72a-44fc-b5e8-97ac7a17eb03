﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections</name>
  </assembly>
  <members>
    <member name="T:System.Collections.BitArray">
      <summary>Administra una matriz compacta de valores de bit que se representan como valores booleanos, donde true indica que el bit está activado (1) y false indica que el bit está desactivado (0).</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Boolean[])">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.BitArray" /> que contiene los valores de bit copiados de la matriz de valores booleanos especificada.</summary>
      <param name="values">Matriz de valores booleanos que se va a copiar. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Byte[])">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.BitArray" /> que contiene los valores de bit copiados de la matriz de bytes especificada.</summary>
      <param name="bytes">Matriz de bytes que contiene los valores que se van a copiar, en la que cada byte representa ocho bits consecutivos. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="bytes" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Collections.BitArray)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.BitArray" /> que contiene los valores de bit copiados de la colección <see cref="T:System.Collections.BitArray" /> especificada.</summary>
      <param name="bits">Colección <see cref="T:System.Collections.BitArray" /> que se va a copiar. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bits" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.BitArray" /> que puede contener el número especificado de valores de bit, establecidos inicialmente en false.</summary>
      <param name="length">Número de valores de bit de la nueva colección <see cref="T:System.Collections.BitArray" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.BitArray" /> que puede contener el número especificado de valores de bit, establecidos inicialmente en el valor especificado.</summary>
      <param name="length">Número de valores de bit de la nueva colección <see cref="T:System.Collections.BitArray" />. </param>
      <param name="defaultValue">Valor booleano que se asigna a cada bit. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32[])">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.BitArray" /> que contiene los valores de bit copiados de la matriz de enteros de 32 bits especificada.</summary>
      <param name="values">Matriz de enteros que contiene los valores que se van a copiar, en la que cada entero representa 32 bits consecutivos. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="values" /> is greater than <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Collections.BitArray.And(System.Collections.BitArray)">
      <summary>Realiza la operación AND bit a bit en los elementos de la colección <see cref="T:System.Collections.BitArray" /> actual frente a los elementos correspondientes de la colección <see cref="T:System.Collections.BitArray" /> especificada.</summary>
      <returns>Instancia actual que contiene el resultado de la operación AND bit a bit realizada en los elementos de la colección <see cref="T:System.Collections.BitArray" /> actual frente a los elementos correspondientes de la colección <see cref="T:System.Collections.BitArray" /> especificada.</returns>
      <param name="value">Colección <see cref="T:System.Collections.BitArray" /> con la que se realiza la operación AND bit a bit. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Get(System.Int32)">
      <summary>Obtiene el valor del bit en una posición específica de <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Valor del bit en la posición <paramref name="index" />.</returns>
      <param name="index">Índice de base cero del valor que se va a obtener. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Interfaz <see cref="T:System.Collections.IEnumerator" /> para toda la colección <see cref="T:System.Collections.BitArray" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Item(System.Int32)">
      <summary>Obtiene o establece el valor del bit que se encuentra en una posición específica de <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Valor del bit en la posición <paramref name="index" />.</returns>
      <param name="index">El índice de base cero del valor que se va a obtener o establecer. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.BitArray.Count" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Length">
      <summary>Obtiene o establece el número de elementos de <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Número de elementos incluidos en <see cref="T:System.Collections.BitArray" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is set to a value that is less than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Not">
      <summary>Invierte todos los valores de bit que se encuentran en la colección <see cref="T:System.Collections.BitArray" /> actual, de manera que los elementos establecidos en true cambien a false y los elementos establecidos en false cambien a true.</summary>
      <returns>Instancia actual con los valores de bit invertidos.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Or(System.Collections.BitArray)">
      <summary>Realiza la operación OR bit a bit en los elementos de la colección <see cref="T:System.Collections.BitArray" /> actual frente a los elementos correspondientes de la colección <see cref="T:System.Collections.BitArray" /> especificada.</summary>
      <returns>Instancia actual que contiene el resultado de la operación OR bit a bit realizada en los elementos de la colección <see cref="T:System.Collections.BitArray" /> actual frente a los elementos correspondientes de la colección <see cref="T:System.Collections.BitArray" /> especificada.</returns>
      <param name="value">Colección <see cref="T:System.Collections.BitArray" /> con la que se realiza la operación OR bit a bit. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Set(System.Int32,System.Boolean)">
      <summary>Establece el bit situado en una posición específica de <see cref="T:System.Collections.BitArray" /> en el valor especificado.</summary>
      <param name="index">El índice de base cero del bit que se va a establecer. </param>
      <param name="value">Valor booleano que se asigna al bit. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.SetAll(System.Boolean)">
      <summary>Establece todos los bits de <see cref="T:System.Collections.BitArray" /> en el valor especificado.</summary>
      <param name="value">Valor booleano que se asigna a todos los bits. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos de <see cref="T:System.Collections.BitArray" /> a un objeto <see cref="T:System.Array" />, a partir del índice <see cref="T:System.Array" /> especificado.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.BitArray" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero en la <paramref name="array" /> donde comienza la copia. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or- The number of elements in the source <see cref="T:System.Collections.BitArray" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.BitArray" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#Count">
      <summary>Obtiene el número de elementos de la colección <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Número de elementos incluidos en <see cref="T:System.Collections.BitArray" />.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a <see cref="T:System.Collections.BitArray" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>Es true si el acceso a <see cref="T:System.Collections.BitArray" /> está sincronizado (es seguro para subprocesos); de lo contrario, es false.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.BitArray" />.</returns>
    </member>
    <member name="M:System.Collections.BitArray.Xor(System.Collections.BitArray)">
      <summary>Realiza la operación exclusiva OR bit a bit en los elementos de la colección <see cref="T:System.Collections.BitArray" /> actual frente a los elementos correspondientes de la colección <see cref="T:System.Collections.BitArray" /> especificada.</summary>
      <returns>Instancia actual que contiene el resultado de la operación exclusiva OR bit a bit realizada en los elementos de la colección <see cref="T:System.Collections.BitArray" /> actual frente a los elementos correspondientes de la colección <see cref="T:System.Collections.BitArray" /> especificada. </returns>
      <param name="value">
        <see cref="T:System.Collections.BitArray" /> con la que se realiza la operación exclusiva OR bit a bit. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Collections.StructuralComparisons">
      <summary>Proporciona los objetos para realizar una comparación estructural de dos objetos de colección.</summary>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralComparer">
      <summary>Obtiene un objeto predefinido que realiza una comparación estructural de dos objetos.</summary>
      <returns>Un objeto predefinido que se usa para realizar una comparación estructural de dos objetos de colección.</returns>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralEqualityComparer">
      <summary>Obtiene un objeto predefinido que compara dos objetos para comprobar su igualdad estructural.</summary>
      <returns>Un objeto predefinido que se usa para comparar dos objetos de colección y comprobar su igualdad estructural.</returns>
    </member>
    <member name="T:System.Collections.Generic.Comparer`1">
      <summary>Proporciona una clase base para las implementaciones de la interfaz genérica <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <typeparam name="T">Tipo de objetos que se van a comparar.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Comparer`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Compare(`0,`0)">
      <summary>Cuando se reemplaza en una clase derivada, realiza una comparación de dos objetos del mismo tipo y devuelve un valor que indica si uno es menor, igual o mayor que el otro.</summary>
      <returns>Entero con signo que indica los valores relativos de <paramref name="x" /> e <paramref name="y" />, como se muestra en la tabla siguiente.Valor Significado Menor que cero <paramref name="x" /> es menor que <paramref name="y" />.Zero <paramref name="x" /> es igual que <paramref name="y" />.Mayor que cero <paramref name="x" /> es mayor que <paramref name="y" />.</returns>
      <param name="x">Primer objeto que se va a comparar.</param>
      <param name="y">Segundo objeto que se va a comparar.</param>
      <exception cref="T:System.ArgumentException">El tipo <paramref name="T" /> no implementa la interfaz genérica <see cref="T:System.IComparable`1" /> o la interfaz <see cref="T:System.IComparable" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Create(System.Comparison{`0})">
      <summary>Crea un comparador mediante la comparación especificada.</summary>
      <returns>El nuevo comparador.</returns>
      <param name="comparison">Comparación que se va a utilizar.</param>
    </member>
    <member name="P:System.Collections.Generic.Comparer`1.Default">
      <summary>Devuelve un comparador de criterios de ordenación predeterminado para el tipo especificado por el argumento genérico.</summary>
      <returns>Objeto que hereda <see cref="T:System.Collections.Generic.Comparer`1" /> y sirve como un comparador de criterios de ordenación para el tipo <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Compara dos objetos y devuelve un valor que indica si uno de ellos es menor, igual o mayor que el otro.</summary>
      <returns>Entero con signo que indica los valores relativos de <paramref name="x" /> e <paramref name="y" />, como se muestra en la tabla siguiente.Valor Significado Menor que cero<paramref name="x" /> es menor que <paramref name="y" />.Zero<paramref name="x" /> es igual que <paramref name="y" />.Mayor que cero<paramref name="x" /> es mayor que <paramref name="y" />.</returns>
      <param name="x">Primer objeto que se va a comparar.</param>
      <param name="y">Segundo objeto que se va a comparar.</param>
      <exception cref="T:System.ArgumentException">El tipo de <paramref name="x" /> o <paramref name="y" /> no se puede convertir al tipo <paramref name="T" />.O bien<paramref name="x" /> e <paramref name="y" /> no implementan la interfaz genérica <see cref="T:System.IComparable`1" />, ni la interfaz <see cref="T:System.IComparable" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2">
      <summary>Representa una colección de claves y valores.Para examinar el código fuente de .NET Framework para este tipo, consulte el Reference Source.</summary>
      <typeparam name="TKey">Tipo de las claves del diccionario.</typeparam>
      <typeparam name="TValue">Tipo de los valores del diccionario.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Dictionary`2" /> que está vacía, tiene la capacidad inicial predeterminada y utiliza el comparador de igualdad predeterminado para el tipo de clave.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Dictionary`2" /> que contiene los elementos copiados de la interfaz <see cref="T:System.Collections.Generic.IDictionary`2" /> especificada y utiliza el comparador de igualdad predeterminado para el tipo de clave.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> cuyos elementos se copian en el nuevo <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="dictionary" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contiene una o varias claves duplicadas.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Dictionary`2" /> que contiene los elementos copiados de la interfaz <see cref="T:System.Collections.Generic.IDictionary`2" /> especificada y utiliza la interfaz <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> especificada.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> cuyos elementos se copian en el nuevo <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> que se va a utilizar para comparar claves o null si se va a utilizar el <see cref="T:System.Collections.Generic.EqualityComparer`1" /> predeterminado para el tipo de la clave.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="dictionary" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contiene una o varias claves duplicadas.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Dictionary`2" /> que está vacía, tiene la capacidad inicial predeterminada y utiliza el <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> especificado.</summary>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> que se va a utilizar para comparar claves o null si se va a utilizar el <see cref="T:System.Collections.Generic.EqualityComparer`1" /> predeterminado para el tipo de la clave.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Dictionary`2" /> que está vacía, tiene la capacidad inicial especificada y utiliza el comparador de igualdad predeterminado para el tipo de clave.</summary>
      <param name="capacity">Número inicial de elementos que puede contener <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> es menor que 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Dictionary`2" /> que está vacía, tiene la capacidad inicial especificada y utiliza el <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> especificado.</summary>
      <param name="capacity">Número inicial de elementos que puede contener <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> que se va a utilizar para comparar claves o null si se va a utilizar el <see cref="T:System.Collections.Generic.EqualityComparer`1" /> predeterminado para el tipo de la clave.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> es menor que 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Add(`0,`1)">
      <summary>Agrega la clave y el valor especificados al diccionario.</summary>
      <param name="key">Clave del elemento que se va a agregar.</param>
      <param name="value">Valor del elemento que se va a agregar.El valor puede ser null para los tipos de referencia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.ArgumentException">Ya existe un elemento con la misma clave en <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Clear">
      <summary>Quita todas las claves y valores de <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Comparer">
      <summary>Obtiene la interfaz <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> que se utiliza para determinar la igualdad de claves para el diccionario. </summary>
      <returns>Implementación de la interfaz genérica <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> que se utiliza con el fin de determinar la igualdad de claves para la colección <see cref="T:System.Collections.Generic.Dictionary`2" /> actual y para proporcionar los valores hash para las claves.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsKey(`0)">
      <summary>Determina si <see cref="T:System.Collections.Generic.Dictionary`2" /> contiene la clave especificada.</summary>
      <returns>true si la colección <see cref="T:System.Collections.Generic.Dictionary`2" /> contiene un elemento con la clave especificada; en caso contrario, false.</returns>
      <param name="key">Clave que se buscará en <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsValue(`1)">
      <summary>Determina si <see cref="T:System.Collections.Generic.Dictionary`2" /> contiene un valor específico.</summary>
      <returns>Es true si <see cref="T:System.Collections.Generic.Dictionary`2" /> contiene un elemento con el valor especificado; en caso contrario, es false.</returns>
      <param name="value">Valor que se va a buscar en <see cref="T:System.Collections.Generic.Dictionary`2" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Count">
      <summary>Obtiene el número de pares clave-valor incluidos en la colección <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Número de pares clave-valor incluidos en <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> para la colección <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Item(`0)">
      <summary>Obtiene o establece el valor asociado a la clave especificada.</summary>
      <returns>Valor asociado a la clave especificada.Si no se encuentra la clave especificada, en el caso de una operación Get, se producirá una excepción <see cref="T:System.Collections.Generic.KeyNotFoundException" /> y, en el caso de una operación Set, se creará un nuevo elemento con la clave especificada.</returns>
      <param name="key">Clave del valor que se va a obtener o establecer.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">La propiedad se recupera y <paramref name="key" /> no existe en la colección.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Keys">
      <summary>Obtiene una colección que contiene las claves de <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> que contiene las claves de <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Remove(`0)">
      <summary>Quita el valor con la clave especificada de la colección <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>true si el elemento se encuentra y quita correctamente; en caso contrario, false.Este método también devuelve false si no se encuentra <paramref name="key" /> en la colección <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
      <param name="key">Clave del elemento que se va a quitar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Agrega el valor especificado a la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> con la clave especificada.</summary>
      <param name="keyValuePair">Estructura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> que representa la clave y el valor que se van a agregar a la colección <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">La clave de <paramref name="keyValuePair" /> es null.</exception>
      <exception cref="T:System.ArgumentException">Ya existe un elemento con la misma clave en <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Determina si <see cref="T:System.Collections.Generic.ICollection`1" /> contiene una clave y un valor específicos.</summary>
      <returns>true si <paramref name="keyValuePair" /> se encuentra en la matriz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.</returns>
      <param name="keyValuePair">Estructura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> que se va a buscar en <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copia los elementos de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> en una matriz de tipo <see cref="T:System.Collections.Generic.KeyValuePair`2" />, comenzando en el índice especificado de la matriz.</summary>
      <param name="array">Matriz unidimensional de tipo <see cref="T:System.Collections.Generic.KeyValuePair`2" /> que constituye el destino de los elementos <see cref="T:System.Collections.Generic.KeyValuePair`2" /> copiados desde la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />.La matriz debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">El número de elementos en el origen de <see cref="T:System.Collections.Generic.ICollection`1" /> es mayor que el espacio disponible desde <paramref name="index" /> hasta el final del destino de <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtiene un valor que indica si el diccionario es de solo lectura.</summary>
      <returns>Es true si la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura; de lo contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.Dictionary`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Quita una clave y valor del diccionario.</summary>
      <returns>true si la clave y el valor que representa <paramref name="keyValuePair" /> se encuentran y quitan correctamente; de lo contrario, false.Este método también devuelve false si no se encuentra <paramref name="keyValuePair" /> en la colección <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">Estructura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> que representa la clave y el valor que se van a quitar de la colección <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Obtiene una interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> que contiene las claves de la interfaz <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> de tipo <paramref name="TKey" /> que contiene las claves de la interfaz <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Obtiene una interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> que contiene los valores de la interfaz <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> de tipo <paramref name="TValue" /> que contiene los valores de la interfaz <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que procesa una iteración en la colección.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Obtiene una colección que contiene las claves de la colección <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</summary>
      <returns>Colección que contiene las claves de <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Obtiene una colección que contiene los valores de la colección <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</summary>
      <returns>Colección que contiene los valores de <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos <see cref="T:System.Collections.Generic.ICollection`1" /> en una matriz, comenzando en el índice especificado de la matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.Generic.ICollection`1" />.La matriz debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> es multidimensional.O bien<paramref name="array" /> no tiene índices de base cero.O bienEl número de elementos en el origen de <see cref="T:System.Collections.Generic.ICollection`1" /> es mayor que el espacio disponible desde <paramref name="index" /> hasta el final del destino de <paramref name="array" />.O bienEl tipo de origen <see cref="T:System.Collections.Generic.ICollection`1" /> no puede convertirse automáticamente al tipo del destino de <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>Es true si el acceso a <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos); de lo contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.Dictionary`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />. </returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Agrega la clave y el valor especificados al diccionario.</summary>
      <param name="key">Objeto que se va a utilizar como clave.</param>
      <param name="value">Objeto que se va a utilizar como valor.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> es de un tipo que no se puede asignar al tipo de clave <paramref name="TKey" /> de la <see cref="T:System.Collections.Generic.Dictionary`2" />.O bien<paramref name="value" /> es de un tipo que no se puede asignar a <paramref name="TValue" />, el tipo de valores en el <see cref="T:System.Collections.Generic.Dictionary`2" />.O bienYa existe un valor con la misma clave en la <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determina si <see cref="T:System.Collections.IDictionary" /> contiene un elemento con la clave especificada.</summary>
      <returns>true si la colección <see cref="T:System.Collections.IDictionary" /> contiene un elemento con la clave especificada; en caso contrario, false.</returns>
      <param name="key">Clave que se buscará en <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Devuelve una interfaz <see cref="T:System.Collections.IDictionaryEnumerator" /> para la interfaz <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.IDictionaryEnumerator" /> para la colección <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Obtiene un valor que indica si la interfaz <see cref="T:System.Collections.IDictionary" /> tiene un tamaño fijo.</summary>
      <returns>Es true si la interfaz <see cref="T:System.Collections.IDictionary" /> tiene un tamaño fijo; de lo contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.Dictionary`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.IDictionary" /> es de solo lectura.</summary>
      <returns>Es true si la interfaz <see cref="T:System.Collections.IDictionary" /> es de solo lectura; de lo contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.Dictionary`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Obtiene o establece el valor con la clave especificada.</summary>
      <returns>Valor asociado a la clave especificada, o null si <paramref name="key" /> no está en el diccionario o si <paramref name="key" /> es de un tipo no asignable al tipo de clave <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
      <param name="key">Clave del valor que se va a obtener.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.ArgumentException">Se asigna un valor, y <paramref name="key" /> es de un tipo que no se puede asignar al tipo de clave <paramref name="TKey" /> de la <see cref="T:System.Collections.Generic.Dictionary`2" />.O bienSe asigna un valor, y <paramref name="value" /> es de un tipo que no se puede asignar al tipo de valor <paramref name="TValue" /> de la <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Keys">
      <summary>Obtiene una interfaz <see cref="T:System.Collections.ICollection" /> que contiene las claves de la interfaz <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> que contiene las claves de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Quita el elemento con la clave especificada de <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Clave del elemento que se va a quitar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Values">
      <summary>Obtiene una interfaz <see cref="T:System.Collections.ICollection" /> que contiene los valores de la interfaz <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> que contiene los valores de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que procesa una iteración en la colección.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.TryGetValue(`0,`1@)">
      <summary>Obtiene el valor asociado a la clave especificada.</summary>
      <returns>true si la colección <see cref="T:System.Collections.Generic.Dictionary`2" /> contiene un elemento con la clave especificada; en caso contrario, false.</returns>
      <param name="key">Clave del valor que se va a obtener.</param>
      <param name="value">Valor asociado a la clave especificada cuando el método devuelve, si se encuentra la clave; en caso contrario, valor predeterminado para el tipo del parámetro <paramref name="value" />.Este parámetro se pasa sin inicializar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Values">
      <summary>Obtiene una colección que contiene los valores de <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Colección <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> que contiene los valores de la colección <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.Enumerator">
      <summary>Enumera los elementos de una colección <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección <see cref="T:System.Collections.Generic.Dictionary`2" /> en la posición actual del enumerador.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.Dispose">
      <summary>Libera todos los recursos utilizados por <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de la colección <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento del diccionario en la posición actual del enumerador, como una estructura <see cref="T:System.Collections.DictionaryEntry" />.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Obtiene la clave del elemento situado en la posición actual del enumerador.</summary>
      <returns>Clave del elemento del diccionario situado en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Obtiene el valor del elemento situado en la posición actual del enumerador.</summary>
      <returns>Valor del elemento del diccionario situado en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección situado en la posición actual del enumerador, como un objeto <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection">
      <summary>Representa la colección de claves de una colección <see cref="T:System.Collections.Generic.Dictionary`2" />.Esta clase no puede heredarse.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> que refleja las claves de la colección <see cref="T:System.Collections.Generic.Dictionary`2" /> especificada.</summary>
      <param name="dictionary">Colección <see cref="T:System.Collections.Generic.Dictionary`2" /> cuyas claves se reflejan en la nueva colección <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Copia los elementos de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> en una <see cref="T:System.Array" /> unidimensional existente, a partir del índice especificado de la matriz.</summary>
      <param name="array">Matriz <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero de <paramref name="array" /> en el que empieza la operación de copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.</exception>
      <exception cref="T:System.ArgumentException">El número de elementos en la colección <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final de la matriz <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Count">
      <summary>Obtiene el número de elementos incluidos en <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>Número de elementos incluidos en <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.La recuperación del valor de esta propiedad es una operación O(1).</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" /> para la colección <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Agrega un elemento a <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Objeto que se va a agregar a <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Siempre se produce.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Quita todos los elementos de <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Siempre se produce.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Determina si la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> contiene un valor específico.</summary>
      <returns>true si <paramref name="item" /> se encuentra en la matriz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.Generic.ICollection`1" /> es de sólo lectura.</summary>
      <returns>true si la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura; en caso contrario, false.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, esta propiedad siempre devuelve true.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Quita la primera aparición de un objeto específico de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true si <paramref name="item" /> se ha quitado correctamente de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.Este método también devuelve false si no se encontró <paramref name="item" /> en el objeto <see cref="T:System.Collections.Generic.ICollection`1" /> original.</returns>
      <param name="item">Objeto que se va a quitar de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Siempre se produce.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos de la interfaz <see cref="T:System.Collections.ICollection" /> en un objeto <see cref="T:System.Array" />, a partir de un índice determinado de la clase <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero de <paramref name="array" /> en el que empieza la operación de copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> es multidimensional.O bien<paramref name="array" /> no tiene índices de base cero.O bienEl número de elementos en la interfaz <see cref="T:System.Collections.ICollection" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final de la matriz <paramref name="array" /> de destino.O bienEl tipo de la interfaz <see cref="T:System.Collections.ICollection" /> de origen no se puede convertir automáticamente al tipo de la matriz <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>Es true si el acceso a <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos); de lo contrario, es false.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede utilizar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede utilizar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, esta propiedad siempre devuelve la instancia actual.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator">
      <summary>Enumera los elementos de un <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> en la posición actual del enumerador.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Libera todos los recursos usados por <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>Es true si el enumerador avanzó con éxito hasta el siguiente elemento; es false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection">
      <summary>Representa la colección de valores de una colección <see cref="T:System.Collections.Generic.Dictionary`2" />.Esta clase no puede heredarse.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> que refleja los valores de la colección <see cref="T:System.Collections.Generic.Dictionary`2" /> especificada.</summary>
      <param name="dictionary">Colección <see cref="T:System.Collections.Generic.Dictionary`2" /> cuyos valores se reflejan en la nueva colección <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Copia los elementos de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> en una <see cref="T:System.Array" /> unidimensional existente, a partir del índice especificado de la matriz.</summary>
      <param name="array">Matriz <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero de <paramref name="array" /> en el que empieza la operación de copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.</exception>
      <exception cref="T:System.ArgumentException">El número de elementos en la colección <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final de la matriz <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Count">
      <summary>Obtiene el número de elementos incluidos en <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>Número de elementos incluidos en <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" /> para la colección <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Agrega un elemento a <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Objeto que se va a agregar a <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Siempre se produce.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Quita todos los elementos de <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Siempre se produce.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Determina si la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> contiene un valor específico.</summary>
      <returns>true si <paramref name="item" /> se encuentra en la matriz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.Generic.ICollection`1" /> es de sólo lectura.</summary>
      <returns>true si la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura; en caso contrario, false.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, esta propiedad siempre devuelve true.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Quita la primera aparición de un objeto específico de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />.Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true si <paramref name="item" /> se ha quitado correctamente de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.Este método también devuelve false si no se encontró <paramref name="item" /> en el objeto <see cref="T:System.Collections.Generic.ICollection`1" /> original.</returns>
      <param name="item">Objeto que se va a quitar de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Siempre se produce.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos de la interfaz <see cref="T:System.Collections.ICollection" /> en un objeto <see cref="T:System.Array" />, a partir de un índice determinado de la clase <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero de <paramref name="array" /> en el que empieza la operación de copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> es multidimensional.O bien<paramref name="array" /> no tiene índices de base cero.O bienEl número de elementos en la interfaz <see cref="T:System.Collections.ICollection" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final de la matriz <paramref name="array" /> de destino.O bienEl tipo de la interfaz <see cref="T:System.Collections.ICollection" /> de origen no se puede convertir automáticamente al tipo de la matriz <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>Es true si el acceso a <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos); de lo contrario, es false.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede utilizar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede utilizar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, esta propiedad siempre devuelve la instancia actual.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator">
      <summary>Enumera los elementos de un objeto <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> en la posición actual del enumerador.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Libera todos los recursos utilizados por <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="T:System.Collections.Generic.EqualityComparer`1">
      <summary>Proporciona una clase base para las implementaciones de la interfaz genérica <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <typeparam name="T">Tipo de objetos que se van a comparar.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.EqualityComparer`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.EqualityComparer`1.Default">
      <summary>Devuelve un comparador de igualdad predeterminado para el tipo especificado por el argumento genérico.</summary>
      <returns>Instancia predeterminada de la clase <see cref="T:System.Collections.Generic.EqualityComparer`1" /> para el tipo <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.Equals(`0,`0)">
      <summary>Cuando se reemplaza en una clase derivada, determina si dos objetos de tipo <paramref name="T" /> son iguales.</summary>
      <returns>true si los objetos especificados son iguales; en caso contrario, false.</returns>
      <param name="x">Primer objeto que se va a comparar.</param>
      <param name="y">Segundo objeto que se va a comparar.</param>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.GetHashCode(`0)">
      <summary>Cuando se reemplaza en una clase derivada sirve como función hash para los objetos especificados para los algoritmos hash y las estructuras de datos, como las tablas hash.</summary>
      <returns>Código hash para el objeto especificado.</returns>
      <param name="obj">Objeto para el que se va a obtener un código hash.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Determina si los objetos especificados son iguales.</summary>
      <returns>true si los objetos especificados son iguales; en caso contrario, false.</returns>
      <param name="x">Primer objeto que se va a comparar.</param>
      <param name="y">Segundo objeto que se va a comparar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> or <paramref name="y" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Devuelve un código hash para el objeto especificado.</summary>
      <returns>Código hash para el objeto especificado.</returns>
      <param name="obj">
        <see cref="T:System.Object" /> para el que se va a devolver un código hash.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.-or-<paramref name="obj" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1">
      <summary>Representa un conjunto de valores.Para examinar el código fuente de .NET Framework para este tipo, consulte el Reference Source.</summary>
      <typeparam name="T">Tipo de los elementos del conjunto hash.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.HashSet`1" /> que está vacía y utiliza el comparador de igualdad predeterminado para el tipo de conjunto.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.HashSet`1" /> que utiliza el comparador de igualdad predeterminado para el tipo de conjunto, contiene elementos copiados de la colección especificada y tiene capacidad suficiente para dar cabida al número de elementos copiados.</summary>
      <param name="collection">Colección cuyos elementos se copian en el nuevo conjunto.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="collection" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.HashSet`1" /> que utiliza el comparador de igualdad especificado para el tipo de conjunto, contiene elementos copiados de la colección especificada y tiene capacidad suficiente para alojar el número de elementos copiados.</summary>
      <param name="collection">Colección cuyos elementos se copian en el nuevo conjunto.</param>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> que se va a usar para comparar valores del conjunto o null si se va a usar la implementación de <see cref="T:System.Collections.Generic.EqualityComparer`1" /> predeterminada para el tipo de conjunto.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="collection" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.HashSet`1" /> que está vacía y utiliza el comparador de igualdad especificado para el tipo de conjunto.</summary>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> que se va a usar para comparar valores del conjunto o null si se va a usar la implementación de <see cref="T:System.Collections.Generic.EqualityComparer`1" /> predeterminada para el tipo de conjunto.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Add(`0)">
      <summary>Agrega el elemento especificado a un conjunto.</summary>
      <returns>Es true si el elemento se agrega al objeto <see cref="T:System.Collections.Generic.HashSet`1" />; es false si el elemento ya está presente.</returns>
      <param name="item">Elemento que se va a agregar al conjunto.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Clear">
      <summary>Quita todos los elementos de un objeto <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Comparer">
      <summary>Obtiene el objeto <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> que se utiliza para determinar la igualdad de los valores del conjunto.</summary>
      <returns>Objeto <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> que se utiliza para determinar la igualdad de los valores del conjunto.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Contains(`0)">
      <summary>Determina si un objeto <see cref="T:System.Collections.Generic.HashSet`1" /> contiene el elemento especificado.</summary>
      <returns>Es true si el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> contiene el elemento especificado; de lo contrario, es false.</returns>
      <param name="item">Elemento que debe buscarse en el objeto <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[])">
      <summary>Copia los elementos de un objeto <see cref="T:System.Collections.Generic.HashSet`1" /> en una matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde el objeto <see cref="T:System.Collections.Generic.HashSet`1" />.La matriz debe tener una indización de base cero.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32)">
      <summary>Copia los elementos de un objeto <see cref="T:System.Collections.Generic.HashSet`1" /> en una matriz, comenzando en el índice especificado de la matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde el objeto <see cref="T:System.Collections.Generic.HashSet`1" />.La matriz debe tener una indización de base cero.</param>
      <param name="arrayIndex">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> es mayor que la longitud del destino de <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Copia el número de elementos especificado de un objeto <see cref="T:System.Collections.Generic.HashSet`1" /> en una matriz, comenzando en el índice especificado de la matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde el objeto <see cref="T:System.Collections.Generic.HashSet`1" />.La matriz debe tener una indización de base cero.</param>
      <param name="arrayIndex">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <param name="count">Número de elementos que se van a copiar en <paramref name="array" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> es menor que 0.o bien<paramref name="count" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> es mayor que la longitud del destino de <paramref name="array" />.o bien<paramref name="count" /> es mayor que el espacio disponible desde el <paramref name="index" /> al final del destino de <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Count">
      <summary>Obtiene el número de elementos contenidos en un conjunto.</summary>
      <returns>Número de elementos contenidos en el conjunto.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Quita del objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual todos los elementos de la colección especificada.</summary>
      <param name="other">Colección de elementos que se van a quitar del objeto <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="other" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración un objeto <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>Objeto <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> para el objeto <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual para que solo contenga elementos que están presentes en ese objeto y en la colección especificada.</summary>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="other" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si un objeto <see cref="T:System.Collections.Generic.HashSet`1" /> es un subconjunto apropiado de la colección especificada.</summary>
      <returns>Es true si el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> es un subconjunto apropiado de <paramref name="other" />; en caso contrario, es false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="other" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si un objeto <see cref="T:System.Collections.Generic.HashSet`1" /> es un supraconjunto apropiado de la colección especificada.</summary>
      <returns>Es true si el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> es un supraconjunto apropiado de <paramref name="other" />; en caso contrario, es false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="other" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si un objeto <see cref="T:System.Collections.Generic.HashSet`1" /> es un subconjunto de la colección especificada.</summary>
      <returns>Es true si el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> es un subconjunto de <paramref name="other" />; en caso contrario, es false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="other" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si un objeto <see cref="T:System.Collections.Generic.HashSet`1" /> es un supraconjunto de la colección especificada.</summary>
      <returns>Es true si el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> es un supraconjunto de <paramref name="other" />; en caso contrario, es false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="other" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual y una colección especificada comparten elementos comunes.</summary>
      <returns>Es true si el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> y <paramref name="other" /> comparten al menos un elemento común; de lo contrario, es false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="other" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Remove(`0)">
      <summary>Quita el elemento especificado de un objeto <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>true si el elemento se encuentra y quita correctamente; en caso contrario, false.Este método devuelve false si <paramref name="item" /> no se encuentra en el objeto <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
      <param name="item">Elemento que se va a quitar.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Quita todos los elementos que cumplen las condiciones definidas por el predicado especificado de una colección <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>Número de elementos que se quitaron de la colección <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones de los elementos que se van a quitar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="match" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si un objeto <see cref="T:System.Collections.Generic.HashSet`1" /> y la colección especificada contienen los mismos elementos.</summary>
      <returns>Es true si el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> es igual a <paramref name="other" />; de lo contrario, es false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="other" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual para que contenga únicamente los elementos que están presentes en ese objeto o en la colección especificada, pero no en ambos.</summary>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="other" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Agrega un elemento a un objeto <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="item">Objeto que se va a agregar al objeto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtiene un valor que indica si una colección es de solo lectura.</summary>
      <returns>true si la colección es de solo lectura; de lo contrario, false.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>Objeto <see cref="T:System.Collections.Generic.IEnumerator`1" /> que puede usarse para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>Objeto <see cref="T:System.Collections.IEnumerator" /> que puede usarse para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.TrimExcess">
      <summary>Establece la capacidad de un objeto <see cref="T:System.Collections.Generic.HashSet`1" /> en el número real de elementos que contiene, redondeado a un valor próximo específico de la implementación.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual para que contenga todos los elementos que están presentes en él y en la colección especificada o en ambos.</summary>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.HashSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="other" /> es null.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1.Enumerator">
      <summary>Enumera los elementos de un objeto <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección <see cref="T:System.Collections.Generic.HashSet`1" /> en la posición actual del enumerador.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.Dispose">
      <summary>Libera todos los recursos utilizados por un objeto <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de la colección <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección situado en la posición actual del enumerador, como un objeto <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1">
      <summary>Representa una lista doblemente vinculada.</summary>
      <typeparam name="T">Especifica el tipo de elemento de la lista vinculada.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.LinkedList`1" /> que está vacía.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.LinkedList`1" /> que contiene elementos copiados de la interfaz <see cref="T:System.Collections.IEnumerable" /> especificada y tiene una capacidad suficiente para alojar el número de elementos copiados. </summary>
      <param name="collection">Interfaz <see cref="T:System.Collections.IEnumerable" /> cuyos elementos se copian en la nueva colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Agrega el nuevo nodo especificado después del nodo existente que se haya concretado en la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> después del cual se va a insertar <paramref name="newNode" />.</param>
      <param name="newNode">Nuevo objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que se va a agregar a la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> es null.O bien<paramref name="newNode" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> no está en la colección <see cref="T:System.Collections.Generic.LinkedList`1" /> actual.O bien<paramref name="newNode" /> pertenece a otra colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Agrega un nuevo nodo que contiene el valor especificado después del nodo existente que se haya concretado en la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Nuevo objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que contiene <paramref name="value" />.</returns>
      <param name="node">Objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> después del que se va a insertar un nuevo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que contiene <paramref name="value" />.</param>
      <param name="value">Valor que se va a agregar a la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> no está en la colección <see cref="T:System.Collections.Generic.LinkedList`1" /> actual.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Agrega el nuevo nodo especificado antes del nodo existente que se haya concretado en la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> antes del cual se va a insertar <paramref name="newNode" />.</param>
      <param name="newNode">Nuevo objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que se va a agregar a la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> es null.O bien<paramref name="newNode" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> no está en la colección <see cref="T:System.Collections.Generic.LinkedList`1" /> actual.O bien<paramref name="newNode" /> pertenece a otra colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Agrega un nuevo nodo que contiene el valor especificado antes del nodo existente que se haya concretado en la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Nuevo objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que contiene <paramref name="value" />.</returns>
      <param name="node">Objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> antes del que se va a insertar un nuevo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que contiene <paramref name="value" />.</param>
      <param name="value">Valor que se va a agregar a la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> no está en la colección <see cref="T:System.Collections.Generic.LinkedList`1" /> actual.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Agrega el nuevo nodo especificado al principio de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Nuevo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que se va a agregar al principio de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> pertenece a otra colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(`0)">
      <summary>Agrega un nuevo nodo que contiene el valor especificado al principio de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Nuevo objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que contiene <paramref name="value" />.</returns>
      <param name="value">Valor que se va a agregar al principio de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Agrega el nuevo nodo especificado al final de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Nuevo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que se va a agregar al final de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> pertenece a otra colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(`0)">
      <summary>Agrega un nuevo nodo que contiene el valor especificado al final de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Nuevo objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que contiene <paramref name="value" />.</returns>
      <param name="value">Valor que se agrega al final de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Clear">
      <summary>Quita todos los nodos de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Contains(`0)">
      <summary>Determina si un valor se encuentra en la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true si <paramref name="value" /> se encuentra en la colección <see cref="T:System.Collections.Generic.LinkedList`1" />; en caso contrario, false.</returns>
      <param name="value">Valor que se va a buscar en <see cref="T:System.Collections.Generic.LinkedList`1" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.CopyTo(`0[],System.Int32)">
      <summary>Copia la totalidad de <see cref="T:System.Collections.Generic.LinkedList`1" /> en una matriz <see cref="T:System.Array" /> unidimensional compatible, comenzando en el índice especificado de la matriz de destino.</summary>
      <param name="array">Matriz <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.Generic.LinkedList`1" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero de <paramref name="array" /> en el que empieza la operación de copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.</exception>
      <exception cref="T:System.ArgumentException">El número de elementos en la colección <see cref="T:System.Collections.Generic.LinkedList`1" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final de la matriz <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Count">
      <summary>Obtiene el número de nodos que ya están incluidos en la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Número de nodos que ya están incluidos en la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Find(`0)">
      <summary>Encuentra el primer nodo que contiene el valor especificado.</summary>
      <returns>Primer objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que contiene el valor especificado, si se encuentra; de lo contrario, null.</returns>
      <param name="value">Valor que se va a buscar en <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.FindLast(`0)">
      <summary>Encuentra el último nodo que contiene el valor especificado.</summary>
      <returns>Último objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que contiene el valor especificado, si se encuentra; de lo contrario, null.</returns>
      <param name="value">Valor que se va a buscar en <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.First">
      <summary>Obtiene el primer nodo de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Primer <see cref="T:System.Collections.Generic.LinkedListNode`1" /> de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración el objeto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" /> para <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Last">
      <summary>Obtiene el último nodo de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Último <see cref="T:System.Collections.Generic.LinkedListNode`1" /> de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Quita el nodo especificado de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que se va a quitar de <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> no está en la colección <see cref="T:System.Collections.Generic.LinkedList`1" /> actual.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(`0)">
      <summary>Quita la primera aparición del valor especificado de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true si el elemento que contiene <paramref name="value" /> se quita correctamente; en caso contrario, false.  Este método también devuelve false si no se encontró <paramref name="value" /> en el objeto <see cref="T:System.Collections.Generic.LinkedList`1" /> original.</returns>
      <param name="value">Valor que se va a quitar de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveFirst">
      <summary>Quita el nodo del principio de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">El objeto <see cref="T:System.Collections.Generic.LinkedList`1" /> está vacío.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveLast">
      <summary>Quita el nodo del final de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">El objeto <see cref="T:System.Collections.Generic.LinkedList`1" /> está vacío.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Agrega un elemento al final de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="value">Valor que se va a agregar al final de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.Generic.ICollection`1" /> es de sólo lectura.</summary>
      <returns>true si la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura; en caso contrario, false.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.LinkedList`1" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos de la interfaz <see cref="T:System.Collections.ICollection" /> en un objeto <see cref="T:System.Array" />, a partir de un índice determinado de la clase <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero de <paramref name="array" /> en el que empieza la operación de copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> es multidimensional.O bien<paramref name="array" /> no tiene índices de base cero.O bienEl número de elementos en la interfaz <see cref="T:System.Collections.ICollection" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final de la matriz <paramref name="array" /> de destino.O bienEl tipo de la interfaz <see cref="T:System.Collections.ICollection" /> de origen no se puede convertir automáticamente al tipo de la matriz <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>Es true si el acceso a <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos); de lo contrario, es false.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.LinkedList`1" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede utilizar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede utilizar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.LinkedList`1" />, esta propiedad siempre devuelve la instancia actual.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la lista vinculada como colección.</summary>
      <returns>Interfaz <see cref="T:System.Collections.IEnumerator" /> que se puede utilizar para recorrer la lista vinculada como colección.</returns>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1.Enumerator">
      <summary>Enumera los elementos de un objeto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de <see cref="T:System.Collections.Generic.LinkedList`1" /> en la posición actual del enumerador.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.Dispose">
      <summary>Libera todos los recursos utilizados por <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).Esta clase no puede heredarse.</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedListNode`1">
      <summary>Representa un nodo en una clase <see cref="T:System.Collections.Generic.LinkedList`1" />.Esta clase no puede heredarse.</summary>
      <typeparam name="T">Especifica el tipo de elemento de la lista vinculada.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedListNode`1.#ctor(`0)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.LinkedListNode`1" /> que contiene el valor especificado.</summary>
      <param name="value">Valor que va a contener el objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.List">
      <summary>Obtiene la colección <see cref="T:System.Collections.Generic.LinkedList`1" /> a la que pertenece el objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</summary>
      <returns>Una referencia a la colección <see cref="T:System.Collections.Generic.LinkedList`1" /> a la que pertenece el objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" />, o null si el objeto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> no está vinculado.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Next">
      <summary>Obtiene el siguiente nodo de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Una referencia al nodo siguiente en <see cref="T:System.Collections.Generic.LinkedList`1" />, o null si el nodo actual es el último elemento (<see cref="P:System.Collections.Generic.LinkedList`1.Last" />) de <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Previous">
      <summary>Obtiene el anterior nodo de la colección <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Una referencia al nodo anterior en <see cref="T:System.Collections.Generic.LinkedList`1" />, o null si el nodo actual es el primer elemento (<see cref="P:System.Collections.Generic.LinkedList`1.First" />) de <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Value">
      <summary>Obtiene el valor que contiene el nodo.</summary>
      <returns>Valor que contiene el nodo.</returns>
    </member>
    <member name="T:System.Collections.Generic.List`1">
      <summary>Representa una lista de objetos fuertemente tipados a la que se puede obtener acceso por índice.Proporciona métodos para buscar, ordenar y manipular listas.Para examinar el código fuente de .NET Framework para este tipo, vea el origen de referencia.</summary>
      <typeparam name="T">Tipo de elementos en la lista.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.List`1" /> que está vacía y tiene la capacidad inicial predeterminada.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.List`1" /> que contiene elementos copiados de la colección especificada y tiene una capacidad suficiente para aceptar el número de elementos copiados.</summary>
      <param name="collection">Colección cuyos elementos se copian en la nueva lista.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.List`1" /> que está vacía y tiene la capacidad inicial especificada.</summary>
      <param name="capacity">Número de elementos que puede almacenar inicialmente la lista nueva.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> es menor que 0. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Add(`0)">
      <summary>Agrega un objeto al final de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="item">Objeto que se va a agregar al final de <see cref="T:System.Collections.Generic.List`1" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Agrega los elementos de la colección especificada al final de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="collection">Colección cuyos elementos deben agregarse al final de <see cref="T:System.Collections.Generic.List`1" />.La propia colección no puede ser null, pero puede contener elementos que sean null si el tipo <paramref name="T" /> es un tipo de referencia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.AsReadOnly">
      <summary>Devuelve un contenedor de <see cref="T:System.Collections.Generic.IList`1" /> de solo lectura para la colección actual.</summary>
      <returns>Un <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> que actúa como contenedor de solo lectura actual <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>Busca un elemento en un intervalo de elementos de la <see cref="T:System.Collections.Generic.List`1" /> ordenada utilizando el comparador especificado y devuelve el índice de base cero del elemento.</summary>
      <returns>Índice de base cero de <paramref name="item" /> en ordenado <see cref="T:System.Collections.Generic.List`1" />si <paramref name="item" /> se encuentra; en caso contrario, número negativo que es el complemento bit a bit del índice del siguiente elemento mayor que <paramref name="item" /> o, si no hay ningún elemento mayor, el complemento bit a bit de <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="index">Índice inicial de base cero del intervalo que se va a buscar.</param>
      <param name="count">Longitud del intervalo en el que se va a buscar.</param>
      <param name="item">Objeto que se va a buscar.El valor puede ser null para los tipos de referencia.</param>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IComparer`1" /> que se va a utilizar al comparar elementos, o null para utilizar el comparador predeterminado <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.o bien<paramref name="count" /> es menor que 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> y <paramref name="count" /> no denotan un intervalo válido en <see cref="T:System.Collections.Generic.List`1" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> es null y el comparador predeterminado <see cref="P:System.Collections.Generic.Comparer`1.Default" /> no puede encontrar una implementación de la interfaz genérica <see cref="T:System.IComparable`1" /> o de la interfaz <see cref="T:System.IComparable" /> para el tipo <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0)">
      <summary>Busca la <see cref="T:System.Collections.Generic.List`1" /> completa ordenada para un elemento utilizando el comparador predeterminado y devuelve el índice de base cero del elemento.</summary>
      <returns>Índice de base cero de <paramref name="item" /> en ordenado <see cref="T:System.Collections.Generic.List`1" />si <paramref name="item" /> se encuentra; en caso contrario, número negativo que es el complemento bit a bit del índice del siguiente elemento mayor que <paramref name="item" /> o, si no hay ningún elemento mayor, el complemento bit a bit de <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">Objeto que se va a buscar.El valor puede ser null para los tipos de referencia.</param>
      <exception cref="T:System.InvalidOperationException">El comparador predeterminado <see cref="P:System.Collections.Generic.Comparer`1.Default" /> no puede encontrar una implementación de la interfaz genérica <see cref="T:System.IComparable`1" /> o de la interfaz <see cref="T:System.IComparable" /> para el tipo <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>Busca la <see cref="T:System.Collections.Generic.List`1" /> completa ordenada para un elemento utilizando el comparador especificado y devuelve el índice de base cero del elemento.</summary>
      <returns>Índice de base cero de <paramref name="item" /> en ordenado <see cref="T:System.Collections.Generic.List`1" />si <paramref name="item" /> se encuentra; en caso contrario, número negativo que es el complemento bit a bit del índice del siguiente elemento mayor que <paramref name="item" /> o, si no hay ningún elemento mayor, el complemento bit a bit de <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">Objeto que se va a buscar.El valor puede ser null para los tipos de referencia.</param>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IComparer`1" /> que se va a usar al comparar elementos.o biennull para utilizar el comparador predeterminado <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> es null y el comparador predeterminado <see cref="P:System.Collections.Generic.Comparer`1.Default" /> no puede encontrar una implementación de la interfaz genérica <see cref="T:System.IComparable`1" /> o de la interfaz <see cref="T:System.IComparable" /> para el tipo <paramref name="T" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Capacity">
      <summary>Obtiene o establece el número total de elementos que puede contener la estructura de datos interna sin cambiar el tamaño.</summary>
      <returns>Número de elementos que puede contener <see cref="T:System.Collections.Generic.List`1" /> antes de que sea necesario cambiar el tamaño.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.List`1.Capacity" /> se establece en un valor que es menor que <see cref="P:System.Collections.Generic.List`1.Count" />. </exception>
      <exception cref="T:System.OutOfMemoryException">No hay suficiente memoria disponible en el sistema.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Clear">
      <summary>Quita todos los elementos de <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Contains(`0)">
      <summary>Determina si un elemento se encuentra en <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true si <paramref name="item" /> se encuentra en la matriz <see cref="T:System.Collections.Generic.List`1" />; en caso contrario, false.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.List`1" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>Copia un intervalo de elementos de <see cref="T:System.Collections.Generic.List`1" /> en una matriz unidimensional compatible, empezando en el índice especificado de la matriz de destino.</summary>
      <param name="index">Índice de base cero de la <see cref="T:System.Collections.Generic.List`1" /> de origen donde comienza la copia.</param>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="arrayIndex">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <param name="count">Número de elementos que se van a copiar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.o bien<paramref name="arrayIndex" /> es menor que 0.o bien<paramref name="count" /> es menor que 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> es mayor o igual que el valor de <see cref="P:System.Collections.Generic.List`1.Count" /> de la <see cref="T:System.Collections.Generic.List`1" /> de origen.o bienEl número de elementos desde <paramref name="index" /> hasta el final del objeto <see cref="T:System.Collections.Generic.List`1" /> de origen es mayor que el espacio disponible desde <paramref name="arrayIndex" /> hasta el final de la matriz <paramref name="array" /> de destino. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[])">
      <summary>Copia toda la <see cref="T:System.Collections.Generic.List`1" /> en una matriz unidimensional compatible, empezando en el principio de la matriz de destino.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentException">El número de elementos de la <see cref="T:System.Collections.Generic.List`1" /> de origen es mayor que el número de elementos que puede contener el parámetro <paramref name="array" /> de destino.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[],System.Int32)">
      <summary>Copia la totalidad de <see cref="T:System.Collections.Generic.List`1" /> en una matriz unidimensional compatible, empezando por el índice especificado de la matriz de destino.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="arrayIndex">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">El número de elementos de la interfaz <see cref="T:System.Collections.Generic.List`1" /> de origen es mayor que el espacio disponible desde <paramref name="arrayIndex" /> hasta el final del parámetro <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Count">
      <summary>Obtiene el número de elementos incluidos en <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Número de elementos incluidos en <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Exists(System.Predicate{`0})">
      <summary>Determina si <see cref="T:System.Collections.Generic.List`1" /> contiene elementos que cumplen las condiciones definidas por el predicado especificado.</summary>
      <returns>trueSi el <see cref="T:System.Collections.Generic.List`1" /> contiene uno o más elementos que coinciden con las condiciones definidas por el predicado especificado; de lo contrario, false.</returns>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones de los elementos que se van a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Find(System.Predicate{`0})">
      <summary>Busca un elemento que coincida con las condiciones definidas por el predicado especificado y devuelve la primera aparición en toda la matriz <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Primer elemento que coincide con las condiciones definidas por el predicado especificado, si se encuentra; de lo contrario, valor predeterminado para el tipo <paramref name="T" />.</returns>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones del elemento que se va a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindAll(System.Predicate{`0})">
      <summary>Recupera todos los elementos que coinciden con las condiciones definidas por el predicado especificado.</summary>
      <returns>Un <see cref="T:System.Collections.Generic.List`1" /> que contiene todos los elementos que cumplen las condiciones definidas por el predicado especificado, si se encuentran; de lo contrario, un valor vacío <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones de los elementos que se van a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Busca un elemento que coincida con las condiciones definidas por el predicado especificado y devuelve el índice de base cero de la primera aparición en el intervalo de elementos de la matriz <see cref="T:System.Collections.Generic.List`1" /> que comienza en el índice especificado y contiene el número especificado de elementos.</summary>
      <returns>Índice de base cero de la primera aparición de un elemento que coincide con las condiciones definidas por <paramref name="match" />, si se encuentra; en caso contrario, -1.</returns>
      <param name="startIndex">Índice inicial de base cero de la búsqueda.</param>
      <param name="count">Número de elementos de la sección en la que se va a realizar la búsqueda.</param>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones del elemento que se va a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> se encuentra fuera del intervalo de índices válidos para <see cref="T:System.Collections.Generic.List`1" />.o bien<paramref name="count" /> es menor que 0.o bien<paramref name="startIndex" /> y <paramref name="count" /> no especifican una sección válida en <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>Busca un elemento que coincida con las condiciones definidas por el predicado especificado y devuelve el índice de base cero de la primera aparición en el intervalo de elementos de la matriz <see cref="T:System.Collections.Generic.List`1" /> que va desde el índice especificado hasta el último elemento.</summary>
      <returns>Índice de base cero de la primera aparición de un elemento que coincide con las condiciones definidas por <paramref name="match" />, si se encuentra; en caso contrario, -1.</returns>
      <param name="startIndex">Índice inicial de base cero de la búsqueda.</param>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones del elemento que se va a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> se encuentra fuera del intervalo de índices válidos para <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Predicate{`0})">
      <summary>Busca un elemento que coincida con las condiciones definidas por el predicado especificado y devuelve el índice de base cero de la primera aparición en toda la matriz <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Índice de base cero de la primera aparición de un elemento que coincide con las condiciones definidas por <paramref name="match" />, si se encuentra; en caso contrario, -1.</returns>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones del elemento que se va a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLast(System.Predicate{`0})">
      <summary>Busca un elemento que coincida con las condiciones definidas por el predicado especificado y devuelve la última aparición en toda la matriz <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Último elemento que coincide con las condiciones definidas por el predicado especificado, si se encuentra; de lo contrario, valor predeterminado para el tipo <paramref name="T" />.</returns>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones del elemento que se va a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Busca un elemento que coincida con las condiciones definidas por el predicado especificado y devuelve el índice de base cero de la última aparición en el intervalo de elementos de la matriz <see cref="T:System.Collections.Generic.List`1" /> que contiene el número especificado de elementos y termina en el índice especificado.</summary>
      <returns>Índice de base cero de la última aparición de un elemento que coincide con las condiciones definidas por <paramref name="match" />, si se encuentra; en caso contrario, -1.</returns>
      <param name="startIndex">Índice inicial de base cero de la búsqueda hacia atrás.</param>
      <param name="count">Número de elementos de la sección en la que se va a realizar la búsqueda.</param>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones del elemento que se va a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> se encuentra fuera del intervalo de índices válidos para <see cref="T:System.Collections.Generic.List`1" />.o bien<paramref name="count" /> es menor que 0.o bien<paramref name="startIndex" /> y <paramref name="count" /> no especifican una sección válida en <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>Busca un elemento que coincida con las condiciones definidas por el predicado especificado y devuelve el índice de base cero de la última aparición en el intervalo de elementos de la matriz <see cref="T:System.Collections.Generic.List`1" /> que va desde el primer elemento hasta el índice especificado.</summary>
      <returns>Índice de base cero de la última aparición de un elemento que coincide con las condiciones definidas por <paramref name="match" />, si se encuentra; en caso contrario, -1.</returns>
      <param name="startIndex">Índice inicial de base cero de la búsqueda hacia atrás.</param>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones del elemento que se va a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> se encuentra fuera del intervalo de índices válidos para <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Predicate{`0})">
      <summary>Busca un elemento que coincida con las condiciones definidas por el predicado especificado y devuelve el índice de base cero de la última aparición en toda la matriz <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Índice de base cero de la última aparición de un elemento que coincide con las condiciones definidas por <paramref name="match" />, si se encuentra; en caso contrario, -1.</returns>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones del elemento que se va a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ForEach(System.Action{`0})">
      <summary>Realiza la acción especificada en cada elemento de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="action">Delegado <see cref="T:System.Action`1" /> para realizar la acción en cada elemento de <see cref="T:System.Collections.Generic.List`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetEnumerator">
      <summary>Devuelve un enumerador que itera a través de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.Generic.List`1.Enumerator" /> para la colección <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetRange(System.Int32,System.Int32)">
      <summary>Crea una copia superficial de un intervalo de elementos en la <see cref="T:System.Collections.Generic.List`1" /> de origen.</summary>
      <returns>Copia superficial de un intervalo de elementos en la <see cref="T:System.Collections.Generic.List`1" /> de origen.</returns>
      <param name="index">Índice de <see cref="T:System.Collections.Generic.List`1" /> de base cero en el que empieza el intervalo.</param>
      <param name="count">Número de elementos del intervalo.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.o bien<paramref name="count" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> y <paramref name="count" /> no denotan un intervalo válido de elementos en <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0)">
      <summary>Busca el objeto especificado y devuelve el índice de base cero de la primera aparición en todo el <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Índice de base cero de la primera aparición de <paramref name="item" /> en la totalidad de <see cref="T:System.Collections.Generic.List`1" />, si se encuentra; en caso contrario, -1.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.List`1" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32)">
      <summary>Busca el objeto especificado y devuelve el índice de base cero de la primera aparición dentro del intervalo de elementos de <see cref="T:System.Collections.Generic.List`1" /> que abarca desde el índice especificado hasta el último elemento.</summary>
      <returns>Índice de base cero de la primera aparición de <paramref name="item" /> dentro del intervalo de elementos de la <see cref="T:System.Collections.Generic.List`1" /> que se extiende desde <paramref name="index" /> al último elemento, si se encuentra; en caso contrario, de -1.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.List`1" />.El valor puede ser null para los tipos de referencia.</param>
      <param name="index">Índice inicial de base cero de la búsqueda.0 (cero) es válido en una lista vacía.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> se encuentra fuera del intervalo de índices válidos para <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32,System.Int32)">
      <summary>Busca el objeto especificado y devuelve el índice de base cero de la primera aparición dentro del intervalo de elementos de <see cref="T:System.Collections.Generic.List`1" /> que comienza en el índice especificado y contiene el número especificado de elementos.</summary>
      <returns>Índice de base cero de la primera aparición de <paramref name="item" /> dentro del intervalo de elementos de la <see cref="T:System.Collections.Generic.List`1" /> que comienza en <paramref name="index" /> y contiene <paramref name="count" /> número de elementos, si se encuentra; en caso contrario, de -1.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.List`1" />.El valor puede ser null para los tipos de referencia.</param>
      <param name="index">Índice inicial de base cero de la búsqueda.0 (cero) es válido en una lista vacía.</param>
      <param name="count">Número de elementos de la sección en la que se va a realizar la búsqueda.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> se encuentra fuera del intervalo de índices válidos para <see cref="T:System.Collections.Generic.List`1" />.o bien<paramref name="count" /> es menor que 0.o bien<paramref name="index" /> y <paramref name="count" /> no especifican una sección válida en <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Insert(System.Int32,`0)">
      <summary>Inserta un elemento en <see cref="T:System.Collections.Generic.List`1" /> en el índice especificado.</summary>
      <param name="index">Índice de base cero en el que debe insertarse <paramref name="item" />.</param>
      <param name="item">Objeto que se va a insertar.El valor puede ser null para los tipos de referencia.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.o bien<paramref name="index" /> es mayor que <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Inserta los elementos de una colección en <see cref="T:System.Collections.Generic.List`1" /> en el índice especificado.</summary>
      <param name="index">Índice de base cero donde se deben insertar los nuevos elementos.</param>
      <param name="collection">Colección cuyos elementos se deben insertar en <see cref="T:System.Collections.Generic.List`1" />.La propia colección no puede ser null, pero puede contener elementos que sean null si el tipo <paramref name="T" /> es un tipo de referencia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.o bien<paramref name="index" /> es mayor que <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Item(System.Int32)">
      <summary>Obtiene o establece el elemento en el índice especificado.</summary>
      <returns>Elemento en el índice especificado.</returns>
      <param name="index">Índice de base cero del elemento que se va a obtener o establecer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.o bien<paramref name="index" /> es mayor o igual que <see cref="P:System.Collections.Generic.List`1.Count" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0)">
      <summary>Busca el objeto especificado y devuelve el índice de base cero de la última aparición en toda la <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Índice de base cero de la última aparición de <paramref name="item" /> en toda la <see cref="T:System.Collections.Generic.List`1" />, si se encuentra; en caso contrario, -1.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.List`1" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32)">
      <summary>Busca el objeto especificado y devuelve el índice de base cero de la última aparición dentro del intervalo de elementos de <see cref="T:System.Collections.Generic.List`1" /> que abarca desde el primer elemento hasta el último índice especificado.</summary>
      <returns>Índice de base cero de la última aparición de <paramref name="item" /> dentro del intervalo de elementos de <see cref="T:System.Collections.Generic.List`1" /> que abarca desde el primer elemento hasta <paramref name="index" />, si se encuentra; en caso contrario, -1.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.List`1" />.El valor puede ser null para los tipos de referencia.</param>
      <param name="index">Índice inicial de base cero de la búsqueda hacia atrás.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> se encuentra fuera del intervalo de índices válidos para <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>Busca el objeto especificado y devuelve el índice de base cero de la última aparición dentro del intervalo de elementos de <see cref="T:System.Collections.Generic.List`1" /> que contiene el número de elementos especificado y termina en el índice determinado.</summary>
      <returns>Índice de base cero de la última aparición de <paramref name="item" /> dentro del intervalo de elementos de la <see cref="T:System.Collections.Generic.List`1" /> que contiene <paramref name="count" /> número de elementos y termina en <paramref name="index" />, si se encuentra; en caso contrario, de -1.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.List`1" />.El valor puede ser null para los tipos de referencia.</param>
      <param name="index">Índice inicial de base cero de la búsqueda hacia atrás.</param>
      <param name="count">Número de elementos de la sección en la que se va a realizar la búsqueda.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> se encuentra fuera del intervalo de índices válidos para <see cref="T:System.Collections.Generic.List`1" />.o bien<paramref name="count" /> es menor que 0.o bien<paramref name="index" /> y <paramref name="count" /> no especifican una sección válida en <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Remove(`0)">
      <summary>Quita la primera aparición de un objeto específico de la interfaz <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Es true si <paramref name="item" /> se quita correctamente; en caso contrario, es false.Este método también devuelve false si no se encuentra <paramref name="item" /> en <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="item">Objeto que se va a quitar de <see cref="T:System.Collections.Generic.List`1" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAll(System.Predicate{`0})">
      <summary>Quita todos los elementos que cumplen las condiciones definidas por el predicado especificado.</summary>
      <returns>Número de elementos que se han quitado de <see cref="T:System.Collections.Generic.List`1" /> .</returns>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones de los elementos que se van a quitar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAt(System.Int32)">
      <summary>Quita el elemento situado en el índice especificado de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="index">Índice de base cero del elemento que se va a quitar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.o bien<paramref name="index" /> es mayor o igual que <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveRange(System.Int32,System.Int32)">
      <summary>Quita un intervalo de elementos de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="index">Índice inicial de base cero del intervalo de elementos que se va a quitar.</param>
      <param name="count">Número de elementos que se va a quitar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.o bien<paramref name="count" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> y <paramref name="count" /> no denotan un intervalo válido de elementos en <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse">
      <summary>Invierte el orden de los elementos en la <see cref="T:System.Collections.Generic.List`1" /> completa.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse(System.Int32,System.Int32)">
      <summary>Invierte el orden de los elementos en el intervalo especificado.</summary>
      <param name="index">Índice inicial de base cero del intervalo que se va a invertir.</param>
      <param name="count">Número de elementos del intervalo que se va a invertir.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.o bien<paramref name="count" /> es menor que 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> y <paramref name="count" /> no denotan un intervalo válido de elementos en <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort">
      <summary>Ordena los elementos de toda la <see cref="T:System.Collections.Generic.List`1" /> utilizando el comparador predeterminado.</summary>
      <exception cref="T:System.InvalidOperationException">El comparador predeterminado <see cref="P:System.Collections.Generic.Comparer`1.Default" /> no puede encontrar una implementación de la interfaz genérica <see cref="T:System.IComparable`1" /> o de la interfaz <see cref="T:System.IComparable" /> para el tipo <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>Ordena los elementos en la <see cref="T:System.Collections.Generic.List`1" /> completa utilizando el comparador especificado.</summary>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IComparer`1" /> que se va a utilizar al comparar elementos, o null para utilizar el comparador predeterminado <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> es null y el comparador predeterminado <see cref="P:System.Collections.Generic.Comparer`1.Default" /> no puede encontrar la implementación de la interfaz genérica <see cref="T:System.IComparable`1" /> o de la interfaz <see cref="T:System.IComparable" /> para el tipo <paramref name="T" />.</exception>
      <exception cref="T:System.ArgumentException">La implementación de <paramref name="comparer" /> ha producido un error durante la ordenación.Por ejemplo, es posible que <paramref name="comparer" /> no devuelva 0 al comparar un elemento con sigo mismo.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Comparison{`0})">
      <summary>Ordena los elementos de toda la <see cref="T:System.Collections.Generic.List`1" /> utilizando el <see cref="T:System.Comparison`1" /> especificado.</summary>
      <param name="comparison">
        <see cref="T:System.Comparison`1" /> que se va a utilizar al comparar elementos.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" />is null.</exception>
      <exception cref="T:System.ArgumentException">La implementación de <paramref name="comparison" /> ha producido un error durante la ordenación.Por ejemplo, es posible que <paramref name="comparison" /> no devuelva 0 al comparar un elemento con sigo mismo.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Ordena los elementos en un intervalo de elementos de <see cref="T:System.Collections.Generic.List`1" /> utilizando el comparador especificado.</summary>
      <param name="index">Índice inicial de base cero del intervalo que se va a ordenar.</param>
      <param name="count">Longitud del intervalo que se va a ordenar.</param>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IComparer`1" /> que se va a utilizar al comparar elementos, o null para utilizar el comparador predeterminado <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.o bien<paramref name="count" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> y <paramref name="count" /> no especifican un intervalo válido en <see cref="T:System.Collections.Generic.List`1" />.o bienLa implementación de <paramref name="comparer" /> ha producido un error durante la ordenación.Por ejemplo, es posible que <paramref name="comparer" /> no devuelva 0 al comparar un elemento con sigo mismo.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> es null y el comparador predeterminado <see cref="P:System.Collections.Generic.Comparer`1.Default" /> no puede encontrar la implementación de la interfaz genérica <see cref="T:System.IComparable`1" /> o de la interfaz <see cref="T:System.IComparable" /> para el tipo <paramref name="T" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura.</summary>
      <returns>trueSi el <see cref="T:System.Collections.Generic.ICollection`1" /> es de sólo lectura; de lo contrario, false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.List`1" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> que se puede usar para iterar por la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos de la <see cref="T:System.Collections.ICollection" /> a una <see cref="T:System.Array" />, empezando en un determinado <see cref="T:System.Array" /> índice.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="arrayIndex">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> es multidimensional.o bien<paramref name="array" /> no tiene índices de base cero.o bienEl número de elementos de la interfaz <see cref="T:System.Collections.ICollection" /> de origen es mayor que el espacio disponible desde <paramref name="arrayIndex" /> hasta el final del parámetro <paramref name="array" /> de destino.o bienEl tipo de la interfaz <see cref="T:System.Collections.ICollection" /> de origen no se puede convertir automáticamente al tipo de la matriz <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>trueSi el acceso a la <see cref="T:System.Collections.ICollection" /> está sincronizado (seguro para subprocesos); de lo contrario, false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.List`1" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.En la implementación predeterminada de <see cref="T:System.Collections.Generic.List`1" />, esta propiedad siempre devuelve la instancia actual.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> que se puede usar para iterar por la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Add(System.Object)">
      <summary>Agrega un elemento a <see cref="T:System.Collections.IList" />.</summary>
      <returns>Posición en la que se insertó el nuevo elemento.</returns>
      <param name="item">
        <see cref="T:System.Object" /> que se va a agregar a <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> es de un tipo que no se puede asignar a <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Contains(System.Object)">
      <summary>Determina si <see cref="T:System.Collections.IList" /> contiene un valor específico.</summary>
      <returns>true si <paramref name="item" /> se encuentra en la matriz <see cref="T:System.Collections.IList" />; en caso contrario, false.</returns>
      <param name="item">
        <see cref="T:System.Object" /> que se va a buscar en <see cref="T:System.Collections.IList" />.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>Determina el índice de un elemento específico de <see cref="T:System.Collections.IList" />.</summary>
      <returns>Devuelve el índice de <paramref name="item" /> si se encuentra en la lista; en caso contrario, devuelve -1.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> es de un tipo que no se puede asignar a <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Inserta un elemento en la interfaz <see cref="T:System.Collections.IList" />, en el índice especificado.</summary>
      <param name="index">Índice de base cero en el que debe insertarse <paramref name="item" />.</param>
      <param name="item">Objeto que se va a insertar en <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> no es un índice válido para <see cref="T:System.Collections.IList" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> es de un tipo que no se puede asignar a <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsFixedSize">
      <summary>Obtiene un valor que indica si la interfaz <see cref="T:System.Collections.IList" /> tiene un tamaño fijo.</summary>
      <returns>trueSi el <see cref="T:System.Collections.IList" /> tiene un tamaño fijo; de lo contrario, false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.List`1" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.IList" /> es de solo lectura.</summary>
      <returns>trueSi el <see cref="T:System.Collections.IList" /> es de sólo lectura; de lo contrario, false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.List`1" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#Item(System.Int32)">
      <summary>Obtiene o establece el elemento en el índice especificado.</summary>
      <returns>Elemento en el índice especificado.</returns>
      <param name="index">Índice de base cero del elemento que se va a obtener o establecer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> no es un índice válido para <see cref="T:System.Collections.IList" />.</exception>
      <exception cref="T:System.ArgumentException">La propiedad ya está establecida y el tipo de <paramref name="value" /> no se puede asignar a la interfaz <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Remove(System.Object)">
      <summary>Quita la primera aparición de un objeto específico de la interfaz <see cref="T:System.Collections.IList" />.</summary>
      <param name="item">Objeto que se va a quitar de <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> es de un tipo que no se puede asignar a <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ToArray">
      <summary>Copia los elementos de <see cref="T:System.Collections.Generic.List`1" /> en una nueva matriz.</summary>
      <returns>Matriz que contiene copias de los elementos de <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrimExcess">
      <summary>Establece la capacidad en el número real de elementos que hay en <see cref="T:System.Collections.Generic.List`1" />, si dicho número es inferior a un valor umbral.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrueForAll(System.Predicate{`0})">
      <summary>Determina si cada elemento de <see cref="T:System.Collections.Generic.List`1" /> cumple las condiciones que define el predicado especificado.</summary>
      <returns>Es true si cada elemento de <see cref="T:System.Collections.Generic.List`1" /> cumple las condiciones definidas por el predicado especificado; en caso contrario, es false.Si la lista no tiene ningún elemento, el valor devuelto es true.</returns>
      <param name="match">Delegado <see cref="T:System.Predicate`1" /> que define las condiciones que los elementos deben cumplir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="T:System.Collections.Generic.List`1.Enumerator">
      <summary>Enumera los elementos de un objeto <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de <see cref="T:System.Collections.Generic.List`1" /> en la posición actual del enumerador.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.Dispose">
      <summary>Libera todos los recursos utilizados por <see cref="T:System.Collections.Generic.List`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de <see cref="T:System.Collections.Generic.List`1" /> en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="T:System.Collections.Generic.Queue`1">
      <summary>Representa una colección de objetos de tipo primero en entrar, primero en salir.</summary>
      <typeparam name="T">Especifica el tipo de elementos en la cola.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Queue`1" /> que está vacía y tiene la capacidad inicial predeterminada.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Queue`1" /> que contiene elementos copiados de la colección especificada y tiene una capacidad suficiente para aceptar el número de elementos copiados.</summary>
      <param name="collection">Colección cuyos elementos se copian en la nueva colección <see cref="T:System.Collections.Generic.Queue`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Queue`1" /> que está vacía y tiene la capacidad inicial especificada.</summary>
      <param name="capacity">Número inicial de elementos que puede contener la colección <see cref="T:System.Collections.Generic.Queue`1" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Clear">
      <summary>Quita todos los objetos de la colección <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Contains(`0)">
      <summary>Determina si un elemento se encuentra en <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>true si <paramref name="item" /> se encuentra en la matriz <see cref="T:System.Collections.Generic.Queue`1" />; en caso contrario, false.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.Queue`1" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.CopyTo(`0[],System.Int32)">
      <summary>Copia los elementos de <see cref="T:System.Collections.Generic.Queue`1" /> en una <see cref="T:System.Array" /> unidimensional existente, a partir del índice especificado de la matriz.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.Generic.Queue`1" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="arrayIndex">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Queue`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Count">
      <summary>Obtiene el número de elementos incluidos en <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>Número de elementos incluidos en <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Dequeue">
      <summary>Quita y devuelve el objeto al comienzo de <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>Objeto que se quita del principio de <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enqueue(`0)">
      <summary>Agrega un objeto al final de <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <param name="item">Objeto que se va a agregar a <see cref="T:System.Collections.Generic.Queue`1" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.Generic.Queue`1.Enumerator" /> para la colección <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Peek">
      <summary>Devuelve un objeto al principio de <see cref="T:System.Collections.Generic.Queue`1" /> sin eliminarlo.</summary>
      <returns>Objeto situado al principio de <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos de <see cref="T:System.Collections.ICollection" /> en una matriz <see cref="T:System.Array" />, a partir de un índice determinado de <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>Es true si el acceso a <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos); de lo contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.Queue`1" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.En la implementación predeterminada de <see cref="T:System.Collections.Generic.Queue`1" />, esta propiedad siempre devuelve la instancia actual.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.ToArray">
      <summary>Copia los elementos de <see cref="T:System.Collections.Generic.Queue`1" /> en una nueva matriz.</summary>
      <returns>Nueva matriz que contiene elementos copiados de <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.TrimExcess">
      <summary>Establece la capacidad en el número real de elementos en la colección <see cref="T:System.Collections.Generic.Queue`1" />, si este número supone menos del 90 por ciento de la capacidad actual.</summary>
    </member>
    <member name="T:System.Collections.Generic.Queue`1.Enumerator">
      <summary>Enumera los elementos de un objeto <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de <see cref="T:System.Collections.Generic.Queue`1" /> en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.Dispose">
      <summary>Libera todos los recursos utilizados por <see cref="T:System.Collections.Generic.Queue`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2">
      <summary>Representa una colección de pares clave-valor que se ordenan por claves. </summary>
      <typeparam name="TKey">Tipo de las claves del diccionario.</typeparam>
      <typeparam name="TValue">Tipo de los valores del diccionario.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedDictionary`2" /> que está vacía y utiliza la implementación predeterminada de <see cref="T:System.Collections.Generic.IComparer`1" /> para el tipo de clave.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedDictionary`2" /> que está vacía y utiliza la implementación especificada de <see cref="T:System.Collections.Generic.IComparer`1" /> para comparar las claves.</summary>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IComparer`1" /> que se va a utilizar para comparar claves o null si se va a utilizar el <see cref="T:System.Collections.Generic.Comparer`1" /> predeterminado para el tipo de la clave.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedDictionary`2" /> que contiene elementos copiados del <see cref="T:System.Collections.Generic.IDictionary`2" /> especificado y que utiliza la implementación predeterminada de <see cref="T:System.Collections.Generic.IComparer`1" /> para el tipo de clave.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> cuyos elementos se copian en el nuevo <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="dictionary" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contiene una o varias claves duplicadas.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedDictionary`2" /> que contiene elementos copiados del <see cref="T:System.Collections.Generic.IDictionary`2" /> especificado y que utiliza la implementación especificada de <see cref="T:System.Collections.Generic.IComparer`1" /> para comparar las claves.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> cuyos elementos se copian en el nuevo <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IComparer`1" /> que se va a utilizar para comparar claves o null si se va a utilizar el <see cref="T:System.Collections.Generic.Comparer`1" /> predeterminado para el tipo de la clave.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="dictionary" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contiene una o varias claves duplicadas.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Add(`0,`1)">
      <summary>Agrega un elemento con la clave y el valor especificados a <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <param name="key">Clave del elemento que se va a agregar.</param>
      <param name="value">Valor del elemento que se va a agregar.El valor puede ser null para los tipos de referencia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.ArgumentException">Ya existe un elemento con la misma clave en <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Clear">
      <summary>Quita todos los elementos de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Comparer">
      <summary>Obtiene el <see cref="T:System.Collections.Generic.IComparer`1" /> utilizado para ordenar los elementos del <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Interfaz <see cref="T:System.Collections.Generic.IComparer`1" /> utilizada para ordenar los elementos de la colección <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsKey(`0)">
      <summary>Determina si <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contiene un elemento con la clave especificada.</summary>
      <returns>Es true si <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contiene un elemento con la clave especificada; en caso contrario, es false.</returns>
      <param name="key">Clave que se buscará en <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsValue(`1)">
      <summary>Determina si <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contiene un elemento con el valor especificado.</summary>
      <returns>Es true si <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contiene un elemento con el valor especificado; en caso contrario, es false.</returns>
      <param name="value">Valor que se va a buscar en <see cref="T:System.Collections.Generic.SortedDictionary`2" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copia los elementos de <see cref="T:System.Collections.Generic.SortedDictionary`2" /> a la matriz especificada de estructuras <see cref="T:System.Collections.Generic.KeyValuePair`2" />, empezando en el índice que se haya indicado.</summary>
      <param name="array">Matriz unidimensional de estructuras <see cref="T:System.Collections.Generic.KeyValuePair`2" /> que es el destino de los elementos copiados del <see cref="T:System.Collections.Generic.SortedDictionary`2" /> actual. Los índices de la matriz deben ser de base cero.</param>
      <param name="index">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">El número de elementos de la interfaz <see cref="T:System.Collections.Generic.SortedDictionary`2" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final del parámetro <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Count">
      <summary>Obtiene el número de pares clave-valor incluidos en <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Número de pares clave-valor incluidos en <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.GetEnumerator">
      <summary>Devuelve un enumerador que itera en <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" /> para la colección <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Item(`0)">
      <summary>Obtiene o establece el valor asociado a la clave especificada.</summary>
      <returns>Valor asociado a la clave especificada.Si no se encuentra la clave especificada, en el caso de una operación Get, se producirá una excepción <see cref="T:System.Collections.Generic.KeyNotFoundException" /> y, en el caso de una operación Set, se creará un nuevo elemento con la clave especificada.</returns>
      <param name="key">Clave del valor que se va a obtener o establecer.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Se ha recuperado la propiedad y <paramref name="key" /> no existe en la colección.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Keys">
      <summary>Obtiene una colección que contiene las claves de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> que contiene las claves de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Remove(`0)">
      <summary>Quita el elemento con la clave especificada de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Es true si el elemento se quita correctamente; en caso contrario, es false.Este método también devuelve false si no se encuentra <paramref name="key" /> en <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
      <param name="key">Clave del elemento que se va a quitar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Agrega un elemento a <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="keyValuePair">Estructura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> que se va a agregar a <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="keyValuePair" /> es null.</exception>
      <exception cref="T:System.ArgumentException">Ya existe un elemento con la misma clave en <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Determina si <see cref="T:System.Collections.Generic.ICollection`1" /> contiene una clave y un valor específicos.</summary>
      <returns>true si <paramref name="keyValuePair" /> se encuentra en la matriz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.</returns>
      <param name="keyValuePair">Estructura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> que se va a buscar en <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura.</summary>
      <returns>Es true si <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura; en caso contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedDictionary`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Quita la primera aparición del elemento especificado de <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>Es true si <paramref name="keyValuePair" /> se ha quitado correctamente de <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, es false.Este método también devuelve false si no se encuentra <paramref name="keyValuePair" /> en <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">Estructura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> que se quita de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Obtiene un <see cref="T:System.Collections.Generic.ICollection`1" /> que contiene las claves de <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> que contiene las claves de <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Obtiene un <see cref="T:System.Collections.Generic.ICollection`1" /> que contiene los valores de <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> que contiene los valores de <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> que se puede usar para iterar por la colección.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Obtiene una colección que contiene las claves en el<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>Una colección que contiene las claves de la<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Obtiene una colección que contiene los valores de la<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>Una colección que contiene los valores de la<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos <see cref="T:System.Collections.Generic.ICollection`1" /> en una matriz, comenzando en el índice especificado de la matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.Generic.ICollection`1" />.La matriz debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> es multidimensional.o bien<paramref name="array" /> no tiene índices de base cero.o bienEl número de elementos de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final del parámetro <paramref name="array" /> de destino.o bienEl tipo de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> de origen no se puede convertir automáticamente al tipo de la matriz <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>Es true si el acceso a <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos); en caso contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedDictionary`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />. </returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Agrega un elemento con la clave y el valor proporcionados a <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Objeto que se va a utilizar como clave del elemento que se va a agregar.</param>
      <param name="value">El objeto que se va a usar como valor del elemento que se va a agregar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> es de un tipo que no se puede asignar al tipo de clave <paramref name="TKey" /> de la colección <see cref="T:System.Collections.IDictionary" />.o bien<paramref name="value" /> es de un tipo que no se puede asignar al tipo de valor <paramref name="TValue" /> de la interfaz <see cref="T:System.Collections.IDictionary" />.o bienYa existe un elemento con la misma clave en <see cref="T:System.Collections.IDictionary" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determina si <see cref="T:System.Collections.IDictionary" /> contiene un elemento con la clave especificada.</summary>
      <returns>Es true si <see cref="T:System.Collections.IDictionary" /> contiene un elemento con la clave; en caso contrario, es false.</returns>
      <param name="key">Clave que se buscará en <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Devuelve una interfaz <see cref="T:System.Collections.IDictionaryEnumerator" /> para la interfaz <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.IDictionaryEnumerator" /> para la colección <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Obtiene un valor que indica si la interfaz <see cref="T:System.Collections.IDictionary" /> tiene un tamaño fijo.</summary>
      <returns>Es true si <see cref="T:System.Collections.IDictionary" /> tiene un tamaño fijo; en caso contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedDictionary`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.IDictionary" /> es de solo lectura.</summary>
      <returns>Es true si <see cref="T:System.Collections.IDictionary" /> es de solo lectura; en caso contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedDictionary`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Obtiene o establece el elemento con la clave especificada.</summary>
      <returns>Elemento con la clave especificada, o null si <paramref name="key" /> no está en el diccionario o si <paramref name="key" /> es de un tipo no asignable al tipo de clave <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
      <param name="key">Clave del elemento que se va a obtener.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.ArgumentException">Se está asignando un valor, pero <paramref name="key" /> es de un tipo no asignable al tipo de clave <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.o bienSe está asignando un valor, pero <paramref name="value" /> es de un tipo no asignable al tipo de valor <paramref name="TValue" /> de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Keys">
      <summary>Obtiene un <see cref="T:System.Collections.ICollection" /> que contiene las claves de <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> que contiene las claves de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Quita el elemento con la clave especificada de <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Clave del elemento que se va a quitar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Values">
      <summary>Obtiene un <see cref="T:System.Collections.ICollection" /> que contiene los valores de <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> que contiene los valores de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que procesa una iteración en la colección.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> que se puede usar para iterar por la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.TryGetValue(`0,`1@)">
      <summary>Obtiene el valor asociado a la clave especificada.</summary>
      <returns>Es true si <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contiene un elemento con la clave especificada; en caso contrario, es false.</returns>
      <param name="key">Clave del valor que se va a obtener.</param>
      <param name="value">Cuando este método devuelve el resultado, el valor asociado a la clave especificada, si se encuentra la clave; en caso contrario, el valor predeterminado para el tipo del parámetro <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Values">
      <summary>Obtiene una colección que contiene los valores de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> que contiene los valores de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.Enumerator">
      <summary>Enumera los elementos de un objeto <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de <see cref="T:System.Collections.Generic.SortedDictionary`2" /> en la posición actual del enumerador.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.Dispose">
      <summary>Libera todos los recursos utilizados por <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Obtiene el elemento que está en la posición actual del enumerador como una estructura <see cref="T:System.Collections.DictionaryEntry" />.</summary>
      <returns>Elemento de la colección que está en la posición actual del diccionario, como una estructura <see cref="T:System.Collections.DictionaryEntry" />.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Obtiene la clave del elemento situado en la posición actual del enumerador.</summary>
      <returns>Clave del elemento de la colección situado en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Obtiene el valor del elemento situado en la posición actual del enumerador.</summary>
      <returns>Valor del elemento de la colección situado en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection">
      <summary>Representa la colección de claves de una colección <see cref="T:System.Collections.Generic.SortedDictionary`2" />.Esta clase no puede heredarse.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> que refleja las claves de la colección <see cref="T:System.Collections.Generic.SortedDictionary`2" /> especificada.</summary>
      <param name="dictionary">Colección <see cref="T:System.Collections.Generic.SortedDictionary`2" /> cuyas claves se reflejan en la nueva colección <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Copia los elementos de la colección <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> en una matriz unidimensional existente, a partir del índice especificado de la matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.La matriz debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero de <paramref name="array" /> en el que empieza la operación de copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="index" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">El número de elementos de la interfaz <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final del parámetro <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Count">
      <summary>Obtiene el número de elementos incluidos en <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>Número de elementos incluidos en <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> para la colección <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Agrega un elemento a <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Objeto que se va a agregar a <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Se produce siempre; la colección es de sólo lectura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Quita todos los elementos de <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Se produce siempre; la colección es de sólo lectura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Determina si la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> contiene el valor especificado.</summary>
      <returns>true si <paramref name="item" /> se encuentra en la matriz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.Generic.ICollection`1" /> es de sólo lectura.</summary>
      <returns>true si la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura; en caso contrario, false.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Quita la primera aparición de un objeto específico de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true si <paramref name="item" /> se ha quitado correctamente de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.Este método también devuelve false si no se encuentra <paramref name="item" /> en <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Objeto que se va a quitar de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Se produce siempre; la colección es de sólo lectura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos de la interfaz <see cref="T:System.Collections.ICollection" /> en una matriz, comenzando en un índice concreto de la matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.ICollection" />.La matriz debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero de <paramref name="array" /> en el que empieza la operación de copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="index" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> es multidimensional.O bien<paramref name="array" /> no tiene índices de base cero.O bienEl número de elementos en la interfaz <see cref="T:System.Collections.ICollection" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final de la matriz <paramref name="array" /> de destino.O bienEl tipo de la interfaz <see cref="T:System.Collections.ICollection" /> de origen no se puede convertir automáticamente al tipo de la matriz <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>Es true si el acceso a <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos); de lo contrario, es false.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede utilizar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede utilizar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, esta propiedad siempre devuelve la instancia actual.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator">
      <summary>Enumera los elementos de un objeto <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> en la posición actual del enumerador.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Libera todos los recursos utilizados por <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection">
      <summary>Representa la colección de valores de una colección <see cref="T:System.Collections.Generic.SortedDictionary`2" />.Esta clase no se puede heredar.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> que refleja los valores de la colección <see cref="T:System.Collections.Generic.SortedDictionary`2" /> especificada.</summary>
      <param name="dictionary">Colección <see cref="T:System.Collections.Generic.SortedDictionary`2" /> cuyos valores se reflejan en la nueva colección <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Copia los elementos de la colección <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> en una matriz unidimensional existente, a partir del índice especificado de la matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.La matriz debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero de <paramref name="array" /> en el que empieza la operación de copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="index" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">El número de elementos de la interfaz <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final del parámetro <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Count">
      <summary>Obtiene el número de elementos incluidos en <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>Número de elementos incluidos en <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> para la colección <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Agrega un elemento a <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Objeto que se va a agregar a <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Se produce siempre; la colección es de sólo lectura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Quita todos los elementos de <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Se produce siempre; la colección es de sólo lectura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Determina si la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> contiene un valor especificado.</summary>
      <returns>true si <paramref name="item" /> se encuentra en la matriz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.Generic.ICollection`1" /> es de sólo lectura.</summary>
      <returns>true si la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura; en caso contrario, false.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Quita la primera aparición de un objeto específico de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true si <paramref name="item" /> se ha quitado correctamente de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.Este método también devuelve false si no se encuentra <paramref name="item" /> en <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Objeto que se va a quitar de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Se produce siempre; la colección es de sólo lectura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Quita la primera aparición de un objeto específico de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />.  Esta implementación siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true si <paramref name="item" /> se ha quitado correctamente de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.Este método también devuelve false si no se encuentra <paramref name="item" /> en <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <exception cref="T:System.NotSupportedException">Se produce siempre; la colección es de sólo lectura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos de la interfaz <see cref="T:System.Collections.ICollection" /> en una matriz, comenzando en un índice concreto de la matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.ICollection" />.La matriz debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero de <paramref name="array" /> en el que empieza la operación de copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="index" /> es menor que 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> es multidimensional.O bien<paramref name="array" /> no tiene índices de base cero.O bienEl número de elementos en la interfaz <see cref="T:System.Collections.ICollection" /> de origen es mayor que el espacio disponible desde <paramref name="index" /> hasta el final de la matriz <paramref name="array" /> de destino.O bienEl tipo de la interfaz <see cref="T:System.Collections.ICollection" /> de origen no se puede convertir automáticamente al tipo de la matriz <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>Es true si el acceso a <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos); de lo contrario, es false.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede utilizar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede utilizar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.  En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, esta propiedad siempre devuelve la instancia actual.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator">
      <summary>Enumera los elementos de un objeto <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> en la posición actual del enumerador.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Libera todos los recursos utilizados por <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedList`2">
      <summary>Representa una colección de pares clave-valor que se ordenan por claves según la implementación de <see cref="T:System.Collections.Generic.IComparer`1" /> asociada. </summary>
      <typeparam name="TKey">Tipo de claves de la colección.</typeparam>
      <typeparam name="TValue">Tipo de valores de la colección.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedList`2" /> que está vacía, tiene la capacidad inicial predeterminada y utiliza el <see cref="T:System.Collections.Generic.IComparer`1" /> predeterminado.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedList`2" /> que está vacía, tiene la capacidad inicial predeterminada y utiliza el <see cref="T:System.Collections.Generic.IComparer`1" /> especificado.</summary>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IComparer`1" /> que se va a usar al comparar claves.o biennull para utilizar el <see cref="T:System.Collections.Generic.Comparer`1" /> predeterminado para el tipo de la clave.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedList`2" /> que contiene elementos copiados del <see cref="T:System.Collections.Generic.IDictionary`2" /> especificado, tiene una capacidad suficiente para alojar el número de elementos copiados y utiliza el <see cref="T:System.Collections.Generic.IComparer`1" /> predeterminado.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> cuyos elementos se copian en el nuevo <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="dictionary" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contiene una o varias claves duplicadas.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedList`2" /> que contiene elementos copiados del <see cref="T:System.Collections.Generic.IDictionary`2" /> especificado, tiene una capacidad suficiente para alojar el número de elementos copiados y utiliza el <see cref="T:System.Collections.Generic.IComparer`1" /> especificado.</summary>
      <param name="dictionary">
        <see cref="T:System.Collections.Generic.IDictionary`2" /> cuyos elementos se copian en el nuevo <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IComparer`1" /> que se va a usar al comparar claves.o biennull para utilizar el <see cref="T:System.Collections.Generic.Comparer`1" /> predeterminado para el tipo de la clave.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="dictionary" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> contiene una o varias claves duplicadas.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedList`2" /> que está vacía, tiene la capacidad inicial especificada y utiliza el <see cref="T:System.Collections.Generic.IComparer`1" /> predeterminado.</summary>
      <param name="capacity">Número inicial de elementos que puede contener <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> es menor que cero.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedList`2" /> que está vacía, tiene la capacidad inicial especificada y utiliza el <see cref="T:System.Collections.Generic.IComparer`1" /> especificado.</summary>
      <param name="capacity">Número inicial de elementos que puede contener <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <param name="comparer">Implementación de <see cref="T:System.Collections.Generic.IComparer`1" /> que se va a usar al comparar claves.o biennull para utilizar el <see cref="T:System.Collections.Generic.Comparer`1" /> predeterminado para el tipo de la clave.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> es menor que cero.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Add(`0,`1)">
      <summary>Agrega un elemento con la clave y el valor especificados a <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <param name="key">Clave del elemento que se va a agregar.</param>
      <param name="value">Valor del elemento que se va a agregar.El valor puede ser null para los tipos de referencia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.ArgumentException">Ya existe un elemento con la misma clave en <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Capacity">
      <summary>Obtiene o establece el número de elementos que puede contener <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Número de elementos que puede contener <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.SortedList`2.Capacity" /> se establece en un valor que es menor que <see cref="P:System.Collections.Generic.SortedList`2.Count" />.</exception>
      <exception cref="T:System.OutOfMemoryException">No hay suficiente memoria disponible en el sistema.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Clear">
      <summary>Quita todos los elementos de <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Comparer">
      <summary>Obtiene el <see cref="T:System.Collections.Generic.IComparer`1" /> para la lista ordenada. </summary>
      <returns>
        <see cref="T:System.IComparable`1" /> de la <see cref="T:System.Collections.Generic.SortedList`2" /> actual.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsKey(`0)">
      <summary>Determina si <see cref="T:System.Collections.Generic.SortedList`2" /> contiene una clave específica.</summary>
      <returns>Es true si <see cref="T:System.Collections.Generic.SortedList`2" /> contiene un elemento con la clave especificada; en caso contrario, es false.</returns>
      <param name="key">Clave que se buscará en <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsValue(`1)">
      <summary>Determina si <see cref="T:System.Collections.Generic.SortedList`2" /> contiene un valor específico.</summary>
      <returns>Es true si <see cref="T:System.Collections.Generic.SortedList`2" /> contiene un elemento con el valor especificado; en caso contrario, es false.</returns>
      <param name="value">Valor que se va a buscar en <see cref="T:System.Collections.Generic.SortedList`2" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Count">
      <summary>Obtiene el número de pares clave-valor incluidos en <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Número de pares clave-valor incluidos en <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.GetEnumerator">
      <summary>Devuelve un enumerador que itera en <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> de tipo <see cref="T:System.Collections.Generic.KeyValuePair`2" /> para <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfKey(`0)">
      <summary>Busca la clave especificada y devuelve el índice de base cero de la totalidad de <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Índice de base cero de <paramref name="key" /> de la totalidad de <see cref="T:System.Collections.Generic.SortedList`2" />, si se encuentra; en caso contrario, -1.</returns>
      <param name="key">Clave que se buscará en <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfValue(`1)">
      <summary>Busca el valor especificado y devuelve el índice de base cero de la primera aparición de la totalidad de <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Índice de base cero de la primera aparición de <paramref name="value" /> en la totalidad de <see cref="T:System.Collections.Generic.SortedList`2" />, si se encuentra; en caso contrario, -1.</returns>
      <param name="value">Valor que se va a buscar en <see cref="T:System.Collections.Generic.SortedList`2" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Item(`0)">
      <summary>Obtiene o establece el valor asociado a la clave especificada.</summary>
      <returns>Valor asociado a la clave especificada.Si no se encuentra la clave especificada, en el caso de una operación Get se producirá una excepción <see cref="T:System.Collections.Generic.KeyNotFoundException" />, y en el caso de una operación Set, se creará un nuevo elemento utilizando la clave especificada.</returns>
      <param name="key">Clave cuyo valor se va a obtener o a establecer.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Se ha recuperado la propiedad y <paramref name="key" /> no existe en la colección.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Keys">
      <summary>Obtiene una colección que contiene las claves de <see cref="T:System.Collections.Generic.SortedList`2" />, de forma ordenada.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IList`1" /> que contiene las claves de <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Remove(`0)">
      <summary>Quita el elemento con la clave especificada de <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Es true si el elemento se quita correctamente; en caso contrario, es false.Este método también devuelve false si no se encontró <paramref name="key" /> en el <see cref="T:System.Collections.Generic.SortedList`2" /> original.</returns>
      <param name="key">Clave del elemento que se va a quitar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.RemoveAt(System.Int32)">
      <summary>Quita el elemento situado en el índice especificado de <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <param name="index">Índice de base cero del elemento que se va a quitar.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.o bien<paramref name="index" /> es igual o mayor que <see cref="P:System.Collections.Generic.SortedList`2.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Agrega un par clave-valor a <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" /> que se va a agregar a <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Determina si <see cref="T:System.Collections.Generic.ICollection`1" /> contiene un elemento específico.</summary>
      <returns>true si <paramref name="keyValuePair" /> se encuentra en la matriz <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, false.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" /> que se va a buscar en <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copia los elementos de <see cref="T:System.Collections.Generic.ICollection`1" /> en <see cref="T:System.Array" />, empezando por un índice determinado de <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.Generic.ICollection`1" />.La matriz <see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="arrayIndex">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> es menor que cero. </exception>
      <exception cref="T:System.ArgumentException">El número de elementos de la interfaz <see cref="T:System.Collections.Generic.ICollection`1" /> de origen es mayor que el espacio disponible desde <paramref name="arrayIndex" /> hasta el final del parámetro <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura.</summary>
      <returns>Es true si <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura; en caso contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedList`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Quita la primera aparición de un par clave-valor específico de <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>Es true si <paramref name="keyValuePair" /> se ha quitado correctamente de <see cref="T:System.Collections.Generic.ICollection`1" />; en caso contrario, es false.Este método también devuelve false si no se encontró <paramref name="keyValuePair" /> en el <see cref="T:System.Collections.Generic.ICollection`1" /> original.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" /> que se va a quitar de <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Obtiene un <see cref="T:System.Collections.Generic.ICollection`1" /> que contiene las claves de <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> que contiene las claves de <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Obtiene un <see cref="T:System.Collections.Generic.ICollection`1" /> que contiene los valores de <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> que contiene los valores de <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> que se puede usar para iterar por la colección.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Obtiene una colección enumerable que contiene las claves del diccionario de solo lectura.</summary>
      <returns>Colección enumerable que contiene las claves del diccionario de solo lectura.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Obtiene una colección enumerable que contiene los valores del diccionario de solo lectura.</summary>
      <returns>Colección enumerable que contiene los valores del diccionario de solo lectura.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos de <see cref="T:System.Collections.ICollection" /> en <see cref="T:System.Array" />, empezando por un índice determinado de <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.ICollection" />.La matriz <see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="arrayIndex">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> es menor que cero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> es multidimensional.o bien<paramref name="array" /> no tiene índices de base cero.o bienEl número de elementos de la interfaz <see cref="T:System.Collections.ICollection" /> de origen es mayor que el espacio disponible desde <paramref name="arrayIndex" /> hasta el final del parámetro <paramref name="array" /> de destino.o bienEl tipo de la interfaz <see cref="T:System.Collections.ICollection" /> de origen no se puede convertir automáticamente al tipo de la matriz <paramref name="array" /> de destino.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>Es true si el acceso a <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos); en caso contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedList`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedList`2" />, esta propiedad siempre devuelve la instancia actual.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Agrega un elemento con la clave y el valor proporcionados a <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">
        <see cref="T:System.Object" /> que se va a utilizar como clave del elemento que se va a agregar.</param>
      <param name="value">
        <see cref="T:System.Object" /> que se va a utilizar como valor del elemento que se va a agregar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> es de un tipo que no se puede asignar al tipo de clave <paramref name="TKey" /> de la colección <see cref="T:System.Collections.IDictionary" />.o bien<paramref name="value" /> es de un tipo que no se puede asignar al tipo de valor <paramref name="TValue" /> de la interfaz <see cref="T:System.Collections.IDictionary" />.o bienYa existe un elemento con la misma clave en <see cref="T:System.Collections.IDictionary" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determina si <see cref="T:System.Collections.IDictionary" /> contiene un elemento con la clave especificada.</summary>
      <returns>Es true si <see cref="T:System.Collections.IDictionary" /> contiene un elemento con la clave; en caso contrario, es false.</returns>
      <param name="key">Clave que se buscará en <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Devuelve una interfaz <see cref="T:System.Collections.IDictionaryEnumerator" /> para la interfaz <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.IDictionaryEnumerator" /> para la colección <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Obtiene un valor que indica si la interfaz <see cref="T:System.Collections.IDictionary" /> tiene un tamaño fijo.</summary>
      <returns>Es true si <see cref="T:System.Collections.IDictionary" /> tiene un tamaño fijo; en caso contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedList`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Obtiene un valor que indica si <see cref="T:System.Collections.IDictionary" /> es de solo lectura.</summary>
      <returns>Es true si <see cref="T:System.Collections.IDictionary" /> es de solo lectura; en caso contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.SortedList`2" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Obtiene o establece el elemento con la clave especificada.</summary>
      <returns>Elemento con la clave especificada, o null si <paramref name="key" /> no está en el diccionario o si <paramref name="key" /> es de un tipo no asignable al tipo de clave <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">La clave del elemento que se obtiene o establece.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
      <exception cref="T:System.ArgumentException">Se está asignando un valor, pero <paramref name="key" /> es de un tipo no asignable al tipo de clave <paramref name="TKey" /> de <see cref="T:System.Collections.Generic.SortedList`2" />.o bienSe está asignando un valor, pero <paramref name="value" /> es de un tipo no asignable al tipo de valor <paramref name="TValue" /> de <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Keys">
      <summary>Obtiene un <see cref="T:System.Collections.ICollection" /> que contiene las claves de <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> que contiene las claves de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Quita el elemento con la clave especificada de <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Clave del elemento que se va a quitar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Values">
      <summary>Obtiene un <see cref="T:System.Collections.ICollection" /> que contiene los valores de <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> que contiene los valores de <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> que se puede usar para iterar por la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TrimExcess">
      <summary>Establece la capacidad en el número real de elementos de <see cref="T:System.Collections.Generic.SortedList`2" />, si este número supone menos del 90 por ciento de la capacidad actual.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TryGetValue(`0,`1@)">
      <summary>Obtiene el valor asociado a la clave especificada.</summary>
      <returns>Es true si <see cref="T:System.Collections.Generic.SortedList`2" /> contiene un elemento con la clave especificada; en caso contrario, es false.</returns>
      <param name="key">Clave cuyo valor se va a obtener.</param>
      <param name="value">Cuando este método devuelve el resultado, el valor asociado a la clave especificada, si se encuentra la clave; en caso contrario, el valor predeterminado para el tipo del parámetro <paramref name="value" />.Este parámetro se pasa sin inicializar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="key" /> es null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Values">
      <summary>Obtiene una colección que contiene los valores de <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IList`1" /> que contiene los valores de <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1">
      <summary>Representa una colección de objetos que se mantiene en el criterio de ordenación.</summary>
      <typeparam name="T">Tipo de elementos del conjunto.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedSet`1" />. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedSet`1" /> que usa un comparador especificado.</summary>
      <param name="comparer">Comparador predeterminado que se va a usar para comparar los objetos. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedSet`1" /> que contiene los elementos copiados de una colección enumerable especificada.</summary>
      <param name="collection">Colección enumerable que se va a copiar. </param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IComparer{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.SortedSet`1" /> que contiene los elementos copiados de una colección enumerable especificada y que usa un comparador especificado.</summary>
      <param name="collection">Colección enumerable que se va a copiar. </param>
      <param name="comparer">Comparador predeterminado que se va a usar para comparar los objetos. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Add(`0)">
      <summary>Agrega un elemento al conjunto y devuelve un valor que indica si se ha agregado correctamente.</summary>
      <returns>trueSi <paramref name="item" /> se agrega al conjunto; de lo contrario, false. </returns>
      <param name="item">Elemento que se va a agregar al conjunto.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Clear">
      <summary>Quita todos los elementos del conjunto.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Comparer">
      <summary>Obtiene el <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> objeto que se utiliza para determinar la igualdad de los valores de la <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Comparador que se usa para determinar la igualdad de los valores de <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Contains(`0)">
      <summary>Determina si el conjunto contiene un elemento específico.</summary>
      <returns>Es true si el conjunto contiene <paramref name="item" />; de lo contrario, es false.</returns>
      <param name="item">Elemento que se debe buscar en el conjunto.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[])">
      <summary>Copia la totalidad de <see cref="T:System.Collections.Generic.SortedSet`1" /> en una matriz unidimensional compatible, empezando en el principio de la matriz de destino.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentException">El número de elementos de la <see cref="T:System.Collections.Generic.SortedSet`1" /> de origen supera el número de elementos que puede contener la matriz de destino. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32)">
      <summary>Copia completa <see cref="T:System.Collections.Generic.SortedSet`1" /> a una matriz unidimensional compatible, empezando en el índice especificado de la matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.Generic.SortedSet`1" />.La matriz debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentException">El número de elementos de la matriz de origen es mayor que el espacio disponible desde el <paramref name="index" /> hasta el final de la matriz de destino.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Copia un número especificado de elementos de <see cref="T:System.Collections.Generic.SortedSet`1" /> a una matriz unidimensional compatible, empezando en el índice especificado de la matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.Generic.SortedSet`1" />.La matriz debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <param name="count">Número de elementos que se van a copiar.</param>
      <exception cref="T:System.ArgumentException">El número de elementos de la matriz de origen es mayor que el espacio disponible desde el <paramref name="index" /> hasta el final de la matriz de destino.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.o bien<paramref name="count" /> es menor que cero.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Count">
      <summary>Obtiene el número de elementos de <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Número de elementos incluidos en <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Quita del objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual todos los elementos que están en la colección especificada.</summary>
      <param name="other">Colección de elementos que se van a quitar del objeto <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetEnumerator">
      <summary>Devuelve un enumerador que itera a través de <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Enumerador que itera por <see cref="T:System.Collections.Generic.SortedSet`1" /> en orden.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetViewBetween(`0,`0)">
      <summary>Devuelve una vista de un subconjunto en <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Una vista de subconjunto que contiene solo los valores del intervalo especificado.</returns>
      <param name="lowerValue">Valor más bajo deseado en la vista.</param>
      <param name="upperValue">Valor más alto deseado en la vista. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="lowerValue" /> es mayor que <paramref name="upperValue" /> según el comparador.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Una operación probada en la vista estaba fuera del intervalo especificado por <paramref name="lowerValue" /> y <paramref name="upperValue" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual para que solo contenga elementos que también estén en una colección especificada.</summary>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si un objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> es un subconjunto apropiado de la colección especificada.</summary>
      <returns>Es true si el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> es un subconjunto apropiado de <paramref name="other" />; en caso contrario, es false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si un objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> es un supraconjunto apropiado de la colección especificada.</summary>
      <returns>Es true si el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> es un supraconjunto apropiado de <paramref name="other" />; en caso contrario, es false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si un objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> es un subconjunto de la colección especificada.</summary>
      <returns>true si el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual es un subconjunto de <paramref name="other" />; de lo contrario, false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si un objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> es un supraconjunto de la colección especificada.</summary>
      <returns>Es true si el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> es un supraconjunto de <paramref name="other" />; en caso contrario, es false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Max">
      <summary>Obtiene el valor máximo de <see cref="T:System.Collections.Generic.SortedSet`1" />, tal y como define el comparador.</summary>
      <returns>Valor máximo del conjunto.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Min">
      <summary>Obtiene el valor mínimo de <see cref="T:System.Collections.Generic.SortedSet`1" />, tal y como define el comparador.</summary>
      <returns>Valor mínimo del conjunto.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual y una colección especificada comparten elementos comunes.</summary>
      <returns>Es true si el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> y <paramref name="other" /> comparten al menos un elemento común; de lo contrario, es false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Remove(`0)">
      <summary>Quita un elemento especificado de <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>trueSi el elemento se encuentra y quita correctamente; de lo contrario, false. </returns>
      <param name="item">Elemento que se va a quitar.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Quita de <see cref="T:System.Collections.Generic.SortedSet`1" /> todos los elementos que cumplen las condiciones definidas por el predicado especificado.</summary>
      <returns>Número de elementos que se quitaron de la colección <see cref="T:System.Collections.Generic.SortedSet`1" />. </returns>
      <param name="match">Delegado que define las condiciones de los elementos que se van a quitar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Reverse">
      <summary>Devuelve un <see cref="T:System.Collections.Generic.IEnumerable`1" /> que recorre en iteración la <see cref="T:System.Collections.Generic.SortedSet`1" /> en orden inverso.</summary>
      <returns>Un enumerador que itera en orden inverso por <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina si el objeto<see cref="T:System.Collections.Generic.SortedSet`1" /> actual y la colección especificada contienen los mismos elementos.</summary>
      <returns>true si el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual es igual a <paramref name="other" />; de lo contrario, false.</returns>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual para que únicamente contenga elementos que están presentes en el objeto actual o en la colección especificada, pero no en ambos.</summary>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Agrega un elemento a un objeto <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="item">Objeto que se va a agregar al objeto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> es de solo lectura.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Obtiene un valor que indica si un objeto <see cref="T:System.Collections.ICollection" /> es de solo lectura.</summary>
      <returns>true si la colección es de solo lectura; de lo contrario, false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>Enumerador que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia completa <see cref="T:System.Collections.Generic.SortedSet`1" /> a una matriz unidimensional compatible, empezando en el índice especificado de la matriz.</summary>
      <param name="array">Matriz unidimensional que constituye el destino de los elementos copiados desde <see cref="T:System.Collections.Generic.SortedSet`1" />.La matriz debe tener una indización de base cero.</param>
      <param name="index">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentException">El número de elementos de la matriz de origen es mayor que el espacio disponible desde el <paramref name="index" /> hasta el final de la matriz de destino. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>true si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado; de lo contrario, false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.En la implementación predeterminada de <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, esta propiedad siempre devuelve la instancia actual.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>Enumerador que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual para que contenga todos los elementos que están presentes en el objeto actual o en la colección especificada. </summary>
      <param name="other">Colección que se va a comparar con el objeto <see cref="T:System.Collections.Generic.SortedSet`1" /> actual.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1.Enumerator">
      <summary>Enumera los elementos de un objeto <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección en la posición actual del enumerador.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.Dispose">
      <summary>Libera todos los recursos utilizados por <see cref="T:System.Collections.Generic.SortedSet`1.Enumerator" />. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de la colección <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="T:System.Collections.Generic.Stack`1">
      <summary>Representa una colección último en entrar, primero en salir (LIFO) de tamaño variable con instancias del mismo tipo especificado.</summary>
      <typeparam name="T">Especifica el tipo de elementos de la pila.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Stack`1" /> que está vacía y tiene la capacidad inicial predeterminada.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Stack`1" /> que contiene elementos copiados de la colección especificada y tiene una capacidad suficiente para aceptar el número de elementos copiados.</summary>
      <param name="collection">Colección de la que se van a copiar los elementos.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Collections.Generic.Stack`1" /> que está vacía y tiene la capacidad inicial especificada o la capacidad inicial predeterminada, la que sea mayor.</summary>
      <param name="capacity">Número inicial de elementos que puede contener la colección <see cref="T:System.Collections.Generic.Stack`1" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Clear">
      <summary>Quita todos los objetos de la colección <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Contains(`0)">
      <summary>Determina si un elemento se encuentra en <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>true si <paramref name="item" /> se encuentra en la matriz <see cref="T:System.Collections.Generic.Stack`1" />; en caso contrario, false.</returns>
      <param name="item">Objeto que se va a buscar en <see cref="T:System.Collections.Generic.Stack`1" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.CopyTo(`0[],System.Int32)">
      <summary>Copia <see cref="T:System.Collections.Generic.Stack`1" /> en una <see cref="T:System.Array" /> unidimensional existente, a partir del índice especificado de la matriz.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.Generic.Stack`1" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="arrayIndex">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Stack`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Count">
      <summary>Obtiene el número de elementos incluidos en <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>Número de elementos incluidos en <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.GetEnumerator">
      <summary>Devuelve un enumerador para la colección <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>Estructura <see cref="T:System.Collections.Generic.Stack`1.Enumerator" /> para la colección <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Peek">
      <summary>Devuelve el objeto situado al principio de <see cref="T:System.Collections.Generic.Stack`1" /> sin eliminarlo.</summary>
      <returns>Objeto situado al principio de la colección <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Pop">
      <summary>Quita y devuelve el objeto situado al principio de <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>Objeto eliminado del principio de la colección <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Push(`0)">
      <summary>Inserta un objeto al principio de <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <param name="item">Objeto que se va a insertar en la colección <see cref="T:System.Collections.Generic.Stack`1" />.El valor puede ser null para los tipos de referencia.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que procesa una iteración en la colección.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia los elementos de <see cref="T:System.Collections.ICollection" /> en una matriz <see cref="T:System.Array" />, a partir de un índice determinado de <see cref="T:System.Array" />.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="arrayIndex">Índice de base cero en la <paramref name="array" /> donde comienza la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#IsSynchronized">
      <summary>Obtiene un valor que indica si el acceso a la interfaz <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos).</summary>
      <returns>Es true si el acceso a <see cref="T:System.Collections.ICollection" /> está sincronizado (es seguro para subprocesos); de lo contrario, es false.En la implementación predeterminada de <see cref="T:System.Collections.Generic.Stack`1" />, esta propiedad siempre devuelve false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#SyncRoot">
      <summary>Obtiene un objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Objeto que se puede usar para sincronizar el acceso a <see cref="T:System.Collections.ICollection" />.En la implementación predeterminada de <see cref="T:System.Collections.Generic.Stack`1" />, esta propiedad siempre devuelve la instancia actual.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración una colección.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> que se puede utilizar para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.ToArray">
      <summary>Copia <see cref="T:System.Collections.Generic.Stack`1" /> en una nueva matriz.</summary>
      <returns>Nueva matriz que contiene copias de los elementos de <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.TrimExcess">
      <summary>Establece la capacidad en el número real de elementos en la colección <see cref="T:System.Collections.Generic.Stack`1" />, si este número supone menos del 90 por ciento de la capacidad actual.</summary>
    </member>
    <member name="T:System.Collections.Generic.Stack`1.Enumerator">
      <summary>Enumera los elementos de un objeto <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de <see cref="T:System.Collections.Generic.Stack`1" /> en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.Dispose">
      <summary>Libera todos los recursos utilizados por <see cref="T:System.Collections.Generic.Stack`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.MoveNext">
      <summary>Desplaza el enumerador al siguiente elemento de <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>true si el enumerador avanzó con éxito hasta el siguiente elemento; false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el elemento en la posición actual del enumerador.</summary>
      <returns>Elemento de la colección en la posición actual del enumerador.</returns>
      <exception cref="T:System.InvalidOperationException">El enumerador se sitúa antes del primer elemento de la colección o después del último. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Establece el enumerador en su posición inicial (antes del primer elemento de la colección).Esta clase no puede heredarse.</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador. </exception>
    </member>
  </members>
</doc>