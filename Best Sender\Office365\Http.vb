﻿Imports System.IO
Imports System.Net
Public Class Http
    Public Shared Function Gett(url As String) As String
        Dim str As String = String.Empty
        Try
            Dim request As HttpWebRequest = DirectCast(WebRequest.Create(url), HttpWebRequest)
            request.AutomaticDecompression = DecompressionMethods.GZip
            request.Proxy = Nothing
            Using response As HttpWebResponse = DirectCast(request.GetResponse(), HttpWebResponse)
                Using stream As Stream = response.GetResponseStream()
                    Using reader As New StreamReader(stream)
                        str = reader.ReadToEnd()
                    End Using
                End Using
            End Using
        Catch ex As Exception
        End Try
        Return str
    End Function
    'Public Shared Function Gett(url As String) As String
    '    Dim str As String = String.Empty
    '    Try
    '        Dim request As HttpWebRequest = WebRequest.Create(url)
    '        request.AutomaticDecompression = DecompressionMethods.GZip
    '        request.Proxy = Nothing
    '        Dim response As HttpWebResponse = request.GetResponse()
    '        Dim stream As Stream = response.GetResponseStream()
    '        Dim reader As StreamReader = New StreamReader(stream)
    '        str = reader.ReadToEnd()
    '    Catch ex As Exception
    '    End Try
    '    Return str
    'End Function
End Class