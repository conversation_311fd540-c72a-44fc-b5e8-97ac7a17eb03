﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections</name>
  </assembly>
  <members>
    <member name="T:System.Collections.BitArray">
      <summary>Управляет компактным массивом двоичных значений, представленных логическими значениями, где значение true соответствует 1, а значение false соответствует 0.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Boolean[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.BitArray" />, который содержит двоичные значения, скопированные из заданного массива логических значений.</summary>
      <param name="values">Копируемый массив логических значений. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Byte[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.BitArray" />, который содержит двоичные значения, скопированные из заданного массива байтов.</summary>
      <param name="bytes">Массив байтов, содержащий копируемые значения, где каждый байт представляет собой восемь последовательных битов. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="bytes" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Collections.BitArray)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.BitArray" />, содержащий двоичные значения, скопированные из указанного массива <see cref="T:System.Collections.BitArray" />.</summary>
      <param name="bits">Копируемый массив <see cref="T:System.Collections.BitArray" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bits" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.BitArray" />, который может содержать указанное количество битов с первоначально заданным значением false.</summary>
      <param name="length">Число двоичных значений в новом массиве <see cref="T:System.Collections.BitArray" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.BitArray" />, который может содержать определенное количество двоичных значений, для которых установлены заданные начальные значения.</summary>
      <param name="length">Число двоичных значений в новом массиве <see cref="T:System.Collections.BitArray" />. </param>
      <param name="defaultValue">Логическое значение, присваиваемое каждому биту. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.BitArray" />, который содержит двоичные значения, скопированные из указанного массива 32-битных целых чисел.</summary>
      <param name="values">Массив целых чисел, содержащий копируемые значения, где каждое целое число представлено 32 последовательными битами. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="values" /> is greater than <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Collections.BitArray.And(System.Collections.BitArray)">
      <summary>Выполняет битовую операцию логического умножения элементов текущего массива <see cref="T:System.Collections.BitArray" /> на соответствующие элементы указанного массива <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Текущий экземпляр, содержащий результат битовой операции логического умножения элементов текущего массива <see cref="T:System.Collections.BitArray" /> на соответствующие элементы указанного массива <see cref="T:System.Collections.BitArray" />.</returns>
      <param name="value">Массив <see cref="T:System.Collections.BitArray" />, на который производится битовое логическое умножение. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Get(System.Int32)">
      <summary>Возвращает значение бита в указанной позиции в массиве <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Значение бита в позиции <paramref name="index" />.</returns>
      <param name="index">Отсчитываемый от нуля индекс значения, которое нужно получить. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов списка <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.IEnumerator" /> для массива <see cref="T:System.Collections.BitArray" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Item(System.Int32)">
      <summary>Возвращает или задает значение в указанной позиции в массиве <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Значение бита в позиции <paramref name="index" />.</returns>
      <param name="index">Отсчитываемый от нуля индекс получаемого или задаваемого значения. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.BitArray.Count" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Length">
      <summary>Возвращает или задает число элементов в массиве <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Число элементов в массиве <see cref="T:System.Collections.BitArray" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is set to a value that is less than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Not">
      <summary>Обращает все двоичные значения в текущем массиве <see cref="T:System.Collections.BitArray" /> таким образом, чтобы каждому элементу со значением true было присвоено значение false, а каждому элементу со значением false было присвоено значение true.</summary>
      <returns>Текущие экземпляры с обращенными двоичными значениями.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Or(System.Collections.BitArray)">
      <summary>Выполняет битовую операцию логического сложения элементов текущего массива <see cref="T:System.Collections.BitArray" /> с соответствующими элементами указанного массива <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Текущий экземпляр, содержащий результат битовой операции логического сложения элементов текущего массива <see cref="T:System.Collections.BitArray" /> с соответствующими элементами указанного массива <see cref="T:System.Collections.BitArray" />.</returns>
      <param name="value">Массив <see cref="T:System.Collections.BitArray" />, с которым производится битовая операция логического сложения. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Set(System.Int32,System.Boolean)">
      <summary>Задает значение бита в указанной позиции в массиве <see cref="T:System.Collections.BitArray" />.</summary>
      <param name="index">Отсчитываемый от нуля индекс задаваемого бита. </param>
      <param name="value">Логическое значение, которое требуется присвоить биту. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.SetAll(System.Boolean)">
      <summary>Присваивает указанное значение всем битам в массиве <see cref="T:System.Collections.BitArray" />.</summary>
      <param name="value">Логическое значение, которое требуется присвоить всем битам. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.BitArray" /> в массив <see cref="T:System.Array" />, начиная с указанного индекса массива <see cref="T:System.Array" />.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.BitArray" />.Массив <see cref="T:System.Array" /> должен иметь индексацию, начинающуюся с нуля.</param>
      <param name="index">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or- The number of elements in the source <see cref="T:System.Collections.BitArray" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.BitArray" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#Count">
      <summary>Возвращает число элементов в массиве <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Число элементов в массиве <see cref="T:System.Collections.BitArray" />.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, определяющее, является ли доступ к коллекции <see cref="T:System.Collections.BitArray" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.BitArray" /> является синхронизированным (потокобезопасным); в противном случае — false.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к <see cref="T:System.Collections.BitArray" />.</returns>
    </member>
    <member name="M:System.Collections.BitArray.Xor(System.Collections.BitArray)">
      <summary>Выполняет битовую операцию исключающего логического сложения элементов текущего массива <see cref="T:System.Collections.BitArray" /> с соответствующими элементами указанного массива <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Текущий экземпляр, содержащий результат битовой операции исключающего логического сложения элементов текущего массива <see cref="T:System.Collections.BitArray" /> с соответствующими элементами указанного массива <see cref="T:System.Collections.BitArray" />. </returns>
      <param name="value">Массив <see cref="T:System.Collections.BitArray" />, с которым производится битовая операция исключающего логического сложения. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Collections.StructuralComparisons">
      <summary>Предоставляет объекты для выполнения структурного сравнения двух объектов коллекции.</summary>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralComparer">
      <summary>Получает предопределенный объект, выполняющий структурное сравнение двух объектов.</summary>
      <returns>Предопределенный объект, который служит для структурного сравнения двух объектов коллекции.</returns>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralEqualityComparer">
      <summary>Получает предопределенный объект, выполняющий проверку структурного равенства двух объектов.</summary>
      <returns>Предопределенный объект, который служит для проверки структурного равенства двух объектов коллекции.</returns>
    </member>
    <member name="T:System.Collections.Generic.Comparer`1">
      <summary>Представляет базовый класс для реализаций универсального интерфейса <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <typeparam name="T">Тип объектов для сравнения.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.Comparer`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Compare(`0,`0)">
      <summary>При переопределении в производном классе выполняет сравнение двух объектов одного типа и возвращает значение, показывающее, что один объект меньше или больше другого объекта или равен ему.</summary>
      <returns>Знаковое целое число, которое определяет относительные значения параметров <paramref name="x" /> и <paramref name="y" />, как показано в следующей таблице.Значение Значение Меньше нуля Значение параметра <paramref name="x" /> меньше значения параметра <paramref name="y" />.Zero Значения параметров <paramref name="x" /> и <paramref name="y" /> равны.Больше нуля. Значение <paramref name="x" /> больше значения <paramref name="y" />.</returns>
      <param name="x">Первый сравниваемый объект.</param>
      <param name="y">Второй сравниваемый объект.</param>
      <exception cref="T:System.ArgumentException">Тип <paramref name="T" /> не реализует универсальный интерфейс <see cref="T:System.IComparable`1" /> или интерфейс <see cref="T:System.IComparable" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Create(System.Comparison{`0})">
      <summary>Создает результат сравнения с использованием указанного сравнения.</summary>
      <returns>Новый компаратор.</returns>
      <param name="comparison">Используемое сравнение.</param>
    </member>
    <member name="P:System.Collections.Generic.Comparer`1.Default">
      <summary>Возвращает компаратор по умолчанию для порядка сортировки, соответствующий типу, указанному универсальным аргументом.</summary>
      <returns>Объект, который наследует <see cref="T:System.Collections.Generic.Comparer`1" /> и служит компаратором, используемым при установлении порядка сортировки для типа <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Сравнение двух объектов и возврат значения, указывающего, является ли один объект меньшим, равным или большим другого.</summary>
      <returns>Знаковое целое число, которое определяет относительные значения параметров <paramref name="x" /> и <paramref name="y" />, как показано в следующей таблице.Значение Значение Меньше нуляЗначение параметра <paramref name="x" /> меньше значения параметра <paramref name="y" />.ZeroЗначения параметров <paramref name="x" /> и <paramref name="y" /> равны.Больше нуля.Значение <paramref name="x" /> больше значения <paramref name="y" />.</returns>
      <param name="x">Первый сравниваемый объект.</param>
      <param name="y">Второй сравниваемый объект.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="x" /> или <paramref name="y" /> имеет тип, который не может быть приведен к типу <paramref name="T" />.-или-<paramref name="x" /> и <paramref name="y" /> не реализуют универсальный интерфейс <see cref="T:System.IComparable`1" /> или интерфейс <see cref="T:System.IComparable" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2">
      <summary>Представляет коллекцию ключей и значений.Чтобы просмотреть исходный код .NET Framework для этого типа, см. Reference Source.</summary>
      <typeparam name="TKey">Тип ключей в словаре.</typeparam>
      <typeparam name="TValue">Тип значений в словаре.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.Dictionary`2" />, имеющий начальную емкость по умолчанию и использующий функцию сравнения по умолчанию, проверяющую равенство для данного типа ключа.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.Dictionary`2" />, который содержит элементы, скопированные из заданной коллекции <see cref="T:System.Collections.Generic.IDictionary`2" />, и использует функцию сравнения по умолчанию, проверяющую равенство для данного типа ключа.</summary>
      <param name="dictionary">Объект <see cref="T:System.Collections.Generic.IDictionary`2" />, элементы которого копируются в новый объект <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="dictionary" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="dictionary" /> содержит один или более повторяющихся ключей.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.Dictionary`2" />, который содержит элементы, скопированные из заданной коллекции <see cref="T:System.Collections.Generic.IDictionary`2" />, и использует указанный интерфейс <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="dictionary">Объект <see cref="T:System.Collections.Generic.IDictionary`2" />, элементы которого копируются в новый объект <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <param name="comparer">Реализация <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, которую следует использовать при сравнении ключей, или null, если для данного типа ключа должна использоваться реализация <see cref="T:System.Collections.Generic.EqualityComparer`1" /> по умолчанию.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="dictionary" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="dictionary" /> содержит один или более повторяющихся ключей.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.Dictionary`2" /> начальной емкостью по умолчанию, использующий указанную функцию сравнения <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="comparer">Реализация <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, которую следует использовать при сравнении ключей, или null, если для данного типа ключа должна использоваться реализация <see cref="T:System.Collections.Generic.EqualityComparer`1" /> по умолчанию.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32)">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.Dictionary`2" />, имеющий заданную начальную емкость и использующий функцию сравнения по умолчанию, проверяющую равенство для данного типа ключа.</summary>
      <param name="capacity">Начальное количество элементов, которое может содержать коллекция <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="capacity" /> меньше 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.Dictionary`2" /> заданной начальной емкостью, использующий указанную функцию сравнения <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="capacity">Начальное количество элементов, которое может содержать коллекция <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <param name="comparer">Реализация <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, которую следует использовать при сравнении ключей, или null, если для данного типа ключа должна использоваться реализация <see cref="T:System.Collections.Generic.EqualityComparer`1" /> по умолчанию.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="capacity" /> меньше 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Add(`0,`1)">
      <summary>Добавляет указанные ключ и значение в словарь.</summary>
      <param name="key">Ключ добавляемого элемента.</param>
      <param name="value">Добавляемое значение элемента.Для ссылочных типов допускается значение null.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Элемент с таким ключом уже существует в <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Clear">
      <summary>Удаляет все ключи и значения из словаря <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Comparer">
      <summary>Возвращает интерфейс <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для установления равенства ключей словаря. </summary>
      <returns>Реализация универсального интерфейса <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемая для установления равенства ключей текущего словаря <see cref="T:System.Collections.Generic.Dictionary`2" /> и для задания хэш-кодов ключей.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsKey(`0)">
      <summary>Определяет, содержится ли указанный ключ в словаре <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>true, если <see cref="T:System.Collections.Generic.Dictionary`2" /> содержит элемент с указанным ключом, в противном случае — false.</returns>
      <param name="key">Ключ, который требуется найти в <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsValue(`1)">
      <summary>Определяет, содержит ли коллекция <see cref="T:System.Collections.Generic.Dictionary`2" /> указанное значение.</summary>
      <returns>Значение true, если <see cref="T:System.Collections.Generic.Dictionary`2" /> содержит элемент с указанным значением; в противном случае — значение false.</returns>
      <param name="value">Значение, которое требуется найти в словаре <see cref="T:System.Collections.Generic.Dictionary`2" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Count">
      <summary>Возвращает число пар "ключ-значение", содержащихся в словаре <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Число пар "ключ-значение", содержащихся в словаре <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов списка <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Структура <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> для <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Item(`0)">
      <summary>Возвращает или задает значение, связанное с указанным ключом.</summary>
      <returns>Значение, связанное с указанным ключом.Если указанный ключ не найден, операция получения создает исключение <see cref="T:System.Collections.Generic.KeyNotFoundException" />, а операция задания значения создает новый элемент с указанным ключом.</returns>
      <param name="key">Ключ, значение которого требуется получить или задать.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Свойство извлечено, параметр <paramref name="key" /> не существует в коллекции.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Keys">
      <summary>Возвращает коллекцию, содержащую ключи из словаря <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, содержащий ключи из <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Remove(`0)">
      <summary>Удаляет значение с указанным ключом из <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Значение true, если элемент был найден и удален; в противном случае — значение false.Этот метод возвращает значение false, если ключ <paramref name="key" /> не удалось найти в <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
      <param name="key">Ключ элемента, который требуется удалить.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Добавляет указанное значение в коллекцию <see cref="T:System.Collections.Generic.ICollection`1" /> с указанным ключом.</summary>
      <param name="keyValuePair">Структура <see cref="T:System.Collections.Generic.KeyValuePair`2" />, представляющая ключ и значение, которые необходимо добавить в словарь <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">Ключ в паре <paramref name="keyValuePair" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Элемент с таким ключом уже существует в <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Определяет, содержит ли коллекция <see cref="T:System.Collections.Generic.ICollection`1" /> указанные ключ и значение.</summary>
      <returns>Значение true, если параметр <paramref name="keyValuePair" /> найден в коллекции <see cref="T:System.Collections.Generic.ICollection`1" />; в противном случае — значение false.</returns>
      <param name="keyValuePair">Структура <see cref="T:System.Collections.Generic.KeyValuePair`2" />, которую необходимо найти в коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.Generic.ICollection`1" /> в массив типа <see cref="T:System.Collections.Generic.KeyValuePair`2" />, начиная с указанного индекса массива.</summary>
      <param name="array">Одномерный массив типа <see cref="T:System.Collections.Generic.KeyValuePair`2" />, в который копируются элементы <see cref="T:System.Collections.Generic.KeyValuePair`2" /> из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.Индекс в массиве должен начинаться с нуля.</param>
      <param name="index">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Количество элементов в исходной коллекции <see cref="T:System.Collections.Generic.ICollection`1" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Возвращает значение, указывающее, доступен ли словарь только для чтения.</summary>
      <returns>Значение true, если интерфейс <see cref="T:System.Collections.Generic.ICollection`1" /> доступен только для чтения; в противном случае — значение false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Dictionary`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Удаляет ключ и значение из словаря.</summary>
      <returns>Значение true, если ключ и значение, представленные структурой <paramref name="keyValuePair" />, успешно найдены и удалены, в противном случае — false.Этот метод возвращает значение false, если ключ <paramref name="keyValuePair" /> не удалось найти в <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">Структура <see cref="T:System.Collections.Generic.KeyValuePair`2" />, представляющая ключ и значение, которые требуется удалить из словаря <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Возвращает интерфейс <see cref="T:System.Collections.Generic.ICollection`1" />, содержащий ключи <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.Generic.ICollection`1" /> типа <paramref name="TKey" />, содержащая ключи из словаря <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Возвращает интерфейс <see cref="T:System.Collections.Generic.ICollection`1" />, содержащий значения из <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.Generic.ICollection`1" /> типа <paramref name="TValue" />, содержащая значения из словаря <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий перебор элементов в коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.Generic.IEnumerator`1" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Возвращает коллекцию, содержащую ключи из словаря <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</summary>
      <returns>Коллекция, содержащая ключи <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Возвращает коллекцию, содержащую значения <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</summary>
      <returns>Коллекция, содержащая значения <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.Generic.ICollection`1" /> в массив, начиная с указанного индекса массива.</summary>
      <param name="array">Одномерный массив, в который копируются элементы из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.Индекс в массиве должен начинаться с нуля.</param>
      <param name="index">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Массив <paramref name="array" /> является многомерным.-или-В массиве <paramref name="array" /> не используется индексация с нуля.-или-Количество элементов в исходной коллекции <see cref="T:System.Collections.Generic.ICollection`1" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.-или-Тип исходной коллекции <see cref="T:System.Collections.Generic.ICollection`1" /> нельзя автоматически привести к типу массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Возвращает значение, показывающее, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.ICollection" /> является синхронизированным (потокобезопасным); в противном случае — false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Dictionary`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Возвращает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к <see cref="T:System.Collections.ICollection" />. </returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Добавляет указанные ключ и значение в словарь.</summary>
      <param name="key">Объект, который используется в качестве ключа.</param>
      <param name="value">Объект, который используется в качестве значения.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="key" /> имеет тип, который не может быть назначен типу ключа <paramref name="TKey" /> объекта <see cref="T:System.Collections.Generic.Dictionary`2" />.-или-Тип параметра <paramref name="value" /> не допускает присваивание типу значений <paramref name="TValue" /> словаря <see cref="T:System.Collections.Generic.Dictionary`2" />.-или-Значение с таким ключом уже существует в <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Определяет, содержится ли элемент с указанным ключом в <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>true, если <see cref="T:System.Collections.IDictionary" /> содержит элемент с указанным ключом, в противном случае — false.</returns>
      <param name="key">Ключ, который требуется найти в <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Возвращает перечислитель <see cref="T:System.Collections.IDictionaryEnumerator" /> для словаря <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.IDictionaryEnumerator" /> для <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Возвращает значение, указывающее, имеет ли список <see cref="T:System.Collections.IDictionary" /> фиксированный размер.</summary>
      <returns>Значение true, если словарь <see cref="T:System.Collections.IDictionary" /> имеет фиксированный размер; в противном случае — значение false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Dictionary`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Возвращает значение, указывающее, является ли объект <see cref="T:System.Collections.IDictionary" /> доступным только для чтения.</summary>
      <returns>Значение true, если интерфейс <see cref="T:System.Collections.IDictionary" /> доступен только для чтения; в противном случае — значение false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Dictionary`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Возвращает или задает значение с указанным ключом.</summary>
      <returns>Значение, связанное с указанным ключом, или null, если <paramref name="key" /> отсутствует в словаре или <paramref name="key" /> имеет тип, который не допускает присваивание типу ключа <paramref name="TKey" /> коллекции <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
      <param name="key">Ключ значения, которое необходимо получить.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Назначаемое значение и параметр <paramref name="key" /> типа, который не может быть назначен типу ключа <paramref name="TKey" /> объекта <see cref="T:System.Collections.Generic.Dictionary`2" />.-или-Значение присваивается, при этом тип параметра <paramref name="value" /> не допускает присваивание типу значения <paramref name="TValue" /> объекту <see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Keys">
      <summary>Возвращает интерфейс <see cref="T:System.Collections.ICollection" />, содержащий ключи <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.ICollection" />, содержащий ключи из <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Удаляет элемент с указанным ключом из <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Ключ элемента, который требуется удалить.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Values">
      <summary>Возвращает интерфейс <see cref="T:System.Collections.ICollection" />, содержащий значения из <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.ICollection" />, содержащая значения из словаря <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий перебор элементов в коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.TryGetValue(`0,`1@)">
      <summary>Возвращает значение, связанное с заданным ключом.</summary>
      <returns>true, если <see cref="T:System.Collections.Generic.Dictionary`2" /> содержит элемент с указанным ключом, в противном случае — false.</returns>
      <param name="key">Ключ значения, которое необходимо получить.</param>
      <param name="value">Этот метод возвращает значение, связанное с указанным ключом, если он найден; в противном случае — значение по умолчанию для типа параметра <paramref name="value" />.Этот параметр передается неинициализированным.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Values">
      <summary>Возвращает коллекцию, содержащую значения из словаря <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, содержащая значения из словаря <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.Enumerator">
      <summary>Выполняет перечисление элементов <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент в <see cref="T:System.Collections.Generic.Dictionary`2" /> находится в текущей позиции перечислителя.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, используемые объектом <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент словаря, находящийся в текущей позиции перечислителя, в виде объекта <see cref="T:System.Collections.DictionaryEntry" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Возвращает ключ элемента, расположенного в текущей позиции перечислителя.</summary>
      <returns>Ключ элемента словаря, расположенного в текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Возвращает значение элемента, расположенного в текущей позиции перечислителя.</summary>
      <returns>Значение элемента словаря, расположенного в текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, находящийся в текущей позиции перечислителя, в виде объекта <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection">
      <summary>Представляет коллекцию ключей в словаре <see cref="T:System.Collections.Generic.Dictionary`2" />.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, который отражает ключи в указанном словаре <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <param name="dictionary">Словарь <see cref="T:System.Collections.Generic.Dictionary`2" />, ключи которого отражены в новой коллекции <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="dictionary" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> в существующий одномерный массив <see cref="T:System.Array" />, начиная с указанного значения индекса массива.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из коллекции <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.Индексация в массиве <see cref="T:System.Array" /> должна начинаться с нуля.</param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.</exception>
      <exception cref="T:System.ArgumentException">Количество элементов в исходной коллекции <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Count">
      <summary>Возвращает число элементов, содержащихся в коллекции <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>Количество элементов в <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.Получение значения данного свойства является операцией порядка сложности O(1).</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов коллекции <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" /> для <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Добавляет элемент в коллекцию <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Объект, добавляемый в коллекцию <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Возникает всегда.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Удаляет все элементы из интерфейса <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Возникает всегда.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Определяет, содержит ли коллекция <see cref="T:System.Collections.Generic.ICollection`1" /> указанное значение.</summary>
      <returns>Значение true, если объект <paramref name="item" /> найден в <see cref="T:System.Collections.Generic.ICollection`1" />; в противном случае — значение false.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Получает значение, указывающее, является ли объект <see cref="T:System.Collections.Generic.ICollection`1" /> доступным только для чтения.</summary>
      <returns>Значение true, если <see cref="T:System.Collections.Generic.ICollection`1" /> доступна только для чтения; в противном случае — значение false.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> это свойство всегда возвращает значение true.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Удаляет первый экземпляр указанного объекта из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Значение true, если объект <paramref name="item" /> успешно удален из <see cref="T:System.Collections.Generic.ICollection`1" />, в противном случае — значение false.Этот метод также возвращает false, если объект <paramref name="item" /> не найден в исходной коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Объект, который необходимо удалить из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Возникает всегда.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор коллекции.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.Generic.IEnumerator`1" />, который может использоваться для перебора коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.ICollection" /> в массив <see cref="T:System.Array" />, начиная с указанного индекса массива <see cref="T:System.Array" />.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.ICollection" />.Индексация в массиве <see cref="T:System.Array" /> должна начинаться с нуля.</param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.</exception>
      <exception cref="T:System.ArgumentException">Массив <paramref name="array" /> является многомерным.– или –В массиве <paramref name="array" /> не используется индексация с нуля.– или –Количество элементов в исходной коллекции <see cref="T:System.Collections.ICollection" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.– или –Тип исходной коллекции <see cref="T:System.Collections.ICollection" /> нельзя автоматически привести к типу массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, позволяющее определить, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.ICollection" /> является синхронизированным (потокобезопасным); в противном случае — false.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к <see cref="T:System.Collections.ICollection" />.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> это свойство всегда возвращает текущий экземпляр.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator">
      <summary>Выполняет перечисление элементов коллекции <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Current">
      <summary>Возвращает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, находящийся в текущей позиции перечислителя.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, занятые модулем <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу коллекции <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Возвращает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, соответствующий текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection">
      <summary>Представляет коллекцию значений словаря <see cref="T:System.Collections.Generic.Dictionary`2" />.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, который отражает значения в указанном словаре <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <param name="dictionary">Словарь <see cref="T:System.Collections.Generic.Dictionary`2" />, значения которого отражены в новой коллекции <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="dictionary" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> в существующий одномерный массив <see cref="T:System.Array" />, начиная с указанного значения индекса массива.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.Индексация в массиве <see cref="T:System.Array" /> должна начинаться с нуля.</param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.</exception>
      <exception cref="T:System.ArgumentException">Количество элементов в исходной коллекции <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Count">
      <summary>Получает число элементов, содержащихся в коллекции <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>Число элементов, содержащихся в <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов коллекции <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" /> для <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Добавляет элемент в коллекцию <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Объект, добавляемый в коллекцию <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Возникает всегда.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Удаляет все элементы из интерфейса <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Возникает всегда.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Определяет, содержит ли коллекция <see cref="T:System.Collections.Generic.ICollection`1" /> указанное значение.</summary>
      <returns>Значение true, если объект <paramref name="item" /> найден в <see cref="T:System.Collections.Generic.ICollection`1" />; в противном случае — значение false.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Получает значение, указывающее, является ли объект <see cref="T:System.Collections.Generic.ICollection`1" /> доступным только для чтения.</summary>
      <returns>Значение true, если <see cref="T:System.Collections.Generic.ICollection`1" /> доступна только для чтения; в противном случае — значение false.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> это свойство всегда возвращает значение true.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Удаляет первый экземпляр указанного объекта из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Значение true, если объект <paramref name="item" /> успешно удален из <see cref="T:System.Collections.Generic.ICollection`1" />, в противном случае — значение false.Этот метод также возвращает false, если объект <paramref name="item" /> не найден в исходной коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Объект, который необходимо удалить из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Возникает всегда.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор коллекции.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.Generic.IEnumerator`1" />, который может использоваться для перебора коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.ICollection" /> в массив <see cref="T:System.Array" />, начиная с указанного индекса массива <see cref="T:System.Array" />.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.ICollection" />.Индексация в массиве <see cref="T:System.Array" /> должна начинаться с нуля.</param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.</exception>
      <exception cref="T:System.ArgumentException">Массив <paramref name="array" /> является многомерным.– или –В массиве <paramref name="array" /> не используется индексация с нуля.– или –Количество элементов в исходной коллекции <see cref="T:System.Collections.ICollection" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.– или –Тип исходной коллекции <see cref="T:System.Collections.ICollection" /> нельзя автоматически привести к типу массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, позволяющее определить, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.ICollection" /> является синхронизированным (потокобезопасным); в противном случае — false.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к <see cref="T:System.Collections.ICollection" />.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> это свойство всегда возвращает текущий экземпляр.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator">
      <summary>Выполняет перечисление элементов <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент в <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> находится в текущей позиции перечислителя.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, используемые объектом <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу объекта <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, соответствующий текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Collections.Generic.EqualityComparer`1">
      <summary>Предоставляет базовый класс для реализаций универсального интерфейса <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <typeparam name="T">Тип объектов для сравнения.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.EqualityComparer`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.EqualityComparer`1.Default">
      <summary>Возвращает компаратор для типа, указанного универсальным аргументом.</summary>
      <returns>Экземпляр по умолчанию класса <see cref="T:System.Collections.Generic.EqualityComparer`1" /> для типа <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.Equals(`0,`0)">
      <summary>При переопределении в производном классе определяет, равны ли два объекта типа <paramref name="T" />.</summary>
      <returns>true, если указанные объекты равны; в противном случае — false.</returns>
      <param name="x">Первый из сравниваемых объектов.</param>
      <param name="y">Второй из сравниваемых объектов.</param>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.GetHashCode(`0)">
      <summary>При переопределении в производном классе служит хэш-функцией для указанного объекта для алгоритмов хэширования и структур данных, например хэш-таблицы.</summary>
      <returns>Хэш-код указанного объекта.</returns>
      <param name="obj">Объект, для которого нужно получить хэш-код.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Определяет, равны ли два указанных объекта.</summary>
      <returns>true, если указанные объекты равны; в противном случае — false.</returns>
      <param name="x">Первый из сравниваемых объектов.</param>
      <param name="y">Второй из сравниваемых объектов.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> or <paramref name="y" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Возвращает хэш-код указанного объекта.</summary>
      <returns>Хэш-код указанного объекта.</returns>
      <param name="obj">Объект <see cref="T:System.Object" />, для которого необходимо вернуть хэш-код.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.-or-<paramref name="obj" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1">
      <summary>Представляет набор значений.Чтобы просмотреть исходный код .NET Framework для этого типа, см. Reference Source.</summary>
      <typeparam name="T">Тип элементов в коллекции.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor">
      <summary>Инициализирует пустой экземпляр класса <see cref="T:System.Collections.Generic.HashSet`1" />, который использует функцию сравнения по умолчанию для типа набора.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.HashSet`1" />, который использует функцию сравнения по умолчанию для типа набора, содержит элементы, скопированные из указанной коллекции, и обладает емкостью, достаточной для того, чтобы вместить количество скопированных элементов.</summary>
      <param name="collection">Коллекция, элементы которой копируются в новый набор.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="collection" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.HashSet`1" />, который использует заданную функцию сравнения для типа набора, содержит элементы, скопированные из указанной коллекции, и обладает емкостью, достаточной для того, чтобы вместить количество скопированных элементов.</summary>
      <param name="collection">Коллекция, элементы которой копируются в новый набор.</param>
      <param name="comparer">Реализация <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, которую следует использовать при сравнении значений в наборе, или значение null, чтобы использовать реализацию <see cref="T:System.Collections.Generic.EqualityComparer`1" /> по умолчанию для типа набора.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="collection" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Инициализирует пустой экземпляр класса <see cref="T:System.Collections.Generic.HashSet`1" />, который использует указанную функцию сравнения для типа набора.</summary>
      <param name="comparer">Реализация <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, которую следует использовать при сравнении значений в наборе, или значение null, чтобы использовать реализацию <see cref="T:System.Collections.Generic.EqualityComparer`1" /> по умолчанию для типа набора.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Add(`0)">
      <summary>Добавляет указанный элемент в набор.</summary>
      <returns>Значение true, если элемент добавлен в объект <see cref="T:System.Collections.Generic.HashSet`1" />; значение false, если элемент уже присутствует в нем.</returns>
      <param name="item">Элемент, добавляемый в набор.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Clear">
      <summary>Удаляет все элементы из объекта <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Comparer">
      <summary>Возвращает объект <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для определения равенства значений в наборе.</summary>
      <returns>Объект <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для определения равенства значений в наборе.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Contains(`0)">
      <summary>Определяет, содержит ли объект <see cref="T:System.Collections.Generic.HashSet`1" /> указанный элемент.</summary>
      <returns>Значение true, если объект <see cref="T:System.Collections.Generic.HashSet`1" /> содержит указанный элемент; в противном случае — значение false.</returns>
      <param name="item">Элемент, который нужно найти в объекте <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[])">
      <summary>Копирует элементы объекта <see cref="T:System.Collections.Generic.HashSet`1" /> в массив.</summary>
      <param name="array">Одномерный массив, являющийся назначением элементов, копируемых из объекта <see cref="T:System.Collections.Generic.HashSet`1" />.Индекс в массиве должен начинаться с нуля.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="array" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32)">
      <summary>Копирует элементы объекта <see cref="T:System.Collections.Generic.HashSet`1" /> в массив, начиная с указанного индекса массива.</summary>
      <param name="array">Одномерный массив, являющийся назначением элементов, копируемых из объекта <see cref="T:System.Collections.Generic.HashSet`1" />.Индекс в массиве должен начинаться с нуля.</param>
      <param name="arrayIndex">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="arrayIndex" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Значение <paramref name="arrayIndex" /> больше длины массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Копирует указанное количество элементов объекта <see cref="T:System.Collections.Generic.HashSet`1" /> в массив, начиная с указанного индекса массива.</summary>
      <param name="array">Одномерный массив, являющийся назначением элементов, копируемых из объекта <see cref="T:System.Collections.Generic.HashSet`1" />.Индекс в массиве должен начинаться с нуля.</param>
      <param name="arrayIndex">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <param name="count">Число элементов, копируемых в массив <paramref name="array" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="arrayIndex" /> меньше 0.-или-Значение параметра <paramref name="count" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Значение <paramref name="arrayIndex" /> больше длины массива назначения <paramref name="array" />.-или-Значение <paramref name="count" /> превышает доступное место, начиная с индекса <paramref name="index" /> и до конца массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Count">
      <summary>Возвращает число элементов, содержащихся в наборе.</summary>
      <returns>Число элементов, содержащихся в наборе.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Удаляет все элементы в указанной коллекции из текущего объекта <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <param name="other">Коллекция элементов, удаляемая из объекта <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="other" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий итерацию элементов объекта <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>Объект <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> для объекта <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Изменяет текущий объект <see cref="T:System.Collections.Generic.HashSet`1" /> так, чтобы он содержал только элементы, которые имеются в этом объекте и в указанной коллекции.</summary>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="other" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, является ли объект <see cref="T:System.Collections.Generic.HashSet`1" /> строгим подмножеством указанной коллекции.</summary>
      <returns>Значение true, если объект <see cref="T:System.Collections.Generic.HashSet`1" /> является строгим подмножеством объекта <paramref name="other" />; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="other" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, является ли объект <see cref="T:System.Collections.Generic.HashSet`1" /> строгим супермножеством указанной коллекции.</summary>
      <returns>Значение true, если объект <see cref="T:System.Collections.Generic.HashSet`1" /> является строгим супермножеством <paramref name="other" />; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.HashSet`1" />. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="other" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, является ли объект <see cref="T:System.Collections.Generic.HashSet`1" /> подмножеством указанной коллекции.</summary>
      <returns>Значение true, если объект <see cref="T:System.Collections.Generic.HashSet`1" /> является подмножеством <paramref name="other" />; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="other" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, является ли объект <see cref="T:System.Collections.Generic.HashSet`1" /> супермножеством указанной коллекции.</summary>
      <returns>Значение true, если объект <see cref="T:System.Collections.Generic.HashSet`1" /> является супермножеством <paramref name="other" />; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="other" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, имеются ли общие элементы в текущем объекте <see cref="T:System.Collections.Generic.HashSet`1" /> и в указанной коллекции.</summary>
      <returns>Значение true, если объект <see cref="T:System.Collections.Generic.HashSet`1" /> и коллекция <paramref name="other" /> имеют по крайней мере один общий элемент; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="other" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Remove(`0)">
      <summary>Удаляет указанный элемент из объекта <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>Значение true, если элемент был найден и удален; в противном случае — значение false.Этот метод возвращает значение false, если элемент <paramref name="item" /> не удалось найти в объекте <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
      <param name="item">Подлежащий удалению элемент.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Удаляет все элементы, удовлетворяющие условиям, которые были определены указанным предикатом из коллекции <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>Количество элементов, удаленных из коллекции <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия удаления элемента.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="match" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, содержат ли объект <see cref="T:System.Collections.Generic.HashSet`1" /> и указанная коллекция одни и те же элементы.</summary>
      <returns>Значение true, если объект <see cref="T:System.Collections.Generic.HashSet`1" /> равен <paramref name="other" />; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="other" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Изменяет текущий объект <see cref="T:System.Collections.Generic.HashSet`1" /> так, чтобы он содержал только элементы, которые имеются либо в этом объекте, либо в указанной коллекции, но не одновременно в них обоих.</summary>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="other" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Добавляет элемент в объект <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="item">Объект, добавляемый в объект <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Объект <see cref="T:System.Collections.Generic.ICollection`1" /> доступен только для чтения.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Возвращает значение, указывающее, доступна ли коллекция только для чтения.</summary>
      <returns>Значение true, если коллекция доступна только для чтения; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.Generic.IEnumerator`1" />, который используется для прохода по коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который используется для прохода по коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.TrimExcess">
      <summary>Задает емкость объекта <see cref="T:System.Collections.Generic.HashSet`1" /> равной фактическому числу элементов, которые в нем содержатся, округленному в большую сторону ближайшего значения, зависящего от реализации.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Изменяет текущий объект <see cref="T:System.Collections.Generic.HashSet`1" /> так, чтобы он содержал все элементы, имеющиеся в нем или в указанной коллекции либо как в нем, так и в указанной коллекции.</summary>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="other" /> имеет значение null.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1.Enumerator">
      <summary>Выполняет перечисление элементов объекта <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции <see cref="T:System.Collections.Generic.HashSet`1" />, соответствующий текущей позиции перечислителя.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, используемые объектом <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу коллекции <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, находящийся в текущей позиции перечислителя, в виде объекта <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1">
      <summary>Представляет двусвязный список.</summary>
      <typeparam name="T">Задает тип элемента в связанном списке.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor">
      <summary>Инициализирует новый экземпляр пустого класса <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.LinkedList`1" />, который содержит элементы, скопированные из заданного класса <see cref="T:System.Collections.IEnumerable" />, и обладает достаточной емкостью для того, чтобы вместить число скопированных элементов. </summary>
      <param name="collection">Объект <see cref="T:System.Collections.IEnumerable" />, элементы которого копируются в новый объект <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="collection" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Добавляет заданный новый узел после заданного существующего узла в <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, после которого следует вставить узел <paramref name="newNode" />.</param>
      <param name="newNode">Новый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, добавляемый к <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="node" /> имеет значение null.– или –Параметр <paramref name="newNode" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Параметр <paramref name="node" /> не находится в текущем объекте <see cref="T:System.Collections.Generic.LinkedList`1" />.– или –Параметр <paramref name="newNode" /> принадлежит другому объекту <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Добавляет новый узел, содержащий заданное значение, после заданного существующего узла в <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Новый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, содержащий <paramref name="value" />.</returns>
      <param name="node">узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, после которого следует вставить новый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, содержащий <paramref name="value" />.</param>
      <param name="value">Значение, добавляемое в <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="node" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Параметр <paramref name="node" /> не находится в текущем объекте <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Добавляет заданный новый узел перед заданным существующим узлом в <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, перед которым следует вставить узел <paramref name="newNode" />.</param>
      <param name="newNode">Новый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, добавляемый к <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="node" /> имеет значение null.– или –Параметр <paramref name="newNode" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Параметр <paramref name="node" /> не находится в текущем объекте <see cref="T:System.Collections.Generic.LinkedList`1" />.– или –Параметр <paramref name="newNode" /> принадлежит другому объекту <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Добавляет новый узел, содержащий заданное значение, перед заданным существующим узлом в <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Новый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, содержащий <paramref name="value" />.</returns>
      <param name="node">узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, перед которым следует вставить новый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, содержащий <paramref name="value" />.</param>
      <param name="value">Значение, добавляемое в <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="node" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Параметр <paramref name="node" /> не находится в текущем объекте <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Добавляет заданный новый узел в начало <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Новый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, добавляемый в начало <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="node" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Параметр <paramref name="node" /> принадлежит другому объекту <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(`0)">
      <summary>Добавляет новый узел, содержащий заданное значение, в начало <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Новый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, содержащий <paramref name="value" />.</returns>
      <param name="value">Значение, добавляемое в начало <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Добавляет заданный новый узел в конец объекта <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Новый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, добавляемый в конец <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="node" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Параметр <paramref name="node" /> принадлежит другому объекту <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(`0)">
      <summary>Добавляет новый узел, содержащий заданное значение, в конец <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Новый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, содержащий <paramref name="value" />.</returns>
      <param name="value">Значение, добавляемое в конец <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Clear">
      <summary>Удаляет все узлы из <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Contains(`0)">
      <summary>Определяет, принадлежит ли значение объекту <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Значение true, если значение <paramref name="value" /> найдено в <see cref="T:System.Collections.Generic.LinkedList`1" />; в противном случае — значение false.</returns>
      <param name="value">Значение, которое требуется найти в <see cref="T:System.Collections.Generic.LinkedList`1" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.CopyTo(`0[],System.Int32)">
      <summary>Копирует целый массив <see cref="T:System.Collections.Generic.LinkedList`1" /> в совместимый одномерный массив <see cref="T:System.Array" />, начиная с заданного индекса целевого массива.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы <see cref="T:System.Collections.Generic.LinkedList`1" />.Индексация в массиве <see cref="T:System.Array" /> должна начинаться с нуля.</param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.</exception>
      <exception cref="T:System.ArgumentException">Количество элементов в исходной коллекции <see cref="T:System.Collections.Generic.LinkedList`1" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Count">
      <summary>Получает число узлов, которое в действительности хранится в <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Число узлов, которое в действительности хранится в <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Find(`0)">
      <summary>Находит первый узел, содержащий указанное значение.</summary>
      <returns>Первый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, содержащий указанное значение, если узел найден; в противном случае — значение null.</returns>
      <param name="value">Значение, которое требуется найти в <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.FindLast(`0)">
      <summary>Находит последний узел, содержащий указанное значение.</summary>
      <returns>Последний узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, содержащий указанное значение, если узел найден; в противном случае — значение null.</returns>
      <param name="value">Значение, которое требуется найти в <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.First">
      <summary>Получает первый узел объекта <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Первый узел <see cref="T:System.Collections.Generic.LinkedListNode`1" /> объекта <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Объект <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" /> для <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Last">
      <summary>Получает последний узел объекта <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Последний узел <see cref="T:System.Collections.Generic.LinkedListNode`1" /> объекта <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Удаляет заданный узел из объекта <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Объект <see cref="T:System.Collections.Generic.LinkedListNode`1" />, удаляемый из списка <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="node" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Параметр <paramref name="node" /> не находится в текущем объекте <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(`0)">
      <summary>Удаляет первое вхождение заданного значения из <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Значение true, если элемент, содержащий <paramref name="value" />, успешно удален; в противном случае — значение false.  Этот метод также возвращает значение false, если параметр <paramref name="value" /> не найден в исходном объекте <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
      <param name="value">Значение, которое необходимо удалить из <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveFirst">
      <summary>Удаляет узел в начале <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Collections.Generic.LinkedList`1" /> является пустым.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveLast">
      <summary>Удаляет узел в конце <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Collections.Generic.LinkedList`1" /> является пустым.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Добавляет элемент в конец <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="value">Значение, добавляемое в конец <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Получает значение, указывающее, является ли объект <see cref="T:System.Collections.Generic.ICollection`1" /> доступным только для чтения.</summary>
      <returns>Значение true, если <see cref="T:System.Collections.Generic.ICollection`1" /> доступна только для чтения; в противном случае — значение false.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.LinkedList`1" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор коллекции.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.Generic.IEnumerator`1" />, который может использоваться для перебора коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.ICollection" /> в массив <see cref="T:System.Array" />, начиная с указанного индекса массива <see cref="T:System.Array" />.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.ICollection" />.Индексация в массиве <see cref="T:System.Array" /> должна начинаться с нуля.</param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.</exception>
      <exception cref="T:System.ArgumentException">Массив <paramref name="array" /> является многомерным.– или –В массиве <paramref name="array" /> не используется индексация с нуля.– или –Количество элементов в исходной коллекции <see cref="T:System.Collections.ICollection" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.– или –Тип исходной коллекции <see cref="T:System.Collections.ICollection" /> нельзя автоматически привести к типу массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, позволяющее определить, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.ICollection" /> является синхронизированным (потокобезопасным); в противном случае — false.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.LinkedList`1" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к <see cref="T:System.Collections.ICollection" />.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.LinkedList`1" /> это свойство всегда возвращает текущий экземпляр.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий перебор элементов связанного списка как коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который может использоваться для перебора элементов связанного списка как коллекции.</returns>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1.Enumerator">
      <summary>Выполняет перечисление элементов коллекции <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции <see cref="T:System.Collections.Generic.LinkedList`1" />, находящийся в текущей позиции перечислителя.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, используемые объектом <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу коллекции <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, соответствующий текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.Этот класс не наследуется.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedListNode`1">
      <summary>Предоставляет узел в <see cref="T:System.Collections.Generic.LinkedList`1" />.Этот класс не наследуется.</summary>
      <typeparam name="T">Задает тип элемента в связанном списке.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedListNode`1.#ctor(`0)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.LinkedListNode`1" /> содержащего указанное значение.</summary>
      <param name="value">Значение, которое должно содержаться в <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.List">
      <summary>Получает коллекцию <see cref="T:System.Collections.Generic.LinkedList`1" />, к которой принадлежит узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</summary>
      <returns>Ссылка на коллекцию <see cref="T:System.Collections.Generic.LinkedList`1" />, к которой принадлежит узел <see cref="T:System.Collections.Generic.LinkedListNode`1" />, или значение null, если узел <see cref="T:System.Collections.Generic.LinkedListNode`1" /> не имеет ссылок.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Next">
      <summary>Получает следующий узел в коллекции <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Ссылка на следующий узел в коллекции <see cref="T:System.Collections.Generic.LinkedList`1" /> или значение null, если текущий узел является последним элементом (свойство <see cref="P:System.Collections.Generic.LinkedList`1.Last" />) коллекции <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Previous">
      <summary>Получает предыдущий узел в коллекции <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Ссылка на предыдущий узел в коллекции <see cref="T:System.Collections.Generic.LinkedList`1" /> или значение null, если текущий узел является первым элементом (свойство <see cref="P:System.Collections.Generic.LinkedList`1.First" />) коллекции <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Value">
      <summary>Возвращает значение, содержащееся в узле.</summary>
      <returns>Значение, содержащееся в узле.</returns>
    </member>
    <member name="T:System.Collections.Generic.List`1">
      <summary>Представляет строго типизированный список объектов, доступных по индексу.Поддерживает методы для поиска по списку, выполнения сортировки и других операций со списками.Чтобы просмотреть исходный код .NET Framework для этого типа, см. ссылки на источник.</summary>
      <typeparam name="T">Тип элементов в списке.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.List`1" /> с начальной емкостью по умолчанию.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Collections.Generic.List`1" />, который содержит элементы, скопированные из указанной коллекции, и имеет емкость, достаточную для размещения всех скопированных элементов.</summary>
      <param name="collection">Коллекция, элементы которой копируются в новый список.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Int32)">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.List`1" /> с указанной начальной емкостью.</summary>
      <param name="capacity">Число элементов, которые может изначально вместить новый список.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="capacity" /> меньше 0. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Add(`0)">
      <summary>Добавляет объект в конец коллекции <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="item">Объект, добавляемый в конец коллекции <see cref="T:System.Collections.Generic.List`1" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Добавляет элементы указанной коллекции в конец списка <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="collection">Коллекция, элементы которой добавляются в конец списка <see cref="T:System.Collections.Generic.List`1" />.Коллекция не может быть задана значением null, но может содержать элементы null, если тип <paramref name="T" /> является ссылочным типом.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.AsReadOnly">
      <summary>Возвращает для текущей коллекции оболочку <see cref="T:System.Collections.Generic.IList`1" />, доступную только для чтения.</summary>
      <returns>Коллекция <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />, которая служит оболочкой только для чтения по отношению к текущему списку <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>Выполняет поиск элемента в диапазоне элементов отсортированного списка <see cref="T:System.Collections.Generic.List`1" />, используя указанный компаратор, и возвращает индекс элемента, отсчитываемый от нуля.</summary>
      <returns>Отсчитываемый от нуля индекс элемента <paramref name="item" /> в отсортированном списке <see cref="T:System.Collections.Generic.List`1" />, если элемент <paramref name="item" /> найден; в противном случае — отрицательное число, которое является поразрядным дополнением индекса следующего элемента, большего, чем <paramref name="item" />, или, если большего элемента не существует, поразрядным дополнением значения <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="index">Отсчитываемый от нуля индекс начала диапазона поиска.</param>
      <param name="count">Длина диапазона поиска.</param>
      <param name="item">Искомый объект.Для ссылочных типов допускается значение null.</param>
      <param name="comparer">Реализация <see cref="T:System.Collections.Generic.IComparer`1" />, которую следует использовать при сравнении элементов, или null, если должен использоваться компаратор по умолчанию <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.-или-Значение параметра <paramref name="count" /> меньше 0. </exception>
      <exception cref="T:System.ArgumentException">Параметры <paramref name="index" /> и <paramref name="count" /> определяют недопустимый диапазон в списке <see cref="T:System.Collections.Generic.List`1" />.</exception>
      <exception cref="T:System.InvalidOperationException">В качестве <paramref name="comparer" /> задано null, и компаратор по умолчанию <see cref="P:System.Collections.Generic.Comparer`1.Default" /> не может найти реализацию универсального интерфейса <see cref="T:System.IComparable`1" /> или интерфейса <see cref="T:System.IComparable" /> для типа <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0)">
      <summary>Выполняет поиск элемента по всему отсортированному списку <see cref="T:System.Collections.Generic.List`1" />, используя компаратор по умолчанию, и возвращает индекс элемента, отсчитываемый от нуля.</summary>
      <returns>Отсчитываемый от нуля индекс элемента <paramref name="item" /> в отсортированном списке <see cref="T:System.Collections.Generic.List`1" />, если элемент <paramref name="item" /> найден; в противном случае — отрицательное число, которое является поразрядным дополнением индекса следующего элемента, большего, чем <paramref name="item" />, или, если большего элемента не существует, поразрядным дополнением значения <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">Искомый объект.Для ссылочных типов допускается значение null.</param>
      <exception cref="T:System.InvalidOperationException">Компаратор по умолчанию <see cref="P:System.Collections.Generic.Comparer`1.Default" /> не может найти реализацию универсального интерфейса <see cref="T:System.IComparable`1" /> или интерфейса <see cref="T:System.IComparable" /> для типа <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>Выполняет поиск элемента по всему отсортированному списку <see cref="T:System.Collections.Generic.List`1" />, используя указанный компаратор, и возвращает индекс элемента, отсчитываемый от нуля.</summary>
      <returns>Отсчитываемый от нуля индекс элемента <paramref name="item" /> в отсортированном списке <see cref="T:System.Collections.Generic.List`1" />, если элемент <paramref name="item" /> найден; в противном случае — отрицательное число, которое является поразрядным дополнением индекса следующего элемента, большего, чем <paramref name="item" />, или, если большего элемента не существует, поразрядным дополнением значения <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">Искомый объект.Для ссылочных типов допускается значение null.</param>
      <param name="comparer">Реализация интерфейса <see cref="T:System.Collections.Generic.IComparer`1" />, которая используется при сравнении элементов.-или-null, если требуется использовать компаратор по умолчанию <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.InvalidOperationException">В качестве <paramref name="comparer" /> задано null, и компаратор по умолчанию <see cref="P:System.Collections.Generic.Comparer`1.Default" /> не может найти реализацию универсального интерфейса <see cref="T:System.IComparable`1" /> или интерфейса <see cref="T:System.IComparable" /> для типа <paramref name="T" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Capacity">
      <summary>Возвращает или задает общее число элементов, которые может вместить внутренняя структура данных без изменения размера.</summary>
      <returns>Число элементов, которые может вместить коллекция <see cref="T:System.Collections.Generic.List`1" />, прежде чем потребуется изменить ее размер.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Свойству <see cref="P:System.Collections.Generic.List`1.Capacity" /> присваивается значение, меньшее значения <see cref="P:System.Collections.Generic.List`1.Count" />. </exception>
      <exception cref="T:System.OutOfMemoryException">Недостаточно свободной памяти в системе.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Clear">
      <summary>Удаляет все элементы из коллекции <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Contains(`0)">
      <summary>Определяет, входит ли элемент в состав <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Значение true, если параметр <paramref name="item" /> найден в коллекции <see cref="T:System.Collections.Generic.List`1" />; в противном случае — значение false.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.List`1" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>Копирует диапазон элементов из списка <see cref="T:System.Collections.Generic.List`1" /> в совместимый одномерный массив, начиная с указанного индекса конечного массива.</summary>
      <param name="index">Отсчитываемый от нуля индекс исходного списка <see cref="T:System.Collections.Generic.List`1" />, с которого начинается копирование.</param>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.Generic.List`1" />.Массив <see cref="T:System.Array" /> должен иметь индексацию, начинающуюся с нуля.</param>
      <param name="arrayIndex">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <param name="count">Число элементов для копирования.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.-или-Значение параметра <paramref name="arrayIndex" /> меньше 0.-или-Значение параметра <paramref name="count" /> меньше 0. </exception>
      <exception cref="T:System.ArgumentException">Значение параметра <paramref name="index" /> больше или равно значению свойства <see cref="P:System.Collections.Generic.List`1.Count" /> исходного списка <see cref="T:System.Collections.Generic.List`1" />.-или-Число элементов, начиная с позиции <paramref name="index" /> и до конца исходного списка <see cref="T:System.Collections.Generic.List`1" />, превышает размер доступного места, начиная с позиции <paramref name="arrayIndex" /> и до конца массива назначения <paramref name="array" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[])">
      <summary>Копирует весь список <see cref="T:System.Collections.Generic.List`1" /> в совместимый одномерный массив, начиная с первого элемента целевого массива.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.Generic.List`1" />.Массив <see cref="T:System.Array" /> должен иметь индексацию, начинающуюся с нуля.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentException">Количество элементов в исходном списке <see cref="T:System.Collections.Generic.List`1" /> больше числа элементов, которое может храниться в конечном массиве <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[],System.Int32)">
      <summary>Копирует <see cref="T:System.Collections.Generic.List`1" /> целиком в совместимый одномерный массив, начиная с указанного индекса конечного массива.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.Generic.List`1" />.Массив <see cref="T:System.Array" /> должен иметь индексацию, начинающуюся с нуля.</param>
      <param name="arrayIndex">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="arrayIndex" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Количество элементов в исходной коллекции <see cref="T:System.Collections.Generic.List`1" /> превышает доступное место, начиная с индекса <paramref name="arrayIndex" /> до конца массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Count">
      <summary>Получает число элементов, содержащихся в интерфейсе <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Число элементов, содержащихся в интерфейсе <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Exists(System.Predicate{`0})">
      <summary>Определяет, содержит ли <see cref="T:System.Collections.Generic.List`1" /> элементы, удовлетворяющие условиям указанного предиката.</summary>
      <returns>true, если <see cref="T:System.Collections.Generic.List`1" /> содержит один или несколько элементов, удовлетворяющих условиям указанного предиката, в противном случае — false.</returns>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия поиска элементов.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Find(System.Predicate{`0})">
      <summary>Выполняет поиск элемента, удовлетворяющего условиям указанного предиката, и возвращает первое найденное вхождение в пределах всего списка <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Первый элемент, удовлетворяющий условиям указанного предиката, если такой элемент найден; в противном случае — значение по умолчанию для типа <paramref name="T" />.</returns>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия поиска элемента.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindAll(System.Predicate{`0})">
      <summary>Извлекает все элементы, удовлетворяющие условиям указанного предиката.</summary>
      <returns>Список <see cref="T:System.Collections.Generic.List`1" />, содержащий все элементы, удовлетворяющие условиям указанного предиката, если такие элементы найдены; в противном случае — пустой список <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия поиска элементов.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Выполняет поиск элемента, удовлетворяющего условиям указанного предиката, и возвращает отсчитываемый от нуля индекс первого вхождения в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, начинающемся с заданного индекса и содержащем указанное число элементов.</summary>
      <returns>Отсчитываемый от нуля индекс первого вхождения элемента, удовлетворяющего условиям предиката <paramref name="match" />, если такой элемент найден; в противном случае — значение –1.</returns>
      <param name="startIndex">Индекс (с нуля) начальной позиции поиска.</param>
      <param name="count">Число элементов в диапазоне, в котором выполняется поиск.</param>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия поиска элемента.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="startIndex" /> находится вне диапазона допустимых индексов для списка <see cref="T:System.Collections.Generic.List`1" />.-или-Значение параметра <paramref name="count" /> меньше 0.-или-Параметры <paramref name="startIndex" /> и <paramref name="count" /> определяют недопустимый раздел списка <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>Выполняет поиск элемента, удовлетворяющего условиям указанного предиката, и возвращает отсчитываемый от нуля индекс первого вхождения в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, начиная с заданного индекса и заканчивая последним элементом.</summary>
      <returns>Отсчитываемый от нуля индекс первого вхождения элемента, удовлетворяющего условиям предиката <paramref name="match" />, если такой элемент найден; в противном случае — значение –1.</returns>
      <param name="startIndex">Индекс (с нуля) начальной позиции поиска.</param>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия поиска элемента.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="startIndex" /> находится вне диапазона допустимых индексов для списка <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Predicate{`0})">
      <summary>Выполняет поиск элемента, удовлетворяющего условиям указанного предиката, и возвращает отсчитываемый от нуля индекс первого найденного вхождения в пределах всего списка <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Отсчитываемый от нуля индекс первого вхождения элемента, удовлетворяющего условиям предиката <paramref name="match" />, если такой элемент найден; в противном случае — значение –1.</returns>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия поиска элемента.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLast(System.Predicate{`0})">
      <summary>Выполняет поиск элемента, удовлетворяющего условиям указанного предиката, и возвращает последнее найденное вхождение в пределах всего списка <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Последний элемент, удовлетворяющий условиям указанного предиката, если такой элемент найден; в противном случае — значение по умолчанию для типа <paramref name="T" />.</returns>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия поиска элемента.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Выполняет поиск элемента, удовлетворяющего условиям указанного предиката, и возвращает отсчитываемый от нуля индекс последнего вхождения в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, содержащем указанное число элементов и заканчивающемся элементом с заданным индексом.</summary>
      <returns>Отсчитываемый от нуля индекс последнего вхождения элемента, удовлетворяющего условиям предиката <paramref name="match" />, если такой элемент найден; в противном случае — значение –1.</returns>
      <param name="startIndex">Индекс (с нуля) начала диапазона поиска в обратном направлении.</param>
      <param name="count">Число элементов в диапазоне, в котором выполняется поиск.</param>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия поиска элемента.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="startIndex" /> находится вне диапазона допустимых индексов для списка <see cref="T:System.Collections.Generic.List`1" />.-или-Значение параметра <paramref name="count" /> меньше 0.-или-Параметры <paramref name="startIndex" /> и <paramref name="count" /> определяют недопустимый раздел списка <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>Выполняет поиск элемента, удовлетворяющего условиям указанного предиката, и возвращает отсчитываемый от нуля индекс последнего вхождения в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, начиная с первого элемента и заканчивая элементом с заданным индексом.</summary>
      <returns>Отсчитываемый от нуля индекс последнего вхождения элемента, удовлетворяющего условиям предиката <paramref name="match" />, если такой элемент найден; в противном случае — значение –1.</returns>
      <param name="startIndex">Индекс (с нуля) начала диапазона поиска в обратном направлении.</param>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия поиска элемента.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="startIndex" /> находится вне диапазона допустимых индексов для списка <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Predicate{`0})">
      <summary>Выполняет поиск элемента, удовлетворяющего условиям указанного предиката, и возвращает отсчитываемый от нуля индекс последнего найденного вхождения в пределах всего списка <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Отсчитываемый от нуля индекс последнего вхождения элемента, удовлетворяющего условиям предиката <paramref name="match" />, если такой элемент найден; в противном случае — значение –1.</returns>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия поиска элемента.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ForEach(System.Action{`0})">
      <summary>Выполняет указанное действие с каждым элементом списка <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="action">Делегат <see cref="T:System.Action`1" />, выполняемый для каждого элемента списка <see cref="T:System.Collections.Generic.List`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов списка <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1.Enumerator" /> для <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetRange(System.Int32,System.Int32)">
      <summary>Создает неполную копию диапазона элементов исходного списка <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Неполная копия диапазона элементов исходного списка <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="index">Отсчитываемый от нуля индекс списка <see cref="T:System.Collections.Generic.List`1" />, с которого начинается диапазон.</param>
      <param name="count">Число элементов в диапазоне.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.-или-Значение параметра <paramref name="count" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Параметры <paramref name="index" /> и <paramref name="count" /> определяют недопустимый диапазон элементов списка <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0)">
      <summary>Осуществляет поиск указанного объекта и возвращает отсчитываемый от нуля индекс первого вхождения, найденного в пределах всего списка <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Индекс (с нуля) первого вхождения параметра <paramref name="item" />, если оно найдено в коллекции <see cref="T:System.Collections.Generic.List`1" />; в противном случае -1.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.List`1" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32)">
      <summary>Осуществляет поиск указанного объекта и возвращает отсчитываемый от нуля индекс первого вхождения в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, начиная с заданного индекса и до последнего элемента.</summary>
      <returns>Отсчитываемый от нуля индекс первого вхождения элемента <paramref name="item" /> в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, начиная с позиции <paramref name="index" /> и до конца списка, если элемент найден; в противном случае — значение –1.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.List`1" />.Для ссылочных типов допускается значение null.</param>
      <param name="index">Индекс (с нуля) начальной позиции поиска.Значение 0 (ноль) действительно в пустом списке.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="index" /> находится вне диапазона допустимых индексов для списка <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32,System.Int32)">
      <summary>Выполняет поиск указанного объекта и возвращает отсчитываемый от нуля индекс первого вхождения в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, начинающемся с заданного индекса и содержащем указанное число элементов.</summary>
      <returns>Отсчитываемый от нуля индекс первого вхождения <paramref name="item" /> в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, который начинается с позиции <paramref name="index" /> и содержит <paramref name="count" /> элементов, если искомый объект найден; в противном случае — значение –1.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.List`1" />.Для ссылочных типов допускается значение null.</param>
      <param name="index">Индекс (с нуля) начальной позиции поиска.Значение 0 (ноль) действительно в пустом списке.</param>
      <param name="count">Число элементов в диапазоне, в котором выполняется поиск.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="index" /> находится вне диапазона допустимых индексов для списка <see cref="T:System.Collections.Generic.List`1" />.-или-Значение параметра <paramref name="count" /> меньше 0.-или-Параметры <paramref name="index" /> и <paramref name="count" /> определяют недопустимый раздел списка <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Insert(System.Int32,`0)">
      <summary>Добавляет элемент в список <see cref="T:System.Collections.Generic.List`1" /> в позиции с указанным индексом.</summary>
      <param name="index">Индекс (с нуля), по которому следует вставить параметр <paramref name="item" />.</param>
      <param name="item">Вставляемый объект.Для ссылочных типов допускается значение null.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.-или-Значение <paramref name="index" /> больше значения <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Вставляет элементы коллекции в список <see cref="T:System.Collections.Generic.List`1" /> в позиции с указанным индексом.</summary>
      <param name="index">Отсчитываемый от нуля индекс места вставки новых элементов.</param>
      <param name="collection">Коллекция, элементы которой следует вставить в список <see cref="T:System.Collections.Generic.List`1" />.Коллекция не может быть задана значением null, но может содержать элементы null, если тип <paramref name="T" /> является ссылочным типом.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.-или-Значение <paramref name="index" /> больше значения <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Item(System.Int32)">
      <summary>Возвращает или задает элемент по указанному индексу.</summary>
      <returns>Элемент, расположенный по указанному индексу.</returns>
      <param name="index">Отсчитываемый от нуля индекс элемента, который требуется возвратить или задать.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.-или-Значение параметра <paramref name="index" /> больше или равно значению свойства <see cref="P:System.Collections.Generic.List`1.Count" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0)">
      <summary>Осуществляет поиск указанного объекта и возвращает отсчитываемый от нуля индекс последнего вхождения, найденного в пределах всего списка <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Отсчитываемый от нуля индекс последнего вхождения <paramref name="item" /> в пределах всего списка <see cref="T:System.Collections.Generic.List`1" />, если элемент найден; в противном случае — значение –1.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.List`1" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32)">
      <summary>Осуществляет поиск указанного объекта и возвращает отсчитываемый от нуля индекс последнего вхождения в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, начиная с первого элемента и до позиции с заданным индексом.</summary>
      <returns>Отсчитываемый от нуля индекс последнего вхождения элемента <paramref name="item" /> в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, начиная с первого элемента и до позиции <paramref name="index" />, если элемент найден; в противном случае — значение –1.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.List`1" />.Для ссылочных типов допускается значение null.</param>
      <param name="index">Индекс (с нуля) начала диапазона поиска в обратном направлении.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="index" /> находится вне диапазона допустимых индексов для списка <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>Выполняет поиск указанного объекта и возвращает отсчитываемый от нуля индекс последнего вхождения в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, содержащем указанное число элементов и заканчивающемся в позиции с указанным индексом.</summary>
      <returns>Отсчитываемый от нуля индекс последнего вхождения <paramref name="item" /> в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" />, состоящем из <paramref name="count" /> элементов и заканчивающемся в позиции <paramref name="index" />, если элемент найден; в противном случае — значение –1.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.List`1" />.Для ссылочных типов допускается значение null.</param>
      <param name="index">Индекс (с нуля) начала диапазона поиска в обратном направлении.</param>
      <param name="count">Число элементов в диапазоне, в котором выполняется поиск.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение <paramref name="index" /> находится вне диапазона допустимых индексов для списка <see cref="T:System.Collections.Generic.List`1" />.-или-Значение параметра <paramref name="count" /> меньше 0.-или-Параметры <paramref name="index" /> и <paramref name="count" /> определяют недопустимый раздел списка <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Remove(`0)">
      <summary>Удаляет первый экземпляр указанного объекта из коллекции <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Значение true, если элемент <paramref name="item" /> успешно удален, в противном случае — значение false.Этот метод также возвращает false, если элемент <paramref name="item" /> не найден в коллекции <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="item">Объект, который необходимо удалить из коллекции <see cref="T:System.Collections.Generic.List`1" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAll(System.Predicate{`0})">
      <summary>Удаляет все элементы, удовлетворяющие условиям указанного предиката.</summary>
      <returns>Число элементов, удаляемых из списка <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия удаления элемента.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAt(System.Int32)">
      <summary>Удаляет элемент списка <see cref="T:System.Collections.Generic.List`1" /> с указанным индексом.</summary>
      <param name="index">Индекс (с нуля) элемента, который требуется удалить.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.-или-Значение параметра <paramref name="index" /> больше или равно значению свойства <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveRange(System.Int32,System.Int32)">
      <summary>Удаляет диапазон элементов из списка <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="index">Отсчитываемый от нуля индекс начала диапазона элементов, которые требуется удалить.</param>
      <param name="count">Число удаляемых элементов.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.-или-Значение параметра <paramref name="count" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Параметры <paramref name="index" /> и <paramref name="count" /> определяют недопустимый диапазон элементов списка <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse">
      <summary>Изменяет порядок элементов во всем списке <see cref="T:System.Collections.Generic.List`1" /> на обратный.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse(System.Int32,System.Int32)">
      <summary>Изменяет порядок элементов в указанном диапазоне.</summary>
      <param name="index">Отсчитываемый от нуля индекс начала диапазона, порядок элементов которого требуется изменить.</param>
      <param name="count">Число элементов в диапазоне, порядок сортировки в котором требуется изменить.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.-или-Значение параметра <paramref name="count" /> меньше 0. </exception>
      <exception cref="T:System.ArgumentException">Параметры <paramref name="index" /> и <paramref name="count" /> определяют недопустимый диапазон элементов списка <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort">
      <summary>Сортирует элементы во всем списке <see cref="T:System.Collections.Generic.List`1" /> с помощью компаратора по умолчанию.</summary>
      <exception cref="T:System.InvalidOperationException">Компаратор по умолчанию <see cref="P:System.Collections.Generic.Comparer`1.Default" /> не может найти реализацию универсального интерфейса <see cref="T:System.IComparable`1" /> или интерфейса <see cref="T:System.IComparable" /> для типа <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>Сортирует элементы во всем списке <see cref="T:System.Collections.Generic.List`1" /> с помощью указанного компаратора.</summary>
      <param name="comparer">Реализация <see cref="T:System.Collections.Generic.IComparer`1" />, которую следует использовать при сравнении элементов, или null, если должен использоваться компаратор по умолчанию <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.InvalidOperationException">В качестве <paramref name="comparer" /> задано null, и компаратор по умолчанию <see cref="P:System.Collections.Generic.Comparer`1.Default" /> не может найти реализацию универсального интерфейса <see cref="T:System.IComparable`1" /> или интерфейса <see cref="T:System.IComparable" /> для типа <paramref name="T" />.</exception>
      <exception cref="T:System.ArgumentException">Реализация <paramref name="comparer" /> вызвала ошибку при сортировке.Например, компаратор <paramref name="comparer" /> мог не возвратить 0 при сравнении элемента с самим собой.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Comparison{`0})">
      <summary>Сортирует элементы во всем списке <see cref="T:System.Collections.Generic.List`1" /> с использованием указанного делегата <see cref="T:System.Comparison`1" />.</summary>
      <param name="comparison">Делегат <see cref="T:System.Comparison`1" />, используемый при сравнении элементов.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" />is null.</exception>
      <exception cref="T:System.ArgumentException">Реализация <paramref name="comparison" /> вызвала ошибку при сортировке.Например, компаратор <paramref name="comparison" /> мог не возвратить 0 при сравнении элемента с самим собой.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Сортирует элементы в диапазоне элементов списка <see cref="T:System.Collections.Generic.List`1" /> с помощью указанного компаратора.</summary>
      <param name="index">Индекс (с нуля) начала диапазона, который требуется отсортировать.</param>
      <param name="count">Длина диапазона сортировки.</param>
      <param name="comparer">Реализация <see cref="T:System.Collections.Generic.IComparer`1" />, которую следует использовать при сравнении элементов, или null, если должен использоваться компаратор по умолчанию <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.-или-Значение параметра <paramref name="count" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Параметры <paramref name="index" /> и <paramref name="count" /> не определяют допустимый диапазон в <see cref="T:System.Collections.Generic.List`1" />.-или-Реализация <paramref name="comparer" /> вызвала ошибку при сортировке.Например, компаратор <paramref name="comparer" /> мог не возвратить 0 при сравнении элемента с самим собой.</exception>
      <exception cref="T:System.InvalidOperationException">В качестве <paramref name="comparer" /> задано null, и компаратор по умолчанию <see cref="P:System.Collections.Generic.Comparer`1.Default" /> не может найти реализацию универсального интерфейса <see cref="T:System.IComparable`1" /> или интерфейса <see cref="T:System.IComparable" /> для типа <paramref name="T" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Получает значение, указывающее, является ли объект <see cref="T:System.Collections.Generic.ICollection`1" /> доступным только для чтения.</summary>
      <returns>trueЕсли <see cref="T:System.Collections.Generic.ICollection`1" /> доступен только для чтения; в противном случае — false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.List`1" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.Generic.IEnumerator`1" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.ICollection" /> в массив <see cref="T:System.Array" />, начиная с указанного индекса массива <see cref="T:System.Array" />.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.ICollection" />.Массив <see cref="T:System.Array" /> должен иметь индексацию, начинающуюся с нуля.</param>
      <param name="arrayIndex">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="arrayIndex" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Массив <paramref name="array" /> является многомерным.-или-В массиве <paramref name="array" /> не используется индексация с нуля.-или-Количество элементов в исходной коллекции <see cref="T:System.Collections.ICollection" /> превышает доступное место, начиная с индекса <paramref name="arrayIndex" /> до конца массива назначения <paramref name="array" />.-или-Тип исходной коллекции <see cref="T:System.Collections.ICollection" /> нельзя автоматически привести к типу массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, показывающее, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.ICollection" /> является синхронизированным (потокобезопасным); в противном случае — false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.List`1" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к <see cref="T:System.Collections.ICollection" />.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.List`1" /> это свойство всегда возвращает текущий экземпляр.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Add(System.Object)">
      <summary>Добавляет элемент в коллекцию <see cref="T:System.Collections.IList" />.</summary>
      <returns>Позиция, на которую вставлен новый элемент.</returns>
      <param name="item">Объект <see cref="T:System.Object" />, добавляемый в список <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">Тип параметра <paramref name="item" /> является недопустимым для списка <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Contains(System.Object)">
      <summary>Определяет, содержит ли коллекция <see cref="T:System.Collections.IList" /> указанное значение.</summary>
      <returns>Значение true, если параметр <paramref name="item" /> найден в коллекции <see cref="T:System.Collections.IList" />; в противном случае — значение false.</returns>
      <param name="item">Объект <see cref="T:System.Object" />, который требуется найти в списке <see cref="T:System.Collections.IList" />.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>Определяет индекс заданного элемента коллекции <see cref="T:System.Collections.IList" />.</summary>
      <returns>Индекс значения <paramref name="item" />, если оно найдено в списке; в противном случае — значение -1.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">Тип параметра <paramref name="item" /> является недопустимым для списка <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Вставляет элемент в коллекцию <see cref="T:System.Collections.IList" /> по указанному индексу.</summary>
      <param name="index">Индекс (с нуля), по которому следует вставить параметр <paramref name="item" />.</param>
      <param name="item">Объект, вставляемый в список <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="index" /> не является допустимым индексом в списке <see cref="T:System.Collections.IList" />. </exception>
      <exception cref="T:System.ArgumentException">Тип параметра <paramref name="item" /> является недопустимым для списка <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsFixedSize">
      <summary>Получает значение, указывающее, имеет ли список <see cref="T:System.Collections.IList" /> фиксированный размер.</summary>
      <returns>Значение true, если список <see cref="T:System.Collections.IList" /> имеет фиксированный размер, в противном случае — значение false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.List`1" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsReadOnly">
      <summary>Получает значение, указывающее, является ли объект <see cref="T:System.Collections.IList" /> доступным только для чтения.</summary>
      <returns>trueЕсли <see cref="T:System.Collections.IList" /> доступен только для чтения; в противном случае — false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.List`1" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#Item(System.Int32)">
      <summary>Возвращает или задает элемент по указанному индексу.</summary>
      <returns>Элемент, расположенный по указанному индексу.</returns>
      <param name="index">Отсчитываемый от нуля индекс элемента, который требуется возвратить или задать.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="index" /> не является допустимым индексом в списке <see cref="T:System.Collections.IList" />.</exception>
      <exception cref="T:System.ArgumentException">Свойство задано, и тип параметра <paramref name="value" /> не может быть назначен <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Remove(System.Object)">
      <summary>Удаляет первый экземпляр указанного объекта из коллекции <see cref="T:System.Collections.IList" />.</summary>
      <param name="item">Объект, который необходимо удалить из коллекции <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">Тип параметра <paramref name="item" /> является недопустимым для списка <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ToArray">
      <summary>Копирует элементы списка <see cref="T:System.Collections.Generic.List`1" /> в новый массив.</summary>
      <returns>Массив, содержащий копии элементов списка <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrimExcess">
      <summary>Задает емкость, равную фактическому числу элементов в списке <see cref="T:System.Collections.Generic.List`1" />, если это число меньше порогового значения.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrueForAll(System.Predicate{`0})">
      <summary>Определяет, все ли элементы списка <see cref="T:System.Collections.Generic.List`1" /> удовлетворяют условиям указанного предиката.</summary>
      <returns>true, если каждый элемент списка <see cref="T:System.Collections.Generic.List`1" /> удовлетворяет условиям заданного предиката, в противном случае — false.Если в списке нет элементов, возвращается true.</returns>
      <param name="match">Делегат <see cref="T:System.Predicate`1" />, определяющий условия, проверяемые для элементов.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="T:System.Collections.Generic.List`1.Enumerator">
      <summary>Выполняет перечисление элементов коллекции <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции <see cref="T:System.Collections.Generic.List`1" />, соответствующий текущей позиции перечислителя.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, используемые перечислителем <see cref="T:System.Collections.Generic.List`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу коллекции <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции <see cref="T:System.Collections.Generic.List`1" />, соответствующий текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Collections.Generic.Queue`1">
      <summary>Представляет коллекцию объектов, основанную на принципе "первым поступил — первым обслужен".</summary>
      <typeparam name="T">Задает тип элементов в очереди.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.Queue`1" /> с начальной емкостью по умолчанию.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Collections.Generic.Queue`1" />, который содержит элементы, скопированные из указанной коллекции, и имеет емкость, достаточную для размещения всех скопированных элементов.</summary>
      <param name="collection">Коллекция, элементы которой копируются в новую коллекцию <see cref="T:System.Collections.Generic.Queue`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Int32)">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.Queue`1" /> с указанной начальной емкостью.</summary>
      <param name="capacity">Начальное количество элементов, которое может содержать коллекция <see cref="T:System.Collections.Generic.Queue`1" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Clear">
      <summary>Удаляет все объекты из <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Contains(`0)">
      <summary>Определяет, входит ли элемент в состав <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>Значение true, если параметр <paramref name="item" /> найден в коллекции <see cref="T:System.Collections.Generic.Queue`1" />; в противном случае — значение false.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.Queue`1" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.CopyTo(`0[],System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.Generic.Queue`1" /> в существующий одномерный массив <see cref="T:System.Array" />, начиная с указанного значения индекса массива.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.Generic.Queue`1" />.Массив <see cref="T:System.Array" /> должен иметь индексацию, начинающуюся с нуля.</param>
      <param name="arrayIndex">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Queue`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Count">
      <summary>Получает число элементов, содержащихся в интерфейсе <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>Число элементов, содержащихся в интерфейсе <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Dequeue">
      <summary>Удаляет и возвращает объект, находящийся в начале <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>Объект, удаленный из начала коллекции <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enqueue(`0)">
      <summary>Добавляет объект в конец коллекции <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <param name="item">Объект, добавляемый в коллекцию <see cref="T:System.Collections.Generic.Queue`1" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов списка <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1.Enumerator" /> для <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Peek">
      <summary>Возвращает объект, находящийся в начале <see cref="T:System.Collections.Generic.Queue`1" />, но не удаляет его.</summary>
      <returns>Объект, находящийся в начале <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.Generic.IEnumerator`1" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.ICollection" /> в массив <see cref="T:System.Array" />, начиная с указанного индекса массива <see cref="T:System.Array" />.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.ICollection" />.Массив <see cref="T:System.Array" /> должен иметь индексацию, начинающуюся с нуля.</param>
      <param name="index">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, показывающее, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.ICollection" /> является синхронизированным (потокобезопасным); в противном случае — false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Queue`1" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к <see cref="T:System.Collections.ICollection" />.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Queue`1" /> это свойство всегда возвращает текущий экземпляр.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.ToArray">
      <summary>Копирует элементы <see cref="T:System.Collections.Generic.Queue`1" /> в новый массив.</summary>
      <returns>Новый массив, содержащий элементы, скопированные из <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.TrimExcess">
      <summary>Устанавливает емкость равной фактическому количеству элементов в <see cref="T:System.Collections.Generic.Queue`1" />, если это количество составляет менее 90 процентов текущей емкости.</summary>
    </member>
    <member name="T:System.Collections.Generic.Queue`1.Enumerator">
      <summary>Выполняет перечисление элементов коллекции <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции <see cref="T:System.Collections.Generic.Queue`1" />, соответствующий текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, используемые перечислителем <see cref="T:System.Collections.Generic.Queue`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, соответствующий текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2">
      <summary>Представляет коллекцию пар ключ/значение, упорядоченных по ключу. </summary>
      <typeparam name="TKey">Тип ключей в словаре.</typeparam>
      <typeparam name="TValue">Тип значений в словаре.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.SortedDictionary`2" />, использующий реализацию <see cref="T:System.Collections.Generic.IComparer`1" /> по умолчанию для данного типа ключа.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.SortedDictionary`2" />, использующий для сравнения ключей указанную реализацию <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="comparer">Реализация <see cref="T:System.Collections.Generic.IComparer`1" />, которую следует использовать при сравнении ключей, или null, если для данного типа ключа должна использоваться реализация <see cref="T:System.Collections.Generic.Comparer`1" /> по умолчанию.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Collections.Generic.SortedDictionary`2" />, который содержит элементы, скопированные из указанного словаря <see cref="T:System.Collections.Generic.IDictionary`2" />, и использует для данного типа ключа реализацию <see cref="T:System.Collections.Generic.IComparer`1" /> по умолчанию.</summary>
      <param name="dictionary">Объект <see cref="T:System.Collections.Generic.IDictionary`2" />, элементы которого копируются в новый объект <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="dictionary" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="dictionary" /> содержит один или более повторяющихся ключей.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Collections.Generic.SortedDictionary`2" />, который содержит элементы, скопированные из указанного словаря <see cref="T:System.Collections.Generic.IDictionary`2" />, и использует для сравнения ключей указанную реализацию <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="dictionary">Объект <see cref="T:System.Collections.Generic.IDictionary`2" />, элементы которого копируются в новый объект <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <param name="comparer">Реализация <see cref="T:System.Collections.Generic.IComparer`1" />, которую следует использовать при сравнении ключей, или null, если для данного типа ключа должна использоваться реализация <see cref="T:System.Collections.Generic.Comparer`1" /> по умолчанию.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="dictionary" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="dictionary" /> содержит один или более повторяющихся ключей.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Add(`0,`1)">
      <summary>Добавляет элемент с указанными ключом и значением в словарь <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <param name="key">Ключ добавляемого элемента.</param>
      <param name="value">Добавляемое значение элемента.Для ссылочных типов допускается значение null.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Элемент с таким ключом уже существует в <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Clear">
      <summary>Удаляет из коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2" /> все элементы.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Comparer">
      <summary>Возвращает компаратор <see cref="T:System.Collections.Generic.IComparer`1" />, использованный для упорядочения элементов словаря <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Компаратор <see cref="T:System.Collections.Generic.IComparer`1" />, использованный для упорядочения элементов словаря <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsKey(`0)">
      <summary>Определяет, содержится ли элемент с указанным ключом в коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>true, если <see cref="T:System.Collections.Generic.SortedDictionary`2" /> содержит элемент с указанным ключом, в противном случае — false.</returns>
      <param name="key">Ключ для поиска в <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsValue(`1)">
      <summary>Определяет, содержится ли элемент с указанным значением в словаре <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Значение true, если <see cref="T:System.Collections.Generic.SortedDictionary`2" /> содержит элемент с указанным значением, в противном случае — значение false.</returns>
      <param name="value">Значение, которое требуется найти в словаре <see cref="T:System.Collections.Generic.SortedDictionary`2" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Копирует элементы словаря <see cref="T:System.Collections.Generic.SortedDictionary`2" /> в указанный массив структур <see cref="T:System.Collections.Generic.KeyValuePair`2" />, начиная с заданной позиции индекса.</summary>
      <param name="array">Одномерный массив структур <see cref="T:System.Collections.Generic.KeyValuePair`2" />, в который копируются элементы из текущего словаря <see cref="T:System.Collections.Generic.SortedDictionary`2" />. Индексация массива должна начинаться с нуля.</param>
      <param name="index">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Количество элементов в исходной коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Count">
      <summary>Возвращает число пар "ключ-значение", содержащихся в словаре <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Число пар "ключ-значение", содержащихся в словаре <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.GetEnumerator">
      <summary>Возвращает перечислитель для просмотра коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" /> для <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Item(`0)">
      <summary>Возвращает или задает значение, связанное с указанным ключом.</summary>
      <returns>Значение, связанное с указанным ключом.Если указанный ключ не найден, операция получения генерирует исключение <see cref="T:System.Collections.Generic.KeyNotFoundException" />, а операция задания значения создает новый элемент с указанным ключом.</returns>
      <param name="key">Ключ, значение которого требуется получить или задать.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Свойство извлечено, параметр <paramref name="key" /> не существует в коллекции.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Keys">
      <summary>Получает коллекцию, содержащую ключи из словаря <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, содержащая ключи из словаря <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Remove(`0)">
      <summary>Удаляет элемент с указанным ключом из объекта <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Значение true, если элемент успешно удален, в противном случае — значение false.Этот метод также возвращает значение false, если элемент <paramref name="key" /> не найден в <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
      <param name="key">Ключ элемента, который требуется удалить.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Добавляет элемент в коллекцию <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="keyValuePair">Структура <see cref="T:System.Collections.Generic.KeyValuePair`2" />, которую необходимо добавить в коллекцию <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="keyValuePair" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Элемент с таким ключом уже существует в <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Определяет, содержит ли коллекция <see cref="T:System.Collections.Generic.ICollection`1" /> указанные ключ и значение.</summary>
      <returns>Значение true, если элемент <paramref name="keyValuePair" /> найден в коллекции <see cref="T:System.Collections.Generic.ICollection`1" />, в противном случае — значение false.</returns>
      <param name="keyValuePair">Структура <see cref="T:System.Collections.Generic.KeyValuePair`2" />, которую требуется найти в коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Возвращает значение, указывающее, доступен ли список <see cref="T:System.Collections.Generic.ICollection`1" /> только для чтения.</summary>
      <returns>Значение true, если коллекция <see cref="T:System.Collections.Generic.ICollection`1" /> доступна только для чтения, в противном случае — значение false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedDictionary`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Удаляет первое вхождение указанного элемента из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>Значение true, если объект <paramref name="keyValuePair" /> успешно удален из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />, в противном случае — значение false.Этот метод также возвращает false, если элемент <paramref name="keyValuePair" /> не найден в коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">Структура <see cref="T:System.Collections.Generic.KeyValuePair`2" />, удаляемая из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Получает интерфейс <see cref="T:System.Collections.Generic.ICollection`1" />, содержащий ключи <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.Generic.ICollection`1" />, содержащая ключи из словаря <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Получает интерфейс <see cref="T:System.Collections.Generic.ICollection`1" />, содержащий значения из <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.Generic.ICollection`1" />, содержащая значения из словаря <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который можно использовать для последовательного просмотра коллекции.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Возвращает коллекцию, содержащую ключи в<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>Коллекция, содержащая ключи в<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Возвращает коллекцию, содержащую значения в<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>Коллекция, содержащая значения в<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.Generic.ICollection`1" /> в массив, начиная с указанного индекса массива.</summary>
      <param name="array">Одномерный массив для приема элементов, копируемых из <see cref="T:System.Collections.Generic.ICollection`1" />.Индекс в массиве должен начинаться с нуля.</param>
      <param name="index">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Массив <paramref name="array" /> является многомерным.-или-В массиве <paramref name="array" /> не используется индексация с нуля.-или-Количество элементов в исходной коллекции <see cref="T:System.Collections.Generic.ICollection`1" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.-или-Тип исходной коллекции <see cref="T:System.Collections.Generic.ICollection`1" /> нельзя автоматически привести к типу массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, показывающее, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.ICollection" /> является синхронизированным (потокобезопасным); в противном случае — false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedDictionary`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Возвращает объект, который используется для синхронизации доступа к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который используется для синхронизации доступа к коллекции <see cref="T:System.Collections.ICollection" />. </returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Добавляет элемент с указанными ключом и значением в <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Объект, используемый в качестве ключа добавляемого элемента.</param>
      <param name="value">Объект, используемый в качестве значения добавляемого элемента.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="key" /> имеет тип, который не может быть назначен типу ключа <paramref name="TKey" /> объекта <see cref="T:System.Collections.IDictionary" />.-или-Параметр <paramref name="value" /> имеет тип, который не может быть назначен типу значения <paramref name="TValue" /> интерфейса <see cref="T:System.Collections.IDictionary" />.-или-Элемент с таким ключом уже существует в <see cref="T:System.Collections.IDictionary" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Определяет, содержится ли элемент с указанным ключом в коллекции <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Значение true, если в <see cref="T:System.Collections.IDictionary" /> содержится элемент с данным ключом; в противном случае — значение false.</returns>
      <param name="key">Ключ для поиска в <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Возвращает объект <see cref="T:System.Collections.IDictionaryEnumerator" /> для <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.IDictionaryEnumerator" /> для коллекции <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Возвращает значение, показывающее, имеет ли список <see cref="T:System.Collections.IDictionary" /> фиксированный размер.</summary>
      <returns>Значение true, если список <see cref="T:System.Collections.IDictionary" /> имеет фиксированный размер, в противном случае — значение false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedDictionary`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Возвращает значение, указывающее, доступен ли список <see cref="T:System.Collections.IDictionary" /> только для чтения.</summary>
      <returns>Значение true, если коллекция <see cref="T:System.Collections.IDictionary" /> доступна только для чтения, в противном случае — значение false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedDictionary`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Возвращает или задает элемент с указанным ключом.</summary>
      <returns>Элемент с указанным ключом, или null, если <paramref name="key" /> отсутствует в словаре или тип <paramref name="key" /> не допускает присваивания типу ключа <paramref name="TKey" /> словаря <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
      <param name="key">Ключ элемента, который требуется получить.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Назначаемое значение и параметр <paramref name="key" /> типа, который не может быть назначен типу ключа <paramref name="TKey" /> объекта <see cref="T:System.Collections.Generic.SortedDictionary`2" />.-или-Значение присваивается, при этом тип параметра <paramref name="value" /> не допускает присваивание типу значения <paramref name="TValue" /> объекту <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Keys">
      <summary>Получает интерфейс <see cref="T:System.Collections.ICollection" />, содержащий ключи <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.ICollection" />, содержащая ключи из словаря <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Удаляет элемент с указанным ключом из объекта <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Ключ элемента, который требуется удалить.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Values">
      <summary>Получает интерфейс <see cref="T:System.Collections.ICollection" />, содержащий значения из <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.ICollection" />, содержащая значения из словаря <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий перебор элементов в коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.Generic.IEnumerator`1" />, который можно использовать для последовательного просмотра коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.TryGetValue(`0,`1@)">
      <summary>Получает значение, связанное с заданным ключом.</summary>
      <returns>true, если <see cref="T:System.Collections.Generic.SortedDictionary`2" /> содержит элемент с указанным ключом, в противном случае — false.</returns>
      <param name="key">Ключ значения, которое необходимо получить.</param>
      <param name="value">Возвращаемое значение, связанное с указанном ключом, если он найден; в противном случае — значение по умолчанию для данного типа параметра <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Values">
      <summary>Получает коллекцию, содержащую значения в объекте <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, содержащая значения из словаря <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.Enumerator">
      <summary>Выполняет перечисление элементов коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2" />, находящийся в текущей позиции перечислителя.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, используемые объектом <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Возвращает элемент, расположенный в текущей позиции перечислителя, в виде структуры <see cref="T:System.Collections.DictionaryEntry" />.</summary>
      <returns>Элемент коллекции, расположенный в текущей позиции словаря и имеющий вид структуры <see cref="T:System.Collections.DictionaryEntry" />.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Возвращает ключ элемента, расположенного в текущей позиции перечислителя.</summary>
      <returns>Ключ элемента коллекции, расположенного в текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Возвращает значение элемента, расположенного в текущей позиции перечислителя.</summary>
      <returns>Значение элемента коллекции, расположенного в текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, соответствующий текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection">
      <summary>Представляет коллекцию ключей в словаре <see cref="T:System.Collections.Generic.SortedDictionary`2" />.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, который отражает ключи в указанном словаре <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <param name="dictionary">Словарь <see cref="T:System.Collections.Generic.SortedDictionary`2" />, ключи которого отражены в новой коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="dictionary" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Копирует элементы <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> в существующий одномерный массив, начиная с указанного значения индекса массива.</summary>
      <param name="array">Одномерный массив, в который копируются элементы из коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.Индексация в массиве должна вестись с нуля.</param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Количество элементов в исходной коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Count">
      <summary>Получает число элементов, содержащихся в интерфейсе <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>Число элементов, содержащихся в интерфейсе <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов списка <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>Структура <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> для словаря <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Добавляет элемент в коллекцию <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Объект, добавляемый в коллекцию <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Всегда выдается; коллекция доступна только для чтения.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Удаляет все элементы из интерфейса <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Всегда выдается; коллекция доступна только для чтения.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Определяет, содержит ли коллекция <see cref="T:System.Collections.Generic.ICollection`1" /> указанное значение.</summary>
      <returns>Значение true, если объект <paramref name="item" /> найден в <see cref="T:System.Collections.Generic.ICollection`1" />; в противном случае — значение false.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Получает значение, указывающее, является ли объект <see cref="T:System.Collections.Generic.ICollection`1" /> доступным только для чтения.</summary>
      <returns>Значение true, если <see cref="T:System.Collections.Generic.ICollection`1" /> доступна только для чтения; в противном случае — значение false.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Удаляет первый экземпляр указанного объекта из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Значение true, если элемент <paramref name="item" /> успешно удален из <see cref="T:System.Collections.Generic.ICollection`1" />, в противном случае — значение false.Этот метод также возвращает значение false, если элемент <paramref name="item" /> не найден в <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Объект, который необходимо удалить из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Всегда выдается; коллекция доступна только для чтения.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий перебор элементов коллекции.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.Generic.IEnumerator`1" />, который может использоваться для перебора коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы <see cref="T:System.Collections.ICollection" /> в массив, начиная с определенного индекса массива.</summary>
      <param name="array">Одномерный массив, в который копируются элементы из коллекции <see cref="T:System.Collections.ICollection" />.Индексация в массиве должна вестись с нуля.</param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Массив <paramref name="array" /> является многомерным.– или –В массиве <paramref name="array" /> не используется индексация с нуля.– или –Количество элементов в исходной коллекции <see cref="T:System.Collections.ICollection" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.– или –Тип исходной коллекции <see cref="T:System.Collections.ICollection" /> нельзя автоматически привести к типу массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, позволяющее определить, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.ICollection" /> является синхронизированным (потокобезопасным); в противном случае — false.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к <see cref="T:System.Collections.ICollection" />.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> это свойство всегда возвращает текущий экземпляр.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий перебор элементов коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator">
      <summary>Выполняет перечисление элементов коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, находящийся в текущей позиции перечислителя.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, используемые объектом <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, соответствующий текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection">
      <summary>Представляет коллекцию значений словаря <see cref="T:System.Collections.Generic.SortedDictionary`2" />.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, который отражает значения в указанном словаре <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <param name="dictionary">Словарь <see cref="T:System.Collections.Generic.SortedDictionary`2" />, значения которого отражены в новой коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="dictionary" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> в существующий одномерный массив, начиная с указанного значения индекса массива.</summary>
      <param name="array">Одномерный массив, в который копируются элементы из коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.Индексация в массиве должна вестись с нуля.</param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Количество элементов в исходной коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Count">
      <summary>Получает число элементов, содержащихся в интерфейсе <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>Число элементов, содержащихся в интерфейсе <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов списка <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>Структура <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> для словаря <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Добавляет элемент в коллекцию <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Объект, добавляемый в коллекцию <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Всегда выдается; коллекция доступна только для чтения.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Удаляет все элементы из интерфейса <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Всегда выдается; коллекция доступна только для чтения.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Определяет, содержит ли коллекция <see cref="T:System.Collections.Generic.ICollection`1" /> указанное значение.</summary>
      <returns>Значение true, если объект <paramref name="item" /> найден в <see cref="T:System.Collections.Generic.ICollection`1" />; в противном случае — значение false.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Получает значение, указывающее, является ли объект <see cref="T:System.Collections.Generic.ICollection`1" /> доступным только для чтения.</summary>
      <returns>Значение true, если <see cref="T:System.Collections.Generic.ICollection`1" /> доступна только для чтения; в противном случае — значение false.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Удаляет первый экземпляр указанного объекта из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Значение true, если элемент <paramref name="item" /> успешно удален из <see cref="T:System.Collections.Generic.ICollection`1" />, в противном случае — значение false.Этот метод также возвращает значение false, если элемент <paramref name="item" /> не найден в <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Объект, который необходимо удалить из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Всегда выдается; коллекция доступна только для чтения.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Удаляет первый экземпляр указанного объекта из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.  Данная реализация всегда выдает исключение <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Значение true, если элемент <paramref name="item" /> успешно удален из <see cref="T:System.Collections.Generic.ICollection`1" />, в противном случае — значение false.Этот метод также возвращает значение false, если элемент <paramref name="item" /> не найден в <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <exception cref="T:System.NotSupportedException">Всегда выдается; коллекция доступна только для чтения.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы <see cref="T:System.Collections.ICollection" /> в массив, начиная с определенного индекса массива.</summary>
      <param name="array">Одномерный массив, в который копируются элементы из коллекции <see cref="T:System.Collections.ICollection" />.Индексация в массиве должна вестись с нуля.</param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше 0.</exception>
      <exception cref="T:System.ArgumentException">Массив <paramref name="array" /> является многомерным.– или –В массиве <paramref name="array" /> не используется индексация с нуля.– или –Количество элементов в исходной коллекции <see cref="T:System.Collections.ICollection" /> превышает доступное место, начиная с индекса <paramref name="index" /> до конца массива назначения <paramref name="array" />.– или –Тип исходной коллекции <see cref="T:System.Collections.ICollection" /> нельзя автоматически привести к типу массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, позволяющее определить, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.ICollection" /> является синхронизированным (потокобезопасным); в противном случае — false.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к <see cref="T:System.Collections.ICollection" />.  В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> это свойство всегда возвращает текущий экземпляр.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий перебор элементов коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator">
      <summary>Выполняет перечисление элементов коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, находящийся в текущей позиции перечислителя.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, используемые объектом <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу коллекции <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, соответствующий текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedList`2">
      <summary>Представляет коллекцию пар ключ/значение, упорядоченных по ключу на основе реализации <see cref="T:System.Collections.Generic.IComparer`1" />. </summary>
      <typeparam name="TKey">Тип ключей в коллекции.</typeparam>
      <typeparam name="TValue">Тип значений в коллекции.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.SortedList`2" /> с начальной емкостью по умолчанию, использующий интерфейс <see cref="T:System.Collections.Generic.IComparer`1" /> по умолчанию.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.SortedList`2" /> с начальной емкостью по умолчанию, использующий указанный компаратор <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="comparer">Реализация интерфейса <see cref="T:System.Collections.Generic.IComparer`1" />, используемая при сравнении ключей.-или-Значение null для использования класса <see cref="T:System.Collections.Generic.Comparer`1" /> по умолчанию для данного типа ключа.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Collections.Generic.SortedList`2" />, содержащий элементы, скопированные из указанного класса <see cref="T:System.Collections.Generic.IDictionary`2" />, обладающий емкостью, достаточной для того, чтобы вместить количество скопированных элементов, и использующий интерфейс <see cref="T:System.Collections.Generic.IComparer`1" /> по умолчанию.</summary>
      <param name="dictionary">Объект <see cref="T:System.Collections.Generic.IDictionary`2" />, элементы которого копируются в новый объект <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="dictionary" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="dictionary" /> содержит один или более повторяющихся ключей.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Collections.Generic.SortedList`2" />, содержащий элементы, скопированные из указанного класса <see cref="T:System.Collections.Generic.IDictionary`2" />, обладающий емкостью, достаточной для того, чтобы вместить количество скопированных элементов, и использующий указанный интерфейс <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="dictionary">Объект <see cref="T:System.Collections.Generic.IDictionary`2" />, элементы которого копируются в новый объект <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <param name="comparer">Реализация интерфейса <see cref="T:System.Collections.Generic.IComparer`1" />, используемая при сравнении ключей.-или-Значение null для использования класса <see cref="T:System.Collections.Generic.Comparer`1" /> по умолчанию для данного типа ключа.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="dictionary" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="dictionary" /> содержит один или более повторяющихся ключей.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32)">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.SortedList`2" /> с указанной начальной емкостью и использует интерфейс <see cref="T:System.Collections.Generic.IComparer`1" /> по умолчанию.</summary>
      <param name="capacity">Начальное количество элементов, которое может содержать коллекция <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="capacity" /> меньше нуля.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.SortedList`2" /> с заданной начальной емкостью и использует заданный компаратор <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="capacity">Начальное количество элементов, которое может содержать коллекция <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <param name="comparer">Реализация интерфейса <see cref="T:System.Collections.Generic.IComparer`1" />, используемая при сравнении ключей.-или-Значение null для использования класса <see cref="T:System.Collections.Generic.Comparer`1" /> по умолчанию для данного типа ключа.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="capacity" /> меньше нуля.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Add(`0,`1)">
      <summary>Добавляет элемент с указанными ключом и значением в словарь <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <param name="key">Ключ добавляемого элемента.</param>
      <param name="value">Добавляемое значение элемента.Для ссылочных типов допускается значение null.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Элемент с таким ключом уже существует в <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Capacity">
      <summary>Возвращает или задает число элементов, которое может содержать класс <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Количество элементов, которое может содержать класс <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Свойству <see cref="P:System.Collections.Generic.SortedList`2.Capacity" /> присваивается значение, меньшее значения <see cref="P:System.Collections.Generic.SortedList`2.Count" />.</exception>
      <exception cref="T:System.OutOfMemoryException">Недостаточно свободной памяти в системе.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Clear">
      <summary>Удаляет из коллекции <see cref="T:System.Collections.Generic.SortedList`2" /> все элементы.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Comparer">
      <summary>Получает интерфейс <see cref="T:System.Collections.Generic.IComparer`1" /> для отсортированного списка. </summary>
      <returns>Интерфейс <see cref="T:System.IComparable`1" /> для текущего объекта <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsKey(`0)">
      <summary>Определяет, содержит ли объект <see cref="T:System.Collections.Generic.SortedList`2" /> заданный ключ.</summary>
      <returns>true, если <see cref="T:System.Collections.Generic.SortedList`2" /> содержит элемент с указанным ключом, в противном случае — false.</returns>
      <param name="key">Ключ для поиска в <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsValue(`1)">
      <summary>Определяет, содержит ли коллекция <see cref="T:System.Collections.Generic.SortedList`2" /> указанное значение.</summary>
      <returns>Значение true, если <see cref="T:System.Collections.Generic.SortedList`2" /> содержит элемент с указанным значением, в противном случае — значение false.</returns>
      <param name="value">Значение, которое требуется найти в словаре <see cref="T:System.Collections.Generic.SortedList`2" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Count">
      <summary>Возвращает число пар "ключ-значение", содержащихся в словаре <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Число пар "ключ-значение", содержащихся в словаре <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.GetEnumerator">
      <summary>Возвращает перечислитель для просмотра коллекции <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.Generic.IEnumerator`1" /> типа <see cref="T:System.Collections.Generic.KeyValuePair`2" /> для объекта <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfKey(`0)">
      <summary>Осуществляет поиск указанного ключа и возвращает индекс (с нуля) для ключа, найденного в пределах всего класса <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Индекс (с нуля) параметра <paramref name="key" />, найденного в пределах всего класса <see cref="T:System.Collections.Generic.SortedList`2" />; если он не найден — значение -1.</returns>
      <param name="key">Ключ для поиска в <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfValue(`1)">
      <summary>Осуществляет поиск указанного значения и возвращает индекс (с нуля) первого вхождения, найденного в пределах всего класса <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Отсчитываемый от нуля индекс первого вхождения элемента <paramref name="value" /> в пределах всей коллекции <see cref="T:System.Collections.Generic.SortedList`2" />, если элемент найден; в противном случае — значение -1.</returns>
      <param name="value">Значение, которое требуется найти в словаре <see cref="T:System.Collections.Generic.SortedList`2" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Item(`0)">
      <summary>Возвращает или задает значение, связанное с указанным ключом.</summary>
      <returns>Значение, связанное с указанным ключом.Если указанный ключ не найден, операция получения выдает исключение <see cref="T:System.Collections.Generic.KeyNotFoundException" />, а операция задания значения создает новый элемент с использованием указанного ключа.</returns>
      <param name="key">Задаваемое или получаемое значение ключа.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Свойство извлечено, параметр <paramref name="key" /> не существует в коллекции.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Keys">
      <summary>Получает коллекцию, содержащую ключи в <see cref="T:System.Collections.Generic.SortedList`2" />, в отсортированном порядке.</summary>
      <returns>Коллекция <see cref="T:System.Collections.Generic.IList`1" />, содержащая ключи из словаря <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Remove(`0)">
      <summary>Удаляет элемент с указанным ключом из объекта <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Значение true, если элемент успешно удален, в противном случае — значение false.Этот метод возвращает также false, если <paramref name="key" /> не был найден в исходном <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">Ключ элемента, который требуется удалить.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.RemoveAt(System.Int32)">
      <summary>Удаляет элемент по указанному индексу из коллекции <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <param name="index">Индекс (с нуля) элемента, который требуется удалить.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.-или-Значение параметра <paramref name="index" /> больше или равно значению свойства <see cref="P:System.Collections.Generic.SortedList`2.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Добавляет пару "ключ-значение" к <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="keyValuePair">Объект <see cref="T:System.Collections.Generic.KeyValuePair`2" />, добавляемый в объект <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Определяет, содержит ли интерфейс <see cref="T:System.Collections.Generic.ICollection`1" /> определенный элемент.</summary>
      <returns>Значение true, если элемент <paramref name="keyValuePair" /> найден в коллекции <see cref="T:System.Collections.Generic.ICollection`1" />, в противном случае — значение false.</returns>
      <param name="keyValuePair">Объект <see cref="T:System.Collections.Generic.KeyValuePair`2" />, который требуется найти в списке <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.Generic.ICollection`1" /> в массив <see cref="T:System.Array" />, начиная с указанного индекса в массиве <see cref="T:System.Array" />.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.Индексация массива <see cref="T:System.Array" /> должна начинаться с нуля.</param>
      <param name="arrayIndex">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="array" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="arrayIndex" /> меньше нуля. </exception>
      <exception cref="T:System.ArgumentException">Количество элементов в исходной коллекции <see cref="T:System.Collections.Generic.ICollection`1" /> превышает доступное место, начиная с индекса <paramref name="arrayIndex" /> до конца массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Возвращает значение, указывающее, доступен ли список <see cref="T:System.Collections.Generic.ICollection`1" /> только для чтения.</summary>
      <returns>Значение true, если коллекция <see cref="T:System.Collections.Generic.ICollection`1" /> доступна только для чтения, в противном случае — значение false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedList`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Удаляет первое вхождение указанной пары "ключ-значение" из интерфейса <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>Значение true, если объект <paramref name="keyValuePair" /> успешно удален из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />, в противном случае — значение false.Этот метод возвращает также false, если <paramref name="keyValuePair" /> не был найден в исходном <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">Объект <see cref="T:System.Collections.Generic.KeyValuePair`2" />, удаляемый из списка <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Получает интерфейс <see cref="T:System.Collections.Generic.ICollection`1" />, содержащий ключи <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.Generic.ICollection`1" />, содержащая ключи из словаря <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Получает интерфейс <see cref="T:System.Collections.Generic.ICollection`1" />, содержащий значения из <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.Generic.ICollection`1" />, содержащая значения из словаря <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.Generic.IEnumerator`1" />, который можно использовать для последовательного просмотра коллекции.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Получает перечисляемую коллекция, содержащую ключи в словаре только для чтения.</summary>
      <returns>Перечисляемая коллекция, содержащая ключи в словаре только для чтения.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Получает перечисляемую коллекцию, содержащая значения в словаре только для чтения.</summary>
      <returns>Перечисляемая коллекция, содержащая значения в словаре только для чтения.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.ICollection" /> в массив <see cref="T:System.Array" />, начиная с указанного индекса в массиве <see cref="T:System.Array" />.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из коллекции <see cref="T:System.Collections.ICollection" />.Индексация массива <see cref="T:System.Array" /> должна начинаться с нуля.</param>
      <param name="arrayIndex">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, с которого начинается копирование.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="array" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="arrayIndex" /> меньше нуля.</exception>
      <exception cref="T:System.ArgumentException">Массив <paramref name="array" /> является многомерным.-или-В массиве <paramref name="array" /> не используется индексация с нуля.-или-Количество элементов в исходной коллекции <see cref="T:System.Collections.ICollection" /> превышает доступное место, начиная с индекса <paramref name="arrayIndex" /> до конца массива назначения <paramref name="array" />.-или-Тип исходной коллекции <see cref="T:System.Collections.ICollection" /> нельзя автоматически привести к типу массива назначения <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, показывающее, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.ICollection" /> является синхронизированным (потокобезопасным); в противном случае — false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedList`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#SyncRoot">
      <summary>Возвращает объект, который используется для синхронизации доступа к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который используется для синхронизации доступа к коллекции <see cref="T:System.Collections.ICollection" />.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedList`2" /> это свойство всегда возвращает текущий экземпляр.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Добавляет элемент с указанными ключом и значением в <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Объект <see cref="T:System.Object" /> используется в качестве ключа добавляемого элемента.</param>
      <param name="value">Объект <see cref="T:System.Object" /> используется в качестве значения добавляемого элемента.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="key" /> имеет тип, который не может быть назначен типу ключа <paramref name="TKey" /> объекта <see cref="T:System.Collections.IDictionary" />.-или-Параметр <paramref name="value" /> имеет тип, который не может быть назначен типу значения <paramref name="TValue" /> интерфейса <see cref="T:System.Collections.IDictionary" />.-или-Элемент с таким ключом уже существует в <see cref="T:System.Collections.IDictionary" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Определяет, содержится ли элемент с указанным ключом в коллекции <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Значение true, если в <see cref="T:System.Collections.IDictionary" /> содержится элемент с данным ключом; в противном случае — значение false.</returns>
      <param name="key">Ключ для поиска в <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Возвращает объект <see cref="T:System.Collections.IDictionaryEnumerator" /> для <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.IDictionaryEnumerator" /> для коллекции <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Возвращает значение, показывающее, имеет ли список <see cref="T:System.Collections.IDictionary" /> фиксированный размер.</summary>
      <returns>Значение true, если список <see cref="T:System.Collections.IDictionary" /> имеет фиксированный размер, в противном случае — значение false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedList`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Возвращает значение, указывающее, доступен ли список <see cref="T:System.Collections.IDictionary" /> только для чтения.</summary>
      <returns>Значение true, если коллекция <see cref="T:System.Collections.IDictionary" /> доступна только для чтения, в противном случае — значение false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.SortedList`2" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Возвращает или задает элемент с указанным ключом.</summary>
      <returns>Элемент с указанным ключом, или null, если <paramref name="key" /> отсутствует в словаре или тип <paramref name="key" /> не допускает присваивания типу ключа <paramref name="TKey" /> словаря <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">Ключ элемента, который требуется получить или задать.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Назначаемое значение и параметр <paramref name="key" /> типа, который не может быть назначен типу ключа <paramref name="TKey" /> объекта <see cref="T:System.Collections.Generic.SortedList`2" />.-или-Значение присваивается, при этом тип параметра <paramref name="value" /> не допускает присваивание типу значения <paramref name="TValue" /> объекту <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Keys">
      <summary>Получает интерфейс <see cref="T:System.Collections.ICollection" />, содержащий ключи <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.ICollection" />, содержащая ключи из словаря <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Удаляет элемент с указанным ключом из объекта <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Ключ элемента, который требуется удалить.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Values">
      <summary>Получает интерфейс <see cref="T:System.Collections.ICollection" />, содержащий значения из <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.ICollection" />, содержащая значения из словаря <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который можно использовать для последовательного просмотра коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TrimExcess">
      <summary>Устанавливает емкость равной фактическому количеству элементов в <see cref="T:System.Collections.Generic.SortedList`2" />, если это количество составляет менее 90 процентов текущей емкости.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TryGetValue(`0,`1@)">
      <summary>Получает значение, связанное с заданным ключом.</summary>
      <returns>true, если <see cref="T:System.Collections.Generic.SortedList`2" /> содержит элемент с указанным ключом, в противном случае — false.</returns>
      <param name="key">Ключ, значение которого необходимо получить.</param>
      <param name="value">Возвращаемое значение, связанное с указанном ключом, если он найден; в противном случае — значение по умолчанию для данного типа параметра <paramref name="value" />.Этот параметр передается неинициализированным.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="key" /> имеет значение null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Values">
      <summary>Получает коллекцию, содержащую значения в объекте <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Коллекция <see cref="T:System.Collections.Generic.IList`1" />, содержащая значения из словаря <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1">
      <summary>Представляет упорядоченную коллекцию объектов.</summary>
      <typeparam name="T">Тип элементов в наборе.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.SortedSet`1" />. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.SortedSet`1" />, использующий заданный компаратор.</summary>
      <param name="comparer">Компаратор по умолчанию для использования при сравнении объектов. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.SortedSet`1" />, содержащий элементы, скопированные из заданной перечисляемой коллекции.</summary>
      <param name="collection">Перечисляемая коллекция, из которой копируются объекты. </param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IComparer{`0})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.SortedSet`1" />, содержащий элементы, скопированные из заданной перечисляемой коллекции, и использующий заданных компаратор.</summary>
      <param name="collection">Перечисляемая коллекция, из которой копируются объекты. </param>
      <param name="comparer">Компаратор по умолчанию для использования при сравнении объектов. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Add(`0)">
      <summary>Добавляет элемент в набор и возвращает значение, указывающее, что элемент был добавлен успешно.</summary>
      <returns>trueЕсли <paramref name="item" /> добавляется в набор; в противном случае — false. </returns>
      <param name="item">Элемент, добавляемый в набор.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Clear">
      <summary>Удаляет из набора все элементы.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Comparer">
      <summary>Получает объект <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для определения равенства значениям в наборе <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Компаратор, используемый для определения равенства значениям в наборе <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Contains(`0)">
      <summary>Определяет, содержит ли набор указанный элемент.</summary>
      <returns>Значение true, если набор содержит элемент <paramref name="item" />; в противном случае — значение false.</returns>
      <param name="item">Элемент, который требуется найти в наборе.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[])">
      <summary>Копирует весь набор <see cref="T:System.Collections.Generic.SortedSet`1" /> в совместимый одномерный массив начиная с первого элемента массива назначения.</summary>
      <param name="array">Одномерный массив, который является назначением элементов, копируемых из <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentException">Количество элементов в исходном списке <see cref="T:System.Collections.Generic.SortedSet`1" /> больше числа элементов, которое может храниться в массиве назначения. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32)">
      <summary>Копирует весь набор <see cref="T:System.Collections.Generic.SortedSet`1" /> в совместимый одномерный массив начиная с указанного индекса.</summary>
      <param name="array">Одномерный массив, который является назначением элементов, копируемых из <see cref="T:System.Collections.Generic.SortedSet`1" />.Индекс в массиве должен начинаться с нуля.</param>
      <param name="index">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <exception cref="T:System.ArgumentException">Количество элементов в исходном массиве больше доступного пространства от заданного индекса <paramref name="index" /> до конца массива назначения.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Копирует заданное число элементов из набора <see cref="T:System.Collections.Generic.SortedSet`1" /> в совместимый одномерный массив начиная с указанного индекса.</summary>
      <param name="array">Одномерный массив, который является назначением элементов, копируемых из <see cref="T:System.Collections.Generic.SortedSet`1" />.Индекс в массиве должен начинаться с нуля.</param>
      <param name="index">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <param name="count">Число элементов для копирования.</param>
      <exception cref="T:System.ArgumentException">Количество элементов в исходном массиве больше доступного пространства от заданного индекса <paramref name="index" /> до конца массива назначения.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.-или-Значение параметра <paramref name="count" /> меньше нуля.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Count">
      <summary>Получает количество элементов в наборе <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Число элементов в массиве <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Удаляет все элементы в указанной коллекции из текущего объекта <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <param name="other">Коллекция элементов, удаляемая из объекта <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов списка <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Перечислитель, выполняющий итерацию объектов <see cref="T:System.Collections.Generic.SortedSet`1" /> в отсортированном порядке.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetViewBetween(`0,`0)">
      <summary>Возвращает представление подмножества в наборе <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Представление подмножества, содержащее только значения из заданного диапазона.</returns>
      <param name="lowerValue">Минимальное требуемое значение в представлении.</param>
      <param name="upperValue">Максимальное требуемое значение в представлении. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="lowerValue" /> больше <paramref name="upperValue" /> согласно сравнению.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Предпринятая над представлением операция вышла за пределы диапазона, указанного значениями <paramref name="lowerValue" /> и <paramref name="upperValue" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Изменяет текущий объект <see cref="T:System.Collections.Generic.SortedSet`1" />, чтобы он содержал только элементы, которые также имеются в заданной коллекции.</summary>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, является ли объект <see cref="T:System.Collections.Generic.SortedSet`1" /> строгим подмножеством указанной коллекции.</summary>
      <returns>Значение true, если объект <see cref="T:System.Collections.Generic.SortedSet`1" /> является строгим подмножеством объекта <paramref name="other" />; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, является ли объект <see cref="T:System.Collections.Generic.SortedSet`1" /> строгим надмножеством указанной коллекции.</summary>
      <returns>Значение true, если объект <see cref="T:System.Collections.Generic.SortedSet`1" /> строгим надмножеством <paramref name="other" />; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.SortedSet`1" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, является ли объект <see cref="T:System.Collections.Generic.SortedSet`1" /> подмножеством указанной коллекции.</summary>
      <returns>Значение true, если текущий объект <see cref="T:System.Collections.Generic.SortedSet`1" /> является подмножеством <paramref name="other" />; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, является ли объект <see cref="T:System.Collections.Generic.SortedSet`1" /> надмножеством указанной коллекции.</summary>
      <returns>Значение true, если объект <see cref="T:System.Collections.Generic.SortedSet`1" /> строгим надмножеством <paramref name="other" />; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.SortedSet`1" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Max">
      <summary>Получает максимальное значение в наборе <see cref="T:System.Collections.Generic.SortedSet`1" />, определенное компаратором.</summary>
      <returns>Максимальное значение в наборе.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Min">
      <summary>Получает минимальное значение в наборе <see cref="T:System.Collections.Generic.SortedSet`1" />, определенное компаратором.</summary>
      <returns>Минимальное значение в наборе.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, имеются ли общие элементы в текущем объекте <see cref="T:System.Collections.Generic.SortedSet`1" /> и в заданной коллекции.</summary>
      <returns>Значение true, если объекты <see cref="T:System.Collections.Generic.SortedSet`1" /> и <paramref name="other" /> совместно используют по крайней мере один общий элемент; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Remove(`0)">
      <summary>Удаляет указанный элемент из набора <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>trueЕсли элемент найден и успешно удален. в противном случае — false. </returns>
      <param name="item">Подлежащий удалению элемент.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Удаляет из набора <see cref="T:System.Collections.Generic.SortedSet`1" /> все элементы, удовлетворяющие условиям, которые определяются указанным предикатом.</summary>
      <returns>Количество элементов, удаленных из коллекции <see cref="T:System.Collections.Generic.SortedSet`1" />. </returns>
      <param name="match">Делегат, определяющий условия удаления элементов.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Reverse">
      <summary>Возвращает перечислитель <see cref="T:System.Collections.Generic.IEnumerable`1" />, который перебирает элементы набора <see cref="T:System.Collections.Generic.SortedSet`1" /> в обратном порядке.</summary>
      <returns>Перечислитель, выполняющий итерацию в коллекции <see cref="T:System.Collections.Generic.SortedSet`1" /> в обратном порядке.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Определяет, содержат ли текущий объект <see cref="T:System.Collections.Generic.SortedSet`1" /> и указанная коллекция одинаковые элементы.</summary>
      <returns>Значение true, если текущий объект <see cref="T:System.Collections.Generic.SortedSet`1" /> совпадает со значением параметра <paramref name="other" />; в противном случае — значение false.</returns>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Изменяет текущий объект <see cref="T:System.Collections.Generic.SortedSet`1" />, чтобы он содержал только элементы, которые имеются либо в текущем объекте, либо в указанной коллекции, но не одновременно в них обоих.</summary>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Добавляет элемент в объект <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="item">Объект, добавляемый в объект <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Список <see cref="T:System.Collections.Generic.ICollection`1" /> доступен только для чтения.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Получает значение, которое указывает, является ли коллекция <see cref="T:System.Collections.ICollection" /> доступной только для чтения.</summary>
      <returns>Значение true, если коллекция доступна только для чтения; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Перечислитель, который можно использовать для итерации по коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует весь набор <see cref="T:System.Collections.Generic.SortedSet`1" /> в совместимый одномерный массив начиная с указанного индекса.</summary>
      <param name="array">Одномерный массив, который является назначением элементов, копируемых из <see cref="T:System.Collections.Generic.SortedSet`1" />.Индекс в массиве должен начинаться с нуля.</param>
      <param name="index">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <exception cref="T:System.ArgumentException">Количество элементов в исходном массиве больше доступного пространства от заданного индекса <paramref name="index" /> до конца массива назначения. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, определяющее, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>Значение true, если доступ к коллекции <see cref="T:System.Collections.ICollection" /> является синхронизированным; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к <see cref="T:System.Collections.ICollection" />.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> это свойство всегда возвращает текущий экземпляр.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Перечислитель, который можно использовать для итерации по коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Изменяет текущий объект <see cref="T:System.Collections.Generic.SortedSet`1" />, чтобы он содержал все элементы, которые имеются как в текущем объекте, так и в указанной коллекции. </summary>
      <param name="other">Коллекция для сравнения с текущим объектом <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />is null.</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1.Enumerator">
      <summary>Выполняет перечисление элементов объекта <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, соответствующий текущей позиции перечислителя.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, используемые объектом <see cref="T:System.Collections.Generic.SortedSet`1.Enumerator" />. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу коллекции <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, соответствующий текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="T:System.Collections.Generic.Stack`1">
      <summary>Представляет коллекцию переменного размера экземпляров одинакового заданного типа, обслуживаемую по принципу "последним пришел - первым вышел" (LIFO).</summary>
      <typeparam name="T">Задает тип элементов в стеке.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor">
      <summary>Инициализирует новый пустой экземпляр класса <see cref="T:System.Collections.Generic.Stack`1" /> с начальной емкостью по умолчанию.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Collections.Generic.Stack`1" />, который содержит элементы, скопированные из указанной коллекции, и имеет емкость, достаточную для размещения всех скопированных элементов.</summary>
      <param name="collection">Коллекция, из которой требуется скопировать элементы.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Collections.Generic.Stack`1" />, который является пустым и обладает указанной начальной емкостью или емкостью по умолчанию в зависимости от того, какое значение больше.</summary>
      <param name="capacity">Начальное количество элементов, которое может содержать коллекция <see cref="T:System.Collections.Generic.Stack`1" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Clear">
      <summary>Удаляет все объекты из <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Contains(`0)">
      <summary>Определяет, входит ли элемент в состав <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>Значение true, если параметр <paramref name="item" /> найден в коллекции <see cref="T:System.Collections.Generic.Stack`1" />; в противном случае — значение false.</returns>
      <param name="item">Объект, который требуется найти в <see cref="T:System.Collections.Generic.Stack`1" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.CopyTo(`0[],System.Int32)">
      <summary>Копирует коллекцию <see cref="T:System.Collections.Generic.Stack`1" /> в существующий одномерный массив <see cref="T:System.Array" />, начиная с указанного значения индекса массива.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.Generic.Stack`1" />.Массив <see cref="T:System.Array" /> должен иметь индексацию, начинающуюся с нуля.</param>
      <param name="arrayIndex">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Stack`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Count">
      <summary>Получает число элементов, содержащихся в интерфейсе <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>Число элементов, содержащихся в интерфейсе <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.GetEnumerator">
      <summary>Возвращает перечислитель для коллекции <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1.Enumerator" /> для <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Peek">
      <summary>Возвращает объект, находящийся в начале <see cref="T:System.Collections.Generic.Stack`1" />, но не удаляет его.</summary>
      <returns>Объект, находящийся в начале <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Pop">
      <summary>Удаляет и возвращает объект, находящийся в начале <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>Объект, удаленный из начала <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Push(`0)">
      <summary>Вставляет объект в верхнюю часть <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <param name="item">Объект, вставляемый в <see cref="T:System.Collections.Generic.Stack`1" />.Для ссылочных типов допускается значение null.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий перебор элементов в коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.Generic.IEnumerator`1" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.ICollection" /> в массив <see cref="T:System.Array" />, начиная с указанного индекса массива <see cref="T:System.Array" />.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Collections.ICollection" />.Массив <see cref="T:System.Array" /> должен иметь индексацию, начинающуюся с нуля.</param>
      <param name="arrayIndex">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, указывающий начало копирования.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, показывающее, является ли доступ к коллекции <see cref="T:System.Collections.ICollection" /> синхронизированным (потокобезопасным).</summary>
      <returns>true, если доступ к классу <see cref="T:System.Collections.ICollection" /> является синхронизированным (потокобезопасным); в противном случае — false.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Stack`1" /> это свойство всегда возвращает значение false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к <see cref="T:System.Collections.ICollection" />.В используемой по умолчанию реализации <see cref="T:System.Collections.Generic.Stack`1" /> это свойство всегда возвращает текущий экземпляр.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, который осуществляет итерацию по коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.ToArray">
      <summary>Копирует <see cref="T:System.Collections.Generic.Stack`1" /> в новый массив.</summary>
      <returns>Новый массив, содержащий копии элементов <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.TrimExcess">
      <summary>Устанавливает емкость равной фактическому количеству элементов в <see cref="T:System.Collections.Generic.Stack`1" />, если это количество составляет менее 90 процентов текущей емкости.</summary>
    </member>
    <member name="T:System.Collections.Generic.Stack`1.Enumerator">
      <summary>Выполняет перечисление элементов <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент в <see cref="T:System.Collections.Generic.Stack`1" /> находится в текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.Dispose">
      <summary>Освобождает все ресурсы, используемые объектом <see cref="T:System.Collections.Generic.Stack`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.MoveNext">
      <summary>Перемещает перечислитель к следующему элементу <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>Значение true, если перечислитель был успешно перемещен к следующему элементу; значение false, если перечислитель достиг конца коллекции.</returns>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Получает элемент, расположенный в текущей позиции перечислителя.</summary>
      <returns>Элемент коллекции, соответствующий текущей позиции перечислителя.</returns>
      <exception cref="T:System.InvalidOperationException">Перечислитель помещается перед первым элементом коллекции или после последнего элемента. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Устанавливает перечислитель в его начальное положение, т. е. перед первым элементом коллекции.Этот класс не наследуется.</summary>
      <exception cref="T:System.InvalidOperationException">Коллекция была изменена после создания перечислителя. </exception>
    </member>
  </members>
</doc>