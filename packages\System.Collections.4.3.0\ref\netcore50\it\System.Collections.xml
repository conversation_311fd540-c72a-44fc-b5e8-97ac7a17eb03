﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections</name>
  </assembly>
  <members>
    <member name="T:System.Collections.BitArray">
      <summary>Gestisce una matrice compatta di valori di bit, rappresentati come booleani, dove true indica che il bit è attivo (1), mentre false indica che il bit è inattivo (0).</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Boolean[])">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.BitArray" /> contenente i valori di bit copiati dalla matrice specificata di valori booleani.</summary>
      <param name="values">Matrice di valori booleani da copiare. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Byte[])">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.BitArray" /> contenente i valori di bit copiati dalla matrice di byte specificata.</summary>
      <param name="bytes">Matrice di byte contenente i valori da copiare, dove ogni byte rappresenta 8 bit consecutivi. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="bytes" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Collections.BitArray)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.BitArray" /> contenente i valori di bit copiati dalla <see cref="T:System.Collections.BitArray" /> specificata.</summary>
      <param name="bits">Oggetto <see cref="T:System.Collections.BitArray" /> da copiare. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bits" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.BitArray" /> che può contenere il numero specificato di valori di bit, inizialmente impostato su false.</summary>
      <param name="length">Numero di valori di bit nel nuovo <see cref="T:System.Collections.BitArray" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32,System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.BitArray" /> che può contenere il numero specificato di valori di bit , inizialmente impostato sul valore specificato.</summary>
      <param name="length">Numero di valori di bit nel nuovo <see cref="T:System.Collections.BitArray" />. </param>
      <param name="defaultValue">Valore booleano da assegnare a ogni bit. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32[])">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.BitArray" /> contenente i valori di bit copiati dalla matrice specificata di valori Integer a 32 bit.</summary>
      <param name="values">Matrice di valori Integer contenente i valori da copiare, dove ogni valore rappresenta 32 bit consecutivi. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="values" /> is greater than <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Collections.BitArray.And(System.Collections.BitArray)">
      <summary>Esegue l'operazione di AND bit per bit sugli elementi nell'oggetto <see cref="T:System.Collections.BitArray" /> corrente in relazione agli elementi corrispondenti in <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Istanza corrente contenente il risultato dell'operazione AND bit per bit sugli elementi nell'oggetto <see cref="T:System.Collections.BitArray" /> corrente rispetto agli elementi corrispondenti nell'oggetto <see cref="T:System.Collections.BitArray" /> specificato.</returns>
      <param name="value">Oggetto <see cref="T:System.Collections.BitArray" /> con il quale eseguire l'operazione di AND bit per bit. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Get(System.Int32)">
      <summary>Ottiene il valore del bit in una posizione specifica nell'oggetto <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Il valore del bit nella posizione <paramref name="index" />.</returns>
      <param name="index">Indice in base zero del valore da ottenere. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.GetEnumerator">
      <summary>Restituisce un enumeratore che esegue l'iterazione di <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> per l'intero oggetto <see cref="T:System.Collections.BitArray" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Item(System.Int32)">
      <summary>Ottiene o imposta il valore del bit in una specifica posizione nell'oggetto <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Il valore del bit nella posizione <paramref name="index" />.</returns>
      <param name="index">Indice in base zero del valore da ottenere o impostare. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.BitArray.Count" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Length">
      <summary>Ottiene o imposta il numero di elementi nell'oggetto <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Numero di elementi in <see cref="T:System.Collections.BitArray" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is set to a value that is less than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Not">
      <summary>Inverte tutti i valori dei bit nell'oggetto <see cref="T:System.Collections.BitArray" /> corrente in modo che gli elementi impostati su true diventino false e gli elementi impostati su false diventino true.</summary>
      <returns>Istanza corrente con i valori di bit invertiti.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Or(System.Collections.BitArray)">
      <summary>Esegue l'operazione di OR bit per bit sugli elementi dell'oggetto <see cref="T:System.Collections.BitArray" /> corrente in relazione agli elementi corrispondenti nell'oggetto <see cref="T:System.Collections.BitArray" /> specificato.</summary>
      <returns>Istanza corrente contenente il risultato dell'operazione OR bit per bit sugli elementi nell'oggetto <see cref="T:System.Collections.BitArray" /> corrente rispetto agli elementi corrispondenti nell'oggetto <see cref="T:System.Collections.BitArray" /> specificato.</returns>
      <param name="value">Oggetto <see cref="T:System.Collections.BitArray" /> con il quale eseguire l'operazione di OR bit per bit. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Set(System.Int32,System.Boolean)">
      <summary>Imposta il bit in una posizione specifica nell'oggetto <see cref="T:System.Collections.BitArray" /> sul valore specificato.</summary>
      <param name="index">Indice in base zero del bit da impostare. </param>
      <param name="value">Valore booleano da assegnare al bit. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.SetAll(System.Boolean)">
      <summary>Imposta tutti i bit dell'oggetto <see cref="T:System.Collections.BitArray" /> sul valore specificato.</summary>
      <param name="value">Valore booleano da assegnare a tutti i bit. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi dell'oggetto <see cref="T:System.Collections.BitArray" /> in un oggetto <see cref="T:System.Array" />, a partire da un indice <see cref="T:System.Array" /> specificato.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.BitArray" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="index">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or- The number of elements in the source <see cref="T:System.Collections.BitArray" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.BitArray" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#Count">
      <summary>Ottiene il numero di elementi in <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Numero di elementi in <see cref="T:System.Collections.BitArray" />.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.BitArray" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso a <see cref="T:System.Collections.BitArray" /> è sincronizzato (thread-safe); in caso contrario, false.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.BitArray" />.</summary>
      <returns>Oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.BitArray" />.</returns>
    </member>
    <member name="M:System.Collections.BitArray.Xor(System.Collections.BitArray)">
      <summary>Esegue l'operazione di OR esclusivo bit per bit sugli elementi della classe <see cref="T:System.Collections.BitArray" /> corrente in relazione agli elementi corrispondenti nell'oggetto <see cref="T:System.Collections.BitArray" /> specificato.</summary>
      <returns>Istanza corrente contenente il risultato dell'operazione OR esclusiva sugli elementi nell'oggetto <see cref="T:System.Collections.BitArray" /> corrente rispetto agli elementi corrispondenti nell'oggetto <see cref="T:System.Collections.BitArray" /> specificato. </returns>
      <param name="value">Classe <see cref="T:System.Collections.BitArray" /> con la quale eseguire l'operazione di OR esclusivo bit per bit. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Collections.StructuralComparisons">
      <summary>Fornisce oggetti per l'esecuzione di un confronto strutturale di due oggetti insieme.</summary>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralComparer">
      <summary>Ottiene un oggetto predefinito che esegue un confronto strutturale di due oggetti.</summary>
      <returns>Oggetto predefinito utilizzato per eseguire un confronto strutturale di due oggetti insieme.</returns>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralEqualityComparer">
      <summary>Ottiene un oggetto predefinito che confronta due oggetti per determinarne l'uguaglianza strutturale.</summary>
      <returns>Oggetto predefinito utilizzato per confrontare due oggetti insieme e determinarne l'uguaglianza strutturale.</returns>
    </member>
    <member name="T:System.Collections.Generic.Comparer`1">
      <summary>Fornisce una classe base per le implementazioni dell'interfaccia generica <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <typeparam name="T">Tipo di oggetti da confrontare.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.Comparer`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Compare(`0,`0)">
      <summary>Quando è sottoposto a override in una classe derivata, esegue un confronto tra due oggetti dello stesso tipo e restituisce un valore che indica se un oggetto è minore, uguale o maggiore dell'altro.</summary>
      <returns>Intero con segno che indica i valori relativi di <paramref name="x" /> e <paramref name="y" />, come illustrato nella tabella seguente.Valore Significato Minore di zero Il parametro <paramref name="x" /> è minore del parametro <paramref name="y" />.Zero <paramref name="x" /> è uguale a <paramref name="y" />.Maggiore di zero <paramref name="x" /> è maggiore di <paramref name="y" />.</returns>
      <param name="x">Primo oggetto da confrontare.</param>
      <param name="y">Secondo oggetto da confrontare.</param>
      <exception cref="T:System.ArgumentException">Il tipo <paramref name="T" /> non implementa l'interfaccia generica <see cref="T:System.IComparable`1" /> o l'interfaccia <see cref="T:System.IComparable" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Create(System.Comparison{`0})">
      <summary>Crea un operatore di confronto utilizzando il confronto specificato.</summary>
      <returns>Nuovo operatore di confronto.</returns>
      <param name="comparison">Confronto da utilizzare.</param>
    </member>
    <member name="P:System.Collections.Generic.Comparer`1.Default">
      <summary>Restituisce un operatore di confronto di ordinamento predefinito per il tipo specificato dall'argomento generico.</summary>
      <returns>Oggetto che eredita l'oggetto <see cref="T:System.Collections.Generic.Comparer`1" /> e viene utilizzato come un operatore di confronto di ordinamento per il tipo <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Confronta due oggetti e restituisce un valore indicante se uno è minore, uguale o maggiore dell'altro.</summary>
      <returns>Intero con segno che indica i valori relativi di <paramref name="x" /> e <paramref name="y" />, come illustrato nella tabella seguente.Valore Significato Minore di zeroIl parametro <paramref name="x" /> è minore del parametro <paramref name="y" />.Zero<paramref name="x" /> è uguale a <paramref name="y" />.Maggiore di zero<paramref name="x" /> è maggiore di <paramref name="y" />.</returns>
      <param name="x">Primo oggetto da confrontare.</param>
      <param name="y">Secondo oggetto da confrontare.</param>
      <exception cref="T:System.ArgumentException">Il tipo del parametro <paramref name="x" /> o <paramref name="y" /> non consente di effettuare il cast al tipo <paramref name="T" />.In alternativaI parametri <paramref name="x" /> e <paramref name="y" /> non implementano l'interfaccia generica <see cref="T:System.IComparable`1" /> o l'interfaccia <see cref="T:System.IComparable" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2">
      <summary>Rappresenta una raccolta di chiavi e valori.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere il Reference Source.</summary>
      <typeparam name="TKey">Tipo di chiavi nel dizionario.</typeparam>
      <typeparam name="TValue">Tipo di valori nel dizionario.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.Dictionary`2" />, con capacità iniziale predefinita e che usa l'operatore di confronto di eguaglianza predefinito per il tipo di chiave.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.Dictionary`2" /> che contiene elementi copiati dall'interfaccia <see cref="T:System.Collections.Generic.IDictionary`2" /> specificata e che usa l'operatore di confronto uguaglianze predefinito per il tipo di chiave.</summary>
      <param name="dictionary">Oggetto <see cref="T:System.Collections.Generic.IDictionary`2" /> i cui elementi sono copiati nel nuovo oggetto <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="dictionary" /> contiene una o più chiavi duplicate.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.Dictionary`2" /> che contiene gli elementi copiati dall'interfaccia <see cref="T:System.Collections.Generic.IDictionary`2" /> specificata e che usa l'interfaccia <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> indicata.</summary>
      <param name="dictionary">Oggetto <see cref="T:System.Collections.Generic.IDictionary`2" /> i cui elementi sono copiati nel nuovo oggetto <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <param name="comparer">Implementazione di <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> da usare per confrontare le chiavi oppure null per usare l'oggetto <see cref="T:System.Collections.Generic.EqualityComparer`1" /> predefinito per il tipo di chiave.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="dictionary" /> contiene una o più chiavi duplicate.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.Dictionary`2" />, con la capacità iniziale predefinita e che usa l'interfaccia <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> specificata.</summary>
      <param name="comparer">Implementazione di <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> da usare per confrontare le chiavi oppure null per usare l'oggetto <see cref="T:System.Collections.Generic.EqualityComparer`1" /> predefinito per il tipo di chiave.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.Dictionary`2" />, con capacità iniziale specificata e che usa l'operatore di confronto di eguaglianza predefinito per il tipo di chiave.</summary>
      <param name="capacity">Il numero iniziale degli elementi che <see cref="T:System.Collections.Generic.Dictionary`2" /> può contenere.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> è minore di 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.Dictionary`2" />, con la capacità iniziale specificata e che usa l'interfaccia <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> specificata.</summary>
      <param name="capacity">Il numero iniziale degli elementi che <see cref="T:System.Collections.Generic.Dictionary`2" /> può contenere.</param>
      <param name="comparer">Implementazione di <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> da usare per confrontare le chiavi oppure null per usare l'oggetto <see cref="T:System.Collections.Generic.EqualityComparer`1" /> predefinito per il tipo di chiave.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> è minore di 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Add(`0,`1)">
      <summary>Aggiunge la chiave e il valore specificati al dizionario.</summary>
      <param name="key">Chiave dell'elemento da aggiungere.</param>
      <param name="value">Valore dell'elemento da aggiungere.Il valore può essere null per i tipi di riferimento.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.ArgumentException">In <see cref="T:System.Collections.Generic.Dictionary`2" /> è già presente un elemento con la stessa chiave.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Clear">
      <summary>Rimuove tutte le chiavi e i valori della raccolta <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Comparer">
      <summary>Ottiene l'oggetto <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, che viene usato per determinare l'uguaglianza delle chiavi per il dizionario. </summary>
      <returns>Implementazione dell'interfaccia generica <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> che viene usata per determinare l'uguaglianza fra chiavi del dizionario <see cref="T:System.Collections.Generic.Dictionary`2" /> corrente e per fornire i valori hash delle chiavi.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsKey(`0)">
      <summary>Determina se la raccolta <see cref="T:System.Collections.Generic.Dictionary`2" /> contiene la chiave specificata.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.Dictionary`2" /> contiene un elemento con la chiave specificata; in caso contrario, false.</returns>
      <param name="key">Chiave da individuare in <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsValue(`1)">
      <summary>Stabilisce se <see cref="T:System.Collections.Generic.Dictionary`2" /> contiene un valore specifico.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.Dictionary`2" /> contiene un elemento con il valore specificato; in caso contrario, false.</returns>
      <param name="value">Valore da individuare in <see cref="T:System.Collections.Generic.Dictionary`2" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Count">
      <summary>Ottiene il numero di coppie chiave/valore contenute in <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Numero di coppie chiave/valore contenute in <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.GetEnumerator">
      <summary>Restituisce un enumeratore che esegue l'iterazione di <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Struttura <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> della raccolta <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Item(`0)">
      <summary>Ottiene o imposta il valore associato alla chiave specificata.</summary>
      <returns>Valore associato alla chiave specificata.Se la chiave specificata non viene trovata, un'operazione Get genera un'eccezione <see cref="T:System.Collections.Generic.KeyNotFoundException" />, mentre un'operazione Set crea una nuovo elemento con la chiave specificata.</returns>
      <param name="key">Chiave del valore da ottenere o impostare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Durante il recupero della proprietà, la chiave indicata nel parametro <paramref name="key" /> non è stata trovata nella raccolta.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Keys">
      <summary>Ottiene una raccolta contenente le chiavi della classe <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Raccolta <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> contenente le chiavi della classe <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Remove(`0)">
      <summary>Rimuove il valore con la chiave specificata dalla raccolta <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>true se l'elemento viene trovato e rimosso; in caso contrario, false.Questo metodo restituisce anche false se <paramref name="key" /> non viene trovato in <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
      <param name="key">Chiave dell'elemento da rimuovere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Aggiunge il valore specificato all'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> con la chiave specificata.</summary>
      <param name="keyValuePair">Struttura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> che rappresenta la chiave e il valore da aggiungere alla raccolta <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">La chiave indicata nel parametro<paramref name="keyValuePair" /> è null.</exception>
      <exception cref="T:System.ArgumentException">In <see cref="T:System.Collections.Generic.Dictionary`2" /> è già presente un elemento con la stessa chiave.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Stabilisce se l'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> contiene una coppia chiave/valore specifica.</summary>
      <returns>true se <paramref name="keyValuePair" /> è presente in <see cref="T:System.Collections.Generic.ICollection`1" />; in caso contrario, false.</returns>
      <param name="keyValuePair">Struttura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> da individuare nell'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copia gli elementi dell'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> in una matrice di tipo <see cref="T:System.Collections.Generic.KeyValuePair`2" />, iniziando dall'indice di matrice specificato.</summary>
      <param name="array">Matrice unidimensionale di tipo <see cref="T:System.Collections.Generic.KeyValuePair`2" /> che costituisce la destinazione degli elementi <see cref="T:System.Collections.Generic.KeyValuePair`2" /> copiati dall'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.La matrice deve avere un'indicizzazione con base zero.</param>
      <param name="index">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">Il numero degli elementi nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se il dizionario è in sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.Dictionary`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Rimuove una chiave e un valore dal dizionario.</summary>
      <returns>true se la chiave e il valore rappresentati da <paramref name="keyValuePair" /> vengono trovati e rimossi; in caso contrario, false.Questo metodo restituisce anche false se <paramref name="keyValuePair" /> non viene trovato in <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">Struttura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> che rappresenta la chiave e il valore da rimuovere da <see cref="T:System.Collections.Generic.Dictionary`2" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Ottiene <see cref="T:System.Collections.Generic.ICollection`1" /> contenente le chiavi di <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> di tipo <paramref name="TKey" /> contenente le chiavi dell'interfaccia <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Ottiene <see cref="T:System.Collections.Generic.ICollection`1" /> contenente i valori in <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> di tipo <paramref name="TValue" /> contenente i valori dell'interfaccia <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere la raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Ottiene una raccolta contenente le chiavi della classe <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</summary>
      <returns>Raccolta contenente le chiavi di <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Ottiene una raccolta contenente i valori di <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</summary>
      <returns>Raccolta contenente i valori di <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi dell'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> in una matrice, iniziando dall'indice di matrice specificato.</summary>
      <param name="array">Matrice unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.La matrice deve avere un'indicizzazione con base zero.</param>
      <param name="index">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> è multidimensionale.-oppure-<paramref name="array" /> non dispone di indicizzazione in base zero.-oppure-Il numero degli elementi nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.-oppure-Non è possibile eseguire automaticamente il cast del tipo dell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" /> di origine al tipo del parametro <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.Dictionary`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />. </returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Aggiunge la chiave e il valore specificati al dizionario.</summary>
      <param name="key">Oggetto da usare come chiave.</param>
      <param name="value">Oggetto da usare come valore.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il tipo della chiave indicata nel parametro <paramref name="key" /> non è assegnabile al tipo di chiave <paramref name="TKey" /> dell'insieme <see cref="T:System.Collections.Generic.Dictionary`2" />.-oppure-Il tipo del valore indicato nel parametro <paramref name="value" /> non è assegnabile al tipo di valore <paramref name="TValue" /> dell'insieme <see cref="T:System.Collections.Generic.Dictionary`2" />.-oppure-nell'insieme <see cref="T:System.Collections.Generic.Dictionary`2" /> è già presente un valore con la stessa chiave.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determina se <see cref="T:System.Collections.IDictionary" /> contiene un elemento con la chiave specificata.</summary>
      <returns>true se <see cref="T:System.Collections.IDictionary" /> contiene un elemento con la chiave specificata; in caso contrario, false.</returns>
      <param name="key">Chiave da individuare in <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Restituisce <see cref="T:System.Collections.IDictionaryEnumerator" /> per <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionaryEnumerator" /> per l'oggetto <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.IDictionary" /> ha dimensioni fisse.</summary>
      <returns>true se <see cref="T:System.Collections.IDictionary" /> è di dimensioni fisse; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.Dictionary`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.IDictionary" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.IDictionary" /> è di sola lettura; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.Dictionary`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Ottiene o imposta il valore con la chiave specificata.</summary>
      <returns>Valore associato alla chiave specificata o null se <paramref name="key" /> non è presente nel dizionario o se il tipo di <paramref name="key" /> non può essere assegnato al tipo di chiave <paramref name="TKey" /> di <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
      <param name="key">Chiave del valore da ottenere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Viene assegnato un valore e il tipo di <paramref name="key" /> non può essere assegnato al tipo di chiave <paramref name="TKey" /> della classe<see cref="T:System.Collections.Generic.Dictionary`2" />.-oppure-Viene assegnato un valore e il tipo di <paramref name="value" /> non può essere assegnato al tipo di valore <paramref name="TValue" /> della classe<see cref="T:System.Collections.Generic.Dictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Keys">
      <summary>Ottiene <see cref="T:System.Collections.ICollection" /> contenente le chiavi di <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.ICollection" /> contenente le chiavi dell'interfaccia <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Rimuove l'elemento con la chiave specificata da <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Chiave dell'elemento da rimuovere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Values">
      <summary>Ottiene <see cref="T:System.Collections.ICollection" /> contenente i valori in <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.ICollection" /> contenente i valori dell'interfaccia <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere la raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.IEnumerator" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.TryGetValue(`0,`1@)">
      <summary>Ottiene il valore associato alla chiave specificata.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.Dictionary`2" /> contiene un elemento con la chiave specificata; in caso contrario, false.</returns>
      <param name="key">Chiave del valore da ottenere.</param>
      <param name="value">Quando termina, questo metodo restituisce il valore associato alla chiave specificata nel caso in cui la chiave venga trovata; in caso contrario, restituisce il valore predefinito per il tipo di parametro <paramref name="value" />.Questo parametro viene passato non inizializzato.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Values">
      <summary>Ottiene una raccolta contenente i valori di <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>Raccolta <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> contenente i valori di <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento dell'insieme <see cref="T:System.Collections.Generic.Dictionary`2" /> nella posizione corrente dell'enumeratore.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.Dispose">
      <summary>Consente di rilasciare tutte le risorse utilizzate dall'oggetto <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo dell'oggetto <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento del dizionario nella posizione corrente dell'enumeratore, restituito come un oggetto <see cref="T:System.Collections.DictionaryEntry" />.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Ottiene la chiave dell'elemento nella posizione corrente dell'enumeratore.</summary>
      <returns>Chiave dell'elemento del dizionario nella posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Ottiene il valore dell'elemento nella posizione corrente dell'enumeratore.</summary>
      <returns>Valore dell'elemento del dizionario nella posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta nella posizione corrente dell'enumeratore, restituito come un oggetto <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection">
      <summary>Rappresenta la raccolta di chiavi in una classe <see cref="T:System.Collections.Generic.Dictionary`2" />.La classe non può essere ereditata.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> in cui sono riprodotte le chiavi dell'oggetto <see cref="T:System.Collections.Generic.Dictionary`2" /> specificato.</summary>
      <param name="dictionary">Classe <see cref="T:System.Collections.Generic.Dictionary`2" /> le cui chiavi vengono riprodotte nella nuova classe <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Consente di copiare gli elementi della <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> in una <see cref="T:System.Array" /> unidimensionale esistente, partendo dall'indice della matrice specificata.</summary>
      <param name="array">Oggetto unidimensionale <see cref="T:System.Array" /> che rappresenta la destinazione degli elementi copiati da <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="index">Indice in base zero della matrice specificata nel parametro <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di zero.</exception>
      <exception cref="T:System.ArgumentException">Il numero degli elementi nell'oggetto <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Count">
      <summary>Ottiene il numero di elementi contenuti in <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>Il numero di elementi contenuti in <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.Il recupero del valore di questa proprietà è un'operazione O(1).</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.GetEnumerator">
      <summary>Restituisce un enumeratore che scorre la classe <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" /> per l'insieme <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Aggiunge un elemento all'insieme <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre l'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Oggetto da aggiungere alla <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Generata sempre.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Consente di rimuovere tutti gli elementi dal controllo <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre l'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Generata sempre.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Stabilisce se l'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> contiene un valore specifico.</summary>
      <returns>true se il valore indicato nel parametro <paramref name="item" /> è presente nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />, in caso contrario false.</returns>
      <param name="item">Oggetto da individuare nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura. In caso contrario, false.  Nell'implementazione predefinita dell'oggetto <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, questa proprietà restituisce sempre true.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Rimuove la prima occorrenza di un oggetto specifico dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre l'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true se <paramref name="item" /> è stato correttamente rimosso dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />; in caso contrario, false.Questo metodo restituisce anche false se <paramref name="item" /> non è stato trovato nell'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> originale.</returns>
      <param name="item">Oggetto da rimuovere dall'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Generata sempre.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere utilizzato per scorrere la raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi dell'interfaccia <see cref="T:System.Collections.ICollection" /> in un oggetto <see cref="T:System.Array" />, a partire da un particolare indice della matrice <see cref="T:System.Array" />.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.ICollection" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="index">Indice in base zero della matrice specificata nel parametro <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> è multidimensionale.- oppure -<paramref name="array" /> non dispone di indicizzazione in base zero.- oppure -Il numero degli elementi nell'oggetto <see cref="T:System.Collections.ICollection" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.- oppure -Non è possibile eseguire automaticamente il cast del tipo dell'oggetto <see cref="T:System.Collections.ICollection" /> di origine al tipo del parametro <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso all'oggetto <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.  Nell'implementazione predefinita dell'oggetto <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.  Nell'implementazione predefinita dell'oggetto <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, questa proprietà restituisce sempre l'istanza corrente.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.IEnumerator" /> che può essere utilizzata per scorrere la raccolta.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento dell'insieme <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> in corrispondenza della posizione corrente dell'enumeratore.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Rilascia tutte le risorse usate dall'oggetto <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo dell'oggetto <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>true se l'enumeratore è stato spostato correttamente in avanti in corrispondenza dell'elemento successivo, false se l'enumeratore ha superato la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection">
      <summary>Rappresenta la raccolta di valori in una classe <see cref="T:System.Collections.Generic.Dictionary`2" />.La classe non può essere ereditata.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> in cui sono riprodotti i valori dell'insieme <see cref="T:System.Collections.Generic.Dictionary`2" /> specificato.</summary>
      <param name="dictionary">Insieme <see cref="T:System.Collections.Generic.Dictionary`2" /> i cui valori sono riportati nel nuovo oggetto <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Consente di copiare gli elementi della <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> in una <see cref="T:System.Array" /> unidimensionale esistente, partendo dall'indice della matrice specificata.</summary>
      <param name="array">Oggetto unidimensionale <see cref="T:System.Array" /> che rappresenta la destinazione degli elementi copiati da <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="index">Indice in base zero della matrice specificata nel parametro <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di zero.</exception>
      <exception cref="T:System.ArgumentException">Il numero degli elementi nell'oggetto <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Count">
      <summary>Ottiene il numero di elementi contenuti in <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>Il numero di elementi contenuti in <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.GetEnumerator">
      <summary>Restituisce un enumeratore che scorre l'insieme <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" /> per l'insieme <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Aggiunge un elemento all'insieme <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre l'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Oggetto da aggiungere alla <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Generata sempre.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Consente di rimuovere tutti gli elementi dal controllo <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre l'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Generata sempre.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Stabilisce se l'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> contiene un valore specifico.</summary>
      <returns>true se il valore indicato nel parametro <paramref name="item" /> è presente nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />, in caso contrario false.</returns>
      <param name="item">Oggetto da individuare nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura. In caso contrario, false.  Nell'implementazione predefinita dell'insieme <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, questa proprietà restituisce sempre true.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Rimuove la prima occorrenza di un oggetto specifico dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.Questa implementazione genera sempre l'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true se <paramref name="item" /> è stato correttamente rimosso dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />; in caso contrario, false.Questo metodo restituisce anche false se <paramref name="item" /> non è stato trovato nell'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> originale.</returns>
      <param name="item">Oggetto da rimuovere dall'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Generata sempre.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere utilizzato per scorrere la raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi dell'interfaccia <see cref="T:System.Collections.ICollection" /> in un oggetto <see cref="T:System.Array" />, a partire da un particolare indice della matrice <see cref="T:System.Array" />.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.ICollection" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="index">Indice in base zero della matrice specificata nel parametro <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> è multidimensionale.- oppure -<paramref name="array" /> non dispone di indicizzazione in base zero.- oppure -Il numero degli elementi nell'oggetto <see cref="T:System.Collections.ICollection" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.- oppure -Non è possibile eseguire automaticamente il cast del tipo dell'oggetto <see cref="T:System.Collections.ICollection" /> di origine al tipo del parametro <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso all'oggetto <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.  Nell'implementazione predefinita dell'insieme <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.  Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, questa proprietà restituisce sempre l'istanza corrente.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.IEnumerator" /> che può essere utilizzata per scorrere la raccolta.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento dell'insieme <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> nella posizione corrente dell'enumeratore.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Consente di rilasciare tutte le risorse utilizzate dall'oggetto <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo dell'oggetto <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="T:System.Collections.Generic.EqualityComparer`1">
      <summary>Fornisce una classe base per le implementazioni dell'interfaccia generica <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <typeparam name="T">Tipo di oggetti da confrontare.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.EqualityComparer`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.EqualityComparer`1.Default">
      <summary>Restituisce un operatore di confronto uguaglianze predefinito per il tipo specificato dall'argomento generico.</summary>
      <returns>Istanza predefinita della classe <see cref="T:System.Collections.Generic.EqualityComparer`1" /> per il tipo <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.Equals(`0,`0)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, determina se due oggetti di tipo <paramref name="T" /> sono uguali.</summary>
      <returns>true se gli oggetti specificati sono uguali; in caso contrario, false.</returns>
      <param name="x">Primo oggetto da confrontare.</param>
      <param name="y">Secondo oggetto da confrontare.</param>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.GetHashCode(`0)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, viene usato come funzione hash dell'oggetto specificato per gli algoritmi e le strutture di dati hash, come ad esempio una tabella hash.</summary>
      <returns>Codice hash per l'oggetto specificato.</returns>
      <param name="obj">Oggetto per il quale ottenere un codice hash.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Determina se gli oggetti specificati sono uguali.</summary>
      <returns>true se gli oggetti specificati sono uguali; in caso contrario, false.</returns>
      <param name="x">Primo oggetto da confrontare.</param>
      <param name="y">Secondo oggetto da confrontare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> or <paramref name="y" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Restituisce un codice hash per l'oggetto specificato.</summary>
      <returns>Codice hash per l'oggetto specificato.</returns>
      <param name="obj">Oggetto <see cref="T:System.Object" /> per cui è necessario che sia restituito un codice hash.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.-or-<paramref name="obj" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1">
      <summary>Rappresenta un insieme di valori.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere il Reference Source.</summary>
      <typeparam name="T">Tipo di elementi contenuti nel set di hash.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.HashSet`1" /> e usa l'operatore di confronto per l'uguaglianza per il tipo di insieme.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.HashSet`1" /> che usa l'operatore di confronto per l'uguaglianza per il tipo di insieme, contiene gli elementi copiati dalla raccolta specificata e presenta una capacità sufficiente a contenere il numero di elementi copiati.</summary>
      <param name="collection">Raccolta i cui elementi vengono copiati nel nuovo insieme.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.HashSet`1" /> che usa l'operatore di confronto per l'uguaglianza specificato per il tipo di insieme, contiene gli elementi copiati dalla raccolta specificata e ha una capacità sufficiente a contenere il numero di elementi copiati.</summary>
      <param name="collection">Raccolta i cui elementi vengono copiati nel nuovo insieme.</param>
      <param name="comparer">Implementazione di <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> da usare per confrontare i valori nel set oppure null per usare l'implementazione di <see cref="T:System.Collections.Generic.EqualityComparer`1" /> predefinita per il tipo di set.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.HashSet`1" /> e usa l'operatore di confronto per l'uguaglianza specificato per il tipo di insieme.</summary>
      <param name="comparer">Implementazione di <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> da usare per confrontare i valori nel set oppure null per usare l'implementazione di <see cref="T:System.Collections.Generic.EqualityComparer`1" /> predefinita per il tipo di set.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Add(`0)">
      <summary>Aggiunge l'elemento specificato a un insieme.</summary>
      <returns>true se l'elemento viene aggiunto all'oggetto <see cref="T:System.Collections.Generic.HashSet`1" />, false se l'elemento è già presente.</returns>
      <param name="item">Elemento da aggiungere all'insieme.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Clear">
      <summary>Rimuove tutti gli elementi da un oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Comparer">
      <summary>Ottiene l'oggetto <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, che viene usato per determinare l'uguaglianza dei valori nell'insieme.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> usato per determinare l'uguaglianza dei valori nell'insieme.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Contains(`0)">
      <summary>Determina se un oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> contiene l'elemento specificato.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> contiene l'elemento specificato; in caso contrario, false.</returns>
      <param name="item">Elemento da individuare nell'oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[])">
      <summary>Copia gli elementi di un oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> in una matrice.</summary>
      <param name="array">Matrice unidimensionale che costituisce la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.La matrice deve avere un'indicizzazione in base zero.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32)">
      <summary>Copia gli elementi di un oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> in una matrice, a partire da un indice di matrice specificato.</summary>
      <param name="array">Matrice unidimensionale che costituisce la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.La matrice deve avere un'indicizzazione in base zero.</param>
      <param name="arrayIndex">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> è maggiore della lunghezza del parametro <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Copia il numero specificato di elementi di un oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> in una matrice, a partire da un indice di matrice specificato.</summary>
      <param name="array">Matrice unidimensionale che costituisce la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.La matrice deve avere un'indicizzazione in base zero.</param>
      <param name="arrayIndex">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <param name="count">Numero di elementi da copiare in <paramref name="array" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> è minore di 0.-oppure-<paramref name="count" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> è maggiore della lunghezza del parametro <paramref name="array" /> di destinazione.-oppure-<paramref name="count" /> è maggiore dello spazio disponibile da <paramref name="index" /> alla fine del parametro <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Count">
      <summary>Ottiene il numero di elementi contenuti in un insieme.</summary>
      <returns>Numero di elementi contenuti nell'insieme.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Rimuove tutti gli elementi della raccolta specificata dall'oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <param name="other">Raccolta di elementi da rimuovere dall'oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.GetEnumerator">
      <summary>Restituisce un enumeratore che consente di eseguire l'iterazione di un oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> per l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente per contenere solo elementi presenti in tale oggetto e nella raccolta specificata.</summary>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se un oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> è un subset corretto della raccolta specificata.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> è un subset corretto di <paramref name="other" />; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se un oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> è un superset corretto della raccolta specificata.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> è un superset corretto di <paramref name="other" />; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se un oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> è un subset della raccolta specificata.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> è un subset di <paramref name="other" />; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se un oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> è un superset della raccolta specificata.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> è un superset di <paramref name="other" />; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente e una raccolta specificata condividono elementi comuni.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> e il parametro <paramref name="other" /> condividono almeno un elemento comune; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Remove(`0)">
      <summary>Rimuove l'elemento specificato da un oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>true se l'elemento viene trovato e rimosso; in caso contrario, false.Questo metodo restituisce false se <paramref name="item" /> non viene trovato nell'oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
      <param name="item">Elemento da rimuovere.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Rimuove tutti gli elementi che corrispondono alle condizioni definite dal predicato specificato da una raccolta <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>Numero di elementi rimossi dalla raccolta <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni degli elementi da rimuovere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se un oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> e la raccolta specificata contengono gli stessi elementi.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> è uguale a <paramref name="other" />; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente in modo che contenga solo elementi presenti in tale oggetto o nella raccolta specificata, ma non entrambi.</summary>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Aggiunge un elemento a un oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="item">Oggetto da aggiungere all'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">La classe <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se una raccolta è di sola lettura.</summary>
      <returns>true se la raccolta è di sola lettura; in caso contrario, false.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di eseguire l'iterazione di una raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di eseguire l'iterazione di una raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.IEnumerator" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.TrimExcess">
      <summary>Imposta la capacità di un oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> sul numero effettivo di elementi che contiene, arrotondato per eccesso a un valore vicino specifico dell'implementazione.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente per contenere tutti gli elementi presenti in tale oggetto, nella raccolta specificata o in entrambi.</summary>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.HashSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta <see cref="T:System.Collections.Generic.HashSet`1" /> in corrispondenza della posizione corrente dell'enumeratore.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.Dispose">
      <summary>Rilascia tutte le risorse utilizzate da un oggetto <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo della raccolta <see cref="T:System.Collections.Generic.HashSet`1" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta nella posizione corrente dell'enumeratore, restituito come un oggetto <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1">
      <summary>Rappresenta una lista bidirezionale.</summary>
      <typeparam name="T">Specifica il tipo di elemento dell'elenco collegato.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.LinkedList`1" /> contenente gli elementi copiati dall'interfaccia <see cref="T:System.Collections.IEnumerable" /> specificata e la cui capacità è sufficiente a contenere il numero di elementi copiati. </summary>
      <param name="collection">Interfaccia <see cref="T:System.Collections.IEnumerable" /> i cui elementi sono copiati nella nuova classe <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Aggiunge il nuovo nodo specificato dopo il nodo esistente indicato nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> dopo il quale inserire il nuovo nodo indicato nel parametro <paramref name="newNode" />.</param>
      <param name="newNode">Nuovo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> da aggiungere all'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> è null.- oppure -<paramref name="newNode" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> non è presente nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" /> corrente.- oppure -<paramref name="newNode" /> appartiene a un altro oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Aggiunge un nuovo nodo che contiene il valore specificato dopo il nodo esistente indicato nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Nuovo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenente il valore indicato nel parametro <paramref name="value" />.</returns>
      <param name="node">Nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> dopo il quale viene inserito un nuovo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenente il valore indicato nel parametro <paramref name="value" />.</param>
      <param name="value">Valore da aggiungere all'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> non è presente nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" /> corrente.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Aggiunge il nuovo nodo specificato prima del nodo esistente indicato nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> prima del quale inserire il nuovo nodo indicato nel parametro <paramref name="newNode" />.</param>
      <param name="newNode">Nuovo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> da aggiungere all'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> è null.- oppure -<paramref name="newNode" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> non è presente nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" /> corrente.- oppure -<paramref name="newNode" /> appartiene a un altro oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Aggiunge un nuovo nodo che contiene il valore specificato prima del nodo esistente indicato nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Nuovo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenente il valore indicato nel parametro <paramref name="value" />.</returns>
      <param name="node">Nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> prima del quale viene inserito un nuovo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenente il valore indicato nel parametro <paramref name="value" />.</param>
      <param name="value">Valore da aggiungere all'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> non è presente nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" /> corrente.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Aggiunge il nuovo nodo specificato all'inizio dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Nuovo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> da aggiungere all'inizio dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> appartiene a un altro oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(`0)">
      <summary>Aggiunge un nuovo nodo contenente il valore specificato all'inizio dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Nuovo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenente il valore indicato nel parametro <paramref name="value" />.</returns>
      <param name="value">Valore da aggiungere all'inizio dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Aggiunge il nuovo nodo specificato alla fine dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Nuovo oggetto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> da aggiungere alla fine dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> appartiene a un altro oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(`0)">
      <summary>Aggiunge un nuovo nodo contenente il valore specificato alla fine dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Nuovo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenente il valore indicato nel parametro <paramref name="value" />.</returns>
      <param name="value">Valore da aggiungere alla fine dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Clear">
      <summary>Rimuove tutti i nodi dall'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Contains(`0)">
      <summary>Determina se un valore è incluso nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true se il valore indicato nel parametro <paramref name="value" /> si trova nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />; in caso contrario, false.</returns>
      <param name="value">Valore da individuare nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.CopyTo(`0[],System.Int32)">
      <summary>Copia l'intero oggetto <see cref="T:System.Collections.Generic.LinkedList`1" /> in un oggetto <see cref="T:System.Array" /> compatibile unidimensionale, a partire dall'indice specificato della matrice di destinazione.</summary>
      <param name="array">Oggetto unidimensionale <see cref="T:System.Array" /> che rappresenta la destinazione degli elementi copiati da <see cref="T:System.Collections.Generic.LinkedList`1" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="index">Indice in base zero della matrice specificata nel parametro <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di zero.</exception>
      <exception cref="T:System.ArgumentException">Il numero degli elementi nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Count">
      <summary>Ottiene il numero di nodi effettivamente contenuti nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Numero di nodi effettivamente contenuti nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Find(`0)">
      <summary>Trova il primo nodo che contiene il valore specificato.</summary>
      <returns>Primo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenente il valore specificato, se presente; in caso contrario, null.</returns>
      <param name="value">Valore da individuare nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.FindLast(`0)">
      <summary>Trova l'ultimo nodo che contiene il valore specificato.</summary>
      <returns>Ultimo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenente il valore specificato, se presente; in caso contrario, null.</returns>
      <param name="value">Valore da individuare nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.First">
      <summary>Ottiene il primo nodo dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Primo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere l’oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" /> per l'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Last">
      <summary>Ottiene l'ultimo nodo dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Ultimo nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" /> dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Rimuove il nodo specificato da <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Oggetto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> da rimuovere da <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> non è presente nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" /> corrente.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(`0)">
      <summary>Rimuove dall'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" /> la prima occorrenza del valore specificato.</summary>
      <returns>true se l'elemento contenente il valore indicato nel parametro <paramref name="value" /> viene rimosso correttamente; in caso contrario, false.  Questo metodo restituisce anche false se <paramref name="value" /> non è stato trovato nell'interfaccia <see cref="T:System.Collections.Generic.LinkedList`1" /> originale.</returns>
      <param name="value">Valore da rimuovere dall'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveFirst">
      <summary>Rimuove il nodo all'inizio dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" /> è vuoto.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveLast">
      <summary>Rimuove il nodo alla fine dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">L'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" /> è vuoto.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Aggiunge un elemento alla fine dell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="value">Valore da aggiungere alla fine dell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura. In caso contrario, false.  Nell'implementazione predefinita dell'insieme <see cref="T:System.Collections.Generic.LinkedList`1" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere utilizzato per scorrere la raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi dell'interfaccia <see cref="T:System.Collections.ICollection" /> in un oggetto <see cref="T:System.Array" />, a partire da un particolare indice della matrice <see cref="T:System.Array" />.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.ICollection" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="index">Indice in base zero della matrice specificata nel parametro <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> è multidimensionale.- oppure -<paramref name="array" /> non dispone di indicizzazione in base zero.- oppure -Il numero degli elementi nell'oggetto <see cref="T:System.Collections.ICollection" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.- oppure -Non è possibile eseguire automaticamente il cast del tipo dell'oggetto <see cref="T:System.Collections.ICollection" /> di origine al tipo del parametro <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso all'oggetto <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.  Nell'implementazione predefinita dell'insieme <see cref="T:System.Collections.Generic.LinkedList`1" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.  Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.LinkedList`1" />, questa proprietà restituisce sempre l'istanza corrente.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere l'elenco collegato come una raccolta.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.IEnumerator" /> che può essere utilizzata per scorrere l'elenco collegato come una raccolta.</returns>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento dell'insieme <see cref="T:System.Collections.Generic.LinkedList`1" /> in corrispondenza della posizione corrente dell'enumeratore.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'oggetto <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.La classe non può essere ereditata.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedListNode`1">
      <summary>Rappresenta un nodo in una <see cref="T:System.Collections.Generic.LinkedList`1" />.La classe non può essere ereditata.</summary>
      <typeparam name="T">Specifica il tipo di elemento dell'elenco collegato.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedListNode`1.#ctor(`0)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.LinkedListNode`1" /> contenente i dati specificati.</summary>
      <param name="value">Valore contenuto nell'oggetto <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.List">
      <summary>Ottiene l'insieme <see cref="T:System.Collections.Generic.LinkedList`1" /> a cui appartiene il nodo <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</summary>
      <returns>Riferimento all'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" /> a cui appartiene l'oggetto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> oppure null se l'oggetto <see cref="T:System.Collections.Generic.LinkedListNode`1" /> non è collegato.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Next">
      <summary>Ottiene il nodo successivo dell'insieme <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Riferimento al nodo successivo nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />, or null se il nodo corrente è l'ultimo elemento (<see cref="P:System.Collections.Generic.LinkedList`1.Last" />) dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Previous">
      <summary>Ottiene il nodo precedente dell'insieme <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>Riferimento al nodo precedente nell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />, or null se il nodo corrente è primo elemento (<see cref="P:System.Collections.Generic.LinkedList`1.First" />) dell'oggetto <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Value">
      <summary>Ottiene il valore contenuto nel nodo.</summary>
      <returns>Valore contenuto nel nodo.</returns>
    </member>
    <member name="T:System.Collections.Generic.List`1">
      <summary>Rappresenta un elenco di oggetti fortemente tipizzato accessibile per indice.Fornisce metodi per la ricerca, l'ordinamento e la modifica degli elenchi.Per esaminare il codice sorgente di .NET Framework per questo tipo, vedere Origine riferimento.</summary>
      <typeparam name="T">Tipo di elementi contenuti nell'elenco.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.List`1" /> vuota e con capacità iniziale predefinita.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.List`1" /> che contiene gli elementi copiati dalla raccolta specificata e ha la capacità sufficiente per contenere il numero di elementi copiati.</summary>
      <param name="collection">Raccolta i cui elementi vengono copiati nel nuovo elenco.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.List`1" /> vuota e con capacità iniziale specificata.</summary>
      <param name="capacity">Numero di elementi che possono essere archiviati inizialmente nel nuovo elenco.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> è minore di 0. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Add(`0)">
      <summary>Aggiunge un oggetto alla fine di <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="item">Oggetto da aggiungere alla fine di <see cref="T:System.Collections.Generic.List`1" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Aggiunge gli elementi della raccolta specificata alla fine di <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="collection">Raccolta i cui elementi devono essere aggiunti alla fine di <see cref="T:System.Collections.Generic.List`1" />.La raccolta non può essere null, ma può contenere elementi null, se il tipo <paramref name="T" /> è un tipo di riferimento.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.AsReadOnly">
      <summary>Restituisce un wrapper <see cref="T:System.Collections.Generic.IList`1" /> di sola lettura per la raccolta corrente.</summary>
      <returns>Oggetto <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> che funge da wrapper di sola lettura per l'oggetto <see cref="T:System.Collections.Generic.List`1" /> corrente.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>Cerca un elemento in un intervallo di elementi nell'oggetto <see cref="T:System.Collections.Generic.List`1" /> ordinato usando l'operatore di confronto specificato e restituisce l'indice in base zero dell'elemento.</summary>
      <returns>Indice in base zero di <paramref name="item" /> nell'oggetto <see cref="T:System.Collections.Generic.List`1" /> ordinato, se <paramref name="item" /> viene trovato; in caso contrario, un numero negativo che rappresenta il complemento bit per bit dell'indice dell'elemento successivo maggiore di <paramref name="item" /> o, se non è disponibile alcun elemento maggiore, il complemento bit per bit di <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="index">Indice iniziale in base zero dell'intervallo in cui eseguire la ricerca.</param>
      <param name="count">Lunghezza dell'intervallo in cui eseguire la ricerca.</param>
      <param name="item">Oggetto da individuare.Il valore può essere null per i tipi di riferimento.</param>
      <param name="comparer">Implementazione <see cref="T:System.Collections.Generic.IComparer`1" /> da usare durante il confronto di elementi oppure null per usare la proprietà <see cref="P:System.Collections.Generic.Comparer`1.Default" /> dell'operatore di confronto.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.-oppure-<paramref name="count" /> è minore di 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> e <paramref name="count" /> non identificano un intervallo valido in <see cref="T:System.Collections.Generic.List`1" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> è null e mediante la proprietà <see cref="P:System.Collections.Generic.Comparer`1.Default" /> dell'operatore di confronto predefinito non è possibile rilevare un'implementazione dell'interfaccia generica <see cref="T:System.IComparable`1" /> o dell'interfaccia <see cref="T:System.IComparable" /> per il tipo <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0)">
      <summary>Cerca un elemento nell'intero <see cref="T:System.Collections.Generic.List`1" /> ordinato usando l'operatore di confronto predefinito e restituisce l'indice in base zero dell'elemento.</summary>
      <returns>Indice in base zero di <paramref name="item" /> nell'oggetto <see cref="T:System.Collections.Generic.List`1" /> ordinato, se <paramref name="item" /> viene trovato; in caso contrario, un numero negativo che rappresenta il complemento bit per bit dell'indice dell'elemento successivo maggiore di <paramref name="item" /> o, se non è disponibile alcun elemento maggiore, il complemento bit per bit di <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">Oggetto da individuare.Il valore può essere null per i tipi di riferimento.</param>
      <exception cref="T:System.InvalidOperationException">Mediante la proprietà <see cref="P:System.Collections.Generic.Comparer`1.Default" /> dell'operatore di confronto predefinito non è possibile rilevare un'implementazione dell'interfaccia generica <see cref="T:System.IComparable`1" /> o dell'interfaccia <see cref="T:System.IComparable" /> per il tipo <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>Cerca un elemento nell'intero <see cref="T:System.Collections.Generic.List`1" /> ordinato usando l'operatore di confronto specificato e restituisce l'indice in base zero dell'elemento.</summary>
      <returns>Indice in base zero di <paramref name="item" /> nell'oggetto <see cref="T:System.Collections.Generic.List`1" /> ordinato, se <paramref name="item" /> viene trovato; in caso contrario, un numero negativo che rappresenta il complemento bit per bit dell'indice dell'elemento successivo maggiore di <paramref name="item" /> o, se non è disponibile alcun elemento maggiore, il complemento bit per bit di <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">Oggetto da individuare.Il valore può essere null per i tipi di riferimento.</param>
      <param name="comparer">Implementazione <see cref="T:System.Collections.Generic.IComparer`1" /> da usare quando si confrontano gli elementi.-oppure-null per usare la proprietà <see cref="P:System.Collections.Generic.Comparer`1.Default" /> dell'operatore di confronto predefinito.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> è null e mediante la proprietà <see cref="P:System.Collections.Generic.Comparer`1.Default" /> dell'operatore di confronto predefinito non è possibile rilevare un'implementazione dell'interfaccia generica <see cref="T:System.IComparable`1" /> o dell'interfaccia <see cref="T:System.IComparable" /> per il tipo <paramref name="T" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Capacity">
      <summary>Ottiene o imposta il numero totale di elementi che la struttura dati interna è in grado di contenere senza alcun ridimensionamento.</summary>
      <returns>Numero di elementi che <see cref="T:System.Collections.Generic.List`1" /> può contenere prima che sia necessario un ridimensionamento.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.List`1.Capacity" /> viene impostato su un valore che è minore di <see cref="P:System.Collections.Generic.List`1.Count" />. </exception>
      <exception cref="T:System.OutOfMemoryException">La memoria disponibile nel sistema non è sufficiente.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Clear">
      <summary>Rimuove tutti gli elementi da <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Contains(`0)">
      <summary>Determina se un elemento è incluso in <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true se <paramref name="item" /> è presente in <see cref="T:System.Collections.Generic.List`1" />; in caso contrario, false.</returns>
      <param name="item">Oggetto da individuare in <see cref="T:System.Collections.Generic.List`1" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>Copia un intervallo di elementi da <see cref="T:System.Collections.Generic.List`1" /> in una matrice compatibile unidimensionale, a partire dall'indice specificato della matrice di destinazione.</summary>
      <param name="index">Indice in base zero dell'oggetto <see cref="T:System.Collections.Generic.List`1" /> di origine a partire dal quale viene effettuata la copia.</param>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.List`1" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="arrayIndex">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <param name="count">Numero degli elementi da copiare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.-oppure-<paramref name="arrayIndex" /> è minore di 0.-oppure-<paramref name="count" /> è minore di 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> è uguale a o maggiore di <see cref="P:System.Collections.Generic.List`1.Count" /> del <see cref="T:System.Collections.Generic.List`1" /> di origine.-oppure-Il numero degli elementi da <paramref name="index" /> alla fine della classe <see cref="T:System.Collections.Generic.List`1" /> di origine è maggiore dello spazio disponibile da <paramref name="arrayIndex" /> alla fine dell'oggetto <paramref name="array" /> di destinazione. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[])">
      <summary>Copia l'intero oggetto <see cref="T:System.Collections.Generic.List`1" /> in una matrice compatibile unidimensionale, a partire dall'inizio della matrice di destinazione.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.List`1" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il numero di elementi nel <see cref="T:System.Collections.Generic.List`1" /> di origine è maggiore del numero di elementi che la <paramref name="array" /> di destinazione può contenere.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[],System.Int32)">
      <summary>Copia l'intero oggetto <see cref="T:System.Collections.Generic.List`1" /> in una matrice compatibile unidimensionale, a partire dall'indice specificato della matrice di destinazione.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.List`1" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="arrayIndex">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">Il numero degli elementi nell'oggetto <see cref="T:System.Collections.Generic.List`1" /> di origine è maggiore dello spazio disponibile tra <paramref name="arrayIndex" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Count">
      <summary>Ottiene il numero di elementi contenuti in <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Il numero di elementi contenuti in <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Exists(System.Predicate{`0})">
      <summary>Determina se <see cref="T:System.Collections.Generic.List`1" /> contiene gli elementi che corrispondono alle condizioni definite dal predicato specificato.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.List`1" /> contiene uno o più elementi che corrispondono alle condizioni definite dal predicato specificato; in caso contrario, false.</returns>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni degli elementi da cercare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Find(System.Predicate{`0})">
      <summary>Cerca un elemento che soddisfi le condizioni definite nel predicato specificato e restituisce la prima occorrenza all'interno dell'intero oggetto <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Primo elemento che soddisfa le condizioni definite dal predicato specificato, se trovato; in caso contrario, viene restituito il valore predefinito del tipo <paramref name="T" />.</returns>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni dell'elemento da cercare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindAll(System.Predicate{`0})">
      <summary>Recupera tutti gli elementi che soddisfano le condizioni definite nel predicato specificato.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.List`1" /> contenente tutti gli elementi che corrispondono alle condizioni definite dal predicato specificato, se presente; in caso contrario, un oggetto <see cref="T:System.Collections.Generic.List`1" /> vuoto.</returns>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni degli elementi da cercare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Cerca un elemento che soddisfi le condizioni definite dal predicato specificato e restituisce l'indice in base zero della prima occorrenza all'interno dell'intervallo di elementi nell'oggetto <see cref="T:System.Collections.Generic.List`1" /> che inizia dall'indice specificato e contiene il numero indicato di elementi.</summary>
      <returns>Indice in base zero della prima occorrenza di un elemento che corrisponde alle condizioni definite da <paramref name="match" />, se presente; in caso contrario, –1.</returns>
      <param name="startIndex">Indice iniziale in base zero della ricerca.</param>
      <param name="count">Numero di elementi nella sezione in cui effettuare la ricerca.</param>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni dell'elemento da cercare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> non rientra nell'intervallo di indici validi per <see cref="T:System.Collections.Generic.List`1" />.-oppure-<paramref name="count" /> è minore di 0.-oppure-<paramref name="startIndex" /> e <paramref name="count" /> non specificano una sezione valida in <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>Cerca un elemento che soddisfi le condizioni definite dal predicato specificato e restituisce l'indice in base zero della prima occorrenza all'interno dell'intervallo di elementi nell'oggetto <see cref="T:System.Collections.Generic.List`1" /> compreso tra l'indice specificato e l'ultimo elemento.</summary>
      <returns>Indice in base zero della prima occorrenza di un elemento che corrisponde alle condizioni definite da <paramref name="match" />, se presente; in caso contrario, –1.</returns>
      <param name="startIndex">Indice iniziale in base zero della ricerca.</param>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni dell'elemento da cercare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> non rientra nell'intervallo di indici validi per <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Predicate{`0})">
      <summary>Cerca un elemento che corrisponda alle condizioni definite dal predicato specificato e restituisce l'indice in base zero della prima occorrenza all'interno di <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Indice in base zero della prima occorrenza di un elemento che corrisponde alle condizioni definite da <paramref name="match" />, se presente; in caso contrario, –1.</returns>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni dell'elemento da cercare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLast(System.Predicate{`0})">
      <summary>Cerca un elemento che soddisfi le condizioni definite nel predicato specificato e restituisce l'ultima occorrenza all'interno dell'intero oggetto <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Ultimo elemento che soddisfa le condizioni definite dal predicato specificato, se trovato; in caso contrario, viene restituito il valore predefinito del tipo <paramref name="T" />.</returns>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni dell'elemento da cercare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Cerca un elemento che soddisfi le condizioni definite dal predicato specificato e restituisce l'indice in base zero dell'ultima occorrenza all'interno dell'intervallo di elementi in <see cref="T:System.Collections.Generic.List`1" /> che contiene il numero indicato di elementi e termina in corrispondenza dell'indice specificato.</summary>
      <returns>Indice in base zero dell'ultima occorrenza di un elemento che corrisponde alle condizioni definite in <paramref name="match" />, se presente; in caso contrario, –1.</returns>
      <param name="startIndex">Indice iniziale in base zero della ricerca all'indietro.</param>
      <param name="count">Numero di elementi nella sezione in cui effettuare la ricerca.</param>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni dell'elemento da cercare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> non rientra nell'intervallo di indici validi per <see cref="T:System.Collections.Generic.List`1" />.-oppure-<paramref name="count" /> è minore di 0.-oppure-<paramref name="startIndex" /> e <paramref name="count" /> non specificano una sezione valida in <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>Cerca un elemento che soddisfi le condizioni definite dal predicato specificato e restituisce l'indice in base zero dell'ultima occorrenza all'interno dell'intervallo di elementi in <see cref="T:System.Collections.Generic.List`1" /> compreso tra il primo elemento e l'indice specificato.</summary>
      <returns>Indice in base zero dell'ultima occorrenza di un elemento che corrisponde alle condizioni definite in <paramref name="match" />, se presente; in caso contrario, –1.</returns>
      <param name="startIndex">Indice iniziale in base zero della ricerca all'indietro.</param>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni dell'elemento da cercare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> non rientra nell'intervallo di indici validi per <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Predicate{`0})">
      <summary>Cerca un elemento che soddisfi le condizioni definite dal predicato specificato e restituisce l'indice in base zero dell'ultima occorrenza all'interno dell'intero oggetto <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Indice in base zero dell'ultima occorrenza di un elemento che corrisponde alle condizioni definite in <paramref name="match" />, se presente; in caso contrario, –1.</returns>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni dell'elemento da cercare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ForEach(System.Action{`0})">
      <summary>Esegue l'azione specificata su ogni elemento di <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="action">Delegato <see cref="T:System.Action`1" /> da eseguire su ogni elemento di <see cref="T:System.Collections.Generic.List`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetEnumerator">
      <summary>Restituisce un enumeratore che esegue l'iterazione di <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.List`1.Enumerator" /> per <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetRange(System.Int32,System.Int32)">
      <summary>Crea una copia dei riferimenti di un intervallo di elementi nella classe <see cref="T:System.Collections.Generic.List`1" /> di origine.</summary>
      <returns>Copia dei riferimenti di un intervallo di elementi nella classe <see cref="T:System.Collections.Generic.List`1" /> di origine.</returns>
      <param name="index">Indice in base zero di <see cref="T:System.Collections.Generic.List`1" /> in corrispondenza del quale inizia la serie.</param>
      <param name="count">Numero di elementi nell'intervallo.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.-oppure-<paramref name="count" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> e <paramref name="count" /> non identificano un intervallo valido di elementi in <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0)">
      <summary>Cerca l'oggetto specificato e restituisce l'indice in base zero della prima occorrenza nell'intero oggetto <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Indice in base zero della prima occorrenza di <paramref name="item" /> all'interno dell'intero oggetto <see cref="T:System.Collections.Generic.List`1" />, se presente; in caso contrario, -1.</returns>
      <param name="item">Oggetto da individuare in <see cref="T:System.Collections.Generic.List`1" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32)">
      <summary>Cerca l'oggetto specificato e restituisce l'indice in base zero della prima occorrenza all'interno dell'intervallo di elementi nell'oggetto <see cref="T:System.Collections.Generic.List`1" /> che è compreso tra l'indice specificato e l'ultimo elemento.</summary>
      <returns>Indice in base zero della prima occorrenza di <paramref name="item" /> all'interno dell'intervallo di elementi dell'oggetto <see cref="T:System.Collections.Generic.List`1" /> compreso tra <paramref name="index" /> e l'ultimo elemento, se presente; in caso contrario, –1.</returns>
      <param name="item">Oggetto da individuare in <see cref="T:System.Collections.Generic.List`1" />.Il valore può essere null per i tipi di riferimento.</param>
      <param name="index">Indice iniziale in base zero della ricerca.0 (zero) è valido in un elenco vuoto.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> non rientra nell'intervallo di indici validi per <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32,System.Int32)">
      <summary>Cerca l'oggetto specificato e restituisce l'indice in base zero della prima occorrenza all'interno dell'intervallo di elementi nell'oggetto <see cref="T:System.Collections.Generic.List`1" /> che inizia in corrispondenza dell'indice specificato e contiene il numero di elementi specificato.</summary>
      <returns>Indice in base zero della prima occorrenza di <paramref name="item" /> all'interno dell'intervallo di elementi dell'oggetto <see cref="T:System.Collections.Generic.List`1" /> che inizia da <paramref name="index" /> e contiene il numero di elementi corrispondente a <paramref name="count" />, se presente; in caso contrario, –1.</returns>
      <param name="item">Oggetto da individuare in <see cref="T:System.Collections.Generic.List`1" />.Il valore può essere null per i tipi di riferimento.</param>
      <param name="index">Indice iniziale in base zero della ricerca.0 (zero) è valido in un elenco vuoto.</param>
      <param name="count">Numero di elementi nella sezione in cui effettuare la ricerca.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> non rientra nell'intervallo di indici validi per <see cref="T:System.Collections.Generic.List`1" />.-oppure-<paramref name="count" /> è minore di 0.-oppure-<paramref name="index" /> e <paramref name="count" /> non specificano una sezione valida in <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Insert(System.Int32,`0)">
      <summary>Inserisce un elemento in <see cref="T:System.Collections.Generic.List`1" /> in corrispondenza dell'indice specificato.</summary>
      <param name="index">Indice in base zero nel quale <paramref name="item" /> deve essere inserito.</param>
      <param name="item">Oggetto da inserire.Il valore può essere null per i tipi di riferimento.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.-oppure-<paramref name="index" /> è maggiore di <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Inserisce gli elementi di una raccolta in <see cref="T:System.Collections.Generic.List`1" /> in corrispondenza dell'indice specificato.</summary>
      <param name="index">Indice in base zero in corrispondenza del quale devono essere inseriti i nuovi elementi.</param>
      <param name="collection">Raccolta i cui elementi devono essere inseriti in <see cref="T:System.Collections.Generic.List`1" />.La raccolta non può essere null, ma può contenere elementi null, se il tipo <paramref name="T" /> è un tipo di riferimento.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.-oppure-<paramref name="index" /> è maggiore di <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Item(System.Int32)">
      <summary>Ottiene o imposta l'elemento in corrispondenza dell'indice specificato.</summary>
      <returns>Elemento in corrispondenza dell'indice specificato.</returns>
      <param name="index">Indice in base zero dell'elemento da ottenere o impostare.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.-oppure-<paramref name="index" /> è maggiore di o uguale a <see cref="P:System.Collections.Generic.List`1.Count" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0)">
      <summary>Cerca l'oggetto specificato e restituisce l'indice in base zero dell'ultima occorrenza nell'intera classe <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Indice in base zero dell'ultima occorrenza di <paramref name="item" /> all'interno dell'intero oggetto <see cref="T:System.Collections.Generic.List`1" />, se presente; in caso contrario, –1.</returns>
      <param name="item">Oggetto da individuare in <see cref="T:System.Collections.Generic.List`1" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32)">
      <summary>Cerca l'oggetto specificato e restituisce l'indice in base zero dell'ultima occorrenza all'interno dell'intervallo di elementi nella classe <see cref="T:System.Collections.Generic.List`1" /> che è compresa tra il primo elemento e l'indice specificato.</summary>
      <returns>Indice in base zero dell'ultima occorrenza di <paramref name="item" /> all'interno dell'intervallo di elementi dell'oggetto <see cref="T:System.Collections.Generic.List`1" /> compreso tra il primo elemento e <paramref name="index" />, se presente; in caso contrario, –1.</returns>
      <param name="item">Oggetto da individuare in <see cref="T:System.Collections.Generic.List`1" />.Il valore può essere null per i tipi di riferimento.</param>
      <param name="index">Indice iniziale in base zero della ricerca all'indietro.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> non rientra nell'intervallo di indici validi per <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>Cerca l'oggetto specificato e restituisce l'indice in base zero dell'ultima occorrenza all'interno dell'intervallo di elementi della classe <see cref="T:System.Collections.Generic.List`1" /> che contiene il numero di elementi specificato e termina in corrispondenza dell'indice specificato.</summary>
      <returns>Indice in base zero dell'ultima occorrenza di <paramref name="item" /> all'interno dell'intervallo di elementi in <see cref="T:System.Collections.Generic.List`1" /> che contiene un numero di elementi corrispondente a <paramref name="count" /> e termina in corrispondenza di <paramref name="index" />, se presente; in caso contrario, –1.</returns>
      <param name="item">Oggetto da individuare in <see cref="T:System.Collections.Generic.List`1" />.Il valore può essere null per i tipi di riferimento.</param>
      <param name="index">Indice iniziale in base zero della ricerca all'indietro.</param>
      <param name="count">Numero di elementi nella sezione in cui effettuare la ricerca.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> non rientra nell'intervallo di indici validi per <see cref="T:System.Collections.Generic.List`1" />.-oppure-<paramref name="count" /> è minore di 0.-oppure-<paramref name="index" /> e <paramref name="count" /> non specificano una sezione valida in <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Remove(`0)">
      <summary>Rimuove la prima occorrenza di un oggetto specifico da <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true se <paramref name="item" /> viene rimosso correttamente; in caso contrario, false.Questo metodo restituisce anche false se <paramref name="item" /> non è stato trovato nell'oggetto <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="item">Oggetto da rimuovere da <see cref="T:System.Collections.Generic.List`1" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAll(System.Predicate{`0})">
      <summary>Rimuove tutti gli elementi che corrispondono alle condizioni definite dal predicato specificato.</summary>
      <returns>Numero di elementi rimossi da <see cref="T:System.Collections.Generic.List`1" /> .</returns>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni degli elementi da rimuovere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAt(System.Int32)">
      <summary>Rimuove l'elemento in corrispondenza dell'indice specificato di <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="index">Indice in base zero dell'elemento da rimuovere.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.-oppure-<paramref name="index" /> è maggiore di o uguale a <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveRange(System.Int32,System.Int32)">
      <summary>Rimuove un intervallo di elementi da <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="index">Indice iniziale in base zero dell'intervallo di elementi da rimuovere.</param>
      <param name="count">Numero di elementi da rimuovere.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.-oppure-<paramref name="count" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> e <paramref name="count" /> non identificano un intervallo valido di elementi in <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse">
      <summary>Inverte l'ordine degli elementi nell'intero <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse(System.Int32,System.Int32)">
      <summary>Inverte l'ordine degli elementi nell'intervallo specificato.</summary>
      <param name="index">Indice iniziale in base zero dell'intervallo da invertire.</param>
      <param name="count">Numero di elementi nell'intervallo da invertire.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.-oppure-<paramref name="count" /> è minore di 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> e <paramref name="count" /> non identificano un intervallo valido di elementi in <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort">
      <summary>Ordina gli elementi dell'intero oggetto <see cref="T:System.Collections.Generic.List`1" /> usando l'operatore di confronto predefinito.</summary>
      <exception cref="T:System.InvalidOperationException">Mediante la proprietà <see cref="P:System.Collections.Generic.Comparer`1.Default" /> dell'operatore di confronto predefinito non è possibile rilevare un'implementazione dell'interfaccia generica <see cref="T:System.IComparable`1" /> o dell'interfaccia <see cref="T:System.IComparable" /> per il tipo <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>Ordina gli elementi dell'intero <see cref="T:System.Collections.Generic.List`1" /> usando l'operatore di confronto specificato.</summary>
      <param name="comparer">Implementazione <see cref="T:System.Collections.Generic.IComparer`1" /> da usare durante il confronto di elementi oppure null per usare la proprietà <see cref="P:System.Collections.Generic.Comparer`1.Default" /> dell'operatore di confronto.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> è null e mediante la proprietà <see cref="P:System.Collections.Generic.Comparer`1.Default" /> dell'operatore di confronto predefinito non è possibile rilevare l'implementazione dell'interfaccia generica <see cref="T:System.IComparable`1" /> o dell'interfaccia <see cref="T:System.IComparable" /> per il tipo <paramref name="T" />.</exception>
      <exception cref="T:System.ArgumentException">L'implementazione di <paramref name="comparer" /> ha causato un errore durante l'ordinamento.Ad esempio, <paramref name="comparer" /> potrebbe non restituire 0 quando si confronta un elemento con se stesso.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Comparison{`0})">
      <summary>Ordina gli elementi nell'intera classe <see cref="T:System.Collections.Generic.List`1" /> usando l'oggetto <see cref="T:System.Comparison`1" /> specificato.</summary>
      <param name="comparison">Oggetto <see cref="T:System.Comparison`1" /> da usare quando si confrontano gli elementi.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" /> è null.</exception>
      <exception cref="T:System.ArgumentException">L'implementazione di <paramref name="comparison" /> ha causato un errore durante l'ordinamento.Ad esempio, <paramref name="comparison" /> potrebbe non restituire 0 quando si confronta un elemento con se stesso.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Ordina gli elementi di un intervallo di elementi di <see cref="T:System.Collections.Generic.List`1" /> usando l'operatore di confronto specificato.</summary>
      <param name="index">Indice iniziale in base zero dell'intervallo da ordinare.</param>
      <param name="count">Lunghezza dell'intervallo da ordinare.</param>
      <param name="comparer">Implementazione <see cref="T:System.Collections.Generic.IComparer`1" /> da usare durante il confronto di elementi oppure null per usare la proprietà <see cref="P:System.Collections.Generic.Comparer`1.Default" /> dell'operatore di confronto.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.-oppure-<paramref name="count" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> e <paramref name="count" /> non specificano un intervallo valido in <see cref="T:System.Collections.Generic.List`1" />.-oppure-L'implementazione di <paramref name="comparer" /> ha causato un errore durante l'ordinamento.Ad esempio, <paramref name="comparer" /> potrebbe non restituire 0 quando si confronta un elemento con se stesso.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> è null e mediante la proprietà <see cref="P:System.Collections.Generic.Comparer`1.Default" /> dell'operatore di confronto predefinito non è possibile rilevare l'implementazione dell'interfaccia generica <see cref="T:System.IComparable`1" /> o dell'interfaccia <see cref="T:System.IComparable" /> per il tipo <paramref name="T" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.List`1" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi di <see cref="T:System.Collections.ICollection" /> in <see cref="T:System.Array" /> a partire da un particolare indice <see cref="T:System.Array" />.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.ICollection" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="arrayIndex">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> è multidimensionale.-oppure-<paramref name="array" /> non dispone di indicizzazione in base zero.-oppure-Il numero degli elementi nell'oggetto <see cref="T:System.Collections.ICollection" /> di origine è maggiore dello spazio disponibile tra <paramref name="arrayIndex" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.-oppure-Non è possibile eseguire automaticamente il cast del tipo dell'oggetto <see cref="T:System.Collections.ICollection" /> di origine al tipo del parametro <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.List`1" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.List`1" />, questa proprietà restituisce sempre l'istanza corrente.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.IEnumerator" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Add(System.Object)">
      <summary>Aggiunge un elemento a <see cref="T:System.Collections.IList" />.</summary>
      <returns>Posizione in cui è stato inserito il nuovo elemento.</returns>
      <param name="item">Oggetto <see cref="T:System.Object" /> da aggiungere a <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> è un tipo non assegnabile all'oggetto <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Contains(System.Object)">
      <summary>Stabilisce se <see cref="T:System.Collections.IList" /> contiene un valore specifico.</summary>
      <returns>true se <paramref name="item" /> è presente in <see cref="T:System.Collections.IList" />; in caso contrario, false.</returns>
      <param name="item">Oggetto <see cref="T:System.Object" /> da individuare in <see cref="T:System.Collections.IList" />.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>Determina l'indice di un elemento specifico in <see cref="T:System.Collections.IList" />.</summary>
      <returns>Indice di <paramref name="item" />, se presente nell'elenco; in caso contrario, -1.</returns>
      <param name="item">Oggetto da individuare in <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> è un tipo non assegnabile all'oggetto <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Inserisce un elemento in <see cref="T:System.Collections.IList" /> in corrispondenza dell'indice specificato.</summary>
      <param name="index">Indice in base zero nel quale <paramref name="item" /> deve essere inserito.</param>
      <param name="item">Oggetto da inserire in <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> non è un indice valido nell'interfaccia <see cref="T:System.Collections.IList" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> è un tipo non assegnabile all'oggetto <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsFixedSize">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.IList" /> ha dimensioni fisse.</summary>
      <returns>true se <see cref="T:System.Collections.IList" /> ha dimensioni fisse; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.List`1" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.IList" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.IList" /> è di sola lettura; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.List`1" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#Item(System.Int32)">
      <summary>Ottiene o imposta l'elemento in corrispondenza dell'indice specificato.</summary>
      <returns>Elemento in corrispondenza dell'indice specificato.</returns>
      <param name="index">Indice in base zero dell'elemento da ottenere o impostare.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> non è un indice valido nell'interfaccia <see cref="T:System.Collections.IList" />.</exception>
      <exception cref="T:System.ArgumentException">La proprietà viene impostata e il tipo del parametro <paramref name="value" /> non è assegnabile all'interfaccia <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Remove(System.Object)">
      <summary>Rimuove la prima occorrenza di un oggetto specifico da <see cref="T:System.Collections.IList" />.</summary>
      <param name="item">Oggetto da rimuovere da <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" /> è un tipo non assegnabile all'oggetto <see cref="T:System.Collections.IList" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ToArray">
      <summary>Copia gli elementi di <see cref="T:System.Collections.Generic.List`1" /> in una nuova matrice.</summary>
      <returns>Matrice contenente le copie degli elementi di <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrimExcess">
      <summary>Imposta la capacità sul numero reale di elementi nell'oggetto <see cref="T:System.Collections.Generic.List`1" />, se tale numero è inferiore a un valore soglia.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrueForAll(System.Predicate{`0})">
      <summary>Determina se ogni elemento nell'oggetto <see cref="T:System.Collections.Generic.List`1" /> corrisponde alle condizioni definite dal predicato specificato.</summary>
      <returns>true se ogni elemento nell'oggetto <see cref="T:System.Collections.Generic.List`1" /> corrisponde alle condizioni definite dal predicato specificato; in caso contrario, false.Se l'elenco è privo di elementi, il valore restituito è true.</returns>
      <param name="match">Delegato <see cref="T:System.Predicate`1" /> che definisce le condizioni per verificare gli elementi.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
    </member>
    <member name="T:System.Collections.Generic.List`1.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento dell'insieme <see cref="T:System.Collections.Generic.List`1" /> in corrispondenza della posizione corrente dell'enumeratore.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'oggetto <see cref="T:System.Collections.Generic.List`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo dell'oggetto <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento dell'insieme <see cref="T:System.Collections.Generic.List`1" /> in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="T:System.Collections.Generic.Queue`1">
      <summary>Rappresenta una raccolta di oggetti FIFO (First-In First-Out).</summary>
      <typeparam name="T">Specifica il tipo di elementi della coda.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.Queue`1" /> vuota e con capacità iniziale predefinita.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.Queue`1" /> che contiene gli elementi copiati dalla raccolta specificata e ha la capacità sufficiente per contenere il numero di elementi copiati.</summary>
      <param name="collection">Raccolta i cui elementi sono copiati nel nuovo oggetto <see cref="T:System.Collections.Generic.Queue`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.Queue`1" /> vuota e con capacità iniziale specificata.</summary>
      <param name="capacity">Il numero iniziale degli elementi che <see cref="T:System.Collections.Generic.Queue`1" /> può contenere.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Clear">
      <summary>Rimuove tutti gli oggetti da <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Contains(`0)">
      <summary>Determina se un elemento è incluso in <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>true se <paramref name="item" /> è presente in <see cref="T:System.Collections.Generic.Queue`1" />; in caso contrario, false.</returns>
      <param name="item">Oggetto da individuare in <see cref="T:System.Collections.Generic.Queue`1" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.CopyTo(`0[],System.Int32)">
      <summary>Copia gli elementi di <see cref="T:System.Collections.Generic.Queue`1" /> in un oggetto <see cref="T:System.Array" /> unidimensionale esistente, partendo dall'indice della matrice specificata.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.Queue`1" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="arrayIndex">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Queue`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Count">
      <summary>Ottiene il numero di elementi contenuti in <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>Il numero di elementi contenuti in <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Dequeue">
      <summary>Rimuove e restituisce l'oggetto all'inizio di <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>Oggetto rimosso dall'inizio di <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enqueue(`0)">
      <summary>Aggiunge un oggetto alla fine di <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <param name="item">Oggetto da aggiungere a <see cref="T:System.Collections.Generic.Queue`1" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.GetEnumerator">
      <summary>Restituisce un enumeratore che esegue l'iterazione di <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1.Enumerator" /> per l'oggetto <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Peek">
      <summary>Restituisce l'oggetto all'inizio di <see cref="T:System.Collections.Generic.Queue`1" /> senza rimuoverlo.</summary>
      <returns>Oggetto all'inizio di <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi di <see cref="T:System.Collections.ICollection" /> in <see cref="T:System.Array" /> a partire da un particolare indice <see cref="T:System.Array" />.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.ICollection" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="index">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.Queue`1" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.Queue`1" />, questa proprietà restituisce sempre l'istanza corrente.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.IEnumerator" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.ToArray">
      <summary>Copia gli elementi di <see cref="T:System.Collections.Generic.Queue`1" /> in una nuova matrice.</summary>
      <returns>Nuova matrice contenente gli elementi copiati da <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.TrimExcess">
      <summary>Imposta la capacità sul numero effettivo di elementi contenuti nell'oggetto <see cref="T:System.Collections.Generic.Queue`1" />, se questo numero è inferiore al 90 per cento della capacità corrente.</summary>
    </member>
    <member name="T:System.Collections.Generic.Queue`1.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento dell'insieme <see cref="T:System.Collections.Generic.Queue`1" /> in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'oggetto <see cref="T:System.Collections.Generic.Queue`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo dell'oggetto <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2">
      <summary>Rappresenta una raccolta di coppie chiave/valore ordinate in base alla chiave. </summary>
      <typeparam name="TKey">Tipo di chiavi nel dizionario.</typeparam>
      <typeparam name="TValue">Tipo di valori nel dizionario.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.SortedDictionary`2" /> e usa l'implementazione <see cref="T:System.Collections.Generic.IComparer`1" /> predefinita per il tipo di chiave.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.SortedDictionary`2" /> e usa l'implementazione <see cref="T:System.Collections.Generic.IComparer`1" /> specificata per il confronto delle chiavi.</summary>
      <param name="comparer">Implementazione di <see cref="T:System.Collections.Generic.IComparer`1" /> da usare per confrontare le chiavi oppure null per usare l'oggetto <see cref="T:System.Collections.Generic.Comparer`1" /> predefinito per il tipo di chiave.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contenente elementi copiati dalla classe <see cref="T:System.Collections.Generic.IDictionary`2" /> specificata e viene usata l'implementazione <see cref="T:System.Collections.Generic.IComparer`1" /> predefinita per il tipo di chiave.</summary>
      <param name="dictionary">Oggetto <see cref="T:System.Collections.Generic.IDictionary`2" /> i cui elementi sono copiati nel nuovo oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="dictionary" /> contiene una o più chiavi duplicate.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contenente elementi copiati dalla classe <see cref="T:System.Collections.Generic.IDictionary`2" /> specificata e viene usata l'implementazione <see cref="T:System.Collections.Generic.IComparer`1" /> specificata per confrontare le chiavi.</summary>
      <param name="dictionary">Oggetto <see cref="T:System.Collections.Generic.IDictionary`2" /> i cui elementi sono copiati nel nuovo oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <param name="comparer">Implementazione di <see cref="T:System.Collections.Generic.IComparer`1" /> da usare per confrontare le chiavi oppure null per usare l'oggetto <see cref="T:System.Collections.Generic.Comparer`1" /> predefinito per il tipo di chiave.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="dictionary" /> contiene una o più chiavi duplicate.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Add(`0,`1)">
      <summary>Aggiunge un elemento con la chiave e il valore specificati al metodo <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <param name="key">Chiave dell'elemento da aggiungere.</param>
      <param name="value">Valore dell'elemento da aggiungere.Il valore può essere null per i tipi di riferimento.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.ArgumentException">In <see cref="T:System.Collections.Generic.SortedDictionary`2" /> è già presente un elemento con la stessa chiave.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Clear">
      <summary>Rimuove tutti gli elementi da <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Comparer">
      <summary>Ottiene l'interfaccia <see cref="T:System.Collections.Generic.IComparer`1" /> usata per ordinare gli elementi della classe <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.Generic.IComparer`1" /> utilizzata per ordinare gli elementi della classe <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsKey(`0)">
      <summary>Determina se <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contiene un elemento con la chiave specificata.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contiene un elemento con la chiave specificata; in caso contrario, false.</returns>
      <param name="key">Chiave da individuare in <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsValue(`1)">
      <summary>Determina se la raccolta <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contiene un elemento con il valore specificato.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contiene un elemento con il valore specificato; in caso contrario, false.</returns>
      <param name="value">Valore da individuare in <see cref="T:System.Collections.Generic.SortedDictionary`2" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copia gli elementi di <see cref="T:System.Collections.Generic.SortedDictionary`2" /> nella matrice di strutture <see cref="T:System.Collections.Generic.KeyValuePair`2" /> indicata, a partire dall'indice specificato.</summary>
      <param name="array">Matrice unidimensionale di strutture <see cref="T:System.Collections.Generic.KeyValuePair`2" /> in cui effettuare la copia degli elementi della raccolta <see cref="T:System.Collections.Generic.SortedDictionary`2" /> corrente. La matrice deve avere indice in base zero.</param>
      <param name="index">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">Il numero degli elementi nell'oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Count">
      <summary>Ottiene il numero di coppie chiave/valore contenute in <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Numero di coppie chiave/valore contenute in <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.GetEnumerator">
      <summary>Restituisce un enumeratore che esegue l'iterazione di <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" /> per <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Item(`0)">
      <summary>Ottiene o imposta il valore associato alla chiave specificata.</summary>
      <returns>Valore associato alla chiave specificata.Se la chiave specificata non viene trovata, un'operazione Get genera un'eccezione <see cref="T:System.Collections.Generic.KeyNotFoundException" />, mentre un'operazione Set crea una nuovo elemento con la chiave specificata.</returns>
      <param name="key">Chiave del valore da ottenere o impostare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Durante il recupero della proprietà, la chiave indicata nel parametro <paramref name="key" /> non è stata trovata nella raccolta.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Keys">
      <summary>Ottiene una raccolta contenente le chiavi della classe <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Raccolta <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> contenente le chiavi della classe <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Remove(`0)">
      <summary>Rimuove l'elemento con la chiave specificata da <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>true se l'elemento viene rimosso correttamente; in caso contrario, false.Questo metodo restituisce anche false se il parametro <paramref name="key" /> non viene trovato nella classe <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
      <param name="key">Chiave dell'elemento da rimuovere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Aggiunge un elemento a <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="keyValuePair">Struttura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> da aggiungere all'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" /> è null.</exception>
      <exception cref="T:System.ArgumentException">In <see cref="T:System.Collections.Generic.SortedDictionary`2" /> è già presente un elemento con la stessa chiave.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Stabilisce se l'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> contiene una coppia chiave/valore specifica.</summary>
      <returns>true se <paramref name="keyValuePair" /> è presente in <see cref="T:System.Collections.Generic.ICollection`1" />; in caso contrario, false.</returns>
      <param name="keyValuePair">Struttura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> da individuare nell'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedDictionary`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Rimuove dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> la prima occorrenza dell'elemento specificato.</summary>
      <returns>true se <paramref name="keyValuePair" /> è stato rimosso correttamente da <see cref="T:System.Collections.Generic.ICollection`1" />; in caso contrario, false.Questo metodo restituisce anche false se <paramref name="keyValuePair" /> non è stato trovato nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">Struttura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> da rimuovere dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Ottiene <see cref="T:System.Collections.Generic.ICollection`1" /> contenente le chiavi di <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> contenente le chiavi dell'interfaccia <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Ottiene <see cref="T:System.Collections.Generic.ICollection`1" /> contenente i valori in <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> contenente i valori dell'interfaccia <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di eseguire l'iterazione di una raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.IEnumerator" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Ottiene una raccolta contenente le chiavi del<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>Un insieme contenente le chiavi nel<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Ottiene una raccolta contenente i valori del<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>Un insieme contenente i valori del<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi dell'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> in una matrice, iniziando dall'indice di matrice specificato.</summary>
      <param name="array">Matrice unidimensionale che è la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.La matrice deve avere un'indicizzazione in base zero.</param>
      <param name="index">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> è multidimensionale.-oppure-<paramref name="array" /> non dispone di indicizzazione in base zero.-oppure-Il numero degli elementi nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.-oppure-Non è possibile eseguire automaticamente il cast del tipo dell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" /> di origine al tipo del parametro <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedDictionary`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />. </returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Aggiunge un elemento con la chiave e il valore forniti all'interfaccia <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Oggetto da usare come chiave dell'elemento da aggiungere.</param>
      <param name="value">Oggetto da usare come valore dell'elemento da aggiungere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il tipo della chiave indicata nel parametro <paramref name="key" /> non è assegnabile al tipo di chiave <paramref name="TKey" /> dell'insieme <see cref="T:System.Collections.IDictionary" />.-oppure-<paramref name="value" /> è un tipo che non è possibile assegnare al tipo di valore <paramref name="TValue" /> dell'oggetto <see cref="T:System.Collections.IDictionary" />.-oppure-In <see cref="T:System.Collections.IDictionary" /> è già presente un elemento con la stessa chiave.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determina se <see cref="T:System.Collections.IDictionary" /> contiene un elemento con la chiave specificata.</summary>
      <returns>true se <see cref="T:System.Collections.IDictionary" /> contiene un elemento contenente la chiave; in caso contrario, false.</returns>
      <param name="key">Chiave da individuare in <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Restituisce <see cref="T:System.Collections.IDictionaryEnumerator" /> per <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionaryEnumerator" /> per l'oggetto <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.IDictionary" /> ha dimensioni fisse.</summary>
      <returns>true se <see cref="T:System.Collections.IDictionary" /> è di dimensioni fisse; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedDictionary`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.IDictionary" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.IDictionary" /> è di sola lettura; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedDictionary`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Ottiene o imposta l'elemento con la chiave specificata.</summary>
      <returns>L'elemento con la chiave specificata, o null se <paramref name="key" /> non è presente nel dizionario o se il tipo di <paramref name="key" /> non può essere assegnato al tipo di chiave <paramref name="TKey" /> della classe <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
      <param name="key">Chiave dell'elemento da ottenere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Viene assegnato un valore e il tipo di <paramref name="key" /> non può essere assegnato al tipo di chiave <paramref name="TKey" /> della classe<see cref="T:System.Collections.Generic.SortedDictionary`2" />.-oppure-Viene assegnato un valore e il tipo di <paramref name="value" /> non può essere assegnato al tipo di valore <paramref name="TValue" /> della classe<see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Keys">
      <summary>Ottiene <see cref="T:System.Collections.ICollection" /> contenente le chiavi di <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.ICollection" /> contenente le chiavi dell'interfaccia <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Rimuove l'elemento con la chiave specificata da <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Chiave dell'elemento da rimuovere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Values">
      <summary>Ottiene <see cref="T:System.Collections.ICollection" /> contenente i valori in <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.ICollection" /> contenente i valori dell'interfaccia <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere la raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.TryGetValue(`0,`1@)">
      <summary>Ottiene il valore associato alla chiave specificata.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contiene un elemento con la chiave specificata; in caso contrario, false.</returns>
      <param name="key">Chiave del valore da ottenere.</param>
      <param name="value">Quando questo metodo viene restituito, il valore associato alla chiave specificata nel caso in cui la chiave venga trovata; in caso contrario, il valore predefinito per il tipo di parametro <paramref name="value" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Values">
      <summary>Ottiene una raccolta contenente i valori di <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>Raccolta <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> contenente i valori di <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento dell'insieme <see cref="T:System.Collections.Generic.SortedDictionary`2" /> in corrispondenza della posizione corrente dell'enumeratore.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo dell'oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore come una struttura <see cref="T:System.Collections.DictionaryEntry" />.</summary>
      <returns>Elemento nell'insieme in corrispondenza della posizione corrente del dizionario, restituito come una struttura <see cref="T:System.Collections.DictionaryEntry" />.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Ottiene la chiave dell'elemento nella posizione corrente dell'enumeratore.</summary>
      <returns>Ottiene la chiave dell'elemento nell'insieme in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Ottiene il valore dell'elemento nella posizione corrente dell'enumeratore.</summary>
      <returns>Valore dell'elemento nell'insieme in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection">
      <summary>Rappresenta la raccolta di chiavi in una classe <see cref="T:System.Collections.Generic.SortedDictionary`2" />.La classe non può essere ereditata.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> in cui sono riprodotte le chiavi dell'oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2" /> specificato.</summary>
      <param name="dictionary">Classe <see cref="T:System.Collections.Generic.SortedDictionary`2" /> le cui chiavi vengono riprodotte nella nuova classe <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Consente di copiare gli elementi dell'oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> in una matrice unidimensionale esistente, a partire dall'indice della matrice specificata.</summary>
      <param name="array">Matrice unidimensionale che è la destinazione degli elementi copiati dall'<see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.L'indicizzazione della matrice deve essere in base zero.</param>
      <param name="index">Indice in base zero della matrice specificata nel parametro <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">Il numero degli elementi nell'oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Count">
      <summary>Ottiene il numero di elementi contenuti in <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>Il numero di elementi contenuti in <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.GetEnumerator">
      <summary>Restituisce un enumeratore che scorre la classe <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>Struttura <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> dell'insieme <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Aggiunge un elemento all'insieme <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Oggetto da aggiungere alla <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Generata sempre; l'insieme è in sola lettura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Consente di rimuovere tutti gli elementi dal controllo <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Generata sempre; l'insieme è in sola lettura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Determina se l'insieme <see cref="T:System.Collections.Generic.ICollection`1" /> contiene il valore specificato.</summary>
      <returns>true se il valore indicato nel parametro <paramref name="item" /> è presente nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />, in caso contrario false.</returns>
      <param name="item">Oggetto da individuare nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura. In caso contrario, false.  Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Rimuove la prima occorrenza di un oggetto specifico dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true, se <paramref name="item" /> viene correttamente rimosso dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />; in caso contrario, false.Questo metodo restituisce anche false se il parametro <paramref name="item" /> non viene trovato nella classe <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Oggetto da rimuovere dall'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Generata sempre; l'insieme è in sola lettura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere la raccolta.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere utilizzato per scorrere la raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi dell'oggetto <see cref="T:System.Collections.ICollection" /> in una matrice, iniziando da un particolare indice di matrice.</summary>
      <param name="array">Matrice unidimensionale che è la destinazione degli elementi copiati dall'<see cref="T:System.Collections.ICollection" />.L'indicizzazione della matrice deve essere in base zero.</param>
      <param name="index">Indice in base zero della matrice specificata nel parametro <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> è multidimensionale.- oppure -<paramref name="array" /> non dispone di indicizzazione in base zero.- oppure -Il numero degli elementi nell'oggetto <see cref="T:System.Collections.ICollection" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.- oppure -Non è possibile eseguire automaticamente il cast del tipo dell'oggetto <see cref="T:System.Collections.ICollection" /> di origine al tipo del parametro <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso all'oggetto <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.  Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.  Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, questa proprietà restituisce sempre l'istanza corrente.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere la raccolta.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.IEnumerator" /> che può essere utilizzata per scorrere la raccolta.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento dell'insieme <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> in corrispondenza della posizione corrente dell'enumeratore.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo dell'oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection">
      <summary>Rappresenta la raccolta di valori in una classe <see cref="T:System.Collections.Generic.SortedDictionary`2" />.Questa classe non può essere ereditata.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> in cui sono riprodotti i valori dell'insieme <see cref="T:System.Collections.Generic.SortedDictionary`2" /> specificato.</summary>
      <param name="dictionary">Insieme <see cref="T:System.Collections.Generic.SortedDictionary`2" /> i cui valori sono riportati nel nuovo oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Copia gli elementi dell'insieme <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> in una matrice unidimensionale esistente, a partire dall'indice di matrice specificato.</summary>
      <param name="array">Matrice unidimensionale che è la destinazione degli elementi copiati dall'<see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.L'indicizzazione della matrice deve essere in base zero.</param>
      <param name="index">Indice in base zero della matrice specificata nel parametro <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">Il numero degli elementi nell'oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Count">
      <summary>Ottiene il numero di elementi contenuti in <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>Il numero di elementi contenuti in <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.GetEnumerator">
      <summary>Restituisce un enumeratore che scorre la classe <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>Struttura <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> dell'insieme <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Aggiunge un elemento all'insieme <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <param name="item">Oggetto da aggiungere alla <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Generata sempre; l'insieme è in sola lettura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Consente di rimuovere tutti gli elementi dal controllo <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <exception cref="T:System.NotSupportedException">Generata sempre; l'insieme è in sola lettura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Stabilisce se l'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> contiene un valore specifico.</summary>
      <returns>true se il valore indicato nel parametro <paramref name="item" /> è presente nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />, in caso contrario false.</returns>
      <param name="item">Oggetto da individuare nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura. In caso contrario, false.  Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Rimuove la prima occorrenza di un oggetto specifico dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true, se <paramref name="item" /> viene correttamente rimosso dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />; in caso contrario, false.Questo metodo restituisce anche false se il parametro <paramref name="item" /> non viene trovato nella classe <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Oggetto da rimuovere dall'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">Generata sempre; l'insieme è in sola lettura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Rimuove la prima occorrenza di un oggetto specifico dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.  Questa implementazione genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <returns>true, se <paramref name="item" /> viene correttamente rimosso dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />; in caso contrario, false.Questo metodo restituisce anche false se il parametro <paramref name="item" /> non viene trovato nella classe <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <exception cref="T:System.NotSupportedException">Generata sempre; l'insieme è in sola lettura.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi dell'oggetto <see cref="T:System.Collections.ICollection" /> in una matrice, iniziando da un particolare indice di matrice.</summary>
      <param name="array">Matrice unidimensionale che è la destinazione degli elementi copiati dall'<see cref="T:System.Collections.ICollection" />.L'indicizzazione della matrice deve essere in base zero.</param>
      <param name="index">Indice in base zero della matrice specificata nel parametro <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> è multidimensionale.- oppure -<paramref name="array" /> non dispone di indicizzazione in base zero.- oppure -Il numero degli elementi nell'oggetto <see cref="T:System.Collections.ICollection" /> di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.- oppure -Non è possibile eseguire automaticamente il cast del tipo dell'oggetto <see cref="T:System.Collections.ICollection" /> di origine al tipo del parametro <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso all'oggetto <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.  Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.  Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, questa proprietà restituisce sempre l'istanza corrente.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere la raccolta.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.IEnumerator" /> che può essere utilizzata per scorrere la raccolta.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento dell'insieme <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> in corrispondenza della posizione corrente dell'enumeratore.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo dell'oggetto <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedList`2">
      <summary>Rappresenta una raccolta di coppie chiave/valore ordinate per chiave in base all'implementazione <see cref="T:System.Collections.Generic.IComparer`1" /> associata. </summary>
      <typeparam name="TKey">Tipo di chiavi nella raccolta.</typeparam>
      <typeparam name="TValue">Tipo di valori nella raccolta.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.SortedList`2" />, con la capacità iniziale predefinita e che usa l'interfaccia <see cref="T:System.Collections.Generic.IComparer`1" /> specificata.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.SortedList`2" />, con la capacità iniziale predefinita e che usa l'interfaccia <see cref="T:System.Collections.Generic.IComparer`1" /> specificata.</summary>
      <param name="comparer">Implementazione di <see cref="T:System.Collections.Generic.IComparer`1" /> da usare quando si confrontano le chiavi.-oppure-null per utilizzare l'oggetto <see cref="T:System.Collections.Generic.Comparer`1" /> per il tipo della chiave.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.SortedList`2" /> che contiene gli elementi copiati dall'interfaccia <see cref="T:System.Collections.Generic.IDictionary`2" /> specificata, la cui capacità è sufficiente a contenere il numero di elementi copiati e che usa l'interfaccia <see cref="T:System.Collections.Generic.IComparer`1" /> predefinita.</summary>
      <param name="dictionary">Oggetto <see cref="T:System.Collections.Generic.IDictionary`2" /> i cui elementi sono copiati nel nuovo oggetto <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="dictionary" /> contiene una o più chiavi duplicate.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.SortedList`2" /> che contiene gli elementi copiati dall'interfaccia <see cref="T:System.Collections.Generic.IDictionary`2" /> specificata, la cui capacità è sufficiente a contenere il numero di elementi copiati e che usa l'interfaccia <see cref="T:System.Collections.Generic.IComparer`1" /> specificata.</summary>
      <param name="dictionary">Oggetto <see cref="T:System.Collections.Generic.IDictionary`2" /> i cui elementi sono copiati nel nuovo oggetto <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <param name="comparer">Implementazione di <see cref="T:System.Collections.Generic.IComparer`1" /> da usare quando si confrontano le chiavi.-oppure-null per utilizzare l'oggetto <see cref="T:System.Collections.Generic.Comparer`1" /> per il tipo della chiave.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="dictionary" /> contiene una o più chiavi duplicate.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.SortedList`2" />, con la capacità iniziale predefinita e che usa l'interfaccia <see cref="T:System.Collections.Generic.IComparer`1" /> specificata.</summary>
      <param name="capacity">Il numero iniziale degli elementi che <see cref="T:System.Collections.Generic.SortedList`2" /> può contenere.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> è minore di zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Inizializza una nuova istanza vuota della classe <see cref="T:System.Collections.Generic.SortedList`2" />, con la capacità iniziale predefinita e che usa l'interfaccia <see cref="T:System.Collections.Generic.IComparer`1" /> specificata.</summary>
      <param name="capacity">Il numero iniziale degli elementi che <see cref="T:System.Collections.Generic.SortedList`2" /> può contenere.</param>
      <param name="comparer">Implementazione di <see cref="T:System.Collections.Generic.IComparer`1" /> da usare quando si confrontano le chiavi.-oppure-null per utilizzare l'oggetto <see cref="T:System.Collections.Generic.Comparer`1" /> per il tipo della chiave.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> è minore di zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Add(`0,`1)">
      <summary>Aggiunge un elemento con la chiave e il valore specificati al metodo <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <param name="key">Chiave dell'elemento da aggiungere.</param>
      <param name="value">Valore dell'elemento da aggiungere.Il valore può essere null per i tipi di riferimento.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.ArgumentException">In <see cref="T:System.Collections.Generic.SortedList`2" /> è già presente un elemento con la stessa chiave.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Capacity">
      <summary>Ottiene o imposta il numero di elementi che <see cref="T:System.Collections.Generic.SortedList`2" /> può contenere.</summary>
      <returns>Numero degli elementi che <see cref="T:System.Collections.Generic.SortedList`2" /> può contenere.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.SortedList`2.Capacity" /> viene impostato su un valore che è minore di <see cref="P:System.Collections.Generic.SortedList`2.Count" />.</exception>
      <exception cref="T:System.OutOfMemoryException">La memoria disponibile nel sistema non è sufficiente.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Clear">
      <summary>Rimuove tutti gli elementi da <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Comparer">
      <summary>Ottiene l'interfaccia <see cref="T:System.Collections.Generic.IComparer`1" /> per l'elenco ordinato. </summary>
      <returns>Oggetto <see cref="T:System.IComparable`1" /> dell'oggetto <see cref="T:System.Collections.Generic.SortedList`2" /> corrente</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsKey(`0)">
      <summary>Determina se l'oggetto <see cref="T:System.Collections.Generic.SortedList`2" /> contiene una chiave specifica.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.SortedList`2" /> contiene un elemento con la chiave specificata; in caso contrario, false.</returns>
      <param name="key">Chiave da individuare in <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsValue(`1)">
      <summary>Stabilisce se <see cref="T:System.Collections.Generic.SortedList`2" /> contiene un valore specifico.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.SortedList`2" /> contiene un elemento con il valore specificato; in caso contrario, false.</returns>
      <param name="value">Valore da individuare in <see cref="T:System.Collections.Generic.SortedList`2" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Count">
      <summary>Ottiene il numero di coppie chiave/valore contenute in <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Numero di coppie chiave/valore contenute in <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.GetEnumerator">
      <summary>Restituisce un enumeratore che esegue l'iterazione di <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.Generic.IEnumerator`1" /> di tipo <see cref="T:System.Collections.Generic.KeyValuePair`2" /> per la raccolta <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfKey(`0)">
      <summary>Cerca la chiave specificata e restituisce l'indice in base zero all'interno dell'intera raccolta <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Indice in base zero della chiave specificata nel parametro <paramref name="key" /> all'interno dell'intera raccolta <see cref="T:System.Collections.Generic.SortedList`2" />, se presente; in caso contrario, -1.</returns>
      <param name="key">Chiave da individuare in <see cref="T:System.Collections.Generic.SortedList`2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfValue(`1)">
      <summary>Cerca il valore specificato e restituisce l'indice in base zero della prima occorrenza all'interno dell'intera raccolta <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Indice in base zero della prima occorrenza di <paramref name="value" /> all'interno dell'intero oggetto <see cref="T:System.Collections.Generic.SortedList`2" />, se presente; in caso contrario, -1.</returns>
      <param name="value">Valore da individuare in <see cref="T:System.Collections.Generic.SortedList`2" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Item(`0)">
      <summary>Ottiene o imposta il valore associato alla chiave specificata.</summary>
      <returns>Valore associato alla chiave specificata.Se la chiave specificata non viene trovata, un'operazione get genera <see cref="T:System.Collections.Generic.KeyNotFoundException" /> e un'operazione set crea un nuovo elemento con la chiave specificata.</returns>
      <param name="key">Chiave di cui si deve ottenere o impostare il valore.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Durante il recupero della proprietà, la chiave indicata nel parametro <paramref name="key" /> non è stata trovata nella raccolta.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Keys">
      <summary>Ottiene una raccolta contenente le chiavi in <see cref="T:System.Collections.Generic.SortedList`2" /> nell'ordine specificato.</summary>
      <returns>Raccolta <see cref="T:System.Collections.Generic.IList`1" /> contenente le chiavi della classe <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Remove(`0)">
      <summary>Rimuove l'elemento con la chiave specificata da <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>true se l'elemento viene rimosso correttamente; in caso contrario, false.Questo metodo restituisce anche false se <paramref name="key" /> non è stato trovato nell'interfaccia <see cref="T:System.Collections.Generic.SortedList`2" /> originale.</returns>
      <param name="key">Chiave dell'elemento da rimuovere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.RemoveAt(System.Int32)">
      <summary>Rimuove l'elemento in corrispondenza dell'indice specificato di <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <param name="index">Indice in base zero dell'elemento da rimuovere.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di zero.-oppure-<paramref name="index" /> è uguale a o maggiore di <see cref="P:System.Collections.Generic.SortedList`2.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Aggiunge una coppia chiave/valore all'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="keyValuePair">Oggetto <see cref="T:System.Collections.Generic.KeyValuePair`2" /> da aggiungere a <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Consente di stabilire se <see cref="T:System.Collections.Generic.ICollection`1" /> contiene un elemento specifico.</summary>
      <returns>true se <paramref name="keyValuePair" /> è presente in <see cref="T:System.Collections.Generic.ICollection`1" />; in caso contrario, false.</returns>
      <param name="keyValuePair">Oggetto <see cref="T:System.Collections.Generic.KeyValuePair`2" /> da individuare in <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Copia gli elementi di <see cref="T:System.Collections.Generic.ICollection`1" /> in <see cref="T:System.Array" /> a partire da un particolare indice <see cref="T:System.Array" />.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="arrayIndex">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> è minore di zero. </exception>
      <exception cref="T:System.ArgumentException">Il numero degli elementi nell'oggetto <see cref="T:System.Collections.Generic.ICollection`1" /> di origine è maggiore dello spazio disponibile tra <paramref name="arrayIndex" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedList`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Rimuove la prima occorrenza di una coppia chiave/valore specifica dall'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>true se <paramref name="keyValuePair" /> è stato rimosso correttamente da <see cref="T:System.Collections.Generic.ICollection`1" />; in caso contrario, false.Questo metodo restituisce anche false se <paramref name="keyValuePair" /> non è stato trovato nell'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> originale.</returns>
      <param name="keyValuePair">Oggetto <see cref="T:System.Collections.Generic.KeyValuePair`2" /> da rimuovere da <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Ottiene <see cref="T:System.Collections.Generic.ICollection`1" /> contenente le chiavi di <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> contenente le chiavi dell'interfaccia <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Ottiene <see cref="T:System.Collections.Generic.ICollection`1" /> contenente i valori in <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> contenente i valori dell'interfaccia <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di eseguire l'iterazione di una raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Ottiene una raccolta enumerabile contenente le chiavi nel dizionario di sola lettura.</summary>
      <returns>Raccolta enumerabile contenente le chiavi nel dizionario di sola lettura.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Ottiene una raccolta enumerabile contenente i valori nel dizionario di sola lettura.</summary>
      <returns>Raccolta enumerabile contenente i valori nel dizionario di sola lettura.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi di <see cref="T:System.Collections.ICollection" /> in <see cref="T:System.Array" /> a partire da un particolare indice <see cref="T:System.Array" />.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.ICollection" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="arrayIndex">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> è minore di zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> è multidimensionale.-oppure-<paramref name="array" /> non dispone di indicizzazione in base zero.-oppure-Il numero degli elementi nell'oggetto <see cref="T:System.Collections.ICollection" /> di origine è maggiore dello spazio disponibile tra <paramref name="arrayIndex" /> e la fine dell'oggetto <paramref name="array" /> di destinazione.-oppure-Non è possibile eseguire automaticamente il cast del tipo dell'oggetto <see cref="T:System.Collections.ICollection" /> di origine al tipo del parametro <paramref name="array" /> di destinazione.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedList`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedList`2" />, questa proprietà restituisce sempre l'istanza corrente.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Aggiunge un elemento con la chiave e il valore forniti all'interfaccia <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Oggetto <see cref="T:System.Object" /> da usare come chiave dell'elemento da aggiungere.</param>
      <param name="value">Oggetto <see cref="T:System.Object" /> da usare come valore dell'elemento da aggiungere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il tipo della chiave indicata nel parametro <paramref name="key" /> non è assegnabile al tipo di chiave <paramref name="TKey" /> dell'insieme <see cref="T:System.Collections.IDictionary" />.-oppure-<paramref name="value" /> è un tipo che non è possibile assegnare al tipo di valore <paramref name="TValue" /> dell'oggetto <see cref="T:System.Collections.IDictionary" />.-oppure-In <see cref="T:System.Collections.IDictionary" /> è già presente un elemento con la stessa chiave.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determina se <see cref="T:System.Collections.IDictionary" /> contiene un elemento con la chiave specificata.</summary>
      <returns>true se <see cref="T:System.Collections.IDictionary" /> contiene un elemento contenente la chiave; in caso contrario, false.</returns>
      <param name="key">Chiave da individuare in <see cref="T:System.Collections.IDictionary" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Restituisce <see cref="T:System.Collections.IDictionaryEnumerator" /> per <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionaryEnumerator" /> per l'oggetto <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.IDictionary" /> ha dimensioni fisse.</summary>
      <returns>true se <see cref="T:System.Collections.IDictionary" /> è di dimensioni fisse; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedList`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.IDictionary" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.IDictionary" /> è di sola lettura; in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.SortedList`2" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Ottiene o imposta l'elemento con la chiave specificata.</summary>
      <returns>L'elemento con la chiave specificata, o null se <paramref name="key" /> non è presente nel dizionario o se il tipo di <paramref name="key" /> non può essere assegnato al tipo di chiave <paramref name="TKey" /> della classe <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">Chiave dell'elemento da ottenere o impostare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Viene assegnato un valore e il tipo di <paramref name="key" /> non può essere assegnato al tipo di chiave <paramref name="TKey" /> della classe<see cref="T:System.Collections.Generic.SortedList`2" />.-oppure-Viene assegnato un valore e il tipo di <paramref name="value" /> non può essere assegnato al tipo di valore <paramref name="TValue" /> della classe<see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Keys">
      <summary>Ottiene <see cref="T:System.Collections.ICollection" /> contenente le chiavi di <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.ICollection" /> contenente le chiavi dell'interfaccia <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Rimuove l'elemento con la chiave specificata da <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Chiave dell'elemento da rimuovere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Values">
      <summary>Ottiene <see cref="T:System.Collections.ICollection" /> contenente i valori in <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.ICollection" /> contenente i valori dell'interfaccia <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di eseguire l'iterazione di una raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.IEnumerator" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TrimExcess">
      <summary>Imposta la capacità sul numero effettivo di elementi contenuti nell'oggetto <see cref="T:System.Collections.Generic.SortedList`2" />, se questo numero è inferiore al 90 per cento della capacità corrente.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TryGetValue(`0,`1@)">
      <summary>Ottiene il valore associato alla chiave specificata.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.SortedList`2" /> contiene un elemento con la chiave specificata; in caso contrario, false.</returns>
      <param name="key">Chiave di cui si deve ottenere il valore.</param>
      <param name="value">Quando questo metodo viene restituito, il valore associato alla chiave specificata nel caso in cui la chiave venga trovata; in caso contrario, il valore predefinito per il tipo di parametro <paramref name="value" />.Questo parametro viene passato non inizializzato.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> è null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Values">
      <summary>Ottiene una raccolta contenente i valori di <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>Raccolta <see cref="T:System.Collections.Generic.IList`1" /> contenente i valori di <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1">
      <summary>Rappresenta una raccolta di oggetti di cui viene mantenuto l'ordinamento.</summary>
      <typeparam name="T">Tipo di elementi presenti nel set.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.SortedSet`1" />. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.SortedSet`1" /> che usa un operatore di confronto specificato.</summary>
      <param name="comparer">Operatore di confronto predefinito da usare per il confronto degli oggetti. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.SortedSet`1" /> che contiene elementi copiati da una raccolta enumerabile specificata.</summary>
      <param name="collection">Raccolta enumerabile da copiare. </param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IComparer{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.SortedSet`1" /> che contiene elementi copiati da una raccolta enumerabile specificata e che usa un operatore di confronto specificato.</summary>
      <param name="collection">Raccolta enumerabile da copiare. </param>
      <param name="comparer">Operatore di confronto predefinito da usare per il confronto degli oggetti. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Add(`0)">
      <summary>Aggiunge un elemento al set e restituisce un valore che indica se l'aggiunta è stata eseguita correttamente.</summary>
      <returns>true se <paramref name="item" /> viene aggiunto al set; in caso contrario, false. </returns>
      <param name="item">Elemento da aggiungere al set.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Clear">
      <summary>Rimuove tutti gli elementi dal set.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Comparer">
      <summary>Ottiene l'oggetto <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> usato per determinare l'uguaglianza dei valori in <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Operatore di confronto usato per determinare l'uguaglianza dei valori in <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Contains(`0)">
      <summary>Determina se il set contiene un elemento specifico.</summary>
      <returns>true se il set contiene <paramref name="item" />; in caso contrario, false.</returns>
      <param name="item">Elemento da individuare nel set.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[])">
      <summary>Copia l'intero oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> in una matrice unidimensionale compatibile, a partire dall'inizio della matrice di destinazione.</summary>
      <param name="array">Matrice unidimensionale che costituisce la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentException">Il numero di elementi nell'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> di origine supera il numero di elementi che può essere contenuto nella matrice di destinazione. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32)">
      <summary>Copia l'intero oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> in una matrice unidimensionale compatibile, a partire dall'indice della matrice specificato.</summary>
      <param name="array">Matrice unidimensionale che costituisce la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" />.La matrice deve avere un'indicizzazione in base zero.</param>
      <param name="index">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentException">Il numero di elementi nella matrice di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine della matrice di destinazione.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Copia un numero specificato di elementi da <see cref="T:System.Collections.Generic.SortedSet`1" /> in una matrice unidimensionale compatibile, a partire dall'indice della matrice specificato.</summary>
      <param name="array">Matrice unidimensionale che costituisce la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" />.La matrice deve avere un'indicizzazione in base zero.</param>
      <param name="index">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <param name="count">Numero degli elementi da copiare.</param>
      <exception cref="T:System.ArgumentException">Il numero di elementi nella matrice di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine della matrice di destinazione.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di zero.-oppure-<paramref name="count" /> è minore di zero.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Count">
      <summary>Ottiene il numero di elementi in <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Numero di elementi in <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Rimuove tutti gli elementi inclusi in una raccolta specificata dall'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente.</summary>
      <param name="other">Raccolta di elementi da rimuovere dall'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetEnumerator">
      <summary>Restituisce un enumeratore che esegue l'iterazione di <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Enumeratore che consente di scorrere <see cref="T:System.Collections.Generic.SortedSet`1" /> nell'ordine specificato.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetViewBetween(`0,`0)">
      <summary>Restituisce una visualizzazione di un subset in un oggetto <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Visualizzazione di un subset contenente solo i valori inclusi nell'intervallo specificato.</returns>
      <param name="lowerValue">Valore minimo desiderato nella visualizzazione.</param>
      <param name="upperValue">Valore massimo desiderato nella visualizzazione. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="lowerValue" /> è maggiore di <paramref name="upperValue" /> secondo l'operatore di confronto.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Un'operazione che si è tentato di eseguire nella visualizzazione non rientra nell'intervallo specificato da <paramref name="lowerValue" /> e <paramref name="upperValue" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente in modo che contenga solo elementi inclusi anche in una raccolta specificata.</summary>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se un oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> è un subset corretto della raccolta specificata.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> è un subset corretto di <paramref name="other" />; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se un oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> è un superset corretto della raccolta specificata.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> è un superset corretto di <paramref name="other" />; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se un oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> è un subset della raccolta specificata.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente è un subset di <paramref name="other" />; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se un oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> è un superset della raccolta specificata.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> è un superset di <paramref name="other" />; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Max">
      <summary>Ottiene il valore massimo in <see cref="T:System.Collections.Generic.SortedSet`1" />, come definito dall'operatore di confronto.</summary>
      <returns>Valore massimo nel set.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Min">
      <summary>Ottiene il valore minimo in <see cref="T:System.Collections.Generic.SortedSet`1" />, come definito dall'operatore di confronto.</summary>
      <returns>Valore minimo nel set.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente e una raccolta specificata condividono elementi comuni.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> e il parametro <paramref name="other" /> condividono almeno un elemento comune; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Remove(`0)">
      <summary>Rimuove un elemento specificato da <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>true se l'elemento viene trovato e rimosso correttamente; in caso contrario, false. </returns>
      <param name="item">Elemento da rimuovere.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Rimuove tutti gli elementi che corrispondono alle condizioni definite dal predicato specificato da un oggetto <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>Numero di elementi rimossi dalla raccolta <see cref="T:System.Collections.Generic.SortedSet`1" />. </returns>
      <param name="match">Delegato che definisce le condizioni degli elementi da rimuovere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Reverse">
      <summary>Restituisce un oggetto <see cref="T:System.Collections.Generic.IEnumerable`1" /> che scorre <see cref="T:System.Collections.Generic.SortedSet`1" /> in ordine inverso.</summary>
      <returns>Enumeratore che scorre <see cref="T:System.Collections.Generic.SortedSet`1" /> in ordine inverso.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Determina se l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente e la raccolta specificata contengono gli stessi elementi.</summary>
      <returns>true se l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente è uguale a <paramref name="other" />; in caso contrario, false.</returns>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente in modo che contenga solo elementi presenti in tale oggetto o nella raccolta specificata, ma non in entrambi.</summary>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Aggiunge un elemento a un oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <param name="item">Oggetto da aggiungere all'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
      <exception cref="T:System.NotSupportedException">L'<see cref="T:System.Collections.Generic.ICollection`1" /> è in sola lettura.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se un oggetto <see cref="T:System.Collections.ICollection" /> è di sola lettura.</summary>
      <returns>true se la raccolta è di sola lettura; in caso contrario, false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>Enumeratore che può essere usato per scorrere la raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia l'intero oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> in una matrice unidimensionale compatibile, a partire dall'indice della matrice specificato.</summary>
      <param name="array">Matrice unidimensionale che costituisce la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" />.La matrice deve avere un'indicizzazione in base zero.</param>
      <param name="index">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentException">Il numero di elementi nella matrice di origine è maggiore dello spazio disponibile tra <paramref name="index" /> e la fine della matrice di destinazione. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> è minore di zero.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, questa proprietà restituisce sempre l'istanza corrente.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>Enumeratore che può essere usato per scorrere la raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Modifica l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente in modo che contenga tutti gli elementi presenti in tale oggetto e nella raccolta specificata. </summary>
      <param name="other">Raccolta da confrontare con l'oggetto <see cref="T:System.Collections.Generic.SortedSet`1" /> corrente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> è null.</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta in corrispondenza della posizione corrente dell'enumeratore.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'oggetto <see cref="T:System.Collections.Generic.SortedSet`1.Enumerator" />. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo della raccolta <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="T:System.Collections.Generic.Stack`1">
      <summary>Rappresenta una raccolta di istanze LIFO (last-in-first-out) a dimensione variabile dello stesso tipo specificato.</summary>
      <typeparam name="T">Specifica il tipo di elementi nello stack.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.Stack`1" /> vuota e con capacità iniziale predefinita.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.Stack`1" /> che contiene gli elementi copiati dalla raccolta specificata e ha la capacità sufficiente per contenere il numero di elementi copiati.</summary>
      <param name="collection">Raccolta da cui copiare gli elementi.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Collections.Generic.Stack`1" /> che è vuota e ha la capacità iniziale maggiore tra quella specificata e quella predefinita.</summary>
      <param name="capacity">Il numero iniziale degli elementi che <see cref="T:System.Collections.Generic.Stack`1" /> può contenere.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Clear">
      <summary>Rimuove tutti gli oggetti da <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Contains(`0)">
      <summary>Determina se un elemento è incluso in <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>true se <paramref name="item" /> è presente in <see cref="T:System.Collections.Generic.Stack`1" />; in caso contrario, false.</returns>
      <param name="item">Oggetto da individuare in <see cref="T:System.Collections.Generic.Stack`1" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.CopyTo(`0[],System.Int32)">
      <summary>Copia l'oggetto <see cref="T:System.Collections.Generic.Stack`1" /> in un oggetto <see cref="T:System.Array" /> unidimensionale esistente, partendo dall'indice della matrice specificata.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.Generic.Stack`1" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="arrayIndex">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Stack`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Count">
      <summary>Ottiene il numero di elementi contenuti in <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>Il numero di elementi contenuti in <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.GetEnumerator">
      <summary>Restituisce un enumeratore per <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1.Enumerator" /> per l'oggetto <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Peek">
      <summary>Restituisce l'oggetto all'inizio dello <see cref="T:System.Collections.Generic.Stack`1" /> senza rimuoverlo.</summary>
      <returns>Oggetto all'inizio dell'oggetto <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Pop">
      <summary>Rimuove e restituisce l'oggetto all'inizio dell'oggetto <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>Oggetto rimosso dall'inizio dell'oggetto <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Push(`0)">
      <summary>Inserisce un oggetto all'inizio di <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <param name="item">Oggetto da inserire in <see cref="T:System.Collections.Generic.Stack`1" />.Il valore può essere null per i tipi di riferimento.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere la raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi di <see cref="T:System.Collections.ICollection" /> in <see cref="T:System.Array" /> a partire da un particolare indice <see cref="T:System.Array" />.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.ICollection" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="arrayIndex">Indice in base zero in <paramref name="array" /> in corrispondenza del quale viene avviata la copia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.Stack`1" />, questa proprietà restituisce sempre false.</returns>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere usato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.Nell'implementazione predefinita di <see cref="T:System.Collections.Generic.Stack`1" />, questa proprietà restituisce sempre l'istanza corrente.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.IEnumerator" /> che può essere usato per eseguire l'iterazione della raccolta.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.ToArray">
      <summary>Copia l'oggetto <see cref="T:System.Collections.Generic.Stack`1" /> in una nuova matrice.</summary>
      <returns>Nuova matrice contenente le copie degli elementi dell'oggetto <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.TrimExcess">
      <summary>Imposta la capacità sul numero effettivo di elementi contenuti nell'oggetto <see cref="T:System.Collections.Generic.Stack`1" />, se questo numero è inferiore al 90 per cento della capacità corrente.</summary>
    </member>
    <member name="T:System.Collections.Generic.Stack`1.Enumerator">
      <summary>Enumera gli elementi di un oggetto <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento dell'insieme <see cref="T:System.Collections.Generic.Stack`1" /> nella posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.Dispose">
      <summary>Consente di rilasciare tutte le risorse utilizzate dall'oggetto <see cref="T:System.Collections.Generic.Stack`1.Enumerator" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.MoveNext">
      <summary>Sposta l'enumeratore all'elemento successivo dell'oggetto <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>true se l'enumeratore ha completato il passaggio all'elemento successivo; false se l'enumeratore ha raggiunto la fine della raccolta.</returns>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ottiene l'elemento in corrispondenza della posizione corrente dell'enumeratore.</summary>
      <returns>Elemento della raccolta in corrispondenza della posizione corrente dell'enumeratore.</returns>
      <exception cref="T:System.InvalidOperationException">L'enumeratore è posizionato prima del primo elemento o dopo l'ultimo elemento della raccolta. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Imposta l'enumeratore sulla propria posizione iniziale, ovvero prima del primo elemento nella raccolta.La classe non può essere ereditata.</summary>
      <exception cref="T:System.InvalidOperationException">La raccolta è stata modificata dopo la creazione dell'enumeratore. </exception>
    </member>
  </members>
</doc>