﻿Imports System
Imports System.Collections.Generic
Imports System.IO
Imports System.Linq
Imports System.Runtime.CompilerServices
Imports System.Text.RegularExpressions
Imports System.Threading
Imports System.Threading.Tasks
Imports DnsClient
Imports DnsClient.Protocol
Public Module Helper1
    Public Sub AppendLine(filename As String, data As String)
        File.AppendAllText(filename, data + Environment.NewLine)
    End Sub
    Public Async Function ParseDomainAsync(domain As String) As Task(Of String)
        ' Use LookupClient to query the DNS asynchronously for MX records
        Dim lookupClient As New LookupClient()
        Dim response As IDnsQueryResponse = Await lookupClient.QueryAsync(domain, QueryType.MX)
        ' Get the first MX record, if any
        Dim mxRecord As MxRecord = response.Answers.MxRecords().FirstOrDefault()
        ' If an MX record was found, process and return the exchange value, otherwise return an empty string
        If mxRecord IsNot Nothing Then
            Dim exchange As String = mxRecord.Exchange.Value
            Return If(Not String.IsNullOrEmpty(exchange), exchange.TrimEnd("."c), String.Empty)
        Else
            Return String.Empty
        End If
    End Function
    'Public Async Function ParseDomain(domain As String) As Task(Of String)
    '    Dim var_3 As TaskAwaiter(Of IDnsQueryResponse) = New LookupClient().QueryAsync(domain, 15, 1, Nothing).GetAwaiter()
    '    If Not var_3.IsCompleted Then
    '        Await var_3
    '        Dim taskAwaiter As TaskAwaiter(Of IDnsQueryResponse)
    '        var_3 = taskAwaiter
    '        taskAwaiter = Nothing
    '    End If
    '    Dim expr_8B As MxRecord = RecordCollectionExtension.MxRecords(var_3.GetResult().Answers).FirstOrDefault()
    '    Dim text As String = If((expr_8B IsNot Nothing), expr_8B.Exchange.Value, Nothing)
    '    Dim result As String
    '    If Not String.IsNullOrEmpty(text) Then
    '        result = text.Remove(text.Length - 1).Trim()
    '    Else
    '        result = String.Empty
    '    End If
    '    Return result
    'End Function
    Public Function ParseHost(address As String) As String
        Dim list As List(Of String) = New List(Of String)()
        Dim host As String = New Uri(address).Host
        Dim text As String = Nothing
        If list IsNot Nothing Then
            For Each current As String In list
                Dim match As Match = New Regex("(?<=\.|)\w+\." + current + "$").Match(host)
                If match.Success Then
                    text = match.Groups(0).Value
                End If
            Next
        End If
        If String.IsNullOrWhiteSpace(text) Then
            Dim match2 As Match = New Regex("(?<=\.|)\w+\.\w+$").Match(host)
            If match2.Success Then
                text = match2.Groups(0).Value
                Return text
            End If
        End If
        Return String.Empty
    End Function
End Module
