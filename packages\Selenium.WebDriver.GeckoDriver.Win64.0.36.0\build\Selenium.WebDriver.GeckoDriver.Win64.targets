<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">  

  <Import Project="$(MSBuildThisFileDirectory)DefinePropertiesGeckoDriver.Win64.targets" />

  <Target Name="DefinePropertiesGeckoDriverWin64" BeforeTargets="BeforeClean;BeforeBuild;BeforePublish">

    <!--
    On a Non-SDK style project, and at the first build, "DefineProperties*.targets"
    will not be imported and the driver file will not be copied into the output folder,
    due to restoring "packages" folder is too late.

    To avoid this problem, this target reevaluates "DefineProperties*.targets" if necessary
    before the "Build" target.
    -->
      
    <PropertyGroup>
      <GeckoDriverWin64Name Condition=" '$(GeckoDriverWin64Name)' == '' ">geckodriver.exe</GeckoDriverWin64Name>
      <GeckoDriverWin64SrcPath Condition=" '$(GeckoDriverWin64SrcPath)' == '' ">$(MSBuildThisFileDirectory)..\driver\$(GeckoDriverWin64Name)</GeckoDriverWin64SrcPath>
      <GeckoDriverWin64TargetDir Condition=" '$(GeckoDriverWin64TargetDir)' == '' ">$(TargetDir)</GeckoDriverWin64TargetDir>
    </PropertyGroup>
  
    <!-- Detect that project type is Web or Not. -->
    <PropertyGroup Condition="'$(ProjectTypeIsWeb)' == ''">
      <!-- Reference: http://www.mztools.com/Articles/2008/*********.aspx -->
      <!-- HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.0\Projects -->
      <ProjectTypeIsWeb Condition="$([System.Text.RegularExpressions.Regex]::IsMatch($(ProjectTypeGuids),'^(.*;)*{349c5851-65df-11da-9384-00065b846f21}(;.*)*$'))">true</ProjectTypeIsWeb>
      <ProjectTypeIsWeb Condition="'$(ProjectTypeIsWeb)' == ''">false</ProjectTypeIsWeb>
    </PropertyGroup>
  
    <!-- Detect definition of compilation symbol that is "_PUBLISH_GECKODRIVER", or "PublishGeckoDriverWin64" msbuild property. -->
    <PropertyGroup Condition="'$(PublishGeckoDriverWin64)' == ''">
      <PublishGeckoDriverWin64 Condition="$([System.Text.RegularExpressions.Regex]::IsMatch($(DefineConstants),'^(.*;)*_PUBLISH_GECKODRIVER(;.*)*$'))">true</PublishGeckoDriverWin64>
      <PublishGeckoDriverWin64 Condition="'$(PublishGeckoDriverWin64)' == ''">false</PublishGeckoDriverWin64>
    </PropertyGroup>
  
    <!-- Setup driver file as project item if publishing driver file is enabled. -->
    <ItemGroup Condition="'$(PublishGeckoDriverWin64)|$(ProjectTypeIsWeb)' == 'true|true'">
      <None Include="$(GeckoDriverWin64SrcPath)">
        <Link>geckodriver.exe</Link>
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <Visible>False</Visible>
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      </None>
    </ItemGroup>
  
    <ItemGroup Condition="'$(PublishGeckoDriverWin64)|$(ProjectTypeIsWeb)' == 'true|false'">
      <Content Include="$(GeckoDriverWin64SrcPath)">
        <Link>geckodriver.exe</Link>
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <Visible>False</Visible>
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      </Content>
    </ItemGroup>
  
  </Target> <!-- Target Name="DefinePropertiesGeckoDriverWin64" -->

  <Target Name="DumpPropertiesGeckoDriverWin64" BeforeTargets="AfterClean;AfterBuild;CopyGeckoDriverWin64ToBin;DeleteGeckoDriverWin64FromBin" Condition="'$(DumpPropertiesGeckoDriverWin64)' == 'true'">
    <Message Importance="high" Text="GeckoDriverWin64Name is [$(GeckoDriverWin64Name)]" />
    <Message Importance="high" Text="GeckoDriverWin64SrcPath is [$(GeckoDriverWin64SrcPath)]" />
    <Message Importance="high" Text="GeckoDriverWin64TargetDir is [$(GeckoDriverWin64TargetDir)]" />
    <Message Importance="high" Text="ProjectTypeIsWeb is [$(ProjectTypeIsWeb)]" />
    <Message Importance="high" Text="PublishGeckoDriverWin64 is [$(PublishGeckoDriverWin64)]" />
    <!-- 
    Platform is Win64 becuase this package is for Win64 package.
    <Message Importance="high" Text="GeckoDriverPlatformWin64 is [$(GeckoDriverPlatformWin64)]" /> 
    -->
  </Target>

  <!-- If publishing driver file is disabled (it's default behavior), define "copy" and "clean" build task. -->
  <Target Name="CopyGeckoDriverWin64ToBin" BeforeTargets="AfterBuild" Condition="'$(PublishGeckoDriverWin64)' == 'false'">
    <Copy SourceFiles="$(GeckoDriverWin64SrcPath)" DestinationFiles="$(GeckoDriverWin64TargetDir)$(GeckoDriverWin64Name)" SkipUnchangedFiles="true"></Copy>
  </Target>
  <Target Name="DeleteGeckoDriverWin64FromBin" BeforeTargets="AfterClean" Condition="'$(PublishGeckoDriverWin64)' == 'false'">
    <Delete Files="$(GeckoDriverWin64TargetDir)$(GeckoDriverWin64Name)"></Delete>
  </Target>
</Project>
