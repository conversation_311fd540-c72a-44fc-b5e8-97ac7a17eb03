﻿Imports System.IO
Imports System.Text
'Imports System.Windows.Controls
Imports System.Windows.Forms.VisualStyles.VisualStyleElement
Imports DevExpress.XtraEditors
Imports DevExpress.XtraPrinting.Native
Public Class frmEncodedAttachment
    Private IsDragging As Boolean = False
    Private MouseOffset As Point
    Private Const VBQuote As String = """"
    ''هنا تخطى هناكشن الليتر
    ReadOnly FunctionList As List(Of String) = New List(Of String) From {"[-Email64-]", "[-Email-]", "[-IP-]", "[-IPChina-]", "[-Domain-]", "[-Name-]", "[-Date-]", "[-DateTomorrow-]",
        "[-RandomBrowser-]", "[-RCountry-]", "[-FakePhone-]", "[-FakeEmail-]", "[-NewYork-]", "[-UCase-]", "[-Link-]", "[-Logo-]", "[-RN1-]", "[-RN2-]", "[-RN3-]", "[-RN4-]",
        "[-RN5-]", "[-RN6-]", "[-QRCode-]", "[-CompanyName-]", "[-Time-]", "[-RN7-]", "[-RN8-]", "[-RN9-]", "[-RN10-]", "[-RN12-]", "[-RCh1-]", "[-RCh2-]", "[-RCh3-]", "[-RCh4-]", "[-RCh5-]", "[-RCh6-]", "[-RCh7-]", "[-RCh8-]"}
    Private Sub ConvertFile(ByVal SourceFile As String, ByVal DestFile As String, ByVal Title As String, Optional ByVal DisableClick As Boolean = False)
        Dim sr As StreamReader = Nothing
        Dim sw As StreamWriter = Nothing
        Dim sb As New StringBuilder()
        Dim Buffer As String = vbNullString
        sr = New StreamReader(SourceFile)
        Buffer = sr.ReadToEnd
        sr.Close()
        If DisableClick Then
            Buffer = Buffer & "<script>document.oncontextmenu=new Function(" & VBQuote & "return false" & VBQuote & ")</script>"
        End If
        sb.AppendLine("<html>")
        sb.AppendLine("<head>")
        sb.AppendLine("<title>" & Title & "</title>")
        sb.AppendLine("<body>")
        sb.AppendLine("<script language=" & VBQuote & "JavaScript" & VBQuote & " type=" & VBQuote & "text/javascript" & VBQuote & ">")
        sb.Append("document.write(unescape('")
        Dim i As Integer = 0
        While i < Buffer.Length
            Dim c As Char = Buffer(i)
            Dim found As Boolean = False
            For Each flWord As String In FunctionList
                If Buffer.Substring(i).StartsWith(flWord) Then
                    sb.Append(flWord)
                    i += flWord.Length
                    found = True
                    Exit For
                End If
            Next
            If Not found Then
                sb.Append("%" & Asc(c).ToString("X2"))
                i += 1
            End If
        End While
        sb.Append("'));" & vbCrLf)
        sb.AppendLine("</script>")
        sb.AppendLine("</body>")
        sb.AppendLine("</html>")
        sw = New StreamWriter(DestFile)
        sw.Write(sb.ToString())
        sw.Close()
    End Sub
    Private Sub bntBrowse_Click(sender As Object, e As EventArgs) Handles LogInButton2.Click
        'Dim ofd As New OpenFileDialog
        'With ofd
        '    .InitialDirectory = My.Computer.FileSystem.SpecialDirectories.Desktop
        '    .Filter = "Html|*.html|HTM|*.htm"
        '    .FilterIndex = 1
        'End With
        'If ofd.ShowDialog() = DialogResult.OK Then
        '    LogInNormalTextBox1.Text = ofd.FileName
        '    'Definir o nome do arquivo de saída.
        '    Dim directoryPath As String = Path.GetDirectoryName(ofd.FileName)
        '    Dim fileName As String = Path.GetFileName(ofd.FileName)
        '    LogInNormalTextBox2.Text = directoryPath & "\Encrypted_" & fileName
        '    'Habilite o botão se não estiver habilitado.
        '    If Not LogInButton3.Enabled Then
        '        LogInButton3.Enabled = True
        '        LogInNormalTextBox2.Enabled = True
        '    End If
        'End If
    End Sub
    Private Sub bntClear_Click(sender As Object, e As EventArgs) Handles bntClear.Click
        'ProgressBarControl1.Position = 0
        'ProgressBarControl1.Properties.Minimum = 0
        'ProgressBarControl1.Properties.Maximum = 100
        'ProgressBarControl1.Visible = False
        'LogInNormalTextBox1.Text = ""
        'LogInNormalTextBox2.Text = ""
    End Sub
    Private Sub LogInButton3_Click(sender As Object, e As EventArgs) Handles LogInButton3.Click
        'If LogInNormalTextBox2.Text = "" Then
        '    DevExpress.XtraEditors.XtraMessageBox.Show("Please specify the path to save the file", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        '    Return
        'End If
        'Dim sd As New SaveFileDialog()
        'sd.Title = "outlook"
        'sd.Filter = "Attachment HTML(*.html)|*.html|Attachment HTM(*.htm)|*.htm"
        'sd.FileName = New FileInfo(LogInNormalTextBox2.Text).Name
        'If sd.ShowDialog().Equals(DialogResult.OK) Then
        '    'Definir o nome do arquivo de saída.
        '    LogInNormalTextBox2.Text = sd.FileName
        'End If
    End Sub
    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        'ProgressBarControl1.Visible = True
        'ProgressBarControl1.Increment(1)
        'If ProgressBarControl1.Position = ProgressBarControl1.Properties.Maximum Then
        '    Timer1.Stop()
        '    If Not File.Exists(LogInNormalTextBox1.Text) Then
        '        XtraMessageBox.Show("Input source file not found.", "file not found", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
        '        Exit Sub
        '        LogInButton2.Focus()
        '    Else
        '        'Criptografe o arquivo.
        '        ConvertFile(LogInNormalTextBox1.Text, LogInNormalTextBox2.Text, LogInNormalTextBox3.Text, LogInCheckBox1.Checked)
        '        'Apresentação de mensagem de acabamento.
        '        Dim msg As String = "The File has been Successfully Encrypted...!" & vbNewLine
        '        msg = msg & "Note: Please Check the Saved output in the selected save path."
        '        XtraMessageBox.Show(msg, Text, MessageBoxButtons.OK, MessageBoxIcon.Information)
        '        'Redefina o sontrols.
        '        LogInNormalTextBox2.Enabled = False
        '        LogInButton3.Enabled = False
        '        ProgressBarControl1.Enabled = False
        '        LogInCheckBox1.Checked = False
        '        LogInButton2.Focus()
        '        Dim argument As String = "/select, """ & LogInNormalTextBox2.Text & """"
        '        Process.Start("explorer.exe", argument)
        '        LogInNormalTextBox1.Text = vbNullString
        '        LogInNormalTextBox2.Text = vbNullString
        '        ProgressBarControl1.Visible = False
        '    End If
        '    'XtraMessageBox.Show("Done Crypter Letter check Same Letter", "Encoded, HTML ", MessageBoxButtons.OK, MessageBoxIcon.Information)
        'End If
    End Sub
    Private Sub bntSave_Click(sender As Object, e As EventArgs) Handles bntSave.Click
        'If LogInNormalTextBox1.Text = "" Then
        '    DevExpress.XtraEditors.XtraMessageBox.Show("Please Select Attachment", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        '    Return
        'End If
        'Timer1.Start()
    End Sub
    Private Sub frmEncodedAttachment_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        CenterForm(Me, frmUtilitiesMain)
        bntClear_Click(Nothing, Nothing)
    End Sub
    'Private Sub BunifuImageButton2_Click(sender As Object, e As EventArgs) Handles BunifuImageButton2.Click
    '    Me.Dispose()
    'End Sub
    'Private Sub BunifuImageButton1_Click(sender As Object, e As EventArgs) Handles BunifuImageButton1.Click
    '    Me.WindowState = FormWindowState.Minimized
    'End Sub
    Private Sub frmEncodedAttachment_MouseDown(sender As Object, e As MouseEventArgs) Handles Me.MouseDown
        If e.Button = MouseButtons.Left Then
            IsDragging = True
            MouseOffset = New Point(-e.X, -e.Y)
        End If
    End Sub
    Private Sub frmEncodedAttachment_MouseMove(sender As Object, e As MouseEventArgs) Handles Me.MouseMove
        If IsDragging Then
            Dim MousePosition As Point = Control.MousePosition
            MousePosition.Offset(MouseOffset.X, MouseOffset.Y)
            Me.Location = MousePosition
        End If
    End Sub
    Private Sub frmEncodedAttachment_MouseUp(sender As Object, e As MouseEventArgs) Handles Me.MouseUp
        If e.Button = MouseButtons.Left Then
            IsDragging = False
        End If
    End Sub
    Private Sub Panel2_MouseDown(sender As Object, e As MouseEventArgs) Handles Me.MouseDown
        If e.Button = MouseButtons.Left Then
            IsDragging = True
            MouseOffset = New Point(-e.X, -e.Y)
        End If
    End Sub
    Private Sub Panel2_MouseMove(sender As Object, e As MouseEventArgs) Handles Me.MouseMove
        If IsDragging Then
            Dim MousePosition As Point = Control.MousePosition
            MousePosition.Offset(MouseOffset.X, MouseOffset.Y)
            Me.Location = MousePosition
        End If
    End Sub
    Private Sub Panel2_MouseUp(sender As Object, e As MouseEventArgs) Handles Me.MouseUp
        If e.Button = MouseButtons.Left Then
            IsDragging = False
        End If
    End Sub
End Class