﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
Best Sender
</name>
</assembly>
<members>
<member name="T:Best_Sender.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources._1">
<summary>
  Looks up a localized string similar to ‏‏.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources._10MinMail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources._12616x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources._16316x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources._777">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources._New">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.actions_add">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.actions_removecircled">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.actions_trash">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.actions_trash1">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.actions_trash2">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.add_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.add_16x161">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.add_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.add16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.add32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.addfooter_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.addgroupfooter_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.addgroupheader_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.addheader_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.addheader_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.addheader_32x321">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.addheader_32x322">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Afterlogic16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Airmail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.alarm">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Alice16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.alignfloatingobjectbottomleft">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.alignfloatingobjecttopleft">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.alignfloatingobjecttopleft_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Aliyun16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.AMazon">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.amazon16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.anazana16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.AntiBotRedirectPaner">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Anydesk32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Aol_image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Aol16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.AolPanel">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Apply_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Apply_32x321">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.aruba16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.AT_T16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.attach_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.attached_file">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.AttachmentPaner">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.attachments">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Attimage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Authentication__image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Autistici16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.bad_PNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Base64_image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Base64EN">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Base64Image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Best_Sender_VIP">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.BestTools_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.BestTools_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.biglobejp">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.BKBSV2025">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.BkPanelButton">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.bksender">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.bksenderlast">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.BKSettings">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.bluehost16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.BooCardGold">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.bot16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.bot32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.BSSenderImage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.BSVNewtools">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.BT16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ButtonEncoded16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ButtonEncoded32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Canary16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Cancel_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Cancel_32x321">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Caver">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Change16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Change32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ChangeLetter">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ChangeLetter16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ChangeText16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ChangeText32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.check">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.checked_PNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.chinaemail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.citadel16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.clear_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.clearall">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.cloudflare_image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.cloudflare_image1">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Cloudflare_Logo">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.cloudflare16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.cloudflare32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ClubInternet16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.cn4e16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Cock_li16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Comcast16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.CommuniG16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.connect16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Connect32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ConvertLinkToAttachment16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ConvertLinkToAttachment32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.CoolLinkHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.copy16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.copy32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.CopyLinkEncode">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.CopyLinkEncodeSender">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.CounterMail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Customized">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Dark_image_New">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.DarkEN">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.DarkImageLeter">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.default16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.default32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Delete_duplicate_mail">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.delete_hyperlink_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.DeleteDuplicatemail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.DeleteDuplicatemail32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.deleteheader_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Descount">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Devl77716x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Devl77732x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.discord16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.discord32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.discordImage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.discountbigNew">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Disroot16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.documentpdf">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.domainloca_host16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.DoneBuild">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.download_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Dragon">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Dragon_Image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.DragonImageLetter">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.DreamHost16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Earthlink16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.edittask_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.edittask_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.eM_Client16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EmailExtractor16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EmailExtractor32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EmailFromScanner16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EmailFromScanner32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Emailimage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EmailSendingFormat16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EmailSendingFormat32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EmailSorter16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EmailSorter32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EmailValidation16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EmailValidation32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.employee_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.employee_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.empty_trash">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EN_Att_Pro_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EN_Att_Pro_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.enablesearch">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Encoded_Link16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Encoded_Link32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EncodedAttachment16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EncodedAttachment32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EncodedLetter_PNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EncodedLetter16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EncodedLetter32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EncodedLetterIMAGE">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EncodedLinkImageNew">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.EncodedLinkWolfImage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Error16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Error32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ErrorPayment">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.existlink_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Exit16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Exit32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.export_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.export_16x161">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.export_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.exportfile">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.exporttohtml">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.exporttohtml_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ExtractDomain16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ExtractDomain32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ezgif_com_crop__2_">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.FakeMail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.FastMail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.finding_signatures">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.FirstClass16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Freenet16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Frontapp16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.gaugestylelinearhorizontal_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.gaugestylelinearvertical_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.gettingstarted">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.gettingstarted1">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.gettingstarted2">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Ghost_Image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.GhostImage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.global_mail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.gmail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.GMX">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.GMX16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Godaddy">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.GoDaddy16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Good_PNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Google_Captcha_Logo">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Google_image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Google_Workspace16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.googlecaptcha16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.googlecaptcha32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Gost">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.GroupWise16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Guerrilla16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Header">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Hey_com16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Hi_Net16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.HIDE_place_marker_100px">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.HideLink16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.HideLink32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.HideLinkHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.HideLinkOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.HideLinkPaner">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.HideRedirect16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.HideRedirect32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.hiworks16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.home_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Horde16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Hotmail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.html16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.html32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Hushmail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.hyperlink">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.hyperlink_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.hyperlink_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.hyperlink_32x321">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IceWarp16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.iCloud_Mail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.iinet16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Image_777_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Check_Office365_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Check_Office365_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Email_Extractor_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Email_Extractor_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Email_Extractor_Pro_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Email_Extractor_Pro_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.image_Email_From_Scanner_">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.image_Email_From_Scanner_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Email_Sorter_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Email_Sorter_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.image_Email_Vaildator_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.image_Email_Vaildator_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Encoded_PHP_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Encoded_PHP_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Extract_Domain_Last_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Extract_Domain_Last_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_HideLink">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Letter__Designer_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_Letter__Designer_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.image_locked">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.image_Multi_Linker">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_SMTP_order_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_SMTP_order_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_TGB_BSV_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAGE_TGB_BSV_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.image16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Image32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageAccountTesterHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageAccountTesterOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageAttachmentHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageAttachmentOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageBitcoin">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ImageCHeckSMTPHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ImageCHeckSMTPHideOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageEncodeLinkHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageEncodeLinkOne">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageIMAP1MonthHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageimport">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageKinkToAttachmentHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageKinkToAttachmentOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageRemovePassHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageRemovePassOne">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageRisizerHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.imageRisizerOne">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAP_New">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAP_PNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAPFail">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAPHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAPOk">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.IMAPOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.inbox_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.inbox_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.inbox16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.inbox32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.insertlistbox">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.int_no">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.intetnet_Yes">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ionos">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ionos16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Kerio_mail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.knowndomain">
<summary>
  Looks up a localized string similar to aol.com
att.net
comcast.net
facebook.com
gmail.com
gmx.com
googlemail.com
google.com
hotmail.com
hotmail.co.uk
mac.com
me.com
mail.com
msn.com
live.com
sbcglobal.net
verizon.net
yahoo.com
yahoo.co.uk
email.com
fastmail.fm
games.com
gmx.net
hush.com
hushmail.com
icloud.com
iname.com
inbox.com
lavabit.com
love.com
outlook.com
pobox.com
protonmail.com
rocketmail.com
safe-mail.net
wow.com
ygm.com
ymail.com
zoho.com
yandex.com
bellsouth.net
charter.net
cox.net
earthlink [rest of string was truncated]&quot;;.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.knowndomains">
<summary>
  Looks up a localized string similar to // 0x00027E96: knowndomain‎ = &quot;aol.com\r\natt.net\r\ncomcast.net\r\nfacebook.com\r\ngmail.com\r\ngmx.com\r\ngooglemail.com\r\ngoogle.com\r\nhotmail.com\r\nhotmail.co.uk\r\nmac.com\r\nme.com\r\nmail.com\r\nmsn.com\r\nlive.com\r\nsbcglobal.net\r\nverizon.net\r\nyahoo.com\r\nyahoo.co.uk\r\nemail.com\r\nfastmail.fm\r\ngames.com\r\ngmx.net\r\nhush.com\r\nhushmail.com\r\nicloud.com\r\niname.com\r\ninbox.com\r\nlavabit.com\r\nlove.com\r\noutlook.com\r\npobox.com\r\nprotonmail.com\r\nrocketmail.com\r\nsafe-mail.net [rest of string was truncated]&quot;;.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.knownmx">
<summary>
  Looks up a localized string similar to mx-aol.mail.gm0.yahoodns.net
ff-ip4-mx-vip1.prodigy.net
mx2.comcast.net
msgin.vvv.facebook.com
gmail-smtp-in.l.google.com
mx00.gmx.net
gmail-smtp-in.l.google.com
aspmx.l.google.com
hotmail-com.olc.protection.outlook.com
eur.olc.protection.outlook.com
mx3.mail.icloud.com
mx6.mail.icloud.com
mx00.mail.com
msn-com.olc.protection.outlook.com
live-com.olc.protection.outlook.com
al-ip4-mx-vip2.prodigy.net
mx-aol.mail.gm0.yahoodns.net
mta7.am0.yahoodns.net
mx-eu.mail.am0.yahoodns.net
mx00.mail.c [rest of string was truncated]&quot;;.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.KolabNow16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Laposte16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Lengthlogo">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.letter">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Letter_Encoded_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Letter_Encoded_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Letter_Maker_PNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Letter16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Letter32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LetterHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.letterOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LetterPNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LetterSettings16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LetterSettings32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LetterTools16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LetterTools32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Libero16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Link">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.link_To_Attachment">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.link1">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.link2">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.linkCool">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LinkDomain16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LinkDomain32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.linked_file">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LinkEN">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LinkEN16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LinkEN32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LinktoAttachmentPaner">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LinkToAttachmentUpdateHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LinkToAttachmentUpdateOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.linkURLSender">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.live_chat">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Location_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Location_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.logo">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Logo_Best">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Logo_Microsoft">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Logo_NewBestSender">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Logo_Puzzle">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Logo_VIP_Redirect">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LogoBSV">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LogoHeader">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Logoicon">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LogoLogin">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.LogoUpdate">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Lotus_Notes16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Luxsci16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Mail_com16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Mail_ru16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Mailbird16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Mailbox_org16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Mailextractpng">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Mailfence16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.mailinator16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.mailmerge">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.mailmerge1">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Mailplug16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.mailSort">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.max_loader">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.media">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Menu">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Menu16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Menu32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.microsoft_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Microsoft_36516x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Microsoft_image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MicrosoftPaner">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Mimecast16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Missive16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.money_bag">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_1_and_1">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_163">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_263">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Aliyun">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Amazon">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Anazana">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_CoreMail">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_EarthLink">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Gmail">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Gmx">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_GoDaddy">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Hinet">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Hotmail">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_iCloud">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_iiNet">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Mail_Ru">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_mimeCast">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_nameCheap">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Office_365">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Orange">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Other">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Outlook">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_ovhcloud">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_QQ">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_RackSpace">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Web_de">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Yahoo">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Yandex">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_ZambriaMail">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.MS_Zoho">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.mweb16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Namecheap16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.naver16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.net_framework16x161">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.net_framework32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.NetEase16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.New_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.New_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.New16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.New32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.NewHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Newton16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.notifcationAnobis">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Notworking16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Notworking32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Office365">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Office365CheckAcccount16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Office365CheckAcccount32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Open_Xchange16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.open16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.open2">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.open21">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.open32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.opencalendar">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Openmailbox16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Orange_fr16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Other16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Outlook">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Outlook16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.outlook16x161">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.PassImage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.PasswordPaner">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.paste_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.paste_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Path16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Path32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.pause_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.PDF16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.PDF32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.pending16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.pending32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.php16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.php32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.phpLaft16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.phpLaft32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.place_marker_100px">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.play_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Plesewait">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.pngAttachment">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.pngEncodedLink">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.pngRidirectLink">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.pngRidirectLink1">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.PNGSMTPHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.PNGSMTPOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.PNGSMTPONG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.pngwing_com">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Polarismail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Polymail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Postbox16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Posteo16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Premium">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.PrivteToolsBSV">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ProgressDownloadMoney">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.prossec">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ProtonMail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.PuzzelPaner">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Puzzle_image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Puzzle16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Puzzle32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.QQ16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.qr_Save">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.QrcodeBTN">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.QRcodeICQ1">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.QRcodeSkype1">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.QRcodeTelegram1">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.rackspace">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Rackspace_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Rainloop16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.red_flag">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.reddatabargradient">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Rediff16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RedirctMicrosoftPaner">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RedirctMicrosoftPaner1">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Redirect_Link_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Redirect_Link_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Redirect_Logo">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Redirect_VIP">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Redirect_VIP_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Redirect_with_Pass">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Redirect16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Redirect32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RedirectBrowser">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.refreshpivottable_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.register_it16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.removeheader">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RemoveHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RemoveON">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RemovePassFromEmail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RemovePassFromEmail32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Reset_Image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.restart">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Restart16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Restart32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RestartApp">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RestartBSV16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RestartBSV32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.restartHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Riseup16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Roundcube16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RR16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.rulerhorizontal">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.rulervertical">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RunBox16x1616x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RunBox32x3216x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.RustDesk32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Save_Image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.saveall_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.scalix16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ScannerImage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.search_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.search_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.security_key">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.security_key1">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Send">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Send_Mail_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Send_Mail_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Send_PNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Send16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.sendbackward">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.sendbackward_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Sender32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.SendMailHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.SendMailOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.sendmht">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.sendpdf_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Serverdata16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.servermode_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.servermode_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Services_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Services_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.ServicesBSV">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.settings16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.settings32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Seznam16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.shopping">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.shopping_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.shopping_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.shopping_cart">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.shoppingOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.showlegend">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.showtestreport_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.signal">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.signal_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.signal16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Sina16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Site_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Site_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.skull">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Skype">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Skype_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.SMTP16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.SMTP32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.smtpServer">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.smtpserverOlder">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.smtpServerOne">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.smtptestacc">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.smtptestaccount16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.smtptestaccount32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.SMTPTester16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.SMTPTester32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.snapinsertfooter">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.snapinsertheader">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.snapinsertheader1">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.snapinsertheader2">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.snaptogglefieldhighlighting">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Soverin16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Spark_mail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Spider_Redirect">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.SPiderPanel">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.SpiderRedirect32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Spike16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.sql">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Squirrelmail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Start_Image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Start_Image16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Start_Image32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Start16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.StartMail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.status_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.status_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.stop_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.stop_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Stop_Image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Stop16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Stop32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.StopPoint">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Strato16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Superhuman16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.support_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.support_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Support_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Support_on">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.SupportImage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Synaq16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.T_Online16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.task">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Telegram_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Telegram_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.telegram16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.telegram32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.TelegramImage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Temp16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Terra16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Thexyz16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Thunderbird16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.time16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.time32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.timeConvert">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.timeOut_PNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trackingchanges_locktracking">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trackingchanges_locktracking1">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trackingchanges_locktracking2">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trackingchanges_locktracking3">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Transfer16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Transfer32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash_16x161">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash_16x162">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash_16x163">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash_16x164">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash_16x165">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash_32x321">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash_32x322">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash_32x323">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash_Image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.trash32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Tutanota16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.txtC">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.txtL">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.txtLetter">
<summary>
  Looks up a localized string similar to &lt;html&gt;
&lt;head&gt;
&lt;meta http-equiv=&quot;Content-Type&quot;
content=&quot;text/html; charset=iso-8859-1&quot;&gt;
&lt;meta name=&quot;Generator&quot; content=&quot;10.70&quot;&gt;
&lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1&quot;&gt;
&lt;meta name=&quot;x-apple-disable-message-reformatting&quot;&gt;
&lt;meta name=&quot;format-detection&quot; content=&quot;telephone=no&quot;&gt;
&lt;style type=&quot;text/css&quot;&gt;#outlook a { padding:0;}.es-button { mso-style-priority:100!important; text-decoration:none!important;}a[x-apple-data-detectors] { color:inherit!important; text-decoration:none!imp [rest of string was truncated]&quot;;.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.txtR">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.updatetableofcontents">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.upload_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.User_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.User_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.usergroup_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.usergroup_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.usernamee">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.usernamee2__1_">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.UserNameImage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Utilities_PNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.UtilitiesHide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.UtilitiesOn">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Valid16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Valid32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.VenomEN">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.VenomENImage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.VenomImage">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Verizon16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.viewmergeddata">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.viewtablegridlines">
<summary>
  Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.VIP_LEGEND_">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.VIP_PRIME_PNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.VIP_ULTRA_PNG">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.VIPPaner">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.VIPRedirect16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.VIPRedirect32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.virgin16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Vivaldi_Mail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.walla16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Wallet">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.web_de16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.web16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.web23x23">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.widthLogo">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.withPass16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.withPass32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Wolf_EN">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Wolf_image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Yahoo">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Yahoo_image">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.yahoo16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.YahooPanel">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.yandex16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.youtube_16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.youtube_32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.YouTube_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.YouTube_On">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.youtube16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.youtube32x32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.zambrea">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Ziggo16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Zimbra16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Best_Sender.My.Resources.Resources.Zoho_Mail16x16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="T:Best_Sender.frmEmailSender.ProxyItem">
 <summary>
 فئة تمثل عنصر بروكسي
 </summary>
</member>
<member name="P:Best_Sender.frmEmailSender.ProxyItem.Proxy">
 <summary>
 عنوان البروكسي (IP:Port)
 </summary>
</member>
<member name="P:Best_Sender.frmEmailSender.ProxyItem.Status">
 <summary>
 حالة البروكسي
 </summary>
</member>
<member name="P:Best_Sender.frmEmailSender.ProxyItem.LastUsedEmail">
 <summary>
 البريد الأخير الذي تم إرساله باستخدام هذا البروكسي
 </summary>
</member>
<member name="P:Best_Sender.frmEmailSender.ProxyItem.EmailCount">
 <summary>
 عدد البرائد المرسلة باستخدام هذا البروكسي
 </summary>
</member>
<member name="P:Best_Sender.frmEmailSender.ProxyItem.LastUsedDate">
 <summary>
 تاريخ آخر استخدام للبروكسي
 </summary>
</member>
<member name="T:Best_Sender.frmEmailSender.TimeoutWebClient">
 <summary>
 WebClient مخصص مع دعم Timeout - حل خبير شبكات
 </summary>
</member>
<member name="T:Best_Sender.frmEmailSender.ProxyStatus">
 <summary>
 تعداد يمثل حالة البروكسي
 </summary>
</member>
<member name="F:Best_Sender.frmEmailSender.ProxyStatus.Pending">
 <summary>
 في انتظار الفحص
 </summary>
</member>
<member name="F:Best_Sender.frmEmailSender.ProxyStatus.Valid">
 <summary>
 صالح
 </summary>
</member>
<member name="F:Best_Sender.frmEmailSender.ProxyStatus.NotWorking">
 <summary>
 غير صالح
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.InitializeBntStopCheckProxy">
 <summary>
 تهيئة زر إيقاف الفحص
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.InitializePopupMenu5">
 <summary>
 تهيئة PopupMenu5 للبروكسي
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.InitializePopupMenu6">
 <summary>
 تهيئة PopupMenu6 للبروكسي (الجديد)
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.TestPopupMenu6">
 <summary>
 اختبار PopupMenu6 للتأكد من عمله
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.InitializeProxyCheckWorker">
 <summary>
 تهيئة BackgroundWorker للبروكسي
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ProxyCheckWorker_DoWork(System.Object,System.ComponentModel.DoWorkEventArgs)">
 <summary>
 معالج حدث DoWork للـ ProxyCheckWorker
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ProxyCheckWorker_ProgressChanged(System.Object,System.ComponentModel.ProgressChangedEventArgs)">
 <summary>
 معالج حدث ProgressChanged للـ ProxyCheckWorker
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ProxyCheckWorker_RunWorkerCompleted(System.Object,System.ComponentModel.RunWorkerCompletedEventArgs)">
 <summary>
 معالج حدث RunWorkerCompleted للـ ProxyCheckWorker
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnPopupMenu6_SelectListProxy(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالج حدث "Select List Proxy" من PopupMenu6
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnPopupMenu6_StartCheckProxy(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالج حدث "Start Check Proxy" من PopupMenu6
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnPopupMenu6_RemoveInvalid(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالج حدث "Remove Invalid" من PopupMenu6
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnPopupMenu6_RemoveAll(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالج حدث "Remove All" من PopupMenu6
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnPopupMenu6_SaveProxyValid(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالج حدث "Save Proxy Valid" من PopupMenu6
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnPopupMenu6_StopScan(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالج حدث "Stop Scan" من PopupMenu6
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnPopupMenu6_RemoveAllPending(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالج حدث "Remove All Pending" من PopupMenu6
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.RecordProxyEmailUsage(System.String,System.String)">
 <summary>
 تسجيل استخدام البروكسي مع البريد المرسل
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GetCurrentPublicIP">
 <summary>
 الحصول على IP العام الحالي - حل خبير شبكات
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GetCurrentPublicIPThroughProxy(System.Net.WebProxy)">
 <summary>
 الحصول على IP العام عبر البروكسي - تحقق متقدم
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.IsValidIP(System.String)">
 <summary>
 التحقق من صحة عنوان IP
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.TestProxyConnection(System.Net.WebProxy)">
 <summary>
 اختبار اتصال البروكسي المتقدم - خبير شبكات
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.LogNetworkDiagnostics(System.Net.WebProxy,System.String,System.Int32)">
 <summary>
 تسجيل تفصيلي لحالة الشبكة - تشخيص خبير
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.InitializeProxyGridControl">
 <summary>
 تهيئة GridControl3
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.AddProxyEventHandlers">
 <summary>
 إضافة معالجات الأحداث
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnSelectListProxyClick(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر اختيار قائمة البروكسيات
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ImportProxiesFromFile(System.String)">
 <summary>
 استيراد البروكسيات من ملف
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.UpdateProxyGridControl">
 <summary>
 تحديث GridControl3 بقائمة البروكسيات الحالية
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.UpdateProxyLabels">
 <summary>
 تحديث التسميات بإحصائيات البروكسيات
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnStartCheckProxyClick(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر بدء فحص البروكسيات
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ToggleProxyButtons(System.Boolean)">
 <summary>
 تمكين أو تعطيل الأزرار
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnStopCheckProxyClick(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر إيقاف فحص البروكسيات
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.BackgroundWorker3_DoWork(System.Object,System.ComponentModel.DoWorkEventArgs)">
 <summary>
 معالج حدث DoWork لـ BackgroundWorker3
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.CheckProxy(System.String)">
 <summary>
 فحص البروكسي للتأكد من صلاحيته مع تسجيل مفصل
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.BackgroundWorker3_ProgressChanged(System.Object,System.ComponentModel.ProgressChangedEventArgs)">
 <summary>
 معالج حدث ProgressChanged لـ BackgroundWorker3
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.BackgroundWorker3_RunWorkerCompleted(System.Object,System.ComponentModel.RunWorkerCompletedEventArgs)">
 <summary>
 معالج حدث RunWorkerCompleted لـ BackgroundWorker3
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnRemoveInvalidClick(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر حذف البروكسيات غير الصالحة
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnRemoveAllClick(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر حذف جميع البروكسيات
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnSaveProxyValidClick(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر حفظ البروكسيات الصالحة
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnToggleProxyUseChanged(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حالة مفتاح التبديل لاستخدام البروكسي
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnToggleAutoRemoveInvalidChanged(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حالة مفتاح التبديل لحذف البروكسيات غير الصالحة تلقائياً
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnEditButtonClick(System.Object,DevExpress.XtraEditors.Controls.ButtonPressedEventArgs)">
 <summary>
 معالج حدث النقر على زر التعديل في GridControl3
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.IsValidProxyFormat(System.String)">
 <summary>
 التحقق من صحة تنسيق البروكسي
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnDeleteButtonClick(System.Object,DevExpress.XtraEditors.Controls.ButtonPressedEventArgs)">
 <summary>
 معالج حدث النقر على زر الحذف في GridControl3 - محسن
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GridView3_CellValueChanged(System.Object,DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs)">
 <summary>
 معالج حدث تغيير قيمة الخلية
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GridView3_CustomRowCellEdit(System.Object,DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs)">
 <summary>
 معالج حدث تخصيص محرر الخلية
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GetNextValidProxy">
 <summary>
 الحصول على بروكسي صالح للاستخدام في الإرسال مع آلية التدوير
 </summary>
 <returns>بروكسي صالح أو Nothing إذا لم يكن هناك بروكسي صالح</returns>
</member>
<member name="M:Best_Sender.frmEmailSender.GridControl3_MouseUp(System.Object,System.Windows.Forms.MouseEventArgs)">
 <summary>
 معالج حدث النقر بزر الفأرة الأيمن على GridControl3 - MouseUp (للتوافق)
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GridView3_MouseDown_PopupMenu6(System.Object,System.Windows.Forms.MouseEventArgs)">
 <summary>
 معالج حدث النقر بزر الفأرة الأيمن على GridView3 - MouseDown (للـ PopupMenu6)
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GridView3_MouseDown(System.Object,System.Windows.Forms.MouseEventArgs)">
 <summary>
 معالج حدث النقر بزر الفأرة الأيمن على GridView3 - MouseDown (القديم)
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GridView_MouseUp(System.Object,System.Windows.Forms.MouseEventArgs)">
 <summary>
 معالج حدث النقر بزر الفأرة الأيمن على GridView - MouseUp (للتوافق)
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.Panel5_MouseUp(System.Object,System.Windows.Forms.MouseEventArgs)">
 <summary>
 معالج حدث النقر بزر الفأرة الأيمن على Panel5
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnSelectListProxyClick(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالجات الأحداث للقائمة المنبثقة - BarButtonItem17 (Select List Proxy)
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnStartCheckProxyClick(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالجات الأحداث للقائمة المنبثقة - BarButtonItem18 (Start Check Proxy)
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnRemoveInvalidClick(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالجات الأحداث للقائمة المنبثقة - BarButtonItem19 (Remove Invalid)
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnRemoveAllClick(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالجات الأحداث للقائمة المنبثقة - BarButtonItem20 (Remove All)
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnSaveProxyValidClick(System.Object,DevExpress.XtraBars.ItemClickEventArgs)">
 <summary>
 معالجات الأحداث للقائمة المنبثقة - BarButtonItem21 (Save Proxy Valid)
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ApplyNewButtonColors">
 <summary>
 تطبيق الألوان الجديدة على جميع أزرار البروكسي
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ApplyButtonStyle(DevExpress.XtraEditors.SimpleButton)">
 <summary>
 تطبيق نمط الألوان على زر واحد
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.SendEmailWithProxy(System.String,System.Int32,System.Boolean,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.List{System.String})">
 <summary>
 إرسال البريد الإلكتروني باستخدام البروكسي مع آلية التبديل التلقائي
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.MarkProxyAsInvalid(System.String)">
 <summary>
 تسجيل البروكسي كغير صالح
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.SendEmailWithSystemProxy(System.Net.WebProxy,System.String,System.Int32,System.Boolean,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.List{System.String})">
 <summary>
 إرسال البريد مع إجبار استخدام البروكسي - حل جذري ونهائي مع تتبع شبكة متقدم
 تم تطويره بخبرة 20+ سنة في أنظمة الشبكات والبريد الإلكتروني
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.SendEmailWithoutProxy(System.String,System.Int32,System.Boolean,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.List{System.String})">
 <summary>
 إرسال البريد الإلكتروني بدون بروكسي
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.OnProxyReportClick(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر تقرير البروكسي
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GenerateProxyStatusReport">
 <summary>
 إنشاء تقرير مفصل عن حالة البروكسي والقيود التقنية
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.SendEmailThroughProxyDirect(System.Net.WebProxy,System.String,System.Int32,System.Boolean,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.List{System.String})">
 <summary>
 إرسال البريد عبر البروكسي مباشرة باستخدام TCP Socket - الحل الجذري
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.SendSMTPThroughTunnel(System.Net.Sockets.NetworkStream,System.Boolean,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.List{System.String},System.String,System.String)">
 <summary>
 إرسال SMTP عبر النفق المُنشأ مع البروكسي
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.SendSMTPCommand(System.IO.Stream,System.String)">
 <summary>
 إرسال أمر SMTP
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ReadSMTPResponse(System.IO.Stream)">
 <summary>
 قراءة استجابة SMTP
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.BuildEmailContent(System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.List{System.String},System.String)">
 <summary>
 بناء محتوى البريد الإلكتروني مع headers البروكسي
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GridControl3_MouseDown(System.Object,System.Windows.Forms.MouseEventArgs)">
 <summary>
 معالج حدث النقر بزر الفأرة الأيمن على GridControl3 - MouseDown
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ShowBinanceStyleContextMenu(System.Drawing.Point)">
 <summary>
 إظهار قائمة منبثقة بتصميم Binance الاحترافي
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ApplyBinanceStyle(System.Windows.Forms.ContextMenuStrip)">
 <summary>
 تطبيق تصميم Binance على القائمة
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.AddBinanceStyleMenuItems(System.Windows.Forms.ContextMenuStrip)">
 <summary>
 إضافة عناصر القائمة بتصميم Binance
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.CreateBinanceMenuItem(System.String,System.String,System.Drawing.Color,System.EventHandler)">
 <summary>
 إنشاء عنصر قائمة بتصميم Binance
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.CreateBinanceMenuItemWithIcon(System.String,System.Drawing.Image,System.Drawing.Color,System.EventHandler)">
 <summary>
 إنشاء عنصر قائمة مع أيقونة من الموارد
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GetResourceIcon(System.String)">
 <summary>
 الحصول على أيقونة من الموارد
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ResizeIcon(System.Drawing.Image,System.Int32,System.Int32)">
 <summary>
 تغيير حجم الأيقونة
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.GetComfortaaFont(System.Single,System.Drawing.FontStyle)">
 <summary>
 الحصول على خط Comfortaa مع fallback
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.CreateBinanceSeparator">
 <summary>
 إنشاء فاصل بتصميم Binance
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSender.ShowSimpleContextMenu(System.Drawing.Point)">
 <summary>
 قائمة بسيطة كـ fallback
 </summary>
</member>
<member name="T:Best_Sender.BinanceMenuRenderer">
 <summary>
 Renderer مخصص لتصميم Binance الاحترافي
 </summary>
</member>
<member name="T:Best_Sender.BinanceColorTable">
 <summary>
 جدول ألوان Binance المخصص
 </summary>
</member>
<member name="M:Best_Sender.FuncSendMail.GetProxyFromManager(Best_Sender.frmEmailSender)">
 <summary>
 الحصول على البروكسي من مدير البروكسيات
 </summary>
 <param name="form">النموذج الرئيسي</param>
 <returns>WebProxy أو Nothing إذا لم يكن هناك بروكسي صالح</returns>
</member>
<member name="M:Best_Sender.frmEncodeLink.frmEncodeLink_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmEncodeLink.CenterControlsInForm">
 <summary>
 توسيط جميع العناصر في النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmlinktoAtt.CenterControlsAsGroup">
 <summary>
 توسيط العناصر كمجموعة واحدة مع الحفاظ على ترتيبها النسبي
 </summary>
</member>
<member name="M:Best_Sender.frmlinktoAtt.bntPaste_Click(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر bntPaste
 </summary>
</member>
<member name="M:Best_Sender.frmlinktoAtt.BntReset_Click(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر BntReset
 </summary>
</member>
<member name="M:Best_Sender.frmlinktoAtt.ComboBox_SleepTime_EditValueChanged(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير القيمة في ComboBox_SleepTime لعرض الوقت بالثواني
 </summary>
</member>
<member name="M:Best_Sender.frmNew.CenterControlsAsGroup">
 <summary>
 توسيط العناصر كمجموعة واحدة
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.IMAP_BSV_Load(System.Object,System.EventArgs)">
 <summary>
 عند تحميل الفورم، تفعيل خاصية التقدم والإلغاء للـ BackgroundWorker
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.bntUploade_Click(System.Object,System.EventArgs)">
 <summary>
 زر رفع الملف النصي
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.SimpleButton1_Click(System.Object,System.EventArgs)">
 <summary>
 زر بدء العملية (Login and Fetch Emails)
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.BackgroundWorker1_DoWork(System.Object,System.ComponentModel.DoWorkEventArgs)">
 <summary>
 العمليات الخلفية: قراءة الملف أو معالجة حساب واحد
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.ProcessSingleAccount(System.ComponentModel.BackgroundWorker,System.ComponentModel.DoWorkEventArgs)">
 <summary>
 معالجة حساب واحد فقط
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.BackgroundWorker1_ProgressChanged(System.Object,System.ComponentModel.ProgressChangedEventArgs)">
 <summary>
 تحديث شريط التقدم والرسائل
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.BackgroundWorker1_RunWorkerCompleted(System.Object,System.ComponentModel.RunWorkerCompletedEventArgs)">
 <summary>
 بعد انتهاء العملية
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.ExtractEmailFromLine(System.String)">
 <summary>
 استخراج البريد من سطر في الملف
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.ExtractPasswordFromLine(System.String)">
 <summary>
 استخراج كلمة المرور من سطر في الملف
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.GetDomainFromEmail(System.String)">
 <summary>
 استخراج اسم الدومين من البريد الإلكتروني
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.ExtractEmails(System.String)">
 <summary>
 استخراج عناوين البريد الإلكتروني باستخدام Regex
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.InitializeDataTable(System.Data.DataTable)">
 <summary>
 إنشاء هيكل الجدول
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.SaveEmailDataToFile(System.String,System.Data.DataTable)">
 <summary>
 حفظ بيانات البريد إلى ملف TXT داخل مجلد باسم البريد
 </summary>
</member>
<member name="M:Best_Sender.IMAP_BSV.ShowAppPasswordInstructions(System.String)">
 <summary>
 تعليمات لإنشاء Application Password حسب نوع الحساب
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectSpider.frmRedirectSpider_FormClosing(System.Object,System.Windows.Forms.FormClosingEventArgs)">
 <summary>
 معالج حدث إغلاق النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectSpider.frmRedirectSpider_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectSpider.CenterControlsInForm">
 <summary>
 توسيط جميع العناصر كمجموعة واحدة مع الحفاظ على مواقعها النسبية بدقة تامة
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectSpider.IsDisposedControl(System.Windows.Forms.Control)">
 <summary>
 التحقق مما إذا كان عنصر التحكم تم التخلص منه
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectWithPassword.XtraTabControl1_SelectedPageChanged(System.Object,DevExpress.XtraTab.TabPageChangedEventArgs)">
 <summary>
 معالج حدث تغيير الصفحة النشطة في XtraTabControl1
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectWithPassword.frmRedirectWithPassword_FormClosing(System.Object,System.Windows.Forms.FormClosingEventArgs)">
 <summary>
 معالج حدث إغلاق النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectWithPassword.frmRedirectWithPassword_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectWithPassword.CenterControlsInForm">
 <summary>
 توسيط جميع العناصر كمجموعة واحدة داخل XtraTabControl1 مع الحفاظ على مواقعها النسبية بدقة تامة
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectWithPassword.IsDisposedControl(System.Windows.Forms.Control)">
 <summary>
 التحقق مما إذا كان عنصر التحكم تم التخلص منه
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectWithPassword.UpdatePreview(System.Object,System.EventArgs)">
 <summary>
 تحديث معاينة الصفحة في WebBrowser2
 </summary>
</member>
<member name="M:Best_Sender.frmHideLink.CenterControls">
 <summary>
 توسيط العناصر كمجموعة واحدة مع الحفاظ على ترتيبها النسبي
 </summary>
</member>
<member name="M:Best_Sender.frmPassMicrosoft.frmPassMicrosoft_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmPassMicrosoft.CenterControlsInForm">
 <summary>
 توسيط جميع العناصر في النموذج دون تغيير حجمها
 </summary>
</member>
<member name="M:Best_Sender.frmReAuthenticationAtt.frmReAuthenticationAtt_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmReAuthenticationAtt.CenterControlsInForm">
 <summary>
 توسيط جميع العناصر في النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectPuzzle.frmRedirectPuzzle_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectPuzzle.CenterControlsInForm">
 <summary>
 توسيط جميع العناصر في النموذج دون تغيير حجمها
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.frmCheckOffce356_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.CenterControls">
 <summary>
 تنسيق أحجام العناصر وموقعها
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.ApplyBinanceStyle">
 <summary>
 تطبيق تصميم Binance على النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.FormatBinanceButton(DevExpress.XtraEditors.SimpleButton,System.String)">
 <summary>
 تنسيق الزر بأسلوب Binance
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.SetupGridControl">
 <summary>
 إعداد GridControl
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.SetupBackgroundWorker">
 <summary>
 إعداد BackgroundWorker
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.BtnBrowse_Click(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر Browse
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.ReadEmailFile(System.String)">
 <summary>
 قراءة ملف البريد الإلكتروني وتحليله
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.IsValidEmail(System.String)">
 <summary>
 التحقق من صحة تنسيق البريد الإلكتروني
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.BtnStart_Click(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر Start
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.BtnStop_Click(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر Stop
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.BackgroundWorker_DoWork(System.Object,System.ComponentModel.DoWorkEventArgs)">
 <summary>
 معالج حدث DoWork للـ BackgroundWorker
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.BackgroundWorker_ProgressChanged(System.Object,System.ComponentModel.ProgressChangedEventArgs)">
 <summary>
 معالج حدث ProgressChanged للـ BackgroundWorker
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.BackgroundWorker_RunWorkerCompleted(System.Object,System.ComponentModel.RunWorkerCompletedEventArgs)">
 <summary>
 معالج حدث RunWorkerCompleted للـ BackgroundWorker
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.CheckEmailAccount(System.String,System.String)">
 <summary>
 التحقق من حساب البريد الإلكتروني - طريقة بسيطة وموثوقة
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.SimpleSmtpCheck(System.String,System.String,System.String,System.Int32)">
 <summary>
 التحقق البسيط من SMTP باستخدام خادم ومنفذ محددين
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.DirectCheckOutlook(System.String,System.String)">
 <summary>
 التحقق المباشر من حساب Outlook/Hotmail باستخدام اتصال TCP مباشر
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.ReadResponseWithTimeout(System.IO.StreamReader,System.Int32)">
 <summary>
 قراءة الاستجابة من خادم SMTP مع مهلة
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.CheckSmtp(System.String,System.String,System.String,System.Int32)">
 <summary>
 التحقق من SMTP باستخدام خادم ومنفذ محددين
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.TestSmtpConnection(System.String,System.Int32,System.String,System.String)">
 <summary>
 اختبار الاتصال بخادم SMTP - باستخدام كود من SmtpTester.vb
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.Test_SmtpAccount(System.String,System.String,System.String,System.String,System.Boolean,System.String)">
 <summary>
 اختبار حساب SMTP - نسخة من SmtpTester.vb
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.BntSave_Click(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر Save
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.GetEmailType(System.String)">
 <summary>
 تحديد نوع البريد الإلكتروني بناءً على المجال
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.GridView_Click(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على GridView
 </summary>
</member>
<member name="M:Best_Sender.frmCheckOffce356.PasswordHideTimer_Tick(System.Object,System.EventArgs)">
 <summary>
 معالج حدث انتهاء مؤقت إخفاء كلمة المرور
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSorterPro.ApplyBinanceGridStyling">
 <summary>
 تطبيق تصميم Binance على GridControl مع الحفاظ على ثيم WXI
 </summary>
</member>
<member name="M:Best_Sender.frmEmailSorterPro.GridView1_RowCellStyle(System.Object,DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs)">
 <summary>
 معالج حدث تنسيق خلايا الصفوف لتطبيق ألوان مخصصة على القيم الموجبة والسالبة
 </summary>
</member>
<member name="M:Best_Sender.frmSmtpTester.ApplyBinanceGridStyling">
 <summary>
 تطبيق تصميم Binance على GridControl
 </summary>
</member>
<member name="M:Best_Sender.frmSmtpTester.GridView1_CustomDrawCell(System.Object,DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs)">
 <summary>
 معالج حدث رسم الخلايا المخصص لعرض الأيقونات والألوان المخصصة في عمود الحالة
 </summary>
</member>
<member name="M:Best_Sender.frmBase64.AddHeaderLabel">
 <summary>
 إضافة صورة العنوان في أعلى النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmBase64.frmBase64_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmBase64.CenterControls">
 <summary>
 تنسيق أحجام العناصر وموقعها
 </summary>
</member>
<member name="M:Best_Sender.frmBase64.ApplyBinanceStyle">
 <summary>
 تطبيق تصميم Binance على النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmBase64.FormatButton(DevExpress.XtraEditors.SimpleButton,System.String)">
 <summary>
 تنسيق الزر
 </summary>
</member>
<member name="M:Best_Sender.frmBase64.FormatBinanceButton(DevExpress.XtraEditors.SimpleButton,System.String)">
 <summary>
 تنسيق الزر بأسلوب Binance (حواف فقط)
 </summary>
</member>
<member name="M:Best_Sender.frmBase64.SetButtonIcon(DevExpress.XtraEditors.SimpleButton,System.String)">
 <summary>
 تعيين أيقونة للزر
 </summary>
</member>
<member name="M:Best_Sender.frmDakEncryption.frmDakEncryption_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmDakEncryption.CenterControls">
 <summary>
 تنسيق أحجام العناصر وموقعها
 </summary>
</member>
<member name="M:Best_Sender.frmDakEncryption.AddHeaderImage">
 <summary>
 إضافة صورة العنوان في أعلى النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmDakEncryption.ApplyBinanceStyle">
 <summary>
 تطبيق تصميم Binance على النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmDakEncryption.FormatBinanceButton(DevExpress.XtraEditors.SimpleButton,System.String)">
 <summary>
 تنسيق الزر بأسلوب Binance (حواف فقط)
 </summary>
</member>
<member name="M:Best_Sender.frmDakEncryption.EnhanceImageTag(System.Text.RegularExpressions.Match)">
 <summary>
 تحسين وسوم الصور لعملاء البريد الإلكتروني
 </summary>
</member>
<member name="M:Best_Sender.frmDragonLetter.frmDragonLetter_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmDragonLetter.CenterControls">
 <summary>
 تنسيق أحجام العناصر وموقعها
 </summary>
</member>
<member name="M:Best_Sender.frmDragonLetter.AddHeaderImage">
 <summary>
 إضافة صورة العنوان في أعلى النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmDragonLetter.ApplyBinanceStyle">
 <summary>
 تطبيق تصميم Binance على النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmDragonLetter.FormatBinanceButton(DevExpress.XtraEditors.SimpleButton,System.String)">
 <summary>
 تنسيق الزر بأسلوب Binance (حواف فقط)
 </summary>
</member>
<member name="M:Best_Sender.frmEncodedLinkWolf.frmEncodedLinkWolf_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmEncodedLinkWolf.CenterControlsInForm">
 <summary>
 توسيط جميع العناصر في النموذج كمجموعة واحدة
 </summary>
</member>
<member name="M:Best_Sender.frmEncodedLinkWolf.ApplyBinanceStyle">
 <summary>
 تطبيق أسلوب Binance على العناصر
 </summary>
</member>
<member name="M:Best_Sender.frmGhost.frmGhost_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmGhost.CenterControls">
 <summary>
 تنسيق أحجام العناصر وموقعها
 </summary>
</member>
<member name="M:Best_Sender.frmGhost.AddHeaderImage">
 <summary>
 إضافة صورة العنوان في أعلى النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmGhost.ApplyBinanceStyle">
 <summary>
 تطبيق تصميم Binance على النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmGhost.FormatBinanceButton(DevExpress.XtraEditors.SimpleButton,System.String)">
 <summary>
 تنسيق الزر بأسلوب Binance (حواف فقط)
 </summary>
</member>
<member name="M:Best_Sender.frmLetterMakerST.ComboBox_ButtonColor_Click(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر اختيار لون الزر
 </summary>
</member>
<member name="M:Best_Sender.frmLetterMakerST.ComboBox_TextColor_Click(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر اختيار لون النص
 </summary>
</member>
<member name="M:Best_Sender.frmLetterMakerST.ComboBox_TitleColor_Click(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر اختيار لون العنوان
 </summary>
</member>
<member name="M:Best_Sender.frmVenomENCLetter.CountOccurrences(System.String,System.String)">
 <summary>
 حساب عدد مرات تكرار نص معين في نص آخر
 </summary>
 <param name="text">النص المراد البحث فيه</param>
 <param name="searchText">النص المراد البحث عنه</param>
 <returns>عدد مرات التكرار</returns>
</member>
<member name="M:Best_Sender.frmVenomENCLetter.frmVenomENCLetter_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmVenomENCLetter.CenterControls">
 <summary>
 توسيط جميع العناصر في النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmVenomENCLetter.AddHeaderImage">
 <summary>
 إضافة صورة العنوان في أعلى النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmVenomENCLetter.ApplyBinanceStyle">
 <summary>
 تطبيق تصميم Binance على النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmVenomENCLetter.FormatBinanceButton(DevExpress.XtraEditors.SimpleButton,System.String)">
 <summary>
 تنسيق الزر بأسلوب Binance (حواف فقط)
 </summary>
</member>
<member name="M:Best_Sender.frmSupportBSVBSV.frmSupportBSVBSV_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmSupportBSVBSV.CenterControls">
 <summary>
 توسيط جميع العناصر في النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmSupportBSVBSV.MoveControlWithOffset(System.Windows.Forms.Control,System.Int32,System.Int32)">
 <summary>
 تحريك عنصر بإزاحة محددة
 </summary>
</member>
<member name="M:Best_Sender.frmMain.InitializeNotificationShake">
 <summary>
 تهيئة timer الاهتزاز للإشعار
 </summary>
</member>
<member name="M:Best_Sender.frmMain.InitializeComfortaaFont">
 <summary>
 تهيئة خط Comfortaa مع معالجة الأخطاء
 </summary>
</member>
<member name="M:Best_Sender.frmMain.NotificationShakeTimer_Tick(System.Object,System.EventArgs)">
 <summary>
 معالج timer الاهتزاز
 </summary>
</member>
<member name="M:Best_Sender.frmMain.StartNotificationShake">
 <summary>
 بدء تأثير الاهتزاز للإشعار
 </summary>
</member>
<member name="M:Best_Sender.frmMain.StopNotificationShake">
 <summary>
 إيقاف تأثير الاهتزاز للإشعار
 </summary>
</member>
<member name="M:Best_Sender.frmMain.CreateShiftedImage(System.Drawing.Image,System.Int32,System.Int32)">
 <summary>
 إنشاء صورة مزاحة لمحاكاة اهتزاز الجرس
 </summary>
</member>
<member name="M:Best_Sender.frmMain.UpdateNotificationText">
 <summary>
 تحديث نص الإشعار مع أرقام الإصدارات
 </summary>
</member>
<member name="M:Best_Sender.frmMain.ApplyBinanceStyle_Click(System.Object,System.EventArgs)">
 <summary>
 تطبيق تصميم Binance على جميع النماذج عند النقر على الزر
 </summary>
</member>
<member name="M:Best_Sender.frmMain.ApplyBinanceStyleToGrid(DevExpress.XtraGrid.GridControl,System.Boolean)">
 <summary>
 تطبيق تصميم Binance على GridControl
 </summary>
 <param name="gridControl">GridControl المراد تطبيق التصميم عليه</param>
 <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
</member>
<member name="M:Best_Sender.frmMain.ApplyBinanceStyleDirectly(DevExpress.XtraGrid.GridControl,System.Boolean)">
 <summary>
 تطبيق تصميم Binance مباشرة على GridControl
 </summary>
 <param name="gridControl">GridControl المراد تطبيق التصميم عليه</param>
 <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
</member>
<member name="M:Best_Sender.frmMain.ApplyBinanceStyleToGridView(DevExpress.XtraGrid.Views.Grid.GridView)">
 <summary>
 تطبيق تصميم Binance على GridView
 </summary>
 <param name="gridView">GridView المراد تطبيق التصميم عليه</param>
</member>
<member name="M:Best_Sender.frmMain.ApplyBinanceStyleToForm(System.Windows.Forms.Form,System.Boolean)">
 <summary>
 تطبيق تصميم Binance على جميع عناصر GridControl في النموذج
 </summary>
 <param name="form">النموذج المراد تطبيق التصميم على عناصر GridControl فيه</param>
 <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
</member>
<member name="M:Best_Sender.frmMain.FindAndApplyStyleToGrids(System.Windows.Forms.Control,System.Boolean)">
 <summary>
 البحث عن عناصر GridControl في العناصر المتداخلة وتطبيق التصميم عليها
 </summary>
</member>
<member name="M:Best_Sender.frmMain.ApplyBinanceStyleToNewForm(System.Object,System.EventArgs)">
 <summary>
 تطبيق تصميم Binance على النموذج الجديد عند تنشيطه
 </summary>
</member>
<member name="M:Best_Sender.frmMain.ApplyBinanceStyleToAllOpenForms">
 <summary>
 تطبيق تصميم Binance على جميع النماذج المفتوحة
 </summary>
</member>
<member name="M:Best_Sender.frmDownloadBSVBSV.frmDownloadBSVBSV_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmDownloadBSVBSV.CenterControlsAsGroup">
 <summary>
 توسيط جميع العناصر كمجموعة واحدة مع الحفاظ على ترتيبها النسبي
 </summary>
</member>
<member name="M:Best_Sender.frmActivationInfo.frmActivationInfo_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmActivationInfo.CenterGroupControl">
 <summary>
 توسيط المجموعة في النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmGoogleCaptcha.frmGoogleCaptcha_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmGoogleCaptcha.CenterControlsInForm">
 <summary>
 توسيط جميع العناصر في النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.txtWidth_TextChanged(System.Object,System.EventArgs)">
 <summary>
 تغيير حجم الصورة عند تغيير قيمة العرض
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.txtHeight_TextChanged(System.Object,System.EventArgs)">
 <summary>
 تغيير حجم الصورة عند تغيير قيمة الارتفاع
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.ResizePreviewImage">
 <summary>
 تغيير حجم الصورة في PicLogo بناءً على القيم المدخلة في txtWidth و txtHeight
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.frmImageResize_Load(System.Object,System.EventArgs)">
 <summary>
 حدث تحميل النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.CreateMainPanel">
 <summary>
 إنشاء MainPanel
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.MoveControlsToMainPanel">
 <summary>
 نقل العناصر إلى MainPanel
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.CenterMainPanel">
 <summary>
 وضع MainPanel في المنتصف
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.CenterControlsInMainPanel">
 <summary>
 توسيط العناصر داخل MainPanel
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.MainPanel_Paint(System.Object,System.Windows.Forms.PaintEventArgs)">
 <summary>
 معالج حدث Paint للـ MainPanel
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.GetRoundedRectPath(System.Drawing.Rectangle,System.Int32)">
 <summary>
 إنشاء مسار لمستطيل بحواف منحنية
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.ApplyRoundedCorners(System.Windows.Forms.Panel,System.Int32)">
 <summary>
 تطبيق حواف منحنية على لوحة
 </summary>
 <param name="panel">اللوحة المراد تطبيق الحواف المنحنية عليها</param>
 <param name="radius">نصف قطر الانحناء</param>
</member>
<member name="M:Best_Sender.frmImageResize.frmImageResize_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmImageResize.MainPanel_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم MainPanel
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectLink.MainPanel_Paint(System.Object,System.Windows.Forms.PaintEventArgs)">
 <summary>
 معالج حدث Paint للـ MainPanel
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectLink.GetRoundedRectPath(System.Drawing.Rectangle,System.Int32)">
 <summary>
 إنشاء مسار لمستطيل بحواف منحنية
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectLink.CenterMainPanel">
 <summary>
 وضع MainPanel في المنتصف وجعلها منحنية من الأطراف
 </summary>
</member>
<member name="M:Best_Sender.frmRedirectLink.ApplyRoundedCorners(System.Windows.Forms.Panel,System.Int32)">
 <summary>
 تطبيق حواف منحنية على لوحة
 </summary>
 <param name="panel">اللوحة المراد تطبيق الحواف المنحنية عليها</param>
 <param name="radius">نصف قطر الانحناء</param>
</member>
<member name="M:Best_Sender.frmRedirectLink.frmRedirectLink_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.frmQuickAccountCheck_Load(System.Object,System.EventArgs)">
 <summary>
 حدث تحميل النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.CreateMainPanel">
 <summary>
 إنشاء MainPanel
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.MoveControlsToMainPanel">
 <summary>
 نقل العناصر إلى MainPanel
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.CenterMainPanel">
 <summary>
 وضع MainPanel في المنتصف
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.CenterControlsInMainPanel">
 <summary>
 توسيط العناصر داخل MainPanel
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.MainPanel_Paint(System.Object,System.Windows.Forms.PaintEventArgs)">
 <summary>
 معالج حدث Paint للـ MainPanel
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.GetRoundedRectPath(System.Drawing.Rectangle,System.Int32)">
 <summary>
 إنشاء مسار لمستطيل بحواف منحنية
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.ApplyRoundedCorners(System.Windows.Forms.Panel,System.Int32)">
 <summary>
 تطبيق حواف منحنية على لوحة
 </summary>
 <param name="panel">اللوحة المراد تطبيق الحواف المنحنية عليها</param>
 <param name="radius">نصف قطر الانحناء</param>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.frmQuickAccountCheck_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم النموذج
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.MainPanel_Resize(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير حجم MainPanel
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.CheckFieldsAndUpdateButtons">
 <summary>
 التحقق من وجود بيانات في الحقول وإظهار/إخفاء BntResetAll
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.TxtSMTPServer_TextChanged(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير النص في TxtSMTPServer
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.TxtEmail_TextChanged(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير النص في TxtEmail
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.TxtPassword_TextChanged(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير النص في TxtPassword
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.TxtPort_TextChanged(System.Object,System.EventArgs)">
 <summary>
 معالج حدث تغيير النص في TxtPort
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.BntAddAccount_Click(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر على زر BntAddAccount
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.ListBoxControl1_DoubleClick(System.Object,System.EventArgs)">
 <summary>
 معالج حدث النقر المزدوج على ListBoxControl1
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.HideAllControls">
 <summary>
 إخفاء جميع العناصر ما عدا ListBoxControl1
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.ShowAllControls">
 <summary>
 إظهار جميع العناصر ما عدا ListBoxControl1
 </summary>
</member>
<member name="M:Best_Sender.frmQuickAccountCheck.CenterListBoxControl1">
 <summary>
 وضع ListBoxControl1 في المنتصف
 </summary>
</member>
<member name="T:Best_Sender.frmZeroPass">
 <summary>
 نموذج Zero Password لاستخراج البريد الإلكتروني من الملفات النصية
 مطور بخبرة 20 عام في VB.NET و Windows Forms
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.KeepUIResponsive">
 <summary>
 دالة لضمان استجابة الواجهة أثناء العمليات الطويلة
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.UploadeList">
 <summary>
 الخطوة 1: رفع ملف نصي باستخدام BackgroundWorker1
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.Startlist">
 <summary>
 الخطوة 2: بدء/استكمال استخراج البريد الإلكتروني باستخدام BackgroundWorker2
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.StopProcess">
 <summary>
 الخطوة 3: إيقاف مؤقت لعملية الاستخراج (يمكن الاستكمال لاحقاً)
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.SaveList">
 <summary>
 الخطوة 4: حفظ البريد المستخرج في مجلد ZeroPass على سطح المكتب
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.bntClearList">
 <summary>
 الخطوة 5: مسح جميع البيانات
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.BackgroundWorker1_DoWork(System.Object,System.ComponentModel.DoWorkEventArgs)">
 <summary>
 تحميل الملف في الخلفية باستخدام StreamReader
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.BackgroundWorker1_RunWorkerCompleted(System.Object,System.ComponentModel.RunWorkerCompletedEventArgs)">
 <summary>
 معالجة نتيجة تحميل الملف
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.BackgroundWorker2_DoWork(System.Object,System.ComponentModel.DoWorkEventArgs)">
 <summary>
 استخراج البريد الإلكتروني في الخلفية باستخدام Regex مع دعم الاستكمال
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.BackgroundWorker2_ProgressChanged(System.Object,System.ComponentModel.ProgressChangedEventArgs)">
 <summary>
 تحديث التقدم أثناء استخراج البريد
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.BackgroundWorker2_RunWorkerCompleted(System.Object,System.ComponentModel.RunWorkerCompletedEventArgs)">
 <summary>
 معالجة نتيجة استخراج البريد مع دعم الإيقاف المؤقت
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.UpdateEmailCount">
 <summary>
 تحديث عدد البريد المستخرج
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.ShowProgressPanel(System.String,System.String)">
 <summary>
 إظهار ProgressPanel1
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.HideProgressPanel">
 <summary>
 إخفاء ProgressPanel1
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.UpdateProgressPanel(System.String)">
 <summary>
 تحديث وصف ProgressPanel1
 </summary>
</member>
<member name="M:Best_Sender.frmZeroPass.SetButtonsEnabled(System.Boolean)">
 <summary>
 تمكين/تعطيل الأزرار
 </summary>
</member>
</members>
</doc>
