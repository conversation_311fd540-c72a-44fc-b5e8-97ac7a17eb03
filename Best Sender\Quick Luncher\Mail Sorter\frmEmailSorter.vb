﻿Imports System.ComponentModel
Imports System.IO
Imports Chilkat
Imports DevExpress.XtraGrid.Views.Grid
Public Class frmEmailSorter
#Region "User Control"
    Private _data As DataModel
    Private quota As Integer
    Private linesLock As Object
    Private WithEvents timer As New System.Timers.Timer
    Private bw1 As BackgroundWorker
    Private bw2 As BackgroundWorker
    Private bw3 As BackgroundWorker
    Private bw4 As BackgroundWorker
    Private bw5 As BackgroundWorker
    Private bw6 As BackgroundWorker
    Private bw7 As BackgroundWorker
    Private bw8 As BackgroundWorker
    Private bw9 As BackgroundWorker
    Private bw10 As BackgroundWorker
    Private bw11 As BackgroundWorker
    Private Sub BntBrowse_Click(sender As Object, e As EventArgs) Handles BntBrowse.Click
        Dim openFileDlg As OpenFileDialog = New OpenFileDialog()
        openFileDlg.Filter = "Text Files|*.txt;..."
        openFileDlg.InitialDirectory = Directory.GetCurrentDirectory()
        If openFileDlg.ShowDialog() = DialogResult.OK Then
            Data.Clear()
            Dim text = File.ReadAllText(openFileDlg.FileName)
            text = text.Replace(Environment.NewLine, " ")
            txtPath.Text = openFileDlg.FileName
            Data.MailList = text.Split(" "c).ToList()
            Data.MailList = Data.MailList.Distinct().ToList()
            Data.lines_1 = Data.MailList.Count
            Data.AllLines = Data.lines_1
            DevExpress.XtraEditors.XtraMessageBox.Show("File Loaded Successfully! Press Start To start Sorting...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub BntSave_Click(sender As Object, e As EventArgs) Handles BntSave.Click
        If txtPath.Text.Trim = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Select the mail list txt file...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        If Data.MailList.Count = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("The Mail list txt file is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        ProgressBarControl1.Properties.Step = 1
        ProgressBarControl1.Properties.PercentView = True
        ProgressBarControl1.Properties.Maximum = 100
        ProgressBarControl1.Properties.Minimum = 0
        ProgressBarControl1.Position = 0
        Panel4.Visible = True
        Dim emailCount = Data.MailList.Count
        quota = emailCount / 10
        Data.lines_1 = 0
        timer.Enabled = True
        bw1.RunWorkerAsync(1)
        bw2.RunWorkerAsync(2)
        bw3.RunWorkerAsync(3)
        bw4.RunWorkerAsync(4)
        bw5.RunWorkerAsync(5)
        bw7.RunWorkerAsync(6)
        bw8.RunWorkerAsync(7)
        bw9.RunWorkerAsync(8)
        bw10.RunWorkerAsync(9)
        bw11.RunWorkerAsync(10)
    End Sub
    '===================================================================
    Friend Property Data As DataModel
        Get
            Return _data
        End Get
        Set(value As DataModel)
            _data = value
        End Set
    End Property
    '===================================================================
    Private Sub timer_Elapsed(sender As Object, e As Timers.ElapsedEventArgs) Handles timer.Elapsed
        'Add code here to check if the web service is updated and update label
        For i = 0 To DTMails.Rows.Count - 1
            Select Case LCase(DTMails.Rows(i).Item("MailService"))
                Case LCase("Gmail")
                    DTMails(i)("MailFount") = Data.sortedMails.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMails
                Case LCase("Yahoo")
                    DTMails(i)("MailFount") = Data.sortedMail1.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail1
                Case LCase("Hotmail")
                    DTMails(i)("MailFount") = Data.sortedMail2.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail2
                Case LCase("Outlook")
                    DTMails(i)("MailFount") = Data.sortedMail3.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail3.ToString() & vbNewLine & Data.sortedMail4.ToString()
                Case LCase("Office 365")
                    DTMails(i)("MailFount") = Data.sortedMail4.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail4
                Case LCase("GoDaddy")
                    DTMails(i)("MailFount") = Data.sortedMail5.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail5
                Case LCase("163")
                    DTMails(i)("MailFount") = Data.sortedMail6.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail6
                Case LCase("1 and 1")
                    DTMails(i)("MailFount") = Data.sortedMail7.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail7
                Case LCase("RackSpace")
                    DTMails(i)("MailFount") = Data.sortedMail8.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail8
                Case LCase("Mail.Ru")
                    DTMails(i)("MailFount") = Data.sortedMail9.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail9
                Case LCase("Other")
                    DTMails(i)("MailFount") = Data.sortedMail10.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail10
                Case LCase("263")
                    DTMails(i)("MailFount") = Data.sortedMail11.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail11
                Case LCase("Aliyan")
                    DTMails(i)("MailFount") = Data.sortedMail12.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail12
                Case LCase("Anazana")
                    DTMails(i)("MailFount") = Data.sortedMail13.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail13
                Case LCase("CoreMail")
                    DTMails(i)("MailFount") = Data.sortedMail14.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail14
                Case LCase("EarthLink")
                    DTMails(i)("MailFount") = Data.sortedMail15.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail15
                Case LCase("GMX")
                    DTMails(i)("MailFount") = Data.sortedMail16.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail16
                Case LCase("Hinet")
                    DTMails(i)("MailFount") = Data.sortedMail17.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail17
                Case LCase("iCloud")
                    DTMails(i)("MailFount") = Data.sortedMail18.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail18
                Case LCase("iiNet")
                    DTMails(i)("MailFount") = Data.sortedMail19.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail19
                Case LCase("MimeCast")
                    DTMails(i)("MailFount") = Data.sortedMail20.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail20
                Case LCase("NameCheap")
                    DTMails(i)("MailFount") = Data.sortedMail21.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail21
                Case LCase("Orange")
                    DTMails(i)("MailFount") = Data.sortedMail22.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail22
                Case LCase("QQ")
                    DTMails(i)("MailFount") = Data.sortedMail23.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail23
                Case LCase("Web.de")
                    DTMails(i)("MailFount") = Data.sortedMail24.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail24
                Case LCase("Yandex")
                    DTMails(i)("MailFount") = Data.sortedMail25.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail25
                Case LCase("ZambriaMail")
                    DTMails(i)("MailFount") = Data.sortedMail26.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail26
                Case LCase("Zoho")
                    DTMails(i)("MailFount") = Data.sortedMail27.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail27
                Case LCase("Amazon")
                    DTMails(i)("MailFount") = Data.sortedMail28.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail28
                Case LCase("Ovhcloud")
                    DTMails(i)("MailFount") = Data.sortedMail29.Count.ToString
                    DTMails(i)("MailAccount") = Data.sortedMail29
            End Select
        Next
        GridControl1.DataSource = DTMails
    End Sub
    Private Sub Extract_MX(ByVal index As Integer, ByVal count As Integer, ByVal sender As Object)
        Dim Client As MailMan = New MailMan()
        Client.UnlockComponent("ATMMINMAILQ_qOJ9Q2FfpW5O")
        Dim mxDomain, host As String
        Dim hostIndex = 0
        For Each email In Data.MailList.GetRange(index, count)
            If CType(sender, BackgroundWorker).CancellationPending Then
                Return
            End If
            SyncLock linesLock
                Data.lines_1 += 1
            End SyncLock
            Try
                host = email.Substring(email.IndexOf("@"c) + 1)
                hostIndex = Data.KnownDomains.IndexOf(host)
                If hostIndex <> -1 Then
                    functions.returndomain(Data.KnownMx(hostIndex), email, Data, Me)
                Else
                    mxDomain = Client.MxLookupAll(email)?.ToString()
                    If Client.LastMethodSuccess = True Then
                        'data.dt.Rows.Add(email, mxDomain, "");
                        functions.returndomain(mxDomain, email, Data, Me)
                    Else
                        functions.returndomain(host, email, Data, Me)
                    End If
                End If
                CType(sender, BackgroundWorker).ReportProgress(Data.percent)
            Catch ex As Exception
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        Next
        'ProgressBar1.Value = Data.percent
        If Data.percent = 100 Then
            Panel4.Invoke(Sub()
                              Panel4.Visible = False
                              Refresh()
                          End Sub)
            'SimpleButton1.Invoke(Sub()
            '                         SimpleButton1.Enabled = True
            '                         Refresh()
            '                     End Sub)
            'Timer1.Enabled = True
            DevExpress.XtraEditors.XtraMessageBox.Show("Task Finished Successfully", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    '===================================================================
    Private Sub backgroundWorkers_DoWork(ByVal sender As Object, ByVal e As DoWorkEventArgs)
        Select Case e.Argument
            Case 1
                Extract_MX(0, quota, sender)
            Case 2
                Extract_MX(quota, quota, sender)
            Case 3
                Extract_MX(2 * quota, quota, sender)
            Case 4
                Extract_MX(3 * quota, quota, sender)
            Case 5
                Extract_MX(4 * quota, quota, sender)
            Case 6
                Extract_MX(5 * quota, quota, sender)
            Case 7
                Extract_MX(6 * quota, quota, sender)
            Case 8
                Extract_MX(7 * quota, quota, sender)
            Case 9
                Extract_MX(8 * quota, quota, sender)
            Case 10
                Extract_MX(9 * quota, Data.MailList.Count - 9 * quota, sender)
            Case Else
        End Select
    End Sub
    '===================================================================
    Private Sub backgroundWorkers_ProgressChanged(sender As Object, e As ProgressChangedEventArgs)
        ProgressBarControl1.EditValue = e.ProgressPercentage.ToString()
        ProgressBarControl1.PerformStep()
        ProgressBarControl1.Update()
    End Sub
    '===================================================================
    Private Sub backgroundWorkers_RunWorkerCompleted(ByVal sender As Object, ByVal e As RunWorkerCompletedEventArgs)
        ' First, handle the case where an exception was thrown.
        If e.Error IsNot Nothing Then
            DevExpress.XtraEditors.XtraMessageBox.Show(e.Error.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        ElseIf e.Cancelled Then
        Else
        End If
    End Sub
    '===================================================================
    Private Sub backgroundWorkers_DoWork6(ByVal sender As Object, ByVal e As DoWorkEventArgs)
        functions.SaveLists(Data, CStr(e.Argument))
    End Sub
    Private Sub backgroundWorkers_RunWorker6Completed(ByVal sender As Object, ByVal e As RunWorkerCompletedEventArgs)
        ' First, handle the case where an exception was thrown.
        If e.Error IsNot Nothing Then
            DevExpress.XtraEditors.XtraMessageBox.Show(e.Error.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        ElseIf e.Cancelled Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Saving Process cancelled...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            DevExpress.XtraEditors.XtraMessageBox.Show("Mail Lists Successfully Saved...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    '===================================================================
    Private Sub InitializeBackgroundWorkers()
        AddHandler bw1.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw1.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw1.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw2.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw2.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw2.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw3.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw3.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw3.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw4.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw4.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw4.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw5.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw5.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw5.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw6.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork6)
        AddHandler bw6.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorker6Completed)
        AddHandler bw7.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw7.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw7.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw8.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw8.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw8.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw9.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw9.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw9.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw10.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw10.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw10.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        AddHandler bw11.DoWork, New DoWorkEventHandler(AddressOf backgroundWorkers_DoWork)
        AddHandler bw11.RunWorkerCompleted, New RunWorkerCompletedEventHandler(AddressOf backgroundWorkers_RunWorkerCompleted)
        AddHandler bw11.ProgressChanged, New ProgressChangedEventHandler(AddressOf backgroundWorkers_ProgressChanged)
        bw1.WorkerSupportsCancellation = True
        bw2.WorkerSupportsCancellation = True
        bw3.WorkerSupportsCancellation = True
        bw4.WorkerSupportsCancellation = True
        bw5.WorkerSupportsCancellation = True
        bw6.WorkerSupportsCancellation = True
        bw7.WorkerSupportsCancellation = True
        bw8.WorkerSupportsCancellation = True
        bw9.WorkerSupportsCancellation = True
        bw10.WorkerSupportsCancellation = True
        bw11.WorkerSupportsCancellation = True
        bw1.WorkerReportsProgress = True
        bw2.WorkerReportsProgress = True
        bw3.WorkerReportsProgress = True
        bw4.WorkerReportsProgress = True
        bw5.WorkerReportsProgress = True
        bw7.WorkerReportsProgress = True
        bw8.WorkerReportsProgress = True
        bw9.WorkerReportsProgress = True
        bw10.WorkerReportsProgress = True
        bw11.WorkerReportsProgress = True
    End Sub
    '===================================================================
#End Region
    Dim CheckAll As Boolean = False
    Dim myValue As Boolean = False
    Dim delitems As List(Of String) = New List(Of String)
    Sub CreateMailTable()
        DTMails = New DataTable
        DTMails.Columns.Clear()
        DTMails.TableName = "TempTable"
        DTMails.Columns.Add("ID", GetType(Int32))
        DTMails.Columns.Add("MailLogo", GetType(Image))
        DTMails.Columns.Add("MailService", GetType(String))
        DTMails.Columns.Add("MailFount", GetType(Int32))
        DTMails.Columns.Add("CanCheck", GetType(Boolean))
        DTMails.Columns.Add("MailAccount", GetType(String))
        '======================================================================
        GridControl1.DataSource = Nothing
        Dim OtherLogo As Image = Nothing
        Dim Counter_ As Integer = 1
        For Each resourceName As String In My.Resources.ResourceManager.GetResourceSet(System.Globalization.CultureInfo.CurrentCulture, True, True).Cast(Of System.Collections.DictionaryEntry).Select(Function(x) x.Key.ToString)
            If resourceName.StartsWith("MS_") Then
                Dim image As Image = DirectCast(My.Resources.ResourceManager.GetObject(resourceName), Image)
                resourceName = resourceName.Replace("MS_", "")
                'Dim fileNameCapitalized As String = CultureInfo.CurrentCulture.TextInfo.ToTitleCase(resourceName)
                If LCase(resourceName) = LCase("Other") Then OtherLogo = image
                Dim row As DataRow = DTMails.NewRow
                row("ID") = Counter_
                row("MailLogo") = image
                row("MailService") = resourceName
                row("MailFount") = 0
                row("CanCheck") = True
                row("MailAccount") = ""
                DTMails.Rows.Add(row)
                Counter_ = Counter_ + 1
            End If
        Next
        DTMails.DefaultView.Sort = "MailService"
        DTMails = DTMails.DefaultView.ToTable()
        For i As Integer = 0 To DTMails.Rows.Count - 1
            If DTMails.Rows(i)("MailService") = "Other" Then
                Dim otherRow As DataRow = DTMails.Rows(i)
                DTMails.Rows.Remove(otherRow)
                DTMails.Rows.Add(DTMails.Rows.Count + 1, OtherLogo, "Other", 0, True)
                Exit For
            End If
        Next
        For i As Integer = 0 To DTMails.Rows.Count - 1
            DTMails.Rows(i).Item("ID") = i + 1
        Next
        GridControl1.DataSource = DTMails
        BntSelectAll.Text = "Unselect All"
        AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
    End Sub
    Private Sub frmEmailSorter_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Opacity = 0
        trmfadein.Enabled = True
        CreateMailTable()
        txtPath.Text = ""
        Data = New DataModel()
        linesLock = New Object()
        timer.Interval = 1000
        timer.SynchronizingObject = Me
        timer.Enabled = True
        bw1 = New BackgroundWorker()
        bw2 = New BackgroundWorker()
        bw3 = New BackgroundWorker()
        bw4 = New BackgroundWorker()
        bw5 = New BackgroundWorker()
        bw6 = New BackgroundWorker()
        bw7 = New BackgroundWorker()
        bw8 = New BackgroundWorker()
        bw9 = New BackgroundWorker()
        bw10 = New BackgroundWorker()
        bw11 = New BackgroundWorker()
        InitializeBackgroundWorkers()
    End Sub
    Private Sub LetterEncoder_bntClearAll_Click(sender As Object, e As EventArgs)
        If GridView1.DataRowCount = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("There are no Mails to Scan...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        delitems.Clear()
        Dim count_ As Integer = 0
        For index_ = 0 To GridView1.DataRowCount - 1
            If CBool(GridView1.GetRowCellValue(index_, "CanCheck")) = True Then
                delitems.Add(GridView1.GetRowCellValue(index_, "id"))
                count_ += 1
            End If
        Next
        If delitems.Count = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please select at least one Mail to Scan...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
    End Sub
    Private Sub BntSelectAll_Click(sender As Object, e As EventArgs)
        If BntSelectAll.Text = "Select All" Then
            BntSelectAll.Text = "Unselect All"
            For i = 0 To GridView1.DataRowCount - 1
                GridView1.SetRowCellValue(i, "CanCheck", True)
                GridView1.UpdateCurrentRow()
                GridView1.PostEditor()
            Next
        ElseIf BntSelectAll.Text = "Unselect All" Then
            BntSelectAll.Text = "Select All"
            For i = 0 To GridView1.DataRowCount - 1
                GridView1.SetRowCellValue(i, "CanCheck", False)
                GridView1.UpdateCurrentRow()
                GridView1.PostEditor()
            Next
        End If
    End Sub
    Private Sub trmfadein_Tick(sender As Object, e As EventArgs) Handles trmfadein.Tick
        Me.Opacity = Me.Opacity + 0.02
        If Me.Opacity = 1 Then
            Me.Opacity = 98 / 100
            trmfadein.Enabled = False
            trmfadein.Stop()
        End If
    End Sub
    Private Sub BntResetAll_Click(sender As Object, e As EventArgs) Handles BntResetAll.Click
        Try
            CreateMailTable()
            ProgressBarControl1.Properties.Step = 1
            ProgressBarControl1.Properties.PercentView = True
            ProgressBarControl1.Properties.Maximum = 100
            ProgressBarControl1.Properties.Minimum = 0
            ProgressBarControl1.Position = 0
            Panel4.Visible = False
            Data.Clear()
            txtPath.Text = ""
            bw1.CancelAsync()
            bw2.CancelAsync()
            bw3.CancelAsync()
            bw4.CancelAsync()
            bw5.CancelAsync()
            bw7.CancelAsync()
            bw8.CancelAsync()
            bw9.CancelAsync()
            bw10.CancelAsync()
            bw11.CancelAsync()
            DevExpress.XtraEditors.XtraMessageBox.Show("Lists Cleared...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
        End Try
    End Sub
    Private Sub GridView1_RowCellStyle(sender As Object, e As RowCellStyleEventArgs)
        Try
            If e.Column.FieldName = "MailFount" Then
                Dim statusValue As String = GridView1.GetRowCellValue(e.RowHandle, "MailFount").ToString()
                If Convert.ToInt32(statusValue) > 0 Then
                    e.Appearance.ForeColor = Color.FromArgb(192, 255, 192)
                Else
                    e.Appearance.ForeColor = Color.FromArgb(255, 255, 192)
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub
    Private Sub Col_Chk_CheckedChanged(sender As Object, e As EventArgs)
        myValue = (If(GridView1.GetRowCellValue(GridView1.FocusedRowHandle, "CanCheck") Is Nothing, False, CBool(GridView1.GetRowCellValue(GridView1.FocusedRowHandle, "CanCheck"))))
        GridView1.SetRowCellValue(GridView1.FocusedRowHandle, "CanCheck", Not myValue)
        GridView1.UpdateCurrentRow()
        GridView1.PostEditor()
    End Sub
    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles BntSelectAll.Click
        If BntSelectAll.Text = "Select All" Then
            BntSelectAll.Text = "Unselect All"
            For i = 0 To GridView1.DataRowCount - 1
                GridView1.SetRowCellValue(i, "CanCheck", True)
                GridView1.UpdateCurrentRow()
                GridView1.PostEditor()
            Next
        ElseIf BntSelectAll.Text = "Unselect All" Then
            BntSelectAll.Text = "Select All"
            For i = 0 To GridView1.DataRowCount - 1
                GridView1.SetRowCellValue(i, "CanCheck", False)
                GridView1.UpdateCurrentRow()
                GridView1.PostEditor()
            Next
        End If
    End Sub
    Private Sub SimpleButton2_Click_1(sender As Object, e As EventArgs) Handles BntStop.Click
        bw1.CancelAsync()
        bw2.CancelAsync()
        bw3.CancelAsync()
        bw4.CancelAsync()
        bw5.CancelAsync()
        bw7.CancelAsync()
        bw8.CancelAsync()
        bw9.CancelAsync()
        bw10.CancelAsync()
        bw11.CancelAsync()
        Panel4.Visible = False
        DevExpress.XtraEditors.XtraMessageBox.Show("Task Cancelled by the user...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    Private Sub BNT_Details_ButtonClick(sender As Object, e As DevExpress.XtraEditors.Controls.ButtonPressedEventArgs)
        If Panel4.Visible = True Then
            DevExpress.XtraEditors.XtraMessageBox.Show("The task is in progress. Please wait for the sorting process to finish...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        Dim SelectedRowHandles As Integer() = GridView1.GetSelectedRows()
        Dim id_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("ID"))
        Dim MailFount_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("MailFount"))
        Dim MailService_ = GridView1.GetRowCellValue(SelectedRowHandles(0), GridView1.Columns("MailService"))
        If MailFount_ = 0 Then
            DevExpress.XtraEditors.XtraMessageBox.Show("No list found for this mail server...", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            Select Case LCase(MailService_)
                Case LCase("Gmail")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Google list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Gmail
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMails)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Yahoo")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Yahoo list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Yahoo
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail1)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Hotmail")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Hotmail list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Hotmail
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail2)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Outlook")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Outlook list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Outlook
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail3) + Environment.NewLine + functions.generateTxt(Data.sortedMail4)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Office 365")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Office365 list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Office_365
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail4)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("GoDaddy")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      GoDaddy list"
                    frmMaillist.picLogo.Image = My.Resources.MS_GoDaddy
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail5)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("163")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      163 list"
                    frmMaillist.picLogo.Image = My.Resources.MS_163
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail6)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("1 and 1")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      1 and 1 list"
                    frmMaillist.picLogo.Image = My.Resources.MS_1_and_1
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail7)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("RackSpace")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      RackSpace list"
                    frmMaillist.picLogo.Image = My.Resources.MS_RackSpace
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail8)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Mail.Ru")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Mail.Ru list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Mail_Ru
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail9)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Other")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Other list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Other
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail10)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("263")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      263 list"
                    frmMaillist.picLogo.Image = My.Resources.MS_263
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail11)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Aliyan")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Aliyan list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Aliyun
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail12)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Anazana")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Anazana list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Anazana
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail13)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("CoreMail")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      CoreMail list"
                    frmMaillist.picLogo.Image = My.Resources.MS_CoreMail
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail14)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("EarthLink")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      EarthLink list"
                    frmMaillist.picLogo.Image = My.Resources.MS_EarthLink
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail15)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("GMX")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      GMX list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Gmx
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail16)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Hinet")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Hinet list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Hinet
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail17)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("iCloud")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      iCloud list"
                    frmMaillist.picLogo.Image = My.Resources.MS_iCloud
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail18)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("iiNet")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      iiNet list"
                    frmMaillist.picLogo.Image = My.Resources.MS_iiNet
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail19)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("MimeCast")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      MimeCast list"
                    frmMaillist.picLogo.Image = My.Resources.MS_mimeCast
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail20)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("NameCheap")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      NameCheap list"
                    frmMaillist.picLogo.Image = My.Resources.MS_nameCheap
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail21)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Orange")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Orange list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Orange
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail22)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("QQ")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      QQ list"
                    frmMaillist.picLogo.Image = My.Resources.MS_QQ
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail23)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Web.de")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Web.de list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Web_de
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail24)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Yandex")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Yandex list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Yandex
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail25)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("ZambriaMail")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      ZambriaMail list"
                    frmMaillist.picLogo.Image = My.Resources.MS_ZambriaMail
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail26)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Zoho")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Zoho list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Zoho
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail27)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("Amazon")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      Amazon list"
                    frmMaillist.picLogo.Image = My.Resources.MS_Amazon
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail28)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
                Case LCase("ovhcloud")
                    timer.Enabled = False
                    frmMaillist.GroupControl2.Text = "      OVH cloud list"
                    frmMaillist.picLogo.Image = My.Resources.MS_ovhcloud
                    frmMaillist.txtList.Text = functions.generateTxt(Data.sortedMail29)
                    frmMaillist.ShowDialog()
                    timer.Enabled = True
            End Select
        End If
    End Sub
    Function GetMailList(ByVal MailServername As String) As String
        Select Case LCase(MailServername)
            Case LCase("Gmail")
                Return functions.generateTxt(Data.sortedMails)
            Case LCase("Yahoo")
                Return functions.generateTxt(Data.sortedMail1)
            Case LCase("Hotmail")
                Return functions.generateTxt(Data.sortedMail2)
            Case LCase("Outlook")
                Return functions.generateTxt(Data.sortedMail3) + Environment.NewLine + functions.generateTxt(Data.sortedMail4)
            Case LCase("Office 365")
                Return functions.generateTxt(Data.sortedMail4)
            Case LCase("GoDaddy")
                Return functions.generateTxt(Data.sortedMail5)
            Case LCase("163")
                Return functions.generateTxt(Data.sortedMail6)
            Case LCase("1 and 1")
                Return functions.generateTxt(Data.sortedMail7)
            Case LCase("RackSpace")
                Return functions.generateTxt(Data.sortedMail8)
            Case LCase("Mail.Ru")
                Return functions.generateTxt(Data.sortedMail9)
            Case LCase("Other")
                Return functions.generateTxt(Data.sortedMail10)
            Case LCase("263")
                Return functions.generateTxt(Data.sortedMail11)
            Case LCase("Aliyan")
                Return functions.generateTxt(Data.sortedMail12)
            Case LCase("Anazana")
                Return functions.generateTxt(Data.sortedMail3)
            Case LCase("CoreMail")
                Return functions.generateTxt(Data.sortedMail14)
            Case LCase("EarthLink")
                Return functions.generateTxt(Data.sortedMail15)
            Case LCase("GMX")
                Return functions.generateTxt(Data.sortedMail16)
            Case LCase("Hinet")
                Return functions.generateTxt(Data.sortedMail17)
            Case LCase("iCloud")
                Return functions.generateTxt(Data.sortedMail18)
            Case LCase("iiNet")
                Return functions.generateTxt(Data.sortedMail19)
            Case LCase("MimeCast")
                Return functions.generateTxt(Data.sortedMail20)
            Case LCase("NameCheap")
                Return functions.generateTxt(Data.sortedMail21)
            Case LCase("Orange")
                Return functions.generateTxt(Data.sortedMail22)
            Case LCase("QQ")
                Return functions.generateTxt(Data.sortedMail23)
            Case LCase("Web.de")
                Return functions.generateTxt(Data.sortedMail24)
            Case LCase("Yandex")
                Return functions.generateTxt(Data.sortedMail25)
            Case LCase("ZambriaMail")
                Return functions.generateTxt(Data.sortedMail26)
            Case LCase("Zoho")
                Return functions.generateTxt(Data.sortedMail27)
            Case LCase("Amazon")
                Return functions.generateTxt(Data.sortedMail28)
            Case Else
                Return ""
        End Select
    End Function
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Dim CellMailList As New DevExpress.XtraEditors.MemoEdit
        Dim gridView As GridView = CType(GridControl1.MainView, GridView)
        Dim FolderCreated As Boolean = False
        Dim emailSorterListPath As String = ""
        For i As Integer = 0 To gridView.RowCount - 1
            Dim rowHandle As Integer = gridView.GetVisibleRowHandle(i)
            Dim cellValue As Object = gridView.GetRowCellValue(rowHandle, "MailFount")
            If CInt(cellValue) > 0 Then
                timer.Enabled = False
                If FolderCreated = False Then
                    ' Get the path to the Desktop directory.
                    Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory)
                    ' Define the path for the "Best Defender" folder.
                    Dim bestDefenderPath As String = Path.Combine(desktopPath, "Best Sender")
                    ' Check if the "Best Defender" folder exists. If not, create it.
                    If Not Directory.Exists(bestDefenderPath) Then
                        Directory.CreateDirectory(bestDefenderPath)
                    End If
                    ' Define the path for the "Email Sorter List" folder.
                    emailSorterListPath = Path.Combine(bestDefenderPath, "Email Sorter List")
                    ' Check if the "Email Sorter List" folder exists. If not, create it.
                    If Not Directory.Exists(emailSorterListPath) Then
                        Directory.CreateDirectory(emailSorterListPath)
                    End If
                    FolderCreated = True
                Else
                    FolderCreated = True
                End If
                'CellMailList.Text = functions.generateTxt(Data.sortedMails)
                CellMailList.Text = GetMailList(gridView.GetRowCellValue(rowHandle, "MailService").ToString())
                Dim filePath As String = Path.Combine(emailSorterListPath, gridView.GetRowCellValue(rowHandle, "MailService").ToString() & ".txt")
                File.WriteAllText(filePath, CellMailList.Text())
            End If
        Next i
        If FolderCreated = True Then
            timer.Enabled = True
            DevExpress.XtraEditors.XtraMessageBox.Show("Mail Lists Successfully Saved...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Process.Start("explorer.exe", emailSorterListPath)
        End If
    End Sub
    Private Sub frmEmailSorter_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        bw1.CancelAsync()
        bw2.CancelAsync()
        bw3.CancelAsync()
        bw4.CancelAsync()
        bw5.CancelAsync()
        bw7.CancelAsync()
        bw8.CancelAsync()
        bw9.CancelAsync()
        bw10.CancelAsync()
        bw11.CancelAsync()
        timer.Enabled = False
    End Sub
End Class