﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmRedirectWithPassword
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmRedirectWithPassword))
        Me.WebBrowser1 = New System.Windows.Forms.WebBrowser()
        Me.WebBrowser2 = New System.Windows.Forms.WebBrowser()
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.RichTextBox2 = New System.Windows.Forms.RichTextBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.cbChange = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.BntCLear = New DevExpress.XtraEditors.SimpleButton()
        Me.BTN_Encrypter = New DevExpress.XtraEditors.SimpleButton()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.TextBoxEmailTagPass = New DevExpress.XtraEditors.TextEdit()
        Me.TextBoxlinkPass = New DevExpress.XtraEditors.TextEdit()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.TextBoxpassword = New DevExpress.XtraEditors.TextEdit()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.textPagePass2 = New DevExpress.XtraEditors.TextEdit()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.textPagePass1 = New DevExpress.XtraEditors.TextEdit()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.TextBoxlogoPass = New DevExpress.XtraEditors.TextEdit()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.TextBoxtitelPass = New DevExpress.XtraEditors.TextEdit()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.TextBoxlogoPassCLear = New DevExpress.XtraEditors.SimpleButton()
        Me.TextBoxtitelPassClear = New DevExpress.XtraEditors.SimpleButton()
        Me.TextBoxlinkPassClear = New DevExpress.XtraEditors.SimpleButton()
        Me.TextBoxpasswordClear = New DevExpress.XtraEditors.SimpleButton()
        Me.textPagePass2Clear = New DevExpress.XtraEditors.SimpleButton()
        Me.textPagePass1CLear = New DevExpress.XtraEditors.SimpleButton()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.XtraTabPage1 = New DevExpress.XtraTab.XtraTabPage()
        Me.ClearTAg = New DevExpress.XtraEditors.SimpleButton()
        Me.lbl_Size_Button_Button = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.txt_width = New DevExpress.XtraEditors.TextEdit()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.txt_height = New DevExpress.XtraEditors.TextEdit()
        Me.Label21 = New System.Windows.Forms.Label()
        Me.Pic_Logo_Button_Button = New System.Windows.Forms.PictureBox()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.XtraTabPage3 = New DevExpress.XtraTab.XtraTabPage()
        CType(Me.cbChange.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxEmailTagPass.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxlinkPass.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxpassword.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.textPagePass2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.textPagePass1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxlogoPass.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxtitelPass.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.XtraTabPage1.SuspendLayout()
        CType(Me.txt_width.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_height.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Pic_Logo_Button_Button, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabPage3.SuspendLayout()
        Me.SuspendLayout()
        '
        'WebBrowser1
        '
        Me.WebBrowser1.Location = New System.Drawing.Point(1030, 69)
        Me.WebBrowser1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.WebBrowser1.MinimumSize = New System.Drawing.Size(20, 22)
        Me.WebBrowser1.Name = "WebBrowser1"
        Me.WebBrowser1.Size = New System.Drawing.Size(267, 22)
        Me.WebBrowser1.TabIndex = 24
        Me.WebBrowser1.Visible = False
        '
        'WebBrowser2
        '
        Me.WebBrowser2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.WebBrowser2.Location = New System.Drawing.Point(0, 0)
        Me.WebBrowser2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.WebBrowser2.MinimumSize = New System.Drawing.Size(20, 22)
        Me.WebBrowser2.Name = "WebBrowser2"
        Me.WebBrowser2.ScriptErrorsSuppressed = True
        Me.WebBrowser2.Size = New System.Drawing.Size(1221, 567)
        Me.WebBrowser2.TabIndex = 25
        '
        'RichTextBox1
        '
        Me.RichTextBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.RichTextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox1.Font = New System.Drawing.Font("Tahoma", 10.0!)
        Me.RichTextBox1.ForeColor = System.Drawing.Color.White
        Me.RichTextBox1.Location = New System.Drawing.Point(1030, 9)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(102, 42)
        Me.RichTextBox1.TabIndex = 26
        Me.RichTextBox1.Text = resources.GetString("RichTextBox1.Text")
        Me.RichTextBox1.Visible = False
        '
        'RichTextBox2
        '
        Me.RichTextBox2.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.RichTextBox2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox2.Font = New System.Drawing.Font("Tahoma", 10.0!)
        Me.RichTextBox2.ForeColor = System.Drawing.Color.White
        Me.RichTextBox2.Location = New System.Drawing.Point(1015, 9)
        Me.RichTextBox2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.RichTextBox2.Name = "RichTextBox2"
        Me.RichTextBox2.Size = New System.Drawing.Size(146, 42)
        Me.RichTextBox2.TabIndex = 26
        Me.RichTextBox2.Text = resources.GetString("RichTextBox2.Text")
        Me.RichTextBox2.Visible = False
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Tahoma", 10.0!)
        Me.Label3.ForeColor = System.Drawing.Color.SandyBrown
        Me.Label3.Location = New System.Drawing.Point(14, 12)
        Me.Label3.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(0, 17)
        Me.Label3.TabIndex = 1
        '
        'cbChange
        '
        Me.cbChange.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cbChange.EditValue = "Attachment"
        Me.cbChange.Location = New System.Drawing.Point(934, 142)
        Me.cbChange.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cbChange.Name = "cbChange"
        Me.cbChange.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cbChange.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cbChange.Properties.Appearance.Options.UseBackColor = True
        Me.cbChange.Properties.Appearance.Options.UseFont = True
        Me.cbChange.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cbChange.Properties.Items.AddRange(New Object() {"Attachment", "Cpanel"})
        Me.cbChange.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cbChange.Size = New System.Drawing.Size(156, 38)
        Me.cbChange.TabIndex = 537
        '
        'BntCLear
        '
        Me.BntCLear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Options.UseBackColor = True
        Me.BntCLear.Appearance.Options.UseBorderColor = True
        Me.BntCLear.Appearance.Options.UseFont = True
        Me.BntCLear.Appearance.Options.UseForeColor = True
        Me.BntCLear.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntCLear.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntCLear.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntCLear.AppearanceDisabled.Options.UseBackColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseFont = True
        Me.BntCLear.AppearanceDisabled.Options.UseForeColor = True
        Me.BntCLear.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntCLear.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntCLear.AppearanceHovered.Options.UseBackColor = True
        Me.BntCLear.AppearanceHovered.Options.UseBorderColor = True
        Me.BntCLear.AppearanceHovered.Options.UseForeColor = True
        Me.BntCLear.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntCLear.AppearancePressed.Options.UseBackColor = True
        Me.BntCLear.AppearancePressed.Options.UseBorderColor = True
        Me.BntCLear.AppearancePressed.Options.UseForeColor = True
        Me.BntCLear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntCLear.Location = New System.Drawing.Point(366, 438)
        Me.BntCLear.Margin = New System.Windows.Forms.Padding(4)
        Me.BntCLear.Name = "BntCLear"
        Me.BntCLear.Size = New System.Drawing.Size(152, 38)
        Me.BntCLear.TabIndex = 535
        Me.BntCLear.Text = "Clear All"
        '
        'BTN_Encrypter
        '
        Me.BTN_Encrypter.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Encrypter.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Encrypter.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_Encrypter.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Encrypter.Appearance.Options.UseBackColor = True
        Me.BTN_Encrypter.Appearance.Options.UseBorderColor = True
        Me.BTN_Encrypter.Appearance.Options.UseFont = True
        Me.BTN_Encrypter.Appearance.Options.UseForeColor = True
        Me.BTN_Encrypter.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BTN_Encrypter.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BTN_Encrypter.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BTN_Encrypter.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BTN_Encrypter.AppearanceDisabled.Options.UseBackColor = True
        Me.BTN_Encrypter.AppearanceDisabled.Options.UseBorderColor = True
        Me.BTN_Encrypter.AppearanceDisabled.Options.UseFont = True
        Me.BTN_Encrypter.AppearanceDisabled.Options.UseForeColor = True
        Me.BTN_Encrypter.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BTN_Encrypter.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Encrypter.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BTN_Encrypter.AppearanceHovered.Options.UseBackColor = True
        Me.BTN_Encrypter.AppearanceHovered.Options.UseBorderColor = True
        Me.BTN_Encrypter.AppearanceHovered.Options.UseForeColor = True
        Me.BTN_Encrypter.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BTN_Encrypter.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BTN_Encrypter.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BTN_Encrypter.AppearancePressed.Options.UseBackColor = True
        Me.BTN_Encrypter.AppearancePressed.Options.UseBorderColor = True
        Me.BTN_Encrypter.AppearancePressed.Options.UseForeColor = True
        Me.BTN_Encrypter.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.BTN_Encrypter.Location = New System.Drawing.Point(193, 438)
        Me.BTN_Encrypter.Margin = New System.Windows.Forms.Padding(4)
        Me.BTN_Encrypter.Name = "BTN_Encrypter"
        Me.BTN_Encrypter.Size = New System.Drawing.Size(152, 38)
        Me.BTN_Encrypter.TabIndex = 534
        Me.BTN_Encrypter.Text = "Generate"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label6.Location = New System.Drawing.Point(930, 118)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(59, 21)
        Me.Label6.TabIndex = 524
        Me.Label6.Text = "Select :"
        '
        'TextBoxEmailTagPass
        '
        Me.TextBoxEmailTagPass.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxEmailTagPass.EditValue = ""
        Me.TextBoxEmailTagPass.Location = New System.Drawing.Point(146, 371)
        Me.TextBoxEmailTagPass.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBoxEmailTagPass.Name = "TextBoxEmailTagPass"
        Me.TextBoxEmailTagPass.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxEmailTagPass.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxEmailTagPass.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxEmailTagPass.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxEmailTagPass.Properties.Appearance.Options.UseFont = True
        Me.TextBoxEmailTagPass.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxEmailTagPass.Properties.NullValuePrompt = "[-Email-] or [-Email64-]"
        Me.TextBoxEmailTagPass.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxEmailTagPass.Size = New System.Drawing.Size(457, 30)
        Me.TextBoxEmailTagPass.TabIndex = 579
        '
        'TextBoxlinkPass
        '
        Me.TextBoxlinkPass.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxlinkPass.EditValue = ""
        Me.TextBoxlinkPass.Location = New System.Drawing.Point(633, 308)
        Me.TextBoxlinkPass.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBoxlinkPass.Name = "TextBoxlinkPass"
        Me.TextBoxlinkPass.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxlinkPass.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxlinkPass.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxlinkPass.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxlinkPass.Properties.Appearance.Options.UseFont = True
        Me.TextBoxlinkPass.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxlinkPass.Properties.NullValuePrompt = "Enter Your Link"
        Me.TextBoxlinkPass.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxlinkPass.Size = New System.Drawing.Size(457, 30)
        Me.TextBoxlinkPass.TabIndex = 578
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label8.Location = New System.Drawing.Point(146, 345)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(35, 21)
        Me.Label8.TabIndex = 572
        Me.Label8.Text = "Tag"
        '
        'TextBoxpassword
        '
        Me.TextBoxpassword.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxpassword.EditValue = ""
        Me.TextBoxpassword.Location = New System.Drawing.Point(633, 247)
        Me.TextBoxpassword.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBoxpassword.Name = "TextBoxpassword"
        Me.TextBoxpassword.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxpassword.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxpassword.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxpassword.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxpassword.Properties.Appearance.Options.UseFont = True
        Me.TextBoxpassword.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxpassword.Properties.NullValuePrompt = "12345"
        Me.TextBoxpassword.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxpassword.Size = New System.Drawing.Size(457, 30)
        Me.TextBoxpassword.TabIndex = 577
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label7.Location = New System.Drawing.Point(633, 282)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(111, 21)
        Me.Label7.TabIndex = 571
        Me.Label7.Text = "Your Link Page"
        '
        'textPagePass2
        '
        Me.textPagePass2.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.textPagePass2.EditValue = ""
        Me.textPagePass2.Location = New System.Drawing.Point(633, 186)
        Me.textPagePass2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.textPagePass2.Name = "textPagePass2"
        Me.textPagePass2.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.textPagePass2.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.textPagePass2.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.textPagePass2.Properties.Appearance.Options.UseBackColor = True
        Me.textPagePass2.Properties.Appearance.Options.UseFont = True
        Me.textPagePass2.Properties.Appearance.Options.UseForeColor = True
        Me.textPagePass2.Properties.NullValuePrompt = "Please enter the password below"
        Me.textPagePass2.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.textPagePass2.Size = New System.Drawing.Size(457, 30)
        Me.textPagePass2.TabIndex = 576
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label5.Location = New System.Drawing.Point(633, 221)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(150, 21)
        Me.Label5.TabIndex = 570
        Me.Label5.Text = "Enter Your Password"
        '
        'textPagePass1
        '
        Me.textPagePass1.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.textPagePass1.EditValue = ""
        Me.textPagePass1.Location = New System.Drawing.Point(146, 308)
        Me.textPagePass1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.textPagePass1.Name = "textPagePass1"
        Me.textPagePass1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.textPagePass1.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.textPagePass1.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.textPagePass1.Properties.Appearance.Options.UseBackColor = True
        Me.textPagePass1.Properties.Appearance.Options.UseFont = True
        Me.textPagePass1.Properties.Appearance.Options.UseForeColor = True
        Me.textPagePass1.Properties.NullValuePrompt = "This area is password protected "
        Me.textPagePass1.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.textPagePass1.Size = New System.Drawing.Size(457, 30)
        Me.textPagePass1.TabIndex = 575
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label4.Location = New System.Drawing.Point(633, 160)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(238, 21)
        Me.Label4.TabIndex = 569
        Me.Label4.Text = "Text appears above the Password"
        '
        'TextBoxlogoPass
        '
        Me.TextBoxlogoPass.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxlogoPass.EditValue = ""
        Me.TextBoxlogoPass.Location = New System.Drawing.Point(146, 247)
        Me.TextBoxlogoPass.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBoxlogoPass.Name = "TextBoxlogoPass"
        Me.TextBoxlogoPass.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxlogoPass.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxlogoPass.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxlogoPass.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxlogoPass.Properties.Appearance.Options.UseFont = True
        Me.TextBoxlogoPass.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxlogoPass.Properties.NullValuePrompt = "Enter Link Logo"
        Me.TextBoxlogoPass.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxlogoPass.Size = New System.Drawing.Size(457, 30)
        Me.TextBoxlogoPass.TabIndex = 574
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label1.Location = New System.Drawing.Point(146, 282)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(73, 21)
        Me.Label1.TabIndex = 568
        Me.Label1.Text = "Page text"
        '
        'TextBoxtitelPass
        '
        Me.TextBoxtitelPass.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBoxtitelPass.EditValue = ""
        Me.TextBoxtitelPass.Location = New System.Drawing.Point(146, 186)
        Me.TextBoxtitelPass.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBoxtitelPass.Name = "TextBoxtitelPass"
        Me.TextBoxtitelPass.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxtitelPass.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxtitelPass.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBoxtitelPass.Properties.Appearance.Options.UseBackColor = True
        Me.TextBoxtitelPass.Properties.Appearance.Options.UseFont = True
        Me.TextBoxtitelPass.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxtitelPass.Properties.NullValuePrompt = "Your Titel"
        Me.TextBoxtitelPass.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBoxtitelPass.Size = New System.Drawing.Size(457, 30)
        Me.TextBoxtitelPass.TabIndex = 573
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(146, 221)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(45, 21)
        Me.Label2.TabIndex = 567
        Me.Label2.Text = "Logo"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label9.Location = New System.Drawing.Point(146, 160)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(75, 21)
        Me.Label9.TabIndex = 566
        Me.Label9.Text = "Your Titel"
        '
        'TextBoxlogoPassCLear
        '
        Me.TextBoxlogoPassCLear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxlogoPassCLear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxlogoPassCLear.Appearance.Options.UseBackColor = True
        Me.TextBoxlogoPassCLear.Appearance.Options.UseBorderColor = True
        Me.TextBoxlogoPassCLear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.TextBoxlogoPassCLear.Location = New System.Drawing.Point(578, 249)
        Me.TextBoxlogoPassCLear.Name = "TextBoxlogoPassCLear"
        Me.TextBoxlogoPassCLear.Size = New System.Drawing.Size(25, 23)
        Me.TextBoxlogoPassCLear.TabIndex = 585
        '
        'TextBoxtitelPassClear
        '
        Me.TextBoxtitelPassClear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxtitelPassClear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxtitelPassClear.Appearance.Options.UseBackColor = True
        Me.TextBoxtitelPassClear.Appearance.Options.UseBorderColor = True
        Me.TextBoxtitelPassClear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.TextBoxtitelPassClear.Location = New System.Drawing.Point(578, 187)
        Me.TextBoxtitelPassClear.Name = "TextBoxtitelPassClear"
        Me.TextBoxtitelPassClear.Size = New System.Drawing.Size(25, 23)
        Me.TextBoxtitelPassClear.TabIndex = 586
        '
        'TextBoxlinkPassClear
        '
        Me.TextBoxlinkPassClear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxlinkPassClear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxlinkPassClear.Appearance.Options.UseBackColor = True
        Me.TextBoxlinkPassClear.Appearance.Options.UseBorderColor = True
        Me.TextBoxlinkPassClear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.TextBoxlinkPassClear.Location = New System.Drawing.Point(1065, 309)
        Me.TextBoxlinkPassClear.Name = "TextBoxlinkPassClear"
        Me.TextBoxlinkPassClear.Size = New System.Drawing.Size(25, 23)
        Me.TextBoxlinkPassClear.TabIndex = 581
        '
        'TextBoxpasswordClear
        '
        Me.TextBoxpasswordClear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxpasswordClear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxpasswordClear.Appearance.Options.UseBackColor = True
        Me.TextBoxpasswordClear.Appearance.Options.UseBorderColor = True
        Me.TextBoxpasswordClear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.TextBoxpasswordClear.Location = New System.Drawing.Point(1065, 247)
        Me.TextBoxpasswordClear.Name = "TextBoxpasswordClear"
        Me.TextBoxpasswordClear.Size = New System.Drawing.Size(25, 23)
        Me.TextBoxpasswordClear.TabIndex = 583
        '
        'textPagePass2Clear
        '
        Me.textPagePass2Clear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.textPagePass2Clear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.textPagePass2Clear.Appearance.Options.UseBackColor = True
        Me.textPagePass2Clear.Appearance.Options.UseBorderColor = True
        Me.textPagePass2Clear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.textPagePass2Clear.Location = New System.Drawing.Point(1065, 189)
        Me.textPagePass2Clear.Name = "textPagePass2Clear"
        Me.textPagePass2Clear.Size = New System.Drawing.Size(25, 23)
        Me.textPagePass2Clear.TabIndex = 582
        '
        'textPagePass1CLear
        '
        Me.textPagePass1CLear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.textPagePass1CLear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.textPagePass1CLear.Appearance.Options.UseBackColor = True
        Me.textPagePass1CLear.Appearance.Options.UseBorderColor = True
        Me.textPagePass1CLear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.textPagePass1CLear.Location = New System.Drawing.Point(578, 311)
        Me.textPagePass1CLear.Name = "textPagePass1CLear"
        Me.textPagePass1CLear.Size = New System.Drawing.Size(25, 23)
        Me.textPagePass1CLear.TabIndex = 584
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.AppearancePage.HeaderActive.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.XtraTabControl1.AppearancePage.HeaderActive.Options.UseBackColor = True
        Me.XtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.XtraTabControl1.Location = New System.Drawing.Point(0, 0)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.XtraTabPage1
        Me.XtraTabControl1.Size = New System.Drawing.Size(1223, 611)
        Me.XtraTabControl1.TabIndex = 591
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.XtraTabPage1, Me.XtraTabPage3})
        '
        'XtraTabPage1
        '
        Me.XtraTabPage1.Appearance.Header.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.XtraTabPage1.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage1.Controls.Add(Me.ClearTAg)
        Me.XtraTabPage1.Controls.Add(Me.lbl_Size_Button_Button)
        Me.XtraTabPage1.Controls.Add(Me.Label10)
        Me.XtraTabPage1.Controls.Add(Me.txt_width)
        Me.XtraTabPage1.Controls.Add(Me.Label20)
        Me.XtraTabPage1.Controls.Add(Me.txt_height)
        Me.XtraTabPage1.Controls.Add(Me.Label21)
        Me.XtraTabPage1.Controls.Add(Me.Pic_Logo_Button_Button)
        Me.XtraTabPage1.Controls.Add(Me.PictureBox1)
        Me.XtraTabPage1.Controls.Add(Me.Label9)
        Me.XtraTabPage1.Controls.Add(Me.Label2)
        Me.XtraTabPage1.Controls.Add(Me.Label1)
        Me.XtraTabPage1.Controls.Add(Me.Label4)
        Me.XtraTabPage1.Controls.Add(Me.cbChange)
        Me.XtraTabPage1.Controls.Add(Me.TextBoxlogoPassCLear)
        Me.XtraTabPage1.Controls.Add(Me.TextBoxtitelPassClear)
        Me.XtraTabPage1.Controls.Add(Me.BntCLear)
        Me.XtraTabPage1.Controls.Add(Me.Label5)
        Me.XtraTabPage1.Controls.Add(Me.BTN_Encrypter)
        Me.XtraTabPage1.Controls.Add(Me.Label6)
        Me.XtraTabPage1.Controls.Add(Me.TextBoxlinkPassClear)
        Me.XtraTabPage1.Controls.Add(Me.TextBoxpasswordClear)
        Me.XtraTabPage1.Controls.Add(Me.Label7)
        Me.XtraTabPage1.Controls.Add(Me.textPagePass2Clear)
        Me.XtraTabPage1.Controls.Add(Me.TextBoxpassword)
        Me.XtraTabPage1.Controls.Add(Me.textPagePass1CLear)
        Me.XtraTabPage1.Controls.Add(Me.Label8)
        Me.XtraTabPage1.Controls.Add(Me.TextBoxlinkPass)
        Me.XtraTabPage1.Controls.Add(Me.TextBoxEmailTagPass)
        Me.XtraTabPage1.Controls.Add(Me.TextBoxtitelPass)
        Me.XtraTabPage1.Controls.Add(Me.TextBoxlogoPass)
        Me.XtraTabPage1.Controls.Add(Me.textPagePass1)
        Me.XtraTabPage1.Controls.Add(Me.textPagePass2)
        Me.XtraTabPage1.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.settings32x32
        Me.XtraTabPage1.Name = "XtraTabPage1"
        Me.XtraTabPage1.Size = New System.Drawing.Size(1221, 567)
        Me.XtraTabPage1.Text = "Settings"
        '
        'ClearTAg
        '
        Me.ClearTAg.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.ClearTAg.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.ClearTAg.Appearance.Options.UseBackColor = True
        Me.ClearTAg.Appearance.Options.UseBorderColor = True
        Me.ClearTAg.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.ClearTAg.Location = New System.Drawing.Point(578, 374)
        Me.ClearTAg.Name = "ClearTAg"
        Me.ClearTAg.Size = New System.Drawing.Size(25, 23)
        Me.ClearTAg.TabIndex = 597
        '
        'lbl_Size_Button_Button
        '
        Me.lbl_Size_Button_Button.AutoSize = True
        Me.lbl_Size_Button_Button.Font = New System.Drawing.Font("Comfortaa", 8.249999!)
        Me.lbl_Size_Button_Button.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.lbl_Size_Button_Button.Location = New System.Drawing.Point(889, 474)
        Me.lbl_Size_Button_Button.Name = "lbl_Size_Button_Button"
        Me.lbl_Size_Button_Button.Size = New System.Drawing.Size(0, 18)
        Me.lbl_Size_Button_Button.TabIndex = 595
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Comfortaa", 8.249999!)
        Me.Label10.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Label10.Location = New System.Drawing.Point(676, 474)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(0, 18)
        Me.Label10.TabIndex = 596
        '
        'txt_width
        '
        Me.txt_width.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_width.EditValue = ""
        Me.txt_width.Location = New System.Drawing.Point(637, 431)
        Me.txt_width.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_width.Name = "txt_width"
        Me.txt_width.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_width.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_width.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_width.Properties.Appearance.Options.UseBackColor = True
        Me.txt_width.Properties.Appearance.Options.UseFont = True
        Me.txt_width.Properties.Appearance.Options.UseForeColor = True
        Me.txt_width.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_width.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_width.Properties.NullValuePrompt = "Width Logo"
        Me.txt_width.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_width.Size = New System.Drawing.Size(167, 30)
        Me.txt_width.TabIndex = 591
        '
        'Label20
        '
        Me.Label20.AutoSize = True
        Me.Label20.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label20.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label20.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label20.Location = New System.Drawing.Point(684, 410)
        Me.Label20.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(88, 21)
        Me.Label20.TabIndex = 593
        Me.Label20.Text = "Width Logo"
        '
        'txt_height
        '
        Me.txt_height.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_height.EditValue = ""
        Me.txt_height.Location = New System.Drawing.Point(637, 377)
        Me.txt_height.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_height.Name = "txt_height"
        Me.txt_height.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_height.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_height.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_height.Properties.Appearance.Options.UseBackColor = True
        Me.txt_height.Properties.Appearance.Options.UseFont = True
        Me.txt_height.Properties.Appearance.Options.UseForeColor = True
        Me.txt_height.Properties.ContextImageOptions.Image = CType(resources.GetObject("txt_height.Properties.ContextImageOptions.Image"), System.Drawing.Image)
        Me.txt_height.Properties.NullValuePrompt = "Height logo"
        Me.txt_height.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_height.Size = New System.Drawing.Size(167, 30)
        Me.txt_height.TabIndex = 592
        '
        'Label21
        '
        Me.Label21.AutoSize = True
        Me.Label21.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label21.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label21.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label21.Location = New System.Drawing.Point(678, 349)
        Me.Label21.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(91, 21)
        Me.Label21.TabIndex = 594
        Me.Label21.Text = "Height logo"
        '
        'Pic_Logo_Button_Button
        '
        Me.Pic_Logo_Button_Button.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Pic_Logo_Button_Button.Location = New System.Drawing.Point(879, 345)
        Me.Pic_Logo_Button_Button.Name = "Pic_Logo_Button_Button"
        Me.Pic_Logo_Button_Button.Size = New System.Drawing.Size(211, 126)
        Me.Pic_Logo_Button_Button.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.Pic_Logo_Button_Button.TabIndex = 588
        Me.Pic_Logo_Button_Button.TabStop = False
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.PasswordPaner
        Me.PictureBox1.Location = New System.Drawing.Point(419, 82)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(379, 75)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 587
        Me.PictureBox1.TabStop = False
        '
        'XtraTabPage3
        '
        Me.XtraTabPage3.Appearance.Header.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.XtraTabPage3.Appearance.Header.Options.UseFont = True
        Me.XtraTabPage3.Controls.Add(Me.WebBrowser2)
        Me.XtraTabPage3.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.html32x32
        Me.XtraTabPage3.Name = "XtraTabPage3"
        Me.XtraTabPage3.Size = New System.Drawing.Size(1221, 567)
        Me.XtraTabPage3.Text = "Preview"
        '
        'frmRedirectWithPassword
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1223, 611)
        Me.Controls.Add(Me.XtraTabControl1)
        Me.Controls.Add(Me.WebBrowser1)
        Me.Controls.Add(Me.RichTextBox2)
        Me.Controls.Add(Me.RichTextBox1)
        Me.Controls.Add(Me.Label3)
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmRedirectWithPassword"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Redirect With Password"
        CType(Me.cbChange.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxEmailTagPass.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxlinkPass.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxpassword.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.textPagePass2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.textPagePass1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxlogoPass.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxtitelPass.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.XtraTabPage1.ResumeLayout(False)
        Me.XtraTabPage1.PerformLayout()
        CType(Me.txt_width.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_height.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Pic_Logo_Button_Button, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabPage3.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents WebBrowser1 As WebBrowser
    Friend WithEvents WebBrowser2 As WebBrowser
    Friend WithEvents RichTextBox2 As RichTextBox
    Friend WithEvents Label3 As Label
    Friend WithEvents cbChange As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents BntCLear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BTN_Encrypter As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label6 As Label
    Friend WithEvents TextBoxEmailTagPass As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextBoxlinkPass As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label8 As Label
    Friend WithEvents TextBoxpassword As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label7 As Label
    Friend WithEvents textPagePass2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label5 As Label
    Friend WithEvents textPagePass1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label4 As Label
    Friend WithEvents TextBoxlogoPass As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label1 As Label
    Friend WithEvents TextBoxtitelPass As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label2 As Label
    Friend WithEvents Label9 As Label
    Friend WithEvents TextBoxlogoPassCLear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextBoxtitelPassClear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextBoxlinkPassClear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextBoxpasswordClear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents textPagePass2Clear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents textPagePass1CLear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents XtraTabPage1 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents XtraTabPage3 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents lbl_Size_Button_Button As Label
    Friend WithEvents Label10 As Label
    Friend WithEvents txt_width As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label20 As Label
    Friend WithEvents txt_height As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label21 As Label
    Friend WithEvents Pic_Logo_Button_Button As PictureBox
    Friend WithEvents ClearTAg As DevExpress.XtraEditors.SimpleButton
End Class
