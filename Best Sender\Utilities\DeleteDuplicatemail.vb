﻿Imports System.ComponentModel
Imports System.IO
Imports System.Linq
Imports System.Text.RegularExpressions
Imports System.Net.Mail
Imports System.Threading.Tasks
Imports System.Diagnostics
Imports DevExpress.XtraEditors
Imports DevExpress.XtraBars.Ribbon ' Added for Stopwatch functionality
Public Class DeleteDuplicatemail
    Public Property RelatedRibbonPage As RibbonPage
    Private isProcessRunning As Boolean = False
    Private totalLines As Integer = 0
    Private stopwatch As Stopwatch = New Stopwatch() ' For timing operations
    Private Sub DeleteDuplicatemail_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        frmMain.StopProcessing.Enabled = False
    End Sub
    ' زر اختيار الملف
    Public Async Sub SelectEmailFile()
        ' Show confirmation message
        frmMain.Clear_AllMail.Visibility = False
        ' Clear all controls
        MemoEdit1.Text = ""
        MemoEdit2.Text = ""
        Label1.Text = ""
        Label2.Text = ""
        Label3.Text = ""
        Label4.Text = ""
        Label5.Text = ""
        Label6.Text = ""
        MemoEdit2.BackColor = Color.FromArgb(26, 26, 29)
        ProgressBarControl1.Position = 0
        ' Proceed with file selection
        Try
            Dim openFileDialog1 As New OpenFileDialog()
            openFileDialog1.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            openFileDialog1.Title = "Select a file"
            openFileDialog1.Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*"
            openFileDialog1.FilterIndex = 1
            If openFileDialog1.ShowDialog() = DialogResult.OK Then
                ' Disable controls before starting
                frmMain.Select_File_text_Email.Enabled = False
                frmMain.StopProcessing.Enabled = True
                Label1.Text = Path.GetFileName(openFileDialog1.FileName)
                ProgressBarControl1.Properties.Minimum = 0
                ProgressBarControl1.Properties.Maximum = 100
                ProgressBarControl1.Position = 0
                MemoEdit1.Text = ""
                frmMain.StopProcessing.Visibility = False
                frmMain.Clear_AllMail.Visibility = True
                frmMain.Start_Delet_Mail.Visibility = True
                ' Start timing the file loading
                stopwatch.Restart()
                ' Read file asynchronously
                Await LoadFileAsync(openFileDialog1.FileName)
                ' Stop timing and display the duration
                stopwatch.Stop()
                Label5.Text = $"File loaded in: {stopwatch.Elapsed.TotalSeconds:F2} seconds"
            End If
        Catch ex As Exception
            XtraMessageBox.Show("Error reading file: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            frmMain.Select_File_text_Email.Enabled = True
        End Try
    End Sub
    Private Async Function LoadFileAsync(filePath As String) As Task
        Try
            Dim fileSize As Long = New FileInfo(filePath).Length
            Dim chunkSize As Integer = 1024 * 1024 ' 1MB
            Dim buffer(chunkSize) As Char
            Dim totalRead As Long = 0
            Using reader As New StreamReader(filePath)
                While Not reader.EndOfStream
                    Dim charsRead As Integer = Await reader.ReadBlockAsync(buffer, 0, chunkSize)
                    totalRead += charsRead
                    ' تحديث الواجهة
                    Dim progress = CInt((totalRead / fileSize) * 100)
                    MemoEdit1.AppendText(New String(buffer, 0, charsRead))
                    ProgressBarControl1.Position = progress
                    Label2.Text = $"{progress}% Loaded | {totalRead / 1024:N0} KB"
                    ' إعطاء فرصة لتحديث الواجهة
                    Await Task.Delay(1)
                End While
            End Using
            Label2.Text = MemoEdit1.Lines.Count.ToString() & " lines loaded"
            frmMain.Start_Delet_Mail.Enabled = True
        Catch ex As Exception
            XtraMessageBox.Show("Error loading file: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Function
    ' زر البدء
    Public Async Sub StartProcessing()
        If String.IsNullOrWhiteSpace(MemoEdit1.Text) Then
            XtraMessageBox.Show("No emails available for processing.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        frmMain.Start_Delet_Mail.Enabled = False
        frmMain.StopProcessing.Enabled = True
        isProcessRunning = True
        ProgressBarControl1.Position = 0
        MemoEdit2.Text = ""
        ' Start timing the processing
        stopwatch.Restart()
        Await ProcessEmailsAsync()
        ' Stop timing and display the duration
        stopwatch.Stop()
        Label6.Text = $"Processing completed in: {stopwatch.Elapsed.TotalSeconds:F2} seconds"
    End Sub
    Private Async Function ProcessEmailsAsync() As Task
        Try
            frmMain.RibbonPageGroup39.Visible = True
            frmMain.RibbonPageGroup40.Visible = True
            Dim lines As String() = MemoEdit1.Lines
            Dim uniqueEmails As New HashSet(Of String)(StringComparer.OrdinalIgnoreCase)
            Dim duplicateCount As Integer = 0
            Dim emailRegex As New Regex("\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b")
            totalLines = lines.Length
            ProgressBarControl1.Properties.Maximum = totalLines
            For i As Integer = 0 To lines.Length - 1
                If Not isProcessRunning Then Exit For
                Dim matches = emailRegex.Matches(lines(i))
                For Each match As Match In matches
                    Dim email = match.Value.Trim()
                    If IsValidEmail(email) Then
                        If Not uniqueEmails.Add(email) Then
                            duplicateCount += 1
                        End If
                    End If
                Next
                ' تحديث التقدم كل 100 سطر
                If i Mod 100 = 0 Then
                    ProgressBarControl1.Position = i
                    Label3.Text = uniqueEmails.Count.ToString()
                    Label4.Text = "Duplicate mail : " & duplicateCount.ToString() & " Mail"
                    Await Task.Delay(1) ' تحديث الواجهة
                End If
            Next
            ' عرض النتائج النهائية
            MemoEdit2.Lines = uniqueEmails.ToArray()
            ProgressBarControl1.Position = totalLines
            Label3.Text = uniqueEmails.Count.ToString()
            Label4.Text = "Duplicate mail : " & duplicateCount.ToString() & " Mail"
            MemoEdit2.BackColor = Color.FromArgb(11, 24, 18)
            MemoEdit2.ForeColor = Color.FromArgb(254, 219, 65)
        Catch ex As Exception
            XtraMessageBox.Show("Error processing emails: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            isProcessRunning = False
            frmMain.Start_Delet_Mail.Enabled = True
            frmMain.StopProcessing.Enabled = False
        End Try
    End Function
    ' زر التوقف
    Public Sub StopProcessing()
        isProcessRunning = False
        frmMain.StopProcessing.Enabled = False
        frmMain.StopProcessing.Caption = "Stopped"
    End Sub
    ' زر المسح
    Public Sub Clear_AllMail()
        Dim result As DialogResult = XtraMessageBox.Show("Do you want to delete all old data?", "Confirmation",
                                              MessageBoxButtons.YesNo, MessageBoxIcon.Question)
        If result = DialogResult.Yes Then
            If Not isProcessRunning Then
                MemoEdit1.Text = ""
                MemoEdit2.Text = ""
                Label1.Text = ""
                Label2.Text = ""
                Label3.Text = ""
                Label4.Text = ""
                Label5.Text = ""
                Label6.Text = ""
                frmMain.StopProcessing.Visibility = False
                MemoEdit2.BackColor = Color.FromArgb(26, 26, 29)
                ProgressBarControl1.Position = 0
            End If
        Else
            XtraMessageBox.Show("Please stop the current process first.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub
    ' التحقق من صحة البريد الإلكتروني
    Private Function IsValidEmail(email As String) As Boolean
        If String.IsNullOrWhiteSpace(email) Then Return False
        If email.Length > 254 Then Return False
        Try
            Dim addr As New MailAddress(email)
            Return addr.Address = email AndAlso
                   email.IndexOf("..", StringComparison.Ordinal) = -1 AndAlso
                   email.IndexOf("@.", StringComparison.Ordinal) = -1 AndAlso
                   Not email.StartsWith(".") AndAlso
                   Not email.EndsWith(".")
        Catch
            Return False
        End Try
    End Function
    ' تصدير إلى ملف نصي
    Public Sub ExportToTextFile()
        If isProcessRunning Then
            XtraMessageBox.Show("Please stop the current process first.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        If String.IsNullOrWhiteSpace(MemoEdit2.Text) Then
            XtraMessageBox.Show("No emails to export.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        Dim saveFileDialog As New SaveFileDialog()
        saveFileDialog.Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*"
        saveFileDialog.FilterIndex = 1
        saveFileDialog.RestoreDirectory = True
        If saveFileDialog.ShowDialog() = DialogResult.OK Then
            Try
                File.WriteAllText(saveFileDialog.FileName, MemoEdit2.Text)
                XtraMessageBox.Show("File saved successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                XtraMessageBox.Show("Error saving file: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
    Private Sub DeleteDuplicatemail_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        frmMain.RibbonPageGroup37.Visible = False
        frmMain.RibbonPageGroup38.Visible = False
        frmMain.RibbonPageGroup39.Visible = False
        frmMain.RibbonPageGroup40.Visible = False
    End Sub
End Class