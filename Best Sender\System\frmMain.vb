﻿Imports DevExpress.XtraEditors
Imports DevExpress.XtraBars.Docking2010
Imports DevExpress.XtraBars.Docking2010.Views.Tabbed
Imports System.ComponentModel
Imports System.Data.SqlClient
Imports System.Drawing
Imports System.Drawing.Imaging
Imports System.IO
Imports System.Management
Imports System.Net
Imports System.Net.NetworkInformation
Imports System.Net.Sockets
Imports System.Text
Imports MicrovisionActivation
Imports Newtonsoft.Json.Linq
Imports System.Diagnostics
Imports DevExpress.Utils.Paint
Imports System.Data.SqlTypes
Imports DevExpress.XtraBars
Imports DevExpress.XtraBars.Ribbon
Imports DevExpress.Office.Drawing
Imports System.Drawing.Drawing2D
Imports Best_Sender.GridStyleApplier
Public Class frmMain
    Private docManager As DocumentManager
    Private notificationIcon As BarStaticItem
    Private isIPHidden As Boolean = False
    Private originalIP As String = ""
    Private userIPAddress As String = ""
    Private userAgreedToShowIP As Boolean = False
    Public Sub New()
        InitializeComponent()
        Me.IsMdiContainer = True
        ' Initialize components container
        If components Is Nothing Then
            components = New System.ComponentModel.Container()
        End If
        ' تفعيل خاصية DoubleBuffered لتحسين أداء رسم الخلفية
        Me.DoubleBuffered = True
        ' تفعيل خصائص إضافية لتحسين أداء الرسم ومنع التقطيع
        SetStyle(ControlStyles.OptimizedDoubleBuffer, True)
        SetStyle(ControlStyles.AllPaintingInWmPaint, True)
        SetStyle(ControlStyles.UserPaint, True)
        SetStyle(ControlStyles.ResizeRedraw, True)
        InitDocumentManager()
        InitializeNotificationShake()
    End Sub
    Private Sub InitDocumentManager()
        If docManager IsNot Nothing Then Return
        docManager = New DocumentManager(components)
        docManager.View = New TabbedView(components)
        docManager.MdiParent = Me
        docManager.View.DocumentProperties.AllowFloat = False
    End Sub

    ''' <summary>
    ''' تهيئة timer الاهتزاز للإشعار
    ''' </summary>
    Private Sub InitializeNotificationShake()
        ' إنشاء timer للاهتزاز
        notificationShakeTimer = New Timer()
        notificationShakeTimer.Interval = 50 ' اهتزاز سريع جداً
        notificationShakeTimer.Enabled = False
        AddHandler notificationShakeTimer.Tick, AddressOf NotificationShakeTimer_Tick

        ' حفظ الصورة الأصلية (لن نستخدمها للاهتزاز)
        originalNotificationImage = My.Resources.alarm

        ' تهيئة خط Comfortaa
        InitializeComfortaaFont()
    End Sub

    ''' <summary>
    ''' تهيئة خط Comfortaa مع معالجة الأخطاء
    ''' </summary>
    Private Sub InitializeComfortaaFont()
        Try
            ' محاولة إنشاء خط Comfortaa
            comfortaaFont = New Font("Comfortaa", 8.249999F, FontStyle.Bold)
        Catch ex As Exception
            ' في حالة عدم توفر الخط، استخدم خط بديل
            Try
                comfortaaFont = New Font("Segoe UI", 8.25F, FontStyle.Bold)
            Catch
                ' خط افتراضي كحل أخير
                comfortaaFont = New Font(FontFamily.GenericSansSerif, 8.25F, FontStyle.Bold)
            End Try
        End Try
    End Sub

    ''' <summary>
    ''' معالج timer الاهتزاز
    ''' </summary>
    Private Sub NotificationShakeTimer_Tick(sender As Object, e As EventArgs)
        If shakeCounter >= maxShakeCount Then
            ' إيقاف الاهتزاز بعد 5 ثوانٍ
            StopNotificationShake()
            Return
        End If

        ' تطبيق تأثير الاهتزاز السريع جداً مع ألوان متنوعة
        ' اختيار لون عشوائي من المصفوفة
        Dim colorIndex As Integer = shakeCounter Mod shakeColors.Length
        notificationIocn.Appearance.ForeColor = shakeColors(colorIndex)
        notificationIocn.Appearance.Font = comfortaaFont

        ' جعل صورة الجرس تهتز وترتعش يمين ويسار
        Dim shakeDirection As Integer = If(shakeCounter Mod 4 = 0, 3, If(shakeCounter Mod 4 = 1, -3, If(shakeCounter Mod 4 = 2, 2, -2)))
        notificationIocn.ImageOptions.Image = CreateShiftedImage(originalNotificationImage, shakeDirection, 0)

        shakeCounter += 1
    End Sub



    ''' <summary>
    ''' بدء تأثير الاهتزاز للإشعار
    ''' </summary>
    Private Sub StartNotificationShake()
        If isShaking Then Return ' تجنب التداخل

        isShaking = True
        shakeCounter = 0

        ' تطبيق تنسيق أولي لافت للانتباه مع أول لون من المصفوفة
        notificationIocn.Appearance.ForeColor = shakeColors(0)
        notificationIocn.Appearance.Font = comfortaaFont

        notificationShakeTimer.Start()
    End Sub

    ''' <summary>
    ''' إيقاف تأثير الاهتزاز للإشعار
    ''' </summary>
    Private Sub StopNotificationShake()
        isShaking = False
        notificationShakeTimer.Stop()
        shakeCounter = 0

        ' إعادة الصورة والتنسيق الأصلي
        If notificationIocn IsNot Nothing AndAlso originalNotificationImage IsNot Nothing Then
            notificationIocn.ImageOptions.Image = originalNotificationImage

            ' إعادة تنسيق النص للحالة الطبيعية
            notificationIocn.Appearance.ForeColor = Color.Blue
            notificationIocn.Appearance.Font = comfortaaFont
        End If
    End Sub

    ''' <summary>
    ''' إنشاء صورة مزاحة لمحاكاة اهتزاز الجرس
    ''' </summary>
    Private Function CreateShiftedImage(originalImage As Image, offsetX As Integer, offsetY As Integer) As Image
        If originalImage Is Nothing Then Return Nothing

        ' إنشاء صورة أكبر لاستيعاب الإزاحة والاهتزاز
        Dim padding As Integer = 8
        Dim shiftedImage As New Bitmap(originalImage.Width + padding * 2, originalImage.Height + padding * 2)
        Using g As Graphics = Graphics.FromImage(shiftedImage)
            g.Clear(Color.Transparent)

            ' تحسين جودة الرسم
            g.SmoothingMode = Drawing2D.SmoothingMode.HighQuality
            g.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic

            ' حساب موضع الرسم مع الإزاحة (اهتزاز الجرس)
            Dim drawX As Integer = padding + offsetX
            Dim drawY As Integer = padding + offsetY

            ' رسم صورة الجرس مع الاهتزاز
            g.DrawImage(originalImage, drawX, drawY, originalImage.Width, originalImage.Height)
        End Using

        Return shiftedImage
    End Function

    ''' <summary>
    ''' تحديث نص الإشعار مع أرقام الإصدارات
    ''' </summary>
    Private Sub UpdateNotificationText()
        Try
            If String.IsNullOrEmpty(currentversion) OrElse String.IsNullOrEmpty(newestversion) Then
                notificationIocn.Caption = "Update Available"
                Return
            End If

            ' تنسيق النص مع أرقام الإصدارات
            Dim currentVer As String = currentversion.Trim()
            Dim newVer As String = newestversion.Trim()

            notificationIocn.Caption = $"Update Available: from {currentVer} to {newVer}"
        Catch ex As Exception
            ' في حالة حدوث خطأ، استخدم النص الافتراضي
            notificationIocn.Caption = "Update Available"
        End Try
    End Sub
    Private Function GetOpenForm(Of T As {Form, New})() As T
        For Each form As Form In My.Application.OpenForms
            If form.GetType() Is GetType(T) Then
                Return CType(form, T)
            End If
        Next
        Return Nothing
    End Function
    '===============================
    'خاصة بتنقل التابات
    Private Sub ActivateFormForRibbonPage(ribbonPage As DevExpress.XtraBars.Ribbon.RibbonPage)
        Dim formToActivate As Form = Nothing
        Select Case ribbonPage.Name
            Case "RibbonPage2"
                formToActivate = GetOpenForm(Of frmEmailSender)()
            Case "RibbonPage7"
                formToActivate = GetOpenForm(Of frmLetterinorSP)()
            Case "RibbonPage3"
                formToActivate = GetOpenForm(Of EmailScanner)()
            Case "RibbonPage17"
                formToActivate = GetOpenForm(Of frmAttPro)()
            Case "RibbonPage6"
                formToActivate = GetOpenForm(Of frmAddHostToSMTP)()
            Case "RibbonPageExtractDomain"
                formToActivate = GetOpenForm(Of frmEMailSorterDomain)()
            Case "RibbonPageCleanMail"
                formToActivate = GetOpenForm(Of DeleteDuplicatemail)()
            Case "RibbonPageZeroPass"
                formToActivate = GetOpenForm(Of frmZeroPass)()
            Case "RibbonPagePHP"
                formToActivate = GetOpenForm(Of frm_En_PHP)()
            Case "RibbonPageCloudFlar"
                formToActivate = GetOpenForm(Of frmRedirectLink)()
            Case "RibbonPage17"
                formToActivate = GetOpenForm(Of frmlinktoAtt)()
        End Select
        If formToActivate IsNot Nothing Then
            formToActivate.Activate()
            formToActivate.BringToFront()
        End If
    End Sub
    ' دالة للبحث عن الفورم لو مفتوح قبل كده
    Private Function FindDocument(formName As String) As DevExpress.XtraBars.Docking2010.Views.BaseDocument
        For Each doc As DevExpress.XtraBars.Docking2010.Views.BaseDocument In docManager.View.Documents
            If doc.Control.Name = formName Then
                Return doc
            End If
        Next
        Return Nothing
    End Function
    ' فتح EmailScanner
    Dim tt As New ToolTip()
    Dim Tick_ As Integer = 0
    Dim PermissionTick As Integer = 0
    Dim isSupportActive As Boolean = False
    Dim isRedirectActive As Boolean = False ' متغير لتتبع حالة زر PicRedirectLink
    Dim TimeDetectFlg As Boolean = False
    Dim AppShutDown As Boolean = False
    Dim PermissionUpdateStatus As Boolean = False
    Dim ChngFlg As String = ""
    Private drag As Boolean
    Private mouseX As Integer
    Private mouseY As Integer
    Private originalLabelText As String = ""
    Private isClicked As Boolean = False

    ' متغيرات تأثير الاهتزاز للإشعار
    Private notificationShakeTimer As Timer
    Private shakeCounter As Integer = 0
    Private maxShakeCount As Integer = 100 ' 5 ثوانٍ (50ms × 100)
    Private originalNotificationImage As Image
    Private isShaking As Boolean = False
    Private comfortaaFont As Font = Nothing
    Private shakeColors() As Color = {
        Color.Red, Color.Orange, Color.Yellow, Color.Lime, Color.Green,
        Color.Cyan, Color.Blue, Color.Purple, Color.Magenta, Color.Pink,
        Color.Gold, Color.Crimson, Color.DarkOrange, Color.SpringGreen,
        Color.DeepSkyBlue, Color.BlueViolet, Color.HotPink, Color.LimeGreen,
        Color.Tomato, Color.DodgerBlue, Color.MediumOrchid, Color.OrangeRed,
        Color.ForestGreen, Color.RoyalBlue, Color.DarkViolet, Color.Coral
    }


#Region "Change Button Image"
    ' تم إزالة تأثير Zoom in
#End Region
    Private Sub TimeChangedHandler(sender As Object, e As System.EventArgs)
        If Background_CheckSystemDate.IsBusy = False Then
            TimeDetectFlg = False
            Background_CheckSystemDate.RunWorkerAsync()
        End If
    End Sub
    Sub calRemainTrailPeriod()
        Dim r1 = DateTime.Parse(Now)
        Dim r2 = DateTime.Parse(ActivationExpireDate)
        Dim result = r2 - r1
        Dim Remain_ = MicrovisionActivation.Sec_Ref.FormatTimeSpan(result)
        Try
            If Remain_ = "" Then
                lblTrail.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
                lblTrail.Caption = "The trial period has expired..."
                Me.Hide()
                'StoreDataSet.Delete()
                If My.Settings.TrainWarning >= 3 Then
                    Dim args As New XtraMessageBoxArgs()
                    args.AutoCloseOptions.Delay = 5000
                    args.AutoCloseOptions.ShowTimerOnDefaultButton = True
                    args.DefaultButtonIndex = 0
                    args.Icon = System.Drawing.SystemIcons.Asterisk
                    args.Caption = "Best Sender"
                    args.Text = " Trial period has expired. Initiate self-destruction in...  "
                    args.Buttons = New DialogResult() {DialogResult.OK}
                    XtraMessageBox.Show(args).ToString()
                    DeleteAllFiles(Application.StartupPath)
                Else
                    Dim r As String = ""
                    If My.Settings.TrainWarning = 1 Then r = "First Warning"
                    If My.Settings.TrainWarning = 2 Then r = "Second Warning"
                    XtraMessageBox.Show("The trial period has expired. The program will now terminate", r, MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    My.Settings.TrainWarning += 1
                    My.Settings.Save()
                End If
                Process.GetCurrentProcess().Kill()
            Else
                lblTrail.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
                lblTrail.Caption = "Your Tail Period Ends in " & Remain_
            End If
        Catch ex As Exception
            Process.GetCurrentProcess().Kill()
        End Try
    End Sub
    Dim Child_frm_En_PHP As frm_En_PHP
    Dim Child_frmZeroPass As frmZeroPass
    Dim Child_frmEMailSorterDomain As frmEMailSorterDomain
    Dim Child_TagBestSender As TagBestSender
    Dim Child_frmLetterinorSP As frmLetterinorSP
    Dim Child_frmSmtpTester As frmSmtpTester
    Dim mySenderForm As frmEmailSender
    Dim Child_frmEmailSender As frmEmailSender
    Dim Child_EmailScanner As EmailScanner
    Dim Child_frmAttPro As frmAttPro
    Dim Child_frmEditLetter As frmAttPro
    Dim Child_frmAddHostToSMTP As frmAddHostToSMTP
    Dim Child_DeleteDuplicatemail As DeleteDuplicatemail
    Dim child_frmRedirectLink As frmRedirectLink
    Dim child_frmQuickAccountCheck As frmQuickAccountCheck
    Dim child_frmImageResize As frmImageResize
    Dim child_frmGoogleCaptcha As frmGoogleCaptcha
    Dim child_frmSupportBSV As frmSupportBSVBSV
    Dim child_frmEncodedLinkWolf As frmEncodedLinkWolf
    Dim child_frmEncodeLink As frmEncodeLink
    Dim child_frmBase64 As frmBase64
    Dim child_frmDakEncryption As frmDakEncryption
    Dim child_frmDragonLetter As frmDragonLetter
    Dim child_frmLetterMakerST As frmLetterMakerST
    Dim child_frmVenomENCLetter As frmVenomENCLetter
    Dim child_MailCatcher As MailCatcher
    Dim child_frmDownloadBSVBSV As frmDownloadBSVBSV
    Dim child_frmEmailSorterPro As frmEmailSorterPro
    Dim child_frmValidationOffice365 As frmValidationOffice365
    Dim child_IMAP_BSV As IMAP_BSV
    Dim child_frmLetterEncoddvb As frmLetterEncoddvb
    Dim child_frmGhost As frmGhost
    Dim child_frmPassMicrosoft As frmPassMicrosoft
    Dim child_frmRedirectPuzzle As frmRedirectPuzzle
    Dim child_frmRedirectWithPassword As frmRedirectWithPassword
    Dim child_frmMultiLinker As frmMultiLinker
    Dim child_frmHideLink As frmHideLink
    Dim child_frmMicrosoftRedirect As frmMicrosoftRedirect
    Dim child_frmRedirectSpider As frmRedirectSpider
    Dim Child_frmlinktoAtt As frmlinktoAtt
    Dim Child_frmActivationInfo As frmActivationInfo





    Function GetPublicIP() As String
        Return New WebClient().DownloadString("https://api.ipify.org/")
    End Function
    Function GetImageFromURL(URL As String) As Image
        Try
            Using webClient As New System.Net.WebClient()
                Dim imageBytes As Byte() = webClient.DownloadData(URL)
                Using ms As New System.IO.MemoryStream(imageBytes)
                    Return System.Drawing.Image.FromStream(ms)
                End Using
            End Using
        Catch ex As Exception
            Return Nothing
        End Try
    End Function
    Private Sub BarButtonItem16_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarButtonItem16.ItemClick
        If FormAccess("18") = True Then
            ' Initialize the form if it's Nothing
            If Child_frmEmailSender Is Nothing Then
                Child_frmEmailSender = New frmEmailSender()
            End If
            OpenForm(Child_frmEmailSender, Me, RibbonPage2)
            BarSubItem4.Enabled = True
            BarSubItem3.Enabled = True
            Bnt_Sender_Start.Enabled = True
            'Bnt_Sender_Stop.Enabled = True
            BarSubItem5.Enabled = True
            Bnt_Tag.Enabled = True
            Bnt_Save_AllSettings.Enabled = True
            Bnt_Reset.Enabled = True
            ' إظهار مجموعات الريبون
            RibbonPageGroup16.Visible = True
            RibbonPageGroup13.Visible = True
            RibbonPageGroup15.Visible = True
            RibbonPageGroup24.Visible = True
            RibbonPageGroup14.Visible = True
            RibbonPageGroup4.Visible = True
            RibbonPageGroup12.Visible = True
            ' تمكين عناصر القوائم الفرعية
            BarSubItem4.Enabled = True
            BarSubItem3.Enabled = True
            BarSubItem5.Enabled = True
            ' تمكين الأزرار
            Bnt_Sender_Start.Enabled = True
            Bnt_Tag.Enabled = True
            Bnt_Save_AllSettings.Enabled = True
            Bnt_Reset.Enabled = True
        End If
    End Sub
    Private Sub LBLACTIVATIONOWNER_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles LBLACTIVATIONOWNER.ItemClick
        If originalLabelText = "" Then
            ' If originalLabelText is empty, store the original text of LBLACTIVATIONOWNER
            originalLabelText = LBLACTIVATIONOWNER.Caption
        End If
        If LBLACTIVATIONOWNER.Caption = "" Then
            ' If the label text is hidden, prompt the user to show the text again
            Dim result As DialogResult = XtraMessageBox.Show("Do you want the name to show?", "Confirmation", MessageBoxButtons.YesNo)
            If result = DialogResult.Yes Then
                ' If the user wants to show the name, set the label text to the original text
                LBLACTIVATIONOWNER.Caption = originalLabelText
            End If
        Else
            ' Prompt the user to hide their name
            Dim result As DialogResult = XtraMessageBox.Show("Do you want to hide your name?", "Confirmation", MessageBoxButtons.YesNo)
            If result = DialogResult.Yes Then
                ' If the user wants to hide their name, hide the text in LBLACTIVATIONOWNER
                LBLACTIVATIONOWNER.Caption = ""
            End If
        End If
    End Sub
    Private Sub BarStaticItem6_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BarStaticItem6.ItemClick
        ' البحث عن الفورم إذا كان مفتوحاً بالفعل
        Dim doc = FindDocument("What are the New Services?")
        If doc IsNot Nothing Then
            ' إذا كان الفورم مفتوحاً، قم بتوجيه المستخدم إليه
            docManager.View.ActivateDocument(doc.Control)
            doc.Control.BringToFront()
        Else
            ' إذا لم يكن الفورم مفتوحاً، قم بإنشاء فورم جديد
            Dim frmNew As New frmNew()
            frmNew.Name = "What are the New Services?"
            frmNew.MdiParent = Me
            frmNew.Show()
            docManager.View.AddDocument(frmNew)
        End If
    End Sub
    Private Sub frmMain_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        lblCureentVersion.Caption = Application.ProductVersion
        '================== السطر دا مهم جدا لازم تضيفو بداية كل برنامج
        System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12
        notificationIocn.Visibility = DevExpress.XtraBars.BarItemVisibility.Never

        ' تطبيق تصميم Binance على النموذج الحالي
        ApplyBinanceStyleToForm(Me, True)

        ' تسجيل حدث لتطبيق التصميم على النماذج الجديدة
        AddHandler Me.MdiChildActivate, AddressOf ApplyBinanceStyleToNewForm


        '==========================================================================================
        BarButtonItem80.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BarButtonItem82.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        RibbonPageGroupCLientNormal.Visible = False
        RibbonPageGroupCliebtVIP.Visible = False
        'Button Sender
        BarSubItem4.Enabled = False
        BarSubItem3.Enabled = False
        Bnt_Sender_Start.Enabled = False
        BntUploadeSMTP.Enabled = False
        BarSubItem5.Enabled = False
        Bnt_Tag.Enabled = False
        Bnt_Save_AllSettings.Enabled = False
        Bnt_Reset.Enabled = False
        '==========================================================================================
        'frm_En_PHP
        BntSelectPHP.Visible = False
        RibbonPageGroup47.Visible = False
        RibbonPageGroup48.Visible = False
        RibbonPageGroup49.Visible = False
        RibbonPageGroup50.Visible = False
        RibbonPageGroup51.Visible = False
        Bnt_Select_PHP.Enabled = False
        BntResetPHP.Enabled = False
        Bnt_php_obfuscation.Enabled = False
        SavePHPFile.Enabled = False
        CopyPHP.Enabled = False
        Bnt_Paste.Enabled = False
        '==========================================================================================
        Me.BackgroundImage = My.Resources.Header
        Me.BackgroundImageLayout = ImageLayout.Zoom
        '============================================
        'frmAddHostSMTP
        BarButtonItem2.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BntStartOlderHost.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BntClear.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Bnt_get_Example.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Bnt_Copy_List.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BntSaveSMTPList.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        RibbonPageGroup11.Visible = False
        RibbonPageGroup19.Visible = False
        RibbonPageGroup20.Visible = False
        RibbonPageGroup21.Visible = False
        RibbonPageGroup22.Visible = False
        RibbonPageGroup23.Visible = False
        '==========================================================================================
        'Remove Email From Password
        BntUploadeList.Enabled = False
        BntStart.Enabled = False
        BntSaveList.Enabled = False
        BntSaveList.Enabled = False
        BarButtonItem67.Enabled = False
        RibbonPageGroup63.Enabled = False
        '==========================================================================================
        'frmRedirectLink
        RibbonPageGroup53.Visible = False
        RibbonPageGroup54.Visible = False
        RibbonPageGroup55.Visible = False
        RibbonPageGroup56.Visible = False
        '==========================================================================================
        'Clean Mail
        RibbonPageGroup37.Visible = False
        RibbonPageGroup38.Visible = False
        RibbonPageGroup39.Visible = False
        RibbonPageGroup40.Visible = False
        '==========================================================================================
        'Sender
        RibbonPageGroup16.Visible = False
        RibbonPageGroup13.Visible = False
        RibbonPageGroup15.Visible = False
        RibbonPageGroup24.Visible = False
        RibbonPageGroup14.Visible = False
        RibbonPageGroup4.Visible = False
        RibbonPageGroup12.Visible = False
        ' تعطيل عناصر القوائم الفرعية
        BarSubItem4.Enabled = False
        BarSubItem3.Enabled = False
        BarSubItem5.Enabled = False
        ' تعطيل الأزرار
        Bnt_Sender_Start.Enabled = False
        Bnt_Tag.Enabled = False
        Bnt_Save_AllSettings.Enabled = False
        Bnt_Reset.Enabled = False
        '==========================================================================================
        'frmLetterinorSP
        RibbonPageGroup26.Visible = False
        RibbonPageGroup27.Visible = False
        RibbonPageGroup28.Visible = False
        RibbonPageGroup29.Visible = False
        RibbonPageGroup30.Visible = False
        RibbonPageGroup31.Visible = False
        '==========================================================================================
        ' إخفاء كل عناصر الريبون المحددة
        BarButtonItem2.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BntClear.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Bnt_get_Example.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Bnt_Copy_List.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BntSaveSMTPList.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BntStartOlderHost.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        '==========================================================================================
        'ExtractDomain
        RibbonPageGroup2.Visible = False
        RibbonPageGroup18.Visible = False
        RibbonPageGroup32.Visible = False
        RibbonPageGroup34.Visible = False
        RibbonPageGroup36.Visible = False
        '==========================================================================================
        '  EmailExtractor Fromm Scanner



        RibbonPageGroup8.Visible = False
        RibbonPageGroup5.Visible = False
        RibbonPageGroup6.Visible = False
        RibbonPageGroup17.Visible = False

        '==========================================================================================

        'مراقبة الفورم
        ' تحديث حالة العناصر لأول مرة عند تحميل النموذج
        UpdateUIElements()
        '==========================================================================================
        LBLACTIVATIONOWNER.Caption = GlobalVariables.ActivationKeyOwner
        If BackGround_CheckActivationStatus.IsBusy Then BackGround_CheckActivationStatus.CancelAsync()
        BackGround_CheckActivationStatus.RunWorkerAsync()
        TimeDetectFlg = False
        Background_CheckSystemDate.RunWorkerAsync()
        AddHandler Microsoft.Win32.SystemEvents.TimeChanged, AddressOf TimeChangedHandler
        lblCureentVersion.Caption = Application.ProductVersion
        lblDateTime.Caption = Now.ToString()
        PictureBox1.Tag = "Nil"
        BackgroundWorker_CheckInternet.WorkerReportsProgress = True
        BackgroundWorker_CheckInternet.WorkerSupportsCancellation = True
        If ActivateionType = "Trail Version" Then
            Timer_CheckRemainTrail.Enabled = True
        Else
            Timer_CheckRemainTrail.Enabled = False
        End If
        Dim xx = ModuleLogo(ActivationModule)
        Select Case xx
            Case "Premium"
                BarButtonItem80.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
                BarButtonItem82.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
                RibbonPageGroupCliebtVIP.Visible = True
                RibbonPageGroupCLientNormal.Visible = False



            Case "Customized"
                BarButtonItem80.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
                BarButtonItem82.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
                RibbonPageGroupCLientNormal.Visible = True
                RibbonPageGroupCliebtVIP.Visible = False

            Case "None"
                BarButtonItem80.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
                BarButtonItem82.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
                RibbonPageGroupCLientNormal.Visible = False
                RibbonPageGroupCliebtVIP.Visible = False

            Case Else
                BarButtonItem80.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
                BarButtonItem82.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        End Select
        'Select Case xx
        '    Case "Premium"
        '        LogoModule.Glyph = My.Resources.Premium
        '        LogoModule.Caption = "VIP Client with a Gold card"
        '    Case "Customized"
        '        LogoModule.Glyph = My.Resources.Customized
        '        LogoModule.Caption = "Client VIP"
        '    Case "None"
        '        LogoModule.Glyph = Nothing
        '        LogoModule.Caption = ""
        '    Case Else
        '        LogoModule.Glyph = Nothing
        'End Select
        Try
            If File.Exists(Application.StartupPath & "\Best Sender_OldVersion.exe") Then File.Delete(Application.StartupPath & "\Best Sender_OldVersion.exe")
        Catch ex As Exception
        End Try
        TrmUpdteChecker.Stop()
        TrmNotificationIcon.Enabled = False
        TrmNotificationIcon.Stop()
        ''Button
        BntEmailScanner_StartScan.Enabled = False
        BntEmailScanner_StopScan.Enabled = False
        BntEmailScanner_SaveList.Enabled = False
        '===========================================
        'مراجعة
        If notificationIcon IsNot Nothing Then
            notificationIcon.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        End If
        '=========================================================
        If BKCheckVersion.IsBusy = False Then
            BKCheckVersion.RunWorkerAsync()
        End If
    End Sub


    Private Sub BackgroundWorker_CheckInternet_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker_CheckInternet.DoWork
        Try
            If CheckForInternetConnection() = False Then
                ' No internet
                INTERNETCONNECTION.Tag = 0
                INTERNETCONNECTION.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
                lblpublicIP.Caption = "----------------"
                lblCity.Caption = "----------------"
                lblCountry.Caption = "----------------"
                If PictureBox1.Glyph IsNot Nothing Then
                    PictureBox1.Glyph.Dispose()
                    PictureBox1.Glyph = Nothing
                End If
                PictureBox1.Tag = ""
            Else
                ' Internet available
                INTERNETCONNECTION.Tag = 1
                INTERNETCONNECTION.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
                INTERNETCONNECTION.ImageOptions.Image = My.Resources.intetnet_Yes
                ' Get IP info
                Dim ur As New Uri("http://ip-api.com/json/")
                Dim IPdata As String = IPInfo.downloadData(ur)
                If Not String.IsNullOrEmpty(IPdata) Then
                    Dim jObject As JObject = JObject.Parse(IPdata)
                    userIPAddress = jObject("query").ToString()
                    flagLocation = jObject("country").ToString()
                    Dim _countryCode As String = jObject("countryCode").ToString()
                    ' DO NOT show IP until user agrees
                    If userAgreedToShowIP Then
                        lblpublicIP.Caption = userIPAddress
                    Else
                        lblpublicIP.Caption = "Click to show IP"
                    End If
                    lblCity.Caption = jObject("city").ToString()
                    lblCountry.Caption = flagLocation
                    If (_countryCode <> "") AndAlso (PictureBox1.Tag?.ToString() <> _countryCode) Then
                        PictureBox1.Tag = _countryCode
                        Dim newImage As Image = GetImageFromURL($"https://flagcdn.com/w320/{_countryCode.ToLowerInvariant()}.png")
                        If newImage IsNot Nothing Then
                            If PictureBox1.Glyph IsNot Nothing Then
                                PictureBox1.Glyph.Dispose()
                            End If
                            PictureBox1.Glyph = newImage
                        End If
                    End If
                End If
            End If
        Catch ex As Exception
            INTERNETCONNECTION.Tag = 0
            INTERNETCONNECTION.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
            lblpublicIP.Caption = "----------------"
            lblCity.Caption = "----------------"
            lblCountry.Caption = "----------------"
            If PictureBox1.Glyph IsNot Nothing Then
                PictureBox1.Glyph.Dispose()
                PictureBox1.Glyph = Nothing
            End If
            PictureBox1.Tag = ""
        End Try
    End Sub
    Private Sub Timer_ChcekInternet_Tick(sender As Object, e As EventArgs) Handles Timer_ChcekInternet.Tick
        'If Tick_ >= 10 Then
        '    Tick_ = 0
        '    Timer_ChcekInternet.Enabled = False
        '    Timer_ChcekInternet.Stop()
        '    If BackgroundWorker_CheckInternet.IsBusy Then BackgroundWorker_CheckInternet.CancelAsync()
        '    BackgroundWorker_CheckInternet.RunWorkerAsync()
        'Else
        '    Tick_ = Tick_ + 1
        'End If
        If BackgroundWorker_CheckInternet.IsBusy = False Then BackgroundWorker_CheckInternet.RunWorkerAsync()
    End Sub
    Private Sub BackgroundWorker_CheckInternet_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker_CheckInternet.RunWorkerCompleted
        INTERNETCONNECTION.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
    End Sub
    Private Sub INTERNETCONNECTION_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs)
        ' تغيير التلميح بناءً على حالة الاتصال
        If INTERNETCONNECTION.Tag = 0 Then
            INTERNETCONNECTION.Hint = "No internet connection"
        ElseIf INTERNETCONNECTION.Tag = 1 Then
            INTERNETCONNECTION.Hint = "Connected to internet"
        End If
    End Sub
    Private Sub Timer_DateTime_Tick(sender As Object, e As EventArgs) Handles Timer_DateTime.Tick
        lblDateTime.Caption = Now.ToString()
    End Sub
    Private Sub Background_CheckSystemDate_DoWork(sender As Object, e As DoWorkEventArgs) Handles Background_CheckSystemDate.DoWork
        TimeDetectFlg = Sec_Ref.DetectedLogTimeChange()
    End Sub
    Private Sub Background_CheckSystemDate_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles Background_CheckSystemDate.RunWorkerCompleted
        If TimeDetectFlg = True Then
            XtraMessageBox.Show("The date of the device has changed. The program will now close.", "alert", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Process.GetCurrentProcess().Kill()
        Else
            TimeDetectFlg = False
        End If
    End Sub
    Private Sub BackGround_CheckActivationStatus_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackGround_CheckActivationStatus.DoWork
        Try
            If ActiveConn.State = 0 Then
                ActiveConn.ConnectionString = ConnectionString_
                ActiveConn.Open()
            End If
            Dim Adp As New SqlClient.SqlDataAdapter
            Dim DT As New DataTable
            Dim SqlCmd As New SqlClient.SqlCommand("Select * from ActivationCodes WHERE   App_Name = N'" & My.Settings.appname & "' and User_Serial = N'" & My.Settings.PROSTRING & "' AND  ActivationCode = N'" & ActivationKeyCode & "'", ActiveConn)
            Adp = New SqlClient.SqlDataAdapter(SqlCmd)
            DT = New DataTable
            Adp.Fill(DT)
            If DT.Rows.Count = 0 Then
                StoreDataSet.UpdateLicenseStatus(False)
            Else
                If DT.Rows(0).Item("LIC_FLG") = False Or DT.Rows(0).Item("Activation_Status") = False Then
                    StoreDataSet.UpdateLicenseStatus(False)
                ElseIf DT.Rows(0).Item("LIC_FLG") = True And DT.Rows(0).Item("Activation_Status") = True Then
                    StoreDataSet.UpdateLicenseStatus(True)
                End If
            End If
        Catch ex As Exception
            If Timer_CheckActivationStatus.Enabled = False Then
                Tick_ = 0
                Timer_CheckActivationStatus.Enabled = True
                Timer_CheckActivationStatus.Start()
            End If
        End Try
    End Sub
    Private Sub BackGround_CheckActivationStatus_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackGround_CheckActivationStatus.RunWorkerCompleted
        If StoreDataSet.StoredaTable.Rows(0)("lic_flg") = False Then
            BackGround_CheckActivationStatus.CancelAsync()
            Timer_CheckActivationStatus.Enabled = False
            Dim args As New XtraMessageBoxArgs()
            args.AutoCloseOptions.Delay = 5000
            args.AutoCloseOptions.ShowTimerOnDefaultButton = True
            args.DefaultButtonIndex = 0
            args.Icon = System.Drawing.SystemIcons.Information
            args.Caption = ApplicationName_
            args.Text = "  Your license has been suspended, the program will now be closed  "
            args.Buttons = New DialogResult() {DialogResult.OK}
            XtraMessageBox.Show(args).ToString()
            Process.GetCurrentProcess().Kill()
        Else
            Tick_ = 0
            Timer_CheckActivationStatus.Enabled = True
        End If
    End Sub
    Private Sub Timer_CheckActivationStatus_Tick(sender As Object, e As EventArgs) Handles Timer_CheckActivationStatus.Tick
        If Tick_ > 10 Then
            If System.Net.NetworkInformation.NetworkInterface.GetIsNetworkAvailable() = False Then
                If StoreDataSet.StoredaTable.Rows(0)("lic_flg") = False Then
                    BackGround_CheckActivationStatus.CancelAsync()
                    Timer_CheckActivationStatus.Enabled = False
                    Dim args As New XtraMessageBoxArgs()
                    args.AutoCloseOptions.Delay = 5000
                    args.AutoCloseOptions.ShowTimerOnDefaultButton = True
                    args.DefaultButtonIndex = 0
                    args.Icon = System.Drawing.SystemIcons.Information
                    args.Caption = ApplicationName_
                    args.Text = " Your license has been suspended, the program will now be closed "
                    args.Buttons = New DialogResult() {DialogResult.OK}
                    XtraMessageBox.Show(args).ToString()
                    Process.GetCurrentProcess().Kill()
                End If
            Else
                Timer_CheckActivationStatus.Enabled = False
                Tick_ = 0
                If BackGround_CheckActivationStatus.IsBusy Then BackGround_CheckActivationStatus.CancelAsync()
                BackGround_CheckActivationStatus.RunWorkerAsync()
            End If
        Else
            Tick_ = Tick_ + 1
        End If
    End Sub
    Private Sub Timer_CheckRemainTrail_Tick(sender As Object, e As EventArgs) Handles Timer_CheckRemainTrail.Tick
        If ActivateionType = "Trail Version" Then
            calRemainTrailPeriod()
        End If
    End Sub
    Private Sub Background_checkPermissionUpdate_DoWork(sender As Object, e As DoWorkEventArgs) Handles Background_checkPermissionUpdate.DoWork
        'Timer_CheckPermissionUpdate.Enabled = False
        'PermissionTick = 0
        If Sec_Ref.CheckInternet() = True Then
            If Sec_Ref.Openserver(My.Settings.appver) = True Then
                PermissionUpdateStatus = Sec_Ref.Get_ActivationModule_UpdateStatus(MachineSerial, ActivationKeyCode, ApplicationName_)
                If PermissionUpdateStatus = False Then
                    ActivationModule = Sec_Ref.Get_ActivationModule(MachineSerial, ActivationKeyCode, ApplicationName_)
                    StoreDataSet.UpdateActivationModule(ActivationModule)
                    ToastNotificationsManager1.ShowNotification("fe4974cf-42ea-4d8c-b050-5e27a8ee6fa5")
                    PermissionUpdateStatus = True
                    Try
                        Dim sqlQuery As String = "UPDATE ActivationCodes SET PermissionUpdated= 'true' where User_Serial=N'" & MachineSerial & "' and ActivationCode=N'" & ActivationKeyCode & "' and App_Name=N'" & ApplicationName_ & "'"
                        Dim command As New SqlCommand(sqlQuery, MicrovisionActivation.Sec_Ref.OnlineServerConn)
                        command.ExecuteNonQuery()
                    Catch ex As Exception
                    End Try
                End If
            End If
        Else
            ActivationModule = Sec_Ref.Get_ActivationModule(MachineSerial, ActivationKeyCode, ApplicationName_)
        End If
        Dim xx = ModuleLogo(ActivationModule)
        Select Case xx
            Case "Premium"
                ' تحديث صورة BarStaticItem لعرض صورة Premium
                'lblpublicIP.Glyph = My.Resources.Premium
                lblpublicIP.Visibility = DevExpress.XtraBars.BarItemVisibility.Always ' عرض العنصر

            Case "Customized"
                ' تحديث صورة BarStaticItem لعرض صورة Customized
                lblpublicIP.Glyph = My.Resources.Customized
                lblpublicIP.Visibility = DevExpress.XtraBars.BarItemVisibility.Always ' عرض العنصر

            Case "None"
                ' إخفاء صورة BarStaticItem (لا توجد صورة)
                lblpublicIP.Glyph = Nothing
                lblpublicIP.Visibility = DevExpress.XtraBars.BarItemVisibility.Never ' إخفاء العنصر

            Case Else
                ' إخفاء صورة BarStaticItem (لا توجد صورة)
                lblpublicIP.Glyph = Nothing
                lblpublicIP.Visibility = DevExpress.XtraBars.BarItemVisibility.Never ' إخفاء العنصر
        End Select
    End Sub
    Private Sub Timer_CheckPermissionUpdate_Tick(sender As Object, e As EventArgs) Handles Timer_CheckPermissionUpdate.Tick
    End Sub
    ' متغير لتخزين الصورة المعاد تحجيمها
    Private cachedBackground As Bitmap = Nothing
    Private lastWidth As Integer = 0
    Private lastHeight As Integer = 0

    ' طريقة مخصصة لرسم خلفية الفورم بجودة عالية
    Protected Overrides Sub OnPaintBackground(e As PaintEventArgs)
        If Me.BackgroundImage IsNot Nothing Then
            ' تحقق مما إذا كان حجم الفورم قد تغير أو لم يتم إنشاء الصورة المخزنة بعد
            If cachedBackground Is Nothing OrElse Me.Width <> lastWidth OrElse Me.Height <> lastHeight Then
                ' تحديث متغيرات الحجم
                lastWidth = Me.Width
                lastHeight = Me.Height

                ' تنظيف الصورة المخزنة القديمة إذا كانت موجودة
                If cachedBackground IsNot Nothing Then
                    cachedBackground.Dispose()
                End If

                ' إنشاء صورة جديدة بحجم الفورم
                cachedBackground = New Bitmap(Me.Width, Me.Height)

                ' إنشاء Graphics للرسم على الصورة المخزنة
                Using g As Graphics = Graphics.FromImage(cachedBackground)
                    ' إعدادات جودة الرسم العالية
                    g.SmoothingMode = SmoothingMode.HighQuality
                    g.InterpolationMode = InterpolationMode.HighQualityBicubic
                    g.CompositingQuality = CompositingQuality.HighQuality
                    g.PixelOffsetMode = PixelOffsetMode.HighQuality

                    ' حساب حجم ومكان الصورة بناءً على نوع التخطيط
                    Dim imgWidth As Integer = Me.BackgroundImage.Width
                    Dim imgHeight As Integer = Me.BackgroundImage.Height
                    Dim destRect As Rectangle

                    ' تطبيق تخطيط Zoom للصورة
                    Dim ratioX As Single = CSng(Me.Width) / imgWidth
                    Dim ratioY As Single = CSng(Me.Height) / imgHeight
                    Dim ratio As Single = Math.Min(ratioX, ratioY)

                    Dim newWidth As Integer = CInt(imgWidth * ratio)
                    Dim newHeight As Integer = CInt(imgHeight * ratio)

                    destRect = New Rectangle(
                        CInt((Me.Width - newWidth) / 2),
                        CInt((Me.Height - newHeight) / 2),
                        newWidth, newHeight)

                    ' رسم الصورة على الصورة المخزنة
                    g.DrawImage(Me.BackgroundImage, destRect, 0, 0, imgWidth, imgHeight, GraphicsUnit.Pixel)
                End Using
            End If

            ' رسم الصورة المخزنة على الفورم (سريع جدًا لأنه لا يحتاج إلى إعادة حساب)
            e.Graphics.DrawImage(cachedBackground, 0, 0)
        Else
            ' إذا لم تكن هناك صورة خلفية، استخدم السلوك الافتراضي
            MyBase.OnPaintBackground(e)
        End If
    End Sub

    ' تم دمج هذه الدالة مع دالة Dispose الأصلية للفورم

    Private Sub Timer2_Tick(sender As Object, e As EventArgs) Handles Timer2.Tick
        If Background_checkPermissionUpdate.IsBusy = False Then
            Background_checkPermissionUpdate.RunWorkerAsync()
            Dim xx = ModuleLogo(ActivationModule)
            Select Case xx
                Case "Premium"
                    ' تحديث صورة BarStaticItem لعرض صورة Premium
                    lblpublicIP.Glyph = My.Resources.Premium
                    lblpublicIP.Visibility = DevExpress.XtraBars.BarItemVisibility.Always ' إظهار العنصر
                Case "Customized"
                    ' تحديث صورة BarStaticItem لعرض صورة Customized
                    lblpublicIP.Glyph = My.Resources.Customized
                    lblpublicIP.Visibility = DevExpress.XtraBars.BarItemVisibility.Always ' إظهار العنصر
                Case "None"
                    ' إخفاء صورة BarStaticItem (لا توجد صورة)
                    lblpublicIP.Glyph = Nothing
                    lblpublicIP.Visibility = DevExpress.XtraBars.BarItemVisibility.Never ' إخفاء العنصر
                Case Else
                    ' إخفاء صورة BarStaticItem (لا توجد صورة)
                    lblpublicIP.Glyph = Nothing
                    lblpublicIP.Visibility = DevExpress.XtraBars.BarItemVisibility.Never ' إخفاء العنصر
            End Select
        End If
    End Sub
    Private Sub TrmUpdteChecker_Tick(sender As Object, e As EventArgs) Handles TrmUpdteChecker.Tick
        If BKCheckVersion.IsBusy = False Then
            BKCheckVersion.RunWorkerAsync()
        End If
    End Sub
    Private Sub BKCheckVersion_DoWork(sender As Object, e As DoWorkEventArgs) Handles BKCheckVersion.DoWork
        TrmUpdteChecker.Enabled = False
        TrmUpdteChecker.Stop()
        If CheckForInternetConnectionn() Then
            Try
                '''' دا رابط التكست
                '''' لما تنسخ الرابط من الدرابوكس شيل الصفر واستبدلو ب 1
                Dim request As System.Net.HttpWebRequest = System.Net.HttpWebRequest.Create("https://www.dropbox.com/scl/fi/3n6d3rqobopxo36pq2zpk/VersionUpDate.txt?rlkey=9hngoxq1wk142zf4k8ycfbx93&dl=1")
                Dim response As System.Net.HttpWebResponse = request.GetResponse()
                Dim sr As System.IO.StreamReader = New System.IO.StreamReader(response.GetResponseStream())
                newestversion = sr.ReadToEnd()
                currentversion = Application.ProductVersion
                nv = Replace(newestversion, ".", "")
                cv = Replace(currentversion, ".", "")
                nv = Convert.ToDecimal(nv)
                cv = Convert.ToDecimal(cv)
            Catch ex As Exception
                TrmUpdteChecker.Enabled = True
                TrmUpdteChecker.Start()
            End Try
        Else
            TrmUpdteChecker.Enabled = True
            TrmUpdteChecker.Start()
        End If
    End Sub
    Private Sub BKCheckVersion_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BKCheckVersion.RunWorkerCompleted
        Try
            If Convert.ToDecimal(nv) > Convert.ToDecimal(cv) Then
                ' تحديث نص الإشعار مع أرقام الإصدارات
                UpdateNotificationText()

                ' بدء تأثير الاهتزاز
                StartNotificationShake()

                ' تفعيل timer الإظهار/الإخفاء الأصلي
                TrmNotificationIcon.Enabled = True
                TrmNotificationIcon.Start()
            End If
        Catch ex As Exception
        End Try
        TrmUpdteChecker.Enabled = True
        TrmUpdteChecker.Start()
    End Sub
    Private Sub TrmNotificationIcon_Tick(sender As Object, e As EventArgs) Handles TrmNotificationIcon.Tick
        If notificationIocn.Visibility = DevExpress.XtraBars.BarItemVisibility.Always Then
            notificationIocn.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Else
            notificationIocn.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
            notificationIocn.Visibility = Not notificationIocn.Visibility
        End If
    End Sub
    Dim isServicesActive As Boolean = False
    Private Sub UserInfo_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles UserInfo.ItemClick

        OpenForm(Child_frmActivationInfo, Me)
    End Sub
    Private Sub notificationIocn_ItemClick(sender As Object, e As ItemClickEventArgs) Handles notificationIocn.ItemClick
        ' إيقاف الاهتزاز عند النقر
        StopNotificationShake()
        OpenForm(child_frmDownloadBSVBSV, Me)
    End Sub
    Private Sub notificationIocn_ItemDoubleClick(sender As Object, e As ItemClickEventArgs) Handles notificationIocn.ItemDoubleClick
        ' إيقاف الاهتزاز عند النقر المزدوج
        StopNotificationShake()
        OpenForm(child_frmDownloadBSVBSV, Me)
    End Sub
    Private Sub BntEmailScanner_StartScan_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntEmailScanner_StartScan.ItemClick
        If Child_EmailScanner Is Nothing Then
            Child_EmailScanner = New EmailScanner()
        End If
        Child_EmailScanner.StartScan()
    End Sub
    Private Sub BntEmailScanner_StopScan_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntEmailScanner_StopScan.ItemClick
        Dim result As DialogResult = XtraMessageBox.Show("Are you sure you want to stop the scanning process now?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        'BntEmailScanner_OpenForm.Enabled = False
        BntEmailScanner_StartScan.Enabled = True
        BntEmailScanner_StopScan.Enabled = False
        Child_EmailScanner.BackgroundWorker2.WorkerSupportsCancellation = True
        Child_EmailScanner.BackgroundWorker2.CancelAsync()
        Child_EmailScanner.Timer1.Enabled = False
    End Sub
    Private Sub BntEmailScanner_SaveList_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntEmailScanner_SaveList.ItemClick
        Child_EmailScanner.SaveList()
    End Sub
    Private Sub FrmMain_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        ' تنظيف الموارد قبل إغلاق التطبيق
        If cachedBackground IsNot Nothing Then
            cachedBackground.Dispose()
            cachedBackground = Nothing
        End If

        ' تنظيف موارد الاهتزاز
        If notificationShakeTimer IsNot Nothing Then
            notificationShakeTimer.Stop()
            notificationShakeTimer.Dispose()
            notificationShakeTimer = Nothing
        End If

        ' تنظيف الخط
        If comfortaaFont IsNot Nothing Then
            comfortaaFont.Dispose()
            comfortaaFont = Nothing
        End If



        Process.GetCurrentProcess.Kill()
    End Sub

    ' تحسين أداء الرسم أثناء تغيير حجم النافذة
    Private isResizing As Boolean = False

    Protected Overrides Sub OnResizeBegin(e As EventArgs)
        isResizing = True
        MyBase.OnResizeBegin(e)
    End Sub

    Protected Overrides Sub OnResizeEnd(e As EventArgs)
        isResizing = False
        ' إعادة رسم الخلفية بجودة كاملة بعد انتهاء تغيير الحجم
        If cachedBackground IsNot Nothing Then
            cachedBackground.Dispose()
            cachedBackground = Nothing
        End If
        Me.Invalidate()
        MyBase.OnResizeEnd(e)
    End Sub

    Protected Overrides Sub OnPaint(e As PaintEventArgs)
        ' تقليل جودة الرسم أثناء تغيير الحجم لتحسين الأداء
        If isResizing Then
            e.Graphics.InterpolationMode = InterpolationMode.Low
            e.Graphics.SmoothingMode = SmoothingMode.HighSpeed
            e.Graphics.CompositingQuality = CompositingQuality.HighSpeed
            e.Graphics.PixelOffsetMode = PixelOffsetMode.HighSpeed
        Else
            e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic
            e.Graphics.SmoothingMode = SmoothingMode.HighQuality
            e.Graphics.CompositingQuality = CompositingQuality.HighQuality
            e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality
        End If
        MyBase.OnPaint(e)
    End Sub
    Private Sub BarButtonItem17_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem17.ItemClick
        If FormAccess("10") = True Then
            OpenForm(Child_frmAttPro, Me, RibbonPage17)
        End If
    End Sub
    Private Sub BntEmailScanner_OpenForm_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntEmailScanner_OpenForm.ItemClick
        If FormAccess("11") = True Then
            OpenForm(Child_EmailScanner, Me, RibbonPage3)
            BntUploadeSMTP.Enabled = True
            RibbonPageGroup8.Visible = True
            RibbonPageGroup5.Visible = True
            RibbonPageGroup6.Visible = True
            RibbonPageGroup17.Visible = True
            BntEmailScanner_StartScan.Enabled = True
        End If
    End Sub
    Private Sub BarButtonItem18_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem18.ItemClick
        If ChngFlg = "" Then
            Dim result As DialogResult = XtraMessageBox.Show("Do you want to Restart the application?", "Confirm Restart", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
            If result = DialogResult.Yes Then
                Dim args As New XtraMessageBoxArgs()
                args.AutoCloseOptions.Delay = 2000
                args.AutoCloseOptions.ShowTimerOnDefaultButton = True
                args.DefaultButtonIndex = 0
                args.Icon = System.Drawing.SystemIcons.Information
                args.Caption = "Restarting"
                args.Text = "Your Best Sender VIP Successfully. Restarting in..."
                args.Buttons = New DialogResult() {DialogResult.OK}
                XtraMessageBox.Show(args).ToString()
                System.Diagnostics.Process.Start(Application.ExecutablePath)
                Process.GetCurrentProcess().Kill()
            End If
        End If
    End Sub
    Private Sub BntYouTube_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntYouTube.ItemClick
        Process.Start("https://www.youtube.com/@BestSenderVIP2024")
    End Sub
    Private Sub BntSignal_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntSignal.ItemClick
        Process.Start("https://signal.me/#eu/N_S6zRFQZxUy7qNMGhjUkDHeWTP5swskArw9cYHyjET-gmX07CtCMq9Al3RPTpEJ")
    End Sub
    Private Sub BntDiscord_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntDiscord.ItemClick
        Process.Start("https://discord.com/users/1247383168408092794")
    End Sub
    Private Sub BntTlg_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntTlg.ItemClick
        Process.Start("https://t.me/BestSenderVIP")
    End Sub
    Private Sub TLGCh_ItemClick(sender As Object, e As ItemClickEventArgs) Handles TLGCh.ItemClick
        Process.Start("https://t.me/bsvtrusted")
    End Sub
    Private Sub Bnt_Sender_Stop_ItemClick(sender As Object, e As ItemClickEventArgs)
        '' تأكد هل الفورم موجود ومفتوح ولا لا
        'If Application.OpenForms().OfType(Of frmEmailSender).Any() Then
        '    Dim SenderForm = Application.OpenForms().OfType(Of frmEmailSender).First()
        '    ' نادى الميثود اللى توقف الإرسال
        '    SenderForm.Stop_Sender()
        'Else
        '    XtraMessageBox.Show("Click Open Sender", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        'End If
    End Sub
    Private Sub Bnt_Clear_Letter_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Clear_Letter.ItemClick
        ' اتأكد ان الفورم مفتوح
        If Application.OpenForms().OfType(Of frmEmailSender).Any() Then
            Dim SenderForm = Application.OpenForms().OfType(Of frmEmailSender).First()
            ' استدعاء Method لمسح الرسالة
            SenderForm.Clear_Letter()
        Else
            XtraMessageBox.Show("Click Open Sender !", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub Bnt_Reset_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Reset.ItemClick
        ' تأكد ان الفورم شغال
        If Application.OpenForms().OfType(Of frmEmailSender).Any() Then
            Dim SenderForm = Application.OpenForms().OfType(Of frmEmailSender).First()
            ' نفذ Method التجهيز كـ New
            SenderForm.DoNew()
        Else
            XtraMessageBox.Show("Click Open Sender", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub Bnt_Save_AllSettings_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Save_AllSettings.ItemClick
        ' اتأكد الفورم شغال
        If Application.OpenForms().OfType(Of frmEmailSender).Any() Then
            Dim SenderForm = Application.OpenForms().OfType(Of frmEmailSender).First()
            ' نادى Method الحفظ
            SenderForm.Save_All_Settings()
        Else
            XtraMessageBox.Show("Click Open Sender", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub Bnt_Inbox_Spam_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Inbox_Spam.ItemClick
        If FormAccess("40") = True Then
            OpenForm(Child_frmLetterinorSP, Me)
        End If
    End Sub
    Private Sub Bnt_Add_Acc_SMTP_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Add_Acc_SMTP.ItemClick
        If Application.OpenForms().OfType(Of frmEmailSender).Any() Then
            Dim SenderForm = Application.OpenForms().OfType(Of frmEmailSender).First()
            SenderForm.Add_SMTP_Account()
        Else
            XtraMessageBox.Show("Click Open Sender", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub Bnt_Clear_List_SMTP_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Clear_List_SMTP.ItemClick
        If Application.OpenForms().OfType(Of frmEmailSender).Any() Then
            Dim SenderForm = Application.OpenForms().OfType(Of frmEmailSender).First()
            ' نادى Method مسح حسابات SMTP
            SenderForm.Clear_SMTP_List()
        Else
            XtraMessageBox.Show("Click Open Sender", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub bnt_ClearMailList_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bnt_ClearMailList.ItemClick
        If Application.OpenForms().OfType(Of frmEmailSender).Any() Then
            Dim SenderForm = Application.OpenForms().OfType(Of frmEmailSender).First()
            ' استدعاء Method المسح
            SenderForm.Clear_Mail_List()
        Else
            XtraMessageBox.Show("Click Open Sender", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub BarButtonItem26_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem26.ItemClick
        If Application.OpenForms().OfType(Of frmEmailSender).Any() Then
            Dim SenderForm = Application.OpenForms().OfType(Of frmEmailSender).First()
            ' نادى Method اضافة الايميلات
            SenderForm.Add_Mail_List()
        Else
            XtraMessageBox.Show("Click Open Sender", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub bntAddletter_ItemClick(sender As Object, e As ItemClickEventArgs) Handles bntAddletter.ItemClick
        If Application.OpenForms().OfType(Of frmEmailSender).Any() Then
            Dim SenderForm = Application.OpenForms().OfType(Of frmEmailSender).First()
            SenderForm.Add_Letter()
        Else
            XtraMessageBox.Show("Click Open Sender", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub btnEdit_ItemClick(sender As Object, e As ItemClickEventArgs) Handles btnEdit.ItemClick
        Dim SenderForm = Application.OpenForms().OfType(Of frmEmailSender).FirstOrDefault()
        If SenderForm IsNot Nothing Then
            If SenderForm.txtLetter.Text = "" Then
                XtraMessageBox.Show("Please Select Letter HTML", "Missing", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Dim result As DialogResult = XtraMessageBox.Show("You Like Add Letter HTML?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
                If result = DialogResult.Yes Then
                    SenderForm.Add_Letter()
                    OpenEditLetter()
                End If
            Else
                ' إذا txtLetter فيها محتوى، فقط افتح نموذج Edit Letter
                OpenEditLetter()
            End If
        Else
            XtraMessageBox.Show("Sender form is not open.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub OpenEditLetter()
        Dim doc = FindDocument("Edit Letter")
        If doc IsNot Nothing Then
            docManager.View.ActivateDocument(doc.Control)
        Else
            Dim frmEditLetter As New frmEditLetter()
            frmEditLetter.Name = "Edit Letter"
            frmEditLetter.MdiParent = Me
            frmEditLetter.Show()
            docManager.View.AddDocument(frmEditLetter)
            Dim senderForm = Application.OpenForms().OfType(Of frmEmailSender).FirstOrDefault()
            If senderForm IsNot Nothing Then
                If Not String.IsNullOrWhiteSpace(senderForm.txtLetter.Text) Then
                    frmEditLetter.RichTextBox2.Text = senderForm.txtLetter.Text
                End If
            End If
        End If
    End Sub
    Private Sub Bnt_Tag_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Tag.ItemClick
        Dim doc = FindDocument("Tag Best Sender")
        If doc IsNot Nothing Then
            docManager.View.ActivateDocument(doc.Control)
        Else
            Dim TagBestSender As New TagBestSender()
            TagBestSender.Name = "Tag Best Sender"
            TagBestSender.MdiParent = Me
            TagBestSender.Show()
            docManager.View.AddDocument(TagBestSender)
        End If
    End Sub
    Private Sub Bnt_Paste_Letter_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Paste_Letter.ItemClick
        ' اتأكد ان الفورم مفتوح
        If Application.OpenForms().OfType(Of frmEmailSender).Any() Then
            Dim SenderForm = Application.OpenForms().OfType(Of frmEmailSender).First()
            ' نادى Method Paste Letter
            SenderForm.Paste_Letter()
        Else
            XtraMessageBox.Show("Click Open Sender", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End If
    End Sub
    Private Sub BntUploadeSMTP_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntUploadeSMTP.ItemClick
        Child_EmailScanner.UploadeSMTPListTO()
    End Sub
    ' مراقبة الفورم
    Private Sub UpdateUIElements()
        Dim emailSenderFormIsOpen As Boolean = False
        ' التحقق مما إذا كان frmEmailSender مفتوحًا
        For Each f As Form In Application.OpenForms
            If TypeOf f Is frmEmailSender Then
                emailSenderFormIsOpen = True
                Exit For
            End If
        Next
        ' تعطيل أو تمكين العناصر بناءً على حالة النموذج
        'BntUploadeSMTP.Enabled = emailSenderFormIsOpen
        BarSubItem4.Enabled = emailSenderFormIsOpen
        BarSubItem3.Enabled = emailSenderFormIsOpen
        Bnt_Sender_Start.Enabled = emailSenderFormIsOpen
        'Bnt_Sender_Stop.Enabled = emailSenderFormIsOpen
        BarSubItem5.Enabled = emailSenderFormIsOpen
        Bnt_Tag.Enabled = emailSenderFormIsOpen
        Bnt_Save_AllSettings.Enabled = emailSenderFormIsOpen
        Bnt_Reset.Enabled = emailSenderFormIsOpen
        'BntUploadeSMTP.Enabled = emailSenderFormIsOpen
    End Sub
    Private Sub Timer4_Tick(sender As Object, e As EventArgs) Handles Timer4.Tick
        UpdateUIElements()
    End Sub
    Private Sub BarButtonItem2_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem2.ItemClick
        Child_frmAddHostToSMTP.Add_Mail_List()
    End Sub
    Private Sub Bnt_Sender_Start_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Sender_Start.ItemClick
        ' Ensure the form is created before calling Start_Sender
        If Child_frmEmailSender Is Nothing Then
            Child_frmEmailSender = New frmEmailSender()
        End If
        Child_frmEmailSender.Start_Sender()
    End Sub
    Private Sub BntStartOlderHost_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntStartOlderHost.ItemClick
        Child_frmAddHostToSMTP.Start_Older()
    End Sub
    Private Sub BntClear_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntClear.ItemClick
        Child_frmAddHostToSMTP.Clear_Mail_List()
    End Sub
    Private Sub Bnt_get_Example_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_get_Example.ItemClick
        Child_frmAddHostToSMTP.Get_example()
    End Sub
    Private Sub Bnt_Copy_List_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Copy_List.ItemClick
        Child_frmAddHostToSMTP.Copy_Mail_Results()
    End Sub
    Private Sub BntSaveSMTPList_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntSaveSMTPList.ItemClick
        Child_frmAddHostToSMTP.Save_Mail_List()
    End Sub
    Private Sub BarButtonItem54_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem54.ItemClick
        OpenForm(Child_frmSmtpTester, Me)
        BarButtonItem2.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BntClear.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Bnt_get_Example.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Bnt_Copy_List.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BntSaveSMTPList.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BntStartOlderHost.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        RibbonPageGroup11.Visible = False
        RibbonPageGroup19.Visible = False
        RibbonPageGroup20.Visible = False
        RibbonPageGroup21.Visible = False
        RibbonPageGroup22.Visible = False
        RibbonPageGroup23.Visible = False
    End Sub
    Private Sub BarButtonItem51_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem51.ItemClick
        OpenForm(Child_frmAddHostToSMTP, Me, RibbonPage6)
        ' إظهار عناصر الريبون المطلوبة
        BarButtonItem2.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        BntClear.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        Bnt_get_Example.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        Bnt_Copy_List.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        BntSaveSMTPList.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        BntStartOlderHost.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        RibbonPageGroup11.Visible = True
        RibbonPageGroup19.Visible = True
        RibbonPageGroup20.Visible = True
        RibbonPageGroup21.Visible = True
        RibbonPageGroup22.Visible = True
        RibbonPageGroup23.Visible = True
        'End If
    End Sub
    Private Sub BntSMTP_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntSMTP.ItemClick
        OpenForm(Child_frmSmtpTester, Me)
        BntClear.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Bnt_get_Example.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        Bnt_Copy_List.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BntSaveSMTPList.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        BntStartOlderHost.Visibility = DevExpress.XtraBars.BarItemVisibility.Never
        RibbonPageGroup11.Visible = False
        RibbonPageGroup19.Visible = False
        RibbonPageGroup20.Visible = False
        RibbonPageGroup21.Visible = False
        RibbonPageGroup22.Visible = False
        RibbonPageGroup23.Visible = False
    End Sub
    Private Sub BarButtonItem55_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem55.ItemClick
        If FormAccess("40") = True Then
            OpenForm(Child_frmLetterinorSP, Me, RibbonPage7)
            RibbonPageGroup26.Visible = True
            RibbonPageGroup27.Visible = True
            RibbonPageGroup28.Visible = True
            RibbonPageGroup29.Visible = True
            RibbonPageGroup30.Visible = True
            RibbonPageGroup31.Visible = True
        End If
    End Sub
    Private Sub BarButtonItem56_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem56.ItemClick
        Child_frmLetterinorSP.Uploade_Letter()
    End Sub
    Private Sub BarButtonItem57_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem57.ItemClick
        Child_frmLetterinorSP.Start_Check()
    End Sub
    Private Sub BarButtonItem58_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem58.ItemClick
        Child_frmLetterinorSP.Transfer_To_Sender()
    End Sub
    Private Sub BarButtonItem59_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem59.ItemClick
        OpenForm(Child_TagBestSender, Me)
    End Sub
    Private Sub BarButtonItem60_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem60.ItemClick
        Child_frmLetterinorSP.Reset_Aall()
    End Sub
    Private Sub BarButtonItem61_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem61.ItemClick
        Child_frmLetterinorSP.Convert_Text()
    End Sub
    Private Sub BarButtonItem62_ItemClick(sender As Object, e As ItemClickEventArgs) Handles btnSelectFile.ItemClick
        Child_frmEMailSorterDomain.Select_txt()
    End Sub
    Private Sub BarButtonItem63_ItemClick(sender As Object, e As ItemClickEventArgs) Handles btnStart.ItemClick
        Child_frmEMailSorterDomain.Start_Extract()
    End Sub
    Private Sub BarButtonItem64_ItemClick(sender As Object, e As ItemClickEventArgs) Handles btnStop.ItemClick
        Child_frmEMailSorterDomain.Stop_Extract()
    End Sub
    Private Sub BarButtonItem66_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntSave.ItemClick
        Child_frmEMailSorterDomain.Save_File_Email()
    End Sub
    Private Sub BarButtonItem64_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem64.ItemClick
        If FormAccess("7") = True Then
            OpenForm(Child_frmEMailSorterDomain, Me, RibbonPageExtractDomain)
            RibbonPageGroup2.Visible = True
            RibbonPageGroup18.Visible = True
            RibbonPageGroup32.Visible = True
            RibbonPageGroup34.Visible = True
            RibbonPageGroup36.Visible = True
            btnStop.Enabled = False
            BntSave.Enabled = False
            ResetAll.Enabled = False
            btnStart.Enabled = False
        End If
    End Sub
    Private Sub ResetAll_ItemClick(sender As Object, e As ItemClickEventArgs) Handles ResetAll.ItemClick
        Child_frmEMailSorterDomain.Clear_All_Data()
    End Sub
    Private Sub Select_File_text_Email_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Select_File_text_Email.ItemClick
        Child_DeleteDuplicatemail.SelectEmailFile()
    End Sub
    Private Sub Start_Delet_Mail_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Start_Delet_Mail.ItemClick
        Child_DeleteDuplicatemail.StartProcessing()
    End Sub
    Private Sub BntDeleteDuplicatemail_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntDeleteDuplicatemail.ItemClick
        If FormAccess("5") = True Then
            OpenForm(Child_DeleteDuplicatemail, Me, RibbonPageCleanMail)
            RibbonPageGroup35.Visible = True
            RibbonPageGroup37.Visible = True
            RibbonPageGroup38.Visible = True
            BntDeleteDuplicatemail.Enabled = True
            Select_File_text_Email.Enabled = True
            Start_Delet_Mail.Enabled = True
            Start_Delet_Mail.Enabled = False
        End If
    End Sub
    Private Sub StopProcessing_ItemClick(sender As Object, e As ItemClickEventArgs) Handles StopProcessing.ItemClick
        Child_DeleteDuplicatemail.StopProcessing()
    End Sub
    Private Sub Clear_AllMail_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Clear_AllMail.ItemClick
        Child_DeleteDuplicatemail.Clear_AllMail()
    End Sub
    Private Sub SaveExport_ItemClick(sender As Object, e As ItemClickEventArgs)
        Child_DeleteDuplicatemail.ExportToTextFile()
    End Sub
    Private Sub BarButtonItem62_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem62.ItemClick
        If FormAccess("6") = True Then
            ' Initialize the form if it's Nothing
            If Child_frmZeroPass Is Nothing Then
                Child_frmZeroPass = New frmZeroPass()
            End If
            OpenForm(Child_frmZeroPass, Me, RibbonPageZeroPass)
            BntStart.Enabled = False
            BntSaveList.Enabled = False
            BarButtonItem67.Enabled = False
            BntUploadeList.Enabled = True
            RibbonPageGroup42.Visible = True
            RibbonPageGroup43.Visible = True
            RibbonPageGroup44.Visible = True
            RibbonPageGroup45.Visible = True
            RibbonPageGroup63.Visible = True
        End If
    End Sub
    Private Sub BarButtonItem67_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem67.ItemClick
        If Child_frmZeroPass Is Nothing Then
            Child_frmZeroPass = New frmZeroPass()
        End If
        Child_frmZeroPass.bntClearList()
    End Sub


    Private Sub SavePHPFile_ItemClick(sender As Object, e As ItemClickEventArgs) Handles SavePHPFile.ItemClick
        If Child_frm_En_PHP Is Nothing Then
            Child_frm_En_PHP = New frm_En_PHP()
        End If
        Child_frm_En_PHP.SaveToDesktop()
    End Sub
    Private Sub BntResetPHP_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntResetPHP.ItemClick
        If Child_frm_En_PHP Is Nothing Then
            Child_frm_En_PHP = New frm_En_PHP()
        End If
        Child_frm_En_PHP.Reset_All()
    End Sub
    Private Sub Bnt_Select_PHP_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Select_PHP.ItemClick
        If Child_frm_En_PHP Is Nothing Then
            Child_frm_En_PHP = New frm_En_PHP()
        End If
        Child_frm_En_PHP.Select_File_PHP()
    End Sub
    Private Sub Bnt_php_obfuscation_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_php_obfuscation.ItemClick
        If Child_frm_En_PHP Is Nothing Then
            Child_frm_En_PHP = New frm_En_PHP()
        End If
        Child_frm_En_PHP.Create_php_obfuscation()
    End Sub
    Private Sub CopyPHP_ItemClick(sender As Object, e As ItemClickEventArgs) Handles CopyPHP.ItemClick
        If Child_frm_En_PHP Is Nothing Then
            Child_frm_En_PHP = New frm_En_PHP()
        End If
        Child_frm_En_PHP.Cut_Code_PHP()
    End Sub
    Private Sub Bnt_Paste_ItemClick(sender As Object, e As ItemClickEventArgs) Handles Bnt_Paste.ItemClick
        If Child_frm_En_PHP Is Nothing Then
            Child_frm_En_PHP = New frm_En_PHP()
        End If
        Child_frm_En_PHP.Paste_Code_PHP()
    End Sub
    Private Sub BarButtonItem68_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem68.ItemClick
        If FormAccess("2") = True Then
            ' Initialize the form if it's Nothing
            If Child_frm_En_PHP Is Nothing Then
                Child_frm_En_PHP = New frm_En_PHP()
            End If
            OpenForm(Child_frm_En_PHP, Me, RibbonPagePHP)
            BntSelectPHP.Visible = True
            Bnt_Paste.Enabled = True
            Bnt_Select_PHP.Enabled = True
            BntResetPHP.Enabled = True
            Bnt_php_obfuscation.Enabled = True
            SavePHPFile.Enabled = True
            SavePHPFile.Enabled = False
            CopyPHP.Enabled = False
            RibbonPageGroup51.Visible = True
        End If
    End Sub
    Private Sub BarButtonItem33_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntCloudFlar.ItemClick
        RibbonPageCloudFlar.Visible = True
        RibbonPageGroup53.Visible = False
        RibbonPageGroup54.Visible = False
        RibbonPageGroup55.Visible = False
        RibbonPageGroup56.Visible = False
        If Ribbon IsNot Nothing Then
            Ribbon.SelectedPage = RibbonPageCloudFlar
        End If
    End Sub
    Private Sub BarButtonItem69_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem69.ItemClick
        If FormAccess("24") = True Then
            OpenForm(child_frmRedirectLink, Me, RibbonPageCloudFlar)
            RibbonPageGroup53.Visible = True
            RibbonPageGroup54.Visible = True
            RibbonPageGroup55.Visible = True
            RibbonPageGroup56.Visible = True
        End If
    End Sub
    Private Sub BntGetLogo_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntGetLogo.ItemClick
        child_frmRedirectLink.Get_Logo()
    End Sub
    Private Sub BntStartRedirect_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntStartRedirect.ItemClick
        child_frmRedirectLink.Start_Build()
    End Sub
    Private Sub BarButtonItem70_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem70.ItemClick
        child_frmRedirectLink.Save_Sittings()
    End Sub
    Private Sub BarButtonItem71_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem71.ItemClick
        child_frmRedirectLink.Reset_All()
    End Sub
    Private Sub BarButtonItem72_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem72.ItemClick
        OpenForm(child_frmQuickAccountCheck, Me)
    End Sub
    Private Sub BarButtonItem52_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem52.ItemClick
        OpenForm(Child_frmAddHostToSMTP, Me)
        ' إظهار عناصر الريبون المطلوبة
        BarButtonItem2.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        BntClear.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        Bnt_get_Example.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        Bnt_Copy_List.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        BntSaveSMTPList.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        BntStartOlderHost.Visibility = DevExpress.XtraBars.BarItemVisibility.Always
        RibbonPageGroup11.Visible = True
        RibbonPageGroup19.Visible = True
        RibbonPageGroup20.Visible = True
        RibbonPageGroup21.Visible = True
        RibbonPageGroup22.Visible = True
        RibbonPageGroup23.Visible = True
        'End If
    End Sub
    Private Sub BarButtonItem73_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem73.ItemClick
        OpenForm(child_frmImageResize, Me)
    End Sub
    Private Sub BarButtonItem34_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntGoogleCaptsha.ItemClick
        If FormAccess("23") = True Then
            OpenForm(child_frmGoogleCaptcha, Me)
        End If
    End Sub
    Private Sub BarStaticItem1_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarStaticItem1.ItemClick
        OpenForm(child_frmSupportBSV, Me)
    End Sub
    Private Sub BarButtonItem28_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem28.ItemClick
        If FormAccess("35") = True Then
            OpenForm(child_frmEncodedLinkWolf, Me)
        End If
    End Sub
    Private Sub BarButtonItem31_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem31.ItemClick
        If FormAccess("3") = True Then
            OpenForm(child_frmEncodeLink, Me)
        End If
    End Sub
    Private Sub BarButtonItem74_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem74.ItemClick
        If FormAccess("33") = True Then
            OpenForm(child_frmDakEncryption, Me)
        End If
    End Sub
    Private Sub BarButtonItem24_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem24.ItemClick
        If FormAccess("34") = True Then
            OpenForm(child_frmBase64, Me)
        End If
    End Sub
    Private Sub BarButtonItem75_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem75.ItemClick
        If FormAccess("32") = True Then
            OpenForm(child_frmDragonLetter, Me)
        End If
    End Sub
    Private Sub BarButtonItem27_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem27.ItemClick
        If FormAccess("31") = True Then
            OpenForm(child_frmLetterMakerST, Me)
        End If
    End Sub
    Private Sub BarButtonItem21_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem21.ItemClick
        If FormAccess("30") = True Then
            OpenForm(child_frmVenomENCLetter, Me)
        End If
    End Sub
    Private Sub BarButtonItem76_ItemClick(sender As Object, e As ItemClickEventArgs)
        Dim x As Integer = Me.Height
        Dim y As Integer = Me.Width
        MsgBox("Height=" & x & vbNewLine & "Width=" & y)
    End Sub
    Private Sub BarButtonItem81_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem81.ItemClick
        If FormAccess("17") = True Then
            OpenForm(child_MailCatcher, Me)
        End If
    End Sub
    Private Sub BarButtonItem43_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem43.ItemClick
        'If FormAccess("17") = True Then
        '    OpenForm(child_frmEmailSorterPro, Me)
        'End If
        OpenForm(child_frmEmailSorterPro, Me)
    End Sub


    Private Sub RibbonControl_SelectedPageChanged(sender As Object, e As EventArgs) Handles RibbonControl.SelectedPageChanged
        Dim selectedPage As RibbonPage = RibbonControl.SelectedPage
        ActivateFormForRibbonPage(selectedPage)
    End Sub
    Private Sub frmMain_MdiChildActivate(sender As Object, e As EventArgs) Handles Me.MdiChildActivate
        Dim activeForm = Me.ActiveMdiChild
        If activeForm IsNot Nothing AndAlso TypeOf activeForm Is Form Then
            Dim relatedPageProp = activeForm.GetType().GetProperty("RelatedRibbonPage")
            If relatedPageProp IsNot Nothing Then
                Dim relatedPage = TryCast(relatedPageProp.GetValue(activeForm), RibbonPage)
                If relatedPage IsNot Nothing Then
                    RibbonControl.SelectedPage = relatedPage
                End If
            End If
        End If
    End Sub
    Private Sub BarButtonItem47_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem47.ItemClick
        If FormAccess("41") = True Then
            OpenForm(child_frmValidationOffice365, Me)
        End If
    End Sub
    Private Sub BntIMAP_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntIMAP.ItemClick
        If FormAccess("42") = True Then
            OpenForm(child_IMAP_BSV, Me)
        End If
    End Sub
    Private Sub BarButtonItem78_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem78.ItemClick
        Dim result As DialogResult = XtraMessageBox.Show("Do you want to download Anydesk from the official website?", "Download Anydesk", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
        If result = DialogResult.Yes Then
            Process.Start("https://anydesk.com/en")
        End If
    End Sub
    Private Sub BarButtonItem79_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem79.ItemClick
        Dim result As DialogResult = XtraMessageBox.Show("Do you want to download Anydesk from the official website?", "Download Anydesk", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
        If result = DialogResult.Yes Then
            Process.Start("https://rustdesk.com/")
        End If
    End Sub
    Private Sub BarButtonItem49_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem49.ItemClick
        Dim result As DialogResult = XtraMessageBox.Show("Do you want to download Anydesk from the official website?", "Download Anydesk", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
        If result = DialogResult.Yes Then
            Process.Start("https://mega.nz/file/I6BEnbLR#TCiKUtnP8ELqBrmM5zmpQ37uKyR7Y1hhYWnWsGF3Uq4")
        End If
    End Sub
    Private Sub BarButtonItem76_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem76.ItemClick
        If FormAccess("19") = True Then
            OpenForm(child_frmLetterEncoddvb, Me)
        End If
    End Sub
    Private Sub BarButtonItem82_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem82.ItemClick
        Process.Start("https://www.bestsendervip.com/product/vip-gold-card/")
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على جميع النماذج عند النقر على الزر
    ''' </summary>
    Public Sub ApplyBinanceStyle_Click(sender As Object, e As EventArgs) Handles BarButtonItem1.ItemClick
        ApplyBinanceStyleToAllOpenForms()
    End Sub
    Friend WithEvents BarStaticItemPublicIP As DevExpress.XtraBars.BarStaticItem
    Private Sub lblpublicIP_ItemClick(sender As Object, e As ItemClickEventArgs) Handles lblpublicIP.ItemClick
        If Not userAgreedToShowIP Then
            Dim result As DialogResult = XtraMessageBox.Show("Do you want to show your Public IP?", "Privacy Confirmation", MessageBoxButtons.YesNo)
            If result = DialogResult.Yes Then
                userAgreedToShowIP = True
                originalLabelText = userIPAddress
                lblpublicIP.Caption = userIPAddress
            Else
                lblpublicIP.Caption = "Click to show IP"
            End If
        Else
            Dim result As DialogResult = XtraMessageBox.Show("Do you want to hide your Public IP?", "Confirmation", MessageBoxButtons.YesNo)
            If result = DialogResult.Yes Then
                userAgreedToShowIP = False
                lblpublicIP.Caption = "Click to show IP"
            End If
        End If
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على GridControl
    ''' </summary>
    ''' <param name="gridControl">GridControl المراد تطبيق التصميم عليه</param>
    ''' <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
    Private Sub ApplyBinanceStyleToGrid(gridControl As DevExpress.XtraGrid.GridControl, Optional useWXITheme As Boolean = True)
        If gridControl Is Nothing Then Return

        ' تطبيق تصميم Binance مباشرة
        ApplyBinanceStyleDirectly(gridControl, useWXITheme)
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance مباشرة على GridControl
    ''' </summary>
    ''' <param name="gridControl">GridControl المراد تطبيق التصميم عليه</param>
    ''' <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
    Private Sub ApplyBinanceStyleDirectly(gridControl As DevExpress.XtraGrid.GridControl, Optional useWXITheme As Boolean = True)
        If gridControl Is Nothing Then Return

        ' تعيين إعدادات GridControl
        If useWXITheme Then
            ' استخدام ثيم WXI مع تعديلات Binance
            gridControl.LookAndFeel.UseDefaultLookAndFeel = True
            gridControl.LookAndFeel.SkinName = "WXI"
        Else
            ' تجاوز الثيم وتطبيق ألوان Binance مباشرة
            gridControl.LookAndFeel.UseDefaultLookAndFeel = False
            gridControl.BackColor = Color.FromArgb(18, 22, 28) ' لون خلفية Binance
            gridControl.ForeColor = Color.White ' لون النص
        End If

        ' تطبيق التصميم على جميع GridViews
        For Each view As DevExpress.XtraGrid.Views.Grid.GridView In gridControl.Views
            ApplyBinanceStyleToGridView(view)
        Next
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على GridView
    ''' </summary>
    ''' <param name="gridView">GridView المراد تطبيق التصميم عليه</param>
    Private Sub ApplyBinanceStyleToGridView(gridView As DevExpress.XtraGrid.Views.Grid.GridView)
        If gridView Is Nothing Then Return

        With gridView
            ' تعيين لون خلفية الجدول
            .Appearance.Empty.BackColor = Color.FromArgb(18, 22, 28) ' لون خلفية Binance
            .Appearance.Empty.Options.UseBackColor = True

            ' تعيين مظهر الصفوف
            .Appearance.Row.BackColor = Color.FromArgb(18, 22, 28) ' لون خلفية Binance
            .Appearance.Row.ForeColor = Color.White ' لون النص
            .Appearance.Row.Options.UseBackColor = True
            .Appearance.Row.Options.UseForeColor = True

            ' تعيين مظهر الصفوف المتناوبة
            .OptionsView.EnableAppearanceEvenRow = True
            .Appearance.EvenRow.BackColor = Color.FromArgb(30, 35, 41) ' لون الصفوف المتناوبة
            .Appearance.EvenRow.Options.UseBackColor = True

            ' تعيين مظهر رأس الأعمدة
            .Appearance.HeaderPanel.BackColor = Color.FromArgb(18, 22, 28) ' لون خلفية Binance
            .Appearance.HeaderPanel.ForeColor = Color.FromArgb(160, 174, 192) ' لون نص العنوان
            .Appearance.HeaderPanel.Font = New Font(.Appearance.HeaderPanel.Font, FontStyle.Bold)
            .Appearance.HeaderPanel.Options.UseBackColor = True
            .Appearance.HeaderPanel.Options.UseForeColor = True
            .Appearance.HeaderPanel.Options.UseFont = True

            ' تعيين مظهر الصف المحدد
            .Appearance.FocusedRow.BackColor = Color.FromArgb(47, 52, 59) ' لون الصف المحدد
            .Appearance.FocusedRow.Options.UseBackColor = True

            ' تعيين مظهر الصف عند المرور عليه
            .Appearance.HideSelectionRow.BackColor = Color.FromArgb(44, 47, 54) ' لون الصف عند المرور عليه
            .Appearance.HideSelectionRow.Options.UseBackColor = True

            ' إزالة خطوط الشبكة
            .OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.False
            .OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.False

            ' تقليل ارتفاع الصفوف لعرض أكثر كثافة
            .RowHeight = 24

            ' تعيين الخط
            .Appearance.Row.Font = New Font("Segoe UI", 10)
            .Appearance.Row.Options.UseFont = True
        End With
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على جميع عناصر GridControl في النموذج
    ''' </summary>
    ''' <param name="form">النموذج المراد تطبيق التصميم على عناصر GridControl فيه</param>
    ''' <param name="useWXITheme">استخدام ثيم WXI (True) أو تجاوزه (False)</param>
    Private Sub ApplyBinanceStyleToForm(form As Form, Optional useWXITheme As Boolean = True)
        If form Is Nothing Then Return

        ' البحث عن جميع عناصر GridControl في النموذج وتطبيق التصميم عليها
        For Each control As Control In form.Controls
            If TypeOf control Is DevExpress.XtraGrid.GridControl Then
                ApplyBinanceStyleToGrid(DirectCast(control, DevExpress.XtraGrid.GridControl), useWXITheme)
            End If

            ' البحث في العناصر المتداخلة
            FindAndApplyStyleToGrids(control, useWXITheme)
        Next
    End Sub

    ''' <summary>
    ''' البحث عن عناصر GridControl في العناصر المتداخلة وتطبيق التصميم عليها
    ''' </summary>
    Private Sub FindAndApplyStyleToGrids(parentControl As Control, useWXITheme As Boolean)
        For Each control As Control In parentControl.Controls
            If TypeOf control Is DevExpress.XtraGrid.GridControl Then
                ApplyBinanceStyleToGrid(DirectCast(control, DevExpress.XtraGrid.GridControl), useWXITheme)
            End If

            ' البحث في العناصر المتداخلة بشكل متكرر
            If control.Controls.Count > 0 Then
                FindAndApplyStyleToGrids(control, useWXITheme)
            End If
        Next
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على النموذج الجديد عند تنشيطه
    ''' </summary>
    Private Sub ApplyBinanceStyleToNewForm(sender As Object, e As EventArgs)
        If Me.ActiveMdiChild IsNot Nothing Then
            ' تطبيق تصميم Binance على النموذج النشط
            ApplyBinanceStyleToForm(Me.ActiveMdiChild, True)
        End If
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على جميع النماذج المفتوحة
    ''' </summary>
    Public Sub ApplyBinanceStyleToAllOpenForms()
        ' تطبيق التصميم على النموذج الرئيسي
        ApplyBinanceStyleToForm(Me, True)

        ' تطبيق التصميم على جميع النماذج الفرعية
        For Each form As Form In Application.OpenForms
            If form IsNot Me Then
                ApplyBinanceStyleToForm(form, True)
            End If
        Next

        ' عرض رسالة تأكيد
        'XtraMessageBox.Show("تم تطبيق تصميم Binance على جميع النماذج بنجاح", "تم التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub BarButtonItem25_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem25.ItemClick
        If FormAccess("29") = True Then
            OpenForm(child_frmGhost, Me)
        End If

    End Sub

    Private Sub BarButtonItem46_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem46.ItemClick
        If FormAccess("11") = True Then
            ' Initialize the form if it's Nothing
            If Child_EmailScanner Is Nothing Then
                Child_EmailScanner = New EmailScanner()
            End If
            OpenForm(Child_EmailScanner, Me, RibbonPage3)
            BntUploadeSMTP.Enabled = True
            RibbonPageGroup17.Enabled = True
        End If
    End Sub



    Private Sub BarButtonItem40_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem40.ItemClick
        If FormAccess("6") = True Then
            ' Initialize the form if it's Nothing
            If Child_frmZeroPass Is Nothing Then
                Child_frmZeroPass = New frmZeroPass()
            End If
            OpenForm(Child_frmZeroPass, Me, RibbonPageZeroPass)
            BntStart.Enabled = False
            BntSaveList.Enabled = False
            BarButtonItem67.Enabled = False
            BntUploadeList.Enabled = True
            RibbonPageGroup42.Visible = True
            RibbonPageGroup43.Visible = True
            RibbonPageGroup44.Visible = True
            RibbonPageGroup45.Visible = True
        End If
    End Sub

    Private Sub BntRedirectMicrosoft_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntRedirectMicrosoft.ItemClick
        If FormAccess("22") = True Then

            OpenForm(child_frmMicrosoftRedirect, Me)
        End If

    End Sub

    Private Sub BntRedirectPuzzel_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntRedirectPuzzel.ItemClick
        If FormAccess("21") = True Then
            OpenForm(child_frmRedirectPuzzle, Me)

        End If

    End Sub

    Private Sub BntRedirectWithPass_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntRedirectWithPass.ItemClick
        If FormAccess("36") = True Then
            OpenForm(child_frmRedirectWithPassword, Me)

        End If


    End Sub

    Private Sub BarButtonItem29_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem29.ItemClick
        If FormAccess("25") = True Then
            OpenForm(child_frmMultiLinker, Me)

        End If
    End Sub

    Private Sub BarButtonItem30_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem30.ItemClick
        If FormAccess("26") = True Then

            OpenForm(child_frmHideLink, Me)
        End If
    End Sub

    Private Sub BntRedirectVIP_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntRedirectVIP.ItemClick
        If FormAccess("20") = True Then
            OpenForm(child_frmPassMicrosoft, Me)

        End If
    End Sub

    Private Sub RedirectEncodedLinkSpider_ItemClick(sender As Object, e As ItemClickEventArgs) Handles RedirectEncodedLinkSpider.ItemClick
        If FormAccess("39") = True Then
            OpenForm(child_frmRedirectSpider, Me)

        End If
    End Sub

    Private Sub BarButtonItem39_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem39.ItemClick
        If FormAccess("5") = True Then
            OpenForm(Child_DeleteDuplicatemail, Me, RibbonPageCleanMail)
            RibbonPageGroup35.Visible = True
            RibbonPageGroup37.Visible = True
            RibbonPageGroup38.Visible = True
            BntDeleteDuplicatemail.Enabled = True
            Select_File_text_Email.Enabled = True
            Start_Delet_Mail.Enabled = True
            Start_Delet_Mail.Enabled = False
        End If
    End Sub

    Private Sub BarButtonItem33_ItemClick_1(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem33.ItemClick
        If FormAccess("4") = True Then
            OpenForm(Child_frmlinktoAtt, Me, RibbonPage17)

        End If
    End Sub

    Private Sub BarButtonItem48_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem48.ItemClick
        If FormAccess("4") = True Then
            OpenForm(Child_frmlinktoAtt, Me, RibbonPage17)

        End If
    End Sub


    Private Sub BarButtonItem77_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BarButtonItem77.ItemClick
        If ChngFlg = "" Then
            Dim result As DialogResult = XtraMessageBox.Show("Do you want to Restart the application?", "Confirm Restart", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
            If result = DialogResult.Yes Then
                Dim args As New XtraMessageBoxArgs()
                args.AutoCloseOptions.Delay = 2000
                args.AutoCloseOptions.ShowTimerOnDefaultButton = True
                args.DefaultButtonIndex = 0
                args.Icon = System.Drawing.SystemIcons.Information
                args.Caption = "Restarting"
                args.Text = "Your Best Sender VIP Successfully. Restarting in..."
                args.Buttons = New DialogResult() {DialogResult.OK}
                XtraMessageBox.Show(args).ToString()
                System.Diagnostics.Process.Start(Application.ExecutablePath)
                Process.GetCurrentProcess().Kill()
            End If
        End If
    End Sub

    Private Sub BntStop_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntStop.ItemClick
        If Child_frmZeroPass Is Nothing Then
            Child_frmZeroPass = New frmZeroPass()
        End If
        Child_frmZeroPass.StopProcess()
    End Sub

    Private Sub BntStart_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntStart.ItemClick
        If Child_frmZeroPass Is Nothing Then
            Child_frmZeroPass = New frmZeroPass()
        End If
        Child_frmZeroPass.Startlist()

    End Sub

    Private Sub BntSaveList_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntSaveList.ItemClick
        If Child_frmZeroPass Is Nothing Then
            Child_frmZeroPass = New frmZeroPass()
        End If
        Child_frmZeroPass.SaveList()

    End Sub

    Private Sub BntUploadeList_ItemClick(sender As Object, e As ItemClickEventArgs) Handles BntUploadeList.ItemClick
        If Child_frmZeroPass Is Nothing Then
            Child_frmZeroPass = New frmZeroPass()
        End If
        Child_frmZeroPass.UploadeList()
    End Sub
End Class
