﻿Imports ZXing
Imports ZXing.QrCode
Imports ZXing.Rendering
Imports ZXing.Common
Public Class CustomPatternRenderer
    Implements IBarcodeRenderer(Of Bitmap)
    Public Function Render(matrix As BitMatrix, format As BarcodeFormat, content As String) As Bitmap Implements IBarcodeRenderer(Of Bitmap).Render
        Return Render(matrix, format, content, Nothing)
    End Function
    Public Function Render(matrix As BitMatrix, format As BarcodeFormat, content As String, options As EncodingOptions) As Bitmap Implements IBarcodeRenderer(Of Bitmap).Render
        Dim width = matrix.Width
        Dim height = matrix.Height
        Dim bmp = New Bitmap(width, height, Imaging.PixelFormat.Format32bppRgb)
        Using g As Graphics = Graphics.FromImage(bmp)
            g.Clear(Color.White)
            Dim rnd As New Random()
            For y = 0 To height - 1
                For x = 0 To width - 1
                    If matrix(x, y) Then ' النقاط السوداء
                        ' اختيار شكل عشوائي للمربع أو الدائرة
                        If rnd.NextDouble() > 0.5 Then
                            g.FillRectangle(Brushes.Black, x, y, 1, 1) ' مربع صغير
                        Else
                            g.FillEllipse(Brushes.Black, x - 0.5F, y - 0.5F, 2, 2) ' دائرة
                        End If
                    End If
                Next
            Next
        End Using
        Return bmp
    End Function
    ' هذه الدالة لم تعد مستخدمة ولكن نتركها للتوافق مع الكود السابق
    Private Function Render(pixelData As PixelData, format As BarcodeFormat, content As String, options As EncodingOptions) As Bitmap
        Dim width = pixelData.Width
        Dim height = pixelData.Height
        Dim bmp = New Bitmap(width, height, Imaging.PixelFormat.Format32bppRgb)
        Using g As Graphics = Graphics.FromImage(bmp)
            g.Clear(Color.White)
            Dim rnd As New Random()
            For y = 0 To height - 1
                For x = 0 To width - 1
                    Dim index = y * width + x
                    If pixelData.Pixels(index) = 0 Then ' النقاط السوداء
                        If rnd.NextDouble() > 0.5 Then
                            g.FillRectangle(Brushes.Black, x, y, 1, 1)
                        Else
                            g.FillEllipse(Brushes.Black, x - 0.5F, y - 0.5F, 2, 2)
                        End If
                    End If
                Next
            Next
        End Using
        Return bmp
    End Function
End Class