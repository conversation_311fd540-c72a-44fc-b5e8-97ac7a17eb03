﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ListsForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(ListsForm))
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.TextBox1 = New System.Windows.Forms.RichTextBox()
        Me.Button2 = New System.Windows.Forms.Button()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.PanelUp2 = New System.Windows.Forms.Panel()
        Me.labelTag = New System.Windows.Forms.Label()
        Me.BtnHide = New System.Windows.Forms.Button()
        Me.BtnFull = New System.Windows.Forms.Button()
        Me.BtnClose = New System.Windows.Forms.Button()
        Me.Pllaft = New System.Windows.Forms.Panel()
        Me.PanelDowen = New System.Windows.Forms.Panel()
        Me.Panelrighet = New System.Windows.Forms.Panel()
        Me.GroupBox1.SuspendLayout()
        Me.PanelUp2.SuspendLayout()
        Me.SuspendLayout()
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.TextBox1)
        Me.GroupBox1.Controls.Add(Me.Button2)
        Me.GroupBox1.Controls.Add(Me.Button1)
        Me.GroupBox1.ForeColor = System.Drawing.Color.White
        Me.GroupBox1.Location = New System.Drawing.Point(13, 41)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(415, 595)
        Me.GroupBox1.TabIndex = 21
        Me.GroupBox1.TabStop = False
        '
        'TextBox1
        '
        Me.TextBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(35, Byte), Integer), CType(CType(33, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.TextBox1.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Bold)
        Me.TextBox1.ForeColor = System.Drawing.Color.White
        Me.TextBox1.Location = New System.Drawing.Point(6, 13)
        Me.TextBox1.Name = "TextBox1"
        Me.TextBox1.Size = New System.Drawing.Size(403, 528)
        Me.TextBox1.TabIndex = 8
        Me.TextBox1.Text = ""
        '
        'Button2
        '
        Me.Button2.BackColor = System.Drawing.Color.FromArgb(CType(CType(35, Byte), Integer), CType(CType(33, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.Button2.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Bold)

        Me.Button2.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button2.Location = New System.Drawing.Point(5, 546)
        Me.Button2.Margin = New System.Windows.Forms.Padding(2)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(119, 38)
        Me.Button2.TabIndex = 7
        Me.Button2.Text = "Save"
        Me.Button2.UseVisualStyleBackColor = False
        '
        'Button1
        '
        Me.Button1.BackColor = System.Drawing.Color.FromArgb(CType(CType(35, Byte), Integer), CType(CType(33, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.Button1.Font = New System.Drawing.Font("Tahoma", 10.0!, System.Drawing.FontStyle.Bold)

        Me.Button1.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button1.Location = New System.Drawing.Point(291, 546)
        Me.Button1.Margin = New System.Windows.Forms.Padding(2)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(119, 38)
        Me.Button1.TabIndex = 5
        Me.Button1.Text = "Close"
        Me.Button1.UseVisualStyleBackColor = False
        '
        'PanelUp2
        '
        Me.PanelUp2.BackColor = System.Drawing.Color.FromArgb(CType(CType(35, Byte), Integer), CType(CType(33, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.PanelUp2.Controls.Add(Me.labelTag)
        Me.PanelUp2.Controls.Add(Me.BtnHide)
        Me.PanelUp2.Controls.Add(Me.BtnFull)
        Me.PanelUp2.Controls.Add(Me.BtnClose)
        Me.PanelUp2.Dock = System.Windows.Forms.DockStyle.Top
        Me.PanelUp2.Location = New System.Drawing.Point(7, 0)
        Me.PanelUp2.Name = "PanelUp2"
        Me.PanelUp2.Size = New System.Drawing.Size(427, 35)
        Me.PanelUp2.TabIndex = 17
        '
        'labelTag
        '
        Me.labelTag.AutoSize = True
        Me.labelTag.Font = New System.Drawing.Font("Segoe UI", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.labelTag.ForeColor = System.Drawing.Color.White
        Me.labelTag.Location = New System.Drawing.Point(1, 5)
        Me.labelTag.Name = "labelTag"
        Me.labelTag.Size = New System.Drawing.Size(34, 21)
        Me.labelTag.TabIndex = 199
        Me.labelTag.Text = "List"
        '
        'BtnHide
        '
        Me.BtnHide.BackColor = System.Drawing.Color.FromArgb(CType(CType(35, Byte), Integer), CType(CType(33, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.BtnHide.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BtnHide.Dock = System.Windows.Forms.DockStyle.Right
        Me.BtnHide.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(3, Byte), Integer), CType(CType(8, Byte), Integer), CType(CType(27, Byte), Integer))
        Me.BtnHide.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(CType(CType(3, Byte), Integer), CType(CType(8, Byte), Integer), CType(CType(27, Byte), Integer))
        Me.BtnHide.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(3, Byte), Integer), CType(CType(8, Byte), Integer), CType(CType(27, Byte), Integer))

        Me.BtnHide.Location = New System.Drawing.Point(316, 0)
        Me.BtnHide.Name = "BtnHide"
        Me.BtnHide.Size = New System.Drawing.Size(37, 35)
        Me.BtnHide.TabIndex = 198
        Me.BtnHide.UseVisualStyleBackColor = False
        '
        'BtnFull
        '
        Me.BtnFull.BackColor = System.Drawing.Color.FromArgb(CType(CType(35, Byte), Integer), CType(CType(33, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.BtnFull.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BtnFull.Dock = System.Windows.Forms.DockStyle.Right
        Me.BtnFull.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(3, Byte), Integer), CType(CType(8, Byte), Integer), CType(CType(27, Byte), Integer))
        Me.BtnFull.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(CType(CType(3, Byte), Integer), CType(CType(8, Byte), Integer), CType(CType(27, Byte), Integer))
        Me.BtnFull.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(3, Byte), Integer), CType(CType(8, Byte), Integer), CType(CType(27, Byte), Integer))

        Me.BtnFull.Location = New System.Drawing.Point(353, 0)
        Me.BtnFull.Name = "BtnFull"
        Me.BtnFull.Size = New System.Drawing.Size(37, 35)
        Me.BtnFull.TabIndex = 197
        Me.BtnFull.UseVisualStyleBackColor = False
        '
        'BtnClose
        '
        Me.BtnClose.BackColor = System.Drawing.Color.FromArgb(CType(CType(35, Byte), Integer), CType(CType(33, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.BtnClose.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BtnClose.Dock = System.Windows.Forms.DockStyle.Right
        Me.BtnClose.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(3, Byte), Integer), CType(CType(8, Byte), Integer), CType(CType(27, Byte), Integer))
        Me.BtnClose.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(CType(CType(3, Byte), Integer), CType(CType(8, Byte), Integer), CType(CType(27, Byte), Integer))
        Me.BtnClose.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(CType(CType(3, Byte), Integer), CType(CType(8, Byte), Integer), CType(CType(27, Byte), Integer))

        Me.BtnClose.Location = New System.Drawing.Point(390, 0)
        Me.BtnClose.Name = "BtnClose"
        Me.BtnClose.Size = New System.Drawing.Size(37, 35)
        Me.BtnClose.TabIndex = 196
        Me.BtnClose.UseVisualStyleBackColor = False
        '
        'Pllaft
        '
        Me.Pllaft.BackColor = System.Drawing.Color.FromArgb(CType(CType(35, Byte), Integer), CType(CType(33, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.Pllaft.Dock = System.Windows.Forms.DockStyle.Left
        Me.Pllaft.Location = New System.Drawing.Point(0, 0)
        Me.Pllaft.Name = "Pllaft"
        Me.Pllaft.Size = New System.Drawing.Size(7, 644)
        Me.Pllaft.TabIndex = 18
        '
        'PanelDowen
        '
        Me.PanelDowen.BackColor = System.Drawing.Color.FromArgb(CType(CType(35, Byte), Integer), CType(CType(33, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.PanelDowen.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.PanelDowen.Location = New System.Drawing.Point(0, 644)
        Me.PanelDowen.Name = "PanelDowen"
        Me.PanelDowen.Size = New System.Drawing.Size(434, 7)
        Me.PanelDowen.TabIndex = 20
        '
        'Panelrighet
        '
        Me.Panelrighet.BackColor = System.Drawing.Color.FromArgb(CType(CType(35, Byte), Integer), CType(CType(33, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.Panelrighet.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panelrighet.Location = New System.Drawing.Point(434, 0)
        Me.Panelrighet.Name = "Panelrighet"
        Me.Panelrighet.Size = New System.Drawing.Size(7, 651)
        Me.Panelrighet.TabIndex = 19
        '
        'ListsForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(57, Byte), Integer), CType(CType(58, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(441, 651)
        Me.Controls.Add(Me.GroupBox1)
        Me.Controls.Add(Me.PanelUp2)
        Me.Controls.Add(Me.Pllaft)
        Me.Controls.Add(Me.PanelDowen)
        Me.Controls.Add(Me.Panelrighet)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Name = "ListsForm"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "ListsForm"
        Me.GroupBox1.ResumeLayout(False)
        Me.PanelUp2.ResumeLayout(False)
        Me.PanelUp2.PerformLayout()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents GroupBox1 As GroupBox
    Friend WithEvents TextBox1 As RichTextBox
    Friend WithEvents Button2 As Button
    Friend WithEvents Button1 As Button
    Friend WithEvents PanelUp2 As Panel
    Friend WithEvents labelTag As Label
    Friend WithEvents BtnHide As Button
    Friend WithEvents BtnFull As Button
    Friend WithEvents BtnClose As Button
    Friend WithEvents Pllaft As Panel
    Friend WithEvents PanelDowen As Panel
    Friend WithEvents Panelrighet As Panel
End Class
