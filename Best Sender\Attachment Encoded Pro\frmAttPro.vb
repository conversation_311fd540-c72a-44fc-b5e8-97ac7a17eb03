﻿Imports System.IO
Imports System.Security.Cryptography
Imports System.Text
Imports DevExpress.CodeParser
Imports DevExpress.XtraEditors
Public Class frmAttPro
    Dim EncodedLetter As New RichTextBox
    Dim NormalLetter As New RichTextBox
    Dim CheckOP As Integer = 0
    '==================================
    Dim i As Integer = 0
    Dim txt As String = ""
    Dim tab As New List(Of Integer)()

    ' Progress variables for ProgressPanel2
    Private progressValue As Integer = 0
    Private progressTimer As New Timer()
    Private isProcessing As Boolean = False

    ' Progress variables for ProgressPanel3
    Private progressValue3 As Integer = 0
    Private progressTimer3 As New Timer()
    Private isProcessing3 As Boolean = False

    ' Progress variables for ProgressPanel1 (VIP)
    Private progressValue1 As Integer = 0
    Private progressTimer1 As New Timer()
    Private isProcessing1 As Boolean = False
    Private ReadOnly excludedWords As String() = {
        "[-Email-]", "[-IP-]", "[-IPChina-]", "[-Domain-]", "[-Email64-]", "[-Name-]", "[-Date-]", "[-DateTomorrow-]",
        "[-RandomBrowser-]", "[-RCountry-]", "[-FakePhone-]", "[-FakeEmail-]", "[-NewYork-]", "[-UCase-]",
        "[-Link-]", "[-Logo-]", "[-QRCode-]", "[-RN1-]", "[-RN2-]", "[-RN3-]", "[-RN4-]", "[-RN5-]", "[-RN6-]",
        "[-RN7-]", "[-RN8-]", "[-RN9-]", "[-RN10-]", "[-RN12-]", "[-RCh1-]", "[-RCh2-]", "[-RCh3-]", "[-RCh4-]",
        "[-RCh5-]", "[-RCh6-]", "[-RCh7-]", "[-RCh8-]"
    }
    Private Const VBQuote As String = """"
    ReadOnly FunctionList As List(Of String) = New List(Of String) From {"[-Email64-]", "[-Email-]", "[-IP-]", "[-IPChina-]", "[-Domain-]", "[-Name-]", "[-Date-]", "[-DateTomorrow-]",
        "[-RandomBrowser-]", "[-RCountry-]", "[-FakePhone-]", "[-FakeEmail-]", "[-NewYork-]", "[-UCase-]", "[-Link-]", "[-Logo-]", "[-RN1-]", "[-RN2-]", "[-RN3-]", "[-RN4-]",
        "[-RN5-]", "[-RN6-]", "[-QRCode-]", "[-CompanyName-]", "[-Time-]", "[-RN7-]", "[-RN8-]", "[-RN9-]", "[-RN10-]", "[-RN12-]", "[-RCh1-]", "[-RCh2-]", "[-RCh3-]", "[-RCh4-]", "[-RCh5-]", "[-RCh6-]", "[-RCh7-]", "[-RCh8-]"}
    Private Function GenerateKey() As Byte()
        Dim keyBytes(31) As Byte ' 256 بت
        Dim rng As New RNGCryptoServiceProvider()
        rng.GetBytes(keyBytes)
        Return keyBytes
    End Function
    ' دالة لإنشاء IV عشوائي 16 بايت
    Private Function GenerateIV() As Byte()
        Dim ivBytes(15) As Byte ' 128 بت
        Dim rng As New RNGCryptoServiceProvider()
        rng.GetBytes(ivBytes)
        Return ivBytes
    End Function
    ' دالة التشفير مع تضمين المفتاح و IV داخل النص المشفر
    Private Function EncryptText(plainText As String) As String
        Dim key As Byte() = GenerateKey() ' إنشاء مفتاح عشوائي
        Dim iv As Byte() = GenerateIV() ' إنشاء IV عشوائي
        Dim aes As New AesManaged()
        aes.Key = key
        aes.IV = iv
        aes.Mode = CipherMode.CBC
        aes.Padding = PaddingMode.PKCS7
        Dim encryptor As ICryptoTransform = aes.CreateEncryptor()
        Dim plainBytes As Byte() = Encoding.UTF8.GetBytes(plainText)
        Dim encryptedBytes As Byte() = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length)
        ' تخزين (المفتاح + IV + النص المشفر) في نفس السلسلة
        Dim resultBytes As Byte() = key.Concat(iv).Concat(encryptedBytes).ToArray()
        Return Convert.ToBase64String(resultBytes)
    End Function
    Private Sub ConvertFile(ByVal SourceFile As String, ByVal DestFile As String, ByVal Title As String, Optional ByVal DisableClick As Boolean = False)
        ' Validate input parameters
        If String.IsNullOrEmpty(SourceFile) Then
            Throw New ArgumentException("Source file path cannot be empty.")
        End If
        If String.IsNullOrEmpty(DestFile) Then
            Throw New ArgumentException("Destination file path cannot be empty.")
        End If
        If Not File.Exists(SourceFile) Then
            Throw New FileNotFoundException($"Source file not found: {SourceFile}")
        End If

        Dim sr As StreamReader = Nothing
        Dim sw As StreamWriter = Nothing
        Dim sb As New StringBuilder()
        Dim Buffer As String = vbNullString
        sr = New StreamReader(SourceFile)
        Buffer = sr.ReadToEnd
        sr.Close()
        If DisableClick Then
            Buffer = Buffer & "<script>document.oncontextmenu=new Function(" & VBQuote & "return false" & VBQuote & ")</script>"
        End If
        sb.AppendLine("<html>")
        sb.AppendLine("<head>")
        sb.AppendLine("<title>" & Title & "</title>")
        sb.AppendLine("<body>")
        sb.AppendLine("<script language=" & VBQuote & "JavaScript" & VBQuote & " type=" & VBQuote & "text/javascript" & VBQuote & ">")
        sb.Append("document.write(unescape('")
        Dim i As Integer = 0
        While i < Buffer.Length
            Dim c As Char = Buffer(i)
            Dim found As Boolean = False
            For Each flWord As String In FunctionList
                If Buffer.Substring(i).StartsWith(flWord) Then
                    sb.Append(flWord)
                    i += flWord.Length
                    found = True
                    Exit For
                End If
            Next
            If Not found Then
                sb.Append("%" & Asc(c).ToString("X2"))
                i += 1
            End If
        End While
        sb.Append("'));" & vbCrLf)
        sb.AppendLine("</script>")
        sb.AppendLine("</body>")
        sb.AppendLine("</html>")
        sw = New StreamWriter(DestFile)
        sw.Write(sb.ToString())
        sw.Close()
    End Sub




    ' استبعاد الكلمات التي لا يجب تشفيرها من النص
    Private Function ExcludeWordsFromEncryption(content As String) As String
        For Each word In excludedWords
            content = content.Replace(word, "[NO_ENCRYPT]" & word & "[NO_ENCRYPT]")
        Next
        Return content
    End Function
    ' توليد محتوى HTML مع النص المشفر واستخدام JavaScript لفك التشفير
    Private Sub SaveEncryptedHtmlFile(originalFilePath As String, encryptedContent As String)
        Dim encryptedHtml As String = GenerateHtmlWithEncryption(encryptedContent)
        Dim encryptedFilePath As String = Path.Combine(Path.GetDirectoryName(originalFilePath), "" & Path.GetFileName(originalFilePath))
        File.WriteAllText(encryptedFilePath, encryptedHtml, Encoding.UTF8)
    End Sub
    Private Function GenerateHtmlWithEncryption(encryptedContent As String) As String
        Dim html As String = "<!DOCTYPE html>" & vbCrLf &
                             "<html>" & vbCrLf &
                             "<head>" & vbCrLf &
                             "<title>Microsoft</title>" & vbCrLf &
                             "</head>" & vbCrLf &
                             "<body>" & vbCrLf &
                             "<h1></h1>" & vbCrLf &
                             "<div id=""content""></div>" & vbCrLf &
                             "<script>" & vbCrLf &
                             "    var encodedHtml = '" & encryptedContent & "';" & vbCrLf &
                             "    var decodedHtml = atob(encodedHtml);" & vbCrLf &
                             "    document.getElementById('content').innerHTML = decodedHtml;" & vbCrLf &
                             "</script>" & vbCrLf &
                             "</body>" & vbCrLf &
                             "</html>"
        Return html
    End Function
    ' دالة لتشفير النص باستخدام Base64
    Private Function EncryptBase64(input As String) As String
        Dim byteArray As Byte() = Encoding.UTF8.GetBytes(input)
        Return Convert.ToBase64String(byteArray)
    End Function
    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        ProgressBarControl1.Visible = True
        Bnt_En_VIP.Enabled = False
        bntClearPathVIP.Enabled = False
        lblPath.Visible = True
        ProgressBarControl1.Increment(1)
        If ProgressBarControl1.Position = ProgressBarControl1.Properties.Maximum Then
            Timer1.Stop()
            If Not File.Exists(PathFile_Att_VIP.Text) Then
                XtraMessageBox.Show("Input source file not found.", "file not found", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                Exit Sub
            Else
                ' Check if save path is empty and ask for save location
                If String.IsNullOrEmpty(PathSaveFile_Encoded.Text) Then
                    Using saveFileDialog As New SaveFileDialog()
                        saveFileDialog.Title = "💾 Save VIP Encoded File"
                        saveFileDialog.Filter = "HTML Files (*.html)|*.html|All Files (*.*)|*.*"
                        saveFileDialog.DefaultExt = "html"
                        saveFileDialog.FileName = $"vip_encoded_{Path.GetFileNameWithoutExtension(PathFile_Att_VIP.Text)}.html"
                        saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)

                        If saveFileDialog.ShowDialog() = DialogResult.OK Then
                            PathSaveFile_Encoded.Text = saveFileDialog.FileName
                        Else
                            ' User cancelled, stop the process
                            ProgressBarControl1.Visible = False
                            Bnt_En_VIP.Enabled = True
                            bntClearPathVIP.Enabled = True
                            lblPath.Visible = False
                            StopProgressPanel1()
                            Exit Sub
                        End If
                    End Using
                End If

                Try
                    'Criptografe o arquivo.
                    ConvertFile(PathFile_Att_VIP.Text, PathSaveFile_Encoded.Text, LogInNormalTextBox3.Text, LogInCheckBox1.Checked)
                    'Apresentação de mensagem de acabamento.
                    Dim msg As String = "The File has been Successfully Encrypted...!" & vbNewLine
                    msg = msg & "Note: Please Check the Saved output in the selected save path."
                    XtraMessageBox.Show(msg, Text, MessageBoxButtons.OK, MessageBoxIcon.Information)
                    'Redefina o sontrols.
                    lblPath.Visible = False
                    Bnt_En_VIP.Enabled = True
                    bntClearPathVIP.Enabled = True
                    PathSaveFile_Encoded.Enabled = False
                    BntSaveVIP.Enabled = False
                    ProgressBarControl1.Enabled = False
                    LogInCheckBox1.Checked = False

                    Dim argument As String = "/select, """ & PathSaveFile_Encoded.Text & """"
                    Process.Start("explorer.exe", argument)
                    PathFile_Att_VIP.Text = vbNullString
                    PathSaveFile_Encoded.Text = vbNullString
                    ProgressBarControl1.Visible = False

                    ' Stop and hide ProgressPanel1
                    StopProgressPanel1()

                Catch ex As Exception
                    ' Handle conversion errors
                    XtraMessageBox.Show($"❌ Error during file conversion: {ex.Message}", "Conversion Error", MessageBoxButtons.OK, MessageBoxIcon.Error)

                    ' Reset controls on error
                    lblPath.Visible = False
                    Bnt_En_VIP.Enabled = True
                    bntClearPathVIP.Enabled = True
                    ProgressBarControl1.Visible = False
                    StopProgressPanel1()
                End Try
            End If
            'XtraMessageBox.Show("Done Crypter Letter check Same Letter", "Encoded, HTML ", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub


    Private Sub XtraTabControl1_SelectedPageChanged(sender As Object, e As DevExpress.XtraTab.TabPageChangedEventArgs) Handles XtraTabControl1.SelectedPageChanged
        ' Handle XtraTabPage2 selection
        If e.Page Is XtraTabPage2 Then
            ' Hide PNLAttachWait when clicking on XtraTabPage2
            If PNLAttachWait IsNot Nothing Then
                PNLAttachWait.Visible = False
            End If
            ' Re-center elements when tab is selected
            CenterXtraTabPage2Elements()
        End If

        If e.Page Is XtraTabPage3 Then
            If FormAccess("28") = True Then
                XtraTabPage3.PageVisible = True ' يجب استخدام PageVisible بدلاً من Visible
                ' Re-center elements when XtraTabPage3 is selected
                CenterXtraTabPage3Elements()
            Else
                XtraTabPage3.Visible = False
            End If
        End If
    End Sub

    ' دالة لإنشاء نص عشوائي يشبه الـ Hex
    Private Function GenerateHexString(length As Integer) As String
        Dim chars As String = "abcdef0123456789"
        Dim random As New Random()
        Dim result As New StringBuilder()
        For i As Integer = 1 To length
            result.Append(chars(random.Next(chars.Length)))
        Next
        Return result.ToString()
    End Function



    Private Sub SelectLetterNormal_Base64_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles SelectLetterNormal_Base64.ItemClick
        Dim ofd As New OpenFileDialog()
        ofd.Filter = "HTML Files|*.html;*.htm|All Files|*.*"
        ofd.Title = "🔐 Select HTML File for Base64 Encoding"
        ofd.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)

        If ofd.ShowDialog() = DialogResult.OK Then
            Try
                Dim filePath As String = ofd.FileName

                ' تعيين مسار الملف في مربع النص
                PathLetterNormalBase64.Text = filePath

                ' Start progress animation
                StartProgressAnimation()

                ' قراءة محتوى الملف
                Dim fileContent As String = File.ReadAllText(filePath)

                ' عرض المحتوى في مربع النص الغني مع تنسيق جميل
                DisplayFileContent(fileContent, filePath)

                ' تحديد نوع التشفير من ComboBoxEdit1
                Dim selectedEncryption As String = cmbEncryption.SelectedItem.ToString()
                Dim encryptedContent As String = ""

                Select Case selectedEncryption
                    Case "Base64"
                        encryptedContent = EncryptBase64(fileContent)
                    Case Else
                        DevExpress.XtraEditors.XtraMessageBox.Show("⚠️ Please Select Algorithm Method!", "Algorithm Required", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        StopProgressAnimation()
                        Return
                End Select

                ' عرض المحتوى المشفر في RichTextBox
                Richtext_En_base64.Text = encryptedContent

                ' عرض النتيجة المشفرة بتنسيق جميل
                DisplayEncodedResult(encryptedContent, filePath)

                ' عرض مربع الحوار لحفظ الملف
                Using saveFileDialog As New SaveFileDialog()
                    saveFileDialog.Title = "💾 Save Base64 Encoded File"
                    saveFileDialog.Filter = "HTML Files (*.html)|*.html|Text Files (*.txt)|*.txt|All Files (*.*)|*.*"
                    saveFileDialog.DefaultExt = "html"
                    saveFileDialog.FileName = $"encoded_{Path.GetFileNameWithoutExtension(filePath)}.html"

                    If saveFileDialog.ShowDialog() = DialogResult.OK Then
                        ' حفظ المحتوى المشفر في الملف الذي اختاره المستخدم
                        Dim selectedFilePath As String = saveFileDialog.FileName
                        SaveEncryptedHtmlFile(selectedFilePath, encryptedContent)

                        ' عرض رسالة تأكيد
                        XtraMessageBox.Show("✅ The encrypted file has been successfully saved!" & vbCrLf & $"📁 Location: {selectedFilePath}", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)

                        ' فتح مجلد الحفظ
                        Dim folderPath As String = Path.GetDirectoryName(selectedFilePath)
                        If Directory.Exists(folderPath) Then
                            Process.Start("explorer.exe", folderPath)
                        Else
                            XtraMessageBox.Show("❌ The folder could not be opened. Check the file path.", "Folder Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        End If
                    Else
                        ' إذا لم يقم المستخدم بتحديد ملف
                        XtraMessageBox.Show("⚠️ The file was not saved.", "Operation Canceled", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    End If
                End Using

                ' تمكين وإظهار الأزرار
                Bnt_EN_Base64.Enabled = True
                Bnt_EN_Base64.Visibility = DevExpress.XtraBars.BarItemVisibility.Always

            Catch ex As Exception
                XtraMessageBox.Show($"❌ Error processing file: {ex.Message}", "Processing Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                StopProgressAnimation()
            End Try
        End If
    End Sub

    ' Start progress animation
    Private Sub StartProgressAnimation()
        Try
            If Not isProcessing Then
                progressValue = 0
                isProcessing = True
                ProgressPanel2.Visible = True
                ProgressPanel2.BringToFront()
                ProgressPanel2.Appearance.ForeColor = Color.FromArgb(240, 185, 11) ' Reset to yellow
                CenterProgressPanel()
                progressTimer.Start()
            End If
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Stop progress animation
    Private Sub StopProgressAnimation()
        Try
            progressTimer.Stop()
            ProgressPanel2.Visible = False
            isProcessing = False
            progressValue = 0
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Display file content with beautiful formatting
    Private Sub DisplayFileContent(content As String, fileName As String)
        Try
            With Richtext_NormalBase64
                .Clear()
                .ForeColor = Color.FromArgb(234, 236, 239)

                ' Add header
                .AppendText($"📄 File: {Path.GetFileName(fileName)}" & vbCrLf)
                .AppendText($"📊 Size: {FormatFileSize(content.Length)}" & vbCrLf)
                .AppendText($"🕒 Loaded: {DateTime.Now:yyyy-MM-dd HH:mm:ss}" & vbCrLf)
                .AppendText("".PadRight(60, "─") & vbCrLf & vbCrLf)

                ' Add content
                .AppendText(content)

                ' Scroll to top
                .SelectionStart = 0
                .ScrollToCaret()
            End With
        Catch ex As Exception
            ' Fallback to simple display
            Richtext_NormalBase64.Text = content
        End Try
    End Sub

    ' Format file size
    Private Function FormatFileSize(bytes As Long) As String
        Try
            Dim sizes() As String = {"B", "KB", "MB", "GB"}
            Dim order As Integer = 0
            Dim size As Double = bytes

            While size >= 1024 AndAlso order < sizes.Length - 1
                order += 1
                size /= 1024
            End While

            Return $"{size:F2} {sizes(order)}"
        Catch
            Return $"{bytes} B"
        End Try
    End Function

    Private Sub Bnt_EN_Base64_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles Bnt_EN_Base64.ItemClick
        Try
            Dim filePath As String = PathLetterNormalBase64.Text

            If String.IsNullOrWhiteSpace(Richtext_NormalBase64.Text) Then
                DevExpress.XtraEditors.XtraMessageBox.Show("🚫 First, choose the HTML file that will be encoded!", "No File Selected", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            If String.IsNullOrEmpty(filePath) OrElse Not File.Exists(filePath) Then
                DevExpress.XtraEditors.XtraMessageBox.Show("🚫 Please select a valid HTML file first!", "Invalid File", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            ' Start encoding progress animation
            StartEncodingProgress()

            ' قراءة محتوى الملف
            Dim fileContent As String = File.ReadAllText(filePath)

            ' تحديد نوع التشفير من ComboBoxEdit1
            Dim selectedEncryption As String = cmbEncryption.SelectedItem.ToString()
            Dim encryptedContent As String = ""

            Select Case selectedEncryption
                Case "Base64"
                    encryptedContent = EncryptBase64(fileContent)
                Case Else
                    DevExpress.XtraEditors.XtraMessageBox.Show("⚠️ Please select Base64 algorithm!", "Algorithm Required", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    StopProgressAnimation()
                    Return
            End Select

            ' عرض المحتوى المشفر في RichTextBox
            Richtext_En_base64.Text = encryptedContent

            ' Display encoded result beautifully
            DisplayEncodedResult(encryptedContent, filePath)

            ' عرض مربع الحوار لحفظ الملف
            Using saveFileDialog As New SaveFileDialog()
                saveFileDialog.Title = "💾 Save Base64 Encoded File"
                saveFileDialog.Filter = "HTML Files (*.html)|*.html|Text Files (*.txt)|*.txt|All Files (*.*)|*.*"
                saveFileDialog.DefaultExt = "html"
                saveFileDialog.FileName = $"encoded_{Path.GetFileNameWithoutExtension(filePath)}.html"

                If saveFileDialog.ShowDialog() = DialogResult.OK Then
                    ' حفظ المحتوى المشفر في الملف الذي اختاره المستخدم
                    Dim selectedFilePath As String = saveFileDialog.FileName
                    SaveEncryptedHtmlFile(selectedFilePath, encryptedContent)

                    ' عرض رسالة تأكيد
                    XtraMessageBox.Show("✅ The encoded file has been successfully saved!" & vbCrLf & $"📁 Location: {selectedFilePath}", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)

                    ' فتح مجلد الحفظ
                    Dim argument As String = "/select, """ & selectedFilePath & """"
                    Process.Start("explorer.exe", argument)
                Else
                    ' إذا لم يقم المستخدم بتحديد ملف
                    XtraMessageBox.Show("⚠️ The file was not saved.", "Operation Canceled", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                End If
            End Using

        Catch ex As Exception
            XtraMessageBox.Show($"❌ Error during encoding: {ex.Message}", "Encoding Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            StopProgressAnimation()
        End Try
    End Sub

    ' Start encoding progress with different animation
    Private Sub StartEncodingProgress()
        Try
            If Not isProcessing Then
                progressValue = 0
                isProcessing = True
                ProgressPanel2.Visible = True
                ProgressPanel2.BringToFront()
                ProgressPanel2.Appearance.ForeColor = Color.FromArgb(240, 185, 11) ' Yellow for encoding
                ProgressPanel2.Caption = "🔐 Starting encoding process..."
                CenterProgressPanel()

                ' Use faster timer for encoding
                progressTimer.Stop()
                progressTimer.Interval = 30 ' Even faster for encoding (30ms)
                progressTimer.Start()
            End If
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Display encoded result with beautiful formatting
    Private Sub DisplayEncodedResult(base64Content As String, originalFilePath As String)
        Try
            With Richtext_NormalBase64
                .Clear()
                .ForeColor = Color.FromArgb(234, 236, 239)

                ' Add beautiful header
                .AppendText("🎯 BASE64 ENCODING COMPLETE" & vbCrLf)
                .AppendText("".PadRight(60, "═") & vbCrLf & vbCrLf)

                ' Add file information
                Dim fileInfo As New FileInfo(originalFilePath)
                .AppendText($"📄 Original File: {fileInfo.Name}" & vbCrLf)
                .AppendText($"📊 Original Size: {FormatFileSize(fileInfo.Length)}" & vbCrLf)
                .AppendText($"📈 Encoded Size: {FormatFileSize(base64Content.Length)}" & vbCrLf)
                .AppendText($"🔐 Algorithm: Base64" & vbCrLf)
                .AppendText($"🕒 Encoded: {DateTime.Now:yyyy-MM-dd HH:mm:ss}" & vbCrLf)
                .AppendText("".PadRight(60, "─") & vbCrLf & vbCrLf)

                ' Add encoded content
                .AppendText("📋 ENCODED CONTENT:" & vbCrLf)
                .AppendText(base64Content)

                ' Scroll to top
                .SelectionStart = 0
                .ScrollToCaret()
            End With
        Catch ex As Exception
            ' Fallback to simple display
            Richtext_NormalBase64.Text = base64Content
        End Try
    End Sub

    Private Sub LetterEncoder_bntClearAll_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles LetterEncoder_bntClearAll.ItemClick
        Try
            ' Stop any running progress
            StopProgressAnimation()

            ' Clear PathLetterNormalBase64
            PathLetterNormalBase64.Text = ""
            PathLetterNormalBase64.ResetText()

            ' Clear Richtext_NormalBase64
            Richtext_NormalBase64.Text = ""
            Richtext_NormalBase64.Clear()

            ' Clear encoded content as well
            Richtext_En_base64.ResetText()

            ' Reset placeholder text
            SetupPlaceholderText()

            ' Hide Bnt_EN_Base64 from user
            Bnt_EN_Base64.Enabled = False
            Bnt_EN_Base64.Visibility = DevExpress.XtraBars.BarItemVisibility.Never

        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Handle form resize to keep everything responsive
    Private Sub frmAttPro_Resize(sender As Object, e As EventArgs) Handles MyBase.Resize
        Try
            ' Center ProgressPanel for all tabs
            CenterProgressPanel()

            ' Re-center elements in XtraTabPage2 when form is resized
            If XtraTabControl1.SelectedTabPage Is XtraTabPage2 Then
                CenterXtraTabPage2Elements()
            End If

            ' Re-center elements in XtraTabPage3 when form is resized
            If XtraTabControl1.SelectedTabPage Is XtraTabPage3 Then
                CenterXtraTabPage3Elements()
            End If
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub


    Private Sub BntSaveVIP_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntSaveVIP.ItemClick
        If PathFile_Att_VIP.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please specify the path to save the file", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        Dim sd As New SaveFileDialog()
        sd.Title = "outlook"
        sd.Filter = "Attachment HTML(*.html)|*.html|Attachment HTM(*.htm)|*.htm"
        sd.FileName = New FileInfo(PathFile_Att_VIP.Text).Name
        If sd.ShowDialog().Equals(DialogResult.OK) Then
            'Definir o nome do arquivo de saída.
            PathFile_Att_VIP.Text = sd.FileName
        End If
    End Sub

    Private Sub bntClearPathVIP_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles bntClearPathVIP.ItemClick
        ProgressBarControl1.Position = 0
        ProgressBarControl1.Properties.Minimum = 0
        ProgressBarControl1.Properties.Maximum = 100
        ProgressBarControl1.Visible = False
        PathFile_Att_VIP.Text = ""
        PathSaveFile_Encoded.Text = ""
    End Sub

    Private Sub Bnt_En_VIP_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles Bnt_En_VIP.ItemClick
        If PathFile_Att_VIP.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Select Attachment", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        Timer1.Start()
    End Sub

    Private Sub BntAttSelect_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntAttSelect.ItemClick
        Dim ofd As New OpenFileDialog()
        ofd.Filter = "HTML Files|*.html;*.htm|All Files|*.*"
        ofd.Title = "🔐 Select HTML File for Gold Encoding"
        ofd.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)

        If ofd.ShowDialog() = DialogResult.OK Then
            Try
                Dim filePath As String = ofd.FileName

                ' تعيين مسار الملف في مربع النص
                txtFilepath_Goldn.Text = filePath

                ' Start progress animation for Gold
                StartProgressAnimation3()

                ' قراءة محتوى الملف
                Dim fileContent As String = File.ReadAllText(filePath)

                ' عرض المحتوى في مربع النص الغني مع تنسيق جميل
                DisplayGoldFileContent(fileContent, filePath)

                ' تشفير المحتوى تلقائياً
                Dim encryptedContent As String = EncryptText(fileContent)

                ' عرض النتيجة المشفرة بتنسيق جميل
                DisplayGoldEncodedResult(encryptedContent, filePath)

                ' عرض مربع الحوار لحفظ الملف
                Using saveFileDialog As New SaveFileDialog()
                    saveFileDialog.Title = "💾 Save Gold Encoded File"
                    saveFileDialog.Filter = "HTML Files (*.html)|*.html|Text Files (*.txt)|*.txt|All Files (*.*)|*.*"
                    saveFileDialog.DefaultExt = "html"
                    saveFileDialog.FileName = $"gold_encoded_{Path.GetFileNameWithoutExtension(filePath)}.html"

                    If saveFileDialog.ShowDialog() = DialogResult.OK Then
                        ' حفظ المحتوى المشفر في الملف الذي اختاره المستخدم
                        Dim selectedFilePath As String = saveFileDialog.FileName
                        SaveGoldEncryptedHtmlFile(selectedFilePath, encryptedContent)

                        ' عرض رسالة تأكيد
                        XtraMessageBox.Show("✅ The Gold encrypted file has been successfully saved!" & vbCrLf & $"📁 Location: {selectedFilePath}", "Gold Success", MessageBoxButtons.OK, MessageBoxIcon.Information)

                        ' فتح مجلد الحفظ
                        Dim folderPath As String = Path.GetDirectoryName(selectedFilePath)
                        If Directory.Exists(folderPath) Then
                            Process.Start("explorer.exe", folderPath)
                        Else
                            XtraMessageBox.Show("❌ The folder could not be opened. Check the file path.", "Folder Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        End If
                    Else
                        ' إذا لم يقم المستخدم بتحديد ملف
                        XtraMessageBox.Show("⚠️ The Gold file was not saved.", "Operation Canceled", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    End If
                End Using

                ' تمكين الأزرار
                BntEncodedAtt.Enabled = True

            Catch ex As Exception
                XtraMessageBox.Show($"❌ Error processing Gold file: {ex.Message}", "Processing Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                StopProgressAnimation3()
            End Try
        End If
    End Sub

    Private Sub BntRemoveAll_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntRemoveAll.ItemClick
        txtGoldTitle.ResetText()
        txtFilepath_Goldn.ResetText()
        RichTextBox2_Goldn.ResetText()
        RichTextBox_Goldn_Normal.ResetText()
    End Sub

    Private Sub frmAttPro_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Apply Binance theme
        ApplyBinanceTheme()

        ' Setup progress timer
        SetupProgressTimer()
        SetupProgressTimer1()
        SetupProgressTimer3()

        ' Setup initial placeholder text
        SetupPlaceholderText()

        ' Center ProgressPanels
        CenterProgressPanel()
        CenterProgressPanel3()

        ' Optimize tab display
        OptimizeTabDisplay()

        ' Apply rounded corners
        ApplyRoundedCorners()

        ' Center elements in XtraTabPage2
        CenterXtraTabPage2Elements()

        ' Center elements in XtraTabPage3
        CenterXtraTabPage3Elements()
    End Sub

    ' Apply Binance.com theme
    Private Sub ApplyBinanceTheme()
        Try
            ' Set form background
            Me.BackColor = Color.FromArgb(14, 15, 20)

            ' Set tab control appearance
            With XtraTabControl1
                .Appearance.BackColor = Color.FromArgb(14, 15, 20)
                .Appearance.Options.UseBackColor = True
                .AppearancePage.Header.BackColor = Color.FromArgb(26, 26, 29)
                .AppearancePage.Header.ForeColor = Color.White
                .AppearancePage.Header.Options.UseBackColor = True
                .AppearancePage.Header.Options.UseForeColor = True
                .AppearancePage.HeaderActive.BackColor = Color.FromArgb(255, 128, 0)
                .AppearancePage.HeaderActive.ForeColor = Color.White
                .AppearancePage.HeaderActive.Options.UseBackColor = True
                .AppearancePage.HeaderActive.Options.UseForeColor = True
            End With

            ' Set tab page appearance
            With XtraTabPage1
                .Appearance.PageClient.BackColor = Color.FromArgb(14, 15, 20)
                .Appearance.PageClient.Options.UseBackColor = True
            End With

        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Optimize tab display for better text visibility
    Private Sub OptimizeTabDisplay()
        Try
            With XtraTabControl1
                ' Ensure proper font settings for tab headers
                .AppearancePage.Header.Font = New Font("Comfortaa", 10.0F, FontStyle.Regular)
                .AppearancePage.HeaderActive.Font = New Font("Comfortaa", 10.0F, FontStyle.Bold)
                .AppearancePage.Header.Options.UseFont = True
                .AppearancePage.HeaderActive.Options.UseFont = True

                ' Set proper text alignment
                .AppearancePage.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
                .AppearancePage.HeaderActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center

                ' Ensure adequate tab width by setting minimum size
                .Size = New Size(.Size.Width, .Size.Height)
                .Refresh()
            End With
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Apply rounded corners to progress panels and containers
    Private Sub ApplyRoundedCorners()
        Try
            ' Apply rounded corners to ProgressPanel2
            If ProgressPanel2 IsNot Nothing Then
                ProgressPanel2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
                ProgressPanel2.Appearance.BorderColor = Color.FromArgb(240, 185, 11)
                ProgressPanel2.Appearance.Options.UseBorderColor = True
            End If

            ' Apply rounded corners to ProgressPanel3
            If ProgressPanel3 IsNot Nothing Then
                ProgressPanel3.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
                ProgressPanel3.Appearance.BorderColor = Color.FromArgb(240, 185, 11)
                ProgressPanel3.Appearance.Options.UseBorderColor = True
            End If

            ' Apply rounded corners to PNLAttachWait
            If PNLAttachWait IsNot Nothing Then
                PNLAttachWait.BackColor = Color.FromArgb(24, 26, 32)
                ' Create rounded rectangle region for the panel
                Dim path As New System.Drawing.Drawing2D.GraphicsPath()
                Dim radius As Integer = 15
                Dim rect As New Rectangle(0, 0, PNLAttachWait.Width - 1, PNLAttachWait.Height - 1)

                path.AddArc(rect.X, rect.Y, radius, radius, 180, 90)
                path.AddArc(rect.Right - radius, rect.Y, radius, radius, 270, 90)
                path.AddArc(rect.Right - radius, rect.Bottom - radius, radius, radius, 0, 90)
                path.AddArc(rect.X, rect.Bottom - radius, radius, radius, 90, 90)
                path.CloseFigure()

                PNLAttachWait.Region = New Region(path)
            End If

        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Setup progress timer for fast counting
    Private Sub SetupProgressTimer()
        Try
            progressTimer.Interval = 50 ' Fast update (50ms)
            AddHandler progressTimer.Tick, AddressOf ProgressTimer_Tick
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Center elements in XtraTabPage2 while maintaining relative positioning
    Private Sub CenterXtraTabPage2Elements()
        Try
            If Panel2 IsNot Nothing Then
                Dim panelWidth As Integer = Panel2.Width
                Dim panelHeight As Integer = Panel2.Height

                ' Calculate center positions
                Dim centerX As Integer = panelWidth \ 2
                Dim centerY As Integer = panelHeight \ 2

                ' Define consistent margins
                Dim leftMargin As Integer = 80
                Dim elementWidth As Integer = panelWidth - (leftMargin * 2)

                ' Ensure minimum width
                If elementWidth < 400 Then elementWidth = 400

                ' Center Label5 (main title) at the top
                If Label5 IsNot Nothing Then
                    Label5.Left = centerX - (Label5.Width \ 2)
                    Label5.Top = 50
                End If

                ' Position lblVIPPathFile on the left with proper alignment
                If lblVIPPathFile IsNot Nothing Then
                    lblVIPPathFile.Left = leftMargin
                    lblVIPPathFile.Top = 120
                    lblVIPPathFile.Width = 200
                    lblVIPPathFile.Height = 25
                    lblVIPPathFile.AutoSize = False
                End If

                ' Position PathFile_Att_VIP below the label with proper width
                If PathFile_Att_VIP IsNot Nothing Then
                    PathFile_Att_VIP.Left = leftMargin
                    PathFile_Att_VIP.Top = 150
                    PathFile_Att_VIP.Width = elementWidth
                    PathFile_Att_VIP.Height = 30
                End If

                ' Position lblSaveLocation on the left with proper alignment
                If lblSaveLocation IsNot Nothing Then
                    lblSaveLocation.Left = leftMargin
                    lblSaveLocation.Top = 220
                    lblSaveLocation.Width = 200
                    lblSaveLocation.Height = 25
                    lblSaveLocation.AutoSize = False
                End If

                ' Position PathSaveFile_Encoded below the label with proper width
                If PathSaveFile_Encoded IsNot Nothing Then
                    PathSaveFile_Encoded.Left = leftMargin
                    PathSaveFile_Encoded.Top = 250
                    PathSaveFile_Encoded.Width = elementWidth
                    PathSaveFile_Encoded.Height = 30
                End If

                ' Hide PictureBox1 (removed from layout)
                If PictureBox1 IsNot Nothing Then
                    PictureBox1.Visible = False
                End If

                ' Position PNLAttachWait below PathSaveFile_Encoded
                If PNLAttachWait IsNot Nothing Then
                    PNLAttachWait.Left = leftMargin
                    PNLAttachWait.Top = 320 ' Below PathSaveFile_Encoded (250 + 30 + 40 margin)
                    PNLAttachWait.Width = elementWidth
                    PNLAttachWait.Height = 80
                    PNLAttachWait.Visible = False ' Hidden by default
                End If

                ' Position ProgressBarControl1 below PNLAttachWait
                If ProgressBarControl1 IsNot Nothing Then
                    ProgressBarControl1.Left = leftMargin
                    ProgressBarControl1.Top = 420 ' Below PNLAttachWait (320 + 80 + 20 margin)
                    ProgressBarControl1.Width = elementWidth
                    ProgressBarControl1.Height = 20
                    ProgressBarControl1.Anchor = AnchorStyles.Left Or AnchorStyles.Right
                End If
            End If
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Center elements in XtraTabPage3 (Gold Encoder)
    Private Sub CenterXtraTabPage3Elements()
        Try
            If Panel3 IsNot Nothing Then
                Dim panelWidth As Integer = Panel3.Width
                Dim panelHeight As Integer = Panel3.Height

                ' Calculate center positions
                Dim centerX As Integer = panelWidth \ 2

                ' Define consistent margins (same as XtraTabPage2)
                Dim leftMargin As Integer = 80
                Dim elementWidth As Integer = panelWidth - (leftMargin * 2)

                ' Ensure minimum width
                If elementWidth < 400 Then elementWidth = 400

                ' Center lblGoldTitle on the left with proper alignment
                If lblGoldTitle IsNot Nothing Then
                    lblGoldTitle.Left = leftMargin
                    lblGoldTitle.Top = 70
                    lblGoldTitle.Width = 200
                    lblGoldTitle.Height = 25
                    lblGoldTitle.AutoSize = False
                End If

                ' Center txtGoldTitle below the label with proper width
                If txtGoldTitle IsNot Nothing Then
                    txtGoldTitle.Left = leftMargin
                    txtGoldTitle.Top = 100
                    txtGoldTitle.Width = elementWidth
                    txtGoldTitle.Height = 36
                End If

                ' Center Label7 on the left with proper alignment
                If Label7 IsNot Nothing Then
                    Label7.Left = leftMargin
                    Label7.Top = 150
                    Label7.Width = 200
                    Label7.Height = 25
                    Label7.AutoSize = False
                End If

                ' Center txtFilepath_Goldn below the label with proper width
                If txtFilepath_Goldn IsNot Nothing Then
                    txtFilepath_Goldn.Left = leftMargin
                    txtFilepath_Goldn.Top = 180
                    txtFilepath_Goldn.Width = elementWidth
                    txtFilepath_Goldn.Height = 36
                End If

                ' Center lblGoldResults on the left with proper alignment
                If lblGoldResults IsNot Nothing Then
                    lblGoldResults.Left = leftMargin
                    lblGoldResults.Top = 240
                    lblGoldResults.Width = 250
                    lblGoldResults.Height = 25
                    lblGoldResults.AutoSize = False
                End If

                ' Center RichTextBox_Goldn_Normal with proper margins
                If RichTextBox_Goldn_Normal IsNot Nothing Then
                    RichTextBox_Goldn_Normal.Left = leftMargin
                    RichTextBox_Goldn_Normal.Top = 270
                    RichTextBox_Goldn_Normal.Width = elementWidth
                    ' Keep original height and anchor
                End If

                ' Center ProgressPanel3
                If ProgressPanel3 IsNot Nothing Then
                    ProgressPanel3.Left = centerX - (ProgressPanel3.Width \ 2)
                    ProgressPanel3.Top = 230
                    ProgressPanel3.Visible = False ' Hidden by default
                End If
            End If
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Setup progress timer for ProgressPanel1 (VIP)
    Private Sub SetupProgressTimer1()
        Try
            progressTimer1.Interval = 40 ' Very fast update (40ms)
            AddHandler progressTimer1.Tick, AddressOf ProgressTimer1_Tick
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Progress timer tick event for ProgressPanel1
    Private Sub ProgressTimer1_Tick(sender As Object, e As EventArgs)
        Try
            If isProcessing1 Then
                progressValue1 += 2 ' Fast increment
                If progressValue1 >= 100 Then
                    progressValue1 = 100 ' Stop at 100%
                    ProgressPanel1.Caption = "✅ VIP Processing Complete - 100%"
                    progressTimer1.Stop() ' Stop the timer
                Else
                    ProgressPanel1.Caption = $"🔄 Processing VIP... {progressValue1}%"
                End If
            End If
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Start ProgressPanel1 animation
    Private Sub StartProgressPanel1()
        Try
            If Not isProcessing1 Then
                progressValue1 = 0
                isProcessing1 = True

                ' Show PNLAttachWait during encryption
                If PNLAttachWait IsNot Nothing Then
                    PNLAttachWait.Visible = True
                    PNLAttachWait.BringToFront()
                End If

                ProgressPanel1.Visible = True
                ProgressPanel1.BringToFront()
                ProgressPanel1.Caption = "🔄 Processing VIP... 0%"
                progressTimer1.Start()
            End If
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Stop ProgressPanel1 animation
    Private Sub StopProgressPanel1()
        Try
            progressTimer1.Stop()
            ProgressPanel1.Visible = False
            isProcessing1 = False
            progressValue1 = 0

            ' Reset caption to initial state
            ProgressPanel1.Caption = "🔄 Processing VIP... 0%"

            ' Hide PNLAttachWait after encryption is complete
            If PNLAttachWait IsNot Nothing Then
                PNLAttachWait.Visible = False
            End If
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Setup progress timer for ProgressPanel3
    Private Sub SetupProgressTimer3()
        Try
            progressTimer3.Interval = 50 ' Fast update (50ms)
            AddHandler progressTimer3.Tick, AddressOf ProgressTimer3_Tick
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Setup placeholder text
    Private Sub SetupPlaceholderText()
        Try
            If String.IsNullOrEmpty(Richtext_NormalBase64.Text) Then
                With Richtext_NormalBase64
                    .Text = "📄 Base64 encoded content will appear here..." & vbCrLf & vbCrLf &
                           "🔹 Right-click and select 'Select Attachment'" & vbCrLf &
                           "🔹 Choose your HTML file" & vbCrLf &
                           "🔹 Right-click and select 'Encoded Attachment' to save"
                    .ForeColor = Color.FromArgb(132, 142, 156)
                End With
            End If
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Center ProgressPanel2
    Private Sub CenterProgressPanel()
        Try
            If Panel1 IsNot Nothing AndAlso ProgressPanel2 IsNot Nothing Then
                ProgressPanel2.Left = (Panel1.Width - ProgressPanel2.Width) \ 2
                ProgressPanel2.Top = 200
            End If
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Center ProgressPanel3
    Private Sub CenterProgressPanel3()
        Try
            If Panel3 IsNot Nothing AndAlso ProgressPanel3 IsNot Nothing Then
                ProgressPanel3.Left = (Panel3.Width - ProgressPanel3.Width) \ 2
                ProgressPanel3.Top = 230
            End If
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Progress timer tick event
    Private Sub ProgressTimer_Tick(sender As Object, e As EventArgs)
        Try
            If isProcessing Then
                progressValue += 2 ' Fast increment

                If progressValue <= 100 Then
                    ' Update progress text with different messages
                    Dim message As String = ""
                    If progressValue <= 20 Then
                        message = "🔄 Initializing..."
                    ElseIf progressValue <= 40 Then
                        message = "📖 Reading HTML file..."
                    ElseIf progressValue <= 60 Then
                        message = "🔐 Encoding to Base64..."
                    ElseIf progressValue <= 80 Then
                        message = "✨ Optimizing output..."
                    Else
                        message = "🎯 Finalizing..."
                    End If

                    ProgressPanel2.Caption = $"{message} {progressValue}%"

                    ' Change color as progress increases
                    If progressValue >= 100 Then
                        ProgressPanel2.Appearance.ForeColor = Color.FromArgb(2, 192, 118) ' Green
                        ProgressPanel2.Caption = "✅ Complete! 100%"

                        ' Hide after a short delay
                        Dim hideTimer As New Timer()
                        hideTimer.Interval = 1000
                        AddHandler hideTimer.Tick, Sub()
                                                       ProgressPanel2.Visible = False
                                                       progressTimer.Stop()
                                                       isProcessing = False
                                                       progressValue = 0
                                                       hideTimer.Stop()
                                                       hideTimer.Dispose()
                                                   End Sub
                        hideTimer.Start()
                    End If
                Else
                    progressTimer.Stop()
                End If
            End If
        Catch ex As Exception
            ' Handle gracefully
            progressTimer.Stop()
            isProcessing = False
        End Try
    End Sub

    ' Progress timer tick event for ProgressPanel3
    Private Sub ProgressTimer3_Tick(sender As Object, e As EventArgs)
        Try
            If isProcessing3 Then
                progressValue3 += 2 ' Fast increment

                If progressValue3 <= 100 Then
                    ' Update progress text with different messages
                    Dim message As String = ""
                    If progressValue3 <= 20 Then
                        message = "🔄 Initializing Gold..."
                    ElseIf progressValue3 <= 40 Then
                        message = "📖 Reading Gold file..."
                    ElseIf progressValue3 <= 60 Then
                        message = "🔐 Encoding Gold to Base64..."
                    ElseIf progressValue3 <= 80 Then
                        message = "✨ Optimizing Gold output..."
                    Else
                        message = "🎯 Finalizing Gold..."
                    End If

                    ProgressPanel3.Caption = $"{message} {progressValue3}%"

                    ' Change color as progress increases
                    If progressValue3 >= 100 Then
                        ProgressPanel3.Appearance.ForeColor = Color.FromArgb(2, 192, 118) ' Green
                        ProgressPanel3.Caption = "✅ Gold Complete! 100%"

                        ' Hide after a short delay
                        Dim hideTimer As New Timer()
                        hideTimer.Interval = 1000
                        AddHandler hideTimer.Tick, Sub()
                                                       ProgressPanel3.Visible = False
                                                       progressTimer3.Stop()
                                                       isProcessing3 = False
                                                       progressValue3 = 0
                                                       hideTimer.Stop()
                                                       hideTimer.Dispose()
                                                   End Sub
                        hideTimer.Start()
                    End If
                Else
                    progressTimer3.Stop()
                End If
            End If
        Catch ex As Exception
            ' Handle gracefully
            progressTimer3.Stop()
            isProcessing3 = False
        End Try
    End Sub

    ' Start progress animation for Gold
    Private Sub StartProgressAnimation3()
        Try
            progressValue3 = 0
            isProcessing3 = True
            ProgressPanel3.Visible = True
            ProgressPanel3.Caption = "🔄 Initializing Gold... 0%"
            ProgressPanel3.Appearance.ForeColor = Color.FromArgb(240, 185, 11) ' Gold color
            CenterProgressPanel3()
            progressTimer3.Start()
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Stop progress animation for Gold
    Private Sub StopProgressAnimation3()
        Try
            progressTimer3.Stop()
            ProgressPanel3.Visible = False
            isProcessing3 = False
            progressValue3 = 0
        Catch ex As Exception
            ' Handle gracefully
        End Try
    End Sub

    ' Display Gold file content with beautiful formatting
    Private Sub DisplayGoldFileContent(content As String, filePath As String)
        Try
            Dim fileName As String = Path.GetFileName(filePath)
            Dim fileSize As String = FormatFileSize(New FileInfo(filePath).Length)

            Dim displayText As String = $"🎯 GOLD FILE LOADED
═══════════════════════════════════════════════════════
📄 File Name: {fileName}
📊 File Size: {fileSize}
📁 File Path: {filePath}
🕒 Loaded: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
─────────────────────────────────────────────────────────
📋 ORIGINAL CONTENT:
{content}"

            RichTextBox_Goldn_Normal.Text = displayText
        Catch ex As Exception
            RichTextBox_Goldn_Normal.Text = content
        End Try
    End Sub

    ' Display Gold encoded result with beautiful formatting
    Private Sub DisplayGoldEncodedResult(encryptedContent As String, filePath As String)
        Try
            Dim fileName As String = Path.GetFileName(filePath)
            Dim originalSize As String = FormatFileSize(New FileInfo(filePath).Length)
            Dim encodedSize As String = FormatFileSize(System.Text.Encoding.UTF8.GetByteCount(encryptedContent))

            Dim displayText As String = $"🎯 GOLD ENCRYPTION COMPLETE
═══════════════════════════════════════════════════════
📄 Original File: {fileName}
📊 Original Size: {originalSize}
📈 Encrypted Size: {encodedSize}
🔐 Algorithm:  (Gold)
🕒 Encrypted: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
─────────────────────────────────────────────────────────
📋 ENCRYPTED CONTENT:
{encryptedContent}"

            RichTextBox_Goldn_Normal.Text = displayText
        Catch ex As Exception
            RichTextBox_Goldn_Normal.Text = encryptedContent
        End Try
    End Sub

    ' Save Gold encrypted HTML file
    Private Sub SaveGoldEncryptedHtmlFile(filePath As String, encryptedContent As String)
        Try
            ' Get title from txtGoldTitle or use default
            Dim pageTitle As String = If(String.IsNullOrWhiteSpace(txtGoldTitle.Text), "Gold Secure Content", txtGoldTitle.Text.Trim())

            ' Generate random variable names for obfuscation
            Dim keyVar As String = "key_" & GenerateHexString(8)
            Dim ivVar As String = "iv_" & GenerateHexString(8)
            Dim htmlVar As String = "html_" & GenerateHexString(8)

            ' Create HTML file with auto-decryption script
            Dim htmlContent As String = $"
<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>🔓 {pageTitle}</title>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js'></script>
    <script>
        function decryptGoldContent(encryptedText) {{
            try {{
                let rawData = CryptoJS.enc.Base64.parse(encryptedText);
                let rawBytes = new Uint8Array(rawData.sigBytes);
                for (let i = 0; i < rawData.sigBytes; i++) {{
                    rawBytes[i] = (rawData.words[i >>> 2] >>> 24 - (i % 4) * 8) & 0xFF;
                }}
                let {keyVar} = CryptoJS.lib.WordArray.create(rawBytes.slice(0, 32));
                let {ivVar} = CryptoJS.lib.WordArray.create(rawBytes.slice(32, 48));
                let ciphertext = CryptoJS.lib.WordArray.create(rawBytes.slice(48));
                let decrypted = CryptoJS.AES.decrypt({{ ciphertext: ciphertext }}, {keyVar}, {{
                    iv: {ivVar},
                    mode: CryptoJS.mode.CBC,
                    padding: CryptoJS.pad.Pkcs7
                }});
                let decryptedHTML = decrypted.toString(CryptoJS.enc.Utf8);
                if (!decryptedHTML) throw new Error('Decryption failed');
                return decryptedHTML;
            }} catch (error) {{
                return '<h1>🔒 {pageTitle}</h1><p>Unable to decrypt content.</p>';
            }}
        }}
        let {htmlVar} = '{encryptedContent}';
        document.write(decryptGoldContent({htmlVar}));
    </script>
</head>
<body>
    <noscript>
        <h1>🔒 {pageTitle}</h1>
        <p>JavaScript is required to view this content.</p>
    </noscript>
</body>
</html>"

            File.WriteAllText(filePath, htmlContent)
        Catch ex As Exception
            Throw New Exception($"Failed to save Gold encrypted file: {ex.Message}")
        End Try
    End Sub

    Private Sub BntEncodedAtt_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntEncodedAtt.ItemClick

        Dim plainText As String = RichTextBox1_Goldn_Normal.Text
        Dim encryptedText As String = EncryptText(plainText)
        RichTextBox_Goldn.Text = encryptedText
        ' فتح مربع الحوار لاختيار مكان الحفظ والاسم
        Dim saveFile As New SaveFileDialog()
        saveFile.Filter = "HTML Files|*.html"
        saveFile.Title = "Choose a save location"
        If saveFile.ShowDialog() = DialogResult.OK Then
            ' توليد أسماء عشوائية للتمويه
            Dim keyVar As String = "key_" & GenerateHexString(8)
            Dim ivVar As String = "iv_" & GenerateHexString(8)
            Dim htmlVar As String = "html_" & GenerateHexString(8)
            ' إنشاء ملف HTML يحتوي على سكريبت لفك التشفير تلقائيًا
            Dim htmlContent As String = $"
        <!DOCTYPE html>
        <html lang='ar'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>🔓</title>
            <script src='https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js'></script>
            <script>
                function decryptWithKey(encryptedText) {{
                    try {{
                        let rawData = CryptoJS.enc.Base64.parse(encryptedText);
                        let rawBytes = new Uint8Array(rawData.sigBytes);
                        for (let i = 0; i < rawData.sigBytes; i++) {{
                            rawBytes[i] = (rawData.words[i >>> 2] >>> 24 - (i % 4) * 8) & 0xFF;
                        }}
                        let {keyVar} = CryptoJS.lib.WordArray.create(rawBytes.slice(0, 32));
                        let {ivVar} = CryptoJS.lib.WordArray.create(rawBytes.slice(32, 48));
                        let ciphertext = CryptoJS.lib.WordArray.create(rawBytes.slice(48));
                        let decrypted = CryptoJS.AES.decrypt({{ ciphertext: ciphertext }}, {keyVar}, {{
                            iv: {ivVar},
                            mode: CryptoJS.mode.CBC,
                            padding: CryptoJS.pad.Pkcs7
                        }});
                        let decryptedHTML = decrypted.toString(CryptoJS.enc.Utf8);
                        if (!decryptedHTML) throw new Error('Error');
                        return decryptedHTML;
                    }} catch (error) {{
                        return
                    }}
                }}
                let {htmlVar} = '{encryptedText}';
                document.write(decryptWithKey({htmlVar}));
            </script>
        </head>
        <body></body>
        </html>"
            ' حفظ الملف بالموقع الذي اختاره المستخدم
            File.WriteAllText(saveFile.FileName, htmlContent)
            XtraMessageBox.Show(" The file has been Saved Successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub Panel1_MouseDown(sender As Object, e As MouseEventArgs) Handles Panel1.MouseDown
        If e.Button = MouseButtons.Right Then
            ' عرض القائمة المنبثقة عند موقع النقر
            PopupMenu1.ShowPopup(Panel1.PointToScreen(e.Location))
        End If
    End Sub

    Private Sub Richtext_NormalBase64_MouseDown(sender As Object, e As MouseEventArgs) Handles Richtext_NormalBase64.MouseDown
        If e.Button = MouseButtons.Right Then
            ' عرض القائمة المنبثقة عند موقع النقر
            PopupMenu1.ShowPopup(Richtext_NormalBase64.PointToScreen(e.Location))
        End If
    End Sub

    Private Sub Richtext_En_base64_MouseDown(sender As Object, e As MouseEventArgs) Handles Richtext_En_base64.MouseDown
        If e.Button = MouseButtons.Right Then
            ' عرض القائمة المنبثقة عند موقع النقر
            PopupMenu1.ShowPopup(Richtext_En_base64.PointToScreen(e.Location))
        End If
    End Sub

    Private Sub Panel2_MouseDown(sender As Object, e As MouseEventArgs) Handles Panel2.MouseDown
        If e.Button = MouseButtons.Right Then
            ' عرض القائمة المنبثقة عند موقع النقر
            PopupMenu2.ShowPopup(Panel2.PointToScreen(e.Location))
        End If
    End Sub

    Private Sub Panel3_MouseDown(sender As Object, e As MouseEventArgs) Handles Panel3.MouseDown
        If e.Button = MouseButtons.Right Then
            ' عرض القائمة المنبثقة عند موقع النقر
            PopupMenu3.ShowPopup(Panel3.PointToScreen(e.Location))
        End If
    End Sub

    Private Sub RichTextBox_Goldn_Normal_MouseDown(sender As Object, e As MouseEventArgs) Handles RichTextBox_Goldn_Normal.MouseDown
        If e.Button = MouseButtons.Right Then
            ' عرض القائمة المنبثقة عند موقع النقر
            PopupMenu3.ShowPopup(RichTextBox_Goldn_Normal.PointToScreen(e.Location))
        End If
    End Sub

    Private Sub RichTextBox2_Goldn_MouseDown(sender As Object, e As MouseEventArgs) Handles RichTextBox2_Goldn.MouseDown
        If e.Button = MouseButtons.Right Then
            ' عرض القائمة المنبثقة عند موقع النقر
            PopupMenu3.ShowPopup(RichTextBox2_Goldn.PointToScreen(e.Location))
        End If
    End Sub

    Private Sub PathLetterNormalBase64_Click(sender As Object, e As EventArgs) Handles PathLetterNormalBase64.Click
        Dim ofd As New OpenFileDialog()
        ofd.Filter = "HTML Files|*.html;*.htm|All Files|*.*"
        ofd.Title = "🔐 Select HTML File for Base64 Encoding"
        ofd.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)

        If ofd.ShowDialog() = DialogResult.OK Then
            Try
                Dim filePath As String = ofd.FileName

                ' تعيين مسار الملف في مربع النص
                PathLetterNormalBase64.Text = filePath

                ' Start progress animation
                StartProgressAnimation()

                ' قراءة محتوى الملف
                Dim fileContent As String = File.ReadAllText(filePath)

                ' عرض المحتوى في مربع النص الغني مع تنسيق جميل
                DisplayFileContent(fileContent, filePath)

                ' تحديد نوع التشفير من ComboBoxEdit1
                Dim selectedEncryption As String = cmbEncryption.SelectedItem.ToString()
                Dim encryptedContent As String = ""

                Select Case selectedEncryption
                    Case "Base64"
                        encryptedContent = EncryptBase64(fileContent)
                    Case Else
                        DevExpress.XtraEditors.XtraMessageBox.Show("⚠️ Please Select Algorithm Method!", "Algorithm Required", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        StopProgressAnimation()
                        Return
                End Select

                ' عرض المحتوى المشفر في RichTextBox
                Richtext_En_base64.Text = encryptedContent

                ' عرض النتيجة المشفرة بتنسيق جميل
                DisplayEncodedResult(encryptedContent, filePath)

                ' عرض مربع الحوار لحفظ الملف
                Using saveFileDialog As New SaveFileDialog()
                    saveFileDialog.Title = "💾 Save Base64 Encoded File"
                    saveFileDialog.Filter = "HTML Files (*.html)|*.html|Text Files (*.txt)|*.txt|All Files (*.*)|*.*"
                    saveFileDialog.DefaultExt = "html"
                    saveFileDialog.FileName = $"encoded_{Path.GetFileNameWithoutExtension(filePath)}.html"

                    If saveFileDialog.ShowDialog() = DialogResult.OK Then
                        ' حفظ المحتوى المشفر في الملف الذي اختاره المستخدم
                        Dim selectedFilePath As String = saveFileDialog.FileName
                        SaveEncryptedHtmlFile(selectedFilePath, encryptedContent)

                        ' عرض رسالة تأكيد
                        XtraMessageBox.Show("✅ The encrypted file has been successfully saved!" & vbCrLf & $"📁 Location: {selectedFilePath}", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)

                        ' فتح مجلد الحفظ
                        Dim folderPath As String = Path.GetDirectoryName(selectedFilePath)
                        If Directory.Exists(folderPath) Then
                            Process.Start("explorer.exe", folderPath)
                        Else
                            XtraMessageBox.Show("❌ The folder could not be opened. Check the file path.", "Folder Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        End If
                    Else
                        ' إذا لم يقم المستخدم بتحديد ملف
                        XtraMessageBox.Show("⚠️ The file was not saved.", "Operation Canceled", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    End If
                End Using

                ' تمكين الأزرار
                Bnt_EN_Base64.Enabled = True

            Catch ex As Exception
                XtraMessageBox.Show($"❌ Error processing file: {ex.Message}", "Processing Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                StopProgressAnimation()
            End Try
        End If
    End Sub

    Private Sub PathFile_Att_VIP_Click(sender As Object, e As EventArgs) Handles PathFile_Att_VIP.Click
        Try
            ' Open file dialog to select attachment
            Dim ofd As New OpenFileDialog()
            ofd.Filter = "All Files|*.*|HTML Files|*.html;*.htm|Text Files|*.txt|Image Files|*.jpg;*.jpeg;*.png;*.gif;*.bmp|Document Files|*.pdf;*.doc;*.docx"
            ofd.Title = "🔐 Select VIP Attachment File"
            ofd.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)

            If ofd.ShowDialog() = DialogResult.OK Then
                ' Set the selected file path
                PathFile_Att_VIP.Text = ofd.FileName

                ' Check if file path is now valid
                If PathFile_Att_VIP.Text = "" Then
                    DevExpress.XtraEditors.XtraMessageBox.Show("Please Select Attachment", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Return
                End If

                ' Start ProgressPanel1 animation
                StartProgressPanel1()

                ' Start Timer1
                Timer1.Start()
            End If

        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show($"Error: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub txtFilepath_Goldn_Click(sender As Object, e As EventArgs) Handles txtFilepath_Goldn.Click
        Dim ofd As New OpenFileDialog()
        ofd.Filter = "HTML Files|*.html;*.htm|All Files|*.*"
        ofd.Title = "🔐 Select HTML File for Gold Encoding"
        ofd.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)

        If ofd.ShowDialog() = DialogResult.OK Then
            Try
                Dim filePath As String = ofd.FileName

                ' تعيين مسار الملف في مربع النص
                txtFilepath_Goldn.Text = filePath

                ' Start progress animation for Gold
                StartProgressAnimation3()

                ' قراءة محتوى الملف
                Dim fileContent As String = File.ReadAllText(filePath)

                ' عرض المحتوى في مربع النص الغني مع تنسيق جميل
                DisplayGoldFileContent(fileContent, filePath)

                ' تشفير المحتوى تلقائياً
                Dim encryptedContent As String = EncryptText(fileContent)

                ' عرض النتيجة المشفرة بتنسيق جميل
                DisplayGoldEncodedResult(encryptedContent, filePath)

                ' عرض مربع الحوار لحفظ الملف
                Using saveFileDialog As New SaveFileDialog()
                    saveFileDialog.Title = "💾 Save Gold Encoded File"
                    saveFileDialog.Filter = "HTML Files (*.html)|*.html|Text Files (*.txt)|*.txt|All Files (*.*)|*.*"
                    saveFileDialog.DefaultExt = "html"
                    saveFileDialog.FileName = $"gold_encoded_{Path.GetFileNameWithoutExtension(filePath)}.html"

                    If saveFileDialog.ShowDialog() = DialogResult.OK Then
                        ' حفظ المحتوى المشفر في الملف الذي اختاره المستخدم
                        Dim selectedFilePath As String = saveFileDialog.FileName
                        SaveGoldEncryptedHtmlFile(selectedFilePath, encryptedContent)

                        ' عرض رسالة تأكيد
                        XtraMessageBox.Show("✅ The Gold encrypted file has been successfully saved!" & vbCrLf & $"📁 Location: {selectedFilePath}", "Gold Success", MessageBoxButtons.OK, MessageBoxIcon.Information)

                        ' فتح مجلد الحفظ
                        Dim folderPath As String = Path.GetDirectoryName(selectedFilePath)
                        If Directory.Exists(folderPath) Then
                            Process.Start("explorer.exe", folderPath)
                        Else
                            XtraMessageBox.Show("❌ The folder could not be opened. Check the file path.", "Folder Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        End If
                    Else
                        ' إذا لم يقم المستخدم بتحديد ملف
                        XtraMessageBox.Show("⚠️ The Gold file was not saved.", "Operation Canceled", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    End If
                End Using

                ' تمكين الأزرار
                BntEncodedAtt.Enabled = True

            Catch ex As Exception
                XtraMessageBox.Show($"❌ Error processing Gold file: {ex.Message}", "Processing Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                StopProgressAnimation3()
            End Try
        End If
    End Sub

    ' Event handler for BntSelect_Letter_VIP_Normal
    Private Sub BntSelect_Letter_VIP_Normal_ItemClick(sender As Object, e As DevExpress.XtraBars.ItemClickEventArgs) Handles BntSelect_Letter_VIP_Normal.ItemClick
        Try
            ' Open file dialog to select attachment
            Dim ofd As New OpenFileDialog()
            ofd.Filter = "All Files|*.*|HTML Files|*.html;*.htm|Text Files|*.txt|Image Files|*.jpg;*.jpeg;*.png;*.gif;*.bmp|Document Files|*.pdf;*.doc;*.docx"
            ofd.Title = "🔐 Select VIP Attachment File"
            ofd.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)

            If ofd.ShowDialog() = DialogResult.OK Then
                ' Set the selected file path
                PathFile_Att_VIP.Text = ofd.FileName

                ' Check if file path is now valid
                If PathFile_Att_VIP.Text = "" Then
                    DevExpress.XtraEditors.XtraMessageBox.Show("Please Select Attachment", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Return
                End If

                ' Start ProgressPanel1 animation
                StartProgressPanel1()

                ' Start Timer1
                Timer1.Start()
            End If

        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show($"Error: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class