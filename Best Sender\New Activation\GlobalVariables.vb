﻿Imports System.Data.SqlClient
Imports System.IO
Imports System.Security.Permissions
Imports System.Net.Mail
Imports System.Net
Imports MicrovisionActivation
Imports DevExpress.XtraEditors
Module GlobalVariables
    Public MachineSerial As String = ""
    Public ActivationKeyCode As String = ""
    Public ActivationKeyOwner As String = ""
    Public ActivateionType As String = ""
    Public ActivationDateTime As DateTime
    Public ActivationExpireDate As DateTime
    Public ActivationRemainDays As String = ""
    Public activeflg As Boolean = False
    Public activation_flg As Integer = 0
    Public ActivationStatus As Boolean = False
    Public TimeDetectFlg As Boolean = False
    Public ActivationTrailDays As String = ""
    Public ApplicationName_ As String = My.Settings.appname
    Public ApplicationkEYPASSWORD_ As String = My.Settings.hastring
    'Public ConnectionString_ As String = "Data Source=SQL5097.site4now.net;Initial Catalog=db_a9317e_trustedseller;User id=db_a9317e_trustedseller_admin;Password=*********"
    Public ConnectionString_ As String = My.Settings.appver
    Public ActiveConn As New SqlConnection
    Public file_list As New List(Of String)
    Public fileAccessDenied_ As New List(Of String)
    '=====================================================
    Public WarningString As String = ""
    Public PermissionList As String = ""
    '=====================================================
    Public ActivationModule As String = ""
    Sub SendEmail(ActivationCode As String, userName As String)
        Try
            If ActiveConn.State = ConnectionState.Closed Then
                ActiveConn.ConnectionString = ConnectionString_
                ActiveConn.Open()
            End If
            Dim RealOwnerString As String = ""
            Dim adp As New SqlDataAdapter("select * from activationcodes where ActivationCode=N'" & ActivationCode & "'", ActiveConn)
            Dim dt As New DataTable
            adp.Fill(dt)
            If dt.Rows.Count = 0 Then
                RealOwnerString = "Unable to get Owner info."
            Else
                RealOwnerString = "Software Name: " & dt.Rows(0).Item("app_name") & vbNewLine
                RealOwnerString = RealOwnerString & "Activation Key: " & dt.Rows(0).Item("ActivationCode") & vbNewLine
                RealOwnerString = RealOwnerString & "Owner Name: " & dt.Rows(0).Item("username") & vbNewLine
                RealOwnerString = RealOwnerString & "Owner Machine Serial: " & dt.Rows(0).Item("User_Serial") & vbNewLine
                RealOwnerString = RealOwnerString & "Owner Activation Status: " & IIf(dt.Rows(0).Item("Activation_Status"), "Active", "In-Active") & vbNewLine
                RealOwnerString = RealOwnerString & "Owner License Status: " & IIf(dt.Rows(0).Item("lic_flg"), "Active", "In-Active") & vbNewLine
            End If
            adp.Dispose()
            dt.Dispose()
            '=====================================================================================================================================
            Dim Mailto As String = ""
            Dim mailfrom As String = ""
            Dim MailfromPassword As String = ""
            Dim smtp_ As String = ""
            Dim port_ As Integer = 0
            adp = New SqlDataAdapter("select * from email_info", ActiveConn)
            dt = New DataTable
            adp.Fill(dt)
            If dt.Rows.Count > 0 Then
                Mailto = dt.Rows(0).Item("mailto").ToString()
                mailfrom = dt.Rows(0).Item("mailfrom").ToString()
                MailfromPassword = dt.Rows(0).Item("MailfromPassword").ToString()
                smtp_ = dt.Rows(0).Item("smtp").ToString()
                port_ = dt.Rows(0).Item("port")
            End If
            Dim onlinetime = UserInfo.Machine_IP.GetNISTTime("utcnist.colorado.edu").ToString()
            Dim ARR = Split(onlinetime, " ")
            Dim ur As New Uri("http://www.find-ip-address.org/")
            Dim IPdata As String = UserInfo.Machine_IP.DownloadData(ur)
            Dim IPString As String = ""
            If Not IPdata Is Nothing Then
                UserInfo.Machine_IP.ipLookup = UserInfo.Machine_IP.ExtractSubject(IPdata, "My IP Country Name:", "IP Address Lookup Location")
                UserInfo.Machine_IP.flagLocation = UserInfo.Machine_IP.ExtractSubject(UserInfo.Machine_IP.ipLookup, "</font>&nbsp;&nbsp;<img src='", "'><br><strong>My IP Country Continent<")
                IPString = "Country: " & UserInfo.Machine_IP.country() & vbNewLine & "PhonePrefix: " & UserInfo.Machine_IP.phonePrefix(UserInfo.Machine_IP.countryCode) & vbNewLine & "City: " & UserInfo.Machine_IP.city() & vbNewLine & "Region: " & UserInfo.Machine_IP.Region() & vbNewLine & "Public IP: " & UserInfo.Machine_IP.publicIP() & vbNewLine & "ISP: " & UserInfo.Machine_IP.isp() & vbNewLine & "Broadband: " & UserInfo.Machine_IP.netBroadband() & vbNewLine & "Latitude: " & UserInfo.Machine_IP.latitude() & "° N" & vbNewLine & "Longtitude: " & UserInfo.Machine_IP.longtitude() & "° E"
            End If
            Dim message As New MailMessage(mailfrom, Mailto)
            message.Subject = "Illegal activation - " & WarningString 'subject.Text
            Dim str As String = "An illegal activation attempt was detected for a device that is not authorized to activate a copy of BEST SENDER software." & vbNewLine & vbNewLine
            Dim strHostName = "Un-authorized User: " & userName & vbNewLine & "Machine Name: " & System.Net.Dns.GetHostName()
            str = str & "Activation Legal Owner info: " & vbNewLine & RealOwnerString & vbNewLine & vbNewLine
            str = str & "Un-authorized info: " & vbNewLine & strHostName & vbNewLine & "Machine Serial No.: " & My.Settings.PROSTRING & vbNewLine
            str = str & IPString & vbNewLine & vbNewLine
            str = str & "Operating Time: " & Now & " (local time)   -   " & ARR(0) & "  " & ARR(1) & " " & ARR(2) & " (utc time)"
            message.Body = str
            message.IsBodyHtml = False
            Dim smtp As New SmtpClient
            smtp.DeliveryMethod = SmtpDeliveryMethod.Network
            smtp.UseDefaultCredentials = False
            Dim basicAuthenticationInfo As New System.Net.NetworkCredential(mailfrom, MailfromPassword)
            smtp.Credentials = basicAuthenticationInfo
            smtp.Host = smtp_
            smtp.Port = port_
            smtp.EnableSsl = True
            smtp.Send(message)
        Catch ex As Exception
        End Try
    End Sub
    Sub Fill_Files_Names_Array(ByVal Folder_Path As String, Optional ByVal include_EXE As Boolean = False)
        file_list.Clear()
        If include_EXE = True Then
            For Each file As String In Directory.GetFiles(Folder_Path)
                If Not InStr(file, New FileInfo(Application.ExecutablePath).Name) = 0 Then
                    file_list.Add(file)
                    Exit For
                End If
            Next
        End If
        For Each file As String In Directory.GetFiles(Folder_Path)
            If InStr(file, New FileInfo(Application.ExecutablePath).Name) = 0 And
                InStr(file, "destruct.bat") = 0 Then
                file_list.Add(file)
            End If
        Next
    End Sub
    Private Sub SelfDestruct(ByVal folder_path As String)
        Dim procDestruct As Process = New Process()
        Dim strName As String = "destruct.bat"
        Dim strPath As String = Path.Combine(Directory.GetCurrentDirectory(), strName)
        Dim swDestruct As StreamWriter = New StreamWriter(strPath)
        Fill_Files_Names_Array(folder_path, True)
        For Each fileName As String In file_list
            swDestruct.WriteLine("attrib """ & New FileInfo(fileName).Name & """" & " -a -s -r -h")
            swDestruct.WriteLine(":Repeat")
            swDestruct.WriteLine("del " & """" & New FileInfo(fileName).Name & """")
            swDestruct.WriteLine("if exist """ & New FileInfo(fileName).Name & """" & " goto Repeat")
        Next
        swDestruct.WriteLine("del """ & strName & """")
        swDestruct.Close()
        procDestruct.StartInfo.FileName = "destruct.bat"
        procDestruct.StartInfo.CreateNoWindow = True
        procDestruct.StartInfo.UseShellExecute = False
        Try
            procDestruct.Start()
        Catch ex As Exception
            Process.GetCurrentProcess().Kill()
        End Try
    End Sub
    Sub DeleteAllFiles(ByVal folder_path As String)
        Try
            If Directory.Exists(folder_path) Then
                Dim childFolders As String() = Directory.GetDirectories(folder_path)
                For Each childFolder As String In childFolders
                    ' Get all files in the child folder
                    For Each file_ As String In Directory.GetFiles(childFolder)
                        ' Delete the file
                        File.Delete(file_)
                    Next
                    ' Delete the child folder
                    Directory.Delete(childFolder, True)
                Next
                ' Clear the array of Access denied files when delete from the main folder
                fileAccessDenied_.Clear()
repeat:
                Fill_Files_Names_Array(folder_path)
                ' remove the Access denied files from the main folder remaining files array
                For Each fileDenied As String In fileAccessDenied_
                    If File.Exists(fileDenied) Then file_list.Remove(fileDenied)
                Next
                ' delete the remaining files in the main folder 
                For i = 0 To file_list.Count - 1
                    If InStr(file_list.Item(i), New FileInfo(Application.ExecutablePath).Name) = 0 Then
                        fileAccessDenied_.Add(file_list.Item(i))
                        File.Delete(file_list.Item(i))
                    End If
                Next
                ' Self destrcut the exe and the remaining access denied files 
                SelfDestruct(folder_path)
                Process.GetCurrentProcess().Kill()
            End If
        Catch ex As Exception
            GoTo repeat
        End Try
    End Sub
    Function FormAccess(ByVal PermissionTag As String, Optional ByVal bnt As DevExpress.XtraEditors.SimpleButton = Nothing) As Boolean
        Dim arr_ = Split(ActivationModule, " | ")
        If arr_.Contains("Premium") Then
            Return True
        ElseIf arr_.Contains(PermissionTag) Then
            Return True
        Else
            If IsNothing(bnt) = False Then
                bnt.ForeColor = Color.White
                bnt.Appearance.BackColor = Color.FromArgb(48, 55, 73)
                bnt.Appearance.BackColor2 = Color.FromArgb(48, 55, 73)
            End If
            DevExpress.XtraEditors.XtraMessageBox.Show("Your license does not include this service", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If
    End Function
    Public Function ModuleLogo(ActivationModule_)
        Dim arr_ = Split(ActivationModule_, " | ")
        For I = 0 To arr_.Length
            If UCase(arr_(I)) = UCase("None") Then Return "None"
            If UCase(arr_(I)) = UCase("Premium") Then Return "Premium"
            Return "Customized"
        Next
        Return "Premium"
    End Function
    Public Function GET_LICENSE_PLAN_STRING(ActivationModule_)
        Dim arr_ = Split(ActivationModule_, " | ")
        For I = 0 To arr_.Length
            If UCase(arr_(I)) = UCase("None") Then Return "License Plan: None"
            If UCase(arr_(I)) = UCase("Premium") Then Return "License Plan: Premium"
            Return "License Plan: Customized"
        Next
        Return "License Plan: Premium"
    End Function
End Module
