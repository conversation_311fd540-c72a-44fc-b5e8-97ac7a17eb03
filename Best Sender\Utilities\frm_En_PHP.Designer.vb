﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frm_En_PHP
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.SeparatorControl1 = New DevExpress.XtraEditors.SeparatorControl()
        Me.GroupControl6 = New DevExpress.XtraEditors.GroupControl()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.Richtext_Encoded = New System.Windows.Forms.RichTextBox()
        Me.txtPath = New DevExpress.XtraEditors.TextEdit()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl6.SuspendLayout()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.txtPath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'SeparatorControl1
        '
        Me.SeparatorControl1.LineThickness = 1
        Me.SeparatorControl1.Location = New System.Drawing.Point(16, 52)
        Me.SeparatorControl1.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl1.Name = "SeparatorControl1"
        Me.SeparatorControl1.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl1.Size = New System.Drawing.Size(936, 34)
        Me.SeparatorControl1.TabIndex = 498
        '
        'GroupControl6
        '
        Me.GroupControl6.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Bold)
        Me.GroupControl6.AppearanceCaption.Options.UseFont = True
        Me.GroupControl6.Controls.Add(Me.LayoutControl1)
        Me.GroupControl6.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControl6.Location = New System.Drawing.Point(0, 0)
        Me.GroupControl6.Margin = New System.Windows.Forms.Padding(4, 1, 4, 1)
        Me.GroupControl6.Name = "GroupControl6"
        Me.GroupControl6.Size = New System.Drawing.Size(972, 602)
        Me.GroupControl6.TabIndex = 502
        Me.GroupControl6.Text = "PHP Obfuscation"
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.Richtext_Encoded)
        Me.LayoutControl1.Controls.Add(Me.txtPath)
        Me.LayoutControl1.Controls.Add(Me.SeparatorControl1)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(2, 29)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(968, 571)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'Richtext_Encoded
        '
        Me.Richtext_Encoded.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.Richtext_Encoded.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Richtext_Encoded.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.Richtext_Encoded.ForeColor = System.Drawing.Color.White
        Me.Richtext_Encoded.Location = New System.Drawing.Point(16, 92)
        Me.Richtext_Encoded.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Richtext_Encoded.Name = "Richtext_Encoded"
        Me.Richtext_Encoded.Size = New System.Drawing.Size(936, 463)
        Me.Richtext_Encoded.TabIndex = 68
        Me.Richtext_Encoded.Text = ""
        '
        'txtPath
        '
        Me.txtPath.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtPath.EditValue = ""
        Me.txtPath.Location = New System.Drawing.Point(129, 16)
        Me.txtPath.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtPath.Name = "txtPath"
        Me.txtPath.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtPath.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtPath.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtPath.Properties.Appearance.Options.UseBackColor = True
        Me.txtPath.Properties.Appearance.Options.UseFont = True
        Me.txtPath.Properties.Appearance.Options.UseForeColor = True
        Me.txtPath.Properties.NullValuePrompt = "Path File"
        Me.txtPath.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtPath.Size = New System.Drawing.Size(823, 30)
        Me.txtPath.StyleController = Me.LayoutControl1
        Me.txtPath.TabIndex = 67
        Me.txtPath.ToolTipTitle = "Path"
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.LayoutControlItem2, Me.LayoutControlItem3})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(968, 571)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.Richtext_Encoded
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 76)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(942, 469)
        Me.LayoutControlItem1.TextVisible = False
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.txtPath
        Me.LayoutControlItem2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(942, 36)
        Me.LayoutControlItem2.Text = "File Path ( *.PHP ) :"
        Me.LayoutControlItem2.TextSize = New System.Drawing.Size(97, 18)
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.SeparatorControl1
        Me.LayoutControlItem3.Location = New System.Drawing.Point(0, 36)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(942, 40)
        Me.LayoutControlItem3.TextVisible = False
        '
        'frm_En_PHP
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.GroupControl6)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.LogoBSV
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frm_En_PHP"
        Me.ShowInTaskbar = False
        Me.Text = "PHP Obfuscation"
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl6.ResumeLayout(False)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.txtPath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents SeparatorControl1 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents GroupControl6 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents txtPath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Richtext_Encoded As RichTextBox
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
End Class
