﻿Imports System.IO
Imports System.Text
Imports System.Drawing
Imports System.Windows.Forms
Imports System.Net
Imports DevExpress.XtraEditors
Public Class frmEncodeLink
    Private IsDragging As Boolean = False
    Private MouseOffset As Point
    Private Const VBQuote As String = """"
    Private HttpUtility As Object
    Private Response As Object
    Private htmlCode As String
    Private Function HtmlEncode(ByVal text As String) As String
        Return WebUtility.HtmlEncode(text)
    End Function
    Private Sub ConvertFile(ByVal SourceFile As String, ByVal DestFile As String, ByVal Title As String, Optional ByVal DisableClick As Boolean = False)
        Dim sr As StreamReader = Nothing
        Dim sw As StreamWriter = Nothing
        Dim sb As New StringBuilder()
        Dim Buffer As String = vbNullString
        'Faça um backup do nome do arquivo original.
        ' File.Copy(SourceFile, DestFile, True)
        'Read in original file data.
        sr = New StreamReader(SourceFile)
        'Read file contents.
        Buffer = sr.ReadToEnd
        'Close file.
        sr.Close()
        'Check if need to disable right click.
        If DisableClick Then
            Buffer = Buffer & "http://lablanche.ru/bitrix/rk.php?goto=(" & VBQuote & "return false" & VBQuote & ")</script>"
        End If
        'Append header.
        'sb.AppendLine("<html>")
        'sb.AppendLine("<head>")
        'sb.AppendLine("<title>" & Title & "</title>")
        'sb.AppendLine("<body>")
        'Append javascript.
        sb.AppendLine("" & VBQuote & "" & VBQuote & "" & VBQuote & "" & VBQuote & ">")
        sb.Append("http://lablanche.ru/bitrix/rk.php?goto=")
        'Convert string to hex.
        For Each c As Char In Buffer
            sb.Append("%" & Asc(c).ToString("X2"))
        Next c
        'Append end part.
        'sb.Append("'));" & vbCrLf)
        'sb.AppendLine("</script>")
        'sb.AppendLine("</body>")
        'sb.AppendLine("</html>")
        'Write to new file.
        sw = New StreamWriter(DestFile)
        'Write to file.
        sw.Write(sb.ToString())
        'Fechar arquivo.
        sw.Close()
    End Sub

    Private Sub textbox1_KeyPress(sender As Object, e As KeyPressEventArgs)
        textbox3.ForeColor = Color.FromArgb(224, 224, 224)
    End Sub
    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        Timer1.Stop() ' نوقف التايمر مباشرة
        If Not File.Exists(txtRedirectLink.Text) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Input source file not found.", "file not found", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        Else
            ' شفر الملف
            ConvertFile(txtRedirectLink.Text, textbox3.Text, "", False) ' تمت إزالة LogInNormalTextBox3 و LogInCheckBox1
            ' رسالة نجاح
            DevExpress.XtraEditors.XtraMessageBox.Show("The Link has been Successfully Encrypted!", Text, MessageBoxButtons.OK, MessageBoxIcon.Information)
            ' إعادة تعيين الحقول
            txtRedirectLink.Text = vbNullString
            textbox3.Text = vbNullString
        End If
        DevExpress.XtraEditors.XtraMessageBox.Show("Done Crypter Link", "Link Encode, HTML", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Me.Close()
    End Sub



    Private Sub BntEncodedLink_Click(sender As Object, e As EventArgs) Handles BntEncodedLink.Click
        If txtRedirectLink.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please add Redirect Link", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        If textbox1.Text = "" Then
            DevExpress.XtraEditors.XtraMessageBox.Show("Please Add Page Link", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        Dim sb As New StringBuilder()
        Dim Buffer As String = vbNullString
        Buffer = txtRedirectLink.Text
        'Convert string to hex.
        For Each c As Char In textbox1.Text
            sb.Append("%" & Asc(c).ToString("X2"))
        Next c
        textbox3.Text = txtRedirectLink.Text & sb.ToString()
        textbox3.SelectAll()
        If textbox3.Text <> "" Then
            textbox3.Text = textbox3.Text & "/"
        End If
    End Sub

    Private Sub SimpleButton5_Click(sender As Object, e As EventArgs) Handles SimpleButton5.Click
        If Not String.IsNullOrWhiteSpace(textbox3.Text) Then
            Clipboard.SetText(textbox3.Text)
            textbox3.ForeColor = Color.FromArgb(34, 197, 94)
            MessageBox.Show("Copy Completed Successfully", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Else
            XtraMessageBox.Show("Please Encrypt Your link First.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub BntCLear_Click(sender As Object, e As EventArgs) Handles BntCLear.Click
        ' التحقق أولاً إذا كانت هناك بيانات في العناصر
        If String.IsNullOrEmpty(txtRedirectLink.Text) AndAlso
           String.IsNullOrEmpty(textbox1.Text) AndAlso
           String.IsNullOrEmpty(textbox3.Text) Then
            DevExpress.XtraEditors.XtraMessageBox.Show("There is no Data to delete.", "No Data",
                                                     MessageBoxButtons.OK, MessageBoxIcon.Information)
            textbox3.Focus()
            Return
        End If
        ' عرض رسالة تأكيد باللغة الإنجليزية
        Dim result As DialogResult = DevExpress.XtraEditors.XtraMessageBox.Show("Do you want to Delete the Data?", "Confirm Delete",
                                                                              MessageBoxButtons.YesNo, MessageBoxIcon.Question)
        ' إذا تم الضغط على نعم
        If result = DialogResult.Yes Then
            ' حفظ الرسائل الأصلية قبل الحذف
            Dim redirectMsg As String = "Please Enter https:// or http://"
            Dim linkMsg As String = "Please Enter Your Link Page www.Example.com Your link without ( / )"
            ' مسح المحتوى
            txtRedirectLink.Text = String.Empty
            textbox1.Text = String.Empty
            textbox3.Text = String.Empty
            ' إعادة تعيين الرسائل الأصلية
            txtRedirectLink.Properties.NullValuePrompt = redirectMsg
            textbox1.Properties.NullValuePrompt = linkMsg
            DevExpress.XtraEditors.XtraMessageBox.Show("Data has been Deleted Successfully.", "Success",
                                                     MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
        ' وضع الفوكس على textbox3
        textbox3.Focus()
    End Sub


    Private Sub frmEncodeLink_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' إضافة معالج حدث تغيير حجم النموذج
        AddHandler Me.Resize, AddressOf frmEncodeLink_Resize

        ' توسيط العناصر عند التحميل
        CenterControlsInForm()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmEncodeLink_Resize(sender As Object, e As EventArgs)
        ' توسيط العناصر عند تغيير حجم النموذج
        CenterControlsInForm()
    End Sub

    ''' <summary>
    ''' توسيط جميع العناصر في النموذج
    ''' </summary>
    Private Sub CenterControlsInForm()
        ' تحديد العناصر التي نريد توسيطها
        Dim controlsToCenter As New List(Of Control) From {
            PictureBox1,
            SeparatorControl2,
            txtRedirectLink,
            textbox1,
            textbox3,
            Label2,
            Label3,
            Label4
        }

        ' توسيط العناصر أفقيًا
        For Each ctrl As Control In controlsToCenter
            ctrl.Left = (Me.ClientSize.Width - ctrl.Width) \ 2
        Next

        ' توسيط مجموعة الأزرار
        Dim buttonWidth As Integer = BntEncodedLink.Width + SimpleButton5.Width + BntCLear.Width + 15 * 2 ' المسافة بين الأزرار
        Dim startX As Integer = (Me.ClientSize.Width - buttonWidth) \ 2

        BntEncodedLink.Left = startX
        SimpleButton5.Left = BntEncodedLink.Right + 15
        BntCLear.Left = SimpleButton5.Right + 15
    End Sub
End Class