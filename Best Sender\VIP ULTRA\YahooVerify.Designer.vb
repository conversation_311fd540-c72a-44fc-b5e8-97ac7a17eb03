﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class YahooVerify
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(YahooVerify))
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.ListView1 = New System.Windows.Forms.ListView()
        Me.cmbDelay = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.PictureBox4 = New System.Windows.Forms.PictureBox()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.PictureBox3 = New System.Windows.Forms.PictureBox()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.lblInvalid = New System.Windows.Forms.Label()
        Me.lblTotalSkipped = New System.Windows.Forms.Label()
        Me.lblTotal = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.SeparatorControl1 = New DevExpress.XtraEditors.SeparatorControl()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.PictureBox5 = New System.Windows.Forms.PictureBox()
        Me.btnStopCheck = New DevExpress.XtraEditors.SimpleButton()
        Me.btnSaveResults = New DevExpress.XtraEditors.SimpleButton()
        Me.btnStartCheck = New DevExpress.XtraEditors.SimpleButton()
        Me.btnSelectFile = New DevExpress.XtraEditors.SimpleButton()
        Me.btnClearList = New DevExpress.XtraEditors.SimpleButton()
        Me.Panel1.SuspendLayout()
        CType(Me.cmbDelay.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel3.SuspendLayout()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.ListView1)
        Me.Panel1.Location = New System.Drawing.Point(12, 25)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(838, 479)
        Me.Panel1.TabIndex = 463
        '
        'ListView1
        '
        Me.ListView1.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.ListView1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.ListView1.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ListView1.ForeColor = System.Drawing.Color.White
        Me.ListView1.HideSelection = False
        Me.ListView1.Location = New System.Drawing.Point(3, 3)
        Me.ListView1.Name = "ListView1"
        Me.ListView1.Size = New System.Drawing.Size(832, 473)
        Me.ListView1.TabIndex = 462
        Me.ListView1.UseCompatibleStateImageBehavior = False
        '
        'cmbDelay
        '
        Me.cmbDelay.EditValue = ""
        Me.cmbDelay.Location = New System.Drawing.Point(12, 149)
        Me.cmbDelay.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.cmbDelay.Name = "cmbDelay"
        Me.cmbDelay.Properties.AllowFocused = False
        Me.cmbDelay.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.cmbDelay.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.249999!, System.Drawing.FontStyle.Bold)
        Me.cmbDelay.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.cmbDelay.Properties.Appearance.Options.UseBackColor = True
        Me.cmbDelay.Properties.Appearance.Options.UseFont = True
        Me.cmbDelay.Properties.Appearance.Options.UseForeColor = True
        Me.cmbDelay.Properties.AppearanceDropDown.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(44, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.cmbDelay.Properties.AppearanceDropDown.ForeColor = System.Drawing.Color.White
        Me.cmbDelay.Properties.AppearanceDropDown.Options.UseBackColor = True
        Me.cmbDelay.Properties.AppearanceDropDown.Options.UseForeColor = True
        Me.cmbDelay.Properties.AppearanceFocused.ForeColor = System.Drawing.Color.White
        Me.cmbDelay.Properties.AppearanceFocused.Options.UseForeColor = True
        Me.cmbDelay.Properties.AppearanceItemHighlight.BackColor = System.Drawing.Color.DarkSlateGray
        Me.cmbDelay.Properties.AppearanceItemHighlight.ForeColor = System.Drawing.Color.White
        Me.cmbDelay.Properties.AppearanceItemHighlight.Options.UseBackColor = True
        Me.cmbDelay.Properties.AppearanceItemHighlight.Options.UseForeColor = True
        Me.cmbDelay.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.cmbDelay.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cmbDelay.Properties.DropDownRows = 20
        Me.cmbDelay.Properties.LookAndFeel.SkinName = "Sharp"
        Me.cmbDelay.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.cmbDelay.Properties.NullValuePrompt = "Extract Mail From..."
        Me.cmbDelay.Properties.Sorted = True
        Me.cmbDelay.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor
        Me.cmbDelay.Size = New System.Drawing.Size(263, 22)
        Me.cmbDelay.TabIndex = 469
        '
        'PictureBox4
        '
        Me.PictureBox4.Image = Global.Best_Sender.My.Resources.Resources.SendMailHide
        Me.PictureBox4.Location = New System.Drawing.Point(58, 371)
        Me.PictureBox4.Name = "PictureBox4"
        Me.PictureBox4.Size = New System.Drawing.Size(25, 26)
        Me.PictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox4.TabIndex = 466
        Me.PictureBox4.TabStop = False
        '
        'PictureBox2
        '
        Me.PictureBox2.Image = Global.Best_Sender.My.Resources.Resources.MS_Other
        Me.PictureBox2.Location = New System.Drawing.Point(58, 201)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(25, 26)
        Me.PictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox2.TabIndex = 465
        Me.PictureBox2.TabStop = False
        '
        'PictureBox3
        '
        Me.PictureBox3.Image = Global.Best_Sender.My.Resources.Resources.Cancel_32x32
        Me.PictureBox3.Location = New System.Drawing.Point(58, 317)
        Me.PictureBox3.Name = "PictureBox3"
        Me.PictureBox3.Size = New System.Drawing.Size(25, 26)
        Me.PictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox3.TabIndex = 465
        Me.PictureBox3.TabStop = False
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.Send
        Me.PictureBox1.Location = New System.Drawing.Point(58, 258)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(25, 26)
        Me.PictureBox1.TabIndex = 465
        Me.PictureBox1.TabStop = False
        '
        'lblInvalid
        '
        Me.lblInvalid.AutoSize = True
        Me.lblInvalid.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblInvalid.ForeColor = System.Drawing.Color.Red
        Me.lblInvalid.Location = New System.Drawing.Point(88, 316)
        Me.lblInvalid.Name = "lblInvalid"
        Me.lblInvalid.Size = New System.Drawing.Size(66, 21)
        Me.lblInvalid.TabIndex = 15
        Me.lblInvalid.Text = "Invalid:0"
        '
        'lblTotalSkipped
        '
        Me.lblTotalSkipped.AutoSize = True
        Me.lblTotalSkipped.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblTotalSkipped.ForeColor = System.Drawing.Color.Yellow
        Me.lblTotalSkipped.Location = New System.Drawing.Point(88, 372)
        Me.lblTotalSkipped.Name = "lblTotalSkipped"
        Me.lblTotalSkipped.Size = New System.Drawing.Size(117, 21)
        Me.lblTotalSkipped.TabIndex = 16
        Me.lblTotalSkipped.Text = "Skipped Email:0"
        '
        'lblTotal
        '
        Me.lblTotal.AutoSize = True
        Me.lblTotal.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblTotal.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(203, Byte), Integer), CType(CType(121, Byte), Integer))
        Me.lblTotal.Location = New System.Drawing.Point(88, 260)
        Me.lblTotal.Name = "lblTotal"
        Me.lblTotal.Size = New System.Drawing.Size(54, 21)
        Me.lblTotal.TabIndex = 17
        Me.lblTotal.Text = "Valid:0"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(88, 204)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(96, 21)
        Me.Label1.TabIndex = 18
        Me.Label1.Text = "Total Email:0"
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.Location = New System.Drawing.Point(46, 420)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(246, 66)
        Me.ProgressPanel1.TabIndex = 465
        Me.ProgressPanel1.Text = "ProgressPanel1"
        '
        'Panel3
        '
        Me.Panel3.Controls.Add(Me.SeparatorControl1)
        Me.Panel3.Controls.Add(Me.Label2)
        Me.Panel3.Controls.Add(Me.ProgressPanel1)
        Me.Panel3.Controls.Add(Me.PictureBox5)
        Me.Panel3.Controls.Add(Me.PictureBox2)
        Me.Panel3.Controls.Add(Me.Label1)
        Me.Panel3.Controls.Add(Me.PictureBox1)
        Me.Panel3.Controls.Add(Me.cmbDelay)
        Me.Panel3.Controls.Add(Me.lblTotal)
        Me.Panel3.Controls.Add(Me.PictureBox3)
        Me.Panel3.Controls.Add(Me.PictureBox4)
        Me.Panel3.Controls.Add(Me.lblInvalid)
        Me.Panel3.Controls.Add(Me.lblTotalSkipped)
        Me.Panel3.Location = New System.Drawing.Point(856, 32)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(312, 527)
        Me.Panel3.TabIndex = 465
        '
        'SeparatorControl1
        '
        Me.SeparatorControl1.LineThickness = 1
        Me.SeparatorControl1.Location = New System.Drawing.Point(46, 107)
        Me.SeparatorControl1.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.SeparatorControl1.Name = "SeparatorControl1"
        Me.SeparatorControl1.Padding = New System.Windows.Forms.Padding(8, 7, 8, 7)
        Me.SeparatorControl1.Size = New System.Drawing.Size(214, 19)
        Me.SeparatorControl1.TabIndex = 496
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 15.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(203, Byte), Integer), CType(CType(121, Byte), Integer))
        Me.Label2.Location = New System.Drawing.Point(72, 71)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(150, 34)
        Me.Label2.TabIndex = 470
        Me.Label2.Text = "Yahoo Verify"
        '
        'PictureBox5
        '
        Me.PictureBox5.Image = Global.Best_Sender.My.Resources.Resources.YahooPanel
        Me.PictureBox5.Location = New System.Drawing.Point(112, 4)
        Me.PictureBox5.Name = "PictureBox5"
        Me.PictureBox5.Size = New System.Drawing.Size(65, 64)
        Me.PictureBox5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox5.TabIndex = 465
        Me.PictureBox5.TabStop = False
        '
        'btnStopCheck
        '
        Me.btnStopCheck.AllowFocus = False
        Me.btnStopCheck.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.btnStopCheck.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnStopCheck.Appearance.Options.UseBackColor = True
        Me.btnStopCheck.Appearance.Options.UseFont = True
        Me.btnStopCheck.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.btnStopCheck.AppearanceHovered.Options.UseBackColor = True
        Me.btnStopCheck.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.btnStopCheck.AppearancePressed.Options.UseBackColor = True
        Me.btnStopCheck.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.pause_16x16
        Me.btnStopCheck.Location = New System.Drawing.Point(279, 525)
        Me.btnStopCheck.LookAndFeel.SkinName = "Darkroom"
        Me.btnStopCheck.LookAndFeel.UseDefaultLookAndFeel = False
        Me.btnStopCheck.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.btnStopCheck.Name = "btnStopCheck"
        Me.btnStopCheck.Size = New System.Drawing.Size(122, 30)
        Me.btnStopCheck.TabIndex = 475
        Me.btnStopCheck.Text = "Stop"
        '
        'btnSaveResults
        '
        Me.btnSaveResults.AllowFocus = False
        Me.btnSaveResults.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.btnSaveResults.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnSaveResults.Appearance.Options.UseBackColor = True
        Me.btnSaveResults.Appearance.Options.UseFont = True
        Me.btnSaveResults.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.btnSaveResults.AppearanceHovered.Options.UseBackColor = True
        Me.btnSaveResults.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.btnSaveResults.AppearancePressed.Options.UseBackColor = True
        Me.btnSaveResults.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.export_16x16
        Me.btnSaveResults.Location = New System.Drawing.Point(410, 525)
        Me.btnSaveResults.LookAndFeel.SkinName = "Darkroom"
        Me.btnSaveResults.LookAndFeel.UseDefaultLookAndFeel = False
        Me.btnSaveResults.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.btnSaveResults.Name = "btnSaveResults"
        Me.btnSaveResults.Size = New System.Drawing.Size(122, 30)
        Me.btnSaveResults.TabIndex = 474
        Me.btnSaveResults.Text = "Save"
        '
        'btnStartCheck
        '
        Me.btnStartCheck.AllowFocus = False
        Me.btnStartCheck.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.btnStartCheck.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnStartCheck.Appearance.Options.UseBackColor = True
        Me.btnStartCheck.Appearance.Options.UseFont = True
        Me.btnStartCheck.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.btnStartCheck.AppearanceHovered.Options.UseBackColor = True
        Me.btnStartCheck.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.btnStartCheck.AppearancePressed.Options.UseBackColor = True
        Me.btnStartCheck.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.play_16x16
        Me.btnStartCheck.Location = New System.Drawing.Point(148, 525)
        Me.btnStartCheck.LookAndFeel.SkinName = "Darkroom"
        Me.btnStartCheck.LookAndFeel.UseDefaultLookAndFeel = False
        Me.btnStartCheck.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.btnStartCheck.Name = "btnStartCheck"
        Me.btnStartCheck.Size = New System.Drawing.Size(122, 30)
        Me.btnStartCheck.TabIndex = 473
        Me.btnStartCheck.Text = "Start"
        '
        'btnSelectFile
        '
        Me.btnSelectFile.AllowFocus = False
        Me.btnSelectFile.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.btnSelectFile.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnSelectFile.Appearance.Options.UseBackColor = True
        Me.btnSelectFile.Appearance.Options.UseFont = True
        Me.btnSelectFile.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.btnSelectFile.AppearanceHovered.Options.UseBackColor = True
        Me.btnSelectFile.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.btnSelectFile.AppearancePressed.Options.UseBackColor = True
        Me.btnSelectFile.ImageOptions.Image = CType(resources.GetObject("btnSelectFile.ImageOptions.Image"), System.Drawing.Image)
        Me.btnSelectFile.Location = New System.Drawing.Point(17, 525)
        Me.btnSelectFile.LookAndFeel.SkinName = "Darkroom"
        Me.btnSelectFile.LookAndFeel.UseDefaultLookAndFeel = False
        Me.btnSelectFile.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.btnSelectFile.Name = "btnSelectFile"
        Me.btnSelectFile.Size = New System.Drawing.Size(122, 30)
        Me.btnSelectFile.TabIndex = 471
        Me.btnSelectFile.Text = "Browse..."
        '
        'btnClearList
        '
        Me.btnClearList.AllowFocus = False
        Me.btnClearList.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.btnClearList.Appearance.Font = New System.Drawing.Font("Comfortaa", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnClearList.Appearance.Options.UseBackColor = True
        Me.btnClearList.Appearance.Options.UseFont = True
        Me.btnClearList.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.btnClearList.AppearanceHovered.Options.UseBackColor = True
        Me.btnClearList.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.btnClearList.AppearancePressed.Options.UseBackColor = True
        Me.btnClearList.ImageOptions.Image = CType(resources.GetObject("btnClearList.ImageOptions.Image"), System.Drawing.Image)
        Me.btnClearList.Location = New System.Drawing.Point(541, 525)
        Me.btnClearList.LookAndFeel.SkinName = "Darkroom"
        Me.btnClearList.LookAndFeel.UseDefaultLookAndFeel = False
        Me.btnClearList.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.btnClearList.Name = "btnClearList"
        Me.btnClearList.Size = New System.Drawing.Size(122, 30)
        Me.btnClearList.TabIndex = 472
        Me.btnClearList.Text = "Clear All"
        '
        'YahooVerify
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(40, Byte), Integer), CType(CType(42, Byte), Integer), CType(CType(59, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1180, 571)
        Me.Controls.Add(Me.btnStopCheck)
        Me.Controls.Add(Me.btnSaveResults)
        Me.Controls.Add(Me.btnStartCheck)
        Me.Controls.Add(Me.btnSelectFile)
        Me.Controls.Add(Me.btnClearList)
        Me.Controls.Add(Me.Panel3)
        Me.Controls.Add(Me.Panel1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.YahooPanel
        Me.MaximizeBox = False
        Me.Name = "YahooVerify"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Yahoo Verify"
        Me.Panel1.ResumeLayout(False)
        CType(Me.cmbDelay.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel3.ResumeLayout(False)
        Me.Panel3.PerformLayout()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
    End Sub
    Friend WithEvents Panel1 As Panel
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents lblInvalid As Label
    Friend WithEvents lblTotalSkipped As Label
    Friend WithEvents lblTotal As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents PictureBox4 As PictureBox
    Friend WithEvents PictureBox2 As PictureBox
    Friend WithEvents PictureBox3 As PictureBox
    Friend WithEvents cmbDelay As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents Panel3 As Panel
    Friend WithEvents btnStopCheck As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSaveResults As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnStartCheck As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSelectFile As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnClearList As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Label2 As Label
    Friend WithEvents PictureBox5 As PictureBox
    Friend WithEvents SeparatorControl1 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents ListView1 As ListView
End Class
