﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmEmailSorter
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim EditorButtonImageOptions3 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmEmailSorter))
        Dim SerializableAppearanceObject9 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject10 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject11 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject12 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim EditorButtonImageOptions4 As DevExpress.XtraEditors.Controls.EditorButtonImageOptions = New DevExpress.XtraEditors.Controls.EditorButtonImageOptions()
        Dim SerializableAppearanceObject13 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject14 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject15 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Dim SerializableAppearanceObject16 As DevExpress.Utils.SerializableAppearanceObject = New DevExpress.Utils.SerializableAppearanceObject()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Col_MailLogo = New DevExpress.XtraEditors.Repository.RepositoryItemPictureEdit()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn11 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.BNT_Details = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.GridColumn6 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Bnt_Remove = New DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit()
        Me.GridColumn8 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn7 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn9 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.Col_Chk = New DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit()
        Me.GridColumn10 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.RepositoryItemMemoEdit1 = New DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit()
        Me.CheckListPath = New DevExpress.XtraEditors.CheckEdit()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.BntBrowse = New DevExpress.XtraEditors.SimpleButton()
        Me.txtPath = New DevExpress.XtraEditors.TextEdit()
        Me.BntSelectAll = New DevExpress.XtraEditors.SimpleButton()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.BntStop = New DevExpress.XtraEditors.SimpleButton()
        Me.ProgressBarControl1 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.trmfadein = New System.Windows.Forms.Timer(Me.components)
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.BntSave = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.BntResetAll = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Col_MailLogo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BNT_Details, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Bnt_Remove, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Col_Chk, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.RepositoryItemMemoEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckListPath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtPath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel4.SuspendLayout()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupControl1
        '
        Me.GroupControl1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GroupControl1.Appearance.Options.UseBackColor = True
        Me.GroupControl1.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.2!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControl1.AppearanceCaption.ForeColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.GroupControl1.AppearanceCaption.Options.UseFont = True
        Me.GroupControl1.AppearanceCaption.Options.UseForeColor = True
        Me.GroupControl1.Controls.Add(Me.GridControl1)
        Me.GroupControl1.Controls.Add(Me.CheckListPath)
        Me.GroupControl1.Controls.Add(Me.Label1)
        Me.GroupControl1.Controls.Add(Me.BntBrowse)
        Me.GroupControl1.Controls.Add(Me.txtPath)
        Me.GroupControl1.Controls.Add(Me.BntSelectAll)
        Me.GroupControl1.Location = New System.Drawing.Point(21, 25)
        Me.GroupControl1.LookAndFeel.SkinName = "WXI"
        Me.GroupControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(748, 658)
        Me.GroupControl1.TabIndex = 304
        '
        'GridControl1
        '
        Me.GridControl1.EmbeddedNavigator.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GridControl1.Location = New System.Drawing.Point(27, 131)
        Me.GridControl1.LookAndFeel.SkinMaskColor = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridControl1.LookAndFeel.SkinMaskColor2 = System.Drawing.Color.FromArgb(CType(CType(78, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.GridControl1.LookAndFeel.SkinName = "DevExpress Dark Style"
        Me.GridControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.RepositoryItems.AddRange(New DevExpress.XtraEditors.Repository.RepositoryItem() {Me.Bnt_Remove, Me.Col_Chk, Me.BNT_Details, Me.Col_MailLogo, Me.RepositoryItemMemoEdit1})
        Me.GridControl1.Size = New System.Drawing.Size(701, 509)
        Me.GridControl1.TabIndex = 64
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Appearance.Empty.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.Empty.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.Empty.Options.UseBackColor = True
        Me.GridView1.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.EvenRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.EvenRow.Options.UseBackColor = True
        Me.GridView1.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.FocusedRow.Options.UseBackColor = True
        Me.GridView1.Appearance.GroupRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.GroupRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.GroupRow.Options.UseBackColor = True
        Me.GridView1.Appearance.OddRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.OddRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.OddRow.Options.UseBackColor = True
        Me.GridView1.Appearance.Row.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.Row.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.Row.Options.UseBackColor = True
        Me.GridView1.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridView1.Appearance.SelectedRow.Options.UseBackColor = True
        Me.GridView1.ColumnPanelRowHeight = 32
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2, Me.GridColumn3, Me.GridColumn11, Me.GridColumn5, Me.GridColumn6, Me.GridColumn8, Me.GridColumn7, Me.GridColumn4, Me.GridColumn9, Me.GridColumn10})
        Me.GridView1.DetailHeight = 284
        Me.GridView1.FixedLineWidth = 1
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.GroupRowHeight = 28
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsFind.AllowFindPanel = False
        Me.GridView1.OptionsFind.AllowMruItems = False
        Me.GridView1.OptionsView.BestFitMaxRowCount = 2
        Me.GridView1.OptionsView.EnableAppearanceEvenRow = True
        Me.GridView1.OptionsView.EnableAppearanceOddRow = True
        Me.GridView1.OptionsView.ShowGroupPanel = False
        Me.GridView1.OptionsView.ShowIndicator = False
        Me.GridView1.RowHeight = 41
        '
        'GridColumn1
        '
        Me.GridColumn1.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn1.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn1.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn1.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn1.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn1.AppearanceCell.Options.UseFont = True
        Me.GridColumn1.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn1.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn1.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.GridColumn1.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!)
        Me.GridColumn1.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn1.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn1.AppearanceHeader.Options.UseFont = True
        Me.GridColumn1.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn1.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn1.Caption = "#"
        Me.GridColumn1.FieldName = "ID"
        Me.GridColumn1.MinWidth = 10
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.OptionsColumn.AllowEdit = False
        Me.GridColumn1.OptionsColumn.AllowFocus = False
        Me.GridColumn1.OptionsColumn.AllowMove = False
        Me.GridColumn1.OptionsColumn.AllowShowHide = False
        Me.GridColumn1.OptionsColumn.AllowSize = False
        Me.GridColumn1.OptionsColumn.FixedWidth = True
        Me.GridColumn1.OptionsColumn.ReadOnly = True
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        Me.GridColumn1.Width = 9
        '
        'GridColumn2
        '
        Me.GridColumn2.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn2.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn2.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn2.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn2.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn2.AppearanceCell.Options.UseFont = True
        Me.GridColumn2.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn2.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn2.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.GridColumn2.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.GridColumn2.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!)
        Me.GridColumn2.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn2.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn2.AppearanceHeader.Options.UseFont = True
        Me.GridColumn2.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn2.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn2.Caption = "Logo"
        Me.GridColumn2.ColumnEdit = Me.Col_MailLogo
        Me.GridColumn2.FieldName = "MailLogo"
        Me.GridColumn2.MinWidth = 10
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.OptionsColumn.AllowEdit = False
        Me.GridColumn2.OptionsColumn.AllowFocus = False
        Me.GridColumn2.OptionsColumn.AllowMove = False
        Me.GridColumn2.OptionsColumn.AllowShowHide = False
        Me.GridColumn2.OptionsColumn.AllowSize = False
        Me.GridColumn2.OptionsColumn.FixedWidth = True
        Me.GridColumn2.OptionsColumn.ReadOnly = True
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 1
        Me.GridColumn2.Width = 13
        '
        'Col_MailLogo
        '
        Me.Col_MailLogo.Name = "Col_MailLogo"
        Me.Col_MailLogo.ReadOnly = True
        Me.Col_MailLogo.ShowMenu = False
        Me.Col_MailLogo.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Squeeze
        '
        'GridColumn3
        '
        Me.GridColumn3.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn3.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn3.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn3.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn3.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn3.AppearanceCell.Options.UseFont = True
        Me.GridColumn3.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn3.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn3.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn3.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.GridColumn3.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!)
        Me.GridColumn3.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn3.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn3.AppearanceHeader.Options.UseFont = True
        Me.GridColumn3.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn3.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn3.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn3.Caption = "Mail Server"
        Me.GridColumn3.FieldName = "MailService"
        Me.GridColumn3.MinWidth = 10
        Me.GridColumn3.Name = "GridColumn3"
        Me.GridColumn3.OptionsColumn.AllowEdit = False
        Me.GridColumn3.OptionsColumn.AllowFocus = False
        Me.GridColumn3.OptionsColumn.AllowMove = False
        Me.GridColumn3.OptionsColumn.AllowShowHide = False
        Me.GridColumn3.OptionsColumn.AllowSize = False
        Me.GridColumn3.OptionsColumn.FixedWidth = True
        Me.GridColumn3.OptionsColumn.ReadOnly = True
        Me.GridColumn3.Visible = True
        Me.GridColumn3.VisibleIndex = 2
        Me.GridColumn3.Width = 51
        '
        'GridColumn11
        '
        Me.GridColumn11.Caption = "MailAccount"
        Me.GridColumn11.FieldName = "MailAccount"
        Me.GridColumn11.MinWidth = 21
        Me.GridColumn11.Name = "GridColumn11"
        Me.GridColumn11.Width = 81
        '
        'GridColumn5
        '
        Me.GridColumn5.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn5.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn5.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn5.AppearanceCell.ForeColor = System.Drawing.Color.LightGray
        Me.GridColumn5.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn5.AppearanceCell.Options.UseFont = True
        Me.GridColumn5.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn5.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn5.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn5.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn5.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.GridColumn5.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!)
        Me.GridColumn5.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn5.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn5.AppearanceHeader.Options.UseFont = True
        Me.GridColumn5.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn5.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn5.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn5.Caption = "Details"
        Me.GridColumn5.ColumnEdit = Me.BNT_Details
        Me.GridColumn5.MinWidth = 10
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.OptionsColumn.AllowMove = False
        Me.GridColumn5.OptionsColumn.AllowShowHide = False
        Me.GridColumn5.OptionsColumn.AllowSize = False
        Me.GridColumn5.OptionsColumn.FixedWidth = True
        Me.GridColumn5.OptionsColumn.ReadOnly = True
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 4
        Me.GridColumn5.Width = 13
        '
        'BNT_Details
        '
        Me.BNT_Details.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.BNT_Details.Appearance.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.BNT_Details.Appearance.Options.UseBackColor = True
        Me.BNT_Details.AppearanceFocused.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.BNT_Details.AppearanceFocused.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.BNT_Details.AppearanceFocused.Options.UseBackColor = True
        Me.BNT_Details.AutoHeight = False
        EditorButtonImageOptions3.Image = CType(resources.GetObject("EditorButtonImageOptions3.Image"), System.Drawing.Image)
        Me.BNT_Details.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, False, EditorButtonImageOptions3, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject9, SerializableAppearanceObject10, SerializableAppearanceObject11, SerializableAppearanceObject12, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.BNT_Details.Name = "BNT_Details"
        Me.BNT_Details.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'GridColumn6
        '
        Me.GridColumn6.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn6.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn6.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn6.AppearanceCell.ForeColor = System.Drawing.Color.LightGray
        Me.GridColumn6.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn6.AppearanceCell.Options.UseFont = True
        Me.GridColumn6.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn6.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn6.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn6.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.GridColumn6.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!)
        Me.GridColumn6.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn6.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn6.AppearanceHeader.Options.UseFont = True
        Me.GridColumn6.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn6.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn6.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn6.Caption = "Remove"
        Me.GridColumn6.ColumnEdit = Me.Bnt_Remove
        Me.GridColumn6.FieldName = "CanCheck"
        Me.GridColumn6.MinWidth = 10
        Me.GridColumn6.Name = "GridColumn6"
        Me.GridColumn6.OptionsColumn.AllowMove = False
        Me.GridColumn6.OptionsColumn.AllowShowHide = False
        Me.GridColumn6.OptionsColumn.AllowSize = False
        Me.GridColumn6.OptionsColumn.FixedWidth = True
        Me.GridColumn6.OptionsColumn.ReadOnly = True
        Me.GridColumn6.Width = 19
        '
        'Bnt_Remove
        '
        Me.Bnt_Remove.AutoHeight = False
        EditorButtonImageOptions4.Image = CType(resources.GetObject("EditorButtonImageOptions4.Image"), System.Drawing.Image)
        Me.Bnt_Remove.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, True, True, False, EditorButtonImageOptions4, New DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), SerializableAppearanceObject13, SerializableAppearanceObject14, SerializableAppearanceObject15, SerializableAppearanceObject16, "", Nothing, Nothing, DevExpress.Utils.ToolTipAnchor.[Default])})
        Me.Bnt_Remove.Name = "Bnt_Remove"
        Me.Bnt_Remove.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor
        '
        'GridColumn8
        '
        Me.GridColumn8.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn8.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn8.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn8.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn8.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn8.AppearanceCell.Options.UseFont = True
        Me.GridColumn8.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn8.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn8.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn8.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.GridColumn8.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!)
        Me.GridColumn8.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn8.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn8.AppearanceHeader.Options.UseFont = True
        Me.GridColumn8.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn8.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn8.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn8.Caption = "Found"
        Me.GridColumn8.FieldName = "MailFount"
        Me.GridColumn8.MinWidth = 10
        Me.GridColumn8.Name = "GridColumn8"
        Me.GridColumn8.OptionsColumn.AllowEdit = False
        Me.GridColumn8.OptionsColumn.AllowFocus = False
        Me.GridColumn8.OptionsColumn.AllowMove = False
        Me.GridColumn8.OptionsColumn.AllowShowHide = False
        Me.GridColumn8.OptionsColumn.AllowSize = False
        Me.GridColumn8.OptionsColumn.FixedWidth = True
        Me.GridColumn8.OptionsColumn.ReadOnly = True
        Me.GridColumn8.Visible = True
        Me.GridColumn8.VisibleIndex = 3
        Me.GridColumn8.Width = 15
        '
        'GridColumn7
        '
        Me.GridColumn7.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn7.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn7.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn7.AppearanceCell.ForeColor = System.Drawing.Color.LightGray
        Me.GridColumn7.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn7.AppearanceCell.Options.UseFont = True
        Me.GridColumn7.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn7.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn7.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn7.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn7.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.GridColumn7.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!)
        Me.GridColumn7.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn7.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn7.AppearanceHeader.Options.UseFont = True
        Me.GridColumn7.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn7.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn7.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn7.Caption = "Password"
        Me.GridColumn7.FieldName = "password"
        Me.GridColumn7.MinWidth = 21
        Me.GridColumn7.Name = "GridColumn7"
        Me.GridColumn7.OptionsColumn.AllowEdit = False
        Me.GridColumn7.OptionsColumn.AllowFocus = False
        Me.GridColumn7.OptionsColumn.AllowMove = False
        Me.GridColumn7.OptionsColumn.AllowShowHide = False
        Me.GridColumn7.OptionsColumn.AllowSize = False
        Me.GridColumn7.OptionsColumn.FixedWidth = True
        Me.GridColumn7.OptionsColumn.ReadOnly = True
        Me.GridColumn7.Width = 26
        '
        'GridColumn4
        '
        Me.GridColumn4.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn4.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn4.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn4.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn4.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn4.AppearanceCell.Options.UseFont = True
        Me.GridColumn4.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn4.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn4.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn4.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.GridColumn4.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!)
        Me.GridColumn4.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn4.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn4.AppearanceHeader.Options.UseFont = True
        Me.GridColumn4.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn4.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn4.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn4.Caption = "Port"
        Me.GridColumn4.FieldName = "port"
        Me.GridColumn4.MinWidth = 10
        Me.GridColumn4.Name = "GridColumn4"
        Me.GridColumn4.OptionsColumn.AllowEdit = False
        Me.GridColumn4.OptionsColumn.AllowFocus = False
        Me.GridColumn4.OptionsColumn.AllowMove = False
        Me.GridColumn4.OptionsColumn.AllowShowHide = False
        Me.GridColumn4.OptionsColumn.AllowSize = False
        Me.GridColumn4.OptionsColumn.FixedWidth = True
        Me.GridColumn4.OptionsColumn.ReadOnly = True
        Me.GridColumn4.Width = 13
        '
        'GridColumn9
        '
        Me.GridColumn9.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn9.AppearanceCell.BackColor2 = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.GridColumn9.AppearanceCell.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.GridColumn9.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.GridColumn9.AppearanceCell.Options.UseBackColor = True
        Me.GridColumn9.AppearanceCell.Options.UseFont = True
        Me.GridColumn9.AppearanceCell.Options.UseForeColor = True
        Me.GridColumn9.AppearanceCell.Options.UseTextOptions = True
        Me.GridColumn9.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn9.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn9.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.GridColumn9.AppearanceHeader.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!)
        Me.GridColumn9.AppearanceHeader.ForeColor = System.Drawing.Color.White
        Me.GridColumn9.AppearanceHeader.Options.UseBackColor = True
        Me.GridColumn9.AppearanceHeader.Options.UseFont = True
        Me.GridColumn9.AppearanceHeader.Options.UseForeColor = True
        Me.GridColumn9.AppearanceHeader.Options.UseTextOptions = True
        Me.GridColumn9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.GridColumn9.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.GridColumn9.Caption = "Select"
        Me.GridColumn9.ColumnEdit = Me.Col_Chk
        Me.GridColumn9.FieldName = "CanCheck"
        Me.GridColumn9.MinWidth = 10
        Me.GridColumn9.Name = "GridColumn9"
        Me.GridColumn9.OptionsColumn.AllowMove = False
        Me.GridColumn9.OptionsColumn.AllowShowHide = False
        Me.GridColumn9.OptionsColumn.AllowSize = False
        Me.GridColumn9.OptionsColumn.FixedWidth = True
        Me.GridColumn9.Width = 15
        '
        'Col_Chk
        '
        Me.Col_Chk.AutoHeight = False
        Me.Col_Chk.CheckBoxOptions.Style = DevExpress.XtraEditors.Controls.CheckBoxStyle.CheckBox
        Me.Col_Chk.LookAndFeel.SkinName = "Dark Side"
        Me.Col_Chk.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Col_Chk.Name = "Col_Chk"
        Me.Col_Chk.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked
        '
        'GridColumn10
        '
        Me.GridColumn10.Caption = "GridColumn10"
        Me.GridColumn10.FieldName = "errormsg"
        Me.GridColumn10.MinWidth = 21
        Me.GridColumn10.Name = "GridColumn10"
        Me.GridColumn10.Width = 81
        '
        'RepositoryItemMemoEdit1
        '
        Me.RepositoryItemMemoEdit1.Name = "RepositoryItemMemoEdit1"
        '
        'CheckListPath
        '
        Me.CheckListPath.Location = New System.Drawing.Point(493, 612)
        Me.CheckListPath.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.CheckListPath.Name = "CheckListPath"
        Me.CheckListPath.Properties.AllowFocused = False
        Me.CheckListPath.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.CheckListPath.Properties.Appearance.ForeColor = System.Drawing.Color.Gray
        Me.CheckListPath.Properties.Appearance.Options.UseFont = True
        Me.CheckListPath.Properties.Appearance.Options.UseForeColor = True
        Me.CheckListPath.Properties.Caption = " Save mail server list path."
        Me.CheckListPath.Properties.LookAndFeel.SkinName = "Dark Side"
        Me.CheckListPath.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckListPath.Size = New System.Drawing.Size(234, 19)
        Me.CheckListPath.TabIndex = 58
        Me.CheckListPath.Visible = False
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(28, 50)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(149, 15)
        Me.Label1.TabIndex = 53
        Me.Label1.Text = "Mail list path ( *.txt ) : "
        '
        'BntBrowse
        '
        Me.BntBrowse.AllowFocus = False
        Me.BntBrowse.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.BntBrowse.Appearance.Options.UseBackColor = True
        Me.BntBrowse.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntBrowse.AppearanceHovered.Options.UseBackColor = True
        Me.BntBrowse.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntBrowse.AppearancePressed.Options.UseBackColor = True
        Me.BntBrowse.Location = New System.Drawing.Point(554, 83)
        Me.BntBrowse.LookAndFeel.SkinName = "Darkroom"
        Me.BntBrowse.LookAndFeel.UseDefaultLookAndFeel = False
        Me.BntBrowse.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntBrowse.Name = "BntBrowse"
        Me.BntBrowse.Size = New System.Drawing.Size(77, 33)
        Me.BntBrowse.TabIndex = 51
        Me.BntBrowse.Text = "Browse.."
        '
        'txtPath
        '
        Me.txtPath.EditValue = ""
        Me.txtPath.Location = New System.Drawing.Point(27, 83)
        Me.txtPath.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtPath.Name = "txtPath"
        Me.txtPath.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.txtPath.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtPath.Properties.Appearance.ForeColor = System.Drawing.Color.White
        Me.txtPath.Properties.Appearance.Options.UseBackColor = True
        Me.txtPath.Properties.Appearance.Options.UseFont = True
        Me.txtPath.Properties.Appearance.Options.UseForeColor = True
        Me.txtPath.Properties.LookAndFeel.SkinName = "Metropolis Dark"
        Me.txtPath.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.txtPath.Properties.NullValuePrompt = "Add your smtp list path..."
        Me.txtPath.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtPath.Properties.ReadOnly = True
        Me.txtPath.Size = New System.Drawing.Size(519, 22)
        Me.txtPath.TabIndex = 50
        '
        'BntSelectAll
        '
        Me.BntSelectAll.AllowFocus = False
        Me.BntSelectAll.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(87, Byte), Integer), CType(CType(90, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.BntSelectAll.Appearance.Options.UseBackColor = True
        Me.BntSelectAll.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntSelectAll.AppearanceHovered.Options.UseBackColor = True
        Me.BntSelectAll.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntSelectAll.AppearancePressed.Options.UseBackColor = True
        Me.BntSelectAll.Location = New System.Drawing.Point(222, 392)
        Me.BntSelectAll.LookAndFeel.SkinName = "Darkroom"
        Me.BntSelectAll.LookAndFeel.UseDefaultLookAndFeel = False
        Me.BntSelectAll.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntSelectAll.Name = "BntSelectAll"
        Me.BntSelectAll.Size = New System.Drawing.Size(77, 33)
        Me.BntSelectAll.TabIndex = 63
        Me.BntSelectAll.Text = "Select All"
        '
        'Panel4
        '
        Me.Panel4.Controls.Add(Me.BntStop)
        Me.Panel4.Controls.Add(Me.ProgressBarControl1)
        Me.Panel4.Location = New System.Drawing.Point(21, 691)
        Me.Panel4.Margin = New System.Windows.Forms.Padding(4)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(748, 82)
        Me.Panel4.TabIndex = 310
        Me.Panel4.Visible = False
        '
        'BntStop
        '
        Me.BntStop.AllowFocus = False
        Me.BntStop.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BntStop.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.BntStop.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BntStop.Appearance.Options.UseBackColor = True
        Me.BntStop.Appearance.Options.UseFont = True
        Me.BntStop.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!)
        Me.BntStop.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.BntStop.AppearanceDisabled.Options.UseFont = True
        Me.BntStop.AppearanceDisabled.Options.UseForeColor = True
        Me.BntStop.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntStop.AppearanceHovered.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!)
        Me.BntStop.AppearanceHovered.Options.UseBackColor = True
        Me.BntStop.AppearanceHovered.Options.UseFont = True
        Me.BntStop.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntStop.AppearancePressed.Options.UseBackColor = True
        Me.BntStop.Location = New System.Drawing.Point(15, 15)
        Me.BntStop.LookAndFeel.SkinName = "Darkroom"
        Me.BntStop.LookAndFeel.UseDefaultLookAndFeel = False
        Me.BntStop.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntStop.Name = "BntStop"
        Me.BntStop.Size = New System.Drawing.Size(152, 55)
        Me.BntStop.TabIndex = 310
        Me.BntStop.Text = "&Cancel"
        '
        'ProgressBarControl1
        '
        Me.ProgressBarControl1.Location = New System.Drawing.Point(180, 21)
        Me.ProgressBarControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ProgressBarControl1.Name = "ProgressBarControl1"
        Me.ProgressBarControl1.Properties.AllowFocused = False
        Me.ProgressBarControl1.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressBarControl1.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.ProgressBarControl1.Properties.LookAndFeel.SkinName = "McSkin"
        Me.ProgressBarControl1.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ProgressBarControl1.Properties.ShowTitle = True
        Me.ProgressBarControl1.Size = New System.Drawing.Size(554, 44)
        Me.ProgressBarControl1.TabIndex = 16
        '
        'trmfadein
        '
        Me.trmfadein.Enabled = True
        Me.trmfadein.Interval = 1
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'BntSave
        '
        Me.BntSave.AllowFocus = False
        Me.BntSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BntSave.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.BntSave.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BntSave.Appearance.Options.UseBackColor = True
        Me.BntSave.Appearance.Options.UseFont = True
        Me.BntSave.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!)
        Me.BntSave.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.BntSave.AppearanceDisabled.Options.UseFont = True
        Me.BntSave.AppearanceDisabled.Options.UseForeColor = True
        Me.BntSave.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntSave.AppearanceHovered.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!)
        Me.BntSave.AppearanceHovered.Options.UseBackColor = True
        Me.BntSave.AppearanceHovered.Options.UseFont = True
        Me.BntSave.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntSave.AppearancePressed.Options.UseBackColor = True
        Me.BntSave.Location = New System.Drawing.Point(441, 711)
        Me.BntSave.LookAndFeel.SkinName = "Darkroom"
        Me.BntSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.BntSave.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntSave.Name = "BntSave"
        Me.BntSave.Size = New System.Drawing.Size(152, 55)
        Me.BntSave.TabIndex = 306
        Me.BntSave.Text = "&Start"
        '
        'SimpleButton1
        '
        Me.SimpleButton1.AllowFocus = False
        Me.SimpleButton1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SimpleButton1.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.SimpleButton1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton1.Appearance.Options.UseBackColor = True
        Me.SimpleButton1.Appearance.Options.UseFont = True
        Me.SimpleButton1.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!)
        Me.SimpleButton1.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.SimpleButton1.AppearanceDisabled.Options.UseFont = True
        Me.SimpleButton1.AppearanceDisabled.Options.UseForeColor = True
        Me.SimpleButton1.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.SimpleButton1.AppearanceHovered.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!)
        Me.SimpleButton1.AppearanceHovered.Options.UseBackColor = True
        Me.SimpleButton1.AppearanceHovered.Options.UseFont = True
        Me.SimpleButton1.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.SimpleButton1.AppearancePressed.Options.UseBackColor = True
        Me.SimpleButton1.Location = New System.Drawing.Point(597, 711)
        Me.SimpleButton1.LookAndFeel.SkinName = "Darkroom"
        Me.SimpleButton1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(152, 55)
        Me.SimpleButton1.TabIndex = 308
        Me.SimpleButton1.Text = "&Save"
        '
        'BntResetAll
        '
        Me.BntResetAll.AllowFocus = False
        Me.BntResetAll.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BntResetAll.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(95, Byte), Integer))
        Me.BntResetAll.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.BntResetAll.Appearance.Options.UseBackColor = True
        Me.BntResetAll.Appearance.Options.UseFont = True
        Me.BntResetAll.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!)
        Me.BntResetAll.AppearanceDisabled.ForeColor = System.Drawing.Color.DimGray
        Me.BntResetAll.AppearanceDisabled.Options.UseFont = True
        Me.BntResetAll.AppearanceDisabled.Options.UseForeColor = True
        Me.BntResetAll.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntResetAll.AppearanceHovered.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.8!)
        Me.BntResetAll.AppearanceHovered.Options.UseBackColor = True
        Me.BntResetAll.AppearanceHovered.Options.UseFont = True
        Me.BntResetAll.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(48, Byte), Integer), CType(CType(54, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.BntResetAll.AppearancePressed.Options.UseBackColor = True
        Me.BntResetAll.Location = New System.Drawing.Point(284, 711)
        Me.BntResetAll.LookAndFeel.SkinName = "Darkroom"
        Me.BntResetAll.LookAndFeel.UseDefaultLookAndFeel = False
        Me.BntResetAll.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BntResetAll.Name = "BntResetAll"
        Me.BntResetAll.Size = New System.Drawing.Size(152, 55)
        Me.BntResetAll.TabIndex = 309
        Me.BntResetAll.Text = "&Reset All"
        '
        'frmEmailSorter
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(30, Byte), Integer), CType(CType(57, Byte), Integer), CType(CType(91, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(922, 789)
        Me.Controls.Add(Me.Panel4)
        Me.Controls.Add(Me.BntResetAll)
        Me.Controls.Add(Me.SimpleButton1)
        Me.Controls.Add(Me.BntSave)
        Me.Controls.Add(Me.GroupControl1)
        Me.FormBorderEffect = DevExpress.XtraEditors.FormBorderEffect.Shadow
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Icon = CType(resources.GetObject("frmEmailSorter.IconOptions.Icon"), System.Drawing.Icon)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.Logo_NewBestSender
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Name = "frmEmailSorter"
        Me.Opacity = 0.98R
        Me.ShowInTaskbar = False
        Me.Text = " Email Sorter..."
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Col_MailLogo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BNT_Details, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Bnt_Remove, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Col_Chk, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.RepositoryItemMemoEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckListPath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtPath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel4.ResumeLayout(False)
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupControl1 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Label1 As Label
    Friend WithEvents BntBrowse As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txtPath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CheckListPath As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents trmfadein As Timer
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents BntSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntResetAll As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntSelectAll As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Panel4 As Panel
    Friend WithEvents BntStop As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ProgressBarControl1 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Col_MailLogo As DevExpress.XtraEditors.Repository.RepositoryItemPictureEdit
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn11 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn5 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents BNT_Details As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents GridColumn6 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Bnt_Remove As DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit
    Friend WithEvents GridColumn8 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn7 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn4 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn9 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents Col_Chk As DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit
    Friend WithEvents GridColumn10 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents RepositoryItemMemoEdit1 As DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit
End Class
