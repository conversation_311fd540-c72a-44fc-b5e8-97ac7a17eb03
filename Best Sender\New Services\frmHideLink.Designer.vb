﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmHideLink
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.SaveFileDialog1 = New System.Windows.Forms.SaveFileDialog()
        Me.SeparatorControl1 = New DevExpress.XtraEditors.SeparatorControl()
        Me.TextBoxlogoPassCLear = New DevExpress.XtraEditors.SimpleButton()
        Me.TextBoxtitelPassClear = New DevExpress.XtraEditors.SimpleButton()
        Me.textPagePass1CLear = New DevExpress.XtraEditors.SimpleButton()
        Me.TextBox2LinkPage = New DevExpress.XtraEditors.TextEdit()
        Me.TextBox1MaskLink = New DevExpress.XtraEditors.TextEdit()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.txtPathHideLink = New DevExpress.XtraEditors.TextEdit()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.BtnSave = New DevExpress.XtraEditors.SimpleButton()
        Me.btn_Encoded = New DevExpress.XtraEditors.SimpleButton()
        Me.BntCLear = New DevExpress.XtraEditors.SimpleButton()
        Me.btn_Uploade_HTML = New DevExpress.XtraEditors.SimpleButton()
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox2LinkPage.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBox1MaskLink.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtPathHideLink.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'SeparatorControl1
        '
        Me.SeparatorControl1.LineThickness = 1
        Me.SeparatorControl1.Location = New System.Drawing.Point(355, 160)
        Me.SeparatorControl1.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl1.Name = "SeparatorControl1"
        Me.SeparatorControl1.Padding = New System.Windows.Forms.Padding(10, 9, 10, 9)
        Me.SeparatorControl1.Size = New System.Drawing.Size(420, 27)
        Me.SeparatorControl1.TabIndex = 519
        '
        'TextBoxlogoPassCLear
        '
        Me.TextBoxlogoPassCLear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxlogoPassCLear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxlogoPassCLear.Appearance.Options.UseBackColor = True
        Me.TextBoxlogoPassCLear.Appearance.Options.UseBorderColor = True
        Me.TextBoxlogoPassCLear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.TextBoxlogoPassCLear.Location = New System.Drawing.Point(892, 328)
        Me.TextBoxlogoPassCLear.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBoxlogoPassCLear.Name = "TextBoxlogoPassCLear"
        Me.TextBoxlogoPassCLear.Size = New System.Drawing.Size(27, 23)
        Me.TextBoxlogoPassCLear.TabIndex = 586
        '
        'TextBoxtitelPassClear
        '
        Me.TextBoxtitelPassClear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxtitelPassClear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBoxtitelPassClear.Appearance.Options.UseBackColor = True
        Me.TextBoxtitelPassClear.Appearance.Options.UseBorderColor = True
        Me.TextBoxtitelPassClear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.TextBoxtitelPassClear.Location = New System.Drawing.Point(892, 252)
        Me.TextBoxtitelPassClear.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBoxtitelPassClear.Name = "TextBoxtitelPassClear"
        Me.TextBoxtitelPassClear.Size = New System.Drawing.Size(27, 23)
        Me.TextBoxtitelPassClear.TabIndex = 587
        '
        'textPagePass1CLear
        '
        Me.textPagePass1CLear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.textPagePass1CLear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.textPagePass1CLear.Appearance.Options.UseBackColor = True
        Me.textPagePass1CLear.Appearance.Options.UseBorderColor = True
        Me.textPagePass1CLear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_Image
        Me.textPagePass1CLear.Location = New System.Drawing.Point(892, 404)
        Me.textPagePass1CLear.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.textPagePass1CLear.Name = "textPagePass1CLear"
        Me.textPagePass1CLear.Size = New System.Drawing.Size(27, 23)
        Me.textPagePass1CLear.TabIndex = 585
        '
        'TextBox2LinkPage
        '
        Me.TextBox2LinkPage.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBox2LinkPage.EditValue = "https://Example.com"
        Me.TextBox2LinkPage.Location = New System.Drawing.Point(216, 401)
        Me.TextBox2LinkPage.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBox2LinkPage.Name = "TextBox2LinkPage"
        Me.TextBox2LinkPage.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBox2LinkPage.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox2LinkPage.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBox2LinkPage.Properties.Appearance.Options.UseBackColor = True
        Me.TextBox2LinkPage.Properties.Appearance.Options.UseFont = True
        Me.TextBox2LinkPage.Properties.Appearance.Options.UseForeColor = True
        Me.TextBox2LinkPage.Properties.NullValuePrompt = "This area is password protected "
        Me.TextBox2LinkPage.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBox2LinkPage.Size = New System.Drawing.Size(704, 30)
        Me.TextBox2LinkPage.TabIndex = 584
        '
        'TextBox1MaskLink
        '
        Me.TextBox1MaskLink.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TextBox1MaskLink.EditValue = "https://outlook.office.com/mail"
        Me.TextBox1MaskLink.Location = New System.Drawing.Point(216, 325)
        Me.TextBox1MaskLink.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TextBox1MaskLink.Name = "TextBox1MaskLink"
        Me.TextBox1MaskLink.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.TextBox1MaskLink.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBox1MaskLink.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TextBox1MaskLink.Properties.Appearance.Options.UseBackColor = True
        Me.TextBox1MaskLink.Properties.Appearance.Options.UseFont = True
        Me.TextBox1MaskLink.Properties.Appearance.Options.UseForeColor = True
        Me.TextBox1MaskLink.Properties.NullValuePrompt = "Enter Link Logo"
        Me.TextBox1MaskLink.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TextBox1MaskLink.Size = New System.Drawing.Size(703, 30)
        Me.TextBox1MaskLink.TabIndex = 583
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label1.Location = New System.Drawing.Point(216, 369)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(76, 21)
        Me.Label1.TabIndex = 581
        Me.Label1.Text = "Link Page"
        '
        'txtPathHideLink
        '
        Me.txtPathHideLink.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtPathHideLink.EditValue = ""
        Me.txtPathHideLink.Location = New System.Drawing.Point(216, 249)
        Me.txtPathHideLink.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtPathHideLink.Name = "txtPathHideLink"
        Me.txtPathHideLink.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtPathHideLink.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtPathHideLink.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtPathHideLink.Properties.Appearance.Options.UseBackColor = True
        Me.txtPathHideLink.Properties.Appearance.Options.UseFont = True
        Me.txtPathHideLink.Properties.Appearance.Options.UseForeColor = True
        Me.txtPathHideLink.Properties.NullValuePrompt = "Your Titel"
        Me.txtPathHideLink.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtPathHideLink.Size = New System.Drawing.Size(703, 30)
        Me.txtPathHideLink.TabIndex = 582
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label3.Location = New System.Drawing.Point(216, 293)
        Me.Label3.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(78, 21)
        Me.Label3.TabIndex = 580
        Me.Label3.Text = "Mask Link"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(216, 217)
        Me.Label2.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(70, 21)
        Me.Label2.TabIndex = 579
        Me.Label2.Text = "File path"
        '
        'BtnSave
        '
        Me.BtnSave.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnSave.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnSave.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BtnSave.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnSave.Appearance.Options.UseBackColor = True
        Me.BtnSave.Appearance.Options.UseBorderColor = True
        Me.BtnSave.Appearance.Options.UseFont = True
        Me.BtnSave.Appearance.Options.UseForeColor = True
        Me.BtnSave.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BtnSave.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BtnSave.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BtnSave.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BtnSave.AppearanceDisabled.Options.UseBackColor = True
        Me.BtnSave.AppearanceDisabled.Options.UseBorderColor = True
        Me.BtnSave.AppearanceDisabled.Options.UseFont = True
        Me.BtnSave.AppearanceDisabled.Options.UseForeColor = True
        Me.BtnSave.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BtnSave.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnSave.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BtnSave.AppearanceHovered.Options.UseBackColor = True
        Me.BtnSave.AppearanceHovered.Options.UseBorderColor = True
        Me.BtnSave.AppearanceHovered.Options.UseForeColor = True
        Me.BtnSave.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BtnSave.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BtnSave.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BtnSave.AppearancePressed.Options.UseBackColor = True
        Me.BtnSave.AppearancePressed.Options.UseBorderColor = True
        Me.BtnSave.AppearancePressed.Options.UseForeColor = True
        Me.BtnSave.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.settings32x32
        Me.BtnSave.Location = New System.Drawing.Point(755, 457)
        Me.BtnSave.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.BtnSave.Name = "BtnSave"
        Me.BtnSave.Size = New System.Drawing.Size(171, 44)
        Me.BtnSave.TabIndex = 591
        Me.BtnSave.Text = "Save All Settings"
        '
        'btn_Encoded
        '
        Me.btn_Encoded.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Encoded.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Encoded.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Encoded.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Encoded.Appearance.Options.UseBackColor = True
        Me.btn_Encoded.Appearance.Options.UseBorderColor = True
        Me.btn_Encoded.Appearance.Options.UseFont = True
        Me.btn_Encoded.Appearance.Options.UseForeColor = True
        Me.btn_Encoded.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn_Encoded.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn_Encoded.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Encoded.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn_Encoded.AppearanceDisabled.Options.UseBackColor = True
        Me.btn_Encoded.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn_Encoded.AppearanceDisabled.Options.UseFont = True
        Me.btn_Encoded.AppearanceDisabled.Options.UseForeColor = True
        Me.btn_Encoded.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn_Encoded.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Encoded.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn_Encoded.AppearanceHovered.Options.UseBackColor = True
        Me.btn_Encoded.AppearanceHovered.Options.UseBorderColor = True
        Me.btn_Encoded.AppearanceHovered.Options.UseForeColor = True
        Me.btn_Encoded.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Encoded.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Encoded.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn_Encoded.AppearancePressed.Options.UseBackColor = True
        Me.btn_Encoded.AppearancePressed.Options.UseBorderColor = True
        Me.btn_Encoded.AppearancePressed.Options.UseForeColor = True
        Me.btn_Encoded.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.btn_Encoded.Location = New System.Drawing.Point(398, 457)
        Me.btn_Encoded.Margin = New System.Windows.Forms.Padding(4)
        Me.btn_Encoded.Name = "btn_Encoded"
        Me.btn_Encoded.Size = New System.Drawing.Size(171, 44)
        Me.btn_Encoded.TabIndex = 590
        Me.btn_Encoded.Text = "Start / Build"
        '
        'BntCLear
        '
        Me.BntCLear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Options.UseBackColor = True
        Me.BntCLear.Appearance.Options.UseBorderColor = True
        Me.BntCLear.Appearance.Options.UseFont = True
        Me.BntCLear.Appearance.Options.UseForeColor = True
        Me.BntCLear.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntCLear.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntCLear.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntCLear.AppearanceDisabled.Options.UseBackColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseFont = True
        Me.BntCLear.AppearanceDisabled.Options.UseForeColor = True
        Me.BntCLear.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntCLear.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntCLear.AppearanceHovered.Options.UseBackColor = True
        Me.BntCLear.AppearanceHovered.Options.UseBorderColor = True
        Me.BntCLear.AppearanceHovered.Options.UseForeColor = True
        Me.BntCLear.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntCLear.AppearancePressed.Options.UseBackColor = True
        Me.BntCLear.AppearancePressed.Options.UseBorderColor = True
        Me.BntCLear.AppearancePressed.Options.UseForeColor = True
        Me.BntCLear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntCLear.Location = New System.Drawing.Point(576, 457)
        Me.BntCLear.Margin = New System.Windows.Forms.Padding(4)
        Me.BntCLear.Name = "BntCLear"
        Me.BntCLear.Size = New System.Drawing.Size(171, 44)
        Me.BntCLear.TabIndex = 589
        Me.BntCLear.Text = "Reset All"
        '
        'btn_Uploade_HTML
        '
        Me.btn_Uploade_HTML.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Uploade_HTML.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Uploade_HTML.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Uploade_HTML.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Uploade_HTML.Appearance.Options.UseBackColor = True
        Me.btn_Uploade_HTML.Appearance.Options.UseBorderColor = True
        Me.btn_Uploade_HTML.Appearance.Options.UseFont = True
        Me.btn_Uploade_HTML.Appearance.Options.UseForeColor = True
        Me.btn_Uploade_HTML.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn_Uploade_HTML.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn_Uploade_HTML.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Uploade_HTML.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn_Uploade_HTML.AppearanceDisabled.Options.UseBackColor = True
        Me.btn_Uploade_HTML.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn_Uploade_HTML.AppearanceDisabled.Options.UseFont = True
        Me.btn_Uploade_HTML.AppearanceDisabled.Options.UseForeColor = True
        Me.btn_Uploade_HTML.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn_Uploade_HTML.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Uploade_HTML.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn_Uploade_HTML.AppearanceHovered.Options.UseBackColor = True
        Me.btn_Uploade_HTML.AppearanceHovered.Options.UseBorderColor = True
        Me.btn_Uploade_HTML.AppearanceHovered.Options.UseForeColor = True
        Me.btn_Uploade_HTML.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Uploade_HTML.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Uploade_HTML.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn_Uploade_HTML.AppearancePressed.Options.UseBackColor = True
        Me.btn_Uploade_HTML.AppearancePressed.Options.UseBorderColor = True
        Me.btn_Uploade_HTML.AppearancePressed.Options.UseForeColor = True
        Me.btn_Uploade_HTML.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.btn_Uploade_HTML.Location = New System.Drawing.Point(219, 457)
        Me.btn_Uploade_HTML.Margin = New System.Windows.Forms.Padding(4)
        Me.btn_Uploade_HTML.Name = "btn_Uploade_HTML"
        Me.btn_Uploade_HTML.Size = New System.Drawing.Size(171, 44)
        Me.btn_Uploade_HTML.TabIndex = 588
        Me.btn_Uploade_HTML.Text = "Select.HTML...."
        '
        'RichTextBox1
        '
        Me.RichTextBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(12, Byte), Integer), CType(CType(16, Byte), Integer), CType(CType(28, Byte), Integer))
        Me.RichTextBox1.ForeColor = System.Drawing.Color.White
        Me.RichTextBox1.Location = New System.Drawing.Point(972, 33)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(166, 118)
        Me.RichTextBox1.TabIndex = 592
        Me.RichTextBox1.Text = ""
        Me.RichTextBox1.Visible = False
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.HideLinkPaner
        Me.PictureBox1.Location = New System.Drawing.Point(246, 69)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(647, 117)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 593
        Me.PictureBox1.TabStop = False
        '
        'frmHideLink
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(39, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseBorderColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1223, 611)
        Me.Controls.Add(Me.RichTextBox1)
        Me.Controls.Add(Me.BtnSave)
        Me.Controls.Add(Me.btn_Encoded)
        Me.Controls.Add(Me.BntCLear)
        Me.Controls.Add(Me.btn_Uploade_HTML)
        Me.Controls.Add(Me.TextBoxlogoPassCLear)
        Me.Controls.Add(Me.TextBoxtitelPassClear)
        Me.Controls.Add(Me.textPagePass1CLear)
        Me.Controls.Add(Me.TextBox2LinkPage)
        Me.Controls.Add(Me.TextBox1MaskLink)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.txtPathHideLink)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.SeparatorControl1)
        Me.Controls.Add(Me.PictureBox1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.IMAGE_HideLink
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmHideLink"
        Me.ShowInTaskbar = False
        Me.Text = "Hide Your Link"
        CType(Me.SeparatorControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox2LinkPage.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBox1MaskLink.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtPathHideLink.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents SaveFileDialog1 As SaveFileDialog
    Friend WithEvents SeparatorControl1 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents TextBoxlogoPassCLear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextBoxtitelPassClear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents textPagePass1CLear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextBox2LinkPage As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextBox1MaskLink As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label1 As Label
    Friend WithEvents txtPathHideLink As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label3 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents BtnSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btn_Encoded As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntCLear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btn_Uploade_HTML As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents PictureBox1 As PictureBox
End Class
