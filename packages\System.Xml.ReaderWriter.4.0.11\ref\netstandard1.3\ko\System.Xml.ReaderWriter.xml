﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.ReaderWriter</name>
  </assembly>
  <members>
    <member name="T:System.Xml.ConformanceLevel">
      <summary>만들어진 <see cref="T:System.Xml.XmlReader" /> 및 <see cref="T:System.Xml.XmlWriter" /> 개체에서 수행할 입력 또는 출력 검사 수준을 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Auto">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> 또는 <see cref="T:System.Xml.XmlWriter" /> 개체가 문서 또는 조각 검사의 수행 여부를 자동으로 확인하고 적합한 검사를 수행합니다.다른 <see cref="T:System.Xml.XmlReader" /> 또는 <see cref="T:System.Xml.XmlWriter" /> 개체를 래핑하면 외부 개체는 추가 규칙 검사를 수행하지 않습니다.내부 개체에서만 규칙 검사를 수행합니다.규격 수준을 결정하는 데 대한 자세한 내용은 <see cref="P:System.Xml.XmlReaderSettings.ConformanceLevel" /> 및 <see cref="P:System.Xml.XmlWriterSettings.ConformanceLevel" /> 속성을 참조하세요.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Document">
      <summary>XML 데이터는 W3C가 정의한 대로 올바른 형식의 XML 1.0 문서에 대한 규칙을 준수합니다.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Fragment">
      <summary>XML 데이터는 W3C가 정의한 대로 올바른 형식의 XML 조각입니다.</summary>
    </member>
    <member name="T:System.Xml.DtdProcessing">
      <summary>DTD 처리 옵션을 지정합니다.<see cref="T:System.Xml.DtdProcessing" /> 열거형은 <see cref="T:System.Xml.XmlReaderSettings" /> 클래스에서 사용됩니다.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Ignore">
      <summary>DOCTYPE 요소가 무시됩니다.DTD 처리가 수행되지 않습니다.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Prohibit">
      <summary>DTD가 발견되면 DTD가 금지되었다는 메시지와 함께 <see cref="T:System.Xml.XmlException" />이 throw되도록 지정합니다.이것은 기본적인 동작입니다.</summary>
    </member>
    <member name="T:System.Xml.IXmlLineInfo">
      <summary>클래스에서 줄과 위치 정보를 반환할 수 있는 인터페이스를 제공합니다.</summary>
    </member>
    <member name="M:System.Xml.IXmlLineInfo.HasLineInfo">
      <summary>클래스에서 줄 정보를 반환할 수 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="P:System.Xml.IXmlLineInfo.LineNumber" /> 및 <see cref="P:System.Xml.IXmlLineInfo.LinePosition" />을 사용할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LineNumber">
      <summary>현재 줄 번호를 가져옵니다.</summary>
      <returns>현재 줄 번호이거나, 예를 들어 <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" />에서 false를 반환하는 경우와 같이 줄 정보가 없는 경우에는 0입니다.</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LinePosition">
      <summary>현재 줄 위치를 가져옵니다.</summary>
      <returns>현재 줄 위치이거나, 예를 들어 <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" />에서 false를 반환하는 경우와 같이 줄 정보가 없는 경우에는 0입니다.</returns>
    </member>
    <member name="T:System.Xml.IXmlNamespaceResolver">
      <summary>접두사 및 네임스페이스 매핑 집합에 읽기 전용으로 액세스하는 데 사용됩니다.</summary>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>현재 범위 내에 정의된 접두사-네임스페이스 매핑 컬렉션을 가져옵니다.</summary>
      <returns>현재 범위 내의 네임스페이스가 포함된 <see cref="T:System.Collections.IDictionary" />입니다.</returns>
      <param name="scope">반환할 네임스페이스 노드의 형식을 지정하는 <see cref="T:System.Xml.XmlNamespaceScope" /> 값입니다.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupNamespace(System.String)">
      <summary>지정된 접두사에 매핑된 네임스페이스 URI를 가져옵니다.</summary>
      <returns>접두사에 매핑된 네임스페이스 URI이거나, 접두사가 네임스페이스 URI에 매핑되지 않은 경우 null입니다.</returns>
      <param name="prefix">찾을 네임스페이스 URI의 접두사입니다.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupPrefix(System.String)">
      <summary>지정된 네임스페이스 URI에 매핑된 접두사를 가져옵니다.</summary>
      <returns>네임스페이스 URI에 매핑된 접두사이거나, 네임스페이스 URI가 접두사에 매핑되지 않은 경우 null입니다.</returns>
      <param name="namespaceName">찾을 접두사의 네임스페이스 URI입니다.</param>
    </member>
    <member name="T:System.Xml.NamespaceHandling">
      <summary>
        <see cref="T:System.Xml.XmlWriter" />에서 중복된 네임스페이스 선언을 제거할지 여부를 지정합니다. </summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.Default">
      <summary>중복된 네임스페이스 선언을 제거하지 않도록 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.OmitDuplicates">
      <summary>중복된 네임스페이스 선언을 제거하도록 지정합니다.중복된 네임스페이스를 제거하려면 접두사와 네임스페이스가 일치해야 합니다.</summary>
    </member>
    <member name="T:System.Xml.NameTable">
      <summary>단일 스레드 <see cref="T:System.Xml.XmlNameTable" />을 구현합니다.</summary>
    </member>
    <member name="M:System.Xml.NameTable.#ctor">
      <summary>NameTable 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>지정된 문자열을 원자화하여 이를 NameTable에 추가합니다.</summary>
      <returns>원자화된 문자열이거나 NameTable에 이미 있을 경우 기존 문자열입니다.<paramref name="len" />이 0이면 String.Empty가 반환됩니다.</returns>
      <param name="key">추가할 문자열이 포함된 문자 배열입니다. </param>
      <param name="start">문자열의 첫 번째 문자를 지정하는 배열의 0부터 시작하는 인덱스입니다. </param>
      <param name="len">문자열에 있는 문자의 수입니다. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />또는 <paramref name="start" /> &gt;= <paramref name="key" />.Length 또는 <paramref name="len" /> &gt;= <paramref name="key" />.Length 위의 경우 <paramref name="len" /> =0이면 예외가 throw되지 않습니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0 </exception>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.String)">
      <summary>지정된 문자열을 원자화하여 이를 NameTable에 추가합니다.</summary>
      <returns>원자화된 문자열이거나 NameTable에 이미 있을 경우 기존 문자열입니다.</returns>
      <param name="key">추가할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null입니다. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>주어진 배열의 지정된 문자 범위와 같은 문자가 포함된 원자화된 문자열을 가져옵니다.</summary>
      <returns>문자열이 이미 원자화되지 않은 경우 원자화된 문자열 또는 null입니다.<paramref name="len" />이 0이면 String.Empty가 반환됩니다.</returns>
      <param name="key">찾을 이름이 포함된 문자 배열입니다. </param>
      <param name="start">이름의 첫 번째 문자를 지정하는 배열의 0부터 시작하는 인덱스입니다. </param>
      <param name="len">이름에 있는 문자의 수입니다. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />또는 <paramref name="start" /> &gt;= <paramref name="key" />.Length 또는 <paramref name="len" /> &gt;= <paramref name="key" />.Length 위의 경우 <paramref name="len" /> =0이면 예외가 throw되지 않습니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0 </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.String)">
      <summary>지정된 값을 가진 원자화된 문자열을 가져옵니다.</summary>
      <returns>원자화된 문자열이거나 문자열이 이미 원자화되지 않은 경우에는 null입니다.</returns>
      <param name="value">찾을 이름입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" />가 null입니다. </exception>
    </member>
    <member name="T:System.Xml.NewLineHandling">
      <summary>줄 바꿈을 처리하는 방법을 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Entitize">
      <summary>새 줄 문자를 엔터티화합니다.이 설정을 사용하면 정규화 <see cref="T:System.Xml.XmlReader" />에서 출력을 읽을 경우 모든 문자가 유지됩니다.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.None">
      <summary>새 줄 문자를 변경하지 않습니다.이 경우에는 입력과 출력이 같습니다.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Replace">
      <summary>
        <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> 속성에 지정된 문자와 일치하도록 새 줄 문자를 바꿉니다.</summary>
    </member>
    <member name="T:System.Xml.ReadState">
      <summary>판독기의 상태를 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Closed">
      <summary>
        <see cref="M:System.Xml.XmlReader.Close" /> 메서드가 호출되었습니다.</summary>
    </member>
    <member name="F:System.Xml.ReadState.EndOfFile">
      <summary>파일 끝에 성공적으로 도달했습니다.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Error">
      <summary>읽기 작업을 계속할 수 없는 오류가 발생했습니다.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Initial">
      <summary>Read 메서드가 호출되지 않았습니다.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Interactive">
      <summary>Read 메서드가 호출되었습니다.판독기에 메서드가 추가로 호출될 수 있습니다.</summary>
    </member>
    <member name="T:System.Xml.WriteState">
      <summary>
        <see cref="T:System.Xml.XmlWriter" />의 상태를 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Attribute">
      <summary>특성 값을 쓰고 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Closed">
      <summary>
        <see cref="M:System.Xml.XmlWriter.Close" /> 메서드가 이미 호출되었음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Content">
      <summary>요소 내용을 쓰고 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Element">
      <summary>요소 시작 태그를 쓰고 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Error">
      <summary>예외가 throw되어 <see cref="T:System.Xml.XmlWriter" />가 잘못된 상태에 있습니다.<see cref="M:System.Xml.XmlWriter.Close" /> 메서드를 호출하여 <see cref="T:System.Xml.XmlWriter" />를 <see cref="F:System.Xml.WriteState.Closed" /> 상태로 설정할 수 있습니다.이외의 경우 <see cref="T:System.Xml.XmlWriter" /> 메서드를 호출하면 <see cref="T:System.InvalidOperationException" />이 throw됩니다.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Prolog">
      <summary>프롤로그를 쓰고 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Start">
      <summary>Write 메서드가 아직 호출되지 않았음을 나타냅니다.</summary>
    </member>
    <member name="T:System.Xml.XmlConvert">
      <summary>XML 이름을 인코딩 및 디코딩하고 공용 언어 런타임 형식과 XSD(XML 스키마 정의) 언어 형식 사이의 변환 메서드를 제공합니다.데이터 형식을 변환할 때 반환되는 값은 로캘과 무관합니다.</summary>
    </member>
    <member name="M:System.Xml.XmlConvert.DecodeName(System.String)">
      <summary>이름을 디코딩합니다.이 메서드는 <see cref="M:System.Xml.XmlConvert.EncodeName(System.String)" /> 메서드 및 <see cref="M:System.Xml.XmlConvert.EncodeLocalName(System.String)" /> 메서드와 반대로 수행합니다.</summary>
      <returns>디코딩한 이름입니다.</returns>
      <param name="name">변환될 이름입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeLocalName(System.String)">
      <summary>이름을 올바른 XML 로컬 이름으로 변환합니다.</summary>
      <returns>인코딩된 이름입니다.</returns>
      <param name="name">인코딩할 이름입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeName(System.String)">
      <summary>이름을 올바른 XML 이름으로 변환합니다.</summary>
      <returns>잘못된 문자가 이스케이프 문자열로 바뀐 이름을 반환합니다.</returns>
      <param name="name">변환할 이름입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeNmToken(System.String)">
      <summary>XML 사양에 따라 올바른 이름인지 확인합니다.</summary>
      <returns>인코딩된 이름입니다.</returns>
      <param name="name">인코딩할 이름입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToBoolean(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.Boolean" /> 값으로 변환합니다.</summary>
      <returns>Boolean 값, 즉 true 또는 false입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> does not represent a Boolean value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToByte(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.Byte" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 Byte 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToChar(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.Char" /> 값으로 변환합니다.</summary>
      <returns>단일 문자를 나타내는 Char입니다.</returns>
      <param name="s">변환할 단일 문자가 포함된 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="s" /> parameter is null. </exception>
      <exception cref="T:System.FormatException">The <paramref name="s" /> parameter contains more than one character. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTime(System.String,System.Xml.XmlDateTimeSerializationMode)">
      <summary>지정된 <see cref="T:System.Xml.XmlDateTimeSerializationMode" />를 사용하여 <see cref="T:System.String" />을 <see cref="T:System.DateTime" />으로 변환합니다.</summary>
      <returns>
        <see cref="T:System.String" />에 해당하는 <see cref="T:System.DateTime" /> 값입니다.</returns>
      <param name="s">변환할 <see cref="T:System.String" /> 값입니다.</param>
      <param name="dateTimeOption">UTC(Coordinated Universal Time) 날짜를 현지 시간으로 변환할지 아니면 UTC로 유지할지 지정하는 <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> 값 중 하나입니다.</param>
      <exception cref="T:System.NullReferenceException">
        <paramref name="s" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="dateTimeOption" /> value is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is an empty string or is not in a valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String)">
      <summary>제공된 <see cref="T:System.String" />을 해당 <see cref="T:System.DateTimeOffset" />으로 변환합니다.</summary>
      <returns>제공된 문자열에 해당하는 <see cref="T:System.DateTimeOffset" />입니다.</returns>
      <param name="s">변환할 문자열입니다.참고   이 문자열은 W3C 권장 사항 중 XML dateTime 형식에 대한 부분을 준수해야 합니다.자세한 내용은 http://www.w3.org/TR/xmlschema-2/#dateTime을 참조하십시오.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The argument passed to this method is outside the range of allowable values.For information about allowable values, see <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.FormatException">The argument passed to this method does not conform to a subset of the W3C Recommendations for the XML dateTime type.For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String)">
      <summary>제공된 <see cref="T:System.String" />을 해당 <see cref="T:System.DateTimeOffset" />으로 변환합니다.</summary>
      <returns>제공된 문자열에 해당하는 <see cref="T:System.DateTimeOffset" />입니다.</returns>
      <param name="s">변환할 문자열입니다.</param>
      <param name="format">
        <paramref name="s" />를 변환할 형식입니다.형식 매개 변수는 W3C 권장 사항 중 XML dateTime 형식에 대한 부분이 될 수 있습니다.자세한 내용은 http://www.w3.org/TR/xmlschema-2/#dateTime을 참조하십시오. 이 형식을 사용하여 문자열 <paramref name="s" />의 유효성을 검사합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> or <paramref name="format" /> is an empty string or is not in the specified format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String[])">
      <summary>제공된 <see cref="T:System.String" />을 해당 <see cref="T:System.DateTimeOffset" />으로 변환합니다.</summary>
      <returns>제공된 문자열에 해당하는 <see cref="T:System.DateTimeOffset" />입니다.</returns>
      <param name="s">변환할 문자열입니다.</param>
      <param name="formats">
        <paramref name="s" />를 변환할 수 있는 형식의 배열입니다.<paramref name="formats" />의 각 형식은 W3C 권장 사항 중 XML dateTime 형식에 대한 부분이 될 수 있습니다.자세한 내용은 http://www.w3.org/TR/xmlschema-2/#dateTime을 참조하십시오. 이러한 형식 중 하나를 사용하여 문자열 <paramref name="s" />의 유효성을 검사합니다.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDecimal(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.Decimal" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 Decimal 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDouble(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.Double" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 Double 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToGuid(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.Guid" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 Guid 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt16(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.Int16" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 Int16 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt32(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.Int32" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 Int32 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt64(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.Int64" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 Int64 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSByte(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.SByte" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 SByte 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSingle(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.Single" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 Single 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Boolean)">
      <summary>
        <see cref="T:System.Boolean" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>Boolean에 대한 문자열 표현, 즉 "true" 또는 "false"입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Byte)">
      <summary>
        <see cref="T:System.Byte" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>Byte의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Char)">
      <summary>
        <see cref="T:System.Char" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>Char의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTime,System.Xml.XmlDateTimeSerializationMode)">
      <summary>지정된 <see cref="T:System.Xml.XmlDateTimeSerializationMode" />를 사용하여 <see cref="T:System.DateTime" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>
        <see cref="T:System.DateTime" />에 해당하는 <see cref="T:System.String" /> 값입니다.</returns>
      <param name="value">변환할 <see cref="T:System.DateTime" /> 값입니다.</param>
      <param name="dateTimeOption">
        <see cref="T:System.DateTime" /> 값 처리 방법을 지정하는 <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="dateTimeOption" /> value is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> or <paramref name="dateTimeOption" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset)">
      <summary>제공된 <see cref="T:System.DateTimeOffset" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>제공된 <see cref="T:System.DateTimeOffset" />의 <see cref="T:System.String" /> 표현입니다.</returns>
      <param name="value">변환될 <see cref="T:System.DateTimeOffset" />입니다.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset,System.String)">
      <summary>제공된 <see cref="T:System.DateTimeOffset" />을 지정된 형식의 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>제공된 <see cref="T:System.DateTimeOffset" />을 지정된 형식으로 나타낸 <see cref="T:System.String" />입니다.</returns>
      <param name="value">변환될 <see cref="T:System.DateTimeOffset" />입니다.</param>
      <param name="format">
        <paramref name="s" />를 변환할 대상 형식입니다.형식 매개 변수는 W3C 권장 사항 중 XML dateTime 형식에 대한 부분이 될 수 있습니다.자세한 내용은 http://www.w3.org/TR/xmlschema-2/#dateTime을 참조하십시오.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Decimal)">
      <summary>
        <see cref="T:System.Decimal" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>Decimal의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Double)">
      <summary>
        <see cref="T:System.Double" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>Double의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Guid)">
      <summary>
        <see cref="T:System.Guid" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>Guid의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int16)">
      <summary>
        <see cref="T:System.Int16" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>Int16의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int32)">
      <summary>
        <see cref="T:System.Int32" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>Int32의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int64)">
      <summary>
        <see cref="T:System.Int64" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>Int64의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.SByte)">
      <summary>
        <see cref="T:System.SByte" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>SByte의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Single)">
      <summary>
        <see cref="T:System.Single" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>Single의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.TimeSpan)">
      <summary>
        <see cref="T:System.TimeSpan" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>TimeSpan의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt16)">
      <summary>
        <see cref="T:System.UInt16" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>UInt16의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt32)">
      <summary>
        <see cref="T:System.UInt32" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>UInt32의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt64)">
      <summary>
        <see cref="T:System.UInt64" />을 <see cref="T:System.String" />으로 변환합니다.</summary>
      <returns>UInt64의 문자열 표현입니다.</returns>
      <param name="value">변환할 값입니다. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToTimeSpan(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.TimeSpan" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 TimeSpan 값입니다.</returns>
      <param name="s">변환할 문자열입니다.형식 문자열은 기간에 대한 W3C XML Schema Part 2: Datatypes 권장 사항을 준수해야 합니다.</param>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in correct format to represent a TimeSpan value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt16(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.UInt16" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 UInt16 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt32(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.UInt32" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 UInt32 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt64(System.String)">
      <summary>
        <see cref="T:System.String" />을 해당하는 <see cref="T:System.UInt64" /> 값으로 변환합니다.</summary>
      <returns>문자열에 해당하는 UInt64 값입니다.</returns>
      <param name="s">변환할 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyName(System.String)">
      <summary>해당 이름이 W3C Extended Markup Language 권장 사항에 따라 올바른 이름인지 확인합니다.</summary>
      <returns>이름입니다. 단, 이름이 올바른 XML 이름인 경우에 한합니다.</returns>
      <param name="name">유효성을 확인할 이름입니다. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid XML name. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNCName(System.String)">
      <summary>이름이 W3C Extended Markup Language 권장 사항에 따라 올바른 NCName인지 확인합니다.NCName은 콜론이 포함될 수 없는 이름입니다.</summary>
      <returns>이름입니다. 단, 이름이 올바른 NCName인 경우에 한합니다.</returns>
      <param name="name">유효성을 확인할 이름입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid non-colon name. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNMTOKEN(System.String)">
      <summary>W3C XML Schema Part2: Datatypes 권장 사항을 기준으로 문자열이 올바른 NMTOKEN인지 확인합니다.</summary>
      <returns>이름 토큰입니다. 단, 문자열이 올바른 NMTOKEN인 경우에 한합니다.</returns>
      <param name="name">확인할 문자열입니다.</param>
      <exception cref="T:System.Xml.XmlException">The string is not a valid name token.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyPublicId(System.String)">
      <summary>문자열 인수에 있는 모든 문자가 올바른 공용 ID 문자이면 전달된 문자열 인스턴스를 반환합니다.</summary>
      <returns>인수에 있는 모든 문자가 올바른 공용 ID 문자이면 전달된 문자열을 반환합니다.</returns>
      <param name="publicId">유효성을 검사할 ID가 포함된 <see cref="T:System.String" />입니다.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyWhitespace(System.String)">
      <summary>문자열 인수에 있는 모든 문자가 올바른 공백 문자이면 전달된 문자열 인스턴스를 반환합니다. </summary>
      <returns>문자열 인수에 있는 모든 문자가 올바른 공백 문자이면 전달된 문자열 인스턴스를 반환하고, 그렇지 않으면 null을 반환합니다.</returns>
      <param name="content">확인할 <see cref="T:System.String" />입니다.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyXmlChars(System.String)">
      <summary>문자열 인수의 모든 문자와 서로게이트 쌍 문자가 올바른 XML 문자인 경우 전달된 문자열을 반환하고 그렇지 않으면 첫 번째 잘못된 문자에 대한 정보로 XmlException을 throw합니다. </summary>
      <returns>문자열 인수의 모든 문자와 서로게이트 쌍 문자가 올바른 XML 문자인 경우 전달된 문자열을 반환하고 그렇지 않으면 첫 번째 잘못된 문자에 대한 정보로 XmlException을 throw합니다.</returns>
      <param name="content">확인할 문자가 포함된 <see cref="T:System.String" />입니다.</param>
    </member>
    <member name="T:System.Xml.XmlDateTimeSerializationMode">
      <summary>문자열과 <see cref="T:System.DateTime" /> 사이에 변환할 때 시간 값을 처리하는 방법을 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Local">
      <summary>현지 시간으로 처리합니다.<see cref="T:System.DateTime" /> 개체가 UTC(Coordinated Universal Time)를 나타내면 값을 현지 시간으로 변환합니다.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.RoundtripKind">
      <summary>변환할 때 표준 시간대 정보를 유지합니다.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Unspecified">
      <summary>
        <see cref="T:System.DateTime" />을 문자열로 변환하는 경우 값을 현지 시간으로 처리합니다.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Utc">
      <summary>UTC로 처리합니다.<see cref="T:System.DateTime" /> 개체가 현지 시간을 나타내면 값을 UTC로 변환합니다.</summary>
    </member>
    <member name="T:System.Xml.XmlException">
      <summary>마지막 예외에 대한 자세한 정보를 반환합니다.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor">
      <summary>XmlException 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 XmlException 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">오류 설명입니다. </param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception)">
      <summary>XmlException 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">오류 조건에 대한 설명입니다. </param>
      <param name="innerException">XmlException을 throw한 <see cref="T:System.Exception" />입니다.이 값은 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception,System.Int32,System.Int32)">
      <summary>지정된 메시지, 내부 예외, 줄 번호 및 줄 위치를 갖는 XmlException 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">오류 설명입니다. </param>
      <param name="innerException">현재 예외의 원인이 되는 예외입니다.이 값은 null일 수 있습니다.</param>
      <param name="lineNumber">오류가 발생한 곳을 나타내는 줄 번호입니다. </param>
      <param name="linePosition">오류가 발생한 곳을 나타내는 줄 위치입니다. </param>
    </member>
    <member name="P:System.Xml.XmlException.LineNumber">
      <summary>오류가 발생한 곳을 나타내는 줄 번호를 가져옵니다.</summary>
      <returns>오류가 발생한 곳을 나타내는 줄 번호입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlException.LinePosition">
      <summary>오류가 발생한 곳을 나타내는 줄 위치를 가져옵니다.</summary>
      <returns>오류가 발생한 곳을 나타내는 줄 위치입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlException.Message">
      <summary>현재 예외를 설명하는 메시지를 가져옵니다.</summary>
      <returns>예외에 대한 이유를 설명하는 오류 메시지입니다.</returns>
    </member>
    <member name="T:System.Xml.XmlNamespaceManager">
      <summary>컬렉션에 대한 네임스페이스를 확인, 추가 및 제거하고 이 네임스페이스에 대한 범위 관리를 제공합니다. </summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.#ctor(System.Xml.XmlNameTable)">
      <summary>지정된 <see cref="T:System.Xml.XmlNameTable" />을 사용하여 <see cref="T:System.Xml.XmlNamespaceManager" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="nameTable">사용할 <see cref="T:System.Xml.XmlNameTable" />입니다. </param>
      <exception cref="T:System.NullReferenceException">null is passed to the constructor </exception>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.AddNamespace(System.String,System.String)">
      <summary>지정된 네임스페이스를 컬렉션에 추가합니다.</summary>
      <param name="prefix">추가할 네임스페이스와 관련된 접두사입니다.기본 네임스페이스를 추가하려면 String.Empty를 사용합니다.참고XPath(XML Path Language) 식에서 네임스페이스를 확인하는 데 <see cref="T:System.Xml.XmlNamespaceManager" />를 사용할 경우에는 접두사를 지정해야 합니다.XPath 식에 접두사가 없으면 네임스페이스 URI(Uniform Resource Identifier)를 빈 네임스페이스로 간주합니다.XPath 식 및 <see cref="T:System.Xml.XmlNamespaceManager" />에 대한 자세한 내용은 <see cref="M:System.Xml.XmlNode.SelectNodes(System.String)" /> 및 <see cref="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.XmlNamespaceManager)" /> 메서드를 참조하세요.</param>
      <param name="uri">추가할 네임스페이스입니다. </param>
      <exception cref="T:System.ArgumentException">The value for <paramref name="prefix" /> is "xml" or "xmlns". </exception>
      <exception cref="T:System.ArgumentNullException">The value for <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.DefaultNamespace">
      <summary>기본 네임스페이스의 네임스페이스 URI를 가져옵니다.</summary>
      <returns>기본 네임스페이스의 네임스페이스 URI를 반환하거나, 기본 네임스페이스가 없을 경우에는 String.Empty를 반환합니다.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetEnumerator">
      <summary>
        <see cref="T:System.Xml.XmlNamespaceManager" />에서 네임스페이스를 반복하는 데 사용할 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XmlNamespaceManager" />가 저장하는 접두사가 포함된 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>현재 범위 내에 있는 네임스페이스를 열거하는 데 사용할 수 있는 접두사가 붙은 네임스페이스 이름 컬렉션을 가져옵니다.</summary>
      <returns>현재 범위 내에 있는 네임스페이스 및 접두사 쌍 컬렉션입니다.</returns>
      <param name="scope">반환할 네임스페이스 노드의 형식을 지정하는 열거형 값입니다.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.HasNamespace(System.String)">
      <summary>제공한 접두사에 현재 푸시된 범위에 정의한 네임스페이스가 있는지를 나타내는 값을 가져옵니다.</summary>
      <returns>정의된 네임스페이스가 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="prefix">찾으려는 네임스페이스의 접두사입니다. </param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupNamespace(System.String)">
      <summary>지정된 접두사의 네임스페이스 URI를 가져옵니다.</summary>
      <returns>
        <paramref name="prefix" />의 네임스페이스 URI를 반환하거나, 매핑된 네임스페이스가 없을 경우에는 null을 반환합니다.반환되는 문자열은 원자화됩니다.원자화된 문자열에 대한 자세한 내용은 <see cref="T:System.Xml.XmlNameTable" /> 클래스를 참조하세요.</returns>
      <param name="prefix">확인할 네임스페이스 URI의 접두사입니다.기본 네임스페이스와 일치시키려면 String.Empty를 전달합니다.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupPrefix(System.String)">
      <summary>지정된 네임스페이스 URI에 대해 선언한 접두사를 찾습니다.</summary>
      <returns>일치하는 접두사입니다.매핑된 접두사가 없으면 메서드에서 String.Empty를 반환합니다.null 값이 제공되면 null이 반환됩니다.</returns>
      <param name="uri">접두사에 대해 확인할 네임스페이스입니다. </param>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.NameTable">
      <summary>이 개체와 연결된 <see cref="T:System.Xml.XmlNameTable" />을 가져옵니다.</summary>
      <returns>이 개체에서 사용한 <see cref="T:System.Xml.XmlNameTable" />입니다.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PopScope">
      <summary>스택에서 네임스페이스 범위를 팝합니다.</summary>
      <returns>스택에 네임스페이스 범위가 남아 있으면 true이고, 팝할 네임스페이스가 없으면 false입니다.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PushScope">
      <summary>스택에 네임스페이스 범위를 푸시합니다.</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.RemoveNamespace(System.String,System.String)">
      <summary>지정된 접두사의 지정된 네임스페이스를 제거합니다.</summary>
      <param name="prefix">네임스페이스의 접두사입니다. </param>
      <param name="uri">지정된 접두사의 제거할 네임스페이스입니다.네임스페이스는 현재 네임스페이스 범위에서 제거됩니다.현재 범위를 벗어난 네임스페이스는 무시됩니다.</param>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="T:System.Xml.XmlNamespaceScope">
      <summary>네임스페이스 범위를 정의합니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.All">
      <summary>현재 노드의 범위에서 정의된 모든 네임스페이스입니다.여기에는 항상 암시적으로 선언되는 xmlns:xml 네임스페이스가 포함됩니다.반환되는 네임스페이스의 순서는 정의되지 않습니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.ExcludeXml">
      <summary>항상 암시적으로 선언되는 xmlns:xml 네임스페이스를 제외하고 현재 노드의 범위에서 정의된 모든 네임스페이스입니다.반환되는 네임스페이스의 순서는 정의되지 않습니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.Local">
      <summary>현재 노드에서 로컬로 정의된 모든 네임스페이스입니다.</summary>
    </member>
    <member name="T:System.Xml.XmlNameTable">
      <summary>원자화된 문자열 개체의 테이블입니다.</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.#ctor">
      <summary>
        <see cref="T:System.Xml.XmlNameTable" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의할 경우 지정한 문자열을 원자화하여 이를 XmlNameTable에 추가합니다.</summary>
      <returns>원자화된 새 문자열이거나 이미 문자열이 존재하는 경우 기존 문자열입니다.길이가 0이면 String.Empty가 반환됩니다.</returns>
      <param name="array">추가할 이름이 포함된 문자 배열입니다. </param>
      <param name="offset">이름의 첫 번째 문자를 지정하는 인덱스이며 배열에서 0부터 시작합니다. </param>
      <param name="length">이름에 있는 문자의 수입니다. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />또는 <paramref name="offset" /> &gt;= <paramref name="array" /> .Length 또는 <paramref name="length" /> &gt; <paramref name="array" /> .Length 위의 경우 <paramref name="length" /> =0이면 예외가 throw되지 않습니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0 </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.String)">
      <summary>파생 클래스에서 재정의할 경우 지정한 문자열을 원자화하여 이를 XmlNameTable에 추가합니다.</summary>
      <returns>원자화된 새 문자열이거나 이미 문자열이 존재하는 경우 기존 문자열입니다.</returns>
      <param name="array">추가할 이름입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의할 경우 지정된 배열에 있는 지정된 범위의 문자와 같은 문자를 포함하는 원자화된 문자열을 가져옵니다.</summary>
      <returns>문자열이 이미 원자화되지 않은 경우 원자화된 문자열 또는 null입니다.<paramref name="length" />가 0이면 String.Empty가 반환됩니다.</returns>
      <param name="array">검색할 이름이 포함된 문자 배열입니다. </param>
      <param name="offset">이름의 첫 번째 문자를 지정하는 배열의 0부터 시작하는 인덱스입니다. </param>
      <param name="length">이름에 있는 문자의 수입니다. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />또는 <paramref name="offset" /> &gt;= <paramref name="array" /> .Length 또는 <paramref name="length" /> &gt; <paramref name="array" /> .Length 위의 경우 <paramref name="length" /> =0이면 예외가 throw되지 않습니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0 </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.String)">
      <summary>파생 클래스에서 재정의할 경우 지정된 문자열과 같은 값을 포함하는 원자화된 문자열을 가져옵니다.</summary>
      <returns>문자열이 이미 원자화되지 않은 경우 원자화된 문자열 또는 null입니다.</returns>
      <param name="array">검색할 이름입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다. </exception>
    </member>
    <member name="T:System.Xml.XmlNodeType">
      <summary>노드 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Attribute">
      <summary>특성입니다(예를 들어, id='123').</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.CDATA">
      <summary>CDATA 섹션입니다(예를 들어, &lt;![CDATA[my escaped text]]&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Comment">
      <summary>주석입니다(예를 들어, &lt;!-- my comment --&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Document">
      <summary>문서 트리의 루트인 문서 개체를 사용하여 전체 XML 문서에 액세스할 수 있습니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentFragment">
      <summary>문서 단편입니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentType">
      <summary>다음 태그를 사용한 문서 형식 선언입니다(예를 들어, &lt;!DOCTYPE...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Element">
      <summary>요소입니다(예를 들어, &lt;item&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndElement">
      <summary>끝 요소 태그입니다(예를 들어, &lt;/item&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndEntity">
      <summary>
        <see cref="M:System.Xml.XmlReader.ResolveEntity" />의 호출 결과 XmlReader가 대체 엔터티 끝에 도달했을 때 반환됩니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Entity">
      <summary>엔터티 선업입니다(예를 들어, &lt;!ENTITY...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EntityReference">
      <summary>엔터티 참조입니다(예를 들어, &amp;num;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.None">
      <summary>Read 메서드가 호출되지 않은 경우 <see cref="T:System.Xml.XmlReader" />에 의해 반환됩니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Notation">
      <summary>문서 형식 선언 표기법입니다(예를 들어, &lt;!NOTATION...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.ProcessingInstruction">
      <summary>처리 명령입니다(예를 들어, &lt;?pi test?&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.SignificantWhitespace">
      <summary>복합 콘텐츠 모델에서 태그들 사이의 공백 또는 xml:space="preserve" 범위 내의 공백입니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Text">
      <summary>노드의 텍스트 내용입니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Whitespace">
      <summary>태그들 사이의 공백입니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.XmlDeclaration">
      <summary>XML 선언입니다(예를 들어, &lt;?xml version='1.0'?&gt;).</summary>
    </member>
    <member name="T:System.Xml.XmlParserContext">
      <summary>
        <see cref="T:System.Xml.XmlReader" />에서 XML 조각을 구문 분석할 때 필요한 모든 컨텍스트 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace)">
      <summary>지정된 <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, 기본 URI, xml:lang, xml:space 및 문서 형식 값을 사용하여 XmlParserContext 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="nt">문자열을 원자화하는 데 사용할 <see cref="T:System.Xml.XmlNameTable" />입니다.null인 경우 <paramref name="nsMgr" />을 생성할 때 사용한 이름 테이블이 대신 사용됩니다.원자화된 문자열에 대한 자세한 내용은 <see cref="T:System.Xml.XmlNameTable" />을 참조하십시오.</param>
      <param name="nsMgr">네임스페이스 정보를 찾는 데 사용할 <see cref="T:System.Xml.XmlNamespaceManager" /> 또는 null입니다. </param>
      <param name="docTypeName">문서 형식 선언의 이름입니다. </param>
      <param name="pubId">public 식별자입니다. </param>
      <param name="sysId">시스템 식별자입니다. </param>
      <param name="internalSubset">내부 DTD 하위집합입니다.DTD 하위 집합은 개체 확인에 사용되며 문서 유효성 검사에는 사용되지 않습니다.</param>
      <param name="baseURI">XML 조각의 기본 URI(로드된 조각이 저장된 위치)입니다. </param>
      <param name="xmlLang">xml:lang 범위입니다. </param>
      <param name="xmlSpace">xml:space 범위를 나타내는 <see cref="T:System.Xml.XmlSpace" /> 값입니다. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" />가 <paramref name="nsMgr" />을 만드는 데 사용한 XmlNameTable과 다른 경우 </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>지정된 <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, 기본 URI, xml:lang, xml:space, 인코딩 및 문서 형식 값을 사용하여 XmlParserContext 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="nt">문자열을 원자화하는 데 사용할 <see cref="T:System.Xml.XmlNameTable" />입니다.null인 경우 <paramref name="nsMgr" />을 생성할 때 사용한 이름 테이블이 대신 사용됩니다.원자화된 문자열에 대한 자세한 내용은 <see cref="T:System.Xml.XmlNameTable" />을 참조하십시오.</param>
      <param name="nsMgr">네임스페이스 정보를 찾는 데 사용할 <see cref="T:System.Xml.XmlNamespaceManager" /> 또는 null입니다. </param>
      <param name="docTypeName">문서 형식 선언의 이름입니다. </param>
      <param name="pubId">public 식별자입니다. </param>
      <param name="sysId">시스템 식별자입니다. </param>
      <param name="internalSubset">내부 DTD 하위집합입니다.DTD는 개체 확인에 사용되며 문서 유효성 검사에는 사용되지 않습니다.</param>
      <param name="baseURI">XML 조각의 기본 URI(로드된 조각이 저장된 위치)입니다. </param>
      <param name="xmlLang">xml:lang 범위입니다. </param>
      <param name="xmlSpace">xml:space 범위를 나타내는 <see cref="T:System.Xml.XmlSpace" /> 값입니다. </param>
      <param name="enc">인코딩 설정을 표시하는 <see cref="T:System.Text.Encoding" /> 개체입니다. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" />가 <paramref name="nsMgr" />을 만드는 데 사용한 XmlNameTable과 다른 경우 </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace)">
      <summary>지정된 <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang 및 xml:space 값을 사용하여 XmlParserContext 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="nt">문자열을 원자화하는 데 사용할 <see cref="T:System.Xml.XmlNameTable" />입니다.null인 경우 <paramref name="nsMgr" />을 생성할 때 사용한 이름 테이블이 대신 사용됩니다.원자화된 문자열에 대한 자세한 내용은 <see cref="T:System.Xml.XmlNameTable" />을 참조하십시오.</param>
      <param name="nsMgr">네임스페이스 정보를 찾는 데 사용할 <see cref="T:System.Xml.XmlNamespaceManager" /> 또는 null입니다. </param>
      <param name="xmlLang">xml:lang 범위입니다. </param>
      <param name="xmlSpace">xml:space 범위를 나타내는 <see cref="T:System.Xml.XmlSpace" /> 값입니다. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" />가 <paramref name="nsMgr" />을 만드는 데 사용한 XmlNameTable과 다른 경우 </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>지정된 <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang, xml:space 및 인코딩을 사용하여 XmlParserContext 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="nt">문자열을 원자화하는 데 사용할 <see cref="T:System.Xml.XmlNameTable" />입니다.null인 경우 <paramref name="nsMgr" />을 생성할 때 사용한 이름 테이블이 대신 사용됩니다.원자화된 문자열에 대한 자세한 내용은 <see cref="T:System.Xml.XmlNameTable" />을 참조하십시오.</param>
      <param name="nsMgr">네임스페이스 정보를 찾는 데 사용할 <see cref="T:System.Xml.XmlNamespaceManager" /> 또는 null입니다. </param>
      <param name="xmlLang">xml:lang 범위입니다. </param>
      <param name="xmlSpace">xml:space 범위를 나타내는 <see cref="T:System.Xml.XmlSpace" /> 값입니다. </param>
      <param name="enc">인코딩 설정을 표시하는 <see cref="T:System.Text.Encoding" /> 개체입니다. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" />가 <paramref name="nsMgr" />을 만드는 데 사용한 XmlNameTable과 다른 경우 </exception>
    </member>
    <member name="P:System.Xml.XmlParserContext.BaseURI">
      <summary>기본URI를 가져오거나 설정합니다.</summary>
      <returns>DTD 파일 확인에 사용할 기본 URI입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.DocTypeName">
      <summary>문서 형식 선언의 이름을 가져오거나 설정합니다.</summary>
      <returns>문서 형식 선언의 이름입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.Encoding">
      <summary>인코딩 형식을 가져오거나 설정합니다.</summary>
      <returns>인코딩 형식을 나타내는 <see cref="T:System.Text.Encoding" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.InternalSubset">
      <summary>내부 DTD 하위 집합을 가져오거나 설정합니다.</summary>
      <returns>내부 DTD 하위집합입니다.예를 들어, 이 속성은 대괄호 &lt;!DOCTYPE doc [...]&gt; 사이에 있는 모든 것을 반환합니다.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NamespaceManager">
      <summary>
        <see cref="T:System.Xml.XmlNamespaceManager" />를 가져오거나 설정합니다.</summary>
      <returns>XmlNamespaceManager</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NameTable">
      <summary>문자열을 원자화할 때 사용하는 <see cref="T:System.Xml.XmlNameTable" />을 가져옵니다.원자화된 문자열에 대한 자세한 내용은 <see cref="T:System.Xml.XmlNameTable" />을 참조하십시오.</summary>
      <returns>XmlNameTable</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.PublicId">
      <summary>public 식별자를 가져오거나 설정합니다.</summary>
      <returns>public 식별자입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.SystemId">
      <summary>시스템 식별자를 가져오거나 설정합니다.</summary>
      <returns>시스템 식별자입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlLang">
      <summary>현재 xml:lang 범위를 가져오거나 설정합니다.</summary>
      <returns>현재 xml:lang 범위입니다.범위에 xml:lang이 없으면 String.Empty가 반환됩니다.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlSpace">
      <summary>현재 xml:space 범위를 가져오거나 설정합니다.</summary>
      <returns>xml:space 범위를 나타내는 <see cref="T:System.Xml.XmlSpace" /> 값입니다.</returns>
    </member>
    <member name="T:System.Xml.XmlQualifiedName">
      <summary>정규화된 XML 이름을 나타냅니다.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String)">
      <summary>지정된 이름을 사용하여 <see cref="T:System.Xml.XmlQualifiedName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">
        <see cref="T:System.Xml.XmlQualifiedName" /> 개체의 이름으로 사용할 로컬 이름입니다. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String,System.String)">
      <summary>지정된 이름과 네임스페이스를 사용하여 <see cref="T:System.Xml.XmlQualifiedName" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">
        <see cref="T:System.Xml.XmlQualifiedName" /> 개체의 이름으로 사용할 로컬 이름입니다. </param>
      <param name="ns">
        <see cref="T:System.Xml.XmlQualifiedName" /> 개체의 네임스페이스입니다. </param>
    </member>
    <member name="F:System.Xml.XmlQualifiedName.Empty">
      <summary>빈 <see cref="T:System.Xml.XmlQualifiedName" />을 제공합니다.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.Equals(System.Object)">
      <summary>지정된 <see cref="T:System.Xml.XmlQualifiedName" /> 개체가 현재 <see cref="T:System.Xml.XmlQualifiedName" /> 개체와 같은지 여부를 확인합니다. </summary>
      <returns>두 개체가 같은 인스턴스 개체이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">비교할 <see cref="T:System.Xml.XmlQualifiedName" />입니다. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.GetHashCode">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" />에 대한 해시 코드를 반환합니다.</summary>
      <returns>이 개체에 대한 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.IsEmpty">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" />가 비어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이름과 네임스페이스가 빈 문자열이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Name">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" />의 정규화된 이름에 대한 문자열 표현을 가져옵니다.</summary>
      <returns>정규화된 이름의 문자열 표현이거나, 개체에 정의된 이름이 없는 경우 String.Empty입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Namespace">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" />의 네임스페이스에 대한 문자열 표현을 가져옵니다.</summary>
      <returns>네임스페이스의 문자열 표현이거나, 개체에 정의된 네임스페이스가 없는 경우 String.Empty입니다.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Equality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>두 <see cref="T:System.Xml.XmlQualifiedName" /> 개체를 비교합니다.</summary>
      <returns>두 개체의 이름과 네임스페이스 값이 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="a">비교할 <see cref="T:System.Xml.XmlQualifiedName" />입니다. </param>
      <param name="b">비교할 <see cref="T:System.Xml.XmlQualifiedName" />입니다. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Inequality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>두 <see cref="T:System.Xml.XmlQualifiedName" /> 개체를 비교합니다.</summary>
      <returns>두 개체의 이름과 네임스페이스 값이 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="a">비교할 <see cref="T:System.Xml.XmlQualifiedName" />입니다. </param>
      <param name="b">비교할 <see cref="T:System.Xml.XmlQualifiedName" />입니다. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" />의 문자열 값을 반환합니다.</summary>
      <returns>namespace:localname 형식의 <see cref="T:System.Xml.XmlQualifiedName" /> 문자열 값입니다.개체에 정의된 네임스페이스가 없으면 이 메서드는 로컬 이름만 반환합니다.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString(System.String,System.String)">
      <summary>
        <see cref="T:System.Xml.XmlQualifiedName" />의 문자열 값을 반환합니다.</summary>
      <returns>namespace:localname 형식의 <see cref="T:System.Xml.XmlQualifiedName" /> 문자열 값입니다.개체에 정의된 네임스페이스가 없으면 이 메서드는 로컬 이름만 반환합니다.</returns>
      <param name="name">개체의 이름입니다. </param>
      <param name="ns">개체의 네임스페이스입니다. </param>
    </member>
    <member name="T:System.Xml.XmlReader">
      <summary>빠르고, 캐시되지 않으며 앞으로만 이동 가능한 XML 데이터 액세스를 제공하는 판독기를 나타냅니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 참조 원본.</summary>
    </member>
    <member name="M:System.Xml.XmlReader.#ctor">
      <summary>XmlReader 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.Xml.XmlReader.AttributeCount">
      <summary>파생 클래스에서 재정의되면 현재 노드에 포함된 특성 수를 가져옵니다.</summary>
      <returns>현재 노드에 포함된 특성의 수입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.BaseURI">
      <summary>파생 클래스에서 재정의되면 현재 노드의 기본 URI를 가져옵니다.</summary>
      <returns>현재 노드의 기본 URI입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadBinaryContent">
      <summary>
        <see cref="T:System.Xml.XmlReader" />가 이진 콘텐츠 읽기 메서드를 구현하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>이진 콘텐츠 읽기 메서드를 구현하면 true이고, 그렇지 않으면 false입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadValueChunk">
      <summary>
        <see cref="T:System.Xml.XmlReader" />가 <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> 메서드를 구현하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Xml.XmlReader" /> implements the <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> method; otherwise false.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanResolveEntity">
      <summary>이 판독기가 엔터티를 구문 분석하고 확인할 수 있는지를 나타내는 값을 가져옵니다.</summary>
      <returns>판독기가 엔터티를 구문 분석하고 확인할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream)">
      <summary>새로 만듭니다 <see cref="T:System.Xml.XmlReader" /> 인스턴스에서 지정 된 스트림에 사용 하 여 기본 설정을 사용 합니다.</summary>
      <returns>스트림의 XML 데이터를 읽는 데 사용되는 개체입니다.</returns>
      <param name="input">XML 데이터가 포함된 스트림입니다.<see cref="T:System.Xml.XmlReader" />는 스트림의 첫 번째 바이트를 검색하여 바이트 순서 표시나 다른 인코딩 기호를 찾습니다.인코딩이 확인되면 이 인코딩을 사용하여 스트림을 읽고, 입력을 문자 스트림(유니코드)으로 구문 분석하는 작업이 수행됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 값이 null인 경우</exception>
      <exception cref="T:System.Security.SecurityException">XML 데이터의 위치에 액세스할 수 있는 충분한 권한이 <see cref="T:System.Xml.XmlReader" />에 없는 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings)">
      <summary>새로 만듭니다 <see cref="T:System.Xml.XmlReader" /> 인스턴스에 지정 된 스트림 및 설정을 사용 합니다.</summary>
      <returns>스트림의 XML 데이터를 읽는 데 사용되는 개체입니다.</returns>
      <param name="input">XML 데이터가 포함된 스트림입니다.<see cref="T:System.Xml.XmlReader" />는 스트림의 첫 번째 바이트를 검색하여 바이트 순서 표시나 다른 인코딩 기호를 찾습니다.인코딩이 확인되면 이 인코딩을 사용하여 스트림을 읽고, 입력을 문자 스트림(유니코드)으로 구문 분석하는 작업이 수행됩니다.</param>
      <param name="settings">새 설정을 <see cref="T:System.Xml.XmlReader" /> 인스턴스.이 값은 null일 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 값이 null인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>새로 만듭니다 <see cref="T:System.Xml.XmlReader" /> 인스턴스에서 지정된 된 스트림, 설정 및 컨텍스트 정보를 사용 하 여 구문 분석 합니다.</summary>
      <returns>스트림의 XML 데이터를 읽는 데 사용되는 개체입니다.</returns>
      <param name="input">XML 데이터가 포함된 스트림입니다. <see cref="T:System.Xml.XmlReader" />는 스트림의 첫 번째 바이트를 검색하여 바이트 순서 표시나 다른 인코딩 기호를 찾습니다.인코딩이 확인되면 이 인코딩을 사용하여 스트림을 읽고, 입력을 문자 스트림(유니코드)으로 구문 분석하는 작업이 수행됩니다.</param>
      <param name="settings">새 설정을 <see cref="T:System.Xml.XmlReader" /> 인스턴스.이 값은 null일 수 있습니다.</param>
      <param name="inputContext">XML 조각을 구문 분석하는 데 필요한 컨텍스트 정보입니다.컨텍스트 정보에는 사용할 <see cref="T:System.Xml.XmlNameTable" />, 인코딩, 네임스페이스 범위, 현재 xml:lang과 xml:space 범위, 기본 URI 및 문서 종류 정의가 포함될 수 있습니다.이 값은 null일 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 값이 null인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader)">
      <summary>새로 만듭니다 <see cref="T:System.Xml.XmlReader" /> 지정 된 텍스트 판독기를 사용 하 여 인스턴스.</summary>
      <returns>스트림의 XML 데이터를 읽는 데 사용되는 개체입니다.</returns>
      <param name="input">XML 데이터를 읽어올 텍스트 판독기입니다.텍스트 판독기는 유니코드 문자 스트림을 반환하므로 XML 선언에 지정된 인코딩은 XML 판독기가 데이터 스트림을 디코딩하는 데 사용되지 않습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 값이 null인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings)">
      <summary>새로 만듭니다 <see cref="T:System.Xml.XmlReader" /> 설정과 지정 된 텍스트 판독기를 사용 하 여 인스턴스.</summary>
      <returns>스트림의 XML 데이터를 읽는 데 사용되는 개체입니다.</returns>
      <param name="input">XML 데이터를 읽어올 텍스트 판독기입니다.텍스트 판독기는 유니코드 문자 스트림을 반환하므로 XML 선언에 지정된 인코딩은 XML 판독기가 데이터 스트림을 디코딩하는 데 사용되지 않습니다.</param>
      <param name="settings">새 설정을 <see cref="T:System.Xml.XmlReader" />.이 값은 null일 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 값이 null인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>새로 만듭니다 <see cref="T:System.Xml.XmlReader" /> 구문 분석에 대 한 지정한 텍스트 판독기, 설정 및 컨텍스트 정보를 사용 하 여 인스턴스.</summary>
      <returns>스트림의 XML 데이터를 읽는 데 사용되는 개체입니다.</returns>
      <param name="input">XML 데이터를 읽어올 텍스트 판독기입니다.텍스트 판독기는 유니코드 문자 스트림을 반환하므로 XML 선언에 지정된 인코딩은 XML 판독기가 데이터 스트림을 디코딩하는 데 사용되지 않습니다.</param>
      <param name="settings">새 설정을 <see cref="T:System.Xml.XmlReader" /> 인스턴스.이 값은 null일 수 있습니다.</param>
      <param name="inputContext">XML 조각을 구문 분석하는 데 필요한 컨텍스트 정보입니다.컨텍스트 정보에는 사용할 <see cref="T:System.Xml.XmlNameTable" />, 인코딩, 네임스페이스 범위, 현재 xml:lang과 xml:space 범위, 기본 URI 및 문서 종류 정의가 포함될 수 있습니다.이 값은 null일 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 값이 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Xml.XmlReaderSettings.NameTable" /> 및 <see cref="P:System.Xml.XmlParserContext.NameTable" /> 속성 모두에 값이 포함된 경우.이러한 NameTable 속성 중 하나만 설정하여 사용할 수 있습니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String)">
      <summary>지정된 URI를 사용하여 새 <see cref="T:System.Xml.XmlReader" /> 인스턴스를 만듭니다.</summary>
      <returns>스트림의 XML 데이터를 읽는 데 사용되는 개체입니다.</returns>
      <param name="inputUri">XML 데이터를 포함하는 파일의 URI입니다.<see cref="T:System.Xml.XmlUrlResolver" /> 클래스는 경로를 정규 데이터 표현으로 변환하는 데 사용됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputUri" /> 값이 null인 경우</exception>
      <exception cref="T:System.Security.SecurityException">XML 데이터의 위치에 액세스할 수 있는 충분한 권한이 <see cref="T:System.Xml.XmlReader" />에 없는 경우</exception>
      <exception cref="T:System.IO.FileNotFoundException">URI가 나타내는 파일이 없는 경우</exception>
      <exception cref="T:System.UriFormatException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.FormatException" />를 catch합니다.URI 형식이 잘못된 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String,System.Xml.XmlReaderSettings)">
      <summary>새로 만듭니다 <see cref="T:System.Xml.XmlReader" /> 지정 된 URI 및 설정을 사용 하 여 인스턴스.</summary>
      <returns>스트림의 XML 데이터를 읽는 데 사용되는 개체입니다.</returns>
      <param name="inputUri">XML 데이터를 포함하는 파일의 URI입니다.<see cref="T:System.Xml.XmlReaderSettings" /> 개체에 지정된 <see cref="T:System.Xml.XmlResolver" /> 개체는 경로를 정규 데이터 표현으로 변환하는 데 사용됩니다.<see cref="P:System.Xml.XmlReaderSettings.XmlResolver" />가 null이면 새 <see cref="T:System.Xml.XmlUrlResolver" /> 개체가 사용됩니다.</param>
      <param name="settings">새 설정을 <see cref="T:System.Xml.XmlReader" /> 인스턴스.이 값은 null일 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputUri" /> 값이 null인 경우</exception>
      <exception cref="T:System.IO.FileNotFoundException">URI로 지정된 파일을 찾을 수 없는 경우</exception>
      <exception cref="T:System.UriFormatException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.FormatException" />를 catch합니다.URI 형식이 잘못된 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.Xml.XmlReader,System.Xml.XmlReaderSettings)">
      <summary>새로 만듭니다 <see cref="T:System.Xml.XmlReader" /> 설정과 지정 된 XML 판독기를 사용 하 여 인스턴스.</summary>
      <returns>래핑된 개체 주위 지정 된 <see cref="T:System.Xml.XmlReader" /> 개체입니다.</returns>
      <param name="reader">내부 XML 판독기로 사용할 개체입니다.</param>
      <param name="settings">새 설정을 <see cref="T:System.Xml.XmlReader" /> 인스턴스.<see cref="T:System.Xml.XmlReaderSettings" /> 개체의 규칙 수준은 내부 판독기의 규칙 수준과 일치하거나 <see cref="F:System.Xml.ConformanceLevel.Auto" />로 설정되어야 합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> 값이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReaderSettings" /> 개체에 지정된 규칙 수준이 내부 판독기의 규칙 수준과 일치하지 않는 경우또는내부 <see cref="T:System.Xml.XmlReader" />의 상태가 <see cref="F:System.Xml.ReadState.Error" /> 또는 <see cref="F:System.Xml.ReadState.Closed" />인 경우</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Depth">
      <summary>파생 클래스에서 재정의되면 XML 문서에서 현재 노드의 수준을 가져옵니다.</summary>
      <returns>XML 문서의 현재 노드 수준입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> 클래스의 현재 인스턴스에서 사용하는 모든 리소스를 해제합니다.</summary>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Xml.XmlReader" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 해제할 수 있습니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.EOF">
      <summary>파생 클래스에서 재정의되면 판독기가 스트림의 끝에 배치되었는지를 나타내는 값을 가져옵니다.</summary>
      <returns>판독기가 스트림의 맨 끝에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정된 인덱스가 있는 특성의 값을 가져옵니다.</summary>
      <returns>지정된 특성의 값을 반환합니다.이 메서드는 판독기를 이동하지 않습니다.</returns>
      <param name="i">특성의 인덱스입니다.인덱스는 0부터 시작합니다.첫 번째 특성의 인덱스는 0입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" />이 범위에서 벗어난 경우.음수가 아니어야 하며 특성 컬렉션의 크기보다 작아야합니다.</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 <see cref="P:System.Xml.XmlReader.Name" />이 있는 특성의 값을 가져옵니다.</summary>
      <returns>지정된 특성의 값을 반환합니다.지정된 특성이 없거나 값이 String.Empty이면 null이 반환됩니다.</returns>
      <param name="name">특성의 정규화된 이름입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 <see cref="P:System.Xml.XmlReader.LocalName" /> 및 <see cref="P:System.Xml.XmlReader.NamespaceURI" />가 있는 특성의 값을 가져옵니다.</summary>
      <returns>지정된 특성의 값을 반환합니다.지정된 특성이 없거나 값이 String.Empty이면 null이 반환됩니다.이 메서드는 판독기를 이동하지 않습니다.</returns>
      <param name="name">특성의 로컬 이름입니다.</param>
      <param name="namespaceURI">특성의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />가 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetValueAsync">
      <summary>현재 노드의 값을 비동기적으로 가져옵니다.</summary>
      <returns>현재 노드의 값입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasAttributes">
      <summary>현재 노드에 특성이 있는지를 나타내는 값을 얻습니다.</summary>
      <returns>현재 노드에 특성이 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasValue">
      <summary>파생 클래스에서 재정의되면 현재 노드에 <see cref="P:System.Xml.XmlReader.Value" />가 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>현재 판독기가 위치한 노드에 Value가 있으면 true이고, 그렇지 않으면 false입니다.false인 경우 노드의 값은 String.Empty입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsDefault">
      <summary>파생 클래스에서 재정의되면 현재 노드가 DTD나 스키마에서 정의한 기본값에서 생성된 값을 가진 특성인지를 나타내는 값을 가져옵니다.</summary>
      <returns>현재 노드가 DTD나 스키마에서 정의한 기본값에서 생성된 값을 가진 특성이면 true이고, 특성 값이 명시적으로 설정되었으면 false 입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsEmptyElement">
      <summary>파생 클래스에서 재정의되면 현재 노드가 &lt;MyElement/&gt;와 같은 빈 요소인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>현재 노드가 /&gt;로 끝나는 요소(<see cref="P:System.Xml.XmlReader.NodeType" />이 XmlNodeType.Element인 경우)이면 true이고, 그렇지 않으면false입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsName(System.String)">
      <summary>문자열 인수가 유효한 XML 이름인지를 나타내는 값을 반환합니다.</summary>
      <returns>유효한 이름이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="str">유효성을 검사할 이름입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="str" /> 값이 null인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsNameToken(System.String)">
      <summary>문자열 인수가 유효한 XML 이름 토큰인지를 나타내는 값을 반환합니다.</summary>
      <returns>유효한 이름 토큰이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="str">유효성을 검사할 이름 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="str" /> 값이 null인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement">
      <summary>
        <see cref="M:System.Xml.XmlReader.MoveToContent" />를 호출하고 현재 콘텐츠 노드가 시작 태그 또는 빈 요소 태그인지 테스트합니다.</summary>
      <returns>
        <see cref="M:System.Xml.XmlReader.MoveToContent" />가 시작 태그나 빈 요소 태그를 찾으면 true이고, XmlNodeType.Element 이외의 노드 형식을 찾으면 false입니다.</returns>
      <exception cref="T:System.Xml.XmlException">입력 스트림에 잘못된 XML이 있는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String)">
      <summary>
        <see cref="M:System.Xml.XmlReader.MoveToContent" />를 호출하고 현재 콘텐츠 노드가 시작 태그 또는 빈 요소 태그인지 여부와 찾은 요소의 <see cref="P:System.Xml.XmlReader.Name" /> 속성이 지정된 인수와 일치하는지 여부를 테스트합니다.</summary>
      <returns>테스트한 결과 현재 노드가 요소이고 Name 속성이 지정된 문자열과 일치하면 true이고,XmlNodeType.Element 이외의 노드 형식을 찾거나 요소 Name 속성이 지정된 문자열과 일치하지 않으면 false입니다.</returns>
      <param name="name">찾은 요소의 Name 속성과 일치하는 문자열입니다.</param>
      <exception cref="T:System.Xml.XmlException">입력 스트림에 잘못된 XML이 있는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String,System.String)">
      <summary>
        <see cref="M:System.Xml.XmlReader.MoveToContent" />를 호출하고 현재 콘텐츠 노드가 시작 태그 또는 빈 요소 태그인지 여부와 찾은 요소의 <see cref="P:System.Xml.XmlReader.LocalName" /> 및 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 속성이 지정된 인수와 일치하는지 여부를 테스트합니다.</summary>
      <returns>테스트한 결과 현재 노드가 요소이면 true이고,XmlNodeType.Element 이외의 노드 형식을 찾거나 요소의 LocalName 및 NamespaceURI 속성이 지정된 문자열과 일치하지 않으면 false입니다.</returns>
      <param name="localname">찾은 요소의 LocalName 속성과 일치하는 문자열입니다.</param>
      <param name="ns">찾은 요소의 NamespaceURI 속성과 일치하는 문자열입니다.</param>
      <exception cref="T:System.Xml.XmlException">입력 스트림에 잘못된 XML이 있는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정된 인덱스가 있는 특성의 값을 가져옵니다.</summary>
      <returns>지정된 특성의 값을 반환합니다.</returns>
      <param name="i">특성의 인덱스입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 <see cref="P:System.Xml.XmlReader.Name" />이 있는 특성의 값을 가져옵니다.</summary>
      <returns>지정된 특성의 값을 반환합니다.특성이 없으면 null이 반환됩니다.</returns>
      <param name="name">특성의 정규화된 이름입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 <see cref="P:System.Xml.XmlReader.LocalName" /> 및 <see cref="P:System.Xml.XmlReader.NamespaceURI" />가 있는 특성의 값을 가져옵니다.</summary>
      <returns>지정된 특성의 값을 반환합니다.특성이 없으면 null이 반환됩니다.</returns>
      <param name="name">특성의 로컬 이름입니다.</param>
      <param name="namespaceURI">특성의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.LocalName">
      <summary>파생 클래스에서 재정의되면 현재 노드의 로컬 이름을 가져옵니다.</summary>
      <returns>접두사를 제거한 현재 노드의 이름입니다.예를 들어, LocalName은 &lt;bk:book&gt; 요소에 대한 book입니다.이름이 없는 노드 형식(예: Text, Comment 등)의 경우 이 속성은 String.Empty를 반환합니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.LookupNamespace(System.String)">
      <summary>파생 클래스에서 재정의되면 현재 요소의 범위에서 네임스페이스 접두사를 확인합니다.</summary>
      <returns>접두사가 매핑되는 네임스페이스 URI이거나 일치하는 접두사가 없는 경우 null입니다.</returns>
      <param name="prefix">확인할 네임스페이스 URI의 접두사입니다.기본 네임스페이스와 일치시키려면 빈 문자열을 전달합니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정된 인덱스가 있는 특성으로 이동합니다.</summary>
      <param name="i">특성의 인덱스입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">매개 변수에 음수 값이 있습니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 <see cref="P:System.Xml.XmlReader.Name" />이 있는 특성으로 이동합니다.</summary>
      <returns>특성이 있으면 true이고, 그렇지 않으면 false입니다.false이면, 판독기의 위치는 변경되지 않습니다.</returns>
      <param name="name">특성의 정규화된 이름입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.ArgumentException">매개 변수가 빈 문자열인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 <see cref="P:System.Xml.XmlReader.LocalName" /> 및 <see cref="P:System.Xml.XmlReader.NamespaceURI" />가 있는 특성으로 이동합니다.</summary>
      <returns>특성이 있으면 true이고, 그렇지 않으면 false입니다.false이면, 판독기의 위치는 변경되지 않습니다.</returns>
      <param name="name">특성의 로컬 이름입니다.</param>
      <param name="ns">특성의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.ArgumentNullException">두 매개 변수 값이 모두 null인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContent">
      <summary>현재 노드가 콘텐츠 노드(공백 없는 텍스트, CDATA, Element, EndElement, EntityReference 또는 EndEntity)인지 여부를 확인합니다.해당 노드가 콘텐츠 노드가 아니면 판독기는 다음 콘텐츠 노드나 파일의 끝으로 건너뜁니다.판독기는 ProcessingInstruction, DocumentType, Comment, Whitespace 또는 SignificantWhitespace 같은 형식의 노드를 건너뜁니다.</summary>
      <returns>메서드를 사용하여 찾은 현재 노드의 <see cref="P:System.Xml.XmlReader.NodeType" />이거나 판독기가 입력 스트림의 끝에 도달한 경우에는 XmlNodeType.None입니다.</returns>
      <exception cref="T:System.Xml.XmlException">입력 스트림에 잘못된 XML이 있는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContentAsync">
      <summary>현재 노드가 콘텐츠 노드인지를 비동기적으로 확인합니다.해당 노드가 콘텐츠 노드가 아니면 판독기는 다음 콘텐츠 노드나 파일의 끝으로 건너뜁니다.</summary>
      <returns>메서드를 사용하여 찾은 현재 노드의 <see cref="P:System.Xml.XmlReader.NodeType" />이거나 판독기가 입력 스트림의 끝에 도달한 경우에는 XmlNodeType.None입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToElement">
      <summary>파생 클래스에서 재정의되면 현재 특성 노드를 포함하는 요소로 이동합니다.</summary>
      <returns>판독기가 특성에 있으면(특성이 있는 요소로 판독기가 이동하면) true이고, 판독기가 특성에 없으면(판독기의 위치가 바뀌지 않으면) false입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToFirstAttribute">
      <summary>파생 클래스에서 재정의되면 첫 번째 특성으로 이동합니다.</summary>
      <returns>특성이 있으면(판독기가 첫 번째 특성으로 이동하면) true이고, 그렇지 않으면(판독기의 위치가 바뀌지 않으면) false입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToNextAttribute">
      <summary>파생 클래스에서 재정의되면 다음 특성으로 이동합니다.</summary>
      <returns>다음 특성이 있으면 true이고, 더 이상 특성이 없으면 false입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Name">
      <summary>파생 클래스에서 재정의되면 현재 노드의 정규화된 이름을 가져옵니다.</summary>
      <returns>현재 노드의 정규화된 이름입니다.예를 들어, Name은 &lt;bk:book&gt; 요소에 대한 bk:book입니다.반환되는 이름은 노드의 <see cref="P:System.Xml.XmlReader.NodeType" />에 따라 달라집니다.다음 노드 형식은 나열된 값을 반환합니다.기타 모든 노드 형식은 빈 문자열을 반환합니다.노드 형식 이름 Attribute특성의 이름입니다. DocumentType문서 형식 이름입니다. Element태그 이름입니다. EntityReference참조된 엔터티의 이름입니다. ProcessingInstruction처리 명령의 대상입니다. XmlDeclaration리터럴 문자열 xml입니다. </returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NamespaceURI">
      <summary>파생 클래스에서 재정의되면 판독기가 배치된 노드의 네임스페이스 URI를 W3C 네임스페이스 사양에 정의된 대로 가져옵니다.</summary>
      <returns>현재 노드의 네임스페이스 URI이거나 빈 문자열입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NameTable">
      <summary>파생 클래스에서 재정의되면 이 구현과 관련된 <see cref="T:System.Xml.XmlNameTable" />을 가져옵니다.</summary>
      <returns>노드 내에 있는 문자열의 원자화된 버전을 가져올 수 있도록 하는 XmlNameTable입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NodeType">
      <summary>파생 클래스에서 재정의되면 현재 노드의 형식을 가져옵니다.</summary>
      <returns>현재 노드의 형식을 지정하는 열거형 값 중 하나입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Prefix">
      <summary>파생 클래스에서 재정의되면 현재 노드와 관련된 네임스페이스 접두사를 가져옵니다.</summary>
      <returns>현재 노드와 관련된 네임스페이스 접두사입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Read">
      <summary>파생 클래스에서 재정의되면 스트림에서 다음 노드를 읽습니다.</summary>
      <returns>true다음 노드를 읽었으면 하는 경우 그렇지 않은 경우 false.</returns>
      <exception cref="T:System.Xml.XmlException">XML을 구문 분석하는 동안 오류가 발생한 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAsync">
      <summary>스트림에서 다음 노드를 비동기적으로 읽습니다.</summary>
      <returns>다음 노드를 읽었으면 true이고, 더 이상 읽을 노드가 없으면 false입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAttributeValue">
      <summary>파생 클래스에서 재정의되면 하나 이상의 Text, EntityReference 또는 EndEntity 노드로 특성 값을 구문 분석합니다.</summary>
      <returns>반환할 노드가 있으면 true입니다.처음 호출할 때 판독기가 Attribute 노드에 있거나 모든 특성 값을 읽었으면 false입니다.misc=""와 같은 빈 특성은 true를 반환하며 이것은 단일 노드가 String.Empty의 값을 갖는 것을 의미합니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>지정한 형식의 개체로 콘텐츠를 읽습니다.</summary>
      <returns>요청된 형식으로 변환된 특성 값 또는 연결된 텍스트 콘텐츠입니다.</returns>
      <param name="returnType">반환될 값의 형식입니다.참고  .NET Framework 3.5 릴리스에서는 <paramref name="returnType" /> 매개 변수 값이 <see cref="T:System.DateTimeOffset" /> 형식이 될 수 있습니다.</param>
      <param name="namespaceResolver">형식 변환과 관련된 모든 네임스페이스 접두사를 확인하는 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체입니다.예를 들어, <see cref="T:System.Xml.XmlQualifiedName" /> 개체를 xs:string으로 변환할 때 이 개체를 사용할 수 있습니다.이 값은 null일 수 있습니다.</param>
      <exception cref="T:System.FormatException">콘텐츠가 대상 형식에 맞지 않는 형식인 경우</exception>
      <exception cref="T:System.InvalidCastException">시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> 값이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">현재 노드가 지원되는 노드 형식이 아닌 경우.자세한 내용은 아래 표를 참조하십시오.</exception>
      <exception cref="T:System.OverflowException">Decimal.MaxValue를 읽는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>지정한 형식의 개체로 콘텐츠를 비동기적으로 읽습니다.</summary>
      <returns>요청된 형식으로 변환된 특성 값 또는 연결된 텍스트 콘텐츠입니다.</returns>
      <param name="returnType">반환될 값의 형식입니다.</param>
      <param name="namespaceResolver">형식 변환과 관련된 모든 네임스페이스 접두사를 확인하는 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>콘텐츠를 읽고 Base64 디코딩된 이진 바이트를 반환합니다.</summary>
      <returns>버퍼에 쓴 바이트 수입니다.</returns>
      <param name="buffer">결과 텍스트를 복사해 넣을 버퍼입니다.이 값은 null일 수 없습니다.</param>
      <param name="index">버퍼에 넣을 결과 복사가 시작되는 오프셋입니다.</param>
      <param name="count">버퍼에 복사할 최대 바이트 수입니다.복사된 실제 바이트 수가 이 메서드에서 반환됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 값이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)" />가 현재 노드에서 지원되지 않는 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">버퍼 내의 인덱스 또는 인덱스와 개수를 합한 값이 할당된 버퍼 크기보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">구현된 <see cref="T:System.Xml.XmlReader" />에서 이 메서드를 지원하지 않는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>콘텐츠를 비동기적으로 읽고 Base64 디코딩된 이진 바이트를 반환합니다.</summary>
      <returns>버퍼에 쓴 바이트 수입니다.</returns>
      <param name="buffer">결과 텍스트를 복사해 넣을 버퍼입니다.이 값은 null일 수 없습니다.</param>
      <param name="index">버퍼에 넣을 결과 복사가 시작되는 오프셋입니다.</param>
      <param name="count">버퍼에 복사할 최대 바이트 수입니다.복사된 실제 바이트 수가 이 메서드에서 반환됩니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>콘텐츠를 읽고 BinHex 디코딩된 이진 바이트를 반환합니다.</summary>
      <returns>버퍼에 쓴 바이트 수입니다.</returns>
      <param name="buffer">결과 텍스트를 복사해 넣을 버퍼입니다.이 값은 null일 수 없습니다.</param>
      <param name="index">버퍼에 넣을 결과 복사가 시작되는 오프셋입니다.</param>
      <param name="count">버퍼에 복사할 최대 바이트 수입니다.복사된 실제 바이트 수가 이 메서드에서 반환됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 값이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)" />가 현재 노드에서 지원되지 않는 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">버퍼 내의 인덱스 또는 인덱스와 개수를 합한 값이 할당된 버퍼 크기보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">구현된 <see cref="T:System.Xml.XmlReader" />에서 이 메서드를 지원하지 않는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>콘텐츠를 비동기적으로 읽고 BinHex 디코딩된 이진 바이트를 반환합니다.</summary>
      <returns>버퍼에 쓴 바이트 수입니다.</returns>
      <param name="buffer">결과 텍스트를 복사해 넣을 버퍼입니다.이 값은 null일 수 없습니다.</param>
      <param name="index">버퍼에 넣을 결과 복사가 시작되는 오프셋입니다.</param>
      <param name="count">버퍼에 복사할 최대 바이트 수입니다.복사된 실제 바이트 수가 이 메서드에서 반환됩니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBoolean">
      <summary>현재 위치의 텍스트 콘텐츠를 Boolean으로 읽습니다.</summary>
      <returns>텍스트 콘텐츠에 해당하는 <see cref="T:System.Boolean" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidCastException">시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.FormatException">문자열 형식이 올바르지 않은 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDateTimeOffset">
      <summary>현재 위치의 텍스트 콘텐츠를 <see cref="T:System.DateTimeOffset" /> 개체로 읽습니다.</summary>
      <returns>텍스트 콘텐츠에 해당하는 <see cref="T:System.DateTimeOffset" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDecimal">
      <summary>현재 위치의 텍스트 콘텐츠를 <see cref="T:System.Decimal" /> 개체로 읽습니다.</summary>
      <returns>현재 위치의 텍스트 콘텐츠에 해당하는 <see cref="T:System.Decimal" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidCastException">시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.FormatException">문자열 형식이 올바르지 않은 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDouble">
      <summary>현재 위치의 텍스트 콘텐츠를 배정밀도 부동 소수점 숫자로 읽습니다.</summary>
      <returns>텍스트 콘텐츠에 해당하는 배정밀도 부동 소수점 숫자입니다.</returns>
      <exception cref="T:System.InvalidCastException">시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.FormatException">문자열 형식이 올바르지 않은 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsFloat">
      <summary>현재 위치의 텍스트 콘텐츠를 단정밀도 부동 소수점 숫자로 읽습니다.</summary>
      <returns>현재 위치의 텍스트 콘텐츠에 해당하는 단정밀도 부동 소수점 숫자입니다.</returns>
      <exception cref="T:System.InvalidCastException">시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.FormatException">문자열 형식이 올바르지 않은 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsInt">
      <summary>현재 위치의 텍스트 콘텐츠를 부호 있는 32비트 정수로 읽습니다.</summary>
      <returns>텍스트 콘텐츠에 해당하는 부호 있는 32비트 정수입니다.</returns>
      <exception cref="T:System.InvalidCastException">시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.FormatException">문자열 형식이 올바르지 않은 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsLong">
      <summary>현재 위치의 텍스트 콘텐츠를 부호 있는 64비트 정수로 읽습니다.</summary>
      <returns>텍스트 콘텐츠에 해당하는 부호 있는 64비트 정수입니다.</returns>
      <exception cref="T:System.InvalidCastException">시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.FormatException">문자열 형식이 올바르지 않은 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObject">
      <summary>현재 위치의 텍스트 콘텐츠를 <see cref="T:System.Object" />로 읽습니다.</summary>
      <returns>텍스트 콘텐츠에 해당하는 가장 적합한 CLR(공용 언어 런타임) 개체입니다.</returns>
      <exception cref="T:System.InvalidCastException">시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.FormatException">문자열 형식이 올바르지 않은 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObjectAsync">
      <summary>현재 위치의 텍스트 콘텐츠를 <see cref="T:System.Object" />로 비동기적으로 읽습니다.</summary>
      <returns>텍스트 콘텐츠에 해당하는 가장 적합한 CLR(공용 언어 런타임) 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsString">
      <summary>현재 위치의 텍스트 콘텐츠를 <see cref="T:System.String" /> 개체로 읽습니다.</summary>
      <returns>텍스트 콘텐츠에 해당하는 <see cref="T:System.String" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidCastException">시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.FormatException">문자열 형식이 올바르지 않은 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsStringAsync">
      <summary>현재 위치의 텍스트 콘텐츠를 <see cref="T:System.String" /> 개체로 읽습니다.</summary>
      <returns>텍스트 콘텐츠에 해당하는 <see cref="T:System.String" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>요소 콘텐츠를 요청된 형식으로 읽습니다.</summary>
      <returns>요청된 형식의 개체로 변환된 요소 콘텐츠입니다.</returns>
      <param name="returnType">반환될 값의 형식입니다.참고  .NET Framework 3.5 릴리스에서는 <paramref name="returnType" /> 매개 변수 값이 <see cref="T:System.DateTimeOffset" /> 형식이 될 수 있습니다.</param>
      <param name="namespaceResolver">형식 변환과 관련된 모든 네임스페이스 접두사를 확인하는 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 요청한 형식으로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.OverflowException">Decimal.MaxValue를 읽는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver,System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI가 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하는지 확인한 다음 요소 콘텐츠를 요청된 형식으로 읽습니다.</summary>
      <returns>요청된 형식의 개체로 변환된 요소 콘텐츠입니다.</returns>
      <param name="returnType">반환될 값의 형식입니다.참고  .NET Framework 3.5 릴리스에서는 <paramref name="returnType" /> 매개 변수 값이 <see cref="T:System.DateTimeOffset" /> 형식이 될 수 있습니다.</param>
      <param name="namespaceResolver">형식 변환과 관련된 모든 네임스페이스 접두사를 확인하는 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체입니다.</param>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 요청한 형식으로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.ArgumentException">지정한 로컬 이름과 네임스페이스 URI가 읽고 있는 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하지 않는 경우</exception>
      <exception cref="T:System.OverflowException">Decimal.MaxValue를 읽는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>요소 콘텐츠를 요청된 형식으로 비동기적으로 읽습니다.</summary>
      <returns>요청된 형식의 개체로 변환된 요소 콘텐츠입니다.</returns>
      <param name="returnType">반환될 값의 형식입니다.</param>
      <param name="namespaceResolver">형식 변환과 관련된 모든 네임스페이스 접두사를 확인하는 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>요소를 읽고 Base64 콘텐츠를 디코딩합니다.</summary>
      <returns>버퍼에 쓴 바이트 수입니다.</returns>
      <param name="buffer">결과 텍스트를 복사해 넣을 버퍼입니다.이 값은 null일 수 없습니다.</param>
      <param name="index">버퍼에 넣을 결과 복사가 시작되는 오프셋입니다.</param>
      <param name="count">버퍼에 복사할 최대 바이트 수입니다.복사된 실제 바이트 수가 이 메서드에서 반환됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 값이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">현재 노드가 요소 노드가 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">버퍼 내의 인덱스 또는 인덱스와 개수를 합한 값이 할당된 버퍼 크기보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">구현된 <see cref="T:System.Xml.XmlReader" />에서 이 메서드를 지원하지 않는 경우</exception>
      <exception cref="T:System.Xml.XmlException">요소가 혼합 콘텐츠를 포함하는 경우</exception>
      <exception cref="T:System.FormatException">요소를 요청한 형식으로 변환할 수 없는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>요소를 비동기적으로 읽고 Base64 콘텐츠를 디코딩합니다.</summary>
      <returns>버퍼에 쓴 바이트 수입니다.</returns>
      <param name="buffer">결과 텍스트를 복사해 넣을 버퍼입니다.이 값은 null일 수 없습니다.</param>
      <param name="index">버퍼에 넣을 결과 복사가 시작되는 오프셋입니다.</param>
      <param name="count">버퍼에 복사할 최대 바이트 수입니다.복사된 실제 바이트 수가 이 메서드에서 반환됩니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>요소를 읽고 BinHex 콘텐츠를 디코딩합니다.</summary>
      <returns>버퍼에 쓴 바이트 수입니다.</returns>
      <param name="buffer">결과 텍스트를 복사해 넣을 버퍼입니다.이 값은 null일 수 없습니다.</param>
      <param name="index">버퍼에 넣을 결과 복사가 시작되는 오프셋입니다.</param>
      <param name="count">버퍼에 복사할 최대 바이트 수입니다.복사된 실제 바이트 수가 이 메서드에서 반환됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 값이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">현재 노드가 요소 노드가 아닌 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">버퍼 내의 인덱스 또는 인덱스와 개수를 합한 값이 할당된 버퍼 크기보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">구현된 <see cref="T:System.Xml.XmlReader" />에서 이 메서드를 지원하지 않는 경우</exception>
      <exception cref="T:System.Xml.XmlException">요소가 혼합 콘텐츠를 포함하는 경우</exception>
      <exception cref="T:System.FormatException">요소를 요청한 형식으로 변환할 수 없는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>요소를 비동기적으로 읽고 BinHex 콘텐츠를 디코딩합니다.</summary>
      <returns>버퍼에 쓴 바이트 수입니다.</returns>
      <param name="buffer">결과 텍스트를 복사해 넣을 버퍼입니다.이 값은 null일 수 없습니다.</param>
      <param name="index">버퍼에 넣을 결과 복사가 시작되는 오프셋입니다.</param>
      <param name="count">버퍼에 복사할 최대 바이트 수입니다.복사된 실제 바이트 수가 이 메서드에서 반환됩니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean">
      <summary>현재 요소를 읽고 콘텐츠를 <see cref="T:System.Boolean" /> 개체로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 <see cref="T:System.Boolean" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 <see cref="T:System.Boolean" /> 개체로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI가 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하는지 확인한 다음 현재 요소를 읽고 콘텐츠를 <see cref="T:System.Boolean" /> 개체로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 <see cref="T:System.Boolean" /> 개체입니다.</returns>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 요청한 형식으로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.ArgumentException">지정한 로컬 이름과 네임스페이스 URI가 읽고 있는 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하지 않는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal">
      <summary>현재 요소를 읽고 콘텐츠를 <see cref="T:System.Decimal" /> 개체로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 <see cref="T:System.Decimal" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 <see cref="T:System.Decimal" />로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI가 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하는지 확인한 다음 현재 요소를 읽고 콘텐츠를 <see cref="T:System.Decimal" /> 개체로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 <see cref="T:System.Decimal" /> 개체입니다.</returns>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 <see cref="T:System.Decimal" />로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.ArgumentException">지정한 로컬 이름과 네임스페이스 URI가 읽고 있는 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하지 않는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble">
      <summary>현재 요소를 읽고 콘텐츠를 배정밀도 부동 소수점 숫자로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 배정밀도 부동 소수점 숫자입니다.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 배정밀도 부동 소수점 숫자로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI가 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하는지 확인한 다음 현재 요소를 읽고 콘텐츠를 배정밀도 부동 소수점 숫자로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 배정밀도 부동 소수점 숫자입니다.</returns>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 요청한 형식으로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.ArgumentException">지정한 로컬 이름과 네임스페이스 URI가 읽고 있는 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하지 않는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat">
      <summary>현재 요소를 읽고 콘텐츠를 단정밀도 부동 소수점 숫자로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 단정밀도 부동 소수점 숫자입니다.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 단정밀도 부동 소수점 숫자로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI가 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하는지 확인한 다음 현재 요소를 읽고 콘텐츠를 단정밀도 부동 소수점 숫자로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 단정밀도 부동 소수점 숫자입니다.</returns>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 단정밀도 부동 소수점 숫자로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.ArgumentException">지정한 로컬 이름과 네임스페이스 URI가 읽고 있는 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하지 않는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt">
      <summary>현재 요소를 읽고 부호 있는 32비트 정수로 콘텐츠를 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 부호 있는 32비트 정수입니다.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 부호 있는 32비트 정수로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI가 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하는지 확인한 다음 현재 요소를 읽고 콘텐츠를 부호 있는 32비트 정수로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 부호 있는 32비트 정수입니다.</returns>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 부호 있는 32비트 정수로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.ArgumentException">지정한 로컬 이름과 네임스페이스 URI가 읽고 있는 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하지 않는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong">
      <summary>현재 요소를 읽고 부호 있는 64비트 정수로 콘텐츠를 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 부호 있는 64비트 정수입니다.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 부호 있는 64비트 정수로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI가 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하는지 확인한 다음 현재 요소를 읽고 콘텐츠를 부호 있는 64비트 정수로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 부호 있는 64비트 정수입니다.</returns>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 부호 있는 64비트 정수로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.ArgumentException">지정한 로컬 이름과 네임스페이스 URI가 읽고 있는 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하지 않는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject">
      <summary>현재 요소를 읽고 콘텐츠를 <see cref="T:System.Object" />로 반환합니다.</summary>
      <returns>가장 적합한 형식의 boxed CLR(공용 언어 런타임) 개체입니다.적합한 CLR 형식은 <see cref="P:System.Xml.XmlReader.ValueType" /> 속성에 따라 결정됩니다.콘텐츠가 목록 형식이면 이 메서드는 적합한 형식의 boxed 개체 배열을 반환합니다.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 요청한 형식으로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI가 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하는지 확인한 다음 현재 요소를 읽고 콘텐츠를 <see cref="T:System.Object" />로 반환합니다.</summary>
      <returns>가장 적합한 형식의 boxed CLR(공용 언어 런타임) 개체입니다.적합한 CLR 형식은 <see cref="P:System.Xml.XmlReader.ValueType" /> 속성에 따라 결정됩니다.콘텐츠가 목록 형식이면 이 메서드는 적합한 형식의 boxed 개체 배열을 반환합니다.</returns>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 요청한 형식으로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.ArgumentException">지정한 로컬 이름과 네임스페이스 URI가 읽고 있는 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하지 않는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObjectAsync">
      <summary>현재 요소를 비동기적으로 읽고 콘텐츠를 <see cref="T:System.Object" />로 반환합니다.</summary>
      <returns>가장 적합한 형식의 boxed CLR(공용 언어 런타임) 개체입니다.적합한 CLR 형식은 <see cref="P:System.Xml.XmlReader.ValueType" /> 속성에 따라 결정됩니다.콘텐츠가 목록 형식이면 이 메서드는 적합한 형식의 boxed 개체 배열을 반환합니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString">
      <summary>현재 요소를 읽고 콘텐츠를 <see cref="T:System.String" /> 개체로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 <see cref="T:System.String" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 <see cref="T:System.String" /> 개체로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI가 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하는지 확인한 다음 현재 요소를 읽고 콘텐츠를 <see cref="T:System.String" /> 개체로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 <see cref="T:System.String" /> 개체입니다.</returns>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" />가 요소에 배치되지 않은 경우</exception>
      <exception cref="T:System.Xml.XmlException">현재 요소에 자식 요소가 포함된 경우또는요소 콘텐츠를 <see cref="T:System.String" /> 개체로 변환할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">메서드가 null 인수를 사용하여 호출된 경우</exception>
      <exception cref="T:System.ArgumentException">지정한 로컬 이름과 네임스페이스 URI가 읽고 있는 현재 요소의 로컬 이름 및 네임스페이스 URI와 일치하지 않는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsStringAsync">
      <summary>현재 요소를 비동기적으로 읽고 콘텐츠를 <see cref="T:System.String" /> 개체로 반환합니다.</summary>
      <returns>요소 콘텐츠에 해당하는 <see cref="T:System.String" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadEndElement">
      <summary>현재 콘텐츠 노드가 끝 태그인지 확인하고 판독기를 다음 노드로 이동합니다.</summary>
      <exception cref="T:System.Xml.XmlException">현재 노드가 끝 태그가 아니거나 입력 스트림에 잘못된 XML이 있는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXml">
      <summary>파생 클래스에서 재정의되면 태그를 포함한 모든 콘텐츠를 문자열로 읽습니다.</summary>
      <returns>태그를 포함한 모든 현재 노드의 XML 콘텐츠입니다.현재 노드에 자식이 없으면 빈 문자열이 반환됩니다.현재 노드가 요소나 특성이 아니면 빈 문자열이 반환됩니다.</returns>
      <exception cref="T:System.Xml.XmlException">XML의 형식이 잘못되었거나 XML을 구문 분석하는 동안 오류가 발생한 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXmlAsync">
      <summary>태그를 포함한 모든 콘텐츠를 문자열로 비동기적으로 읽습니다.</summary>
      <returns>태그를 포함한 모든 현재 노드의 XML 콘텐츠입니다.현재 노드에 자식이 없으면 빈 문자열이 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXml">
      <summary>파생 클래스에서 재정의되면 태그를 포함하여 이 노드 및 모든 자식 노드를 나타내는 콘텐츠를 읽습니다.</summary>
      <returns>판독기가 요소 또는 특성 노드에 배치되면 이 메서드는 태그를 포함해 현재 노드와 모든 자식 노드의 xml 콘텐츠를 모두 반환하고, 그렇지 않으면 빈 문자열을 반환합니다.</returns>
      <exception cref="T:System.Xml.XmlException">XML의 형식이 잘못되었거나 XML을 구문 분석하는 동안 오류가 발생한 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXmlAsync">
      <summary>이 노드 및 이 노드의 모든 자식을 나타내는 태그를 포함한 콘텐츠를 비동기적으로 읽습니다.</summary>
      <returns>판독기가 요소 또는 특성 노드에 배치되면 이 메서드는 태그를 포함해 현재 노드와 모든 자식 노드의 xml 콘텐츠를 모두 반환하고, 그렇지 않으면 빈 문자열을 반환합니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement">
      <summary>현재 노드가 요소인지 확인하고 판독기를 다음 노드로 진행합니다.</summary>
      <exception cref="T:System.Xml.XmlException">입력 스트림에 잘못된 XML이 있는 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String)">
      <summary>현재 콘텐츠 노드가 지정된 <see cref="P:System.Xml.XmlReader.Name" />을 가진 요소인지 확인하고 판독기를 다음 노드로 이동합니다.</summary>
      <param name="name">요소의 정규화된 이름입니다.</param>
      <exception cref="T:System.Xml.XmlException">입력 스트림에 잘못된 XML이 있는 경우 또는 이 요소의 <see cref="P:System.Xml.XmlReader.Name" />는 주어진 <paramref name="name" />에 매치되지 않습니다.</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String,System.String)">
      <summary>현재 콘텐츠 노드가 지정된 <see cref="P:System.Xml.XmlReader.LocalName" /> 및 <see cref="P:System.Xml.XmlReader.NamespaceURI" />가 있는 요소인지 확인하고 판독기를 다음 노드로 이동합니다.</summary>
      <param name="localname">요소의 로컬 이름입니다.</param>
      <param name="ns">요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.Xml.XmlException">입력 스트림에 잘못된 XML이 있는 경우또는검색된 요소의 <see cref="P:System.Xml.XmlReader.LocalName" /> 및 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 속성은 주어진 인수와 일치하지 않습니다.</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ReadState">
      <summary>파생 클래스에서 재정의되면 판독기의 상태를 가져옵니다.</summary>
      <returns>판독기 상태를 지정하는 열거형 값 중 하나입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadSubtree">
      <summary>현재 노드와 해당 하위 노드 전체를 읽는 데 사용되는 새 XmlReader 인스턴스를 반환합니다.</summary>
      <returns>로 설정 된 새 XML 판독기 인스턴스 <see cref="F:System.Xml.ReadState.Initial" />.호출 된 <see cref="M:System.Xml.XmlReader.Read" /> 메서드를 호출 하기 전에 현재 노드 였던 노드에 새 판독기가 배치는 <see cref="M:System.Xml.XmlReader.ReadSubtree" /> 메서드.</returns>
      <exception cref="T:System.InvalidOperationException">이 메서드가 호출 될 때 XML 판독기 요소에 배치 되지 않습니다.</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String)">
      <summary>지정된 정규화 이름을 사용하는 다음 하위 요소로 <see cref="T:System.Xml.XmlReader" />를 이동합니다.</summary>
      <returns>일치하는 하위 요소가 있으면 true이고, 그렇지 않으면 false입니다.일치하는 하위 요소가 없으면 요소의 끝 태그, 즉 <see cref="P:System.Xml.XmlReader.NodeType" />이 XmlNodeType.EndElement인 태그에 <see cref="T:System.Xml.XmlReader" />가 배치됩니다.<see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String)" />를 호출했을 때 <see cref="T:System.Xml.XmlReader" />가 요소에 배치되어 있지 않으면 이 메서드가 false를 반환하고 <see cref="T:System.Xml.XmlReader" />의 위치는 변경되지 않습니다.</returns>
      <param name="name">판독기를 이동할 요소의 정규화된 이름입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.ArgumentException">매개 변수가 빈 문자열인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI를 사용하는 다음 하위 요소로 <see cref="T:System.Xml.XmlReader" />를 이동합니다.</summary>
      <returns>일치하는 하위 요소가 있으면 true이고, 그렇지 않으면 false입니다.일치하는 하위 요소가 없으면 요소의 끝 태그, 즉 <see cref="P:System.Xml.XmlReader.NodeType" />이 XmlNodeType.EndElement인 태그에 <see cref="T:System.Xml.XmlReader" />가 배치됩니다.If the <see cref="T:System.Xml.XmlReader" /> is not positioned on an element when <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)" /> was called, this method returns false and the position of the <see cref="T:System.Xml.XmlReader" /> is not changed.</returns>
      <param name="localName">판독기를 이동할 요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">판독기를 이동할 하위 요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.ArgumentNullException">두 매개 변수 값이 모두 null인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String)">
      <summary>지정된 정규화된 이름의 요소를 찾을 때까지 읽습니다.</summary>
      <returns>일치하는 요소가 있으면 true이고, 그렇지 않으면 false이고 <see cref="T:System.Xml.XmlReader" />가 파일 끝에 도달합니다.</returns>
      <param name="name">요소의 정규화된 이름입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.ArgumentException">매개 변수가 빈 문자열인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String,System.String)">
      <summary>지정된 로컬 이름 및 네임스페이스 URI를 사용하는 요소를 찾을 때까지 읽습니다.</summary>
      <returns>일치하는 요소가 있으면 true이고, 그렇지 않으면 false이고 <see cref="T:System.Xml.XmlReader" />가 파일 끝에 도달합니다.</returns>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.ArgumentNullException">두 매개 변수 값이 모두 null인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String)">
      <summary>지정된 정규화 이름을 사용하는 다음 형제 요소로 XmlReader를 이동합니다.</summary>
      <returns>일치하는 형제 요소가 있으면 true이고, 그렇지 않으면 false입니다.일치하는 형제 요소가 없으면 부모 요소의 끝 태그, 즉 <see cref="P:System.Xml.XmlReader.NodeType" />이 XmlNodeType.EndElement인 태그에 XmlReader가 배치됩니다.</returns>
      <param name="name">판독기를 이동할 형제 요소의 정규화된 이름입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.ArgumentException">매개 변수가 빈 문자열인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI를 사용하는 다음 형제 요소로 XmlReader를 이동합니다.</summary>
      <returns>일치하는 형제 요소가 있으면 true이고, 그렇지 않으면 false입니다.일치하는 형제 요소가 없으면 부모 요소의 끝 태그, 즉 <see cref="P:System.Xml.XmlReader.NodeType" />이 XmlNodeType.EndElement인 태그에 XmlReader가 배치됩니다.</returns>
      <param name="localName">판독기를 이동할 형제 요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">판독기를 이동할 형제 요소의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.ArgumentNullException">두 매개 변수 값이 모두 null인 경우</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)">
      <summary>XML 문서에 포함된 큰 텍스트 스트림을 읽습니다.</summary>
      <returns>버퍼로 읽어온 문자 수입니다.텍스트 콘텐츠가 더 이상 없으면 0이 반환됩니다.</returns>
      <param name="buffer">텍스트 콘텐츠를 쓸 버퍼 역할을 하는 문자 배열입니다.이 값은 null일 수 없습니다.</param>
      <param name="index">
        <see cref="T:System.Xml.XmlReader" />가 버퍼 내에서 결과 복사를 시작할 수 있는 오프셋입니다.</param>
      <param name="count">버퍼에 복사할 최대 문자 수입니다.이 메서드는 복사된 실제 문자 수를 반환합니다.</param>
      <exception cref="T:System.InvalidOperationException">현재 노드에 값이 없는 경우, 즉 <see cref="P:System.Xml.XmlReader.HasValue" />가 false인 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 값이 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">버퍼 내의 인덱스 또는 인덱스와 개수를 합한 값이 할당된 버퍼 크기보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">구현된 <see cref="T:System.Xml.XmlReader" />에서 이 메서드를 지원하지 않는 경우</exception>
      <exception cref="T:System.Xml.XmlException">XML 데이터가 올바른 형식이 아닌 경우</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunkAsync(System.Char[],System.Int32,System.Int32)">
      <summary>XML 문서에 포함된 큰 텍스트 스트림을 비동기적으로 읽습니다.</summary>
      <returns>버퍼로 읽어온 문자 수입니다.텍스트 콘텐츠가 더 이상 없으면 0이 반환됩니다.</returns>
      <param name="buffer">텍스트 콘텐츠를 쓸 버퍼 역할을 하는 문자 배열입니다.이 값은 null일 수 없습니다.</param>
      <param name="index">
        <see cref="T:System.Xml.XmlReader" />가 버퍼 내에서 결과 복사를 시작할 수 있는 오프셋입니다.</param>
      <param name="count">버퍼에 복사할 최대 문자 수입니다.이 메서드는 복사된 실제 문자 수를 반환합니다.</param>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ResolveEntity">
      <summary>파생 클래스에서 재정의되면 EntityReference 노드에 대한 엔터티 참조를 확인합니다.</summary>
      <exception cref="T:System.InvalidOperationException">판독기가 EntityReference 노드에 배치되지 않고 판독기의 이 구현에서 엔터티를 확인할 수 없는 경우(<see cref="P:System.Xml.XmlReader.CanResolveEntity" />가 false를 반환하는 경우)</exception>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Settings">
      <summary>Gets the <see cref="T:System.Xml.XmlReaderSettings" /> object used to create this <see cref="T:System.Xml.XmlReader" /> instance.</summary>
      <returns>이 판독기 인스턴스를 만드는 데 사용되는 <see cref="T:System.Xml.XmlReaderSettings" />입니다.<see cref="Overload:System.Xml.XmlReader.Create" /> 메서드를 사용하여 판독기를 만들지 않은 경우 이 속성은 null을 반환합니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Skip">
      <summary>현재 노드의 자식을 건너뜁니다.</summary>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="M:System.Xml.XmlReader.SkipAsync">
      <summary>현재 노드의 자식을 비동기적으로 건너뜁니다.</summary>
      <returns>현재 노드입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xml.XmlReaderSettings.Async" /> 플래그를 true로 설정하지 않고 <see cref="T:System.Xml.XmlReader" /> 비동기 메서드가 호출되었습니다.이 경우 <see cref="T:System.InvalidOperationException" />은 “비동기 메서드를 사용하려면 XmlReaderSettings.Async를 true로 설정하십시오.”라는 메시지와 함께 throw됩니다.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Value">
      <summary>파생 클래스에서 재정의되면 현재 노드의 텍스트 값을 가져옵니다.</summary>
      <returns>반환되는 값은 노드의 <see cref="P:System.Xml.XmlReader.NodeType" />에 따라 달라집니다.다음 표에서는 반환할 값이 있는 노드 형식을 보여 줍니다.다른 모든 노드 형식은 String.Empty를 반환합니다.노드 형식 값 Attribute특성 값입니다. CDATACDATA 섹션의 콘텐츠입니다. Comment주석의 콘텐츠입니다. DocumentType내부 하위 집합입니다. ProcessingInstruction대상을 제외한 전체 콘텐츠입니다. SignificantWhitespace혼합된 콘텐츠 모델의 태그 간 공백입니다. Text텍스트 노드의 내용입니다. Whitespace태그 사이의 공백입니다. XmlDeclaration선언의 내용입니다. </returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ValueType">
      <summary>현재 노드의 CLR(공용 언어 런타임) 형식을 가져옵니다.</summary>
      <returns>노드의 형식화된 값에 해당하는 CLR 형식입니다.기본값은 System.String입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlLang">
      <summary>파생 클래스에서 재정의되면 현재 xml:lang 범위를 가져옵니다.</summary>
      <returns>현재 xml:lang 범위입니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlSpace">
      <summary>파생 클래스에서 재정의되면 현재 xml:space 범위를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.XmlSpace" /> 값 중 하나입니다.xml:space 범위가 존재하지 않으면 이 속성은 기본적으로 XmlSpace.None으로 설정됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">이전 비동기 작업이 완료되기 전에 <see cref="T:System.Xml.XmlReader" /> 메서드가 호출되었습니다.이 경우 "비동기 작업이 이미 진행 중"이라는 메시지와 함께 <see cref="T:System.InvalidOperationException" />이 throw됩니다</exception>
    </member>
    <member name="T:System.Xml.XmlReaderSettings">
      <summary>
        <see cref="Overload:System.Xml.XmlReader.Create" /> 메서드를 사용하여 만든 <see cref="T:System.Xml.XmlReader" /> 개체에서 지원할 기능 집합을 지정합니다. </summary>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.#ctor">
      <summary>
        <see cref="T:System.Xml.XmlReaderSettings" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.Async">
      <summary>비동기 <see cref="T:System.Xml.XmlReader" /> 메서드를 특정 <see cref="T:System.Xml.XmlReader" /> 인스턴스에서 사용할 수 있는지 여부를 가져오거나 설정합니다.</summary>
      <returns>비동기 메서드를 사용할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CheckCharacters">
      <summary>문자 검사를 수행할지를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>문자 검사를 하려면 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.참고텍스트 데이터를 처리할 경우 <see cref="T:System.Xml.XmlReader" />는 이 속성의 설정에 상관없이 XML 이름 및 텍스트 콘텐츠의 유효성을 항상 검사합니다.<see cref="P:System.Xml.XmlReaderSettings.CheckCharacters" />를 false로 설정하면 문자 엔터티 참조에 대해 문자 검사가 수행되지 않습니다.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Clone">
      <summary>
        <see cref="T:System.Xml.XmlReaderSettings" /> 인스턴스의 복사본을 만듭니다.</summary>
      <returns>복제된 <see cref="T:System.Xml.XmlReaderSettings" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CloseInput">
      <summary>판독기를 닫을 때 내부 스트림 또는 <see cref="T:System.IO.TextReader" />를 함께 닫을지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>판독기를 닫을 때 내부 스트림 또는 <see cref="T:System.IO.TextReader" />를 함께 닫으면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.ConformanceLevel">
      <summary>
        <see cref="T:System.Xml.XmlReader" />에 적용할 규칙 수준을 가져오거나 설정합니다.</summary>
      <returns>XML 판독기를 적용할 규칙 수준을 지정하는 열거형 값 중 하나입니다.기본값은 <see cref="F:System.Xml.ConformanceLevel.Document" />입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.DtdProcessing">
      <summary>DTD 처리를 결정하는 값을 가져오거나 설정합니다.</summary>
      <returns>DTD 처리를 결정하는 열거형 값 중 하나입니다.기본값은 <see cref="F:System.Xml.DtdProcessing.Prohibit" />입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreComments">
      <summary>주석을 무시할지를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>주석을 무시하면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreProcessingInstructions">
      <summary>처리 명령을 무시할지를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>처리 명령을 무시하면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreWhitespace">
      <summary>유효하지 않은 공백을 무시할지를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>공백을 무시하면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LineNumberOffset">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> 개체의 줄 번호 오프셋을 가져오거나 설정합니다.</summary>
      <returns>줄 번호 오프셋입니다.기본값은 0입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LinePositionOffset">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> 개체의 줄 위치 오프셋을 가져오거나 설정합니다.</summary>
      <returns>선 위치 오프셋입니다.기본값은 0입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersFromEntities">
      <summary>문서에서 엔터티 확장 후의 최대 허용 문자 수를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>확장된 엔터티의 최대 허용 문자 수입니다.기본값은 0입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersInDocument">
      <summary>XML 문서의 최대 허용 문자 수를 나타내는 값을 가져오거나 설정합니다.값 0은 XML 문서 크기에 제한이 없음을 의미합니다.0이 아닌 값은 최대 크기(문자 수)를 지정합니다.</summary>
      <returns>XML 문서의 최대 허용 문자 수입니다.기본값은 0입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.NameTable">
      <summary>원자화된 문자열을 비교하는 데 사용할 <see cref="T:System.Xml.XmlNameTable" />을 가져오거나 설정합니다.</summary>
      <returns>이 <see cref="T:System.Xml.XmlReaderSettings" /> 개체를 사용하여 만든 모든 <see cref="T:System.Xml.XmlReader" /> 인스턴스에서 사용하는 원자화된 문자열 전체가 저장되는 <see cref="T:System.Xml.XmlNameTable" />입니다.기본값은 null입니다.이 값이 null이면 <see cref="T:System.Xml.XmlReader" /> 인스턴스는 비어 있는 새 <see cref="T:System.Xml.NameTable" />을 사용합니다.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Reset">
      <summary>설정 클래스의 멤버를 해당 기본값으로 다시 설정합니다.</summary>
    </member>
    <member name="T:System.Xml.XmlSpace">
      <summary>현재 xml:space 범위를 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Default">
      <summary>xml:space 범위가 default입니다.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.None">
      <summary>xml:space 범위가 없습니다.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Preserve">
      <summary>xml:space 범위가 preserve입니다.</summary>
    </member>
    <member name="T:System.Xml.XmlWriter">
      <summary>XML 데이터가 포함된 스트림 또는 파일을 생성할 수 있도록 빠르고, 앞으로만 이동 가능하며, 캐시되지 않은 방법을 제공하는 작성기를 나타냅니다.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.#ctor">
      <summary>
        <see cref="T:System.Xml.XmlWriter" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream)">
      <summary>지정된 스트림을 사용하여 새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 만듭니다.</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <param name="output">작성하려는 스트림입니다.<see cref="T:System.Xml.XmlWriter" />는 XML 1.0 텍스트 구문을 쓴 후 지정된 스트림에 추가합니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream,System.Xml.XmlWriterSettings)">
      <summary>스트림과 <see cref="T:System.Xml.XmlWriterSettings" /> 개체를 사용하여 새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 만듭니다.</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <param name="output">작성하려는 스트림입니다.<see cref="T:System.Xml.XmlWriter" />는 XML 1.0 텍스트 구문을 쓴 후 지정된 스트림에 추가합니다.</param>
      <param name="settings">새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 구성하는 데 사용되는 <see cref="T:System.Xml.XmlWriterSettings" /> 개체입니다.값이 null이면 기본 설정이 지정된 <see cref="T:System.Xml.XmlWriterSettings" />이 사용됩니다.<see cref="T:System.Xml.XmlWriter" />가 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 메서드와 함께 사용되는 경우 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 속성을 사용하여 올바른 설정을 포함하는 <see cref="T:System.Xml.XmlWriterSettings" /> 개체를 가져와야 합니다.이에 따라 만들어진 <see cref="T:System.Xml.XmlWriter" /> 개체가 올바른 출력 설정을 갖게 됩니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter)">
      <summary>지정된 <see cref="T:System.IO.TextWriter" />를 사용하여 새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 만듭니다.</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <param name="output">쓰기에 사용할 <see cref="T:System.IO.TextWriter" />입니다.<see cref="T:System.Xml.XmlWriter" />는 XML 1.0 텍스트 구문을 쓴 후 지정된 <see cref="T:System.IO.TextWriter" />에 추가합니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter,System.Xml.XmlWriterSettings)">
      <summary>지정된 <see cref="T:System.IO.TextWriter" /> 및 <see cref="T:System.Xml.XmlWriterSettings" /> 개체를 사용하여 새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 만듭니다.</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <param name="output">쓰기에 사용할 <see cref="T:System.IO.TextWriter" />입니다.<see cref="T:System.Xml.XmlWriter" />는 XML 1.0 텍스트 구문을 쓴 후 지정된 <see cref="T:System.IO.TextWriter" />에 추가합니다.</param>
      <param name="settings">새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 구성하는 데 사용되는 <see cref="T:System.Xml.XmlWriterSettings" /> 개체입니다.값이 null이면 기본 설정이 지정된 <see cref="T:System.Xml.XmlWriterSettings" />이 사용됩니다.<see cref="T:System.Xml.XmlWriter" />가 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 메서드와 함께 사용되는 경우 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 속성을 사용하여 올바른 설정을 포함하는 <see cref="T:System.Xml.XmlWriterSettings" /> 개체를 가져와야 합니다.이에 따라 만들어진 <see cref="T:System.Xml.XmlWriter" /> 개체가 올바른 출력 설정을 갖게 됩니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder)">
      <summary>지정된 <see cref="T:System.Text.StringBuilder" />를 사용하여 새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 만듭니다.</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <param name="output">쓰기에 사용할 <see cref="T:System.Text.StringBuilder" />입니다.<see cref="T:System.Xml.XmlWriter" />가 쓰는 콘텐츠는 <see cref="T:System.Text.StringBuilder" />에 추가됩니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder,System.Xml.XmlWriterSettings)">
      <summary>
        <see cref="T:System.Text.StringBuilder" /> 및 <see cref="T:System.Xml.XmlWriterSettings" /> 개체를 사용하여 새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 만듭니다.</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <param name="output">쓰기에 사용할 <see cref="T:System.Text.StringBuilder" />입니다.<see cref="T:System.Xml.XmlWriter" />가 쓰는 콘텐츠는 <see cref="T:System.Text.StringBuilder" />에 추가됩니다.</param>
      <param name="settings">새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 구성하는 데 사용되는 <see cref="T:System.Xml.XmlWriterSettings" /> 개체입니다.값이 null이면 기본 설정이 지정된 <see cref="T:System.Xml.XmlWriterSettings" />이 사용됩니다.<see cref="T:System.Xml.XmlWriter" />가 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 메서드와 함께 사용되는 경우 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 속성을 사용하여 올바른 설정을 포함하는 <see cref="T:System.Xml.XmlWriterSettings" /> 개체를 가져와야 합니다.이에 따라 만들어진 <see cref="T:System.Xml.XmlWriter" /> 개체가 올바른 출력 설정을 갖게 됩니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter)">
      <summary>지정된 <see cref="T:System.Xml.XmlWriter" /> 개체를 사용하여 새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 만듭니다.</summary>
      <returns>지정된 <see cref="T:System.Xml.XmlWriter" /> 개체를 래핑하는 <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <param name="output">내부 작성기로 사용할 <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter,System.Xml.XmlWriterSettings)">
      <summary>지정된 <see cref="T:System.Xml.XmlWriter" /> 및 <see cref="T:System.Xml.XmlWriterSettings" /> 개체를 사용하여 새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 만듭니다.</summary>
      <returns>지정된 <see cref="T:System.Xml.XmlWriter" /> 개체를 래핑하는 <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <param name="output">내부 작성기로 사용할 <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</param>
      <param name="settings">새 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 구성하는 데 사용되는 <see cref="T:System.Xml.XmlWriterSettings" /> 개체입니다.값이 null이면 기본 설정이 지정된 <see cref="T:System.Xml.XmlWriterSettings" />이 사용됩니다.<see cref="T:System.Xml.XmlWriter" />가 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 메서드와 함께 사용되는 경우 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 속성을 사용하여 올바른 설정을 포함하는 <see cref="T:System.Xml.XmlWriterSettings" /> 개체를 가져와야 합니다.이에 따라 만들어진 <see cref="T:System.Xml.XmlWriter" /> 개체가 올바른 출력 설정을 갖게 됩니다.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose">
      <summary>
        <see cref="T:System.Xml.XmlWriter" /> 클래스의 현재 인스턴스에서 사용하는 모든 리소스를 해제합니다.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Xml.XmlWriter" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 해제할 수 있습니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Flush">
      <summary>파생 클래스에서 재정의되면 버퍼에 있는 항목을 내부 스트림으로 플러시하고 내부 스트림도 플러시합니다.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.FlushAsync">
      <summary>버퍼에 있는 모든 내용을 내부 스트림으로 비동기적으로 플러시하고 내부 스트림도 플러시합니다.</summary>
      <returns>비동기 Flush 작업을 나타내는 작업입니다.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.LookupPrefix(System.String)">
      <summary>파생 클래스에서 재정의되면 네임스페이스 URI의 현재 네임스페이스 범위에 정의된 가장 비슷한 접두사를 반환합니다.</summary>
      <returns>일치하는 접두사이거나 현재 범위에 일치하는 네임스페이스 URI가 없는 경우 null입니다.</returns>
      <param name="ns">찾으려는 접두사를 가진 네임스페이스 URI입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ns" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.Settings">
      <summary>이 <see cref="T:System.Xml.XmlWriter" /> 인스턴스를 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriterSettings" /> 개체를 가져옵니다.</summary>
      <returns>이 작성기 인스턴스를 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriterSettings" />입니다.<see cref="Overload:System.Xml.XmlWriter.Create" /> 메서드를 사용하여 작성기를 만들지 않은 경우 이 속성은 null을 반환합니다.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributes(System.Xml.XmlReader,System.Boolean)">
      <summary>파생 클래스에서 재정의되면 <see cref="T:System.Xml.XmlReader" />의 현재 위치에 있는 모든 특성을 작성합니다.</summary>
      <param name="reader">특성을 복사할 원본 XmlReader입니다.</param>
      <param name="defattr">XmlReader에서 기본 특성을 복사하려면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null. </exception>
      <exception cref="T:System.Xml.XmlException">The reader is not positioned on an element, attribute or XmlDeclaration node. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributesAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>
        <see cref="T:System.Xml.XmlReader" />의 현재 위치에서 찾은 모든 특성을 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteAttributes 작업을 나타내는 작업입니다.</returns>
      <param name="reader">특성을 복사할 원본 XmlReader입니다.</param>
      <param name="defattr">XmlReader에서 기본 특성을 복사하려면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 로컬 이름 및 값이 있는 특성을 작성합니다.</summary>
      <param name="localName">특성의 로컬 이름입니다.</param>
      <param name="value">특성 값</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 로컬 이름, 네임스페이스 URI 및 값을 갖는 특성을 작성합니다.</summary>
      <param name="localName">특성의 로컬 이름입니다.</param>
      <param name="ns">특성에 연결할 네임스페이스 URI입니다.</param>
      <param name="value">특성 값</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 접두사, 로컬 이름, 네임스페이스 URI 및 값을 갖는 특성을 작성합니다.</summary>
      <param name="prefix">특성의 네임스페이스 접두사입니다.</param>
      <param name="localName">특성의 로컬 이름입니다.</param>
      <param name="ns">특성의 네임스페이스 URI입니다.</param>
      <param name="value">특성 값</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.Xml.XmlException">The <paramref name="localName" /> or <paramref name="ns" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeStringAsync(System.String,System.String,System.String,System.String)">
      <summary>지정된 접두사, 로컬 이름, 네임스페이스 URI 및 값을 사용하여 특성을 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteAttributeString 작업을 나타내는 작업입니다.</returns>
      <param name="prefix">특성의 네임스페이스 접두사입니다.</param>
      <param name="localName">특성의 로컬 이름입니다.</param>
      <param name="ns">특성의 네임스페이스 URI입니다.</param>
      <param name="value">특성 값</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정된 이진 바이트를 Base64로 인코딩하고 결과 텍스트를 작성합니다.</summary>
      <param name="buffer">인코딩할 바이트 배열입니다.</param>
      <param name="index">쓸 바이트의 시작을 나타내는 버퍼 내의 위치입니다.</param>
      <param name="count">쓸 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>지정된 바이너리 바이트를 Base64로 비동기적으로 인코딩하고 결과 텍스트를 작성합니다.</summary>
      <returns>비동기 WriteBase64 작업을 나타내는 작업입니다.</returns>
      <param name="buffer">인코딩할 바이트 배열입니다.</param>
      <param name="index">쓸 바이트의 시작을 나타내는 버퍼 내의 위치입니다.</param>
      <param name="count">쓸 바이트 수입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정된 이진 바이트를 BinHex로 인코딩하고 결과 텍스트를 작성합니다.</summary>
      <param name="buffer">인코딩할 바이트 배열입니다.</param>
      <param name="index">쓸 바이트의 시작을 나타내는 버퍼 내의 위치입니다.</param>
      <param name="count">쓸 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>지정된 바이너리 바이트를 BinHex로 비동기적으로 인코딩하고 결과 텍스트를 작성합니다.</summary>
      <returns>비동기 WriteBinHex 작업을 나타내는 작업입니다.</returns>
      <param name="buffer">인코딩할 바이트 배열입니다.</param>
      <param name="index">쓸 바이트의 시작을 나타내는 버퍼 내의 위치입니다.</param>
      <param name="count">쓸 바이트 수입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCData(System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 텍스트가 포함된 &lt;![CDATA[...]]&gt; 블록을 작성합니다.</summary>
      <param name="text">CDATA 블록 내부에 배치할 텍스트입니다.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCDataAsync(System.String)">
      <summary>지정된 텍스트를 포함하는 &lt;![CDATA[...]]&gt; 블록을 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteCData 작업을 나타내는 작업입니다.</returns>
      <param name="text">CDATA 블록 내부에 배치할 텍스트입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntity(System.Char)">
      <summary>파생 클래스에서 재정의되면 지정된 유니코드 문자 값의 문자 엔터티를 생성하게 합니다.</summary>
      <param name="ch">문자 엔터티를 생성할 유니코드 문자입니다.</param>
      <exception cref="T:System.ArgumentException">The character is in the surrogate pair character range, 0xd800 - 0xdfff.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntityAsync(System.Char)">
      <summary>지정된 유니코드 문자 값에 대한 문자 엔터티가 비동기적으로 생성되도록 합니다.</summary>
      <returns>비동기 WriteCharEntity 작업을 나타내는 작업입니다.</returns>
      <param name="ch">문자 엔터티를 생성할 유니코드 문자입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteChars(System.Char[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의되면 한 번에 한 버퍼씩 텍스트를 작성합니다.</summary>
      <param name="buffer">쓸 텍스트가 포함된 문자 배열입니다.</param>
      <param name="index">쓸 텍스트의 시작을 나타내는 버퍼 내의 위치입니다.</param>
      <param name="count">쓸 문자 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />; the call results in surrogate pair characters being split or an invalid surrogate pair being written.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="buffer" /> parameter value is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharsAsync(System.Char[],System.Int32,System.Int32)">
      <summary>한 번에 한 버퍼씩 텍스트를 비동기적으로 씁니다.</summary>
      <returns>비동기 WriteChars 작업을 나타내는 작업입니다.</returns>
      <param name="buffer">쓸 텍스트가 포함된 문자 배열입니다.</param>
      <param name="index">쓸 텍스트의 시작을 나타내는 버퍼 내의 위치입니다.</param>
      <param name="count">쓸 문자 수입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteComment(System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 텍스트가 포함된 주석(&lt;!--...--&gt;)을 작성합니다.</summary>
      <param name="text">주석 내에 배치할 텍스트입니다.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well-formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCommentAsync(System.String)">
      <summary>지정된 텍스트를 포함하는 주석&lt;!--...--&gt;을 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteComment 작업을 나타내는 작업입니다.</returns>
      <param name="text">주석 내에 배치할 텍스트입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocType(System.String,System.String,System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 이름 및 선택적 특성이 있는 DOCTYPE 선언을 작성합니다.</summary>
      <param name="name">DOCTYPE의 이름입니다.이 이름은 비어 있지 않아야 합니다.</param>
      <param name="pubid">null이 아닌 경우 PUBLIC "pubid" "sysid"도 씁니다. 여기서 <paramref name="pubid" />와 <paramref name="sysid" />는 지정된 인수 값으로 바뀝니다.</param>
      <param name="sysid">
        <paramref name="pubid" />가 null이고 <paramref name="sysid" />가 null이 아닌 경우 SYSTEM "sysid"를 씁니다. 여기서 <paramref name="sysid" />는 이 인수 값으로 바뀝니다.</param>
      <param name="subset">null이 아닌 경우 하위 집합이 이 인수 값으로 대체되는 [subset]을 작성합니다.</param>
      <exception cref="T:System.InvalidOperationException">This method was called outside the prolog (after the root element). </exception>
      <exception cref="T:System.ArgumentException">The value for <paramref name="name" /> would result in invalid XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocTypeAsync(System.String,System.String,System.String,System.String)">
      <summary>지정된 이름과 선택적 특성을 사용하여 DOCTYPE 선언을 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteDocType 작업을 나타내는 작업입니다.</returns>
      <param name="name">DOCTYPE의 이름입니다.이 이름은 비어 있지 않아야 합니다.</param>
      <param name="pubid">null이 아닌 경우 PUBLIC "pubid" "sysid"도 씁니다. 여기서 <paramref name="pubid" />와 <paramref name="sysid" />는 지정된 인수 값으로 바뀝니다.</param>
      <param name="sysid">
        <paramref name="pubid" />가 null이고 <paramref name="sysid" />가 null이 아닌 경우 SYSTEM "sysid"를 씁니다. 여기서 <paramref name="sysid" />는 이 인수 값으로 바뀝니다.</param>
      <param name="subset">null이 아닌 경우 하위 집합이 이 인수 값으로 대체되는 [subset]을 작성합니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String)">
      <summary>지정된 로컬 이름 및 값을 사용하여 요소를 작성합니다.</summary>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="value">요소의 값입니다.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String)">
      <summary>지정된 로컬 이름, 네임스페이스 URI 및 값을 사용하여 요소를 작성합니다.</summary>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="ns">요소와 연결할 네임스페이스 URI입니다.</param>
      <param name="value">요소의 값입니다.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String,System.String)">
      <summary>지정된 접두사, 로컬 이름, 네임스페이스 URI 및 값을 사용하여 요소를 씁니다.</summary>
      <param name="prefix">요소의 접두사입니다.</param>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="ns">요소의 네임스페이스 URI입니다.</param>
      <param name="value">요소의 값입니다.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementStringAsync(System.String,System.String,System.String,System.String)">
      <summary>지정된 접두사, 로컬 이름, 네임스페이스 URI 및 값을 사용하여 요소를 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteElementString 작업을 나타내는 작업입니다.</returns>
      <param name="prefix">요소의 접두사입니다.</param>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="ns">요소의 네임스페이스 URI입니다.</param>
      <param name="value">요소의 값입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttribute">
      <summary>파생 클래스에서 재정의되면 이전 <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> 호출을 닫습니다.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttributeAsync">
      <summary>이전 <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> 호출을 비동기적으로 닫습니다.</summary>
      <returns>비동기 WriteEndAttribute 작업을 나타내는 작업입니다.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocument">
      <summary>파생 클래스에서 재정의되면 열려 있는 모든 요소나 특성을 닫고 작성기를 다시 시작 상태로 설정합니다.</summary>
      <exception cref="T:System.ArgumentException">The XML document is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocumentAsync">
      <summary>열려 있는 모든 요소나 특성을 비동기적으로 닫고 작성기를 시작 상태로 설정합니다.</summary>
      <returns>비동기 WriteEndDocument 작업을 나타내는 작업입니다.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElement">
      <summary>파생 클래스에서 재정의되면 한 요소를 닫고 해당 네임스페이스 범위를 팝합니다.</summary>
      <exception cref="T:System.InvalidOperationException">This results in an invalid XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElementAsync">
      <summary>한 요소를 비동기적으로 닫고 해당 네임스페이스 범위를 팝합니다.</summary>
      <returns>비동기 WriteEndElement 작업을 나타내는 작업입니다.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRef(System.String)">
      <summary>파생 클래스에서 재정의되면 &amp;name; 같이 엔터티 참조를 작성합니다.</summary>
      <param name="name">엔터티 참조의 이름입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRefAsync(System.String)">
      <summary>엔터티 참조를 &amp;name;으로 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteEntityRef 작업을 나타내는 작업입니다.</returns>
      <param name="name">엔터티 참조의 이름입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElement">
      <summary>파생 클래스에서 재정의되면 한 요소를 닫고 해당 네임스페이스 범위를 팝합니다.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElementAsync">
      <summary>한 요소를 비동기적으로 닫고 해당 네임스페이스 범위를 팝합니다.</summary>
      <returns>비동기 WriteFullEndElement 작업을 나타내는 작업입니다.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteName(System.String)">
      <summary>파생 클래스에서 재정의되면 W3C XML 1.0 권장 사항(http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name)에 따라 유효한 이름이 되도록 지정된 이름을 작성합니다.</summary>
      <param name="name">작성할 이름입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid XML name; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNameAsync(System.String)">
      <summary>W3C XML 1.0 권장 사항(http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name)에 따라 유효한 이름이 되도록 지정된 이름을 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteName 작업을 나타내는 작업입니다.</returns>
      <param name="name">작성할 이름입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmToken(System.String)">
      <summary>파생 클래스에서 재정의되면 W3C XML 1.0 권장 사항(http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name)에 따라 NmToken이 되도록 지정된 이름을 작성합니다.</summary>
      <param name="name">작성할 이름입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid NmToken; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmTokenAsync(System.String)">
      <summary>W3C XML 1.0 권장 사항(http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name)에 따라 유효한 NmToken이 되도록 지정된 이름을 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteNmToken 작업을 나타내는 작업입니다.</returns>
      <param name="name">작성할 이름입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNode(System.Xml.XmlReader,System.Boolean)">
      <summary>파생 클래스에서 재정의되면 판독기에서 작성기로 모든 항목을 복사하고 판독기를 다음 형제 노드의 시작 부분으로 이동합니다.</summary>
      <param name="reader">읽을 <see cref="T:System.Xml.XmlReader" />입니다.</param>
      <param name="defattr">XmlReader에서 기본 특성을 복사하려면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains invalid characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNodeAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>파생 클래스에서 재정의되면 판독기에서 작성기로 모든 항목을 비동기적으로 복사하고 판독기를 다음 형제 노드의 시작 부분으로 이동합니다.</summary>
      <returns>비동기 WriteNode 작업을 나타내는 작업입니다.</returns>
      <param name="reader">읽을 <see cref="T:System.Xml.XmlReader" />입니다.</param>
      <param name="defattr">XmlReader에서 기본 특성을 복사하려면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstruction(System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 &lt;?name text?&gt; 같이 이름과 텍스트 사이에 공백이 있는 처리 명령을 작성합니다.</summary>
      <param name="name">처리 명령의 이름입니다.</param>
      <param name="text">처리 명령에 포함할 텍스트입니다.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.<paramref name="name" /> is either null or String.Empty.This method is being used to create an XML declaration after <see cref="M:System.Xml.XmlWriter.WriteStartDocument" /> has already been called. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstructionAsync(System.String,System.String)">
      <summary>이름과 텍스트 사이의 공백을 사용하여 처리 명령을 비동기적으로 씁니다(예: &lt;?name text?&gt;).</summary>
      <returns>비동기 WriteProcessingInstruction 작업을 나타내는 작업입니다.</returns>
      <param name="name">처리 명령의 이름입니다.</param>
      <param name="text">처리 명령에 포함할 텍스트입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedName(System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 네임스페이스로 한정된 이름을 작성합니다.이 메서드는 지정된 네임스페이스의 범위에 속하는 접두사를 찾습니다.</summary>
      <param name="localName">작성할 로컬 이름입니다.</param>
      <param name="ns">이름의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="localName" /> is either null or String.Empty.<paramref name="localName" /> is not a valid name. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedNameAsync(System.String,System.String)">
      <summary>네임스페이스로 한정된 이름을 비동기적으로 작성합니다.이 메서드는 지정된 네임스페이스의 범위에 속하는 접두사를 찾습니다.</summary>
      <returns>비동기 WriteQualifiedName 작업을 나타내는 작업입니다.</returns>
      <param name="localName">작성할 로컬 이름입니다.</param>
      <param name="ns">이름의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.Char[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의되면 문자 버퍼에서 원시 태그를 직접 작성합니다.</summary>
      <param name="buffer">쓸 텍스트가 포함된 문자 배열입니다.</param>
      <param name="index">쓸 텍스트의 시작을 나타내는 버퍼 내의 위치입니다.</param>
      <param name="count">쓸 문자 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.String)">
      <summary>파생 클래스에서 재정의되면 문자열에서 원시 태그를 직접 작성합니다.</summary>
      <param name="data">작성할 텍스트를 포함하는 문자열입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.Char[],System.Int32,System.Int32)">
      <summary>문자 버퍼에서 직접 원시 태그를 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteRaw 작업을 나타내는 작업입니다.</returns>
      <param name="buffer">쓸 텍스트가 포함된 문자 배열입니다.</param>
      <param name="index">쓸 텍스트의 시작을 나타내는 버퍼 내의 위치입니다.</param>
      <param name="count">쓸 문자 수입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.String)">
      <summary>문자열에서 직접 원시 태그를 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteRaw 작업을 나타내는 작업입니다.</returns>
      <param name="data">작성할 텍스트를 포함하는 문자열입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String)">
      <summary>지정된 로컬 이름을 사용하여 특성의 시작 부분을 작성합니다.</summary>
      <param name="localName">특성의 로컬 이름입니다.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI를 사용하여 특성의 시작 부분을 작성합니다.</summary>
      <param name="localName">특성의 로컬 이름입니다.</param>
      <param name="ns">특성의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 접두사, 로컬 이름 및 네임스페이스 URI를 사용하여 특성의 시작 부분을 작성합니다.</summary>
      <param name="prefix">특성의 네임스페이스 접두사입니다.</param>
      <param name="localName">특성의 로컬 이름입니다.</param>
      <param name="ns">특성의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttributeAsync(System.String,System.String,System.String)">
      <summary>지정된 접두사, 로컬 이름 및 네임스페이스 URI를 사용하여 특성의 시작 부분을 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteStartAttribute 작업을 나타내는 작업입니다.</returns>
      <param name="prefix">특성의 네임스페이스 접두사입니다.</param>
      <param name="localName">특성의 로컬 이름입니다.</param>
      <param name="ns">특성의 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument">
      <summary>파생 클래스에서 재정의되면 버전이 "1.0"인 XML 선언을 작성합니다.</summary>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument(System.Boolean)">
      <summary>파생 클래스에서 재정의되면 버전이 "1.0"이고 독립형 특성이 포함된 XML 선언을 작성합니다.</summary>
      <param name="standalone">true이면 "standalone=yes"로 쓰고, false이면 "standalone=no"로 씁니다.</param>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync">
      <summary>버전 "1.0"을 사용하여 XML 선언을 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteStartDocument 작업을 나타내는 작업입니다.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync(System.Boolean)">
      <summary>버전 "1.0"과 독립형 특성을 사용하여 XML 선언을 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteStartDocument 작업을 나타내는 작업입니다.</returns>
      <param name="standalone">true이면 "standalone=yes"로 쓰고, false이면 "standalone=no"로 씁니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 로컬 이름을 사용하여 시작 태그를 작성합니다.</summary>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 시작 태그를 작성하고 지정된 네임스페이스에 연결합니다.</summary>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="ns">요소와 연결할 네임스페이스 URI입니다.이 네임스페이스가 이미 범위에 있고 관련된 접두사가 있는 경우 작성기는 해당 접두사도 자동으로 작성합니다.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String,System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 시작 태그를 작성하고 지정된 네임스페이스 및 접두사에 연결합니다.</summary>
      <param name="prefix">요소의 네임스페이스 접두사입니다.</param>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="ns">요소와 연결할 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElementAsync(System.String,System.String,System.String)">
      <summary>지정된 시작 태그를 비동기적으로 작성하고 주어진 네임스페이스 및 접두사와 연결합니다.</summary>
      <returns>비동기 WriteStartElement 작업을 나타내는 작업입니다.</returns>
      <param name="prefix">요소의 네임스페이스 접두사입니다.</param>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="ns">요소와 연결할 네임스페이스 URI입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.WriteState">
      <summary>파생 클래스에서 재정의되면 작성기의 상태를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.WriteState" /> 값 중 하나입니다.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteString(System.String)">
      <summary>파생 클래스에서 재정의되는 경우 지정된 텍스트 콘텐츠를 작성합니다.</summary>
      <param name="text">쓸 텍스트입니다.</param>
      <exception cref="T:System.ArgumentException">The text string contains an invalid surrogate pair.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStringAsync(System.String)">
      <summary>주어진 텍스트 콘텐츠를 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteString 작업을 나타내는 작업입니다.</returns>
      <param name="text">쓸 텍스트입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntity(System.Char,System.Char)">
      <summary>파생 클래스에서 재정의되면 서로게이트 문자 쌍에 대한 서로게이트 문자 엔터티를 생성하고 작성합니다.</summary>
      <param name="lowChar">하위 서로게이트입니다.이 값은 0xDC00에서 0xDFFF 사이에 있어야 합니다.</param>
      <param name="highChar">상위 서로게이트입니다.이 값은 0xD800에서 0xDBFF 사이에 있어야 합니다.</param>
      <exception cref="T:System.ArgumentException">An invalid surrogate character pair was passed.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntityAsync(System.Char,System.Char)">
      <summary>서로게이트 문자 쌍에 대한 서로게이트 문자 엔터티를 비동기적으로 생성하고 작성합니다.</summary>
      <returns>비동기 WriteSurrogateCharEntity 작업을 나타내는 작업입니다.</returns>
      <param name="lowChar">하위 서로게이트입니다.이 값은 0xDC00에서 0xDFFF 사이에 있어야 합니다.</param>
      <param name="highChar">상위 서로게이트입니다.이 값은 0xD800에서 0xDBFF 사이에 있어야 합니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Boolean)">
      <summary>
        <see cref="T:System.Boolean" /> 값을 씁니다.</summary>
      <param name="value">쓸 <see cref="T:System.Boolean" /> 값입니다.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.DateTimeOffset)">
      <summary>
        <see cref="T:System.DateTimeOffset" /> 값을 씁니다.</summary>
      <param name="value">쓸 <see cref="T:System.DateTimeOffset" /> 값입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Decimal)">
      <summary>
        <see cref="T:System.Decimal" /> 값을 씁니다.</summary>
      <param name="value">쓸 <see cref="T:System.Decimal" /> 값입니다.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Double)">
      <summary>
        <see cref="T:System.Double" /> 값을 씁니다.</summary>
      <param name="value">쓸 <see cref="T:System.Double" /> 값입니다.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int32)">
      <summary>
        <see cref="T:System.Int32" /> 값을 씁니다.</summary>
      <param name="value">쓸 <see cref="T:System.Int32" /> 값입니다.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int64)">
      <summary>
        <see cref="T:System.Int64" /> 값을 씁니다.</summary>
      <param name="value">쓸 <see cref="T:System.Int64" /> 값입니다.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Object)">
      <summary>개체 값을 씁니다.</summary>
      <param name="value">쓸 개체 값입니다.참고   .NET Framework 3.5 릴리스에서 이 메서드는 <see cref="T:System.DateTimeOffset" />을 매개 변수로 받습니다.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Single)">
      <summary>단정밀도 부동 소수점 숫자를 씁니다.</summary>
      <param name="value">쓸 단정밀도 부동 소수점 숫자입니다.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.String)">
      <summary>
        <see cref="T:System.String" /> 값을 씁니다.</summary>
      <param name="value">쓸 <see cref="T:System.String" /> 값입니다.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespace(System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 공백을 작성합니다.</summary>
      <param name="ws">공백 문자의 문자열입니다.</param>
      <exception cref="T:System.ArgumentException">The string contains non-white space characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespaceAsync(System.String)">
      <summary>주어진 공백을 비동기적으로 작성합니다.</summary>
      <returns>비동기 WriteWhitespace 작업을 나타내는 작업입니다.</returns>
      <param name="ws">공백 문자의 문자열입니다.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlLang">
      <summary>파생 클래스에서 재정의되면 현재 xml:lang 범위를 가져옵니다.</summary>
      <returns>현재 xml:lang 범위입니다.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlSpace">
      <summary>파생 클래스에서 재정의되면 현재 xml:space 범위를 나타내는 <see cref="T:System.Xml.XmlSpace" />를 가져옵니다.</summary>
      <returns>현재 xml:space 범위를 나타내는 XmlSpace입니다.값 의미 Nonexml:space 범위가 없는 경우 기본값입니다.Default현재 범위가 xml:space="default"입니다.Preserve현재 범위가 xml:space="preserve"입니다.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="T:System.Xml.XmlWriterSettings">
      <summary>
        <see cref="Overload:System.Xml.XmlWriter.Create" /> 메서드를 사용하여 만든 <see cref="T:System.Xml.XmlWriter" /> 개체에서 지원할 기능 집합을 지정합니다.</summary>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.#ctor">
      <summary>
        <see cref="T:System.Xml.XmlWriterSettings" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Async">
      <summary>비동기 <see cref="T:System.Xml.XmlWriter" /> 메서드를 특정 <see cref="T:System.Xml.XmlWriter" /> 인스턴스에서 사용할 수 있는지를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>비동기 메서드를 사용할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CheckCharacters">
      <summary>XML 작성기가 문서의 모든 문자가 W3C XML 1.0 권장 사항의 "2.2 문자"를 따르는지 확인해야 하는 경우 표시하는 값을 가져오거나 설정합니다.</summary>
      <returns>문자 검사를 하려면 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Clone">
      <summary>
        <see cref="T:System.Xml.XmlWriterSettings" /> 인스턴스의 복사본을 만듭니다.</summary>
      <returns>복제된 <see cref="T:System.Xml.XmlWriterSettings" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CloseOutput">
      <summary>
        <see cref="M:System.Xml.XmlWriter.Close" /> 메서드를 호출한 경우 <see cref="T:System.IO.TextWriter" />가 내부 스트림 또는 <see cref="T:System.Xml.XmlWriter" />도 함께 닫을지를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>내부 스트림 또는 <see cref="T:System.IO.TextWriter" />를 함께 닫으려면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.ConformanceLevel">
      <summary>XML 작성기가 XML 출력을 확인하는 규칙 수준을 가져오거나 설정합니다.</summary>
      <returns>규칙 수준(문서, 조각 또는 자동 검색)을 지정하는 열거형 값 중 하나입니다.기본값은 <see cref="F:System.Xml.ConformanceLevel.Document" />입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Encoding">
      <summary>사용할 텍스트 인코딩의 형식을 가져오거나 설정합니다.</summary>
      <returns>사용할 텍스트 인코딩입니다.기본값은 Encoding.UTF8입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Indent">
      <summary>요소의 들여쓰기 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>새 줄에 개별 요소를 들여 쓰면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.IndentChars">
      <summary>들여쓰기에 사용할 문자열을 가져오거나 설정합니다.이 설정은 <see cref="P:System.Xml.XmlWriterSettings.Indent" /> 속성이 true로 설정된 경우에 사용됩니다.</summary>
      <returns>들여쓰기에 사용할 문자열입니다.이 속성에 설정할 수 있는 문자열 값에는 제한이 없습니다.그러나 XML을 올바르게 유지하려면 공백 문자, 탭, 캐리지 리턴 또는 줄 바꿈 같은 유효한 공백 문자만 지정해야 합니다.기본값은 공백 두 개입니다.</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.IndentChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NamespaceHandling">
      <summary>XML 콘텐츠를 쓸 때 <see cref="T:System.Xml.XmlWriter" />에서 중복된 네임스페이스 선언을 제거할지를 표시하는 값을 가져오거나 설정합니다.기본 동작은 작성기에서 작성기의 네임스페이스 확인자에 있는 모든 네임스페이스 선언을 출력하는 것입니다.</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" />에서 중복된 네임스페이스 선언을 제거할지를 지정하는 데 사용되는 <see cref="T:System.Xml.NamespaceHandling" /> 열거형입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineChars">
      <summary>줄 바꿈에 사용할 문자열을 가져오거나 설정합니다.</summary>
      <returns>줄 바꿈에 사용할 문자열입니다.이 속성에 설정할 수 있는 문자열 값에는 제한이 없습니다.그러나 XML을 올바르게 유지하려면 공백 문자, 탭, 캐리지 리턴 또는 줄 바꿈 같은 유효한 공백 문자만 지정해야 합니다.기본값은 \r\n(캐리지 리턴, 줄 바꿈)입니다.</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineHandling">
      <summary>줄 바꿈을 출력에 정규화할지를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.NewLineHandling" /> 값 중 하나입니다.기본값은 <see cref="F:System.Xml.NewLineHandling.Replace" />입니다.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineOnAttributes">
      <summary>특성을 새 줄에 쓸지를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>특성을 개별 줄에 쓰려면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.참고<see cref="P:System.Xml.XmlWriterSettings.Indent" /> 속성 값이 false인 경우에는 이 설정을 적용해도 효과가 없습니다.<see cref="P:System.Xml.XmlWriterSettings.NewLineOnAttributes" />를 true로 설정하면 각 특성 앞에 줄 바꿈과 한 수준 들여쓰기가 추가됩니다.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.OmitXmlDeclaration">
      <summary>XML 선언을 생략할지를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>XML 선언을 생략하려면 true이고, 그렇지 않으면 false입니다.기본값은 false로, XML 선언이 작성됩니다.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Reset">
      <summary>설정 클래스의 멤버를 해당 기본값으로 다시 설정합니다.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.WriteEndDocumentOnClose">
      <summary>
        <see cref="M:System.Xml.XmlWriter.Close" /> 메서드가 호출될 때 <see cref="T:System.Xml.XmlWriter" />가 닫히지 않은 모든 요소 태그에 닫는 태그를 추가할지를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>닫히지 않은 모든 요소 태그가 닫히면 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.</returns>
    </member>
    <member name="T:System.Xml.Schema.XmlSchema">
      <summary>XML 스키마의 메모리 내 표현으로 W3C(World Wide Web Consortium)XML 스키마 파트 1: 구조 및 XML 스키나 파트 2: 데이터 형식 사양을 참조하세요.</summary>
    </member>
    <member name="T:System.Xml.Schema.XmlSchemaForm">
      <summary>특성이나 요소를 네임스페이스 접두사로 한정해야 하는지 여부를 나타냅니다.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.None">
      <summary>스키마에 요소 및 특성 형식을 지정하지 않습니다.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Qualified">
      <summary>요소와 특성을 네임스페이스 접두사로 한정해야 합니다.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Unqualified">
      <summary>요소와 특성을 네임스페이스 접두사로 한정할 필요는 없습니다.</summary>
    </member>
    <member name="T:System.Xml.Serialization.IXmlSerializable">
      <summary>XML serialization 및 deserialization을 위한 사용자 지정 서식을 제공합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.GetSchema">
      <summary>이 메서드는 예약되어 있으므로 사용해서는 안 됩니다.IXmlSerializable 인터페이스를 구현할 때 이 메서드에서 null(Visual Basic에서는 Nothing)을 반환해야 하지만 사용자 지정 스키마를 지정해야 하는 경우에는 <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" />를 클래스에 적용합니다.</summary>
      <returns>
        <see cref="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)" /> 메서드에 의해 생성되고 <see cref="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)" /> 메서드가 사용하는 개체의 XML 표현을 설명하는 <see cref="T:System.Xml.Schema.XmlSchema" />입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)">
      <summary>개체의 XML 표현에서 개체를 생성합니다.</summary>
      <param name="reader">개체가 deserialize되는 <see cref="T:System.Xml.XmlReader" /> 스트림입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)">
      <summary>개체를 XML 표현으로 변환합니다.</summary>
      <param name="writer">개체가 serialize되는 <see cref="T:System.Xml.XmlWriter" /> 스트림입니다. </param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaProviderAttribute">
      <summary>형식에 적용되는 경우 XML 스키마를 반환하는 형식의 정적 메서드 이름과 형식의 serialization을 제어하는 <see cref="T:System.Xml.XmlQualifiedName" />(익명 형식의 경우 <see cref="T:System.Xml.Schema.XmlSchemaType" />)을 저장합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaProviderAttribute.#ctor(System.String)">
      <summary>형식의 XML 스키마를 제공하는 정적 메서드 이름을 가져와서 <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="methodName">구현되어야 하는 정적 메서드의 이름입니다.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.IsAny">
      <summary>대상 클래스가 와일드카드이거나 클래스의 스키마에 xs:any 요소만 포함되어 있는지 여부를 확인하는 값을 가져오거나 설정합니다.</summary>
      <returns>클래스가 와일드카드이거나 스키마에 xs:any 요소만 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.MethodName">
      <summary>형식의 XML 스키마를 제공하는 정적 메서드의 이름과 해당 XML 스키마 데이터 형식의 이름을 가져옵니다.</summary>
      <returns>XML 스키마를 반환하기 위해 XML 인프라에서 호출하는 메서드의 이름입니다.</returns>
    </member>
  </members>
</doc>