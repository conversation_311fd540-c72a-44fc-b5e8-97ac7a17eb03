Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Public Class frmAddToAllAccounts
    Private Sub frmSMTPTester_Details_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        CenterForm(Me, frmEmailSender)
        DxErrorProvider1.ClearErrors()
        BntNew_Click(Nothing, Nothing)
    End Sub
    Private Sub TxtSMTPServer_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtEmail_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPassword_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPort_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtFromName_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtFromMail_EditValueChanged(sender As Object, e As EventArgs) Handles TxtFromMail.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtSubject_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub BntNew_Click(sender As Object, e As EventArgs) Handles BntNew.Click
        DxErrorProvider1.ClearErrors()
        TxtTag_1.SelectedIndex = -1
        TxtFromMail.Text = ""
        TxtFromName.Text = ""
        TxtTag_2.SelectedIndex = -1
        TxtSubject.Text = ""
        Me.ActiveControl = TxtFromName
    End Sub
    Private Sub BntAdd_Click(sender As Object, e As EventArgs) Handles BntAdd.Click
        'If TxtFromName.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtFromName, "Enter from Name tag...!")
        '    TxtFromName.Focus()
        '    Exit Sub
        'End If
        'If TxtFromMail.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtFromMail, "Invalid from Email ...!")
        '    TxtFromMail.Focus()
        '    Exit Sub
        'End If
        'If TxtSubject.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtSubject, "Enter Subject tag...!")
        '    TxtSubject.Focus()
        '    Exit Sub
        'End If
        Dim result As DialogResult = XtraMessageBox.Show("All Smtp account will have the same Name Tag, from Email Name and Subject Tag. Do you want to continue?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            ' التحقق من وجود بيانات SMTP
            If LS_SenderSmtp Is Nothing OrElse LS_SenderSmtp.Count = 0 Then
                DevExpress.XtraEditors.XtraMessageBox.Show("No SMTP accounts found to update. Please load SMTP accounts first.", "No Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If

            ' تحديث البيانات في القائمة
            For i = 0 To LS_SenderSmtp.Count - 1
                If Not String.IsNullOrEmpty(TxtFromName.Text.Trim) Then
                    LS_SenderSmtp(i).smtpfromname = TxtFromName.Text.Trim
                End If
                If Not String.IsNullOrEmpty(TxtFromMail.Text.Trim) Then
                    LS_SenderSmtp(i).smtpfrommail = TxtFromMail.Text.Trim
                End If
                If Not String.IsNullOrEmpty(TxtSubject.Text.Trim) Then
                    LS_SenderSmtp(i).smtpsubject = TxtSubject.Text.Trim
                End If
                LS_SenderSmtp(i).tag1 = TxtTag_1.Text.Trim
                LS_SenderSmtp(i).tag2 = TxtTag_2.Text.Trim
            Next

            ' تحديث GridControl في frmEmailSender
            UpdateEmailSenderGrid()

            ' إجبار تحديث فوري للواجهة
            Application.DoEvents()

            ' تأخير قصير للتأكد من اكتمال التحديث
            System.Threading.Thread.Sleep(100)

            ' إظهار رسالة نجاح مع تفاصيل التحديث
            Dim updatedFields As New List(Of String)
            If Not String.IsNullOrEmpty(TxtFromName.Text.Trim) Then updatedFields.Add("From Name")
            If Not String.IsNullOrEmpty(TxtFromMail.Text.Trim) Then updatedFields.Add("From Email")
            If Not String.IsNullOrEmpty(TxtSubject.Text.Trim) Then updatedFields.Add("Subject")
            If Not String.IsNullOrEmpty(TxtTag_1.Text.Trim) Then updatedFields.Add("Tag 1")
            If Not String.IsNullOrEmpty(TxtTag_2.Text.Trim) Then updatedFields.Add("Tag 2")

            Dim fieldsText As String = String.Join(", ", updatedFields)
            DevExpress.XtraEditors.XtraMessageBox.Show($"Successfully updated {LS_SenderSmtp.Count} SMTP accounts." & vbNewLine &
                                                      $"Updated fields: {fieldsText}" & vbNewLine &
                                                      "Changes are now visible in GridColumn4 (Subject) and GridColumn11 (From Name).",
                                                      "Update Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)

            BntNew_Click(Nothing, Nothing)
            Me.Dispose()

        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show($"An error occurred while updating SMTP accounts: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تحديث GridControl في frmEmailSender بطريقة آمنة
    ''' </summary>
    Private Sub UpdateEmailSenderGrid()
        Try
            ' التحقق من وجود frmEmailSender
            If frmEmailSender Is Nothing OrElse frmEmailSender.IsDisposed Then
                Debug.WriteLine("frmEmailSender is not available for grid update")
                Return
            End If

            ' التحقق من وجود GridControl1
            If frmEmailSender.GridControl1 Is Nothing OrElse frmEmailSender.GridControl1.IsDisposed Then
                Debug.WriteLine("GridControl1 is not available for update")
                Return
            End If

            ' تحديث مصدر البيانات والـ Grid
            If frmEmailSender.GridControl1.InvokeRequired Then
                frmEmailSender.GridControl1.Invoke(New Action(Sub()
                                                                  Try
                                                                      ' إعادة تعيين مصدر البيانات بطريقة محسنة
                                                                      frmEmailSender.GridControl1.BeginUpdate()
                                                                      Try
                                                                          frmEmailSender.GridControl1.DataSource = Nothing
                                                                          Application.DoEvents()
                                                                          frmEmailSender.GridControl1.DataSource = LS_SenderSmtp
                                                                          frmEmailSender.GridControl1.RefreshDataSource()
                                                                      Finally
                                                                          frmEmailSender.GridControl1.EndUpdate()
                                                                      End Try

                                                                      ' تحديث العرض
                                                                      If frmEmailSender.GridView1 IsNot Nothing Then
                                                                          frmEmailSender.GridView1.RefreshData()
                                                                          frmEmailSender.GridView1.LayoutChanged()

                                                                          ' تحديث الأعمدة المحددة بشكل خاص
                                                                          RefreshSpecificColumns(frmEmailSender.GridView1)
                                                                      End If

                                                                      Debug.WriteLine("Grid updated successfully via Invoke")
                                                                  Catch ex As Exception
                                                                      Debug.WriteLine($"Error updating grid via Invoke: {ex.Message}")
                                                                  End Try
                                                              End Sub))
            Else
                ' إعادة تعيين مصدر البيانات بطريقة محسنة
                frmEmailSender.GridControl1.BeginUpdate()
                Try
                    frmEmailSender.GridControl1.DataSource = Nothing
                    Application.DoEvents()
                    frmEmailSender.GridControl1.DataSource = LS_SenderSmtp
                    frmEmailSender.GridControl1.RefreshDataSource()
                Finally
                    frmEmailSender.GridControl1.EndUpdate()
                End Try

                ' تحديث العرض
                If frmEmailSender.GridView1 IsNot Nothing Then
                    frmEmailSender.GridView1.RefreshData()
                    frmEmailSender.GridView1.LayoutChanged()

                    ' تحديث الأعمدة المحددة بشكل خاص
                    RefreshSpecificColumns(frmEmailSender.GridView1)
                End If

                Debug.WriteLine("Grid updated successfully")
            End If

            ' حفظ البيانات المحدثة
            SaveUpdatedData()

            ' تحديث عدد الـ SMTP accounts في frmEmailSender
            UpdateSmtpCount()

            ' تحديث إضافي للتأكد من ظهور البيانات
            ForceGridRefresh()

            ' تحديث مباشر للبيانات
            DirectGridUpdate()

            ' تأخير قصير للتأكد من اكتمال جميع التحديثات
            System.Threading.Thread.Sleep(50)
            Application.DoEvents()

        Catch ex As Exception
            Debug.WriteLine($"Error in UpdateEmailSenderGrid: {ex.Message}")
            Debug.WriteLine($"Stack trace: {ex.StackTrace}")
        End Try
    End Sub

    ''' <summary>
    ''' حفظ البيانات المحدثة في الإعدادات
    ''' </summary>
    Private Sub SaveUpdatedData()
        Try
            ' حفظ البيانات في الإعدادات
            My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
            My.Settings.Save()

            ' تحديث البيانات في frmEmailSender أيضاً إذا كان لديه method للحفظ
            If frmEmailSender IsNot Nothing AndAlso Not frmEmailSender.IsDisposed Then
                Try
                    ' استدعاء method الحفظ في frmEmailSender إذا كان موجوداً
                    frmEmailSender.Save_All_Settings()
                    Debug.WriteLine("Called Save_All_Settings in frmEmailSender")
                Catch ex As Exception
                    Debug.WriteLine($"Error calling Save_All_Settings: {ex.Message}")
                End Try
            End If

            Debug.WriteLine("Updated SMTP data saved to settings")

        Catch ex As Exception
            Debug.WriteLine($"Error saving updated data: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تحديث الأعمدة المحددة (GridColumn4 و GridColumn11) بشكل خاص
    ''' </summary>
    ''' <param name="gridView">GridView المراد تحديثه</param>
    Private Sub RefreshSpecificColumns(gridView As DevExpress.XtraGrid.Views.Grid.GridView)
        Try
            If gridView Is Nothing Then
                Return
            End If

            ' تحديث الأعمدة المحددة بطريقة أكثر فعالية
            Dim targetColumns As String() = {"smtpsubject", "smtpfromname", "smtpfrommail"}

            For Each column As DevExpress.XtraGrid.Columns.GridColumn In gridView.Columns
                If targetColumns.Contains(column.FieldName) Then
                    ' إجبار إعادة حساب العمود
                    Dim currentWidth As Integer = column.Width
                    column.Width = currentWidth + 1
                    column.Width = currentWidth

                    Debug.WriteLine($"Refreshed column: {column.FieldName} ({column.Caption})")
                End If
            Next

            ' إجبار إعادة رسم الـ Grid
            gridView.BeginUpdate()
            Try
                ' تحديث جميع الصفوف المرئية
                For i As Integer = 0 To gridView.RowCount - 1
                    gridView.RefreshRow(i)
                Next

                ' إجبار إعادة حساب العرض
                gridView.LayoutChanged()
                gridView.Invalidate()
            Finally
                gridView.EndUpdate()
            End Try

            Debug.WriteLine("Specific columns refreshed successfully")

        Catch ex As Exception
            Debug.WriteLine($"Error refreshing specific columns: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تحديث عدد الـ SMTP accounts في frmEmailSender
    ''' </summary>
    Private Sub UpdateSmtpCount()
        Try
            If frmEmailSender Is Nothing OrElse frmEmailSender.IsDisposed Then
                Return
            End If

            ' تحديث عدد الـ SMTP accounts
            If frmEmailSender.lbltotalstmp IsNot Nothing Then
                If frmEmailSender.lbltotalstmp.InvokeRequired Then
                    frmEmailSender.lbltotalstmp.Invoke(New Action(Sub()
                                                                      frmEmailSender.lbltotalstmp.Text = $"Total Smtps: {LS_SenderSmtp.Count}"
                                                                  End Sub))
                Else
                    frmEmailSender.lbltotalstmp.Text = $"Total Smtps: {LS_SenderSmtp.Count}"
                End If

                Debug.WriteLine($"Updated SMTP count to: {LS_SenderSmtp.Count}")
            End If

        Catch ex As Exception
            Debug.WriteLine($"Error updating SMTP count: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' إجبار تحديث شامل للـ Grid
    ''' </summary>
    Private Sub ForceGridRefresh()
        Try
            If frmEmailSender Is Nothing OrElse frmEmailSender.IsDisposed Then
                Return
            End If

            If frmEmailSender.GridControl1 IsNot Nothing AndAlso Not frmEmailSender.GridControl1.IsDisposed Then
                If frmEmailSender.GridControl1.InvokeRequired Then
                    frmEmailSender.GridControl1.Invoke(New Action(Sub()
                                                                      Try
                                                                          ' إجبار تحديث كامل
                                                                          frmEmailSender.GridControl1.Refresh()

                                                                          If frmEmailSender.GridView1 IsNot Nothing Then
                                                                              frmEmailSender.GridView1.BestFitColumns()
                                                                              frmEmailSender.GridView1.Invalidate()
                                                                          End If

                                                                          Debug.WriteLine("Force grid refresh completed via Invoke")
                                                                      Catch ex As Exception
                                                                          Debug.WriteLine($"Error in force refresh via Invoke: {ex.Message}")
                                                                      End Try
                                                                  End Sub))
                Else
                    ' إجبار تحديث كامل
                    frmEmailSender.GridControl1.Refresh()

                    If frmEmailSender.GridView1 IsNot Nothing Then
                        frmEmailSender.GridView1.BestFitColumns()
                        frmEmailSender.GridView1.Invalidate()
                    End If

                    Debug.WriteLine("Force grid refresh completed")
                End If
            End If

        Catch ex As Exception
            Debug.WriteLine($"Error in ForceGridRefresh: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تحديث البيانات في الـ Grid بطريقة مباشرة
    ''' </summary>
    Private Sub DirectGridUpdate()
        Try
            If frmEmailSender Is Nothing OrElse frmEmailSender.IsDisposed Then
                Return
            End If

            If frmEmailSender.GridView1 IsNot Nothing Then
                ' تحديث مباشر للصفوف مع Thread-safe approach
                If frmEmailSender.GridView1.InvokeRequired Then
                    frmEmailSender.GridView1.Invoke(New Action(Sub()
                                                                   UpdateGridRowsDirectly(frmEmailSender.GridView1)
                                                               End Sub))
                Else
                    UpdateGridRowsDirectly(frmEmailSender.GridView1)
                End If

                Debug.WriteLine("Direct grid update completed")
            End If

        Catch ex As Exception
            Debug.WriteLine($"Error in DirectGridUpdate: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تحديث صفوف الـ Grid مباشرة
    ''' </summary>
    ''' <param name="gridView">GridView المراد تحديثه</param>
    Private Sub UpdateGridRowsDirectly(gridView As DevExpress.XtraGrid.Views.Grid.GridView)
        Try
            gridView.BeginUpdate()

            ' تحديث مباشر للصفوف
            For i As Integer = 0 To gridView.RowCount - 1
                ' تحديث القيم في الأعمدة المحددة
                If i < LS_SenderSmtp.Count Then
                    gridView.SetRowCellValue(i, "smtpsubject", LS_SenderSmtp(i).smtpsubject)
                    gridView.SetRowCellValue(i, "smtpfromname", LS_SenderSmtp(i).smtpfromname)
                    gridView.SetRowCellValue(i, "smtpfrommail", LS_SenderSmtp(i).smtpfrommail)
                    gridView.SetRowCellValue(i, "tag1", LS_SenderSmtp(i).tag1)
                    gridView.SetRowCellValue(i, "tag2", LS_SenderSmtp(i).tag2)

                    ' تحديث الصف
                    gridView.RefreshRow(i)
                End If
            Next

            ' إجبار تحديث العرض
            gridView.PostEditor()
            gridView.UpdateCurrentRow()

        Catch ex As Exception
            Debug.WriteLine($"Error in UpdateGridRowsDirectly: {ex.Message}")
        Finally
            gridView.EndUpdate()
        End Try
    End Sub
    Private Sub TxtTag_1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles TxtTag_1.SelectedIndexChanged
        DxErrorProvider1.ClearErrors()
        If TxtTag_1.SelectedIndex >= 0 Then
            If TxtTag_1.SelectedItem.ToString() = "Receiver Email" Then
                TxtFromName.Text = "[-Email-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Encoded Email 64" Then
                TxtFromName.Text = "[-Email64-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Start Name With Upper Case" Then
                TxtFromName.Text = "[-UCase-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Add Link" Then
                TxtFromName.Text = "[-Link-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Send With Logo" Then
                TxtFromName.Text = "[-Logo-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 3" Then
                TxtFromName.Text = "[-RCh3-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 4" Then
                TxtFromName.Text = "[-RCh4-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 5" Then
                TxtFromName.Text = "[-RCh5-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 6" Then
                TxtFromName.Text = "[-RCh6-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 7" Then
                TxtFromName.Text = "[-RCh7-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 3" Then
                TxtFromName.Text = "[-RN3-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 4" Then
                TxtFromName.Text = "[-RN4-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 5" Then
                TxtFromName.Text = "[-RN5-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 6" Then
                TxtFromName.Text = "[-RN6-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 7" Then
                TxtFromName.Text = "[-RN7-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random IP" Then
                TxtFromName.Text = "[-IP-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random IP China" Then
                TxtFromName.Text = "[-IPChina-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show The Domain" Then
                TxtFromName.Text = "[-Domain-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Name" Then
                TxtFromName.Text = "[-Name-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Company Name" Then
                TxtFromName.Text = "[-CompanyName-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Date" Then
                TxtFromName.Text = "[-Date-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Time" Then
                TxtFromName.Text = "[-Time-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Date Tomorrow" Then
                TxtFromName.Text = "[-DateTomorrow-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Country" Then
                TxtFromName.Text = "[-RCountry-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Browser" Then
                TxtFromName.Text = "[-RandomBrowser-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                TxtFromName.Text = "[-FakePhone-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                TxtFromName.Text = "[-FakeEmail-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Street New York" Then
                TxtFromName.Text = "[-NewYork-]"
            End If
        End If
    End Sub
    Private Sub TxtTag_2_SelectedIndexChanged(sender As Object, e As EventArgs) Handles TxtTag_2.SelectedIndexChanged
        DxErrorProvider1.ClearErrors()
        If TxtTag_2.SelectedIndex >= 0 Then
            If TxtTag_2.SelectedItem.ToString() = "Receiver Email" Then
                TxtSubject.Text = "[-Email-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Encoded Email 64" Then
                TxtSubject.Text = "[-Email64-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Start Name With Upper Case" Then
                TxtSubject.Text = "[-UCase-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Add Link" Then
                TxtSubject.Text = "[-Link-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Send With Logo" Then
                TxtSubject.Text = "[-Logo-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 3" Then
                TxtSubject.Text = "[-RCh3-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 4" Then
                TxtSubject.Text = "[-RCh4-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 5" Then
                TxtSubject.Text = "[-RCh5-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 6" Then
                TxtSubject.Text = "[-RCh6-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 7" Then
                TxtSubject.Text = "[-RCh7-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 3" Then
                TxtSubject.Text = "[-RN3-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 4" Then
                TxtSubject.Text = "[-RN4-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 5" Then
                TxtSubject.Text = "[-RN5-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 6" Then
                TxtSubject.Text = "[-RN6-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 7" Then
                TxtSubject.Text = "[-RN7-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random IP" Then
                TxtSubject.Text = "[-IP-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random IP China" Then
                TxtSubject.Text = "[-IPChina-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show The Domain" Then
                TxtSubject.Text = "[-Domain-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Name" Then
                TxtSubject.Text = "[-Name-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Company Name" Then
                TxtSubject.Text = "[-CompanyName-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Date" Then
                TxtSubject.Text = "[-Date-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Time" Then
                TxtSubject.Text = "[-Time-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Date Tomorrow" Then
                TxtSubject.Text = "[-DateTomorrow-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Country" Then
                TxtSubject.Text = "[-RCountry-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Browser" Then
                TxtSubject.Text = "[-RandomBrowser-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                TxtSubject.Text = "[-FakePhone-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                TxtSubject.Text = "[-FakeEmail-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Street New York" Then
                TxtSubject.Text = "[-NewYork-]"
            End If
        End If
    End Sub
End Class