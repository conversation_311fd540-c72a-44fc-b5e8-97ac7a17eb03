﻿Imports DevExpress.XtraEditors
Imports System.Runtime.InteropServices
Imports DevExpress.XtraGrid
Public Class frmAddToAllAccounts

    ' Windows API declarations for simulating mouse clicks
    <DllImport("user32.dll", CharSet:=CharSet.Auto, CallingConvention:=CallingConvention.StdCall)>
    Private Shared Function SetCursorPos(x As Integer, y As Integer) As Boolean
    End Function

    <DllImport("user32.dll", CharSet:=CharSet.Auto, CallingConvention:=CallingConvention.StdCall)>
    Private Shared Sub mouse_event(dwFlags As Integer, dx As Integer, dy As Integer, cButtons As Integer, dwExtraInfo As Integer)
    End Sub

    ' Mouse event constants
    Private Const MOUSEEVENTF_LEFTDOWN As Integer = &H2
    Private Const MOUSEEVENTF_LEFTUP As Integer = &H4
    Private Sub frmSMTPTester_Details_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        CenterForm(Me, frmEmailSender)
        DxErrorProvider1.ClearErrors()
        BntNew_Click(Nothing, Nothing)
    End Sub
    Private Sub TxtSMTPServer_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtEmail_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPassword_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPort_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtFromName_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtFromMail_EditValueChanged(sender As Object, e As EventArgs) Handles TxtFromMail.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtSubject_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub BntNew_Click(sender As Object, e As EventArgs) Handles BntNew.Click
        DxErrorProvider1.ClearErrors()
        TxtTag_1.SelectedIndex = -1
        TxtFromMail.Text = ""
        TxtFromName.Text = ""
        TxtTag_2.SelectedIndex = -1
        TxtSubject.Text = ""
        Me.ActiveControl = TxtFromName
    End Sub
    Private Sub BntAdd_Click(sender As Object, e As EventArgs) Handles BntAdd.Click
        'If TxtFromName.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtFromName, "Enter from Name tag...!")
        '    TxtFromName.Focus()
        '    Exit Sub
        'End If
        'If TxtFromMail.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtFromMail, "Invalid from Email ...!")
        '    TxtFromMail.Focus()
        '    Exit Sub
        'End If
        'If TxtSubject.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtSubject, "Enter Subject tag...!")
        '    TxtSubject.Focus()
        '    Exit Sub
        'End If
        Dim result As DialogResult = XtraMessageBox.Show("All Smtp account will have the same Name Tag, from Email Name and Subject Tag. Do you want to continue?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            ' التحقق من وجود بيانات SMTP
            If LS_SenderSmtp Is Nothing OrElse LS_SenderSmtp.Count = 0 Then
                DevExpress.XtraEditors.XtraMessageBox.Show("No SMTP accounts found to update. Please load SMTP accounts first.", "No Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If

            ' تحديث البيانات في القائمة
            For i = 0 To LS_SenderSmtp.Count - 1
                If Not String.IsNullOrEmpty(TxtFromName.Text.Trim) Then
                    LS_SenderSmtp(i).smtpfromname = TxtFromName.Text.Trim
                End If
                If Not String.IsNullOrEmpty(TxtFromMail.Text.Trim) Then
                    LS_SenderSmtp(i).smtpfrommail = TxtFromMail.Text.Trim
                End If
                If Not String.IsNullOrEmpty(TxtSubject.Text.Trim) Then
                    LS_SenderSmtp(i).smtpsubject = TxtSubject.Text.Trim
                End If
                LS_SenderSmtp(i).tag1 = TxtTag_1.Text.Trim
                LS_SenderSmtp(i).tag2 = TxtTag_2.Text.Trim
            Next

            ' تحديث GridControl في frmEmailSender بطريقة محترفة
            RefreshEmailSenderGrid()

            ' إظهار رسالة نجاح مع تفاصيل التحديث
            Dim updatedFields As New List(Of String)
            If Not String.IsNullOrEmpty(TxtFromName.Text.Trim) Then updatedFields.Add("From Name")
            If Not String.IsNullOrEmpty(TxtFromMail.Text.Trim) Then updatedFields.Add("From Email")
            If Not String.IsNullOrEmpty(TxtSubject.Text.Trim) Then updatedFields.Add("Subject")
            If Not String.IsNullOrEmpty(TxtTag_1.Text.Trim) Then updatedFields.Add("Tag 1")
            If Not String.IsNullOrEmpty(TxtTag_2.Text.Trim) Then updatedFields.Add("Tag 2")

            Dim fieldsText As String = String.Join(", ", updatedFields)
            DevExpress.XtraEditors.XtraMessageBox.Show($"Successfully updated {LS_SenderSmtp.Count} SMTP accounts." & vbNewLine &
                                                      $"Updated fields: {fieldsText}" & vbNewLine &
                                                      "Changes are now visible in the Email Sender grid.",
                                                      "Update Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)

            BntNew_Click(Nothing, Nothing)
            Me.Dispose()

        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show($"An error occurred while updating SMTP accounts: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تحديث GridControl في frmEmailSender بطريقة محترفة وبسيطة
    ''' </summary>
    Private Sub RefreshEmailSenderGrid()
        Try
            ' التحقق من وجود frmEmailSender
            If frmEmailSender Is Nothing OrElse frmEmailSender.IsDisposed Then
                Return
            End If

            ' التحقق من وجود GridControl1
            If frmEmailSender.GridControl1 Is Nothing OrElse frmEmailSender.GridControl1.IsDisposed Then
                Return
            End If

            ' الطريقة المحترفة: إعادة تعيين DataSource بالكامل لإجبار تحديث الأعمدة
            If frmEmailSender.GridControl1.InvokeRequired Then
                frmEmailSender.GridControl1.Invoke(New Action(Sub()
                                                                  ' الطريقة المحترفة: إعادة تعيين كاملة
                                                                  frmEmailSender.GridControl1.DataSource = Nothing
                                                                  Application.DoEvents()
                                                                  frmEmailSender.GridControl1.DataSource = LS_SenderSmtp
                                                                  frmEmailSender.GridControl1.RefreshDataSource()

                                                                  ' إجبار تحديث الأعمدة المحددة
                                                                  RefreshSpecificColumns(frmEmailSender.GridView1)

                                                                  ' تحديث عداد الـ SMTP
                                                                  If frmEmailSender.lbltotalstmp IsNot Nothing Then
                                                                      frmEmailSender.lbltotalstmp.Text = $"Total Smtps: {LS_SenderSmtp.Count}"
                                                                  End If
                                                              End Sub))
            Else
                ' الطريقة المحترفة: إعادة تعيين كاملة
                frmEmailSender.GridControl1.DataSource = Nothing
                Application.DoEvents()
                frmEmailSender.GridControl1.DataSource = LS_SenderSmtp
                frmEmailSender.GridControl1.RefreshDataSource()

                ' إجبار تحديث الأعمدة المحددة
                RefreshSpecificColumns(frmEmailSender.GridView1)

                ' تحديث عداد الـ SMTP
                If frmEmailSender.lbltotalstmp IsNot Nothing Then
                    frmEmailSender.lbltotalstmp.Text = $"Total Smtps: {LS_SenderSmtp.Count}"
                End If
            End If

            ' حفظ البيانات
            My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
            My.Settings.Save()

            ' تأخير قصير للتأكد من اكتمال التحديث
            System.Threading.Thread.Sleep(100)
            Application.DoEvents()

            ' طريقة إضافية: محاكاة النقر الفعلي على header الأعمدة
            SimulateColumnHeaderClick(frmEmailSender.GridView1)

            ' الحل الجذري: محاكاة النقر الفعلي باستخدام Windows API
            SimulateRealMouseClick(frmEmailSender.GridView1)

            ' طريقة بديلة: محاكاة النقر بطريقة أبسط
            SimulateSimpleHeaderClick(frmEmailSender.GridView1)

            ' طريقة نهائية: تأخير إضافي مع تحديث مؤجل
            Dim timer As New System.Windows.Forms.Timer()
            timer.Interval = 500
            AddHandler timer.Tick, Sub(sender, e)
                                       timer.Stop()
                                       timer.Dispose()
                                       Try
                                           If frmEmailSender IsNot Nothing AndAlso Not frmEmailSender.IsDisposed Then
                                               If frmEmailSender.GridView1 IsNot Nothing Then
                                                   frmEmailSender.GridView1.RefreshData()
                                                   frmEmailSender.GridView1.LayoutChanged()
                                                   ' محاولة أخيرة لإجبار التحديث
                                                   ForceColumnUpdate(frmEmailSender.GridView1)
                                                   ' الحل الأخير: إعادة إنشاء الأعمدة
                                                   RecreateColumns(frmEmailSender.GridView1)
                                               End If
                                           End If
                                       Catch
                                       End Try
                                   End Sub
            timer.Start()

        Catch ex As Exception
            Debug.WriteLine($"Error in RefreshEmailSenderGrid: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تحديث الأعمدة المحددة بطريقة محترفة - محاكاة النقر على header
    ''' </summary>
    ''' <param name="gridView">GridView المراد تحديثه</param>
    Private Sub RefreshSpecificColumns(gridView As DevExpress.XtraGrid.Views.Grid.GridView)
        Try
            If gridView Is Nothing Then Return

            ' الطريقة المحترفة: محاكاة النقر الفعلي على header الأعمدة
            Dim targetColumns As String() = {"smtpsubject", "smtpfromname", "smtpfrommail"}

            gridView.BeginUpdate()
            Try
                For Each column As DevExpress.XtraGrid.Columns.GridColumn In gridView.Columns
                    If targetColumns.Contains(column.FieldName) Then
                        ' طريقة 1: محاكاة النقر على header
                        Dim originalSortOrder = column.SortOrder

                        ' تطبيق sort مؤقت لإجبار refresh (نفس تأثير النقر على header)
                        column.SortOrder = DevExpress.Data.ColumnSortOrder.Ascending
                        gridView.RefreshData()
                        Application.DoEvents()

                        ' إعادة SortOrder الأصلي
                        column.SortOrder = originalSortOrder
                        gridView.RefreshData()
                        Application.DoEvents()

                        ' طريقة 2: إجبار إعادة حساب العمود
                        Dim tempVisible As Boolean = column.Visible
                        column.Visible = False
                        Application.DoEvents()
                        column.Visible = tempVisible

                        Debug.WriteLine($"Simulated header click for column: {column.FieldName}")
                    End If
                Next

                ' تحديث شامل نهائي
                gridView.RefreshData()
                gridView.LayoutChanged()
                gridView.Invalidate()

            Finally
                gridView.EndUpdate()
            End Try

        Catch ex As Exception
            Debug.WriteLine($"Error in RefreshSpecificColumns: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' محاكاة النقر الفعلي على header الأعمدة لإجبار التحديث
    ''' </summary>
    ''' <param name="gridView">GridView المراد تحديثه</param>
    Private Sub SimulateColumnHeaderClick(gridView As DevExpress.XtraGrid.Views.Grid.GridView)
        Try
            If gridView Is Nothing Then Return

            ' الطريقة المحترفة: استخدام DevExpress API لمحاكاة النقر
            Dim targetColumns As String() = {"smtpsubject", "smtpfromname", "smtpfrommail"}

            For Each column As DevExpress.XtraGrid.Columns.GridColumn In gridView.Columns
                If targetColumns.Contains(column.FieldName) Then
                    Try
                        ' طريقة 1: استخدام PerformCallback لإجبار تحديث العمود
                        If TypeOf gridView.GridControl Is DevExpress.XtraGrid.GridControl Then
                            Dim gridControl As DevExpress.XtraGrid.GridControl = CType(gridView.GridControl, DevExpress.XtraGrid.GridControl)

                            ' محاكاة النقر على header باستخدام DevExpress events
                            Dim hitInfo As DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo = gridView.CalcHitInfo(New Point(column.AbsoluteIndex * 100, 0))

                            ' تطبيق sort مؤقت (نفس تأثير النقر على header)
                            Dim originalSortOrder = column.SortOrder
                            gridView.Columns.ColumnByFieldName(column.FieldName).SortOrder = DevExpress.Data.ColumnSortOrder.Ascending
                            gridView.RefreshData()
                            System.Threading.Thread.Sleep(50)
                            gridView.Columns.ColumnByFieldName(column.FieldName).SortOrder = originalSortOrder
                            gridView.RefreshData()

                            Debug.WriteLine($"Simulated header click for: {column.FieldName}")
                        End If

                    Catch ex As Exception
                        Debug.WriteLine($"Error simulating click for {column.FieldName}: {ex.Message}")
                    End Try
                End If
            Next

            ' تحديث نهائي شامل
            gridView.RefreshData()
            gridView.LayoutChanged()
            Application.DoEvents()

        Catch ex As Exception
            Debug.WriteLine($"Error in SimulateColumnHeaderClick: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' محاكاة النقر الفعلي باستخدام Windows API
    ''' </summary>
    ''' <param name="gridView">GridView المراد تحديثه</param>
    Private Sub SimulateRealMouseClick(gridView As DevExpress.XtraGrid.Views.Grid.GridView)
        Try
            If gridView Is Nothing OrElse frmEmailSender Is Nothing OrElse frmEmailSender.IsDisposed Then Return

            ' الحصول على الأعمدة المستهدفة
            Dim targetColumns As String() = {"smtpsubject", "smtpfromname", "smtpfrommail"}

            For Each column As DevExpress.XtraGrid.Columns.GridColumn In gridView.Columns
                If targetColumns.Contains(column.FieldName) AndAlso column.Visible Then
                    Try
                        ' الحصول على ViewInfo للوصول إلى معلومات العمود
                        Dim viewInfo As DevExpress.XtraGrid.Views.Grid.ViewInfo.GridViewInfo = gridView.GetViewInfo()
                        If viewInfo IsNot Nothing Then
                            ' البحث عن header العمود
                            Dim columnInfo = viewInfo.ColumnsInfo.FirstOrDefault(Function(c) c.Column Is column)
                            If columnInfo IsNot Nothing AndAlso columnInfo.Bounds.Width > 0 Then
                                ' حساب موقع header العمود
                                Dim headerBounds As Rectangle = columnInfo.Bounds
                                Dim headerPoint As New Point(headerBounds.X + headerBounds.Width \ 2, headerBounds.Y - 10) ' -10 للوصول إلى header

                                ' تحويل إلى screen coordinates
                                Dim screenPoint As Point = frmEmailSender.GridControl1.PointToScreen(headerPoint)

                                ' حفظ موقع الماوس الحالي
                                Dim originalCursorPos As Point = Cursor.Position

                                ' تحريك الماوس إلى header العمود
                                SetCursorPos(screenPoint.X, screenPoint.Y)
                                System.Threading.Thread.Sleep(100)

                                ' محاكاة النقر
                                mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                                System.Threading.Thread.Sleep(50)
                                mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                                System.Threading.Thread.Sleep(100)

                                ' إعادة الماوس إلى موقعه الأصلي
                                SetCursorPos(originalCursorPos.X, originalCursorPos.Y)

                                Debug.WriteLine($"Real mouse click simulated for column: {column.FieldName} at {screenPoint}")
                            End If
                        End If

                    Catch ex As Exception
                        Debug.WriteLine($"Error simulating real click for {column.FieldName}: {ex.Message}")
                    End Try
                End If
            Next

        Catch ex As Exception
            Debug.WriteLine($"Error in SimulateRealMouseClick: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' إجبار تحديث الأعمدة بطريقة قوية
    ''' </summary>
    ''' <param name="gridView">GridView المراد تحديثه</param>
    Private Sub ForceColumnUpdate(gridView As DevExpress.XtraGrid.Views.Grid.GridView)
        Try
            If gridView Is Nothing Then Return

            ' الطريقة القوية: إعادة تعيين كامل للأعمدة
            Dim targetColumns As String() = {"smtpsubject", "smtpfromname", "smtpfrommail"}

            gridView.BeginUpdate()
            Try
                For Each column As DevExpress.XtraGrid.Columns.GridColumn In gridView.Columns
                    If targetColumns.Contains(column.FieldName) Then
                        ' إعادة تعيين خصائص العمود
                        Dim tempCaption As String = column.Caption
                        Dim tempFieldName As String = column.FieldName
                        Dim tempVisible As Boolean = column.Visible
                        Dim tempWidth As Integer = column.Width

                        ' إعادة تعيين العمود
                        column.Caption = ""
                        column.Visible = False
                        Application.DoEvents()

                        column.Caption = tempCaption
                        column.FieldName = tempFieldName
                        column.Visible = tempVisible
                        column.Width = tempWidth
                        Application.DoEvents()

                        Debug.WriteLine($"Force updated column: {column.FieldName}")
                    End If
                Next

                ' تحديث شامل نهائي
                gridView.RefreshData()
                gridView.LayoutChanged()
                gridView.Invalidate()

            Finally
                gridView.EndUpdate()
            End Try

        Catch ex As Exception
            Debug.WriteLine($"Error in ForceColumnUpdate: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' الحل الأخير: إعادة إنشاء الأعمدة المستهدفة بالكامل
    ''' </summary>
    ''' <param name="gridView">GridView المراد تحديثه</param>
    Private Sub RecreateColumns(gridView As DevExpress.XtraGrid.Views.Grid.GridView)
        Try
            If gridView Is Nothing Then Return

            ' الأعمدة المستهدفة
            Dim targetColumns As String() = {"smtpsubject", "smtpfromname", "smtpfrommail"}

            gridView.BeginUpdate()
            Try
                For Each fieldName As String In targetColumns
                    Dim existingColumn = gridView.Columns.ColumnByFieldName(fieldName)
                    If existingColumn IsNot Nothing Then
                        ' حفظ خصائص العمود
                        Dim caption As String = existingColumn.Caption
                        Dim visible As Boolean = existingColumn.Visible
                        Dim width As Integer = existingColumn.Width
                        Dim visibleIndex As Integer = existingColumn.VisibleIndex

                        ' حذف العمود الموجود
                        gridView.Columns.Remove(existingColumn)
                        Application.DoEvents()

                        ' إنشاء عمود جديد
                        Dim newColumn As New DevExpress.XtraGrid.Columns.GridColumn()
                        newColumn.FieldName = fieldName
                        newColumn.Caption = caption
                        newColumn.Visible = visible
                        newColumn.Width = width
                        newColumn.VisibleIndex = visibleIndex

                        ' إضافة العمود الجديد
                        gridView.Columns.Add(newColumn)
                        Application.DoEvents()

                        Debug.WriteLine($"Recreated column: {fieldName}")
                    End If
                Next

                ' تحديث شامل نهائي
                gridView.RefreshData()
                gridView.LayoutChanged()
                gridView.Invalidate()
                gridView.BestFitColumns()

            Finally
                gridView.EndUpdate()
            End Try

        Catch ex As Exception
            Debug.WriteLine($"Error in RecreateColumns: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' محاكاة النقر البسيط على header الأعمدة
    ''' </summary>
    ''' <param name="gridView">GridView المراد تحديثه</param>
    Private Sub SimulateSimpleHeaderClick(gridView As DevExpress.XtraGrid.Views.Grid.GridView)
        Try
            If gridView Is Nothing OrElse frmEmailSender Is Nothing OrElse frmEmailSender.IsDisposed Then Return

            ' الطريقة البسيطة: حساب موقع تقريبي لـ header الأعمدة
            Dim targetColumns As String() = {"smtpsubject", "smtpfromname", "smtpfrommail"}
            Dim gridControl As DevExpress.XtraGrid.GridControl = frmEmailSender.GridControl1

            For Each column As DevExpress.XtraGrid.Columns.GridColumn In gridView.Columns
                If targetColumns.Contains(column.FieldName) AndAlso column.Visible Then
                    Try
                        ' حساب موقع تقريبي للعمود
                        Dim columnIndex As Integer = column.VisibleIndex
                        Dim approximateX As Integer = columnIndex * 120 + 60 ' تقدير عرض العمود
                        Dim headerY As Integer = 25 ' تقدير ارتفاع header

                        ' تحويل إلى screen coordinates
                        Dim localPoint As New Point(approximateX, headerY)
                        Dim screenPoint As Point = gridControl.PointToScreen(localPoint)

                        ' حفظ موقع الماوس الحالي
                        Dim originalCursorPos As Point = Cursor.Position

                        ' تحريك الماوس إلى الموقع المقدر
                        SetCursorPos(screenPoint.X, screenPoint.Y)
                        System.Threading.Thread.Sleep(100)

                        ' محاكاة النقر
                        mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                        System.Threading.Thread.Sleep(50)
                        mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                        System.Threading.Thread.Sleep(100)

                        ' إعادة الماوس إلى موقعه الأصلي
                        SetCursorPos(originalCursorPos.X, originalCursorPos.Y)

                        Debug.WriteLine($"Simple header click simulated for column: {column.FieldName} at approximate position {screenPoint}")

                    Catch ex As Exception
                        Debug.WriteLine($"Error in simple header click for {column.FieldName}: {ex.Message}")
                    End Try
                End If
            Next

        Catch ex As Exception
            Debug.WriteLine($"Error in SimulateSimpleHeaderClick: {ex.Message}")
        End Try
    End Sub




    Private Sub TxtTag_1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles TxtTag_1.SelectedIndexChanged
        DxErrorProvider1.ClearErrors()
        If TxtTag_1.SelectedIndex >= 0 Then
            If TxtTag_1.SelectedItem.ToString() = "Receiver Email" Then
                TxtFromName.Text = "[-Email-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Encoded Email 64" Then
                TxtFromName.Text = "[-Email64-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Start Name With Upper Case" Then
                TxtFromName.Text = "[-UCase-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Add Link" Then
                TxtFromName.Text = "[-Link-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Send With Logo" Then
                TxtFromName.Text = "[-Logo-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 3" Then
                TxtFromName.Text = "[-RCh3-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 4" Then
                TxtFromName.Text = "[-RCh4-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 5" Then
                TxtFromName.Text = "[-RCh5-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 6" Then
                TxtFromName.Text = "[-RCh6-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 7" Then
                TxtFromName.Text = "[-RCh7-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 3" Then
                TxtFromName.Text = "[-RN3-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 4" Then
                TxtFromName.Text = "[-RN4-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 5" Then
                TxtFromName.Text = "[-RN5-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 6" Then
                TxtFromName.Text = "[-RN6-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 7" Then
                TxtFromName.Text = "[-RN7-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random IP" Then
                TxtFromName.Text = "[-IP-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random IP China" Then
                TxtFromName.Text = "[-IPChina-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show The Domain" Then
                TxtFromName.Text = "[-Domain-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Name" Then
                TxtFromName.Text = "[-Name-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Company Name" Then
                TxtFromName.Text = "[-CompanyName-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Date" Then
                TxtFromName.Text = "[-Date-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Time" Then
                TxtFromName.Text = "[-Time-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Date Tomorrow" Then
                TxtFromName.Text = "[-DateTomorrow-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Country" Then
                TxtFromName.Text = "[-RCountry-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Browser" Then
                TxtFromName.Text = "[-RandomBrowser-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                TxtFromName.Text = "[-FakePhone-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                TxtFromName.Text = "[-FakeEmail-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Street New York" Then
                TxtFromName.Text = "[-NewYork-]"
            End If
        End If
    End Sub
    Private Sub TxtTag_2_SelectedIndexChanged(sender As Object, e As EventArgs) Handles TxtTag_2.SelectedIndexChanged
        DxErrorProvider1.ClearErrors()
        If TxtTag_2.SelectedIndex >= 0 Then
            If TxtTag_2.SelectedItem.ToString() = "Receiver Email" Then
                TxtSubject.Text = "[-Email-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Encoded Email 64" Then
                TxtSubject.Text = "[-Email64-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Start Name With Upper Case" Then
                TxtSubject.Text = "[-UCase-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Add Link" Then
                TxtSubject.Text = "[-Link-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Send With Logo" Then
                TxtSubject.Text = "[-Logo-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 3" Then
                TxtSubject.Text = "[-RCh3-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 4" Then
                TxtSubject.Text = "[-RCh4-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 5" Then
                TxtSubject.Text = "[-RCh5-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 6" Then
                TxtSubject.Text = "[-RCh6-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 7" Then
                TxtSubject.Text = "[-RCh7-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 3" Then
                TxtSubject.Text = "[-RN3-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 4" Then
                TxtSubject.Text = "[-RN4-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 5" Then
                TxtSubject.Text = "[-RN5-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 6" Then
                TxtSubject.Text = "[-RN6-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 7" Then
                TxtSubject.Text = "[-RN7-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random IP" Then
                TxtSubject.Text = "[-IP-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random IP China" Then
                TxtSubject.Text = "[-IPChina-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show The Domain" Then
                TxtSubject.Text = "[-Domain-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Name" Then
                TxtSubject.Text = "[-Name-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Company Name" Then
                TxtSubject.Text = "[-CompanyName-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Date" Then
                TxtSubject.Text = "[-Date-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Time" Then
                TxtSubject.Text = "[-Time-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Date Tomorrow" Then
                TxtSubject.Text = "[-DateTomorrow-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Country" Then
                TxtSubject.Text = "[-RCountry-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Browser" Then
                TxtSubject.Text = "[-RandomBrowser-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                TxtSubject.Text = "[-FakePhone-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                TxtSubject.Text = "[-FakeEmail-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Street New York" Then
                TxtSubject.Text = "[-NewYork-]"
            End If
        End If
    End Sub
End Class