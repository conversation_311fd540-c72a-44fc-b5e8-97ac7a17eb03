﻿Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid
Public Class frmAddToAllAccounts
    Private Sub frmSMTPTester_Details_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        CenterForm(Me, frmEmailSender)
        DxErrorProvider1.ClearErrors()
        BntNew_Click(Nothing, Nothing)
    End Sub
    Private Sub TxtSMTPServer_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtEmail_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPassword_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtPort_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtFromName_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtFromMail_EditValueChanged(sender As Object, e As EventArgs) Handles TxtFromMail.EditValueChanged
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub TxtSubject_EditValueChanged(sender As Object, e As EventArgs)
        DxErrorProvider1.ClearErrors()
    End Sub
    Private Sub BntNew_Click(sender As Object, e As EventArgs) Handles BntNew.Click
        DxErrorProvider1.ClearErrors()
        TxtTag_1.SelectedIndex = -1
        TxtFromMail.Text = ""
        TxtFromName.Text = ""
        TxtTag_2.SelectedIndex = -1
        TxtSubject.Text = ""
        Me.ActiveControl = TxtFromName
    End Sub
    Private Sub BntAdd_Click(sender As Object, e As EventArgs) Handles BntAdd.Click
        'If TxtFromName.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtFromName, "Enter from Name tag...!")
        '    TxtFromName.Focus()
        '    Exit Sub
        'End If
        'If TxtFromMail.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtFromMail, "Invalid from Email ...!")
        '    TxtFromMail.Focus()
        '    Exit Sub
        'End If
        'If TxtSubject.Text.Trim = "" Then
        '    DxErrorProvider1.SetError(TxtSubject, "Enter Subject tag...!")
        '    TxtSubject.Focus()
        '    Exit Sub
        'End If
        Dim result As DialogResult = XtraMessageBox.Show("All Smtp account will have the same Name Tag, from Email Name and Subject Tag. Do you want to continue?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2)
        If result = DialogResult.No Then Exit Sub
        Try
            ' التحقق من وجود بيانات SMTP
            If LS_SenderSmtp Is Nothing OrElse LS_SenderSmtp.Count = 0 Then
                DevExpress.XtraEditors.XtraMessageBox.Show("No SMTP accounts found to update. Please load SMTP accounts first.", "No Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If

            ' تحديث البيانات في القائمة
            For i = 0 To LS_SenderSmtp.Count - 1
                If Not String.IsNullOrEmpty(TxtFromName.Text.Trim) Then
                    LS_SenderSmtp(i).smtpfromname = TxtFromName.Text.Trim
                End If
                If Not String.IsNullOrEmpty(TxtFromMail.Text.Trim) Then
                    LS_SenderSmtp(i).smtpfrommail = TxtFromMail.Text.Trim
                End If
                If Not String.IsNullOrEmpty(TxtSubject.Text.Trim) Then
                    LS_SenderSmtp(i).smtpsubject = TxtSubject.Text.Trim
                End If
                LS_SenderSmtp(i).tag1 = TxtTag_1.Text.Trim
                LS_SenderSmtp(i).tag2 = TxtTag_2.Text.Trim
            Next

            ' تحديث GridControl في frmEmailSender بطريقة محترفة
            RefreshEmailSenderGrid()

            ' إظهار رسالة نجاح مع تفاصيل التحديث
            Dim updatedFields As New List(Of String)
            If Not String.IsNullOrEmpty(TxtFromName.Text.Trim) Then updatedFields.Add("From Name")
            If Not String.IsNullOrEmpty(TxtFromMail.Text.Trim) Then updatedFields.Add("From Email")
            If Not String.IsNullOrEmpty(TxtSubject.Text.Trim) Then updatedFields.Add("Subject")
            If Not String.IsNullOrEmpty(TxtTag_1.Text.Trim) Then updatedFields.Add("Tag 1")
            If Not String.IsNullOrEmpty(TxtTag_2.Text.Trim) Then updatedFields.Add("Tag 2")

            Dim fieldsText As String = String.Join(", ", updatedFields)
            DevExpress.XtraEditors.XtraMessageBox.Show($"Successfully updated {LS_SenderSmtp.Count} SMTP accounts." & vbNewLine &
                                                      $"Updated fields: {fieldsText}" & vbNewLine &
                                                      "Changes are now visible in the Email Sender grid.",
                                                      "Update Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)

            BntNew_Click(Nothing, Nothing)
            Me.Dispose()

        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show($"An error occurred while updating SMTP accounts: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تحديث GridControl في frmEmailSender - الحل الجذري الأبسط
    ''' </summary>
    Private Sub RefreshEmailSenderGrid()
        Try
            ' التحقق من وجود frmEmailSender
            If frmEmailSender Is Nothing OrElse frmEmailSender.IsDisposed Then
                Return
            End If

            ' الحل الجذري: إعادة تحميل البيانات بالكامل مثل فتح النموذج من جديد
            ReloadGridDataCompletely()

        Catch ex As Exception
            Debug.WriteLine($"Error in RefreshEmailSenderGrid: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' إعادة تحميل البيانات بالكامل - نفس طريقة فتح frmEmailSender
    ''' </summary>
    Private Sub ReloadGridDataCompletely()
        Try
            If frmEmailSender Is Nothing OrElse frmEmailSender.IsDisposed Then Return

            ' حفظ البيانات أولاً
            My.Settings.SMTP_DATA_TABLE_XML = FuncSendMail.SerializeListToXml(LS_SenderSmtp)
            My.Settings.Save()

            ' الطريقة الجذرية: إعادة تحميل البيانات مثل ما يحدث في Form_Load
            If frmEmailSender.GridControl1.InvokeRequired Then
                frmEmailSender.GridControl1.Invoke(New Action(Sub()
                                                                  PerformCompleteReload()
                                                              End Sub))
            Else
                PerformCompleteReload()
            End If

        Catch ex As Exception
            Debug.WriteLine($"Error in ReloadGridDataCompletely: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تنفيذ إعادة التحميل الكامل
    ''' </summary>
    Private Sub PerformCompleteReload()
        Try
            ' خطوة 1: مسح البيانات الحالية
            frmEmailSender.GridControl1.DataSource = Nothing
            frmEmailSender.GridView1.RefreshData()
            Application.DoEvents()

            ' خطوة 2: إعادة تحميل البيانات من Settings (نفس طريقة Form_Load)
            Dim smtpCachedData = FuncSendMail.DeserializeXmlToList(Of SenderSmtpSettings)(My.Settings.SMTP_DATA_TABLE_XML)
            If smtpCachedData IsNot Nothing AndAlso smtpCachedData.Count > 0 Then
                ' تحديث LS_SenderSmtp في frmEmailSender
                LS_SenderSmtp.Clear()
                LS_SenderSmtp.AddRange(smtpCachedData)

                ' تحديث العداد
                frmEmailSender.lbltotalstmp.Text = $"Total Smtps: {LS_SenderSmtp.Count}"

                ' إعادة تعيين مصدر البيانات (نفس طريقة Form_Load)
                frmEmailSender.GridControl1.DataSource = LS_SenderSmtp
                frmEmailSender.GridControl1.RefreshDataSource()

                Debug.WriteLine($"Complete reload successful - {LS_SenderSmtp.Count} records loaded")
            End If

            ' خطوة 3: تحديث إضافي للتأكد
            frmEmailSender.GridView1.RefreshData()
            frmEmailSender.GridView1.LayoutChanged()
            frmEmailSender.GridView1.BestFitColumns()
            Application.DoEvents()

            ' خطوة 4: تأخير قصير ثم تحديث نهائي
            System.Threading.Thread.Sleep(200)
            frmEmailSender.GridView1.RefreshData()
            Application.DoEvents()

            ' خطوة 5: إجبار تحديث الأعمدة المحددة
            ForceUpdateSpecificColumns()

        Catch ex As Exception
            Debug.WriteLine($"Error in PerformCompleteReload: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' إجبار تحديث الأعمدة المحددة بطريقة مباشرة
    ''' </summary>
    Private Sub ForceUpdateSpecificColumns()
        Try
            Debug.WriteLine("Starting forced column update...")

            ' الأعمدة المستهدفة
            Dim targetFields As String() = {"smtpsubject", "smtpfromname", "smtpfrommail"}

            ' إجبار تحديث كل صف في الأعمدة المحددة
            For i As Integer = 0 To frmEmailSender.GridView1.DataRowCount - 1
                For Each fieldName As String In targetFields
                    Try
                        ' الحصول على القيمة الحالية
                        Dim currentValue = frmEmailSender.GridView1.GetRowCellValue(i, fieldName)

                        ' إعادة تعيين نفس القيمة لإجبار التحديث
                        frmEmailSender.GridView1.SetRowCellValue(i, fieldName, currentValue)

                        Debug.WriteLine($"Updated row {i}, field {fieldName} with value: {currentValue}")
                    Catch ex As Exception
                        Debug.WriteLine($"Error updating row {i}, field {fieldName}: {ex.Message}")
                    End Try
                Next
            Next

            ' تحديث شامل
            frmEmailSender.GridView1.UpdateCurrentRow()
            frmEmailSender.GridView1.PostEditor()
            frmEmailSender.GridView1.RefreshData()
            frmEmailSender.GridView1.LayoutChanged()
            Application.DoEvents()

            Debug.WriteLine("Forced column update completed")

        Catch ex As Exception
            Debug.WriteLine($"Error in ForceUpdateSpecificColumns: {ex.Message}")
        End Try
    End Sub






    Private Sub TxtTag_1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles TxtTag_1.SelectedIndexChanged
        DxErrorProvider1.ClearErrors()
        If TxtTag_1.SelectedIndex >= 0 Then
            If TxtTag_1.SelectedItem.ToString() = "Receiver Email" Then
                TxtFromName.Text = "[-Email-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Encoded Email 64" Then
                TxtFromName.Text = "[-Email64-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Start Name With Upper Case" Then
                TxtFromName.Text = "[-UCase-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Add Link" Then
                TxtFromName.Text = "[-Link-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Send With Logo" Then
                TxtFromName.Text = "[-Logo-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 3" Then
                TxtFromName.Text = "[-RCh3-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 4" Then
                TxtFromName.Text = "[-RCh4-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 5" Then
                TxtFromName.Text = "[-RCh5-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 6" Then
                TxtFromName.Text = "[-RCh6-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Character 7" Then
                TxtFromName.Text = "[-RCh7-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 3" Then
                TxtFromName.Text = "[-RN3-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 4" Then
                TxtFromName.Text = "[-RN4-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 5" Then
                TxtFromName.Text = "[-RN5-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 6" Then
                TxtFromName.Text = "[-RN6-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Number 7" Then
                TxtFromName.Text = "[-RN7-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random IP" Then
                TxtFromName.Text = "[-IP-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random IP China" Then
                TxtFromName.Text = "[-IPChina-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show The Domain" Then
                TxtFromName.Text = "[-Domain-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Name" Then
                TxtFromName.Text = "[-Name-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Company Name" Then
                TxtFromName.Text = "[-CompanyName-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Date" Then
                TxtFromName.Text = "[-Date-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Time" Then
                TxtFromName.Text = "[-Time-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Date Tomorrow" Then
                TxtFromName.Text = "[-DateTomorrow-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Country" Then
                TxtFromName.Text = "[-RCountry-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Random Browser" Then
                TxtFromName.Text = "[-RandomBrowser-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                TxtFromName.Text = "[-FakePhone-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                TxtFromName.Text = "[-FakeEmail-]"
            ElseIf TxtTag_1.SelectedItem.ToString() = "Show Random Street New York" Then
                TxtFromName.Text = "[-NewYork-]"
            End If
        End If
    End Sub
    Private Sub TxtTag_2_SelectedIndexChanged(sender As Object, e As EventArgs) Handles TxtTag_2.SelectedIndexChanged
        DxErrorProvider1.ClearErrors()
        If TxtTag_2.SelectedIndex >= 0 Then
            If TxtTag_2.SelectedItem.ToString() = "Receiver Email" Then
                TxtSubject.Text = "[-Email-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Encoded Email 64" Then
                TxtSubject.Text = "[-Email64-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Start Name With Upper Case" Then
                TxtSubject.Text = "[-UCase-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Add Link" Then
                TxtSubject.Text = "[-Link-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Send With Logo" Then
                TxtSubject.Text = "[-Logo-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 3" Then
                TxtSubject.Text = "[-RCh3-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 4" Then
                TxtSubject.Text = "[-RCh4-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 5" Then
                TxtSubject.Text = "[-RCh5-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 6" Then
                TxtSubject.Text = "[-RCh6-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Character 7" Then
                TxtSubject.Text = "[-RCh7-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 3" Then
                TxtSubject.Text = "[-RN3-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 4" Then
                TxtSubject.Text = "[-RN4-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 5" Then
                TxtSubject.Text = "[-RN5-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 6" Then
                TxtSubject.Text = "[-RN6-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Number 7" Then
                TxtSubject.Text = "[-RN7-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random IP" Then
                TxtSubject.Text = "[-IP-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random IP China" Then
                TxtSubject.Text = "[-IPChina-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show The Domain" Then
                TxtSubject.Text = "[-Domain-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Name" Then
                TxtSubject.Text = "[-Name-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Company Name" Then
                TxtSubject.Text = "[-CompanyName-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Date" Then
                TxtSubject.Text = "[-Date-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Time" Then
                TxtSubject.Text = "[-Time-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Date Tomorrow" Then
                TxtSubject.Text = "[-DateTomorrow-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Country" Then
                TxtSubject.Text = "[-RCountry-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Random Browser" Then
                TxtSubject.Text = "[-RandomBrowser-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Fake phone Numbers" Then
                TxtSubject.Text = "[-FakePhone-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Fake Email Address Office" Then
                TxtSubject.Text = "[-FakeEmail-]"
            ElseIf TxtTag_2.SelectedItem.ToString() = "Show Random Street New York" Then
                TxtSubject.Text = "[-NewYork-]"
            End If
        End If
    End Sub
End Class