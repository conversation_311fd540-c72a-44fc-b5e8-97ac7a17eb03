﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmActivationInfo
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmActivationInfo))
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.BackgroundWorker2 = New System.ComponentModel.BackgroundWorker()
        Me.TrmFadeIn = New System.Windows.Forms.Timer(Me.components)
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.PictureBox3 = New System.Windows.Forms.PictureBox()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.lbllic = New System.Windows.Forms.Label()
        Me.lblVersion = New System.Windows.Forms.Label()
        Me.HyperLinkEdit1 = New DevExpress.XtraEditors.HyperLinkEdit()
        Me.LBLACTIVATIONSTATUS = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.LBLEXPIREDATE = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.LBLREMAINDAYS = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.LBLACTIVATIONTYPE = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.LBLACTIVATIONCODE = New System.Windows.Forms.Label()
        Me.LBLPCSERIAL = New System.Windows.Forms.Label()
        Me.LBLACTIVATIONOWNER = New System.Windows.Forms.Label()
        Me.Label50 = New System.Windows.Forms.Label()
        Me.Label48 = New System.Windows.Forms.Label()
        Me.Label46 = New System.Windows.Forms.Label()
        Me.GroupControl6 = New DevExpress.XtraEditors.GroupControl()
        Me.BntUpdate = New DevExpress.XtraEditors.SimpleButton()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.HyperLinkEdit1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl6.SuspendLayout()
        Me.SuspendLayout()
        '
        'BackgroundWorker1
        '
        Me.BackgroundWorker1.WorkerReportsProgress = True
        Me.BackgroundWorker1.WorkerSupportsCancellation = True
        '
        'BackgroundWorker2
        '
        Me.BackgroundWorker2.WorkerSupportsCancellation = True
        '
        'TrmFadeIn
        '
        Me.TrmFadeIn.Enabled = True
        Me.TrmFadeIn.Interval = 15
        '
        'Timer1
        '
        Me.Timer1.Interval = 1000
        '
        'PictureBox3
        '
        Me.PictureBox3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureBox3.BackColor = System.Drawing.Color.Transparent
        Me.PictureBox3.Image = Global.Best_Sender.My.Resources.Resources.LogoUpdate
        Me.PictureBox3.Location = New System.Drawing.Point(790, 296)
        Me.PictureBox3.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureBox3.Name = "PictureBox3"
        Me.PictureBox3.Size = New System.Drawing.Size(79, 72)
        Me.PictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox3.TabIndex = 436
        Me.PictureBox3.TabStop = False
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.Location = New System.Drawing.Point(899, 147)
        Me.ProgressPanel1.LookAndFeel.SkinName = "WXI"
        Me.ProgressPanel1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ProgressPanel1.Margin = New System.Windows.Forms.Padding(4)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.ShowCaption = False
        Me.ProgressPanel1.ShowDescription = False
        Me.ProgressPanel1.Size = New System.Drawing.Size(43, 58)
        Me.ProgressPanel1.TabIndex = 456
        Me.ProgressPanel1.Text = "ProgressPanel1"
        '
        'lbllic
        '
        Me.lbllic.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.lbllic.Cursor = System.Windows.Forms.Cursors.Help
        Me.lbllic.Font = New System.Drawing.Font("Comfortaa", 9.0!)
        Me.lbllic.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.lbllic.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lbllic.Location = New System.Drawing.Point(505, 156)
        Me.lbllic.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lbllic.Name = "lbllic"
        Me.lbllic.Size = New System.Drawing.Size(648, 35)
        Me.lbllic.TabIndex = 455
        Me.lbllic.Text = "License Plam: "
        Me.lbllic.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblVersion
        '
        Me.lblVersion.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.lblVersion.Font = New System.Drawing.Font("Comfortaa", 9.0!)
        Me.lblVersion.ForeColor = System.Drawing.Color.Cornsilk
        Me.lblVersion.ImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lblVersion.Location = New System.Drawing.Point(505, 100)
        Me.lblVersion.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.lblVersion.Name = "lblVersion"
        Me.lblVersion.Size = New System.Drawing.Size(648, 35)
        Me.lblVersion.TabIndex = 446
        Me.lblVersion.Text = "Version : "
        Me.lblVersion.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'HyperLinkEdit1
        '
        Me.HyperLinkEdit1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperLinkEdit1.EditValue = "www.bestsendervip.com"
        Me.HyperLinkEdit1.Location = New System.Drawing.Point(50, 410)
        Me.HyperLinkEdit1.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperLinkEdit1.Name = "HyperLinkEdit1"
        Me.HyperLinkEdit1.Properties.AllowFocused = False
        Me.HyperLinkEdit1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.HyperLinkEdit1.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.0!)
        Me.HyperLinkEdit1.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.HyperLinkEdit1.Properties.Appearance.Options.UseBackColor = True
        Me.HyperLinkEdit1.Properties.Appearance.Options.UseFont = True
        Me.HyperLinkEdit1.Properties.Appearance.Options.UseForeColor = True
        Me.HyperLinkEdit1.Properties.Appearance.Options.UseTextOptions = True
        Me.HyperLinkEdit1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.HyperLinkEdit1.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.HyperLinkEdit1.Properties.AutoHeight = False
        Me.HyperLinkEdit1.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.HyperLinkEdit1.Properties.LinkColor = System.Drawing.Color.DeepSkyBlue
        Me.HyperLinkEdit1.Size = New System.Drawing.Size(374, 61)
        Me.HyperLinkEdit1.TabIndex = 453
        Me.HyperLinkEdit1.ToolTipAnchor = DevExpress.Utils.ToolTipAnchor.[Object]
        Me.HyperLinkEdit1.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Information
        Me.HyperLinkEdit1.ToolTipTitle = "Best Sender"
        '
        'LBLACTIVATIONSTATUS
        '
        Me.LBLACTIVATIONSTATUS.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.LBLACTIVATIONSTATUS.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LBLACTIVATIONSTATUS.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.LBLACTIVATIONSTATUS.Location = New System.Drawing.Point(189, 238)
        Me.LBLACTIVATIONSTATUS.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.LBLACTIVATIONSTATUS.Name = "LBLACTIVATIONSTATUS"
        Me.LBLACTIVATIONSTATUS.Size = New System.Drawing.Size(385, 26)
        Me.LBLACTIVATIONSTATUS.TabIndex = 452
        Me.LBLACTIVATIONSTATUS.Text = "--------------------------------"
        Me.LBLACTIVATIONSTATUS.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label2
        '
        Me.Label2.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label2.Location = New System.Drawing.Point(37, 229)
        Me.Label2.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(117, 35)
        Me.Label2.TabIndex = 451
        Me.Label2.Text = "License status:"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'LBLEXPIREDATE
        '
        Me.LBLEXPIREDATE.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.LBLEXPIREDATE.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LBLEXPIREDATE.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.LBLEXPIREDATE.Location = New System.Drawing.Point(189, 379)
        Me.LBLEXPIREDATE.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.LBLEXPIREDATE.Name = "LBLEXPIREDATE"
        Me.LBLEXPIREDATE.Size = New System.Drawing.Size(385, 26)
        Me.LBLEXPIREDATE.TabIndex = 450
        Me.LBLEXPIREDATE.Text = "--------------------------------"
        Me.LBLEXPIREDATE.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label3
        '
        Me.Label3.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label3.Location = New System.Drawing.Point(37, 371)
        Me.Label3.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(117, 35)
        Me.Label3.TabIndex = 449
        Me.Label3.Text = "Expiration date : "
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'LBLREMAINDAYS
        '
        Me.LBLREMAINDAYS.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.LBLREMAINDAYS.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LBLREMAINDAYS.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.LBLREMAINDAYS.Location = New System.Drawing.Point(189, 332)
        Me.LBLREMAINDAYS.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.LBLREMAINDAYS.Name = "LBLREMAINDAYS"
        Me.LBLREMAINDAYS.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.LBLREMAINDAYS.Size = New System.Drawing.Size(385, 26)
        Me.LBLREMAINDAYS.TabIndex = 448
        Me.LBLREMAINDAYS.Text = "--------------------------------"
        Me.LBLREMAINDAYS.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label6
        '
        Me.Label6.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label6.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label6.Location = New System.Drawing.Point(37, 324)
        Me.Label6.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(117, 35)
        Me.Label6.TabIndex = 447
        Me.Label6.Text = "Expiry date : "
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'LBLACTIVATIONTYPE
        '
        Me.LBLACTIVATIONTYPE.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.LBLACTIVATIONTYPE.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LBLACTIVATIONTYPE.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.LBLACTIVATIONTYPE.Location = New System.Drawing.Point(189, 285)
        Me.LBLACTIVATIONTYPE.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.LBLACTIVATIONTYPE.Name = "LBLACTIVATIONTYPE"
        Me.LBLACTIVATIONTYPE.Size = New System.Drawing.Size(385, 26)
        Me.LBLACTIVATIONTYPE.TabIndex = 444
        Me.LBLACTIVATIONTYPE.Text = "--------------------------------"
        Me.LBLACTIVATIONTYPE.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label4
        '
        Me.Label4.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label4.Location = New System.Drawing.Point(37, 276)
        Me.Label4.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(117, 35)
        Me.Label4.TabIndex = 443
        Me.Label4.Text = "License type :  "
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'LBLACTIVATIONCODE
        '
        Me.LBLACTIVATIONCODE.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.LBLACTIVATIONCODE.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LBLACTIVATIONCODE.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.LBLACTIVATIONCODE.Location = New System.Drawing.Point(189, 191)
        Me.LBLACTIVATIONCODE.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.LBLACTIVATIONCODE.Name = "LBLACTIVATIONCODE"
        Me.LBLACTIVATIONCODE.Size = New System.Drawing.Size(385, 26)
        Me.LBLACTIVATIONCODE.TabIndex = 442
        Me.LBLACTIVATIONCODE.Text = "xxxx-xxxx-xxxx-xxxx-xxxx"
        Me.LBLACTIVATIONCODE.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'LBLPCSERIAL
        '
        Me.LBLPCSERIAL.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.LBLPCSERIAL.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LBLPCSERIAL.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.LBLPCSERIAL.Location = New System.Drawing.Point(189, 138)
        Me.LBLPCSERIAL.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.LBLPCSERIAL.Name = "LBLPCSERIAL"
        Me.LBLPCSERIAL.Size = New System.Drawing.Size(385, 26)
        Me.LBLPCSERIAL.TabIndex = 441
        Me.LBLPCSERIAL.Text = "xxxx-xxxx-xxxx-xxxx-xxxx"
        Me.LBLPCSERIAL.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'LBLACTIVATIONOWNER
        '
        Me.LBLACTIVATIONOWNER.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.LBLACTIVATIONOWNER.Font = New System.Drawing.Font("Comfortaa", 9.0!)
        Me.LBLACTIVATIONOWNER.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(128, Byte), Integer))
        Me.LBLACTIVATIONOWNER.Location = New System.Drawing.Point(662, 379)
        Me.LBLACTIVATIONOWNER.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.LBLACTIVATIONOWNER.Name = "LBLACTIVATIONOWNER"
        Me.LBLACTIVATIONOWNER.Size = New System.Drawing.Size(335, 35)
        Me.LBLACTIVATIONOWNER.TabIndex = 440
        Me.LBLACTIVATIONOWNER.Text = "---------------------------------------"
        Me.LBLACTIVATIONOWNER.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label50
        '
        Me.Label50.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label50.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label50.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label50.Location = New System.Drawing.Point(37, 182)
        Me.Label50.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label50.Name = "Label50"
        Me.Label50.Size = New System.Drawing.Size(117, 35)
        Me.Label50.TabIndex = 439
        Me.Label50.Text = "Activation code:"
        Me.Label50.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label48
        '
        Me.Label48.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label48.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label48.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label48.Location = New System.Drawing.Point(37, 135)
        Me.Label48.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label48.Name = "Label48"
        Me.Label48.Size = New System.Drawing.Size(117, 35)
        Me.Label48.TabIndex = 438
        Me.Label48.Text = "Device serial:"
        Me.Label48.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label46
        '
        Me.Label46.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label46.Font = New System.Drawing.Font("Comfortaa", 10.0!)
        Me.Label46.ForeColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.Label46.Location = New System.Drawing.Point(37, 39)
        Me.Label46.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label46.Name = "Label46"
        Me.Label46.Size = New System.Drawing.Size(537, 35)
        Me.Label46.TabIndex = 437
        Me.Label46.Text = "This product is Licensed to: "
        Me.Label46.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'GroupControl6
        '
        Me.GroupControl6.Controls.Add(Me.PictureBox3)
        Me.GroupControl6.Controls.Add(Me.BntUpdate)
        Me.GroupControl6.Controls.Add(Me.lblVersion)
        Me.GroupControl6.Controls.Add(Me.ProgressPanel1)
        Me.GroupControl6.Controls.Add(Me.lbllic)
        Me.GroupControl6.Controls.Add(Me.Label46)
        Me.GroupControl6.Controls.Add(Me.Label48)
        Me.GroupControl6.Controls.Add(Me.Label50)
        Me.GroupControl6.Controls.Add(Me.LBLACTIVATIONOWNER)
        Me.GroupControl6.Controls.Add(Me.LBLPCSERIAL)
        Me.GroupControl6.Controls.Add(Me.HyperLinkEdit1)
        Me.GroupControl6.Controls.Add(Me.LBLACTIVATIONCODE)
        Me.GroupControl6.Controls.Add(Me.LBLACTIVATIONSTATUS)
        Me.GroupControl6.Controls.Add(Me.Label4)
        Me.GroupControl6.Controls.Add(Me.Label2)
        Me.GroupControl6.Controls.Add(Me.LBLACTIVATIONTYPE)
        Me.GroupControl6.Controls.Add(Me.LBLEXPIREDATE)
        Me.GroupControl6.Controls.Add(Me.Label6)
        Me.GroupControl6.Controls.Add(Me.Label3)
        Me.GroupControl6.Controls.Add(Me.LBLREMAINDAYS)
        Me.GroupControl6.Controls.Add(Me.Label5)
        Me.GroupControl6.Controls.Add(Me.Label9)
        Me.GroupControl6.Controls.Add(Me.Label1)
        Me.GroupControl6.Controls.Add(Me.Label7)
        Me.GroupControl6.Controls.Add(Me.Label8)
        Me.GroupControl6.Location = New System.Drawing.Point(28, 45)
        Me.GroupControl6.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.GroupControl6.Name = "GroupControl6"
        Me.GroupControl6.Size = New System.Drawing.Size(1168, 495)
        Me.GroupControl6.TabIndex = 516
        Me.GroupControl6.Text = "User License"
        '
        'BntUpdate
        '
        Me.BntUpdate.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntUpdate.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntUpdate.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.BntUpdate.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntUpdate.Appearance.Options.UseBackColor = True
        Me.BntUpdate.Appearance.Options.UseBorderColor = True
        Me.BntUpdate.Appearance.Options.UseFont = True
        Me.BntUpdate.Appearance.Options.UseForeColor = True
        Me.BntUpdate.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntUpdate.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntUpdate.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntUpdate.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntUpdate.AppearanceDisabled.Options.UseBackColor = True
        Me.BntUpdate.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntUpdate.AppearanceDisabled.Options.UseFont = True
        Me.BntUpdate.AppearanceDisabled.Options.UseForeColor = True
        Me.BntUpdate.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntUpdate.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntUpdate.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntUpdate.AppearanceHovered.Options.UseBackColor = True
        Me.BntUpdate.AppearanceHovered.Options.UseBorderColor = True
        Me.BntUpdate.AppearanceHovered.Options.UseForeColor = True
        Me.BntUpdate.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntUpdate.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntUpdate.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntUpdate.AppearancePressed.Options.UseBackColor = True
        Me.BntUpdate.AppearancePressed.Options.UseBorderColor = True
        Me.BntUpdate.AppearancePressed.Options.UseForeColor = True
        Me.BntUpdate.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BntUpdate.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Transfer32x32
        Me.BntUpdate.Location = New System.Drawing.Point(753, 238)
        Me.BntUpdate.Margin = New System.Windows.Forms.Padding(3, 2, 3, 2)
        Me.BntUpdate.Name = "BntUpdate"
        Me.BntUpdate.Size = New System.Drawing.Size(159, 42)
        Me.BntUpdate.TabIndex = 510
        Me.BntUpdate.Text = "Click Update"
        '
        'Label5
        '
        Me.Label5.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label5.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.Label5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.Label5.Location = New System.Drawing.Point(1112, 296)
        Me.Label5.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(479, 39)
        Me.Label5.TabIndex = 454
        Me.Label5.Text = "Warning: This computer program is protected by copyright"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label5.Visible = False
        '
        'Label9
        '
        Me.Label9.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label9.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.Label9.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.Label9.Location = New System.Drawing.Point(1112, 330)
        Me.Label9.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(479, 39)
        Me.Label9.TabIndex = 455
        Me.Label9.Text = " laws and international treaties. Unauthorized reproduction or"
        Me.Label9.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label9.Visible = False
        '
        'Label1
        '
        Me.Label1.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(1112, 364)
        Me.Label1.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(479, 39)
        Me.Label1.TabIndex = 456
        Me.Label1.Text = "distribution of this program, or any portion of it, may result in"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label1.Visible = False
        '
        'Label7
        '
        Me.Label7.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label7.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.Label7.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.Label7.Location = New System.Drawing.Point(1112, 398)
        Me.Label7.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(479, 39)
        Me.Label7.TabIndex = 457
        Me.Label7.Text = "severe civil and criminal penalties, and will be prosecuted to"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label7.Visible = False
        '
        'Label8
        '
        Me.Label8.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Label8.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold)
        Me.Label8.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.Label8.Location = New System.Drawing.Point(1112, 429)
        Me.Label8.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(479, 39)
        Me.Label8.TabIndex = 458
        Me.Label8.Text = "the maximum extent possible under law."
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Label8.Visible = False
        '
        'frmActivationInfo
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(1, Byte), Integer), CType(CType(217, Byte), Integer), CType(CType(25, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1223, 611)
        Me.Controls.Add(Me.GroupControl6)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Icon = CType(resources.GetObject("frmActivationInfo.IconOptions.Icon"), System.Drawing.Icon)
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.Name = "frmActivationInfo"
        Me.Opacity = 0R
        Me.RightToLeftLayout = True
        Me.ShowInTaskbar = False
        Me.Text = "  License info ..."
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.HyperLinkEdit1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControl6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl6.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents BackgroundWorker2 As System.ComponentModel.BackgroundWorker
    Private WithEvents TrmFadeIn As Timer
    Private WithEvents Timer1 As Timer
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents PictureBox3 As PictureBox
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents lbllic As Label
    Friend WithEvents lblVersion As Label
    Friend WithEvents HyperLinkEdit1 As DevExpress.XtraEditors.HyperLinkEdit
    Friend WithEvents LBLACTIVATIONSTATUS As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents LBLEXPIREDATE As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents LBLREMAINDAYS As Label
    Friend WithEvents Label6 As Label
    Friend WithEvents LBLACTIVATIONTYPE As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents LBLACTIVATIONCODE As Label
    Friend WithEvents LBLPCSERIAL As Label
    Friend WithEvents LBLACTIVATIONOWNER As Label
    Friend WithEvents Label50 As Label
    Friend WithEvents Label48 As Label
    Friend WithEvents Label46 As Label
    Friend WithEvents GroupControl6 As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Label5 As Label
    Friend WithEvents Label9 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents Label7 As Label
    Friend WithEvents Label8 As Label
    Friend WithEvents BntUpdate As DevExpress.XtraEditors.SimpleButton
End Class
