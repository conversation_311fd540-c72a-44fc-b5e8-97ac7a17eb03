﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AWSSDK.Core" version="3.7.105.8" targetFramework="net472" />
  <package id="AWSSDK.S3" version="3.7.103.15" targetFramework="net472" />
  <package id="BitMiracle.LibTiff.NET" version="2.4.649" targetFramework="net472" />
  <package id="chilkat-x64" version="10.1.2" targetFramework="net472" />
  <package id="DnsClient" version="1.7.0" targetFramework="net472" />
  <package id="Google.Protobuf" version="3.23.0" targetFramework="net472" />
  <package id="Grpc" version="2.46.6" targetFramework="net472" />
  <package id="Grpc.Core" version="2.46.6" targetFramework="net472" />
  <package id="Grpc.Core.Api" version="2.62.0" targetFramework="net472" />
  <package id="Grpc.Net.Client" version="2.62.0" targetFramework="net472" />
  <package id="Grpc.Net.Common" version="2.62.0" targetFramework="net472" />
  <package id="HtmlAgilityPack" version="1.12.1" targetFramework="net472" />
  <package id="HtmlRenderer.Core" version="1.5.0.5" targetFramework="net472" />
  <package id="IronPdf.Extensions.ASPX" version="2023.10.3" targetFramework="net472" />
  <package id="IronPdf.Native.Chrome.Windows" version="2024.3.24" targetFramework="net472" />
  <package id="IronPdf.Slim" version="2024.12.9" targetFramework="net472" />
  <package id="IronSoftware.Abstractions" version="2024.11.8" targetFramework="net472" />
  <package id="IronSoftware.Common" version="2024.11.2" targetFramework="net472" />
  <package id="IronSoftware.Drawing.Abstractions" version="2024.11.8" targetFramework="net472" />
  <package id="IronSoftware.Native.PdfModel" version="2024.3.24" targetFramework="net472" />
  <package id="IronSoftware.System.Drawing" version="2024.12.3" targetFramework="net472" />
  <package id="MailKit" version="3.5.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.FileExtensions" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Json" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.UserSecrets" version="6.0.1" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Physical" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileSystemGlobbing" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.NETCore.Platforms" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.Office.Interop.Excel" version="15.0.4795.1001" targetFramework="net472" />
  <package id="Microsoft.Win32.Primitives" version="4.0.1" targetFramework="net472" />
  <package id="Microsoft.Win32.Registry" version="5.0.0" targetFramework="net472" />
  <package id="MimeKit" version="3.5.0" targetFramework="net472" />
  <package id="NETStandard.Library" version="2.0.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net472" />
  <package id="OpenQA.Selenium.Winium" version="1.0.0.4" targetFramework="net472" />
  <package id="PDFsharp" version="1.32.3057.0" targetFramework="net472" />
  <package id="Portable.BouncyCastle" version="1.9.0" targetFramework="net472" />
  <package id="QRCoder" version="1.4.3" targetFramework="net472" />
  <package id="Select.HtmlToPdf" version="23.2.0" targetFramework="net472" />
  <package id="Selenium.WebDriver" version="3.141.0" targetFramework="net472" />
  <package id="Selenium.WebDriver.ChromeDriver" version="134.0.6998.16500" targetFramework="net472" />
  <package id="Selenium.WebDriver.GeckoDriver" version="0.36.0" targetFramework="net472" />
  <package id="Selenium.WebDriver.GeckoDriver.Win64" version="0.36.0" targetFramework="net472" />
  <package id="SixLabors.Fonts" version="1.0.0" targetFramework="net472" />
  <package id="SixLabors.ImageSharp" version="2.1.9" targetFramework="net472" />
  <package id="SixLabors.ImageSharp.Drawing" version="1.0.0" targetFramework="net472" />
  <package id="System.AppContext" version="4.1.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Collections" version="4.3.0" targetFramework="net472" />
  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="net472" />
  <package id="System.Configuration.ConfigurationManager" version="6.0.0" targetFramework="net472" />
  <package id="System.Console" version="4.0.0" targetFramework="net472" />
  <package id="System.Diagnostics.Debug" version="4.0.11" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="6.0.1" targetFramework="net472" />
  <package id="System.Diagnostics.Tools" version="4.0.1" targetFramework="net472" />
  <package id="System.Diagnostics.Tracing" version="4.1.0" targetFramework="net472" />
  <package id="System.Drawing.Common" version="6.0.0" targetFramework="net472" />
  <package id="System.Globalization" version="4.3.0" targetFramework="net472" />
  <package id="System.Globalization.Calendars" version="4.0.1" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Compression" version="4.1.0" targetFramework="net472" />
  <package id="System.IO.Compression.ZipFile" version="4.0.1" targetFramework="net472" />
  <package id="System.IO.FileSystem" version="4.0.1" targetFramework="net472" />
  <package id="System.IO.FileSystem.Primitives" version="4.0.1" targetFramework="net472" />
  <package id="System.Linq" version="4.1.0" targetFramework="net472" />
  <package id="System.Linq.Expressions" version="4.1.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Net.Http" version="4.1.0" targetFramework="net472" />
  <package id="System.Net.Http.WinHttpHandler" version="7.0.0" targetFramework="net472" />
  <package id="System.Net.Primitives" version="4.0.11" targetFramework="net472" />
  <package id="System.Net.Sockets" version="4.1.0" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.ObjectModel" version="4.0.12" targetFramework="net472" />
  <package id="System.Reflection" version="4.3.0" targetFramework="net472" />
  <package id="System.Reflection.Extensions" version="4.0.1" targetFramework="net472" />
  <package id="System.Reflection.Primitives" version="4.0.1" targetFramework="net472" />
  <package id="System.Reflection.TypeExtensions" version="4.3.0" targetFramework="net472" />
  <package id="System.Resources.ResourceManager" version="4.0.1" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Runtime.Extensions" version="4.1.0" targetFramework="net472" />
  <package id="System.Runtime.Handles" version="4.0.1" targetFramework="net472" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.0.0" targetFramework="net472" />
  <package id="System.Runtime.Numerics" version="4.0.1" targetFramework="net472" />
  <package id="System.Security.AccessControl" version="6.0.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.1" targetFramework="net472" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.1.0" targetFramework="net472" />
  <package id="System.Security.Permissions" version="6.0.0" targetFramework="net472" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net472" />
  <package id="System.Text.Encoding" version="4.0.11" targetFramework="net472" />
  <package id="System.Text.Encoding.CodePages" version="5.0.0" targetFramework="net472" />
  <package id="System.Text.Encoding.Extensions" version="4.0.11" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="6.0.0" targetFramework="net472" />
  <package id="System.Text.Json" version="6.0.0" targetFramework="net472" />
  <package id="System.Text.RegularExpressions" version="4.1.0" targetFramework="net472" />
  <package id="System.Threading" version="4.0.11" targetFramework="net472" />
  <package id="System.Threading.Tasks" version="4.0.11" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.Threading.Thread" version="4.0.0" targetFramework="net472" />
  <package id="System.Threading.Timer" version="4.0.1" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
  <package id="System.Xml.ReaderWriter" version="4.0.11" targetFramework="net472" />
  <package id="System.Xml.XDocument" version="4.0.11" targetFramework="net472" />
  <package id="ZXing" version="2.1.1" targetFramework="net472" />
  <package id="ZXing.Net" version="0.16.9" targetFramework="net472" />
</packages>