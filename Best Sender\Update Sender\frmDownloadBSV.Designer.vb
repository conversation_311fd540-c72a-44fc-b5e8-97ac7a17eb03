﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDownloadBSVBSV
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.labelDownloaded = New System.Windows.Forms.Label()
        Me.labelSpeed = New System.Windows.Forms.Label()
        Me.lblUpload = New System.Windows.Forms.Label()
        Me.ProgressBarControl1 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.ProgressPanel1 = New DevExpress.XtraWaitForm.ProgressPanel()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'labelDownloaded
        '
        Me.labelDownloaded.Font = New System.Drawing.Font("Comfortaa", 10.25!, System.Drawing.FontStyle.Bold)
        Me.labelDownloaded.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.labelDownloaded.Location = New System.Drawing.Point(303, 211)
        Me.labelDownloaded.Name = "labelDownloaded"
        Me.labelDownloaded.Size = New System.Drawing.Size(359, 30)
        Me.labelDownloaded.TabIndex = 417
        Me.labelDownloaded.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'labelSpeed
        '
        Me.labelSpeed.Font = New System.Drawing.Font("Comfortaa", 10.25!, System.Drawing.FontStyle.Bold)
        Me.labelSpeed.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.labelSpeed.Location = New System.Drawing.Point(303, 249)
        Me.labelSpeed.Name = "labelSpeed"
        Me.labelSpeed.Size = New System.Drawing.Size(359, 30)
        Me.labelSpeed.TabIndex = 418
        Me.labelSpeed.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblUpload
        '
        Me.lblUpload.Font = New System.Drawing.Font("Comfortaa", 10.25!, System.Drawing.FontStyle.Bold)
        Me.lblUpload.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.lblUpload.Location = New System.Drawing.Point(303, 288)
        Me.lblUpload.Name = "lblUpload"
        Me.lblUpload.Size = New System.Drawing.Size(359, 30)
        Me.lblUpload.TabIndex = 419
        Me.lblUpload.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'ProgressBarControl1
        '
        Me.ProgressBarControl1.Location = New System.Drawing.Point(12, 329)
        Me.ProgressBarControl1.Name = "ProgressBarControl1"
        Me.ProgressBarControl1.Properties.ShowTitle = True
        Me.ProgressBarControl1.Size = New System.Drawing.Size(940, 24)
        Me.ProgressBarControl1.TabIndex = 416
        '
        'ProgressPanel1
        '
        Me.ProgressPanel1.AnimationToTextDistance = 10
        Me.ProgressPanel1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ProgressPanel1.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.25!, System.Drawing.FontStyle.Bold)
        Me.ProgressPanel1.Appearance.Options.UseBackColor = True
        Me.ProgressPanel1.Appearance.Options.UseFont = True
        Me.ProgressPanel1.AppearanceCaption.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel1.AppearanceCaption.Options.UseFont = True
        Me.ProgressPanel1.AppearanceDescription.Font = New System.Drawing.Font("Microsoft Sans Serif", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ProgressPanel1.AppearanceDescription.Options.UseFont = True
        Me.ProgressPanel1.BarAnimationElementThickness = 5
        Me.ProgressPanel1.Caption = "Please Wait..."
        Me.ProgressPanel1.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
        Me.ProgressPanel1.Description = "New Update Downloading......"
        Me.ProgressPanel1.Location = New System.Drawing.Point(285, 97)
        Me.ProgressPanel1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.ProgressPanel1.Name = "ProgressPanel1"
        Me.ProgressPanel1.Size = New System.Drawing.Size(395, 104)
        Me.ProgressPanel1.TabIndex = 415
        Me.ProgressPanel1.Text = "picWait"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 15.25!)
        Me.Label1.Location = New System.Drawing.Point(251, 363)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(463, 34)
        Me.Label1.TabIndex = 420
        Me.Label1.Text = "A new version of Best Sender is availablel"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 15.25!)
        Me.Label2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(254, Byte), Integer), CType(CType(219, Byte), Integer), CType(CType(65, Byte), Integer))
        Me.Label2.Location = New System.Drawing.Point(345, 517)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(275, 34)
        Me.Label2.TabIndex = 420
        Me.Label2.Text = "www.BestSenderVIP.com"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'frmDownloadBSVBSV
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.labelDownloaded)
        Me.Controls.Add(Me.labelSpeed)
        Me.Controls.Add(Me.lblUpload)
        Me.Controls.Add(Me.ProgressBarControl1)
        Me.Controls.Add(Me.ProgressPanel1)
        Me.IconOptions.ShowIcon = False
        Me.Name = "frmDownloadBSVBSV"
        Me.ShowInTaskbar = False
        Me.Text = "Download New Update"
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents labelDownloaded As Label
    Friend WithEvents labelSpeed As Label
    Friend WithEvents lblUpload As Label
    Friend WithEvents ProgressBarControl1 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents ProgressPanel1 As DevExpress.XtraWaitForm.ProgressPanel
    Friend WithEvents Label1 As Label
    Friend WithEvents Label2 As Label
End Class
