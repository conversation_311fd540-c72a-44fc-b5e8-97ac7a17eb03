﻿Imports System.Drawing.Imaging
Imports System.IO
Imports DevExpress.XtraEditors
Imports System.Drawing
Imports System.Drawing.Drawing2D
Public Class frmImageResize
    ' تعريف MainPanel كمتغير عام في الفئة
    Private MainPanel As Panel
    Private Sub bntBrowse_Click(sender As Object, e As EventArgs) Handles bntBrowse.Click
        Dim ofd As New OpenFileDialog
        With ofd
            .InitialDirectory = My.Computer.FileSystem.SpecialDirectories.Desktop
            .Filter = "JPEGs|*.jpg|GIFs|*.gif|Bitmaps|*.bmp|PNGs|*.png|All Image|*.jpg;*.jpeg;*.png;*.gif*"
            .FilterIndex = 1
        End With
        If ofd.ShowDialog() = DialogResult.OK Then
            PicLogo.SizeMode = PictureBoxSizeMode.StretchImage
            PicLogo.Image = Image.FromFile(ofd.FileName)
            txtSourcePath.Text = ofd.FileName
            Dim bmp As New Bitmap(ofd.FileName)
            lblOriginalSize.Text = "Original Size:" & bmp.Width.ToString() + " X " & bmp.Height.ToString()
            Me.Tag = Convert.ToInt16(bmp.Width.ToString()) + Convert.ToInt16(bmp.Height.ToString())
            PicLogo.Tag = ofd.FileName
            txtWidth.Text = bmp.Width.ToString()
            txtHeight.Text = bmp.Height.ToString()
        End If
    End Sub
    Private Sub bntSave_Click(sender As Object, e As EventArgs) Handles bntSave.Click
        If (txtSourcePath.Text = "" Or IsNothing(txtSourcePath.Text)) Then
            XtraMessageBox.Show("Please Select JPG | PNG | GIF....!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If (txtWidth.Text = "" Or IsNothing(txtWidth.Text)) Then
            XtraMessageBox.Show("Size Width | 190....!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If (txtHeight.Text = "" Or IsNothing(txtHeight.Text)) Then
            XtraMessageBox.Show("Size Height | 40....!", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Dim SFD As New SaveFileDialog
        SFD.AddExtension = True
        SFD.DefaultExt = Path.GetExtension(txtSourcePath.Text)
        With SFD
            .InitialDirectory = My.Computer.FileSystem.SpecialDirectories.Desktop
            .Filter = "JPEGs|*.jpg|GIFs|*.gif|Bitmaps|*.bmp|PNGs|*.png"
            .AddExtension = True
            .DefaultExt = Path.GetExtension(txtSourcePath.Text)
            Select Case .DefaultExt
                Case "jpg"
                    .FilterIndex = 1
                Case "gif"
                    .FilterIndex = 2
                Case "bmp"
                    .FilterIndex = 3
                Case "png"
                    .FilterIndex = 4
            End Select
        End With
        If SFD.ShowDialog = DialogResult.OK Then
            Try
                Select Case SFD.FilterIndex
                    Case 1
                        PicLogo.Image.Save(SFD.FileName, ImageFormat.Jpeg)
                    Case 2
                        PicLogo.Image.Save(SFD.FileName, ImageFormat.Gif)
                    Case 3
                        PicLogo.Image.Save(SFD.FileName, ImageFormat.Bmp)
                    Case 4
                        PicLogo.Image.Save(SFD.FileName, ImageFormat.Png)
                End Select
                MsgBox("Image is Successfullly Saved.", MessageBoxIcon.Information, "Information")
            Catch ex As Exception
                MsgBox("This is no image. Please browse an image first.", MessageBoxIcon.Error, "Error")
            End Try
        End If
    End Sub
    Private Sub bntClear_Click(sender As Object, e As EventArgs) Handles bntClear.Click
        txtSourcePath.Text = ""
        txtWidth.Text = ""
        txtHeight.Text = ""
        PicLogo.Image = Nothing
        lblOriginalSize.Text = lblOriginalSize.Tag
        Me.Tag = ""
    End Sub

    ''' <summary>
    ''' تغيير حجم الصورة عند تغيير قيمة العرض
    ''' </summary>
    Private Sub txtWidth_TextChanged(sender As Object, e As EventArgs) Handles txtWidth.TextChanged
        ResizePreviewImage()
    End Sub

    ''' <summary>
    ''' تغيير حجم الصورة عند تغيير قيمة الارتفاع
    ''' </summary>
    Private Sub txtHeight_TextChanged(sender As Object, e As EventArgs) Handles txtHeight.TextChanged
        ResizePreviewImage()
    End Sub

    ''' <summary>
    ''' تغيير حجم الصورة في PicLogo بناءً على القيم المدخلة في txtWidth و txtHeight
    ''' </summary>
    Private Sub ResizePreviewImage()
        ' التحقق من وجود صورة
        If PicLogo.Image Is Nothing Then Return

        ' التحقق من صحة القيم المدخلة
        Dim width As Integer
        Dim height As Integer

        If Not Integer.TryParse(txtWidth.Text, width) OrElse width <= 0 Then Return
        If Not Integer.TryParse(txtHeight.Text, height) OrElse height <= 0 Then Return

        ' حفظ الصورة الأصلية إذا لم تكن محفوظة
        If PicLogo.Tag IsNot Nothing AndAlso TypeOf PicLogo.Tag Is String Then
            Try
                ' الحصول على الصورة الأصلية
                Dim originalImage As Image = Image.FromFile(PicLogo.Tag.ToString())

                ' إنشاء صورة جديدة بالحجم المطلوب
                Dim resizedImage As New Bitmap(width, height)

                ' رسم الصورة الأصلية على الصورة الجديدة بالحجم المطلوب
                Using g As Graphics = Graphics.FromImage(resizedImage)
                    ' تعيين جودة الرسم
                    g.SmoothingMode = SmoothingMode.AntiAlias
                    g.InterpolationMode = InterpolationMode.HighQualityBicubic
                    g.PixelOffsetMode = PixelOffsetMode.HighQuality

                    ' رسم الصورة
                    g.DrawImage(originalImage, 0, 0, width, height)
                End Using

                ' تعيين الصورة الجديدة
                PicLogo.Image = resizedImage

                ' تعيين وضع العرض للحفاظ على نسبة العرض إلى الارتفاع
                PicLogo.SizeMode = PictureBoxSizeMode.Zoom

                ' تحديث نص حجم الصورة
                lblOriginalSize.Text = "Original Size: " & originalImage.Width & " X " & originalImage.Height & " | New Size: " & width & " X " & height

                ' إعادة توسيط النص في MainPanel
                Dim lblWidth As Integer = lblOriginalSize.PreferredWidth
                Dim lblX As Integer = (MainPanel.Width - lblWidth) \ 2
                Dim lblY As Integer = PicLogo.Bottom + 20
                lblOriginalSize.Location = New Point(lblX, lblY)
            Catch ex As Exception
                ' معالجة الأخطاء
                MessageBox.Show("Error resizing image: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    ''' <summary>
    ''' حدث تحميل النموذج
    ''' </summary>
    Private Sub frmImageResize_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' إنشاء MainPanel
        CreateMainPanel()

        ' إضافة معالج حدث Paint للـ MainPanel
        AddHandler MainPanel.Paint, AddressOf MainPanel_Paint

        ' إضافة معالج حدث Resize للـ MainPanel
        AddHandler MainPanel.Resize, AddressOf MainPanel_Resize

        ' إضافة معالج حدث Resize للنموذج
        AddHandler Me.Resize, AddressOf frmImageResize_Resize

        ' نقل العناصر إلى MainPanel
        MoveControlsToMainPanel()

        ' تهيئة MainPanel ليكون في المنتصف
        CenterMainPanel()
    End Sub

    ''' <summary>
    ''' إنشاء MainPanel
    ''' </summary>
    Private Sub CreateMainPanel()
        ' إنشاء MainPanel إذا لم يكن موجودًا
        If MainPanel Is Nothing Then
            MainPanel = New Panel()
            MainPanel.Name = "MainPanel"
            MainPanel.Anchor = AnchorStyles.None
            MainPanel.BackColor = Color.FromArgb(46, 46, 46)
            MainPanel.Size = New Size(Me.ClientSize.Width - 20, Me.ClientSize.Height - 20)
            MainPanel.Padding = New Padding(10)
            Me.Controls.Add(MainPanel)
            MainPanel.BringToFront()
        End If
    End Sub

    ''' <summary>
    ''' نقل العناصر إلى MainPanel
    ''' </summary>
    Private Sub MoveControlsToMainPanel()
        ' التأكد من وجود MainPanel
        If MainPanel Is Nothing Then Return

        ' نقل العناصر من النموذج إلى MainPanel
        If Not MainPanel.Controls.Contains(GroupBox3) Then
            MainPanel.Controls.Add(GroupBox3)
        End If

        If Not MainPanel.Controls.Contains(PicLogo) Then
            MainPanel.Controls.Add(PicLogo)
        End If

        If Not MainPanel.Controls.Contains(bntBrowse) Then
            MainPanel.Controls.Add(bntBrowse)
        End If

        If Not MainPanel.Controls.Contains(txtSourcePath) Then
            MainPanel.Controls.Add(txtSourcePath)
        End If

        If Not MainPanel.Controls.Contains(bntSave) Then
            MainPanel.Controls.Add(bntSave)
        End If

        If Not MainPanel.Controls.Contains(txtWidth) Then
            MainPanel.Controls.Add(txtWidth)
        End If

        If Not MainPanel.Controls.Contains(txtHeight) Then
            MainPanel.Controls.Add(txtHeight)
        End If

        If Not MainPanel.Controls.Contains(bntClear) Then
            MainPanel.Controls.Add(bntClear)
        End If

        If Not MainPanel.Controls.Contains(lblOriginalSize) Then
            MainPanel.Controls.Add(lblOriginalSize)
        End If
    End Sub

    ''' <summary>
    ''' وضع MainPanel في المنتصف
    ''' </summary>
    Private Sub CenterMainPanel()
        ' الحفاظ على الحجم الأصلي للـ MainPanel
        Dim originalWidth As Integer = 972 ' العرض الأصلي
        Dim originalHeight As Integer = 602 ' الارتفاع الأصلي

        ' تعيين حجم MainPanel إلى الحجم الأصلي
        MainPanel.Width = originalWidth
        MainPanel.Height = originalHeight

        ' حساب موقع MainPanel ليكون في المنتصف
        Dim x As Integer = (Me.ClientSize.Width - MainPanel.Width) \ 2
        Dim y As Integer = (Me.ClientSize.Height - MainPanel.Height) \ 2

        ' تعيين موقع MainPanel في المنتصف
        MainPanel.Location = New Point(Math.Max(0, x), Math.Max(0, y))

        ' توسيط العناصر داخل MainPanel
        CenterControlsInMainPanel()

        ' جعل MainPanel منحنية من الأطراف
        ApplyRoundedCorners(MainPanel, 20)
    End Sub

    ''' <summary>
    ''' توسيط العناصر داخل MainPanel
    ''' </summary>
    Private Sub CenterControlsInMainPanel()
        ' حساب نقطة المنتصف في MainPanel
        Dim centerX As Integer = MainPanel.Width \ 2
        Dim centerY As Integer = MainPanel.Height \ 2

        ' حساب المساحة الإجمالية التي تشغلها جميع العناصر
        Dim totalWidth As Integer = 800  ' تقريبًا عرض جميع العناصر مع المسافات بينها
        Dim totalHeight As Integer = 400 ' تقريبًا ارتفاع جميع العناصر مع المسافات بينها

        ' حساب نقطة البداية لوضع العناصر
        Dim startX As Integer = centerX - (totalWidth \ 2)
        Dim startY As Integer = centerY - (totalHeight \ 2)

        ' تحديد المواقع النسبية للعناصر (نحافظ على المسافات النسبية بين العناصر)

        ' الأزرار في العمود الأيسر
        Dim leftColumnX As Integer = startX + 50

        ' زر Browse
        bntBrowse.Location = New Point(leftColumnX, startY + 50)

        ' زر Save
        bntSave.Location = New Point(leftColumnX, bntBrowse.Bottom + 10)

        ' زر Reset All
        bntClear.Location = New Point(leftColumnX, bntSave.Bottom + 10)

        ' مربعات النص في العمود الأوسط
        Dim middleColumnX As Integer = leftColumnX + bntBrowse.Width + 20

        ' مربع نص المسار
        txtSourcePath.Location = New Point(middleColumnX, startY + 50)

        ' مربع نص العرض
        txtWidth.Location = New Point(middleColumnX, bntBrowse.Bottom + 10)

        ' مربع نص الارتفاع
        txtHeight.Location = New Point(middleColumnX, bntSave.Bottom + 10)

        ' حساب عرض المجموعة الكاملة (الأزرار + مربعات النص + الصورة)
        Dim totalGroupWidth As Integer = bntBrowse.Width + 20 + txtSourcePath.Width + 20 + PicLogo.Width

        ' حساب نقطة البداية للمجموعة بحيث تكون في المنتصف
        Dim groupStartX As Integer = centerX - (totalGroupWidth \ 2)

        ' تحديد مواقع العناصر بناءً على نقطة البداية الجديدة
        leftColumnX = groupStartX

        ' إعادة تعيين مواقع الأزرار
        bntBrowse.Location = New Point(leftColumnX, startY + 50)
        bntSave.Location = New Point(leftColumnX, bntBrowse.Bottom + 10)
        bntClear.Location = New Point(leftColumnX, bntSave.Bottom + 10)

        ' إعادة تعيين مواقع مربعات النص
        middleColumnX = leftColumnX + bntBrowse.Width + 20
        txtSourcePath.Location = New Point(middleColumnX, startY + 50)
        txtWidth.Location = New Point(middleColumnX, bntBrowse.Bottom + 10)
        txtHeight.Location = New Point(middleColumnX, bntSave.Bottom + 10)

        ' منطقة الصورة في المنتصف مع باقي العناصر
        Dim rightColumnX As Integer = middleColumnX + txtSourcePath.Width + 20
        PicLogo.Location = New Point(rightColumnX, startY + 50)

        ' نص حجم الصورة الأصلي
        lblOriginalSize.AutoSize = True
        lblOriginalSize.ForeColor = Color.FromArgb(34, 197, 94)
        lblOriginalSize.Text = "Original Size: " & If(PicLogo.Image IsNot Nothing, PicLogo.Image.Width & " X " & PicLogo.Image.Height, "")

        ' التأكد من أن النص مرئي دائمًا
        If lblOriginalSize.Text = "" Then
            lblOriginalSize.Text = "Original Size: 0 X 0"
        End If

        ' وضع النص في منتصف MainPanel
        Dim lblWidth As Integer = lblOriginalSize.PreferredWidth
        Dim lblX As Integer = (MainPanel.Width - lblWidth) \ 2
        Dim lblY As Integer = PicLogo.Bottom + 20
        lblOriginalSize.Location = New Point(lblX, lblY)

        ' توسيط GroupBox3 في أسفل النموذج
        Dim groupBoxX As Integer = (MainPanel.Width - GroupBox3.Width) \ 2
        GroupBox3.Location = New Point(groupBoxX, MainPanel.Height - GroupBox3.Height - 40)

        ' تعديل نص Label7 داخل GroupBox3
        Label7.AutoSize = True
        Dim label7X As Integer = (GroupBox3.Width - Label7.Width) \ 2
        Label7.Location = New Point(label7X, (GroupBox3.Height - Label7.Height) \ 2)
    End Sub

    ''' <summary>
    ''' معالج حدث Paint للـ MainPanel
    ''' </summary>
    Private Sub MainPanel_Paint(sender As Object, e As PaintEventArgs)
        ' رسم حدود منحنية للـ MainPanel
        Dim panel As Panel = DirectCast(sender, Panel)
        Dim radius As Integer = 20

        ' تعيين جودة الرسم للحصول على أفضل نتيجة
        e.Graphics.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
        e.Graphics.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
        e.Graphics.PixelOffsetMode = Drawing2D.PixelOffsetMode.HighQuality

        ' تعديل المستطيل ليكون داخل حدود اللوحة بشكل كامل
        ' نترك هامشًا كبيرًا (15 بكسل) لضمان ظهور الحدود بالكامل
        Dim rect As New Rectangle(15, 15, panel.Width - 30, panel.Height - 30)

        ' رسم مستطيل بحواف منحنية باللون الذهبي (254, 219, 65) بسمك 2 بكسل
        Using pen As New Pen(Color.FromArgb(254, 219, 65), 2)
            ' تعيين نهايات الخطوط لتكون دائرية
            pen.StartCap = Drawing2D.LineCap.Round
            pen.EndCap = Drawing2D.LineCap.Round
            pen.LineJoin = Drawing2D.LineJoin.Round
            pen.Alignment = Drawing2D.PenAlignment.Center

            ' رسم مستطيل بحواف منحنية
            e.Graphics.DrawPath(pen, GetRoundedRectPath(rect, radius))

            ' رسم مستطيل آخر بنفس الحجم لضمان ظهور الخط بشكل كامل
            ' هذه تقنية لتجنب مشكلة تقطيع الخطوط في GDI+
            e.Graphics.DrawPath(pen, GetRoundedRectPath(rect, radius))
        End Using
    End Sub

    ''' <summary>
    ''' إنشاء مسار لمستطيل بحواف منحنية
    ''' </summary>
    Private Function GetRoundedRectPath(rect As Rectangle, radius As Integer) As Drawing2D.GraphicsPath
        Dim path As New Drawing2D.GraphicsPath()

        ' تأكد من أن نصف القطر لا يتجاوز نصف عرض أو ارتفاع المستطيل
        radius = Math.Min(radius, Math.Min(rect.Width \ 2, rect.Height \ 2))

        ' تعديل المستطيل لضمان عدم تجاوز الحدود
        Dim adjustedRect As New Rectangle(
            rect.X,
            rect.Y,
            Math.Max(radius * 2, rect.Width),
            Math.Max(radius * 2, rect.Height)
        )

        ' إضافة الأقواس والخطوط للمسار بطريقة أكثر دقة
        ' الزاوية العلوية اليسرى
        path.AddArc(adjustedRect.X, adjustedRect.Y, radius * 2, radius * 2, 180, 90)

        ' الخط العلوي
        path.AddLine(
            adjustedRect.X + radius,
            adjustedRect.Y,
            adjustedRect.Right - radius,
            adjustedRect.Y
        )

        ' الزاوية العلوية اليمنى
        path.AddArc(
            adjustedRect.Right - radius * 2,
            adjustedRect.Y,
            radius * 2,
            radius * 2,
            270,
            90
        )

        ' الخط الأيمن
        path.AddLine(
            adjustedRect.Right,
            adjustedRect.Y + radius,
            adjustedRect.Right,
            adjustedRect.Bottom - radius
        )

        ' الزاوية السفلية اليمنى
        path.AddArc(
            adjustedRect.Right - radius * 2,
            adjustedRect.Bottom - radius * 2,
            radius * 2,
            radius * 2,
            0,
            90
        )

        ' الخط السفلي
        path.AddLine(
            adjustedRect.Right - radius,
            adjustedRect.Bottom,
            adjustedRect.X + radius,
            adjustedRect.Bottom
        )

        ' الزاوية السفلية اليسرى
        path.AddArc(
            adjustedRect.X,
            adjustedRect.Bottom - radius * 2,
            radius * 2,
            radius * 2,
            90,
            90
        )

        ' الخط الأيسر
        path.AddLine(
            adjustedRect.X,
            adjustedRect.Bottom - radius,
            adjustedRect.X,
            adjustedRect.Y + radius
        )

        ' إغلاق المسار
        path.CloseAllFigures()

        Return path
    End Function

    ''' <summary>
    ''' تطبيق حواف منحنية على لوحة
    ''' </summary>
    ''' <param name="panel">اللوحة المراد تطبيق الحواف المنحنية عليها</param>
    ''' <param name="radius">نصف قطر الانحناء</param>
    Private Sub ApplyRoundedCorners(panel As Panel, radius As Integer)
        ' تعديل المستطيل ليكون داخل حدود اللوحة بشكل كامل
        ' نترك هامشًا كبيرًا (12 بكسل) لضمان ظهور الحدود بالكامل
        Dim rect As New Rectangle(12, 12, panel.Width - 25, panel.Height - 25)

        ' إنشاء مسار منحني للحواف باستخدام الدالة المساعدة
        Dim path As Drawing2D.GraphicsPath = GetRoundedRectPath(rect, radius)

        ' تطبيق المسار على اللوحة
        panel.Region = New Region(path)

        ' تعيين خاصية BorderStyle إلى None لإخفاء الحدود الافتراضية
        panel.BorderStyle = BorderStyle.None
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmImageResize_Resize(sender As Object, e As EventArgs)
        ' إعادة تعيين حجم وموقع MainPanel عند تغيير حجم النموذج
        CenterMainPanel()
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم MainPanel
    ''' </summary>
    Private Sub MainPanel_Resize(sender As Object, e As EventArgs)
        ' إعادة تطبيق الحواف المنحنية عند تغيير حجم اللوحة
        ApplyRoundedCorners(MainPanel, 20)

        ' إعادة رسم اللوحة
        MainPanel.Invalidate()
    End Sub

End Class