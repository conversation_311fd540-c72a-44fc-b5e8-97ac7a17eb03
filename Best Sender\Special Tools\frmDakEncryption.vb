﻿Imports System.IO
Imports System.Text.RegularExpressions
Imports System.Net
Imports System.Text
Imports DevExpress.XtraEditors
Public Class frmDakEncryption
    Dim dynamicKeywords As String() = {
        "[-Email-]", "[-IP-]", "[-IPChina-]", "[-Domain-]", "[-Name-]", "[-Date-]", "[-DateTomorrow-]",
        "[-RandomBrowser-]", "[-RCountry-]", "[-FakePhone-]", "[-FakeEmail-]", "[-NewYork-]", "[-UCase-]",
        "[-Link-]", "[-Logo-]", "[-QRCode-]", "[-RN1-]", "[-RN2-]", "[-RN3-]", "[-RN4-]", "[-RN5-]", "[-RN6-]",
        "[-RN7-]", "[-RN8-]", "[-RN9-]", "[-RN10-]", "[-RN12-]", "[-RCh1-]", "[-RCh2-]", "[-RCh3-]", "[-RCh4-]",
        "[-RCh5-]", "[-RCh6-]", "[-RCh7-]", "[-RCh8-]"
    }
    Private Sub LetterEncoder_bntBrowse_Click(sender As Object, e As EventArgs) Handles LetterEncoder_bntBrowse.Click
        ' إعادة تعيين القيم الافتراضية
        LetterEncoder_txtFilepath.ResetText()
        LetterEncoder_Richtext.ResetText()
        LetterEncoder_Richtext2.ResetText()
        LetterEncoder_bntEncrypt.Enabled = True
        LetterEncoder_bntSave.Enabled = False
        ' فتح مربع الحوار لاختيار ملف HTML
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "HTML Files|*.html;*.htm"
        If openFileDialog.ShowDialog() = DialogResult.OK Then
            Dim loadedHtml As String
            Try
                ' قراءة محتوى الملف باستخدام StreamReader لتحسين الأداء
                Using reader As New StreamReader(openFileDialog.FileName)
                    loadedHtml = reader.ReadToEnd()
                End Using
                ' تحديث الواجهة
                LetterEncoder_txtFilepath.Text = openFileDialog.FileName
                LetterEncoder_Richtext.Text = loadedHtml
                LetterEncoder_bntEncrypt.Enabled = True
            Catch ex As Exception
                XtraMessageBox.Show("Error loading file: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
    Private Sub LetterEncoder_bntClearAll_Click(sender As Object, e As EventArgs) Handles LetterEncoder_bntClearAll.Click
        LetterEncoder_txtFilepath.ResetText()
        LetterEncoder_Richtext.ResetText()
        LetterEncoder_Richtext2.ResetText()
        LetterEncoder_bntEncrypt.Enabled = False
        LetterEncoder_bntSave.Enabled = False
    End Sub
    Private Sub frmDakEncryption_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' جعل النموذج غير مرئي أثناء تطبيق التصميم
        Me.Opacity = 0

        ' تعطيل زر التشفير في البداية
        LetterEncoder_bntEncrypt.Enabled = False
        LetterEncoder_bntSave.Enabled = False

        ' تعيين عنوان النموذج
        Me.Text = "Dark Encryption"

        ' تطبيق تصميم Binance مباشرة
        Try
            ApplyBinanceStyle()

            ' إضافة معالج حدث لتغيير حجم النموذج
            AddHandler Me.Resize, AddressOf frmDakEncryption_Resize

            ' وضع العناصر في المنتصف عند تحميل النموذج
            CenterControls()

            ' إضافة عنوان في أعلى النموذج
            AddHeaderImage()

            ' جعل النموذج مرئي بعد تطبيق التصميم
            Me.Opacity = 1
        Catch ex As Exception
            ' في حالة حدوث خطأ، جعل النموذج مرئي على أي حال
            Me.Opacity = 1
            Debug.WriteLine($"Error in frmDakEncryption_Load: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' معالج حدث تغيير حجم النموذج
    ''' </summary>
    Private Sub frmDakEncryption_Resize(sender As Object, e As EventArgs)
        ' وضع العناصر في المنتصف عند تغيير حجم النموذج
        CenterControls()
    End Sub

    ''' <summary>
    ''' تنسيق أحجام العناصر وموقعها
    ''' </summary>
    Private Sub CenterControls()
        Try
            ' الحصول على حجم النموذج
            Dim formWidth As Integer = Me.ClientSize.Width
            Dim formHeight As Integer = Me.ClientSize.Height

            ' البحث عن HeaderPictureBox
            Dim headerPictureBox As PictureBox = Nothing
            For Each ctrl As Control In Me.Controls
                If TypeOf ctrl Is PictureBox AndAlso ctrl.Name = "HeaderPictureBox" Then
                    headerPictureBox = DirectCast(ctrl, PictureBox)
                    Exit For
                End If
            Next

            ' تنسيق أحجام GroupControl1 و GroupControl2
            Dim panelWidth As Integer = (formWidth - 40) / 2
            Dim panelHeight As Integer = formHeight - (If(headerPictureBox IsNot Nothing, headerPictureBox.Height, 0)) - 15

            ' تنسيق GroupControl1
            GroupControl1.Size = New Size(panelWidth, panelHeight)
            GroupControl1.Location = New Point(10, If(headerPictureBox IsNot Nothing, headerPictureBox.Bottom + 5, 10))

            ' تنسيق GroupControl2
            GroupControl2.Size = New Size(panelWidth, panelHeight)
            GroupControl2.Location = New Point(GroupControl1.Right + 20, If(headerPictureBox IsNot Nothing, headerPictureBox.Bottom + 5, 10))

            ' تنسيق LayoutControl2
            LayoutControl2.Dock = DockStyle.Fill

            ' تنسيق أحجام الأزرار
            LetterEncoder_bntBrowse.Size = New Size(100, 30)
            LetterEncoder_bntEncrypt.Size = New Size(100, 30)
            LetterEncoder_bntClearAll.Size = New Size(100, 30)
            LetterEncoder_bntSave.Size = New Size(100, 30)

            ' تنسيق LetterEncoder_txtFilepath
            LetterEncoder_txtFilepath.Size = New Size(300, LetterEncoder_txtFilepath.Height)

            Debug.WriteLine($"Controls resized. Form size: {formWidth}x{formHeight}")
        Catch ex As Exception
            ' طباعة الخطأ للتصحيح
            Debug.WriteLine($"Error resizing controls: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' إضافة صورة العنوان في أعلى النموذج
    ''' </summary>
    Private Sub AddHeaderImage()
        Try
            ' إنشاء PictureBox للصورة
            Dim headerPictureBox As New PictureBox()
            headerPictureBox.Name = "HeaderPictureBox"
            headerPictureBox.BackColor = Color.Transparent
            headerPictureBox.SizeMode = PictureBoxSizeMode.Zoom ' جعل الصورة قابلة للتكبير

            ' تحميل الصورة من الموارد
            Try
                headerPictureBox.Image = My.Resources.DarkImageLeter
                Debug.WriteLine("Image loaded from resources successfully")
            Catch ex As Exception
                Debug.WriteLine($"Error loading image from resources: {ex.Message}")
                ' إذا لم يتم العثور على الصورة، نستخدم صورة افتراضية
                headerPictureBox.BackColor = Color.FromArgb(13, 13, 13)
                headerPictureBox.Image = Nothing
            End Try

            ' تعيين حجم وموقع الصورة
            Dim imageHeight As Integer = 50
            headerPictureBox.Size = New Size(200, imageHeight)
            headerPictureBox.Location = New Point((Me.ClientSize.Width - headerPictureBox.Width) \ 2, 0)
            headerPictureBox.Dock = DockStyle.Top ' جعل الصورة في أعلى النموذج

            ' إضافة PictureBox إلى النموذج
            Me.Controls.Add(headerPictureBox)
            headerPictureBox.BringToFront()

            Debug.WriteLine("Header image added successfully")
        Catch ex As Exception
            Debug.WriteLine($"Error adding header image: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تطبيق تصميم Binance على النموذج
    ''' </summary>
    Private Sub ApplyBinanceStyle()
        Try
            ' تعيين لون خلفية النموذج - لون Binance الأساسي الداكن
            Me.BackColor = Color.FromArgb(13, 13, 13) ' لون أكثر قتامة مثل Binance

            ' تنسيق LayoutControl2
            LayoutControl2.BackColor = Color.FromArgb(13, 13, 13)
            LayoutControl2.ForeColor = Color.White

            ' تنسيق GroupControl1
            GroupControl1.Appearance.BackColor = Color.FromArgb(22, 26, 30) ' لون Binance للبطاقات
            GroupControl1.Appearance.BorderColor = Color.FromArgb(30, 35, 40) ' حدود رمادية داكنة
            GroupControl1.Appearance.ForeColor = Color.White
            GroupControl1.Appearance.Options.UseBackColor = True
            GroupControl1.Appearance.Options.UseBorderColor = True
            GroupControl1.Appearance.Options.UseForeColor = True

            GroupControl1.AppearanceCaption.BackColor = Color.FromArgb(22, 26, 30)
            GroupControl1.AppearanceCaption.BorderColor = Color.FromArgb(30, 35, 40)
            GroupControl1.AppearanceCaption.ForeColor = Color.White
            GroupControl1.AppearanceCaption.Options.UseBackColor = True
            GroupControl1.AppearanceCaption.Options.UseBorderColor = True
            GroupControl1.AppearanceCaption.Options.UseForeColor = True
            GroupControl1.Text = "Normal"

            ' تنسيق GroupControl2
            GroupControl2.Appearance.BackColor = Color.FromArgb(22, 26, 30) ' لون Binance للبطاقات
            GroupControl2.Appearance.BorderColor = Color.FromArgb(30, 35, 40) ' حدود رمادية داكنة
            GroupControl2.Appearance.ForeColor = Color.White
            GroupControl2.Appearance.Options.UseBackColor = True
            GroupControl2.Appearance.Options.UseBorderColor = True
            GroupControl2.Appearance.Options.UseForeColor = True

            GroupControl2.AppearanceCaption.BackColor = Color.FromArgb(22, 26, 30)
            GroupControl2.AppearanceCaption.BorderColor = Color.FromArgb(30, 35, 40)
            GroupControl2.AppearanceCaption.ForeColor = Color.White
            GroupControl2.AppearanceCaption.Options.UseBackColor = True
            GroupControl2.AppearanceCaption.Options.UseBorderColor = True
            GroupControl2.AppearanceCaption.Options.UseForeColor = True
            GroupControl2.Text = "Encoded"

            ' تنسيق LetterEncoder_Richtext - مثل حقول الإدخال في Binance
            LetterEncoder_Richtext.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_Richtext.ForeColor = Color.White
            LetterEncoder_Richtext.BorderStyle = BorderStyle.None
            LetterEncoder_Richtext.Font = New Font("Segoe UI", 9.5F)

            ' تنسيق LetterEncoder_Richtext2 - مع لون النص الأصفر المميز لـ Binance
            LetterEncoder_Richtext2.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_Richtext2.ForeColor = Color.FromArgb(240, 185, 11) ' اللون الأصفر الذهبي لـ Binance
            LetterEncoder_Richtext2.BorderStyle = BorderStyle.None
            LetterEncoder_Richtext2.Font = New Font("Segoe UI", 9.5F)

            ' تنسيق LetterEncoder_txtFilepath - مثل حقول البحث في Binance
            LetterEncoder_txtFilepath.Properties.Appearance.BackColor = Color.FromArgb(30, 35, 40)
            LetterEncoder_txtFilepath.Properties.Appearance.ForeColor = Color.White
            LetterEncoder_txtFilepath.Properties.Appearance.Options.UseBackColor = True
            LetterEncoder_txtFilepath.Properties.Appearance.Options.UseForeColor = True
            LetterEncoder_txtFilepath.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
            LetterEncoder_txtFilepath.Properties.Appearance.Font = New Font("Segoe UI", 9)



            Debug.WriteLine("Binance style applied successfully")
        Catch ex As Exception
            Debug.WriteLine($"Error applying Binance style: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub

    ''' <summary>
    ''' تنسيق الزر بأسلوب Binance (حواف فقط)
    ''' </summary>
    Private Sub FormatBinanceButton(button As DevExpress.XtraEditors.SimpleButton, tooltipText As String)
        Try
            ' تعيين نص التلميح
            button.ToolTip = tooltipText

            ' تعيين خصائص الزر - أسلوب Binance
            button.Appearance.BackColor = Color.FromArgb(40, 40, 40) ' خلفية داكنة
            button.Appearance.ForeColor = Color.White ' نص أبيض
            button.Cursor = Cursors.Hand
            button.Appearance.Options.UseBackColor = True
            button.Appearance.Options.UseForeColor = True

            ' تعيين حواف الزر باللون الأصفر
            button.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
            button.Appearance.BorderColor = Color.FromArgb(96, 96, 96) ' لون الحواف العادي
            button.Appearance.Options.UseBorderColor = True

            ' تعيين خصائص الزر عند المرور فوقه
            button.Appearance.Options.UseFont = True
            button.Appearance.Font = New Font("Segoe UI", 9, FontStyle.Regular)

            ' تعيين خصائص الزر عند الضغط عليه
            button.PaintStyle = DevExpress.XtraEditors.Controls.PaintStyles.Light

            ' تعيين الحواف المنحنية
            button.LookAndFeel.UseDefaultLookAndFeel = False
            button.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat

            ' تعيين خصائص الزر عند المرور فوقه - تغيير لون الحواف فقط
            button.AppearanceHovered.BorderColor = Color.FromArgb(254, 219, 65) ' لون الحواف الأصفر عند المرور
            button.AppearanceHovered.Options.UseBorderColor = True

            ' تعيين خصائص الزر عند الضغط عليه
            button.AppearancePressed.BorderColor = Color.FromArgb(254, 219, 65) ' لون الحواف الأصفر عند الضغط
            button.AppearancePressed.Options.UseBorderColor = True
        Catch ex As Exception
            Debug.WriteLine($"Error formatting button: {ex.Message}")
            Debug.WriteLine(ex.StackTrace)
        End Try
    End Sub
    Private Sub LetterEncoder_bntEncrypt_Click(sender As Object, e As EventArgs) Handles LetterEncoder_bntEncrypt.Click
        Try
            Dim originalHtml As String = LetterEncoder_Richtext.Text
            If String.IsNullOrWhiteSpace(originalHtml) Then
                XtraMessageBox.Show("Please select an HTML file first!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If
            ' 🔹 استثناء الشعار من التشفير:
            Dim patternImg As String = "(<img[^>]*>)"
            Dim imgMatches As MatchCollection = Regex.Matches(originalHtml, patternImg)
            ' حفظ الشعار قبل التشفير
            Dim imgTags As New List(Of String)
            For Each match As Match In imgMatches
                imgTags.Add(match.Value)
            Next

            ' حفظ وسوم الصور المحتملة الأخرى (مثل img مع سمات مختلفة)
            Dim patternImgAlt As String = "(<img\s+[^>]*src\s*=\s*[""'][^""']*[""'][^>]*>)"
            Dim imgMatchesAlt As MatchCollection = Regex.Matches(originalHtml, patternImgAlt)
            For Each match As Match In imgMatchesAlt
                If Not imgTags.Contains(match.Value) Then
                    imgTags.Add(match.Value)
                End If
            Next
            ' 🔹 التشفير مع التأكد من أنه لا يسبب أخطاء
            Dim pattern As String = "(>)([^<>]+?)(<)"
            Dim encodedHtml As String = Regex.Replace(originalHtml, pattern, AddressOf EncryptHtmlText, RegexOptions.Singleline)
            ' 🔹 إعادة الشعار إلى مكانه في النص المشفر
            ' استخدام تعبير نمطي أكثر دقة للعثور على وسوم الصور المشفرة واستبدالها بالوسوم الأصلية
            For Each imgTag As String In imgTags
                ' استخراج src من وسم الصورة الأصلي
                Dim srcMatch As Match = Regex.Match(imgTag, "src\s*=\s*[""']([^""']*)[""']")
                If srcMatch.Success Then
                    Dim srcValue As String = srcMatch.Groups(1).Value
                    ' البحث عن وسم الصورة المشفر الذي يحتوي على نفس src
                    Dim encodedImgPattern As String = $"<img[^>]*src\s*=\s*[""']{Regex.Escape(srcValue)}[""'][^>]*>"
                    Dim encodedImgMatch As Match = Regex.Match(encodedHtml, encodedImgPattern)
                    If encodedImgMatch.Success Then
                        ' استبدال وسم الصورة المشفر بالوسم الأصلي
                        encodedHtml = encodedHtml.Replace(encodedImgMatch.Value, imgTag)
                    End If
                Else
                    ' إذا لم يتم العثور على src، استخدم الطريقة البسيطة
                    encodedHtml = encodedHtml.Replace(imgTag, imgTag)
                End If
            Next

            ' 🔹 تحسين وسوم الصور لعملاء البريد الإلكتروني
            ' إضافة سمات إضافية لتحسين توافق الصور مع عملاء البريد الإلكتروني
            Dim imgFixPattern As String = "<img([^>]*)>"
            encodedHtml = Regex.Replace(encodedHtml, imgFixPattern, AddressOf EnhanceImageTag)
            ' 🔹 تحسين هيكلة HTML قبل التشفير
            If encodedHtml.Contains("<!DOCTYPE html>") Then
                Dim insertIndex As Integer = encodedHtml.IndexOf("<!DOCTYPE html>") + "<!DOCTYPE html>".Length
                encodedHtml = encodedHtml.Insert(insertIndex, vbCrLf & "<html lang='en'>" & vbCrLf & "<head>" & vbCrLf & "<meta charset='UTF-8'>" & vbCrLf & "<meta name='viewport' content='width=device-width, initial-scale=1.0'>" & vbCrLf & "<style type='text/css'>" & vbCrLf & "body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; text-align: center; direction: ltr; }" & vbCrLf & "img { max-width: none !important; width: auto !important; height: auto !important; display: inline-block !important; }" & vbCrLf & "table { border-collapse: collapse; }" & vbCrLf & "td { padding: 0; }" & vbCrLf & "</style>" & vbCrLf & "</head>")
            Else
                encodedHtml = "<!DOCTYPE html>" & vbCrLf & "<html lang='en'>" & vbCrLf & "<head>" & vbCrLf & "<meta charset='UTF-8'>" & vbCrLf & "<meta name='viewport' content='width=device-width, initial-scale=1.0'>" & vbCrLf & "<style type='text/css'>" & vbCrLf & "body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; text-align: center; direction: ltr; }" & vbCrLf & "img { max-width: none !important; width: auto !important; height: auto !important; display: inline-block !important; }" & vbCrLf & "table { border-collapse: collapse; }" & vbCrLf & "td { padding: 0; }" & vbCrLf & "</style>" & vbCrLf & "</head>" & vbCrLf & encodedHtml
            End If
            If Not encodedHtml.Contains("<body>") Then
                encodedHtml &= vbCrLf & "<body>"
            End If
            If Not encodedHtml.Contains("</html>") Then
                encodedHtml &= vbCrLf & "</body>" & vbCrLf & "</html>"
            End If
            ' 🔹 إزالة الـ &nbsp; بعد التشفير (بجميع أشكالها المحتملة)
            encodedHtml = encodedHtml.Replace("&nbsp;", " ")
            encodedHtml = encodedHtml.Replace("&amp;nbsp;", " ")
            encodedHtml = encodedHtml.Replace("&amp;amp;nbsp;", " ")

            ' استبدال أي تشفير HTML آخر للمسافة
            encodedHtml = encodedHtml.Replace("&#160;", " ")
            encodedHtml = encodedHtml.Replace("&#xA0;", " ")
            ' 🔹 تنسيق الرسالة
            encodedHtml = Regex.Replace(encodedHtml, "(<p[^>]*>)([^<]+)(</p>)", "<p style='margin: 20px 0; padding: 10px;'>$2</p>")
            encodedHtml = Regex.Replace(encodedHtml, "(<h1[^>]*>)([^<]+)(</h1>)", "<h1 style='font-size: 24px; margin-bottom: 10px;'>$2</h1>")
            ' تحديث محتوى RichTextBox2
            LetterEncoder_Richtext2.Text = encodedHtml
            XtraMessageBox.Show("Successful Encryption!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LetterEncoder_bntEncrypt.Enabled = False
            LetterEncoder_bntSave.Enabled = True
        Catch ex As Exception
            XtraMessageBox.Show("An error occurred during encryption: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Function EncryptHtmlText(m As Match) As String
        Dim textOnly As String = m.Groups(2).Value
        If String.IsNullOrEmpty(textOnly) Then Return m.Value

        ' استبدال &nbsp; بمسافة عادية قبل التشفير
        textOnly = textOnly.Replace("&nbsp;", " ")
        textOnly = textOnly.Replace("&amp;nbsp;", " ")
        textOnly = textOnly.Replace("&#160;", " ")
        textOnly = textOnly.Replace("&#xA0;", " ")

        ' ⚠️ قائمة الكلمات المحجوزة التي لا يجب تشفيرها
        For Each keyword As String In dynamicKeywords
            If textOnly.Contains(keyword) Then
                Return m.Value
            End If
        Next
        ' ⚠️ عدم تشفير `<!DOCTYPE html>` أو `<html>` أو `<meta>` أو `<head>` أو `<style>` أو `logo`
        If textOnly.ToLower().Contains("logo") OrElse
           textOnly.Contains("<!DOCTYPE html>") OrElse
           textOnly.Contains("<html") OrElse
           textOnly.Contains("<meta") OrElse
           textOnly.Contains("<head") OrElse
           textOnly.Contains("<style") OrElse
           textOnly.Contains("<button") Then
            Return m.Value
        End If
        ' 🔥 التشفير العشوائي للحروف مع إدخال عناصر غير مرئية
        Dim newText As New StringBuilder()
        Dim randomChars As String() = {
            "⚚", "ꙮ", "⟁", "⟆", "꧄", "〶", "⫷", "࿃", "𖹭", "⻱", "𐡷", "⿻", "𑁯", "〠", "𓁿", "⸘", "𖣘", "⟴", "𓆏", "𖬷", "𓊽", "⿰",
            "𓂀", "𑄢", "⿴", "⼴", "⏃", "⏚", "ꖎ", "𑁍", "⳨", "𖠿", "𑗘", "⛶", "𑄣", "⿱", "𑄦", "ꕧ", "𓉡", "𖥔", "𑗋", "𑗌", "⏁",
            "𓅓", "ꖺ", "⨷", "𑄇", "𐎂", "⩿", "𑄘", "ⳙ", "𖬻", "⸎", "Ⳇ", "⩣", "⻌", "𐰀", "𑄽", "Ⱖ", "𓆰", "ⱽ", "⧫", "⭆", "𓅱",
            "𖨵", "⦳", "𑊩", "⩆", "⛧", "ⴽ", "𖠵", "⸿", "ꖴ", "𐰗", "⨄", "𐡟", "⨎", "𑄛", "ꖌ", "ⷦ", "ⲿ", "𑄞", "⛨", "𑗏", "⭑", "日", "本",
            "語", "火", "山", "水", "神", "魔", "鬼", "霊", "風", "雷", "夢", "死", "光", "影", "星", "月", "愛", "夜", "血", "剣",
            "あ", "い", "う", "え", "お", "か", "き", "く", "け", "こ", "さ", "し", "す", "せ", "そ",
            "ア", "イ", "ウ", "エ", "オ", "カ", "キ", "ク", "ケ", "コ", "サ", "シ", "ス", "セ", "ソ", "𐤀", "𐤁", "𐤂", "𐤃", "𐤄",
            "𐤅", "𐤆", "𐤇", "𐤈x2x2x2x", "𐤉", "𐤊", "𐤋", "𐤌", "𐤍", "𐤎", "𐤏", "x2x2x2x𐤐", "𐤑", "𐤒", "𐤓", "𐤔", "𒀀", "𒀁", "𒀂", "𒀃", "𒀄",
            "𒀅", "𒀆", "𒀇", "𒀈", "𒀉", "𒀊", "𒀋", "𒀌", "𒀍", "𒀎", "𒀏", "𒀐", "𒀑", "Ⴀ", "Ⴁ", "Ⴂ", "Ⴃ", "Ⴄ",
            "Ⴅ", "Ⴆ", "Ⴇ", "Ⴈ", "Ⴉ", "Ⴊ", "Ⴋ", "Ⴌ", "Ⴍ", "Ⴎ", "Ⴏ", "Ⴐ", "Ⴑ", "Ⴒ", "Ⴓ", "Ⴔ", "Ⴕ", "ༀ", "༁", "༂", "༃", "༄",
            "༅", "༆", "༇", "༈", "༉", "༊", "་", "༌", "།", "༎", "༏", "༐", "༑", "༒", "༓", "∞", "∑", "√", "∫", "Δ", "Ω", "Ψ", "∇",
            "⊗", "⊕", "∴", "∵", "≈", "≠", "≤", "≥", "∂", "∃", "∀", "∈", "𐎀", "𐎁", "𐎂", "𐎃", "𐎄", "𐎅", "𐎆", "𐎇", "𐎈",
            "𐎉", "𐎊", "𐎋", "𐎌", "𐎍", "𐎎", "𐎏", "𐎐", "𐎑", "𐎒", "𐎓", "𒀀𒈾", "𒂍𒁲", "𒀉𒌓", "𒆠𒈠", "𒌓𒄒", "𒀭𒈹",
            "𒅗𒄿", "𒀜𒂖", "𒈾𒉈", "𒋼𒀭", "𒅖𒆠𒀀", "𒌓𒌝", "𒄿𒀸", "𒀀𒉿",
            "𒅗𒂍", "𒀭𒌓", "𒀭𒁍", "𒋡𒆳", "𒀀𒀀", "𒊺𒀜", "𒀀𒈾𒅗", "𒌓𒀀𒅗", "𒀭𒀜𒉿", "𒀜𒉈𒀭", "𒄿𒆠𒉿", "𒁍𒅗𒀜",
            "𒆳𒉺𒀀", "𒉺𒋾𒀀", "𒀜𒉿𒀀", "𒋗𒉺𒀀", "𐌀𐌉𐌕𐌀𐌍", "𐌇𐌀𐌏𐌉𐌋", "𐌕𐌀𐌊𐌄", "𐌌𐌀𐌅𐌄", "𐌎𐌌𐌄𐌉",
            "𐌑𐌀𐌔𐌄", "𐌋𐌀𐌓𐌕", "𐌁𐌀𐌑𐌄", "𐌃𐌄𐌓𐌅", "𐌉𐌊𐌀𐌍", "𐘀𐘁𐘂", "𐘃𐘄𐘅", "𐘆𐘇𐘈", "𐘉𐘊𐘋", "𐘌𐘍𐘎", "𐘏𐘐𐘑", "𐘒𐘓𐘔", "𐘕𐘖𐘗", "𐘘𐘙𐘚", "𐘛𐘜𐘝",
            "Qaz'athuur", "Xil'vren", "Thry'gothan", "Yj'ormin", "Glith'kaarn", "Vrex'zulon", "Kha'theroth", "Zyx'lmora",
            "Dra'voorth", "Myth'quorin", "⨀⨁⨂⨃⨄", "⏚⏛⏜⏝⏞", "ⵠⵡⵢⵣⵤ", "𑄢𑄣𑄤𑄥𑄦", "𝌆𝌇𝌈𝌉𝌊", "𐎀𐎁𐎂𐎃𐎄", "𑗃𑗄𑗅𑗆𑗇", "𝍁𝍂𝍃𝍄𝍅", "𓆲𓆳𓆴𓆵𓆶", "ⵞⵟⵠⵡⵢ", "[-rrandomletters-]", "[-rrandomletters𓄀𓄁-]", "[-rrandomletters𓆣𓆑𓆘𓆤-]", " 𓅃 𓏏𓊪 𓂝", "ḥr rꜣ tꜣ mꜣꜥ ḫrw", "ḥr f ꜥ mꜣꜥ-ḫrw", "ḥr tt ꜣḫw", "ḥr jrjt snṯr mꜣꜥ ḫrw", "ḥr jrjt tp f", "𓅃 𓁹 𓂝 𓆑 � 𓊪 𓂻 𓏏 𓍱", "𓂀 𓃭 𓇳 𓏏 𓊪 𓏤 𓂝 𓁷 𓏤 𓆑 𓄿 𓂤", "𓆣 𓏏 𓎛 𓄿 𓍱 𓏏 𓆑 𓂤 𓁷", "𐰦𐰯𐰹𐰲", "𖣘⟴𓆏", "⟴𓆏", "𐌁𐌀𐌑𐌄𓆏", "𒀜𒉿𒀀𓄿", "𓄿𒀭", "𓄿𒀀", "𓄿𐎑", "𓄿∀", "𐤀 𐤁 𐤂 𐤃 𐤄 𐤅 𐤆 𐤇 𐤈 𐤉 𐤊 𐤋 𐤌 𐤍 𐤎 𐤏 𐤐 𐤑 𐤒 𐤓 𐤔 𐤕", "ᚷᚨᚺ ᚱᚨᛁᛜ ᛞᛖ ᚷᛟᚾᛟ ᛁᚨᛞᛈᛁᛚ", "ᛉᛟᛞᚨᚲᚨᚱᛖ ᛖᚲᚨ ᛟᛞ ᛉᛟᛞᚨᛗᛖᚱᚨᚾᚢ", "ᛗᛁᚲᛗᚨ! ᚷᛟᚺᛟ ᛈᛖᛁ-ᛁᚨᛞ",
            "ᛟᚱᛟ ᛁᛒᚨᚺ ᚨᛟᛉᛈᛁ ᛗᚨᛟᛉ ᛟᚦ ᛏᛟᚾᚲᛁᛚ", "+6564161⻱$$$$$$$##@!@****$$$$$$$$$", "X2x2x2x2x2x2x2x", "X6x8x6x8x7x6x5x4x7x6", "XOXOXOXOXOXOXOXOXOXOXOX", "XQXQXQXQXQXQXQXQXQXQXQ", "𒀐𓄿", "𝌊𓄿", "Xyloquent", "Zytharian", "Quixotrix", "Vexillium", "Pyroclast", "Nexorvian", "Mythanoid", "Glyptonic",
    "Klyptorix", "Xanthozor", "Qwyllith", "Vorphalax", "Zyphorian", "Krythazix", "Nyxlithic", "Pyrothium",
    "Xylophane", "Qwantzor", "Vexomorph", "Zorphalon", "Klythanar", "Nexorith", "Myxalith", "Qworphazm",
    "Xanthorix", "Vorphalion", "Zyphanar", "Klyxalith", "Pyxarion", "Nyxtorium", "Glyxarion", "Qwyllarion",
    "Xantholith", "Vorphazium", "Zyphanix", "Klyxarion", "Nexorazm", "Myxarion", "Pyrotharion", "Qwantzium",
    "Xylopharion", "Vexillith", "Zorphalith", "Krythanar", "Nyxtorix", "Glypharion", "Qwyllith", "Xanthorith", "X@X@X@", "Y#Y#Y#", "Z$Z$Z$", "A%A%A%", "B^B^B^", "C&C&C&", "D*D*D*", "E(E(E(", "F)F)F)", "G_G_G_",
    "H+H+H+", "I-I-I-", "J=J=J=", "K[K[K[", "L]L]L]", "M{M{M}", "N|N|N|", "O\O\O\", "P;P;P;", "Q:Q:Q:",
    "R'R'R'", "S""S""S""", "T,T,T,", "U<U<U<", "V>V>V>", "W?W?W?", "X.X.X.", "Y/Y/Y/", "Z`Z`Z`", "A~A~A~", "X2X3X5", "X3X4X6", "X5X7X9", "X1X2X3", "X4X5X6", "X7X8X9", "X0X1X2", "X9X8X7", "X6X5X4", "X3X2X1",
    "Z9Z8Z7", "Z6Z5Z4", "Z3Z2Z1", "Z0Z1Z2", "Z3Z4Z5", "Z6Z7Z8", "Z9Z0Z1", "Z2Z3Z4", "Z5Z6Z7", "Z8Z9Z0",
    "Y1Y3Y5", "Y2Y4Y6", "Y7Y9Y1", "Y8Y0Y2", "Y3Y5Y7", "Y4Y6Y8", "Y9Y1Y3", "Y0Y2Y4", "Y5Y7Y9", "Y6Y8Y0",
    "VxVxVx", "WxWxWx", "UxUxUx", "TxTxTx", "SxSxSx", "RxRxRx", "QxQxQx", "PxPxPx", "OxOxOx", "NxNxNx",
    "MkMkMk", "LkLkLk", "KkKkKk", "JkJkJk", "IkIkIk", "HkHkHk", "GkGkGk", "FkFkFk", "EkEkEk", "DkDkDk",
    "CvCvCv", "BvBvBv", "AvAvAv", "ZvZvZv", "YvYvYv", "XvXvXv", "WvWvWv", "VvVvVv", "UvUvUv", "TvTvTv",
    "QwQwQw", "WqWqWq", "ErErEr", "ReReRe", "TtTtTt", "YyYyYy", "UuUuUu", "IiIiIi", "OoOoOo", "PpPpPp",
    "AaAaAa", "SsSsSs", "DdDdDd", "FfFfFf", "GgGgGg", "HhHhHh", "JjJjJj", "KkKkKk", "LlLlLl", "ZzZzZz",
    "XxXxXx", "CcCcCc", "VvVvVv", "BbBbBb", "NnNnNn", "MmMmMm", "QqQqQq", "WwWwWw", "EeEeEe", "RrRrRr"
        }
        Dim rand As New Random()
        For Each ch As Char In textOnly
            newText.Append(ch)
            ' 🔥 إدخال عدد عشوائي من الحروف الغير مرئية
            Dim numRandomChars As Integer = rand.Next(1, 3)
            For i As Integer = 1 To numRandomChars
                Dim randomChar As String = randomChars(rand.Next(randomChars.Length))
                newText.Append("<span style='display:none !important;font-size:0 !important;height:0 !important;width:0 !important;'>" & randomChar & "</span>")
            Next
        Next
        Return m.Groups(1).Value & newText.ToString() & m.Groups(3).Value
    End Function
    Private Sub LetterEncoder_bntSave_Click(sender As Object, e As EventArgs) Handles LetterEncoder_bntSave.Click
        If String.IsNullOrWhiteSpace(LetterEncoder_Richtext2.Text) Then
            XtraMessageBox.Show("Please encrypt your letter first!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        ' 🔹 تنظيف النص من الكلمات غير المرغوب فيها
        Dim wordsToRemove As String() = {"<script>", "</script>", "<iframe>", "</iframe>", "<object>", "</object>"}
        Dim cleanedText As String = LetterEncoder_Richtext2.Text
        For Each word In wordsToRemove
            cleanedText = cleanedText.Replace(word, "")
        Next

        ' 🔹 استبدال &nbsp; بمسافة عادية
        cleanedText = cleanedText.Replace("&nbsp;", " ")

        ' 🔹 استبدال &nbsp; المشفرة أيضًا (قد تكون مشفرة بأشكال مختلفة)
        cleanedText = Regex.Replace(cleanedText, "&amp;nbsp;", " ")
        cleanedText = Regex.Replace(cleanedText, "&amp;amp;nbsp;", " ")

        ' 🔹 تجهيز محتوى ملف HTML مع إضافة اسم للعنوان
        ' إضافة تعليمات CSS لضمان عدم تغيير حجم الصور وتحسين التوافق مع عملاء البريد الإلكتروني
        Dim fullHtmlContent As String = $"<!DOCTYPE html>
<html lang='en'>
<head>
    <title>Dark Letter</title>
    <meta http-equiv='Content-Type' content='text/html; charset=utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <!--[if !mso]><!-->
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <!--<![endif]-->
    <style type='text/css'>
        /* تحسين التوافق مع عملاء البريد الإلكتروني */
        body, html {{ margin: 0; padding: 0; width: 100% !important; }}
        body {{ font-family: Arial, Helvetica, sans-serif; line-height: 1.6; }}
        img {{ border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none; max-width: none !important; width: auto !important; display: inline-block !important; }}
        table {{ border-collapse: collapse !important; }}
        td {{ padding: 0; }}
        /* تحسين التوافق مع Outlook */
        table, td {{ mso-table-lspace: 0pt; mso-table-rspace: 0pt; }}
        img {{ -ms-interpolation-mode: bicubic; }}
    </style>
</head>
<body>
{cleanedText}
</body>
</html>"
        ' 🔹 تحديد المسار
        Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
        Dim saveFolder As String = Path.Combine(desktopPath, "Dark Encoded Letter")
        If Not Directory.Exists(saveFolder) Then Directory.CreateDirectory(saveFolder)
        ' 🔹 تجهيز اسم الملف
        Dim baseFileName As String = "Dark Encoded Letter"
        Dim filePath As String = Path.Combine(saveFolder, baseFileName & ".html")
        Dim counter As Integer = 1
        ' 🔹 لو الملف موجود عدل الاسم
        While File.Exists(filePath)
            filePath = Path.Combine(saveFolder, $"{baseFileName}{counter}.html")
            counter += 1
        End While
        Try
            ' 🔹 حفظ الملف
            File.WriteAllText(filePath, fullHtmlContent, Encoding.UTF8)
            ' 🔹 عرض رسالة تأكيد
            XtraMessageBox.Show($"The file has been successfully saved to: {filePath}", "Saved Successfully", MessageBoxButtons.OK, MessageBoxIcon.Information)
            ' 🔹 فتح المجلد مع تحديد الملف
            Process.Start("explorer.exe", "/select," & filePath)
            ' 🔹 إعادة تعيين الحقول
            LetterEncoder_txtFilepath.ResetText()
            LetterEncoder_Richtext.ResetText()
            LetterEncoder_Richtext2.ResetText()
            LetterEncoder_bntEncrypt.Enabled = False
            LetterEncoder_bntSave.Enabled = False
        Catch ex As Exception
            XtraMessageBox.Show("Error saving file: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    ''' <summary>
    ''' تحسين وسوم الصور لعملاء البريد الإلكتروني
    ''' </summary>
    Private Function EnhanceImageTag(m As Match) As String
        Try
            Dim imgAttributes As String = m.Groups(1).Value

            ' استخراج قيمة src
            Dim srcMatch As Match = Regex.Match(imgAttributes, "src\s*=\s*[""']([^""']*)[""']")
            If Not srcMatch.Success Then
                Return m.Value ' إذا لم يتم العثور على src، أعد الوسم كما هو
            End If

            Dim srcValue As String = srcMatch.Groups(1).Value

            ' استخراج قيمة alt إذا وجدت
            Dim altValue As String = ""
            Dim altMatch As Match = Regex.Match(imgAttributes, "alt\s*=\s*[""']([^""']*)[""']")
            If altMatch.Success Then
                altValue = altMatch.Groups(1).Value
            End If

            ' استخراج قيمة width و height إذا وجدت
            Dim widthValue As String = ""
            Dim heightValue As String = ""
            Dim widthMatch As Match = Regex.Match(imgAttributes, "width\s*=\s*[""']?([^""'\s>]+)[""']?")
            Dim heightMatch As Match = Regex.Match(imgAttributes, "height\s*=\s*[""']?([^""'\s>]+)[""']?")

            If widthMatch.Success Then
                widthValue = widthMatch.Groups(1).Value
            End If

            If heightMatch.Success Then
                heightValue = heightMatch.Groups(1).Value
            End If

            ' إنشاء وسم الصورة المحسن
            Dim enhancedImgTag As String = "<img"

            ' إضافة السمات الأساسية
            enhancedImgTag &= $" src=""{srcValue}"""
            enhancedImgTag &= $" alt=""{If(String.IsNullOrEmpty(altValue), "Image", altValue)}"""

            ' إضافة width و height إذا كانت موجودة
            If Not String.IsNullOrEmpty(widthValue) Then
                enhancedImgTag &= $" width=""{widthValue}"""
            End If

            If Not String.IsNullOrEmpty(heightValue) Then
                enhancedImgTag &= $" height=""{heightValue}"""
            End If

            ' إضافة سمات إضافية لتحسين التوافق مع عملاء البريد الإلكتروني
            enhancedImgTag &= " border=""0"" style=""border:0; outline:none; text-decoration:none; display:inline-block; max-width:none !important;"""

            ' إضافة سمات MSO لـ Outlook
            enhancedImgTag &= " mso-line-height-rule=""exactly"""

            enhancedImgTag &= ">"

            Return enhancedImgTag
        Catch ex As Exception
            ' في حالة حدوث خطأ، أعد الوسم الأصلي
            Return m.Value
        End Try
    End Function

    Private Sub TimerWatcher_Tick(sender As Object, e As EventArgs) Handles TimerWatcher.Tick
        ' تحقق من محتوى الـ RichTextBox
        If String.IsNullOrWhiteSpace(LetterEncoder_Richtext.Text) AndAlso
           String.IsNullOrWhiteSpace(LetterEncoder_Richtext2.Text) Then
            LetterEncoder_bntClearAll.Enabled = False
        Else
            LetterEncoder_bntClearAll.Enabled = True
        End If
    End Sub
End Class