﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmValidationOffice365
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmValidationOffice365))
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.LayoutControl1 = New DevExpress.XtraLayout.LayoutControl()
        Me.BntSave = New DevExpress.XtraEditors.SimpleButton()
        Me.txtSleepTime = New DevExpress.XtraEditors.TextEdit()
        Me.BntResetAll = New DevExpress.XtraEditors.SimpleButton()
        Me.btnStop = New DevExpress.XtraEditors.SimpleButton()
        Me.btnStart = New DevExpress.XtraEditors.SimpleButton()
        Me.btnSelectFile = New DevExpress.XtraEditors.SimpleButton()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem1 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.EmptySpaceItem1 = New DevExpress.XtraLayout.EmptySpaceItem()
        Me.LayoutControlItem2 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem3 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem4 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem5 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem11 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.ProgressBarControl1 = New DevExpress.XtraEditors.ProgressBarControl()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.LayoutControl2 = New DevExpress.XtraLayout.LayoutControl()
        Me.txtFilePath = New DevExpress.XtraEditors.TextEdit()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn2 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn3 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn4 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn5 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.GridColumn6 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlItem6 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem8 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem7 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem9 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.LayoutControlItem10 = New DevExpress.XtraLayout.LayoutControlItem()
        Me.BackgroundWorker1 = New System.ComponentModel.BackgroundWorker()
        Me.Panel1.SuspendLayout()
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl1.SuspendLayout()
        CType(Me.txtSleepTime.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel2.SuspendLayout()
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel3.SuspendLayout()
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.LayoutControl2.SuspendLayout()
        CType(Me.txtFilePath.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.LayoutControl1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(972, 90)
        Me.Panel1.TabIndex = 0
        '
        'LayoutControl1
        '
        Me.LayoutControl1.Controls.Add(Me.BntSave)
        Me.LayoutControl1.Controls.Add(Me.txtSleepTime)
        Me.LayoutControl1.Controls.Add(Me.BntResetAll)
        Me.LayoutControl1.Controls.Add(Me.btnStop)
        Me.LayoutControl1.Controls.Add(Me.btnStart)
        Me.LayoutControl1.Controls.Add(Me.btnSelectFile)
        Me.LayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl1.Name = "LayoutControl1"
        Me.LayoutControl1.Root = Me.Root
        Me.LayoutControl1.Size = New System.Drawing.Size(972, 90)
        Me.LayoutControl1.TabIndex = 0
        Me.LayoutControl1.Text = "LayoutControl1"
        '
        'BntSave
        '
        Me.BntSave.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntSave.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.Appearance.Options.UseBackColor = True
        Me.BntSave.Appearance.Options.UseBorderColor = True
        Me.BntSave.Appearance.Options.UseFont = True
        Me.BntSave.Appearance.Options.UseForeColor = True
        Me.BntSave.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntSave.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntSave.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntSave.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntSave.AppearanceDisabled.Options.UseBackColor = True
        Me.BntSave.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntSave.AppearanceDisabled.Options.UseFont = True
        Me.BntSave.AppearanceDisabled.Options.UseForeColor = True
        Me.BntSave.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntSave.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntSave.AppearanceHovered.Options.UseBackColor = True
        Me.BntSave.AppearanceHovered.Options.UseBorderColor = True
        Me.BntSave.AppearanceHovered.Options.UseForeColor = True
        Me.BntSave.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntSave.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntSave.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntSave.AppearancePressed.Options.UseBackColor = True
        Me.BntSave.AppearancePressed.Options.UseBorderColor = True
        Me.BntSave.AppearancePressed.Options.UseForeColor = True
        Me.BntSave.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Save_Image
        Me.BntSave.Location = New System.Drawing.Point(663, 16)
        Me.BntSave.Name = "BntSave"
        Me.BntSave.Size = New System.Drawing.Size(123, 38)
        Me.BntSave.StyleController = Me.LayoutControl1
        Me.BntSave.TabIndex = 68
        Me.BntSave.Text = "Save"
        '
        'txtSleepTime
        '
        Me.txtSleepTime.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtSleepTime.EditValue = "1000"
        Me.txtSleepTime.Location = New System.Drawing.Point(876, 16)
        Me.txtSleepTime.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtSleepTime.Name = "txtSleepTime"
        Me.txtSleepTime.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtSleepTime.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtSleepTime.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtSleepTime.Properties.Appearance.Options.UseBackColor = True
        Me.txtSleepTime.Properties.Appearance.Options.UseFont = True
        Me.txtSleepTime.Properties.Appearance.Options.UseForeColor = True
        Me.txtSleepTime.Properties.ContextImageOptions.SvgImage = CType(resources.GetObject("txtSleepTime.Properties.ContextImageOptions.SvgImage"), DevExpress.Utils.Svg.SvgImage)
        Me.txtSleepTime.Properties.NullValuePrompt = "Sleep Time"
        Me.txtSleepTime.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtSleepTime.Size = New System.Drawing.Size(80, 44)
        Me.txtSleepTime.StyleController = Me.LayoutControl1
        Me.txtSleepTime.TabIndex = 67
        '
        'BntResetAll
        '
        Me.BntResetAll.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetAll.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetAll.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntResetAll.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetAll.Appearance.Options.UseBackColor = True
        Me.BntResetAll.Appearance.Options.UseBorderColor = True
        Me.BntResetAll.Appearance.Options.UseFont = True
        Me.BntResetAll.Appearance.Options.UseForeColor = True
        Me.BntResetAll.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntResetAll.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntResetAll.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntResetAll.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntResetAll.AppearanceDisabled.Options.UseBackColor = True
        Me.BntResetAll.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntResetAll.AppearanceDisabled.Options.UseFont = True
        Me.BntResetAll.AppearanceDisabled.Options.UseForeColor = True
        Me.BntResetAll.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntResetAll.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetAll.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntResetAll.AppearanceHovered.Options.UseBackColor = True
        Me.BntResetAll.AppearanceHovered.Options.UseBorderColor = True
        Me.BntResetAll.AppearanceHovered.Options.UseForeColor = True
        Me.BntResetAll.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntResetAll.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntResetAll.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntResetAll.AppearancePressed.Options.UseBackColor = True
        Me.BntResetAll.AppearancePressed.Options.UseBorderColor = True
        Me.BntResetAll.AppearancePressed.Options.UseForeColor = True
        Me.BntResetAll.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntResetAll.Location = New System.Drawing.Point(503, 16)
        Me.BntResetAll.Name = "BntResetAll"
        Me.BntResetAll.Size = New System.Drawing.Size(154, 38)
        Me.BntResetAll.StyleController = Me.LayoutControl1
        Me.BntResetAll.TabIndex = 4
        Me.BntResetAll.Text = "Reset All"
        '
        'btnStop
        '
        Me.btnStop.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnStop.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnStop.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnStop.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnStop.Appearance.Options.UseBackColor = True
        Me.btnStop.Appearance.Options.UseBorderColor = True
        Me.btnStop.Appearance.Options.UseFont = True
        Me.btnStop.Appearance.Options.UseForeColor = True
        Me.btnStop.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btnStop.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btnStop.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnStop.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnStop.AppearanceDisabled.Options.UseBackColor = True
        Me.btnStop.AppearanceDisabled.Options.UseBorderColor = True
        Me.btnStop.AppearanceDisabled.Options.UseFont = True
        Me.btnStop.AppearanceDisabled.Options.UseForeColor = True
        Me.btnStop.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btnStop.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnStop.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btnStop.AppearanceHovered.Options.UseBackColor = True
        Me.btnStop.AppearanceHovered.Options.UseBorderColor = True
        Me.btnStop.AppearanceHovered.Options.UseForeColor = True
        Me.btnStop.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnStop.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnStop.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btnStop.AppearancePressed.Options.UseBackColor = True
        Me.btnStop.AppearancePressed.Options.UseBorderColor = True
        Me.btnStop.AppearancePressed.Options.UseForeColor = True
        Me.btnStop.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Stop32x32
        Me.btnStop.Location = New System.Drawing.Point(336, 16)
        Me.btnStop.Name = "btnStop"
        Me.btnStop.Size = New System.Drawing.Size(161, 38)
        Me.btnStop.StyleController = Me.LayoutControl1
        Me.btnStop.TabIndex = 3
        Me.btnStop.Text = "Stop"
        '
        'btnStart
        '
        Me.btnStart.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnStart.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnStart.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnStart.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnStart.Appearance.Options.UseBackColor = True
        Me.btnStart.Appearance.Options.UseBorderColor = True
        Me.btnStart.Appearance.Options.UseFont = True
        Me.btnStart.Appearance.Options.UseForeColor = True
        Me.btnStart.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btnStart.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btnStart.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnStart.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnStart.AppearanceDisabled.Options.UseBackColor = True
        Me.btnStart.AppearanceDisabled.Options.UseBorderColor = True
        Me.btnStart.AppearanceDisabled.Options.UseFont = True
        Me.btnStart.AppearanceDisabled.Options.UseForeColor = True
        Me.btnStart.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btnStart.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnStart.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btnStart.AppearanceHovered.Options.UseBackColor = True
        Me.btnStart.AppearanceHovered.Options.UseBorderColor = True
        Me.btnStart.AppearanceHovered.Options.UseForeColor = True
        Me.btnStart.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnStart.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnStart.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btnStart.AppearancePressed.Options.UseBackColor = True
        Me.btnStart.AppearancePressed.Options.UseBorderColor = True
        Me.btnStart.AppearancePressed.Options.UseForeColor = True
        Me.btnStart.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.btnStart.Location = New System.Drawing.Point(180, 16)
        Me.btnStart.Name = "btnStart"
        Me.btnStart.Size = New System.Drawing.Size(150, 38)
        Me.btnStart.StyleController = Me.LayoutControl1
        Me.btnStart.TabIndex = 2
        Me.btnStart.Text = "Start"
        '
        'btnSelectFile
        '
        Me.btnSelectFile.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnSelectFile.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnSelectFile.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnSelectFile.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnSelectFile.Appearance.Options.UseBackColor = True
        Me.btnSelectFile.Appearance.Options.UseBorderColor = True
        Me.btnSelectFile.Appearance.Options.UseFont = True
        Me.btnSelectFile.Appearance.Options.UseForeColor = True
        Me.btnSelectFile.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btnSelectFile.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btnSelectFile.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btnSelectFile.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnSelectFile.AppearanceDisabled.Options.UseBackColor = True
        Me.btnSelectFile.AppearanceDisabled.Options.UseBorderColor = True
        Me.btnSelectFile.AppearanceDisabled.Options.UseFont = True
        Me.btnSelectFile.AppearanceDisabled.Options.UseForeColor = True
        Me.btnSelectFile.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btnSelectFile.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnSelectFile.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btnSelectFile.AppearanceHovered.Options.UseBackColor = True
        Me.btnSelectFile.AppearanceHovered.Options.UseBorderColor = True
        Me.btnSelectFile.AppearanceHovered.Options.UseForeColor = True
        Me.btnSelectFile.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btnSelectFile.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btnSelectFile.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btnSelectFile.AppearancePressed.Options.UseBackColor = True
        Me.btnSelectFile.AppearancePressed.Options.UseBorderColor = True
        Me.btnSelectFile.AppearancePressed.Options.UseForeColor = True
        Me.btnSelectFile.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.add32x32
        Me.btnSelectFile.Location = New System.Drawing.Point(16, 16)
        Me.btnSelectFile.Name = "btnSelectFile"
        Me.btnSelectFile.Size = New System.Drawing.Size(158, 38)
        Me.btnSelectFile.StyleController = Me.LayoutControl1
        Me.btnSelectFile.TabIndex = 1
        Me.btnSelectFile.Text = "Select List..."
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem1, Me.EmptySpaceItem1, Me.LayoutControlItem2, Me.LayoutControlItem3, Me.LayoutControlItem4, Me.LayoutControlItem5, Me.LayoutControlItem11})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(972, 90)
        Me.Root.TextVisible = False
        '
        'LayoutControlItem1
        '
        Me.LayoutControlItem1.Control = Me.btnSelectFile
        Me.LayoutControlItem1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem1.Name = "LayoutControlItem1"
        Me.LayoutControlItem1.Size = New System.Drawing.Size(164, 50)
        Me.LayoutControlItem1.TextVisible = False
        '
        'EmptySpaceItem1
        '
        Me.EmptySpaceItem1.Location = New System.Drawing.Point(0, 50)
        Me.EmptySpaceItem1.Name = "EmptySpaceItem1"
        Me.EmptySpaceItem1.Size = New System.Drawing.Size(946, 14)
        '
        'LayoutControlItem2
        '
        Me.LayoutControlItem2.Control = Me.btnStart
        Me.LayoutControlItem2.Location = New System.Drawing.Point(164, 0)
        Me.LayoutControlItem2.Name = "LayoutControlItem2"
        Me.LayoutControlItem2.Size = New System.Drawing.Size(156, 50)
        Me.LayoutControlItem2.TextVisible = False
        '
        'LayoutControlItem3
        '
        Me.LayoutControlItem3.Control = Me.btnStop
        Me.LayoutControlItem3.Location = New System.Drawing.Point(320, 0)
        Me.LayoutControlItem3.Name = "LayoutControlItem3"
        Me.LayoutControlItem3.Size = New System.Drawing.Size(167, 50)
        Me.LayoutControlItem3.TextVisible = False
        '
        'LayoutControlItem4
        '
        Me.LayoutControlItem4.Control = Me.BntResetAll
        Me.LayoutControlItem4.Location = New System.Drawing.Point(487, 0)
        Me.LayoutControlItem4.Name = "LayoutControlItem4"
        Me.LayoutControlItem4.Size = New System.Drawing.Size(160, 50)
        Me.LayoutControlItem4.TextVisible = False
        '
        'LayoutControlItem5
        '
        Me.LayoutControlItem5.AppearanceItemCaption.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.LayoutControlItem5.AppearanceItemCaption.Options.UseFont = True
        Me.LayoutControlItem5.Control = Me.txtSleepTime
        Me.LayoutControlItem5.Location = New System.Drawing.Point(776, 0)
        Me.LayoutControlItem5.Name = "LayoutControlItem5"
        Me.LayoutControlItem5.Size = New System.Drawing.Size(170, 50)
        Me.LayoutControlItem5.Text = "Sleep Time:"
        Me.LayoutControlItem5.TextSize = New System.Drawing.Size(68, 19)
        '
        'LayoutControlItem11
        '
        Me.LayoutControlItem11.Control = Me.BntSave
        Me.LayoutControlItem11.Location = New System.Drawing.Point(647, 0)
        Me.LayoutControlItem11.Name = "LayoutControlItem11"
        Me.LayoutControlItem11.Size = New System.Drawing.Size(129, 50)
        Me.LayoutControlItem11.TextVisible = False
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.ProgressBarControl1)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 573)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(972, 29)
        Me.Panel2.TabIndex = 1
        '
        'ProgressBarControl1
        '
        Me.ProgressBarControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ProgressBarControl1.Location = New System.Drawing.Point(0, 0)
        Me.ProgressBarControl1.Name = "ProgressBarControl1"
        Me.ProgressBarControl1.Properties.ShowTitle = True
        Me.ProgressBarControl1.Size = New System.Drawing.Size(972, 29)
        Me.ProgressBarControl1.TabIndex = 0
        '
        'Panel3
        '
        Me.Panel3.Controls.Add(Me.LayoutControl2)
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel3.Location = New System.Drawing.Point(0, 90)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(972, 483)
        Me.Panel3.TabIndex = 2
        '
        'LayoutControl2
        '
        Me.LayoutControl2.Controls.Add(Me.txtFilePath)
        Me.LayoutControl2.Controls.Add(Me.Label3)
        Me.LayoutControl2.Controls.Add(Me.Label1)
        Me.LayoutControl2.Controls.Add(Me.Label2)
        Me.LayoutControl2.Controls.Add(Me.GridControl1)
        Me.LayoutControl2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LayoutControl2.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControl2.Name = "LayoutControl2"
        Me.LayoutControl2.Root = Me.LayoutControlGroup1
        Me.LayoutControl2.Size = New System.Drawing.Size(972, 483)
        Me.LayoutControl2.TabIndex = 0
        Me.LayoutControl2.Text = "LayoutControl2"
        '
        'txtFilePath
        '
        Me.txtFilePath.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtFilePath.EditValue = ""
        Me.txtFilePath.Location = New System.Drawing.Point(62, 16)
        Me.txtFilePath.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtFilePath.Name = "txtFilePath"
        Me.txtFilePath.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtFilePath.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtFilePath.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtFilePath.Properties.Appearance.Options.UseBackColor = True
        Me.txtFilePath.Properties.Appearance.Options.UseFont = True
        Me.txtFilePath.Properties.Appearance.Options.UseForeColor = True
        Me.txtFilePath.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Path16x16
        Me.txtFilePath.Properties.NullValuePrompt = "Path File"
        Me.txtFilePath.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtFilePath.Size = New System.Drawing.Size(444, 30)
        Me.txtFilePath.StyleController = Me.LayoutControl2
        Me.txtFilePath.TabIndex = 68
        '
        'Label3
        '
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label3.Location = New System.Drawing.Point(807, 52)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(149, 49)
        Me.Label3.TabIndex = 7
        '
        'Label1
        '
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label1.Location = New System.Drawing.Point(477, 52)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(324, 49)
        Me.Label1.TabIndex = 6
        '
        'Label2
        '
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(16, 52)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(455, 49)
        Me.Label2.TabIndex = 5
        '
        'GridControl1
        '
        Me.GridControl1.Location = New System.Drawing.Point(16, 107)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(940, 360)
        Me.GridControl1.TabIndex = 4
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1, Me.GridColumn2, Me.GridColumn3, Me.GridColumn4, Me.GridColumn5, Me.GridColumn6})
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'GridColumn1
        '
        Me.GridColumn1.Caption = "#"
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        Me.GridColumn1.Width = 25
        '
        'GridColumn2
        '
        Me.GridColumn2.Caption = "Email"
        Me.GridColumn2.Name = "GridColumn2"
        Me.GridColumn2.Visible = True
        Me.GridColumn2.VisibleIndex = 1
        Me.GridColumn2.Width = 176
        '
        'GridColumn3
        '
        Me.GridColumn3.Caption = "Status"
        Me.GridColumn3.Name = "GridColumn3"
        Me.GridColumn3.Visible = True
        Me.GridColumn3.VisibleIndex = 2
        Me.GridColumn3.Width = 176
        '
        'GridColumn4
        '
        Me.GridColumn4.Caption = "Time"
        Me.GridColumn4.Name = "GridColumn4"
        Me.GridColumn4.Visible = True
        Me.GridColumn4.VisibleIndex = 3
        Me.GridColumn4.Width = 176
        '
        'GridColumn5
        '
        Me.GridColumn5.Caption = "Date"
        Me.GridColumn5.Name = "GridColumn5"
        Me.GridColumn5.Visible = True
        Me.GridColumn5.VisibleIndex = 4
        Me.GridColumn5.Width = 176
        '
        'GridColumn6
        '
        Me.GridColumn6.Caption = "icon"
        Me.GridColumn6.Name = "GridColumn6"
        Me.GridColumn6.Visible = True
        Me.GridColumn6.VisibleIndex = 5
        Me.GridColumn6.Width = 179
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlItem6, Me.LayoutControlItem8, Me.LayoutControlItem7, Me.LayoutControlItem9, Me.LayoutControlItem10})
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(972, 483)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'LayoutControlItem6
        '
        Me.LayoutControlItem6.Control = Me.GridControl1
        Me.LayoutControlItem6.Location = New System.Drawing.Point(0, 91)
        Me.LayoutControlItem6.Name = "LayoutControlItem6"
        Me.LayoutControlItem6.Size = New System.Drawing.Size(946, 366)
        Me.LayoutControlItem6.TextVisible = False
        '
        'LayoutControlItem8
        '
        Me.LayoutControlItem8.Control = Me.Label2
        Me.LayoutControlItem8.Location = New System.Drawing.Point(0, 36)
        Me.LayoutControlItem8.Name = "LayoutControlItem8"
        Me.LayoutControlItem8.Size = New System.Drawing.Size(461, 55)
        Me.LayoutControlItem8.TextVisible = False
        '
        'LayoutControlItem7
        '
        Me.LayoutControlItem7.Control = Me.Label1
        Me.LayoutControlItem7.Location = New System.Drawing.Point(461, 36)
        Me.LayoutControlItem7.Name = "LayoutControlItem7"
        Me.LayoutControlItem7.Size = New System.Drawing.Size(330, 55)
        Me.LayoutControlItem7.TextVisible = False
        '
        'LayoutControlItem9
        '
        Me.LayoutControlItem9.Control = Me.Label3
        Me.LayoutControlItem9.Location = New System.Drawing.Point(791, 36)
        Me.LayoutControlItem9.Name = "LayoutControlItem9"
        Me.LayoutControlItem9.Size = New System.Drawing.Size(155, 55)
        Me.LayoutControlItem9.TextVisible = False
        '
        'LayoutControlItem10
        '
        Me.LayoutControlItem10.Control = Me.txtFilePath
        Me.LayoutControlItem10.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlItem10.Name = "LayoutControlItem10"
        Me.LayoutControlItem10.Size = New System.Drawing.Size(946, 36)
        Me.LayoutControlItem10.Spacing = New DevExpress.XtraLayout.Utils.Padding(0, 450, 0, 0)
        Me.LayoutControlItem10.Text = "Path :"
        Me.LayoutControlItem10.TextSize = New System.Drawing.Size(30, 18)
        '
        'BackgroundWorker1
        '
        '
        'frmValidationOffice365
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.Panel3)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.Panel1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.ShowIcon = False
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.TouchUIMode = DevExpress.Utils.DefaultBoolean.[False]
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "frmValidationOffice365"
        Me.ShowInTaskbar = False
        Me.Text = "Validation Office365"
        Me.Panel1.ResumeLayout(False)
        CType(Me.LayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl1.ResumeLayout(False)
        CType(Me.txtSleepTime.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.EmptySpaceItem1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem11, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel2.ResumeLayout(False)
        CType(Me.ProgressBarControl1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel3.ResumeLayout(False)
        CType(Me.LayoutControl2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.LayoutControl2.ResumeLayout(False)
        CType(Me.txtFilePath.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlItem10, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel1 As Panel
    Friend WithEvents LayoutControl1 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents btnStop As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnStart As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents btnSelectFile As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem1 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents EmptySpaceItem1 As DevExpress.XtraLayout.EmptySpaceItem
    Friend WithEvents LayoutControlItem2 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem3 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents BntResetAll As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem4 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents txtSleepTime As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem5 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents Panel2 As Panel
    Friend WithEvents Panel3 As Panel
    Friend WithEvents LayoutControl2 As DevExpress.XtraLayout.LayoutControl
    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlItem6 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn2 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn3 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn4 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents GridColumn5 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents ProgressBarControl1 As DevExpress.XtraEditors.ProgressBarControl
    Friend WithEvents Label2 As Label
    Friend WithEvents LayoutControlItem8 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents Label3 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents LayoutControlItem7 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents LayoutControlItem9 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents BackgroundWorker1 As System.ComponentModel.BackgroundWorker
    Friend WithEvents txtFilePath As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlItem10 As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents GridColumn6 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents BntSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LayoutControlItem11 As DevExpress.XtraLayout.LayoutControlItem
End Class
