﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmAddToAllAccounts
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmAddToAllAccounts))
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.TxtSubject = New DevExpress.XtraEditors.MemoEdit()
        Me.TxtFromName = New DevExpress.XtraEditors.TextEdit()
        Me.TxtTag_2 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.TxtTag_1 = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.TxtFromMail = New DevExpress.XtraEditors.TextEdit()
        Me.DxErrorProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(Me.components)
        Me.BntAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.BntNew = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupBox1.SuspendLayout()
        CType(Me.TxtSubject.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtFromName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtTag_2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtTag_1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TxtFromMail.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.TxtSubject)
        Me.GroupBox1.Controls.Add(Me.TxtFromName)
        Me.GroupBox1.Controls.Add(Me.TxtTag_2)
        Me.GroupBox1.Controls.Add(Me.TxtTag_1)
        Me.GroupBox1.Controls.Add(Me.Label18)
        Me.GroupBox1.Controls.Add(Me.Label17)
        Me.GroupBox1.Controls.Add(Me.Label15)
        Me.GroupBox1.Controls.Add(Me.Label14)
        Me.GroupBox1.Controls.Add(Me.Label9)
        Me.GroupBox1.Controls.Add(Me.Label8)
        Me.GroupBox1.Controls.Add(Me.Label7)
        Me.GroupBox1.Controls.Add(Me.Label5)
        Me.GroupBox1.Location = New System.Drawing.Point(22, 24)
        Me.GroupBox1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Padding = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.GroupBox1.Size = New System.Drawing.Size(458, 406)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        '
        'TxtSubject
        '
        Me.TxtSubject.Location = New System.Drawing.Point(32, 281)
        Me.TxtSubject.Name = "TxtSubject"
        Me.TxtSubject.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TxtSubject.Properties.Appearance.Options.UseBackColor = True
        Me.TxtSubject.Size = New System.Drawing.Size(400, 98)
        Me.TxtSubject.TabIndex = 374
        '
        'TxtFromName
        '
        Me.TxtFromName.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.TxtFromName.EditValue = ""
        Me.TxtFromName.Location = New System.Drawing.Point(32, 135)
        Me.TxtFromName.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtFromName.Name = "TxtFromName"
        Me.TxtFromName.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TxtFromName.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtFromName.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.TxtFromName.Properties.Appearance.Options.UseBackColor = True
        Me.TxtFromName.Properties.Appearance.Options.UseFont = True
        Me.TxtFromName.Properties.Appearance.Options.UseForeColor = True
        Me.TxtFromName.Properties.NullValuePrompt = "From Name"
        Me.TxtFromName.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtFromName.Size = New System.Drawing.Size(400, 30)
        Me.TxtFromName.TabIndex = 374
        '
        'TxtTag_2
        '
        Me.TxtTag_2.Cursor = System.Windows.Forms.Cursors.Hand
        Me.TxtTag_2.EditValue = ""
        Me.TxtTag_2.Location = New System.Drawing.Point(32, 204)
        Me.TxtTag_2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtTag_2.Name = "TxtTag_2"
        Me.TxtTag_2.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TxtTag_2.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.TxtTag_2.Properties.Appearance.Options.UseBackColor = True
        Me.TxtTag_2.Properties.Appearance.Options.UseFont = True
        Me.TxtTag_2.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TxtTag_2.Properties.Items.AddRange(New Object() {"Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Country", "Random IP", "Random IP China", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show Random Fake Phone Numbers", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.TxtTag_2.Size = New System.Drawing.Size(400, 38)
        Me.TxtTag_2.TabIndex = 375
        '
        'TxtTag_1
        '
        Me.TxtTag_1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.TxtTag_1.EditValue = ""
        Me.TxtTag_1.Location = New System.Drawing.Point(32, 58)
        Me.TxtTag_1.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtTag_1.Name = "TxtTag_1"
        Me.TxtTag_1.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.TxtTag_1.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.TxtTag_1.Properties.Appearance.Options.UseBackColor = True
        Me.TxtTag_1.Properties.Appearance.Options.UseFont = True
        Me.TxtTag_1.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TxtTag_1.Properties.Items.AddRange(New Object() {"Random Character 3", "Random Character 4", "Random Character 5", "Random Character 6", "Random Character 7", "Random Country", "Random IP", "Random IP China", "Random Number 3", "Random Number 4", "Random Number 5", "Random Number 6", "Random Number 7", "Receiver Email", "Show Company Name", "Show Date", "Show Date Tomorrow", "Show Name", "Show Random Fake Phone Numbers", "Show The Domain", "Show Time", "Start Name With Upper Case"})
        Me.TxtTag_1.Size = New System.Drawing.Size(400, 38)
        Me.TxtTag_1.TabIndex = 375
        '
        'Label18
        '
        Me.Label18.AutoSize = True
        Me.Label18.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label18.ForeColor = System.Drawing.Color.Crimson
        Me.Label18.Location = New System.Drawing.Point(13, 287)
        Me.Label18.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(14, 19)
        Me.Label18.TabIndex = 375
        Me.Label18.Text = "*"
        '
        'Label17
        '
        Me.Label17.AutoSize = True
        Me.Label17.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label17.ForeColor = System.Drawing.Color.Crimson
        Me.Label17.Location = New System.Drawing.Point(13, 213)
        Me.Label17.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(14, 19)
        Me.Label17.TabIndex = 374
        Me.Label17.Text = "*"
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.ForeColor = System.Drawing.Color.Crimson
        Me.Label15.Location = New System.Drawing.Point(13, 139)
        Me.Label15.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(14, 19)
        Me.Label15.TabIndex = 372
        Me.Label15.Text = "*"
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label14.ForeColor = System.Drawing.Color.Crimson
        Me.Label14.Location = New System.Drawing.Point(13, 65)
        Me.Label14.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(14, 19)
        Me.Label14.TabIndex = 371
        Me.Label14.Text = "*"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.ForeColor = System.Drawing.Color.White
        Me.Label9.Location = New System.Drawing.Point(32, 252)
        Me.Label9.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(65, 19)
        Me.Label9.TabIndex = 366
        Me.Label9.Text = "Subject : "
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label8.ForeColor = System.Drawing.Color.White
        Me.Label8.Location = New System.Drawing.Point(32, 175)
        Me.Label8.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(91, 19)
        Me.Label8.TabIndex = 364
        Me.Label8.Text = "Subject Tag : "
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.ForeColor = System.Drawing.Color.White
        Me.Label7.Location = New System.Drawing.Point(32, 106)
        Me.Label7.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(92, 19)
        Me.Label7.TabIndex = 360
        Me.Label7.Text = "From Name : "
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.White
        Me.Label5.Location = New System.Drawing.Point(32, 29)
        Me.Label5.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(81, 19)
        Me.Label5.TabIndex = 358
        Me.Label5.Text = "Name Tag : "
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label16.ForeColor = System.Drawing.Color.Crimson
        Me.Label16.Location = New System.Drawing.Point(610, 197)
        Me.Label16.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(14, 19)
        Me.Label16.TabIndex = 373
        Me.Label16.Text = "*"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.ForeColor = System.Drawing.Color.FromArgb(CType(CType(16, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(101, Byte), Integer))
        Me.Label6.Location = New System.Drawing.Point(622, 197)
        Me.Label6.Margin = New System.Windows.Forms.Padding(4, 0, 4, 0)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(84, 19)
        Me.Label6.TabIndex = 362
        Me.Label6.Text = "From Mail : "
        '
        'TxtFromMail
        '
        Me.TxtFromMail.EditValue = ""
        Me.TxtFromMail.Location = New System.Drawing.Point(607, 227)
        Me.TxtFromMail.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.TxtFromMail.Name = "TxtFromMail"
        Me.TxtFromMail.Properties.Appearance.BackColor = System.Drawing.Color.White
        Me.TxtFromMail.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TxtFromMail.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.TxtFromMail.Properties.Appearance.Options.UseBackColor = True
        Me.TxtFromMail.Properties.Appearance.Options.UseFont = True
        Me.TxtFromMail.Properties.Appearance.Options.UseForeColor = True
        Me.TxtFromMail.Properties.LookAndFeel.SkinName = "The Bezier"
        Me.TxtFromMail.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.TxtFromMail.Properties.NullValuePrompt = "Enter from name enail title..."
        Me.TxtFromMail.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.TxtFromMail.Size = New System.Drawing.Size(408, 26)
        Me.TxtFromMail.TabIndex = 6
        Me.TxtFromMail.Visible = False
        '
        'DxErrorProvider1
        '
        Me.DxErrorProvider1.ContainerControl = Me
        '
        'BntAdd
        '
        Me.BntAdd.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAdd.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntAdd.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.Appearance.Options.UseBackColor = True
        Me.BntAdd.Appearance.Options.UseBorderColor = True
        Me.BntAdd.Appearance.Options.UseFont = True
        Me.BntAdd.Appearance.Options.UseForeColor = True
        Me.BntAdd.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntAdd.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntAdd.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntAdd.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntAdd.AppearanceDisabled.Options.UseBackColor = True
        Me.BntAdd.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntAdd.AppearanceDisabled.Options.UseFont = True
        Me.BntAdd.AppearanceDisabled.Options.UseForeColor = True
        Me.BntAdd.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntAdd.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAdd.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntAdd.AppearanceHovered.Options.UseBackColor = True
        Me.BntAdd.AppearanceHovered.Options.UseBorderColor = True
        Me.BntAdd.AppearanceHovered.Options.UseForeColor = True
        Me.BntAdd.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntAdd.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntAdd.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntAdd.AppearancePressed.Options.UseBackColor = True
        Me.BntAdd.AppearancePressed.Options.UseBorderColor = True
        Me.BntAdd.AppearancePressed.Options.UseForeColor = True
        Me.BntAdd.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Transfer32x32
        Me.BntAdd.Location = New System.Drawing.Point(322, 436)
        Me.BntAdd.Name = "BntAdd"
        Me.BntAdd.Size = New System.Drawing.Size(118, 47)
        Me.BntAdd.TabIndex = 374
        Me.BntAdd.Text = "Update"
        '
        'BntNew
        '
        Me.BntNew.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntNew.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntNew.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.Appearance.Options.UseBackColor = True
        Me.BntNew.Appearance.Options.UseBorderColor = True
        Me.BntNew.Appearance.Options.UseFont = True
        Me.BntNew.Appearance.Options.UseForeColor = True
        Me.BntNew.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntNew.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntNew.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntNew.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntNew.AppearanceDisabled.Options.UseBackColor = True
        Me.BntNew.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntNew.AppearanceDisabled.Options.UseFont = True
        Me.BntNew.AppearanceDisabled.Options.UseForeColor = True
        Me.BntNew.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntNew.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntNew.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntNew.AppearanceHovered.Options.UseBackColor = True
        Me.BntNew.AppearanceHovered.Options.UseBorderColor = True
        Me.BntNew.AppearanceHovered.Options.UseForeColor = True
        Me.BntNew.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntNew.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntNew.AppearancePressed.Options.UseBackColor = True
        Me.BntNew.AppearancePressed.Options.UseBorderColor = True
        Me.BntNew.AppearancePressed.Options.UseForeColor = True
        Me.BntNew.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntNew.Location = New System.Drawing.Point(58, 436)
        Me.BntNew.Name = "BntNew"
        Me.BntNew.Size = New System.Drawing.Size(118, 47)
        Me.BntNew.TabIndex = 375
        Me.BntNew.Text = "Clear All"
        '
        'frmAddToAllAccounts
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer), CType(CType(46, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(511, 516)
        Me.Controls.Add(Me.BntNew)
        Me.Controls.Add(Me.BntAdd)
        Me.Controls.Add(Me.Label16)
        Me.Controls.Add(Me.GroupBox1)
        Me.Controls.Add(Me.Label6)
        Me.Controls.Add(Me.TxtFromMail)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.IconOptions.Icon = CType(resources.GetObject("frmAddToAllAccounts.IconOptions.Icon"), System.Drawing.Icon)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.smtpserverOlder
        Me.LookAndFeel.SkinName = "WXI"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmAddToAllAccounts"
        Me.Opacity = 0.99R
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "  Add to all Smtp accounts ..."
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.TxtSubject.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtFromName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtTag_2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtTag_1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TxtFromMail.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()
    End Sub
    Friend WithEvents GroupBox1 As GroupBox
    Friend WithEvents DxErrorProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider
    Friend WithEvents Label18 As Label
    Friend WithEvents Label17 As Label
    Friend WithEvents Label16 As Label
    Friend WithEvents Label14 As Label
    Friend WithEvents Label9 As Label
    Friend WithEvents Label8 As Label
    Friend WithEvents Label6 As Label
    Friend WithEvents TxtFromMail As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label5 As Label
    Friend WithEvents Label15 As Label
    Friend WithEvents Label7 As Label
    Friend WithEvents TxtFromName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TxtTag_1 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents TxtSubject As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents TxtTag_2 As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents BntNew As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntAdd As DevExpress.XtraEditors.SimpleButton
End Class
