﻿Imports System.Text
Imports System.Runtime.InteropServices
Imports System.IO
Imports System.Text.RegularExpressions
Public Class frmAddAttach
    Dim tt As New ToolTip()
    Private Function ColorBrightness(color As Color) As Double
        ' Calculate the brightness of a color
        Return Math.Sqrt(color.R ^ 2 * 0.241 + color.G ^ 2 * 0.691 + color.B ^ 2 * 0.068)
    End Function
    Function ReturnShortString(ByVal str As String,
                                       ByVal maxLength As Integer) _
                                       As String
        If str.Length <= maxLength Then
            Return str
        Else
            Dim leftEndPosition As Integer = CInt((maxLength / 2) - 2.5)
            Dim rightStartPosition As Integer = str.Length - (CInt((maxLength / 2) - 2.5))
            Dim sb As New System.Text.StringBuilder
            sb.Append(str.Substring(0, leftEndPosition) & " ... " & str.Substring(rightStartPosition))
            Return sb.ToString
        End If
    End Function
    Function CompactString(ByVal MyString As String, ByVal Width As Integer,
    ByVal Font As Drawing.Font,
    ByVal FormatFlags As Windows.Forms.TextFormatFlags) As String
        Dim Result As String = String.Copy(MyString)
        TextRenderer.MeasureText(Result, Font, New Drawing.Size(Width, 0),
    FormatFlags Or TextFormatFlags.ModifyString)
        Return Result
    End Function
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Dim OFD As New OpenFileDialog
        OFD.FileName = ""
        OFD.Filter = "All Graphics Types|*.bmp;*.jpg;*.png;*.tif;*.tiff|Html files|*.html|MS Word Files|*.doc;*.docx|MS Excel Files|*.xls;*.xlsx|PDF Document|*.pdf|All Files|*.*"
        If OFD.ShowDialog = DialogResult.OK Then
            Dim shortPath As String = ReturnShortString(OFD.FileName, 35)
            lbl4.Text = shortPath
            AttachmentFilePath = OFD.FileName
        Else
            lbl4.Text = "No file chosen"
            AttachmentFilePath = ""
        End If
    End Sub
    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs)
    End Sub
    Private Sub Label2_MouseEnter(sender As Object, e As EventArgs) Handles lbl4.MouseEnter
        tt.SetToolTip(lbl4, "")
        If AttachmentFilePath <> "" Then tt.SetToolTip(lbl4, AttachmentFilePath)
    End Sub
    Private Sub frmAddAttachment_FormClosed(sender As Object, e As FormClosedEventArgs) Handles Me.FormClosed
        If lbl4.Text = "No file chosen" Then
            EmailScanner.LinkLabel1.Tag = ""
        Else
            Dim myFile As New FileInfo(AttachmentFilePath)
            Dim sizeInBytes As Long = myFile.Length
            If Val(sizeInBytes) > 25000000 Then
                Dim msg = "Attachment size exceeds the allowable limit (25 MB)."
                DevExpress.XtraEditors.XtraMessageBox.Show(msg, "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Exit Sub
            End If
            EmailScanner.LinkLabel1.Tag = lbl4.Text
        End If
        Me.Dispose()
    End Sub
    Private Sub frmaddAttach_Load(sender As Object, e As EventArgs) Handles Me.Load
        'CenterForm(Me, frmEmailScanner)
        If EmailScanner.LinkLabel1.Tag = "" Then
            lbl4.Text = "No file chosen"
        Else
            lbl4.Text = EmailScanner.LinkLabel1.Tag
        End If
        Dim brightness As Double = ColorBrightness(Me.BackColor)
        If brightness < 130 Then
            lbl2.ForeColor = Color.Salmon
            lbl3.ForeColor = Color.White
            lbl4.ForeColor = Color.White
        Else
            lbl2.ForeColor = Color.Navy
            lbl3.ForeColor = Color.Black
            lbl4.ForeColor = Color.Black
            SimpleButton1.ForeColor = Color.Gold
        End If ' Dark background color
    End Sub
End Class