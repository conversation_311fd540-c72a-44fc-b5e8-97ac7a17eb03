Imports System.Text
Imports System.Runtime.InteropServices
Imports System.IO
Imports System.Text.RegularExpressions
Public Class frmAddAttach
    Dim tt As New ToolTip()
    Private Function ColorBrightness(color As Color) As Double
        ' Calculate the brightness of a color
        Return Math.Sqrt(color.R ^ 2 * 0.241 + color.G ^ 2 * 0.691 + color.B ^ 2 * 0.068)
    End Function
    Function ReturnShortString(ByVal str As String,
                                       ByVal maxLength As Integer) _
                                       As String
        If str.Length <= maxLength Then
            Return str
        Else
            Dim leftEndPosition As Integer = CInt((maxLength / 2) - 2.5)
            Dim rightStartPosition As Integer = str.Length - (CInt((maxLength / 2) - 2.5))
            Dim sb As New System.Text.StringBuilder
            sb.Append(str.Substring(0, leftEndPosition) & " ... " & str.Substring(rightStartPosition))
            Return sb.ToString
        End If
    End Function
    Function CompactString(ByVal MyString As String, ByVal Width As Integer,
    ByVal Font As Drawing.Font,
    ByVal FormatFlags As Windows.Forms.TextFormatFlags) As String
        Dim Result As String = String.Copy(MyString)
        TextRenderer.MeasureText(Result, Font, New Drawing.Size(Width, 0),
    FormatFlags Or TextFormatFlags.ModifyString)
        Return Result
    End Function
    Private Sub SimpleButton1_Click(sender As Object, e As EventArgs) Handles SimpleButton1.Click
        Try
            Dim OFD As New OpenFileDialog
            OFD.FileName = ""
            OFD.Filter = "All Graphics Types|*.bmp;*.jpg;*.png;*.tif;*.tiff|Html files|*.html|MS Word Files|*.doc;*.docx|MS Excel Files|*.xls;*.xlsx|PDF Document|*.pdf|All Files|*.*"
            OFD.Title = "اختر ملف للإرفاق"

            If OFD.ShowDialog = DialogResult.OK Then
                ' التحقق من حجم الملف
                Dim fileInfo As New FileInfo(OFD.FileName)
                If fileInfo.Length > 25000000 Then ' 25 MB
                    DevExpress.XtraEditors.XtraMessageBox.Show("حجم الملف يتجاوز الحد المسموح (25 ميجابايت).", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Exit Sub
                End If

                Dim shortPath As String = ReturnShortString(OFD.FileName, 35)
                lbl4.Text = shortPath
                AttachmentFilePath = OFD.FileName

                ' تحديث حالة الأزرار
                UpdateButtonStates()

                Me.Dispose()
            Else
                lbl4.Text = "No file chosen"
                AttachmentFilePath = ""
                UpdateButtonStates()
            End If

        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("حدث خطأ أثناء اختيار الملف: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            lbl4.Text = "No file chosen"
            AttachmentFilePath = ""
            UpdateButtonStates()
        End Try
    End Sub
    Private Sub SimpleButton2_Click(sender As Object, e As EventArgs) Handles SimpleButton2.Click
        Try
            ' مسح الملف المرفق
            AttachmentFilePath = ""
            lbl4.Text = "No file chosen"

            ' تحديث EmailScanner إذا كان متاحاً
            If EmailScanner IsNot Nothing Then
                Try
                    ' البحث عن instance مفتوح من EmailScanner
                    Dim openEmailScanner As EmailScanner = Nothing
                    For Each form As Form In Application.OpenForms
                        If TypeOf form Is EmailScanner Then
                            openEmailScanner = CType(form, EmailScanner)
                            Exit For
                        End If
                    Next

                    If openEmailScanner IsNot Nothing AndAlso Not openEmailScanner.IsDisposed AndAlso Not openEmailScanner.Disposing Then
                        ' تحديث LinkLabel1 و lblattachfile في EmailScanner
                        openEmailScanner.LinkLabel1.Tag = ""
                        If openEmailScanner.lblattachfile IsNot Nothing Then
                            openEmailScanner.lblattachfile.Text = "no file attached."
                        End If
                    End If
                Catch ex As Exception
                    ' في حالة حدوث خطأ في الوصول لـ EmailScanner، تجاهل الخطأ
                End Try
            End If

            ' تحديث حالة الأزرار
            UpdateButtonStates()

            ' إظهار رسالة تأكيد
            DevExpress.XtraEditors.XtraMessageBox.Show("تم مسح الملف المرفق بنجاح", "تم المسح", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            DevExpress.XtraEditors.XtraMessageBox.Show("حدث خطأ أثناء مسح الملف المرفق: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub Label2_MouseEnter(sender As Object, e As EventArgs) Handles lbl4.MouseEnter
        tt.SetToolTip(lbl4, "")
        If AttachmentFilePath <> "" Then tt.SetToolTip(lbl4, AttachmentFilePath)
    End Sub
    Private Sub frmAddAttachment_FormClosed(sender As Object, e As FormClosedEventArgs) Handles Me.FormClosed
        Try
            ' البحث عن instance مفتوح من EmailScanner
            Dim openEmailScanner As EmailScanner = Nothing
            For Each form As Form In Application.OpenForms
                If TypeOf form Is EmailScanner Then
                    openEmailScanner = CType(form, EmailScanner)
                    Exit For
                End If
            Next

            If openEmailScanner IsNot Nothing AndAlso Not openEmailScanner.IsDisposed AndAlso Not openEmailScanner.Disposing Then
                If lbl4.Text = "No file chosen" Then
                    ' مسح الملف المرفق
                    openEmailScanner.LinkLabel1.Tag = ""
                    If openEmailScanner.lblattachfile IsNot Nothing Then
                        openEmailScanner.lblattachfile.Text = "no file attached."
                    End If
                Else
                    ' التحقق من حجم الملف مرة أخرى
                    If AttachmentFilePath <> "" AndAlso File.Exists(AttachmentFilePath) Then
                        Dim myFile As New FileInfo(AttachmentFilePath)
                        Dim sizeInBytes As Long = myFile.Length
                        If sizeInBytes > 25000000 Then
                            Dim msg = "حجم الملف المرفق يتجاوز الحد المسموح (25 ميجابايت)."
                            DevExpress.XtraEditors.XtraMessageBox.Show(msg, "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                            ' مسح الملف المرفق
                            openEmailScanner.LinkLabel1.Tag = ""
                            If openEmailScanner.lblattachfile IsNot Nothing Then
                                openEmailScanner.lblattachfile.Text = "no file attached."
                            End If
                            Exit Sub
                        End If

                        ' تحديث EmailScanner بالملف الجديد
                        openEmailScanner.LinkLabel1.Tag = lbl4.Text
                        If openEmailScanner.lblattachfile IsNot Nothing Then
                            openEmailScanner.lblattachfile.Text = "1 file ."
                        End If
                    End If
                End If
            End If

        Catch ex As Exception
            ' في حالة حدوث خطأ، تجاهل الخطأ
        Finally
            Me.Dispose()
        End Try
    End Sub
    Private Sub frmaddAttach_Load(sender As Object, e As EventArgs) Handles Me.Load
        Try
            'CenterForm(Me, frmEmailScanner)

            ' البحث عن instance مفتوح من EmailScanner
            Dim openEmailScanner As EmailScanner = Nothing
            For Each form As Form In Application.OpenForms
                If TypeOf form Is EmailScanner Then
                    openEmailScanner = CType(form, EmailScanner)
                    Exit For
                End If
            Next

            ' تحديد حالة الملف المرفق
            If openEmailScanner IsNot Nothing AndAlso Not openEmailScanner.IsDisposed AndAlso Not openEmailScanner.Disposing Then
                If openEmailScanner.LinkLabel1.Tag = "" Then
                    lbl4.Text = "No file chosen"
                    SimpleButton2.Enabled = False ' تعطيل زر المسح إذا لم يكن هناك ملف
                Else
                    lbl4.Text = openEmailScanner.LinkLabel1.Tag
                    SimpleButton2.Enabled = True ' تفعيل زر المسح إذا كان هناك ملف
                End If
            Else
                lbl4.Text = "No file chosen"
                SimpleButton2.Enabled = False
            End If

            ' تطبيق الألوان حسب السمة
            ApplyThemeColors()

            ' إضافة tooltips للأزرار
            tt.SetToolTip(SimpleButton1, "اختر ملف للإرفاق مع الرسالة")
            tt.SetToolTip(SimpleButton2, "مسح الملف المرفق الحالي")

            ' تحديث حالة الأزرار
            UpdateButtonStates()

        Catch ex As Exception
            ' في حالة حدوث خطأ، استخدم القيم الافتراضية
            lbl4.Text = "No file chosen"
            SimpleButton2.Enabled = False
            ApplyThemeColors()
        End Try
    End Sub

    ' Method منفصل لتطبيق الألوان
    Private Sub ApplyThemeColors()
        Try
            Dim brightness As Double = ColorBrightness(Me.BackColor)
            If brightness < 130 Then
                ' Dark theme colors (Binance-inspired)
                lbl2.ForeColor = Color.FromArgb(160, 174, 192) ' Gray soft color
                lbl4.ForeColor = Color.White

                ' تحديث ألوان الأزرار للسمة المظلمة
                SimpleButton1.Appearance.ForeColor = Color.FromArgb(241, 202, 17) ' Gold
                SimpleButton2.Appearance.ForeColor = Color.FromArgb(255, 76, 76) ' Binance red
            Else
                ' Light theme colors
                lbl2.ForeColor = Color.Navy
                lbl4.ForeColor = Color.Black
                SimpleButton1.Appearance.ForeColor = Color.Gold
                SimpleButton2.Appearance.ForeColor = Color.Red
            End If
        Catch ex As Exception
            ' في حالة حدوث خطأ، استخدم الألوان الافتراضية
        End Try
    End Sub

    ' Method لتحديث حالة الأزرار
    Private Sub UpdateButtonStates()
        Try
            If AttachmentFilePath <> "" AndAlso File.Exists(AttachmentFilePath) Then
                SimpleButton2.Enabled = True
                SimpleButton2.Text = "Clear Attachment"
            Else
                SimpleButton2.Enabled = False
                SimpleButton2.Text = "No Attachment"
            End If
        Catch ex As Exception
            SimpleButton2.Enabled = False
            SimpleButton2.Text = "Clear Attachment"
        End Try
    End Sub

    ' إضافة keyboard shortcuts
    Private Sub frmAddAttach_KeyDown(sender As Object, e As KeyEventArgs) Handles Me.KeyDown
        Try
            If e.KeyCode = Keys.Delete AndAlso SimpleButton2.Enabled Then
                ' مسح الملف المرفق بالضغط على Delete
                SimpleButton2_Click(Nothing, Nothing)
            ElseIf e.Control AndAlso e.KeyCode = Keys.O Then
                ' فتح ملف بالضغط على Ctrl+O
                SimpleButton1_Click(Nothing, Nothing)
            ElseIf e.KeyCode = Keys.Escape Then
                ' إغلاق النافذة بالضغط على Escape
                Me.Close()
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في keyboard shortcuts
        End Try
    End Sub
End Class