﻿Imports System.ComponentModel
Imports System.IO
Imports DevExpress.XtraEditors
Imports DevExpress.XtraGrid.Views.Grid
Public Class FrmEmailValidation
    Dim StopBYError As Boolean = False
    Dim ErrorAccountAddress As String = ""
    Private Sub Bnt_Reset_Click(sender As Object, e As EventArgs) Handles Bnt_Reset.Click
        ErrorAccountAddress = ""
        StopBYError = False
        GroupControl2.Visible = False
        txtPath.Text = ""
        CreateTable_EmailValidation()
        GridControl1.DataSource = Nothing
        BackgroundWorker1.WorkerSupportsCancellation = True
        BackgroundWorker1.WorkerReportsProgress = True
        BackgroundWorker2.WorkerSupportsCancellation = True
        BackgroundWorker2.WorkerReportsProgress = True
        Bnt_Start.Text = "&Start"
    End Sub
    Private Sub BntBrowse_Click(sender As Object, e As EventArgs) Handles BntBrowse.Click
        Dim openFileDialog As New OpenFileDialog()
        openFileDialog.Filter = "Text Files (*.txt)|*.txt"
        If (openFileDialog.ShowDialog() = DialogResult.OK) Then
            CreateTable_EmailValidation()
            Dim filePath As String = openFileDialog.FileName
            txtPath.Text = filePath
            picWait.Image = My.Resources.finding_signatures
            GroupControl2.Visible = True
            If Not BackgroundWorker2.IsBusy Then
                BackgroundWorker2.RunWorkerAsync()
            Else
                BackgroundWorker2.CancelAsync()
            End If
        End If
    End Sub
    Private Sub Bnt_Start_Click(sender As Object, e As EventArgs) Handles Bnt_Start.Click
        StopBYError = False
        ErrorAccountAddress = ""
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("Add atleast one email address to start...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        Bnt_Start.Text = "Cancel"
        DevExpress.Data.CurrencyDataController.DisableThreadingProblemsDetection = True
        picWait.Image = My.Resources.check
        GroupControl2.Visible = True
        Timer1.Enabled = True
        If Not BackgroundWorker1.IsBusy Then
            BackgroundWorker1.RunWorkerAsync()
        Else
            BackgroundWorker1.CancelAsync()
        End If
    End Sub
    Private Sub GridView1_RowCellStyle(sender As Object, e As RowCellStyleEventArgs) Handles GridView1.RowCellStyle
        If e.Column.FieldName = "Status" Then
            Dim statusValue As String = GridView1.GetRowCellValue(e.RowHandle, "Status").ToString()
            If statusValue = "Success" Then
                e.Appearance.ForeColor = Color.PaleGreen
            ElseIf statusValue = "Fail" Or statusValue = "Invalid" Then
                e.Appearance.ForeColor = Color.Coral
            ElseIf statusValue = "Inconclusive" Then
                e.Appearance.ForeColor = Color.Orange
            ElseIf statusValue = "Unchecked" Then
                e.Appearance.ForeColor = Color.FromArgb(255, 255, 192)
            ElseIf statusValue = "Verifing" Then
                e.Appearance.ForeColor = Color.Cyan
            End If
        End If
    End Sub
    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        UpdateGridControlDataSource()
    End Sub
    Private Sub UpdateGridControlDataSource()
        If GridControl1.InvokeRequired Then
            GridControl1.BeginInvoke(New MethodInvoker(AddressOf UpdateGridControlDataSource))
        Else
            GridControl1.DataSource = MailTable
            GridControl1.RefreshDataSource()
        End If
    End Sub
    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        Dim DomainName As String = ""
        Dim SmtpServerName As String = ""
        Dim EmailStatus As String = ""
        Dim messageStr As String = ""
        Try
            For i = 0 To MailTable.Rows.Count - 1
                ErrorAccountAddress = MailTable.Rows(i).Item("emailAddress")
                If BackgroundWorker1.CancellationPending Then
                    e.Cancel = True
                    Return
                End If
                CheckForIllegalCrossThreadCalls = False
                If MailTable.Rows(i).Item("Status") <> "Unchecked" Or MailTable.Rows(i).Item("Status") = "Invalid" Then GoTo nextRow
                If IsValidEmail(MailTable.Rows(i).Item("emailAddress")) = False Then
                    MailTable.Rows(i).Item("Status") = "Invalid"
                    GoTo nextRow
                End If
                MailTable.Rows(i).Item("Status") = "Verifing"
                AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
                DomainName = get_Domain_Name(MailTable.Rows(i).Item("emailAddress"))
                EmailStatus = VerifyEmailAddress(MailTable.Rows(i).Item("emailAddress"))
                Dim arr = Split(EmailStatus, "|")
                MailTable.Rows(i).Item("Status") = StrConv(arr(0), VbStrConv.ProperCase)
                MailTable.Rows(i).Item("DomanName") = DomainName
                MailTable.Rows(i).Item("SMTPServer") = arr(1)
                MailTable.Rows(i).Item("SMTPReply") = arr(2)
nextRow:
                CheckForIllegalCrossThreadCalls = False
            Next
            StopBYError = False
        Catch ex As Exception
            StopBYError = True
        End Try
    End Sub
    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        Timer1.Enabled = False
        GroupControl2.Visible = False
        GridControl1.DataSource = MailTable.DefaultView
        GridControl1.Refresh()
        Bnt_Start.Text = "Start"
        If StopBYError = True Then
            XtraMessageBox.Show("An error occurred while checking ( " & ErrorAccountAddress & " ). " & vbNewLine & "Click Start again if you want to continue the verification process....!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        If e.Cancelled Then
            XtraMessageBox.Show("Verification proccess was cancelled by the user...!", "Alert", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
        Else
            XtraMessageBox.Show("Verification proccess Successfully completed...!", "Confirmation", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub
    Private Sub GridView1_DataSourceChanged(sender As Object, e As EventArgs) Handles GridView1.DataSourceChanged
        Dim view As GridView = TryCast(sender, GridView)
        If view IsNot Nothing AndAlso view.DataSource IsNot Nothing Then
            Dim focusedRowHandle As Integer = view.FocusedRowHandle
            If focusedRowHandle >= 0 Then
                view.FocusedRowHandle = focusedRowHandle
            Else
                ' you can set the focus to any specific row in the grid by specifying its handle here.
                view.FocusedRowHandle = 0
            End If
        End If
    End Sub
    Private Sub Bnt_Save_Click(sender As Object, e As EventArgs) Handles Bnt_Save.Click
        If GridView1.DataRowCount = 0 Then
            XtraMessageBox.Show("The Email list is empty...", "Instructions", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If
        If GroupControl2.Visible = True Then
            Dim result As DialogResult = XtraMessageBox.Show("The Verification process Is Not over yet. Do you want to continue with the saving process?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2)
            If result = DialogResult.No Then Exit Sub
        End If
        ' Get the path to the Desktop directory.
        Dim desktopPath As String = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory)
        ' Define the path for the "Main Folder Path" folder.
        Dim MainFolderPath As String = Path.Combine(desktopPath, "Email Verification")
        ' Check if the "Best Defender" folder exists. If not, create it.
        If Not Directory.Exists(MainFolderPath) Then
            Directory.CreateDirectory(MainFolderPath)
        End If
        ' Define the path for the "Sub Folder Path" folder.
        Dim SubFolderPath As String = Path.Combine(MainFolderPath, "List Dated " & DateTime.Now.ToString("yyyy-MM-dd") & " at " & DateTime.Now.ToString("HH-mm-ss tt"))
        ' Check if the "Email Sorter List" folder exists. If not, create it.
        If Not Directory.Exists(SubFolderPath) Then
            Directory.CreateDirectory(SubFolderPath)
        End If
        Dim Invalid_List As New List(Of String)
        Dim Success_list As New List(Of String)
        Dim Fail_List As New List(Of String)
        Dim Inconclusive_list As New List(Of String)
        Dim Unchecked_list As New List(Of String)
        Dim Other_list As New List(Of String)
        For i As Integer = 0 To GridView1.RowCount - 1
            If GridView1.GetRowCellValue(i, "Status").ToString() = "Invalid" Then
                Invalid_List.Add(GridView1.GetRowCellValue(i, "emailAddress").ToString())
            ElseIf GridView1.GetRowCellValue(i, "Status").ToString() = "Success" Then
                Success_list.Add(GridView1.GetRowCellValue(i, "emailAddress").ToString())
            ElseIf GridView1.GetRowCellValue(i, "Status").ToString() = "Fail" Then
                Fail_List.Add(GridView1.GetRowCellValue(i, "emailAddress").ToString())
            ElseIf GridView1.GetRowCellValue(i, "Status").ToString() = "Inconclusive" Then
                Inconclusive_list.Add(GridView1.GetRowCellValue(i, "emailAddress").ToString())
            ElseIf GridView1.GetRowCellValue(i, "Status").ToString() = "Unchecked" Then
                Unchecked_list.Add(GridView1.GetRowCellValue(i, "emailAddress").ToString())
            Else
                Other_list.Add(GridView1.GetRowCellValue(i, "emailAddress").ToString())
            End If
        Next
        If Invalid_List.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Invalid.txt"), Invalid_List)
        If Success_list.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Verified.txt"), Success_list)
        If Fail_List.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Failed.txt"), Fail_List)
        If Inconclusive_list.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Inconclusive.txt"), Inconclusive_list)
        If Unchecked_list.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Unchecked.txt"), Unchecked_list)
        If Other_list.Count > 0 Then System.IO.File.WriteAllLines(System.IO.Path.Combine(SubFolderPath, "Other.txt"), Other_list)
        Process.Start("explorer.exe", SubFolderPath)
        DevExpress.XtraEditors.XtraMessageBox.Show("All lists were saved Successfully...!", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    Private Sub BackgroundWorker2_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker2.DoWork
        Dim filePath As String = txtPath.Text
        Dim Counter_ As Integer = 1
        Dim emailList As New List(Of String)
        Dim newRows As New List(Of DataRow)
        ' قراءة كل السطور دفعة واحدة
        Dim lines As String() = File.ReadAllLines(filePath)
        ' تحميل كل عناوين البريد الإلكتروني الحالية في List
        For Each MailRow As DataRow In MailTable.Rows
            emailList.Add(MailRow("emailAddress").ToString().Trim())
        Next
        ' معالجة كل سطر في الملف
        For Each line As String In lines
            If Not emailList.Contains(line.Trim()) AndAlso Trim(line) <> "" Then
                Dim row As DataRow = MailTable.NewRow()
                row("id") = Counter_
                row("emailAddress") = line.Trim()
                row("Status") = "Unchecked"
                row("DomanName") = "----------"
                row("SMTPServer") = "----------"
                row("SMTPReply") = "----------"
                newRows.Add(row)
                Counter_ += 1
            End If
        Next
        ' إضافة الصفوف الجديدة دفعة واحدة
        For Each row As DataRow In newRows
            MailTable.Rows.Add(row)
        Next
    End Sub
    Private Sub BackgroundWorker2_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker2.RunWorkerCompleted
        Timer1.Enabled = False
        GroupControl2.Visible = False
        GridControl1.DataSource = MailTable.DefaultView
        AddHandler GridView1.RowCellStyle, AddressOf GridView1_RowCellStyle
        GridControl1.Refresh()
    End Sub
    Private Sub trmfadein_Tick(sender As Object, e As EventArgs) Handles trmfadein.Tick
        Me.Opacity = Me.Opacity + 0.02
        If Me.Opacity = 1 Then
            Me.Opacity = 99 / 100
            trmfadein.Enabled = False
            trmfadein.Stop()
        End If
    End Sub
    Private Sub FrmEmailValidation_Load(sender As Object, e As EventArgs) Handles Me.Load
        Me.Opacity = 0
        trmfadein.Enabled = True
        Bnt_Reset_Click(Nothing, Nothing)
    End Sub
End Class
