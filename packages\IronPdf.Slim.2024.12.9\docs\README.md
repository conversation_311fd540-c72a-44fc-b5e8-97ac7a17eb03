![Nuget](https://img.shields.io/nuget/v/IronPdf?color=informational&label=latest)  ![Installs](https://img.shields.io/nuget/dt/IronPdf?color=informational&label=installs&logo=nuget)  ![Passed](https://img.shields.io/badge/build-%20%E2%9C%93%203158%20tests%20passed%20(0%20failed)%20-107C10?logo=visualstudio)  [![windows](https://img.shields.io/badge/%E2%80%8E%20-%20%E2%9C%93-107C10?logo=windows)](https://ironpdf.com/docs/questions/installation/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=topshield) [![macOS](https://img.shields.io/badge/%E2%80%8E%20-%20%E2%9C%93-107C10?logo=apple)](https://ironpdf.com/docs/questions/macos/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=topshield) [![linux](https://img.shields.io/badge/%E2%80%8E%20-%20%E2%9C%93-107C10?logo=linux&logoColor=white)](https://ironpdf.com/docs/questions/linux/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=topshield) [![docker](https://img.shields.io/badge/%E2%80%8E%20-%20%E2%9C%93-107C10?logo=docker&logoColor=white)](https://ironpdf.com/docs/questions/docker-linux/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=topshield) [![aws](https://img.shields.io/badge/%E2%80%8E%20-%20%E2%9C%93-107C10?logo=amazonaws)](https://ironpdf.com/docs/questions/creating-pdfs-csharp-amazon-aws-lambda/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=topshield) [![microsoftazure](https://img.shields.io/badge/%E2%80%8E%20-%20%E2%9C%93-107C10?logo=microsoftazure)](https://ironpdf.com/docs/questions/azure/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=topshield) [![livechat](https://img.shields.io/badge/Live%20Chat:-24/5-purple?logo=googlechat&logoColor=white)](https://ironpdf.com/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=topshield#helpscout-support)

# IronPDF - Create, Edit, and Read PDFs in .NET Applications


[![IronPDF NuGet Trial Banner Image](https://raw.githubusercontent.com/iron-software/iron-nuget-assets/main/IronPDF-readme/nuget-trial-banner-large.png)](https://ironpdf.com/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=topbanner#trial-license)

[Get Started](https://ironpdf.com/docs/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=navigation) | [Features](https://ironpdf.com/features/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=navigation) | [How-Tos](https://ironpdf.com/how-to/html-file-to-pdf/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=navigation) | [Code Examples](https://ironpdf.com/examples/using-html-to-create-a-pdf/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=navigation) | [Licensing](https://ironpdf.com/licensing/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=navigation) | [Free Trial](https://ironpdf.com/docs/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=navigation#trial-license)

IronPDF is a library developed and maintained by Iron Software that helps C# Software Engineers to create, edit and extract PDF content in projects in .NET.

 

### IronPDF excels at: 
 - Generating PDFs from: [HTML](https://ironpdf.com/examples/using-html-to-create-a-pdf/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist), [URL](https://ironpdf.com/examples/converting-a-url-to-a-pdf/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist), [JavaScript](https://ironpdf.com/examples/javascript-html-to-pdf/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist), [CSS](https://ironpdf.com/docs/questions/base-urls/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist) and many [image formats](https://ironpdf.com/examples/image-to-pdf/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist)
 -  Adding [headers/footers](https://ironpdf.com/examples/headers-and-footers/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist), [signatures](https://ironpdf.com/examples/digitally-sign-a-pdf/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist), [attachments](https://ironpdf.com/examples/csharp-add-attachment-to-pdf/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist), and [passwords and security](https://ironpdf.com/examples/security-and-metadata/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist)
 - Performance optimization: Full [Multithreading](https://ironpdf.com/examples/threading/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist) and [Async](https://ironpdf.com/examples/async/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist) support
 - Embeddable [PDF Viewer](https://ironpdf.com/tutorials/pdf-viewing/), and other extensions for [MAUI](https://ironpdf.com/how-to/net-maui-create-pdf-tutorial) and [ASP.NET](https://ironpdf.com/how-to/razor-to-pdf-blazor-server/)
 - And many more! *Visit [our website](https://ironpdf.com/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist) to see all our [code examples](https://ironpdf.com/examples/using-html-to-create-a-pdf/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist) and a [full list of our 50+ features](https://ironpdf.com/features/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist)*

### IronPDF has cross platform support compatibility with:
- **.NET 8, .NET 7, .NET 6**, .NET 5, and .NET Core, Standard, and Framework
- Windows, macOS, Linux, Docker, Azure, and AWS
- Console, Desktop, and Web Apps. [MVC](https://ironpdf.com/how-to/cshtml-to-pdf-mvc-core/), [Blazor](https://ironpdf.com/how-to/razor-to-pdf-blazor-server/), [MAUI](https://ironpdf.com/how-to/xaml-to-pdf-maui/), [Razor Pages](https://ironpdf.com/how-to/cshtml-to-pdf-razor/), [Web Forms](https://ironpdf.com/how-to/aspx-to-pdf/)

[![IronPDF Cross Platform Compatibility Support Image](https://raw.githubusercontent.com/iron-software/iron-nuget-assets/main/IronPDF-readme/cross-platform-compatibility.png)](https://ironpdf.com/docs/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=crossplatformbanner)


Additionally, our [API reference](https://ironpdf.com/object-reference/api/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist) and [full licensing information](https://ironpdf.com/licensing/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featureslist#trial-license) can easily be found on our website.

## Using IronPDF

Installing the IronPDF NuGet package is quick and easy, please install the package like this:
```
PM> Install-Package IronPdf
```
Once installed, you can get started by adding `using IronPdf` to the top of your C# code. Here is a sample HTML to PDF example to get started:
```
// HTML to PDF
using IronPdf;

var renderer = new ChromePdfRenderer(); // Instantiates Chrome Renderer
var pdf = renderer.RenderHtmlAsPdf(" <h1> ~Hello World~ </h1> Made with IronPDF!");
pdf.SaveAs("html_saved.pdf"); // Saves our PdfDocument object as a PDF
```
And another option is to create from URL to PDF:
```
// URL to PDF
using IronPdf;

var renderer = new ChromePdfRenderer(); // Instantiates Chrome Renderer

// To include elements that are usually removed to save ink during printing we choose screen
renderer.RenderingOptions.CssMediaType = IronPdf.Rendering.PdfCssMediaType.Screen;

var pdf = renderer.RenderUrlAsPdf("https://ironpdf.com/");
pdf.SaveAs("url_saved.pdf");
```
## Features Table
[![IronPDF Features](https://raw.githubusercontent.com/iron-software/iron-nuget-assets/main/IronPDF-readme/features-table.png)](https://ironpdf.com/features/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=featuresbanner)

## Licensing & Support Available
For our full list of code examples, tutorials, licensing information, and documentation visit: [https://ironpdf.com/](https://ironpdf.com/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=supportanddocs)

For more support and inquiries, please email us at: <EMAIL>

## Documentation Links

-   How-To Guides : [https://ironpdf.com/how-to/](https://ironpdf.com/how-to/html-file-to-pdf/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=supportanddocs)
-   Live Demos : [https://ironpdf.com/demos/](https://ironpdf.com/demos/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=supportanddocs)
-   Code Examples : [https://ironpdf.com/examples/](https://ironpdf.com/examples/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=supportanddocs)
-   API Reference : [https://ironpdf.com/object-reference/api/](https://ironpdf.com/object-reference/api/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=supportanddocs)
-   Tutorials : [https://ironpdf.com/tutorials/](https://ironpdf.com/tutorials/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=supportanddocs)
-   Licensing : [https://ironpdf.com/licensing/](https://ironpdf.com/licensing/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=supportanddocs)
- Live Chat Support : [https://ironpdf.com/#helpscout-support](https://ironpdf.com/?utm_source=nuget&utm_medium=organic&utm_campaign=readme&utm_content=supportanddocs#helpscout-support)
