﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="BarManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="LeftAlignPictureBox.EditValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABF0RVh0VGl0
        bGUAQWxpZ247TGVmdDtwpv7rAAADgElEQVRYR8WW904bQRDGKSkPERLANhBCsek2PjdMQgkGkqCQENLz
        NhABygsFUh6DIoroiCJA8Mdkvt3b4+a4YP5AZqWf8Kz35pvbb2dNERHdKr6ThcR3spD4ThYS38lCIgM9
        im1KbohS12fklZoi4AXn5+dTp6enP4+Pj2ePjo58OTw89OXg4GDufyDf2dnZFDSEpgj4S4hnh79Rdujr
        BYNfHLpATpPOfaYMkx74ZPORUs+ZfvCBea9I9o1TJN5PKII1SoSmCOwC2tIvqCaSUVQbwpoqRZpC4ZRN
        kkINmmBDwoVFwXqLAnVxB+zedQqYwML9/f25vb09h93d3Uvs7Oz88rK1taXAZ7MOz8Mi5IaG0BSBLmAa
        BfiJesWAETRsbm6qv7boDOe8w5iDmP8QnpyczGbYX3ibybGvBuOv8lj7m1SMK48TveCdwmLCsT7CW3PO
        knC0pyjc3oP8aghNEfBiHJTm5BCFGtnfRvaWCYJLHjP1cQo6HndS4EknVSpiVFkbcwqoqOkoAmYITRHw
        DnABE2gbs73YUi8bGxuXWF9f/21AjOfZhu+c8y5jbMhvARcwjQPoFveKAbcgWFtbE5hC8DxyISdyQ0No
        ioC3CwfQ9K/pYbe/id4xSvSMkdXzlqxn4A0zStbTUYp3g9fUmQUjFDN0jVB9S5ZQBDSEpgj4S/jWFB/Q
        Pcwemx7W3ro9jlLlY9BB7C+VO7RdUGVoofJQiymgVGiKQO/AJFoI2+fd1tXVVcHKyopgeXn5DzCx2wq0
        MXJDQ2iKQBcwg8V4MJ8gMKKGpaUlVQBEt7e3f3DOewwOonMXCE0R8AJ0gOV4DH8Z46/yVvtrgL+xDHhF
        sfRLRZSv8rqmDOFFbOHiukgK+dUQmiKwC+CLw/ZY++vrcTW8bdWwv+BRsFngLqAsEEF+NYSmCHQBk2gb
        9/ZiW90sLi4KFhYWBJiD/9yC+Pm9z5i7IL8FuL/RuyjguoLz8/N/vWAez+IFcH7wUvZvw5WHsBQd4O1h
        7a/2GP5G08MUTdnwtd2eAIOKVgvkJPEc1XJb43aEhtAUgV1AfVu3y2P2NsSeAuFxEz0EgQixv1RWCcKS
        ikamgR6Ua7AL+QpQ9wAqhYem1byYs+HG2OUFluF72MoXEX4brv6HBAsYHBgcHIA+vgmudQjdA8XcJM4Q
        mu7gNvCdLCS+k4XEd7JwUNE/dnqnjECQHzAAAAAASUVORK5CYII=
</value>
  </data>
  <data name="CenterAlignPictureBox.EditValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABN0RVh0VGl0
        bGUAQWxpZ247Q2VudGVyO6pOXacAAAOkSURBVFhHxZbpUhNREIUDAXwIUZYEEElI2BOyEBbZAwiUKOLu
        0wgK6AspLo/BUizFTrEU64+2z517w3SYAD+ocKu+Yvom06enT98JLiK6Vxw3s4njZjZx3MwmjpvZRAbW
        ytHk3hFu2zXySk0R8BcuLi6mT09Pfx4fH/86Ojpy5PDw0JGDg4O5TCDf+fn5NDSEpgj4Q4i3D32m9sFP
        lwx8TNEGkhaJ5AdqZRL97zXvqKWP6QVvmTeKeM8EBSO9hCJYI1doikAX0JB4ThXBVkW5IWBRpkiQN9Ci
        iZPXb+Hxx2xEyeOLUmlVJAW6d2MBZ2dnX05OTkRL9/f35/b29uZ2d3cFOzs7v9PZ2tpS4Np8D/ciHz/c
        JDSEpgisZQYQw+PmqmdRBJKkiwEjaNjc3FR/tegs58jTuW41hGrVxkeYYVzmwrdE0uav8tjyN66YUB7H
        usFrRZQJhHsIT40cgVCXK9DYhXxqCU0R6OUNJFze6hZc5sK3mliSPU33mPFFyJPyuJlKnzZTiSJMJZXh
        VAHFFU0uYJbQFIG17Bbkcfun0E7TXrCxsXGF9fX1PwbEsIfv+8o58pFH57v5PcCDMoPKIWoGzYhnEgRr
        a2sCU4iZCQwy2zkDDaEpAt1yc37NGbb7G+sep1jXOEW7XlG0E7xkxij6bIwiHeAFNbeDUQob2kbJV9dO
        KAIaQlME/CGevibSb51h9ticYctbu8chKnkCmoj9paIUDZeUGeqoyFtnCnALTRFwe7hNkzh2aL9pvWnx
        6uqqYGVlRbC8vPwXmNhuBfJxd6fwkEJTBNbCoKgBZPJ5Fr6jECSzixmMqGFpaUkVANHt7e0fnKMAeXRO
        9S4QmiLQyx/qY3px6UbbUv4qby1/DfA33ApGKJwYVoT4VV5V00p4auRgcqqC6lirJTRFoBf7q+ClCvA3
        dF71uBze1luwv+Cxp1ZgL6CwNIh8aglNEVjLWIC2FfAx/IZ2oq1oL1hcXBQsLCwIsAfLeH7w8/tA54Kl
        N1qAYziLdwBEzeAZbzMJzs/P/0sH+7gH9yIH5kj/Nlw7hG6Ip59hy1/LY/gbSgxRqEUTH6TGGBhQ1EdB
        UhJJUiUfa7zUoCE0RaAL8DV02Dxmb73sKRAe19AjUBok9pcKS0BAUlzN+OlhkQW6cFMBsGAKlWY6dsB+
        7AxmPtKBZfgc7xMeaPw2XP8PCb7AqHeABuf4LrjVENoXirlLUkto2oP7wHEzmzhuZhPHzexBrv+sWVoq
        aerjMAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="RightAlignPictureBox.EditValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABJ0RVh0VGl0
        bGUAQWxpZ247UmlnaHQ7cux79gAAA21JREFUWEfFlulSU0EQhSMRXkIRQgKIEMMWCGRlERAIoFCiiLtP
        oyjLEymKT0GxFEuxFFAsxfqj7TNz53I7XEh+UGGqvmJmErrP7dN9wUNEd4rrZT5xvcwnrpf5xPUyn8iD
        XvcsCm4Jr2OPuDKnOPAXLi4uJk9PT38dHx//Pjo6cuXw8NCVg4OD2etAvPPz80nkEDnFgT9E8o6hr9Qx
        +OWSgc827SCtSaU/URuT6v9o8YGSfUwveM+8UySej1NttJcggnMUiJziYAkIp15QZW2bosIQ0pQrUhQI
        JS0SFAhq/MG4gxj5a2JUVh21QfWyCjg7O/t2cnIiSrq/vz+7t7c3u7u7K9jZ2fmTyfb2tmBra0v9RAyu
        wPdsArBMA6J5vKx6GiIgINeEbuD3WcAUYouc4mCt+sSwh73FtgC+pdIOf5XH2t+EYlx5HO8BbxUx0D3G
        vKFY12tFqKmbUFXEFDnFwVqBUMpTHx/AtgC+1cXT7Gmmx0xNlPy2x61U9qSVfIoW8lWBZvI9bqbSSk2u
        ApwW3OfyT6B8zvJubm5eYWNj46+T9fV1G5zRM/wwE4gtcoqDnoIpKDVNZ3zOTHxTQsPa2poCe8ThuNPZ
        BKiSm/k1M+z0N94zRnHbX+PxKMWejVK0E7yi1g4wQi2G9hGqaeggTEJWAXj6umi/nmH22Myw9tbpcYT9
        BdrfEpvwJeWGBioJNBgBXpFTHNgCzCrGDuU3pTclNiU1rK6uClZWVuYyWV5ensNniIN+wkOKnOKgF5pQ
        NSBTyL0wAyEQkJkQuCXMBPcQwBWY4ZhZBXiCkT4Pe4ytF2Wz/VXean8N8LelDQxTS+qlIsKv8kgSDCma
        EkNUXZskVBUxRU5xsBb76wlFurFVAoLhrqseV8DbRg37Cx756wXF/joqLqulhxa5CjAWFDJFPD4/MXYo
        tynp0tKSYHFxUbCwsPDPCe7QR2znD46ZdQyn8Q5AUtN4xttcE4L5+XkbfAex0E+c48YKeJE8c4a1v9pj
        5W9Ke6tIDFJTHAwoGmMgLYmmqYrHGi+jnATUhDsdHrO3AfYUCI/ZX6fHPhCSlD5lgvSgRINpyiYAFkxA
        6XVjB5xjZzD9kQksw+fWGGbtAfOHSL0DLIpuCcRU/5iKnOIgF8TcJvYSOZ2Hu8D1Mp+4XuYT18v8QZ7/
        YjWnprIQRqQAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="PopupMenu1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>141, 17</value>
  </metadata>
</root>